{"ast": null, "code": "var namedColors = {\n  aliceblue: \"f0f8ff\",\n  antiquewhite: \"faebd7\",\n  aqua: \"00ffff\",\n  aquamarine: \"7fffd4\",\n  azure: \"f0ffff\",\n  beige: \"f5f5dc\",\n  bisque: \"ffe4c4\",\n  black: \"000000\",\n  blanchedalmond: \"ffebcd\",\n  blue: \"0000ff\",\n  blueviolet: \"8a2be2\",\n  brown: \"a52a2a\",\n  burlywood: \"deb887\",\n  cadetblue: \"5f9ea0\",\n  chartreuse: \"7fff00\",\n  chocolate: \"d2691e\",\n  coral: \"ff7f50\",\n  cornflowerblue: \"6495ed\",\n  cornsilk: \"fff8dc\",\n  crimson: \"dc143c\",\n  cyan: \"00ffff\",\n  darkblue: \"00008b\",\n  darkcyan: \"008b8b\",\n  darkgoldenrod: \"b8860b\",\n  darkgray: \"a9a9a9\",\n  darkgrey: \"a9a9a9\",\n  darkgreen: \"006400\",\n  darkkhaki: \"bdb76b\",\n  darkmagenta: \"8b008b\",\n  darkolivegreen: \"556b2f\",\n  darkorange: \"ff8c00\",\n  darkorchid: \"9932cc\",\n  darkred: \"8b0000\",\n  darksalmon: \"e9967a\",\n  darkseagreen: \"8fbc8f\",\n  darkslateblue: \"483d8b\",\n  darkslategray: \"2f4f4f\",\n  darkslategrey: \"2f4f4f\",\n  darkturquoise: \"00ced1\",\n  darkviolet: \"9400d3\",\n  deeppink: \"ff1493\",\n  deepskyblue: \"00bfff\",\n  dimgray: \"696969\",\n  dimgrey: \"696969\",\n  dodgerblue: \"1e90ff\",\n  firebrick: \"b22222\",\n  floralwhite: \"fffaf0\",\n  forestgreen: \"228b22\",\n  fuchsia: \"ff00ff\",\n  gainsboro: \"dcdcdc\",\n  ghostwhite: \"f8f8ff\",\n  gold: \"ffd700\",\n  goldenrod: \"daa520\",\n  gray: \"808080\",\n  grey: \"808080\",\n  green: \"008000\",\n  greenyellow: \"adff2f\",\n  honeydew: \"f0fff0\",\n  hotpink: \"ff69b4\",\n  indianred: \"cd5c5c\",\n  indigo: \"4b0082\",\n  ivory: \"fffff0\",\n  khaki: \"f0e68c\",\n  lavender: \"e6e6fa\",\n  lavenderblush: \"fff0f5\",\n  lawngreen: \"7cfc00\",\n  lemonchiffon: \"fffacd\",\n  lightblue: \"add8e6\",\n  lightcoral: \"f08080\",\n  lightcyan: \"e0ffff\",\n  lightgoldenrodyellow: \"fafad2\",\n  lightgray: \"d3d3d3\",\n  lightgrey: \"d3d3d3\",\n  lightgreen: \"90ee90\",\n  lightpink: \"ffb6c1\",\n  lightsalmon: \"ffa07a\",\n  lightseagreen: \"20b2aa\",\n  lightskyblue: \"87cefa\",\n  lightslategray: \"778899\",\n  lightslategrey: \"778899\",\n  lightsteelblue: \"b0c4de\",\n  lightyellow: \"ffffe0\",\n  lime: \"00ff00\",\n  limegreen: \"32cd32\",\n  linen: \"faf0e6\",\n  magenta: \"ff00ff\",\n  maroon: \"800000\",\n  mediumaquamarine: \"66cdaa\",\n  mediumblue: \"0000cd\",\n  mediumorchid: \"ba55d3\",\n  mediumpurple: \"9370d8\",\n  mediumseagreen: \"3cb371\",\n  mediumslateblue: \"7b68ee\",\n  mediumspringgreen: \"00fa9a\",\n  mediumturquoise: \"48d1cc\",\n  mediumvioletred: \"c71585\",\n  midnightblue: \"191970\",\n  mintcream: \"f5fffa\",\n  mistyrose: \"ffe4e1\",\n  moccasin: \"ffe4b5\",\n  navajowhite: \"ffdead\",\n  navy: \"000080\",\n  oldlace: \"fdf5e6\",\n  olive: \"808000\",\n  olivedrab: \"6b8e23\",\n  orange: \"ffa500\",\n  orangered: \"ff4500\",\n  orchid: \"da70d6\",\n  palegoldenrod: \"eee8aa\",\n  palegreen: \"98fb98\",\n  paleturquoise: \"afeeee\",\n  palevioletred: \"d87093\",\n  papayawhip: \"ffefd5\",\n  peachpuff: \"ffdab9\",\n  peru: \"cd853f\",\n  pink: \"ffc0cb\",\n  plum: \"dda0dd\",\n  powderblue: \"b0e0e6\",\n  purple: \"800080\",\n  red: \"ff0000\",\n  rosybrown: \"bc8f8f\",\n  royalblue: \"4169e1\",\n  saddlebrown: \"8b4513\",\n  salmon: \"fa8072\",\n  sandybrown: \"f4a460\",\n  seagreen: \"2e8b57\",\n  seashell: \"fff5ee\",\n  sienna: \"a0522d\",\n  silver: \"c0c0c0\",\n  skyblue: \"87ceeb\",\n  slateblue: \"6a5acd\",\n  slategray: \"708090\",\n  slategrey: \"708090\",\n  snow: \"fffafa\",\n  springgreen: \"00ff7f\",\n  steelblue: \"4682b4\",\n  tan: \"d2b48c\",\n  teal: \"008080\",\n  thistle: \"d8bfd8\",\n  tomato: \"ff6347\",\n  turquoise: \"40e0d0\",\n  violet: \"ee82ee\",\n  wheat: \"f5deb3\",\n  white: \"ffffff\",\n  whitesmoke: \"f5f5f5\",\n  yellow: \"ffff00\",\n  yellowgreen: \"9acd32\"\n};\nexport default namedColors;", "map": {"version": 3, "names": ["namedColors", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "<PERSON><PERSON>rey", "darkgreen", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "grey", "green", "greenyellow", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "<PERSON><PERSON>rey", "lightgreen", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "wheat", "white", "whitesmoke", "yellow", "yellowgreen"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/common/color/named-colors.js"], "sourcesContent": ["var namedColors = {\n    aliceblue: \"f0f8ff\", antiquewhite: \"faebd7\", aqua: \"00ffff\",\n    aquamarine: \"7fffd4\", azure: \"f0ffff\", beige: \"f5f5dc\",\n    bisque: \"ffe4c4\", black: \"000000\", blanchedalmond: \"ffebcd\",\n    blue: \"0000ff\", blueviolet: \"8a2be2\", brown: \"a52a2a\",\n    burlywood: \"deb887\", cadetblue: \"5f9ea0\", chartreuse: \"7fff00\",\n    chocolate: \"d2691e\", coral: \"ff7f50\", cornflowerblue: \"6495ed\",\n    cornsilk: \"fff8dc\", crimson: \"dc143c\", cyan: \"00ffff\",\n    darkblue: \"00008b\", darkcyan: \"008b8b\", darkgoldenrod: \"b8860b\",\n    darkgray: \"a9a9a9\", darkgrey: \"a9a9a9\", darkgreen: \"006400\",\n    darkkhaki: \"bdb76b\", darkmagenta: \"8b008b\", darkolivegreen: \"556b2f\",\n    darkorange: \"ff8c00\", darkorchid: \"9932cc\", darkred: \"8b0000\",\n    darksalmon: \"e9967a\", darkseagreen: \"8fbc8f\", darkslateblue: \"483d8b\",\n    darkslategray: \"2f4f4f\", darkslategrey: \"2f4f4f\", darkturquoise: \"00ced1\",\n    darkviolet: \"9400d3\", deeppink: \"ff1493\", deepskyblue: \"00bfff\",\n    dimgray: \"696969\", dimgrey: \"696969\", dodgerblue: \"1e90ff\",\n    firebrick: \"b22222\", floralwhite: \"fffaf0\", forestgreen: \"228b22\",\n    fuchsia: \"ff00ff\", gainsboro: \"dcdcdc\", ghostwhite: \"f8f8ff\",\n    gold: \"ffd700\", goldenrod: \"daa520\", gray: \"808080\",\n    grey: \"808080\", green: \"008000\", greenyellow: \"adff2f\",\n    honeydew: \"f0fff0\", hotpink: \"ff69b4\", indianred: \"cd5c5c\",\n    indigo: \"4b0082\", ivory: \"fffff0\", khaki: \"f0e68c\",\n    lavender: \"e6e6fa\", lavenderblush: \"fff0f5\", lawngreen: \"7cfc00\",\n    lemonchiffon: \"fffacd\", lightblue: \"add8e6\", lightcoral: \"f08080\",\n    lightcyan: \"e0ffff\", lightgoldenrodyellow: \"fafad2\", lightgray: \"d3d3d3\",\n    lightgrey: \"d3d3d3\", lightgreen: \"90ee90\", lightpink: \"ffb6c1\",\n    lightsalmon: \"ffa07a\", lightseagreen: \"20b2aa\", lightskyblue: \"87cefa\",\n    lightslategray: \"778899\", lightslategrey: \"778899\", lightsteelblue: \"b0c4de\",\n    lightyellow: \"ffffe0\", lime: \"00ff00\", limegreen: \"32cd32\",\n    linen: \"faf0e6\", magenta: \"ff00ff\", maroon: \"800000\",\n    mediumaquamarine: \"66cdaa\", mediumblue: \"0000cd\", mediumorchid: \"ba55d3\",\n    mediumpurple: \"9370d8\", mediumseagreen: \"3cb371\", mediumslateblue: \"7b68ee\",\n    mediumspringgreen: \"00fa9a\", mediumturquoise: \"48d1cc\", mediumvioletred: \"c71585\",\n    midnightblue: \"191970\", mintcream: \"f5fffa\", mistyrose: \"ffe4e1\",\n    moccasin: \"ffe4b5\", navajowhite: \"ffdead\", navy: \"000080\",\n    oldlace: \"fdf5e6\", olive: \"808000\", olivedrab: \"6b8e23\",\n    orange: \"ffa500\", orangered: \"ff4500\", orchid: \"da70d6\",\n    palegoldenrod: \"eee8aa\", palegreen: \"98fb98\", paleturquoise: \"afeeee\",\n    palevioletred: \"d87093\", papayawhip: \"ffefd5\", peachpuff: \"ffdab9\",\n    peru: \"cd853f\", pink: \"ffc0cb\", plum: \"dda0dd\",\n    powderblue: \"b0e0e6\", purple: \"800080\", red: \"ff0000\",\n    rosybrown: \"bc8f8f\", royalblue: \"4169e1\", saddlebrown: \"8b4513\",\n    salmon: \"fa8072\", sandybrown: \"f4a460\", seagreen: \"2e8b57\",\n    seashell: \"fff5ee\", sienna: \"a0522d\", silver: \"c0c0c0\",\n    skyblue: \"87ceeb\", slateblue: \"6a5acd\", slategray: \"708090\",\n    slategrey: \"708090\", snow: \"fffafa\", springgreen: \"00ff7f\",\n    steelblue: \"4682b4\", tan: \"d2b48c\", teal: \"008080\",\n    thistle: \"d8bfd8\", tomato: \"ff6347\", turquoise: \"40e0d0\",\n    violet: \"ee82ee\", wheat: \"f5deb3\", white: \"ffffff\",\n    whitesmoke: \"f5f5f5\", yellow: \"ffff00\", yellowgreen: \"9acd32\"\n};\n\nexport default namedColors;"], "mappings": "AAAA,IAAIA,WAAW,GAAG;EACdC,SAAS,EAAE,QAAQ;EAAEC,YAAY,EAAE,QAAQ;EAAEC,IAAI,EAAE,QAAQ;EAC3DC,UAAU,EAAE,QAAQ;EAAEC,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE,QAAQ;EACtDC,MAAM,EAAE,QAAQ;EAAEC,KAAK,EAAE,QAAQ;EAAEC,cAAc,EAAE,QAAQ;EAC3DC,IAAI,EAAE,QAAQ;EAAEC,UAAU,EAAE,QAAQ;EAAEC,KAAK,EAAE,QAAQ;EACrDC,SAAS,EAAE,QAAQ;EAAEC,SAAS,EAAE,QAAQ;EAAEC,UAAU,EAAE,QAAQ;EAC9DC,SAAS,EAAE,QAAQ;EAAEC,KAAK,EAAE,QAAQ;EAAEC,cAAc,EAAE,QAAQ;EAC9DC,QAAQ,EAAE,QAAQ;EAAEC,OAAO,EAAE,QAAQ;EAAEC,IAAI,EAAE,QAAQ;EACrDC,QAAQ,EAAE,QAAQ;EAAEC,QAAQ,EAAE,QAAQ;EAAEC,aAAa,EAAE,QAAQ;EAC/DC,QAAQ,EAAE,QAAQ;EAAEC,QAAQ,EAAE,QAAQ;EAAEC,SAAS,EAAE,QAAQ;EAC3DC,SAAS,EAAE,QAAQ;EAAEC,WAAW,EAAE,QAAQ;EAAEC,cAAc,EAAE,QAAQ;EACpEC,UAAU,EAAE,QAAQ;EAAEC,UAAU,EAAE,QAAQ;EAAEC,OAAO,EAAE,QAAQ;EAC7DC,UAAU,EAAE,QAAQ;EAAEC,YAAY,EAAE,QAAQ;EAAEC,aAAa,EAAE,QAAQ;EACrEC,aAAa,EAAE,QAAQ;EAAEC,aAAa,EAAE,QAAQ;EAAEC,aAAa,EAAE,QAAQ;EACzEC,UAAU,EAAE,QAAQ;EAAEC,QAAQ,EAAE,QAAQ;EAAEC,WAAW,EAAE,QAAQ;EAC/DC,OAAO,EAAE,QAAQ;EAAEC,OAAO,EAAE,QAAQ;EAAEC,UAAU,EAAE,QAAQ;EAC1DC,SAAS,EAAE,QAAQ;EAAEC,WAAW,EAAE,QAAQ;EAAEC,WAAW,EAAE,QAAQ;EACjEC,OAAO,EAAE,QAAQ;EAAEC,SAAS,EAAE,QAAQ;EAAEC,UAAU,EAAE,QAAQ;EAC5DC,IAAI,EAAE,QAAQ;EAAEC,SAAS,EAAE,QAAQ;EAAEC,IAAI,EAAE,QAAQ;EACnDC,IAAI,EAAE,QAAQ;EAAEC,KAAK,EAAE,QAAQ;EAAEC,WAAW,EAAE,QAAQ;EACtDC,QAAQ,EAAE,QAAQ;EAAEC,OAAO,EAAE,QAAQ;EAAEC,SAAS,EAAE,QAAQ;EAC1DC,MAAM,EAAE,QAAQ;EAAEC,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE,QAAQ;EAClDC,QAAQ,EAAE,QAAQ;EAAEC,aAAa,EAAE,QAAQ;EAAEC,SAAS,EAAE,QAAQ;EAChEC,YAAY,EAAE,QAAQ;EAAEC,SAAS,EAAE,QAAQ;EAAEC,UAAU,EAAE,QAAQ;EACjEC,SAAS,EAAE,QAAQ;EAAEC,oBAAoB,EAAE,QAAQ;EAAEC,SAAS,EAAE,QAAQ;EACxEC,SAAS,EAAE,QAAQ;EAAEC,UAAU,EAAE,QAAQ;EAAEC,SAAS,EAAE,QAAQ;EAC9DC,WAAW,EAAE,QAAQ;EAAEC,aAAa,EAAE,QAAQ;EAAEC,YAAY,EAAE,QAAQ;EACtEC,cAAc,EAAE,QAAQ;EAAEC,cAAc,EAAE,QAAQ;EAAEC,cAAc,EAAE,QAAQ;EAC5EC,WAAW,EAAE,QAAQ;EAAEC,IAAI,EAAE,QAAQ;EAAEC,SAAS,EAAE,QAAQ;EAC1DC,KAAK,EAAE,QAAQ;EAAEC,OAAO,EAAE,QAAQ;EAAEC,MAAM,EAAE,QAAQ;EACpDC,gBAAgB,EAAE,QAAQ;EAAEC,UAAU,EAAE,QAAQ;EAAEC,YAAY,EAAE,QAAQ;EACxEC,YAAY,EAAE,QAAQ;EAAEC,cAAc,EAAE,QAAQ;EAAEC,eAAe,EAAE,QAAQ;EAC3EC,iBAAiB,EAAE,QAAQ;EAAEC,eAAe,EAAE,QAAQ;EAAEC,eAAe,EAAE,QAAQ;EACjFC,YAAY,EAAE,QAAQ;EAAEC,SAAS,EAAE,QAAQ;EAAEC,SAAS,EAAE,QAAQ;EAChEC,QAAQ,EAAE,QAAQ;EAAEC,WAAW,EAAE,QAAQ;EAAEC,IAAI,EAAE,QAAQ;EACzDC,OAAO,EAAE,QAAQ;EAAEC,KAAK,EAAE,QAAQ;EAAEC,SAAS,EAAE,QAAQ;EACvDC,MAAM,EAAE,QAAQ;EAAEC,SAAS,EAAE,QAAQ;EAAEC,MAAM,EAAE,QAAQ;EACvDC,aAAa,EAAE,QAAQ;EAAEC,SAAS,EAAE,QAAQ;EAAEC,aAAa,EAAE,QAAQ;EACrEC,aAAa,EAAE,QAAQ;EAAEC,UAAU,EAAE,QAAQ;EAAEC,SAAS,EAAE,QAAQ;EAClEC,IAAI,EAAE,QAAQ;EAAEC,IAAI,EAAE,QAAQ;EAAEC,IAAI,EAAE,QAAQ;EAC9CC,UAAU,EAAE,QAAQ;EAAEC,MAAM,EAAE,QAAQ;EAAEC,GAAG,EAAE,QAAQ;EACrDC,SAAS,EAAE,QAAQ;EAAEC,SAAS,EAAE,QAAQ;EAAEC,WAAW,EAAE,QAAQ;EAC/DC,MAAM,EAAE,QAAQ;EAAEC,UAAU,EAAE,QAAQ;EAAEC,QAAQ,EAAE,QAAQ;EAC1DC,QAAQ,EAAE,QAAQ;EAAEC,MAAM,EAAE,QAAQ;EAAEC,MAAM,EAAE,QAAQ;EACtDC,OAAO,EAAE,QAAQ;EAAEC,SAAS,EAAE,QAAQ;EAAEC,SAAS,EAAE,QAAQ;EAC3DC,SAAS,EAAE,QAAQ;EAAEC,IAAI,EAAE,QAAQ;EAAEC,WAAW,EAAE,QAAQ;EAC1DC,SAAS,EAAE,QAAQ;EAAEC,GAAG,EAAE,QAAQ;EAAEC,IAAI,EAAE,QAAQ;EAClDC,OAAO,EAAE,QAAQ;EAAEC,MAAM,EAAE,QAAQ;EAAEC,SAAS,EAAE,QAAQ;EACxDC,MAAM,EAAE,QAAQ;EAAEC,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE,QAAQ;EAClDC,UAAU,EAAE,QAAQ;EAAEC,MAAM,EAAE,QAAQ;EAAEC,WAAW,EAAE;AACzD,CAAC;AAED,eAAenJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}