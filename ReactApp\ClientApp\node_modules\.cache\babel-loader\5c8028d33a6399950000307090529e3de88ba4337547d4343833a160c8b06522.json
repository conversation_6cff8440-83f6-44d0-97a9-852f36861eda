{"ast": null, "code": "import { isPresent } from '../utils';\n// tslint:enable:max-line-length\n/**\n * @hidden\n * Type guard for `CompositeFilterDescriptor`.\n */\nexport var isCompositeFilterDescriptor = function (source) {\n  return isPresent(source.filters);\n};", "map": {"version": 3, "names": ["isPresent", "isCompositeFilterDescriptor", "source", "filters"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-data-query/dist/es/filtering/filter-descriptor.interface.js"], "sourcesContent": ["import { isPresent } from '../utils';\n// tslint:enable:max-line-length\n/**\n * @hidden\n * Type guard for `CompositeFilterDescriptor`.\n */\nexport var isCompositeFilterDescriptor = function (source) {\n    return isPresent(source.filters);\n};\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,UAAU;AACpC;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,2BAA2B,GAAG,SAAAA,CAAUC,MAAM,EAAE;EACvD,OAAOF,SAAS,CAACE,MAAM,CAACC,OAAO,CAAC;AACpC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}