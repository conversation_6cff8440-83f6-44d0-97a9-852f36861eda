{"ast": null, "code": "import { fieldList } from './field-list';\nvar getterCache = {};\n// tslint:disable-next-line:no-string-literal\ngetterCache['undefined'] = function (obj) {\n  return obj;\n};\n/**\n * @hidden\n */\nexport function getter(field) {\n  if (getterCache[field]) {\n    return getterCache[field];\n  }\n  var fields = fieldList(field);\n  getterCache[field] = function (obj) {\n    var result = obj;\n    for (var idx = 0; idx < fields.length && result; idx++) {\n      result = result[fields[idx]];\n    }\n    return result;\n  };\n  return getterCache[field];\n}", "map": {"version": 3, "names": ["fieldList", "getter<PERSON>ache", "obj", "getter", "field", "fields", "result", "idx", "length"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-common/dist/es/accessors/getter.js"], "sourcesContent": ["import { fieldList } from './field-list';\nvar getterCache = {};\n// tslint:disable-next-line:no-string-literal\ngetterCache['undefined'] = function (obj) { return obj; };\n/**\n * @hidden\n */\nexport function getter(field) {\n    if (getterCache[field]) {\n        return getterCache[field];\n    }\n    var fields = fieldList(field);\n    getterCache[field] = function (obj) {\n        var result = obj;\n        for (var idx = 0; idx < fields.length && result; idx++) {\n            result = result[fields[idx]];\n        }\n        return result;\n    };\n    return getterCache[field];\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,cAAc;AACxC,IAAIC,WAAW,GAAG,CAAC,CAAC;AACpB;AACAA,WAAW,CAAC,WAAW,CAAC,GAAG,UAAUC,GAAG,EAAE;EAAE,OAAOA,GAAG;AAAE,CAAC;AACzD;AACA;AACA;AACA,OAAO,SAASC,MAAMA,CAACC,KAAK,EAAE;EAC1B,IAAIH,WAAW,CAACG,KAAK,CAAC,EAAE;IACpB,OAAOH,WAAW,CAACG,KAAK,CAAC;EAC7B;EACA,IAAIC,MAAM,GAAGL,SAAS,CAACI,KAAK,CAAC;EAC7BH,WAAW,CAACG,KAAK,CAAC,GAAG,UAAUF,GAAG,EAAE;IAChC,IAAII,MAAM,GAAGJ,GAAG;IAChB,KAAK,IAAIK,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGF,MAAM,CAACG,MAAM,IAAIF,MAAM,EAAEC,GAAG,EAAE,EAAE;MACpDD,MAAM,GAAGA,MAAM,CAACD,MAAM,CAACE,GAAG,CAAC,CAAC;IAChC;IACA,OAAOD,MAAM;EACjB,CAAC;EACD,OAAOL,WAAW,CAACG,KAAK,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}