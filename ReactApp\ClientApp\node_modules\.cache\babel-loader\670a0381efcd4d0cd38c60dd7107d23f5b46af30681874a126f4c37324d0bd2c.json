{"ast": null, "code": "/* istanbul ignore next */\n\n/**\n * This is a syntactic sugar for `columns` prop.\n * So HOC will not work on this.\n */\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction ColumnGroup(_) {\n  return null;\n}\nexport default ColumnGroup;", "map": {"version": 3, "names": ["ColumnGroup", "_"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/rc-table/es/sugar/ColumnGroup.js"], "sourcesContent": ["/* istanbul ignore next */\n\n/**\n * This is a syntactic sugar for `columns` prop.\n * So HOC will not work on this.\n */\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction ColumnGroup(_) {\n  return null;\n}\n\nexport default ColumnGroup;"], "mappings": "AAAA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASA,WAAWA,CAACC,CAAC,EAAE;EACtB,OAAO,IAAI;AACb;AAEA,eAAeD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}