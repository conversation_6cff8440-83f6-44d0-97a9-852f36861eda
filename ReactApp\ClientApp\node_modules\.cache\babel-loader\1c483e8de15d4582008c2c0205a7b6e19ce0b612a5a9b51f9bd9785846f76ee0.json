{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as t from \"react\";\nimport r from \"prop-types\";\nimport { classNames as s } from \"@progress/kendo-react-common\";\nconst a = e => /* @__PURE__ */t.createElement(\"div\", {\n  style: e.style,\n  className: s(\"k-card-title\", e.className)\n}, e.children);\na.propTypes = {\n  className: r.string\n};\nexport { a as CardTitle };", "map": {"version": 3, "names": ["t", "r", "classNames", "s", "a", "e", "createElement", "style", "className", "children", "propTypes", "string", "CardTitle"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/card/CardTitle.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as t from \"react\";\nimport r from \"prop-types\";\nimport { classNames as s } from \"@progress/kendo-react-common\";\nconst a = (e) => /* @__PURE__ */ t.createElement(\"div\", { style: e.style, className: s(\"k-card-title\", e.className) }, e.children);\na.propTypes = {\n  className: r.string\n};\nexport {\n  a as CardTitle\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AAC9D,MAAMC,CAAC,GAAIC,CAAC,IAAK,eAAgBL,CAAC,CAACM,aAAa,CAAC,KAAK,EAAE;EAAEC,KAAK,EAAEF,CAAC,CAACE,KAAK;EAAEC,SAAS,EAAEL,CAAC,CAAC,cAAc,EAAEE,CAAC,CAACG,SAAS;AAAE,CAAC,EAAEH,CAAC,CAACI,QAAQ,CAAC;AAClIL,CAAC,CAACM,SAAS,GAAG;EACZF,SAAS,EAAEP,CAAC,CAACU;AACf,CAAC;AACD,SACEP,CAAC,IAAIQ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}