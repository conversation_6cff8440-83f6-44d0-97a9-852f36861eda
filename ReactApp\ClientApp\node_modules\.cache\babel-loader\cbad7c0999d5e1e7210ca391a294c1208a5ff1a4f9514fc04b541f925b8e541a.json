{"ast": null, "code": "import { currencyDisplay, localeCurrency } from '../cldr';\nexport default function formatCurrencySymbol(info, options) {\n  if (options === void 0) options = {};\n  if (!options.currency) {\n    options.currency = localeCurrency(info, true);\n  }\n  var display = currencyDisplay(info, options);\n  return display;\n}", "map": {"version": 3, "names": ["currencyDisplay", "localeCurrency", "formatCurrencySymbol", "info", "options", "currency", "display"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-intl/dist/es/numbers/format-currency-symbol.js"], "sourcesContent": ["import { currencyDisplay, localeCurrency } from '../cldr';\n\nexport default function formatCurrencySymbol(info, options) {\n    if ( options === void 0 ) options = {};\n\n    if (!options.currency) {\n        options.currency = localeCurrency(info, true);\n    }\n\n    var display = currencyDisplay(info, options);\n\n    return display;\n}\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,cAAc,QAAQ,SAAS;AAEzD,eAAe,SAASC,oBAAoBA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACxD,IAAKA,OAAO,KAAK,KAAK,CAAC,EAAGA,OAAO,GAAG,CAAC,CAAC;EAEtC,IAAI,CAACA,OAAO,CAACC,QAAQ,EAAE;IACnBD,OAAO,CAACC,QAAQ,GAAGJ,cAAc,CAACE,IAAI,EAAE,IAAI,CAAC;EACjD;EAEA,IAAIG,OAAO,GAAGN,eAAe,CAACG,IAAI,EAAEC,OAAO,CAAC;EAE5C,OAAOE,OAAO;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}