{"ast": null, "code": "var DASH_ARRAYS = {\n  dot: [1.5, 3.5],\n  dash: [4, 3.5],\n  longdash: [8, 3.5],\n  dashdot: [3.5, 3.5, 1.5, 3.5],\n  longdashdot: [8, 3.5, 1.5, 3.5],\n  longdashdotdot: [8, 3.5, 1.5, 3.5, 1.5, 3.5]\n};\nvar SOLID = \"solid\";\nvar BUTT = \"butt\";\nvar PATTERN = \"Pattern\";\nexport { DASH_ARRAYS, SOLID, BUTT, PATTERN };", "map": {"version": 3, "names": ["DASH_ARRAYS", "dot", "dash", "long<PERSON>h", "dashdot", "<PERSON>das<PERSON><PERSON><PERSON>", "<PERSON>das<PERSON>do<PERSON>do<PERSON>", "SOLID", "BUTT", "PATTERN"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/core/constants.js"], "sourcesContent": ["var DASH_ARRAYS = {\n    dot: [ 1.5, 3.5 ],\n    dash: [ 4, 3.5 ],\n    longdash: [ 8, 3.5 ],\n    dashdot: [ 3.5, 3.5, 1.5, 3.5 ],\n    longdashdot: [ 8, 3.5, 1.5, 3.5 ],\n    longdashdotdot: [ 8, 3.5, 1.5, 3.5, 1.5, 3.5 ]\n};\n\nvar SOLID = \"solid\";\nvar BUTT = \"butt\";\nvar PATTERN = \"Pattern\";\n\nexport { DASH_ARRAYS, SOLID, BUTT, PATTERN };\n"], "mappings": "AAAA,IAAIA,WAAW,GAAG;EACdC,GAAG,EAAE,CAAE,GAAG,EAAE,GAAG,CAA<PERSON>;EACjBC,IAAI,EAAE,CAAE,CAAC,EAAE,GAAG,CAAE;EAChBC,QAAQ,EAAE,CAAE,CAAC,EAAE,GAAG,CAAE;EACpBC,OAAO,EAAE,CAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAE;EAC/BC,WAAW,EAAE,CAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAE;EACjCC,cAAc,EAAE,CAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;AAChD,CAAC;AAED,IAAIC,KAAK,GAAG,OAAO;AACnB,IAAIC,IAAI,GAAG,MAAM;AACjB,IAAIC,OAAO,GAAG,SAAS;AAEvB,SAAST,WAAW,EAAEO,KAAK,EAAEC,IAAI,EAAEC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}