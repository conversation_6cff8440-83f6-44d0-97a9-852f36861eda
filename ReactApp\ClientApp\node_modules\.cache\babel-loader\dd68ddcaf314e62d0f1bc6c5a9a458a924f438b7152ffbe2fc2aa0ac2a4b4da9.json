{"ast": null, "code": "import { Point, Segment } from '../../geometry';\nimport { last, round } from '../../util';\nvar WEIGHT = 0.333;\nvar EXTREMUM_ALLOWED_DEVIATION = 0.01;\nvar X = \"x\";\nvar Y = \"y\";\nexport function pointsToCurve(pointsIn, closed) {\n  var points = pointsIn.slice(0);\n  var segments = [];\n  var length = points.length;\n  if (length > 2) {\n    removeDuplicates(0, points);\n    length = points.length;\n  }\n  if (length < 2 || length === 2 && points[0].equals(points[1])) {\n    return segments;\n  }\n  var p0 = points[0];\n  var p1 = points[1];\n  var p2 = points[2];\n  segments.push(new Segment(p0));\n  while (p0.equals(points[length - 1])) {\n    closed = true;\n    points.pop();\n    length--;\n  }\n  if (length === 2) {\n    var tangent = getTangent(p0, p1, X, Y);\n    last(segments).controlOut(firstControlPoint(tangent, p0, p1, X, Y));\n    segments.push(new Segment(p1, secondControlPoint(tangent, p0, p1, X, Y)));\n    return segments;\n  }\n  var initialControlPoint, lastControlPoint;\n  if (closed) {\n    p0 = points[length - 1];\n    p1 = points[0];\n    p2 = points[1];\n    var controlPoints = getControlPoints(p0, p1, p2);\n    initialControlPoint = controlPoints[1];\n    lastControlPoint = controlPoints[0];\n  } else {\n    var tangent$1 = getTangent(p0, p1, X, Y);\n    initialControlPoint = firstControlPoint(tangent$1, p0, p1, X, Y);\n  }\n  var cp0 = initialControlPoint;\n  for (var idx = 0; idx <= length - 3; idx++) {\n    removeDuplicates(idx, points);\n    length = points.length;\n    if (idx + 3 <= length) {\n      p0 = points[idx];\n      p1 = points[idx + 1];\n      p2 = points[idx + 2];\n      var controlPoints$1 = getControlPoints(p0, p1, p2);\n      last(segments).controlOut(cp0);\n      cp0 = controlPoints$1[1];\n      var cp1 = controlPoints$1[0];\n      segments.push(new Segment(p1, cp1));\n    }\n  }\n  if (closed) {\n    p0 = points[length - 2];\n    p1 = points[length - 1];\n    p2 = points[0];\n    var controlPoints$2 = getControlPoints(p0, p1, p2);\n    last(segments).controlOut(cp0);\n    segments.push(new Segment(p1, controlPoints$2[0]));\n    last(segments).controlOut(controlPoints$2[1]);\n    segments.push(new Segment(p2, lastControlPoint));\n  } else {\n    var tangent$2 = getTangent(p1, p2, X, Y);\n    last(segments).controlOut(cp0);\n    segments.push(new Segment(p2, secondControlPoint(tangent$2, p1, p2, X, Y)));\n  }\n  return segments;\n}\nexport function pointsToClosedCurve(pointsIn) {\n  return pointsToCurve(pointsIn, true);\n}\nexport function removeDuplicates(idx, points) {\n  while (points[idx + 1] && (points[idx].equals(points[idx + 1]) || points[idx + 1].equals(points[idx + 2]))) {\n    points.splice(idx + 1, 1);\n  }\n}\nexport function invertAxis(p0, p1, p2) {\n  var invertAxis = false;\n  if (p0.x === p1.x) {\n    invertAxis = true;\n  } else if (p1.x === p2.x) {\n    if (p1.y < p2.y && p0.y <= p1.y || p2.y < p1.y && p1.y <= p0.y) {\n      invertAxis = true;\n    }\n  } else {\n    var fn = lineFunction(p0, p1);\n    var y2 = calculateFunction(fn, p2.x);\n    if (!(p0.y <= p1.y && p2.y <= y2) && !(p1.y <= p0.y && p2.y >= y2)) {\n      invertAxis = true;\n    }\n  }\n  return invertAxis;\n}\nexport function isLine(p0, p1, p2) {\n  var fn = lineFunction(p0, p1);\n  var y2 = calculateFunction(fn, p2.x);\n  return p0.x === p1.x && p1.x === p2.x || round(y2, 1) === round(p2.y, 1);\n}\nexport function lineFunction(p1, p2) {\n  var a = (p2.y - p1.y) / (p2.x - p1.x);\n  var b = p1.y - a * p1.x;\n  return [b, a];\n}\nexport function getControlPoints(p0, p1, p2) {\n  var xField = X;\n  var yField = Y;\n  var restrict = false;\n  var switchOrientation = false;\n  var tangent;\n  if (isLine(p0, p1, p2)) {\n    tangent = getTangent(p0, p1, X, Y);\n  } else {\n    var monotonic = {\n      x: isMonotonicByField(p0, p1, p2, X),\n      y: isMonotonicByField(p0, p1, p2, Y)\n    };\n    if (monotonic.x && monotonic.y) {\n      tangent = getTangent(p0, p2, X, Y);\n      restrict = true;\n    } else {\n      if (invertAxis(p0, p1, p2)) {\n        xField = Y;\n        yField = X;\n      }\n      if (monotonic[xField]) {\n        tangent = 0;\n      } else {\n        var sign;\n        if (p2[yField] < p0[yField] && p0[yField] <= p1[yField] || p0[yField] < p2[yField] && p1[yField] <= p0[yField]) {\n          sign = numberSign((p2[yField] - p0[yField]) * (p1[xField] - p0[xField]));\n        } else {\n          sign = -numberSign((p2[xField] - p0[xField]) * (p1[yField] - p0[yField]));\n        }\n        tangent = EXTREMUM_ALLOWED_DEVIATION * sign;\n        switchOrientation = true;\n      }\n    }\n  }\n  var secondCP = secondControlPoint(tangent, p0, p1, xField, yField);\n  if (switchOrientation) {\n    var oldXField = xField;\n    xField = yField;\n    yField = oldXField;\n  }\n  var firstCP = firstControlPoint(tangent, p1, p2, xField, yField);\n  if (restrict) {\n    restrictControlPoint(p0, p1, secondCP, tangent);\n    restrictControlPoint(p1, p2, firstCP, tangent);\n  }\n  return [secondCP, firstCP];\n}\nexport function restrictControlPoint(p1, p2, cp, tangent) {\n  if (p1.y < p2.y) {\n    if (p2.y < cp.y) {\n      cp.x = p1.x + (p2.y - p1.y) / tangent;\n      cp.y = p2.y;\n    } else if (cp.y < p1.y) {\n      cp.x = p2.x - (p2.y - p1.y) / tangent;\n      cp.y = p1.y;\n    }\n  } else {\n    if (cp.y < p2.y) {\n      cp.x = p1.x - (p1.y - p2.y) / tangent;\n      cp.y = p2.y;\n    } else if (p1.y < cp.y) {\n      cp.x = p2.x + (p1.y - p2.y) / tangent;\n      cp.y = p1.y;\n    }\n  }\n}\nexport function getTangent(p0, p1, xField, yField) {\n  var x = p1[xField] - p0[xField];\n  var y = p1[yField] - p0[yField];\n  var tangent;\n  if (x === 0) {\n    tangent = 0;\n  } else {\n    tangent = y / x;\n  }\n  return tangent;\n}\nexport function isMonotonicByField(p0, p1, p2, field) {\n  return p2[field] > p1[field] && p1[field] > p0[field] || p2[field] < p1[field] && p1[field] < p0[field];\n}\nexport function firstControlPoint(tangent, p0, p3, xField, yField) {\n  var t1 = p0[xField];\n  var t2 = p3[xField];\n  var distance = (t2 - t1) * WEIGHT;\n  return point(t1 + distance, p0[yField] + distance * tangent, xField, yField);\n}\nexport function secondControlPoint(tangent, p0, p3, xField, yField) {\n  var t1 = p0[xField];\n  var t2 = p3[xField];\n  var distance = (t2 - t1) * WEIGHT;\n  return point(t2 - distance, p3[yField] - distance * tangent, xField, yField);\n}\nexport function point(xValue, yValue, xField, yField) {\n  var controlPoint = new Point();\n  controlPoint[xField] = xValue;\n  controlPoint[yField] = yValue;\n  return controlPoint;\n}\nexport function calculateFunction(fn, x) {\n  var length = fn.length;\n  var result = 0;\n  for (var i = 0; i < length; i++) {\n    result += Math.pow(x, i) * fn[i];\n  }\n  return result;\n}\nexport function numberSign(value) {\n  return value <= 0 ? -1 : 1;\n}", "map": {"version": 3, "names": ["Point", "Segment", "last", "round", "WEIGHT", "EXTREMUM_ALLOWED_DEVIATION", "X", "Y", "pointsToCurve", "pointsIn", "closed", "points", "slice", "segments", "length", "removeDuplicates", "equals", "p0", "p1", "p2", "push", "pop", "tangent", "getTangent", "controlOut", "firstControlPoint", "secondControlPoint", "initialControlPoint", "lastControlPoint", "controlPoints", "getControlPoints", "tangent$1", "cp0", "idx", "controlPoints$1", "cp1", "controlPoints$2", "tangent$2", "pointsToClosedCurve", "splice", "invertAxis", "x", "y", "fn", "lineFunction", "y2", "calculateFunction", "isLine", "a", "b", "xField", "yField", "restrict", "switchOrientation", "monotonic", "isMonotonicByField", "sign", "numberSign", "secondCP", "oldXField", "firstCP", "restrictControlPoint", "cp", "field", "p3", "t1", "t2", "distance", "point", "xValue", "yValue", "controlPoint", "result", "i", "Math", "pow", "value"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/shapes/utils/points-to-curve.js"], "sourcesContent": ["import { Point, Segment } from '../../geometry';\nimport { last, round } from '../../util';\n\nvar WEIGHT = 0.333;\nvar EXTREMUM_ALLOWED_DEVIATION = 0.01;\nvar X = \"x\";\nvar Y = \"y\";\n\nexport function pointsToCurve(pointsIn, closed) {\n    var points = pointsIn.slice(0);\n    var segments = [];\n    var length = points.length;\n\n    if (length > 2) {\n        removeDuplicates(0, points);\n        length = points.length;\n    }\n\n    if (length < 2 || (length === 2 && points[0].equals(points[1]))) {\n        return segments;\n    }\n\n    var p0 = points[0];\n    var p1 = points[1];\n    var p2 = points[2];\n\n    segments.push(new Segment(p0));\n\n    while (p0.equals(points[length - 1])) {\n        closed = true;\n        points.pop();\n        length--;\n    }\n\n    if (length === 2) {\n        var tangent = getTangent(p0,p1, X, Y);\n\n        last(segments).controlOut(\n            firstControlPoint(tangent, p0, p1, X, Y)\n        );\n\n        segments.push(new Segment(\n            p1,\n            secondControlPoint(tangent, p0, p1, X, Y)\n        ));\n\n        return segments;\n    }\n\n    var initialControlPoint, lastControlPoint;\n\n    if (closed) {\n        p0 = points[length - 1]; p1 = points[0]; p2 = points[1];\n        var controlPoints = getControlPoints(p0, p1, p2);\n        initialControlPoint = controlPoints[1];\n        lastControlPoint = controlPoints[0];\n    } else {\n        var tangent$1 = getTangent(p0, p1, X,Y);\n        initialControlPoint = firstControlPoint(tangent$1, p0, p1, X, Y);\n    }\n\n    var cp0 = initialControlPoint;\n    for (var idx = 0; idx <= length - 3; idx++) {\n        removeDuplicates(idx, points);\n        length = points.length;\n        if (idx + 3 <= length) {\n            p0 = points[idx]; p1 = points[idx + 1]; p2 = points[idx + 2];\n            var controlPoints$1 = getControlPoints(p0,p1,p2);\n\n            last(segments).controlOut(cp0);\n            cp0 = controlPoints$1[1];\n\n            var cp1 = controlPoints$1[0];\n            segments.push(new Segment(p1, cp1));\n        }\n    }\n\n    if (closed) {\n        p0 = points[length - 2]; p1 = points[length - 1]; p2 = points[0];\n        var controlPoints$2 = getControlPoints(p0, p1, p2);\n\n        last(segments).controlOut(cp0);\n        segments.push(new Segment(\n            p1,\n            controlPoints$2[0]\n        ));\n\n        last(segments).controlOut(controlPoints$2[1]);\n        segments.push(new Segment(\n            p2,\n            lastControlPoint\n        ));\n    } else {\n        var tangent$2 = getTangent(p1, p2, X, Y);\n\n        last(segments).controlOut(cp0);\n        segments.push(new Segment(\n            p2,\n            secondControlPoint(tangent$2, p1, p2, X, Y)\n        ));\n    }\n\n    return segments;\n}\n\nexport function pointsToClosedCurve(pointsIn) {\n    return pointsToCurve(pointsIn, true);\n}\n\nexport function removeDuplicates(idx, points) {\n    while (points[idx + 1] && (points[idx].equals(points[idx + 1]) || points[idx + 1].equals(points[idx + 2]))) {\n        points.splice(idx + 1, 1);\n    }\n}\n\nexport function invertAxis(p0, p1, p2) {\n    var invertAxis = false;\n\n    if (p0.x === p1.x) {\n        invertAxis = true;\n    } else if (p1.x === p2.x) {\n        if ((p1.y < p2.y && p0.y <= p1.y) || (p2.y < p1.y && p1.y <= p0.y)) {\n            invertAxis = true;\n        }\n    } else {\n        var fn = lineFunction(p0,p1);\n        var y2 = calculateFunction(fn, p2.x);\n        if (!(p0.y <= p1.y && p2.y <= y2) &&\n            !(p1.y <= p0.y && p2.y >= y2)) {\n            invertAxis = true;\n        }\n    }\n\n    return invertAxis;\n}\n\nexport function isLine(p0, p1, p2) {\n    var fn = lineFunction(p0, p1);\n    var y2 = calculateFunction(fn, p2.x);\n\n    return (p0.x === p1.x && p1.x === p2.x) || round(y2, 1) === round(p2.y, 1);\n}\n\nexport function lineFunction(p1, p2) {\n    var a = (p2.y - p1.y) / (p2.x - p1.x);\n    var b = p1.y - a * p1.x;\n\n    return [ b, a ];\n}\n\nexport function getControlPoints(p0, p1, p2) {\n    var xField = X;\n    var yField = Y;\n    var restrict = false;\n    var switchOrientation = false;\n    var tangent;\n\n    if (isLine(p0, p1, p2)) {\n        tangent = getTangent(p0, p1, X, Y);\n    } else {\n        var monotonic = {\n            x: isMonotonicByField(p0, p1, p2, X),\n            y: isMonotonicByField(p0, p1, p2, Y)\n        };\n\n        if (monotonic.x && monotonic.y) {\n            tangent = getTangent(p0, p2, X, Y);\n            restrict = true;\n        } else {\n            if (invertAxis(p0, p1, p2)) {\n                xField = Y;\n                yField = X;\n            }\n\n            if (monotonic[xField]) {\n                tangent = 0;\n            } else {\n                var sign;\n                if ((p2[yField] < p0[yField] && p0[yField] <= p1[yField]) ||\n                    (p0[yField] < p2[yField] && p1[yField] <= p0[yField])) {\n                    sign = numberSign((p2[yField] - p0[yField]) * (p1[xField] - p0[xField]));\n                } else {\n                    sign = -numberSign((p2[xField] - p0[xField]) * (p1[yField] - p0[yField]));\n                }\n\n                tangent = EXTREMUM_ALLOWED_DEVIATION * sign;\n                switchOrientation = true;\n            }\n        }\n    }\n\n    var secondCP = secondControlPoint(tangent, p0, p1, xField, yField);\n\n    if (switchOrientation) {\n        var oldXField = xField;\n        xField = yField;\n        yField = oldXField;\n    }\n\n    var firstCP = firstControlPoint(tangent, p1, p2, xField, yField);\n\n    if (restrict) {\n        restrictControlPoint(p0, p1, secondCP, tangent);\n        restrictControlPoint(p1, p2, firstCP, tangent);\n    }\n\n    return [ secondCP, firstCP ];\n}\n\nexport function restrictControlPoint(p1, p2, cp, tangent) {\n    if (p1.y < p2.y) {\n        if (p2.y < cp.y) {\n            cp.x = p1.x + (p2.y - p1.y) / tangent;\n            cp.y = p2.y;\n        } else if (cp.y < p1.y) {\n            cp.x = p2.x - (p2.y - p1.y) / tangent;\n            cp.y = p1.y;\n        }\n    } else {\n        if (cp.y < p2.y) {\n            cp.x = p1.x - (p1.y - p2.y) / tangent;\n            cp.y = p2.y;\n        } else if (p1.y < cp.y) {\n            cp.x = p2.x + (p1.y - p2.y) / tangent;\n            cp.y = p1.y;\n        }\n    }\n}\n\nexport function getTangent(p0, p1, xField, yField) {\n    var x = p1[xField] - p0[xField];\n    var y = p1[yField] - p0[yField];\n    var tangent;\n\n    if (x === 0) {\n        tangent = 0;\n    } else {\n        tangent = y / x;\n    }\n\n    return tangent;\n}\n\nexport function isMonotonicByField(p0, p1, p2, field) {\n    return (p2[field] > p1[field] && p1[field] > p0[field]) ||\n                (p2[field] < p1[field] && p1[field] < p0[field]);\n}\n\nexport function firstControlPoint(tangent, p0, p3, xField, yField) {\n    var t1 = p0[xField];\n    var t2 = p3[xField];\n    var distance = (t2 - t1) * WEIGHT;\n\n    return point(t1 + distance, p0[yField] + distance * tangent, xField, yField);\n}\n\nexport function secondControlPoint(tangent, p0, p3, xField, yField) {\n    var t1 = p0[xField];\n    var t2 = p3[xField];\n    var distance = (t2 - t1) * WEIGHT;\n\n    return point(t2 - distance, p3[yField] - distance * tangent, xField, yField);\n}\n\nexport function point(xValue, yValue, xField, yField) {\n    var controlPoint = new Point();\n    controlPoint[xField] = xValue;\n    controlPoint[yField] = yValue;\n\n    return controlPoint;\n}\n\nexport function calculateFunction(fn, x) {\n    var length = fn.length;\n    var result = 0;\n\n    for (var i = 0; i < length; i++) {\n        result += Math.pow(x,i) * fn[i];\n    }\n    return result;\n}\n\nexport function numberSign(value) {\n    return value <= 0 ? -1 : 1;\n}\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,OAAO,QAAQ,gBAAgB;AAC/C,SAASC,IAAI,EAAEC,KAAK,QAAQ,YAAY;AAExC,IAAIC,MAAM,GAAG,KAAK;AAClB,IAAIC,0BAA0B,GAAG,IAAI;AACrC,IAAIC,CAAC,GAAG,GAAG;AACX,IAAIC,CAAC,GAAG,GAAG;AAEX,OAAO,SAASC,aAAaA,CAACC,QAAQ,EAAEC,MAAM,EAAE;EAC5C,IAAIC,MAAM,GAAGF,QAAQ,CAACG,KAAK,CAAC,CAAC,CAAC;EAC9B,IAAIC,QAAQ,GAAG,EAAE;EACjB,IAAIC,MAAM,GAAGH,MAAM,CAACG,MAAM;EAE1B,IAAIA,MAAM,GAAG,CAAC,EAAE;IACZC,gBAAgB,CAAC,CAAC,EAAEJ,MAAM,CAAC;IAC3BG,MAAM,GAAGH,MAAM,CAACG,MAAM;EAC1B;EAEA,IAAIA,MAAM,GAAG,CAAC,IAAKA,MAAM,KAAK,CAAC,IAAIH,MAAM,CAAC,CAAC,CAAC,CAACK,MAAM,CAACL,MAAM,CAAC,CAAC,CAAC,CAAE,EAAE;IAC7D,OAAOE,QAAQ;EACnB;EAEA,IAAII,EAAE,GAAGN,MAAM,CAAC,CAAC,CAAC;EAClB,IAAIO,EAAE,GAAGP,MAAM,CAAC,CAAC,CAAC;EAClB,IAAIQ,EAAE,GAAGR,MAAM,CAAC,CAAC,CAAC;EAElBE,QAAQ,CAACO,IAAI,CAAC,IAAInB,OAAO,CAACgB,EAAE,CAAC,CAAC;EAE9B,OAAOA,EAAE,CAACD,MAAM,CAACL,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE;IAClCJ,MAAM,GAAG,IAAI;IACbC,MAAM,CAACU,GAAG,CAAC,CAAC;IACZP,MAAM,EAAE;EACZ;EAEA,IAAIA,MAAM,KAAK,CAAC,EAAE;IACd,IAAIQ,OAAO,GAAGC,UAAU,CAACN,EAAE,EAACC,EAAE,EAAEZ,CAAC,EAAEC,CAAC,CAAC;IAErCL,IAAI,CAACW,QAAQ,CAAC,CAACW,UAAU,CACrBC,iBAAiB,CAACH,OAAO,EAAEL,EAAE,EAAEC,EAAE,EAAEZ,CAAC,EAAEC,CAAC,CAC3C,CAAC;IAEDM,QAAQ,CAACO,IAAI,CAAC,IAAInB,OAAO,CACrBiB,EAAE,EACFQ,kBAAkB,CAACJ,OAAO,EAAEL,EAAE,EAAEC,EAAE,EAAEZ,CAAC,EAAEC,CAAC,CAC5C,CAAC,CAAC;IAEF,OAAOM,QAAQ;EACnB;EAEA,IAAIc,mBAAmB,EAAEC,gBAAgB;EAEzC,IAAIlB,MAAM,EAAE;IACRO,EAAE,GAAGN,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC;IAAEI,EAAE,GAAGP,MAAM,CAAC,CAAC,CAAC;IAAEQ,EAAE,GAAGR,MAAM,CAAC,CAAC,CAAC;IACvD,IAAIkB,aAAa,GAAGC,gBAAgB,CAACb,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;IAChDQ,mBAAmB,GAAGE,aAAa,CAAC,CAAC,CAAC;IACtCD,gBAAgB,GAAGC,aAAa,CAAC,CAAC,CAAC;EACvC,CAAC,MAAM;IACH,IAAIE,SAAS,GAAGR,UAAU,CAACN,EAAE,EAAEC,EAAE,EAAEZ,CAAC,EAACC,CAAC,CAAC;IACvCoB,mBAAmB,GAAGF,iBAAiB,CAACM,SAAS,EAAEd,EAAE,EAAEC,EAAE,EAAEZ,CAAC,EAAEC,CAAC,CAAC;EACpE;EAEA,IAAIyB,GAAG,GAAGL,mBAAmB;EAC7B,KAAK,IAAIM,GAAG,GAAG,CAAC,EAAEA,GAAG,IAAInB,MAAM,GAAG,CAAC,EAAEmB,GAAG,EAAE,EAAE;IACxClB,gBAAgB,CAACkB,GAAG,EAAEtB,MAAM,CAAC;IAC7BG,MAAM,GAAGH,MAAM,CAACG,MAAM;IACtB,IAAImB,GAAG,GAAG,CAAC,IAAInB,MAAM,EAAE;MACnBG,EAAE,GAAGN,MAAM,CAACsB,GAAG,CAAC;MAAEf,EAAE,GAAGP,MAAM,CAACsB,GAAG,GAAG,CAAC,CAAC;MAAEd,EAAE,GAAGR,MAAM,CAACsB,GAAG,GAAG,CAAC,CAAC;MAC5D,IAAIC,eAAe,GAAGJ,gBAAgB,CAACb,EAAE,EAACC,EAAE,EAACC,EAAE,CAAC;MAEhDjB,IAAI,CAACW,QAAQ,CAAC,CAACW,UAAU,CAACQ,GAAG,CAAC;MAC9BA,GAAG,GAAGE,eAAe,CAAC,CAAC,CAAC;MAExB,IAAIC,GAAG,GAAGD,eAAe,CAAC,CAAC,CAAC;MAC5BrB,QAAQ,CAACO,IAAI,CAAC,IAAInB,OAAO,CAACiB,EAAE,EAAEiB,GAAG,CAAC,CAAC;IACvC;EACJ;EAEA,IAAIzB,MAAM,EAAE;IACRO,EAAE,GAAGN,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC;IAAEI,EAAE,GAAGP,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC;IAAEK,EAAE,GAAGR,MAAM,CAAC,CAAC,CAAC;IAChE,IAAIyB,eAAe,GAAGN,gBAAgB,CAACb,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;IAElDjB,IAAI,CAACW,QAAQ,CAAC,CAACW,UAAU,CAACQ,GAAG,CAAC;IAC9BnB,QAAQ,CAACO,IAAI,CAAC,IAAInB,OAAO,CACrBiB,EAAE,EACFkB,eAAe,CAAC,CAAC,CACrB,CAAC,CAAC;IAEFlC,IAAI,CAACW,QAAQ,CAAC,CAACW,UAAU,CAACY,eAAe,CAAC,CAAC,CAAC,CAAC;IAC7CvB,QAAQ,CAACO,IAAI,CAAC,IAAInB,OAAO,CACrBkB,EAAE,EACFS,gBACJ,CAAC,CAAC;EACN,CAAC,MAAM;IACH,IAAIS,SAAS,GAAGd,UAAU,CAACL,EAAE,EAAEC,EAAE,EAAEb,CAAC,EAAEC,CAAC,CAAC;IAExCL,IAAI,CAACW,QAAQ,CAAC,CAACW,UAAU,CAACQ,GAAG,CAAC;IAC9BnB,QAAQ,CAACO,IAAI,CAAC,IAAInB,OAAO,CACrBkB,EAAE,EACFO,kBAAkB,CAACW,SAAS,EAAEnB,EAAE,EAAEC,EAAE,EAAEb,CAAC,EAAEC,CAAC,CAC9C,CAAC,CAAC;EACN;EAEA,OAAOM,QAAQ;AACnB;AAEA,OAAO,SAASyB,mBAAmBA,CAAC7B,QAAQ,EAAE;EAC1C,OAAOD,aAAa,CAACC,QAAQ,EAAE,IAAI,CAAC;AACxC;AAEA,OAAO,SAASM,gBAAgBA,CAACkB,GAAG,EAAEtB,MAAM,EAAE;EAC1C,OAAOA,MAAM,CAACsB,GAAG,GAAG,CAAC,CAAC,KAAKtB,MAAM,CAACsB,GAAG,CAAC,CAACjB,MAAM,CAACL,MAAM,CAACsB,GAAG,GAAG,CAAC,CAAC,CAAC,IAAItB,MAAM,CAACsB,GAAG,GAAG,CAAC,CAAC,CAACjB,MAAM,CAACL,MAAM,CAACsB,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;IACxGtB,MAAM,CAAC4B,MAAM,CAACN,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;EAC7B;AACJ;AAEA,OAAO,SAASO,UAAUA,CAACvB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EACnC,IAAIqB,UAAU,GAAG,KAAK;EAEtB,IAAIvB,EAAE,CAACwB,CAAC,KAAKvB,EAAE,CAACuB,CAAC,EAAE;IACfD,UAAU,GAAG,IAAI;EACrB,CAAC,MAAM,IAAItB,EAAE,CAACuB,CAAC,KAAKtB,EAAE,CAACsB,CAAC,EAAE;IACtB,IAAKvB,EAAE,CAACwB,CAAC,GAAGvB,EAAE,CAACuB,CAAC,IAAIzB,EAAE,CAACyB,CAAC,IAAIxB,EAAE,CAACwB,CAAC,IAAMvB,EAAE,CAACuB,CAAC,GAAGxB,EAAE,CAACwB,CAAC,IAAIxB,EAAE,CAACwB,CAAC,IAAIzB,EAAE,CAACyB,CAAE,EAAE;MAChEF,UAAU,GAAG,IAAI;IACrB;EACJ,CAAC,MAAM;IACH,IAAIG,EAAE,GAAGC,YAAY,CAAC3B,EAAE,EAACC,EAAE,CAAC;IAC5B,IAAI2B,EAAE,GAAGC,iBAAiB,CAACH,EAAE,EAAExB,EAAE,CAACsB,CAAC,CAAC;IACpC,IAAI,EAAExB,EAAE,CAACyB,CAAC,IAAIxB,EAAE,CAACwB,CAAC,IAAIvB,EAAE,CAACuB,CAAC,IAAIG,EAAE,CAAC,IAC7B,EAAE3B,EAAE,CAACwB,CAAC,IAAIzB,EAAE,CAACyB,CAAC,IAAIvB,EAAE,CAACuB,CAAC,IAAIG,EAAE,CAAC,EAAE;MAC/BL,UAAU,GAAG,IAAI;IACrB;EACJ;EAEA,OAAOA,UAAU;AACrB;AAEA,OAAO,SAASO,MAAMA,CAAC9B,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAC/B,IAAIwB,EAAE,GAAGC,YAAY,CAAC3B,EAAE,EAAEC,EAAE,CAAC;EAC7B,IAAI2B,EAAE,GAAGC,iBAAiB,CAACH,EAAE,EAAExB,EAAE,CAACsB,CAAC,CAAC;EAEpC,OAAQxB,EAAE,CAACwB,CAAC,KAAKvB,EAAE,CAACuB,CAAC,IAAIvB,EAAE,CAACuB,CAAC,KAAKtB,EAAE,CAACsB,CAAC,IAAKtC,KAAK,CAAC0C,EAAE,EAAE,CAAC,CAAC,KAAK1C,KAAK,CAACgB,EAAE,CAACuB,CAAC,EAAE,CAAC,CAAC;AAC9E;AAEA,OAAO,SAASE,YAAYA,CAAC1B,EAAE,EAAEC,EAAE,EAAE;EACjC,IAAI6B,CAAC,GAAG,CAAC7B,EAAE,CAACuB,CAAC,GAAGxB,EAAE,CAACwB,CAAC,KAAKvB,EAAE,CAACsB,CAAC,GAAGvB,EAAE,CAACuB,CAAC,CAAC;EACrC,IAAIQ,CAAC,GAAG/B,EAAE,CAACwB,CAAC,GAAGM,CAAC,GAAG9B,EAAE,CAACuB,CAAC;EAEvB,OAAO,CAAEQ,CAAC,EAAED,CAAC,CAAE;AACnB;AAEA,OAAO,SAASlB,gBAAgBA,CAACb,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EACzC,IAAI+B,MAAM,GAAG5C,CAAC;EACd,IAAI6C,MAAM,GAAG5C,CAAC;EACd,IAAI6C,QAAQ,GAAG,KAAK;EACpB,IAAIC,iBAAiB,GAAG,KAAK;EAC7B,IAAI/B,OAAO;EAEX,IAAIyB,MAAM,CAAC9B,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,EAAE;IACpBG,OAAO,GAAGC,UAAU,CAACN,EAAE,EAAEC,EAAE,EAAEZ,CAAC,EAAEC,CAAC,CAAC;EACtC,CAAC,MAAM;IACH,IAAI+C,SAAS,GAAG;MACZb,CAAC,EAAEc,kBAAkB,CAACtC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEb,CAAC,CAAC;MACpCoC,CAAC,EAAEa,kBAAkB,CAACtC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEZ,CAAC;IACvC,CAAC;IAED,IAAI+C,SAAS,CAACb,CAAC,IAAIa,SAAS,CAACZ,CAAC,EAAE;MAC5BpB,OAAO,GAAGC,UAAU,CAACN,EAAE,EAAEE,EAAE,EAAEb,CAAC,EAAEC,CAAC,CAAC;MAClC6C,QAAQ,GAAG,IAAI;IACnB,CAAC,MAAM;MACH,IAAIZ,UAAU,CAACvB,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,EAAE;QACxB+B,MAAM,GAAG3C,CAAC;QACV4C,MAAM,GAAG7C,CAAC;MACd;MAEA,IAAIgD,SAAS,CAACJ,MAAM,CAAC,EAAE;QACnB5B,OAAO,GAAG,CAAC;MACf,CAAC,MAAM;QACH,IAAIkC,IAAI;QACR,IAAKrC,EAAE,CAACgC,MAAM,CAAC,GAAGlC,EAAE,CAACkC,MAAM,CAAC,IAAIlC,EAAE,CAACkC,MAAM,CAAC,IAAIjC,EAAE,CAACiC,MAAM,CAAC,IACnDlC,EAAE,CAACkC,MAAM,CAAC,GAAGhC,EAAE,CAACgC,MAAM,CAAC,IAAIjC,EAAE,CAACiC,MAAM,CAAC,IAAIlC,EAAE,CAACkC,MAAM,CAAE,EAAE;UACvDK,IAAI,GAAGC,UAAU,CAAC,CAACtC,EAAE,CAACgC,MAAM,CAAC,GAAGlC,EAAE,CAACkC,MAAM,CAAC,KAAKjC,EAAE,CAACgC,MAAM,CAAC,GAAGjC,EAAE,CAACiC,MAAM,CAAC,CAAC,CAAC;QAC5E,CAAC,MAAM;UACHM,IAAI,GAAG,CAACC,UAAU,CAAC,CAACtC,EAAE,CAAC+B,MAAM,CAAC,GAAGjC,EAAE,CAACiC,MAAM,CAAC,KAAKhC,EAAE,CAACiC,MAAM,CAAC,GAAGlC,EAAE,CAACkC,MAAM,CAAC,CAAC,CAAC;QAC7E;QAEA7B,OAAO,GAAGjB,0BAA0B,GAAGmD,IAAI;QAC3CH,iBAAiB,GAAG,IAAI;MAC5B;IACJ;EACJ;EAEA,IAAIK,QAAQ,GAAGhC,kBAAkB,CAACJ,OAAO,EAAEL,EAAE,EAAEC,EAAE,EAAEgC,MAAM,EAAEC,MAAM,CAAC;EAElE,IAAIE,iBAAiB,EAAE;IACnB,IAAIM,SAAS,GAAGT,MAAM;IACtBA,MAAM,GAAGC,MAAM;IACfA,MAAM,GAAGQ,SAAS;EACtB;EAEA,IAAIC,OAAO,GAAGnC,iBAAiB,CAACH,OAAO,EAAEJ,EAAE,EAAEC,EAAE,EAAE+B,MAAM,EAAEC,MAAM,CAAC;EAEhE,IAAIC,QAAQ,EAAE;IACVS,oBAAoB,CAAC5C,EAAE,EAAEC,EAAE,EAAEwC,QAAQ,EAAEpC,OAAO,CAAC;IAC/CuC,oBAAoB,CAAC3C,EAAE,EAAEC,EAAE,EAAEyC,OAAO,EAAEtC,OAAO,CAAC;EAClD;EAEA,OAAO,CAAEoC,QAAQ,EAAEE,OAAO,CAAE;AAChC;AAEA,OAAO,SAASC,oBAAoBA,CAAC3C,EAAE,EAAEC,EAAE,EAAE2C,EAAE,EAAExC,OAAO,EAAE;EACtD,IAAIJ,EAAE,CAACwB,CAAC,GAAGvB,EAAE,CAACuB,CAAC,EAAE;IACb,IAAIvB,EAAE,CAACuB,CAAC,GAAGoB,EAAE,CAACpB,CAAC,EAAE;MACboB,EAAE,CAACrB,CAAC,GAAGvB,EAAE,CAACuB,CAAC,GAAG,CAACtB,EAAE,CAACuB,CAAC,GAAGxB,EAAE,CAACwB,CAAC,IAAIpB,OAAO;MACrCwC,EAAE,CAACpB,CAAC,GAAGvB,EAAE,CAACuB,CAAC;IACf,CAAC,MAAM,IAAIoB,EAAE,CAACpB,CAAC,GAAGxB,EAAE,CAACwB,CAAC,EAAE;MACpBoB,EAAE,CAACrB,CAAC,GAAGtB,EAAE,CAACsB,CAAC,GAAG,CAACtB,EAAE,CAACuB,CAAC,GAAGxB,EAAE,CAACwB,CAAC,IAAIpB,OAAO;MACrCwC,EAAE,CAACpB,CAAC,GAAGxB,EAAE,CAACwB,CAAC;IACf;EACJ,CAAC,MAAM;IACH,IAAIoB,EAAE,CAACpB,CAAC,GAAGvB,EAAE,CAACuB,CAAC,EAAE;MACboB,EAAE,CAACrB,CAAC,GAAGvB,EAAE,CAACuB,CAAC,GAAG,CAACvB,EAAE,CAACwB,CAAC,GAAGvB,EAAE,CAACuB,CAAC,IAAIpB,OAAO;MACrCwC,EAAE,CAACpB,CAAC,GAAGvB,EAAE,CAACuB,CAAC;IACf,CAAC,MAAM,IAAIxB,EAAE,CAACwB,CAAC,GAAGoB,EAAE,CAACpB,CAAC,EAAE;MACpBoB,EAAE,CAACrB,CAAC,GAAGtB,EAAE,CAACsB,CAAC,GAAG,CAACvB,EAAE,CAACwB,CAAC,GAAGvB,EAAE,CAACuB,CAAC,IAAIpB,OAAO;MACrCwC,EAAE,CAACpB,CAAC,GAAGxB,EAAE,CAACwB,CAAC;IACf;EACJ;AACJ;AAEA,OAAO,SAASnB,UAAUA,CAACN,EAAE,EAAEC,EAAE,EAAEgC,MAAM,EAAEC,MAAM,EAAE;EAC/C,IAAIV,CAAC,GAAGvB,EAAE,CAACgC,MAAM,CAAC,GAAGjC,EAAE,CAACiC,MAAM,CAAC;EAC/B,IAAIR,CAAC,GAAGxB,EAAE,CAACiC,MAAM,CAAC,GAAGlC,EAAE,CAACkC,MAAM,CAAC;EAC/B,IAAI7B,OAAO;EAEX,IAAImB,CAAC,KAAK,CAAC,EAAE;IACTnB,OAAO,GAAG,CAAC;EACf,CAAC,MAAM;IACHA,OAAO,GAAGoB,CAAC,GAAGD,CAAC;EACnB;EAEA,OAAOnB,OAAO;AAClB;AAEA,OAAO,SAASiC,kBAAkBA,CAACtC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE4C,KAAK,EAAE;EAClD,OAAQ5C,EAAE,CAAC4C,KAAK,CAAC,GAAG7C,EAAE,CAAC6C,KAAK,CAAC,IAAI7C,EAAE,CAAC6C,KAAK,CAAC,GAAG9C,EAAE,CAAC8C,KAAK,CAAC,IACzC5C,EAAE,CAAC4C,KAAK,CAAC,GAAG7C,EAAE,CAAC6C,KAAK,CAAC,IAAI7C,EAAE,CAAC6C,KAAK,CAAC,GAAG9C,EAAE,CAAC8C,KAAK,CAAE;AAChE;AAEA,OAAO,SAAStC,iBAAiBA,CAACH,OAAO,EAAEL,EAAE,EAAE+C,EAAE,EAAEd,MAAM,EAAEC,MAAM,EAAE;EAC/D,IAAIc,EAAE,GAAGhD,EAAE,CAACiC,MAAM,CAAC;EACnB,IAAIgB,EAAE,GAAGF,EAAE,CAACd,MAAM,CAAC;EACnB,IAAIiB,QAAQ,GAAG,CAACD,EAAE,GAAGD,EAAE,IAAI7D,MAAM;EAEjC,OAAOgE,KAAK,CAACH,EAAE,GAAGE,QAAQ,EAAElD,EAAE,CAACkC,MAAM,CAAC,GAAGgB,QAAQ,GAAG7C,OAAO,EAAE4B,MAAM,EAAEC,MAAM,CAAC;AAChF;AAEA,OAAO,SAASzB,kBAAkBA,CAACJ,OAAO,EAAEL,EAAE,EAAE+C,EAAE,EAAEd,MAAM,EAAEC,MAAM,EAAE;EAChE,IAAIc,EAAE,GAAGhD,EAAE,CAACiC,MAAM,CAAC;EACnB,IAAIgB,EAAE,GAAGF,EAAE,CAACd,MAAM,CAAC;EACnB,IAAIiB,QAAQ,GAAG,CAACD,EAAE,GAAGD,EAAE,IAAI7D,MAAM;EAEjC,OAAOgE,KAAK,CAACF,EAAE,GAAGC,QAAQ,EAAEH,EAAE,CAACb,MAAM,CAAC,GAAGgB,QAAQ,GAAG7C,OAAO,EAAE4B,MAAM,EAAEC,MAAM,CAAC;AAChF;AAEA,OAAO,SAASiB,KAAKA,CAACC,MAAM,EAAEC,MAAM,EAAEpB,MAAM,EAAEC,MAAM,EAAE;EAClD,IAAIoB,YAAY,GAAG,IAAIvE,KAAK,CAAC,CAAC;EAC9BuE,YAAY,CAACrB,MAAM,CAAC,GAAGmB,MAAM;EAC7BE,YAAY,CAACpB,MAAM,CAAC,GAAGmB,MAAM;EAE7B,OAAOC,YAAY;AACvB;AAEA,OAAO,SAASzB,iBAAiBA,CAACH,EAAE,EAAEF,CAAC,EAAE;EACrC,IAAI3B,MAAM,GAAG6B,EAAE,CAAC7B,MAAM;EACtB,IAAI0D,MAAM,GAAG,CAAC;EAEd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3D,MAAM,EAAE2D,CAAC,EAAE,EAAE;IAC7BD,MAAM,IAAIE,IAAI,CAACC,GAAG,CAAClC,CAAC,EAACgC,CAAC,CAAC,GAAG9B,EAAE,CAAC8B,CAAC,CAAC;EACnC;EACA,OAAOD,MAAM;AACjB;AAEA,OAAO,SAASf,UAAUA,CAACmB,KAAK,EAAE;EAC9B,OAAOA,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}