{"ast": null, "code": "import { Constants } from '../common/constants';\nimport { Key } from '../common/key';\n/**\n * @hidden\n */\nexport var padZero = function (length) {\n  return new Array(Math.max(length, 0)).fill('0').join('');\n};\n/**\n * @hidden\n */\nexport var unpadZero = function (value) {\n  return value.replace(/^0*/, '');\n};\n/**\n * @hidden\n */\nexport var approximateStringMatching = function (_a) {\n  var oldText = _a.oldText,\n    newText = _a.newText,\n    formatPattern = _a.formatPattern,\n    selectionStart = _a.selectionStart,\n    isInCaretMode = _a.isInCaretMode,\n    keyEvent = _a.keyEvent;\n  /*\n    Remove the right part of the cursor.\n    oldFormat = oldFormat.substring(0, caret + oldText.length - newText.length);\n  */\n  var oldIndex = selectionStart + oldText.length - newText.length;\n  var oldTextSeparator = oldText[oldIndex];\n  var oldSegmentText = oldText.substring(0, oldIndex);\n  var newSegmentText = newText.substring(0, selectionStart);\n  var diff = [];\n  /* Handle the typing of a single character over the same selection. */\n  if (oldSegmentText === newSegmentText && selectionStart > 0) {\n    diff.push([formatPattern[selectionStart - 1], newSegmentText[selectionStart - 1]]);\n    return diff;\n  }\n  if (oldSegmentText.indexOf(newSegmentText) === 0 && isInCaretMode && (keyEvent.key === Key.DELETE || keyEvent.key === Key.BACKSPACE) || oldSegmentText.indexOf(newSegmentText) === 0 && !isInCaretMode && (newSegmentText.length === 0 || formatPattern[newSegmentText.length - 1] !== formatPattern[newSegmentText.length])) {\n    /* Handle Delete/Backspace. */\n    var deletedSymbol = '';\n    /*\n        The whole text is replaced by the same character.\n        A nasty patch is required to keep the selection in the first segment.\n    */\n    if (!isInCaretMode && newSegmentText.length === 1) {\n      diff.push([formatPattern[0], newSegmentText[0]]);\n    }\n    for (var i = newSegmentText.length; i < oldSegmentText.length; i++) {\n      if (formatPattern[i] !== deletedSymbol && formatPattern[i] !== Constants.formatSeparator) {\n        deletedSymbol = formatPattern[i];\n        diff.push([deletedSymbol, '']);\n      }\n    }\n    return diff;\n  }\n  /*\n      Handle the insertion of the text (the new text is longer than the previous one).\n      Handle the typing over a literal as well.\n  */\n  if (isInCaretMode && (newSegmentText.indexOf(oldSegmentText) === 0 || formatPattern[selectionStart - 1] === Constants.formatSeparator) || !isInCaretMode && (newSegmentText.indexOf(oldSegmentText) === 0 || formatPattern[selectionStart - 1] === Constants.formatSeparator)) {\n    var symbol = formatPattern[0];\n    for (var i = Math.max(0, oldSegmentText.length - 1); i < formatPattern.length; i++) {\n      if (formatPattern[i] !== Constants.formatSeparator) {\n        symbol = formatPattern[i];\n        break;\n      }\n    }\n    return [[symbol, newSegmentText[selectionStart - 1]]];\n  }\n  /* Handle the entering of a space or a separator for navigating to the next item. */\n  if (newSegmentText[newSegmentText.length - 1] === ' ' || newSegmentText[newSegmentText.length - 1] === oldTextSeparator && formatPattern[oldIndex] === '_') {\n    return [[formatPattern[selectionStart - 1], Constants.formatSeparator]];\n  }\n  /* Handle typing over a correctly selected part. */\n  var result = [[formatPattern[selectionStart - 1], newSegmentText[selectionStart - 1]]];\n  return result;\n};\n/**\n * @hidden\n */\nexport var dateSymbolMap = function (map, part) {\n  map[part.pattern[0]] = part.type;\n  return map;\n};\n/**\n * @hidden\n */\nexport var isInRange = function (candidate, min, max) {\n  return candidate === null || !(min && min > candidate || max && max < candidate);\n};", "map": {"version": 3, "names": ["Constants", "Key", "padZero", "length", "Array", "Math", "max", "fill", "join", "unpadZero", "value", "replace", "approximateStringMatching", "_a", "oldText", "newText", "formatPattern", "selectionStart", "isInCaretMode", "keyEvent", "oldIndex", "oldTextSeparator", "oldSegmentText", "substring", "newSegmentText", "diff", "push", "indexOf", "key", "DELETE", "BACKSPACE", "deletedSymbol", "i", "formatSeparator", "symbol", "result", "dateSymbolMap", "map", "part", "pattern", "type", "isInRange", "candidate", "min"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-dateinputs-common/dist/es/dateinput/utils.js"], "sourcesContent": ["import { Constants } from '../common/constants';\nimport { Key } from '../common/key';\n/**\n * @hidden\n */\nexport var padZero = function (length) { return new Array(Math.max(length, 0)).fill('0').join(''); };\n/**\n * @hidden\n */\nexport var unpadZero = function (value) { return value.replace(/^0*/, ''); };\n/**\n * @hidden\n */\nexport var approximateStringMatching = function (_a) {\n    var oldText = _a.oldText, newText = _a.newText, formatPattern = _a.formatPattern, selectionStart = _a.selectionStart, isInCaretMode = _a.isInCaretMode, keyEvent = _a.keyEvent;\n    /*\n      Remove the right part of the cursor.\n      oldFormat = oldFormat.substring(0, caret + oldText.length - newText.length);\n    */\n    var oldIndex = selectionStart + oldText.length - newText.length;\n    var oldTextSeparator = oldText[oldIndex];\n    var oldSegmentText = oldText.substring(0, oldIndex);\n    var newSegmentText = newText.substring(0, selectionStart);\n    var diff = [];\n    /* Handle the typing of a single character over the same selection. */\n    if (oldSegmentText === newSegmentText && selectionStart > 0) {\n        diff.push([formatPattern[selectionStart - 1], newSegmentText[selectionStart - 1]]);\n        return diff;\n    }\n    if (oldSegmentText.indexOf(newSegmentText) === 0 && (isInCaretMode &&\n        (keyEvent.key === Key.DELETE || keyEvent.key === Key.BACKSPACE)) ||\n        (oldSegmentText.indexOf(newSegmentText) === 0 && !isInCaretMode &&\n            (newSegmentText.length === 0 ||\n                formatPattern[newSegmentText.length - 1] !== formatPattern[newSegmentText.length]))) {\n        /* Handle Delete/Backspace. */\n        var deletedSymbol = '';\n        /*\n            The whole text is replaced by the same character.\n            A nasty patch is required to keep the selection in the first segment.\n        */\n        if (!isInCaretMode && newSegmentText.length === 1) {\n            diff.push([formatPattern[0], newSegmentText[0]]);\n        }\n        for (var i = newSegmentText.length; i < oldSegmentText.length; i++) {\n            if (formatPattern[i] !== deletedSymbol && formatPattern[i] !== Constants.formatSeparator) {\n                deletedSymbol = formatPattern[i];\n                diff.push([deletedSymbol, '']);\n            }\n        }\n        return diff;\n    }\n    /*\n        Handle the insertion of the text (the new text is longer than the previous one).\n        Handle the typing over a literal as well.\n    */\n    if ((isInCaretMode &&\n        (newSegmentText.indexOf(oldSegmentText) === 0 ||\n            formatPattern[selectionStart - 1] === Constants.formatSeparator)) ||\n        (!isInCaretMode &&\n            (newSegmentText.indexOf(oldSegmentText) === 0 ||\n                formatPattern[selectionStart - 1] === Constants.formatSeparator))) {\n        var symbol = formatPattern[0];\n        for (var i = Math.max(0, oldSegmentText.length - 1); i < formatPattern.length; i++) {\n            if (formatPattern[i] !== Constants.formatSeparator) {\n                symbol = formatPattern[i];\n                break;\n            }\n        }\n        return [[symbol, newSegmentText[selectionStart - 1]]];\n    }\n    /* Handle the entering of a space or a separator for navigating to the next item. */\n    if ((newSegmentText[newSegmentText.length - 1] === ' ') ||\n        (newSegmentText[newSegmentText.length - 1] === oldTextSeparator && formatPattern[oldIndex] === '_')) {\n        return [[formatPattern[selectionStart - 1], Constants.formatSeparator]];\n    }\n    /* Handle typing over a correctly selected part. */\n    var result = [[formatPattern[selectionStart - 1], newSegmentText[selectionStart - 1]]];\n    return result;\n};\n/**\n * @hidden\n */\nexport var dateSymbolMap = function (map, part) {\n    map[part.pattern[0]] = part.type;\n    return map;\n};\n/**\n * @hidden\n */\nexport var isInRange = function (candidate, min, max) { return (candidate === null || !((min && min > candidate) || (max && max < candidate))); };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,GAAG,QAAQ,eAAe;AACnC;AACA;AACA;AACA,OAAO,IAAIC,OAAO,GAAG,SAAAA,CAAUC,MAAM,EAAE;EAAE,OAAO,IAAIC,KAAK,CAACC,IAAI,CAACC,GAAG,CAACH,MAAM,EAAE,CAAC,CAAC,CAAC,CAACI,IAAI,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;AAAE,CAAC;AACpG;AACA;AACA;AACA,OAAO,IAAIC,SAAS,GAAG,SAAAA,CAAUC,KAAK,EAAE;EAAE,OAAOA,KAAK,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AAAE,CAAC;AAC5E;AACA;AACA;AACA,OAAO,IAAIC,yBAAyB,GAAG,SAAAA,CAAUC,EAAE,EAAE;EACjD,IAAIC,OAAO,GAAGD,EAAE,CAACC,OAAO;IAAEC,OAAO,GAAGF,EAAE,CAACE,OAAO;IAAEC,aAAa,GAAGH,EAAE,CAACG,aAAa;IAAEC,cAAc,GAAGJ,EAAE,CAACI,cAAc;IAAEC,aAAa,GAAGL,EAAE,CAACK,aAAa;IAAEC,QAAQ,GAAGN,EAAE,CAACM,QAAQ;EAC9K;AACJ;AACA;AACA;EACI,IAAIC,QAAQ,GAAGH,cAAc,GAAGH,OAAO,CAACX,MAAM,GAAGY,OAAO,CAACZ,MAAM;EAC/D,IAAIkB,gBAAgB,GAAGP,OAAO,CAACM,QAAQ,CAAC;EACxC,IAAIE,cAAc,GAAGR,OAAO,CAACS,SAAS,CAAC,CAAC,EAAEH,QAAQ,CAAC;EACnD,IAAII,cAAc,GAAGT,OAAO,CAACQ,SAAS,CAAC,CAAC,EAAEN,cAAc,CAAC;EACzD,IAAIQ,IAAI,GAAG,EAAE;EACb;EACA,IAAIH,cAAc,KAAKE,cAAc,IAAIP,cAAc,GAAG,CAAC,EAAE;IACzDQ,IAAI,CAACC,IAAI,CAAC,CAACV,aAAa,CAACC,cAAc,GAAG,CAAC,CAAC,EAAEO,cAAc,CAACP,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;IAClF,OAAOQ,IAAI;EACf;EACA,IAAIH,cAAc,CAACK,OAAO,CAACH,cAAc,CAAC,KAAK,CAAC,IAAKN,aAAa,KAC7DC,QAAQ,CAACS,GAAG,KAAK3B,GAAG,CAAC4B,MAAM,IAAIV,QAAQ,CAACS,GAAG,KAAK3B,GAAG,CAAC6B,SAAS,CAAE,IAC/DR,cAAc,CAACK,OAAO,CAACH,cAAc,CAAC,KAAK,CAAC,IAAI,CAACN,aAAa,KAC1DM,cAAc,CAACrB,MAAM,KAAK,CAAC,IACxBa,aAAa,CAACQ,cAAc,CAACrB,MAAM,GAAG,CAAC,CAAC,KAAKa,aAAa,CAACQ,cAAc,CAACrB,MAAM,CAAC,CAAE,EAAE;IAC7F;IACA,IAAI4B,aAAa,GAAG,EAAE;IACtB;AACR;AACA;AACA;IACQ,IAAI,CAACb,aAAa,IAAIM,cAAc,CAACrB,MAAM,KAAK,CAAC,EAAE;MAC/CsB,IAAI,CAACC,IAAI,CAAC,CAACV,aAAa,CAAC,CAAC,CAAC,EAAEQ,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD;IACA,KAAK,IAAIQ,CAAC,GAAGR,cAAc,CAACrB,MAAM,EAAE6B,CAAC,GAAGV,cAAc,CAACnB,MAAM,EAAE6B,CAAC,EAAE,EAAE;MAChE,IAAIhB,aAAa,CAACgB,CAAC,CAAC,KAAKD,aAAa,IAAIf,aAAa,CAACgB,CAAC,CAAC,KAAKhC,SAAS,CAACiC,eAAe,EAAE;QACtFF,aAAa,GAAGf,aAAa,CAACgB,CAAC,CAAC;QAChCP,IAAI,CAACC,IAAI,CAAC,CAACK,aAAa,EAAE,EAAE,CAAC,CAAC;MAClC;IACJ;IACA,OAAON,IAAI;EACf;EACA;AACJ;AACA;AACA;EACI,IAAKP,aAAa,KACbM,cAAc,CAACG,OAAO,CAACL,cAAc,CAAC,KAAK,CAAC,IACzCN,aAAa,CAACC,cAAc,GAAG,CAAC,CAAC,KAAKjB,SAAS,CAACiC,eAAe,CAAC,IACnE,CAACf,aAAa,KACVM,cAAc,CAACG,OAAO,CAACL,cAAc,CAAC,KAAK,CAAC,IACzCN,aAAa,CAACC,cAAc,GAAG,CAAC,CAAC,KAAKjB,SAAS,CAACiC,eAAe,CAAE,EAAE;IAC3E,IAAIC,MAAM,GAAGlB,aAAa,CAAC,CAAC,CAAC;IAC7B,KAAK,IAAIgB,CAAC,GAAG3B,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEgB,cAAc,CAACnB,MAAM,GAAG,CAAC,CAAC,EAAE6B,CAAC,GAAGhB,aAAa,CAACb,MAAM,EAAE6B,CAAC,EAAE,EAAE;MAChF,IAAIhB,aAAa,CAACgB,CAAC,CAAC,KAAKhC,SAAS,CAACiC,eAAe,EAAE;QAChDC,MAAM,GAAGlB,aAAa,CAACgB,CAAC,CAAC;QACzB;MACJ;IACJ;IACA,OAAO,CAAC,CAACE,MAAM,EAAEV,cAAc,CAACP,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;EACzD;EACA;EACA,IAAKO,cAAc,CAACA,cAAc,CAACrB,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,IACjDqB,cAAc,CAACA,cAAc,CAACrB,MAAM,GAAG,CAAC,CAAC,KAAKkB,gBAAgB,IAAIL,aAAa,CAACI,QAAQ,CAAC,KAAK,GAAI,EAAE;IACrG,OAAO,CAAC,CAACJ,aAAa,CAACC,cAAc,GAAG,CAAC,CAAC,EAAEjB,SAAS,CAACiC,eAAe,CAAC,CAAC;EAC3E;EACA;EACA,IAAIE,MAAM,GAAG,CAAC,CAACnB,aAAa,CAACC,cAAc,GAAG,CAAC,CAAC,EAAEO,cAAc,CAACP,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;EACtF,OAAOkB,MAAM;AACjB,CAAC;AACD;AACA;AACA;AACA,OAAO,IAAIC,aAAa,GAAG,SAAAA,CAAUC,GAAG,EAAEC,IAAI,EAAE;EAC5CD,GAAG,CAACC,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAGD,IAAI,CAACE,IAAI;EAChC,OAAOH,GAAG;AACd,CAAC;AACD;AACA;AACA;AACA,OAAO,IAAII,SAAS,GAAG,SAAAA,CAAUC,SAAS,EAAEC,GAAG,EAAErC,GAAG,EAAE;EAAE,OAAQoC,SAAS,KAAK,IAAI,IAAI,EAAGC,GAAG,IAAIA,GAAG,GAAGD,SAAS,IAAMpC,GAAG,IAAIA,GAAG,GAAGoC,SAAU,CAAC;AAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}