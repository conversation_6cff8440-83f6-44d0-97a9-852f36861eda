{"ast": null, "code": "import Matrix from '../geometry/matrix';\nvar matrixRegexp = /matrix\\((.*)\\)/;\nfunction parseMatrix(matrixString) {\n  var match = matrixString.match(matrixRegexp);\n  if (match === null || match.length !== 2) {\n    return Matrix.unit();\n  }\n  var members = match[1].split(',').map(function (x) {\n    return parseFloat(x);\n  });\n  return new (Function.prototype.bind.apply(Matrix, [null].concat(members)))();\n}\nfunction transformMatrix(element) {\n  var transform = getComputedStyle(element).transform;\n  if (transform === 'none') {\n    return Matrix.unit();\n  }\n  return parseMatrix(transform);\n}\nexport default function elementScale(element) {\n  if (!element) {\n    return Matrix.unit();\n  }\n  var matrix = transformMatrix(element);\n  var parent = element.parentElement;\n  while (parent) {\n    var parentMatrix = transformMatrix(parent);\n    matrix = matrix.multiplyCopy(parentMatrix);\n    parent = parent.parentElement;\n  }\n  matrix.b = matrix.c = matrix.e = matrix.f = 0;\n  return matrix;\n}", "map": {"version": 3, "names": ["Matrix", "matrixRegexp", "parseMatrix", "matrixString", "match", "length", "unit", "members", "split", "map", "x", "parseFloat", "Function", "prototype", "bind", "apply", "concat", "transformMatrix", "element", "transform", "getComputedStyle", "elementScale", "matrix", "parent", "parentElement", "parentMatrix", "multiplyCopy", "b", "c", "e", "f"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/util/element-scale.js"], "sourcesContent": ["import Matrix from '../geometry/matrix';\n\nvar matrixRegexp = /matrix\\((.*)\\)/;\n\nfunction parseMatrix(matrixString) {\n    var match = matrixString.match(matrixRegexp);\n    if (match === null || match.length !== 2) {\n        return Matrix.unit();\n    }\n\n    var members = match[1].split(',').map(function (x) { return parseFloat(x); });\n    return new (Function.prototype.bind.apply( Matrix, [ null ].concat( members) ));\n}\n\nfunction transformMatrix(element) {\n    var transform = getComputedStyle(element).transform;\n\n    if (transform === 'none') {\n        return Matrix.unit();\n    }\n\n    return parseMatrix(transform);\n}\n\nexport default function elementScale(element) {\n    if (!element) {\n        return Matrix.unit();\n    }\n\n    var matrix = transformMatrix(element);\n    var parent = element.parentElement;\n    while (parent) {\n        var parentMatrix = transformMatrix(parent);\n        matrix = matrix.multiplyCopy(parentMatrix);\n        parent = parent.parentElement;\n    }\n\n    matrix.b = matrix.c = matrix.e = matrix.f = 0;\n    return matrix;\n}\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AAEvC,IAAIC,YAAY,GAAG,gBAAgB;AAEnC,SAASC,WAAWA,CAACC,YAAY,EAAE;EAC/B,IAAIC,KAAK,GAAGD,YAAY,CAACC,KAAK,CAACH,YAAY,CAAC;EAC5C,IAAIG,KAAK,KAAK,IAAI,IAAIA,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;IACtC,OAAOL,MAAM,CAACM,IAAI,CAAC,CAAC;EACxB;EAEA,IAAIC,OAAO,GAAGH,KAAK,CAAC,CAAC,CAAC,CAACI,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,UAAUC,CAAC,EAAE;IAAE,OAAOC,UAAU,CAACD,CAAC,CAAC;EAAE,CAAC,CAAC;EAC7E,OAAO,KAAKE,QAAQ,CAACC,SAAS,CAACC,IAAI,CAACC,KAAK,CAAEf,MAAM,EAAE,CAAE,IAAI,CAAE,CAACgB,MAAM,CAAET,OAAO,CAAE,CAAC,GAAC;AACnF;AAEA,SAASU,eAAeA,CAACC,OAAO,EAAE;EAC9B,IAAIC,SAAS,GAAGC,gBAAgB,CAACF,OAAO,CAAC,CAACC,SAAS;EAEnD,IAAIA,SAAS,KAAK,MAAM,EAAE;IACtB,OAAOnB,MAAM,CAACM,IAAI,CAAC,CAAC;EACxB;EAEA,OAAOJ,WAAW,CAACiB,SAAS,CAAC;AACjC;AAEA,eAAe,SAASE,YAAYA,CAACH,OAAO,EAAE;EAC1C,IAAI,CAACA,OAAO,EAAE;IACV,OAAOlB,MAAM,CAACM,IAAI,CAAC,CAAC;EACxB;EAEA,IAAIgB,MAAM,GAAGL,eAAe,CAACC,OAAO,CAAC;EACrC,IAAIK,MAAM,GAAGL,OAAO,CAACM,aAAa;EAClC,OAAOD,MAAM,EAAE;IACX,IAAIE,YAAY,GAAGR,eAAe,CAACM,MAAM,CAAC;IAC1CD,MAAM,GAAGA,MAAM,CAACI,YAAY,CAACD,YAAY,CAAC;IAC1CF,MAAM,GAAGA,MAAM,CAACC,aAAa;EACjC;EAEAF,MAAM,CAACK,CAAC,GAAGL,MAAM,CAACM,CAAC,GAAGN,MAAM,CAACO,CAAC,GAAGP,MAAM,CAACQ,CAAC,GAAG,CAAC;EAC7C,OAAOR,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}