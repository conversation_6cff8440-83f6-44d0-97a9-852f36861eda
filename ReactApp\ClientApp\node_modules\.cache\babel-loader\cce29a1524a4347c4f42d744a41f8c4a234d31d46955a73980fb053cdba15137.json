{"ast": null, "code": "import { addMonths } from './add-months';\nimport { createDate } from './create-date';\nimport { lastDayOfMonth } from './last-day-of-month';\n/**\n * @hidden\n */\nexport var setMonth = function (value, month) {\n  var day = value.getDate();\n  var candidate = createDate(value.getFullYear(), month, day, value.getHours(), value.getMinutes(), value.getSeconds(), value.getMilliseconds());\n  return candidate.getDate() === day ? candidate : lastDayOfMonth(addMonths(candidate, -1));\n};", "map": {"version": 3, "names": ["addMonths", "createDate", "lastDayOfMonth", "setMonth", "value", "month", "day", "getDate", "candidate", "getFullYear", "getHours", "getMinutes", "getSeconds", "getMilliseconds"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/set-month.js"], "sourcesContent": ["import { addMonths } from './add-months';\nimport { createDate } from './create-date';\nimport { lastDayOfMonth } from './last-day-of-month';\n/**\n * @hidden\n */\nexport var setMonth = function (value, month) {\n    var day = value.getDate();\n    var candidate = createDate(value.getFullYear(), month, day, value.getHours(), value.getMinutes(), value.getSeconds(), value.getMilliseconds());\n    return candidate.getDate() === day ? candidate : lastDayOfMonth(addMonths(candidate, -1));\n};\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,cAAc;AACxC,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,cAAc,QAAQ,qBAAqB;AACpD;AACA;AACA;AACA,OAAO,IAAIC,QAAQ,GAAG,SAAAA,CAAUC,KAAK,EAAEC,KAAK,EAAE;EAC1C,IAAIC,GAAG,GAAGF,KAAK,CAACG,OAAO,CAAC,CAAC;EACzB,IAAIC,SAAS,GAAGP,UAAU,CAACG,KAAK,CAACK,WAAW,CAAC,CAAC,EAAEJ,KAAK,EAAEC,GAAG,EAAEF,KAAK,CAACM,QAAQ,CAAC,CAAC,EAAEN,KAAK,CAACO,UAAU,CAAC,CAAC,EAAEP,KAAK,CAACQ,UAAU,CAAC,CAAC,EAAER,KAAK,CAACS,eAAe,CAAC,CAAC,CAAC;EAC9I,OAAOL,SAAS,CAACD,OAAO,CAAC,CAAC,KAAKD,GAAG,GAAGE,SAAS,GAAGN,cAAc,CAACF,SAAS,CAACQ,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;AAC7F,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}