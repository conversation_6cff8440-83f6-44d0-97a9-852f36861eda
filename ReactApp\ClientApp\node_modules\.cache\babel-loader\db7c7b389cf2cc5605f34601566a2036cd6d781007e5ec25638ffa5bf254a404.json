{"ast": null, "code": "export default function ownerDocument(element) {\n  return element.ownerDocument || element.document || element;\n}", "map": {"version": 3, "names": ["ownerDocument", "element", "document"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-popup-common/dist/es/owner-document.js"], "sourcesContent": ["export default function ownerDocument(element) {\n    return element.ownerDocument || element.document || element;\n}\n"], "mappings": "AAAA,eAAe,SAASA,aAAaA,CAACC,OAAO,EAAE;EAC3C,OAAOA,OAAO,CAACD,aAAa,IAAIC,OAAO,CAACC,QAAQ,IAAID,OAAO;AAC/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}