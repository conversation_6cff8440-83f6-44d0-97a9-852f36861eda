{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst l = \"labels.optional\",\n  o = {\n    [l]: \"(Optional)\"\n  };\nexport { l as labelsOptional, o as messages };", "map": {"version": 3, "names": ["l", "o", "labelsOptional", "messages"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-labels/messages/index.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst l = \"labels.optional\", o = {\n  [l]: \"(Optional)\"\n};\nexport {\n  l as labelsOptional,\n  o as messages\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAG,iBAAiB;EAAEC,CAAC,GAAG;IAC/B,CAACD,CAAC,GAAG;EACP,CAAC;AACD,SACEA,CAAC,IAAIE,cAAc,EACnBD,CAAC,IAAIE,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}