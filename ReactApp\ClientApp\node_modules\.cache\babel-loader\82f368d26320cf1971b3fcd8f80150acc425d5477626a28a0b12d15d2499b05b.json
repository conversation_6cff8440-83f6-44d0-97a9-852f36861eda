{"ast": null, "code": "import createTransform from '../geometry/transform';\nexport default function translateToPoint(point, bbox, element) {\n  var transofrm = element.transform() || createTransform();\n  var matrix = transofrm.matrix();\n  matrix.e += point.x - bbox.origin.x;\n  matrix.f += point.y - bbox.origin.y;\n  transofrm.matrix(matrix);\n  element.transform(transofrm);\n}", "map": {"version": 3, "names": ["createTransform", "translateToPoint", "point", "bbox", "element", "transofrm", "transform", "matrix", "e", "x", "origin", "f", "y"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/alignment/translate-to-point.js"], "sourcesContent": ["import createTransform from '../geometry/transform';\n\nexport default function translateToPoint(point, bbox, element) {\n    var transofrm = element.transform() || createTransform();\n    var matrix = transofrm.matrix();\n    matrix.e += point.x - bbox.origin.x;\n    matrix.f += point.y - bbox.origin.y;\n\n    transofrm.matrix(matrix);\n    element.transform(transofrm);\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,uBAAuB;AAEnD,eAAe,SAASC,gBAAgBA,CAACC,KAAK,EAAEC,IAAI,EAAEC,OAAO,EAAE;EAC3D,IAAIC,SAAS,GAAGD,OAAO,CAACE,SAAS,CAAC,CAAC,IAAIN,eAAe,CAAC,CAAC;EACxD,IAAIO,MAAM,GAAGF,SAAS,CAACE,MAAM,CAAC,CAAC;EAC/BA,MAAM,CAACC,CAAC,IAAIN,KAAK,CAACO,CAAC,GAAGN,IAAI,CAACO,MAAM,CAACD,CAAC;EACnCF,MAAM,CAACI,CAAC,IAAIT,KAAK,CAACU,CAAC,GAAGT,IAAI,CAACO,MAAM,CAACE,CAAC;EAEnCP,SAAS,CAACE,MAAM,CAACA,MAAM,CAAC;EACxBH,OAAO,CAACE,SAAS,CAACD,SAAS,CAAC;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}