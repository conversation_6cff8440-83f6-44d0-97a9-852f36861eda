{"ast": null, "code": "import errorDetails from './error-details';\nvar formatRegExp = /\\{(\\d+)}?\\}/g;\nvar IntlError = function IntlError(ref) {\n  var name = ref.name;\n  var message = ref.message;\n  if (!name || !message) {\n    throw new Error(\"{ name: string, message: string } object is required!\");\n  }\n  this.name = name;\n  this.message = message;\n};\nIntlError.prototype.formatMessage = function formatMessage() {\n  var values = [],\n    len = arguments.length;\n  while (len--) values[len] = arguments[len];\n  var flattenValues = flatten(values);\n  var formattedMessage = this.message.replace(formatRegExp, function (match, index) {\n    return flattenValues[parseInt(index, 10)];\n  });\n  return this.name + \": \" + formattedMessage;\n};\nIntlError.prototype.error = function error() {\n  var values = [],\n    len = arguments.length;\n  while (len--) values[len] = arguments[len];\n  return new Error(this.formatMessage(values));\n};\nvar flatten = function (arr) {\n  return arr.reduce(function (a, b) {\n    return a.concat(b);\n  }, []);\n};\nvar toIntlErrors = function (errors) {\n  var predicate = function (prev, name) {\n    prev[name] = new IntlError({\n      name: name,\n      message: errors[name]\n    });\n    return prev;\n  };\n  return Object.keys(errors).reduce(predicate, {});\n};\nvar errors = toIntlErrors(errorDetails);\nexport { errors, IntlError, toIntlErrors };", "map": {"version": 3, "names": ["errorDetails", "formatRegExp", "IntlError", "ref", "name", "message", "Error", "prototype", "formatMessage", "values", "len", "arguments", "length", "flattenV<PERSON>ues", "flatten", "formattedMessage", "replace", "match", "index", "parseInt", "error", "arr", "reduce", "a", "b", "concat", "toIntlErrors", "errors", "predicate", "prev", "Object", "keys"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-intl/dist/es/errors.js"], "sourcesContent": ["import errorDetails from './error-details';\n\nvar formatRegExp = /\\{(\\d+)}?\\}/g;\n\nvar IntlError = function IntlError(ref) {\n    var name = ref.name;\n    var message = ref.message;\n\n    if (!name || !message) {\n        throw new Error(\"{ name: string, message: string } object is required!\");\n    }\n\n    this.name = name;\n    this.message = message;\n};\n\nIntlError.prototype.formatMessage = function formatMessage () {\n        var values = [], len = arguments.length;\n        while ( len-- ) values[ len ] = arguments[ len ];\n\n    var flattenValues = flatten(values);\n\n    var formattedMessage = this.message.replace(formatRegExp, function(match, index) {\n        return flattenValues[parseInt(index, 10)];\n    });\n\n    return ((this.name) + \": \" + formattedMessage);\n};\n\nIntlError.prototype.error = function error () {\n        var values = [], len = arguments.length;\n        while ( len-- ) values[ len ] = arguments[ len ];\n\n    return new Error(this.formatMessage(values));\n};\n\nvar flatten = function(arr) {\n    return arr.reduce(function (a, b) { return a.concat(b); }, []);\n};\n\nvar toIntlErrors = function(errors) {\n    var predicate = function(prev, name) {\n        prev[name] = new IntlError({ name: name, message: errors[name] });\n        return prev;\n    };\n\n    return Object.keys(errors).reduce(predicate, {});\n};\n\nvar errors = toIntlErrors(errorDetails);\n\nexport {\n    errors,\n    IntlError,\n    toIntlErrors\n};\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,iBAAiB;AAE1C,IAAIC,YAAY,GAAG,cAAc;AAEjC,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,GAAG,EAAE;EACpC,IAAIC,IAAI,GAAGD,GAAG,CAACC,IAAI;EACnB,IAAIC,OAAO,GAAGF,GAAG,CAACE,OAAO;EAEzB,IAAI,CAACD,IAAI,IAAI,CAACC,OAAO,EAAE;IACnB,MAAM,IAAIC,KAAK,CAAC,uDAAuD,CAAC;EAC5E;EAEA,IAAI,CAACF,IAAI,GAAGA,IAAI;EAChB,IAAI,CAACC,OAAO,GAAGA,OAAO;AAC1B,CAAC;AAEDH,SAAS,CAACK,SAAS,CAACC,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAI;EACtD,IAAIC,MAAM,GAAG,EAAE;IAAEC,GAAG,GAAGC,SAAS,CAACC,MAAM;EACvC,OAAQF,GAAG,EAAE,EAAGD,MAAM,CAAEC,GAAG,CAAE,GAAGC,SAAS,CAAED,GAAG,CAAE;EAEpD,IAAIG,aAAa,GAAGC,OAAO,CAACL,MAAM,CAAC;EAEnC,IAAIM,gBAAgB,GAAG,IAAI,CAACV,OAAO,CAACW,OAAO,CAACf,YAAY,EAAE,UAASgB,KAAK,EAAEC,KAAK,EAAE;IAC7E,OAAOL,aAAa,CAACM,QAAQ,CAACD,KAAK,EAAE,EAAE,CAAC,CAAC;EAC7C,CAAC,CAAC;EAEF,OAAS,IAAI,CAACd,IAAI,GAAI,IAAI,GAAGW,gBAAgB;AACjD,CAAC;AAEDb,SAAS,CAACK,SAAS,CAACa,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAI;EACtC,IAAIX,MAAM,GAAG,EAAE;IAAEC,GAAG,GAAGC,SAAS,CAACC,MAAM;EACvC,OAAQF,GAAG,EAAE,EAAGD,MAAM,CAAEC,GAAG,CAAE,GAAGC,SAAS,CAAED,GAAG,CAAE;EAEpD,OAAO,IAAIJ,KAAK,CAAC,IAAI,CAACE,aAAa,CAACC,MAAM,CAAC,CAAC;AAChD,CAAC;AAED,IAAIK,OAAO,GAAG,SAAAA,CAASO,GAAG,EAAE;EACxB,OAAOA,GAAG,CAACC,MAAM,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IAAE,OAAOD,CAAC,CAACE,MAAM,CAACD,CAAC,CAAC;EAAE,CAAC,EAAE,EAAE,CAAC;AAClE,CAAC;AAED,IAAIE,YAAY,GAAG,SAAAA,CAASC,MAAM,EAAE;EAChC,IAAIC,SAAS,GAAG,SAAAA,CAASC,IAAI,EAAEzB,IAAI,EAAE;IACjCyB,IAAI,CAACzB,IAAI,CAAC,GAAG,IAAIF,SAAS,CAAC;MAAEE,IAAI,EAAEA,IAAI;MAAEC,OAAO,EAAEsB,MAAM,CAACvB,IAAI;IAAE,CAAC,CAAC;IACjE,OAAOyB,IAAI;EACf,CAAC;EAED,OAAOC,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACL,MAAM,CAACM,SAAS,EAAE,CAAC,CAAC,CAAC;AACpD,CAAC;AAED,IAAID,MAAM,GAAGD,YAAY,CAAC1B,YAAY,CAAC;AAEvC,SACI2B,MAAM,EACNzB,SAAS,EACTwB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}