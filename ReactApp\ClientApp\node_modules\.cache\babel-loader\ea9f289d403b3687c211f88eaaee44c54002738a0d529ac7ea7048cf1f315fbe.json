{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nclass n {\n  constructor(e, s, i) {\n    this.openOnClick = e, this.resetMenu = s, this.openItem = i, this.mouseDown = !1, this.openOnClick = e, this.isMouseOverEnabled = !e;\n  }\n  set OpenOnClick(e) {\n    !!e != !!this.openOnClick && (this.mouseDown = !1, this.isMouseOverEnabled = !e), this.openOnClick = e;\n  }\n  handleItemSelectedViaKeyboard() {\n    this.openOnClick && (this.isMouseOverEnabled = !1, this.resetMenu());\n  }\n  get IsMouseOverEnabled() {\n    return this.isMouseOverEnabled;\n  }\n  handleItemMouseDown() {\n    this.mouseDown = !0;\n  }\n  handleItemFocus() {\n    this.openOnClick && !this.mouseDown && (this.isMouseOverEnabled = !0), this.mouseDown = !1;\n  }\n  handleItemClick(e, s) {\n    this.openOnClick && (this.isMouseOverEnabled ? s && (this.isMouseOverEnabled = !1, this.resetMenu()) : (this.isMouseOverEnabled = !0, this.openItem(e)));\n  }\n}\nexport { n as MouseOverHandler };", "map": {"version": 3, "names": ["n", "constructor", "e", "s", "i", "openOnClick", "resetMenu", "openItem", "mouseDown", "isMouseOverEnabled", "OpenOnClick", "handleItemSelectedViaKeyboard", "IsMouseOverEnabled", "handleItemMouseDown", "handleItemFocus", "handleItemClick", "MouseOverHandler"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/menu/utils/MouseOverHandler.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nclass n {\n  constructor(e, s, i) {\n    this.openOnClick = e, this.resetMenu = s, this.openItem = i, this.mouseDown = !1, this.openOnClick = e, this.isMouseOverEnabled = !e;\n  }\n  set OpenOnClick(e) {\n    !!e != !!this.openOnClick && (this.mouseDown = !1, this.isMouseOverEnabled = !e), this.openOnClick = e;\n  }\n  handleItemSelectedViaKeyboard() {\n    this.openOnClick && (this.isMouseOverEnabled = !1, this.resetMenu());\n  }\n  get IsMouseOverEnabled() {\n    return this.isMouseOverEnabled;\n  }\n  handleItemMouseDown() {\n    this.mouseDown = !0;\n  }\n  handleItemFocus() {\n    this.openOnClick && !this.mouseDown && (this.isMouseOverEnabled = !0), this.mouseDown = !1;\n  }\n  handleItemClick(e, s) {\n    this.openOnClick && (this.isMouseOverEnabled ? s && (this.isMouseOverEnabled = !1, this.resetMenu()) : (this.isMouseOverEnabled = !0, this.openItem(e)));\n  }\n}\nexport {\n  n as MouseOverHandler\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,CAAC;EACNC,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;IACnB,IAAI,CAACC,WAAW,GAAGH,CAAC,EAAE,IAAI,CAACI,SAAS,GAAGH,CAAC,EAAE,IAAI,CAACI,QAAQ,GAAGH,CAAC,EAAE,IAAI,CAACI,SAAS,GAAG,CAAC,CAAC,EAAE,IAAI,CAACH,WAAW,GAAGH,CAAC,EAAE,IAAI,CAACO,kBAAkB,GAAG,CAACP,CAAC;EACtI;EACA,IAAIQ,WAAWA,CAACR,CAAC,EAAE;IACjB,CAAC,CAACA,CAAC,IAAI,CAAC,CAAC,IAAI,CAACG,WAAW,KAAK,IAAI,CAACG,SAAS,GAAG,CAAC,CAAC,EAAE,IAAI,CAACC,kBAAkB,GAAG,CAACP,CAAC,CAAC,EAAE,IAAI,CAACG,WAAW,GAAGH,CAAC;EACxG;EACAS,6BAA6BA,CAAA,EAAG;IAC9B,IAAI,CAACN,WAAW,KAAK,IAAI,CAACI,kBAAkB,GAAG,CAAC,CAAC,EAAE,IAAI,CAACH,SAAS,CAAC,CAAC,CAAC;EACtE;EACA,IAAIM,kBAAkBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACH,kBAAkB;EAChC;EACAI,mBAAmBA,CAAA,EAAG;IACpB,IAAI,CAACL,SAAS,GAAG,CAAC,CAAC;EACrB;EACAM,eAAeA,CAAA,EAAG;IAChB,IAAI,CAACT,WAAW,IAAI,CAAC,IAAI,CAACG,SAAS,KAAK,IAAI,CAACC,kBAAkB,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAACD,SAAS,GAAG,CAAC,CAAC;EAC5F;EACAO,eAAeA,CAACb,CAAC,EAAEC,CAAC,EAAE;IACpB,IAAI,CAACE,WAAW,KAAK,IAAI,CAACI,kBAAkB,GAAGN,CAAC,KAAK,IAAI,CAACM,kBAAkB,GAAG,CAAC,CAAC,EAAE,IAAI,CAACH,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAACG,kBAAkB,GAAG,CAAC,CAAC,EAAE,IAAI,CAACF,QAAQ,CAACL,CAAC,CAAC,CAAC,CAAC;EAC1J;AACF;AACA,SACEF,CAAC,IAAIgB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}