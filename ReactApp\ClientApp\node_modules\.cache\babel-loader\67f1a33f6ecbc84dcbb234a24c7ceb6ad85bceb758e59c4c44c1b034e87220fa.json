{"ast": null, "code": "/* Copyright 2014 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar _AnnotationLayerBuilder_instances, _AnnotationLayerBuilder_onAppend, _AnnotationLayerBuilder_eventAbortController, _AnnotationLayerBuilder_updatePresentationModeState;\nimport { __awaiter, __classPrivateFieldGet, __classPrivateFieldSet } from \"tslib\";\n/** @typedef {import(\"../src/display/api\").PDFPageProxy} PDFPageProxy */\n/** @typedef {import(\"../src/display/display_utils\").PageViewport} PageViewport */\n/** @typedef {import(\"../src/display/annotation_storage\").AnnotationStorage} AnnotationStorage */\n/** @typedef {import(\"./interfaces\").IDownloadManager} IDownloadManager */\n/** @typedef {import(\"./interfaces\").IPDFLinkService} IPDFLinkService */\n/** @typedef {import(\"./text_accessibility.js\").TextAccessibilityManager} TextAccessibilityManager */\n/** @typedef {import(\"../src/display/editor/tools.js\").AnnotationEditorUIManager} AnnotationEditorUIManager */\nimport { convertToHtml } from \"../common/core\";\nimport { AnnotationLayer } from \"./annotation-layer\";\nimport { PresentationModeState } from \"./shared/ui_utils\";\n// import { PresentationModeState } from \"./shared/ui_utils\";\n/**\n * @typedef {Object} AnnotationLayerBuilderOptions\n * @property {PDFPageProxy} pdfPage\n * @property {AnnotationStorage} [annotationStorage]\n * @property {string} [imageResourcesPath] - Path for image resources, mainly\n *   for annotation icons. Include trailing slash.\n * @property {boolean} renderForms\n * @property {IPDFLinkService} linkService\n * @property {IDownloadManager} [downloadManager]\n * @property {boolean} [enableScripting]\n * @property {Promise<boolean>} [hasJSActionsPromise]\n * @property {Promise<Object<string, Array<Object>> | null>}\n *   [fieldObjectsPromise]\n * @property {Map<string, HTMLCanvasElement>} [annotationCanvasMap]\n * @property {TextAccessibilityManager} [accessibilityManager]\n * @property {AnnotationEditorUIManager} [annotationEditorUIManager]\n * @property {function} [onAppend]\n */\nclass AnnotationLayerBuilder {\n  /**\n   * @param {AnnotationLayerBuilderOptions} options\n   */\n  constructor({\n    pdfPage,\n    eventBus,\n    linkService = null,\n    // downloadManager,\n    annotationStorage = null,\n    // imageResourcesPath = \"\",\n    renderForms = true,\n    // enableScripting = false,\n    // hasJSActionsPromise = null,\n    // fieldObjectsPromise = null,\n    annotationCanvasMap = null,\n    accessibilityManager = null,\n    annotationEditorUIManager = null,\n    onAppend = null\n  }) {\n    _AnnotationLayerBuilder_instances.add(this);\n    // todo: props ported from pdf.js\n    this.annotationLayer = null;\n    this.pdfPage = null;\n    this.linkService = null;\n    this.annotationStorage = null;\n    this._annotationCanvasMap = null;\n    this._annotationEditorUIManager = null;\n    this.div = null;\n    this._cancelled = null;\n    this._eventBus = null;\n    this._accessibilityManager = null;\n    // todo: props ported from pdf.js\n    _AnnotationLayerBuilder_onAppend.set(this, null);\n    _AnnotationLayerBuilder_eventAbortController.set(this, null);\n    this.renderForms = null;\n    this.pdfPage = pdfPage;\n    this.linkService = linkService;\n    // this.downloadManager = downloadManager;\n    // this.imageResourcesPath = imageResourcesPath;\n    this.renderForms = renderForms;\n    this.annotationStorage = annotationStorage;\n    // this.enableScripting = enableScripting;\n    // this._hasJSActionsPromise = hasJSActionsPromise || Promise.resolve(false);\n    // this._fieldObjectsPromise = fieldObjectsPromise || Promise.resolve(null);\n    this._annotationCanvasMap = annotationCanvasMap;\n    this._accessibilityManager = accessibilityManager;\n    this._annotationEditorUIManager = annotationEditorUIManager;\n    __classPrivateFieldSet(this, _AnnotationLayerBuilder_onAppend, onAppend, \"f\");\n    this.annotationLayer = null;\n    this.div = null;\n    this._cancelled = false;\n    this._eventBus = (linkService === null || linkService === void 0 ? void 0 : linkService.eventBus) || eventBus;\n  }\n  /**\n   * @param {PageViewport} viewport\n   * @param {string} intent (default value is 'display')\n   * @returns {Promise<void>} A promise that is resolved when rendering of the\n   *   annotations is complete.\n   */\n  render(viewport_1) {\n    return __awaiter(this, arguments, void 0, function* (viewport, intent = \"display\") {\n      var _a, _b, _c;\n      if (this.div) {\n        if (this._cancelled || !this.annotationLayer) {\n          return;\n        }\n        // If an annotationLayer already exists, refresh its children's\n        // transformation matrices.\n        this.annotationLayer.update({\n          viewport: viewport.clone({\n            dontFlip: true\n          })\n        });\n        return;\n      }\n      // const [annotations, hasJSActions, fieldObjects] = await Promise.all([\n      const [annotations] = yield Promise.all([this.pdfPage.getAnnotations({\n        intent\n      })\n      // this._hasJSActionsPromise,\n      // this._fieldObjectsPromise,\n      ]);\n      if (this._cancelled) {\n        return;\n      }\n      // Create an annotation layer div and render the annotations\n      // if there is at least one annotation.\n      // const div = (this.div = document.createElement(\"div\"));\n      // div.className = \"annotationLayer\";\n      // this.#onAppend?.(div);\n      if (annotations.length === 0) {\n        this.hide();\n        return;\n      }\n      const page = this.pdfPage;\n      const pageView = (_a = page._pageInfo) === null || _a === void 0 ? void 0 : _a.view;\n      const pageWidthAnnotationLayer = (pageView[2] || 0) + \"px\";\n      const pageHeightAnnotationLayer = (pageView[3] || 0) + \"px\";\n      const div = convertToHtml(`\n            <div class=\"k-annotation-layer\"\n                 data-main-rotation=\"0\"\n                 style=\"width: round(var(--scale-factor) * ${pageWidthAnnotationLayer}, 1px);\n                        height: round(var(--scale-factor) * ${pageHeightAnnotationLayer}, 1px);\">\n            </div>\n        `);\n      this.div = div;\n      (_b = __classPrivateFieldGet(this, _AnnotationLayerBuilder_onAppend, \"f\")) === null || _b === void 0 ? void 0 : _b.call(this, div);\n      this.annotationLayer = new AnnotationLayer({\n        div,\n        // accessibilityManager: this._accessibilityManager,\n        accessibilityManager: null,\n        annotationCanvasMap: this._annotationCanvasMap,\n        annotationEditorUIManager: this._annotationEditorUIManager,\n        page: this.pdfPage,\n        viewport: viewport.clone({\n          dontFlip: true\n        })\n      });\n      yield this.annotationLayer.render({\n        annotations,\n        // imageResourcesPath: this.imageResourcesPath,\n        renderForms: this.renderForms,\n        linkService: this.linkService\n        // downloadManager: this.downloadManager,\n        // annotationStorage: this.annotationStorage,\n        // enableScripting: this.enableScripting,\n        // hasJSActions,\n        // fieldObjects,\n      });\n      // Ensure that interactive form elements in the annotationLayer are\n      // disabled while PresentationMode is active (see issue 12232).\n      if (this.linkService.isInPresentationMode) {\n        __classPrivateFieldGet(this, _AnnotationLayerBuilder_instances, \"m\", _AnnotationLayerBuilder_updatePresentationModeState).call(this, PresentationModeState.FULLSCREEN);\n      }\n      if (!__classPrivateFieldGet(this, _AnnotationLayerBuilder_eventAbortController, \"f\")) {\n        __classPrivateFieldSet(this, _AnnotationLayerBuilder_eventAbortController, new AbortController(), \"f\");\n        (_c = this._eventBus) === null || _c === void 0 ? void 0 : _c._on(\"presentationmodechanged\", evt => {\n          __classPrivateFieldGet(this, _AnnotationLayerBuilder_instances, \"m\", _AnnotationLayerBuilder_updatePresentationModeState).call(this, evt.state);\n        }, {\n          signal: __classPrivateFieldGet(this, _AnnotationLayerBuilder_eventAbortController, \"f\").signal\n        });\n      }\n    });\n  }\n  cancel() {\n    var _a;\n    this._cancelled = true;\n    (_a = __classPrivateFieldGet(this, _AnnotationLayerBuilder_eventAbortController, \"f\")) === null || _a === void 0 ? void 0 : _a.abort();\n    __classPrivateFieldSet(this, _AnnotationLayerBuilder_eventAbortController, null, \"f\");\n  }\n  hide() {\n    if (!this.div) {\n      return;\n    }\n    this.div.hidden = true;\n  }\n  hasEditableAnnotations() {\n    var _a;\n    return !!((_a = this.annotationLayer) === null || _a === void 0 ? void 0 : _a.hasEditableAnnotations());\n  }\n}\n_AnnotationLayerBuilder_onAppend = new WeakMap(), _AnnotationLayerBuilder_eventAbortController = new WeakMap(), _AnnotationLayerBuilder_instances = new WeakSet(), _AnnotationLayerBuilder_updatePresentationModeState = function _AnnotationLayerBuilder_updatePresentationModeState(state) {\n  if (!this.div) {\n    return;\n  }\n  let disableFormElements = false;\n  switch (state) {\n    case PresentationModeState.FULLSCREEN:\n      disableFormElements = true;\n      break;\n    case PresentationModeState.NORMAL:\n      break;\n    default:\n      return;\n  }\n  for (const section of this.div.childNodes) {\n    if (section.hasAttribute(\"data-internal-link\")) {\n      continue;\n    }\n    section.inert = disableFormElements;\n  }\n};\nexport { AnnotationLayerBuilder };", "map": {"version": 3, "names": ["_AnnotationLayerBuilder_instances", "_AnnotationLayerBuilder_onAppend", "_AnnotationLayerBuilder_eventAbortController", "_AnnotationLayerBuilder_updatePresentationModeState", "__awaiter", "__classPrivateFieldGet", "__classPrivateFieldSet", "convertToHtml", "Annotation<PERSON><PERSON>er", "PresentationModeState", "AnnotationLayerBuilder", "constructor", "pdfPage", "eventBus", "linkService", "annotationStorage", "renderForms", "annotationCanvasMap", "accessibilityManager", "annotationEditorUIManager", "onAppend", "add", "annotationLayer", "_annotationCanvasMap", "_annotationEditorUIManager", "div", "_cancelled", "_eventBus", "_accessibilityManager", "set", "render", "viewport_1", "arguments", "viewport", "intent", "_a", "_b", "_c", "update", "clone", "dont<PERSON><PERSON>", "annotations", "Promise", "all", "getAnnotations", "length", "hide", "page", "pageView", "_pageInfo", "view", "pageWidthAnnotationLayer", "pageHeightAnnotationLayer", "call", "isInPresentationMode", "FULLSCREEN", "AbortController", "_on", "evt", "state", "signal", "cancel", "abort", "hidden", "hasEditableAnnotations", "WeakMap", "WeakSet", "disableFormElements", "NORMAL", "section", "childNodes", "hasAttribute", "inert"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-pdfviewer-common/dist/es/annotations/annotation-layer-builder.js"], "sourcesContent": ["/* Copyright 2014 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar _AnnotationLayerBuilder_instances, _AnnotationLayerBuilder_onAppend, _AnnotationLayerBuilder_eventAbortController, _AnnotationLayerBuilder_updatePresentationModeState;\nimport { __awaiter, __classPrivateFieldGet, __classPrivateFieldSet } from \"tslib\";\n/** @typedef {import(\"../src/display/api\").PDFPageProxy} PDFPageProxy */\n/** @typedef {import(\"../src/display/display_utils\").PageViewport} PageViewport */\n/** @typedef {import(\"../src/display/annotation_storage\").AnnotationStorage} AnnotationStorage */\n/** @typedef {import(\"./interfaces\").IDownloadManager} IDownloadManager */\n/** @typedef {import(\"./interfaces\").IPDFLinkService} IPDFLinkService */\n/** @typedef {import(\"./text_accessibility.js\").TextAccessibilityManager} TextAccessibilityManager */\n/** @typedef {import(\"../src/display/editor/tools.js\").AnnotationEditorUIManager} AnnotationEditorUIManager */\nimport { convertToHtml } from \"../common/core\";\nimport { AnnotationLayer } from \"./annotation-layer\";\nimport { PresentationModeState } from \"./shared/ui_utils\";\n// import { PresentationModeState } from \"./shared/ui_utils\";\n/**\n * @typedef {Object} AnnotationLayerBuilderOptions\n * @property {PDFPageProxy} pdfPage\n * @property {AnnotationStorage} [annotationStorage]\n * @property {string} [imageResourcesPath] - Path for image resources, mainly\n *   for annotation icons. Include trailing slash.\n * @property {boolean} renderForms\n * @property {IPDFLinkService} linkService\n * @property {IDownloadManager} [downloadManager]\n * @property {boolean} [enableScripting]\n * @property {Promise<boolean>} [hasJSActionsPromise]\n * @property {Promise<Object<string, Array<Object>> | null>}\n *   [fieldObjectsPromise]\n * @property {Map<string, HTMLCanvasElement>} [annotationCanvasMap]\n * @property {TextAccessibilityManager} [accessibilityManager]\n * @property {AnnotationEditorUIManager} [annotationEditorUIManager]\n * @property {function} [onAppend]\n */\nclass AnnotationLayerBuilder {\n    /**\n     * @param {AnnotationLayerBuilderOptions} options\n     */\n    constructor({ pdfPage, eventBus, linkService = null, \n    // downloadManager,\n    annotationStorage = null, \n    // imageResourcesPath = \"\",\n    renderForms = true, \n    // enableScripting = false,\n    // hasJSActionsPromise = null,\n    // fieldObjectsPromise = null,\n    annotationCanvasMap = null, accessibilityManager = null, annotationEditorUIManager = null, onAppend = null }) {\n        _AnnotationLayerBuilder_instances.add(this);\n        // todo: props ported from pdf.js\n        this.annotationLayer = null;\n        this.pdfPage = null;\n        this.linkService = null;\n        this.annotationStorage = null;\n        this._annotationCanvasMap = null;\n        this._annotationEditorUIManager = null;\n        this.div = null;\n        this._cancelled = null;\n        this._eventBus = null;\n        this._accessibilityManager = null;\n        // todo: props ported from pdf.js\n        _AnnotationLayerBuilder_onAppend.set(this, null);\n        _AnnotationLayerBuilder_eventAbortController.set(this, null);\n        this.renderForms = null;\n        this.pdfPage = pdfPage;\n        this.linkService = linkService;\n        // this.downloadManager = downloadManager;\n        // this.imageResourcesPath = imageResourcesPath;\n        this.renderForms = renderForms;\n        this.annotationStorage = annotationStorage;\n        // this.enableScripting = enableScripting;\n        // this._hasJSActionsPromise = hasJSActionsPromise || Promise.resolve(false);\n        // this._fieldObjectsPromise = fieldObjectsPromise || Promise.resolve(null);\n        this._annotationCanvasMap = annotationCanvasMap;\n        this._accessibilityManager = accessibilityManager;\n        this._annotationEditorUIManager = annotationEditorUIManager;\n        __classPrivateFieldSet(this, _AnnotationLayerBuilder_onAppend, onAppend, \"f\");\n        this.annotationLayer = null;\n        this.div = null;\n        this._cancelled = false;\n        this._eventBus = (linkService === null || linkService === void 0 ? void 0 : linkService.eventBus) || eventBus;\n    }\n    /**\n     * @param {PageViewport} viewport\n     * @param {string} intent (default value is 'display')\n     * @returns {Promise<void>} A promise that is resolved when rendering of the\n     *   annotations is complete.\n     */\n    render(viewport_1) {\n        return __awaiter(this, arguments, void 0, function* (viewport, intent = \"display\") {\n            var _a, _b, _c;\n            if (this.div) {\n                if (this._cancelled || !this.annotationLayer) {\n                    return;\n                }\n                // If an annotationLayer already exists, refresh its children's\n                // transformation matrices.\n                this.annotationLayer.update({\n                    viewport: viewport.clone({ dontFlip: true })\n                });\n                return;\n            }\n            // const [annotations, hasJSActions, fieldObjects] = await Promise.all([\n            const [annotations] = yield Promise.all([\n                this.pdfPage.getAnnotations({ intent })\n                // this._hasJSActionsPromise,\n                // this._fieldObjectsPromise,\n            ]);\n            if (this._cancelled) {\n                return;\n            }\n            // Create an annotation layer div and render the annotations\n            // if there is at least one annotation.\n            // const div = (this.div = document.createElement(\"div\"));\n            // div.className = \"annotationLayer\";\n            // this.#onAppend?.(div);\n            if (annotations.length === 0) {\n                this.hide();\n                return;\n            }\n            const page = this.pdfPage;\n            const pageView = (_a = page._pageInfo) === null || _a === void 0 ? void 0 : _a.view;\n            const pageWidthAnnotationLayer = (pageView[2] || 0) + \"px\";\n            const pageHeightAnnotationLayer = (pageView[3] || 0) + \"px\";\n            const div = convertToHtml(`\n            <div class=\"k-annotation-layer\"\n                 data-main-rotation=\"0\"\n                 style=\"width: round(var(--scale-factor) * ${pageWidthAnnotationLayer}, 1px);\n                        height: round(var(--scale-factor) * ${pageHeightAnnotationLayer}, 1px);\">\n            </div>\n        `);\n            this.div = div;\n            (_b = __classPrivateFieldGet(this, _AnnotationLayerBuilder_onAppend, \"f\")) === null || _b === void 0 ? void 0 : _b.call(this, div);\n            this.annotationLayer = new AnnotationLayer({\n                div,\n                // accessibilityManager: this._accessibilityManager,\n                accessibilityManager: null,\n                annotationCanvasMap: this._annotationCanvasMap,\n                annotationEditorUIManager: this._annotationEditorUIManager,\n                page: this.pdfPage,\n                viewport: viewport.clone({ dontFlip: true })\n            });\n            yield this.annotationLayer.render({\n                annotations,\n                // imageResourcesPath: this.imageResourcesPath,\n                renderForms: this.renderForms,\n                linkService: this.linkService\n                // downloadManager: this.downloadManager,\n                // annotationStorage: this.annotationStorage,\n                // enableScripting: this.enableScripting,\n                // hasJSActions,\n                // fieldObjects,\n            });\n            // Ensure that interactive form elements in the annotationLayer are\n            // disabled while PresentationMode is active (see issue 12232).\n            if (this.linkService.isInPresentationMode) {\n                __classPrivateFieldGet(this, _AnnotationLayerBuilder_instances, \"m\", _AnnotationLayerBuilder_updatePresentationModeState).call(this, PresentationModeState.FULLSCREEN);\n            }\n            if (!__classPrivateFieldGet(this, _AnnotationLayerBuilder_eventAbortController, \"f\")) {\n                __classPrivateFieldSet(this, _AnnotationLayerBuilder_eventAbortController, new AbortController(), \"f\");\n                (_c = this._eventBus) === null || _c === void 0 ? void 0 : _c._on(\"presentationmodechanged\", evt => {\n                    __classPrivateFieldGet(this, _AnnotationLayerBuilder_instances, \"m\", _AnnotationLayerBuilder_updatePresentationModeState).call(this, evt.state);\n                }, { signal: __classPrivateFieldGet(this, _AnnotationLayerBuilder_eventAbortController, \"f\").signal });\n            }\n        });\n    }\n    cancel() {\n        var _a;\n        this._cancelled = true;\n        (_a = __classPrivateFieldGet(this, _AnnotationLayerBuilder_eventAbortController, \"f\")) === null || _a === void 0 ? void 0 : _a.abort();\n        __classPrivateFieldSet(this, _AnnotationLayerBuilder_eventAbortController, null, \"f\");\n    }\n    hide() {\n        if (!this.div) {\n            return;\n        }\n        this.div.hidden = true;\n    }\n    hasEditableAnnotations() {\n        var _a;\n        return !!((_a = this.annotationLayer) === null || _a === void 0 ? void 0 : _a.hasEditableAnnotations());\n    }\n}\n_AnnotationLayerBuilder_onAppend = new WeakMap(), _AnnotationLayerBuilder_eventAbortController = new WeakMap(), _AnnotationLayerBuilder_instances = new WeakSet(), _AnnotationLayerBuilder_updatePresentationModeState = function _AnnotationLayerBuilder_updatePresentationModeState(state) {\n    if (!this.div) {\n        return;\n    }\n    let disableFormElements = false;\n    switch (state) {\n        case PresentationModeState.FULLSCREEN:\n            disableFormElements = true;\n            break;\n        case PresentationModeState.NORMAL:\n            break;\n        default:\n            return;\n    }\n    for (const section of this.div.childNodes) {\n        if (section.hasAttribute(\"data-internal-link\")) {\n            continue;\n        }\n        section.inert = disableFormElements;\n    }\n};\nexport { AnnotationLayerBuilder };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,iCAAiC,EAAEC,gCAAgC,EAAEC,4CAA4C,EAAEC,mDAAmD;AAC1K,SAASC,SAAS,EAAEC,sBAAsB,EAAEC,sBAAsB,QAAQ,OAAO;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,qBAAqB,QAAQ,mBAAmB;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,CAAC;EACzB;AACJ;AACA;EACIC,WAAWA,CAAC;IAAEC,OAAO;IAAEC,QAAQ;IAAEC,WAAW,GAAG,IAAI;IACnD;IACAC,iBAAiB,GAAG,IAAI;IACxB;IACAC,WAAW,GAAG,IAAI;IAClB;IACA;IACA;IACAC,mBAAmB,GAAG,IAAI;IAAEC,oBAAoB,GAAG,IAAI;IAAEC,yBAAyB,GAAG,IAAI;IAAEC,QAAQ,GAAG;EAAK,CAAC,EAAE;IAC1GpB,iCAAiC,CAACqB,GAAG,CAAC,IAAI,CAAC;IAC3C;IACA,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACV,OAAO,GAAG,IAAI;IACnB,IAAI,CAACE,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACQ,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACC,0BAA0B,GAAG,IAAI;IACtC,IAAI,CAACC,GAAG,GAAG,IAAI;IACf,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,qBAAqB,GAAG,IAAI;IACjC;IACA3B,gCAAgC,CAAC4B,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAChD3B,4CAA4C,CAAC2B,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAC5D,IAAI,CAACb,WAAW,GAAG,IAAI;IACvB,IAAI,CAACJ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,WAAW,GAAGA,WAAW;IAC9B;IACA;IACA,IAAI,CAACE,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACD,iBAAiB,GAAGA,iBAAiB;IAC1C;IACA;IACA;IACA,IAAI,CAACQ,oBAAoB,GAAGN,mBAAmB;IAC/C,IAAI,CAACW,qBAAqB,GAAGV,oBAAoB;IACjD,IAAI,CAACM,0BAA0B,GAAGL,yBAAyB;IAC3Db,sBAAsB,CAAC,IAAI,EAAEL,gCAAgC,EAAEmB,QAAQ,EAAE,GAAG,CAAC;IAC7E,IAAI,CAACE,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACG,GAAG,GAAG,IAAI;IACf,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,SAAS,GAAG,CAACb,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACD,QAAQ,KAAKA,QAAQ;EACjH;EACA;AACJ;AACA;AACA;AACA;AACA;EACIiB,MAAMA,CAACC,UAAU,EAAE;IACf,OAAO3B,SAAS,CAAC,IAAI,EAAE4B,SAAS,EAAE,KAAK,CAAC,EAAE,WAAWC,QAAQ,EAAEC,MAAM,GAAG,SAAS,EAAE;MAC/E,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE;MACd,IAAI,IAAI,CAACZ,GAAG,EAAE;QACV,IAAI,IAAI,CAACC,UAAU,IAAI,CAAC,IAAI,CAACJ,eAAe,EAAE;UAC1C;QACJ;QACA;QACA;QACA,IAAI,CAACA,eAAe,CAACgB,MAAM,CAAC;UACxBL,QAAQ,EAAEA,QAAQ,CAACM,KAAK,CAAC;YAAEC,QAAQ,EAAE;UAAK,CAAC;QAC/C,CAAC,CAAC;QACF;MACJ;MACA;MACA,MAAM,CAACC,WAAW,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACpC,IAAI,CAAC/B,OAAO,CAACgC,cAAc,CAAC;QAAEV;MAAO,CAAC;MACtC;MACA;MAAA,CACH,CAAC;MACF,IAAI,IAAI,CAACR,UAAU,EAAE;QACjB;MACJ;MACA;MACA;MACA;MACA;MACA;MACA,IAAIe,WAAW,CAACI,MAAM,KAAK,CAAC,EAAE;QAC1B,IAAI,CAACC,IAAI,CAAC,CAAC;QACX;MACJ;MACA,MAAMC,IAAI,GAAG,IAAI,CAACnC,OAAO;MACzB,MAAMoC,QAAQ,GAAG,CAACb,EAAE,GAAGY,IAAI,CAACE,SAAS,MAAM,IAAI,IAAId,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACe,IAAI;MACnF,MAAMC,wBAAwB,GAAG,CAACH,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI;MAC1D,MAAMI,yBAAyB,GAAG,CAACJ,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI;MAC3D,MAAMvB,GAAG,GAAGlB,aAAa,CAAC;AACtC;AACA;AACA,6DAA6D4C,wBAAwB;AACrF,8DAA8DC,yBAAyB;AACvF;AACA,SAAS,CAAC;MACE,IAAI,CAAC3B,GAAG,GAAGA,GAAG;MACd,CAACW,EAAE,GAAG/B,sBAAsB,CAAC,IAAI,EAAEJ,gCAAgC,EAAE,GAAG,CAAC,MAAM,IAAI,IAAImC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiB,IAAI,CAAC,IAAI,EAAE5B,GAAG,CAAC;MAClI,IAAI,CAACH,eAAe,GAAG,IAAId,eAAe,CAAC;QACvCiB,GAAG;QACH;QACAP,oBAAoB,EAAE,IAAI;QAC1BD,mBAAmB,EAAE,IAAI,CAACM,oBAAoB;QAC9CJ,yBAAyB,EAAE,IAAI,CAACK,0BAA0B;QAC1DuB,IAAI,EAAE,IAAI,CAACnC,OAAO;QAClBqB,QAAQ,EAAEA,QAAQ,CAACM,KAAK,CAAC;UAAEC,QAAQ,EAAE;QAAK,CAAC;MAC/C,CAAC,CAAC;MACF,MAAM,IAAI,CAAClB,eAAe,CAACQ,MAAM,CAAC;QAC9BW,WAAW;QACX;QACAzB,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BF,WAAW,EAAE,IAAI,CAACA;QAClB;QACA;QACA;QACA;QACA;MACJ,CAAC,CAAC;MACF;MACA;MACA,IAAI,IAAI,CAACA,WAAW,CAACwC,oBAAoB,EAAE;QACvCjD,sBAAsB,CAAC,IAAI,EAAEL,iCAAiC,EAAE,GAAG,EAAEG,mDAAmD,CAAC,CAACkD,IAAI,CAAC,IAAI,EAAE5C,qBAAqB,CAAC8C,UAAU,CAAC;MAC1K;MACA,IAAI,CAAClD,sBAAsB,CAAC,IAAI,EAAEH,4CAA4C,EAAE,GAAG,CAAC,EAAE;QAClFI,sBAAsB,CAAC,IAAI,EAAEJ,4CAA4C,EAAE,IAAIsD,eAAe,CAAC,CAAC,EAAE,GAAG,CAAC;QACtG,CAACnB,EAAE,GAAG,IAAI,CAACV,SAAS,MAAM,IAAI,IAAIU,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACoB,GAAG,CAAC,yBAAyB,EAAEC,GAAG,IAAI;UAChGrD,sBAAsB,CAAC,IAAI,EAAEL,iCAAiC,EAAE,GAAG,EAAEG,mDAAmD,CAAC,CAACkD,IAAI,CAAC,IAAI,EAAEK,GAAG,CAACC,KAAK,CAAC;QACnJ,CAAC,EAAE;UAAEC,MAAM,EAAEvD,sBAAsB,CAAC,IAAI,EAAEH,4CAA4C,EAAE,GAAG,CAAC,CAAC0D;QAAO,CAAC,CAAC;MAC1G;IACJ,CAAC,CAAC;EACN;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI1B,EAAE;IACN,IAAI,CAACT,UAAU,GAAG,IAAI;IACtB,CAACS,EAAE,GAAG9B,sBAAsB,CAAC,IAAI,EAAEH,4CAA4C,EAAE,GAAG,CAAC,MAAM,IAAI,IAAIiC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC2B,KAAK,CAAC,CAAC;IACtIxD,sBAAsB,CAAC,IAAI,EAAEJ,4CAA4C,EAAE,IAAI,EAAE,GAAG,CAAC;EACzF;EACA4C,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAACrB,GAAG,EAAE;MACX;IACJ;IACA,IAAI,CAACA,GAAG,CAACsC,MAAM,GAAG,IAAI;EAC1B;EACAC,sBAAsBA,CAAA,EAAG;IACrB,IAAI7B,EAAE;IACN,OAAO,CAAC,EAAE,CAACA,EAAE,GAAG,IAAI,CAACb,eAAe,MAAM,IAAI,IAAIa,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6B,sBAAsB,CAAC,CAAC,CAAC;EAC3G;AACJ;AACA/D,gCAAgC,GAAG,IAAIgE,OAAO,CAAC,CAAC,EAAE/D,4CAA4C,GAAG,IAAI+D,OAAO,CAAC,CAAC,EAAEjE,iCAAiC,GAAG,IAAIkE,OAAO,CAAC,CAAC,EAAE/D,mDAAmD,GAAG,SAASA,mDAAmDA,CAACwD,KAAK,EAAE;EACzR,IAAI,CAAC,IAAI,CAAClC,GAAG,EAAE;IACX;EACJ;EACA,IAAI0C,mBAAmB,GAAG,KAAK;EAC/B,QAAQR,KAAK;IACT,KAAKlD,qBAAqB,CAAC8C,UAAU;MACjCY,mBAAmB,GAAG,IAAI;MAC1B;IACJ,KAAK1D,qBAAqB,CAAC2D,MAAM;MAC7B;IACJ;MACI;EACR;EACA,KAAK,MAAMC,OAAO,IAAI,IAAI,CAAC5C,GAAG,CAAC6C,UAAU,EAAE;IACvC,IAAID,OAAO,CAACE,YAAY,CAAC,oBAAoB,CAAC,EAAE;MAC5C;IACJ;IACAF,OAAO,CAACG,KAAK,GAAGL,mBAAmB;EACvC;AACJ,CAAC;AACD,SAASzD,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}