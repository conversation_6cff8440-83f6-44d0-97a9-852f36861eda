{"ast": null, "code": "var reComment = /\\/\\*[\\s\\S]*?\\*\\//g;\n/*\n([^\\s:;]+?)\\s*:\\s*         # Property name and colon\n(\n  (?:\n    (?:                    # Begin alternation\n      url\\(                # Match 'url('\n        \\s*\n        (?:\n          (?:[^\"')\\\\]|\\\\.)*  # Content inside url(), excluding quotes and closing parenthesis\n          |\n          \"(?:[^\"\\\\]|\\\\.)*\"  # Double-quoted strings, handling escaped characters\n          |\n          '(?:[^'\\\\]|\\\\.)*'  # Single-quoted strings, handling escaped characters\n        )\n        \\s*\n      \\)\n      |\n      \"(?:[^\"\\\\]|\\\\.)*\"     # Double-quoted strings, handling escaped characters\n      |\n      '(?:[^'\\\\]|\\\\.)*'     # Single-quoted strings, handling escaped characters\n      |\n      [^;\"']                # Any character except ';', double or single quotes\n    )*?                     # Repeat zero or more times, non-greedy\n    \\s*\n  )\n)\n(?=;|$)                     # Lookahead for ';' or end of string\n*/\nvar reDeclaration = /([^\\s:;]+?)\\s*:\\s*((?:(?:url\\(\\s*(?:(?:[^\"')\\\\]|\\\\.)*|\"(?:[^\"\\\\]|\\\\.)*\"|'(?:[^'\\\\]|\\\\.)*')\\s*\\)|\"(?:[^\"\\\\]|\\\\.)*\"|'(?:[^'\\\\]|\\\\.)*'|[^;\"'])*?)\\s*)(?=;|$)/gi;\nvar reDoubleQuoted = /&quot;|&#34;|&#x22;/gi;\nvar reSingleQuoted = /&apos;|&#39;|&#x27;/gi;\nvar doubleQuote = '\"';\nvar singleQuote = \"'\";\nvar empty = '';\nfunction replaceQuoteEntities(str) {\n  return str.replace(reDoubleQuoted, doubleQuote).replace(reSingleQuoted, singleQuote);\n}\n/**\n * Parse inline styles string into object.\n *\n * @param styleString - inline styles string\n * @returns object with styles\n */\nexport function parseInlineStyles(styleString) {\n  var styleObject = {};\n  var input = replaceQuoteEntities((styleString || empty).replace(reComment, empty));\n  var match = reDeclaration.exec(input),\n    property,\n    value;\n  while (match !== null) {\n    property = match[1].trim();\n    value = match[2].trim();\n    styleObject[property] = value;\n    match = reDeclaration.exec(input);\n  }\n  return styleObject;\n}", "map": {"version": 3, "names": ["reComment", "reDeclaration", "reDoubleQuoted", "reSingleQuoted", "doubleQuote", "singleQuote", "empty", "replaceQuoteEntities", "str", "replace", "parseInlineStyles", "styleString", "styleObject", "input", "match", "exec", "property", "value", "trim"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-common/dist/es/parse-style.js"], "sourcesContent": ["var reComment = /\\/\\*[\\s\\S]*?\\*\\//g;\n/*\n([^\\s:;]+?)\\s*:\\s*         # Property name and colon\n(\n  (?:\n    (?:                    # Begin alternation\n      url\\(                # Match 'url('\n        \\s*\n        (?:\n          (?:[^\"')\\\\]|\\\\.)*  # Content inside url(), excluding quotes and closing parenthesis\n          |\n          \"(?:[^\"\\\\]|\\\\.)*\"  # Double-quoted strings, handling escaped characters\n          |\n          '(?:[^'\\\\]|\\\\.)*'  # Single-quoted strings, handling escaped characters\n        )\n        \\s*\n      \\)\n      |\n      \"(?:[^\"\\\\]|\\\\.)*\"     # Double-quoted strings, handling escaped characters\n      |\n      '(?:[^'\\\\]|\\\\.)*'     # Single-quoted strings, handling escaped characters\n      |\n      [^;\"']                # Any character except ';', double or single quotes\n    )*?                     # Repeat zero or more times, non-greedy\n    \\s*\n  )\n)\n(?=;|$)                     # Lookahead for ';' or end of string\n*/\nvar reDeclaration = /([^\\s:;]+?)\\s*:\\s*((?:(?:url\\(\\s*(?:(?:[^\"')\\\\]|\\\\.)*|\"(?:[^\"\\\\]|\\\\.)*\"|'(?:[^'\\\\]|\\\\.)*')\\s*\\)|\"(?:[^\"\\\\]|\\\\.)*\"|'(?:[^'\\\\]|\\\\.)*'|[^;\"'])*?)\\s*)(?=;|$)/gi;\nvar reDoubleQuoted = /&quot;|&#34;|&#x22;/gi;\nvar reSingleQuoted = /&apos;|&#39;|&#x27;/gi;\nvar doubleQuote = '\"';\nvar singleQuote = \"'\";\nvar empty = '';\nfunction replaceQuoteEntities(str) {\n    return str.replace(reDoubleQuoted, doubleQuote)\n        .replace(reSingleQuoted, singleQuote);\n}\n/**\n * Parse inline styles string into object.\n *\n * @param styleString - inline styles string\n * @returns object with styles\n */\nexport function parseInlineStyles(styleString) {\n    var styleObject = {};\n    var input = replaceQuoteEntities((styleString || empty).replace(reComment, empty));\n    var match = reDeclaration.exec(input), property, value;\n    while (match !== null) {\n        property = match[1].trim();\n        value = match[2].trim();\n        styleObject[property] = value;\n        match = reDeclaration.exec(input);\n    }\n    return styleObject;\n}\n"], "mappings": "AAAA,IAAIA,SAAS,GAAG,mBAAmB;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,aAAa,GAAG,6JAA6J;AACjL,IAAIC,cAAc,GAAG,uBAAuB;AAC5C,IAAIC,cAAc,GAAG,uBAAuB;AAC5C,IAAIC,WAAW,GAAG,GAAG;AACrB,IAAIC,WAAW,GAAG,GAAG;AACrB,IAAIC,KAAK,GAAG,EAAE;AACd,SAASC,oBAAoBA,CAACC,GAAG,EAAE;EAC/B,OAAOA,GAAG,CAACC,OAAO,CAACP,cAAc,EAAEE,WAAW,CAAC,CAC1CK,OAAO,CAACN,cAAc,EAAEE,WAAW,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASK,iBAAiBA,CAACC,WAAW,EAAE;EAC3C,IAAIC,WAAW,GAAG,CAAC,CAAC;EACpB,IAAIC,KAAK,GAAGN,oBAAoB,CAAC,CAACI,WAAW,IAAIL,KAAK,EAAEG,OAAO,CAACT,SAAS,EAAEM,KAAK,CAAC,CAAC;EAClF,IAAIQ,KAAK,GAAGb,aAAa,CAACc,IAAI,CAACF,KAAK,CAAC;IAAEG,QAAQ;IAAEC,KAAK;EACtD,OAAOH,KAAK,KAAK,IAAI,EAAE;IACnBE,QAAQ,GAAGF,KAAK,CAAC,CAAC,CAAC,CAACI,IAAI,CAAC,CAAC;IAC1BD,KAAK,GAAGH,KAAK,CAAC,CAAC,CAAC,CAACI,IAAI,CAAC,CAAC;IACvBN,WAAW,CAACI,QAAQ,CAAC,GAAGC,KAAK;IAC7BH,KAAK,GAAGb,aAAa,CAACc,IAAI,CAACF,KAAK,CAAC;EACrC;EACA,OAAOD,WAAW;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}