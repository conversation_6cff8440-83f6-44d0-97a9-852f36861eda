{"ast": null, "code": "import { cloneDate } from './clone-date';\n/**\n * @hidden\n */\nexport var adjustDST = function (date, hour) {\n  var newDate = cloneDate(date);\n  if (hour === 0 && newDate.getHours() === 23) {\n    newDate.setHours(newDate.getHours() + 2);\n  }\n  return newDate;\n};", "map": {"version": 3, "names": ["cloneDate", "adjustDST", "date", "hour", "newDate", "getHours", "setHours"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/adjust-dst.js"], "sourcesContent": ["import { cloneDate } from './clone-date';\n/**\n * @hidden\n */\nexport var adjustDST = function (date, hour) {\n    var newDate = cloneDate(date);\n    if (hour === 0 && newDate.getHours() === 23) {\n        newDate.setHours(newDate.getHours() + 2);\n    }\n    return newDate;\n};\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,cAAc;AACxC;AACA;AACA;AACA,OAAO,IAAIC,SAAS,GAAG,SAAAA,CAAUC,IAAI,EAAEC,IAAI,EAAE;EACzC,IAAIC,OAAO,GAAGJ,SAAS,CAACE,IAAI,CAAC;EAC7B,IAAIC,IAAI,KAAK,CAAC,IAAIC,OAAO,CAACC,QAAQ,CAAC,CAAC,KAAK,EAAE,EAAE;IACzCD,OAAO,CAACE,QAAQ,CAACF,OAAO,CAACC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;EAC5C;EACA,OAAOD,OAAO;AAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}