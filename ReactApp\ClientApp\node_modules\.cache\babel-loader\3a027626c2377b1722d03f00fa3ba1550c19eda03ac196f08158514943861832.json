{"ast": null, "code": "import ResizeObserver from 'resize-observer-polyfill';\n// =============================== Const ===============================\nvar elementListeners = new Map();\nfunction onResize(entities) {\n  entities.forEach(function (entity) {\n    var _elementListeners$get;\n    var target = entity.target;\n    (_elementListeners$get = elementListeners.get(target)) === null || _elementListeners$get === void 0 || _elementListeners$get.forEach(function (listener) {\n      return listener(target);\n    });\n  });\n}\n\n// Note: ResizeObserver polyfill not support option to measure border-box resize\nvar resizeObserver = new ResizeObserver(onResize);\n\n// Dev env only\nexport var _el = process.env.NODE_ENV !== 'production' ? elementListeners : null; // eslint-disable-line\nexport var _rs = process.env.NODE_ENV !== 'production' ? onResize : null; // eslint-disable-line\n\n// ============================== Observe ==============================\nexport function observe(element, callback) {\n  if (!elementListeners.has(element)) {\n    elementListeners.set(element, new Set());\n    resizeObserver.observe(element);\n  }\n  elementListeners.get(element).add(callback);\n}\nexport function unobserve(element, callback) {\n  if (elementListeners.has(element)) {\n    elementListeners.get(element).delete(callback);\n    if (!elementListeners.get(element).size) {\n      resizeObserver.unobserve(element);\n      elementListeners.delete(element);\n    }\n  }\n}", "map": {"version": 3, "names": ["ResizeObserver", "elementListeners", "Map", "onResize", "entities", "for<PERSON>ach", "entity", "_elementListeners$get", "target", "get", "listener", "resizeObserver", "_el", "process", "env", "NODE_ENV", "_rs", "observe", "element", "callback", "has", "set", "Set", "add", "unobserve", "delete", "size"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/rc-resize-observer/es/utils/observerUtil.js"], "sourcesContent": ["import ResizeObserver from 'resize-observer-polyfill';\n// =============================== Const ===============================\nvar elementListeners = new Map();\nfunction onResize(entities) {\n  entities.forEach(function (entity) {\n    var _elementListeners$get;\n    var target = entity.target;\n    (_elementListeners$get = elementListeners.get(target)) === null || _elementListeners$get === void 0 || _elementListeners$get.forEach(function (listener) {\n      return listener(target);\n    });\n  });\n}\n\n// Note: ResizeObserver polyfill not support option to measure border-box resize\nvar resizeObserver = new ResizeObserver(onResize);\n\n// Dev env only\nexport var _el = process.env.NODE_ENV !== 'production' ? elementListeners : null; // eslint-disable-line\nexport var _rs = process.env.NODE_ENV !== 'production' ? onResize : null; // eslint-disable-line\n\n// ============================== Observe ==============================\nexport function observe(element, callback) {\n  if (!elementListeners.has(element)) {\n    elementListeners.set(element, new Set());\n    resizeObserver.observe(element);\n  }\n  elementListeners.get(element).add(callback);\n}\nexport function unobserve(element, callback) {\n  if (elementListeners.has(element)) {\n    elementListeners.get(element).delete(callback);\n    if (!elementListeners.get(element).size) {\n      resizeObserver.unobserve(element);\n      elementListeners.delete(element);\n    }\n  }\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0BAA0B;AACrD;AACA,IAAIC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;AAChC,SAASC,QAAQA,CAACC,QAAQ,EAAE;EAC1BA,QAAQ,CAACC,OAAO,CAAC,UAAUC,MAAM,EAAE;IACjC,IAAIC,qBAAqB;IACzB,IAAIC,MAAM,GAAGF,MAAM,CAACE,MAAM;IAC1B,CAACD,qBAAqB,GAAGN,gBAAgB,CAACQ,GAAG,CAACD,MAAM,CAAC,MAAM,IAAI,IAAID,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACF,OAAO,CAAC,UAAUK,QAAQ,EAAE;MACvJ,OAAOA,QAAQ,CAACF,MAAM,CAAC;IACzB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;;AAEA;AACA,IAAIG,cAAc,GAAG,IAAIX,cAAc,CAACG,QAAQ,CAAC;;AAEjD;AACA,OAAO,IAAIS,GAAG,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGd,gBAAgB,GAAG,IAAI,CAAC,CAAC;AAClF,OAAO,IAAIe,GAAG,GAAGH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGZ,QAAQ,GAAG,IAAI,CAAC,CAAC;;AAE1E;AACA,OAAO,SAASc,OAAOA,CAACC,OAAO,EAAEC,QAAQ,EAAE;EACzC,IAAI,CAAClB,gBAAgB,CAACmB,GAAG,CAACF,OAAO,CAAC,EAAE;IAClCjB,gBAAgB,CAACoB,GAAG,CAACH,OAAO,EAAE,IAAII,GAAG,CAAC,CAAC,CAAC;IACxCX,cAAc,CAACM,OAAO,CAACC,OAAO,CAAC;EACjC;EACAjB,gBAAgB,CAACQ,GAAG,CAACS,OAAO,CAAC,CAACK,GAAG,CAACJ,QAAQ,CAAC;AAC7C;AACA,OAAO,SAASK,SAASA,CAACN,OAAO,EAAEC,QAAQ,EAAE;EAC3C,IAAIlB,gBAAgB,CAACmB,GAAG,CAACF,OAAO,CAAC,EAAE;IACjCjB,gBAAgB,CAACQ,GAAG,CAACS,OAAO,CAAC,CAACO,MAAM,CAACN,QAAQ,CAAC;IAC9C,IAAI,CAAClB,gBAAgB,CAACQ,GAAG,CAACS,OAAO,CAAC,CAACQ,IAAI,EAAE;MACvCf,cAAc,CAACa,SAAS,CAACN,OAAO,CAAC;MACjCjB,gBAAgB,CAACwB,MAAM,CAACP,OAAO,CAAC;IAClC;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}