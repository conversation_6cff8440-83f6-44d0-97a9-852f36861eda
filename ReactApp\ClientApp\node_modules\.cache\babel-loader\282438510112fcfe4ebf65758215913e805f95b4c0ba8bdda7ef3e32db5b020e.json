{"ast": null, "code": "import \"antd/es/spin/style\";\nimport _Spin from \"antd/es/spin\";\nvar _jsxFileName = \"D:\\\\Zone24x7\\\\Workspaces\\\\FrontEnd-Portal\\\\ReactApp\\\\ClientApp\\\\src\\\\app\\\\components\\\\TermsAndConditions\\\\index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { PDFViewer } from '@progress/kendo-react-pdf-viewer';\nimport { Button } from '@progress/kendo-react-buttons';\nimport styles from './index.module.less';\nimport { FiRefreshCw, FiDownload, FiPrinter } from 'react-icons/fi';\nimport { useTermsAndConditions } from '@app/hooks/useTermsAndConditions';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TermsAndConditions = () => {\n  _s();\n  const {\n    tncDocument,\n    pdfViewerRef,\n    tncLoading,\n    tncRefetch,\n    isAccepting,\n    onDocumentLoad,\n    handleAgree,\n    clickToolbarButtonByTitle\n  } = useTermsAndConditions();\n  const [pdfBlobUrl, setPdfBlobUrl] = useState(\"\");\n  useEffect(() => {\n    const fetchPdfWithAuth = async () => {\n      try {\n        const response = await fetch(\"https://usermanagement.sandbox.irispme.com/NBT1/api/Users/<USER>/1/download\", {\n          headers: {\n            Authorization: `Bearer ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(\"Failed to fetch PDF\");\n        }\n        const blob = await response.blob();\n        const blobUrl = URL.createObjectURL(blob);\n        setPdfBlobUrl(blobUrl);\n      } catch (error) {\n        console.error(\"Error fetching PDF:\", error);\n      }\n    };\n    fetchPdfWithAuth();\n\n    // Cleanup the object URL when component unmounts\n    return () => {\n      if (pdfBlobUrl) {\n        URL.revokeObjectURL(pdfBlobUrl);\n      }\n    };\n  }, []);\n  if (!tncDocument || !tncDocument.document) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: styles.dialogMainContainer,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.pdfViewContainer,\n      children: /*#__PURE__*/_jsxDEV(PDFViewer, {\n        url: pdfBlobUrl,\n        onLoad: onDocumentLoad,\n        ref: pdfViewerRef,\n        tools: ['pager', 'print', 'download', 'selection', 'zoomInOut'],\n        style: {\n          height: '100%'\n        },\n        zoom: 0.9\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.agreement_text,\n      children: tncDocument.document.statementOfAgreement\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.dialogActionsBar,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.dialogActionsBar_left,\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          startIcon: tncLoading ? /*#__PURE__*/_jsxDEV(_Spin, {\n            size: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 37\n          }, this) : /*#__PURE__*/_jsxDEV(FiRefreshCw, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 61\n          }, this),\n          disabled: tncLoading,\n          themeColor: \"primary\",\n          fillMode: \"outline\",\n          onClick: tncRefetch,\n          children: \"Refresh\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          startIcon: /*#__PURE__*/_jsxDEV(FiDownload, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 24\n          }, this),\n          themeColor: \"primary\",\n          fillMode: \"outline\",\n          onClick: () => clickToolbarButtonByTitle('Download'),\n          disabled: tncLoading,\n          children: \"Download\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          startIcon: /*#__PURE__*/_jsxDEV(FiPrinter, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 24\n          }, this),\n          disabled: tncLoading,\n          themeColor: \"primary\",\n          fillMode: \"outline\",\n          onClick: () => clickToolbarButtonByTitle('Print'),\n          children: \"Print\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        themeColor: \"primary\",\n        fillMode: \"solid\",\n        onClick: handleAgree,\n        disabled: isAccepting || tncLoading,\n        startIcon: isAccepting ? /*#__PURE__*/_jsxDEV(_Spin, {\n          size: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 36\n        }, this) : undefined,\n        children: \"AGREE & CONTINUE\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this);\n};\n_s(TermsAndConditions, \"ZKbFPByo6MUo5fQs2974lxDAaC0=\", false, function () {\n  return [useTermsAndConditions];\n});\n_c = TermsAndConditions;\nexport default TermsAndConditions;\nvar _c;\n$RefreshReg$(_c, \"TermsAndConditions\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "PDFViewer", "<PERSON><PERSON>", "styles", "FiRefreshCw", "FiDownload", "FiPrinter", "useTermsAndConditions", "jsxDEV", "_jsxDEV", "TermsAndConditions", "_s", "tncDocument", "pdfViewerRef", "tncLoading", "tncRefetch", "isAccepting", "onDocumentLoad", "handleAgree", "clickToolbarButtonByTitle", "pdfBlobUrl", "setPdfBlobUrl", "fetchPdfWithAuth", "response", "fetch", "headers", "Authorization", "ok", "Error", "blob", "blobUrl", "URL", "createObjectURL", "error", "console", "revokeObjectURL", "document", "className", "dialogMainContainer", "children", "pdfViewContainer", "url", "onLoad", "ref", "tools", "style", "height", "zoom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "agreement_text", "statementOfAgreement", "dialogActionsBar", "dialogActionsBar_left", "startIcon", "_Spin", "size", "disabled", "themeColor", "fillMode", "onClick", "undefined", "_c", "$RefreshReg$"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/src/app/components/TermsAndConditions/index.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { PDFViewer } from '@progress/kendo-react-pdf-viewer';\r\nimport { Button } from '@progress/kendo-react-buttons';\r\nimport styles from './index.module.less';\r\nimport { FiRefreshCw, FiDownload, FiPrinter } from 'react-icons/fi';\r\nimport { useTermsAndConditions } from '@app/hooks/useTermsAndConditions';\r\nimport { Spin } from 'antd';\r\n\r\nconst TermsAndConditions: React.FC = () => {\r\n  const {\r\n    tncDocument,\r\n    pdfViewerRef,\r\n    tncLoading,\r\n    tncRefetch,\r\n    isAccepting,\r\n    onDocumentLoad,\r\n    handleAgree,\r\n    clickToolbarButtonByTitle\r\n  } = useTermsAndConditions();\r\n const [pdfBlobUrl, setPdfBlobUrl] = useState<string>(\"\");\r\n\r\n     useEffect(() => {\r\n    const fetchPdfWithAuth = async () => {\r\n      try {\r\n        const response = await fetch(\r\n          \"https://usermanagement.sandbox.irispme.com/NBT1/api/Users/<USER>/1/download\",\r\n          {\r\n            headers: {\r\n              Authorization: `Bearer ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`,\r\n            },\r\n          }\r\n        );\r\n\r\n        if (!response.ok) {\r\n          throw new Error(\"Failed to fetch PDF\");\r\n        }\r\n\r\n        const blob = await response.blob();\r\n        const blobUrl = URL.createObjectURL(blob);\r\n        setPdfBlobUrl(blobUrl);\r\n      } catch (error) {\r\n        console.error(\"Error fetching PDF:\", error);\r\n      }\r\n    };\r\n\r\n    fetchPdfWithAuth();\r\n\r\n    // Cleanup the object URL when component unmounts\r\n    return () => {\r\n      if (pdfBlobUrl) {\r\n        URL.revokeObjectURL(pdfBlobUrl);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  if (!tncDocument || !tncDocument.document) return null;\r\n\r\n  return (\r\n    <div className={styles.dialogMainContainer}>\r\n      <div className={styles.pdfViewContainer}>\r\n        <PDFViewer\r\n          url={pdfBlobUrl}\r\n          onLoad={onDocumentLoad}\r\n          ref={pdfViewerRef}\r\n          tools={['pager', 'print', 'download', 'selection', 'zoomInOut']}\r\n          style={{ height: '100%' }}\r\n          zoom={0.9}\r\n        />\r\n      </div>\r\n\r\n      <div className={styles.agreement_text}>\r\n        {tncDocument.document.statementOfAgreement}\r\n      </div>\r\n\r\n      <div className={styles.dialogActionsBar}>\r\n        <div className={styles.dialogActionsBar_left}>\r\n          <Button\r\n            startIcon={tncLoading ? <Spin size='small' /> : <FiRefreshCw />}\r\n            disabled={tncLoading}\r\n            themeColor=\"primary\"\r\n            fillMode=\"outline\"\r\n            onClick={tncRefetch}\r\n          >\r\n            Refresh\r\n          </Button>\r\n\r\n          <Button\r\n            startIcon={<FiDownload />}\r\n            themeColor=\"primary\"\r\n            fillMode=\"outline\"\r\n            onClick={() => clickToolbarButtonByTitle('Download')}\r\n            disabled={tncLoading}\r\n          >\r\n            Download\r\n          </Button>\r\n\r\n          <Button\r\n            startIcon={<FiPrinter />}\r\n            disabled={tncLoading}\r\n            themeColor=\"primary\"\r\n            fillMode=\"outline\"\r\n            onClick={() => clickToolbarButtonByTitle('Print')}\r\n          >\r\n            Print\r\n          </Button>\r\n        </div>\r\n\r\n        <Button\r\n          themeColor=\"primary\"\r\n          fillMode=\"solid\"\r\n          onClick={handleAgree}\r\n          disabled={isAccepting || tncLoading}\r\n          startIcon={isAccepting ? <Spin size='small' /> : undefined}\r\n        >\r\n          AGREE & CONTINUE\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TermsAndConditions;\r\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,SAASC,MAAM,QAAQ,+BAA+B;AACtD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,SAASC,WAAW,EAAEC,UAAU,EAAEC,SAAS,QAAQ,gBAAgB;AACnE,SAASC,qBAAqB,QAAQ,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGzE,MAAMC,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM;IACJC,WAAW;IACXC,YAAY;IACZC,UAAU;IACVC,UAAU;IACVC,WAAW;IACXC,cAAc;IACdC,WAAW;IACXC;EACF,CAAC,GAAGZ,qBAAqB,CAAC,CAAC;EAC5B,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAS,EAAE,CAAC;EAEpDD,SAAS,CAAC,MAAM;IACjB,MAAMuB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAC1B,2FAA2F,EAC3F;UACEC,OAAO,EAAE;YACPC,aAAa,EAAE;UACjB;QACF,CACF,CAAC;QAED,IAAI,CAACH,QAAQ,CAACI,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,qBAAqB,CAAC;QACxC;QAEA,MAAMC,IAAI,GAAG,MAAMN,QAAQ,CAACM,IAAI,CAAC,CAAC;QAClC,MAAMC,OAAO,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;QACzCR,aAAa,CAACS,OAAO,CAAC;MACxB,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC7C;IACF,CAAC;IAEDX,gBAAgB,CAAC,CAAC;;IAElB;IACA,OAAO,MAAM;MACX,IAAIF,UAAU,EAAE;QACdW,GAAG,CAACI,eAAe,CAACf,UAAU,CAAC;MACjC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,IAAI,CAACR,WAAW,IAAI,CAACA,WAAW,CAACwB,QAAQ,EAAE,OAAO,IAAI;EAEtD,oBACE3B,OAAA;IAAK4B,SAAS,EAAElC,MAAM,CAACmC,mBAAoB;IAAAC,QAAA,gBACzC9B,OAAA;MAAK4B,SAAS,EAAElC,MAAM,CAACqC,gBAAiB;MAAAD,QAAA,eACtC9B,OAAA,CAACR,SAAS;QACRwC,GAAG,EAAErB,UAAW;QAChBsB,MAAM,EAAEzB,cAAe;QACvB0B,GAAG,EAAE9B,YAAa;QAClB+B,KAAK,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,CAAE;QAChEC,KAAK,EAAE;UAAEC,MAAM,EAAE;QAAO,CAAE;QAC1BC,IAAI,EAAE;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN1C,OAAA;MAAK4B,SAAS,EAAElC,MAAM,CAACiD,cAAe;MAAAb,QAAA,EACnC3B,WAAW,CAACwB,QAAQ,CAACiB;IAAoB;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC,eAEN1C,OAAA;MAAK4B,SAAS,EAAElC,MAAM,CAACmD,gBAAiB;MAAAf,QAAA,gBACtC9B,OAAA;QAAK4B,SAAS,EAAElC,MAAM,CAACoD,qBAAsB;QAAAhB,QAAA,gBAC3C9B,OAAA,CAACP,MAAM;UACLsD,SAAS,EAAE1C,UAAU,gBAAGL,OAAA,CAAAgD,KAAA;YAAMC,IAAI,EAAC;UAAO;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG1C,OAAA,CAACL,WAAW;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAChEQ,QAAQ,EAAE7C,UAAW;UACrB8C,UAAU,EAAC,SAAS;UACpBC,QAAQ,EAAC,SAAS;UAClBC,OAAO,EAAE/C,UAAW;UAAAwB,QAAA,EACrB;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET1C,OAAA,CAACP,MAAM;UACLsD,SAAS,eAAE/C,OAAA,CAACJ,UAAU;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BS,UAAU,EAAC,SAAS;UACpBC,QAAQ,EAAC,SAAS;UAClBC,OAAO,EAAEA,CAAA,KAAM3C,yBAAyB,CAAC,UAAU,CAAE;UACrDwC,QAAQ,EAAE7C,UAAW;UAAAyB,QAAA,EACtB;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET1C,OAAA,CAACP,MAAM;UACLsD,SAAS,eAAE/C,OAAA,CAACH,SAAS;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBQ,QAAQ,EAAE7C,UAAW;UACrB8C,UAAU,EAAC,SAAS;UACpBC,QAAQ,EAAC,SAAS;UAClBC,OAAO,EAAEA,CAAA,KAAM3C,yBAAyB,CAAC,OAAO,CAAE;UAAAoB,QAAA,EACnD;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN1C,OAAA,CAACP,MAAM;QACL0D,UAAU,EAAC,SAAS;QACpBC,QAAQ,EAAC,OAAO;QAChBC,OAAO,EAAE5C,WAAY;QACrByC,QAAQ,EAAE3C,WAAW,IAAIF,UAAW;QACpC0C,SAAS,EAAExC,WAAW,gBAAGP,OAAA,CAAAgD,KAAA;UAAMC,IAAI,EAAC;QAAO;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GAAGY,SAAU;QAAAxB,QAAA,EAC5D;MAED;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxC,EAAA,CA/GID,kBAA4B;EAAA,QAU5BH,qBAAqB;AAAA;AAAAyD,EAAA,GAVrBtD,kBAA4B;AAiHlC,eAAeA,kBAAkB;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}