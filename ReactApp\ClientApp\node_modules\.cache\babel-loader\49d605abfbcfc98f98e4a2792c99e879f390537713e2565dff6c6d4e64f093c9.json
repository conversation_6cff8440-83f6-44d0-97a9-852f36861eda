{"ast": null, "code": "import { timezones } from './timezones';\nimport { ruleToDate } from './rule-to-date';\nvar CURRENT_UTC_TIME = new Date().getTime();\n/**\n * @hidden\n *\n * A function that finds zone rules which become applicable after a specific time.\n *\n * @param timezone - The timezone name. For example, `America/Chicago`, `Europe/Sofia`.\n * @param utcTime - The UTC time boundary for a zone rule. Defaults to the current UTC time.\n *\n * @return - Returns a zone rule for the specific zone name.\n *\n * @example\n * ```ts-no-run\n * findZone('Europe/Sofia'); //[-120,\"EU\",\"EE%sT\",null]\n * ```\n */\nexport var findRule = function (zoneRule, utcTime, zoneOffset) {\n  if (utcTime === void 0) {\n    utcTime = CURRENT_UTC_TIME;\n  }\n  if (zoneOffset === void 0) {\n    zoneOffset = 0;\n  }\n  var rules = timezones.rules[zoneRule];\n  if (!rules) {\n    var time = zoneRule.split(\":\");\n    var offset = 0;\n    if (time.length > 1) {\n      offset = time[0] * 60 + Number(time[1]);\n    }\n    return [-1000000, 'max', '-', 'Jan', 1, [0, 0, 0], offset, '-'];\n  }\n  var year = new Date(utcTime).getUTCFullYear();\n  rules = rules.filter(function (currentRule) {\n    var from = currentRule[0];\n    var to = currentRule[1];\n    return from <= year && (to >= year || from === year && to === \"only\" || to === \"max\");\n  });\n  rules.push(utcTime);\n  rules.sort(function (a, b) {\n    if (typeof a !== \"number\") {\n      a = Number(ruleToDate(year, a, zoneOffset));\n    }\n    if (typeof b !== \"number\") {\n      b = Number(ruleToDate(year, b, zoneOffset));\n    }\n    return a - b;\n  });\n  var rule = rules[rules.indexOf(utcTime) - 1] || rules[rules.length - 1];\n  return isNaN(rule) ? rule : null;\n};", "map": {"version": 3, "names": ["timezones", "ruleToDate", "CURRENT_UTC_TIME", "Date", "getTime", "findRule", "zoneRule", "utcTime", "zoneOffset", "rules", "time", "split", "offset", "length", "Number", "year", "getUTCFullYear", "filter", "currentRule", "from", "to", "push", "sort", "a", "b", "rule", "indexOf", "isNaN"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/tz/find-rule.js"], "sourcesContent": ["import { timezones } from './timezones';\nimport { ruleToDate } from './rule-to-date';\nvar CURRENT_UTC_TIME = (new Date()).getTime();\n/**\n * @hidden\n *\n * A function that finds zone rules which become applicable after a specific time.\n *\n * @param timezone - The timezone name. For example, `America/Chicago`, `Europe/Sofia`.\n * @param utcTime - The UTC time boundary for a zone rule. Defaults to the current UTC time.\n *\n * @return - Returns a zone rule for the specific zone name.\n *\n * @example\n * ```ts-no-run\n * findZone('Europe/Sofia'); //[-120,\"EU\",\"EE%sT\",null]\n * ```\n */\nexport var findRule = function (zoneRule, utcTime, zoneOffset) {\n    if (utcTime === void 0) { utcTime = CURRENT_UTC_TIME; }\n    if (zoneOffset === void 0) { zoneOffset = 0; }\n    var rules = timezones.rules[zoneRule];\n    if (!rules) {\n        var time = zoneRule.split(\":\");\n        var offset = 0;\n        if (time.length > 1) {\n            offset = time[0] * 60 + Number(time[1]);\n        }\n        return [-1000000, 'max', '-', 'Jan', 1, [0, 0, 0], offset, '-'];\n    }\n    var year = new Date(utcTime).getUTCFullYear();\n    rules = rules.filter(function (currentRule) {\n        var from = currentRule[0];\n        var to = currentRule[1];\n        return from <= year && (to >= year || (from === year && to === \"only\") || to === \"max\");\n    });\n    rules.push(utcTime);\n    rules.sort(function (a, b) {\n        if (typeof a !== \"number\") {\n            a = Number(ruleToDate(year, a, zoneOffset));\n        }\n        if (typeof b !== \"number\") {\n            b = Number(ruleToDate(year, b, zoneOffset));\n        }\n        return a - b;\n    });\n    var rule = rules[rules.indexOf(utcTime) - 1] || rules[rules.length - 1];\n    return isNaN(rule) ? rule : null;\n};\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AACvC,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,IAAIC,gBAAgB,GAAI,IAAIC,IAAI,CAAC,CAAC,CAAEC,OAAO,CAAC,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,QAAQ,GAAG,SAAAA,CAAUC,QAAQ,EAAEC,OAAO,EAAEC,UAAU,EAAE;EAC3D,IAAID,OAAO,KAAK,KAAK,CAAC,EAAE;IAAEA,OAAO,GAAGL,gBAAgB;EAAE;EACtD,IAAIM,UAAU,KAAK,KAAK,CAAC,EAAE;IAAEA,UAAU,GAAG,CAAC;EAAE;EAC7C,IAAIC,KAAK,GAAGT,SAAS,CAACS,KAAK,CAACH,QAAQ,CAAC;EACrC,IAAI,CAACG,KAAK,EAAE;IACR,IAAIC,IAAI,GAAGJ,QAAQ,CAACK,KAAK,CAAC,GAAG,CAAC;IAC9B,IAAIC,MAAM,GAAG,CAAC;IACd,IAAIF,IAAI,CAACG,MAAM,GAAG,CAAC,EAAE;MACjBD,MAAM,GAAGF,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,GAAGI,MAAM,CAACJ,IAAI,CAAC,CAAC,CAAC,CAAC;IAC3C;IACA,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAEE,MAAM,EAAE,GAAG,CAAC;EACnE;EACA,IAAIG,IAAI,GAAG,IAAIZ,IAAI,CAACI,OAAO,CAAC,CAACS,cAAc,CAAC,CAAC;EAC7CP,KAAK,GAAGA,KAAK,CAACQ,MAAM,CAAC,UAAUC,WAAW,EAAE;IACxC,IAAIC,IAAI,GAAGD,WAAW,CAAC,CAAC,CAAC;IACzB,IAAIE,EAAE,GAAGF,WAAW,CAAC,CAAC,CAAC;IACvB,OAAOC,IAAI,IAAIJ,IAAI,KAAKK,EAAE,IAAIL,IAAI,IAAKI,IAAI,KAAKJ,IAAI,IAAIK,EAAE,KAAK,MAAO,IAAIA,EAAE,KAAK,KAAK,CAAC;EAC3F,CAAC,CAAC;EACFX,KAAK,CAACY,IAAI,CAACd,OAAO,CAAC;EACnBE,KAAK,CAACa,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IACvB,IAAI,OAAOD,CAAC,KAAK,QAAQ,EAAE;MACvBA,CAAC,GAAGT,MAAM,CAACb,UAAU,CAACc,IAAI,EAAEQ,CAAC,EAAEf,UAAU,CAAC,CAAC;IAC/C;IACA,IAAI,OAAOgB,CAAC,KAAK,QAAQ,EAAE;MACvBA,CAAC,GAAGV,MAAM,CAACb,UAAU,CAACc,IAAI,EAAES,CAAC,EAAEhB,UAAU,CAAC,CAAC;IAC/C;IACA,OAAOe,CAAC,GAAGC,CAAC;EAChB,CAAC,CAAC;EACF,IAAIC,IAAI,GAAGhB,KAAK,CAACA,KAAK,CAACiB,OAAO,CAACnB,OAAO,CAAC,GAAG,CAAC,CAAC,IAAIE,KAAK,CAACA,KAAK,CAACI,MAAM,GAAG,CAAC,CAAC;EACvE,OAAOc,KAAK,CAACF,IAAI,CAAC,GAAGA,IAAI,GAAG,IAAI;AACpC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}