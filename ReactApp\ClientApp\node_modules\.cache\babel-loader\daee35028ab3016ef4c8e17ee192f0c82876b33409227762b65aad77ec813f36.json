{"ast": null, "code": "import GradientNode from './gradient-node';\nvar RadialGradientNode = function (GradientNode) {\n  function RadialGradientNode() {\n    GradientNode.apply(this, arguments);\n  }\n  if (GradientNode) RadialGradientNode.__proto__ = GradientNode;\n  RadialGradientNode.prototype = Object.create(GradientNode && GradientNode.prototype);\n  RadialGradientNode.prototype.constructor = RadialGradientNode;\n  RadialGradientNode.prototype.template = function template() {\n    return \"<radialGradient id='\" + this.id + \"' \" + this.renderCoordinates() + \">\" + this.renderChildren() + \"</radialGradient>\";\n  };\n  RadialGradientNode.prototype.mapCoordinates = function mapCoordinates() {\n    var srcElement = this.srcElement;\n    var center = srcElement.center();\n    var radius = srcElement.radius();\n    var attrs = [[\"cx\", center.x], [\"cy\", center.y], [\"r\", radius], this.mapSpace()];\n    return attrs;\n  };\n  return RadialGradientNode;\n}(GradientNode);\nexport default RadialGradientNode;", "map": {"version": 3, "names": ["GradientNode", "RadialGradientNode", "apply", "arguments", "__proto__", "prototype", "Object", "create", "constructor", "template", "id", "renderCoordinates", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mapCoordinates", "srcElement", "center", "radius", "attrs", "x", "y", "mapSpace"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/svg/radial-gradient-node.js"], "sourcesContent": ["import GradientNode from './gradient-node';\n\nvar RadialGradientNode = (function (GradientNode) {\n    function RadialGradientNode () {\n        GradientNode.apply(this, arguments);\n    }\n\n    if ( GradientNode ) RadialGradientNode.__proto__ = GradientNode;\n    RadialGradientNode.prototype = Object.create( GradientNode && GradientNode.prototype );\n    RadialGradientNode.prototype.constructor = RadialGradientNode;\n\n    RadialGradientNode.prototype.template = function template () {\n        return (\"<radialGradient id='\" + (this.id) + \"' \" + (this.renderCoordinates()) + \">\" + (this.renderChildren()) + \"</radialGradient>\");\n    };\n\n    RadialGradientNode.prototype.mapCoordinates = function mapCoordinates () {\n        var srcElement = this.srcElement;\n        var center = srcElement.center();\n        var radius = srcElement.radius();\n        var attrs = [\n            [ \"cx\", center.x ],\n            [ \"cy\", center.y ],\n            [ \"r\", radius ],\n            this.mapSpace()\n        ];\n        return attrs;\n    };\n\n    return RadialGradientNode;\n}(GradientNode));\n\nexport default RadialGradientNode;"], "mappings": "AAAA,OAAOA,YAAY,MAAM,iBAAiB;AAE1C,IAAIC,kBAAkB,GAAI,UAAUD,YAAY,EAAE;EAC9C,SAASC,kBAAkBA,CAAA,EAAI;IAC3BD,YAAY,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACvC;EAEA,IAAKH,YAAY,EAAGC,kBAAkB,CAACG,SAAS,GAAGJ,YAAY;EAC/DC,kBAAkB,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAEP,YAAY,IAAIA,YAAY,CAACK,SAAU,CAAC;EACtFJ,kBAAkB,CAACI,SAAS,CAACG,WAAW,GAAGP,kBAAkB;EAE7DA,kBAAkB,CAACI,SAAS,CAACI,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;IACzD,OAAQ,sBAAsB,GAAI,IAAI,CAACC,EAAG,GAAG,IAAI,GAAI,IAAI,CAACC,iBAAiB,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAACC,cAAc,CAAC,CAAE,GAAG,mBAAmB;EACxI,CAAC;EAEDX,kBAAkB,CAACI,SAAS,CAACQ,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAI;IACrE,IAAIC,UAAU,GAAG,IAAI,CAACA,UAAU;IAChC,IAAIC,MAAM,GAAGD,UAAU,CAACC,MAAM,CAAC,CAAC;IAChC,IAAIC,MAAM,GAAGF,UAAU,CAACE,MAAM,CAAC,CAAC;IAChC,IAAIC,KAAK,GAAG,CACR,CAAE,IAAI,EAAEF,MAAM,CAACG,CAAC,CAAE,EAClB,CAAE,IAAI,EAAEH,MAAM,CAACI,CAAC,CAAE,EAClB,CAAE,GAAG,EAAEH,MAAM,CAAE,EACf,IAAI,CAACI,QAAQ,CAAC,CAAC,CAClB;IACD,OAAOH,KAAK;EAChB,CAAC;EAED,OAAOhB,kBAAkB;AAC7B,CAAC,CAACD,YAAY,CAAE;AAEhB,eAAeC,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}