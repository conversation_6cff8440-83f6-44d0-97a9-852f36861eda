{"ast": null, "code": "import defaultData from './default-data';\nimport isString from '../common/is-string';\nimport { errors } from '../errors';\nfunction availableLocaleInfo(fullName, suffixes) {\n  var parts = fullName.split(\"-\");\n  var language = parts[0];\n  var script = parts[1];\n  var territory = parts[2];\n  return cldr[fullName] || suffixes.indexOf(territory) !== -1 && cldr[language + \"-\" + territory] || suffixes.indexOf(script) !== -1 && cldr[language + \"-\" + script] || cldr[language];\n}\nfunction localeFullName(language, suffixes) {\n  var likelySubtags = cldr.supplemental.likelySubtags;\n  for (var idx = 0; idx < suffixes.length; idx++) {\n    var name = likelySubtags[language + \"-\" + suffixes[idx]];\n    if (name) {\n      return name;\n    }\n  }\n  if (likelySubtags[language]) {\n    return likelySubtags[language];\n  }\n}\nexport var cldr = defaultData;\nexport function getLocaleInfo(locale) {\n  var info;\n  if (isString(locale)) {\n    info = localeInfo(locale);\n  } else {\n    info = locale;\n  }\n  return info;\n}\nexport function localeInfo(locale) {\n  if (cldr[locale]) {\n    return cldr[locale];\n  }\n  var likelySubtags = cldr.supplemental.likelySubtags;\n  if (likelySubtags) {\n    var parts = locale.split(\"-\");\n    var language = parts[0];\n    var suffixes = parts.slice(1);\n    var fullName = localeFullName(language, suffixes);\n    var info = fullName ? availableLocaleInfo(fullName, suffixes) : null;\n    if (info) {\n      return info;\n    }\n  }\n  throw errors.NoLocale.error(locale);\n}", "map": {"version": 3, "names": ["defaultData", "isString", "errors", "availableLocaleInfo", "fullName", "suffixes", "parts", "split", "language", "script", "territory", "cldr", "indexOf", "localeFullName", "likelySubtags", "supplemental", "idx", "length", "name", "getLocaleInfo", "locale", "info", "localeInfo", "slice", "NoLocale", "error"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-intl/dist/es/cldr/info.js"], "sourcesContent": ["import defaultData from './default-data';\nimport isString from '../common/is-string';\nimport { errors } from '../errors';\n\nfunction availableLocaleInfo(fullName, suffixes) {\n    var parts = fullName.split(\"-\");\n    var language = parts[0];\n    var script = parts[1];\n    var territory = parts[2];\n\n    return cldr[fullName] || (suffixes.indexOf(territory) !== -1 && cldr[language + \"-\" + territory]) || (suffixes.indexOf(script) !== -1 && cldr[language + \"-\" + script]) || cldr[language];\n}\n\nfunction localeFullName(language, suffixes) {\n    var likelySubtags = cldr.supplemental.likelySubtags;\n\n    for (var idx = 0; idx < suffixes.length; idx++) {\n        var name = likelySubtags[language + \"-\" + suffixes[idx ]];\n        if (name) {\n            return name;\n        }\n    }\n\n    if (likelySubtags[language]) {\n        return likelySubtags[language];\n    }\n}\n\nexport var cldr = defaultData;\n\nexport function getLocaleInfo(locale) {\n    var info;\n    if (isString(locale)) {\n        info = localeInfo(locale);\n    } else {\n        info = locale;\n    }\n    return info;\n}\n\nexport function localeInfo(locale) {\n    if (cldr[locale]) {\n        return cldr[locale];\n    }\n\n    var likelySubtags = cldr.supplemental.likelySubtags;\n    if (likelySubtags) {\n        var parts = locale.split(\"-\");\n        var language = parts[0];\n        var suffixes = parts.slice(1);\n        var fullName = localeFullName(language, suffixes);\n        var info = fullName ? availableLocaleInfo(fullName, suffixes) : null;\n        if (info) {\n            return info;\n        }\n    }\n\n    throw errors.NoLocale.error(locale);\n}\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,gBAAgB;AACxC,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C,SAASC,MAAM,QAAQ,WAAW;AAElC,SAASC,mBAAmBA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;EAC7C,IAAIC,KAAK,GAAGF,QAAQ,CAACG,KAAK,CAAC,GAAG,CAAC;EAC/B,IAAIC,QAAQ,GAAGF,KAAK,CAAC,CAAC,CAAC;EACvB,IAAIG,MAAM,GAAGH,KAAK,CAAC,CAAC,CAAC;EACrB,IAAII,SAAS,GAAGJ,KAAK,CAAC,CAAC,CAAC;EAExB,OAAOK,IAAI,CAACP,QAAQ,CAAC,IAAKC,QAAQ,CAACO,OAAO,CAACF,SAAS,CAAC,KAAK,CAAC,CAAC,IAAIC,IAAI,CAACH,QAAQ,GAAG,GAAG,GAAGE,SAAS,CAAE,IAAKL,QAAQ,CAACO,OAAO,CAACH,MAAM,CAAC,KAAK,CAAC,CAAC,IAAIE,IAAI,CAACH,QAAQ,GAAG,GAAG,GAAGC,MAAM,CAAE,IAAIE,IAAI,CAACH,QAAQ,CAAC;AAC7L;AAEA,SAASK,cAAcA,CAACL,QAAQ,EAAEH,QAAQ,EAAE;EACxC,IAAIS,aAAa,GAAGH,IAAI,CAACI,YAAY,CAACD,aAAa;EAEnD,KAAK,IAAIE,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGX,QAAQ,CAACY,MAAM,EAAED,GAAG,EAAE,EAAE;IAC5C,IAAIE,IAAI,GAAGJ,aAAa,CAACN,QAAQ,GAAG,GAAG,GAAGH,QAAQ,CAACW,GAAG,CAAE,CAAC;IACzD,IAAIE,IAAI,EAAE;MACN,OAAOA,IAAI;IACf;EACJ;EAEA,IAAIJ,aAAa,CAACN,QAAQ,CAAC,EAAE;IACzB,OAAOM,aAAa,CAACN,QAAQ,CAAC;EAClC;AACJ;AAEA,OAAO,IAAIG,IAAI,GAAGX,WAAW;AAE7B,OAAO,SAASmB,aAAaA,CAACC,MAAM,EAAE;EAClC,IAAIC,IAAI;EACR,IAAIpB,QAAQ,CAACmB,MAAM,CAAC,EAAE;IAClBC,IAAI,GAAGC,UAAU,CAACF,MAAM,CAAC;EAC7B,CAAC,MAAM;IACHC,IAAI,GAAGD,MAAM;EACjB;EACA,OAAOC,IAAI;AACf;AAEA,OAAO,SAASC,UAAUA,CAACF,MAAM,EAAE;EAC/B,IAAIT,IAAI,CAACS,MAAM,CAAC,EAAE;IACd,OAAOT,IAAI,CAACS,MAAM,CAAC;EACvB;EAEA,IAAIN,aAAa,GAAGH,IAAI,CAACI,YAAY,CAACD,aAAa;EACnD,IAAIA,aAAa,EAAE;IACf,IAAIR,KAAK,GAAGc,MAAM,CAACb,KAAK,CAAC,GAAG,CAAC;IAC7B,IAAIC,QAAQ,GAAGF,KAAK,CAAC,CAAC,CAAC;IACvB,IAAID,QAAQ,GAAGC,KAAK,CAACiB,KAAK,CAAC,CAAC,CAAC;IAC7B,IAAInB,QAAQ,GAAGS,cAAc,CAACL,QAAQ,EAAEH,QAAQ,CAAC;IACjD,IAAIgB,IAAI,GAAGjB,QAAQ,GAAGD,mBAAmB,CAACC,QAAQ,EAAEC,QAAQ,CAAC,GAAG,IAAI;IACpE,IAAIgB,IAAI,EAAE;MACN,OAAOA,IAAI;IACf;EACJ;EAEA,MAAMnB,MAAM,CAACsB,QAAQ,CAACC,KAAK,CAACL,MAAM,CAAC;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}