{"ast": null, "code": "import scrollPosition from './scroll-position';\nexport default function (element) {\n  if (element === (element.ownerDocument || {}).body) {\n    return scrollPosition(element);\n  }\n  return {\n    x: element.scrollLeft,\n    y: element.scrollTop\n  };\n}\n;", "map": {"version": 3, "names": ["scrollPosition", "element", "ownerDocument", "body", "x", "scrollLeft", "y", "scrollTop"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-popup-common/dist/es/element-scroll-position.js"], "sourcesContent": ["import scrollPosition from './scroll-position';\n\nexport default function (element) {\n    if (element === (element.ownerDocument || {}).body) {\n        return scrollPosition(element);\n    }\n\n    return {\n        x: element.scrollLeft,\n        y: element.scrollTop\n    };\n};\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,mBAAmB;AAE9C,eAAe,UAAUC,OAAO,EAAE;EAC9B,IAAIA,OAAO,KAAK,CAACA,OAAO,CAACC,aAAa,IAAI,CAAC,CAAC,EAAEC,IAAI,EAAE;IAChD,OAAOH,cAAc,CAACC,OAAO,CAAC;EAClC;EAEA,OAAO;IACHG,CAAC,EAAEH,OAAO,CAACI,UAAU;IACrBC,CAAC,EAAEL,OAAO,CAACM;EACf,CAAC;AACL;AAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}