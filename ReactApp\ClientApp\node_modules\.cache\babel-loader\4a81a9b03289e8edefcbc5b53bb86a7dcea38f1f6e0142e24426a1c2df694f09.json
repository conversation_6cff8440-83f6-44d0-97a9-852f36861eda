{"ast": null, "code": "import \"antd/es/spin/style\";\nimport _Spin from \"antd/es/spin\";\nvar _jsxFileName = \"D:\\\\Zone24x7\\\\Workspaces\\\\FrontEnd-Portal\\\\ReactApp\\\\ClientApp\\\\src\\\\app\\\\components\\\\TermsAndConditions\\\\index.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { PDFViewer } from '@progress/kendo-react-pdf-viewer';\nimport { Button } from '@progress/kendo-react-buttons';\nimport styles from './index.module.less';\nimport { FiDownload, FiPrinter, FiRefreshCw } from 'react-icons/fi';\nimport { useTermsAndConditions } from '@app/hooks/useTermsAndConditions';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TermsAndConditions = () => {\n  _s();\n  const {\n    tncDocument,\n    pdfViewerRef,\n    tncLoading,\n    pdfDocument,\n    setPdfDocument,\n    tncRefetch,\n    isAccepting,\n    onDocumentLoad,\n    handleAgree,\n    clickToolbarButtonByTitle\n  } = useTermsAndConditions();\n  if (!tncDocument || !tncDocument.document) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: styles.dialogMainContainer,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.pdfViewContainer,\n      children: /*#__PURE__*/_jsxDEV(PDFViewer, {\n        url: tncDocument.document.documentUrl,\n        onLoad: onDocumentLoad,\n        ref: pdfViewerRef,\n        tools: ['pager', 'print', 'download', 'selection', 'zoomInOut'],\n        style: {\n          height: '100%'\n        },\n        zoom: 0.9\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.agreement_text,\n      children: tncDocument.document.statementOfAgreement\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.dialogActionsBar,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.dialogActionsBar_left,\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          startIcon: tncLoading ? /*#__PURE__*/_jsxDEV(_Spin, {\n            size: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 37\n          }, this) : /*#__PURE__*/_jsxDEV(FiRefreshCw, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 61\n          }, this),\n          disabled: tncLoading,\n          themeColor: \"primary\",\n          fillMode: \"outline\",\n          onClick: tncRefetch,\n          children: \"Refresh\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          startIcon: /*#__PURE__*/_jsxDEV(FiDownload, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 24\n          }, this),\n          themeColor: \"primary\",\n          fillMode: \"outline\",\n          onClick: () => clickToolbarButtonByTitle('Download'),\n          disabled: tncLoading,\n          children: \"Download\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          startIcon: /*#__PURE__*/_jsxDEV(FiPrinter, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 24\n          }, this),\n          disabled: tncLoading,\n          themeColor: \"primary\",\n          fillMode: \"outline\",\n          onClick: () => clickToolbarButtonByTitle('Print'),\n          children: \"Print\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        themeColor: \"primary\",\n        fillMode: \"solid\",\n        onClick: handleAgree,\n        disabled: isAccepting || tncLoading,\n        startIcon: isAccepting ? /*#__PURE__*/_jsxDEV(_Spin, {\n          size: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 36\n        }, this) : undefined,\n        children: \"AGREE & CONTINUE\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n_s(TermsAndConditions, \"2oaydOSlQBhYVkEedjZmbwZzIzs=\", false, function () {\n  return [useTermsAndConditions];\n});\n_c = TermsAndConditions;\nexport default TermsAndConditions;\nvar _c;\n$RefreshReg$(_c, \"TermsAndConditions\");", "map": {"version": 3, "names": ["React", "PDFViewer", "<PERSON><PERSON>", "styles", "FiDownload", "FiPrinter", "FiRefreshCw", "useTermsAndConditions", "jsxDEV", "_jsxDEV", "TermsAndConditions", "_s", "tncDocument", "pdfViewerRef", "tncLoading", "pdfDocument", "setPdfDocument", "tncRefetch", "isAccepting", "onDocumentLoad", "handleAgree", "clickToolbarButtonByTitle", "document", "className", "dialogMainContainer", "children", "pdfViewContainer", "url", "documentUrl", "onLoad", "ref", "tools", "style", "height", "zoom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "agreement_text", "statementOfAgreement", "dialogActionsBar", "dialogActionsBar_left", "startIcon", "_Spin", "size", "disabled", "themeColor", "fillMode", "onClick", "undefined", "_c", "$RefreshReg$"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/src/app/components/TermsAndConditions/index.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { PDFViewer } from '@progress/kendo-react-pdf-viewer';\r\nimport { Button } from '@progress/kendo-react-buttons';\r\nimport styles from './index.module.less';\r\nimport { FiDownload, FiPrinter, FiRefreshCw } from 'react-icons/fi';\r\nimport { useTermsAndConditions } from '@app/hooks/useTermsAndConditions';\r\nimport { Spin } from 'antd';\r\n\r\nconst TermsAndConditions: React.FC = () => {\r\n  const {\r\n    tncDocument,\r\n    pdfViewerRef,\r\n    tncLoading,\r\n    pdfDocument,\r\n    setPdfDocument,\r\n    tncRefetch,\r\n    isAccepting,\r\n    onDocumentLoad,\r\n    handleAgree,\r\n    clickToolbarButtonByTitle\r\n  } = useTermsAndConditions();\r\n\r\n  if (!tncDocument || !tncDocument.document) return null;\r\n\r\n  return (\r\n    <div className={styles.dialogMainContainer}>\r\n      <div className={styles.pdfViewContainer}>\r\n        <PDFViewer\r\n          url={tncDocument.document.documentUrl}\r\n          onLoad={onDocumentLoad}\r\n          ref={pdfViewerRef}\r\n          tools={['pager', 'print', 'download', 'selection', 'zoomInOut']}\r\n          style={{ height: '100%' }}\r\n          zoom={0.9}\r\n        />\r\n      </div>\r\n\r\n      <div className={styles.agreement_text}>\r\n        {tncDocument.document.statementOfAgreement}\r\n      </div>\r\n\r\n      <div className={styles.dialogActionsBar}>\r\n        <div className={styles.dialogActionsBar_left}>\r\n          <Button\r\n            startIcon={tncLoading ? <Spin size='small' /> : <FiRefreshCw />}\r\n            disabled={tncLoading}\r\n            themeColor=\"primary\"\r\n            fillMode=\"outline\"\r\n            onClick={tncRefetch}\r\n          >\r\n            Refresh\r\n          </Button>\r\n\r\n          <Button\r\n            startIcon={<FiDownload />}\r\n            themeColor=\"primary\"\r\n            fillMode=\"outline\"\r\n            onClick={() => clickToolbarButtonByTitle('Download')}\r\n            disabled={tncLoading}\r\n          >\r\n            Download\r\n          </Button>\r\n\r\n          <Button\r\n            startIcon={<FiPrinter />}\r\n            disabled={tncLoading}\r\n            themeColor=\"primary\"\r\n            fillMode=\"outline\"\r\n            onClick={() => clickToolbarButtonByTitle('Print')}\r\n          >\r\n            Print\r\n          </Button>\r\n        </div>\r\n\r\n        <Button\r\n          themeColor=\"primary\"\r\n          fillMode=\"solid\"\r\n          onClick={handleAgree}\r\n          disabled={isAccepting || tncLoading}\r\n          startIcon={isAccepting ? <Spin size='small' /> : undefined}\r\n        >\r\n          AGREE & CONTINUE\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TermsAndConditions;\r\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,SAASC,MAAM,QAAQ,+BAA+B;AACtD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,SAASC,UAAU,EAAEC,SAAS,EAAEC,WAAW,QAAQ,gBAAgB;AACnE,SAASC,qBAAqB,QAAQ,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGzE,MAAMC,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM;IACJC,WAAW;IACXC,YAAY;IACZC,UAAU;IACVC,WAAW;IACXC,cAAc;IACdC,UAAU;IACVC,WAAW;IACXC,cAAc;IACdC,WAAW;IACXC;EACF,CAAC,GAAGd,qBAAqB,CAAC,CAAC;EAE3B,IAAI,CAACK,WAAW,IAAI,CAACA,WAAW,CAACU,QAAQ,EAAE,OAAO,IAAI;EAEtD,oBACEb,OAAA;IAAKc,SAAS,EAAEpB,MAAM,CAACqB,mBAAoB;IAAAC,QAAA,gBACzChB,OAAA;MAAKc,SAAS,EAAEpB,MAAM,CAACuB,gBAAiB;MAAAD,QAAA,eACtChB,OAAA,CAACR,SAAS;QACR0B,GAAG,EAAEf,WAAW,CAACU,QAAQ,CAACM,WAAY;QACtCC,MAAM,EAAEV,cAAe;QACvBW,GAAG,EAAEjB,YAAa;QAClBkB,KAAK,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,CAAE;QAChEC,KAAK,EAAE;UAAEC,MAAM,EAAE;QAAO,CAAE;QAC1BC,IAAI,EAAE;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN7B,OAAA;MAAKc,SAAS,EAAEpB,MAAM,CAACoC,cAAe;MAAAd,QAAA,EACnCb,WAAW,CAACU,QAAQ,CAACkB;IAAoB;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC,eAEN7B,OAAA;MAAKc,SAAS,EAAEpB,MAAM,CAACsC,gBAAiB;MAAAhB,QAAA,gBACtChB,OAAA;QAAKc,SAAS,EAAEpB,MAAM,CAACuC,qBAAsB;QAAAjB,QAAA,gBAC3ChB,OAAA,CAACP,MAAM;UACLyC,SAAS,EAAE7B,UAAU,gBAAGL,OAAA,CAAAmC,KAAA;YAAMC,IAAI,EAAC;UAAO;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG7B,OAAA,CAACH,WAAW;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAChEQ,QAAQ,EAAEhC,UAAW;UACrBiC,UAAU,EAAC,SAAS;UACpBC,QAAQ,EAAC,SAAS;UAClBC,OAAO,EAAEhC,UAAW;UAAAQ,QAAA,EACrB;QAED;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET7B,OAAA,CAACP,MAAM;UACLyC,SAAS,eAAElC,OAAA,CAACL,UAAU;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BS,UAAU,EAAC,SAAS;UACpBC,QAAQ,EAAC,SAAS;UAClBC,OAAO,EAAEA,CAAA,KAAM5B,yBAAyB,CAAC,UAAU,CAAE;UACrDyB,QAAQ,EAAEhC,UAAW;UAAAW,QAAA,EACtB;QAED;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET7B,OAAA,CAACP,MAAM;UACLyC,SAAS,eAAElC,OAAA,CAACJ,SAAS;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBQ,QAAQ,EAAEhC,UAAW;UACrBiC,UAAU,EAAC,SAAS;UACpBC,QAAQ,EAAC,SAAS;UAClBC,OAAO,EAAEA,CAAA,KAAM5B,yBAAyB,CAAC,OAAO,CAAE;UAAAI,QAAA,EACnD;QAED;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN7B,OAAA,CAACP,MAAM;QACL6C,UAAU,EAAC,SAAS;QACpBC,QAAQ,EAAC,OAAO;QAChBC,OAAO,EAAE7B,WAAY;QACrB0B,QAAQ,EAAE5B,WAAW,IAAIJ,UAAW;QACpC6B,SAAS,EAAEzB,WAAW,gBAAGT,OAAA,CAAAmC,KAAA;UAAMC,IAAI,EAAC;QAAO;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GAAGY,SAAU;QAAAzB,QAAA,EAC5D;MAED;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3B,EAAA,CA9EID,kBAA4B;EAAA,QAY5BH,qBAAqB;AAAA;AAAA4C,EAAA,GAZrBzC,kBAA4B;AAgFlC,eAAeA,kBAAkB;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}