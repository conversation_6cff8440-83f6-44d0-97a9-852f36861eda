{"ast": null, "code": "import createTransform from '../geometry/transform';\nexport default function fit(element, rect) {\n  var bbox = element.clippedBBox();\n  if (bbox) {\n    var elementSize = bbox.size;\n    var rectSize = rect.size;\n    if (rectSize.width < elementSize.width || rectSize.height < elementSize.height) {\n      var scale = Math.min(rectSize.width / elementSize.width, rectSize.height / elementSize.height);\n      var transform = element.transform() || createTransform();\n      transform.scale(scale, scale);\n      element.transform(transform);\n    }\n  }\n}", "map": {"version": 3, "names": ["createTransform", "fit", "element", "rect", "bbox", "clippedBBox", "elementSize", "size", "rectSize", "width", "height", "scale", "Math", "min", "transform"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/alignment/fit.js"], "sourcesContent": ["import createTransform from '../geometry/transform';\n\nexport default function fit(element, rect) {\n    var bbox = element.clippedBBox();\n    if (bbox) {\n        var elementSize = bbox.size;\n        var rectSize = rect.size;\n        if (rectSize.width < elementSize.width || rectSize.height < elementSize.height) {\n            var scale = Math.min(rectSize.width / elementSize.width, rectSize.height / elementSize.height);\n            var transform = element.transform() || createTransform();\n            transform.scale(scale, scale);\n            element.transform(transform);\n        }\n    }\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,uBAAuB;AAEnD,eAAe,SAASC,GAAGA,CAACC,OAAO,EAAEC,IAAI,EAAE;EACvC,IAAIC,IAAI,GAAGF,OAAO,CAACG,WAAW,CAAC,CAAC;EAChC,IAAID,IAAI,EAAE;IACN,IAAIE,WAAW,GAAGF,IAAI,CAACG,IAAI;IAC3B,IAAIC,QAAQ,GAAGL,IAAI,CAACI,IAAI;IACxB,IAAIC,QAAQ,CAACC,KAAK,GAAGH,WAAW,CAACG,KAAK,IAAID,QAAQ,CAACE,MAAM,GAAGJ,WAAW,CAACI,MAAM,EAAE;MAC5E,IAAIC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACL,QAAQ,CAACC,KAAK,GAAGH,WAAW,CAACG,KAAK,EAAED,QAAQ,CAACE,MAAM,GAAGJ,WAAW,CAACI,MAAM,CAAC;MAC9F,IAAII,SAAS,GAAGZ,OAAO,CAACY,SAAS,CAAC,CAAC,IAAId,eAAe,CAAC,CAAC;MACxDc,SAAS,CAACH,KAAK,CAACA,KAAK,EAAEA,KAAK,CAAC;MAC7BT,OAAO,CAACY,SAAS,CAACA,SAAS,CAAC;IAChC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}