{"ast": null, "code": "/* eslint-disable */\nimport { Result, ResultType } from './result';\nimport { Stream } from './stream';\nvar toArray = function (value) {\n  return (value || '').split('');\n};\nvar ESCAPE_CHARACTER = '\\\\';\n/**\n * @hidden\n */\nvar Parser = /** @class */function () {\n  function Parser(parse) {\n    this.parse = parse;\n  }\n  Parser.prototype.run = function (input, control) {\n    if (control === void 0) {\n      control = '';\n    }\n    if (input instanceof Stream) {\n      return this.parse(input);\n    } else {\n      return this.parse(new Stream(toArray(input), toArray(control)));\n    }\n  };\n  //map :: Functor f => f a ~> (a -> b) -> f b\n  Parser.prototype.map = function (f) {\n    var _this = this;\n    return new Parser(function (stream) {\n      return _this.parse(stream).map(f);\n    });\n  };\n  //chain :: Chain m => m a ~> (a -> m b) -> m b\n  Parser.prototype.chain = function (f) {\n    var _this = this;\n    return new Parser(function (stream) {\n      return _this.parse(stream).chain(function (v, s) {\n        return f(v).run(s);\n      });\n    });\n  };\n  Parser.prototype.isLiteral = function (c) {\n    return this.run(c).type === ResultType.Literal;\n  };\n  return Parser;\n}();\nexport { Parser };\n/**\n * @hidden\n */\nexport var mask = function (_a) {\n  var prompt = _a.prompt,\n    promptPlaceholder = _a.promptPlaceholder;\n  return function (rule) {\n    return new Parser(function (stream) {\n      while (!stream.eof()) {\n        var _a = stream.peek(),\n          char = _a.char,\n          control = _a.control;\n        if (char === control && control === prompt) {\n          stream.eat();\n          return new Result(prompt, stream, ResultType.Mask);\n        }\n        if (rule.test(char)) {\n          stream.eat();\n          return new Result(char, stream, ResultType.Mask);\n        }\n        if (char === promptPlaceholder) {\n          stream.eat();\n          return new Result(prompt, stream, ResultType.Mask);\n        }\n        stream.eat_input();\n      }\n      stream.eat();\n      return new Result(prompt, stream, ResultType.Mask);\n    });\n  };\n};\n/**\n * @hidden\n */\nexport var literal = function (_token) {\n  return new Parser(function (stream) {\n    //    let {char, control} = stream.peek();\n    var char = stream.peek().char;\n    if (char === _token) {\n      stream.eat();\n      return new Result(_token, stream, ResultType.Literal);\n    }\n    //    if (control === _token) {\n    //        while (!stream.eof() && char !== _token) {\n    //            stream.eat_input();\n    //            char = stream.peek().char;\n    //        }\n    //    }\n    //\n    //    if (control !== undefined) {\n    //        stream.eat();\n    //    }\n    return new Result(_token, stream, ResultType.Literal);\n  });\n};\n/**\n * @hidden\n */\nexport var unmask = function (prompt) {\n  return function (rule) {\n    return new Parser(function (stream) {\n      while (!stream.eof()) {\n        var _a = stream.peek(),\n          char = _a.char,\n          control = _a.control;\n        if (char === prompt && control === prompt) {\n          stream.eat();\n          return new Result(char, stream);\n        }\n        if (rule.test(char)) {\n          stream.eat();\n          return new Result(char, stream);\n        }\n        stream.eat_input();\n      }\n      stream.eat();\n      return new Result('', stream);\n    });\n  };\n};\n/**\n * @hidden\n */\nexport var unliteral = function (_token) {\n  return new Parser(function (stream) {\n    if (stream.eof()) {\n      return new Result('', stream);\n    }\n    var char = stream.peek().char;\n    if (char === _token) {\n      stream.eat();\n    }\n    return new Result(_token, stream);\n  });\n};\n/**\n * @hidden\n */\nexport var token = function (rules, creator) {\n  return new Parser(function (stream) {\n    var char = stream.next().char;\n    var rule = rules[char];\n    if (char === ESCAPE_CHARACTER) {\n      char = stream.next().char;\n      return new Result(creator.literal(char), stream);\n    }\n    if (!rule) {\n      return new Result(creator.literal(char), stream);\n    }\n    return new Result(creator.mask(rule), stream);\n  });\n};\n/**\n * @hidden\n */\nexport var rawMask = function (_a) {\n  var prompt = _a.prompt,\n    promptPlaceholder = _a.promptPlaceholder;\n  return new Parser(function (stream) {\n    var char = stream.next().char;\n    if (char === prompt) {\n      return new Result(promptPlaceholder, stream);\n    }\n    return new Result(char, stream);\n  });\n};\n/**\n * @hidden\n */\nexport var rawLiteral = function (includeLiterals) {\n  return new Parser(function (stream) {\n    var char = stream.next().char;\n    if (includeLiterals) {\n      return new Result(char, stream);\n    }\n    return new Result('', stream);\n  });\n};", "map": {"version": 3, "names": ["Result", "ResultType", "Stream", "toArray", "value", "split", "ESCAPE_CHARACTER", "<PERSON><PERSON><PERSON>", "parse", "prototype", "run", "input", "control", "map", "f", "_this", "stream", "chain", "v", "s", "isLiteral", "c", "type", "Literal", "mask", "_a", "prompt", "promptPlaceholder", "rule", "eof", "peek", "char", "eat", "Mask", "test", "eat_input", "literal", "_token", "unmask", "unliteral", "token", "rules", "creator", "next", "rawMask", "rawLiteral", "includeLiterals"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-inputs-common/dist/es/maskedtextbox/parsing/parsers.js"], "sourcesContent": ["/* eslint-disable */\nimport { Result, ResultType } from './result';\nimport { Stream } from './stream';\nvar toArray = function (value) { return (value || '').split(''); };\nvar ESCAPE_CHARACTER = '\\\\';\n/**\n * @hidden\n */\nvar Parser = /** @class */ (function () {\n    function Parser(parse) {\n        this.parse = parse;\n    }\n    Parser.prototype.run = function (input, control) {\n        if (control === void 0) { control = ''; }\n        if (input instanceof Stream) {\n            return this.parse(input);\n        }\n        else {\n            return this.parse(new Stream(toArray(input), toArray(control)));\n        }\n    };\n    //map :: Functor f => f a ~> (a -> b) -> f b\n    Parser.prototype.map = function (f) {\n        var _this = this;\n        return new Parser(function (stream) { return _this.parse(stream).map(f); });\n    };\n    //chain :: Chain m => m a ~> (a -> m b) -> m b\n    Parser.prototype.chain = function (f) {\n        var _this = this;\n        return new Parser(function (stream) { return _this.parse(stream).chain(function (v, s) { return f(v).run(s); }); });\n    };\n    Parser.prototype.isLiteral = function (c) {\n        return this.run(c).type === ResultType.Literal;\n    };\n    return Parser;\n}());\nexport { Parser };\n/**\n * @hidden\n */\nexport var mask = function (_a) {\n    var prompt = _a.prompt, promptPlaceholder = _a.promptPlaceholder;\n    return function (rule) { return new Parser(function (stream) {\n        while (!stream.eof()) {\n            var _a = stream.peek(), char = _a.char, control = _a.control;\n            if (char === control && control === prompt) {\n                stream.eat();\n                return new Result(prompt, stream, ResultType.Mask);\n            }\n            if (rule.test(char)) {\n                stream.eat();\n                return new Result(char, stream, ResultType.Mask);\n            }\n            if (char === promptPlaceholder) {\n                stream.eat();\n                return new Result(prompt, stream, ResultType.Mask);\n            }\n            stream.eat_input();\n        }\n        stream.eat();\n        return new Result(prompt, stream, ResultType.Mask);\n    }); };\n};\n/**\n * @hidden\n */\nexport var literal = function (_token) { return new Parser(function (stream) {\n    //    let {char, control} = stream.peek();\n    var char = stream.peek().char;\n    if (char === _token) {\n        stream.eat();\n        return new Result(_token, stream, ResultType.Literal);\n    }\n    //    if (control === _token) {\n    //        while (!stream.eof() && char !== _token) {\n    //            stream.eat_input();\n    //            char = stream.peek().char;\n    //        }\n    //    }\n    //\n    //    if (control !== undefined) {\n    //        stream.eat();\n    //    }\n    return new Result(_token, stream, ResultType.Literal);\n}); };\n/**\n * @hidden\n */\nexport var unmask = function (prompt) { return function (rule) { return new Parser(function (stream) {\n    while (!stream.eof()) {\n        var _a = stream.peek(), char = _a.char, control = _a.control;\n        if (char === prompt && control === prompt) {\n            stream.eat();\n            return new Result(char, stream);\n        }\n        if (rule.test(char)) {\n            stream.eat();\n            return new Result(char, stream);\n        }\n        stream.eat_input();\n    }\n    stream.eat();\n    return new Result('', stream);\n}); }; };\n/**\n * @hidden\n */\nexport var unliteral = function (_token) { return new Parser(function (stream) {\n    if (stream.eof()) {\n        return new Result('', stream);\n    }\n    var char = stream.peek().char;\n    if (char === _token) {\n        stream.eat();\n    }\n    return new Result(_token, stream);\n}); };\n/**\n * @hidden\n */\nexport var token = function (rules, creator) { return new Parser(function (stream) {\n    var char = stream.next().char;\n    var rule = rules[char];\n    if (char === ESCAPE_CHARACTER) {\n        char = stream.next().char;\n        return new Result(creator.literal(char), stream);\n    }\n    if (!rule) {\n        return new Result(creator.literal(char), stream);\n    }\n    return new Result(creator.mask(rule), stream);\n}); };\n/**\n * @hidden\n */\nexport var rawMask = function (_a) {\n    var prompt = _a.prompt, promptPlaceholder = _a.promptPlaceholder;\n    return new Parser(function (stream) {\n        var char = stream.next().char;\n        if (char === prompt) {\n            return new Result(promptPlaceholder, stream);\n        }\n        return new Result(char, stream);\n    });\n};\n/**\n * @hidden\n */\nexport var rawLiteral = function (includeLiterals) { return new Parser(function (stream) {\n    var char = stream.next().char;\n    if (includeLiterals) {\n        return new Result(char, stream);\n    }\n    return new Result('', stream);\n}); };\n"], "mappings": "AAAA;AACA,SAASA,MAAM,EAAEC,UAAU,QAAQ,UAAU;AAC7C,SAASC,MAAM,QAAQ,UAAU;AACjC,IAAIC,OAAO,GAAG,SAAAA,CAAUC,KAAK,EAAE;EAAE,OAAO,CAACA,KAAK,IAAI,EAAE,EAAEC,KAAK,CAAC,EAAE,CAAC;AAAE,CAAC;AAClE,IAAIC,gBAAgB,GAAG,IAAI;AAC3B;AACA;AACA;AACA,IAAIC,MAAM,GAAG,aAAe,YAAY;EACpC,SAASA,MAAMA,CAACC,KAAK,EAAE;IACnB,IAAI,CAACA,KAAK,GAAGA,KAAK;EACtB;EACAD,MAAM,CAACE,SAAS,CAACC,GAAG,GAAG,UAAUC,KAAK,EAAEC,OAAO,EAAE;IAC7C,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;MAAEA,OAAO,GAAG,EAAE;IAAE;IACxC,IAAID,KAAK,YAAYT,MAAM,EAAE;MACzB,OAAO,IAAI,CAACM,KAAK,CAACG,KAAK,CAAC;IAC5B,CAAC,MACI;MACD,OAAO,IAAI,CAACH,KAAK,CAAC,IAAIN,MAAM,CAACC,OAAO,CAACQ,KAAK,CAAC,EAAER,OAAO,CAACS,OAAO,CAAC,CAAC,CAAC;IACnE;EACJ,CAAC;EACD;EACAL,MAAM,CAACE,SAAS,CAACI,GAAG,GAAG,UAAUC,CAAC,EAAE;IAChC,IAAIC,KAAK,GAAG,IAAI;IAChB,OAAO,IAAIR,MAAM,CAAC,UAAUS,MAAM,EAAE;MAAE,OAAOD,KAAK,CAACP,KAAK,CAACQ,MAAM,CAAC,CAACH,GAAG,CAACC,CAAC,CAAC;IAAE,CAAC,CAAC;EAC/E,CAAC;EACD;EACAP,MAAM,CAACE,SAAS,CAACQ,KAAK,GAAG,UAAUH,CAAC,EAAE;IAClC,IAAIC,KAAK,GAAG,IAAI;IAChB,OAAO,IAAIR,MAAM,CAAC,UAAUS,MAAM,EAAE;MAAE,OAAOD,KAAK,CAACP,KAAK,CAACQ,MAAM,CAAC,CAACC,KAAK,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;QAAE,OAAOL,CAAC,CAACI,CAAC,CAAC,CAACR,GAAG,CAACS,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC,CAAC;EACvH,CAAC;EACDZ,MAAM,CAACE,SAAS,CAACW,SAAS,GAAG,UAAUC,CAAC,EAAE;IACtC,OAAO,IAAI,CAACX,GAAG,CAACW,CAAC,CAAC,CAACC,IAAI,KAAKrB,UAAU,CAACsB,OAAO;EAClD,CAAC;EACD,OAAOhB,MAAM;AACjB,CAAC,CAAC,CAAE;AACJ,SAASA,MAAM;AACf;AACA;AACA;AACA,OAAO,IAAIiB,IAAI,GAAG,SAAAA,CAAUC,EAAE,EAAE;EAC5B,IAAIC,MAAM,GAAGD,EAAE,CAACC,MAAM;IAAEC,iBAAiB,GAAGF,EAAE,CAACE,iBAAiB;EAChE,OAAO,UAAUC,IAAI,EAAE;IAAE,OAAO,IAAIrB,MAAM,CAAC,UAAUS,MAAM,EAAE;MACzD,OAAO,CAACA,MAAM,CAACa,GAAG,CAAC,CAAC,EAAE;QAClB,IAAIJ,EAAE,GAAGT,MAAM,CAACc,IAAI,CAAC,CAAC;UAAEC,IAAI,GAAGN,EAAE,CAACM,IAAI;UAAEnB,OAAO,GAAGa,EAAE,CAACb,OAAO;QAC5D,IAAImB,IAAI,KAAKnB,OAAO,IAAIA,OAAO,KAAKc,MAAM,EAAE;UACxCV,MAAM,CAACgB,GAAG,CAAC,CAAC;UACZ,OAAO,IAAIhC,MAAM,CAAC0B,MAAM,EAAEV,MAAM,EAAEf,UAAU,CAACgC,IAAI,CAAC;QACtD;QACA,IAAIL,IAAI,CAACM,IAAI,CAACH,IAAI,CAAC,EAAE;UACjBf,MAAM,CAACgB,GAAG,CAAC,CAAC;UACZ,OAAO,IAAIhC,MAAM,CAAC+B,IAAI,EAAEf,MAAM,EAAEf,UAAU,CAACgC,IAAI,CAAC;QACpD;QACA,IAAIF,IAAI,KAAKJ,iBAAiB,EAAE;UAC5BX,MAAM,CAACgB,GAAG,CAAC,CAAC;UACZ,OAAO,IAAIhC,MAAM,CAAC0B,MAAM,EAAEV,MAAM,EAAEf,UAAU,CAACgC,IAAI,CAAC;QACtD;QACAjB,MAAM,CAACmB,SAAS,CAAC,CAAC;MACtB;MACAnB,MAAM,CAACgB,GAAG,CAAC,CAAC;MACZ,OAAO,IAAIhC,MAAM,CAAC0B,MAAM,EAAEV,MAAM,EAAEf,UAAU,CAACgC,IAAI,CAAC;IACtD,CAAC,CAAC;EAAE,CAAC;AACT,CAAC;AACD;AACA;AACA;AACA,OAAO,IAAIG,OAAO,GAAG,SAAAA,CAAUC,MAAM,EAAE;EAAE,OAAO,IAAI9B,MAAM,CAAC,UAAUS,MAAM,EAAE;IACzE;IACA,IAAIe,IAAI,GAAGf,MAAM,CAACc,IAAI,CAAC,CAAC,CAACC,IAAI;IAC7B,IAAIA,IAAI,KAAKM,MAAM,EAAE;MACjBrB,MAAM,CAACgB,GAAG,CAAC,CAAC;MACZ,OAAO,IAAIhC,MAAM,CAACqC,MAAM,EAAErB,MAAM,EAAEf,UAAU,CAACsB,OAAO,CAAC;IACzD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,OAAO,IAAIvB,MAAM,CAACqC,MAAM,EAAErB,MAAM,EAAEf,UAAU,CAACsB,OAAO,CAAC;EACzD,CAAC,CAAC;AAAE,CAAC;AACL;AACA;AACA;AACA,OAAO,IAAIe,MAAM,GAAG,SAAAA,CAAUZ,MAAM,EAAE;EAAE,OAAO,UAAUE,IAAI,EAAE;IAAE,OAAO,IAAIrB,MAAM,CAAC,UAAUS,MAAM,EAAE;MACjG,OAAO,CAACA,MAAM,CAACa,GAAG,CAAC,CAAC,EAAE;QAClB,IAAIJ,EAAE,GAAGT,MAAM,CAACc,IAAI,CAAC,CAAC;UAAEC,IAAI,GAAGN,EAAE,CAACM,IAAI;UAAEnB,OAAO,GAAGa,EAAE,CAACb,OAAO;QAC5D,IAAImB,IAAI,KAAKL,MAAM,IAAId,OAAO,KAAKc,MAAM,EAAE;UACvCV,MAAM,CAACgB,GAAG,CAAC,CAAC;UACZ,OAAO,IAAIhC,MAAM,CAAC+B,IAAI,EAAEf,MAAM,CAAC;QACnC;QACA,IAAIY,IAAI,CAACM,IAAI,CAACH,IAAI,CAAC,EAAE;UACjBf,MAAM,CAACgB,GAAG,CAAC,CAAC;UACZ,OAAO,IAAIhC,MAAM,CAAC+B,IAAI,EAAEf,MAAM,CAAC;QACnC;QACAA,MAAM,CAACmB,SAAS,CAAC,CAAC;MACtB;MACAnB,MAAM,CAACgB,GAAG,CAAC,CAAC;MACZ,OAAO,IAAIhC,MAAM,CAAC,EAAE,EAAEgB,MAAM,CAAC;IACjC,CAAC,CAAC;EAAE,CAAC;AAAE,CAAC;AACR;AACA;AACA;AACA,OAAO,IAAIuB,SAAS,GAAG,SAAAA,CAAUF,MAAM,EAAE;EAAE,OAAO,IAAI9B,MAAM,CAAC,UAAUS,MAAM,EAAE;IAC3E,IAAIA,MAAM,CAACa,GAAG,CAAC,CAAC,EAAE;MACd,OAAO,IAAI7B,MAAM,CAAC,EAAE,EAAEgB,MAAM,CAAC;IACjC;IACA,IAAIe,IAAI,GAAGf,MAAM,CAACc,IAAI,CAAC,CAAC,CAACC,IAAI;IAC7B,IAAIA,IAAI,KAAKM,MAAM,EAAE;MACjBrB,MAAM,CAACgB,GAAG,CAAC,CAAC;IAChB;IACA,OAAO,IAAIhC,MAAM,CAACqC,MAAM,EAAErB,MAAM,CAAC;EACrC,CAAC,CAAC;AAAE,CAAC;AACL;AACA;AACA;AACA,OAAO,IAAIwB,KAAK,GAAG,SAAAA,CAAUC,KAAK,EAAEC,OAAO,EAAE;EAAE,OAAO,IAAInC,MAAM,CAAC,UAAUS,MAAM,EAAE;IAC/E,IAAIe,IAAI,GAAGf,MAAM,CAAC2B,IAAI,CAAC,CAAC,CAACZ,IAAI;IAC7B,IAAIH,IAAI,GAAGa,KAAK,CAACV,IAAI,CAAC;IACtB,IAAIA,IAAI,KAAKzB,gBAAgB,EAAE;MAC3ByB,IAAI,GAAGf,MAAM,CAAC2B,IAAI,CAAC,CAAC,CAACZ,IAAI;MACzB,OAAO,IAAI/B,MAAM,CAAC0C,OAAO,CAACN,OAAO,CAACL,IAAI,CAAC,EAAEf,MAAM,CAAC;IACpD;IACA,IAAI,CAACY,IAAI,EAAE;MACP,OAAO,IAAI5B,MAAM,CAAC0C,OAAO,CAACN,OAAO,CAACL,IAAI,CAAC,EAAEf,MAAM,CAAC;IACpD;IACA,OAAO,IAAIhB,MAAM,CAAC0C,OAAO,CAAClB,IAAI,CAACI,IAAI,CAAC,EAAEZ,MAAM,CAAC;EACjD,CAAC,CAAC;AAAE,CAAC;AACL;AACA;AACA;AACA,OAAO,IAAI4B,OAAO,GAAG,SAAAA,CAAUnB,EAAE,EAAE;EAC/B,IAAIC,MAAM,GAAGD,EAAE,CAACC,MAAM;IAAEC,iBAAiB,GAAGF,EAAE,CAACE,iBAAiB;EAChE,OAAO,IAAIpB,MAAM,CAAC,UAAUS,MAAM,EAAE;IAChC,IAAIe,IAAI,GAAGf,MAAM,CAAC2B,IAAI,CAAC,CAAC,CAACZ,IAAI;IAC7B,IAAIA,IAAI,KAAKL,MAAM,EAAE;MACjB,OAAO,IAAI1B,MAAM,CAAC2B,iBAAiB,EAAEX,MAAM,CAAC;IAChD;IACA,OAAO,IAAIhB,MAAM,CAAC+B,IAAI,EAAEf,MAAM,CAAC;EACnC,CAAC,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA,OAAO,IAAI6B,UAAU,GAAG,SAAAA,CAAUC,eAAe,EAAE;EAAE,OAAO,IAAIvC,MAAM,CAAC,UAAUS,MAAM,EAAE;IACrF,IAAIe,IAAI,GAAGf,MAAM,CAAC2B,IAAI,CAAC,CAAC,CAACZ,IAAI;IAC7B,IAAIe,eAAe,EAAE;MACjB,OAAO,IAAI9C,MAAM,CAAC+B,IAAI,EAAEf,MAAM,CAAC;IACnC;IACA,OAAO,IAAIhB,MAAM,CAAC,EAAE,EAAEgB,MAAM,CAAC;EACjC,CAAC,CAAC;AAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}