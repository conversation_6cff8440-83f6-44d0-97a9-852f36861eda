{"ast": null, "code": "/* Copyright 2014 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar _AnnotationElement_instances, _AnnotationElement_updates, _AnnotationElement_hasBorder, _AnnotationElement_popupElement, _AnnotationElement_setRectEdited, _LinkAnnotationElement_instances, _LinkAnnotationElement_setInternalLink, _LinkAnnotationElement_bindAttachment, _LinkAnnotationElement_bindSetOCGState, _PopupElement_instances, _PopupElement_boundKeyDown, _PopupElement_boundHide, _PopupElement_boundShow, _PopupElement_boundToggle, _PopupElement_color, _PopupElement_container, _PopupElement_contentsObj, _PopupElement_dateObj, _PopupElement_elements, _PopupElement_parent, _PopupElement_parentRect, _PopupElement_pinned, _PopupElement_popup, _PopupElement_position, _PopupElement_rect, _PopupElement_richText, _PopupElement_titleObj, _PopupElement_updates, _PopupElement_wasVisible, _PopupElement_html_get, _PopupElement_fontSize_get, _PopupElement_fontColor_get, _PopupElement_makePopupContent, _PopupElement_keyDown, _PopupElement_setPosition, _PopupElement_toggle, _PopupElement_show, _PopupElement_hide, _AnnotationLayer_instances, _AnnotationLayer_accessibilityManager, _AnnotationLayer_annotationCanvasMap, _AnnotationLayer_editableAnnotations, _AnnotationLayer_appendElement, _AnnotationLayer_setAnnotationCanvasMap;\nimport { __awaiter, __classPrivateFieldGet, __classPrivateFieldSet } from \"tslib\";\nimport { AnnotationEditorType, DOMSVGFactory, FeatureTest, PDFDateString, setLayerDimensions, shadow, Util, XfaLayer } from \"pdfjs-dist/legacy/build/pdf.mjs\";\nimport { AnnotationBorderStyleType, AnnotationPrefix, AnnotationType, LINE_FACTOR } from \"./shared/utils\";\nimport { ColorConverters } from \"./shared/scripting_utils\";\n// import { AnnotationStorage } from \"pdfjs-dist/types/src/display/annotation_storage\";\n// import { AnnotationStorage } from \"pdfjs-dist/types/src/display/annotation_storage\";\n// const DEFAULT_TAB_INDEX = 1000;\n// it is unclear why the value 1000 is chosen by pdf.js\n// kendo a11y tests fail if this is not 0\nconst DEFAULT_TAB_INDEX = 0;\nconst DEFAULT_FONT_SIZE = 9;\nconst GetElementsByNameSet = new WeakSet();\nfunction getRectDims(rect) {\n  return {\n    width: rect[2] - rect[0],\n    height: rect[3] - rect[1]\n  };\n}\nclass AnnotationElementFactory {\n  /**\n   * @param {AnnotationElementParameters} parameters\n   * @returns {AnnotationElement}\n   */\n  static create(parameters) {\n    const subtype = parameters.data.annotationType;\n    switch (subtype) {\n      case AnnotationType.LINK:\n        return new LinkAnnotationElement(parameters);\n      // case AnnotationType.TEXT:\n      //     return new TextAnnotationElement(parameters);\n      case AnnotationType.WIDGET:\n        const fieldType = parameters.data.fieldType;\n        switch (fieldType) {\n          case \"Tx\":\n            return new TextWidgetAnnotationElement(parameters);\n          // case \"Btn\":\n          //     if (parameters.data.radioButton) {\n          //         return new RadioButtonWidgetAnnotationElement(parameters);\n          //     } else if (parameters.data.checkBox) {\n          //         return new CheckboxWidgetAnnotationElement(parameters);\n          //     }\n          //     return new PushButtonWidgetAnnotationElement(parameters);\n          // case \"Ch\":\n          //     return new ChoiceWidgetAnnotationElement(parameters);\n          // case \"Sig\":\n          //     return new SignatureWidgetAnnotationElement(parameters);\n          default:\n            break;\n        }\n      // return new WidgetAnnotationElement(parameters);\n      // case AnnotationType.POPUP:\n      //     return new PopupAnnotationElement(parameters);\n      case AnnotationType.FREETEXT:\n        return new FreeTextAnnotationElement(parameters);\n      // case AnnotationType.LINE:\n      //     return new LineAnnotationElement(parameters);\n      // case AnnotationType.SQUARE:\n      //     return new SquareAnnotationElement(parameters);\n      // case AnnotationType.CIRCLE:\n      //     return new CircleAnnotationElement(parameters);\n      // case AnnotationType.POLYLINE:\n      //     return new PolylineAnnotationElement(parameters);\n      // case AnnotationType.CARET:\n      //     return new CaretAnnotationElement(parameters);\n      // case AnnotationType.INK:\n      //     return new InkAnnotationElement(parameters);\n      // case AnnotationType.POLYGON:\n      //     return new PolygonAnnotationElement(parameters);\n      case AnnotationType.HIGHLIGHT:\n        return new HighlightAnnotationElement(parameters);\n      // case AnnotationType.UNDERLINE:\n      //     return new UnderlineAnnotationElement(parameters);\n      // case AnnotationType.SQUIGGLY:\n      //     return new SquigglyAnnotationElement(parameters);\n      // case AnnotationType.STRIKEOUT:\n      //     return new StrikeOutAnnotationElement(parameters);\n      // case AnnotationType.STAMP:\n      //     return new StampAnnotationElement(parameters);\n      // case AnnotationType.FILEATTACHMENT:\n      //     return new FileAttachmentAnnotationElement(parameters);\n      default:\n        return new AnnotationElement(parameters);\n    }\n  }\n}\nclass AnnotationElement {\n  constructor(parameters, {\n    isRenderable = false,\n    ignoreBorder = false,\n    createQuadrilaterals = false\n  } = {}) {\n    _AnnotationElement_instances.add(this);\n    // todo: props\n    this.isRenderable = null;\n    this.data = null;\n    this.layer = null;\n    this.linkService = null;\n    this.downloadManager = null;\n    this.imageResourcesPath = null;\n    this.renderForms = null;\n    this.svgFactory = null;\n    this.annotationStorage = null;\n    this.enableScripting = null;\n    this.hasJSActions = null;\n    this._fieldObjects = null;\n    this.parent = null;\n    this.container = null;\n    this.popup = null;\n    this.annotationEditorType = null;\n    // todo: props\n    _AnnotationElement_updates.set(this, null);\n    _AnnotationElement_hasBorder.set(this, false);\n    _AnnotationElement_popupElement.set(this, null);\n    this.isRenderable = isRenderable;\n    this.data = parameters.data;\n    this.layer = parameters.layer;\n    this.linkService = parameters.linkService;\n    this.downloadManager = parameters.downloadManager;\n    this.imageResourcesPath = parameters.imageResourcesPath;\n    this.renderForms = parameters.renderForms;\n    this.svgFactory = parameters.svgFactory;\n    this.annotationStorage = parameters.annotationStorage;\n    this.enableScripting = parameters.enableScripting;\n    this.hasJSActions = parameters.hasJSActions;\n    this._fieldObjects = parameters.fieldObjects;\n    this.parent = parameters.parent;\n    if (isRenderable) {\n      this.container = this._createContainer(ignoreBorder);\n    }\n    if (createQuadrilaterals) {\n      this._createQuadrilaterals();\n    }\n  }\n  static _hasPopupData({\n    titleObj,\n    contentsObj,\n    richText\n  }) {\n    return !!((titleObj === null || titleObj === void 0 ? void 0 : titleObj.str) || (contentsObj === null || contentsObj === void 0 ? void 0 : contentsObj.str) || (richText === null || richText === void 0 ? void 0 : richText.str));\n  }\n  get _isEditable() {\n    return this.data.isEditable;\n  }\n  get hasPopupData() {\n    return AnnotationElement._hasPopupData(this.data);\n  }\n  updateEdited(params) {\n    var _a;\n    if (!this.container) {\n      return;\n    }\n    __classPrivateFieldSet(this, _AnnotationElement_updates, __classPrivateFieldGet(this, _AnnotationElement_updates, \"f\") || {\n      rect: this.data.rect.slice(0)\n    }, \"f\");\n    const {\n      rect\n    } = params;\n    if (rect) {\n      __classPrivateFieldGet(this, _AnnotationElement_instances, \"m\", _AnnotationElement_setRectEdited).call(this, rect);\n    }\n    (_a = __classPrivateFieldGet(this, _AnnotationElement_popupElement, \"f\")) === null || _a === void 0 ? void 0 : _a.popup.updateEdited(params);\n  }\n  resetEdited() {\n    var _a;\n    if (!__classPrivateFieldGet(this, _AnnotationElement_updates, \"f\")) {\n      return;\n    }\n    __classPrivateFieldGet(this, _AnnotationElement_instances, \"m\", _AnnotationElement_setRectEdited).call(this, __classPrivateFieldGet(this, _AnnotationElement_updates, \"f\").rect);\n    (_a = __classPrivateFieldGet(this, _AnnotationElement_popupElement, \"f\")) === null || _a === void 0 ? void 0 : _a.popup.resetEdited();\n    __classPrivateFieldSet(this, _AnnotationElement_updates, null, \"f\");\n  }\n  /**\n   * Create an empty container for the annotation's HTML element.\n   *\n   * @private\n   * @param {boolean} ignoreBorder\n   * @memberof AnnotationElement\n   * @returns {HTMLElement} A section element.\n   */\n  _createContainer(ignoreBorder) {\n    const {\n      data,\n      parent: {\n        page,\n        viewport\n      }\n    } = this;\n    const container = document.createElement(\"section\");\n    container.setAttribute(\"data-annotation-id\", data.id);\n    if (!(this instanceof WidgetAnnotationElement)) {\n      container.tabIndex = DEFAULT_TAB_INDEX;\n    }\n    const {\n      style\n    } = container;\n    // The accessibility manager will move the annotation in the DOM in\n    // order to match the visual ordering.\n    // But if an annotation is above an other one, then we must draw it\n    // after the other one whatever the order is in the DOM, hence the\n    // use of the z-index.\n    style.zIndex = (this.parent.zIndex++).toString();\n    if (data.popupRef) {\n      container.setAttribute(\"aria-haspopup\", \"dialog\");\n    }\n    if (data.alternativeText) {\n      container.title = data.alternativeText;\n    }\n    if (data.noRotate) {\n      // container.classList.add(\"norotate\");\n    }\n    if (!data.rect || this instanceof PopupAnnotationElement) {\n      const {\n        rotation: rotationValue\n      } = data;\n      if (!data.hasOwnCanvas && rotationValue !== 0) {\n        this.setRotation(rotationValue, container);\n      }\n      return container;\n    }\n    const {\n      width,\n      height\n    } = getRectDims(data.rect);\n    if (!ignoreBorder && data.borderStyle.width > 0) {\n      style.borderWidth = `${data.borderStyle.width}px`;\n      const horizontalRadius = data.borderStyle.horizontalCornerRadius;\n      const verticalRadius = data.borderStyle.verticalCornerRadius;\n      if (horizontalRadius > 0 || verticalRadius > 0) {\n        const radius = `calc(${horizontalRadius}px * var(--scale-factor)) / calc(${verticalRadius}px * var(--scale-factor))`;\n        style.borderRadius = radius;\n      } else if (this instanceof RadioButtonWidgetAnnotationElement) {\n        const radius = `calc(${width}px * var(--scale-factor)) / calc(${height}px * var(--scale-factor))`;\n        style.borderRadius = radius;\n      }\n      switch (data.borderStyle.style) {\n        case AnnotationBorderStyleType.SOLID:\n          style.borderStyle = \"solid\";\n          break;\n        case AnnotationBorderStyleType.DASHED:\n          style.borderStyle = \"dashed\";\n          break;\n        case AnnotationBorderStyleType.BEVELED:\n          // warn(\"Unimplemented border style: beveled\");\n          break;\n        case AnnotationBorderStyleType.INSET:\n          // warn(\"Unimplemented border style: inset\");\n          break;\n        case AnnotationBorderStyleType.UNDERLINE:\n          style.borderBottomStyle = \"solid\";\n          break;\n        default:\n          break;\n      }\n      const borderColor = data.borderColor || null;\n      if (borderColor) {\n        __classPrivateFieldSet(this, _AnnotationElement_hasBorder, true, \"f\");\n        style.borderColor = Util.makeHexColor(borderColor[0] | 0, borderColor[1] | 0, borderColor[2] | 0);\n      } else {\n        // Transparent (invisible) border, so do not draw it at all.\n        style.borderWidth = 0 .toString();\n      }\n    }\n    // Do *not* modify `data.rect`, since that will corrupt the annotation\n    // position on subsequent calls to `_createContainer` (see issue 6804).\n    const rect = Util.normalizeRect([data.rect[0], page.view[3] - data.rect[1] + page.view[1], data.rect[2], page.view[3] - data.rect[3] + page.view[1]]);\n    const {\n      pageWidth,\n      pageHeight,\n      pageX,\n      pageY\n    } = viewport.rawDims;\n    style.left = `${100 * (rect[0] - pageX) / pageWidth}%`;\n    style.top = `${100 * (rect[1] - pageY) / pageHeight}%`;\n    const {\n      rotation\n    } = data;\n    if (data.hasOwnCanvas || rotation === 0) {\n      style.width = `${100 * width / pageWidth}%`;\n      style.height = `${100 * height / pageHeight}%`;\n    } else {\n      this.setRotation(rotation, container);\n    }\n    return container;\n  }\n  setRotation(angle, container = this.container) {\n    if (!this.data.rect) {\n      return;\n    }\n    const {\n      pageWidth,\n      pageHeight\n    } = this.parent.viewport.rawDims;\n    const {\n      width,\n      height\n    } = getRectDims(this.data.rect);\n    let elementWidth, elementHeight;\n    if (angle % 180 === 0) {\n      elementWidth = 100 * width / pageWidth;\n      elementHeight = 100 * height / pageHeight;\n    } else {\n      elementWidth = 100 * height / pageWidth;\n      elementHeight = 100 * width / pageHeight;\n    }\n    container.style.width = `${elementWidth}%`;\n    container.style.height = `${elementHeight}%`;\n    container.setAttribute(\"data-main-rotation\", (360 - angle) % 360);\n  }\n  get _commonActions() {\n    const setColor = (jsName, styleName, event) => {\n      const color = event.detail[jsName];\n      const colorType = color[0];\n      const colorArray = color.slice(1);\n      event.target.style[styleName] = ColorConverters[`${colorType}_HTML`](colorArray);\n      this.annotationStorage.setValue(this.data.id, {\n        [styleName]: ColorConverters[`${colorType}_rgb`](colorArray)\n      });\n    };\n    return shadow(this, \"_commonActions\", {\n      display: event => {\n        const {\n          display\n        } = event.detail;\n        // See scripting/constants.js for the values of `Display`.\n        // 0 = visible, 1 = hidden, 2 = noPrint and 3 = noView.\n        const hidden = display % 2 === 1;\n        this.container.style.visibility = hidden ? \"hidden\" : \"visible\";\n        this.annotationStorage.setValue(this.data.id, {\n          noView: hidden,\n          noPrint: display === 1 || display === 2\n        });\n      },\n      print: event => {\n        this.annotationStorage.setValue(this.data.id, {\n          noPrint: !event.detail.print\n        });\n      },\n      hidden: event => {\n        const {\n          hidden\n        } = event.detail;\n        this.container.style.visibility = hidden ? \"hidden\" : \"visible\";\n        this.annotationStorage.setValue(this.data.id, {\n          noPrint: hidden,\n          noView: hidden\n        });\n      },\n      focus: event => {\n        setTimeout(() => event.target.focus({\n          preventScroll: false\n        }), 0);\n      },\n      userName: event => {\n        // tooltip\n        event.target.title = event.detail.userName;\n      },\n      readonly: event => {\n        event.target.disabled = event.detail.readonly;\n      },\n      required: event => {\n        this._setRequired(event.target, event.detail.required);\n      },\n      bgColor: event => {\n        setColor(\"bgColor\", \"backgroundColor\", event);\n      },\n      fillColor: event => {\n        setColor(\"fillColor\", \"backgroundColor\", event);\n      },\n      fgColor: event => {\n        setColor(\"fgColor\", \"color\", event);\n      },\n      textColor: event => {\n        setColor(\"textColor\", \"color\", event);\n      },\n      borderColor: event => {\n        setColor(\"borderColor\", \"borderColor\", event);\n      },\n      strokeColor: event => {\n        setColor(\"strokeColor\", \"borderColor\", event);\n      },\n      rotation: event => {\n        const angle = event.detail.rotation;\n        this.setRotation(angle);\n        this.annotationStorage.setValue(this.data.id, {\n          rotation: angle\n        });\n      }\n    });\n  }\n  _dispatchEventFromSandbox(actions, jsEvent) {\n    const commonActions = this._commonActions;\n    for (const name of Object.keys(jsEvent.detail)) {\n      const action = actions[name] || commonActions[name];\n      action === null || action === void 0 ? void 0 : action(jsEvent);\n    }\n  }\n  _setDefaultPropertiesFromJS(element) {\n    if (!this.enableScripting) {\n      return;\n    }\n    // Some properties may have been updated thanks to JS.\n    const storedData = this.annotationStorage.getRawValue(this.data.id);\n    if (!storedData) {\n      return;\n    }\n    const commonActions = this._commonActions;\n    for (const [actionName, detail] of Object.entries(storedData)) {\n      const action = commonActions[actionName];\n      if (action) {\n        const eventProxy = {\n          detail: {\n            [actionName]: detail\n          },\n          target: element\n        };\n        action(eventProxy);\n        // The action has been consumed: no need to keep it.\n        delete storedData[actionName];\n      }\n    }\n  }\n  /**\n   * Create quadrilaterals from the annotation's quadpoints.\n   *\n   * @private\n   * @memberof AnnotationElement\n   */\n  _createQuadrilaterals() {\n    if (!this.container) {\n      return;\n    }\n    const {\n      quadPoints\n    } = this.data;\n    if (!quadPoints) {\n      return;\n    }\n    const [rectBlX, rectBlY, rectTrX, rectTrY] = this.data.rect.map(x => Math.fround(x));\n    if (quadPoints.length === 8) {\n      const [trX, trY, blX, blY] = quadPoints.subarray(2, 6);\n      if (rectTrX === trX && rectTrY === trY && rectBlX === blX && rectBlY === blY) {\n        // The quadpoints cover the whole annotation rectangle, so no need to\n        // create a quadrilateral.\n        return;\n      }\n    }\n    const {\n      style\n    } = this.container;\n    let svgBuffer;\n    if (__classPrivateFieldGet(this, _AnnotationElement_hasBorder, \"f\")) {\n      const {\n        borderColor,\n        borderWidth\n      } = style;\n      style.borderWidth = 0;\n      svgBuffer = [\"url('data:image/svg+xml;utf8,\", `<svg xmlns=\"http://www.w3.org/2000/svg\"`, ` preserveAspectRatio=\"none\" viewBox=\"0 0 1 1\">`, `<g fill=\"transparent\" stroke=\"${borderColor}\" stroke-width=\"${borderWidth}\">`];\n      // this.container.classList.add(\"hasBorder\");\n    }\n    // todo: debug\n    // if (typeof PDFJSDev !== \"undefined\" && PDFJSDev.test(\"TESTING\")) {\n    // this.container.classList.add(\"hasClipPath\");\n    // }\n    const width = rectTrX - rectBlX;\n    const height = rectTrY - rectBlY;\n    const {\n      svgFactory\n    } = this;\n    const svg = svgFactory.createElement(\"svg\");\n    // svg.classList.add(\"quadrilateralsContainer\");\n    svg.setAttribute(\"width\", 0);\n    svg.setAttribute(\"height\", 0);\n    const defs = svgFactory.createElement(\"defs\");\n    svg.append(defs);\n    const clipPath = svgFactory.createElement(\"clipPath\");\n    const id = `clippath_${this.data.id}`;\n    clipPath.setAttribute(\"id\", id);\n    clipPath.setAttribute(\"clipPathUnits\", \"objectBoundingBox\");\n    defs.append(clipPath);\n    for (let i = 2, ii = quadPoints.length; i < ii; i += 8) {\n      const trX = quadPoints[i];\n      const trY = quadPoints[i + 1];\n      const blX = quadPoints[i + 2];\n      const blY = quadPoints[i + 3];\n      const rect = svgFactory.createElement(\"rect\");\n      const x = (blX - rectBlX) / width;\n      const y = (rectTrY - trY) / height;\n      const rectWidth = (trX - blX) / width;\n      const rectHeight = (trY - blY) / height;\n      rect.setAttribute(\"x\", x);\n      rect.setAttribute(\"y\", y);\n      rect.setAttribute(\"width\", rectWidth);\n      rect.setAttribute(\"height\", rectHeight);\n      clipPath.append(rect);\n      svgBuffer === null || svgBuffer === void 0 ? void 0 : svgBuffer.push(`<rect vector-effect=\"non-scaling-stroke\" x=\"${x}\" y=\"${y}\" width=\"${rectWidth}\" height=\"${rectHeight}\"/>`);\n    }\n    if (__classPrivateFieldGet(this, _AnnotationElement_hasBorder, \"f\")) {\n      svgBuffer.push(`</g></svg>')`);\n      style.backgroundImage = svgBuffer.join(\"\");\n    }\n    this.container.append(svg);\n    this.container.style.clipPath = `url(#${id})`;\n  }\n  /**\n   * Create a popup for the annotation's HTML element. This is used for\n   * annotations that do not have a Popup entry in the dictionary, but\n   * are of a type that works with popups (such as Highlight annotations).\n   *\n   * @private\n   * @memberof AnnotationElement\n   */\n  _createPopup() {\n    // const { container, data } = this;\n    // container.setAttribute(\"aria-haspopup\", \"dialog\");\n    // const popup = (this.#popupElement = new PopupAnnotationElement({\n    //     data: {\n    //         color: data.color,\n    //         titleObj: data.titleObj,\n    //         modificationDate: data.modificationDate,\n    //         contentsObj: data.contentsObj,\n    //         richText: data.richText,\n    //         parentRect: data.rect,\n    //         borderStyle: 0,\n    //         id: `popup_${data.id}`,\n    //         rotation: data.rotation,\n    //     },\n    //     parent: this.parent,\n    //     elements: [this],\n    // }));\n    // this.parent.div.append(popup.render());\n  }\n  /**\n   * Render the annotation's HTML element(s).\n   *\n   * @public\n   * @memberof AnnotationElement\n   */\n  render() {\n    // unreachable(\"Abstract method `AnnotationElement.render` called\");\n  }\n  /**\n   * @private\n   * @returns {Array}\n   */\n  _getElementsByName(name, skipId = null) {\n    const fields = [];\n    if (this._fieldObjects) {\n      const fieldObj = this._fieldObjects[name];\n      if (fieldObj) {\n        for (const {\n          page,\n          id,\n          exportValues\n        } of fieldObj) {\n          if (page === -1) {\n            continue;\n          }\n          if (id === skipId) {\n            continue;\n          }\n          const exportValue = typeof exportValues === \"string\" ? exportValues : null;\n          const domElement = document.querySelector(`[data-element-id=\"${id}\"]`);\n          if (domElement && !GetElementsByNameSet.has(domElement)) {\n            // warn(`_getElementsByName - element not allowed: ${id}`);\n            continue;\n          }\n          fields.push({\n            id,\n            exportValue,\n            domElement\n          });\n        }\n      }\n      return fields;\n    }\n    // Fallback to a regular DOM lookup, to ensure that the standalone\n    // viewer components won't break.\n    for (const domElement of document.getElementsByName(name)) {\n      const {\n        exportValue\n      } = domElement;\n      const id = domElement.getAttribute(\"data-element-id\");\n      if (id === skipId) {\n        continue;\n      }\n      if (!GetElementsByNameSet.has(domElement)) {\n        continue;\n      }\n      fields.push({\n        id,\n        exportValue,\n        domElement\n      });\n    }\n    return fields;\n  }\n  show() {\n    var _a;\n    if (this.container) {\n      this.container.hidden = false;\n    }\n    (_a = this.popup) === null || _a === void 0 ? void 0 : _a.maybeShow();\n  }\n  hide() {\n    var _a;\n    if (this.container) {\n      this.container.hidden = true;\n    }\n    (_a = this.popup) === null || _a === void 0 ? void 0 : _a.forceHide();\n  }\n  /**\n   * Get the HTML element(s) which can trigger a popup when clicked or hovered.\n   *\n   * @public\n   * @memberof AnnotationElement\n   * @returns {Array<HTMLElement>|HTMLElement} An array of elements or an\n   *          element.\n   */\n  getElementsToTriggerPopup() {\n    return this.container;\n  }\n  addHighlightArea() {\n    const triggers = this.getElementsToTriggerPopup();\n    if (Array.isArray(triggers)) {\n      // for (const element of triggers) {\n      // element.classList.add(\"highlightArea\");\n      // }\n    } else {\n      // triggers.classList.add(\"highlightArea\");\n    }\n  }\n  _editOnDoubleClick() {\n    if (!this._isEditable) {\n      return;\n    }\n    const {\n      annotationEditorType: mode,\n      data: {\n        id: editId\n      }\n    } = this;\n    this.container.addEventListener(\"dblclick\", () => {\n      var _a, _b;\n      (_b = (_a = this.linkService) === null || _a === void 0 ? void 0 : _a.eventBus) === null || _b === void 0 ? void 0 : _b.dispatch(\"switchannotationeditormode\", {\n        source: this,\n        mode,\n        editId\n      });\n    });\n  }\n  _setRequired(element, isRequired) {\n    if (isRequired) {\n      element.setAttribute(\"required\", true);\n    } else {\n      element.removeAttribute(\"required\");\n    }\n    element.setAttribute(\"aria-required\", isRequired);\n  }\n}\n_AnnotationElement_updates = new WeakMap(), _AnnotationElement_hasBorder = new WeakMap(), _AnnotationElement_popupElement = new WeakMap(), _AnnotationElement_instances = new WeakSet(), _AnnotationElement_setRectEdited = function _AnnotationElement_setRectEdited(rect) {\n  const {\n    container: {\n      style\n    },\n    data: {\n      rect: currentRect,\n      rotation\n    },\n    parent: {\n      viewport: {\n        rawDims: {\n          pageWidth,\n          pageHeight,\n          pageX,\n          pageY\n        }\n      }\n    }\n  } = this;\n  currentRect === null || currentRect === void 0 ? void 0 : currentRect.splice(0, 4, ...rect);\n  const {\n    width,\n    height\n  } = getRectDims(rect);\n  style.left = `${100 * (rect[0] - pageX) / pageWidth}%`;\n  style.top = `${100 * (pageHeight - rect[3] + pageY) / pageHeight}%`;\n  if (rotation === 0) {\n    style.width = `${100 * width / pageWidth}%`;\n    style.height = `${100 * height / pageHeight}%`;\n  } else {\n    this.setRotation(rotation);\n  }\n};\nclass LinkAnnotationElement extends AnnotationElement {\n  constructor(parameters, options = null) {\n    super(parameters, {\n      isRenderable: true,\n      ignoreBorder: !!(options === null || options === void 0 ? void 0 : options.ignoreBorder),\n      createQuadrilaterals: true\n    });\n    _LinkAnnotationElement_instances.add(this);\n    this.isTooltipOnly = false;\n    this.isTooltipOnly = parameters.data.isTooltipOnly;\n  }\n  render() {\n    const {\n      data,\n      linkService\n    } = this;\n    const link = document.createElement(\"a\");\n    link.setAttribute(\"data-element-id\", data.id);\n    let isBound = false;\n    if (data.url) {\n      linkService.addLinkAttributes(link, data.url, data.newWindow);\n      isBound = true;\n    } else if (data.action) {\n      this._bindNamedAction(link, data.action);\n      isBound = true;\n    } else if (data.attachment) {\n      __classPrivateFieldGet(this, _LinkAnnotationElement_instances, \"m\", _LinkAnnotationElement_bindAttachment).call(this, link, data.attachment, data.attachmentDest);\n      isBound = true;\n    } else if (data.setOCGState) {\n      __classPrivateFieldGet(this, _LinkAnnotationElement_instances, \"m\", _LinkAnnotationElement_bindSetOCGState).call(this, link, data.setOCGState);\n      isBound = true;\n    } else if (data.dest) {\n      this._bindLink(link, data.dest);\n      isBound = true;\n    } else {\n      if (data.actions && (data.actions.Action || data.actions[\"Mouse Up\"] || data.actions[\"Mouse Down\"]) && this.enableScripting && this.hasJSActions) {\n        this._bindJSAction(link, data);\n        isBound = true;\n      }\n      if (data.resetForm) {\n        this._bindResetFormAction(link, data.resetForm);\n        isBound = true;\n      } else if (this.isTooltipOnly && !isBound) {\n        this._bindLink(link, \"\");\n        isBound = true;\n      }\n    }\n    // todo: do not render the class as the rendering is yet to be determined\n    // this.container.classList.add(\"linkAnnotation\");\n    this.container.classList.add(\"k-link-annotation\");\n    if (isBound) {\n      this.container.append(link);\n    }\n    return this.container;\n  }\n  /**\n   * Bind internal links to the link element.\n   *\n   * @private\n   * @param {Object} link\n   * @param {Object} destination\n   * @memberof LinkAnnotationElement\n   */\n  _bindLink(link, destination) {\n    link.href = this.linkService.getDestinationHash(destination);\n    link.onclick = () => {\n      if (destination) {\n        this.linkService.goToDestination(destination);\n      }\n      return false;\n    };\n    if (destination || destination === /* isTooltipOnly = */\"\") {\n      __classPrivateFieldGet(this, _LinkAnnotationElement_instances, \"m\", _LinkAnnotationElement_setInternalLink).call(this);\n    }\n  }\n  /**\n   * Bind named actions to the link element.\n   *\n   * @private\n   * @param {Object} link\n   * @param {Object} action\n   * @memberof LinkAnnotationElement\n   */\n  _bindNamedAction(link, action) {\n    link.href = this.linkService.getAnchorUrl(\"\");\n    link.onclick = () => {\n      this.linkService.executeNamedAction(action);\n      return false;\n    };\n    __classPrivateFieldGet(this, _LinkAnnotationElement_instances, \"m\", _LinkAnnotationElement_setInternalLink).call(this);\n  }\n  /**\n   * Bind JS actions to the link element.\n   *\n   * @private\n   * @param {Object} link\n   * @param {Object} data\n   * @memberof LinkAnnotationElement\n   */\n  _bindJSAction(link, data) {\n    link.href = this.linkService.getAnchorUrl(\"\");\n    const map = new Map([[\"Action\", \"onclick\"], [\"Mouse Up\", \"onmouseup\"], [\"Mouse Down\", \"onmousedown\"]]);\n    for (const name of Object.keys(data.actions)) {\n      const jsName = map.get(name);\n      if (!jsName) {\n        continue;\n      }\n      link[jsName] = () => {\n        var _a;\n        (_a = this.linkService.eventBus) === null || _a === void 0 ? void 0 : _a.dispatch(\"dispatcheventinsandbox\", {\n          source: this,\n          detail: {\n            id: data.id,\n            name\n          }\n        });\n        return false;\n      };\n    }\n    if (!link.onclick) {\n      link.onclick = () => false;\n    }\n    __classPrivateFieldGet(this, _LinkAnnotationElement_instances, \"m\", _LinkAnnotationElement_setInternalLink).call(this);\n  }\n  _bindResetFormAction(link, resetForm) {\n    const otherClickAction = link.onclick;\n    if (!otherClickAction) {\n      link.href = this.linkService.getAnchorUrl(\"\");\n    }\n    __classPrivateFieldGet(this, _LinkAnnotationElement_instances, \"m\", _LinkAnnotationElement_setInternalLink).call(this);\n    if (!this._fieldObjects) {\n      // warn(\n      //     `_bindResetFormAction - \"resetForm\" action not supported, ` +\n      //     \"ensure that the `fieldObjects` parameter is provided.\"\n      // );\n      if (!otherClickAction) {\n        link.onclick = () => false;\n      }\n      return;\n    }\n    link.onclick = () => {\n      var _a;\n      otherClickAction === null || otherClickAction === void 0 ? void 0 : otherClickAction();\n      const {\n        fields: resetFormFields,\n        refs: resetFormRefs,\n        include\n      } = resetForm;\n      const allFields = [];\n      if (resetFormFields.length !== 0 || resetFormRefs.length !== 0) {\n        const fieldIds = new Set(resetFormRefs);\n        for (const fieldName of resetFormFields) {\n          const fields = this._fieldObjects[fieldName] || [];\n          for (const {\n            id\n          } of fields) {\n            fieldIds.add(id);\n          }\n        }\n        for (const fields of Object.values(this._fieldObjects)) {\n          for (const field of fields) {\n            if (fieldIds.has(field.id) === include) {\n              allFields.push(field);\n            }\n          }\n        }\n      } else {\n        for (const fields of Object.values(this._fieldObjects)) {\n          allFields.push(...fields);\n        }\n      }\n      const storage = this.annotationStorage;\n      const allIds = [];\n      for (const field of allFields) {\n        const {\n          id\n        } = field;\n        allIds.push(id);\n        switch (field.type) {\n          case \"text\":\n            {\n              const value = field.defaultValue || \"\";\n              storage.setValue(id, {\n                value\n              });\n              break;\n            }\n          case \"checkbox\":\n          case \"radiobutton\":\n            {\n              const value = field.defaultValue === field.exportValues;\n              storage.setValue(id, {\n                value\n              });\n              break;\n            }\n          case \"combobox\":\n          case \"listbox\":\n            {\n              const value = field.defaultValue || \"\";\n              storage.setValue(id, {\n                value\n              });\n              break;\n            }\n          default:\n            continue;\n        }\n        const domElement = document.querySelector(`[data-element-id=\"${id}\"]`);\n        if (!domElement) {\n          continue;\n        } else if (!GetElementsByNameSet.has(domElement)) {\n          // warn(`_bindResetFormAction - element not allowed: ${id}`);\n          continue;\n        }\n        domElement.dispatchEvent(new Event(\"resetform\"));\n      }\n      if (this.enableScripting) {\n        // Update the values in the sandbox.\n        (_a = this.linkService.eventBus) === null || _a === void 0 ? void 0 : _a.dispatch(\"dispatcheventinsandbox\", {\n          source: this,\n          detail: {\n            id: \"app\",\n            ids: allIds,\n            name: \"ResetForm\"\n          }\n        });\n      }\n      return false;\n    };\n  }\n}\n_LinkAnnotationElement_instances = new WeakSet(), _LinkAnnotationElement_setInternalLink = function _LinkAnnotationElement_setInternalLink() {\n  this.container.setAttribute(\"data-internal-link\", \"\");\n}, _LinkAnnotationElement_bindAttachment = function _LinkAnnotationElement_bindAttachment(link, attachment, dest = null) {\n  link.href = this.linkService.getAnchorUrl(\"\");\n  if (attachment.description) {\n    link.title = attachment.description;\n  }\n  link.onclick = () => {\n    var _a;\n    (_a = this.downloadManager) === null || _a === void 0 ? void 0 : _a.openOrDownloadData(attachment.content, attachment.filename, dest);\n    return false;\n  };\n  __classPrivateFieldGet(this, _LinkAnnotationElement_instances, \"m\", _LinkAnnotationElement_setInternalLink).call(this);\n}, _LinkAnnotationElement_bindSetOCGState = function _LinkAnnotationElement_bindSetOCGState(link, action) {\n  link.href = this.linkService.getAnchorUrl(\"\");\n  link.onclick = () => {\n    this.linkService.executeSetOCGState(action);\n    return false;\n  };\n  __classPrivateFieldGet(this, _LinkAnnotationElement_instances, \"m\", _LinkAnnotationElement_setInternalLink).call(this);\n};\n// class TextAnnotationElement extends AnnotationElement {\n//     constructor(parameters) {\n//         super(parameters, { isRenderable: true });\n//     }\n//     render() {\n//         this.container.classList.add(\"textAnnotation\");\n//         const image = document.createElement(\"img\");\n//         image.src =\n//             this.imageResourcesPath +\n//             \"annotation-\" +\n//             this.data.name.toLowerCase() +\n//             \".svg\";\n//         image.setAttribute(\"data-l10n-id\", \"pdfjs-text-annotation-type\");\n//         image.setAttribute(\n//             \"data-l10n-args\",\n//             JSON.stringify({ type: this.data.name })\n//         );\n//         if (!this.data.popupRef && this.hasPopupData) {\n//             this._createPopup();\n//         }\n//         this.container.append(image);\n//         return this.container;\n//     }\n// }\nclass WidgetAnnotationElement extends AnnotationElement {\n  render() {\n    // Show only the container for unsupported field types.\n    return this.container;\n  }\n  showElementAndHideCanvas(element) {\n    var _a;\n    if (this.data.hasOwnCanvas) {\n      if (((_a = element.previousSibling) === null || _a === void 0 ? void 0 : _a.nodeName) === \"CANVAS\") {\n        element.previousSibling.hidden = true;\n      }\n      element.hidden = false;\n    }\n  }\n  _getKeyModifier(event) {\n    return FeatureTest.platform.isMac ? event.metaKey : event.ctrlKey;\n  }\n  _setEventListener(element, elementData, baseName, eventName, valueGetter) {\n    if (baseName.includes(\"mouse\")) {\n      // Mouse events\n      element.addEventListener(baseName, event => {\n        var _a;\n        (_a = this.linkService.eventBus) === null || _a === void 0 ? void 0 : _a.dispatch(\"dispatcheventinsandbox\", {\n          source: this,\n          detail: {\n            id: this.data.id,\n            name: eventName,\n            value: valueGetter(event),\n            shift: event.shiftKey,\n            modifier: this._getKeyModifier(event)\n          }\n        });\n      });\n    } else {\n      // Non-mouse events\n      element.addEventListener(baseName, event => {\n        var _a;\n        if (baseName === \"blur\") {\n          if (!elementData.focused || !event.relatedTarget) {\n            return;\n          }\n          elementData.focused = false;\n        } else if (baseName === \"focus\") {\n          if (elementData.focused) {\n            return;\n          }\n          elementData.focused = true;\n        }\n        if (!valueGetter) {\n          return;\n        }\n        (_a = this.linkService.eventBus) === null || _a === void 0 ? void 0 : _a.dispatch(\"dispatcheventinsandbox\", {\n          source: this,\n          detail: {\n            id: this.data.id,\n            name: eventName,\n            value: valueGetter(event)\n          }\n        });\n      });\n    }\n  }\n  _setEventListeners(element, elementData, names, getter) {\n    var _a, _b, _c;\n    for (const [baseName, eventName] of names) {\n      if (eventName === \"Action\" || ((_a = this.data.actions) === null || _a === void 0 ? void 0 : _a[eventName])) {\n        if (eventName === \"Focus\" || eventName === \"Blur\") {\n          elementData || (elementData = {\n            focused: false\n          });\n        }\n        this._setEventListener(element, elementData, baseName, eventName, getter);\n        if (eventName === \"Focus\" && !((_b = this.data.actions) === null || _b === void 0 ? void 0 : _b.Blur)) {\n          // Ensure that elementData will have the correct value.\n          this._setEventListener(element, elementData, \"blur\", \"Blur\", null);\n        } else if (eventName === \"Blur\" && !((_c = this.data.actions) === null || _c === void 0 ? void 0 : _c.Focus)) {\n          this._setEventListener(element, elementData, \"focus\", \"Focus\", null);\n        }\n      }\n    }\n  }\n  _setBackgroundColor(element) {\n    const color = this.data.backgroundColor || null;\n    element.style.backgroundColor = color === null ? \"transparent\" : Util.makeHexColor(color[0], color[1], color[2]);\n  }\n  /**\n   * Apply text styles to the text in the element.\n   *\n   * @private\n   * @param {HTMLDivElement} element\n   * @memberof TextWidgetAnnotationElement\n   */\n  _setTextStyle(element) {\n    const TEXT_ALIGNMENT = [\"left\", \"center\", \"right\"];\n    const {\n      fontColor\n    } = this.data.defaultAppearanceData;\n    const fontSize = this.data.defaultAppearanceData.fontSize || DEFAULT_FONT_SIZE;\n    const style = element.style;\n    // TODO: If the font-size is zero, calculate it based on the height and\n    //       width of the element.\n    // Not setting `style.fontSize` will use the default font-size for now.\n    // We don't use the font, as specified in the PDF document, for the <input>\n    // element. Hence using the original `fontSize` could look bad, which is why\n    // it's instead based on the field height.\n    // If the height is \"big\" then it could lead to a too big font size\n    // so in this case use the one we've in the pdf (hence the min).\n    let computedFontSize;\n    const BORDER_SIZE = 2;\n    const roundToOneDecimal = x => Math.round(10 * x) / 10;\n    if (this.data.multiLine) {\n      const height = Math.abs(this.data.rect[3] - this.data.rect[1] - BORDER_SIZE);\n      const numberOfLines = Math.round(height / (LINE_FACTOR * fontSize)) || 1;\n      const lineHeight = height / numberOfLines;\n      computedFontSize = Math.min(fontSize, roundToOneDecimal(lineHeight / LINE_FACTOR));\n    } else {\n      const height = Math.abs(this.data.rect[3] - this.data.rect[1] - BORDER_SIZE);\n      computedFontSize = Math.min(fontSize, roundToOneDecimal(height / LINE_FACTOR));\n    }\n    style.fontSize = `calc(${computedFontSize}px * var(--scale-factor))`;\n    style.color = Util.makeHexColor(fontColor[0], fontColor[1], fontColor[2]);\n    if (this.data.textAlignment !== null) {\n      style.textAlign = TEXT_ALIGNMENT[this.data.textAlignment];\n    }\n  }\n  _setRequired(element, isRequired) {\n    if (isRequired) {\n      element.setAttribute(\"required\", true);\n    } else {\n      element.removeAttribute(\"required\");\n    }\n    element.setAttribute(\"aria-required\", isRequired);\n  }\n}\nclass TextWidgetAnnotationElement extends WidgetAnnotationElement {\n  constructor(parameters) {\n    const isRenderable = parameters.renderForms || parameters.data.hasOwnCanvas || !parameters.data.hasAppearance && !!parameters.data.fieldValue;\n    super(parameters, {\n      isRenderable\n    });\n  }\n  setPropertyOnSiblings(base, key, value, keyInStorage) {\n    const storage = this.annotationStorage;\n    for (const element of this._getElementsByName(base.name, /* skipId = */base.id)) {\n      if (element.domElement) {\n        element.domElement[key] = value;\n      }\n      storage.setValue(element.id, {\n        [keyInStorage]: value\n      });\n    }\n  }\n  render() {\n    var _a, _b;\n    const storage = this.annotationStorage;\n    const id = this.data.id;\n    // this.container.classList.add(\"textWidgetAnnotation\");\n    this.container.classList.add(\"k-text-widget-annotation\");\n    let element = null;\n    if (this.renderForms) {\n      // NOTE: We cannot set the values using `element.value` below, since it\n      //       prevents the AnnotationLayer rasterizer in `test/driver.js`\n      //       from parsing the elements correctly for the reference tests.\n      const storedData = storage.getValue(id, {\n        value: this.data.fieldValue\n      });\n      let textContent = storedData.value || \"\";\n      const maxLen = storage.getValue(id, {\n        charLimit: this.data.maxLen\n      }).charLimit;\n      if (maxLen && textContent.length > maxLen) {\n        textContent = textContent.slice(0, maxLen);\n      }\n      let fieldFormattedValues = storedData.formattedValue || ((_a = this.data.textContent) === null || _a === void 0 ? void 0 : _a.join(\"\\n\")) || null;\n      if (fieldFormattedValues && this.data.comb) {\n        fieldFormattedValues = fieldFormattedValues.replaceAll(/\\s+/g, \"\");\n      }\n      const elementData = {\n        userValue: textContent,\n        formattedValue: fieldFormattedValues,\n        lastCommittedValue: null,\n        commitKey: 1,\n        focused: false\n      };\n      if (this.data.multiLine) {\n        element = document.createElement(\"textarea\");\n        element.textContent = fieldFormattedValues !== null && fieldFormattedValues !== void 0 ? fieldFormattedValues : textContent;\n        if (this.data.doNotScroll) {\n          element.style.overflowY = \"hidden\";\n        }\n      } else {\n        element = document.createElement(\"input\");\n        element.type = \"text\";\n        element.setAttribute(\"value\", fieldFormattedValues !== null && fieldFormattedValues !== void 0 ? fieldFormattedValues : textContent);\n        if (this.data.doNotScroll) {\n          element.style.overflowX = \"hidden\";\n        }\n      }\n      if (this.data.hasOwnCanvas) {\n        element.hidden = true;\n      }\n      GetElementsByNameSet.add(element);\n      element.setAttribute(\"data-element-id\", id);\n      element.disabled = this.data.readOnly;\n      element.name = this.data.fieldName;\n      element.tabIndex = DEFAULT_TAB_INDEX;\n      this._setRequired(element, this.data.required);\n      if (maxLen) {\n        element.maxLength = maxLen;\n      }\n      element.addEventListener(\"input\", event => {\n        storage.setValue(id, {\n          value: event.target.value\n        });\n        this.setPropertyOnSiblings(element, \"value\", event.target.value, \"value\");\n        elementData.formattedValue = null;\n      });\n      element.addEventListener(\"resetform\", () => {\n        var _a;\n        const defaultValue = (_a = this.data.defaultFieldValue) !== null && _a !== void 0 ? _a : \"\";\n        element.value = elementData.userValue = defaultValue;\n        elementData.formattedValue = null;\n      });\n      let blurListener = event => {\n        const {\n          formattedValue\n        } = elementData;\n        if (formattedValue !== null && formattedValue !== undefined) {\n          event.target.value = formattedValue;\n        }\n        // Reset the cursor position to the start of the field (issue 12359).\n        event.target.scrollLeft = 0;\n      };\n      if (this.enableScripting && this.hasJSActions) {\n        element.addEventListener(\"focus\", event => {\n          var _a;\n          if (elementData.focused) {\n            return;\n          }\n          const {\n            target\n          } = event;\n          if (elementData.userValue) {\n            target.value = elementData.userValue;\n          }\n          elementData.lastCommittedValue = target.value;\n          elementData.commitKey = 1;\n          if (!((_a = this.data.actions) === null || _a === void 0 ? void 0 : _a.Focus)) {\n            elementData.focused = true;\n          }\n        });\n        element.addEventListener(\"updatefromsandbox\", jsEvent => {\n          this.showElementAndHideCanvas(jsEvent.target);\n          const actions = {\n            value(event) {\n              var _a;\n              elementData.userValue = (_a = event.detail.value) !== null && _a !== void 0 ? _a : \"\";\n              storage.setValue(id, {\n                value: elementData.userValue.toString()\n              });\n              event.target.value = elementData.userValue;\n            },\n            formattedValue(event) {\n              const {\n                formattedValue\n              } = event.detail;\n              elementData.formattedValue = formattedValue;\n              if (formattedValue !== null && formattedValue !== undefined && event.target !== document.activeElement) {\n                // Input hasn't the focus so display formatted string\n                event.target.value = formattedValue;\n              }\n              storage.setValue(id, {\n                formattedValue\n              });\n            },\n            selRange(event) {\n              event.target.setSelectionRange(...event.detail.selRange);\n            },\n            charLimit: event => {\n              var _a;\n              const {\n                charLimit\n              } = event.detail;\n              const {\n                target\n              } = event;\n              if (charLimit === 0) {\n                target.removeAttribute(\"maxLength\");\n                return;\n              }\n              target.setAttribute(\"maxLength\", charLimit);\n              let value = elementData.userValue;\n              if (!value || value.length <= charLimit) {\n                return;\n              }\n              value = value.slice(0, charLimit);\n              target.value = elementData.userValue = value;\n              storage.setValue(id, {\n                value\n              });\n              (_a = this.linkService.eventBus) === null || _a === void 0 ? void 0 : _a.dispatch(\"dispatcheventinsandbox\", {\n                source: this,\n                detail: {\n                  id,\n                  name: \"Keystroke\",\n                  value,\n                  willCommit: true,\n                  commitKey: 1,\n                  selStart: target.selectionStart,\n                  selEnd: target.selectionEnd\n                }\n              });\n            }\n          };\n          this._dispatchEventFromSandbox(actions, jsEvent);\n        });\n        // Even if the field hasn't any actions\n        // leaving it can still trigger some actions with Calculate\n        element.addEventListener(\"keydown\", event => {\n          var _a;\n          elementData.commitKey = 1;\n          // If the key is one of Escape, Enter then the data are committed.\n          // If we've a Tab then data will be committed on blur.\n          let commitKey = -1;\n          if (event.key === \"Escape\") {\n            commitKey = 0;\n          } else if (event.key === \"Enter\" && !this.data.multiLine) {\n            // When we've a multiline field, \"Enter\" key is a key as the other\n            // hence we don't commit the data (Acrobat behaves the same way)\n            // (see issue #15627).\n            commitKey = 2;\n          } else if (event.key === \"Tab\") {\n            elementData.commitKey = 3;\n          }\n          if (commitKey === -1) {\n            return;\n          }\n          const {\n            value\n          } = event.target;\n          if (elementData.lastCommittedValue === value) {\n            return;\n          }\n          elementData.lastCommittedValue = value;\n          // Save the entered value\n          elementData.userValue = value;\n          (_a = this.linkService.eventBus) === null || _a === void 0 ? void 0 : _a.dispatch(\"dispatcheventinsandbox\", {\n            source: this,\n            detail: {\n              id,\n              name: \"Keystroke\",\n              value,\n              willCommit: true,\n              commitKey,\n              selStart: event.target.selectionStart,\n              selEnd: event.target.selectionEnd\n            }\n          });\n        });\n        const _blurListener = blurListener;\n        blurListener = null;\n        element.addEventListener(\"blur\", event => {\n          var _a, _b;\n          if (!elementData.focused || !event.relatedTarget) {\n            return;\n          }\n          if (!((_a = this.data.actions) === null || _a === void 0 ? void 0 : _a.Blur)) {\n            elementData.focused = false;\n          }\n          const {\n            value\n          } = event.target;\n          elementData.userValue = value;\n          if (elementData.lastCommittedValue !== value) {\n            (_b = this.linkService.eventBus) === null || _b === void 0 ? void 0 : _b.dispatch(\"dispatcheventinsandbox\", {\n              source: this,\n              detail: {\n                id,\n                name: \"Keystroke\",\n                value,\n                willCommit: true,\n                commitKey: elementData.commitKey,\n                selStart: event.target.selectionStart,\n                selEnd: event.target.selectionEnd\n              }\n            });\n          }\n          _blurListener(event);\n        });\n        if ((_b = this.data.actions) === null || _b === void 0 ? void 0 : _b.Keystroke) {\n          element.addEventListener(\"beforeinput\", event => {\n            var _a;\n            elementData.lastCommittedValue = null;\n            const {\n              data,\n              target\n            } = event;\n            const {\n              value,\n              selectionStart,\n              selectionEnd\n            } = target;\n            let selStart = selectionStart,\n              selEnd = selectionEnd;\n            switch (event.inputType) {\n              // https://rawgit.com/w3c/input-events/v1/index.html#interface-InputEvent-Attributes\n              case \"deleteWordBackward\":\n                {\n                  const match = value.substring(0, selectionStart).match(/\\w*[^\\w]*$/);\n                  if (match) {\n                    selStart -= match[0].length;\n                  }\n                  break;\n                }\n              case \"deleteWordForward\":\n                {\n                  const match = value.substring(selectionStart).match(/^[^\\w]*\\w*/);\n                  if (match) {\n                    selEnd += match[0].length;\n                  }\n                  break;\n                }\n              case \"deleteContentBackward\":\n                if (selectionStart === selectionEnd) {\n                  selStart -= 1;\n                }\n                break;\n              case \"deleteContentForward\":\n                if (selectionStart === selectionEnd) {\n                  selEnd += 1;\n                }\n                break;\n              default:\n                break;\n            }\n            // We handle the event ourselves.\n            event.preventDefault();\n            (_a = this.linkService.eventBus) === null || _a === void 0 ? void 0 : _a.dispatch(\"dispatcheventinsandbox\", {\n              source: this,\n              detail: {\n                id,\n                name: \"Keystroke\",\n                value,\n                change: data || \"\",\n                willCommit: false,\n                selStart,\n                selEnd\n              }\n            });\n          });\n        }\n        this._setEventListeners(element, elementData, [[\"focus\", \"Focus\"], [\"blur\", \"Blur\"], [\"mousedown\", \"Mouse Down\"], [\"mouseenter\", \"Mouse Enter\"], [\"mouseleave\", \"Mouse Exit\"], [\"mouseup\", \"Mouse Up\"]], event => event.target.value);\n      }\n      if (blurListener) {\n        element.addEventListener(\"blur\", blurListener);\n      }\n      if (this.data.comb) {\n        const fieldWidth = this.data.rect[2] - this.data.rect[0];\n        const combWidth = fieldWidth / maxLen;\n        element.classList.add(\"comb\");\n        element.style.letterSpacing = `calc(${combWidth}px * var(--scale-factor) - 1ch)`;\n      }\n    } else {\n      element = document.createElement(\"div\");\n      element.textContent = this.data.fieldValue;\n      element.style.verticalAlign = \"middle\";\n      element.style.display = \"table-cell\";\n      if (this.data.hasOwnCanvas) {\n        element.hidden = true;\n      }\n    }\n    this._setTextStyle(element);\n    this._setBackgroundColor(element);\n    this._setDefaultPropertiesFromJS(element);\n    this.container.append(element);\n    return this.container;\n  }\n}\n// class SignatureWidgetAnnotationElement extends WidgetAnnotationElement {\n//     constructor(parameters) {\n//         super(parameters, { isRenderable: !!parameters.data.hasOwnCanvas });\n//     }\n// }\n// class CheckboxWidgetAnnotationElement extends WidgetAnnotationElement {\n//     constructor(parameters) {\n//         super(parameters, { isRenderable: parameters.renderForms });\n//     }\n//     render() {\n//         const storage = this.annotationStorage;\n//         const data = this.data;\n//         const id = data.id;\n//         let value = storage.getValue(id, {\n//             value: data.exportValue === data.fieldValue,\n//         }).value;\n//         if (typeof value === \"string\") {\n//             // The value has been changed through js and set in annotationStorage.\n//             value = value !== \"Off\";\n//             storage.setValue(id, { value });\n//         }\n//         this.container.classList.add(\"buttonWidgetAnnotation\", \"checkBox\");\n//         const element = document.createElement(\"input\");\n//         GetElementsByNameSet.add(element);\n//         element.setAttribute(\"data-element-id\", id);\n//         element.disabled = data.readOnly;\n//         this._setRequired(element, this.data.required);\n//         element.type = \"checkbox\";\n//         element.name = data.fieldName;\n//         if (value) {\n//             element.setAttribute(\"checked\", (true).toString());\n//         }\n//         element.setAttribute(\"exportValue\", data.exportValue);\n//         element.tabIndex = DEFAULT_TAB_INDEX;\n//         element.addEventListener(\"change\", event => {\n//             const { name, checked } = event.target as any;\n//             for (const checkbox of this._getElementsByName(name, /* skipId = */ id)) {\n//                 const curChecked = checked && checkbox.exportValue === data.exportValue;\n//                 if (checkbox.domElement) {\n//                     checkbox.domElement.checked = curChecked;\n//                 }\n//                 storage.setValue(checkbox.id, { value: curChecked });\n//             }\n//             storage.setValue(id, { value: checked });\n//         });\n//         element.addEventListener(\"resetform\", event => {\n//             const defaultValue = data.defaultFieldValue || \"Off\";\n//             // @ts-expect-error(TS)\n//             event.target.checked = defaultValue === data.exportValue;\n//         });\n//         if (this.enableScripting && this.hasJSActions) {\n//             element.addEventListener(\"updatefromsandbox\", jsEvent => {\n//                 const actions = {\n//                     value(event) {\n//                         event.target.checked = event.detail.value !== \"Off\";\n//                         storage.setValue(id, { value: event.target.checked });\n//                     },\n//                 };\n//                 this._dispatchEventFromSandbox(actions, jsEvent);\n//             });\n//             this._setEventListeners(\n//                 element,\n//                 null,\n//                 [\n//                     [\"change\", \"Validate\"],\n//                     [\"change\", \"Action\"],\n//                     [\"focus\", \"Focus\"],\n//                     [\"blur\", \"Blur\"],\n//                     [\"mousedown\", \"Mouse Down\"],\n//                     [\"mouseenter\", \"Mouse Enter\"],\n//                     [\"mouseleave\", \"Mouse Exit\"],\n//                     [\"mouseup\", \"Mouse Up\"],\n//                 ],\n//                 event => event.target.checked\n//             );\n//         }\n//         this._setBackgroundColor(element);\n//         this._setDefaultPropertiesFromJS(element);\n//         this.container.append(element);\n//         return this.container;\n//     }\n// }\nclass RadioButtonWidgetAnnotationElement extends WidgetAnnotationElement {\n  constructor(parameters) {\n    super(parameters, {\n      isRenderable: parameters.renderForms\n    });\n  }\n  render() {\n    this.container.classList.add(\"buttonWidgetAnnotation\", \"radioButton\");\n    const storage = this.annotationStorage;\n    const data = this.data;\n    const id = data.id;\n    let value = storage.getValue(id, {\n      value: data.fieldValue === data.buttonValue\n    }).value;\n    if (typeof value === \"string\") {\n      // The value has been changed through js and set in annotationStorage.\n      value = value !== data.buttonValue;\n      storage.setValue(id, {\n        value\n      });\n    }\n    if (value) {\n      // It's possible that multiple radio buttons are checked.\n      // So if this one is checked we just reset the other ones.\n      // (see bug 1864136). Then when the other ones will be rendered they will\n      // unchecked (because of their value in the storage).\n      // Consequently, the first checked radio button will be the only checked\n      // one.\n      for (const radio of this._getElementsByName(data.fieldName, /* skipId = */id)) {\n        storage.setValue(radio.id, {\n          value: false\n        });\n      }\n    }\n    const element = document.createElement(\"input\");\n    GetElementsByNameSet.add(element);\n    element.setAttribute(\"data-element-id\", id);\n    element.disabled = data.readOnly;\n    this._setRequired(element, this.data.required);\n    element.type = \"radio\";\n    element.name = data.fieldName;\n    if (value) {\n      element.setAttribute(\"checked\", true.toString());\n    }\n    element.tabIndex = DEFAULT_TAB_INDEX;\n    element.addEventListener(\"change\", event => {\n      const {\n        name,\n        checked\n      } = event.target;\n      for (const radio of this._getElementsByName(name, /* skipId = */id)) {\n        storage.setValue(radio.id, {\n          value: false\n        });\n      }\n      storage.setValue(id, {\n        value: checked\n      });\n    });\n    element.addEventListener(\"resetform\", event => {\n      const defaultValue = data.defaultFieldValue;\n      // @ts-expect-error(TS)\n      event.target.checked = defaultValue !== null && defaultValue !== undefined && defaultValue === data.buttonValue;\n    });\n    if (this.enableScripting && this.hasJSActions) {\n      const pdfButtonValue = data.buttonValue;\n      element.addEventListener(\"updatefromsandbox\", jsEvent => {\n        const actions = {\n          value: event => {\n            const checked = pdfButtonValue === event.detail.value;\n            for (const radio of this._getElementsByName(event.target.name)) {\n              const curChecked = checked && radio.id === id;\n              if (radio.domElement) {\n                radio.domElement.checked = curChecked;\n              }\n              storage.setValue(radio.id, {\n                value: curChecked\n              });\n            }\n          }\n        };\n        this._dispatchEventFromSandbox(actions, jsEvent);\n      });\n      this._setEventListeners(element, null, [[\"change\", \"Validate\"], [\"change\", \"Action\"], [\"focus\", \"Focus\"], [\"blur\", \"Blur\"], [\"mousedown\", \"Mouse Down\"], [\"mouseenter\", \"Mouse Enter\"], [\"mouseleave\", \"Mouse Exit\"], [\"mouseup\", \"Mouse Up\"]], event => event.target.checked);\n    }\n    this._setBackgroundColor(element);\n    this._setDefaultPropertiesFromJS(element);\n    this.container.append(element);\n    return this.container;\n  }\n}\n// class PushButtonWidgetAnnotationElement extends LinkAnnotationElement {\n//     constructor(parameters) {\n//         super(parameters, { ignoreBorder: parameters.data.hasAppearance });\n//     }\n//     render() {\n//         // The rendering and functionality of a push button widget annotation is\n//         // equal to that of a link annotation, but may have more functionality, such\n//         // as performing actions on form fields (resetting, submitting, et cetera).\n//         const container = super.render();\n//         container.classList.add(\"buttonWidgetAnnotation\", \"pushButton\");\n//         const linkElement = container.lastChild;\n//         if (this.enableScripting && this.hasJSActions && linkElement) {\n//             this._setDefaultPropertiesFromJS(linkElement);\n//             linkElement.addEventListener(\"updatefromsandbox\", jsEvent => {\n//                 this._dispatchEventFromSandbox({}, jsEvent);\n//             });\n//         }\n//         return container;\n//     }\n// }\n// class ChoiceWidgetAnnotationElement extends WidgetAnnotationElement {\n//     constructor(parameters) {\n//         super(parameters, { isRenderable: parameters.renderForms });\n//     }\n//     render() {\n//         this.container.classList.add(\"choiceWidgetAnnotation\");\n//         const storage = this.annotationStorage;\n//         const id = this.data.id;\n//         const storedData = storage.getValue(id, {\n//             value: this.data.fieldValue,\n//         });\n//         const selectElement = document.createElement(\"select\");\n//         GetElementsByNameSet.add(selectElement);\n//         selectElement.setAttribute(\"data-element-id\", id);\n//         selectElement.disabled = this.data.readOnly;\n//         this._setRequired(selectElement, this.data.required);\n//         selectElement.name = this.data.fieldName;\n//         selectElement.tabIndex = DEFAULT_TAB_INDEX;\n//         let addAnEmptyEntry = this.data.combo && this.data.options.length > 0;\n//         if (!this.data.combo) {\n//             // List boxes have a size and (optionally) multiple selection.\n//             selectElement.size = this.data.options.length;\n//             if (this.data.multiSelect) {\n//                 selectElement.multiple = true;\n//             }\n//         }\n//         selectElement.addEventListener(\"resetform\", () => {\n//             const defaultValue = this.data.defaultFieldValue;\n//             for (const option of (selectElement as any).options) {\n//                 option.selected = option.value === defaultValue;\n//             }\n//         });\n//         // Insert the options into the choice field.\n//         for (const option of this.data.options) {\n//             const optionElement = document.createElement(\"option\");\n//             optionElement.textContent = option.displayValue;\n//             optionElement.value = option.exportValue;\n//             if (storedData.value.includes(option.exportValue)) {\n//                 optionElement.setAttribute(\"selected\", (true).toString());\n//                 addAnEmptyEntry = false;\n//             }\n//             selectElement.append(optionElement);\n//         }\n//         let removeEmptyEntry = null;\n//         if (addAnEmptyEntry) {\n//             const noneOptionElement = document.createElement(\"option\");\n//             noneOptionElement.value = \" \";\n//             noneOptionElement.setAttribute(\"hidden\", (true).toString());\n//             noneOptionElement.setAttribute(\"selected\", (true).toString());\n//             selectElement.prepend(noneOptionElement);\n//             removeEmptyEntry = () => {\n//                 noneOptionElement.remove();\n//                 selectElement.removeEventListener(\"input\", removeEmptyEntry);\n//                 removeEmptyEntry = null;\n//             };\n//             selectElement.addEventListener(\"input\", removeEmptyEntry);\n//         }\n//         const getValue = isExport => {\n//             const name = isExport ? \"value\" : \"textContent\";\n//             const { options, multiple } = selectElement;\n//             if (!multiple) {\n//                 return options.selectedIndex === -1\n//                     ? null\n//                     : options[options.selectedIndex][name];\n//             }\n//             return Array.prototype.filter\n//                 .call(options, option => option.selected)\n//                 .map(option => option[name]);\n//         };\n//         let selectedValues = getValue(/* isExport */ false);\n//         const getItems = event => {\n//             const options = event.target.options;\n//             return Array.prototype.map.call(options, option => ({\n//                 displayValue: option.textContent,\n//                 exportValue: option.value,\n//             }));\n//         };\n//         if (this.enableScripting && this.hasJSActions) {\n//             selectElement.addEventListener(\"updatefromsandbox\", jsEvent => {\n//                 const actions = {\n//                     value(event) {\n//                         removeEmptyEntry?.();\n//                         const value = event.detail.value;\n//                         const values = new Set(Array.isArray(value) ? value : [value]);\n//                         for (const option of (selectElement as any).options) {\n//                             option.selected = values.has(option.value);\n//                         }\n//                         storage.setValue(id, {\n//                             value: getValue(/* isExport */ true),\n//                         });\n//                         selectedValues = getValue(/* isExport */ false);\n//                     },\n//                     multipleSelection() {\n//                         selectElement.multiple = true;\n//                     },\n//                     remove(event) {\n//                         const options = selectElement.options;\n//                         const index = event.detail.remove;\n//                         options[index].selected = false;\n//                         selectElement.remove(index);\n//                         if (options.length > 0) {\n//                             const i = Array.prototype.findIndex.call(\n//                                 options,\n//                                 option => option.selected\n//                             );\n//                             if (i === -1) {\n//                                 options[0].selected = true;\n//                             }\n//                         }\n//                         storage.setValue(id, {\n//                             value: getValue(/* isExport */ true),\n//                             items: getItems(event),\n//                         });\n//                         selectedValues = getValue(/* isExport */ false);\n//                     },\n//                     clear() {\n//                         while (selectElement.length !== 0) {\n//                             selectElement.remove(0);\n//                         }\n//                         storage.setValue(id, { value: null, items: [] });\n//                         selectedValues = getValue(/* isExport */ false);\n//                     },\n//                     insert(event) {\n//                         const { index, displayValue, exportValue } = event.detail.insert;\n//                         const selectChild = selectElement.children[index];\n//                         const optionElement = document.createElement(\"option\");\n//                         optionElement.textContent = displayValue;\n//                         optionElement.value = exportValue;\n//                         if (selectChild) {\n//                             selectChild.before(optionElement);\n//                         } else {\n//                             selectElement.append(optionElement);\n//                         }\n//                         storage.setValue(id, {\n//                             value: getValue(/* isExport */ true),\n//                             items: getItems(event),\n//                         });\n//                         selectedValues = getValue(/* isExport */ false);\n//                     },\n//                     items(event) {\n//                         const { items } = event.detail;\n//                         while (selectElement.length !== 0) {\n//                             selectElement.remove(0);\n//                         }\n//                         for (const item of items) {\n//                             const { displayValue, exportValue } = item;\n//                             const optionElement = document.createElement(\"option\");\n//                             optionElement.textContent = displayValue;\n//                             optionElement.value = exportValue;\n//                             selectElement.append(optionElement);\n//                         }\n//                         if (selectElement.options.length > 0) {\n//                             selectElement.options[0].selected = true;\n//                         }\n//                         storage.setValue(id, {\n//                             value: getValue(/* isExport */ true),\n//                             items: getItems(event),\n//                         });\n//                         selectedValues = getValue(/* isExport */ false);\n//                     },\n//                     indices(event) {\n//                         const indices = new Set(event.detail.indices);\n//                         for (const option of event.target.options) {\n//                             option.selected = indices.has(option.index);\n//                         }\n//                         storage.setValue(id, {\n//                             value: getValue(/* isExport */ true),\n//                         });\n//                         selectedValues = getValue(/* isExport */ false);\n//                     },\n//                     editable(event) {\n//                         event.target.disabled = !event.detail.editable;\n//                     },\n//                 };\n//                 this._dispatchEventFromSandbox(actions, jsEvent);\n//             });\n//             selectElement.addEventListener(\"input\", event => {\n//                 const exportValue = getValue(/* isExport */ true);\n//                 const change = getValue(/* isExport */ false);\n//                 storage.setValue(id, { value: exportValue });\n//                 event.preventDefault();\n//                 this.linkService.eventBus?.dispatch(\"dispatcheventinsandbox\", {\n//                     source: this,\n//                     detail: {\n//                         id,\n//                         name: \"Keystroke\",\n//                         value: selectedValues,\n//                         change,\n//                         changeEx: exportValue,\n//                         willCommit: false,\n//                         commitKey: 1,\n//                         keyDown: false,\n//                     },\n//                 });\n//             });\n//             this._setEventListeners(\n//                 selectElement,\n//                 null,\n//                 [\n//                     [\"focus\", \"Focus\"],\n//                     [\"blur\", \"Blur\"],\n//                     [\"mousedown\", \"Mouse Down\"],\n//                     [\"mouseenter\", \"Mouse Enter\"],\n//                     [\"mouseleave\", \"Mouse Exit\"],\n//                     [\"mouseup\", \"Mouse Up\"],\n//                     [\"input\", \"Action\"],\n//                     [\"input\", \"Validate\"],\n//                 ],\n//                 event => event.target.value\n//             );\n//         } else {\n//             selectElement.addEventListener(\"input\", function () {\n//                 storage.setValue(id, { value: getValue(/* isExport */ true) });\n//             });\n//         }\n//         if (this.data.combo) {\n//             this._setTextStyle(selectElement);\n//         } else {\n//             // Just use the default font size...\n//             // it's a bit hard to guess what is a good size.\n//         }\n//         this._setBackgroundColor(selectElement);\n//         this._setDefaultPropertiesFromJS(selectElement);\n//         this.container.append(selectElement);\n//         return this.container;\n//     }\n// }\nclass PopupAnnotationElement extends AnnotationElement {\n  // todo: props\n  constructor(parameters) {\n    const {\n      data,\n      elements\n    } = parameters;\n    super(parameters, {\n      isRenderable: AnnotationElement._hasPopupData(data)\n    });\n    // todo: props\n    this.elements = [];\n    this.elements = elements;\n    this.popup = null;\n  }\n  render() {\n    this.container.classList.add(\"popupAnnotation\");\n    const popup = this.popup = new PopupElement({\n      container: this.container,\n      color: this.data.color,\n      titleObj: this.data.titleObj,\n      modificationDate: this.data.modificationDate,\n      contentsObj: this.data.contentsObj,\n      richText: this.data.richText,\n      rect: this.data.rect,\n      parentRect: this.data.parentRect || null,\n      parent: this.parent,\n      elements: this.elements,\n      open: this.data.open\n    });\n    const elementIds = [];\n    for (const element of this.elements) {\n      element.popup = popup;\n      elementIds.push(element.data.id);\n      element.addHighlightArea();\n    }\n    this.container.setAttribute(\"aria-controls\", elementIds.map(id => `${AnnotationPrefix}${id}`).join(\",\"));\n    return this.container;\n  }\n}\nclass PopupElement {\n  constructor({\n    container,\n    color,\n    elements,\n    titleObj,\n    modificationDate,\n    contentsObj,\n    richText,\n    parent,\n    rect,\n    parentRect,\n    open\n  }) {\n    var _a;\n    _PopupElement_instances.add(this);\n    // todo: props\n    this.trigger = null;\n    // todo: props\n    _PopupElement_boundKeyDown.set(this, __classPrivateFieldGet(this, _PopupElement_instances, \"m\", _PopupElement_keyDown).bind(this));\n    _PopupElement_boundHide.set(this, __classPrivateFieldGet(this, _PopupElement_instances, \"m\", _PopupElement_hide).bind(this));\n    _PopupElement_boundShow.set(this, __classPrivateFieldGet(this, _PopupElement_instances, \"m\", _PopupElement_show).bind(this));\n    _PopupElement_boundToggle.set(this, __classPrivateFieldGet(this, _PopupElement_instances, \"m\", _PopupElement_toggle).bind(this));\n    _PopupElement_color.set(this, null);\n    _PopupElement_container.set(this, null);\n    _PopupElement_contentsObj.set(this, null);\n    _PopupElement_dateObj.set(this, null);\n    _PopupElement_elements.set(this, null);\n    _PopupElement_parent.set(this, null);\n    _PopupElement_parentRect.set(this, null);\n    _PopupElement_pinned.set(this, false);\n    _PopupElement_popup.set(this, null);\n    _PopupElement_position.set(this, null);\n    _PopupElement_rect.set(this, null);\n    _PopupElement_richText.set(this, null);\n    _PopupElement_titleObj.set(this, null);\n    _PopupElement_updates.set(this, null);\n    _PopupElement_wasVisible.set(this, false);\n    __classPrivateFieldSet(this, _PopupElement_container, container, \"f\");\n    __classPrivateFieldSet(this, _PopupElement_titleObj, titleObj, \"f\");\n    __classPrivateFieldSet(this, _PopupElement_contentsObj, contentsObj, \"f\");\n    __classPrivateFieldSet(this, _PopupElement_richText, richText, \"f\");\n    __classPrivateFieldSet(this, _PopupElement_parent, parent, \"f\");\n    __classPrivateFieldSet(this, _PopupElement_color, color, \"f\");\n    __classPrivateFieldSet(this, _PopupElement_rect, rect, \"f\");\n    __classPrivateFieldSet(this, _PopupElement_parentRect, parentRect, \"f\");\n    __classPrivateFieldSet(this, _PopupElement_elements, elements, \"f\");\n    // The modification date is shown in the popup instead of the creation\n    // date if it is available and can be parsed correctly, which is\n    // consistent with other viewers such as Adobe Acrobat.\n    __classPrivateFieldSet(this, _PopupElement_dateObj, PDFDateString.toDateObject(modificationDate), \"f\");\n    this.trigger = elements.flatMap(e => e.getElementsToTriggerPopup());\n    // Attach the event listeners to the trigger element.\n    for (const element of this.trigger) {\n      element.addEventListener(\"click\", __classPrivateFieldGet(this, _PopupElement_boundToggle, \"f\"));\n      element.addEventListener(\"mouseenter\", __classPrivateFieldGet(this, _PopupElement_boundShow, \"f\"));\n      element.addEventListener(\"mouseleave\", __classPrivateFieldGet(this, _PopupElement_boundHide, \"f\"));\n      element.classList.add(\"popupTriggerArea\");\n    }\n    // Attach the event listener to toggle the popup with the keyboard.\n    for (const element of elements) {\n      (_a = element.container) === null || _a === void 0 ? void 0 : _a.addEventListener(\"keydown\", __classPrivateFieldGet(this, _PopupElement_boundKeyDown, \"f\"));\n    }\n    __classPrivateFieldGet(this, _PopupElement_container, \"f\").hidden = true;\n    if (open) {\n      __classPrivateFieldGet(this, _PopupElement_instances, \"m\", _PopupElement_toggle).call(this);\n    }\n    // if (typeof PDFJSDev !== \"undefined\" && PDFJSDev.test(\"TESTING\")) {\n    // Since the popup is lazily created, we need to ensure that it'll be\n    // created and displayed during reference tests.\n    __classPrivateFieldGet(this, _PopupElement_parent, \"f\").popupShow.push(() => __awaiter(this, void 0, void 0, function* () {\n      if (__classPrivateFieldGet(this, _PopupElement_container, \"f\").hidden) {\n        __classPrivateFieldGet(this, _PopupElement_instances, \"m\", _PopupElement_show).call(this);\n      }\n    }));\n    // }\n  }\n  render() {\n    if (__classPrivateFieldGet(this, _PopupElement_popup, \"f\")) {\n      return;\n    }\n    const popup = __classPrivateFieldSet(this, _PopupElement_popup, document.createElement(\"div\"), \"f\");\n    popup.className = \"popup\";\n    if (__classPrivateFieldGet(this, _PopupElement_color, \"f\")) {\n      const baseColor = popup.style.outlineColor = Util.makeHexColor(\n      // @ts-expect-error TS(2556):\n      ...__classPrivateFieldGet(this, _PopupElement_color, \"f\"));\n      if (\n      // (typeof PDFJSDev !== \"undefined\" && PDFJSDev.test(\"MOZCENTRAL\")) ||\n      CSS.supports(\"background-color\", \"color-mix(in srgb, red 30%, white)\")) {\n        popup.style.backgroundColor = `color-mix(in srgb, ${baseColor} 30%, white)`;\n      } else {\n        // color-mix isn't supported in some browsers hence this version.\n        // See https://developer.mozilla.org/en-US/docs/Web/CSS/color_value/color-mix#browser_compatibility\n        // TODO: Use color-mix when it's supported everywhere.\n        // Enlighten the color.\n        const BACKGROUND_ENLIGHT = 0.7;\n        popup.style.backgroundColor = Util.makeHexColor(\n        // @ts-expect-error TS(2556):\n        ...__classPrivateFieldGet(this, _PopupElement_color, \"f\").map(c => Math.floor(BACKGROUND_ENLIGHT * (255 - c) + c)));\n      }\n    }\n    const header = document.createElement(\"span\");\n    header.className = \"header\";\n    const title = document.createElement(\"h1\");\n    header.append(title);\n    ({\n      dir: title.dir,\n      str: title.textContent\n    } = __classPrivateFieldGet(this, _PopupElement_titleObj, \"f\"));\n    popup.append(header);\n    if (__classPrivateFieldGet(this, _PopupElement_dateObj, \"f\")) {\n      const modificationDate = document.createElement(\"span\");\n      modificationDate.classList.add(\"popupDate\");\n      modificationDate.setAttribute(\"data-l10n-id\", \"pdfjs-annotation-date-string\");\n      modificationDate.setAttribute(\"data-l10n-args\", JSON.stringify({\n        date: __classPrivateFieldGet(this, _PopupElement_dateObj, \"f\").toLocaleDateString(),\n        time: __classPrivateFieldGet(this, _PopupElement_dateObj, \"f\").toLocaleTimeString()\n      }));\n      header.append(modificationDate);\n    }\n    const html = __classPrivateFieldGet(this, _PopupElement_instances, \"a\", _PopupElement_html_get);\n    if (html) {\n      // @ts-expect-error TS(2556):\n      XfaLayer.render({\n        xfaHtml: html,\n        intent: \"richText\",\n        div: popup\n      });\n      // @ts-expect-error TS(2556):\n      popup.lastChild.classList.add(\"richText\", \"popupContent\");\n    } else {\n      const contents = this._formatContents(__classPrivateFieldGet(this, _PopupElement_contentsObj, \"f\"));\n      popup.append(contents);\n    }\n    __classPrivateFieldGet(this, _PopupElement_container, \"f\").append(popup);\n  }\n  /**\n   * Format the contents of the popup by adding newlines where necessary.\n   *\n   * @private\n   * @param {Object<string, string>} contentsObj\n   * @memberof PopupElement\n   * @returns {HTMLParagraphElement}\n   */\n  _formatContents({\n    str,\n    dir\n  }) {\n    const p = document.createElement(\"p\");\n    p.classList.add(\"popupContent\");\n    p.dir = dir;\n    const lines = str.split(/(?:\\r\\n?|\\n)/);\n    for (let i = 0, ii = lines.length; i < ii; ++i) {\n      const line = lines[i];\n      p.append(document.createTextNode(line));\n      if (i < ii - 1) {\n        p.append(document.createElement(\"br\"));\n      }\n    }\n    return p;\n  }\n  updateEdited({\n    rect,\n    popupContent\n  }) {\n    var _a;\n    __classPrivateFieldSet(this, _PopupElement_updates, __classPrivateFieldGet(this, _PopupElement_updates, \"f\") || {\n      contentsObj: __classPrivateFieldGet(this, _PopupElement_contentsObj, \"f\"),\n      richText: __classPrivateFieldGet(this, _PopupElement_richText, \"f\")\n    }, \"f\");\n    if (rect) {\n      __classPrivateFieldSet(this, _PopupElement_position, null, \"f\");\n    }\n    if (popupContent) {\n      __classPrivateFieldSet(this, _PopupElement_richText, __classPrivateFieldGet(this, _PopupElement_instances, \"m\", _PopupElement_makePopupContent).call(this, popupContent), \"f\");\n      __classPrivateFieldSet(this, _PopupElement_contentsObj, null, \"f\");\n    }\n    (_a = __classPrivateFieldGet(this, _PopupElement_popup, \"f\")) === null || _a === void 0 ? void 0 : _a.remove();\n    __classPrivateFieldSet(this, _PopupElement_popup, null, \"f\");\n  }\n  resetEdited() {\n    var _a;\n    var _b, _c;\n    if (!__classPrivateFieldGet(this, _PopupElement_updates, \"f\")) {\n      return;\n    }\n    _b = this, _c = this, {\n      contentsObj: {\n        set value(_a) {\n          __classPrivateFieldSet(_b, _PopupElement_contentsObj, _a, \"f\");\n        }\n      }.value,\n      richText: {\n        set value(_a) {\n          __classPrivateFieldSet(_c, _PopupElement_richText, _a, \"f\");\n        }\n      }.value\n    } = __classPrivateFieldGet(this, _PopupElement_updates, \"f\");\n    __classPrivateFieldSet(this, _PopupElement_updates, null, \"f\");\n    (_a = __classPrivateFieldGet(this, _PopupElement_popup, \"f\")) === null || _a === void 0 ? void 0 : _a.remove();\n    __classPrivateFieldSet(this, _PopupElement_popup, null, \"f\");\n    __classPrivateFieldSet(this, _PopupElement_position, null, \"f\");\n  }\n  forceHide() {\n    __classPrivateFieldSet(this, _PopupElement_wasVisible, this.isVisible, \"f\");\n    if (!__classPrivateFieldGet(this, _PopupElement_wasVisible, \"f\")) {\n      return;\n    }\n    __classPrivateFieldGet(this, _PopupElement_container, \"f\").hidden = true;\n  }\n  maybeShow() {\n    if (!__classPrivateFieldGet(this, _PopupElement_wasVisible, \"f\")) {\n      return;\n    }\n    if (!__classPrivateFieldGet(this, _PopupElement_popup, \"f\")) {\n      __classPrivateFieldGet(this, _PopupElement_instances, \"m\", _PopupElement_show).call(this);\n    }\n    __classPrivateFieldSet(this, _PopupElement_wasVisible, false, \"f\");\n    __classPrivateFieldGet(this, _PopupElement_container, \"f\").hidden = false;\n  }\n  get isVisible() {\n    return __classPrivateFieldGet(this, _PopupElement_container, \"f\").hidden === false;\n  }\n}\n_PopupElement_boundKeyDown = new WeakMap(), _PopupElement_boundHide = new WeakMap(), _PopupElement_boundShow = new WeakMap(), _PopupElement_boundToggle = new WeakMap(), _PopupElement_color = new WeakMap(), _PopupElement_container = new WeakMap(), _PopupElement_contentsObj = new WeakMap(), _PopupElement_dateObj = new WeakMap(), _PopupElement_elements = new WeakMap(), _PopupElement_parent = new WeakMap(), _PopupElement_parentRect = new WeakMap(), _PopupElement_pinned = new WeakMap(), _PopupElement_popup = new WeakMap(), _PopupElement_position = new WeakMap(), _PopupElement_rect = new WeakMap(), _PopupElement_richText = new WeakMap(), _PopupElement_titleObj = new WeakMap(), _PopupElement_updates = new WeakMap(), _PopupElement_wasVisible = new WeakMap(), _PopupElement_instances = new WeakSet(), _PopupElement_html_get = function _PopupElement_html_get() {\n  const richText = __classPrivateFieldGet(this, _PopupElement_richText, \"f\");\n  const contentsObj = __classPrivateFieldGet(this, _PopupElement_contentsObj, \"f\");\n  if ((richText === null || richText === void 0 ? void 0 : richText.str) && (!(contentsObj === null || contentsObj === void 0 ? void 0 : contentsObj.str) || contentsObj.str === richText.str)) {\n    return __classPrivateFieldGet(this, _PopupElement_richText, \"f\").html || null;\n  }\n  return null;\n}, _PopupElement_fontSize_get = function _PopupElement_fontSize_get() {\n  var _a, _b, _c;\n  return ((_c = (_b = (_a = __classPrivateFieldGet(this, _PopupElement_instances, \"a\", _PopupElement_html_get)) === null || _a === void 0 ? void 0 : _a.attributes) === null || _b === void 0 ? void 0 : _b.style) === null || _c === void 0 ? void 0 : _c.fontSize) || 0;\n}, _PopupElement_fontColor_get = function _PopupElement_fontColor_get() {\n  var _a, _b, _c;\n  return ((_c = (_b = (_a = __classPrivateFieldGet(this, _PopupElement_instances, \"a\", _PopupElement_html_get)) === null || _a === void 0 ? void 0 : _a.attributes) === null || _b === void 0 ? void 0 : _b.style) === null || _c === void 0 ? void 0 : _c.color) || null;\n}, _PopupElement_makePopupContent = function _PopupElement_makePopupContent(text) {\n  const popupLines = [];\n  const popupContent = {\n    str: text,\n    html: {\n      name: \"div\",\n      attributes: {\n        dir: \"auto\"\n      },\n      children: [{\n        name: \"p\",\n        children: popupLines\n      }]\n    }\n  };\n  const lineAttributes = {\n    style: {\n      color: __classPrivateFieldGet(this, _PopupElement_instances, \"a\", _PopupElement_fontColor_get),\n      fontSize: __classPrivateFieldGet(this, _PopupElement_instances, \"a\", _PopupElement_fontSize_get) ? `calc(${__classPrivateFieldGet(this, _PopupElement_instances, \"a\", _PopupElement_fontSize_get)}px * var(--scale-factor))` : \"\"\n    }\n  };\n  for (const line of text.split(\"\\n\")) {\n    popupLines.push({\n      name: \"span\",\n      value: line,\n      attributes: lineAttributes\n    });\n  }\n  return popupContent;\n}, _PopupElement_keyDown = function _PopupElement_keyDown(event) {\n  if (event.altKey || event.shiftKey || event.ctrlKey || event.metaKey) {\n    return;\n  }\n  if (event.key === \"Enter\" || event.key === \"Escape\" && __classPrivateFieldGet(this, _PopupElement_pinned, \"f\")) {\n    __classPrivateFieldGet(this, _PopupElement_instances, \"m\", _PopupElement_toggle).call(this);\n  }\n}, _PopupElement_setPosition = function _PopupElement_setPosition() {\n  if (__classPrivateFieldGet(this, _PopupElement_position, \"f\") !== null) {\n    return;\n  }\n  const {\n    page: {\n      view\n    },\n    viewport: {\n      rawDims: {\n        pageWidth,\n        pageHeight,\n        pageX,\n        pageY\n      }\n    }\n  } = __classPrivateFieldGet(this, _PopupElement_parent, \"f\");\n  let useParentRect = !!__classPrivateFieldGet(this, _PopupElement_parentRect, \"f\");\n  let rect = useParentRect ? __classPrivateFieldGet(this, _PopupElement_parentRect, \"f\") : __classPrivateFieldGet(this, _PopupElement_rect, \"f\");\n  for (const element of __classPrivateFieldGet(this, _PopupElement_elements, \"f\")) {\n    if (!rect || Util.intersect(element.data.rect, rect) !== null) {\n      rect = element.data.rect;\n      useParentRect = true;\n      break;\n    }\n  }\n  const normalizedRect = Util.normalizeRect([rect[0], view[3] - rect[1] + view[1], rect[2], view[3] - rect[3] + view[1]]);\n  const HORIZONTAL_SPACE_AFTER_ANNOTATION = 5;\n  const parentWidth = useParentRect ? rect[2] - rect[0] + HORIZONTAL_SPACE_AFTER_ANNOTATION : 0;\n  const popupLeft = normalizedRect[0] + parentWidth;\n  const popupTop = normalizedRect[1];\n  __classPrivateFieldSet(this, _PopupElement_position, [100 * (popupLeft - pageX) / pageWidth, 100 * (popupTop - pageY) / pageHeight], \"f\");\n  const {\n    style\n  } = __classPrivateFieldGet(this, _PopupElement_container, \"f\");\n  style.left = `${__classPrivateFieldGet(this, _PopupElement_position, \"f\")[0]}%`;\n  style.top = `${__classPrivateFieldGet(this, _PopupElement_position, \"f\")[1]}%`;\n}, _PopupElement_toggle = function _PopupElement_toggle() {\n  __classPrivateFieldSet(this, _PopupElement_pinned, !__classPrivateFieldGet(this, _PopupElement_pinned, \"f\"), \"f\");\n  if (__classPrivateFieldGet(this, _PopupElement_pinned, \"f\")) {\n    __classPrivateFieldGet(this, _PopupElement_instances, \"m\", _PopupElement_show).call(this);\n    __classPrivateFieldGet(this, _PopupElement_container, \"f\").addEventListener(\"click\", __classPrivateFieldGet(this, _PopupElement_boundToggle, \"f\"));\n    __classPrivateFieldGet(this, _PopupElement_container, \"f\").addEventListener(\"keydown\", __classPrivateFieldGet(this, _PopupElement_boundKeyDown, \"f\"));\n  } else {\n    __classPrivateFieldGet(this, _PopupElement_instances, \"m\", _PopupElement_hide).call(this);\n    __classPrivateFieldGet(this, _PopupElement_container, \"f\").removeEventListener(\"click\", __classPrivateFieldGet(this, _PopupElement_boundToggle, \"f\"));\n    __classPrivateFieldGet(this, _PopupElement_container, \"f\").removeEventListener(\"keydown\", __classPrivateFieldGet(this, _PopupElement_boundKeyDown, \"f\"));\n  }\n}, _PopupElement_show = function _PopupElement_show() {\n  if (!__classPrivateFieldGet(this, _PopupElement_popup, \"f\")) {\n    this.render();\n  }\n  if (!this.isVisible) {\n    __classPrivateFieldGet(this, _PopupElement_instances, \"m\", _PopupElement_setPosition).call(this);\n    __classPrivateFieldGet(this, _PopupElement_container, \"f\").hidden = false;\n    __classPrivateFieldGet(this, _PopupElement_container, \"f\").style.zIndex = (parseInt(__classPrivateFieldGet(this, _PopupElement_container, \"f\").style.zIndex, 10) + 1000).toString();\n  } else if (__classPrivateFieldGet(this, _PopupElement_pinned, \"f\")) {\n    __classPrivateFieldGet(this, _PopupElement_container, \"f\").classList.add(\"focused\");\n  }\n}, _PopupElement_hide = function _PopupElement_hide() {\n  __classPrivateFieldGet(this, _PopupElement_container, \"f\").classList.remove(\"focused\");\n  if (__classPrivateFieldGet(this, _PopupElement_pinned, \"f\") || !this.isVisible) {\n    return;\n  }\n  __classPrivateFieldGet(this, _PopupElement_container, \"f\").hidden = true;\n  __classPrivateFieldGet(this, _PopupElement_container, \"f\").style.zIndex = (parseInt(__classPrivateFieldGet(this, _PopupElement_container, \"f\").style.zIndex, 10) - 1000).toString();\n};\nclass FreeTextAnnotationElement extends AnnotationElement {\n  // todo: props\n  constructor(parameters) {\n    super(parameters, {\n      isRenderable: true,\n      ignoreBorder: true\n    });\n    // todo: props\n    this.textContent = null;\n    this.textPosition = null;\n    this.textContent = parameters.data.textContent;\n    this.textPosition = parameters.data.textPosition;\n    this.annotationEditorType = AnnotationEditorType.FREETEXT;\n  }\n  render() {\n    // this.container.classList.add(\"freeTextAnnotation\");\n    // this.container.classList.add(\"freeTextAnnotation\");\n    this.container.classList.add(\"k-free-text-annotation\");\n    if (this.textContent) {\n      const content = document.createElement(\"div\");\n      // content.classList.add(\"annotationTextContent\");\n      content.classList.add(\"k-annotation-text-content\");\n      content.setAttribute(\"role\", \"comment\");\n      for (const line of this.textContent) {\n        const lineSpan = document.createElement(\"span\");\n        lineSpan.textContent = line;\n        content.append(lineSpan);\n      }\n      this.container.append(content);\n    }\n    // if (!this.data.popupRef && this.hasPopupData) {\n    //     this._createPopup();\n    // }\n    this._editOnDoubleClick();\n    return this.container;\n  }\n}\n// class LineAnnotationElement extends AnnotationElement {\n//     #line = null;\n//     constructor(parameters) {\n//         super(parameters, { isRenderable: true, ignoreBorder: true });\n//     }\n//     render() {\n//         this.container.classList.add(\"lineAnnotation\");\n//         // Create an invisible line with the same starting and ending coordinates\n//         // that acts as the trigger for the popup. Only the line itself should\n//         // trigger the popup, not the entire container.\n//         const data = this.data;\n//         const { width, height } = getRectDims(data.rect);\n//         const svg = this.svgFactory.create(\n//             width,\n//             height,\n//             /* skipDimensions = */ true\n//         );\n//         // PDF coordinates are calculated from a bottom left origin, so transform\n//         // the line coordinates to a top left origin for the SVG element.\n//         const line = (this.#line = this.svgFactory.createElement(\"svg:line\"));\n//         line.setAttribute(\"x1\", data.rect[2] - data.lineCoordinates[0]);\n//         line.setAttribute(\"y1\", data.rect[3] - data.lineCoordinates[1]);\n//         line.setAttribute(\"x2\", data.rect[2] - data.lineCoordinates[2]);\n//         line.setAttribute(\"y2\", data.rect[3] - data.lineCoordinates[3]);\n//         // Ensure that the 'stroke-width' is always non-zero, since otherwise it\n//         // won't be possible to open/close the popup (note e.g. issue 11122).\n//         line.setAttribute(\"stroke-width\", data.borderStyle.width || 1);\n//         line.setAttribute(\"stroke\", \"transparent\");\n//         line.setAttribute(\"fill\", \"transparent\");\n//         svg.append(line);\n//         this.container.append(svg);\n//         // Create the popup ourselves so that we can bind it to the line instead\n//         // of to the entire container (which is the default).\n//         if (!data.popupRef && this.hasPopupData) {\n//             this._createPopup();\n//         }\n//         return this.container;\n//     }\n//     getElementsToTriggerPopup() {\n//         return this.#line;\n//     }\n//     addHighlightArea() {\n//         this.container.classList.add(\"highlightArea\");\n//     }\n// }\n// class SquareAnnotationElement extends AnnotationElement {\n//     #square = null;\n//     constructor(parameters) {\n//         super(parameters, { isRenderable: true, ignoreBorder: true });\n//     }\n//     render() {\n//         this.container.classList.add(\"squareAnnotation\");\n//         // Create an invisible square with the same rectangle that acts as the\n//         // trigger for the popup. Only the square itself should trigger the\n//         // popup, not the entire container.\n//         const data = this.data;\n//         const { width, height } = getRectDims(data.rect);\n//         const svg = this.svgFactory.create(\n//             width,\n//             height,\n//             /* skipDimensions = */ true\n//         );\n//         // The browser draws half of the borders inside the square and half of\n//         // the borders outside the square by default. This behavior cannot be\n//         // changed programmatically, so correct for that here.\n//         const borderWidth = data.borderStyle.width;\n//         const square = (this.#square = this.svgFactory.createElement(\"svg:rect\"));\n//         square.setAttribute(\"x\", borderWidth / 2);\n//         square.setAttribute(\"y\", borderWidth / 2);\n//         square.setAttribute(\"width\", width - borderWidth);\n//         square.setAttribute(\"height\", height - borderWidth);\n//         // Ensure that the 'stroke-width' is always non-zero, since otherwise it\n//         // won't be possible to open/close the popup (note e.g. issue 11122).\n//         square.setAttribute(\"stroke-width\", borderWidth || 1);\n//         square.setAttribute(\"stroke\", \"transparent\");\n//         square.setAttribute(\"fill\", \"transparent\");\n//         svg.append(square);\n//         this.container.append(svg);\n//         // Create the popup ourselves so that we can bind it to the square instead\n//         // of to the entire container (which is the default).\n//         if (!data.popupRef && this.hasPopupData) {\n//             this._createPopup();\n//         }\n//         return this.container;\n//     }\n//     getElementsToTriggerPopup() {\n//         return this.#square;\n//     }\n//     addHighlightArea() {\n//         this.container.classList.add(\"highlightArea\");\n//     }\n// }\n// class CircleAnnotationElement extends AnnotationElement {\n//     #circle = null;\n//     constructor(parameters) {\n//         super(parameters, { isRenderable: true, ignoreBorder: true });\n//     }\n//     render() {\n//         this.container.classList.add(\"circleAnnotation\");\n//         // Create an invisible circle with the same ellipse that acts as the\n//         // trigger for the popup. Only the circle itself should trigger the\n//         // popup, not the entire container.\n//         const data = this.data;\n//         const { width, height } = getRectDims(data.rect);\n//         const svg = this.svgFactory.create(\n//             width,\n//             height,\n//             /* skipDimensions = */ true\n//         );\n//         // The browser draws half of the borders inside the circle and half of\n//         // the borders outside the circle by default. This behavior cannot be\n//         // changed programmatically, so correct for that here.\n//         const borderWidth = data.borderStyle.width;\n//         const circle = (this.#circle =\n//             this.svgFactory.createElement(\"svg:ellipse\"));\n//         circle.setAttribute(\"cx\", width / 2);\n//         circle.setAttribute(\"cy\", height / 2);\n//         circle.setAttribute(\"rx\", width / 2 - borderWidth / 2);\n//         circle.setAttribute(\"ry\", height / 2 - borderWidth / 2);\n//         // Ensure that the 'stroke-width' is always non-zero, since otherwise it\n//         // won't be possible to open/close the popup (note e.g. issue 11122).\n//         circle.setAttribute(\"stroke-width\", borderWidth || 1);\n//         circle.setAttribute(\"stroke\", \"transparent\");\n//         circle.setAttribute(\"fill\", \"transparent\");\n//         svg.append(circle);\n//         this.container.append(svg);\n//         // Create the popup ourselves so that we can bind it to the circle instead\n//         // of to the entire container (which is the default).\n//         if (!data.popupRef && this.hasPopupData) {\n//             this._createPopup();\n//         }\n//         return this.container;\n//     }\n//     getElementsToTriggerPopup() {\n//         return this.#circle;\n//     }\n//     addHighlightArea() {\n//         this.container.classList.add(\"highlightArea\");\n//     }\n// }\n// class PolylineAnnotationElement extends AnnotationElement {\n//     // todo: props\n//     containerClassName: any = null;\n//     svgElementName: any = null;\n//     // todo: props\n//     #polyline = null;\n//     constructor(parameters) {\n//         super(parameters, { isRenderable: true, ignoreBorder: true });\n//         this.containerClassName = \"polylineAnnotation\";\n//         this.svgElementName = \"svg:polyline\";\n//     }\n//     render() {\n//         this.container.classList.add(this.containerClassName);\n//         // Create an invisible polyline with the same points that acts as the\n//         // trigger for the popup. Only the polyline itself should trigger the\n//         // popup, not the entire container.\n//         const {\n//             data: { rect, vertices, borderStyle, popupRef },\n//         } = this;\n//         if (!vertices) {\n//             return this.container;\n//         }\n//         const { width, height } = getRectDims(rect);\n//         const svg = this.svgFactory.create(\n//             width,\n//             height,\n//             /* skipDimensions = */ true\n//         );\n//         // Convert the vertices array to a single points string that the SVG\n//         // polyline element expects (\"x1,y1 x2,y2 ...\"). PDF coordinates are\n//         // calculated from a bottom left origin, so transform the polyline\n//         // coordinates to a top left origin for the SVG element.\n//         const pointsArray = [];\n//         for (let i = 0, ii = vertices.length; i < ii; i += 2) {\n//             const x = vertices[i] - rect[0];\n//             const y = rect[3] - vertices[i + 1];\n//             pointsArray.push(`${x},${y}`);\n//         }\n//         const points = pointsArray.join(\" \");\n//         const polyline = (this.#polyline = this.svgFactory.createElement(\n//             this.svgElementName\n//         ));\n//         polyline.setAttribute(\"points\", points);\n//         // Ensure that the 'stroke-width' is always non-zero, since otherwise it\n//         // won't be possible to open/close the popup (note e.g. issue 11122).\n//         polyline.setAttribute(\"stroke-width\", borderStyle.width || 1);\n//         polyline.setAttribute(\"stroke\", \"transparent\");\n//         polyline.setAttribute(\"fill\", \"transparent\");\n//         svg.append(polyline);\n//         this.container.append(svg);\n//         // Create the popup ourselves so that we can bind it to the polyline\n//         // instead of to the entire container (which is the default).\n//         if (!popupRef && this.hasPopupData) {\n//             this._createPopup();\n//         }\n//         return this.container;\n//     }\n//     getElementsToTriggerPopup() {\n//         return this.#polyline;\n//     }\n//     addHighlightArea() {\n//         this.container.classList.add(\"highlightArea\");\n//     }\n// }\n// class PolygonAnnotationElement extends PolylineAnnotationElement {\n//     constructor(parameters) {\n//         // Polygons are specific forms of polylines, so reuse their logic.\n//         super(parameters);\n//         this.containerClassName = \"polygonAnnotation\";\n//         this.svgElementName = \"svg:polygon\";\n//     }\n// }\n// class CaretAnnotationElement extends AnnotationElement {\n//     constructor(parameters) {\n//         super(parameters, { isRenderable: true, ignoreBorder: true });\n//     }\n//     render() {\n//         this.container.classList.add(\"caretAnnotation\");\n//         if (!this.data.popupRef && this.hasPopupData) {\n//             this._createPopup();\n//         }\n//         return this.container;\n//     }\n// }\n// class InkAnnotationElement extends AnnotationElement {\n//     // todo: props\n//     containerClassName: any = null;\n//     svgElementName: any = null;\n//     // todo: props\n//     #polylines = [];\n//     constructor(parameters) {\n//         super(parameters, { isRenderable: true, ignoreBorder: true });\n//         this.containerClassName = \"inkAnnotation\";\n//         // Use the polyline SVG element since it allows us to use coordinates\n//         // directly and to draw both straight lines and curves.\n//         this.svgElementName = \"svg:polyline\";\n//         this.annotationEditorType = AnnotationEditorType.INK;\n//     }\n//     render() {\n//         this.container.classList.add(this.containerClassName);\n//         // Create an invisible polyline with the same points that acts as the\n//         // trigger for the popup.\n//         const {\n//             data: { rect, inkLists, borderStyle, popupRef },\n//         } = this;\n//         const { width, height } = getRectDims(rect);\n//         const svg = this.svgFactory.create(\n//             width,\n//             height,\n//             /* skipDimensions = */ true\n//         );\n//         for (const inkList of inkLists) {\n//             // Convert the ink list to a single points string that the SVG\n//             // polyline element expects (\"x1,y1 x2,y2 ...\"). PDF coordinates are\n//             // calculated from a bottom left origin, so transform the polyline\n//             // coordinates to a top left origin for the SVG element.\n//             const pointsArray = [];\n//             for (let i = 0, ii = inkList.length; i < ii; i += 2) {\n//                 const x = inkList[i] - rect[0];\n//                 const y = rect[3] - inkList[i + 1];\n//                 pointsArray.push(`${x},${y}`);\n//             }\n//             const points = pointsArray.join(\" \");\n//             const polyline = this.svgFactory.createElement(this.svgElementName);\n//             this.#polylines.push(polyline);\n//             polyline.setAttribute(\"points\", points);\n//             // Ensure that the 'stroke-width' is always non-zero, since otherwise it\n//             // won't be possible to open/close the popup (note e.g. issue 11122).\n//             polyline.setAttribute(\"stroke-width\", borderStyle.width || 1);\n//             polyline.setAttribute(\"stroke\", \"transparent\");\n//             polyline.setAttribute(\"fill\", \"transparent\");\n//             // Create the popup ourselves so that we can bind it to the polyline\n//             // instead of to the entire container (which is the default).\n//             if (!popupRef && this.hasPopupData) {\n//                 this._createPopup();\n//             }\n//             svg.append(polyline);\n//         }\n//         this.container.append(svg);\n//         return this.container;\n//     }\n//     getElementsToTriggerPopup() {\n//         return this.#polylines;\n//     }\n//     addHighlightArea() {\n//         this.container.classList.add(\"highlightArea\");\n//     }\n// }\nclass HighlightAnnotationElement extends AnnotationElement {\n  constructor(parameters) {\n    super(parameters, {\n      isRenderable: true,\n      ignoreBorder: true,\n      createQuadrilaterals: true\n    });\n  }\n  render() {\n    // if (!this.data.popupRef && this.hasPopupData) {\n    //     this._createPopup();\n    // }\n    // this.container.classList.add(\"highlightAnnotation\");\n    this.container.classList.add(\"k-highlight-annotation\");\n    return this.container;\n  }\n}\n// class UnderlineAnnotationElement extends AnnotationElement {\n//     constructor(parameters) {\n//         super(parameters, {\n//             isRenderable: true,\n//             ignoreBorder: true,\n//             createQuadrilaterals: true,\n//         });\n//     }\n//     render() {\n//         if (!this.data.popupRef && this.hasPopupData) {\n//             this._createPopup();\n//         }\n//         this.container.classList.add(\"underlineAnnotation\");\n//         return this.container;\n//     }\n// }\n// class SquigglyAnnotationElement extends AnnotationElement {\n//     constructor(parameters) {\n//         super(parameters, {\n//             isRenderable: true,\n//             ignoreBorder: true,\n//             createQuadrilaterals: true,\n//         });\n//     }\n//     render() {\n//         if (!this.data.popupRef && this.hasPopupData) {\n//             this._createPopup();\n//         }\n//         this.container.classList.add(\"squigglyAnnotation\");\n//         return this.container;\n//     }\n// }\n// class StrikeOutAnnotationElement extends AnnotationElement {\n//     constructor(parameters) {\n//         super(parameters, {\n//             isRenderable: true,\n//             ignoreBorder: true,\n//             createQuadrilaterals: true,\n//         });\n//     }\n//     render() {\n//         if (!this.data.popupRef && this.hasPopupData) {\n//             this._createPopup();\n//         }\n//         this.container.classList.add(\"strikeoutAnnotation\");\n//         return this.container;\n//     }\n// }\n// class StampAnnotationElement extends AnnotationElement {\n//     constructor(parameters) {\n//         super(parameters, { isRenderable: true, ignoreBorder: true });\n//     }\n//     render() {\n//         this.container.classList.add(\"stampAnnotation\");\n//         if (!this.data.popupRef && this.hasPopupData) {\n//             this._createPopup();\n//         }\n//         return this.container;\n//     }\n// }\n// class FileAttachmentAnnotationElement extends AnnotationElement {\n//     // todo: props\n//     filename: any = null;\n//     content: any = null;\n//     // todo: props\n//     #trigger = null;\n//     constructor(parameters) {\n//         super(parameters, { isRenderable: true });\n//         const { file } = this.data;\n//         this.filename = file.filename;\n//         this.content = file.content;\n//         this.linkService.eventBus?.dispatch(\"fileattachmentannotation\", {\n//             source: this,\n//             ...file,\n//         });\n//     }\n//     render() {\n//         this.container.classList.add(\"fileAttachmentAnnotation\");\n//         const { container, data } = this;\n//         let trigger;\n//         if (data.hasAppearance || data.fillAlpha === 0) {\n//             trigger = document.createElement(\"div\");\n//         } else {\n//             // Unfortunately it seems that it's not clearly specified exactly what\n//             // names are actually valid, since Table 184 contains:\n//             //   Conforming readers shall provide predefined icon appearances for at\n//             //   least the following standard names: GraphPushPin, PaperclipTag.\n//             //   Additional names may be supported as well. Default value: PushPin.\n//             trigger = document.createElement(\"img\");\n//             trigger.src = `${this.imageResourcesPath}annotation-${/paperclip/i.test(data.name) ?\n//                 \"paperclip\" : \"pushpin\"}.svg`;\n//             if (data.fillAlpha && data.fillAlpha < 1) {\n//                 trigger.style = `filter: opacity(${Math.round(\n//                     data.fillAlpha * 100\n//                 )}%);`;\n//                 // todo: debug\n//                 // if (typeof PDFJSDev !== \"undefined\" && PDFJSDev.test(\"TESTING\")) {\n//                 //     this.container.classList.add(\"hasFillAlpha\");\n//                 // }\n//             }\n//         }\n//         trigger.addEventListener(\"dblclick\", this.#download.bind(this));\n//         this.#trigger = trigger;\n//         const { isMac } = FeatureTest.platform;\n//         container.addEventListener(\"keydown\", evt => {\n//             if (evt.key === \"Enter\" && (isMac ? evt.metaKey : evt.ctrlKey)) {\n//                 this.#download();\n//             }\n//         });\n//         if (!data.popupRef && this.hasPopupData) {\n//             this._createPopup();\n//         } else {\n//             trigger.classList.add(\"popupTriggerArea\");\n//         }\n//         container.append(trigger);\n//         return container;\n//     }\n//     getElementsToTriggerPopup() {\n//         return this.#trigger;\n//     }\n//     addHighlightArea() {\n//         this.container.classList.add(\"highlightArea\");\n//     }\n//     /**\n//      * Download the file attachment associated with this annotation.\n//      */\n//     #download() {\n//         this.downloadManager?.openOrDownloadData(this.content, this.filename);\n//     }\n// }\n/**\n * Manage the layer containing all the annotations.\n */\nclass AnnotationLayer {\n  constructor({\n    div,\n    accessibilityManager,\n    annotationCanvasMap,\n    annotationEditorUIManager,\n    page,\n    viewport\n  }) {\n    _AnnotationLayer_instances.add(this);\n    this.div = null;\n    this.page = null;\n    this.viewport = null;\n    this.zIndex = null;\n    this._annotationEditorUIManager = null;\n    _AnnotationLayer_accessibilityManager.set(this, null);\n    _AnnotationLayer_annotationCanvasMap.set(this, null);\n    _AnnotationLayer_editableAnnotations.set(this, new Map());\n    this.div = div;\n    __classPrivateFieldSet(this, _AnnotationLayer_accessibilityManager, accessibilityManager, \"f\");\n    __classPrivateFieldSet(this, _AnnotationLayer_annotationCanvasMap, annotationCanvasMap, \"f\");\n    this.page = page;\n    this.viewport = viewport;\n    this.zIndex = 0;\n    this._annotationEditorUIManager = annotationEditorUIManager;\n    // if (typeof PDFJSDev !== \"undefined\" && PDFJSDev.test(\"TESTING\")) {\n    //     // For testing purposes.\n    //     Object.defineProperty(this, \"showPopups\", {\n    //         value: async () => {\n    //             for (const show of this.popupShow) {\n    //                 await show();\n    //             }\n    //         },\n    //     });\n    //     this.popupShow = [];\n    // }\n  }\n  // todo: ported from AnnotationLayerBuilder\n  hide() {\n    if (!this.div) {\n      return;\n    }\n    this.div.hidden = true;\n  }\n  // todo: ported from AnnotationLayerBuilder\n  hasEditableAnnotations() {\n    return __classPrivateFieldGet(this, _AnnotationLayer_editableAnnotations, \"f\").size > 0;\n  }\n  /**\n   * Render a new annotation layer with all annotation elements.\n   *\n   * @param {AnnotationLayerParameters} params\n   * @memberof AnnotationLayer\n   */\n  // async render(params) {\n  render(params) {\n    var _a;\n    const {\n      annotations\n    } = params;\n    const layer = this.div;\n    setLayerDimensions(layer, this.viewport);\n    const popupToElements = new Map();\n    const elementParams = {\n      data: null,\n      layer,\n      linkService: params.linkService,\n      downloadManager: params.downloadManager,\n      imageResourcesPath: params.imageResourcesPath || \"\",\n      renderForms: params.renderForms !== false,\n      svgFactory: new DOMSVGFactory(),\n      annotationStorage: params.annotationStorage,\n      enableScripting: params.enableScripting === true,\n      hasJSActions: params.hasJSActions,\n      fieldObjects: params.fieldObjects,\n      parent: this,\n      elements: null\n    };\n    for (const data of annotations) {\n      if (data.noHTML) {\n        continue;\n      }\n      const isPopupAnnotation = data.annotationType === AnnotationType.POPUP;\n      if (!isPopupAnnotation) {\n        const {\n          width,\n          height\n        } = getRectDims(data.rect);\n        if (width <= 0 || height <= 0) {\n          continue; // Ignore empty annotations.\n        }\n      } else {\n        const elements = popupToElements.get(data.id);\n        if (!elements) {\n          // Ignore popup annotations without a corresponding annotation.\n          continue;\n        }\n        elementParams.elements = elements;\n      }\n      elementParams.data = data;\n      const element = AnnotationElementFactory.create(elementParams);\n      if (!element.isRenderable) {\n        continue;\n      }\n      if (!isPopupAnnotation && data.popupRef) {\n        const elements = popupToElements.get(data.popupRef);\n        if (!elements) {\n          popupToElements.set(data.popupRef, [element]);\n        } else {\n          elements.push(element);\n        }\n      }\n      const rendered = element.render();\n      if (data.hidden) {\n        rendered.style.visibility = \"hidden\";\n      }\n      __classPrivateFieldGet(this, _AnnotationLayer_instances, \"m\", _AnnotationLayer_appendElement).call(this, rendered, data.id);\n      if (element._isEditable) {\n        __classPrivateFieldGet(this, _AnnotationLayer_editableAnnotations, \"f\").set(element.data.id, element);\n        (_a = this._annotationEditorUIManager) === null || _a === void 0 ? void 0 : _a.renderAnnotationElement(element);\n      }\n    }\n    __classPrivateFieldGet(this, _AnnotationLayer_instances, \"m\", _AnnotationLayer_setAnnotationCanvasMap).call(this);\n  }\n  /**\n   * Update the annotation elements on existing annotation layer.\n   *\n   * @param {AnnotationLayerParameters} viewport\n   * @memberof AnnotationLayer\n   */\n  update({\n    viewport\n  }) {\n    const layer = this.div;\n    this.viewport = viewport;\n    // setLayerDimensions(layer, { rotation: viewport.rotation });\n    setLayerDimensions(layer, viewport);\n    __classPrivateFieldGet(this, _AnnotationLayer_instances, \"m\", _AnnotationLayer_setAnnotationCanvasMap).call(this);\n    layer.hidden = false;\n  }\n  getEditableAnnotations() {\n    return Array.from(__classPrivateFieldGet(this, _AnnotationLayer_editableAnnotations, \"f\").values());\n  }\n  getEditableAnnotation(id) {\n    return __classPrivateFieldGet(this, _AnnotationLayer_editableAnnotations, \"f\").get(id);\n  }\n}\n_AnnotationLayer_accessibilityManager = new WeakMap(), _AnnotationLayer_annotationCanvasMap = new WeakMap(), _AnnotationLayer_editableAnnotations = new WeakMap(), _AnnotationLayer_instances = new WeakSet(), _AnnotationLayer_appendElement = function _AnnotationLayer_appendElement(element, id) {\n  var _a;\n  const contentElement = element.firstChild || element;\n  contentElement.id = `${AnnotationPrefix}${id}`;\n  this.div.append(element);\n  (_a = __classPrivateFieldGet(this, _AnnotationLayer_accessibilityManager, \"f\")) === null || _a === void 0 ? void 0 : _a.moveElementInDOM(this.div, element, contentElement, /* isRemovable = */false);\n}, _AnnotationLayer_setAnnotationCanvasMap = function _AnnotationLayer_setAnnotationCanvasMap() {\n  if (!__classPrivateFieldGet(this, _AnnotationLayer_annotationCanvasMap, \"f\")) {\n    return;\n  }\n  const layer = this.div;\n  for (const [id, canvas] of __classPrivateFieldGet(this, _AnnotationLayer_annotationCanvasMap, \"f\")) {\n    const element = layer.querySelector(`[data-annotation-id=\"${id}\"]`);\n    if (!element) {\n      continue;\n    }\n    canvas.className = \"annotationContent k-annotation-content\";\n    const {\n      firstChild\n    } = element;\n    if (!firstChild) {\n      element.append(canvas);\n    } else if (firstChild.nodeName === \"CANVAS\") {\n      firstChild.replaceWith(canvas);\n    } else if (!firstChild.classList.contains(\"annotationContent\") || !firstChild.classList.contains(\"k-annotation-content\")) {\n      firstChild.before(canvas);\n    } else {\n      firstChild.after(canvas);\n    }\n  }\n  __classPrivateFieldGet(this, _AnnotationLayer_annotationCanvasMap, \"f\").clear();\n};\nexport { AnnotationLayer, FreeTextAnnotationElement\n// InkAnnotationElement,\n// StampAnnotationElement,\n};", "map": {"version": 3, "names": ["_AnnotationElement_instances", "_AnnotationElement_updates", "_AnnotationElement_hasBorder", "_AnnotationElement_popupElement", "_AnnotationElement_setRectEdited", "_LinkAnnotationElement_instances", "_LinkAnnotationElement_setInternalLink", "_LinkAnnotationElement_bindAttachment", "_LinkAnnotationElement_bindSetOCGState", "_PopupElement_instances", "_PopupElement_boundKeyDown", "_PopupElement_boundHide", "_PopupElement_boundShow", "_PopupElement_boundToggle", "_PopupElement_color", "_PopupElement_container", "_PopupElement_contentsObj", "_PopupElement_dateObj", "_PopupElement_elements", "_PopupElement_parent", "_PopupElement_parentRect", "_PopupElement_pinned", "_PopupElement_popup", "_PopupElement_position", "_PopupElement_rect", "_PopupElement_richText", "_PopupElement_titleObj", "_PopupElement_updates", "_PopupElement_wasVisible", "_PopupElement_html_get", "_PopupElement_fontSize_get", "_PopupElement_fontColor_get", "_PopupElement_makePopupContent", "_PopupElement_keyDown", "_PopupElement_setPosition", "_PopupElement_toggle", "_PopupElement_show", "_PopupElement_hide", "_AnnotationLayer_instances", "_AnnotationLayer_accessibilityManager", "_AnnotationLayer_annotationCanvasMap", "_AnnotationLayer_editableAnnotations", "_AnnotationLayer_appendElement", "_AnnotationLayer_setAnnotationCanvasMap", "__awaiter", "__classPrivateFieldGet", "__classPrivateFieldSet", "AnnotationEditorType", "DOMSVGFactory", "FeatureTest", "PDFDateString", "setLayerDimensions", "shadow", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AnnotationBorderStyleType", "AnnotationPrefix", "AnnotationType", "LINE_FACTOR", "ColorConverters", "DEFAULT_TAB_INDEX", "DEFAULT_FONT_SIZE", "GetElementsByNameSet", "WeakSet", "getRectDims", "rect", "width", "height", "AnnotationElementFactory", "create", "parameters", "subtype", "data", "annotationType", "LINK", "LinkAnnotationElement", "WIDGET", "fieldType", "TextWidgetAnnotationElement", "FREETEXT", "FreeTextAnnotationElement", "HIGHLIGHT", "HighlightAnnotationElement", "AnnotationElement", "constructor", "isRenderable", "ignoreBorder", "createQuadrilaterals", "add", "layer", "linkService", "downloadManager", "imageResourcesPath", "renderForms", "svgFactory", "annotationStorage", "enableScripting", "hasJSActions", "_fieldObjects", "parent", "container", "popup", "annotationEditorType", "set", "fieldObjects", "_createContainer", "_createQuadrilaterals", "_hasPopupData", "titleObj", "contentsObj", "richText", "str", "_isEditable", "isEditable", "hasPopupData", "updateEdited", "params", "_a", "slice", "call", "resetEdited", "page", "viewport", "document", "createElement", "setAttribute", "id", "WidgetAnnotationElement", "tabIndex", "style", "zIndex", "toString", "popupRef", "alternativeText", "title", "noRotate", "PopupAnnotationElement", "rotation", "rotationValue", "hasOwnCanvas", "setRotation", "borderStyle", "borderWidth", "horizontalRadius", "horizontalCornerRadius", "verticalRadius", "verticalCornerRadius", "radius", "borderRadius", "RadioButtonWidgetAnnotationElement", "SOLID", "DASHED", "BEVELED", "INSET", "UNDERLINE", "borderBottomStyle", "borderColor", "makeHexColor", "normalizeRect", "view", "pageWidth", "pageHeight", "pageX", "pageY", "rawDims", "left", "top", "angle", "elementWidth", "elementHeight", "_commonActions", "setColor", "jsName", "styleName", "event", "color", "detail", "colorType", "colorArray", "target", "setValue", "display", "hidden", "visibility", "noView", "noPrint", "print", "focus", "setTimeout", "preventScroll", "userName", "readonly", "disabled", "required", "_setRequired", "bgColor", "fillColor", "fgColor", "textColor", "strokeColor", "_dispatchEventFromSandbox", "actions", "jsEvent", "commonActions", "name", "Object", "keys", "action", "_setDefaultPropertiesFromJS", "element", "storedData", "getRawValue", "actionName", "entries", "eventProxy", "quadPoints", "rectBlX", "rectBlY", "rectTrX", "rectTrY", "map", "x", "Math", "fround", "length", "trX", "trY", "blX", "blY", "subarray", "svgBuffer", "svg", "defs", "append", "clipPath", "i", "ii", "y", "rectWidth", "rectHeight", "push", "backgroundImage", "join", "_createPopup", "render", "_getElementsByName", "skipId", "fields", "field<PERSON>bj", "exportValues", "exportValue", "dom<PERSON>lement", "querySelector", "has", "getElementsByName", "getAttribute", "show", "maybeShow", "hide", "forceHide", "getElementsToTriggerPopup", "addHighlightArea", "triggers", "Array", "isArray", "_editOnDoubleClick", "mode", "editId", "addEventListener", "_b", "eventBus", "dispatch", "source", "isRequired", "removeAttribute", "WeakMap", "currentRect", "splice", "options", "isTooltipOnly", "link", "isBound", "url", "addLinkAttributes", "newWindow", "_bindNamedAction", "attachment", "attachmentDest", "setOCGState", "dest", "_bindLink", "Action", "_bindJSAction", "resetForm", "_bindResetFormAction", "classList", "destination", "href", "getDestinationHash", "onclick", "goToDestination", "getAnchorUrl", "executeNamedAction", "Map", "get", "otherClickAction", "reset<PERSON><PERSON><PERSON><PERSON>s", "refs", "resetFormRefs", "include", "allFields", "fieldIds", "Set", "fieldName", "values", "field", "storage", "allIds", "type", "value", "defaultValue", "dispatchEvent", "Event", "ids", "description", "openOrDownloadData", "content", "filename", "executeSetOCGState", "showElementAndHideCanvas", "previousSibling", "nodeName", "_getKeyModifier", "platform", "isMac", "metaKey", "ctrl<PERSON>ey", "_setEventListener", "elementData", "baseName", "eventName", "valueGetter", "includes", "shift", "shift<PERSON>ey", "modifier", "focused", "relatedTarget", "_setEventListeners", "names", "getter", "_c", "Blur", "Focus", "_setBackgroundColor", "backgroundColor", "_setTextStyle", "TEXT_ALIGNMENT", "fontColor", "defaultAppearanceData", "fontSize", "computedFontSize", "BORDER_SIZE", "roundToOneDecimal", "round", "multiLine", "abs", "numberOfLines", "lineHeight", "min", "textAlignment", "textAlign", "has<PERSON><PERSON><PERSON><PERSON>", "fieldValue", "setPropertyOnSiblings", "base", "key", "keyInStorage", "getValue", "textContent", "maxLen", "charLimit", "fieldFormattedValues", "formattedValue", "comb", "replaceAll", "userValue", "lastCommittedValue", "<PERSON><PERSON><PERSON>", "doNotScroll", "overflowY", "overflowX", "readOnly", "max<PERSON><PERSON><PERSON>", "defaultFieldValue", "blurListener", "undefined", "scrollLeft", "activeElement", "sel<PERSON><PERSON><PERSON>", "setSelectionRange", "will<PERSON><PERSON><PERSON>", "selStart", "selectionStart", "selEnd", "selectionEnd", "_blurListener", "Keystroke", "inputType", "match", "substring", "preventDefault", "change", "<PERSON><PERSON><PERSON><PERSON>", "combWidth", "letterSpacing", "verticalAlign", "buttonValue", "radio", "checked", "pdfButtonValue", "curChecked", "elements", "PopupElement", "modificationDate", "parentRect", "open", "elementIds", "trigger", "bind", "toDateObject", "flatMap", "e", "popupShow", "className", "baseColor", "outlineColor", "CSS", "supports", "BACKGROUND_ENLIGHT", "c", "floor", "header", "dir", "JSON", "stringify", "date", "toLocaleDateString", "time", "toLocaleTimeString", "html", "xfaHtml", "intent", "div", "<PERSON><PERSON><PERSON><PERSON>", "contents", "_formatContents", "p", "lines", "split", "line", "createTextNode", "popup<PERSON><PERSON>nt", "remove", "isVisible", "attributes", "text", "popupLines", "children", "lineAttributes", "altKey", "useParentRect", "intersect", "normalizedRect", "HORIZONTAL_SPACE_AFTER_ANNOTATION", "parentWidth", "popupLeft", "popupTop", "removeEventListener", "parseInt", "textPosition", "lineSpan", "Annotation<PERSON><PERSON>er", "accessibilityManager", "annotationCanvasMap", "annotationEditorUIManager", "_annotationEditorUIManager", "hasEditableAnnotations", "size", "annotations", "popupToElements", "elementParams", "noHTML", "isPopupAnnotation", "POPUP", "rendered", "renderAnnotationElement", "update", "getEditableAnnotations", "from", "getEditableAnnotation", "contentElement", "<PERSON><PERSON><PERSON><PERSON>", "moveElementInDOM", "canvas", "replaceWith", "contains", "before", "after", "clear"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-pdfviewer-common/dist/es/annotations/annotation-layer.js"], "sourcesContent": ["/* Copyright 2014 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar _AnnotationElement_instances, _AnnotationElement_updates, _AnnotationElement_hasBorder, _AnnotationElement_popupElement, _AnnotationElement_setRectEdited, _LinkAnnotationElement_instances, _LinkAnnotationElement_setInternalLink, _LinkAnnotationElement_bindAttachment, _LinkAnnotationElement_bindSetOCGState, _PopupElement_instances, _PopupElement_boundKeyDown, _PopupElement_boundHide, _PopupElement_boundShow, _PopupElement_boundToggle, _PopupElement_color, _PopupElement_container, _PopupElement_contentsObj, _PopupElement_dateObj, _PopupElement_elements, _PopupElement_parent, _PopupElement_parentRect, _PopupElement_pinned, _PopupElement_popup, _PopupElement_position, _PopupElement_rect, _PopupElement_richText, _PopupElement_titleObj, _PopupElement_updates, _PopupElement_wasVisible, _PopupElement_html_get, _PopupElement_fontSize_get, _PopupElement_fontColor_get, _PopupElement_makePopupContent, _PopupElement_keyDown, _PopupElement_setPosition, _PopupElement_toggle, _PopupElement_show, _PopupElement_hide, _AnnotationLayer_instances, _AnnotationLayer_accessibilityManager, _AnnotationLayer_annotationCanvasMap, _AnnotationLayer_editableAnnotations, _AnnotationLayer_appendElement, _AnnotationLayer_setAnnotationCanvasMap;\nimport { __awaiter, __classPrivateFieldGet, __classPrivateFieldSet } from \"tslib\";\nimport { AnnotationEditorType, DOMSVGFactory, FeatureTest, PDFDateString, setLayerDimensions, shadow, Util, XfaLayer } from \"pdfjs-dist/legacy/build/pdf.mjs\";\nimport { AnnotationBorderStyleType, AnnotationPrefix, AnnotationType, LINE_FACTOR } from \"./shared/utils\";\nimport { ColorConverters } from \"./shared/scripting_utils\";\n// import { AnnotationStorage } from \"pdfjs-dist/types/src/display/annotation_storage\";\n// import { AnnotationStorage } from \"pdfjs-dist/types/src/display/annotation_storage\";\n// const DEFAULT_TAB_INDEX = 1000;\n// it is unclear why the value 1000 is chosen by pdf.js\n// kendo a11y tests fail if this is not 0\nconst DEFAULT_TAB_INDEX = 0;\nconst DEFAULT_FONT_SIZE = 9;\nconst GetElementsByNameSet = new WeakSet();\nfunction getRectDims(rect) {\n    return {\n        width: rect[2] - rect[0],\n        height: rect[3] - rect[1]\n    };\n}\nclass AnnotationElementFactory {\n    /**\n     * @param {AnnotationElementParameters} parameters\n     * @returns {AnnotationElement}\n     */\n    static create(parameters) {\n        const subtype = parameters.data.annotationType;\n        switch (subtype) {\n            case AnnotationType.LINK:\n                return new LinkAnnotationElement(parameters);\n            // case AnnotationType.TEXT:\n            //     return new TextAnnotationElement(parameters);\n            case AnnotationType.WIDGET:\n                const fieldType = parameters.data.fieldType;\n                switch (fieldType) {\n                    case \"Tx\":\n                        return new TextWidgetAnnotationElement(parameters);\n                    // case \"Btn\":\n                    //     if (parameters.data.radioButton) {\n                    //         return new RadioButtonWidgetAnnotationElement(parameters);\n                    //     } else if (parameters.data.checkBox) {\n                    //         return new CheckboxWidgetAnnotationElement(parameters);\n                    //     }\n                    //     return new PushButtonWidgetAnnotationElement(parameters);\n                    // case \"Ch\":\n                    //     return new ChoiceWidgetAnnotationElement(parameters);\n                    // case \"Sig\":\n                    //     return new SignatureWidgetAnnotationElement(parameters);\n                    default:\n                        break;\n                }\n            // return new WidgetAnnotationElement(parameters);\n            // case AnnotationType.POPUP:\n            //     return new PopupAnnotationElement(parameters);\n            case AnnotationType.FREETEXT:\n                return new FreeTextAnnotationElement(parameters);\n            // case AnnotationType.LINE:\n            //     return new LineAnnotationElement(parameters);\n            // case AnnotationType.SQUARE:\n            //     return new SquareAnnotationElement(parameters);\n            // case AnnotationType.CIRCLE:\n            //     return new CircleAnnotationElement(parameters);\n            // case AnnotationType.POLYLINE:\n            //     return new PolylineAnnotationElement(parameters);\n            // case AnnotationType.CARET:\n            //     return new CaretAnnotationElement(parameters);\n            // case AnnotationType.INK:\n            //     return new InkAnnotationElement(parameters);\n            // case AnnotationType.POLYGON:\n            //     return new PolygonAnnotationElement(parameters);\n            case AnnotationType.HIGHLIGHT:\n                return new HighlightAnnotationElement(parameters);\n            // case AnnotationType.UNDERLINE:\n            //     return new UnderlineAnnotationElement(parameters);\n            // case AnnotationType.SQUIGGLY:\n            //     return new SquigglyAnnotationElement(parameters);\n            // case AnnotationType.STRIKEOUT:\n            //     return new StrikeOutAnnotationElement(parameters);\n            // case AnnotationType.STAMP:\n            //     return new StampAnnotationElement(parameters);\n            // case AnnotationType.FILEATTACHMENT:\n            //     return new FileAttachmentAnnotationElement(parameters);\n            default:\n                return new AnnotationElement(parameters);\n        }\n    }\n}\nclass AnnotationElement {\n    constructor(parameters, { isRenderable = false, ignoreBorder = false, createQuadrilaterals = false } = {}) {\n        _AnnotationElement_instances.add(this);\n        // todo: props\n        this.isRenderable = null;\n        this.data = null;\n        this.layer = null;\n        this.linkService = null;\n        this.downloadManager = null;\n        this.imageResourcesPath = null;\n        this.renderForms = null;\n        this.svgFactory = null;\n        this.annotationStorage = null;\n        this.enableScripting = null;\n        this.hasJSActions = null;\n        this._fieldObjects = null;\n        this.parent = null;\n        this.container = null;\n        this.popup = null;\n        this.annotationEditorType = null;\n        // todo: props\n        _AnnotationElement_updates.set(this, null);\n        _AnnotationElement_hasBorder.set(this, false);\n        _AnnotationElement_popupElement.set(this, null);\n        this.isRenderable = isRenderable;\n        this.data = parameters.data;\n        this.layer = parameters.layer;\n        this.linkService = parameters.linkService;\n        this.downloadManager = parameters.downloadManager;\n        this.imageResourcesPath = parameters.imageResourcesPath;\n        this.renderForms = parameters.renderForms;\n        this.svgFactory = parameters.svgFactory;\n        this.annotationStorage = parameters.annotationStorage;\n        this.enableScripting = parameters.enableScripting;\n        this.hasJSActions = parameters.hasJSActions;\n        this._fieldObjects = parameters.fieldObjects;\n        this.parent = parameters.parent;\n        if (isRenderable) {\n            this.container = this._createContainer(ignoreBorder);\n        }\n        if (createQuadrilaterals) {\n            this._createQuadrilaterals();\n        }\n    }\n    static _hasPopupData({ titleObj, contentsObj, richText }) {\n        return !!((titleObj === null || titleObj === void 0 ? void 0 : titleObj.str) || (contentsObj === null || contentsObj === void 0 ? void 0 : contentsObj.str) || (richText === null || richText === void 0 ? void 0 : richText.str));\n    }\n    get _isEditable() {\n        return this.data.isEditable;\n    }\n    get hasPopupData() {\n        return AnnotationElement._hasPopupData(this.data);\n    }\n    updateEdited(params) {\n        var _a;\n        if (!this.container) {\n            return;\n        }\n        __classPrivateFieldSet(this, _AnnotationElement_updates, __classPrivateFieldGet(this, _AnnotationElement_updates, \"f\") || {\n            rect: this.data.rect.slice(0)\n        }, \"f\");\n        const { rect } = params;\n        if (rect) {\n            __classPrivateFieldGet(this, _AnnotationElement_instances, \"m\", _AnnotationElement_setRectEdited).call(this, rect);\n        }\n        (_a = __classPrivateFieldGet(this, _AnnotationElement_popupElement, \"f\")) === null || _a === void 0 ? void 0 : _a.popup.updateEdited(params);\n    }\n    resetEdited() {\n        var _a;\n        if (!__classPrivateFieldGet(this, _AnnotationElement_updates, \"f\")) {\n            return;\n        }\n        __classPrivateFieldGet(this, _AnnotationElement_instances, \"m\", _AnnotationElement_setRectEdited).call(this, __classPrivateFieldGet(this, _AnnotationElement_updates, \"f\").rect);\n        (_a = __classPrivateFieldGet(this, _AnnotationElement_popupElement, \"f\")) === null || _a === void 0 ? void 0 : _a.popup.resetEdited();\n        __classPrivateFieldSet(this, _AnnotationElement_updates, null, \"f\");\n    }\n    /**\n     * Create an empty container for the annotation's HTML element.\n     *\n     * @private\n     * @param {boolean} ignoreBorder\n     * @memberof AnnotationElement\n     * @returns {HTMLElement} A section element.\n     */\n    _createContainer(ignoreBorder) {\n        const { data, parent: { page, viewport } } = this;\n        const container = document.createElement(\"section\");\n        container.setAttribute(\"data-annotation-id\", data.id);\n        if (!(this instanceof WidgetAnnotationElement)) {\n            container.tabIndex = DEFAULT_TAB_INDEX;\n        }\n        const { style } = container;\n        // The accessibility manager will move the annotation in the DOM in\n        // order to match the visual ordering.\n        // But if an annotation is above an other one, then we must draw it\n        // after the other one whatever the order is in the DOM, hence the\n        // use of the z-index.\n        style.zIndex = (this.parent.zIndex++).toString();\n        if (data.popupRef) {\n            container.setAttribute(\"aria-haspopup\", \"dialog\");\n        }\n        if (data.alternativeText) {\n            container.title = data.alternativeText;\n        }\n        if (data.noRotate) {\n            // container.classList.add(\"norotate\");\n        }\n        if (!data.rect || this instanceof PopupAnnotationElement) {\n            const { rotation: rotationValue } = data;\n            if (!data.hasOwnCanvas && rotationValue !== 0) {\n                this.setRotation(rotationValue, container);\n            }\n            return container;\n        }\n        const { width, height } = getRectDims(data.rect);\n        if (!ignoreBorder && data.borderStyle.width > 0) {\n            style.borderWidth = `${data.borderStyle.width}px`;\n            const horizontalRadius = data.borderStyle.horizontalCornerRadius;\n            const verticalRadius = data.borderStyle.verticalCornerRadius;\n            if (horizontalRadius > 0 || verticalRadius > 0) {\n                const radius = `calc(${horizontalRadius}px * var(--scale-factor)) / calc(${verticalRadius}px * var(--scale-factor))`;\n                style.borderRadius = radius;\n            }\n            else if (this instanceof RadioButtonWidgetAnnotationElement) {\n                const radius = `calc(${width}px * var(--scale-factor)) / calc(${height}px * var(--scale-factor))`;\n                style.borderRadius = radius;\n            }\n            switch (data.borderStyle.style) {\n                case AnnotationBorderStyleType.SOLID:\n                    style.borderStyle = \"solid\";\n                    break;\n                case AnnotationBorderStyleType.DASHED:\n                    style.borderStyle = \"dashed\";\n                    break;\n                case AnnotationBorderStyleType.BEVELED:\n                    // warn(\"Unimplemented border style: beveled\");\n                    break;\n                case AnnotationBorderStyleType.INSET:\n                    // warn(\"Unimplemented border style: inset\");\n                    break;\n                case AnnotationBorderStyleType.UNDERLINE:\n                    style.borderBottomStyle = \"solid\";\n                    break;\n                default:\n                    break;\n            }\n            const borderColor = data.borderColor || null;\n            if (borderColor) {\n                __classPrivateFieldSet(this, _AnnotationElement_hasBorder, true, \"f\");\n                style.borderColor = Util.makeHexColor(borderColor[0] | 0, borderColor[1] | 0, borderColor[2] | 0);\n            }\n            else {\n                // Transparent (invisible) border, so do not draw it at all.\n                style.borderWidth = (0).toString();\n            }\n        }\n        // Do *not* modify `data.rect`, since that will corrupt the annotation\n        // position on subsequent calls to `_createContainer` (see issue 6804).\n        const rect = Util.normalizeRect([\n            data.rect[0],\n            page.view[3] - data.rect[1] + page.view[1],\n            data.rect[2],\n            page.view[3] - data.rect[3] + page.view[1]\n        ]);\n        const { pageWidth, pageHeight, pageX, pageY } = viewport.rawDims;\n        style.left = `${(100 * (rect[0] - pageX)) / pageWidth}%`;\n        style.top = `${(100 * (rect[1] - pageY)) / pageHeight}%`;\n        const { rotation } = data;\n        if (data.hasOwnCanvas || rotation === 0) {\n            style.width = `${(100 * width) / pageWidth}%`;\n            style.height = `${(100 * height) / pageHeight}%`;\n        }\n        else {\n            this.setRotation(rotation, container);\n        }\n        return container;\n    }\n    setRotation(angle, container = this.container) {\n        if (!this.data.rect) {\n            return;\n        }\n        const { pageWidth, pageHeight } = this.parent.viewport.rawDims;\n        const { width, height } = getRectDims(this.data.rect);\n        let elementWidth, elementHeight;\n        if (angle % 180 === 0) {\n            elementWidth = (100 * width) / pageWidth;\n            elementHeight = (100 * height) / pageHeight;\n        }\n        else {\n            elementWidth = (100 * height) / pageWidth;\n            elementHeight = (100 * width) / pageHeight;\n        }\n        container.style.width = `${elementWidth}%`;\n        container.style.height = `${elementHeight}%`;\n        container.setAttribute(\"data-main-rotation\", (360 - angle) % 360);\n    }\n    get _commonActions() {\n        const setColor = (jsName, styleName, event) => {\n            const color = event.detail[jsName];\n            const colorType = color[0];\n            const colorArray = color.slice(1);\n            event.target.style[styleName] =\n                ColorConverters[`${colorType}_HTML`](colorArray);\n            this.annotationStorage.setValue(this.data.id, {\n                [styleName]: ColorConverters[`${colorType}_rgb`](colorArray)\n            });\n        };\n        return shadow(this, \"_commonActions\", {\n            display: event => {\n                const { display } = event.detail;\n                // See scripting/constants.js for the values of `Display`.\n                // 0 = visible, 1 = hidden, 2 = noPrint and 3 = noView.\n                const hidden = display % 2 === 1;\n                this.container.style.visibility = hidden ? \"hidden\" : \"visible\";\n                this.annotationStorage.setValue(this.data.id, {\n                    noView: hidden,\n                    noPrint: display === 1 || display === 2\n                });\n            },\n            print: event => {\n                this.annotationStorage.setValue(this.data.id, {\n                    noPrint: !event.detail.print\n                });\n            },\n            hidden: event => {\n                const { hidden } = event.detail;\n                this.container.style.visibility = hidden ? \"hidden\" : \"visible\";\n                this.annotationStorage.setValue(this.data.id, {\n                    noPrint: hidden,\n                    noView: hidden\n                });\n            },\n            focus: event => {\n                setTimeout(() => event.target.focus({ preventScroll: false }), 0);\n            },\n            userName: event => {\n                // tooltip\n                event.target.title = event.detail.userName;\n            },\n            readonly: event => {\n                event.target.disabled = event.detail.readonly;\n            },\n            required: event => {\n                this._setRequired(event.target, event.detail.required);\n            },\n            bgColor: event => {\n                setColor(\"bgColor\", \"backgroundColor\", event);\n            },\n            fillColor: event => {\n                setColor(\"fillColor\", \"backgroundColor\", event);\n            },\n            fgColor: event => {\n                setColor(\"fgColor\", \"color\", event);\n            },\n            textColor: event => {\n                setColor(\"textColor\", \"color\", event);\n            },\n            borderColor: event => {\n                setColor(\"borderColor\", \"borderColor\", event);\n            },\n            strokeColor: event => {\n                setColor(\"strokeColor\", \"borderColor\", event);\n            },\n            rotation: event => {\n                const angle = event.detail.rotation;\n                this.setRotation(angle);\n                this.annotationStorage.setValue(this.data.id, {\n                    rotation: angle\n                });\n            }\n        });\n    }\n    _dispatchEventFromSandbox(actions, jsEvent) {\n        const commonActions = this._commonActions;\n        for (const name of Object.keys(jsEvent.detail)) {\n            const action = actions[name] || commonActions[name];\n            action === null || action === void 0 ? void 0 : action(jsEvent);\n        }\n    }\n    _setDefaultPropertiesFromJS(element) {\n        if (!this.enableScripting) {\n            return;\n        }\n        // Some properties may have been updated thanks to JS.\n        const storedData = this.annotationStorage.getRawValue(this.data.id);\n        if (!storedData) {\n            return;\n        }\n        const commonActions = this._commonActions;\n        for (const [actionName, detail] of Object.entries(storedData)) {\n            const action = commonActions[actionName];\n            if (action) {\n                const eventProxy = {\n                    detail: {\n                        [actionName]: detail\n                    },\n                    target: element\n                };\n                action(eventProxy);\n                // The action has been consumed: no need to keep it.\n                delete storedData[actionName];\n            }\n        }\n    }\n    /**\n     * Create quadrilaterals from the annotation's quadpoints.\n     *\n     * @private\n     * @memberof AnnotationElement\n     */\n    _createQuadrilaterals() {\n        if (!this.container) {\n            return;\n        }\n        const { quadPoints } = this.data;\n        if (!quadPoints) {\n            return;\n        }\n        const [rectBlX, rectBlY, rectTrX, rectTrY] = this.data.rect.map(x => Math.fround(x));\n        if (quadPoints.length === 8) {\n            const [trX, trY, blX, blY] = quadPoints.subarray(2, 6);\n            if (rectTrX === trX &&\n                rectTrY === trY &&\n                rectBlX === blX &&\n                rectBlY === blY) {\n                // The quadpoints cover the whole annotation rectangle, so no need to\n                // create a quadrilateral.\n                return;\n            }\n        }\n        const { style } = this.container;\n        let svgBuffer;\n        if (__classPrivateFieldGet(this, _AnnotationElement_hasBorder, \"f\")) {\n            const { borderColor, borderWidth } = style;\n            style.borderWidth = 0;\n            svgBuffer = [\n                \"url('data:image/svg+xml;utf8,\",\n                `<svg xmlns=\"http://www.w3.org/2000/svg\"`,\n                ` preserveAspectRatio=\"none\" viewBox=\"0 0 1 1\">`,\n                `<g fill=\"transparent\" stroke=\"${borderColor}\" stroke-width=\"${borderWidth}\">`\n            ];\n            // this.container.classList.add(\"hasBorder\");\n        }\n        // todo: debug\n        // if (typeof PDFJSDev !== \"undefined\" && PDFJSDev.test(\"TESTING\")) {\n        // this.container.classList.add(\"hasClipPath\");\n        // }\n        const width = rectTrX - rectBlX;\n        const height = rectTrY - rectBlY;\n        const { svgFactory } = this;\n        const svg = svgFactory.createElement(\"svg\");\n        // svg.classList.add(\"quadrilateralsContainer\");\n        svg.setAttribute(\"width\", 0);\n        svg.setAttribute(\"height\", 0);\n        const defs = svgFactory.createElement(\"defs\");\n        svg.append(defs);\n        const clipPath = svgFactory.createElement(\"clipPath\");\n        const id = `clippath_${this.data.id}`;\n        clipPath.setAttribute(\"id\", id);\n        clipPath.setAttribute(\"clipPathUnits\", \"objectBoundingBox\");\n        defs.append(clipPath);\n        for (let i = 2, ii = quadPoints.length; i < ii; i += 8) {\n            const trX = quadPoints[i];\n            const trY = quadPoints[i + 1];\n            const blX = quadPoints[i + 2];\n            const blY = quadPoints[i + 3];\n            const rect = svgFactory.createElement(\"rect\");\n            const x = (blX - rectBlX) / width;\n            const y = (rectTrY - trY) / height;\n            const rectWidth = (trX - blX) / width;\n            const rectHeight = (trY - blY) / height;\n            rect.setAttribute(\"x\", x);\n            rect.setAttribute(\"y\", y);\n            rect.setAttribute(\"width\", rectWidth);\n            rect.setAttribute(\"height\", rectHeight);\n            clipPath.append(rect);\n            svgBuffer === null || svgBuffer === void 0 ? void 0 : svgBuffer.push(`<rect vector-effect=\"non-scaling-stroke\" x=\"${x}\" y=\"${y}\" width=\"${rectWidth}\" height=\"${rectHeight}\"/>`);\n        }\n        if (__classPrivateFieldGet(this, _AnnotationElement_hasBorder, \"f\")) {\n            svgBuffer.push(`</g></svg>')`);\n            style.backgroundImage = svgBuffer.join(\"\");\n        }\n        this.container.append(svg);\n        this.container.style.clipPath = `url(#${id})`;\n    }\n    /**\n     * Create a popup for the annotation's HTML element. This is used for\n     * annotations that do not have a Popup entry in the dictionary, but\n     * are of a type that works with popups (such as Highlight annotations).\n     *\n     * @private\n     * @memberof AnnotationElement\n     */\n    _createPopup() {\n        // const { container, data } = this;\n        // container.setAttribute(\"aria-haspopup\", \"dialog\");\n        // const popup = (this.#popupElement = new PopupAnnotationElement({\n        //     data: {\n        //         color: data.color,\n        //         titleObj: data.titleObj,\n        //         modificationDate: data.modificationDate,\n        //         contentsObj: data.contentsObj,\n        //         richText: data.richText,\n        //         parentRect: data.rect,\n        //         borderStyle: 0,\n        //         id: `popup_${data.id}`,\n        //         rotation: data.rotation,\n        //     },\n        //     parent: this.parent,\n        //     elements: [this],\n        // }));\n        // this.parent.div.append(popup.render());\n    }\n    /**\n     * Render the annotation's HTML element(s).\n     *\n     * @public\n     * @memberof AnnotationElement\n     */\n    render() {\n        // unreachable(\"Abstract method `AnnotationElement.render` called\");\n    }\n    /**\n     * @private\n     * @returns {Array}\n     */\n    _getElementsByName(name, skipId = null) {\n        const fields = [];\n        if (this._fieldObjects) {\n            const fieldObj = this._fieldObjects[name];\n            if (fieldObj) {\n                for (const { page, id, exportValues } of fieldObj) {\n                    if (page === -1) {\n                        continue;\n                    }\n                    if (id === skipId) {\n                        continue;\n                    }\n                    const exportValue = typeof exportValues === \"string\" ? exportValues : null;\n                    const domElement = document.querySelector(`[data-element-id=\"${id}\"]`);\n                    if (domElement && !GetElementsByNameSet.has(domElement)) {\n                        // warn(`_getElementsByName - element not allowed: ${id}`);\n                        continue;\n                    }\n                    fields.push({ id, exportValue, domElement });\n                }\n            }\n            return fields;\n        }\n        // Fallback to a regular DOM lookup, to ensure that the standalone\n        // viewer components won't break.\n        for (const domElement of document.getElementsByName(name)) {\n            const { exportValue } = domElement;\n            const id = domElement.getAttribute(\"data-element-id\");\n            if (id === skipId) {\n                continue;\n            }\n            if (!GetElementsByNameSet.has(domElement)) {\n                continue;\n            }\n            fields.push({ id, exportValue, domElement });\n        }\n        return fields;\n    }\n    show() {\n        var _a;\n        if (this.container) {\n            this.container.hidden = false;\n        }\n        (_a = this.popup) === null || _a === void 0 ? void 0 : _a.maybeShow();\n    }\n    hide() {\n        var _a;\n        if (this.container) {\n            this.container.hidden = true;\n        }\n        (_a = this.popup) === null || _a === void 0 ? void 0 : _a.forceHide();\n    }\n    /**\n     * Get the HTML element(s) which can trigger a popup when clicked or hovered.\n     *\n     * @public\n     * @memberof AnnotationElement\n     * @returns {Array<HTMLElement>|HTMLElement} An array of elements or an\n     *          element.\n     */\n    getElementsToTriggerPopup() {\n        return this.container;\n    }\n    addHighlightArea() {\n        const triggers = this.getElementsToTriggerPopup();\n        if (Array.isArray(triggers)) {\n            // for (const element of triggers) {\n            // element.classList.add(\"highlightArea\");\n            // }\n        }\n        else {\n            // triggers.classList.add(\"highlightArea\");\n        }\n    }\n    _editOnDoubleClick() {\n        if (!this._isEditable) {\n            return;\n        }\n        const { annotationEditorType: mode, data: { id: editId } } = this;\n        this.container.addEventListener(\"dblclick\", () => {\n            var _a, _b;\n            (_b = (_a = this.linkService) === null || _a === void 0 ? void 0 : _a.eventBus) === null || _b === void 0 ? void 0 : _b.dispatch(\"switchannotationeditormode\", {\n                source: this,\n                mode,\n                editId\n            });\n        });\n    }\n    _setRequired(element, isRequired) {\n        if (isRequired) {\n            element.setAttribute(\"required\", true);\n        }\n        else {\n            element.removeAttribute(\"required\");\n        }\n        element.setAttribute(\"aria-required\", isRequired);\n    }\n}\n_AnnotationElement_updates = new WeakMap(), _AnnotationElement_hasBorder = new WeakMap(), _AnnotationElement_popupElement = new WeakMap(), _AnnotationElement_instances = new WeakSet(), _AnnotationElement_setRectEdited = function _AnnotationElement_setRectEdited(rect) {\n    const { container: { style }, data: { rect: currentRect, rotation }, parent: { viewport: { rawDims: { pageWidth, pageHeight, pageX, pageY } } } } = this;\n    currentRect === null || currentRect === void 0 ? void 0 : currentRect.splice(0, 4, ...rect);\n    const { width, height } = getRectDims(rect);\n    style.left = `${(100 * (rect[0] - pageX)) / pageWidth}%`;\n    style.top = `${(100 * (pageHeight - rect[3] + pageY)) / pageHeight}%`;\n    if (rotation === 0) {\n        style.width = `${(100 * width) / pageWidth}%`;\n        style.height = `${(100 * height) / pageHeight}%`;\n    }\n    else {\n        this.setRotation(rotation);\n    }\n};\nclass LinkAnnotationElement extends AnnotationElement {\n    constructor(parameters, options = null) {\n        super(parameters, {\n            isRenderable: true,\n            ignoreBorder: !!(options === null || options === void 0 ? void 0 : options.ignoreBorder),\n            createQuadrilaterals: true\n        });\n        _LinkAnnotationElement_instances.add(this);\n        this.isTooltipOnly = false;\n        this.isTooltipOnly = parameters.data.isTooltipOnly;\n    }\n    render() {\n        const { data, linkService } = this;\n        const link = document.createElement(\"a\");\n        link.setAttribute(\"data-element-id\", data.id);\n        let isBound = false;\n        if (data.url) {\n            linkService.addLinkAttributes(link, data.url, data.newWindow);\n            isBound = true;\n        }\n        else if (data.action) {\n            this._bindNamedAction(link, data.action);\n            isBound = true;\n        }\n        else if (data.attachment) {\n            __classPrivateFieldGet(this, _LinkAnnotationElement_instances, \"m\", _LinkAnnotationElement_bindAttachment).call(this, link, data.attachment, data.attachmentDest);\n            isBound = true;\n        }\n        else if (data.setOCGState) {\n            __classPrivateFieldGet(this, _LinkAnnotationElement_instances, \"m\", _LinkAnnotationElement_bindSetOCGState).call(this, link, data.setOCGState);\n            isBound = true;\n        }\n        else if (data.dest) {\n            this._bindLink(link, data.dest);\n            isBound = true;\n        }\n        else {\n            if (data.actions &&\n                (data.actions.Action ||\n                    data.actions[\"Mouse Up\"] ||\n                    data.actions[\"Mouse Down\"]) &&\n                this.enableScripting &&\n                this.hasJSActions) {\n                this._bindJSAction(link, data);\n                isBound = true;\n            }\n            if (data.resetForm) {\n                this._bindResetFormAction(link, data.resetForm);\n                isBound = true;\n            }\n            else if (this.isTooltipOnly && !isBound) {\n                this._bindLink(link, \"\");\n                isBound = true;\n            }\n        }\n        // todo: do not render the class as the rendering is yet to be determined\n        // this.container.classList.add(\"linkAnnotation\");\n        this.container.classList.add(\"k-link-annotation\");\n        if (isBound) {\n            this.container.append(link);\n        }\n        return this.container;\n    }\n    /**\n     * Bind internal links to the link element.\n     *\n     * @private\n     * @param {Object} link\n     * @param {Object} destination\n     * @memberof LinkAnnotationElement\n     */\n    _bindLink(link, destination) {\n        link.href = this.linkService.getDestinationHash(destination);\n        link.onclick = () => {\n            if (destination) {\n                this.linkService.goToDestination(destination);\n            }\n            return false;\n        };\n        if (destination || destination === /* isTooltipOnly = */ \"\") {\n            __classPrivateFieldGet(this, _LinkAnnotationElement_instances, \"m\", _LinkAnnotationElement_setInternalLink).call(this);\n        }\n    }\n    /**\n     * Bind named actions to the link element.\n     *\n     * @private\n     * @param {Object} link\n     * @param {Object} action\n     * @memberof LinkAnnotationElement\n     */\n    _bindNamedAction(link, action) {\n        link.href = this.linkService.getAnchorUrl(\"\");\n        link.onclick = () => {\n            this.linkService.executeNamedAction(action);\n            return false;\n        };\n        __classPrivateFieldGet(this, _LinkAnnotationElement_instances, \"m\", _LinkAnnotationElement_setInternalLink).call(this);\n    }\n    /**\n     * Bind JS actions to the link element.\n     *\n     * @private\n     * @param {Object} link\n     * @param {Object} data\n     * @memberof LinkAnnotationElement\n     */\n    _bindJSAction(link, data) {\n        link.href = this.linkService.getAnchorUrl(\"\");\n        const map = new Map([\n            [\"Action\", \"onclick\"],\n            [\"Mouse Up\", \"onmouseup\"],\n            [\"Mouse Down\", \"onmousedown\"]\n        ]);\n        for (const name of Object.keys(data.actions)) {\n            const jsName = map.get(name);\n            if (!jsName) {\n                continue;\n            }\n            link[jsName] = () => {\n                var _a;\n                (_a = this.linkService.eventBus) === null || _a === void 0 ? void 0 : _a.dispatch(\"dispatcheventinsandbox\", {\n                    source: this,\n                    detail: {\n                        id: data.id,\n                        name\n                    }\n                });\n                return false;\n            };\n        }\n        if (!link.onclick) {\n            link.onclick = () => false;\n        }\n        __classPrivateFieldGet(this, _LinkAnnotationElement_instances, \"m\", _LinkAnnotationElement_setInternalLink).call(this);\n    }\n    _bindResetFormAction(link, resetForm) {\n        const otherClickAction = link.onclick;\n        if (!otherClickAction) {\n            link.href = this.linkService.getAnchorUrl(\"\");\n        }\n        __classPrivateFieldGet(this, _LinkAnnotationElement_instances, \"m\", _LinkAnnotationElement_setInternalLink).call(this);\n        if (!this._fieldObjects) {\n            // warn(\n            //     `_bindResetFormAction - \"resetForm\" action not supported, ` +\n            //     \"ensure that the `fieldObjects` parameter is provided.\"\n            // );\n            if (!otherClickAction) {\n                link.onclick = () => false;\n            }\n            return;\n        }\n        link.onclick = () => {\n            var _a;\n            otherClickAction === null || otherClickAction === void 0 ? void 0 : otherClickAction();\n            const { fields: resetFormFields, refs: resetFormRefs, include } = resetForm;\n            const allFields = [];\n            if (resetFormFields.length !== 0 || resetFormRefs.length !== 0) {\n                const fieldIds = new Set(resetFormRefs);\n                for (const fieldName of resetFormFields) {\n                    const fields = this._fieldObjects[fieldName] || [];\n                    for (const { id } of fields) {\n                        fieldIds.add(id);\n                    }\n                }\n                for (const fields of Object.values(this._fieldObjects)) {\n                    for (const field of fields) {\n                        if (fieldIds.has(field.id) === include) {\n                            allFields.push(field);\n                        }\n                    }\n                }\n            }\n            else {\n                for (const fields of Object.values(this._fieldObjects)) {\n                    allFields.push(...fields);\n                }\n            }\n            const storage = this.annotationStorage;\n            const allIds = [];\n            for (const field of allFields) {\n                const { id } = field;\n                allIds.push(id);\n                switch (field.type) {\n                    case \"text\": {\n                        const value = field.defaultValue || \"\";\n                        storage.setValue(id, { value });\n                        break;\n                    }\n                    case \"checkbox\":\n                    case \"radiobutton\": {\n                        const value = field.defaultValue === field.exportValues;\n                        storage.setValue(id, { value });\n                        break;\n                    }\n                    case \"combobox\":\n                    case \"listbox\": {\n                        const value = field.defaultValue || \"\";\n                        storage.setValue(id, { value });\n                        break;\n                    }\n                    default:\n                        continue;\n                }\n                const domElement = document.querySelector(`[data-element-id=\"${id}\"]`);\n                if (!domElement) {\n                    continue;\n                }\n                else if (!GetElementsByNameSet.has(domElement)) {\n                    // warn(`_bindResetFormAction - element not allowed: ${id}`);\n                    continue;\n                }\n                domElement.dispatchEvent(new Event(\"resetform\"));\n            }\n            if (this.enableScripting) {\n                // Update the values in the sandbox.\n                (_a = this.linkService.eventBus) === null || _a === void 0 ? void 0 : _a.dispatch(\"dispatcheventinsandbox\", {\n                    source: this,\n                    detail: {\n                        id: \"app\",\n                        ids: allIds,\n                        name: \"ResetForm\"\n                    }\n                });\n            }\n            return false;\n        };\n    }\n}\n_LinkAnnotationElement_instances = new WeakSet(), _LinkAnnotationElement_setInternalLink = function _LinkAnnotationElement_setInternalLink() {\n    this.container.setAttribute(\"data-internal-link\", \"\");\n}, _LinkAnnotationElement_bindAttachment = function _LinkAnnotationElement_bindAttachment(link, attachment, dest = null) {\n    link.href = this.linkService.getAnchorUrl(\"\");\n    if (attachment.description) {\n        link.title = attachment.description;\n    }\n    link.onclick = () => {\n        var _a;\n        (_a = this.downloadManager) === null || _a === void 0 ? void 0 : _a.openOrDownloadData(attachment.content, attachment.filename, dest);\n        return false;\n    };\n    __classPrivateFieldGet(this, _LinkAnnotationElement_instances, \"m\", _LinkAnnotationElement_setInternalLink).call(this);\n}, _LinkAnnotationElement_bindSetOCGState = function _LinkAnnotationElement_bindSetOCGState(link, action) {\n    link.href = this.linkService.getAnchorUrl(\"\");\n    link.onclick = () => {\n        this.linkService.executeSetOCGState(action);\n        return false;\n    };\n    __classPrivateFieldGet(this, _LinkAnnotationElement_instances, \"m\", _LinkAnnotationElement_setInternalLink).call(this);\n};\n// class TextAnnotationElement extends AnnotationElement {\n//     constructor(parameters) {\n//         super(parameters, { isRenderable: true });\n//     }\n//     render() {\n//         this.container.classList.add(\"textAnnotation\");\n//         const image = document.createElement(\"img\");\n//         image.src =\n//             this.imageResourcesPath +\n//             \"annotation-\" +\n//             this.data.name.toLowerCase() +\n//             \".svg\";\n//         image.setAttribute(\"data-l10n-id\", \"pdfjs-text-annotation-type\");\n//         image.setAttribute(\n//             \"data-l10n-args\",\n//             JSON.stringify({ type: this.data.name })\n//         );\n//         if (!this.data.popupRef && this.hasPopupData) {\n//             this._createPopup();\n//         }\n//         this.container.append(image);\n//         return this.container;\n//     }\n// }\nclass WidgetAnnotationElement extends AnnotationElement {\n    render() {\n        // Show only the container for unsupported field types.\n        return this.container;\n    }\n    showElementAndHideCanvas(element) {\n        var _a;\n        if (this.data.hasOwnCanvas) {\n            if (((_a = element.previousSibling) === null || _a === void 0 ? void 0 : _a.nodeName) === \"CANVAS\") {\n                element.previousSibling.hidden = true;\n            }\n            element.hidden = false;\n        }\n    }\n    _getKeyModifier(event) {\n        return FeatureTest.platform.isMac ? event.metaKey : event.ctrlKey;\n    }\n    _setEventListener(element, elementData, baseName, eventName, valueGetter) {\n        if (baseName.includes(\"mouse\")) {\n            // Mouse events\n            element.addEventListener(baseName, event => {\n                var _a;\n                (_a = this.linkService.eventBus) === null || _a === void 0 ? void 0 : _a.dispatch(\"dispatcheventinsandbox\", {\n                    source: this,\n                    detail: {\n                        id: this.data.id,\n                        name: eventName,\n                        value: valueGetter(event),\n                        shift: event.shiftKey,\n                        modifier: this._getKeyModifier(event)\n                    }\n                });\n            });\n        }\n        else {\n            // Non-mouse events\n            element.addEventListener(baseName, event => {\n                var _a;\n                if (baseName === \"blur\") {\n                    if (!elementData.focused || !event.relatedTarget) {\n                        return;\n                    }\n                    elementData.focused = false;\n                }\n                else if (baseName === \"focus\") {\n                    if (elementData.focused) {\n                        return;\n                    }\n                    elementData.focused = true;\n                }\n                if (!valueGetter) {\n                    return;\n                }\n                (_a = this.linkService.eventBus) === null || _a === void 0 ? void 0 : _a.dispatch(\"dispatcheventinsandbox\", {\n                    source: this,\n                    detail: {\n                        id: this.data.id,\n                        name: eventName,\n                        value: valueGetter(event)\n                    }\n                });\n            });\n        }\n    }\n    _setEventListeners(element, elementData, names, getter) {\n        var _a, _b, _c;\n        for (const [baseName, eventName] of names) {\n            if (eventName === \"Action\" || ((_a = this.data.actions) === null || _a === void 0 ? void 0 : _a[eventName])) {\n                if (eventName === \"Focus\" || eventName === \"Blur\") {\n                    elementData || (elementData = { focused: false });\n                }\n                this._setEventListener(element, elementData, baseName, eventName, getter);\n                if (eventName === \"Focus\" && !((_b = this.data.actions) === null || _b === void 0 ? void 0 : _b.Blur)) {\n                    // Ensure that elementData will have the correct value.\n                    this._setEventListener(element, elementData, \"blur\", \"Blur\", null);\n                }\n                else if (eventName === \"Blur\" && !((_c = this.data.actions) === null || _c === void 0 ? void 0 : _c.Focus)) {\n                    this._setEventListener(element, elementData, \"focus\", \"Focus\", null);\n                }\n            }\n        }\n    }\n    _setBackgroundColor(element) {\n        const color = this.data.backgroundColor || null;\n        element.style.backgroundColor =\n            color === null\n                ? \"transparent\"\n                : Util.makeHexColor(color[0], color[1], color[2]);\n    }\n    /**\n     * Apply text styles to the text in the element.\n     *\n     * @private\n     * @param {HTMLDivElement} element\n     * @memberof TextWidgetAnnotationElement\n     */\n    _setTextStyle(element) {\n        const TEXT_ALIGNMENT = [\"left\", \"center\", \"right\"];\n        const { fontColor } = this.data.defaultAppearanceData;\n        const fontSize = this.data.defaultAppearanceData.fontSize || DEFAULT_FONT_SIZE;\n        const style = element.style;\n        // TODO: If the font-size is zero, calculate it based on the height and\n        //       width of the element.\n        // Not setting `style.fontSize` will use the default font-size for now.\n        // We don't use the font, as specified in the PDF document, for the <input>\n        // element. Hence using the original `fontSize` could look bad, which is why\n        // it's instead based on the field height.\n        // If the height is \"big\" then it could lead to a too big font size\n        // so in this case use the one we've in the pdf (hence the min).\n        let computedFontSize;\n        const BORDER_SIZE = 2;\n        const roundToOneDecimal = x => Math.round(10 * x) / 10;\n        if (this.data.multiLine) {\n            const height = Math.abs(this.data.rect[3] - this.data.rect[1] - BORDER_SIZE);\n            const numberOfLines = Math.round(height / (LINE_FACTOR * fontSize)) || 1;\n            const lineHeight = height / numberOfLines;\n            computedFontSize = Math.min(fontSize, roundToOneDecimal(lineHeight / LINE_FACTOR));\n        }\n        else {\n            const height = Math.abs(this.data.rect[3] - this.data.rect[1] - BORDER_SIZE);\n            computedFontSize = Math.min(fontSize, roundToOneDecimal(height / LINE_FACTOR));\n        }\n        style.fontSize = `calc(${computedFontSize}px * var(--scale-factor))`;\n        style.color = Util.makeHexColor(fontColor[0], fontColor[1], fontColor[2]);\n        if (this.data.textAlignment !== null) {\n            style.textAlign = TEXT_ALIGNMENT[this.data.textAlignment];\n        }\n    }\n    _setRequired(element, isRequired) {\n        if (isRequired) {\n            element.setAttribute(\"required\", true);\n        }\n        else {\n            element.removeAttribute(\"required\");\n        }\n        element.setAttribute(\"aria-required\", isRequired);\n    }\n}\nclass TextWidgetAnnotationElement extends WidgetAnnotationElement {\n    constructor(parameters) {\n        const isRenderable = parameters.renderForms ||\n            parameters.data.hasOwnCanvas ||\n            (!parameters.data.hasAppearance && !!parameters.data.fieldValue);\n        super(parameters, { isRenderable });\n    }\n    setPropertyOnSiblings(base, key, value, keyInStorage) {\n        const storage = this.annotationStorage;\n        for (const element of this._getElementsByName(base.name, \n        /* skipId = */ base.id)) {\n            if (element.domElement) {\n                element.domElement[key] = value;\n            }\n            storage.setValue(element.id, { [keyInStorage]: value });\n        }\n    }\n    render() {\n        var _a, _b;\n        const storage = this.annotationStorage;\n        const id = this.data.id;\n        // this.container.classList.add(\"textWidgetAnnotation\");\n        this.container.classList.add(\"k-text-widget-annotation\");\n        let element = null;\n        if (this.renderForms) {\n            // NOTE: We cannot set the values using `element.value` below, since it\n            //       prevents the AnnotationLayer rasterizer in `test/driver.js`\n            //       from parsing the elements correctly for the reference tests.\n            const storedData = storage.getValue(id, {\n                value: this.data.fieldValue\n            });\n            let textContent = storedData.value || \"\";\n            const maxLen = storage.getValue(id, {\n                charLimit: this.data.maxLen\n            }).charLimit;\n            if (maxLen && textContent.length > maxLen) {\n                textContent = textContent.slice(0, maxLen);\n            }\n            let fieldFormattedValues = storedData.formattedValue || ((_a = this.data.textContent) === null || _a === void 0 ? void 0 : _a.join(\"\\n\")) || null;\n            if (fieldFormattedValues && this.data.comb) {\n                fieldFormattedValues = fieldFormattedValues.replaceAll(/\\s+/g, \"\");\n            }\n            const elementData = {\n                userValue: textContent,\n                formattedValue: fieldFormattedValues,\n                lastCommittedValue: null,\n                commitKey: 1,\n                focused: false\n            };\n            if (this.data.multiLine) {\n                element = document.createElement(\"textarea\");\n                element.textContent = fieldFormattedValues !== null && fieldFormattedValues !== void 0 ? fieldFormattedValues : textContent;\n                if (this.data.doNotScroll) {\n                    element.style.overflowY = \"hidden\";\n                }\n            }\n            else {\n                element = document.createElement(\"input\");\n                element.type = \"text\";\n                element.setAttribute(\"value\", fieldFormattedValues !== null && fieldFormattedValues !== void 0 ? fieldFormattedValues : textContent);\n                if (this.data.doNotScroll) {\n                    element.style.overflowX = \"hidden\";\n                }\n            }\n            if (this.data.hasOwnCanvas) {\n                element.hidden = true;\n            }\n            GetElementsByNameSet.add(element);\n            element.setAttribute(\"data-element-id\", id);\n            element.disabled = this.data.readOnly;\n            element.name = this.data.fieldName;\n            element.tabIndex = DEFAULT_TAB_INDEX;\n            this._setRequired(element, this.data.required);\n            if (maxLen) {\n                element.maxLength = maxLen;\n            }\n            element.addEventListener(\"input\", event => {\n                storage.setValue(id, { value: event.target.value });\n                this.setPropertyOnSiblings(element, \"value\", event.target.value, \"value\");\n                elementData.formattedValue = null;\n            });\n            element.addEventListener(\"resetform\", () => {\n                var _a;\n                const defaultValue = (_a = this.data.defaultFieldValue) !== null && _a !== void 0 ? _a : \"\";\n                element.value = elementData.userValue = defaultValue;\n                elementData.formattedValue = null;\n            });\n            let blurListener = event => {\n                const { formattedValue } = elementData;\n                if (formattedValue !== null && formattedValue !== undefined) {\n                    event.target.value = formattedValue;\n                }\n                // Reset the cursor position to the start of the field (issue 12359).\n                event.target.scrollLeft = 0;\n            };\n            if (this.enableScripting && this.hasJSActions) {\n                element.addEventListener(\"focus\", event => {\n                    var _a;\n                    if (elementData.focused) {\n                        return;\n                    }\n                    const { target } = event;\n                    if (elementData.userValue) {\n                        target.value = elementData.userValue;\n                    }\n                    elementData.lastCommittedValue = target.value;\n                    elementData.commitKey = 1;\n                    if (!((_a = this.data.actions) === null || _a === void 0 ? void 0 : _a.Focus)) {\n                        elementData.focused = true;\n                    }\n                });\n                element.addEventListener(\"updatefromsandbox\", jsEvent => {\n                    this.showElementAndHideCanvas(jsEvent.target);\n                    const actions = {\n                        value(event) {\n                            var _a;\n                            elementData.userValue = (_a = event.detail.value) !== null && _a !== void 0 ? _a : \"\";\n                            storage.setValue(id, { value: elementData.userValue.toString() });\n                            event.target.value = elementData.userValue;\n                        },\n                        formattedValue(event) {\n                            const { formattedValue } = event.detail;\n                            elementData.formattedValue = formattedValue;\n                            if (formattedValue !== null &&\n                                formattedValue !== undefined &&\n                                event.target !== document.activeElement) {\n                                // Input hasn't the focus so display formatted string\n                                event.target.value = formattedValue;\n                            }\n                            storage.setValue(id, {\n                                formattedValue\n                            });\n                        },\n                        selRange(event) {\n                            event.target.setSelectionRange(...event.detail.selRange);\n                        },\n                        charLimit: event => {\n                            var _a;\n                            const { charLimit } = event.detail;\n                            const { target } = event;\n                            if (charLimit === 0) {\n                                target.removeAttribute(\"maxLength\");\n                                return;\n                            }\n                            target.setAttribute(\"maxLength\", charLimit);\n                            let value = elementData.userValue;\n                            if (!value || value.length <= charLimit) {\n                                return;\n                            }\n                            value = value.slice(0, charLimit);\n                            target.value = elementData.userValue = value;\n                            storage.setValue(id, { value });\n                            (_a = this.linkService.eventBus) === null || _a === void 0 ? void 0 : _a.dispatch(\"dispatcheventinsandbox\", {\n                                source: this,\n                                detail: {\n                                    id,\n                                    name: \"Keystroke\",\n                                    value,\n                                    willCommit: true,\n                                    commitKey: 1,\n                                    selStart: target.selectionStart,\n                                    selEnd: target.selectionEnd\n                                }\n                            });\n                        }\n                    };\n                    this._dispatchEventFromSandbox(actions, jsEvent);\n                });\n                // Even if the field hasn't any actions\n                // leaving it can still trigger some actions with Calculate\n                element.addEventListener(\"keydown\", event => {\n                    var _a;\n                    elementData.commitKey = 1;\n                    // If the key is one of Escape, Enter then the data are committed.\n                    // If we've a Tab then data will be committed on blur.\n                    let commitKey = -1;\n                    if (event.key === \"Escape\") {\n                        commitKey = 0;\n                    }\n                    else if (event.key === \"Enter\" && !this.data.multiLine) {\n                        // When we've a multiline field, \"Enter\" key is a key as the other\n                        // hence we don't commit the data (Acrobat behaves the same way)\n                        // (see issue #15627).\n                        commitKey = 2;\n                    }\n                    else if (event.key === \"Tab\") {\n                        elementData.commitKey = 3;\n                    }\n                    if (commitKey === -1) {\n                        return;\n                    }\n                    const { value } = event.target;\n                    if (elementData.lastCommittedValue === value) {\n                        return;\n                    }\n                    elementData.lastCommittedValue = value;\n                    // Save the entered value\n                    elementData.userValue = value;\n                    (_a = this.linkService.eventBus) === null || _a === void 0 ? void 0 : _a.dispatch(\"dispatcheventinsandbox\", {\n                        source: this,\n                        detail: {\n                            id,\n                            name: \"Keystroke\",\n                            value,\n                            willCommit: true,\n                            commitKey,\n                            selStart: event.target.selectionStart,\n                            selEnd: event.target.selectionEnd\n                        }\n                    });\n                });\n                const _blurListener = blurListener;\n                blurListener = null;\n                element.addEventListener(\"blur\", event => {\n                    var _a, _b;\n                    if (!elementData.focused || !event.relatedTarget) {\n                        return;\n                    }\n                    if (!((_a = this.data.actions) === null || _a === void 0 ? void 0 : _a.Blur)) {\n                        elementData.focused = false;\n                    }\n                    const { value } = event.target;\n                    elementData.userValue = value;\n                    if (elementData.lastCommittedValue !== value) {\n                        (_b = this.linkService.eventBus) === null || _b === void 0 ? void 0 : _b.dispatch(\"dispatcheventinsandbox\", {\n                            source: this,\n                            detail: {\n                                id,\n                                name: \"Keystroke\",\n                                value,\n                                willCommit: true,\n                                commitKey: elementData.commitKey,\n                                selStart: event.target.selectionStart,\n                                selEnd: event.target.selectionEnd\n                            }\n                        });\n                    }\n                    _blurListener(event);\n                });\n                if ((_b = this.data.actions) === null || _b === void 0 ? void 0 : _b.Keystroke) {\n                    element.addEventListener(\"beforeinput\", event => {\n                        var _a;\n                        elementData.lastCommittedValue = null;\n                        const { data, target } = event;\n                        const { value, selectionStart, selectionEnd } = target;\n                        let selStart = selectionStart, selEnd = selectionEnd;\n                        switch (event.inputType) {\n                            // https://rawgit.com/w3c/input-events/v1/index.html#interface-InputEvent-Attributes\n                            case \"deleteWordBackward\": {\n                                const match = value\n                                    .substring(0, selectionStart)\n                                    .match(/\\w*[^\\w]*$/);\n                                if (match) {\n                                    selStart -= match[0].length;\n                                }\n                                break;\n                            }\n                            case \"deleteWordForward\": {\n                                const match = value\n                                    .substring(selectionStart)\n                                    .match(/^[^\\w]*\\w*/);\n                                if (match) {\n                                    selEnd += match[0].length;\n                                }\n                                break;\n                            }\n                            case \"deleteContentBackward\":\n                                if (selectionStart === selectionEnd) {\n                                    selStart -= 1;\n                                }\n                                break;\n                            case \"deleteContentForward\":\n                                if (selectionStart === selectionEnd) {\n                                    selEnd += 1;\n                                }\n                                break;\n                            default: break;\n                        }\n                        // We handle the event ourselves.\n                        event.preventDefault();\n                        (_a = this.linkService.eventBus) === null || _a === void 0 ? void 0 : _a.dispatch(\"dispatcheventinsandbox\", {\n                            source: this,\n                            detail: {\n                                id,\n                                name: \"Keystroke\",\n                                value,\n                                change: data || \"\",\n                                willCommit: false,\n                                selStart,\n                                selEnd\n                            }\n                        });\n                    });\n                }\n                this._setEventListeners(element, elementData, [\n                    [\"focus\", \"Focus\"],\n                    [\"blur\", \"Blur\"],\n                    [\"mousedown\", \"Mouse Down\"],\n                    [\"mouseenter\", \"Mouse Enter\"],\n                    [\"mouseleave\", \"Mouse Exit\"],\n                    [\"mouseup\", \"Mouse Up\"]\n                ], event => event.target.value);\n            }\n            if (blurListener) {\n                element.addEventListener(\"blur\", blurListener);\n            }\n            if (this.data.comb) {\n                const fieldWidth = this.data.rect[2] - this.data.rect[0];\n                const combWidth = fieldWidth / maxLen;\n                element.classList.add(\"comb\");\n                element.style.letterSpacing = `calc(${combWidth}px * var(--scale-factor) - 1ch)`;\n            }\n        }\n        else {\n            element = document.createElement(\"div\");\n            element.textContent = this.data.fieldValue;\n            element.style.verticalAlign = \"middle\";\n            element.style.display = \"table-cell\";\n            if (this.data.hasOwnCanvas) {\n                element.hidden = true;\n            }\n        }\n        this._setTextStyle(element);\n        this._setBackgroundColor(element);\n        this._setDefaultPropertiesFromJS(element);\n        this.container.append(element);\n        return this.container;\n    }\n}\n// class SignatureWidgetAnnotationElement extends WidgetAnnotationElement {\n//     constructor(parameters) {\n//         super(parameters, { isRenderable: !!parameters.data.hasOwnCanvas });\n//     }\n// }\n// class CheckboxWidgetAnnotationElement extends WidgetAnnotationElement {\n//     constructor(parameters) {\n//         super(parameters, { isRenderable: parameters.renderForms });\n//     }\n//     render() {\n//         const storage = this.annotationStorage;\n//         const data = this.data;\n//         const id = data.id;\n//         let value = storage.getValue(id, {\n//             value: data.exportValue === data.fieldValue,\n//         }).value;\n//         if (typeof value === \"string\") {\n//             // The value has been changed through js and set in annotationStorage.\n//             value = value !== \"Off\";\n//             storage.setValue(id, { value });\n//         }\n//         this.container.classList.add(\"buttonWidgetAnnotation\", \"checkBox\");\n//         const element = document.createElement(\"input\");\n//         GetElementsByNameSet.add(element);\n//         element.setAttribute(\"data-element-id\", id);\n//         element.disabled = data.readOnly;\n//         this._setRequired(element, this.data.required);\n//         element.type = \"checkbox\";\n//         element.name = data.fieldName;\n//         if (value) {\n//             element.setAttribute(\"checked\", (true).toString());\n//         }\n//         element.setAttribute(\"exportValue\", data.exportValue);\n//         element.tabIndex = DEFAULT_TAB_INDEX;\n//         element.addEventListener(\"change\", event => {\n//             const { name, checked } = event.target as any;\n//             for (const checkbox of this._getElementsByName(name, /* skipId = */ id)) {\n//                 const curChecked = checked && checkbox.exportValue === data.exportValue;\n//                 if (checkbox.domElement) {\n//                     checkbox.domElement.checked = curChecked;\n//                 }\n//                 storage.setValue(checkbox.id, { value: curChecked });\n//             }\n//             storage.setValue(id, { value: checked });\n//         });\n//         element.addEventListener(\"resetform\", event => {\n//             const defaultValue = data.defaultFieldValue || \"Off\";\n//             // @ts-expect-error(TS)\n//             event.target.checked = defaultValue === data.exportValue;\n//         });\n//         if (this.enableScripting && this.hasJSActions) {\n//             element.addEventListener(\"updatefromsandbox\", jsEvent => {\n//                 const actions = {\n//                     value(event) {\n//                         event.target.checked = event.detail.value !== \"Off\";\n//                         storage.setValue(id, { value: event.target.checked });\n//                     },\n//                 };\n//                 this._dispatchEventFromSandbox(actions, jsEvent);\n//             });\n//             this._setEventListeners(\n//                 element,\n//                 null,\n//                 [\n//                     [\"change\", \"Validate\"],\n//                     [\"change\", \"Action\"],\n//                     [\"focus\", \"Focus\"],\n//                     [\"blur\", \"Blur\"],\n//                     [\"mousedown\", \"Mouse Down\"],\n//                     [\"mouseenter\", \"Mouse Enter\"],\n//                     [\"mouseleave\", \"Mouse Exit\"],\n//                     [\"mouseup\", \"Mouse Up\"],\n//                 ],\n//                 event => event.target.checked\n//             );\n//         }\n//         this._setBackgroundColor(element);\n//         this._setDefaultPropertiesFromJS(element);\n//         this.container.append(element);\n//         return this.container;\n//     }\n// }\nclass RadioButtonWidgetAnnotationElement extends WidgetAnnotationElement {\n    constructor(parameters) {\n        super(parameters, { isRenderable: parameters.renderForms });\n    }\n    render() {\n        this.container.classList.add(\"buttonWidgetAnnotation\", \"radioButton\");\n        const storage = this.annotationStorage;\n        const data = this.data;\n        const id = data.id;\n        let value = storage.getValue(id, {\n            value: data.fieldValue === data.buttonValue\n        }).value;\n        if (typeof value === \"string\") {\n            // The value has been changed through js and set in annotationStorage.\n            value = value !== data.buttonValue;\n            storage.setValue(id, { value });\n        }\n        if (value) {\n            // It's possible that multiple radio buttons are checked.\n            // So if this one is checked we just reset the other ones.\n            // (see bug 1864136). Then when the other ones will be rendered they will\n            // unchecked (because of their value in the storage).\n            // Consequently, the first checked radio button will be the only checked\n            // one.\n            for (const radio of this._getElementsByName(data.fieldName, \n            /* skipId = */ id)) {\n                storage.setValue(radio.id, { value: false });\n            }\n        }\n        const element = document.createElement(\"input\");\n        GetElementsByNameSet.add(element);\n        element.setAttribute(\"data-element-id\", id);\n        element.disabled = data.readOnly;\n        this._setRequired(element, this.data.required);\n        element.type = \"radio\";\n        element.name = data.fieldName;\n        if (value) {\n            element.setAttribute(\"checked\", (true).toString());\n        }\n        element.tabIndex = DEFAULT_TAB_INDEX;\n        element.addEventListener(\"change\", event => {\n            const { name, checked } = event.target;\n            for (const radio of this._getElementsByName(name, /* skipId = */ id)) {\n                storage.setValue(radio.id, { value: false });\n            }\n            storage.setValue(id, { value: checked });\n        });\n        element.addEventListener(\"resetform\", event => {\n            const defaultValue = data.defaultFieldValue;\n            // @ts-expect-error(TS)\n            event.target.checked =\n                defaultValue !== null &&\n                    defaultValue !== undefined &&\n                    defaultValue === data.buttonValue;\n        });\n        if (this.enableScripting && this.hasJSActions) {\n            const pdfButtonValue = data.buttonValue;\n            element.addEventListener(\"updatefromsandbox\", jsEvent => {\n                const actions = {\n                    value: event => {\n                        const checked = pdfButtonValue === event.detail.value;\n                        for (const radio of this._getElementsByName(event.target.name)) {\n                            const curChecked = checked && radio.id === id;\n                            if (radio.domElement) {\n                                radio.domElement.checked = curChecked;\n                            }\n                            storage.setValue(radio.id, { value: curChecked });\n                        }\n                    }\n                };\n                this._dispatchEventFromSandbox(actions, jsEvent);\n            });\n            this._setEventListeners(element, null, [\n                [\"change\", \"Validate\"],\n                [\"change\", \"Action\"],\n                [\"focus\", \"Focus\"],\n                [\"blur\", \"Blur\"],\n                [\"mousedown\", \"Mouse Down\"],\n                [\"mouseenter\", \"Mouse Enter\"],\n                [\"mouseleave\", \"Mouse Exit\"],\n                [\"mouseup\", \"Mouse Up\"]\n            ], event => event.target.checked);\n        }\n        this._setBackgroundColor(element);\n        this._setDefaultPropertiesFromJS(element);\n        this.container.append(element);\n        return this.container;\n    }\n}\n// class PushButtonWidgetAnnotationElement extends LinkAnnotationElement {\n//     constructor(parameters) {\n//         super(parameters, { ignoreBorder: parameters.data.hasAppearance });\n//     }\n//     render() {\n//         // The rendering and functionality of a push button widget annotation is\n//         // equal to that of a link annotation, but may have more functionality, such\n//         // as performing actions on form fields (resetting, submitting, et cetera).\n//         const container = super.render();\n//         container.classList.add(\"buttonWidgetAnnotation\", \"pushButton\");\n//         const linkElement = container.lastChild;\n//         if (this.enableScripting && this.hasJSActions && linkElement) {\n//             this._setDefaultPropertiesFromJS(linkElement);\n//             linkElement.addEventListener(\"updatefromsandbox\", jsEvent => {\n//                 this._dispatchEventFromSandbox({}, jsEvent);\n//             });\n//         }\n//         return container;\n//     }\n// }\n// class ChoiceWidgetAnnotationElement extends WidgetAnnotationElement {\n//     constructor(parameters) {\n//         super(parameters, { isRenderable: parameters.renderForms });\n//     }\n//     render() {\n//         this.container.classList.add(\"choiceWidgetAnnotation\");\n//         const storage = this.annotationStorage;\n//         const id = this.data.id;\n//         const storedData = storage.getValue(id, {\n//             value: this.data.fieldValue,\n//         });\n//         const selectElement = document.createElement(\"select\");\n//         GetElementsByNameSet.add(selectElement);\n//         selectElement.setAttribute(\"data-element-id\", id);\n//         selectElement.disabled = this.data.readOnly;\n//         this._setRequired(selectElement, this.data.required);\n//         selectElement.name = this.data.fieldName;\n//         selectElement.tabIndex = DEFAULT_TAB_INDEX;\n//         let addAnEmptyEntry = this.data.combo && this.data.options.length > 0;\n//         if (!this.data.combo) {\n//             // List boxes have a size and (optionally) multiple selection.\n//             selectElement.size = this.data.options.length;\n//             if (this.data.multiSelect) {\n//                 selectElement.multiple = true;\n//             }\n//         }\n//         selectElement.addEventListener(\"resetform\", () => {\n//             const defaultValue = this.data.defaultFieldValue;\n//             for (const option of (selectElement as any).options) {\n//                 option.selected = option.value === defaultValue;\n//             }\n//         });\n//         // Insert the options into the choice field.\n//         for (const option of this.data.options) {\n//             const optionElement = document.createElement(\"option\");\n//             optionElement.textContent = option.displayValue;\n//             optionElement.value = option.exportValue;\n//             if (storedData.value.includes(option.exportValue)) {\n//                 optionElement.setAttribute(\"selected\", (true).toString());\n//                 addAnEmptyEntry = false;\n//             }\n//             selectElement.append(optionElement);\n//         }\n//         let removeEmptyEntry = null;\n//         if (addAnEmptyEntry) {\n//             const noneOptionElement = document.createElement(\"option\");\n//             noneOptionElement.value = \" \";\n//             noneOptionElement.setAttribute(\"hidden\", (true).toString());\n//             noneOptionElement.setAttribute(\"selected\", (true).toString());\n//             selectElement.prepend(noneOptionElement);\n//             removeEmptyEntry = () => {\n//                 noneOptionElement.remove();\n//                 selectElement.removeEventListener(\"input\", removeEmptyEntry);\n//                 removeEmptyEntry = null;\n//             };\n//             selectElement.addEventListener(\"input\", removeEmptyEntry);\n//         }\n//         const getValue = isExport => {\n//             const name = isExport ? \"value\" : \"textContent\";\n//             const { options, multiple } = selectElement;\n//             if (!multiple) {\n//                 return options.selectedIndex === -1\n//                     ? null\n//                     : options[options.selectedIndex][name];\n//             }\n//             return Array.prototype.filter\n//                 .call(options, option => option.selected)\n//                 .map(option => option[name]);\n//         };\n//         let selectedValues = getValue(/* isExport */ false);\n//         const getItems = event => {\n//             const options = event.target.options;\n//             return Array.prototype.map.call(options, option => ({\n//                 displayValue: option.textContent,\n//                 exportValue: option.value,\n//             }));\n//         };\n//         if (this.enableScripting && this.hasJSActions) {\n//             selectElement.addEventListener(\"updatefromsandbox\", jsEvent => {\n//                 const actions = {\n//                     value(event) {\n//                         removeEmptyEntry?.();\n//                         const value = event.detail.value;\n//                         const values = new Set(Array.isArray(value) ? value : [value]);\n//                         for (const option of (selectElement as any).options) {\n//                             option.selected = values.has(option.value);\n//                         }\n//                         storage.setValue(id, {\n//                             value: getValue(/* isExport */ true),\n//                         });\n//                         selectedValues = getValue(/* isExport */ false);\n//                     },\n//                     multipleSelection() {\n//                         selectElement.multiple = true;\n//                     },\n//                     remove(event) {\n//                         const options = selectElement.options;\n//                         const index = event.detail.remove;\n//                         options[index].selected = false;\n//                         selectElement.remove(index);\n//                         if (options.length > 0) {\n//                             const i = Array.prototype.findIndex.call(\n//                                 options,\n//                                 option => option.selected\n//                             );\n//                             if (i === -1) {\n//                                 options[0].selected = true;\n//                             }\n//                         }\n//                         storage.setValue(id, {\n//                             value: getValue(/* isExport */ true),\n//                             items: getItems(event),\n//                         });\n//                         selectedValues = getValue(/* isExport */ false);\n//                     },\n//                     clear() {\n//                         while (selectElement.length !== 0) {\n//                             selectElement.remove(0);\n//                         }\n//                         storage.setValue(id, { value: null, items: [] });\n//                         selectedValues = getValue(/* isExport */ false);\n//                     },\n//                     insert(event) {\n//                         const { index, displayValue, exportValue } = event.detail.insert;\n//                         const selectChild = selectElement.children[index];\n//                         const optionElement = document.createElement(\"option\");\n//                         optionElement.textContent = displayValue;\n//                         optionElement.value = exportValue;\n//                         if (selectChild) {\n//                             selectChild.before(optionElement);\n//                         } else {\n//                             selectElement.append(optionElement);\n//                         }\n//                         storage.setValue(id, {\n//                             value: getValue(/* isExport */ true),\n//                             items: getItems(event),\n//                         });\n//                         selectedValues = getValue(/* isExport */ false);\n//                     },\n//                     items(event) {\n//                         const { items } = event.detail;\n//                         while (selectElement.length !== 0) {\n//                             selectElement.remove(0);\n//                         }\n//                         for (const item of items) {\n//                             const { displayValue, exportValue } = item;\n//                             const optionElement = document.createElement(\"option\");\n//                             optionElement.textContent = displayValue;\n//                             optionElement.value = exportValue;\n//                             selectElement.append(optionElement);\n//                         }\n//                         if (selectElement.options.length > 0) {\n//                             selectElement.options[0].selected = true;\n//                         }\n//                         storage.setValue(id, {\n//                             value: getValue(/* isExport */ true),\n//                             items: getItems(event),\n//                         });\n//                         selectedValues = getValue(/* isExport */ false);\n//                     },\n//                     indices(event) {\n//                         const indices = new Set(event.detail.indices);\n//                         for (const option of event.target.options) {\n//                             option.selected = indices.has(option.index);\n//                         }\n//                         storage.setValue(id, {\n//                             value: getValue(/* isExport */ true),\n//                         });\n//                         selectedValues = getValue(/* isExport */ false);\n//                     },\n//                     editable(event) {\n//                         event.target.disabled = !event.detail.editable;\n//                     },\n//                 };\n//                 this._dispatchEventFromSandbox(actions, jsEvent);\n//             });\n//             selectElement.addEventListener(\"input\", event => {\n//                 const exportValue = getValue(/* isExport */ true);\n//                 const change = getValue(/* isExport */ false);\n//                 storage.setValue(id, { value: exportValue });\n//                 event.preventDefault();\n//                 this.linkService.eventBus?.dispatch(\"dispatcheventinsandbox\", {\n//                     source: this,\n//                     detail: {\n//                         id,\n//                         name: \"Keystroke\",\n//                         value: selectedValues,\n//                         change,\n//                         changeEx: exportValue,\n//                         willCommit: false,\n//                         commitKey: 1,\n//                         keyDown: false,\n//                     },\n//                 });\n//             });\n//             this._setEventListeners(\n//                 selectElement,\n//                 null,\n//                 [\n//                     [\"focus\", \"Focus\"],\n//                     [\"blur\", \"Blur\"],\n//                     [\"mousedown\", \"Mouse Down\"],\n//                     [\"mouseenter\", \"Mouse Enter\"],\n//                     [\"mouseleave\", \"Mouse Exit\"],\n//                     [\"mouseup\", \"Mouse Up\"],\n//                     [\"input\", \"Action\"],\n//                     [\"input\", \"Validate\"],\n//                 ],\n//                 event => event.target.value\n//             );\n//         } else {\n//             selectElement.addEventListener(\"input\", function () {\n//                 storage.setValue(id, { value: getValue(/* isExport */ true) });\n//             });\n//         }\n//         if (this.data.combo) {\n//             this._setTextStyle(selectElement);\n//         } else {\n//             // Just use the default font size...\n//             // it's a bit hard to guess what is a good size.\n//         }\n//         this._setBackgroundColor(selectElement);\n//         this._setDefaultPropertiesFromJS(selectElement);\n//         this.container.append(selectElement);\n//         return this.container;\n//     }\n// }\nclass PopupAnnotationElement extends AnnotationElement {\n    // todo: props\n    constructor(parameters) {\n        const { data, elements } = parameters;\n        super(parameters, { isRenderable: AnnotationElement._hasPopupData(data) });\n        // todo: props\n        this.elements = [];\n        this.elements = elements;\n        this.popup = null;\n    }\n    render() {\n        this.container.classList.add(\"popupAnnotation\");\n        const popup = (this.popup = new PopupElement({\n            container: this.container,\n            color: this.data.color,\n            titleObj: this.data.titleObj,\n            modificationDate: this.data.modificationDate,\n            contentsObj: this.data.contentsObj,\n            richText: this.data.richText,\n            rect: this.data.rect,\n            parentRect: this.data.parentRect || null,\n            parent: this.parent,\n            elements: this.elements,\n            open: this.data.open\n        }));\n        const elementIds = [];\n        for (const element of this.elements) {\n            element.popup = popup;\n            elementIds.push(element.data.id);\n            element.addHighlightArea();\n        }\n        this.container.setAttribute(\"aria-controls\", elementIds.map(id => `${AnnotationPrefix}${id}`).join(\",\"));\n        return this.container;\n    }\n}\nclass PopupElement {\n    constructor({ container, color, elements, titleObj, modificationDate, contentsObj, richText, parent, rect, parentRect, open }) {\n        var _a;\n        _PopupElement_instances.add(this);\n        // todo: props\n        this.trigger = null;\n        // todo: props\n        _PopupElement_boundKeyDown.set(this, __classPrivateFieldGet(this, _PopupElement_instances, \"m\", _PopupElement_keyDown).bind(this));\n        _PopupElement_boundHide.set(this, __classPrivateFieldGet(this, _PopupElement_instances, \"m\", _PopupElement_hide).bind(this));\n        _PopupElement_boundShow.set(this, __classPrivateFieldGet(this, _PopupElement_instances, \"m\", _PopupElement_show).bind(this));\n        _PopupElement_boundToggle.set(this, __classPrivateFieldGet(this, _PopupElement_instances, \"m\", _PopupElement_toggle).bind(this));\n        _PopupElement_color.set(this, null);\n        _PopupElement_container.set(this, null);\n        _PopupElement_contentsObj.set(this, null);\n        _PopupElement_dateObj.set(this, null);\n        _PopupElement_elements.set(this, null);\n        _PopupElement_parent.set(this, null);\n        _PopupElement_parentRect.set(this, null);\n        _PopupElement_pinned.set(this, false);\n        _PopupElement_popup.set(this, null);\n        _PopupElement_position.set(this, null);\n        _PopupElement_rect.set(this, null);\n        _PopupElement_richText.set(this, null);\n        _PopupElement_titleObj.set(this, null);\n        _PopupElement_updates.set(this, null);\n        _PopupElement_wasVisible.set(this, false);\n        __classPrivateFieldSet(this, _PopupElement_container, container, \"f\");\n        __classPrivateFieldSet(this, _PopupElement_titleObj, titleObj, \"f\");\n        __classPrivateFieldSet(this, _PopupElement_contentsObj, contentsObj, \"f\");\n        __classPrivateFieldSet(this, _PopupElement_richText, richText, \"f\");\n        __classPrivateFieldSet(this, _PopupElement_parent, parent, \"f\");\n        __classPrivateFieldSet(this, _PopupElement_color, color, \"f\");\n        __classPrivateFieldSet(this, _PopupElement_rect, rect, \"f\");\n        __classPrivateFieldSet(this, _PopupElement_parentRect, parentRect, \"f\");\n        __classPrivateFieldSet(this, _PopupElement_elements, elements, \"f\");\n        // The modification date is shown in the popup instead of the creation\n        // date if it is available and can be parsed correctly, which is\n        // consistent with other viewers such as Adobe Acrobat.\n        __classPrivateFieldSet(this, _PopupElement_dateObj, PDFDateString.toDateObject(modificationDate), \"f\");\n        this.trigger = elements.flatMap(e => e.getElementsToTriggerPopup());\n        // Attach the event listeners to the trigger element.\n        for (const element of this.trigger) {\n            element.addEventListener(\"click\", __classPrivateFieldGet(this, _PopupElement_boundToggle, \"f\"));\n            element.addEventListener(\"mouseenter\", __classPrivateFieldGet(this, _PopupElement_boundShow, \"f\"));\n            element.addEventListener(\"mouseleave\", __classPrivateFieldGet(this, _PopupElement_boundHide, \"f\"));\n            element.classList.add(\"popupTriggerArea\");\n        }\n        // Attach the event listener to toggle the popup with the keyboard.\n        for (const element of elements) {\n            (_a = element.container) === null || _a === void 0 ? void 0 : _a.addEventListener(\"keydown\", __classPrivateFieldGet(this, _PopupElement_boundKeyDown, \"f\"));\n        }\n        __classPrivateFieldGet(this, _PopupElement_container, \"f\").hidden = true;\n        if (open) {\n            __classPrivateFieldGet(this, _PopupElement_instances, \"m\", _PopupElement_toggle).call(this);\n        }\n        // if (typeof PDFJSDev !== \"undefined\" && PDFJSDev.test(\"TESTING\")) {\n        // Since the popup is lazily created, we need to ensure that it'll be\n        // created and displayed during reference tests.\n        __classPrivateFieldGet(this, _PopupElement_parent, \"f\").popupShow.push(() => __awaiter(this, void 0, void 0, function* () {\n            if (__classPrivateFieldGet(this, _PopupElement_container, \"f\").hidden) {\n                __classPrivateFieldGet(this, _PopupElement_instances, \"m\", _PopupElement_show).call(this);\n            }\n        }));\n        // }\n    }\n    render() {\n        if (__classPrivateFieldGet(this, _PopupElement_popup, \"f\")) {\n            return;\n        }\n        const popup = (__classPrivateFieldSet(this, _PopupElement_popup, document.createElement(\"div\"), \"f\"));\n        popup.className = \"popup\";\n        if (__classPrivateFieldGet(this, _PopupElement_color, \"f\")) {\n            const baseColor = (popup.style.outlineColor = Util.makeHexColor(\n            // @ts-expect-error TS(2556):\n            ...__classPrivateFieldGet(this, _PopupElement_color, \"f\")));\n            if (\n            // (typeof PDFJSDev !== \"undefined\" && PDFJSDev.test(\"MOZCENTRAL\")) ||\n            CSS.supports(\"background-color\", \"color-mix(in srgb, red 30%, white)\")) {\n                popup.style.backgroundColor = `color-mix(in srgb, ${baseColor} 30%, white)`;\n            }\n            else {\n                // color-mix isn't supported in some browsers hence this version.\n                // See https://developer.mozilla.org/en-US/docs/Web/CSS/color_value/color-mix#browser_compatibility\n                // TODO: Use color-mix when it's supported everywhere.\n                // Enlighten the color.\n                const BACKGROUND_ENLIGHT = 0.7;\n                popup.style.backgroundColor = Util.makeHexColor(\n                // @ts-expect-error TS(2556):\n                ...__classPrivateFieldGet(this, _PopupElement_color, \"f\").map(c => Math.floor(BACKGROUND_ENLIGHT * (255 - c) + c)));\n            }\n        }\n        const header = document.createElement(\"span\");\n        header.className = \"header\";\n        const title = document.createElement(\"h1\");\n        header.append(title);\n        ({ dir: title.dir, str: title.textContent } = __classPrivateFieldGet(this, _PopupElement_titleObj, \"f\"));\n        popup.append(header);\n        if (__classPrivateFieldGet(this, _PopupElement_dateObj, \"f\")) {\n            const modificationDate = document.createElement(\"span\");\n            modificationDate.classList.add(\"popupDate\");\n            modificationDate.setAttribute(\"data-l10n-id\", \"pdfjs-annotation-date-string\");\n            modificationDate.setAttribute(\"data-l10n-args\", JSON.stringify({\n                date: __classPrivateFieldGet(this, _PopupElement_dateObj, \"f\").toLocaleDateString(),\n                time: __classPrivateFieldGet(this, _PopupElement_dateObj, \"f\").toLocaleTimeString()\n            }));\n            header.append(modificationDate);\n        }\n        const html = __classPrivateFieldGet(this, _PopupElement_instances, \"a\", _PopupElement_html_get);\n        if (html) {\n            // @ts-expect-error TS(2556):\n            XfaLayer.render({\n                xfaHtml: html,\n                intent: \"richText\",\n                div: popup\n            });\n            // @ts-expect-error TS(2556):\n            popup.lastChild.classList.add(\"richText\", \"popupContent\");\n        }\n        else {\n            const contents = this._formatContents(__classPrivateFieldGet(this, _PopupElement_contentsObj, \"f\"));\n            popup.append(contents);\n        }\n        __classPrivateFieldGet(this, _PopupElement_container, \"f\").append(popup);\n    }\n    /**\n     * Format the contents of the popup by adding newlines where necessary.\n     *\n     * @private\n     * @param {Object<string, string>} contentsObj\n     * @memberof PopupElement\n     * @returns {HTMLParagraphElement}\n     */\n    _formatContents({ str, dir }) {\n        const p = document.createElement(\"p\");\n        p.classList.add(\"popupContent\");\n        p.dir = dir;\n        const lines = str.split(/(?:\\r\\n?|\\n)/);\n        for (let i = 0, ii = lines.length; i < ii; ++i) {\n            const line = lines[i];\n            p.append(document.createTextNode(line));\n            if (i < ii - 1) {\n                p.append(document.createElement(\"br\"));\n            }\n        }\n        return p;\n    }\n    updateEdited({ rect, popupContent }) {\n        var _a;\n        __classPrivateFieldSet(this, _PopupElement_updates, __classPrivateFieldGet(this, _PopupElement_updates, \"f\") || {\n            contentsObj: __classPrivateFieldGet(this, _PopupElement_contentsObj, \"f\"),\n            richText: __classPrivateFieldGet(this, _PopupElement_richText, \"f\")\n        }, \"f\");\n        if (rect) {\n            __classPrivateFieldSet(this, _PopupElement_position, null, \"f\");\n        }\n        if (popupContent) {\n            __classPrivateFieldSet(this, _PopupElement_richText, __classPrivateFieldGet(this, _PopupElement_instances, \"m\", _PopupElement_makePopupContent).call(this, popupContent), \"f\");\n            __classPrivateFieldSet(this, _PopupElement_contentsObj, null, \"f\");\n        }\n        (_a = __classPrivateFieldGet(this, _PopupElement_popup, \"f\")) === null || _a === void 0 ? void 0 : _a.remove();\n        __classPrivateFieldSet(this, _PopupElement_popup, null, \"f\");\n    }\n    resetEdited() {\n        var _a;\n        var _b, _c;\n        if (!__classPrivateFieldGet(this, _PopupElement_updates, \"f\")) {\n            return;\n        }\n        (_b = this, _c = this, { contentsObj: ({ set value(_a) { __classPrivateFieldSet(_b, _PopupElement_contentsObj, _a, \"f\"); } }).value, richText: ({ set value(_a) { __classPrivateFieldSet(_c, _PopupElement_richText, _a, \"f\"); } }).value } =\n            __classPrivateFieldGet(this, _PopupElement_updates, \"f\"));\n        __classPrivateFieldSet(this, _PopupElement_updates, null, \"f\");\n        (_a = __classPrivateFieldGet(this, _PopupElement_popup, \"f\")) === null || _a === void 0 ? void 0 : _a.remove();\n        __classPrivateFieldSet(this, _PopupElement_popup, null, \"f\");\n        __classPrivateFieldSet(this, _PopupElement_position, null, \"f\");\n    }\n    forceHide() {\n        __classPrivateFieldSet(this, _PopupElement_wasVisible, this.isVisible, \"f\");\n        if (!__classPrivateFieldGet(this, _PopupElement_wasVisible, \"f\")) {\n            return;\n        }\n        __classPrivateFieldGet(this, _PopupElement_container, \"f\").hidden = true;\n    }\n    maybeShow() {\n        if (!__classPrivateFieldGet(this, _PopupElement_wasVisible, \"f\")) {\n            return;\n        }\n        if (!__classPrivateFieldGet(this, _PopupElement_popup, \"f\")) {\n            __classPrivateFieldGet(this, _PopupElement_instances, \"m\", _PopupElement_show).call(this);\n        }\n        __classPrivateFieldSet(this, _PopupElement_wasVisible, false, \"f\");\n        __classPrivateFieldGet(this, _PopupElement_container, \"f\").hidden = false;\n    }\n    get isVisible() {\n        return __classPrivateFieldGet(this, _PopupElement_container, \"f\").hidden === false;\n    }\n}\n_PopupElement_boundKeyDown = new WeakMap(), _PopupElement_boundHide = new WeakMap(), _PopupElement_boundShow = new WeakMap(), _PopupElement_boundToggle = new WeakMap(), _PopupElement_color = new WeakMap(), _PopupElement_container = new WeakMap(), _PopupElement_contentsObj = new WeakMap(), _PopupElement_dateObj = new WeakMap(), _PopupElement_elements = new WeakMap(), _PopupElement_parent = new WeakMap(), _PopupElement_parentRect = new WeakMap(), _PopupElement_pinned = new WeakMap(), _PopupElement_popup = new WeakMap(), _PopupElement_position = new WeakMap(), _PopupElement_rect = new WeakMap(), _PopupElement_richText = new WeakMap(), _PopupElement_titleObj = new WeakMap(), _PopupElement_updates = new WeakMap(), _PopupElement_wasVisible = new WeakMap(), _PopupElement_instances = new WeakSet(), _PopupElement_html_get = function _PopupElement_html_get() {\n    const richText = __classPrivateFieldGet(this, _PopupElement_richText, \"f\");\n    const contentsObj = __classPrivateFieldGet(this, _PopupElement_contentsObj, \"f\");\n    if ((richText === null || richText === void 0 ? void 0 : richText.str) &&\n        (!(contentsObj === null || contentsObj === void 0 ? void 0 : contentsObj.str) || contentsObj.str === richText.str)) {\n        return __classPrivateFieldGet(this, _PopupElement_richText, \"f\").html || null;\n    }\n    return null;\n}, _PopupElement_fontSize_get = function _PopupElement_fontSize_get() {\n    var _a, _b, _c;\n    return ((_c = (_b = (_a = __classPrivateFieldGet(this, _PopupElement_instances, \"a\", _PopupElement_html_get)) === null || _a === void 0 ? void 0 : _a.attributes) === null || _b === void 0 ? void 0 : _b.style) === null || _c === void 0 ? void 0 : _c.fontSize) || 0;\n}, _PopupElement_fontColor_get = function _PopupElement_fontColor_get() {\n    var _a, _b, _c;\n    return ((_c = (_b = (_a = __classPrivateFieldGet(this, _PopupElement_instances, \"a\", _PopupElement_html_get)) === null || _a === void 0 ? void 0 : _a.attributes) === null || _b === void 0 ? void 0 : _b.style) === null || _c === void 0 ? void 0 : _c.color) || null;\n}, _PopupElement_makePopupContent = function _PopupElement_makePopupContent(text) {\n    const popupLines = [];\n    const popupContent = {\n        str: text,\n        html: {\n            name: \"div\",\n            attributes: {\n                dir: \"auto\"\n            },\n            children: [\n                {\n                    name: \"p\",\n                    children: popupLines\n                }\n            ]\n        }\n    };\n    const lineAttributes = {\n        style: {\n            color: __classPrivateFieldGet(this, _PopupElement_instances, \"a\", _PopupElement_fontColor_get),\n            fontSize: __classPrivateFieldGet(this, _PopupElement_instances, \"a\", _PopupElement_fontSize_get)\n                ? `calc(${__classPrivateFieldGet(this, _PopupElement_instances, \"a\", _PopupElement_fontSize_get)}px * var(--scale-factor))`\n                : \"\"\n        }\n    };\n    for (const line of text.split(\"\\n\")) {\n        popupLines.push({\n            name: \"span\",\n            value: line,\n            attributes: lineAttributes\n        });\n    }\n    return popupContent;\n}, _PopupElement_keyDown = function _PopupElement_keyDown(event) {\n    if (event.altKey || event.shiftKey || event.ctrlKey || event.metaKey) {\n        return;\n    }\n    if (event.key === \"Enter\" || (event.key === \"Escape\" && __classPrivateFieldGet(this, _PopupElement_pinned, \"f\"))) {\n        __classPrivateFieldGet(this, _PopupElement_instances, \"m\", _PopupElement_toggle).call(this);\n    }\n}, _PopupElement_setPosition = function _PopupElement_setPosition() {\n    if (__classPrivateFieldGet(this, _PopupElement_position, \"f\") !== null) {\n        return;\n    }\n    const { page: { view }, viewport: { rawDims: { pageWidth, pageHeight, pageX, pageY } } } = __classPrivateFieldGet(this, _PopupElement_parent, \"f\");\n    let useParentRect = !!__classPrivateFieldGet(this, _PopupElement_parentRect, \"f\");\n    let rect = useParentRect ? __classPrivateFieldGet(this, _PopupElement_parentRect, \"f\") : __classPrivateFieldGet(this, _PopupElement_rect, \"f\");\n    for (const element of __classPrivateFieldGet(this, _PopupElement_elements, \"f\")) {\n        if (!rect || Util.intersect(element.data.rect, rect) !== null) {\n            rect = element.data.rect;\n            useParentRect = true;\n            break;\n        }\n    }\n    const normalizedRect = Util.normalizeRect([\n        rect[0],\n        view[3] - rect[1] + view[1],\n        rect[2],\n        view[3] - rect[3] + view[1]\n    ]);\n    const HORIZONTAL_SPACE_AFTER_ANNOTATION = 5;\n    const parentWidth = useParentRect\n        ? rect[2] - rect[0] + HORIZONTAL_SPACE_AFTER_ANNOTATION\n        : 0;\n    const popupLeft = normalizedRect[0] + parentWidth;\n    const popupTop = normalizedRect[1];\n    __classPrivateFieldSet(this, _PopupElement_position, [\n        (100 * (popupLeft - pageX)) / pageWidth,\n        (100 * (popupTop - pageY)) / pageHeight\n    ], \"f\");\n    const { style } = __classPrivateFieldGet(this, _PopupElement_container, \"f\");\n    style.left = `${__classPrivateFieldGet(this, _PopupElement_position, \"f\")[0]}%`;\n    style.top = `${__classPrivateFieldGet(this, _PopupElement_position, \"f\")[1]}%`;\n}, _PopupElement_toggle = function _PopupElement_toggle() {\n    __classPrivateFieldSet(this, _PopupElement_pinned, !__classPrivateFieldGet(this, _PopupElement_pinned, \"f\"), \"f\");\n    if (__classPrivateFieldGet(this, _PopupElement_pinned, \"f\")) {\n        __classPrivateFieldGet(this, _PopupElement_instances, \"m\", _PopupElement_show).call(this);\n        __classPrivateFieldGet(this, _PopupElement_container, \"f\").addEventListener(\"click\", __classPrivateFieldGet(this, _PopupElement_boundToggle, \"f\"));\n        __classPrivateFieldGet(this, _PopupElement_container, \"f\").addEventListener(\"keydown\", __classPrivateFieldGet(this, _PopupElement_boundKeyDown, \"f\"));\n    }\n    else {\n        __classPrivateFieldGet(this, _PopupElement_instances, \"m\", _PopupElement_hide).call(this);\n        __classPrivateFieldGet(this, _PopupElement_container, \"f\").removeEventListener(\"click\", __classPrivateFieldGet(this, _PopupElement_boundToggle, \"f\"));\n        __classPrivateFieldGet(this, _PopupElement_container, \"f\").removeEventListener(\"keydown\", __classPrivateFieldGet(this, _PopupElement_boundKeyDown, \"f\"));\n    }\n}, _PopupElement_show = function _PopupElement_show() {\n    if (!__classPrivateFieldGet(this, _PopupElement_popup, \"f\")) {\n        this.render();\n    }\n    if (!this.isVisible) {\n        __classPrivateFieldGet(this, _PopupElement_instances, \"m\", _PopupElement_setPosition).call(this);\n        __classPrivateFieldGet(this, _PopupElement_container, \"f\").hidden = false;\n        __classPrivateFieldGet(this, _PopupElement_container, \"f\").style.zIndex = (parseInt(__classPrivateFieldGet(this, _PopupElement_container, \"f\").style.zIndex, 10) + 1000).toString();\n    }\n    else if (__classPrivateFieldGet(this, _PopupElement_pinned, \"f\")) {\n        __classPrivateFieldGet(this, _PopupElement_container, \"f\").classList.add(\"focused\");\n    }\n}, _PopupElement_hide = function _PopupElement_hide() {\n    __classPrivateFieldGet(this, _PopupElement_container, \"f\").classList.remove(\"focused\");\n    if (__classPrivateFieldGet(this, _PopupElement_pinned, \"f\") || !this.isVisible) {\n        return;\n    }\n    __classPrivateFieldGet(this, _PopupElement_container, \"f\").hidden = true;\n    __classPrivateFieldGet(this, _PopupElement_container, \"f\").style.zIndex = (parseInt(__classPrivateFieldGet(this, _PopupElement_container, \"f\").style.zIndex, 10) - 1000).toString();\n};\nclass FreeTextAnnotationElement extends AnnotationElement {\n    // todo: props\n    constructor(parameters) {\n        super(parameters, { isRenderable: true, ignoreBorder: true });\n        // todo: props\n        this.textContent = null;\n        this.textPosition = null;\n        this.textContent = parameters.data.textContent;\n        this.textPosition = parameters.data.textPosition;\n        this.annotationEditorType = AnnotationEditorType.FREETEXT;\n    }\n    render() {\n        // this.container.classList.add(\"freeTextAnnotation\");\n        // this.container.classList.add(\"freeTextAnnotation\");\n        this.container.classList.add(\"k-free-text-annotation\");\n        if (this.textContent) {\n            const content = document.createElement(\"div\");\n            // content.classList.add(\"annotationTextContent\");\n            content.classList.add(\"k-annotation-text-content\");\n            content.setAttribute(\"role\", \"comment\");\n            for (const line of this.textContent) {\n                const lineSpan = document.createElement(\"span\");\n                lineSpan.textContent = line;\n                content.append(lineSpan);\n            }\n            this.container.append(content);\n        }\n        // if (!this.data.popupRef && this.hasPopupData) {\n        //     this._createPopup();\n        // }\n        this._editOnDoubleClick();\n        return this.container;\n    }\n}\n// class LineAnnotationElement extends AnnotationElement {\n//     #line = null;\n//     constructor(parameters) {\n//         super(parameters, { isRenderable: true, ignoreBorder: true });\n//     }\n//     render() {\n//         this.container.classList.add(\"lineAnnotation\");\n//         // Create an invisible line with the same starting and ending coordinates\n//         // that acts as the trigger for the popup. Only the line itself should\n//         // trigger the popup, not the entire container.\n//         const data = this.data;\n//         const { width, height } = getRectDims(data.rect);\n//         const svg = this.svgFactory.create(\n//             width,\n//             height,\n//             /* skipDimensions = */ true\n//         );\n//         // PDF coordinates are calculated from a bottom left origin, so transform\n//         // the line coordinates to a top left origin for the SVG element.\n//         const line = (this.#line = this.svgFactory.createElement(\"svg:line\"));\n//         line.setAttribute(\"x1\", data.rect[2] - data.lineCoordinates[0]);\n//         line.setAttribute(\"y1\", data.rect[3] - data.lineCoordinates[1]);\n//         line.setAttribute(\"x2\", data.rect[2] - data.lineCoordinates[2]);\n//         line.setAttribute(\"y2\", data.rect[3] - data.lineCoordinates[3]);\n//         // Ensure that the 'stroke-width' is always non-zero, since otherwise it\n//         // won't be possible to open/close the popup (note e.g. issue 11122).\n//         line.setAttribute(\"stroke-width\", data.borderStyle.width || 1);\n//         line.setAttribute(\"stroke\", \"transparent\");\n//         line.setAttribute(\"fill\", \"transparent\");\n//         svg.append(line);\n//         this.container.append(svg);\n//         // Create the popup ourselves so that we can bind it to the line instead\n//         // of to the entire container (which is the default).\n//         if (!data.popupRef && this.hasPopupData) {\n//             this._createPopup();\n//         }\n//         return this.container;\n//     }\n//     getElementsToTriggerPopup() {\n//         return this.#line;\n//     }\n//     addHighlightArea() {\n//         this.container.classList.add(\"highlightArea\");\n//     }\n// }\n// class SquareAnnotationElement extends AnnotationElement {\n//     #square = null;\n//     constructor(parameters) {\n//         super(parameters, { isRenderable: true, ignoreBorder: true });\n//     }\n//     render() {\n//         this.container.classList.add(\"squareAnnotation\");\n//         // Create an invisible square with the same rectangle that acts as the\n//         // trigger for the popup. Only the square itself should trigger the\n//         // popup, not the entire container.\n//         const data = this.data;\n//         const { width, height } = getRectDims(data.rect);\n//         const svg = this.svgFactory.create(\n//             width,\n//             height,\n//             /* skipDimensions = */ true\n//         );\n//         // The browser draws half of the borders inside the square and half of\n//         // the borders outside the square by default. This behavior cannot be\n//         // changed programmatically, so correct for that here.\n//         const borderWidth = data.borderStyle.width;\n//         const square = (this.#square = this.svgFactory.createElement(\"svg:rect\"));\n//         square.setAttribute(\"x\", borderWidth / 2);\n//         square.setAttribute(\"y\", borderWidth / 2);\n//         square.setAttribute(\"width\", width - borderWidth);\n//         square.setAttribute(\"height\", height - borderWidth);\n//         // Ensure that the 'stroke-width' is always non-zero, since otherwise it\n//         // won't be possible to open/close the popup (note e.g. issue 11122).\n//         square.setAttribute(\"stroke-width\", borderWidth || 1);\n//         square.setAttribute(\"stroke\", \"transparent\");\n//         square.setAttribute(\"fill\", \"transparent\");\n//         svg.append(square);\n//         this.container.append(svg);\n//         // Create the popup ourselves so that we can bind it to the square instead\n//         // of to the entire container (which is the default).\n//         if (!data.popupRef && this.hasPopupData) {\n//             this._createPopup();\n//         }\n//         return this.container;\n//     }\n//     getElementsToTriggerPopup() {\n//         return this.#square;\n//     }\n//     addHighlightArea() {\n//         this.container.classList.add(\"highlightArea\");\n//     }\n// }\n// class CircleAnnotationElement extends AnnotationElement {\n//     #circle = null;\n//     constructor(parameters) {\n//         super(parameters, { isRenderable: true, ignoreBorder: true });\n//     }\n//     render() {\n//         this.container.classList.add(\"circleAnnotation\");\n//         // Create an invisible circle with the same ellipse that acts as the\n//         // trigger for the popup. Only the circle itself should trigger the\n//         // popup, not the entire container.\n//         const data = this.data;\n//         const { width, height } = getRectDims(data.rect);\n//         const svg = this.svgFactory.create(\n//             width,\n//             height,\n//             /* skipDimensions = */ true\n//         );\n//         // The browser draws half of the borders inside the circle and half of\n//         // the borders outside the circle by default. This behavior cannot be\n//         // changed programmatically, so correct for that here.\n//         const borderWidth = data.borderStyle.width;\n//         const circle = (this.#circle =\n//             this.svgFactory.createElement(\"svg:ellipse\"));\n//         circle.setAttribute(\"cx\", width / 2);\n//         circle.setAttribute(\"cy\", height / 2);\n//         circle.setAttribute(\"rx\", width / 2 - borderWidth / 2);\n//         circle.setAttribute(\"ry\", height / 2 - borderWidth / 2);\n//         // Ensure that the 'stroke-width' is always non-zero, since otherwise it\n//         // won't be possible to open/close the popup (note e.g. issue 11122).\n//         circle.setAttribute(\"stroke-width\", borderWidth || 1);\n//         circle.setAttribute(\"stroke\", \"transparent\");\n//         circle.setAttribute(\"fill\", \"transparent\");\n//         svg.append(circle);\n//         this.container.append(svg);\n//         // Create the popup ourselves so that we can bind it to the circle instead\n//         // of to the entire container (which is the default).\n//         if (!data.popupRef && this.hasPopupData) {\n//             this._createPopup();\n//         }\n//         return this.container;\n//     }\n//     getElementsToTriggerPopup() {\n//         return this.#circle;\n//     }\n//     addHighlightArea() {\n//         this.container.classList.add(\"highlightArea\");\n//     }\n// }\n// class PolylineAnnotationElement extends AnnotationElement {\n//     // todo: props\n//     containerClassName: any = null;\n//     svgElementName: any = null;\n//     // todo: props\n//     #polyline = null;\n//     constructor(parameters) {\n//         super(parameters, { isRenderable: true, ignoreBorder: true });\n//         this.containerClassName = \"polylineAnnotation\";\n//         this.svgElementName = \"svg:polyline\";\n//     }\n//     render() {\n//         this.container.classList.add(this.containerClassName);\n//         // Create an invisible polyline with the same points that acts as the\n//         // trigger for the popup. Only the polyline itself should trigger the\n//         // popup, not the entire container.\n//         const {\n//             data: { rect, vertices, borderStyle, popupRef },\n//         } = this;\n//         if (!vertices) {\n//             return this.container;\n//         }\n//         const { width, height } = getRectDims(rect);\n//         const svg = this.svgFactory.create(\n//             width,\n//             height,\n//             /* skipDimensions = */ true\n//         );\n//         // Convert the vertices array to a single points string that the SVG\n//         // polyline element expects (\"x1,y1 x2,y2 ...\"). PDF coordinates are\n//         // calculated from a bottom left origin, so transform the polyline\n//         // coordinates to a top left origin for the SVG element.\n//         const pointsArray = [];\n//         for (let i = 0, ii = vertices.length; i < ii; i += 2) {\n//             const x = vertices[i] - rect[0];\n//             const y = rect[3] - vertices[i + 1];\n//             pointsArray.push(`${x},${y}`);\n//         }\n//         const points = pointsArray.join(\" \");\n//         const polyline = (this.#polyline = this.svgFactory.createElement(\n//             this.svgElementName\n//         ));\n//         polyline.setAttribute(\"points\", points);\n//         // Ensure that the 'stroke-width' is always non-zero, since otherwise it\n//         // won't be possible to open/close the popup (note e.g. issue 11122).\n//         polyline.setAttribute(\"stroke-width\", borderStyle.width || 1);\n//         polyline.setAttribute(\"stroke\", \"transparent\");\n//         polyline.setAttribute(\"fill\", \"transparent\");\n//         svg.append(polyline);\n//         this.container.append(svg);\n//         // Create the popup ourselves so that we can bind it to the polyline\n//         // instead of to the entire container (which is the default).\n//         if (!popupRef && this.hasPopupData) {\n//             this._createPopup();\n//         }\n//         return this.container;\n//     }\n//     getElementsToTriggerPopup() {\n//         return this.#polyline;\n//     }\n//     addHighlightArea() {\n//         this.container.classList.add(\"highlightArea\");\n//     }\n// }\n// class PolygonAnnotationElement extends PolylineAnnotationElement {\n//     constructor(parameters) {\n//         // Polygons are specific forms of polylines, so reuse their logic.\n//         super(parameters);\n//         this.containerClassName = \"polygonAnnotation\";\n//         this.svgElementName = \"svg:polygon\";\n//     }\n// }\n// class CaretAnnotationElement extends AnnotationElement {\n//     constructor(parameters) {\n//         super(parameters, { isRenderable: true, ignoreBorder: true });\n//     }\n//     render() {\n//         this.container.classList.add(\"caretAnnotation\");\n//         if (!this.data.popupRef && this.hasPopupData) {\n//             this._createPopup();\n//         }\n//         return this.container;\n//     }\n// }\n// class InkAnnotationElement extends AnnotationElement {\n//     // todo: props\n//     containerClassName: any = null;\n//     svgElementName: any = null;\n//     // todo: props\n//     #polylines = [];\n//     constructor(parameters) {\n//         super(parameters, { isRenderable: true, ignoreBorder: true });\n//         this.containerClassName = \"inkAnnotation\";\n//         // Use the polyline SVG element since it allows us to use coordinates\n//         // directly and to draw both straight lines and curves.\n//         this.svgElementName = \"svg:polyline\";\n//         this.annotationEditorType = AnnotationEditorType.INK;\n//     }\n//     render() {\n//         this.container.classList.add(this.containerClassName);\n//         // Create an invisible polyline with the same points that acts as the\n//         // trigger for the popup.\n//         const {\n//             data: { rect, inkLists, borderStyle, popupRef },\n//         } = this;\n//         const { width, height } = getRectDims(rect);\n//         const svg = this.svgFactory.create(\n//             width,\n//             height,\n//             /* skipDimensions = */ true\n//         );\n//         for (const inkList of inkLists) {\n//             // Convert the ink list to a single points string that the SVG\n//             // polyline element expects (\"x1,y1 x2,y2 ...\"). PDF coordinates are\n//             // calculated from a bottom left origin, so transform the polyline\n//             // coordinates to a top left origin for the SVG element.\n//             const pointsArray = [];\n//             for (let i = 0, ii = inkList.length; i < ii; i += 2) {\n//                 const x = inkList[i] - rect[0];\n//                 const y = rect[3] - inkList[i + 1];\n//                 pointsArray.push(`${x},${y}`);\n//             }\n//             const points = pointsArray.join(\" \");\n//             const polyline = this.svgFactory.createElement(this.svgElementName);\n//             this.#polylines.push(polyline);\n//             polyline.setAttribute(\"points\", points);\n//             // Ensure that the 'stroke-width' is always non-zero, since otherwise it\n//             // won't be possible to open/close the popup (note e.g. issue 11122).\n//             polyline.setAttribute(\"stroke-width\", borderStyle.width || 1);\n//             polyline.setAttribute(\"stroke\", \"transparent\");\n//             polyline.setAttribute(\"fill\", \"transparent\");\n//             // Create the popup ourselves so that we can bind it to the polyline\n//             // instead of to the entire container (which is the default).\n//             if (!popupRef && this.hasPopupData) {\n//                 this._createPopup();\n//             }\n//             svg.append(polyline);\n//         }\n//         this.container.append(svg);\n//         return this.container;\n//     }\n//     getElementsToTriggerPopup() {\n//         return this.#polylines;\n//     }\n//     addHighlightArea() {\n//         this.container.classList.add(\"highlightArea\");\n//     }\n// }\nclass HighlightAnnotationElement extends AnnotationElement {\n    constructor(parameters) {\n        super(parameters, {\n            isRenderable: true,\n            ignoreBorder: true,\n            createQuadrilaterals: true\n        });\n    }\n    render() {\n        // if (!this.data.popupRef && this.hasPopupData) {\n        //     this._createPopup();\n        // }\n        // this.container.classList.add(\"highlightAnnotation\");\n        this.container.classList.add(\"k-highlight-annotation\");\n        return this.container;\n    }\n}\n// class UnderlineAnnotationElement extends AnnotationElement {\n//     constructor(parameters) {\n//         super(parameters, {\n//             isRenderable: true,\n//             ignoreBorder: true,\n//             createQuadrilaterals: true,\n//         });\n//     }\n//     render() {\n//         if (!this.data.popupRef && this.hasPopupData) {\n//             this._createPopup();\n//         }\n//         this.container.classList.add(\"underlineAnnotation\");\n//         return this.container;\n//     }\n// }\n// class SquigglyAnnotationElement extends AnnotationElement {\n//     constructor(parameters) {\n//         super(parameters, {\n//             isRenderable: true,\n//             ignoreBorder: true,\n//             createQuadrilaterals: true,\n//         });\n//     }\n//     render() {\n//         if (!this.data.popupRef && this.hasPopupData) {\n//             this._createPopup();\n//         }\n//         this.container.classList.add(\"squigglyAnnotation\");\n//         return this.container;\n//     }\n// }\n// class StrikeOutAnnotationElement extends AnnotationElement {\n//     constructor(parameters) {\n//         super(parameters, {\n//             isRenderable: true,\n//             ignoreBorder: true,\n//             createQuadrilaterals: true,\n//         });\n//     }\n//     render() {\n//         if (!this.data.popupRef && this.hasPopupData) {\n//             this._createPopup();\n//         }\n//         this.container.classList.add(\"strikeoutAnnotation\");\n//         return this.container;\n//     }\n// }\n// class StampAnnotationElement extends AnnotationElement {\n//     constructor(parameters) {\n//         super(parameters, { isRenderable: true, ignoreBorder: true });\n//     }\n//     render() {\n//         this.container.classList.add(\"stampAnnotation\");\n//         if (!this.data.popupRef && this.hasPopupData) {\n//             this._createPopup();\n//         }\n//         return this.container;\n//     }\n// }\n// class FileAttachmentAnnotationElement extends AnnotationElement {\n//     // todo: props\n//     filename: any = null;\n//     content: any = null;\n//     // todo: props\n//     #trigger = null;\n//     constructor(parameters) {\n//         super(parameters, { isRenderable: true });\n//         const { file } = this.data;\n//         this.filename = file.filename;\n//         this.content = file.content;\n//         this.linkService.eventBus?.dispatch(\"fileattachmentannotation\", {\n//             source: this,\n//             ...file,\n//         });\n//     }\n//     render() {\n//         this.container.classList.add(\"fileAttachmentAnnotation\");\n//         const { container, data } = this;\n//         let trigger;\n//         if (data.hasAppearance || data.fillAlpha === 0) {\n//             trigger = document.createElement(\"div\");\n//         } else {\n//             // Unfortunately it seems that it's not clearly specified exactly what\n//             // names are actually valid, since Table 184 contains:\n//             //   Conforming readers shall provide predefined icon appearances for at\n//             //   least the following standard names: GraphPushPin, PaperclipTag.\n//             //   Additional names may be supported as well. Default value: PushPin.\n//             trigger = document.createElement(\"img\");\n//             trigger.src = `${this.imageResourcesPath}annotation-${/paperclip/i.test(data.name) ?\n//                 \"paperclip\" : \"pushpin\"}.svg`;\n//             if (data.fillAlpha && data.fillAlpha < 1) {\n//                 trigger.style = `filter: opacity(${Math.round(\n//                     data.fillAlpha * 100\n//                 )}%);`;\n//                 // todo: debug\n//                 // if (typeof PDFJSDev !== \"undefined\" && PDFJSDev.test(\"TESTING\")) {\n//                 //     this.container.classList.add(\"hasFillAlpha\");\n//                 // }\n//             }\n//         }\n//         trigger.addEventListener(\"dblclick\", this.#download.bind(this));\n//         this.#trigger = trigger;\n//         const { isMac } = FeatureTest.platform;\n//         container.addEventListener(\"keydown\", evt => {\n//             if (evt.key === \"Enter\" && (isMac ? evt.metaKey : evt.ctrlKey)) {\n//                 this.#download();\n//             }\n//         });\n//         if (!data.popupRef && this.hasPopupData) {\n//             this._createPopup();\n//         } else {\n//             trigger.classList.add(\"popupTriggerArea\");\n//         }\n//         container.append(trigger);\n//         return container;\n//     }\n//     getElementsToTriggerPopup() {\n//         return this.#trigger;\n//     }\n//     addHighlightArea() {\n//         this.container.classList.add(\"highlightArea\");\n//     }\n//     /**\n//      * Download the file attachment associated with this annotation.\n//      */\n//     #download() {\n//         this.downloadManager?.openOrDownloadData(this.content, this.filename);\n//     }\n// }\n/**\n * Manage the layer containing all the annotations.\n */\nclass AnnotationLayer {\n    constructor({ div, accessibilityManager, annotationCanvasMap, annotationEditorUIManager, page, viewport }) {\n        _AnnotationLayer_instances.add(this);\n        this.div = null;\n        this.page = null;\n        this.viewport = null;\n        this.zIndex = null;\n        this._annotationEditorUIManager = null;\n        _AnnotationLayer_accessibilityManager.set(this, null);\n        _AnnotationLayer_annotationCanvasMap.set(this, null);\n        _AnnotationLayer_editableAnnotations.set(this, new Map());\n        this.div = div;\n        __classPrivateFieldSet(this, _AnnotationLayer_accessibilityManager, accessibilityManager, \"f\");\n        __classPrivateFieldSet(this, _AnnotationLayer_annotationCanvasMap, annotationCanvasMap, \"f\");\n        this.page = page;\n        this.viewport = viewport;\n        this.zIndex = 0;\n        this._annotationEditorUIManager = annotationEditorUIManager;\n        // if (typeof PDFJSDev !== \"undefined\" && PDFJSDev.test(\"TESTING\")) {\n        //     // For testing purposes.\n        //     Object.defineProperty(this, \"showPopups\", {\n        //         value: async () => {\n        //             for (const show of this.popupShow) {\n        //                 await show();\n        //             }\n        //         },\n        //     });\n        //     this.popupShow = [];\n        // }\n    }\n    // todo: ported from AnnotationLayerBuilder\n    hide() {\n        if (!this.div) {\n            return;\n        }\n        this.div.hidden = true;\n    }\n    // todo: ported from AnnotationLayerBuilder\n    hasEditableAnnotations() {\n        return __classPrivateFieldGet(this, _AnnotationLayer_editableAnnotations, \"f\").size > 0;\n    }\n    /**\n     * Render a new annotation layer with all annotation elements.\n     *\n     * @param {AnnotationLayerParameters} params\n     * @memberof AnnotationLayer\n     */\n    // async render(params) {\n    render(params) {\n        var _a;\n        const { annotations } = params;\n        const layer = this.div;\n        setLayerDimensions(layer, this.viewport);\n        const popupToElements = new Map();\n        const elementParams = {\n            data: null,\n            layer,\n            linkService: params.linkService,\n            downloadManager: params.downloadManager,\n            imageResourcesPath: params.imageResourcesPath || \"\",\n            renderForms: params.renderForms !== false,\n            svgFactory: new DOMSVGFactory(),\n            annotationStorage: params.annotationStorage,\n            enableScripting: params.enableScripting === true,\n            hasJSActions: params.hasJSActions,\n            fieldObjects: params.fieldObjects,\n            parent: this,\n            elements: null\n        };\n        for (const data of annotations) {\n            if (data.noHTML) {\n                continue;\n            }\n            const isPopupAnnotation = data.annotationType === AnnotationType.POPUP;\n            if (!isPopupAnnotation) {\n                const { width, height } = getRectDims(data.rect);\n                if (width <= 0 || height <= 0) {\n                    continue; // Ignore empty annotations.\n                }\n            }\n            else {\n                const elements = popupToElements.get(data.id);\n                if (!elements) {\n                    // Ignore popup annotations without a corresponding annotation.\n                    continue;\n                }\n                elementParams.elements = elements;\n            }\n            elementParams.data = data;\n            const element = AnnotationElementFactory.create(elementParams);\n            if (!element.isRenderable) {\n                continue;\n            }\n            if (!isPopupAnnotation && data.popupRef) {\n                const elements = popupToElements.get(data.popupRef);\n                if (!elements) {\n                    popupToElements.set(data.popupRef, [element]);\n                }\n                else {\n                    elements.push(element);\n                }\n            }\n            const rendered = element.render();\n            if (data.hidden) {\n                rendered.style.visibility = \"hidden\";\n            }\n            __classPrivateFieldGet(this, _AnnotationLayer_instances, \"m\", _AnnotationLayer_appendElement).call(this, rendered, data.id);\n            if (element._isEditable) {\n                __classPrivateFieldGet(this, _AnnotationLayer_editableAnnotations, \"f\").set(element.data.id, element);\n                (_a = this._annotationEditorUIManager) === null || _a === void 0 ? void 0 : _a.renderAnnotationElement(element);\n            }\n        }\n        __classPrivateFieldGet(this, _AnnotationLayer_instances, \"m\", _AnnotationLayer_setAnnotationCanvasMap).call(this);\n    }\n    /**\n     * Update the annotation elements on existing annotation layer.\n     *\n     * @param {AnnotationLayerParameters} viewport\n     * @memberof AnnotationLayer\n     */\n    update({ viewport }) {\n        const layer = this.div;\n        this.viewport = viewport;\n        // setLayerDimensions(layer, { rotation: viewport.rotation });\n        setLayerDimensions(layer, viewport);\n        __classPrivateFieldGet(this, _AnnotationLayer_instances, \"m\", _AnnotationLayer_setAnnotationCanvasMap).call(this);\n        layer.hidden = false;\n    }\n    getEditableAnnotations() {\n        return Array.from(__classPrivateFieldGet(this, _AnnotationLayer_editableAnnotations, \"f\").values());\n    }\n    getEditableAnnotation(id) {\n        return __classPrivateFieldGet(this, _AnnotationLayer_editableAnnotations, \"f\").get(id);\n    }\n}\n_AnnotationLayer_accessibilityManager = new WeakMap(), _AnnotationLayer_annotationCanvasMap = new WeakMap(), _AnnotationLayer_editableAnnotations = new WeakMap(), _AnnotationLayer_instances = new WeakSet(), _AnnotationLayer_appendElement = function _AnnotationLayer_appendElement(element, id) {\n    var _a;\n    const contentElement = element.firstChild || element;\n    contentElement.id = `${AnnotationPrefix}${id}`;\n    this.div.append(element);\n    (_a = __classPrivateFieldGet(this, _AnnotationLayer_accessibilityManager, \"f\")) === null || _a === void 0 ? void 0 : _a.moveElementInDOM(this.div, element, contentElement, \n    /* isRemovable = */ false);\n}, _AnnotationLayer_setAnnotationCanvasMap = function _AnnotationLayer_setAnnotationCanvasMap() {\n    if (!__classPrivateFieldGet(this, _AnnotationLayer_annotationCanvasMap, \"f\")) {\n        return;\n    }\n    const layer = this.div;\n    for (const [id, canvas] of __classPrivateFieldGet(this, _AnnotationLayer_annotationCanvasMap, \"f\")) {\n        const element = layer.querySelector(`[data-annotation-id=\"${id}\"]`);\n        if (!element) {\n            continue;\n        }\n        canvas.className = \"annotationContent k-annotation-content\";\n        const { firstChild } = element;\n        if (!firstChild) {\n            element.append(canvas);\n        }\n        else if (firstChild.nodeName === \"CANVAS\") {\n            firstChild.replaceWith(canvas);\n        }\n        else if (!firstChild.classList.contains(\"annotationContent\") ||\n            !firstChild.classList.contains(\"k-annotation-content\")) {\n            firstChild.before(canvas);\n        }\n        else {\n            firstChild.after(canvas);\n        }\n    }\n    __classPrivateFieldGet(this, _AnnotationLayer_annotationCanvasMap, \"f\").clear();\n};\nexport { AnnotationLayer, FreeTextAnnotationElement\n// InkAnnotationElement,\n// StampAnnotationElement,\n };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,4BAA4B,EAAEC,0BAA0B,EAAEC,4BAA4B,EAAEC,+BAA+B,EAAEC,gCAAgC,EAAEC,gCAAgC,EAAEC,sCAAsC,EAAEC,qCAAqC,EAAEC,sCAAsC,EAAEC,uBAAuB,EAAEC,0BAA0B,EAAEC,uBAAuB,EAAEC,uBAAuB,EAAEC,yBAAyB,EAAEC,mBAAmB,EAAEC,uBAAuB,EAAEC,yBAAyB,EAAEC,qBAAqB,EAAEC,sBAAsB,EAAEC,oBAAoB,EAAEC,wBAAwB,EAAEC,oBAAoB,EAAEC,mBAAmB,EAAEC,sBAAsB,EAAEC,kBAAkB,EAAEC,sBAAsB,EAAEC,sBAAsB,EAAEC,qBAAqB,EAAEC,wBAAwB,EAAEC,sBAAsB,EAAEC,0BAA0B,EAAEC,2BAA2B,EAAEC,8BAA8B,EAAEC,qBAAqB,EAAEC,yBAAyB,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,kBAAkB,EAAEC,0BAA0B,EAAEC,qCAAqC,EAAEC,oCAAoC,EAAEC,oCAAoC,EAAEC,8BAA8B,EAAEC,uCAAuC;AACjtC,SAASC,SAAS,EAAEC,sBAAsB,EAAEC,sBAAsB,QAAQ,OAAO;AACjF,SAASC,oBAAoB,EAAEC,aAAa,EAAEC,WAAW,EAAEC,aAAa,EAAEC,kBAAkB,EAAEC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,iCAAiC;AAC7J,SAASC,yBAAyB,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,WAAW,QAAQ,gBAAgB;AACzG,SAASC,eAAe,QAAQ,0BAA0B;AAC1D;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,CAAC;AAC3B,MAAMC,iBAAiB,GAAG,CAAC;AAC3B,MAAMC,oBAAoB,GAAG,IAAIC,OAAO,CAAC,CAAC;AAC1C,SAASC,WAAWA,CAACC,IAAI,EAAE;EACvB,OAAO;IACHC,KAAK,EAAED,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC;IACxBE,MAAM,EAAEF,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC;EAC5B,CAAC;AACL;AACA,MAAMG,wBAAwB,CAAC;EAC3B;AACJ;AACA;AACA;EACI,OAAOC,MAAMA,CAACC,UAAU,EAAE;IACtB,MAAMC,OAAO,GAAGD,UAAU,CAACE,IAAI,CAACC,cAAc;IAC9C,QAAQF,OAAO;MACX,KAAKd,cAAc,CAACiB,IAAI;QACpB,OAAO,IAAIC,qBAAqB,CAACL,UAAU,CAAC;MAChD;MACA;MACA,KAAKb,cAAc,CAACmB,MAAM;QACtB,MAAMC,SAAS,GAAGP,UAAU,CAACE,IAAI,CAACK,SAAS;QAC3C,QAAQA,SAAS;UACb,KAAK,IAAI;YACL,OAAO,IAAIC,2BAA2B,CAACR,UAAU,CAAC;UACtD;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;YACI;QACR;MACJ;MACA;MACA;MACA,KAAKb,cAAc,CAACsB,QAAQ;QACxB,OAAO,IAAIC,yBAAyB,CAACV,UAAU,CAAC;MACpD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,KAAKb,cAAc,CAACwB,SAAS;QACzB,OAAO,IAAIC,0BAA0B,CAACZ,UAAU,CAAC;MACrD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACI,OAAO,IAAIa,iBAAiB,CAACb,UAAU,CAAC;IAChD;EACJ;AACJ;AACA,MAAMa,iBAAiB,CAAC;EACpBC,WAAWA,CAACd,UAAU,EAAE;IAAEe,YAAY,GAAG,KAAK;IAAEC,YAAY,GAAG,KAAK;IAAEC,oBAAoB,GAAG;EAAM,CAAC,GAAG,CAAC,CAAC,EAAE;IACvGvF,4BAA4B,CAACwF,GAAG,CAAC,IAAI,CAAC;IACtC;IACA,IAAI,CAACH,YAAY,GAAG,IAAI;IACxB,IAAI,CAACb,IAAI,GAAG,IAAI;IAChB,IAAI,CAACiB,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAChC;IACArG,0BAA0B,CAACsG,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAC1CrG,4BAA4B,CAACqG,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC;IAC7CpG,+BAA+B,CAACoG,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAC/C,IAAI,CAAClB,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACb,IAAI,GAAGF,UAAU,CAACE,IAAI;IAC3B,IAAI,CAACiB,KAAK,GAAGnB,UAAU,CAACmB,KAAK;IAC7B,IAAI,CAACC,WAAW,GAAGpB,UAAU,CAACoB,WAAW;IACzC,IAAI,CAACC,eAAe,GAAGrB,UAAU,CAACqB,eAAe;IACjD,IAAI,CAACC,kBAAkB,GAAGtB,UAAU,CAACsB,kBAAkB;IACvD,IAAI,CAACC,WAAW,GAAGvB,UAAU,CAACuB,WAAW;IACzC,IAAI,CAACC,UAAU,GAAGxB,UAAU,CAACwB,UAAU;IACvC,IAAI,CAACC,iBAAiB,GAAGzB,UAAU,CAACyB,iBAAiB;IACrD,IAAI,CAACC,eAAe,GAAG1B,UAAU,CAAC0B,eAAe;IACjD,IAAI,CAACC,YAAY,GAAG3B,UAAU,CAAC2B,YAAY;IAC3C,IAAI,CAACC,aAAa,GAAG5B,UAAU,CAACkC,YAAY;IAC5C,IAAI,CAACL,MAAM,GAAG7B,UAAU,CAAC6B,MAAM;IAC/B,IAAId,YAAY,EAAE;MACd,IAAI,CAACe,SAAS,GAAG,IAAI,CAACK,gBAAgB,CAACnB,YAAY,CAAC;IACxD;IACA,IAAIC,oBAAoB,EAAE;MACtB,IAAI,CAACmB,qBAAqB,CAAC,CAAC;IAChC;EACJ;EACA,OAAOC,aAAaA,CAAC;IAAEC,QAAQ;IAAEC,WAAW;IAAEC;EAAS,CAAC,EAAE;IACtD,OAAO,CAAC,EAAE,CAACF,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACG,GAAG,MAAMF,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACE,GAAG,CAAC,KAAKD,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACC,GAAG,CAAC,CAAC;EACtO;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACxC,IAAI,CAACyC,UAAU;EAC/B;EACA,IAAIC,YAAYA,CAAA,EAAG;IACf,OAAO/B,iBAAiB,CAACwB,aAAa,CAAC,IAAI,CAACnC,IAAI,CAAC;EACrD;EACA2C,YAAYA,CAACC,MAAM,EAAE;IACjB,IAAIC,EAAE;IACN,IAAI,CAAC,IAAI,CAACjB,SAAS,EAAE;MACjB;IACJ;IACAtD,sBAAsB,CAAC,IAAI,EAAE7C,0BAA0B,EAAE4C,sBAAsB,CAAC,IAAI,EAAE5C,0BAA0B,EAAE,GAAG,CAAC,IAAI;MACtHgE,IAAI,EAAE,IAAI,CAACO,IAAI,CAACP,IAAI,CAACqD,KAAK,CAAC,CAAC;IAChC,CAAC,EAAE,GAAG,CAAC;IACP,MAAM;MAAErD;IAAK,CAAC,GAAGmD,MAAM;IACvB,IAAInD,IAAI,EAAE;MACNpB,sBAAsB,CAAC,IAAI,EAAE7C,4BAA4B,EAAE,GAAG,EAAEI,gCAAgC,CAAC,CAACmH,IAAI,CAAC,IAAI,EAAEtD,IAAI,CAAC;IACtH;IACA,CAACoD,EAAE,GAAGxE,sBAAsB,CAAC,IAAI,EAAE1C,+BAA+B,EAAE,GAAG,CAAC,MAAM,IAAI,IAAIkH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAChB,KAAK,CAACc,YAAY,CAACC,MAAM,CAAC;EAChJ;EACAI,WAAWA,CAAA,EAAG;IACV,IAAIH,EAAE;IACN,IAAI,CAACxE,sBAAsB,CAAC,IAAI,EAAE5C,0BAA0B,EAAE,GAAG,CAAC,EAAE;MAChE;IACJ;IACA4C,sBAAsB,CAAC,IAAI,EAAE7C,4BAA4B,EAAE,GAAG,EAAEI,gCAAgC,CAAC,CAACmH,IAAI,CAAC,IAAI,EAAE1E,sBAAsB,CAAC,IAAI,EAAE5C,0BAA0B,EAAE,GAAG,CAAC,CAACgE,IAAI,CAAC;IAChL,CAACoD,EAAE,GAAGxE,sBAAsB,CAAC,IAAI,EAAE1C,+BAA+B,EAAE,GAAG,CAAC,MAAM,IAAI,IAAIkH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAChB,KAAK,CAACmB,WAAW,CAAC,CAAC;IACrI1E,sBAAsB,CAAC,IAAI,EAAE7C,0BAA0B,EAAE,IAAI,EAAE,GAAG,CAAC;EACvE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIwG,gBAAgBA,CAACnB,YAAY,EAAE;IAC3B,MAAM;MAAEd,IAAI;MAAE2B,MAAM,EAAE;QAAEsB,IAAI;QAAEC;MAAS;IAAE,CAAC,GAAG,IAAI;IACjD,MAAMtB,SAAS,GAAGuB,QAAQ,CAACC,aAAa,CAAC,SAAS,CAAC;IACnDxB,SAAS,CAACyB,YAAY,CAAC,oBAAoB,EAAErD,IAAI,CAACsD,EAAE,CAAC;IACrD,IAAI,EAAE,IAAI,YAAYC,uBAAuB,CAAC,EAAE;MAC5C3B,SAAS,CAAC4B,QAAQ,GAAGpE,iBAAiB;IAC1C;IACA,MAAM;MAAEqE;IAAM,CAAC,GAAG7B,SAAS;IAC3B;IACA;IACA;IACA;IACA;IACA6B,KAAK,CAACC,MAAM,GAAG,CAAC,IAAI,CAAC/B,MAAM,CAAC+B,MAAM,EAAE,EAAEC,QAAQ,CAAC,CAAC;IAChD,IAAI3D,IAAI,CAAC4D,QAAQ,EAAE;MACfhC,SAAS,CAACyB,YAAY,CAAC,eAAe,EAAE,QAAQ,CAAC;IACrD;IACA,IAAIrD,IAAI,CAAC6D,eAAe,EAAE;MACtBjC,SAAS,CAACkC,KAAK,GAAG9D,IAAI,CAAC6D,eAAe;IAC1C;IACA,IAAI7D,IAAI,CAAC+D,QAAQ,EAAE;MACf;IAAA;IAEJ,IAAI,CAAC/D,IAAI,CAACP,IAAI,IAAI,IAAI,YAAYuE,sBAAsB,EAAE;MACtD,MAAM;QAAEC,QAAQ,EAAEC;MAAc,CAAC,GAAGlE,IAAI;MACxC,IAAI,CAACA,IAAI,CAACmE,YAAY,IAAID,aAAa,KAAK,CAAC,EAAE;QAC3C,IAAI,CAACE,WAAW,CAACF,aAAa,EAAEtC,SAAS,CAAC;MAC9C;MACA,OAAOA,SAAS;IACpB;IACA,MAAM;MAAElC,KAAK;MAAEC;IAAO,CAAC,GAAGH,WAAW,CAACQ,IAAI,CAACP,IAAI,CAAC;IAChD,IAAI,CAACqB,YAAY,IAAId,IAAI,CAACqE,WAAW,CAAC3E,KAAK,GAAG,CAAC,EAAE;MAC7C+D,KAAK,CAACa,WAAW,GAAG,GAAGtE,IAAI,CAACqE,WAAW,CAAC3E,KAAK,IAAI;MACjD,MAAM6E,gBAAgB,GAAGvE,IAAI,CAACqE,WAAW,CAACG,sBAAsB;MAChE,MAAMC,cAAc,GAAGzE,IAAI,CAACqE,WAAW,CAACK,oBAAoB;MAC5D,IAAIH,gBAAgB,GAAG,CAAC,IAAIE,cAAc,GAAG,CAAC,EAAE;QAC5C,MAAME,MAAM,GAAG,QAAQJ,gBAAgB,oCAAoCE,cAAc,2BAA2B;QACpHhB,KAAK,CAACmB,YAAY,GAAGD,MAAM;MAC/B,CAAC,MACI,IAAI,IAAI,YAAYE,kCAAkC,EAAE;QACzD,MAAMF,MAAM,GAAG,QAAQjF,KAAK,oCAAoCC,MAAM,2BAA2B;QACjG8D,KAAK,CAACmB,YAAY,GAAGD,MAAM;MAC/B;MACA,QAAQ3E,IAAI,CAACqE,WAAW,CAACZ,KAAK;QAC1B,KAAK1E,yBAAyB,CAAC+F,KAAK;UAChCrB,KAAK,CAACY,WAAW,GAAG,OAAO;UAC3B;QACJ,KAAKtF,yBAAyB,CAACgG,MAAM;UACjCtB,KAAK,CAACY,WAAW,GAAG,QAAQ;UAC5B;QACJ,KAAKtF,yBAAyB,CAACiG,OAAO;UAClC;UACA;QACJ,KAAKjG,yBAAyB,CAACkG,KAAK;UAChC;UACA;QACJ,KAAKlG,yBAAyB,CAACmG,SAAS;UACpCzB,KAAK,CAAC0B,iBAAiB,GAAG,OAAO;UACjC;QACJ;UACI;MACR;MACA,MAAMC,WAAW,GAAGpF,IAAI,CAACoF,WAAW,IAAI,IAAI;MAC5C,IAAIA,WAAW,EAAE;QACb9G,sBAAsB,CAAC,IAAI,EAAE5C,4BAA4B,EAAE,IAAI,EAAE,GAAG,CAAC;QACrE+H,KAAK,CAAC2B,WAAW,GAAGvG,IAAI,CAACwG,YAAY,CAACD,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEA,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEA,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACrG,CAAC,MACI;QACD;QACA3B,KAAK,CAACa,WAAW,GAAI,CAAC,EAAEX,QAAQ,CAAC,CAAC;MACtC;IACJ;IACA;IACA;IACA,MAAMlE,IAAI,GAAGZ,IAAI,CAACyG,aAAa,CAAC,CAC5BtF,IAAI,CAACP,IAAI,CAAC,CAAC,CAAC,EACZwD,IAAI,CAACsC,IAAI,CAAC,CAAC,CAAC,GAAGvF,IAAI,CAACP,IAAI,CAAC,CAAC,CAAC,GAAGwD,IAAI,CAACsC,IAAI,CAAC,CAAC,CAAC,EAC1CvF,IAAI,CAACP,IAAI,CAAC,CAAC,CAAC,EACZwD,IAAI,CAACsC,IAAI,CAAC,CAAC,CAAC,GAAGvF,IAAI,CAACP,IAAI,CAAC,CAAC,CAAC,GAAGwD,IAAI,CAACsC,IAAI,CAAC,CAAC,CAAC,CAC7C,CAAC;IACF,MAAM;MAAEC,SAAS;MAAEC,UAAU;MAAEC,KAAK;MAAEC;IAAM,CAAC,GAAGzC,QAAQ,CAAC0C,OAAO;IAChEnC,KAAK,CAACoC,IAAI,GAAG,GAAI,GAAG,IAAIpG,IAAI,CAAC,CAAC,CAAC,GAAGiG,KAAK,CAAC,GAAIF,SAAS,GAAG;IACxD/B,KAAK,CAACqC,GAAG,GAAG,GAAI,GAAG,IAAIrG,IAAI,CAAC,CAAC,CAAC,GAAGkG,KAAK,CAAC,GAAIF,UAAU,GAAG;IACxD,MAAM;MAAExB;IAAS,CAAC,GAAGjE,IAAI;IACzB,IAAIA,IAAI,CAACmE,YAAY,IAAIF,QAAQ,KAAK,CAAC,EAAE;MACrCR,KAAK,CAAC/D,KAAK,GAAG,GAAI,GAAG,GAAGA,KAAK,GAAI8F,SAAS,GAAG;MAC7C/B,KAAK,CAAC9D,MAAM,GAAG,GAAI,GAAG,GAAGA,MAAM,GAAI8F,UAAU,GAAG;IACpD,CAAC,MACI;MACD,IAAI,CAACrB,WAAW,CAACH,QAAQ,EAAErC,SAAS,CAAC;IACzC;IACA,OAAOA,SAAS;EACpB;EACAwC,WAAWA,CAAC2B,KAAK,EAAEnE,SAAS,GAAG,IAAI,CAACA,SAAS,EAAE;IAC3C,IAAI,CAAC,IAAI,CAAC5B,IAAI,CAACP,IAAI,EAAE;MACjB;IACJ;IACA,MAAM;MAAE+F,SAAS;MAAEC;IAAW,CAAC,GAAG,IAAI,CAAC9D,MAAM,CAACuB,QAAQ,CAAC0C,OAAO;IAC9D,MAAM;MAAElG,KAAK;MAAEC;IAAO,CAAC,GAAGH,WAAW,CAAC,IAAI,CAACQ,IAAI,CAACP,IAAI,CAAC;IACrD,IAAIuG,YAAY,EAAEC,aAAa;IAC/B,IAAIF,KAAK,GAAG,GAAG,KAAK,CAAC,EAAE;MACnBC,YAAY,GAAI,GAAG,GAAGtG,KAAK,GAAI8F,SAAS;MACxCS,aAAa,GAAI,GAAG,GAAGtG,MAAM,GAAI8F,UAAU;IAC/C,CAAC,MACI;MACDO,YAAY,GAAI,GAAG,GAAGrG,MAAM,GAAI6F,SAAS;MACzCS,aAAa,GAAI,GAAG,GAAGvG,KAAK,GAAI+F,UAAU;IAC9C;IACA7D,SAAS,CAAC6B,KAAK,CAAC/D,KAAK,GAAG,GAAGsG,YAAY,GAAG;IAC1CpE,SAAS,CAAC6B,KAAK,CAAC9D,MAAM,GAAG,GAAGsG,aAAa,GAAG;IAC5CrE,SAAS,CAACyB,YAAY,CAAC,oBAAoB,EAAE,CAAC,GAAG,GAAG0C,KAAK,IAAI,GAAG,CAAC;EACrE;EACA,IAAIG,cAAcA,CAAA,EAAG;IACjB,MAAMC,QAAQ,GAAGA,CAACC,MAAM,EAAEC,SAAS,EAAEC,KAAK,KAAK;MAC3C,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACJ,MAAM,CAAC;MAClC,MAAMK,SAAS,GAAGF,KAAK,CAAC,CAAC,CAAC;MAC1B,MAAMG,UAAU,GAAGH,KAAK,CAACzD,KAAK,CAAC,CAAC,CAAC;MACjCwD,KAAK,CAACK,MAAM,CAAClD,KAAK,CAAC4C,SAAS,CAAC,GACzBlH,eAAe,CAAC,GAAGsH,SAAS,OAAO,CAAC,CAACC,UAAU,CAAC;MACpD,IAAI,CAACnF,iBAAiB,CAACqF,QAAQ,CAAC,IAAI,CAAC5G,IAAI,CAACsD,EAAE,EAAE;QAC1C,CAAC+C,SAAS,GAAGlH,eAAe,CAAC,GAAGsH,SAAS,MAAM,CAAC,CAACC,UAAU;MAC/D,CAAC,CAAC;IACN,CAAC;IACD,OAAO9H,MAAM,CAAC,IAAI,EAAE,gBAAgB,EAAE;MAClCiI,OAAO,EAAEP,KAAK,IAAI;QACd,MAAM;UAAEO;QAAQ,CAAC,GAAGP,KAAK,CAACE,MAAM;QAChC;QACA;QACA,MAAMM,MAAM,GAAGD,OAAO,GAAG,CAAC,KAAK,CAAC;QAChC,IAAI,CAACjF,SAAS,CAAC6B,KAAK,CAACsD,UAAU,GAAGD,MAAM,GAAG,QAAQ,GAAG,SAAS;QAC/D,IAAI,CAACvF,iBAAiB,CAACqF,QAAQ,CAAC,IAAI,CAAC5G,IAAI,CAACsD,EAAE,EAAE;UAC1C0D,MAAM,EAAEF,MAAM;UACdG,OAAO,EAAEJ,OAAO,KAAK,CAAC,IAAIA,OAAO,KAAK;QAC1C,CAAC,CAAC;MACN,CAAC;MACDK,KAAK,EAAEZ,KAAK,IAAI;QACZ,IAAI,CAAC/E,iBAAiB,CAACqF,QAAQ,CAAC,IAAI,CAAC5G,IAAI,CAACsD,EAAE,EAAE;UAC1C2D,OAAO,EAAE,CAACX,KAAK,CAACE,MAAM,CAACU;QAC3B,CAAC,CAAC;MACN,CAAC;MACDJ,MAAM,EAAER,KAAK,IAAI;QACb,MAAM;UAAEQ;QAAO,CAAC,GAAGR,KAAK,CAACE,MAAM;QAC/B,IAAI,CAAC5E,SAAS,CAAC6B,KAAK,CAACsD,UAAU,GAAGD,MAAM,GAAG,QAAQ,GAAG,SAAS;QAC/D,IAAI,CAACvF,iBAAiB,CAACqF,QAAQ,CAAC,IAAI,CAAC5G,IAAI,CAACsD,EAAE,EAAE;UAC1C2D,OAAO,EAAEH,MAAM;UACfE,MAAM,EAAEF;QACZ,CAAC,CAAC;MACN,CAAC;MACDK,KAAK,EAAEb,KAAK,IAAI;QACZc,UAAU,CAAC,MAAMd,KAAK,CAACK,MAAM,CAACQ,KAAK,CAAC;UAAEE,aAAa,EAAE;QAAM,CAAC,CAAC,EAAE,CAAC,CAAC;MACrE,CAAC;MACDC,QAAQ,EAAEhB,KAAK,IAAI;QACf;QACAA,KAAK,CAACK,MAAM,CAAC7C,KAAK,GAAGwC,KAAK,CAACE,MAAM,CAACc,QAAQ;MAC9C,CAAC;MACDC,QAAQ,EAAEjB,KAAK,IAAI;QACfA,KAAK,CAACK,MAAM,CAACa,QAAQ,GAAGlB,KAAK,CAACE,MAAM,CAACe,QAAQ;MACjD,CAAC;MACDE,QAAQ,EAAEnB,KAAK,IAAI;QACf,IAAI,CAACoB,YAAY,CAACpB,KAAK,CAACK,MAAM,EAAEL,KAAK,CAACE,MAAM,CAACiB,QAAQ,CAAC;MAC1D,CAAC;MACDE,OAAO,EAAErB,KAAK,IAAI;QACdH,QAAQ,CAAC,SAAS,EAAE,iBAAiB,EAAEG,KAAK,CAAC;MACjD,CAAC;MACDsB,SAAS,EAAEtB,KAAK,IAAI;QAChBH,QAAQ,CAAC,WAAW,EAAE,iBAAiB,EAAEG,KAAK,CAAC;MACnD,CAAC;MACDuB,OAAO,EAAEvB,KAAK,IAAI;QACdH,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAEG,KAAK,CAAC;MACvC,CAAC;MACDwB,SAAS,EAAExB,KAAK,IAAI;QAChBH,QAAQ,CAAC,WAAW,EAAE,OAAO,EAAEG,KAAK,CAAC;MACzC,CAAC;MACDlB,WAAW,EAAEkB,KAAK,IAAI;QAClBH,QAAQ,CAAC,aAAa,EAAE,aAAa,EAAEG,KAAK,CAAC;MACjD,CAAC;MACDyB,WAAW,EAAEzB,KAAK,IAAI;QAClBH,QAAQ,CAAC,aAAa,EAAE,aAAa,EAAEG,KAAK,CAAC;MACjD,CAAC;MACDrC,QAAQ,EAAEqC,KAAK,IAAI;QACf,MAAMP,KAAK,GAAGO,KAAK,CAACE,MAAM,CAACvC,QAAQ;QACnC,IAAI,CAACG,WAAW,CAAC2B,KAAK,CAAC;QACvB,IAAI,CAACxE,iBAAiB,CAACqF,QAAQ,CAAC,IAAI,CAAC5G,IAAI,CAACsD,EAAE,EAAE;UAC1CW,QAAQ,EAAE8B;QACd,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;EACN;EACAiC,yBAAyBA,CAACC,OAAO,EAAEC,OAAO,EAAE;IACxC,MAAMC,aAAa,GAAG,IAAI,CAACjC,cAAc;IACzC,KAAK,MAAMkC,IAAI,IAAIC,MAAM,CAACC,IAAI,CAACJ,OAAO,CAAC1B,MAAM,CAAC,EAAE;MAC5C,MAAM+B,MAAM,GAAGN,OAAO,CAACG,IAAI,CAAC,IAAID,aAAa,CAACC,IAAI,CAAC;MACnDG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACL,OAAO,CAAC;IACnE;EACJ;EACAM,2BAA2BA,CAACC,OAAO,EAAE;IACjC,IAAI,CAAC,IAAI,CAACjH,eAAe,EAAE;MACvB;IACJ;IACA;IACA,MAAMkH,UAAU,GAAG,IAAI,CAACnH,iBAAiB,CAACoH,WAAW,CAAC,IAAI,CAAC3I,IAAI,CAACsD,EAAE,CAAC;IACnE,IAAI,CAACoF,UAAU,EAAE;MACb;IACJ;IACA,MAAMP,aAAa,GAAG,IAAI,CAACjC,cAAc;IACzC,KAAK,MAAM,CAAC0C,UAAU,EAAEpC,MAAM,CAAC,IAAI6B,MAAM,CAACQ,OAAO,CAACH,UAAU,CAAC,EAAE;MAC3D,MAAMH,MAAM,GAAGJ,aAAa,CAACS,UAAU,CAAC;MACxC,IAAIL,MAAM,EAAE;QACR,MAAMO,UAAU,GAAG;UACftC,MAAM,EAAE;YACJ,CAACoC,UAAU,GAAGpC;UAClB,CAAC;UACDG,MAAM,EAAE8B;QACZ,CAAC;QACDF,MAAM,CAACO,UAAU,CAAC;QAClB;QACA,OAAOJ,UAAU,CAACE,UAAU,CAAC;MACjC;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI1G,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAACN,SAAS,EAAE;MACjB;IACJ;IACA,MAAM;MAAEmH;IAAW,CAAC,GAAG,IAAI,CAAC/I,IAAI;IAChC,IAAI,CAAC+I,UAAU,EAAE;MACb;IACJ;IACA,MAAM,CAACC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,CAAC,GAAG,IAAI,CAACnJ,IAAI,CAACP,IAAI,CAAC2J,GAAG,CAACC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAACF,CAAC,CAAC,CAAC;IACpF,IAAIN,UAAU,CAACS,MAAM,KAAK,CAAC,EAAE;MACzB,MAAM,CAACC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,CAAC,GAAGb,UAAU,CAACc,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;MACtD,IAAIX,OAAO,KAAKO,GAAG,IACfN,OAAO,KAAKO,GAAG,IACfV,OAAO,KAAKW,GAAG,IACfV,OAAO,KAAKW,GAAG,EAAE;QACjB;QACA;QACA;MACJ;IACJ;IACA,MAAM;MAAEnG;IAAM,CAAC,GAAG,IAAI,CAAC7B,SAAS;IAChC,IAAIkI,SAAS;IACb,IAAIzL,sBAAsB,CAAC,IAAI,EAAE3C,4BAA4B,EAAE,GAAG,CAAC,EAAE;MACjE,MAAM;QAAE0J,WAAW;QAAEd;MAAY,CAAC,GAAGb,KAAK;MAC1CA,KAAK,CAACa,WAAW,GAAG,CAAC;MACrBwF,SAAS,GAAG,CACR,+BAA+B,EAC/B,yCAAyC,EACzC,gDAAgD,EAChD,iCAAiC1E,WAAW,mBAAmBd,WAAW,IAAI,CACjF;MACD;IACJ;IACA;IACA;IACA;IACA;IACA,MAAM5E,KAAK,GAAGwJ,OAAO,GAAGF,OAAO;IAC/B,MAAMrJ,MAAM,GAAGwJ,OAAO,GAAGF,OAAO;IAChC,MAAM;MAAE3H;IAAW,CAAC,GAAG,IAAI;IAC3B,MAAMyI,GAAG,GAAGzI,UAAU,CAAC8B,aAAa,CAAC,KAAK,CAAC;IAC3C;IACA2G,GAAG,CAAC1G,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC;IAC5B0G,GAAG,CAAC1G,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC7B,MAAM2G,IAAI,GAAG1I,UAAU,CAAC8B,aAAa,CAAC,MAAM,CAAC;IAC7C2G,GAAG,CAACE,MAAM,CAACD,IAAI,CAAC;IAChB,MAAME,QAAQ,GAAG5I,UAAU,CAAC8B,aAAa,CAAC,UAAU,CAAC;IACrD,MAAME,EAAE,GAAG,YAAY,IAAI,CAACtD,IAAI,CAACsD,EAAE,EAAE;IACrC4G,QAAQ,CAAC7G,YAAY,CAAC,IAAI,EAAEC,EAAE,CAAC;IAC/B4G,QAAQ,CAAC7G,YAAY,CAAC,eAAe,EAAE,mBAAmB,CAAC;IAC3D2G,IAAI,CAACC,MAAM,CAACC,QAAQ,CAAC;IACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,EAAE,GAAGrB,UAAU,CAACS,MAAM,EAAEW,CAAC,GAAGC,EAAE,EAAED,CAAC,IAAI,CAAC,EAAE;MACpD,MAAMV,GAAG,GAAGV,UAAU,CAACoB,CAAC,CAAC;MACzB,MAAMT,GAAG,GAAGX,UAAU,CAACoB,CAAC,GAAG,CAAC,CAAC;MAC7B,MAAMR,GAAG,GAAGZ,UAAU,CAACoB,CAAC,GAAG,CAAC,CAAC;MAC7B,MAAMP,GAAG,GAAGb,UAAU,CAACoB,CAAC,GAAG,CAAC,CAAC;MAC7B,MAAM1K,IAAI,GAAG6B,UAAU,CAAC8B,aAAa,CAAC,MAAM,CAAC;MAC7C,MAAMiG,CAAC,GAAG,CAACM,GAAG,GAAGX,OAAO,IAAItJ,KAAK;MACjC,MAAM2K,CAAC,GAAG,CAAClB,OAAO,GAAGO,GAAG,IAAI/J,MAAM;MAClC,MAAM2K,SAAS,GAAG,CAACb,GAAG,GAAGE,GAAG,IAAIjK,KAAK;MACrC,MAAM6K,UAAU,GAAG,CAACb,GAAG,GAAGE,GAAG,IAAIjK,MAAM;MACvCF,IAAI,CAAC4D,YAAY,CAAC,GAAG,EAAEgG,CAAC,CAAC;MACzB5J,IAAI,CAAC4D,YAAY,CAAC,GAAG,EAAEgH,CAAC,CAAC;MACzB5K,IAAI,CAAC4D,YAAY,CAAC,OAAO,EAAEiH,SAAS,CAAC;MACrC7K,IAAI,CAAC4D,YAAY,CAAC,QAAQ,EAAEkH,UAAU,CAAC;MACvCL,QAAQ,CAACD,MAAM,CAACxK,IAAI,CAAC;MACrBqK,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACU,IAAI,CAAC,+CAA+CnB,CAAC,QAAQgB,CAAC,YAAYC,SAAS,aAAaC,UAAU,KAAK,CAAC;IACpL;IACA,IAAIlM,sBAAsB,CAAC,IAAI,EAAE3C,4BAA4B,EAAE,GAAG,CAAC,EAAE;MACjEoO,SAAS,CAACU,IAAI,CAAC,cAAc,CAAC;MAC9B/G,KAAK,CAACgH,eAAe,GAAGX,SAAS,CAACY,IAAI,CAAC,EAAE,CAAC;IAC9C;IACA,IAAI,CAAC9I,SAAS,CAACqI,MAAM,CAACF,GAAG,CAAC;IAC1B,IAAI,CAACnI,SAAS,CAAC6B,KAAK,CAACyG,QAAQ,GAAG,QAAQ5G,EAAE,GAAG;EACjD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIqH,YAAYA,CAAA,EAAG;IACX;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA;EAEJ;AACJ;AACA;AACA;AACA;AACA;EACIC,MAAMA,CAAA,EAAG;IACL;EAAA;EAEJ;AACJ;AACA;AACA;EACIC,kBAAkBA,CAACzC,IAAI,EAAE0C,MAAM,GAAG,IAAI,EAAE;IACpC,MAAMC,MAAM,GAAG,EAAE;IACjB,IAAI,IAAI,CAACrJ,aAAa,EAAE;MACpB,MAAMsJ,QAAQ,GAAG,IAAI,CAACtJ,aAAa,CAAC0G,IAAI,CAAC;MACzC,IAAI4C,QAAQ,EAAE;QACV,KAAK,MAAM;UAAE/H,IAAI;UAAEK,EAAE;UAAE2H;QAAa,CAAC,IAAID,QAAQ,EAAE;UAC/C,IAAI/H,IAAI,KAAK,CAAC,CAAC,EAAE;YACb;UACJ;UACA,IAAIK,EAAE,KAAKwH,MAAM,EAAE;YACf;UACJ;UACA,MAAMI,WAAW,GAAG,OAAOD,YAAY,KAAK,QAAQ,GAAGA,YAAY,GAAG,IAAI;UAC1E,MAAME,UAAU,GAAGhI,QAAQ,CAACiI,aAAa,CAAC,qBAAqB9H,EAAE,IAAI,CAAC;UACtE,IAAI6H,UAAU,IAAI,CAAC7L,oBAAoB,CAAC+L,GAAG,CAACF,UAAU,CAAC,EAAE;YACrD;YACA;UACJ;UACAJ,MAAM,CAACP,IAAI,CAAC;YAAElH,EAAE;YAAE4H,WAAW;YAAEC;UAAW,CAAC,CAAC;QAChD;MACJ;MACA,OAAOJ,MAAM;IACjB;IACA;IACA;IACA,KAAK,MAAMI,UAAU,IAAIhI,QAAQ,CAACmI,iBAAiB,CAAClD,IAAI,CAAC,EAAE;MACvD,MAAM;QAAE8C;MAAY,CAAC,GAAGC,UAAU;MAClC,MAAM7H,EAAE,GAAG6H,UAAU,CAACI,YAAY,CAAC,iBAAiB,CAAC;MACrD,IAAIjI,EAAE,KAAKwH,MAAM,EAAE;QACf;MACJ;MACA,IAAI,CAACxL,oBAAoB,CAAC+L,GAAG,CAACF,UAAU,CAAC,EAAE;QACvC;MACJ;MACAJ,MAAM,CAACP,IAAI,CAAC;QAAElH,EAAE;QAAE4H,WAAW;QAAEC;MAAW,CAAC,CAAC;IAChD;IACA,OAAOJ,MAAM;EACjB;EACAS,IAAIA,CAAA,EAAG;IACH,IAAI3I,EAAE;IACN,IAAI,IAAI,CAACjB,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACkF,MAAM,GAAG,KAAK;IACjC;IACA,CAACjE,EAAE,GAAG,IAAI,CAAChB,KAAK,MAAM,IAAI,IAAIgB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4I,SAAS,CAAC,CAAC;EACzE;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI7I,EAAE;IACN,IAAI,IAAI,CAACjB,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACkF,MAAM,GAAG,IAAI;IAChC;IACA,CAACjE,EAAE,GAAG,IAAI,CAAChB,KAAK,MAAM,IAAI,IAAIgB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC8I,SAAS,CAAC,CAAC;EACzE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,yBAAyBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAAChK,SAAS;EACzB;EACAiK,gBAAgBA,CAAA,EAAG;IACf,MAAMC,QAAQ,GAAG,IAAI,CAACF,yBAAyB,CAAC,CAAC;IACjD,IAAIG,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,EAAE;MACzB;MACA;MACA;IAAA,CACH,MACI;MACD;IAAA;EAER;EACAG,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACzJ,WAAW,EAAE;MACnB;IACJ;IACA,MAAM;MAAEV,oBAAoB,EAAEoK,IAAI;MAAElM,IAAI,EAAE;QAAEsD,EAAE,EAAE6I;MAAO;IAAE,CAAC,GAAG,IAAI;IACjE,IAAI,CAACvK,SAAS,CAACwK,gBAAgB,CAAC,UAAU,EAAE,MAAM;MAC9C,IAAIvJ,EAAE,EAAEwJ,EAAE;MACV,CAACA,EAAE,GAAG,CAACxJ,EAAE,GAAG,IAAI,CAAC3B,WAAW,MAAM,IAAI,IAAI2B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyJ,QAAQ,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,QAAQ,CAAC,4BAA4B,EAAE;QAC3JC,MAAM,EAAE,IAAI;QACZN,IAAI;QACJC;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAzE,YAAYA,CAACe,OAAO,EAAEgE,UAAU,EAAE;IAC9B,IAAIA,UAAU,EAAE;MACZhE,OAAO,CAACpF,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC;IAC1C,CAAC,MACI;MACDoF,OAAO,CAACiE,eAAe,CAAC,UAAU,CAAC;IACvC;IACAjE,OAAO,CAACpF,YAAY,CAAC,eAAe,EAAEoJ,UAAU,CAAC;EACrD;AACJ;AACAhR,0BAA0B,GAAG,IAAIkR,OAAO,CAAC,CAAC,EAAEjR,4BAA4B,GAAG,IAAIiR,OAAO,CAAC,CAAC,EAAEhR,+BAA+B,GAAG,IAAIgR,OAAO,CAAC,CAAC,EAAEnR,4BAA4B,GAAG,IAAI+D,OAAO,CAAC,CAAC,EAAE3D,gCAAgC,GAAG,SAASA,gCAAgCA,CAAC6D,IAAI,EAAE;EACxQ,MAAM;IAAEmC,SAAS,EAAE;MAAE6B;IAAM,CAAC;IAAEzD,IAAI,EAAE;MAAEP,IAAI,EAAEmN,WAAW;MAAE3I;IAAS,CAAC;IAAEtC,MAAM,EAAE;MAAEuB,QAAQ,EAAE;QAAE0C,OAAO,EAAE;UAAEJ,SAAS;UAAEC,UAAU;UAAEC,KAAK;UAAEC;QAAM;MAAE;IAAE;EAAE,CAAC,GAAG,IAAI;EACxJiH,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,GAAGpN,IAAI,CAAC;EAC3F,MAAM;IAAEC,KAAK;IAAEC;EAAO,CAAC,GAAGH,WAAW,CAACC,IAAI,CAAC;EAC3CgE,KAAK,CAACoC,IAAI,GAAG,GAAI,GAAG,IAAIpG,IAAI,CAAC,CAAC,CAAC,GAAGiG,KAAK,CAAC,GAAIF,SAAS,GAAG;EACxD/B,KAAK,CAACqC,GAAG,GAAG,GAAI,GAAG,IAAIL,UAAU,GAAGhG,IAAI,CAAC,CAAC,CAAC,GAAGkG,KAAK,CAAC,GAAIF,UAAU,GAAG;EACrE,IAAIxB,QAAQ,KAAK,CAAC,EAAE;IAChBR,KAAK,CAAC/D,KAAK,GAAG,GAAI,GAAG,GAAGA,KAAK,GAAI8F,SAAS,GAAG;IAC7C/B,KAAK,CAAC9D,MAAM,GAAG,GAAI,GAAG,GAAGA,MAAM,GAAI8F,UAAU,GAAG;EACpD,CAAC,MACI;IACD,IAAI,CAACrB,WAAW,CAACH,QAAQ,CAAC;EAC9B;AACJ,CAAC;AACD,MAAM9D,qBAAqB,SAASQ,iBAAiB,CAAC;EAClDC,WAAWA,CAACd,UAAU,EAAEgN,OAAO,GAAG,IAAI,EAAE;IACpC,KAAK,CAAChN,UAAU,EAAE;MACde,YAAY,EAAE,IAAI;MAClBC,YAAY,EAAE,CAAC,EAAEgM,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAChM,YAAY,CAAC;MACxFC,oBAAoB,EAAE;IAC1B,CAAC,CAAC;IACFlF,gCAAgC,CAACmF,GAAG,CAAC,IAAI,CAAC;IAC1C,IAAI,CAAC+L,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACA,aAAa,GAAGjN,UAAU,CAACE,IAAI,CAAC+M,aAAa;EACtD;EACAnC,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE5K,IAAI;MAAEkB;IAAY,CAAC,GAAG,IAAI;IAClC,MAAM8L,IAAI,GAAG7J,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxC4J,IAAI,CAAC3J,YAAY,CAAC,iBAAiB,EAAErD,IAAI,CAACsD,EAAE,CAAC;IAC7C,IAAI2J,OAAO,GAAG,KAAK;IACnB,IAAIjN,IAAI,CAACkN,GAAG,EAAE;MACVhM,WAAW,CAACiM,iBAAiB,CAACH,IAAI,EAAEhN,IAAI,CAACkN,GAAG,EAAElN,IAAI,CAACoN,SAAS,CAAC;MAC7DH,OAAO,GAAG,IAAI;IAClB,CAAC,MACI,IAAIjN,IAAI,CAACuI,MAAM,EAAE;MAClB,IAAI,CAAC8E,gBAAgB,CAACL,IAAI,EAAEhN,IAAI,CAACuI,MAAM,CAAC;MACxC0E,OAAO,GAAG,IAAI;IAClB,CAAC,MACI,IAAIjN,IAAI,CAACsN,UAAU,EAAE;MACtBjP,sBAAsB,CAAC,IAAI,EAAExC,gCAAgC,EAAE,GAAG,EAAEE,qCAAqC,CAAC,CAACgH,IAAI,CAAC,IAAI,EAAEiK,IAAI,EAAEhN,IAAI,CAACsN,UAAU,EAAEtN,IAAI,CAACuN,cAAc,CAAC;MACjKN,OAAO,GAAG,IAAI;IAClB,CAAC,MACI,IAAIjN,IAAI,CAACwN,WAAW,EAAE;MACvBnP,sBAAsB,CAAC,IAAI,EAAExC,gCAAgC,EAAE,GAAG,EAAEG,sCAAsC,CAAC,CAAC+G,IAAI,CAAC,IAAI,EAAEiK,IAAI,EAAEhN,IAAI,CAACwN,WAAW,CAAC;MAC9IP,OAAO,GAAG,IAAI;IAClB,CAAC,MACI,IAAIjN,IAAI,CAACyN,IAAI,EAAE;MAChB,IAAI,CAACC,SAAS,CAACV,IAAI,EAAEhN,IAAI,CAACyN,IAAI,CAAC;MAC/BR,OAAO,GAAG,IAAI;IAClB,CAAC,MACI;MACD,IAAIjN,IAAI,CAACiI,OAAO,KACXjI,IAAI,CAACiI,OAAO,CAAC0F,MAAM,IAChB3N,IAAI,CAACiI,OAAO,CAAC,UAAU,CAAC,IACxBjI,IAAI,CAACiI,OAAO,CAAC,YAAY,CAAC,CAAC,IAC/B,IAAI,CAACzG,eAAe,IACpB,IAAI,CAACC,YAAY,EAAE;QACnB,IAAI,CAACmM,aAAa,CAACZ,IAAI,EAAEhN,IAAI,CAAC;QAC9BiN,OAAO,GAAG,IAAI;MAClB;MACA,IAAIjN,IAAI,CAAC6N,SAAS,EAAE;QAChB,IAAI,CAACC,oBAAoB,CAACd,IAAI,EAAEhN,IAAI,CAAC6N,SAAS,CAAC;QAC/CZ,OAAO,GAAG,IAAI;MAClB,CAAC,MACI,IAAI,IAAI,CAACF,aAAa,IAAI,CAACE,OAAO,EAAE;QACrC,IAAI,CAACS,SAAS,CAACV,IAAI,EAAE,EAAE,CAAC;QACxBC,OAAO,GAAG,IAAI;MAClB;IACJ;IACA;IACA;IACA,IAAI,CAACrL,SAAS,CAACmM,SAAS,CAAC/M,GAAG,CAAC,mBAAmB,CAAC;IACjD,IAAIiM,OAAO,EAAE;MACT,IAAI,CAACrL,SAAS,CAACqI,MAAM,CAAC+C,IAAI,CAAC;IAC/B;IACA,OAAO,IAAI,CAACpL,SAAS;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI8L,SAASA,CAACV,IAAI,EAAEgB,WAAW,EAAE;IACzBhB,IAAI,CAACiB,IAAI,GAAG,IAAI,CAAC/M,WAAW,CAACgN,kBAAkB,CAACF,WAAW,CAAC;IAC5DhB,IAAI,CAACmB,OAAO,GAAG,MAAM;MACjB,IAAIH,WAAW,EAAE;QACb,IAAI,CAAC9M,WAAW,CAACkN,eAAe,CAACJ,WAAW,CAAC;MACjD;MACA,OAAO,KAAK;IAChB,CAAC;IACD,IAAIA,WAAW,IAAIA,WAAW,KAAK,qBAAsB,EAAE,EAAE;MACzD3P,sBAAsB,CAAC,IAAI,EAAExC,gCAAgC,EAAE,GAAG,EAAEC,sCAAsC,CAAC,CAACiH,IAAI,CAAC,IAAI,CAAC;IAC1H;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIsK,gBAAgBA,CAACL,IAAI,EAAEzE,MAAM,EAAE;IAC3ByE,IAAI,CAACiB,IAAI,GAAG,IAAI,CAAC/M,WAAW,CAACmN,YAAY,CAAC,EAAE,CAAC;IAC7CrB,IAAI,CAACmB,OAAO,GAAG,MAAM;MACjB,IAAI,CAACjN,WAAW,CAACoN,kBAAkB,CAAC/F,MAAM,CAAC;MAC3C,OAAO,KAAK;IAChB,CAAC;IACDlK,sBAAsB,CAAC,IAAI,EAAExC,gCAAgC,EAAE,GAAG,EAAEC,sCAAsC,CAAC,CAACiH,IAAI,CAAC,IAAI,CAAC;EAC1H;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI6K,aAAaA,CAACZ,IAAI,EAAEhN,IAAI,EAAE;IACtBgN,IAAI,CAACiB,IAAI,GAAG,IAAI,CAAC/M,WAAW,CAACmN,YAAY,CAAC,EAAE,CAAC;IAC7C,MAAMjF,GAAG,GAAG,IAAImF,GAAG,CAAC,CAChB,CAAC,QAAQ,EAAE,SAAS,CAAC,EACrB,CAAC,UAAU,EAAE,WAAW,CAAC,EACzB,CAAC,YAAY,EAAE,aAAa,CAAC,CAChC,CAAC;IACF,KAAK,MAAMnG,IAAI,IAAIC,MAAM,CAACC,IAAI,CAACtI,IAAI,CAACiI,OAAO,CAAC,EAAE;MAC1C,MAAM7B,MAAM,GAAGgD,GAAG,CAACoF,GAAG,CAACpG,IAAI,CAAC;MAC5B,IAAI,CAAChC,MAAM,EAAE;QACT;MACJ;MACA4G,IAAI,CAAC5G,MAAM,CAAC,GAAG,MAAM;QACjB,IAAIvD,EAAE;QACN,CAACA,EAAE,GAAG,IAAI,CAAC3B,WAAW,CAACoL,QAAQ,MAAM,IAAI,IAAIzJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0J,QAAQ,CAAC,wBAAwB,EAAE;UACxGC,MAAM,EAAE,IAAI;UACZhG,MAAM,EAAE;YACJlD,EAAE,EAAEtD,IAAI,CAACsD,EAAE;YACX8E;UACJ;QACJ,CAAC,CAAC;QACF,OAAO,KAAK;MAChB,CAAC;IACL;IACA,IAAI,CAAC4E,IAAI,CAACmB,OAAO,EAAE;MACfnB,IAAI,CAACmB,OAAO,GAAG,MAAM,KAAK;IAC9B;IACA9P,sBAAsB,CAAC,IAAI,EAAExC,gCAAgC,EAAE,GAAG,EAAEC,sCAAsC,CAAC,CAACiH,IAAI,CAAC,IAAI,CAAC;EAC1H;EACA+K,oBAAoBA,CAACd,IAAI,EAAEa,SAAS,EAAE;IAClC,MAAMY,gBAAgB,GAAGzB,IAAI,CAACmB,OAAO;IACrC,IAAI,CAACM,gBAAgB,EAAE;MACnBzB,IAAI,CAACiB,IAAI,GAAG,IAAI,CAAC/M,WAAW,CAACmN,YAAY,CAAC,EAAE,CAAC;IACjD;IACAhQ,sBAAsB,CAAC,IAAI,EAAExC,gCAAgC,EAAE,GAAG,EAAEC,sCAAsC,CAAC,CAACiH,IAAI,CAAC,IAAI,CAAC;IACtH,IAAI,CAAC,IAAI,CAACrB,aAAa,EAAE;MACrB;MACA;MACA;MACA;MACA,IAAI,CAAC+M,gBAAgB,EAAE;QACnBzB,IAAI,CAACmB,OAAO,GAAG,MAAM,KAAK;MAC9B;MACA;IACJ;IACAnB,IAAI,CAACmB,OAAO,GAAG,MAAM;MACjB,IAAItL,EAAE;MACN4L,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAAC,CAAC;MACtF,MAAM;QAAE1D,MAAM,EAAE2D,eAAe;QAAEC,IAAI,EAAEC,aAAa;QAAEC;MAAQ,CAAC,GAAGhB,SAAS;MAC3E,MAAMiB,SAAS,GAAG,EAAE;MACpB,IAAIJ,eAAe,CAAClF,MAAM,KAAK,CAAC,IAAIoF,aAAa,CAACpF,MAAM,KAAK,CAAC,EAAE;QAC5D,MAAMuF,QAAQ,GAAG,IAAIC,GAAG,CAACJ,aAAa,CAAC;QACvC,KAAK,MAAMK,SAAS,IAAIP,eAAe,EAAE;UACrC,MAAM3D,MAAM,GAAG,IAAI,CAACrJ,aAAa,CAACuN,SAAS,CAAC,IAAI,EAAE;UAClD,KAAK,MAAM;YAAE3L;UAAG,CAAC,IAAIyH,MAAM,EAAE;YACzBgE,QAAQ,CAAC/N,GAAG,CAACsC,EAAE,CAAC;UACpB;QACJ;QACA,KAAK,MAAMyH,MAAM,IAAI1C,MAAM,CAAC6G,MAAM,CAAC,IAAI,CAACxN,aAAa,CAAC,EAAE;UACpD,KAAK,MAAMyN,KAAK,IAAIpE,MAAM,EAAE;YACxB,IAAIgE,QAAQ,CAAC1D,GAAG,CAAC8D,KAAK,CAAC7L,EAAE,CAAC,KAAKuL,OAAO,EAAE;cACpCC,SAAS,CAACtE,IAAI,CAAC2E,KAAK,CAAC;YACzB;UACJ;QACJ;MACJ,CAAC,MACI;QACD,KAAK,MAAMpE,MAAM,IAAI1C,MAAM,CAAC6G,MAAM,CAAC,IAAI,CAACxN,aAAa,CAAC,EAAE;UACpDoN,SAAS,CAACtE,IAAI,CAAC,GAAGO,MAAM,CAAC;QAC7B;MACJ;MACA,MAAMqE,OAAO,GAAG,IAAI,CAAC7N,iBAAiB;MACtC,MAAM8N,MAAM,GAAG,EAAE;MACjB,KAAK,MAAMF,KAAK,IAAIL,SAAS,EAAE;QAC3B,MAAM;UAAExL;QAAG,CAAC,GAAG6L,KAAK;QACpBE,MAAM,CAAC7E,IAAI,CAAClH,EAAE,CAAC;QACf,QAAQ6L,KAAK,CAACG,IAAI;UACd,KAAK,MAAM;YAAE;cACT,MAAMC,KAAK,GAAGJ,KAAK,CAACK,YAAY,IAAI,EAAE;cACtCJ,OAAO,CAACxI,QAAQ,CAACtD,EAAE,EAAE;gBAAEiM;cAAM,CAAC,CAAC;cAC/B;YACJ;UACA,KAAK,UAAU;UACf,KAAK,aAAa;YAAE;cAChB,MAAMA,KAAK,GAAGJ,KAAK,CAACK,YAAY,KAAKL,KAAK,CAAClE,YAAY;cACvDmE,OAAO,CAACxI,QAAQ,CAACtD,EAAE,EAAE;gBAAEiM;cAAM,CAAC,CAAC;cAC/B;YACJ;UACA,KAAK,UAAU;UACf,KAAK,SAAS;YAAE;cACZ,MAAMA,KAAK,GAAGJ,KAAK,CAACK,YAAY,IAAI,EAAE;cACtCJ,OAAO,CAACxI,QAAQ,CAACtD,EAAE,EAAE;gBAAEiM;cAAM,CAAC,CAAC;cAC/B;YACJ;UACA;YACI;QACR;QACA,MAAMpE,UAAU,GAAGhI,QAAQ,CAACiI,aAAa,CAAC,qBAAqB9H,EAAE,IAAI,CAAC;QACtE,IAAI,CAAC6H,UAAU,EAAE;UACb;QACJ,CAAC,MACI,IAAI,CAAC7L,oBAAoB,CAAC+L,GAAG,CAACF,UAAU,CAAC,EAAE;UAC5C;UACA;QACJ;QACAA,UAAU,CAACsE,aAAa,CAAC,IAAIC,KAAK,CAAC,WAAW,CAAC,CAAC;MACpD;MACA,IAAI,IAAI,CAAClO,eAAe,EAAE;QACtB;QACA,CAACqB,EAAE,GAAG,IAAI,CAAC3B,WAAW,CAACoL,QAAQ,MAAM,IAAI,IAAIzJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0J,QAAQ,CAAC,wBAAwB,EAAE;UACxGC,MAAM,EAAE,IAAI;UACZhG,MAAM,EAAE;YACJlD,EAAE,EAAE,KAAK;YACTqM,GAAG,EAAEN,MAAM;YACXjH,IAAI,EAAE;UACV;QACJ,CAAC,CAAC;MACN;MACA,OAAO,KAAK;IAChB,CAAC;EACL;AACJ;AACAvM,gCAAgC,GAAG,IAAI0D,OAAO,CAAC,CAAC,EAAEzD,sCAAsC,GAAG,SAASA,sCAAsCA,CAAA,EAAG;EACzI,IAAI,CAAC8F,SAAS,CAACyB,YAAY,CAAC,oBAAoB,EAAE,EAAE,CAAC;AACzD,CAAC,EAAEtH,qCAAqC,GAAG,SAASA,qCAAqCA,CAACiR,IAAI,EAAEM,UAAU,EAAEG,IAAI,GAAG,IAAI,EAAE;EACrHT,IAAI,CAACiB,IAAI,GAAG,IAAI,CAAC/M,WAAW,CAACmN,YAAY,CAAC,EAAE,CAAC;EAC7C,IAAIf,UAAU,CAACsC,WAAW,EAAE;IACxB5C,IAAI,CAAClJ,KAAK,GAAGwJ,UAAU,CAACsC,WAAW;EACvC;EACA5C,IAAI,CAACmB,OAAO,GAAG,MAAM;IACjB,IAAItL,EAAE;IACN,CAACA,EAAE,GAAG,IAAI,CAAC1B,eAAe,MAAM,IAAI,IAAI0B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgN,kBAAkB,CAACvC,UAAU,CAACwC,OAAO,EAAExC,UAAU,CAACyC,QAAQ,EAAEtC,IAAI,CAAC;IACrI,OAAO,KAAK;EAChB,CAAC;EACDpP,sBAAsB,CAAC,IAAI,EAAExC,gCAAgC,EAAE,GAAG,EAAEC,sCAAsC,CAAC,CAACiH,IAAI,CAAC,IAAI,CAAC;AAC1H,CAAC,EAAE/G,sCAAsC,GAAG,SAASA,sCAAsCA,CAACgR,IAAI,EAAEzE,MAAM,EAAE;EACtGyE,IAAI,CAACiB,IAAI,GAAG,IAAI,CAAC/M,WAAW,CAACmN,YAAY,CAAC,EAAE,CAAC;EAC7CrB,IAAI,CAACmB,OAAO,GAAG,MAAM;IACjB,IAAI,CAACjN,WAAW,CAAC8O,kBAAkB,CAACzH,MAAM,CAAC;IAC3C,OAAO,KAAK;EAChB,CAAC;EACDlK,sBAAsB,CAAC,IAAI,EAAExC,gCAAgC,EAAE,GAAG,EAAEC,sCAAsC,CAAC,CAACiH,IAAI,CAAC,IAAI,CAAC;AAC1H,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMQ,uBAAuB,SAAS5C,iBAAiB,CAAC;EACpDiK,MAAMA,CAAA,EAAG;IACL;IACA,OAAO,IAAI,CAAChJ,SAAS;EACzB;EACAqO,wBAAwBA,CAACxH,OAAO,EAAE;IAC9B,IAAI5F,EAAE;IACN,IAAI,IAAI,CAAC7C,IAAI,CAACmE,YAAY,EAAE;MACxB,IAAI,CAAC,CAACtB,EAAE,GAAG4F,OAAO,CAACyH,eAAe,MAAM,IAAI,IAAIrN,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsN,QAAQ,MAAM,QAAQ,EAAE;QAChG1H,OAAO,CAACyH,eAAe,CAACpJ,MAAM,GAAG,IAAI;MACzC;MACA2B,OAAO,CAAC3B,MAAM,GAAG,KAAK;IAC1B;EACJ;EACAsJ,eAAeA,CAAC9J,KAAK,EAAE;IACnB,OAAO7H,WAAW,CAAC4R,QAAQ,CAACC,KAAK,GAAGhK,KAAK,CAACiK,OAAO,GAAGjK,KAAK,CAACkK,OAAO;EACrE;EACAC,iBAAiBA,CAAChI,OAAO,EAAEiI,WAAW,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAE;IACtE,IAAIF,QAAQ,CAACG,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC5B;MACArI,OAAO,CAAC2D,gBAAgB,CAACuE,QAAQ,EAAErK,KAAK,IAAI;QACxC,IAAIzD,EAAE;QACN,CAACA,EAAE,GAAG,IAAI,CAAC3B,WAAW,CAACoL,QAAQ,MAAM,IAAI,IAAIzJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0J,QAAQ,CAAC,wBAAwB,EAAE;UACxGC,MAAM,EAAE,IAAI;UACZhG,MAAM,EAAE;YACJlD,EAAE,EAAE,IAAI,CAACtD,IAAI,CAACsD,EAAE;YAChB8E,IAAI,EAAEwI,SAAS;YACfrB,KAAK,EAAEsB,WAAW,CAACvK,KAAK,CAAC;YACzByK,KAAK,EAAEzK,KAAK,CAAC0K,QAAQ;YACrBC,QAAQ,EAAE,IAAI,CAACb,eAAe,CAAC9J,KAAK;UACxC;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC,MACI;MACD;MACAmC,OAAO,CAAC2D,gBAAgB,CAACuE,QAAQ,EAAErK,KAAK,IAAI;QACxC,IAAIzD,EAAE;QACN,IAAI8N,QAAQ,KAAK,MAAM,EAAE;UACrB,IAAI,CAACD,WAAW,CAACQ,OAAO,IAAI,CAAC5K,KAAK,CAAC6K,aAAa,EAAE;YAC9C;UACJ;UACAT,WAAW,CAACQ,OAAO,GAAG,KAAK;QAC/B,CAAC,MACI,IAAIP,QAAQ,KAAK,OAAO,EAAE;UAC3B,IAAID,WAAW,CAACQ,OAAO,EAAE;YACrB;UACJ;UACAR,WAAW,CAACQ,OAAO,GAAG,IAAI;QAC9B;QACA,IAAI,CAACL,WAAW,EAAE;UACd;QACJ;QACA,CAAChO,EAAE,GAAG,IAAI,CAAC3B,WAAW,CAACoL,QAAQ,MAAM,IAAI,IAAIzJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0J,QAAQ,CAAC,wBAAwB,EAAE;UACxGC,MAAM,EAAE,IAAI;UACZhG,MAAM,EAAE;YACJlD,EAAE,EAAE,IAAI,CAACtD,IAAI,CAACsD,EAAE;YAChB8E,IAAI,EAAEwI,SAAS;YACfrB,KAAK,EAAEsB,WAAW,CAACvK,KAAK;UAC5B;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;IACN;EACJ;EACA8K,kBAAkBA,CAAC3I,OAAO,EAAEiI,WAAW,EAAEW,KAAK,EAAEC,MAAM,EAAE;IACpD,IAAIzO,EAAE,EAAEwJ,EAAE,EAAEkF,EAAE;IACd,KAAK,MAAM,CAACZ,QAAQ,EAAEC,SAAS,CAAC,IAAIS,KAAK,EAAE;MACvC,IAAIT,SAAS,KAAK,QAAQ,KAAK,CAAC/N,EAAE,GAAG,IAAI,CAAC7C,IAAI,CAACiI,OAAO,MAAM,IAAI,IAAIpF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+N,SAAS,CAAC,CAAC,EAAE;QACzG,IAAIA,SAAS,KAAK,OAAO,IAAIA,SAAS,KAAK,MAAM,EAAE;UAC/CF,WAAW,KAAKA,WAAW,GAAG;YAAEQ,OAAO,EAAE;UAAM,CAAC,CAAC;QACrD;QACA,IAAI,CAACT,iBAAiB,CAAChI,OAAO,EAAEiI,WAAW,EAAEC,QAAQ,EAAEC,SAAS,EAAEU,MAAM,CAAC;QACzE,IAAIV,SAAS,KAAK,OAAO,IAAI,EAAE,CAACvE,EAAE,GAAG,IAAI,CAACrM,IAAI,CAACiI,OAAO,MAAM,IAAI,IAAIoE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmF,IAAI,CAAC,EAAE;UACnG;UACA,IAAI,CAACf,iBAAiB,CAAChI,OAAO,EAAEiI,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC;QACtE,CAAC,MACI,IAAIE,SAAS,KAAK,MAAM,IAAI,EAAE,CAACW,EAAE,GAAG,IAAI,CAACvR,IAAI,CAACiI,OAAO,MAAM,IAAI,IAAIsJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,KAAK,CAAC,EAAE;UACxG,IAAI,CAAChB,iBAAiB,CAAChI,OAAO,EAAEiI,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC;QACxE;MACJ;IACJ;EACJ;EACAgB,mBAAmBA,CAACjJ,OAAO,EAAE;IACzB,MAAMlC,KAAK,GAAG,IAAI,CAACvG,IAAI,CAAC2R,eAAe,IAAI,IAAI;IAC/ClJ,OAAO,CAAChF,KAAK,CAACkO,eAAe,GACzBpL,KAAK,KAAK,IAAI,GACR,aAAa,GACb1H,IAAI,CAACwG,YAAY,CAACkB,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;EAC7D;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIqL,aAAaA,CAACnJ,OAAO,EAAE;IACnB,MAAMoJ,cAAc,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC;IAClD,MAAM;MAAEC;IAAU,CAAC,GAAG,IAAI,CAAC9R,IAAI,CAAC+R,qBAAqB;IACrD,MAAMC,QAAQ,GAAG,IAAI,CAAChS,IAAI,CAAC+R,qBAAqB,CAACC,QAAQ,IAAI3S,iBAAiB;IAC9E,MAAMoE,KAAK,GAAGgF,OAAO,CAAChF,KAAK;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIwO,gBAAgB;IACpB,MAAMC,WAAW,GAAG,CAAC;IACrB,MAAMC,iBAAiB,GAAG9I,CAAC,IAAIC,IAAI,CAAC8I,KAAK,CAAC,EAAE,GAAG/I,CAAC,CAAC,GAAG,EAAE;IACtD,IAAI,IAAI,CAACrJ,IAAI,CAACqS,SAAS,EAAE;MACrB,MAAM1S,MAAM,GAAG2J,IAAI,CAACgJ,GAAG,CAAC,IAAI,CAACtS,IAAI,CAACP,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAACO,IAAI,CAACP,IAAI,CAAC,CAAC,CAAC,GAAGyS,WAAW,CAAC;MAC5E,MAAMK,aAAa,GAAGjJ,IAAI,CAAC8I,KAAK,CAACzS,MAAM,IAAIT,WAAW,GAAG8S,QAAQ,CAAC,CAAC,IAAI,CAAC;MACxE,MAAMQ,UAAU,GAAG7S,MAAM,GAAG4S,aAAa;MACzCN,gBAAgB,GAAG3I,IAAI,CAACmJ,GAAG,CAACT,QAAQ,EAAEG,iBAAiB,CAACK,UAAU,GAAGtT,WAAW,CAAC,CAAC;IACtF,CAAC,MACI;MACD,MAAMS,MAAM,GAAG2J,IAAI,CAACgJ,GAAG,CAAC,IAAI,CAACtS,IAAI,CAACP,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAACO,IAAI,CAACP,IAAI,CAAC,CAAC,CAAC,GAAGyS,WAAW,CAAC;MAC5ED,gBAAgB,GAAG3I,IAAI,CAACmJ,GAAG,CAACT,QAAQ,EAAEG,iBAAiB,CAACxS,MAAM,GAAGT,WAAW,CAAC,CAAC;IAClF;IACAuE,KAAK,CAACuO,QAAQ,GAAG,QAAQC,gBAAgB,2BAA2B;IACpExO,KAAK,CAAC8C,KAAK,GAAG1H,IAAI,CAACwG,YAAY,CAACyM,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CAAC;IACzE,IAAI,IAAI,CAAC9R,IAAI,CAAC0S,aAAa,KAAK,IAAI,EAAE;MAClCjP,KAAK,CAACkP,SAAS,GAAGd,cAAc,CAAC,IAAI,CAAC7R,IAAI,CAAC0S,aAAa,CAAC;IAC7D;EACJ;EACAhL,YAAYA,CAACe,OAAO,EAAEgE,UAAU,EAAE;IAC9B,IAAIA,UAAU,EAAE;MACZhE,OAAO,CAACpF,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC;IAC1C,CAAC,MACI;MACDoF,OAAO,CAACiE,eAAe,CAAC,UAAU,CAAC;IACvC;IACAjE,OAAO,CAACpF,YAAY,CAAC,eAAe,EAAEoJ,UAAU,CAAC;EACrD;AACJ;AACA,MAAMnM,2BAA2B,SAASiD,uBAAuB,CAAC;EAC9D3C,WAAWA,CAACd,UAAU,EAAE;IACpB,MAAMe,YAAY,GAAGf,UAAU,CAACuB,WAAW,IACvCvB,UAAU,CAACE,IAAI,CAACmE,YAAY,IAC3B,CAACrE,UAAU,CAACE,IAAI,CAAC4S,aAAa,IAAI,CAAC,CAAC9S,UAAU,CAACE,IAAI,CAAC6S,UAAW;IACpE,KAAK,CAAC/S,UAAU,EAAE;MAAEe;IAAa,CAAC,CAAC;EACvC;EACAiS,qBAAqBA,CAACC,IAAI,EAAEC,GAAG,EAAEzD,KAAK,EAAE0D,YAAY,EAAE;IAClD,MAAM7D,OAAO,GAAG,IAAI,CAAC7N,iBAAiB;IACtC,KAAK,MAAMkH,OAAO,IAAI,IAAI,CAACoC,kBAAkB,CAACkI,IAAI,CAAC3K,IAAI,EACvD,cAAe2K,IAAI,CAACzP,EAAE,CAAC,EAAE;MACrB,IAAImF,OAAO,CAAC0C,UAAU,EAAE;QACpB1C,OAAO,CAAC0C,UAAU,CAAC6H,GAAG,CAAC,GAAGzD,KAAK;MACnC;MACAH,OAAO,CAACxI,QAAQ,CAAC6B,OAAO,CAACnF,EAAE,EAAE;QAAE,CAAC2P,YAAY,GAAG1D;MAAM,CAAC,CAAC;IAC3D;EACJ;EACA3E,MAAMA,CAAA,EAAG;IACL,IAAI/H,EAAE,EAAEwJ,EAAE;IACV,MAAM+C,OAAO,GAAG,IAAI,CAAC7N,iBAAiB;IACtC,MAAM+B,EAAE,GAAG,IAAI,CAACtD,IAAI,CAACsD,EAAE;IACvB;IACA,IAAI,CAAC1B,SAAS,CAACmM,SAAS,CAAC/M,GAAG,CAAC,0BAA0B,CAAC;IACxD,IAAIyH,OAAO,GAAG,IAAI;IAClB,IAAI,IAAI,CAACpH,WAAW,EAAE;MAClB;MACA;MACA;MACA,MAAMqH,UAAU,GAAG0G,OAAO,CAAC8D,QAAQ,CAAC5P,EAAE,EAAE;QACpCiM,KAAK,EAAE,IAAI,CAACvP,IAAI,CAAC6S;MACrB,CAAC,CAAC;MACF,IAAIM,WAAW,GAAGzK,UAAU,CAAC6G,KAAK,IAAI,EAAE;MACxC,MAAM6D,MAAM,GAAGhE,OAAO,CAAC8D,QAAQ,CAAC5P,EAAE,EAAE;QAChC+P,SAAS,EAAE,IAAI,CAACrT,IAAI,CAACoT;MACzB,CAAC,CAAC,CAACC,SAAS;MACZ,IAAID,MAAM,IAAID,WAAW,CAAC3J,MAAM,GAAG4J,MAAM,EAAE;QACvCD,WAAW,GAAGA,WAAW,CAACrQ,KAAK,CAAC,CAAC,EAAEsQ,MAAM,CAAC;MAC9C;MACA,IAAIE,oBAAoB,GAAG5K,UAAU,CAAC6K,cAAc,KAAK,CAAC1Q,EAAE,GAAG,IAAI,CAAC7C,IAAI,CAACmT,WAAW,MAAM,IAAI,IAAItQ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6H,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI;MACjJ,IAAI4I,oBAAoB,IAAI,IAAI,CAACtT,IAAI,CAACwT,IAAI,EAAE;QACxCF,oBAAoB,GAAGA,oBAAoB,CAACG,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC;MACtE;MACA,MAAM/C,WAAW,GAAG;QAChBgD,SAAS,EAAEP,WAAW;QACtBI,cAAc,EAAED,oBAAoB;QACpCK,kBAAkB,EAAE,IAAI;QACxBC,SAAS,EAAE,CAAC;QACZ1C,OAAO,EAAE;MACb,CAAC;MACD,IAAI,IAAI,CAAClR,IAAI,CAACqS,SAAS,EAAE;QACrB5J,OAAO,GAAGtF,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;QAC5CqF,OAAO,CAAC0K,WAAW,GAAGG,oBAAoB,KAAK,IAAI,IAAIA,oBAAoB,KAAK,KAAK,CAAC,GAAGA,oBAAoB,GAAGH,WAAW;QAC3H,IAAI,IAAI,CAACnT,IAAI,CAAC6T,WAAW,EAAE;UACvBpL,OAAO,CAAChF,KAAK,CAACqQ,SAAS,GAAG,QAAQ;QACtC;MACJ,CAAC,MACI;QACDrL,OAAO,GAAGtF,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;QACzCqF,OAAO,CAAC6G,IAAI,GAAG,MAAM;QACrB7G,OAAO,CAACpF,YAAY,CAAC,OAAO,EAAEiQ,oBAAoB,KAAK,IAAI,IAAIA,oBAAoB,KAAK,KAAK,CAAC,GAAGA,oBAAoB,GAAGH,WAAW,CAAC;QACpI,IAAI,IAAI,CAACnT,IAAI,CAAC6T,WAAW,EAAE;UACvBpL,OAAO,CAAChF,KAAK,CAACsQ,SAAS,GAAG,QAAQ;QACtC;MACJ;MACA,IAAI,IAAI,CAAC/T,IAAI,CAACmE,YAAY,EAAE;QACxBsE,OAAO,CAAC3B,MAAM,GAAG,IAAI;MACzB;MACAxH,oBAAoB,CAAC0B,GAAG,CAACyH,OAAO,CAAC;MACjCA,OAAO,CAACpF,YAAY,CAAC,iBAAiB,EAAEC,EAAE,CAAC;MAC3CmF,OAAO,CAACjB,QAAQ,GAAG,IAAI,CAACxH,IAAI,CAACgU,QAAQ;MACrCvL,OAAO,CAACL,IAAI,GAAG,IAAI,CAACpI,IAAI,CAACiP,SAAS;MAClCxG,OAAO,CAACjF,QAAQ,GAAGpE,iBAAiB;MACpC,IAAI,CAACsI,YAAY,CAACe,OAAO,EAAE,IAAI,CAACzI,IAAI,CAACyH,QAAQ,CAAC;MAC9C,IAAI2L,MAAM,EAAE;QACR3K,OAAO,CAACwL,SAAS,GAAGb,MAAM;MAC9B;MACA3K,OAAO,CAAC2D,gBAAgB,CAAC,OAAO,EAAE9F,KAAK,IAAI;QACvC8I,OAAO,CAACxI,QAAQ,CAACtD,EAAE,EAAE;UAAEiM,KAAK,EAAEjJ,KAAK,CAACK,MAAM,CAAC4I;QAAM,CAAC,CAAC;QACnD,IAAI,CAACuD,qBAAqB,CAACrK,OAAO,EAAE,OAAO,EAAEnC,KAAK,CAACK,MAAM,CAAC4I,KAAK,EAAE,OAAO,CAAC;QACzEmB,WAAW,CAAC6C,cAAc,GAAG,IAAI;MACrC,CAAC,CAAC;MACF9K,OAAO,CAAC2D,gBAAgB,CAAC,WAAW,EAAE,MAAM;QACxC,IAAIvJ,EAAE;QACN,MAAM2M,YAAY,GAAG,CAAC3M,EAAE,GAAG,IAAI,CAAC7C,IAAI,CAACkU,iBAAiB,MAAM,IAAI,IAAIrR,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;QAC3F4F,OAAO,CAAC8G,KAAK,GAAGmB,WAAW,CAACgD,SAAS,GAAGlE,YAAY;QACpDkB,WAAW,CAAC6C,cAAc,GAAG,IAAI;MACrC,CAAC,CAAC;MACF,IAAIY,YAAY,GAAG7N,KAAK,IAAI;QACxB,MAAM;UAAEiN;QAAe,CAAC,GAAG7C,WAAW;QACtC,IAAI6C,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAKa,SAAS,EAAE;UACzD9N,KAAK,CAACK,MAAM,CAAC4I,KAAK,GAAGgE,cAAc;QACvC;QACA;QACAjN,KAAK,CAACK,MAAM,CAAC0N,UAAU,GAAG,CAAC;MAC/B,CAAC;MACD,IAAI,IAAI,CAAC7S,eAAe,IAAI,IAAI,CAACC,YAAY,EAAE;QAC3CgH,OAAO,CAAC2D,gBAAgB,CAAC,OAAO,EAAE9F,KAAK,IAAI;UACvC,IAAIzD,EAAE;UACN,IAAI6N,WAAW,CAACQ,OAAO,EAAE;YACrB;UACJ;UACA,MAAM;YAAEvK;UAAO,CAAC,GAAGL,KAAK;UACxB,IAAIoK,WAAW,CAACgD,SAAS,EAAE;YACvB/M,MAAM,CAAC4I,KAAK,GAAGmB,WAAW,CAACgD,SAAS;UACxC;UACAhD,WAAW,CAACiD,kBAAkB,GAAGhN,MAAM,CAAC4I,KAAK;UAC7CmB,WAAW,CAACkD,SAAS,GAAG,CAAC;UACzB,IAAI,EAAE,CAAC/Q,EAAE,GAAG,IAAI,CAAC7C,IAAI,CAACiI,OAAO,MAAM,IAAI,IAAIpF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4O,KAAK,CAAC,EAAE;YAC3Ef,WAAW,CAACQ,OAAO,GAAG,IAAI;UAC9B;QACJ,CAAC,CAAC;QACFzI,OAAO,CAAC2D,gBAAgB,CAAC,mBAAmB,EAAElE,OAAO,IAAI;UACrD,IAAI,CAAC+H,wBAAwB,CAAC/H,OAAO,CAACvB,MAAM,CAAC;UAC7C,MAAMsB,OAAO,GAAG;YACZsH,KAAKA,CAACjJ,KAAK,EAAE;cACT,IAAIzD,EAAE;cACN6N,WAAW,CAACgD,SAAS,GAAG,CAAC7Q,EAAE,GAAGyD,KAAK,CAACE,MAAM,CAAC+I,KAAK,MAAM,IAAI,IAAI1M,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;cACrFuM,OAAO,CAACxI,QAAQ,CAACtD,EAAE,EAAE;gBAAEiM,KAAK,EAAEmB,WAAW,CAACgD,SAAS,CAAC/P,QAAQ,CAAC;cAAE,CAAC,CAAC;cACjE2C,KAAK,CAACK,MAAM,CAAC4I,KAAK,GAAGmB,WAAW,CAACgD,SAAS;YAC9C,CAAC;YACDH,cAAcA,CAACjN,KAAK,EAAE;cAClB,MAAM;gBAAEiN;cAAe,CAAC,GAAGjN,KAAK,CAACE,MAAM;cACvCkK,WAAW,CAAC6C,cAAc,GAAGA,cAAc;cAC3C,IAAIA,cAAc,KAAK,IAAI,IACvBA,cAAc,KAAKa,SAAS,IAC5B9N,KAAK,CAACK,MAAM,KAAKxD,QAAQ,CAACmR,aAAa,EAAE;gBACzC;gBACAhO,KAAK,CAACK,MAAM,CAAC4I,KAAK,GAAGgE,cAAc;cACvC;cACAnE,OAAO,CAACxI,QAAQ,CAACtD,EAAE,EAAE;gBACjBiQ;cACJ,CAAC,CAAC;YACN,CAAC;YACDgB,QAAQA,CAACjO,KAAK,EAAE;cACZA,KAAK,CAACK,MAAM,CAAC6N,iBAAiB,CAAC,GAAGlO,KAAK,CAACE,MAAM,CAAC+N,QAAQ,CAAC;YAC5D,CAAC;YACDlB,SAAS,EAAE/M,KAAK,IAAI;cAChB,IAAIzD,EAAE;cACN,MAAM;gBAAEwQ;cAAU,CAAC,GAAG/M,KAAK,CAACE,MAAM;cAClC,MAAM;gBAAEG;cAAO,CAAC,GAAGL,KAAK;cACxB,IAAI+M,SAAS,KAAK,CAAC,EAAE;gBACjB1M,MAAM,CAAC+F,eAAe,CAAC,WAAW,CAAC;gBACnC;cACJ;cACA/F,MAAM,CAACtD,YAAY,CAAC,WAAW,EAAEgQ,SAAS,CAAC;cAC3C,IAAI9D,KAAK,GAAGmB,WAAW,CAACgD,SAAS;cACjC,IAAI,CAACnE,KAAK,IAAIA,KAAK,CAAC/F,MAAM,IAAI6J,SAAS,EAAE;gBACrC;cACJ;cACA9D,KAAK,GAAGA,KAAK,CAACzM,KAAK,CAAC,CAAC,EAAEuQ,SAAS,CAAC;cACjC1M,MAAM,CAAC4I,KAAK,GAAGmB,WAAW,CAACgD,SAAS,GAAGnE,KAAK;cAC5CH,OAAO,CAACxI,QAAQ,CAACtD,EAAE,EAAE;gBAAEiM;cAAM,CAAC,CAAC;cAC/B,CAAC1M,EAAE,GAAG,IAAI,CAAC3B,WAAW,CAACoL,QAAQ,MAAM,IAAI,IAAIzJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0J,QAAQ,CAAC,wBAAwB,EAAE;gBACxGC,MAAM,EAAE,IAAI;gBACZhG,MAAM,EAAE;kBACJlD,EAAE;kBACF8E,IAAI,EAAE,WAAW;kBACjBmH,KAAK;kBACLkF,UAAU,EAAE,IAAI;kBAChBb,SAAS,EAAE,CAAC;kBACZc,QAAQ,EAAE/N,MAAM,CAACgO,cAAc;kBAC/BC,MAAM,EAAEjO,MAAM,CAACkO;gBACnB;cACJ,CAAC,CAAC;YACN;UACJ,CAAC;UACD,IAAI,CAAC7M,yBAAyB,CAACC,OAAO,EAAEC,OAAO,CAAC;QACpD,CAAC,CAAC;QACF;QACA;QACAO,OAAO,CAAC2D,gBAAgB,CAAC,SAAS,EAAE9F,KAAK,IAAI;UACzC,IAAIzD,EAAE;UACN6N,WAAW,CAACkD,SAAS,GAAG,CAAC;UACzB;UACA;UACA,IAAIA,SAAS,GAAG,CAAC,CAAC;UAClB,IAAItN,KAAK,CAAC0M,GAAG,KAAK,QAAQ,EAAE;YACxBY,SAAS,GAAG,CAAC;UACjB,CAAC,MACI,IAAItN,KAAK,CAAC0M,GAAG,KAAK,OAAO,IAAI,CAAC,IAAI,CAAChT,IAAI,CAACqS,SAAS,EAAE;YACpD;YACA;YACA;YACAuB,SAAS,GAAG,CAAC;UACjB,CAAC,MACI,IAAItN,KAAK,CAAC0M,GAAG,KAAK,KAAK,EAAE;YAC1BtC,WAAW,CAACkD,SAAS,GAAG,CAAC;UAC7B;UACA,IAAIA,SAAS,KAAK,CAAC,CAAC,EAAE;YAClB;UACJ;UACA,MAAM;YAAErE;UAAM,CAAC,GAAGjJ,KAAK,CAACK,MAAM;UAC9B,IAAI+J,WAAW,CAACiD,kBAAkB,KAAKpE,KAAK,EAAE;YAC1C;UACJ;UACAmB,WAAW,CAACiD,kBAAkB,GAAGpE,KAAK;UACtC;UACAmB,WAAW,CAACgD,SAAS,GAAGnE,KAAK;UAC7B,CAAC1M,EAAE,GAAG,IAAI,CAAC3B,WAAW,CAACoL,QAAQ,MAAM,IAAI,IAAIzJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0J,QAAQ,CAAC,wBAAwB,EAAE;YACxGC,MAAM,EAAE,IAAI;YACZhG,MAAM,EAAE;cACJlD,EAAE;cACF8E,IAAI,EAAE,WAAW;cACjBmH,KAAK;cACLkF,UAAU,EAAE,IAAI;cAChBb,SAAS;cACTc,QAAQ,EAAEpO,KAAK,CAACK,MAAM,CAACgO,cAAc;cACrCC,MAAM,EAAEtO,KAAK,CAACK,MAAM,CAACkO;YACzB;UACJ,CAAC,CAAC;QACN,CAAC,CAAC;QACF,MAAMC,aAAa,GAAGX,YAAY;QAClCA,YAAY,GAAG,IAAI;QACnB1L,OAAO,CAAC2D,gBAAgB,CAAC,MAAM,EAAE9F,KAAK,IAAI;UACtC,IAAIzD,EAAE,EAAEwJ,EAAE;UACV,IAAI,CAACqE,WAAW,CAACQ,OAAO,IAAI,CAAC5K,KAAK,CAAC6K,aAAa,EAAE;YAC9C;UACJ;UACA,IAAI,EAAE,CAACtO,EAAE,GAAG,IAAI,CAAC7C,IAAI,CAACiI,OAAO,MAAM,IAAI,IAAIpF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC2O,IAAI,CAAC,EAAE;YAC1Ed,WAAW,CAACQ,OAAO,GAAG,KAAK;UAC/B;UACA,MAAM;YAAE3B;UAAM,CAAC,GAAGjJ,KAAK,CAACK,MAAM;UAC9B+J,WAAW,CAACgD,SAAS,GAAGnE,KAAK;UAC7B,IAAImB,WAAW,CAACiD,kBAAkB,KAAKpE,KAAK,EAAE;YAC1C,CAAClD,EAAE,GAAG,IAAI,CAACnL,WAAW,CAACoL,QAAQ,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,QAAQ,CAAC,wBAAwB,EAAE;cACxGC,MAAM,EAAE,IAAI;cACZhG,MAAM,EAAE;gBACJlD,EAAE;gBACF8E,IAAI,EAAE,WAAW;gBACjBmH,KAAK;gBACLkF,UAAU,EAAE,IAAI;gBAChBb,SAAS,EAAElD,WAAW,CAACkD,SAAS;gBAChCc,QAAQ,EAAEpO,KAAK,CAACK,MAAM,CAACgO,cAAc;gBACrCC,MAAM,EAAEtO,KAAK,CAACK,MAAM,CAACkO;cACzB;YACJ,CAAC,CAAC;UACN;UACAC,aAAa,CAACxO,KAAK,CAAC;QACxB,CAAC,CAAC;QACF,IAAI,CAAC+F,EAAE,GAAG,IAAI,CAACrM,IAAI,CAACiI,OAAO,MAAM,IAAI,IAAIoE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0I,SAAS,EAAE;UAC5EtM,OAAO,CAAC2D,gBAAgB,CAAC,aAAa,EAAE9F,KAAK,IAAI;YAC7C,IAAIzD,EAAE;YACN6N,WAAW,CAACiD,kBAAkB,GAAG,IAAI;YACrC,MAAM;cAAE3T,IAAI;cAAE2G;YAAO,CAAC,GAAGL,KAAK;YAC9B,MAAM;cAAEiJ,KAAK;cAAEoF,cAAc;cAAEE;YAAa,CAAC,GAAGlO,MAAM;YACtD,IAAI+N,QAAQ,GAAGC,cAAc;cAAEC,MAAM,GAAGC,YAAY;YACpD,QAAQvO,KAAK,CAAC0O,SAAS;cACnB;cACA,KAAK,oBAAoB;gBAAE;kBACvB,MAAMC,KAAK,GAAG1F,KAAK,CACd2F,SAAS,CAAC,CAAC,EAAEP,cAAc,CAAC,CAC5BM,KAAK,CAAC,YAAY,CAAC;kBACxB,IAAIA,KAAK,EAAE;oBACPP,QAAQ,IAAIO,KAAK,CAAC,CAAC,CAAC,CAACzL,MAAM;kBAC/B;kBACA;gBACJ;cACA,KAAK,mBAAmB;gBAAE;kBACtB,MAAMyL,KAAK,GAAG1F,KAAK,CACd2F,SAAS,CAACP,cAAc,CAAC,CACzBM,KAAK,CAAC,YAAY,CAAC;kBACxB,IAAIA,KAAK,EAAE;oBACPL,MAAM,IAAIK,KAAK,CAAC,CAAC,CAAC,CAACzL,MAAM;kBAC7B;kBACA;gBACJ;cACA,KAAK,uBAAuB;gBACxB,IAAImL,cAAc,KAAKE,YAAY,EAAE;kBACjCH,QAAQ,IAAI,CAAC;gBACjB;gBACA;cACJ,KAAK,sBAAsB;gBACvB,IAAIC,cAAc,KAAKE,YAAY,EAAE;kBACjCD,MAAM,IAAI,CAAC;gBACf;gBACA;cACJ;gBAAS;YACb;YACA;YACAtO,KAAK,CAAC6O,cAAc,CAAC,CAAC;YACtB,CAACtS,EAAE,GAAG,IAAI,CAAC3B,WAAW,CAACoL,QAAQ,MAAM,IAAI,IAAIzJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0J,QAAQ,CAAC,wBAAwB,EAAE;cACxGC,MAAM,EAAE,IAAI;cACZhG,MAAM,EAAE;gBACJlD,EAAE;gBACF8E,IAAI,EAAE,WAAW;gBACjBmH,KAAK;gBACL6F,MAAM,EAAEpV,IAAI,IAAI,EAAE;gBAClByU,UAAU,EAAE,KAAK;gBACjBC,QAAQ;gBACRE;cACJ;YACJ,CAAC,CAAC;UACN,CAAC,CAAC;QACN;QACA,IAAI,CAACxD,kBAAkB,CAAC3I,OAAO,EAAEiI,WAAW,EAAE,CAC1C,CAAC,OAAO,EAAE,OAAO,CAAC,EAClB,CAAC,MAAM,EAAE,MAAM,CAAC,EAChB,CAAC,WAAW,EAAE,YAAY,CAAC,EAC3B,CAAC,YAAY,EAAE,aAAa,CAAC,EAC7B,CAAC,YAAY,EAAE,YAAY,CAAC,EAC5B,CAAC,SAAS,EAAE,UAAU,CAAC,CAC1B,EAAEpK,KAAK,IAAIA,KAAK,CAACK,MAAM,CAAC4I,KAAK,CAAC;MACnC;MACA,IAAI4E,YAAY,EAAE;QACd1L,OAAO,CAAC2D,gBAAgB,CAAC,MAAM,EAAE+H,YAAY,CAAC;MAClD;MACA,IAAI,IAAI,CAACnU,IAAI,CAACwT,IAAI,EAAE;QAChB,MAAM6B,UAAU,GAAG,IAAI,CAACrV,IAAI,CAACP,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAACO,IAAI,CAACP,IAAI,CAAC,CAAC,CAAC;QACxD,MAAM6V,SAAS,GAAGD,UAAU,GAAGjC,MAAM;QACrC3K,OAAO,CAACsF,SAAS,CAAC/M,GAAG,CAAC,MAAM,CAAC;QAC7ByH,OAAO,CAAChF,KAAK,CAAC8R,aAAa,GAAG,QAAQD,SAAS,iCAAiC;MACpF;IACJ,CAAC,MACI;MACD7M,OAAO,GAAGtF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACvCqF,OAAO,CAAC0K,WAAW,GAAG,IAAI,CAACnT,IAAI,CAAC6S,UAAU;MAC1CpK,OAAO,CAAChF,KAAK,CAAC+R,aAAa,GAAG,QAAQ;MACtC/M,OAAO,CAAChF,KAAK,CAACoD,OAAO,GAAG,YAAY;MACpC,IAAI,IAAI,CAAC7G,IAAI,CAACmE,YAAY,EAAE;QACxBsE,OAAO,CAAC3B,MAAM,GAAG,IAAI;MACzB;IACJ;IACA,IAAI,CAAC8K,aAAa,CAACnJ,OAAO,CAAC;IAC3B,IAAI,CAACiJ,mBAAmB,CAACjJ,OAAO,CAAC;IACjC,IAAI,CAACD,2BAA2B,CAACC,OAAO,CAAC;IACzC,IAAI,CAAC7G,SAAS,CAACqI,MAAM,CAACxB,OAAO,CAAC;IAC9B,OAAO,IAAI,CAAC7G,SAAS;EACzB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiD,kCAAkC,SAAStB,uBAAuB,CAAC;EACrE3C,WAAWA,CAACd,UAAU,EAAE;IACpB,KAAK,CAACA,UAAU,EAAE;MAAEe,YAAY,EAAEf,UAAU,CAACuB;IAAY,CAAC,CAAC;EAC/D;EACAuJ,MAAMA,CAAA,EAAG;IACL,IAAI,CAAChJ,SAAS,CAACmM,SAAS,CAAC/M,GAAG,CAAC,wBAAwB,EAAE,aAAa,CAAC;IACrE,MAAMoO,OAAO,GAAG,IAAI,CAAC7N,iBAAiB;IACtC,MAAMvB,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMsD,EAAE,GAAGtD,IAAI,CAACsD,EAAE;IAClB,IAAIiM,KAAK,GAAGH,OAAO,CAAC8D,QAAQ,CAAC5P,EAAE,EAAE;MAC7BiM,KAAK,EAAEvP,IAAI,CAAC6S,UAAU,KAAK7S,IAAI,CAACyV;IACpC,CAAC,CAAC,CAAClG,KAAK;IACR,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC3B;MACAA,KAAK,GAAGA,KAAK,KAAKvP,IAAI,CAACyV,WAAW;MAClCrG,OAAO,CAACxI,QAAQ,CAACtD,EAAE,EAAE;QAAEiM;MAAM,CAAC,CAAC;IACnC;IACA,IAAIA,KAAK,EAAE;MACP;MACA;MACA;MACA;MACA;MACA;MACA,KAAK,MAAMmG,KAAK,IAAI,IAAI,CAAC7K,kBAAkB,CAAC7K,IAAI,CAACiP,SAAS,EAC1D,cAAe3L,EAAE,CAAC,EAAE;QAChB8L,OAAO,CAACxI,QAAQ,CAAC8O,KAAK,CAACpS,EAAE,EAAE;UAAEiM,KAAK,EAAE;QAAM,CAAC,CAAC;MAChD;IACJ;IACA,MAAM9G,OAAO,GAAGtF,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC/C9D,oBAAoB,CAAC0B,GAAG,CAACyH,OAAO,CAAC;IACjCA,OAAO,CAACpF,YAAY,CAAC,iBAAiB,EAAEC,EAAE,CAAC;IAC3CmF,OAAO,CAACjB,QAAQ,GAAGxH,IAAI,CAACgU,QAAQ;IAChC,IAAI,CAACtM,YAAY,CAACe,OAAO,EAAE,IAAI,CAACzI,IAAI,CAACyH,QAAQ,CAAC;IAC9CgB,OAAO,CAAC6G,IAAI,GAAG,OAAO;IACtB7G,OAAO,CAACL,IAAI,GAAGpI,IAAI,CAACiP,SAAS;IAC7B,IAAIM,KAAK,EAAE;MACP9G,OAAO,CAACpF,YAAY,CAAC,SAAS,EAAG,IAAI,CAAEM,QAAQ,CAAC,CAAC,CAAC;IACtD;IACA8E,OAAO,CAACjF,QAAQ,GAAGpE,iBAAiB;IACpCqJ,OAAO,CAAC2D,gBAAgB,CAAC,QAAQ,EAAE9F,KAAK,IAAI;MACxC,MAAM;QAAE8B,IAAI;QAAEuN;MAAQ,CAAC,GAAGrP,KAAK,CAACK,MAAM;MACtC,KAAK,MAAM+O,KAAK,IAAI,IAAI,CAAC7K,kBAAkB,CAACzC,IAAI,EAAE,cAAe9E,EAAE,CAAC,EAAE;QAClE8L,OAAO,CAACxI,QAAQ,CAAC8O,KAAK,CAACpS,EAAE,EAAE;UAAEiM,KAAK,EAAE;QAAM,CAAC,CAAC;MAChD;MACAH,OAAO,CAACxI,QAAQ,CAACtD,EAAE,EAAE;QAAEiM,KAAK,EAAEoG;MAAQ,CAAC,CAAC;IAC5C,CAAC,CAAC;IACFlN,OAAO,CAAC2D,gBAAgB,CAAC,WAAW,EAAE9F,KAAK,IAAI;MAC3C,MAAMkJ,YAAY,GAAGxP,IAAI,CAACkU,iBAAiB;MAC3C;MACA5N,KAAK,CAACK,MAAM,CAACgP,OAAO,GAChBnG,YAAY,KAAK,IAAI,IACjBA,YAAY,KAAK4E,SAAS,IAC1B5E,YAAY,KAAKxP,IAAI,CAACyV,WAAW;IAC7C,CAAC,CAAC;IACF,IAAI,IAAI,CAACjU,eAAe,IAAI,IAAI,CAACC,YAAY,EAAE;MAC3C,MAAMmU,cAAc,GAAG5V,IAAI,CAACyV,WAAW;MACvChN,OAAO,CAAC2D,gBAAgB,CAAC,mBAAmB,EAAElE,OAAO,IAAI;QACrD,MAAMD,OAAO,GAAG;UACZsH,KAAK,EAAEjJ,KAAK,IAAI;YACZ,MAAMqP,OAAO,GAAGC,cAAc,KAAKtP,KAAK,CAACE,MAAM,CAAC+I,KAAK;YACrD,KAAK,MAAMmG,KAAK,IAAI,IAAI,CAAC7K,kBAAkB,CAACvE,KAAK,CAACK,MAAM,CAACyB,IAAI,CAAC,EAAE;cAC5D,MAAMyN,UAAU,GAAGF,OAAO,IAAID,KAAK,CAACpS,EAAE,KAAKA,EAAE;cAC7C,IAAIoS,KAAK,CAACvK,UAAU,EAAE;gBAClBuK,KAAK,CAACvK,UAAU,CAACwK,OAAO,GAAGE,UAAU;cACzC;cACAzG,OAAO,CAACxI,QAAQ,CAAC8O,KAAK,CAACpS,EAAE,EAAE;gBAAEiM,KAAK,EAAEsG;cAAW,CAAC,CAAC;YACrD;UACJ;QACJ,CAAC;QACD,IAAI,CAAC7N,yBAAyB,CAACC,OAAO,EAAEC,OAAO,CAAC;MACpD,CAAC,CAAC;MACF,IAAI,CAACkJ,kBAAkB,CAAC3I,OAAO,EAAE,IAAI,EAAE,CACnC,CAAC,QAAQ,EAAE,UAAU,CAAC,EACtB,CAAC,QAAQ,EAAE,QAAQ,CAAC,EACpB,CAAC,OAAO,EAAE,OAAO,CAAC,EAClB,CAAC,MAAM,EAAE,MAAM,CAAC,EAChB,CAAC,WAAW,EAAE,YAAY,CAAC,EAC3B,CAAC,YAAY,EAAE,aAAa,CAAC,EAC7B,CAAC,YAAY,EAAE,YAAY,CAAC,EAC5B,CAAC,SAAS,EAAE,UAAU,CAAC,CAC1B,EAAEnC,KAAK,IAAIA,KAAK,CAACK,MAAM,CAACgP,OAAO,CAAC;IACrC;IACA,IAAI,CAACjE,mBAAmB,CAACjJ,OAAO,CAAC;IACjC,IAAI,CAACD,2BAA2B,CAACC,OAAO,CAAC;IACzC,IAAI,CAAC7G,SAAS,CAACqI,MAAM,CAACxB,OAAO,CAAC;IAC9B,OAAO,IAAI,CAAC7G,SAAS;EACzB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoC,sBAAsB,SAASrD,iBAAiB,CAAC;EACnD;EACAC,WAAWA,CAACd,UAAU,EAAE;IACpB,MAAM;MAAEE,IAAI;MAAE8V;IAAS,CAAC,GAAGhW,UAAU;IACrC,KAAK,CAACA,UAAU,EAAE;MAAEe,YAAY,EAAEF,iBAAiB,CAACwB,aAAa,CAACnC,IAAI;IAAE,CAAC,CAAC;IAC1E;IACA,IAAI,CAAC8V,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACjU,KAAK,GAAG,IAAI;EACrB;EACA+I,MAAMA,CAAA,EAAG;IACL,IAAI,CAAChJ,SAAS,CAACmM,SAAS,CAAC/M,GAAG,CAAC,iBAAiB,CAAC;IAC/C,MAAMa,KAAK,GAAI,IAAI,CAACA,KAAK,GAAG,IAAIkU,YAAY,CAAC;MACzCnU,SAAS,EAAE,IAAI,CAACA,SAAS;MACzB2E,KAAK,EAAE,IAAI,CAACvG,IAAI,CAACuG,KAAK;MACtBnE,QAAQ,EAAE,IAAI,CAACpC,IAAI,CAACoC,QAAQ;MAC5B4T,gBAAgB,EAAE,IAAI,CAAChW,IAAI,CAACgW,gBAAgB;MAC5C3T,WAAW,EAAE,IAAI,CAACrC,IAAI,CAACqC,WAAW;MAClCC,QAAQ,EAAE,IAAI,CAACtC,IAAI,CAACsC,QAAQ;MAC5B7C,IAAI,EAAE,IAAI,CAACO,IAAI,CAACP,IAAI;MACpBwW,UAAU,EAAE,IAAI,CAACjW,IAAI,CAACiW,UAAU,IAAI,IAAI;MACxCtU,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBmU,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBI,IAAI,EAAE,IAAI,CAAClW,IAAI,CAACkW;IACpB,CAAC,CAAE;IACH,MAAMC,UAAU,GAAG,EAAE;IACrB,KAAK,MAAM1N,OAAO,IAAI,IAAI,CAACqN,QAAQ,EAAE;MACjCrN,OAAO,CAAC5G,KAAK,GAAGA,KAAK;MACrBsU,UAAU,CAAC3L,IAAI,CAAC/B,OAAO,CAACzI,IAAI,CAACsD,EAAE,CAAC;MAChCmF,OAAO,CAACoD,gBAAgB,CAAC,CAAC;IAC9B;IACA,IAAI,CAACjK,SAAS,CAACyB,YAAY,CAAC,eAAe,EAAE8S,UAAU,CAAC/M,GAAG,CAAC9F,EAAE,IAAI,GAAGtE,gBAAgB,GAAGsE,EAAE,EAAE,CAAC,CAACoH,IAAI,CAAC,GAAG,CAAC,CAAC;IACxG,OAAO,IAAI,CAAC9I,SAAS;EACzB;AACJ;AACA,MAAMmU,YAAY,CAAC;EACfnV,WAAWA,CAAC;IAAEgB,SAAS;IAAE2E,KAAK;IAAEuP,QAAQ;IAAE1T,QAAQ;IAAE4T,gBAAgB;IAAE3T,WAAW;IAAEC,QAAQ;IAAEX,MAAM;IAAElC,IAAI;IAAEwW,UAAU;IAAEC;EAAK,CAAC,EAAE;IAC3H,IAAIrT,EAAE;IACN5G,uBAAuB,CAAC+E,GAAG,CAAC,IAAI,CAAC;IACjC;IACA,IAAI,CAACoV,OAAO,GAAG,IAAI;IACnB;IACAla,0BAA0B,CAAC6F,GAAG,CAAC,IAAI,EAAE1D,sBAAsB,CAAC,IAAI,EAAEpC,uBAAuB,EAAE,GAAG,EAAEwB,qBAAqB,CAAC,CAAC4Y,IAAI,CAAC,IAAI,CAAC,CAAC;IAClIla,uBAAuB,CAAC4F,GAAG,CAAC,IAAI,EAAE1D,sBAAsB,CAAC,IAAI,EAAEpC,uBAAuB,EAAE,GAAG,EAAE4B,kBAAkB,CAAC,CAACwY,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5Hja,uBAAuB,CAAC2F,GAAG,CAAC,IAAI,EAAE1D,sBAAsB,CAAC,IAAI,EAAEpC,uBAAuB,EAAE,GAAG,EAAE2B,kBAAkB,CAAC,CAACyY,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5Hha,yBAAyB,CAAC0F,GAAG,CAAC,IAAI,EAAE1D,sBAAsB,CAAC,IAAI,EAAEpC,uBAAuB,EAAE,GAAG,EAAE0B,oBAAoB,CAAC,CAAC0Y,IAAI,CAAC,IAAI,CAAC,CAAC;IAChI/Z,mBAAmB,CAACyF,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IACnCxF,uBAAuB,CAACwF,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IACvCvF,yBAAyB,CAACuF,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IACzCtF,qBAAqB,CAACsF,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IACrCrF,sBAAsB,CAACqF,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IACtCpF,oBAAoB,CAACoF,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IACpCnF,wBAAwB,CAACmF,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IACxClF,oBAAoB,CAACkF,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC;IACrCjF,mBAAmB,CAACiF,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IACnChF,sBAAsB,CAACgF,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IACtC/E,kBAAkB,CAAC+E,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAClC9E,sBAAsB,CAAC8E,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IACtC7E,sBAAsB,CAAC6E,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IACtC5E,qBAAqB,CAAC4E,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IACrC3E,wBAAwB,CAAC2E,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC;IACzCzD,sBAAsB,CAAC,IAAI,EAAE/B,uBAAuB,EAAEqF,SAAS,EAAE,GAAG,CAAC;IACrEtD,sBAAsB,CAAC,IAAI,EAAEpB,sBAAsB,EAAEkF,QAAQ,EAAE,GAAG,CAAC;IACnE9D,sBAAsB,CAAC,IAAI,EAAE9B,yBAAyB,EAAE6F,WAAW,EAAE,GAAG,CAAC;IACzE/D,sBAAsB,CAAC,IAAI,EAAErB,sBAAsB,EAAEqF,QAAQ,EAAE,GAAG,CAAC;IACnEhE,sBAAsB,CAAC,IAAI,EAAE3B,oBAAoB,EAAEgF,MAAM,EAAE,GAAG,CAAC;IAC/DrD,sBAAsB,CAAC,IAAI,EAAEhC,mBAAmB,EAAEiK,KAAK,EAAE,GAAG,CAAC;IAC7DjI,sBAAsB,CAAC,IAAI,EAAEtB,kBAAkB,EAAEyC,IAAI,EAAE,GAAG,CAAC;IAC3DnB,sBAAsB,CAAC,IAAI,EAAE1B,wBAAwB,EAAEqZ,UAAU,EAAE,GAAG,CAAC;IACvE3X,sBAAsB,CAAC,IAAI,EAAE5B,sBAAsB,EAAEoZ,QAAQ,EAAE,GAAG,CAAC;IACnE;IACA;IACA;IACAxX,sBAAsB,CAAC,IAAI,EAAE7B,qBAAqB,EAAEiC,aAAa,CAAC4X,YAAY,CAACN,gBAAgB,CAAC,EAAE,GAAG,CAAC;IACtG,IAAI,CAACI,OAAO,GAAGN,QAAQ,CAACS,OAAO,CAACC,CAAC,IAAIA,CAAC,CAAC5K,yBAAyB,CAAC,CAAC,CAAC;IACnE;IACA,KAAK,MAAMnD,OAAO,IAAI,IAAI,CAAC2N,OAAO,EAAE;MAChC3N,OAAO,CAAC2D,gBAAgB,CAAC,OAAO,EAAE/N,sBAAsB,CAAC,IAAI,EAAEhC,yBAAyB,EAAE,GAAG,CAAC,CAAC;MAC/FoM,OAAO,CAAC2D,gBAAgB,CAAC,YAAY,EAAE/N,sBAAsB,CAAC,IAAI,EAAEjC,uBAAuB,EAAE,GAAG,CAAC,CAAC;MAClGqM,OAAO,CAAC2D,gBAAgB,CAAC,YAAY,EAAE/N,sBAAsB,CAAC,IAAI,EAAElC,uBAAuB,EAAE,GAAG,CAAC,CAAC;MAClGsM,OAAO,CAACsF,SAAS,CAAC/M,GAAG,CAAC,kBAAkB,CAAC;IAC7C;IACA;IACA,KAAK,MAAMyH,OAAO,IAAIqN,QAAQ,EAAE;MAC5B,CAACjT,EAAE,GAAG4F,OAAO,CAAC7G,SAAS,MAAM,IAAI,IAAIiB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuJ,gBAAgB,CAAC,SAAS,EAAE/N,sBAAsB,CAAC,IAAI,EAAEnC,0BAA0B,EAAE,GAAG,CAAC,CAAC;IAC/J;IACAmC,sBAAsB,CAAC,IAAI,EAAE9B,uBAAuB,EAAE,GAAG,CAAC,CAACuK,MAAM,GAAG,IAAI;IACxE,IAAIoP,IAAI,EAAE;MACN7X,sBAAsB,CAAC,IAAI,EAAEpC,uBAAuB,EAAE,GAAG,EAAE0B,oBAAoB,CAAC,CAACoF,IAAI,CAAC,IAAI,CAAC;IAC/F;IACA;IACA;IACA;IACA1E,sBAAsB,CAAC,IAAI,EAAE1B,oBAAoB,EAAE,GAAG,CAAC,CAAC8Z,SAAS,CAACjM,IAAI,CAAC,MAAMpM,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MACtH,IAAIC,sBAAsB,CAAC,IAAI,EAAE9B,uBAAuB,EAAE,GAAG,CAAC,CAACuK,MAAM,EAAE;QACnEzI,sBAAsB,CAAC,IAAI,EAAEpC,uBAAuB,EAAE,GAAG,EAAE2B,kBAAkB,CAAC,CAACmF,IAAI,CAAC,IAAI,CAAC;MAC7F;IACJ,CAAC,CAAC,CAAC;IACH;EACJ;EACA6H,MAAMA,CAAA,EAAG;IACL,IAAIvM,sBAAsB,CAAC,IAAI,EAAEvB,mBAAmB,EAAE,GAAG,CAAC,EAAE;MACxD;IACJ;IACA,MAAM+E,KAAK,GAAIvD,sBAAsB,CAAC,IAAI,EAAExB,mBAAmB,EAAEqG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,EAAE,GAAG,CAAE;IACrGvB,KAAK,CAAC6U,SAAS,GAAG,OAAO;IACzB,IAAIrY,sBAAsB,CAAC,IAAI,EAAE/B,mBAAmB,EAAE,GAAG,CAAC,EAAE;MACxD,MAAMqa,SAAS,GAAI9U,KAAK,CAAC4B,KAAK,CAACmT,YAAY,GAAG/X,IAAI,CAACwG,YAAY;MAC/D;MACA,GAAGhH,sBAAsB,CAAC,IAAI,EAAE/B,mBAAmB,EAAE,GAAG,CAAC,CAAE;MAC3D;MACA;MACAua,GAAG,CAACC,QAAQ,CAAC,kBAAkB,EAAE,oCAAoC,CAAC,EAAE;QACpEjV,KAAK,CAAC4B,KAAK,CAACkO,eAAe,GAAG,sBAAsBgF,SAAS,cAAc;MAC/E,CAAC,MACI;QACD;QACA;QACA;QACA;QACA,MAAMI,kBAAkB,GAAG,GAAG;QAC9BlV,KAAK,CAAC4B,KAAK,CAACkO,eAAe,GAAG9S,IAAI,CAACwG,YAAY;QAC/C;QACA,GAAGhH,sBAAsB,CAAC,IAAI,EAAE/B,mBAAmB,EAAE,GAAG,CAAC,CAAC8M,GAAG,CAAC4N,CAAC,IAAI1N,IAAI,CAAC2N,KAAK,CAACF,kBAAkB,IAAI,GAAG,GAAGC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC;MACvH;IACJ;IACA,MAAME,MAAM,GAAG/T,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IAC7C8T,MAAM,CAACR,SAAS,GAAG,QAAQ;IAC3B,MAAM5S,KAAK,GAAGX,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;IAC1C8T,MAAM,CAACjN,MAAM,CAACnG,KAAK,CAAC;IACpB,CAAC;MAAEqT,GAAG,EAAErT,KAAK,CAACqT,GAAG;MAAE5U,GAAG,EAAEuB,KAAK,CAACqP;IAAY,CAAC,GAAG9U,sBAAsB,CAAC,IAAI,EAAEnB,sBAAsB,EAAE,GAAG,CAAC;IACvG2E,KAAK,CAACoI,MAAM,CAACiN,MAAM,CAAC;IACpB,IAAI7Y,sBAAsB,CAAC,IAAI,EAAE5B,qBAAqB,EAAE,GAAG,CAAC,EAAE;MAC1D,MAAMuZ,gBAAgB,GAAG7S,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;MACvD4S,gBAAgB,CAACjI,SAAS,CAAC/M,GAAG,CAAC,WAAW,CAAC;MAC3CgV,gBAAgB,CAAC3S,YAAY,CAAC,cAAc,EAAE,8BAA8B,CAAC;MAC7E2S,gBAAgB,CAAC3S,YAAY,CAAC,gBAAgB,EAAE+T,IAAI,CAACC,SAAS,CAAC;QAC3DC,IAAI,EAAEjZ,sBAAsB,CAAC,IAAI,EAAE5B,qBAAqB,EAAE,GAAG,CAAC,CAAC8a,kBAAkB,CAAC,CAAC;QACnFC,IAAI,EAAEnZ,sBAAsB,CAAC,IAAI,EAAE5B,qBAAqB,EAAE,GAAG,CAAC,CAACgb,kBAAkB,CAAC;MACtF,CAAC,CAAC,CAAC;MACHP,MAAM,CAACjN,MAAM,CAAC+L,gBAAgB,CAAC;IACnC;IACA,MAAM0B,IAAI,GAAGrZ,sBAAsB,CAAC,IAAI,EAAEpC,uBAAuB,EAAE,GAAG,EAAEoB,sBAAsB,CAAC;IAC/F,IAAIqa,IAAI,EAAE;MACN;MACA5Y,QAAQ,CAAC8L,MAAM,CAAC;QACZ+M,OAAO,EAAED,IAAI;QACbE,MAAM,EAAE,UAAU;QAClBC,GAAG,EAAEhW;MACT,CAAC,CAAC;MACF;MACAA,KAAK,CAACiW,SAAS,CAAC/J,SAAS,CAAC/M,GAAG,CAAC,UAAU,EAAE,cAAc,CAAC;IAC7D,CAAC,MACI;MACD,MAAM+W,QAAQ,GAAG,IAAI,CAACC,eAAe,CAAC3Z,sBAAsB,CAAC,IAAI,EAAE7B,yBAAyB,EAAE,GAAG,CAAC,CAAC;MACnGqF,KAAK,CAACoI,MAAM,CAAC8N,QAAQ,CAAC;IAC1B;IACA1Z,sBAAsB,CAAC,IAAI,EAAE9B,uBAAuB,EAAE,GAAG,CAAC,CAAC0N,MAAM,CAACpI,KAAK,CAAC;EAC5E;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACImW,eAAeA,CAAC;IAAEzV,GAAG;IAAE4U;EAAI,CAAC,EAAE;IAC1B,MAAMc,CAAC,GAAG9U,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrC6U,CAAC,CAAClK,SAAS,CAAC/M,GAAG,CAAC,cAAc,CAAC;IAC/BiX,CAAC,CAACd,GAAG,GAAGA,GAAG;IACX,MAAMe,KAAK,GAAG3V,GAAG,CAAC4V,KAAK,CAAC,cAAc,CAAC;IACvC,KAAK,IAAIhO,CAAC,GAAG,CAAC,EAAEC,EAAE,GAAG8N,KAAK,CAAC1O,MAAM,EAAEW,CAAC,GAAGC,EAAE,EAAE,EAAED,CAAC,EAAE;MAC5C,MAAMiO,IAAI,GAAGF,KAAK,CAAC/N,CAAC,CAAC;MACrB8N,CAAC,CAAChO,MAAM,CAAC9G,QAAQ,CAACkV,cAAc,CAACD,IAAI,CAAC,CAAC;MACvC,IAAIjO,CAAC,GAAGC,EAAE,GAAG,CAAC,EAAE;QACZ6N,CAAC,CAAChO,MAAM,CAAC9G,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC,CAAC;MAC1C;IACJ;IACA,OAAO6U,CAAC;EACZ;EACAtV,YAAYA,CAAC;IAAElD,IAAI;IAAE6Y;EAAa,CAAC,EAAE;IACjC,IAAIzV,EAAE;IACNvE,sBAAsB,CAAC,IAAI,EAAEnB,qBAAqB,EAAEkB,sBAAsB,CAAC,IAAI,EAAElB,qBAAqB,EAAE,GAAG,CAAC,IAAI;MAC5GkF,WAAW,EAAEhE,sBAAsB,CAAC,IAAI,EAAE7B,yBAAyB,EAAE,GAAG,CAAC;MACzE8F,QAAQ,EAAEjE,sBAAsB,CAAC,IAAI,EAAEpB,sBAAsB,EAAE,GAAG;IACtE,CAAC,EAAE,GAAG,CAAC;IACP,IAAIwC,IAAI,EAAE;MACNnB,sBAAsB,CAAC,IAAI,EAAEvB,sBAAsB,EAAE,IAAI,EAAE,GAAG,CAAC;IACnE;IACA,IAAIub,YAAY,EAAE;MACdha,sBAAsB,CAAC,IAAI,EAAErB,sBAAsB,EAAEoB,sBAAsB,CAAC,IAAI,EAAEpC,uBAAuB,EAAE,GAAG,EAAEuB,8BAA8B,CAAC,CAACuF,IAAI,CAAC,IAAI,EAAEuV,YAAY,CAAC,EAAE,GAAG,CAAC;MAC9Kha,sBAAsB,CAAC,IAAI,EAAE9B,yBAAyB,EAAE,IAAI,EAAE,GAAG,CAAC;IACtE;IACA,CAACqG,EAAE,GAAGxE,sBAAsB,CAAC,IAAI,EAAEvB,mBAAmB,EAAE,GAAG,CAAC,MAAM,IAAI,IAAI+F,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0V,MAAM,CAAC,CAAC;IAC9Gja,sBAAsB,CAAC,IAAI,EAAExB,mBAAmB,EAAE,IAAI,EAAE,GAAG,CAAC;EAChE;EACAkG,WAAWA,CAAA,EAAG;IACV,IAAIH,EAAE;IACN,IAAIwJ,EAAE,EAAEkF,EAAE;IACV,IAAI,CAAClT,sBAAsB,CAAC,IAAI,EAAElB,qBAAqB,EAAE,GAAG,CAAC,EAAE;MAC3D;IACJ;IACCkP,EAAE,GAAG,IAAI,EAAEkF,EAAE,GAAG,IAAI,EAAE;MAAElP,WAAW,EAAG;QAAE,IAAIkN,KAAKA,CAAC1M,EAAE,EAAE;UAAEvE,sBAAsB,CAAC+N,EAAE,EAAE7P,yBAAyB,EAAEqG,EAAE,EAAE,GAAG,CAAC;QAAE;MAAE,CAAC,CAAE0M,KAAK;MAAEjN,QAAQ,EAAG;QAAE,IAAIiN,KAAKA,CAAC1M,EAAE,EAAE;UAAEvE,sBAAsB,CAACiT,EAAE,EAAEtU,sBAAsB,EAAE4F,EAAE,EAAE,GAAG,CAAC;QAAE;MAAE,CAAC,CAAE0M;IAAM,CAAC,GACvOlR,sBAAsB,CAAC,IAAI,EAAElB,qBAAqB,EAAE,GAAG,CAAC;IAC5DmB,sBAAsB,CAAC,IAAI,EAAEnB,qBAAqB,EAAE,IAAI,EAAE,GAAG,CAAC;IAC9D,CAAC0F,EAAE,GAAGxE,sBAAsB,CAAC,IAAI,EAAEvB,mBAAmB,EAAE,GAAG,CAAC,MAAM,IAAI,IAAI+F,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0V,MAAM,CAAC,CAAC;IAC9Gja,sBAAsB,CAAC,IAAI,EAAExB,mBAAmB,EAAE,IAAI,EAAE,GAAG,CAAC;IAC5DwB,sBAAsB,CAAC,IAAI,EAAEvB,sBAAsB,EAAE,IAAI,EAAE,GAAG,CAAC;EACnE;EACA4O,SAASA,CAAA,EAAG;IACRrN,sBAAsB,CAAC,IAAI,EAAElB,wBAAwB,EAAE,IAAI,CAACob,SAAS,EAAE,GAAG,CAAC;IAC3E,IAAI,CAACna,sBAAsB,CAAC,IAAI,EAAEjB,wBAAwB,EAAE,GAAG,CAAC,EAAE;MAC9D;IACJ;IACAiB,sBAAsB,CAAC,IAAI,EAAE9B,uBAAuB,EAAE,GAAG,CAAC,CAACuK,MAAM,GAAG,IAAI;EAC5E;EACA2E,SAASA,CAAA,EAAG;IACR,IAAI,CAACpN,sBAAsB,CAAC,IAAI,EAAEjB,wBAAwB,EAAE,GAAG,CAAC,EAAE;MAC9D;IACJ;IACA,IAAI,CAACiB,sBAAsB,CAAC,IAAI,EAAEvB,mBAAmB,EAAE,GAAG,CAAC,EAAE;MACzDuB,sBAAsB,CAAC,IAAI,EAAEpC,uBAAuB,EAAE,GAAG,EAAE2B,kBAAkB,CAAC,CAACmF,IAAI,CAAC,IAAI,CAAC;IAC7F;IACAzE,sBAAsB,CAAC,IAAI,EAAElB,wBAAwB,EAAE,KAAK,EAAE,GAAG,CAAC;IAClEiB,sBAAsB,CAAC,IAAI,EAAE9B,uBAAuB,EAAE,GAAG,CAAC,CAACuK,MAAM,GAAG,KAAK;EAC7E;EACA,IAAI0R,SAASA,CAAA,EAAG;IACZ,OAAOna,sBAAsB,CAAC,IAAI,EAAE9B,uBAAuB,EAAE,GAAG,CAAC,CAACuK,MAAM,KAAK,KAAK;EACtF;AACJ;AACA5K,0BAA0B,GAAG,IAAIyQ,OAAO,CAAC,CAAC,EAAExQ,uBAAuB,GAAG,IAAIwQ,OAAO,CAAC,CAAC,EAAEvQ,uBAAuB,GAAG,IAAIuQ,OAAO,CAAC,CAAC,EAAEtQ,yBAAyB,GAAG,IAAIsQ,OAAO,CAAC,CAAC,EAAErQ,mBAAmB,GAAG,IAAIqQ,OAAO,CAAC,CAAC,EAAEpQ,uBAAuB,GAAG,IAAIoQ,OAAO,CAAC,CAAC,EAAEnQ,yBAAyB,GAAG,IAAImQ,OAAO,CAAC,CAAC,EAAElQ,qBAAqB,GAAG,IAAIkQ,OAAO,CAAC,CAAC,EAAEjQ,sBAAsB,GAAG,IAAIiQ,OAAO,CAAC,CAAC,EAAEhQ,oBAAoB,GAAG,IAAIgQ,OAAO,CAAC,CAAC,EAAE/P,wBAAwB,GAAG,IAAI+P,OAAO,CAAC,CAAC,EAAE9P,oBAAoB,GAAG,IAAI8P,OAAO,CAAC,CAAC,EAAE7P,mBAAmB,GAAG,IAAI6P,OAAO,CAAC,CAAC,EAAE5P,sBAAsB,GAAG,IAAI4P,OAAO,CAAC,CAAC,EAAE3P,kBAAkB,GAAG,IAAI2P,OAAO,CAAC,CAAC,EAAE1P,sBAAsB,GAAG,IAAI0P,OAAO,CAAC,CAAC,EAAEzP,sBAAsB,GAAG,IAAIyP,OAAO,CAAC,CAAC,EAAExP,qBAAqB,GAAG,IAAIwP,OAAO,CAAC,CAAC,EAAEvP,wBAAwB,GAAG,IAAIuP,OAAO,CAAC,CAAC,EAAE1Q,uBAAuB,GAAG,IAAIsD,OAAO,CAAC,CAAC,EAAElC,sBAAsB,GAAG,SAASA,sBAAsBA,CAAA,EAAG;EACz1B,MAAMiF,QAAQ,GAAGjE,sBAAsB,CAAC,IAAI,EAAEpB,sBAAsB,EAAE,GAAG,CAAC;EAC1E,MAAMoF,WAAW,GAAGhE,sBAAsB,CAAC,IAAI,EAAE7B,yBAAyB,EAAE,GAAG,CAAC;EAChF,IAAI,CAAC8F,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACC,GAAG,MAChE,EAAEF,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACE,GAAG,CAAC,IAAIF,WAAW,CAACE,GAAG,KAAKD,QAAQ,CAACC,GAAG,CAAC,EAAE;IACpH,OAAOlE,sBAAsB,CAAC,IAAI,EAAEpB,sBAAsB,EAAE,GAAG,CAAC,CAACya,IAAI,IAAI,IAAI;EACjF;EACA,OAAO,IAAI;AACf,CAAC,EAAEpa,0BAA0B,GAAG,SAASA,0BAA0BA,CAAA,EAAG;EAClE,IAAIuF,EAAE,EAAEwJ,EAAE,EAAEkF,EAAE;EACd,OAAO,CAAC,CAACA,EAAE,GAAG,CAAClF,EAAE,GAAG,CAACxJ,EAAE,GAAGxE,sBAAsB,CAAC,IAAI,EAAEpC,uBAAuB,EAAE,GAAG,EAAEoB,sBAAsB,CAAC,MAAM,IAAI,IAAIwF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4V,UAAU,MAAM,IAAI,IAAIpM,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC5I,KAAK,MAAM,IAAI,IAAI8N,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACS,QAAQ,KAAK,CAAC;AAC3Q,CAAC,EAAEzU,2BAA2B,GAAG,SAASA,2BAA2BA,CAAA,EAAG;EACpE,IAAIsF,EAAE,EAAEwJ,EAAE,EAAEkF,EAAE;EACd,OAAO,CAAC,CAACA,EAAE,GAAG,CAAClF,EAAE,GAAG,CAACxJ,EAAE,GAAGxE,sBAAsB,CAAC,IAAI,EAAEpC,uBAAuB,EAAE,GAAG,EAAEoB,sBAAsB,CAAC,MAAM,IAAI,IAAIwF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4V,UAAU,MAAM,IAAI,IAAIpM,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC5I,KAAK,MAAM,IAAI,IAAI8N,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAChL,KAAK,KAAK,IAAI;AAC3Q,CAAC,EAAE/I,8BAA8B,GAAG,SAASA,8BAA8BA,CAACkb,IAAI,EAAE;EAC9E,MAAMC,UAAU,GAAG,EAAE;EACrB,MAAML,YAAY,GAAG;IACjB/V,GAAG,EAAEmW,IAAI;IACThB,IAAI,EAAE;MACFtP,IAAI,EAAE,KAAK;MACXqQ,UAAU,EAAE;QACRtB,GAAG,EAAE;MACT,CAAC;MACDyB,QAAQ,EAAE,CACN;QACIxQ,IAAI,EAAE,GAAG;QACTwQ,QAAQ,EAAED;MACd,CAAC;IAET;EACJ,CAAC;EACD,MAAME,cAAc,GAAG;IACnBpV,KAAK,EAAE;MACH8C,KAAK,EAAElI,sBAAsB,CAAC,IAAI,EAAEpC,uBAAuB,EAAE,GAAG,EAAEsB,2BAA2B,CAAC;MAC9FyU,QAAQ,EAAE3T,sBAAsB,CAAC,IAAI,EAAEpC,uBAAuB,EAAE,GAAG,EAAEqB,0BAA0B,CAAC,GAC1F,QAAQe,sBAAsB,CAAC,IAAI,EAAEpC,uBAAuB,EAAE,GAAG,EAAEqB,0BAA0B,CAAC,2BAA2B,GACzH;IACV;EACJ,CAAC;EACD,KAAK,MAAM8a,IAAI,IAAIM,IAAI,CAACP,KAAK,CAAC,IAAI,CAAC,EAAE;IACjCQ,UAAU,CAACnO,IAAI,CAAC;MACZpC,IAAI,EAAE,MAAM;MACZmH,KAAK,EAAE6I,IAAI;MACXK,UAAU,EAAEI;IAChB,CAAC,CAAC;EACN;EACA,OAAOP,YAAY;AACvB,CAAC,EAAE7a,qBAAqB,GAAG,SAASA,qBAAqBA,CAAC6I,KAAK,EAAE;EAC7D,IAAIA,KAAK,CAACwS,MAAM,IAAIxS,KAAK,CAAC0K,QAAQ,IAAI1K,KAAK,CAACkK,OAAO,IAAIlK,KAAK,CAACiK,OAAO,EAAE;IAClE;EACJ;EACA,IAAIjK,KAAK,CAAC0M,GAAG,KAAK,OAAO,IAAK1M,KAAK,CAAC0M,GAAG,KAAK,QAAQ,IAAI3U,sBAAsB,CAAC,IAAI,EAAExB,oBAAoB,EAAE,GAAG,CAAE,EAAE;IAC9GwB,sBAAsB,CAAC,IAAI,EAAEpC,uBAAuB,EAAE,GAAG,EAAE0B,oBAAoB,CAAC,CAACoF,IAAI,CAAC,IAAI,CAAC;EAC/F;AACJ,CAAC,EAAErF,yBAAyB,GAAG,SAASA,yBAAyBA,CAAA,EAAG;EAChE,IAAIW,sBAAsB,CAAC,IAAI,EAAEtB,sBAAsB,EAAE,GAAG,CAAC,KAAK,IAAI,EAAE;IACpE;EACJ;EACA,MAAM;IAAEkG,IAAI,EAAE;MAAEsC;IAAK,CAAC;IAAErC,QAAQ,EAAE;MAAE0C,OAAO,EAAE;QAAEJ,SAAS;QAAEC,UAAU;QAAEC,KAAK;QAAEC;MAAM;IAAE;EAAE,CAAC,GAAGtH,sBAAsB,CAAC,IAAI,EAAE1B,oBAAoB,EAAE,GAAG,CAAC;EAClJ,IAAIoc,aAAa,GAAG,CAAC,CAAC1a,sBAAsB,CAAC,IAAI,EAAEzB,wBAAwB,EAAE,GAAG,CAAC;EACjF,IAAI6C,IAAI,GAAGsZ,aAAa,GAAG1a,sBAAsB,CAAC,IAAI,EAAEzB,wBAAwB,EAAE,GAAG,CAAC,GAAGyB,sBAAsB,CAAC,IAAI,EAAErB,kBAAkB,EAAE,GAAG,CAAC;EAC9I,KAAK,MAAMyL,OAAO,IAAIpK,sBAAsB,CAAC,IAAI,EAAE3B,sBAAsB,EAAE,GAAG,CAAC,EAAE;IAC7E,IAAI,CAAC+C,IAAI,IAAIZ,IAAI,CAACma,SAAS,CAACvQ,OAAO,CAACzI,IAAI,CAACP,IAAI,EAAEA,IAAI,CAAC,KAAK,IAAI,EAAE;MAC3DA,IAAI,GAAGgJ,OAAO,CAACzI,IAAI,CAACP,IAAI;MACxBsZ,aAAa,GAAG,IAAI;MACpB;IACJ;EACJ;EACA,MAAME,cAAc,GAAGpa,IAAI,CAACyG,aAAa,CAAC,CACtC7F,IAAI,CAAC,CAAC,CAAC,EACP8F,IAAI,CAAC,CAAC,CAAC,GAAG9F,IAAI,CAAC,CAAC,CAAC,GAAG8F,IAAI,CAAC,CAAC,CAAC,EAC3B9F,IAAI,CAAC,CAAC,CAAC,EACP8F,IAAI,CAAC,CAAC,CAAC,GAAG9F,IAAI,CAAC,CAAC,CAAC,GAAG8F,IAAI,CAAC,CAAC,CAAC,CAC9B,CAAC;EACF,MAAM2T,iCAAiC,GAAG,CAAC;EAC3C,MAAMC,WAAW,GAAGJ,aAAa,GAC3BtZ,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAGyZ,iCAAiC,GACrD,CAAC;EACP,MAAME,SAAS,GAAGH,cAAc,CAAC,CAAC,CAAC,GAAGE,WAAW;EACjD,MAAME,QAAQ,GAAGJ,cAAc,CAAC,CAAC,CAAC;EAClC3a,sBAAsB,CAAC,IAAI,EAAEvB,sBAAsB,EAAE,CAChD,GAAG,IAAIqc,SAAS,GAAG1T,KAAK,CAAC,GAAIF,SAAS,EACtC,GAAG,IAAI6T,QAAQ,GAAG1T,KAAK,CAAC,GAAIF,UAAU,CAC1C,EAAE,GAAG,CAAC;EACP,MAAM;IAAEhC;EAAM,CAAC,GAAGpF,sBAAsB,CAAC,IAAI,EAAE9B,uBAAuB,EAAE,GAAG,CAAC;EAC5EkH,KAAK,CAACoC,IAAI,GAAG,GAAGxH,sBAAsB,CAAC,IAAI,EAAEtB,sBAAsB,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG;EAC/E0G,KAAK,CAACqC,GAAG,GAAG,GAAGzH,sBAAsB,CAAC,IAAI,EAAEtB,sBAAsB,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG;AAClF,CAAC,EAAEY,oBAAoB,GAAG,SAASA,oBAAoBA,CAAA,EAAG;EACtDW,sBAAsB,CAAC,IAAI,EAAEzB,oBAAoB,EAAE,CAACwB,sBAAsB,CAAC,IAAI,EAAExB,oBAAoB,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC;EACjH,IAAIwB,sBAAsB,CAAC,IAAI,EAAExB,oBAAoB,EAAE,GAAG,CAAC,EAAE;IACzDwB,sBAAsB,CAAC,IAAI,EAAEpC,uBAAuB,EAAE,GAAG,EAAE2B,kBAAkB,CAAC,CAACmF,IAAI,CAAC,IAAI,CAAC;IACzF1E,sBAAsB,CAAC,IAAI,EAAE9B,uBAAuB,EAAE,GAAG,CAAC,CAAC6P,gBAAgB,CAAC,OAAO,EAAE/N,sBAAsB,CAAC,IAAI,EAAEhC,yBAAyB,EAAE,GAAG,CAAC,CAAC;IAClJgC,sBAAsB,CAAC,IAAI,EAAE9B,uBAAuB,EAAE,GAAG,CAAC,CAAC6P,gBAAgB,CAAC,SAAS,EAAE/N,sBAAsB,CAAC,IAAI,EAAEnC,0BAA0B,EAAE,GAAG,CAAC,CAAC;EACzJ,CAAC,MACI;IACDmC,sBAAsB,CAAC,IAAI,EAAEpC,uBAAuB,EAAE,GAAG,EAAE4B,kBAAkB,CAAC,CAACkF,IAAI,CAAC,IAAI,CAAC;IACzF1E,sBAAsB,CAAC,IAAI,EAAE9B,uBAAuB,EAAE,GAAG,CAAC,CAAC+c,mBAAmB,CAAC,OAAO,EAAEjb,sBAAsB,CAAC,IAAI,EAAEhC,yBAAyB,EAAE,GAAG,CAAC,CAAC;IACrJgC,sBAAsB,CAAC,IAAI,EAAE9B,uBAAuB,EAAE,GAAG,CAAC,CAAC+c,mBAAmB,CAAC,SAAS,EAAEjb,sBAAsB,CAAC,IAAI,EAAEnC,0BAA0B,EAAE,GAAG,CAAC,CAAC;EAC5J;AACJ,CAAC,EAAE0B,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;EAClD,IAAI,CAACS,sBAAsB,CAAC,IAAI,EAAEvB,mBAAmB,EAAE,GAAG,CAAC,EAAE;IACzD,IAAI,CAAC8N,MAAM,CAAC,CAAC;EACjB;EACA,IAAI,CAAC,IAAI,CAAC4N,SAAS,EAAE;IACjBna,sBAAsB,CAAC,IAAI,EAAEpC,uBAAuB,EAAE,GAAG,EAAEyB,yBAAyB,CAAC,CAACqF,IAAI,CAAC,IAAI,CAAC;IAChG1E,sBAAsB,CAAC,IAAI,EAAE9B,uBAAuB,EAAE,GAAG,CAAC,CAACuK,MAAM,GAAG,KAAK;IACzEzI,sBAAsB,CAAC,IAAI,EAAE9B,uBAAuB,EAAE,GAAG,CAAC,CAACkH,KAAK,CAACC,MAAM,GAAG,CAAC6V,QAAQ,CAAClb,sBAAsB,CAAC,IAAI,EAAE9B,uBAAuB,EAAE,GAAG,CAAC,CAACkH,KAAK,CAACC,MAAM,EAAE,EAAE,CAAC,GAAG,IAAI,EAAEC,QAAQ,CAAC,CAAC;EACvL,CAAC,MACI,IAAItF,sBAAsB,CAAC,IAAI,EAAExB,oBAAoB,EAAE,GAAG,CAAC,EAAE;IAC9DwB,sBAAsB,CAAC,IAAI,EAAE9B,uBAAuB,EAAE,GAAG,CAAC,CAACwR,SAAS,CAAC/M,GAAG,CAAC,SAAS,CAAC;EACvF;AACJ,CAAC,EAAEnD,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;EAClDQ,sBAAsB,CAAC,IAAI,EAAE9B,uBAAuB,EAAE,GAAG,CAAC,CAACwR,SAAS,CAACwK,MAAM,CAAC,SAAS,CAAC;EACtF,IAAIla,sBAAsB,CAAC,IAAI,EAAExB,oBAAoB,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC2b,SAAS,EAAE;IAC5E;EACJ;EACAna,sBAAsB,CAAC,IAAI,EAAE9B,uBAAuB,EAAE,GAAG,CAAC,CAACuK,MAAM,GAAG,IAAI;EACxEzI,sBAAsB,CAAC,IAAI,EAAE9B,uBAAuB,EAAE,GAAG,CAAC,CAACkH,KAAK,CAACC,MAAM,GAAG,CAAC6V,QAAQ,CAAClb,sBAAsB,CAAC,IAAI,EAAE9B,uBAAuB,EAAE,GAAG,CAAC,CAACkH,KAAK,CAACC,MAAM,EAAE,EAAE,CAAC,GAAG,IAAI,EAAEC,QAAQ,CAAC,CAAC;AACvL,CAAC;AACD,MAAMnD,yBAAyB,SAASG,iBAAiB,CAAC;EACtD;EACAC,WAAWA,CAACd,UAAU,EAAE;IACpB,KAAK,CAACA,UAAU,EAAE;MAAEe,YAAY,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAK,CAAC,CAAC;IAC7D;IACA,IAAI,CAACqS,WAAW,GAAG,IAAI;IACvB,IAAI,CAACqG,YAAY,GAAG,IAAI;IACxB,IAAI,CAACrG,WAAW,GAAGrT,UAAU,CAACE,IAAI,CAACmT,WAAW;IAC9C,IAAI,CAACqG,YAAY,GAAG1Z,UAAU,CAACE,IAAI,CAACwZ,YAAY;IAChD,IAAI,CAAC1X,oBAAoB,GAAGvD,oBAAoB,CAACgC,QAAQ;EAC7D;EACAqK,MAAMA,CAAA,EAAG;IACL;IACA;IACA,IAAI,CAAChJ,SAAS,CAACmM,SAAS,CAAC/M,GAAG,CAAC,wBAAwB,CAAC;IACtD,IAAI,IAAI,CAACmS,WAAW,EAAE;MAClB,MAAMrD,OAAO,GAAG3M,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC7C;MACA0M,OAAO,CAAC/B,SAAS,CAAC/M,GAAG,CAAC,2BAA2B,CAAC;MAClD8O,OAAO,CAACzM,YAAY,CAAC,MAAM,EAAE,SAAS,CAAC;MACvC,KAAK,MAAM+U,IAAI,IAAI,IAAI,CAACjF,WAAW,EAAE;QACjC,MAAMsG,QAAQ,GAAGtW,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;QAC/CqW,QAAQ,CAACtG,WAAW,GAAGiF,IAAI;QAC3BtI,OAAO,CAAC7F,MAAM,CAACwP,QAAQ,CAAC;MAC5B;MACA,IAAI,CAAC7X,SAAS,CAACqI,MAAM,CAAC6F,OAAO,CAAC;IAClC;IACA;IACA;IACA;IACA,IAAI,CAAC7D,kBAAkB,CAAC,CAAC;IACzB,OAAO,IAAI,CAACrK,SAAS;EACzB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMlB,0BAA0B,SAASC,iBAAiB,CAAC;EACvDC,WAAWA,CAACd,UAAU,EAAE;IACpB,KAAK,CAACA,UAAU,EAAE;MACde,YAAY,EAAE,IAAI;MAClBC,YAAY,EAAE,IAAI;MAClBC,oBAAoB,EAAE;IAC1B,CAAC,CAAC;EACN;EACA6J,MAAMA,CAAA,EAAG;IACL;IACA;IACA;IACA;IACA,IAAI,CAAChJ,SAAS,CAACmM,SAAS,CAAC/M,GAAG,CAAC,wBAAwB,CAAC;IACtD,OAAO,IAAI,CAACY,SAAS;EACzB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8X,eAAe,CAAC;EAClB9Y,WAAWA,CAAC;IAAEiX,GAAG;IAAE8B,oBAAoB;IAAEC,mBAAmB;IAAEC,yBAAyB;IAAE5W,IAAI;IAAEC;EAAS,CAAC,EAAE;IACvGpF,0BAA0B,CAACkD,GAAG,CAAC,IAAI,CAAC;IACpC,IAAI,CAAC6W,GAAG,GAAG,IAAI;IACf,IAAI,CAAC5U,IAAI,GAAG,IAAI;IAChB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACQ,MAAM,GAAG,IAAI;IAClB,IAAI,CAACoW,0BAA0B,GAAG,IAAI;IACtC/b,qCAAqC,CAACgE,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IACrD/D,oCAAoC,CAAC+D,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IACpD9D,oCAAoC,CAAC8D,GAAG,CAAC,IAAI,EAAE,IAAIwM,GAAG,CAAC,CAAC,CAAC;IACzD,IAAI,CAACsJ,GAAG,GAAGA,GAAG;IACdvZ,sBAAsB,CAAC,IAAI,EAAEP,qCAAqC,EAAE4b,oBAAoB,EAAE,GAAG,CAAC;IAC9Frb,sBAAsB,CAAC,IAAI,EAAEN,oCAAoC,EAAE4b,mBAAmB,EAAE,GAAG,CAAC;IAC5F,IAAI,CAAC3W,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACQ,MAAM,GAAG,CAAC;IACf,IAAI,CAACoW,0BAA0B,GAAGD,yBAAyB;IAC3D;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACJ;EACA;EACAnO,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAACmM,GAAG,EAAE;MACX;IACJ;IACA,IAAI,CAACA,GAAG,CAAC/Q,MAAM,GAAG,IAAI;EAC1B;EACA;EACAiT,sBAAsBA,CAAA,EAAG;IACrB,OAAO1b,sBAAsB,CAAC,IAAI,EAAEJ,oCAAoC,EAAE,GAAG,CAAC,CAAC+b,IAAI,GAAG,CAAC;EAC3F;EACA;AACJ;AACA;AACA;AACA;AACA;EACI;EACApP,MAAMA,CAAChI,MAAM,EAAE;IACX,IAAIC,EAAE;IACN,MAAM;MAAEoX;IAAY,CAAC,GAAGrX,MAAM;IAC9B,MAAM3B,KAAK,GAAG,IAAI,CAAC4W,GAAG;IACtBlZ,kBAAkB,CAACsC,KAAK,EAAE,IAAI,CAACiC,QAAQ,CAAC;IACxC,MAAMgX,eAAe,GAAG,IAAI3L,GAAG,CAAC,CAAC;IACjC,MAAM4L,aAAa,GAAG;MAClBna,IAAI,EAAE,IAAI;MACViB,KAAK;MACLC,WAAW,EAAE0B,MAAM,CAAC1B,WAAW;MAC/BC,eAAe,EAAEyB,MAAM,CAACzB,eAAe;MACvCC,kBAAkB,EAAEwB,MAAM,CAACxB,kBAAkB,IAAI,EAAE;MACnDC,WAAW,EAAEuB,MAAM,CAACvB,WAAW,KAAK,KAAK;MACzCC,UAAU,EAAE,IAAI9C,aAAa,CAAC,CAAC;MAC/B+C,iBAAiB,EAAEqB,MAAM,CAACrB,iBAAiB;MAC3CC,eAAe,EAAEoB,MAAM,CAACpB,eAAe,KAAK,IAAI;MAChDC,YAAY,EAAEmB,MAAM,CAACnB,YAAY;MACjCO,YAAY,EAAEY,MAAM,CAACZ,YAAY;MACjCL,MAAM,EAAE,IAAI;MACZmU,QAAQ,EAAE;IACd,CAAC;IACD,KAAK,MAAM9V,IAAI,IAAIia,WAAW,EAAE;MAC5B,IAAIja,IAAI,CAACoa,MAAM,EAAE;QACb;MACJ;MACA,MAAMC,iBAAiB,GAAGra,IAAI,CAACC,cAAc,KAAKhB,cAAc,CAACqb,KAAK;MACtE,IAAI,CAACD,iBAAiB,EAAE;QACpB,MAAM;UAAE3a,KAAK;UAAEC;QAAO,CAAC,GAAGH,WAAW,CAACQ,IAAI,CAACP,IAAI,CAAC;QAChD,IAAIC,KAAK,IAAI,CAAC,IAAIC,MAAM,IAAI,CAAC,EAAE;UAC3B,SAAS,CAAC;QACd;MACJ,CAAC,MACI;QACD,MAAMmW,QAAQ,GAAGoE,eAAe,CAAC1L,GAAG,CAACxO,IAAI,CAACsD,EAAE,CAAC;QAC7C,IAAI,CAACwS,QAAQ,EAAE;UACX;UACA;QACJ;QACAqE,aAAa,CAACrE,QAAQ,GAAGA,QAAQ;MACrC;MACAqE,aAAa,CAACna,IAAI,GAAGA,IAAI;MACzB,MAAMyI,OAAO,GAAG7I,wBAAwB,CAACC,MAAM,CAACsa,aAAa,CAAC;MAC9D,IAAI,CAAC1R,OAAO,CAAC5H,YAAY,EAAE;QACvB;MACJ;MACA,IAAI,CAACwZ,iBAAiB,IAAIra,IAAI,CAAC4D,QAAQ,EAAE;QACrC,MAAMkS,QAAQ,GAAGoE,eAAe,CAAC1L,GAAG,CAACxO,IAAI,CAAC4D,QAAQ,CAAC;QACnD,IAAI,CAACkS,QAAQ,EAAE;UACXoE,eAAe,CAACnY,GAAG,CAAC/B,IAAI,CAAC4D,QAAQ,EAAE,CAAC6E,OAAO,CAAC,CAAC;QACjD,CAAC,MACI;UACDqN,QAAQ,CAACtL,IAAI,CAAC/B,OAAO,CAAC;QAC1B;MACJ;MACA,MAAM8R,QAAQ,GAAG9R,OAAO,CAACmC,MAAM,CAAC,CAAC;MACjC,IAAI5K,IAAI,CAAC8G,MAAM,EAAE;QACbyT,QAAQ,CAAC9W,KAAK,CAACsD,UAAU,GAAG,QAAQ;MACxC;MACA1I,sBAAsB,CAAC,IAAI,EAAEP,0BAA0B,EAAE,GAAG,EAAEI,8BAA8B,CAAC,CAAC6E,IAAI,CAAC,IAAI,EAAEwX,QAAQ,EAAEva,IAAI,CAACsD,EAAE,CAAC;MAC3H,IAAImF,OAAO,CAACjG,WAAW,EAAE;QACrBnE,sBAAsB,CAAC,IAAI,EAAEJ,oCAAoC,EAAE,GAAG,CAAC,CAAC8D,GAAG,CAAC0G,OAAO,CAACzI,IAAI,CAACsD,EAAE,EAAEmF,OAAO,CAAC;QACrG,CAAC5F,EAAE,GAAG,IAAI,CAACiX,0BAA0B,MAAM,IAAI,IAAIjX,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC2X,uBAAuB,CAAC/R,OAAO,CAAC;MACnH;IACJ;IACApK,sBAAsB,CAAC,IAAI,EAAEP,0BAA0B,EAAE,GAAG,EAAEK,uCAAuC,CAAC,CAAC4E,IAAI,CAAC,IAAI,CAAC;EACrH;EACA;AACJ;AACA;AACA;AACA;AACA;EACI0X,MAAMA,CAAC;IAAEvX;EAAS,CAAC,EAAE;IACjB,MAAMjC,KAAK,GAAG,IAAI,CAAC4W,GAAG;IACtB,IAAI,CAAC3U,QAAQ,GAAGA,QAAQ;IACxB;IACAvE,kBAAkB,CAACsC,KAAK,EAAEiC,QAAQ,CAAC;IACnC7E,sBAAsB,CAAC,IAAI,EAAEP,0BAA0B,EAAE,GAAG,EAAEK,uCAAuC,CAAC,CAAC4E,IAAI,CAAC,IAAI,CAAC;IACjH9B,KAAK,CAAC6F,MAAM,GAAG,KAAK;EACxB;EACA4T,sBAAsBA,CAAA,EAAG;IACrB,OAAO3O,KAAK,CAAC4O,IAAI,CAACtc,sBAAsB,CAAC,IAAI,EAAEJ,oCAAoC,EAAE,GAAG,CAAC,CAACiR,MAAM,CAAC,CAAC,CAAC;EACvG;EACA0L,qBAAqBA,CAACtX,EAAE,EAAE;IACtB,OAAOjF,sBAAsB,CAAC,IAAI,EAAEJ,oCAAoC,EAAE,GAAG,CAAC,CAACuQ,GAAG,CAAClL,EAAE,CAAC;EAC1F;AACJ;AACAvF,qCAAqC,GAAG,IAAI4O,OAAO,CAAC,CAAC,EAAE3O,oCAAoC,GAAG,IAAI2O,OAAO,CAAC,CAAC,EAAE1O,oCAAoC,GAAG,IAAI0O,OAAO,CAAC,CAAC,EAAE7O,0BAA0B,GAAG,IAAIyB,OAAO,CAAC,CAAC,EAAErB,8BAA8B,GAAG,SAASA,8BAA8BA,CAACuK,OAAO,EAAEnF,EAAE,EAAE;EACjS,IAAIT,EAAE;EACN,MAAMgY,cAAc,GAAGpS,OAAO,CAACqS,UAAU,IAAIrS,OAAO;EACpDoS,cAAc,CAACvX,EAAE,GAAG,GAAGtE,gBAAgB,GAAGsE,EAAE,EAAE;EAC9C,IAAI,CAACuU,GAAG,CAAC5N,MAAM,CAACxB,OAAO,CAAC;EACxB,CAAC5F,EAAE,GAAGxE,sBAAsB,CAAC,IAAI,EAAEN,qCAAqC,EAAE,GAAG,CAAC,MAAM,IAAI,IAAI8E,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACkY,gBAAgB,CAAC,IAAI,CAAClD,GAAG,EAAEpP,OAAO,EAAEoS,cAAc,EAC1K,mBAAoB,KAAK,CAAC;AAC9B,CAAC,EAAE1c,uCAAuC,GAAG,SAASA,uCAAuCA,CAAA,EAAG;EAC5F,IAAI,CAACE,sBAAsB,CAAC,IAAI,EAAEL,oCAAoC,EAAE,GAAG,CAAC,EAAE;IAC1E;EACJ;EACA,MAAMiD,KAAK,GAAG,IAAI,CAAC4W,GAAG;EACtB,KAAK,MAAM,CAACvU,EAAE,EAAE0X,MAAM,CAAC,IAAI3c,sBAAsB,CAAC,IAAI,EAAEL,oCAAoC,EAAE,GAAG,CAAC,EAAE;IAChG,MAAMyK,OAAO,GAAGxH,KAAK,CAACmK,aAAa,CAAC,wBAAwB9H,EAAE,IAAI,CAAC;IACnE,IAAI,CAACmF,OAAO,EAAE;MACV;IACJ;IACAuS,MAAM,CAACtE,SAAS,GAAG,wCAAwC;IAC3D,MAAM;MAAEoE;IAAW,CAAC,GAAGrS,OAAO;IAC9B,IAAI,CAACqS,UAAU,EAAE;MACbrS,OAAO,CAACwB,MAAM,CAAC+Q,MAAM,CAAC;IAC1B,CAAC,MACI,IAAIF,UAAU,CAAC3K,QAAQ,KAAK,QAAQ,EAAE;MACvC2K,UAAU,CAACG,WAAW,CAACD,MAAM,CAAC;IAClC,CAAC,MACI,IAAI,CAACF,UAAU,CAAC/M,SAAS,CAACmN,QAAQ,CAAC,mBAAmB,CAAC,IACxD,CAACJ,UAAU,CAAC/M,SAAS,CAACmN,QAAQ,CAAC,sBAAsB,CAAC,EAAE;MACxDJ,UAAU,CAACK,MAAM,CAACH,MAAM,CAAC;IAC7B,CAAC,MACI;MACDF,UAAU,CAACM,KAAK,CAACJ,MAAM,CAAC;IAC5B;EACJ;EACA3c,sBAAsB,CAAC,IAAI,EAAEL,oCAAoC,EAAE,GAAG,CAAC,CAACqd,KAAK,CAAC,CAAC;AACnF,CAAC;AACD,SAAS3B,eAAe,EAAElZ;AAC1B;AACA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}