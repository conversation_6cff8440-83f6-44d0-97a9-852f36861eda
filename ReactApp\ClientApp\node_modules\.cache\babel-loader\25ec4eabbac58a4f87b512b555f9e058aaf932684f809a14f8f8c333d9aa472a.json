{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { parseColor as u, Color as m } from \"@progress/kendo-drawing\";\nimport { isPresent as c, fitIntoBounds as s } from \"./misc.mjs\";\nconst B = (t, r, o = !0) => {\n    if ([\"hex\", \"rgba\"].indexOf(r) === -1) throw new Error(`Unsupported color output format '${r}'. The available options are 'hex' or 'rgba'.`);\n    if (!c(t)) return;\n    const n = u(t.trim(), o);\n    if (c(n)) return r === \"hex\" ? n.toCss() : n.toCssRgba();\n  },\n  R = (t, r = !0) => {\n    const o = u(t, r);\n    return c(o) ? o.toHSV() : {};\n  },\n  F = (t, r = !0) => {\n    const o = u(t, r);\n    return c(o) ? o.toBytes() : {};\n  },\n  C = t => {\n    const r = s(t.h, 0, 359.9),\n      o = s(t.s, 0, 1),\n      e = s(t.v, 0, 1),\n      n = s(t.a, 0, 1);\n    return m.fromHSV(r, o, e, n).toCssRgba();\n  },\n  w = t => C({\n    h: t,\n    s: 1,\n    v: 1,\n    a: 1\n  }),\n  x = t => {\n    const r = s(t.r, 0, 255),\n      o = s(t.g, 0, 255),\n      e = s(t.b, 0, 255),\n      n = s(t.a, 0, 1);\n    return m.fromBytes(r, o, e, n).toCssRgba();\n  },\n  p = (t, r) => {\n    const o = s(t.r, 0, 255),\n      e = s(t.g, 0, 255),\n      n = s(t.b, 0, 255),\n      a = s(t.a, 0, 1),\n      g = s(r.r, 0, 255),\n      h = s(r.g, 0, 255),\n      l = s(r.b, 0, 255);\n    return {\n      r: Math.round((1 - a) * g + a * o),\n      g: Math.round((1 - a) * h + a * e),\n      b: Math.round((1 - a) * l + a * n)\n    };\n  },\n  i = t => {\n    const r = [t.r || 0, t.g || 0, t.b || 0].map(function (o) {\n      return o /= 255, o <= 0.03928 ? o / 12.92 : Math.pow((o + 0.055) / 1.055, 2.4);\n    });\n    return r[0] * 0.2126 + r[1] * 0.7152 + r[2] * 0.0722;\n  },\n  b = (t, r) => {\n    const o = Math.max(t, r),\n      e = Math.min(t, r);\n    return (o + 0.05) / (e + 0.05);\n  },\n  M = (t, r) => b(i(p(t, r)), i(p(r, {\n    r: 0,\n    g: 0,\n    b: 0,\n    a: 1\n  })));\nexport { C as getColorFromHSV, w as getColorFromHue, x as getColorFromRGBA, b as getContrast, M as getContrastFromTwoRGBAs, R as getHSV, i as getLuminance, F as getRGBA, p as getRGBFromRGBA, B as parseColor };", "map": {"version": 3, "names": ["parseColor", "u", "Color", "m", "isPresent", "c", "fitIntoBounds", "s", "B", "t", "r", "o", "indexOf", "Error", "n", "trim", "to<PERSON>s", "toCssRgba", "R", "toHSV", "F", "toBytes", "C", "h", "e", "v", "a", "fromHSV", "w", "x", "g", "b", "fromBytes", "p", "l", "Math", "round", "i", "map", "pow", "max", "min", "M", "getColorFromHSV", "getColorFromHue", "getColorFromRGBA", "getContrast", "getContrastFromTwoRGBAs", "getHSV", "getLuminance", "getRGBA", "getRGBFromRGBA"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-inputs/colors/utils/color-parser.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { parseColor as u, Color as m } from \"@progress/kendo-drawing\";\nimport { isPresent as c, fitIntoBounds as s } from \"./misc.mjs\";\nconst B = (t, r, o = !0) => {\n  if ([\"hex\", \"rgba\"].indexOf(r) === -1)\n    throw new Error(`Unsupported color output format '${r}'. The available options are 'hex' or 'rgba'.`);\n  if (!c(t))\n    return;\n  const n = u(t.trim(), o);\n  if (c(n))\n    return r === \"hex\" ? n.toCss() : n.toCssRgba();\n}, R = (t, r = !0) => {\n  const o = u(t, r);\n  return c(o) ? o.toHSV() : {};\n}, F = (t, r = !0) => {\n  const o = u(t, r);\n  return c(o) ? o.toBytes() : {};\n}, C = (t) => {\n  const r = s(t.h, 0, 359.9), o = s(t.s, 0, 1), e = s(t.v, 0, 1), n = s(t.a, 0, 1);\n  return m.fromHSV(r, o, e, n).toCssRgba();\n}, w = (t) => C({ h: t, s: 1, v: 1, a: 1 }), x = (t) => {\n  const r = s(t.r, 0, 255), o = s(t.g, 0, 255), e = s(t.b, 0, 255), n = s(t.a, 0, 1);\n  return m.fromBytes(r, o, e, n).toCssRgba();\n}, p = (t, r) => {\n  const o = s(t.r, 0, 255), e = s(t.g, 0, 255), n = s(t.b, 0, 255), a = s(t.a, 0, 1), g = s(r.r, 0, 255), h = s(r.g, 0, 255), l = s(r.b, 0, 255);\n  return {\n    r: Math.round((1 - a) * g + a * o),\n    g: Math.round((1 - a) * h + a * e),\n    b: Math.round((1 - a) * l + a * n)\n  };\n}, i = (t) => {\n  const r = [t.r || 0, t.g || 0, t.b || 0].map(function(o) {\n    return o /= 255, o <= 0.03928 ? o / 12.92 : Math.pow((o + 0.055) / 1.055, 2.4);\n  });\n  return r[0] * 0.2126 + r[1] * 0.7152 + r[2] * 0.0722;\n}, b = (t, r) => {\n  const o = Math.max(t, r), e = Math.min(t, r);\n  return (o + 0.05) / (e + 0.05);\n}, M = (t, r) => b(i(p(t, r)), i(p(r, { r: 0, g: 0, b: 0, a: 1 })));\nexport {\n  C as getColorFromHSV,\n  w as getColorFromHue,\n  x as getColorFromRGBA,\n  b as getContrast,\n  M as getContrastFromTwoRGBAs,\n  R as getHSV,\n  i as getLuminance,\n  F as getRGBA,\n  p as getRGBFromRGBA,\n  B as parseColor\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAU,IAAIC,CAAC,EAAEC,KAAK,IAAIC,CAAC,QAAQ,yBAAyB;AACrE,SAASC,SAAS,IAAIC,CAAC,EAAEC,aAAa,IAAIC,CAAC,QAAQ,YAAY;AAC/D,MAAMC,CAAC,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC,KAAK;IAC1B,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAACC,OAAO,CAACF,CAAC,CAAC,KAAK,CAAC,CAAC,EACnC,MAAM,IAAIG,KAAK,CAAC,oCAAoCH,CAAC,+CAA+C,CAAC;IACvG,IAAI,CAACL,CAAC,CAACI,CAAC,CAAC,EACP;IACF,MAAMK,CAAC,GAAGb,CAAC,CAACQ,CAAC,CAACM,IAAI,CAAC,CAAC,EAAEJ,CAAC,CAAC;IACxB,IAAIN,CAAC,CAACS,CAAC,CAAC,EACN,OAAOJ,CAAC,KAAK,KAAK,GAAGI,CAAC,CAACE,KAAK,CAAC,CAAC,GAAGF,CAAC,CAACG,SAAS,CAAC,CAAC;EAClD,CAAC;EAAEC,CAAC,GAAGA,CAACT,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC,KAAK;IACpB,MAAMC,CAAC,GAAGV,CAAC,CAACQ,CAAC,EAAEC,CAAC,CAAC;IACjB,OAAOL,CAAC,CAACM,CAAC,CAAC,GAAGA,CAAC,CAACQ,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;EAC9B,CAAC;EAAEC,CAAC,GAAGA,CAACX,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC,KAAK;IACpB,MAAMC,CAAC,GAAGV,CAAC,CAACQ,CAAC,EAAEC,CAAC,CAAC;IACjB,OAAOL,CAAC,CAACM,CAAC,CAAC,GAAGA,CAAC,CAACU,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;EAChC,CAAC;EAAEC,CAAC,GAAIb,CAAC,IAAK;IACZ,MAAMC,CAAC,GAAGH,CAAC,CAACE,CAAC,CAACc,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC;MAAEZ,CAAC,GAAGJ,CAAC,CAACE,CAAC,CAACF,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAAEiB,CAAC,GAAGjB,CAAC,CAACE,CAAC,CAACgB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAAEX,CAAC,GAAGP,CAAC,CAACE,CAAC,CAACiB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAChF,OAAOvB,CAAC,CAACwB,OAAO,CAACjB,CAAC,EAAEC,CAAC,EAAEa,CAAC,EAAEV,CAAC,CAAC,CAACG,SAAS,CAAC,CAAC;EAC1C,CAAC;EAAEW,CAAC,GAAInB,CAAC,IAAKa,CAAC,CAAC;IAAEC,CAAC,EAAEd,CAAC;IAAEF,CAAC,EAAE,CAAC;IAAEkB,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAAEG,CAAC,GAAIpB,CAAC,IAAK;IACtD,MAAMC,CAAC,GAAGH,CAAC,CAACE,CAAC,CAACC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;MAAEC,CAAC,GAAGJ,CAAC,CAACE,CAAC,CAACqB,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;MAAEN,CAAC,GAAGjB,CAAC,CAACE,CAAC,CAACsB,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;MAAEjB,CAAC,GAAGP,CAAC,CAACE,CAAC,CAACiB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAClF,OAAOvB,CAAC,CAAC6B,SAAS,CAACtB,CAAC,EAAEC,CAAC,EAAEa,CAAC,EAAEV,CAAC,CAAC,CAACG,SAAS,CAAC,CAAC;EAC5C,CAAC;EAAEgB,CAAC,GAAGA,CAACxB,CAAC,EAAEC,CAAC,KAAK;IACf,MAAMC,CAAC,GAAGJ,CAAC,CAACE,CAAC,CAACC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;MAAEc,CAAC,GAAGjB,CAAC,CAACE,CAAC,CAACqB,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;MAAEhB,CAAC,GAAGP,CAAC,CAACE,CAAC,CAACsB,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;MAAEL,CAAC,GAAGnB,CAAC,CAACE,CAAC,CAACiB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAAEI,CAAC,GAAGvB,CAAC,CAACG,CAAC,CAACA,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;MAAEa,CAAC,GAAGhB,CAAC,CAACG,CAAC,CAACoB,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;MAAEI,CAAC,GAAG3B,CAAC,CAACG,CAAC,CAACqB,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;IAC9I,OAAO;MACLrB,CAAC,EAAEyB,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,GAAGV,CAAC,IAAII,CAAC,GAAGJ,CAAC,GAAGf,CAAC,CAAC;MAClCmB,CAAC,EAAEK,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,GAAGV,CAAC,IAAIH,CAAC,GAAGG,CAAC,GAAGF,CAAC,CAAC;MAClCO,CAAC,EAAEI,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,GAAGV,CAAC,IAAIQ,CAAC,GAAGR,CAAC,GAAGZ,CAAC;IACnC,CAAC;EACH,CAAC;EAAEuB,CAAC,GAAI5B,CAAC,IAAK;IACZ,MAAMC,CAAC,GAAG,CAACD,CAAC,CAACC,CAAC,IAAI,CAAC,EAAED,CAAC,CAACqB,CAAC,IAAI,CAAC,EAAErB,CAAC,CAACsB,CAAC,IAAI,CAAC,CAAC,CAACO,GAAG,CAAC,UAAS3B,CAAC,EAAE;MACvD,OAAOA,CAAC,IAAI,GAAG,EAAEA,CAAC,IAAI,OAAO,GAAGA,CAAC,GAAG,KAAK,GAAGwB,IAAI,CAACI,GAAG,CAAC,CAAC5B,CAAC,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC;IAChF,CAAC,CAAC;IACF,OAAOD,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM;EACtD,CAAC;EAAEqB,CAAC,GAAGA,CAACtB,CAAC,EAAEC,CAAC,KAAK;IACf,MAAMC,CAAC,GAAGwB,IAAI,CAACK,GAAG,CAAC/B,CAAC,EAAEC,CAAC,CAAC;MAAEc,CAAC,GAAGW,IAAI,CAACM,GAAG,CAAChC,CAAC,EAAEC,CAAC,CAAC;IAC5C,OAAO,CAACC,CAAC,GAAG,IAAI,KAAKa,CAAC,GAAG,IAAI,CAAC;EAChC,CAAC;EAAEkB,CAAC,GAAGA,CAACjC,CAAC,EAAEC,CAAC,KAAKqB,CAAC,CAACM,CAAC,CAACJ,CAAC,CAACxB,CAAC,EAAEC,CAAC,CAAC,CAAC,EAAE2B,CAAC,CAACJ,CAAC,CAACvB,CAAC,EAAE;IAAEA,CAAC,EAAE,CAAC;IAAEoB,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEL,CAAC,EAAE;EAAE,CAAC,CAAC,CAAC,CAAC;AACnE,SACEJ,CAAC,IAAIqB,eAAe,EACpBf,CAAC,IAAIgB,eAAe,EACpBf,CAAC,IAAIgB,gBAAgB,EACrBd,CAAC,IAAIe,WAAW,EAChBJ,CAAC,IAAIK,uBAAuB,EAC5B7B,CAAC,IAAI8B,MAAM,EACXX,CAAC,IAAIY,YAAY,EACjB7B,CAAC,IAAI8B,OAAO,EACZjB,CAAC,IAAIkB,cAAc,EACnB3C,CAAC,IAAIR,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}