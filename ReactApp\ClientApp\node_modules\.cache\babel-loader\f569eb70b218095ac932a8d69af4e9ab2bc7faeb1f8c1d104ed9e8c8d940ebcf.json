{"ast": null, "code": "var fromCharCode = String.fromCharCode;\nexport var BOM = '\\xfe\\xff';\n\n// Encodes a string as UTF-8\nexport function encodeUTF8(input) {\n  var output = \"\";\n  for (var i = 0; i < input.length; i++) {\n    var code = input.charCodeAt(i);\n    if (0xD800 <= code && code <= 0xDBFF) {\n      var hi = code;\n      var low = input.charCodeAt(++i);\n      if (!isNaN(low)) {\n        // Combine high and low surrogate\n        // See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/charCodeAt\n        code = (hi - 0xD800) * 0x400 + (low - 0xDC00) + 0x10000;\n      }\n    }\n    if (code < 0x80) {\n      // One byte\n      output += fromCharCode(code);\n    } else if (code < 0x800) {\n      // Two bytes\n      output += fromCharCode(0xC0 | code >>> 6);\n      output += fromCharCode(0x80 | code & 0x3f);\n    } else if (code < 0x10000) {\n      // Three bytes\n      output += fromCharCode(0xE0 | code >>> 12);\n      output += fromCharCode(0x80 | code >>> 6 & 0x3f);\n      output += fromCharCode(0x80 | code & 0x3f);\n    } else if (code < 0x10FFFF) {\n      // Four bytes\n      output += fromCharCode(0xF0 | code >>> 18);\n      output += fromCharCode(0x80 | code >>> 12 & 0x3f);\n      output += fromCharCode(0x80 | code >>> 6 & 0x3f);\n      output += fromCharCode(0x80 | code & 0x3f);\n    }\n  }\n  return output;\n}\nfunction encodeUnit(codeUnit) {\n  return fromCharCode(codeUnit >> 8) + fromCharCode(codeUnit & 0x00ff);\n}\n\n// Encodes a string as UTF-16 big-endian\nexport function encodeUTF16BE(input) {\n  var output = '';\n  for (var i = 0; i < input.length; i++) {\n    var c = input.charCodeAt(i);\n    if (c < 0xFFFF) {\n      output += encodeUnit(c);\n    } else {\n      var lead = (c - 0x10000 >> 10) + 0xD800;\n      var trail = (c - 0x10000 & 0x3FF) + 0xDC00;\n      output += encodeUnit(lead);\n      output += encodeUnit(trail);\n    }\n  }\n  return output;\n}", "map": {"version": 3, "names": ["fromCharCode", "String", "BOM", "encodeUTF8", "input", "output", "i", "length", "code", "charCodeAt", "hi", "low", "isNaN", "encodeUnit", "codeUnit", "encodeUTF16BE", "c", "lead", "trail"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/util/encode-utf.js"], "sourcesContent": ["var fromCharCode = String.fromCharCode;\n\nexport var BOM = '\\xfe\\xff';\n\n// Encodes a string as UTF-8\nexport function encodeUTF8(input) {\n    var output = \"\";\n\n    for (var i = 0; i < input.length; i++) {\n        var code = input.charCodeAt(i);\n\n        if (0xD800 <= code && code <= 0xDBFF) {\n            var hi = code;\n            var low = input.charCodeAt(++i);\n\n            if (!isNaN(low)) {\n                // Combine high and low surrogate\n                // See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/charCodeAt\n                code = (hi - 0xD800) * 0x400 +\n                       (low - 0xDC00) + 0x10000;\n            }\n        }\n\n        if (code < 0x80) {\n            // One byte\n            output += fromCharCode(code);\n        } else if (code < 0x800) {\n            // Two bytes\n            output += fromCharCode(0xC0 | (code >>> 6));\n            output += fromCharCode(0x80 | (code & 0x3f));\n        } else if (code < 0x10000) {\n            // Three bytes\n            output += fromCharCode(0xE0 | (code >>> 12));\n            output += fromCharCode(0x80 | (code >>> 6 & 0x3f));\n            output += fromCharCode(0x80 | (code & 0x3f));\n        } else if (code < 0x10FFFF) {\n            // Four bytes\n            output += fromCharCode(0xF0 | (code >>> 18));\n            output += fromCharCode(0x80 | (code >>> 12 & 0x3f));\n            output += fromCharCode(0x80 | (code >>> 6 & 0x3f));\n            output += fromCharCode(0x80 | (code & 0x3f));\n        }\n    }\n\n    return output;\n}\n\nfunction encodeUnit(codeUnit) {\n    return fromCharCode(codeUnit >> 8) + fromCharCode(codeUnit & 0x00ff);\n}\n\n// Encodes a string as UTF-16 big-endian\nexport function encodeUTF16BE(input) {\n    var output = '';\n\n    for (var i = 0; i < input.length; i++) {\n        var c = input.charCodeAt(i);\n\n        if (c < 0xFFFF) {\n            output += encodeUnit(c);\n        } else {\n            var lead = ((c - 0x10000) >> 10) + 0xD800;\n            var trail = ((c - 0x10000) & 0x3FF) + 0xDC00;\n            output += encodeUnit(lead);\n            output += encodeUnit(trail);\n        }\n    }\n\n    return output;\n}\n"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,MAAM,CAACD,YAAY;AAEtC,OAAO,IAAIE,GAAG,GAAG,UAAU;;AAE3B;AACA,OAAO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAC9B,IAAIC,MAAM,GAAG,EAAE;EAEf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACnC,IAAIE,IAAI,GAAGJ,KAAK,CAACK,UAAU,CAACH,CAAC,CAAC;IAE9B,IAAI,MAAM,IAAIE,IAAI,IAAIA,IAAI,IAAI,MAAM,EAAE;MAClC,IAAIE,EAAE,GAAGF,IAAI;MACb,IAAIG,GAAG,GAAGP,KAAK,CAACK,UAAU,CAAC,EAAEH,CAAC,CAAC;MAE/B,IAAI,CAACM,KAAK,CAACD,GAAG,CAAC,EAAE;QACb;QACA;QACAH,IAAI,GAAG,CAACE,EAAE,GAAG,MAAM,IAAI,KAAK,IACpBC,GAAG,GAAG,MAAM,CAAC,GAAG,OAAO;MACnC;IACJ;IAEA,IAAIH,IAAI,GAAG,IAAI,EAAE;MACb;MACAH,MAAM,IAAIL,YAAY,CAACQ,IAAI,CAAC;IAChC,CAAC,MAAM,IAAIA,IAAI,GAAG,KAAK,EAAE;MACrB;MACAH,MAAM,IAAIL,YAAY,CAAC,IAAI,GAAIQ,IAAI,KAAK,CAAE,CAAC;MAC3CH,MAAM,IAAIL,YAAY,CAAC,IAAI,GAAIQ,IAAI,GAAG,IAAK,CAAC;IAChD,CAAC,MAAM,IAAIA,IAAI,GAAG,OAAO,EAAE;MACvB;MACAH,MAAM,IAAIL,YAAY,CAAC,IAAI,GAAIQ,IAAI,KAAK,EAAG,CAAC;MAC5CH,MAAM,IAAIL,YAAY,CAAC,IAAI,GAAIQ,IAAI,KAAK,CAAC,GAAG,IAAK,CAAC;MAClDH,MAAM,IAAIL,YAAY,CAAC,IAAI,GAAIQ,IAAI,GAAG,IAAK,CAAC;IAChD,CAAC,MAAM,IAAIA,IAAI,GAAG,QAAQ,EAAE;MACxB;MACAH,MAAM,IAAIL,YAAY,CAAC,IAAI,GAAIQ,IAAI,KAAK,EAAG,CAAC;MAC5CH,MAAM,IAAIL,YAAY,CAAC,IAAI,GAAIQ,IAAI,KAAK,EAAE,GAAG,IAAK,CAAC;MACnDH,MAAM,IAAIL,YAAY,CAAC,IAAI,GAAIQ,IAAI,KAAK,CAAC,GAAG,IAAK,CAAC;MAClDH,MAAM,IAAIL,YAAY,CAAC,IAAI,GAAIQ,IAAI,GAAG,IAAK,CAAC;IAChD;EACJ;EAEA,OAAOH,MAAM;AACjB;AAEA,SAASQ,UAAUA,CAACC,QAAQ,EAAE;EAC1B,OAAOd,YAAY,CAACc,QAAQ,IAAI,CAAC,CAAC,GAAGd,YAAY,CAACc,QAAQ,GAAG,MAAM,CAAC;AACxE;;AAEA;AACA,OAAO,SAASC,aAAaA,CAACX,KAAK,EAAE;EACjC,IAAIC,MAAM,GAAG,EAAE;EAEf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACnC,IAAIU,CAAC,GAAGZ,KAAK,CAACK,UAAU,CAACH,CAAC,CAAC;IAE3B,IAAIU,CAAC,GAAG,MAAM,EAAE;MACZX,MAAM,IAAIQ,UAAU,CAACG,CAAC,CAAC;IAC3B,CAAC,MAAM;MACH,IAAIC,IAAI,GAAG,CAAED,CAAC,GAAG,OAAO,IAAK,EAAE,IAAI,MAAM;MACzC,IAAIE,KAAK,GAAG,CAAEF,CAAC,GAAG,OAAO,GAAI,KAAK,IAAI,MAAM;MAC5CX,MAAM,IAAIQ,UAAU,CAACI,IAAI,CAAC;MAC1BZ,MAAM,IAAIQ,UAAU,CAACK,KAAK,CAAC;IAC/B;EACJ;EAEA,OAAOb,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}