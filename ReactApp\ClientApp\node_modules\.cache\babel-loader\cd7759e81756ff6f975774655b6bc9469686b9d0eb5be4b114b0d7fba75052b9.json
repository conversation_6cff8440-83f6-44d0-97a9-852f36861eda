{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useEffect, useRef, useState } from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport { useAcceptTncMutation, useEvaluateTncQuery } from '@app/api/tncApiSlice';\nimport { useAppSelector } from '@app/hooks/useAppSelector';\nimport { errorNotification } from '@app/utils/antNotifications';\nexport const useTermsAndConditions = () => {\n  _s();\n  var _location$state;\n  const navigate = useNavigate();\n  const location = useLocation();\n  const pdfViewerRef = useRef(null);\n  const [pdfDocument, setPdfDocument] = useState(null);\n  const FtncDocument = useAppSelector(state => state.app.tnc);\n  const redirectPath = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.from) || '/';\n  const {\n    refetch: tncRefetch,\n    isFetching: tncLoading\n  } = useEvaluateTncQuery();\n  const [acceptTnc, {\n    isLoading: isAccepting\n  }] = useAcceptTncMutation();\n  const onDocumentLoad = event => {\n    setPdfDocument(event.target);\n  };\n  const handleAgree = async () => {\n    let messageText;\n    try {\n      var _tncDocument, _tncDocument$document;\n      await acceptTnc({\n        termsAndConditionsId: tncDocument.document.termsAndConditionsId,\n        triggerType: ((_tncDocument = tncDocument) === null || _tncDocument === void 0 ? void 0 : (_tncDocument$document = _tncDocument.document) === null || _tncDocument$document === void 0 ? void 0 : _tncDocument$document.triggerType) || \"\"\n      }).unwrap();\n      navigate(redirectPath);\n    } catch (err) {\n      var _err$data;\n      messageText = 'Terms & Conditions acceptance failed';\n      if ((err === null || err === void 0 ? void 0 : err.status) >= 400 && (err === null || err === void 0 ? void 0 : err.status) < 500 && err !== null && err !== void 0 && (_err$data = err.data) !== null && _err$data !== void 0 && _err$data.message) {\n        var _err$data2;\n        messageText = err === null || err === void 0 ? void 0 : (_err$data2 = err.data) === null || _err$data2 === void 0 ? void 0 : _err$data2.message;\n      }\n      errorNotification([''], messageText);\n    }\n  };\n  const clickToolbarButtonByTitle = title => {\n    const button = document.querySelector(`button[title=\"${title}\"]`);\n    button === null || button === void 0 ? void 0 : button.click();\n  };\n  useEffect(() => {\n    if (tncDocument.isValidated || !tncDocument.document) {\n      navigate(redirectPath);\n    }\n  }, [tncDocument, navigate, redirectPath]);\n  return {\n    tncDocument,\n    pdfViewerRef,\n    pdfDocument,\n    setPdfDocument,\n    tncRefetch,\n    tncLoading,\n    isAccepting,\n    onDocumentLoad,\n    handleAgree,\n    clickToolbarButtonByTitle\n  };\n};\n_s(useTermsAndConditions, \"38Gm8jcC0rvkOmuo6IF4lv969Lc=\", false, function () {\n  return [useNavigate, useLocation, useAppSelector, useEvaluateTncQuery, useAcceptTncMutation];\n});", "map": {"version": 3, "names": ["useEffect", "useRef", "useState", "useLocation", "useNavigate", "useAcceptTncMutation", "useEvaluateTncQuery", "useAppSelector", "errorNotification", "useTermsAndConditions", "_s", "_location$state", "navigate", "location", "pdfViewerRef", "pdfDocument", "setPdfDocument", "FtncDocument", "state", "app", "tnc", "redirectPath", "from", "refetch", "tncRefetch", "isFetching", "tncLoading", "acceptTnc", "isLoading", "isAccepting", "onDocumentLoad", "event", "target", "handleAgree", "messageText", "_tncDocument", "_tncDocument$document", "termsAndConditionsId", "tncDocument", "document", "triggerType", "unwrap", "err", "_err$data", "status", "data", "message", "_err$data2", "clickToolbarButtonByTitle", "title", "button", "querySelector", "click", "isValidated"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/src/app/hooks/useTermsAndConditions.ts"], "sourcesContent": ["import { useEffect, useRef, useState } from 'react';\r\nimport { useLocation, useNavigate } from 'react-router-dom';\r\nimport { useAcceptTncMutation, useEvaluateTncQuery } from '@app/api/tncApiSlice';\r\nimport { useAppSelector } from '@app/hooks/useAppSelector';\r\nimport { errorNotification } from '@app/utils/antNotifications';\r\n\r\nexport const useTermsAndConditions = () => {\r\n    const navigate = useNavigate();\r\n    const location = useLocation();\r\n    const pdfViewerRef = useRef<any>(null);\r\n    const [pdfDocument, setPdfDocument] = useState<any>(null);\r\n    const FtncDocument = useAppSelector((state) => state.app.tnc);\r\n\r\n    const redirectPath = location.state?.from || '/';\r\n\r\n    const { refetch: tncRefetch, isFetching: tncLoading } = useEvaluateTncQuery();\r\n    const [acceptTnc, { isLoading: isAccepting }] = useAcceptTncMutation();\r\n\r\n\r\n\r\n    const onDocumentLoad = (event: any) => {\r\n        setPdfDocument(event.target);\r\n    };\r\n\r\n    const handleAgree = async () => {\r\n        let messageText;\r\n        try {\r\n            await acceptTnc({ termsAndConditionsId: tncDocument.document!.termsAndConditionsId, triggerType: tncDocument?.document?.triggerType || \"\" }).unwrap();\r\n            navigate(redirectPath);\r\n        } catch (err: any) {\r\n            messageText = 'Terms & Conditions acceptance failed';\r\n            if (err?.status >= 400 && err?.status < 500 && err?.data?.message) {\r\n                messageText = err?.data?.message;\r\n            }\r\n            errorNotification([''], messageText);\r\n        }\r\n    };\r\n\r\n    const clickToolbarButtonByTitle = (title: string) => {\r\n        const button = document.querySelector(`button[title=\"${title}\"]`) as HTMLElement;\r\n        button?.click();\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (tncDocument.isValidated || !tncDocument.document) {\r\n            navigate(redirectPath);\r\n        }\r\n    }, [tncDocument, navigate, redirectPath]);\r\n\r\n\r\n    return {\r\n        tncDocument,\r\n        pdfViewerRef,\r\n        pdfDocument,\r\n        setPdfDocument,\r\n        tncRefetch,\r\n        tncLoading,\r\n        isAccepting,\r\n        onDocumentLoad,\r\n        handleAgree,\r\n        clickToolbarButtonByTitle\r\n    };\r\n};\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,oBAAoB,EAAEC,mBAAmB,QAAQ,sBAAsB;AAChF,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,iBAAiB,QAAQ,6BAA6B;AAE/D,OAAO,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA;EACvC,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAMS,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,YAAY,GAAGb,MAAM,CAAM,IAAI,CAAC;EACtC,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAM,IAAI,CAAC;EACzD,MAAMe,YAAY,GAAGV,cAAc,CAAEW,KAAK,IAAKA,KAAK,CAACC,GAAG,CAACC,GAAG,CAAC;EAE7D,MAAMC,YAAY,GAAG,EAAAV,eAAA,GAAAE,QAAQ,CAACK,KAAK,cAAAP,eAAA,uBAAdA,eAAA,CAAgBW,IAAI,KAAI,GAAG;EAEhD,MAAM;IAAEC,OAAO,EAAEC,UAAU;IAAEC,UAAU,EAAEC;EAAW,CAAC,GAAGpB,mBAAmB,CAAC,CAAC;EAC7E,MAAM,CAACqB,SAAS,EAAE;IAAEC,SAAS,EAAEC;EAAY,CAAC,CAAC,GAAGxB,oBAAoB,CAAC,CAAC;EAItE,MAAMyB,cAAc,GAAIC,KAAU,IAAK;IACnCf,cAAc,CAACe,KAAK,CAACC,MAAM,CAAC;EAChC,CAAC;EAED,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAIC,WAAW;IACf,IAAI;MAAA,IAAAC,YAAA,EAAAC,qBAAA;MACA,MAAMT,SAAS,CAAC;QAAEU,oBAAoB,EAAEC,WAAW,CAACC,QAAQ,CAAEF,oBAAoB;QAAEG,WAAW,EAAE,EAAAL,YAAA,GAAAG,WAAW,cAAAH,YAAA,wBAAAC,qBAAA,GAAXD,YAAA,CAAaI,QAAQ,cAAAH,qBAAA,uBAArBA,qBAAA,CAAuBI,WAAW,KAAI;MAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;MACrJ7B,QAAQ,CAACS,YAAY,CAAC;IAC1B,CAAC,CAAC,OAAOqB,GAAQ,EAAE;MAAA,IAAAC,SAAA;MACfT,WAAW,GAAG,sCAAsC;MACpD,IAAI,CAAAQ,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEE,MAAM,KAAI,GAAG,IAAI,CAAAF,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEE,MAAM,IAAG,GAAG,IAAIF,GAAG,aAAHA,GAAG,gBAAAC,SAAA,GAAHD,GAAG,CAAEG,IAAI,cAAAF,SAAA,eAATA,SAAA,CAAWG,OAAO,EAAE;QAAA,IAAAC,UAAA;QAC/Db,WAAW,GAAGQ,GAAG,aAAHA,GAAG,wBAAAK,UAAA,GAAHL,GAAG,CAAEG,IAAI,cAAAE,UAAA,uBAATA,UAAA,CAAWD,OAAO;MACpC;MACAtC,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE0B,WAAW,CAAC;IACxC;EACJ,CAAC;EAED,MAAMc,yBAAyB,GAAIC,KAAa,IAAK;IACjD,MAAMC,MAAM,GAAGX,QAAQ,CAACY,aAAa,CAAC,iBAAiBF,KAAK,IAAI,CAAgB;IAChFC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEE,KAAK,CAAC,CAAC;EACnB,CAAC;EAEDpD,SAAS,CAAC,MAAM;IACZ,IAAIsC,WAAW,CAACe,WAAW,IAAI,CAACf,WAAW,CAACC,QAAQ,EAAE;MAClD3B,QAAQ,CAACS,YAAY,CAAC;IAC1B;EACJ,CAAC,EAAE,CAACiB,WAAW,EAAE1B,QAAQ,EAAES,YAAY,CAAC,CAAC;EAGzC,OAAO;IACHiB,WAAW;IACXxB,YAAY;IACZC,WAAW;IACXC,cAAc;IACdQ,UAAU;IACVE,UAAU;IACVG,WAAW;IACXC,cAAc;IACdG,WAAW;IACXe;EACJ,CAAC;AACL,CAAC;AAACtC,EAAA,CAxDWD,qBAAqB;EAAA,QACbL,WAAW,EACXD,WAAW,EAGPI,cAAc,EAIqBD,mBAAmB,EAC3BD,oBAAoB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}