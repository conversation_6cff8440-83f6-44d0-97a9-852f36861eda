{"ast": null, "code": "import { localeInfo } from './info';\nimport { errors } from '../errors';\nimport { DEFAULT_LOCALE } from '../common/constants';\nexport default function dateFieldName(options, locale) {\n  if (locale === void 0) locale = DEFAULT_LOCALE;\n  var info = localeInfo(locale);\n  var dateFields = info.calendar.dateFields;\n  if (!dateFields) {\n    throw errors.NoDateFieldNames.error();\n  }\n  var fieldNameInfo = dateFields[options.type] || {};\n  return fieldNameInfo[options.nameType] || fieldNameInfo['wide'];\n}", "map": {"version": 3, "names": ["localeInfo", "errors", "DEFAULT_LOCALE", "dateFieldName", "options", "locale", "info", "dateFields", "calendar", "NoDateFieldNames", "error", "fieldNameInfo", "type", "nameType"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-intl/dist/es/cldr/date-field-name.js"], "sourcesContent": ["import { localeInfo } from './info';\nimport { errors } from '../errors';\nimport { DEFAULT_LOCALE } from '../common/constants';\n\nexport default function dateFieldName(options, locale) {\n    if ( locale === void 0 ) locale = DEFAULT_LOCALE;\n\n    var info = localeInfo(locale);\n    var dateFields = info.calendar.dateFields;\n    if (!dateFields) {\n        throw errors.NoDateFieldNames.error();\n    }\n\n    var fieldNameInfo = dateFields[options.type] || {};\n\n    return fieldNameInfo[options.nameType] || fieldNameInfo['wide'];\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,QAAQ;AACnC,SAASC,MAAM,QAAQ,WAAW;AAClC,SAASC,cAAc,QAAQ,qBAAqB;AAEpD,eAAe,SAASC,aAAaA,CAACC,OAAO,EAAEC,MAAM,EAAE;EACnD,IAAKA,MAAM,KAAK,KAAK,CAAC,EAAGA,MAAM,GAAGH,cAAc;EAEhD,IAAII,IAAI,GAAGN,UAAU,CAACK,MAAM,CAAC;EAC7B,IAAIE,UAAU,GAAGD,IAAI,CAACE,QAAQ,CAACD,UAAU;EACzC,IAAI,CAACA,UAAU,EAAE;IACb,MAAMN,MAAM,CAACQ,gBAAgB,CAACC,KAAK,CAAC,CAAC;EACzC;EAEA,IAAIC,aAAa,GAAGJ,UAAU,CAACH,OAAO,CAACQ,IAAI,CAAC,IAAI,CAAC,CAAC;EAElD,OAAOD,aAAa,CAACP,OAAO,CAACS,QAAQ,CAAC,IAAIF,aAAa,CAAC,MAAM,CAAC;AACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}