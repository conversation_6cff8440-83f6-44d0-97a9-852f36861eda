{"ast": null, "code": "// tslint:disable:max-line-length\n/**\n * An enumeration which represents the horizontal direction. The `Forward` option moves forward. The `Backward` option moves backward.\n */\nexport var Direction;\n(function (Direction) {\n  /**\n   * The `Forward` value with an underlying `1` number value.\n   */\n  Direction[Direction[\"Forward\"] = 1] = \"Forward\";\n  /**\n   * The `Backward` value with an underlying `-1` (minus one) number value.\n   */\n  Direction[Direction[\"Backward\"] = -1] = \"Backward\";\n})(Direction || (Direction = {}));\n// tslint:enable:max-line-length", "map": {"version": 3, "names": ["Direction"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/direction.enum.js"], "sourcesContent": ["// tslint:disable:max-line-length\n/**\n * An enumeration which represents the horizontal direction. The `Forward` option moves forward. The `Backward` option moves backward.\n */\nexport var Direction;\n(function (Direction) {\n    /**\n     * The `Forward` value with an underlying `1` number value.\n     */\n    Direction[Direction[\"Forward\"] = 1] = \"Forward\";\n    /**\n     * The `Backward` value with an underlying `-1` (minus one) number value.\n     */\n    Direction[Direction[\"Backward\"] = -1] = \"Backward\";\n})(Direction || (Direction = {}));\n// tslint:enable:max-line-length\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,IAAIA,SAAS;AACpB,CAAC,UAAUA,SAAS,EAAE;EAClB;AACJ;AACA;EACIA,SAAS,CAACA,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EAC/C;AACJ;AACA;EACIA,SAAS,CAACA,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,UAAU;AACtD,CAAC,EAAEA,SAAS,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}