{"ast": null, "code": "import { Direction } from \"./direction.enum\";\nimport { dayOfWeek } from './day-of-week';\n/**\n * A function which returns a date by a specific week name. For example, `Day.Monday`.\n *\n * @param date - The date to calculate from.\n * @param weekDay - The `Day` enum specifying the desired week day.\n * @returns - A `Date` instance.\n *\n * @example\n * ```ts-no-run\n * prevDayOfWeek(new Date(2016, 0, 1), Day.Wednesday); // 2015-12-30, Wednesday\n * ```\n */\nexport var prevDayOfWeek = function (date, weekDay) {\n  return dayOfWeek(date, weekDay, Direction.Backward);\n};", "map": {"version": 3, "names": ["Direction", "dayOfWeek", "prevDayOfWeek", "date", "weekDay", "Backward"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/prev-day-of-week.js"], "sourcesContent": ["import { Direction } from \"./direction.enum\";\nimport { dayOfWeek } from './day-of-week';\n/**\n * A function which returns a date by a specific week name. For example, `Day.Monday`.\n *\n * @param date - The date to calculate from.\n * @param weekDay - The `Day` enum specifying the desired week day.\n * @returns - A `Date` instance.\n *\n * @example\n * ```ts-no-run\n * prevDayOfWeek(new Date(2016, 0, 1), Day.Wednesday); // 2015-12-30, Wednesday\n * ```\n */\nexport var prevDayOfWeek = function (date, weekDay) {\n    return dayOfWeek(date, weekDay, Direction.Backward);\n};\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,SAAS,QAAQ,eAAe;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,aAAa,GAAG,SAAAA,CAAUC,IAAI,EAAEC,OAAO,EAAE;EAChD,OAAOH,SAAS,CAACE,IAAI,EAAEC,OAAO,EAAEJ,SAAS,CAACK,QAAQ,CAAC;AACvD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}