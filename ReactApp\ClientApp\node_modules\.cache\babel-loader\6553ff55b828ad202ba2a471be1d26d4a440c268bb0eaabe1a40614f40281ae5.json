{"ast": null, "code": "import { isPresent } from '../utils';\nimport { isCompositeFilterDescriptor } from './filter-descriptor.interface';\nimport { normalizeFilters } from './filter.operators';\nimport { transformCompositeFilter } from \"./filter-no-eval\";\n// tslint:disable:max-line-length\n/**\n * Creates a [Predicate]({% slug api_kendo-data-query_predicate %}) function for the specified [CompositeFilterDescriptor]({% slug api_kendo-data-query_compositefilterdescriptor %}).\n *\n * @param {CompositeFilterDescriptor} descriptor - The descriptor for which the predicate is created.\n * @returns {Predicate} - The created function instance.\n *\n * @example\n * ```ts\n * import { compileFilter } from '@progress/kendo-data-query';\n *\n * const data = [{ name: \"Pork\" }, { name: \"Pepper\" }, { name: \"Beef\" } ];\n * const predicate = compileFilter({ logic: \"and\", filters: [{ field: \"name\", operator: \"startswith\", value: \"P\" }] });\n * const result = data.filter(predicate);\n *\n * ```\n */\n// tslint:enable:max-line-length\nexport var compileFilter = function (descriptor) {\n  if (!descriptor || descriptor.filters.length === 0) {\n    return function () {\n      return true;\n    };\n  }\n  return transformCompositeFilter(descriptor);\n};\n// tslint:disable:max-line-length\n/**\n * Filters the provided array according to the specified [CompositeFilterDescriptor]({% slug api_kendo-data-query_compositefilterdescriptor %}).\n *\n * @param {T[]} data - The data that will be filtered.\n * @param {(CompositeFilterDescriptor | FilterDescriptor)} descriptor - The filter criteria that will be applied.\n * @returns {T[]} - The filtered data.\n *\n * @example\n * ```ts\n * import { filterBy } from '@progress/kendo-data-query';\n *\n * const data = [\n *  { name: \"Pork\", category: \"Food\", subcategory: \"Meat\" },\n *  { name: \"Pepper\", category: \"Food\", subcategory: \"Vegetables\" },\n *  { name: \"Beef\", category: \"Food\", subcategory: \"Meat\" }\n * ];\n *\n * const result = filterBy(data, {\n *     logic: 'and',\n *     filters: [\n *           { field: \"name\", operator: \"startswith\", value: \"p\", ignoreCase: true },\n *           { field: \"subcategory\", operator: \"eq\", value: \"Meat\" },\n *     ]\n * });\n *\n * // output:\n * // [{ \"name\": \"Pork\", \"category\": \"Food\", \"subcategory\": \"Meat\" }]\n * ```\n */\n// tslint:enable:max-line-length\nexport var filterBy = function (data, descriptor) {\n  if (!isPresent(descriptor) || isCompositeFilterDescriptor(descriptor) && descriptor.filters.length === 0) {\n    return data;\n  }\n  return data.filter(compileFilter(normalizeFilters(descriptor)));\n};", "map": {"version": 3, "names": ["isPresent", "isCompositeFilterDescriptor", "normalizeFilters", "transformCompositeFilter", "compileFilter", "descriptor", "filters", "length", "filterBy", "data", "filter"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-data-query/dist/es/filtering/filter-expression.factory.js"], "sourcesContent": ["import { isPresent } from '../utils';\nimport { isCompositeFilterDescriptor } from './filter-descriptor.interface';\nimport { normalizeFilters } from './filter.operators';\nimport { transformCompositeFilter } from \"./filter-no-eval\";\n// tslint:disable:max-line-length\n/**\n * Creates a [Predicate]({% slug api_kendo-data-query_predicate %}) function for the specified [CompositeFilterDescriptor]({% slug api_kendo-data-query_compositefilterdescriptor %}).\n *\n * @param {CompositeFilterDescriptor} descriptor - The descriptor for which the predicate is created.\n * @returns {Predicate} - The created function instance.\n *\n * @example\n * ```ts\n * import { compileFilter } from '@progress/kendo-data-query';\n *\n * const data = [{ name: \"Pork\" }, { name: \"Pepper\" }, { name: \"Beef\" } ];\n * const predicate = compileFilter({ logic: \"and\", filters: [{ field: \"name\", operator: \"startswith\", value: \"P\" }] });\n * const result = data.filter(predicate);\n *\n * ```\n */\n// tslint:enable:max-line-length\nexport var compileFilter = function (descriptor) {\n    if (!descriptor || descriptor.filters.length === 0) {\n        return function () { return true; };\n    }\n    return transformCompositeFilter(descriptor);\n};\n// tslint:disable:max-line-length\n/**\n * Filters the provided array according to the specified [CompositeFilterDescriptor]({% slug api_kendo-data-query_compositefilterdescriptor %}).\n *\n * @param {T[]} data - The data that will be filtered.\n * @param {(CompositeFilterDescriptor | FilterDescriptor)} descriptor - The filter criteria that will be applied.\n * @returns {T[]} - The filtered data.\n *\n * @example\n * ```ts\n * import { filterBy } from '@progress/kendo-data-query';\n *\n * const data = [\n *  { name: \"Pork\", category: \"Food\", subcategory: \"Meat\" },\n *  { name: \"Pepper\", category: \"Food\", subcategory: \"Vegetables\" },\n *  { name: \"Beef\", category: \"Food\", subcategory: \"Meat\" }\n * ];\n *\n * const result = filterBy(data, {\n *     logic: 'and',\n *     filters: [\n *           { field: \"name\", operator: \"startswith\", value: \"p\", ignoreCase: true },\n *           { field: \"subcategory\", operator: \"eq\", value: \"Meat\" },\n *     ]\n * });\n *\n * // output:\n * // [{ \"name\": \"Pork\", \"category\": \"Food\", \"subcategory\": \"Meat\" }]\n * ```\n */\n// tslint:enable:max-line-length\nexport var filterBy = function (data, descriptor) {\n    if (!isPresent(descriptor) || (isCompositeFilterDescriptor(descriptor) && descriptor.filters.length === 0)) {\n        return data;\n    }\n    return data.filter(compileFilter(normalizeFilters(descriptor)));\n};\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,UAAU;AACpC,SAASC,2BAA2B,QAAQ,+BAA+B;AAC3E,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,wBAAwB,QAAQ,kBAAkB;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,aAAa,GAAG,SAAAA,CAAUC,UAAU,EAAE;EAC7C,IAAI,CAACA,UAAU,IAAIA,UAAU,CAACC,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;IAChD,OAAO,YAAY;MAAE,OAAO,IAAI;IAAE,CAAC;EACvC;EACA,OAAOJ,wBAAwB,CAACE,UAAU,CAAC;AAC/C,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIG,QAAQ,GAAG,SAAAA,CAAUC,IAAI,EAAEJ,UAAU,EAAE;EAC9C,IAAI,CAACL,SAAS,CAACK,UAAU,CAAC,IAAKJ,2BAA2B,CAACI,UAAU,CAAC,IAAIA,UAAU,CAACC,OAAO,CAACC,MAAM,KAAK,CAAE,EAAE;IACxG,OAAOE,IAAI;EACf;EACA,OAAOA,IAAI,CAACC,MAAM,CAACN,aAAa,CAACF,gBAAgB,CAACG,UAAU,CAAC,CAAC,CAAC;AACnE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}