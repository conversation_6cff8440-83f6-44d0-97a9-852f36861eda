{"ast": null, "code": "import { addMonths, cloneDate, createDate, isEqual, getDate, lastDayOfMonth } from '@progress/kendo-date-math';\nimport { Mask } from './mask';\nimport { dateSymbolMap, padZero, unpad<PERSON>ero } from '../dateinput/utils';\nimport { extend, isPresent, cropTwoDigitYear, setYears, parseToInt, clamp, areDatePartsEqualTo, isNumber, isValidDate, isParseableToInt } from './utils';\nimport { Constants } from './constants';\nvar MONTH_INDEX_FEBRUARY = 1;\nvar DEFAULT_LEAP_YEAR = 2000;\nvar PREVIOUS_CENTURY_BASE = 1900;\nvar CURRENT_CENTURY_BASE = 2000;\nvar SHORT_PATTERN_LENGTH_REGEXP = /d|M|H|h|m|s/;\nvar MONTH_PART_WITH_WORDS_THRESHOLD = 2;\nvar MONTH_SYMBOL = \"M\";\n// JS months start from 0 (January) instead of 1 (January)\nvar JS_MONTH_OFFSET = 1;\nvar DateObject = /** @class */function () {\n  function DateObject(_a) {\n    var intlService = _a.intlService,\n      formatPlaceholder = _a.formatPlaceholder,\n      format = _a.format,\n      _b = _a.cycleTime,\n      cycleTime = _b === void 0 ? false : _b,\n      _c = _a.twoDigitYearMax,\n      twoDigitYearMax = _c === void 0 ? Constants.twoDigitYearMax : _c,\n      _d = _a.value,\n      value = _d === void 0 ? null : _d,\n      _e = _a.autoCorrectParts,\n      autoCorrectParts = _e === void 0 ? true : _e,\n      _f = _a.toggleDayPeriod,\n      toggleDayPeriod = _f === void 0 ? false : _f,\n      _g = _a.autoSwitchParts,\n      autoSwitchParts = _g === void 0 ? true : _g;\n    this.year = true;\n    this.month = true;\n    this.date = true;\n    this.hours = true;\n    this.minutes = true;\n    this.seconds = true;\n    this.milliseconds = true;\n    this.dayperiod = true;\n    this.leadingZero = null;\n    this.typedMonthPart = '';\n    this.knownParts = 'adHhmMsEyS';\n    this.symbols = {\n      'E': 'E',\n      'H': 'H',\n      'M': 'M',\n      'a': 'a',\n      'd': 'd',\n      'h': 'h',\n      'm': 'm',\n      's': 's',\n      'y': 'y',\n      'S': 'S'\n    };\n    this._value = this.getDefaultDate();\n    this.cycleTime = false;\n    this._partiallyInvalidDate = {\n      startDate: null,\n      invalidDateParts: {\n        'E': {\n          value: null,\n          date: null,\n          startDateOffset: 0\n        },\n        'H': {\n          value: null,\n          date: null,\n          startDateOffset: 0\n        },\n        'M': {\n          value: null,\n          date: null,\n          startDateOffset: 0\n        },\n        'a': {\n          value: null,\n          date: null,\n          startDateOffset: 0\n        },\n        'd': {\n          value: null,\n          date: null,\n          startDateOffset: 0\n        },\n        'h': {\n          value: null,\n          date: null,\n          startDateOffset: 0\n        },\n        'm': {\n          value: null,\n          date: null,\n          startDateOffset: 0\n        },\n        's': {\n          value: null,\n          date: null,\n          startDateOffset: 0\n        },\n        'y': {\n          value: null,\n          date: null,\n          startDateOffset: 0\n        },\n        'S': {\n          value: null,\n          date: null,\n          startDateOffset: 0\n        }\n      }\n    };\n    this.setOptions({\n      intlService: intlService,\n      formatPlaceholder: formatPlaceholder,\n      format: format,\n      cycleTime: cycleTime,\n      twoDigitYearMax: twoDigitYearMax,\n      value: value,\n      autoCorrectParts: autoCorrectParts,\n      toggleDayPeriod: toggleDayPeriod,\n      autoSwitchParts: autoSwitchParts\n    });\n    if (!value) {\n      this._value = this.getDefaultDate();\n      var sampleFormat = this.dateFormatString(this.value, this.format).symbols;\n      for (var i = 0; i < sampleFormat.length; i++) {\n        this.setExisting(sampleFormat[i], false);\n      }\n    } else {\n      this._value = cloneDate(value);\n    }\n  }\n  Object.defineProperty(DateObject.prototype, \"value\", {\n    get: function () {\n      return this._value;\n    },\n    set: function (value) {\n      if (value && !(value instanceof Date)) {\n        // throw new Error(\"The 'value' should be a valid JavaScript Date instance.\");\n        return;\n      }\n      this._value = value;\n      this.resetInvalidDate();\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(DateObject.prototype, \"localeId\", {\n    get: function () {\n      var localeId = Constants.defaultLocaleId;\n      var cldrKeys = Object.keys(this.intl.cldr);\n      for (var i = 0; i < cldrKeys.length; i++) {\n        var key = cldrKeys[i];\n        var value = this.intl.cldr[key];\n        if (value.name && value.calendar && value.numbers && value.name !== Constants.defaultLocaleId) {\n          localeId = value.name;\n          break;\n        }\n      }\n      return localeId;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  /**\n   * @hidden\n   */\n  DateObject.prototype.setOptions = function (options) {\n    this.intl = options.intlService;\n    this.formatPlaceholder = options.formatPlaceholder || 'wide';\n    this.format = options.format;\n    this.cycleTime = options.cycleTime;\n    this.monthNames = this.allFormattedMonths(this.localeId);\n    this.dayPeriods = this.allDayPeriods(this.localeId);\n    this.twoDigitYearMax = options.twoDigitYearMax;\n    this.autoCorrectParts = options.autoCorrectParts;\n    this.toggleDayPeriod = options.toggleDayPeriod;\n    this.autoSwitchParts = options.autoSwitchParts;\n  };\n  DateObject.prototype.setValue = function (value) {\n    if (!value) {\n      this._value = this.getDefaultDate();\n      this.modifyExisting(false);\n    } else if (!isEqual(value, this._value)) {\n      this._value = cloneDate(value);\n      this.modifyExisting(true);\n    } else if (isEqual(value, this._value) && this.dayPeriods) {\n      this.setExisting('a', true);\n    }\n    this.resetInvalidDate();\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.hasValue = function () {\n    var _this = this;\n    var pred = function (a, p) {\n      return a || p.type !== 'literal' && p.type !== 'dayperiod' && _this.getExisting(p.pattern[0]);\n    };\n    return this.intl.splitDateFormat(this.format, this.localeId).reduce(pred, false);\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.getValue = function () {\n    for (var i = 0; i < this.knownParts.length; i++) {\n      if (!this.getExisting(this.knownParts[i])) {\n        return null;\n      }\n    }\n    return cloneDate(this.value);\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.createDefaultDate = function () {\n    // use the leap year 2000 that has 29th February\n    // and a month that has 31 days\n    // so that the default date can accommodate maximum date values\n    // it is better to use a fixed date instead of new Date()\n    return createDate(DEFAULT_LEAP_YEAR, 0, 31);\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.getDefaultDate = function () {\n    return getDate(this.createDefaultDate());\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.getFormattedDate = function (format) {\n    return this.intl.formatDate(this.getValue(), format, this.localeId);\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.getTextAndFormat = function (customFormat) {\n    if (customFormat === void 0) {\n      customFormat = \"\";\n    }\n    var format = customFormat || this.format;\n    var text = this.intl.formatDate(this.value, format, this.localeId);\n    var mask = this.dateFormatString(this.value, format);\n    if (!this.autoCorrectParts && this._partiallyInvalidDate.startDate) {\n      var partiallyInvalidText = \"\";\n      var formattedDate = this.intl.formatDate(this.value, format, this.localeId);\n      var formattedDates = this.getFormattedInvalidDates(format);\n      for (var i = 0; i < formattedDate.length; i++) {\n        var symbol = mask.symbols[i];\n        if (mask.partMap[i].type === \"literal\") {\n          partiallyInvalidText += text[i];\n        } else if (this.getInvalidDatePartValue(symbol)) {\n          var partsForSegment = this.getPartsForSegment(mask, i);\n          if (symbol === \"M\") {\n            var datePartText = (parseToInt(this.getInvalidDatePartValue(symbol)) + JS_MONTH_OFFSET).toString();\n            if (partsForSegment.length > MONTH_PART_WITH_WORDS_THRESHOLD) {\n              partiallyInvalidText += formattedDates[symbol][i];\n            } else {\n              if (this.getInvalidDatePartValue(symbol)) {\n                var formattedDatePart = padZero(partsForSegment.length - datePartText.length) + datePartText;\n                partiallyInvalidText += formattedDatePart;\n                // add -1 as the first character in the segment is at index i\n                i += partsForSegment.length - 1;\n              } else {\n                partiallyInvalidText += formattedDates[symbol][i];\n              }\n            }\n          } else {\n            if (this.getInvalidDatePartValue(symbol)) {\n              var datePartText = this.getInvalidDatePartValue(symbol).toString();\n              var formattedDatePart = padZero(partsForSegment.length - datePartText.length) + datePartText;\n              partiallyInvalidText += formattedDatePart;\n              // add -1 as the first character in the segment is at index i\n              i += partsForSegment.length - 1;\n            } else {\n              partiallyInvalidText += formattedDates[symbol][i];\n            }\n          }\n        } else {\n          partiallyInvalidText += text[i];\n        }\n      }\n      text = partiallyInvalidText;\n    }\n    var result = this.merge(text, mask);\n    return result;\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.getFormattedInvalidDates = function (customFormat) {\n    var _this = this;\n    if (customFormat === void 0) {\n      customFormat = \"\";\n    }\n    var format = customFormat || this.format;\n    var formattedDatesForSymbol = {\n      'E': '',\n      'H': '',\n      'M': '',\n      'a': '',\n      'd': '',\n      'h': '',\n      'm': '',\n      's': '',\n      'y': '',\n      'S': ''\n    };\n    Object.keys(this._partiallyInvalidDate.invalidDateParts).forEach(function (key) {\n      var date = _this.getInvalidDatePart(key).date;\n      if (date) {\n        var formattedInvalidDate = _this.intl.formatDate(date, format, _this.localeId);\n        formattedDatesForSymbol[key] = formattedInvalidDate;\n      }\n    });\n    return formattedDatesForSymbol;\n  };\n  DateObject.prototype.modifyExisting = function (value) {\n    var sampleFormat = this.dateFormatString(this.value, this.format).symbols;\n    for (var i = 0; i < sampleFormat.length; i++) {\n      this.setExisting(sampleFormat[i], value);\n    }\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.getExisting = function (symbol) {\n    switch (symbol) {\n      case 'y':\n        return this.year;\n      case 'M':\n      case 'L':\n        return this.month;\n      case 'd':\n        return this.date;\n      case 'E':\n        return this.date && this.month && this.year;\n      case 'h':\n      case 'H':\n        return this.hours;\n      case 't':\n      case 'a':\n        return this.dayperiod;\n      case 'm':\n        return this.minutes;\n      case 's':\n        return this.seconds;\n      case \"S\":\n        return this.milliseconds;\n      default:\n        return true;\n    }\n  };\n  DateObject.prototype.setExisting = function (symbol, value) {\n    switch (symbol) {\n      case 'y':\n        // allow 2/29 dates\n        this.year = value;\n        if (value === false) {\n          this._value.setFullYear(DEFAULT_LEAP_YEAR);\n        }\n        break;\n      case 'M':\n        // make sure you can type 31 in the day part\n        this.month = value;\n        if (value === false) {\n          if (this.autoCorrectParts) {\n            this._value.setMonth(0);\n          }\n        }\n        break;\n      case 'd':\n        this.date = value;\n        break;\n      case 'h':\n      case 'H':\n        this.hours = value;\n        break;\n      case 't':\n      case 'a':\n        this.dayperiod = value;\n        break;\n      case 'm':\n        this.minutes = value;\n        break;\n      case 's':\n        this.seconds = value;\n        break;\n      case \"S\":\n        this.milliseconds = value;\n        break;\n      default:\n        break;\n    }\n    if (this.getValue()) {\n      this.resetInvalidDate();\n    }\n  };\n  DateObject.prototype.modifyPart = function (symbol, offset) {\n    if (!isPresent(symbol) || !isPresent(offset) || offset === 0) {\n      return;\n    }\n    var newValue = cloneDate(this.value);\n    var timeModified = false;\n    var invalidDateFound;\n    var isMonth = symbol === \"M\";\n    var isDay = symbol === \"d\" || symbol === \"E\";\n    var symbolExists = this.getExisting(symbol);\n    if (!this.autoCorrectParts && (isDay || isMonth)) {\n      var invalidDateParts = this._partiallyInvalidDate.invalidDateParts || {};\n      var invalidDatePartValue = this.getInvalidDatePartValue(symbol);\n      var year = invalidDateParts.y.value || newValue.getFullYear();\n      var month = invalidDateParts.M.value || newValue.getMonth();\n      var day = invalidDateParts.d.value || invalidDateParts.E.value || newValue.getDate();\n      var hour = invalidDateParts.h.value || invalidDateParts.H.value || newValue.getHours();\n      var minutes = invalidDateParts.m.value || newValue.getMinutes();\n      var seconds = invalidDateParts.s.value || newValue.getSeconds();\n      var milliseconds = invalidDateParts.S.value || newValue.getMilliseconds();\n      switch (symbol) {\n        case 'y':\n          year += offset;\n          break;\n        case 'M':\n          month += offset;\n          break;\n        case 'd':\n        case 'E':\n          day += offset;\n          break;\n        // case 'h':\n        // case 'H': hour += offset; break;\n        // case 'm': minutes += offset; break;\n        // case 's': seconds += offset; break;\n        // case 'S': milliseconds += offset; break;\n        default:\n          break;\n      }\n      if (symbol === \"M\") {\n        if (month < 0 || month > 11) {\n          if (symbolExists) {\n            this.setExisting(symbol, false);\n            this.resetInvalidDateSymbol(symbol);\n            return;\n          }\n        }\n        if (!symbolExists) {\n          if (month < 0) {\n            month = clamp(11 + (month % 11 + 1), 0, 11);\n          } else {\n            var monthValue = isPresent(invalidDatePartValue) ? month : (offset - JS_MONTH_OFFSET) % 12;\n            month = clamp(monthValue, 0, 11);\n          }\n          month = clamp(month, 0, 11);\n        }\n        month = clamp(month, 0, 11);\n      } else if (symbol === \"d\") {\n        if (symbolExists) {\n          if (day <= 0 || day > 31) {\n            this.setExisting(symbol, false);\n            this.resetInvalidDateSymbol(symbol);\n            return;\n          }\n        } else if (!symbolExists) {\n          if (isPresent(invalidDatePartValue)) {\n            if (day <= 0 || day > 31) {\n              this.setExisting(symbol, false);\n              this.resetInvalidDateSymbol(symbol);\n              return;\n            }\n          }\n          if (offset < 0) {\n            var dayValue = isPresent(invalidDatePartValue) ? day : 1 + (31 - Math.abs(offset % 31));\n            day = clamp(dayValue, 1, 31);\n          } else {\n            var dayValue = isPresent(invalidDatePartValue) ? day : offset % 31;\n            day = clamp(dayValue, 1, 31);\n          }\n          day = clamp(day, 1, 31);\n        }\n      }\n      var dateCandidate = createDate(year, month, day, hour, minutes, seconds, milliseconds);\n      var newValueCandidate = isMonth || isDay ? this.modifyDateSymbolWithValue(newValue, symbol, isMonth ? month : day) : null;\n      var dateCandidateExists = areDatePartsEqualTo(dateCandidate, year, month, day, hour, minutes, seconds, milliseconds);\n      if (this.getValue() && areDatePartsEqualTo(dateCandidate, year, month, day, hour, minutes, seconds, milliseconds)) {\n        newValue = cloneDate(dateCandidate);\n        this.markDatePartsAsExisting();\n      } else if (isMonth && newValueCandidate) {\n        if (newValueCandidate.getMonth() === month) {\n          if (this.getExisting(\"d\")) {\n            if (dateCandidateExists) {\n              newValue = cloneDate(dateCandidate);\n              this.resetInvalidDateSymbol(symbol);\n            } else {\n              invalidDateFound = true;\n              this.setInvalidDatePart(symbol, {\n                value: month,\n                date: cloneDate(newValueCandidate),\n                startDateOffset: offset,\n                startDate: cloneDate(this.value)\n              });\n              this.setExisting(symbol, false);\n            }\n          } else if (dateCandidateExists) {\n            this.resetInvalidDateSymbol(symbol);\n            newValue = cloneDate(dateCandidate);\n            if (this.getExisting(\"M\") && this.getExisting(\"y\")) {\n              // changing from 28/Feb to 29/Feb to 29/March\n              this.setExisting(\"d\", true);\n              this.resetInvalidDateSymbol(\"d\");\n            }\n          } else {\n            this.resetInvalidDateSymbol(symbol);\n            newValue = cloneDate(newValueCandidate);\n          }\n        } else {\n          invalidDateFound = true;\n          this.setInvalidDatePart(symbol, {\n            value: month,\n            date: cloneDate(newValueCandidate),\n            startDateOffset: offset,\n            startDate: cloneDate(this.value)\n          });\n          this.setExisting(symbol, false);\n        }\n      } else if (isDay && newValueCandidate) {\n        if (newValueCandidate.getDate() === day) {\n          if (this.getExisting(\"M\")) {\n            if (dateCandidateExists) {\n              newValue = cloneDate(dateCandidate);\n              this.resetInvalidDateSymbol(symbol);\n            } else {\n              invalidDateFound = true;\n              this.setInvalidDatePart(symbol, {\n                value: day,\n                date: cloneDate(newValueCandidate),\n                startDateOffset: offset,\n                startDate: cloneDate(this.value)\n              });\n              this.setExisting(symbol, false);\n            }\n          } else if (dateCandidateExists) {\n            newValue = cloneDate(dateCandidate);\n            this.resetInvalidDateSymbol(symbol);\n            if (this.getExisting(\"d\") && this.getExisting(\"y\")) {\n              // changing from 31/Jan to 31/Feb to 28/Feb\n              this.setExisting(\"M\", true);\n              this.resetInvalidDateSymbol(\"M\");\n            }\n          } else {\n            this.resetInvalidDateSymbol(symbol);\n            newValue = cloneDate(newValueCandidate);\n          }\n        } else {\n          invalidDateFound = true;\n          this.setInvalidDatePart(symbol, {\n            value: day,\n            date: cloneDate(this.value),\n            startDateOffset: offset,\n            startDate: cloneDate(this.value)\n          });\n          this.setExisting(symbol, false);\n        }\n      }\n    } else {\n      var hours = newValue.getHours();\n      switch (symbol) {\n        case 'y':\n          newValue.setFullYear(newValue.getFullYear() + offset);\n          break;\n        case 'M':\n          newValue = addMonths(this.value, offset);\n          break;\n        case 'd':\n        case 'E':\n          newValue.setDate(newValue.getDate() + offset);\n          break;\n        case 'h':\n        case 'H':\n          newValue.setHours(newValue.getHours() + offset);\n          timeModified = true;\n          break;\n        case 'm':\n          newValue.setMinutes(newValue.getMinutes() + offset);\n          timeModified = true;\n          break;\n        case 's':\n          newValue.setSeconds(newValue.getSeconds() + offset);\n          timeModified = true;\n          break;\n        case \"S\":\n          newValue.setMilliseconds(newValue.getMilliseconds() + offset);\n          break;\n        case 'a':\n          if (this.toggleDayPeriod) {\n            newValue.setHours(hours >= 12 ? hours - 12 : hours + 12);\n          } else {\n            newValue.setHours(newValue.getHours() + 12 * offset);\n          }\n          timeModified = true;\n          break;\n        default:\n          break;\n      }\n    }\n    if (this.shouldNormalizeCentury()) {\n      newValue = this.normalizeCentury(newValue);\n    }\n    if (timeModified && !this.cycleTime && newValue.getDate() !== this._value.getDate()) {\n      // todo: blazor has this fix, but this fails a unit test\n      // newValue.setDate(this._value.getDate());\n      // newValue.setMonth(this._value.getMonth());\n      // newValue.setFullYear(this._value.getFullYear());\n    }\n    if (!invalidDateFound) {\n      this.setExisting(symbol, true);\n      this._value = newValue;\n      if (this.getValue()) {\n        this.resetInvalidDate();\n      }\n    }\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.parsePart = function (_a) {\n    var symbol = _a.symbol,\n      currentChar = _a.currentChar,\n      resetSegmentValue = _a.resetSegmentValue,\n      cycleSegmentValue = _a.cycleSegmentValue,\n      rawInputValue = _a.rawTextValue,\n      isDeleting = _a.isDeleting,\n      originalFormat = _a.originalFormat;\n    var isInCaretMode = !cycleSegmentValue;\n    var dateParts = this.dateFormatString(this.value, this.format);\n    var datePartsLiterals = dateParts.partMap.filter(function (x) {\n      return x.type === \"literal\";\n    }).map(function (x, index) {\n      return {\n        datePartIndex: index,\n        type: x.type,\n        pattern: x.pattern,\n        literal: \"\"\n      };\n    });\n    var flatDateParts = dateParts.partMap.map(function (x) {\n      return {\n        type: x.type,\n        pattern: x.pattern,\n        text: \"\"\n      };\n    });\n    for (var i = 0; i < datePartsLiterals.length; i++) {\n      var datePart = datePartsLiterals[i];\n      for (var j = 0; j < datePart.pattern.length; j++) {\n        if (datePartsLiterals[i + j]) {\n          datePartsLiterals[i + j].literal = datePart.pattern[j];\n        }\n      }\n      i += datePart.pattern.length - 1;\n    }\n    for (var i = 0; i < flatDateParts.length; i++) {\n      var datePart = flatDateParts[i];\n      for (var j = 0; j < datePart.pattern.length; j++) {\n        if (flatDateParts[i + j]) {\n          flatDateParts[i + j].text = datePart.pattern[j];\n        }\n      }\n      i += datePart.pattern.length - 1;\n    }\n    var shouldResetPart = isInCaretMode && symbol === \"M\" && dateParts.partMap.filter(function (x) {\n      return x.type === \"month\";\n    }).some(function (x) {\n      return x.pattern.length > MONTH_PART_WITH_WORDS_THRESHOLD;\n    });\n    var parseResult = {\n      value: null,\n      switchToNext: false,\n      resetPart: shouldResetPart,\n      hasInvalidDatePart: false\n    };\n    if (!currentChar) {\n      if (isInCaretMode) {\n        for (var i = 0; i < datePartsLiterals.length; i++) {\n          var literal = datePartsLiterals[i].literal;\n          var rawValueStartsWithLiteral = rawInputValue.startsWith(literal);\n          var rawValueEndsWithLiteral = rawInputValue.endsWith(literal);\n          var rawValueHasConsecutiveLiterals = rawInputValue.indexOf(literal + literal) >= 0;\n          if (rawValueStartsWithLiteral || rawValueEndsWithLiteral || rawValueHasConsecutiveLiterals) {\n            this.resetLeadingZero();\n            this.setExisting(symbol, false);\n            this.resetInvalidDateSymbol(symbol);\n            return extend(parseResult, {\n              value: null,\n              switchToNext: false\n            });\n          }\n        }\n      } else {\n        this.resetLeadingZero();\n        this.setExisting(symbol, false);\n        this.resetInvalidDateSymbol(symbol);\n        return extend(parseResult, {\n          value: null,\n          switchToNext: false\n        });\n      }\n    }\n    var baseDate = this.intl.formatDate(this.value, this.format, this.localeId);\n    var baseFormat = dateParts.symbols;\n    var replaced = false;\n    var prefix = '';\n    var current = '';\n    var datePartText = '';\n    var basePrefix = '';\n    var baseSuffix = '';\n    var suffix = '';\n    var convertedBaseFormat = \"\";\n    for (var i = 0; i < flatDateParts.length; i++) {\n      convertedBaseFormat += flatDateParts[i].text;\n    }\n    var hasFixedFormat = this.format === baseFormat || this.format === convertedBaseFormat || this.format === originalFormat || this.format.length === originalFormat.length;\n    var datePartStartIndex = (hasFixedFormat ? convertedBaseFormat : originalFormat).indexOf(symbol);\n    var datePartEndIndex = (hasFixedFormat ? convertedBaseFormat : originalFormat).lastIndexOf(symbol);\n    var segmentLength = datePartEndIndex - datePartStartIndex + 1;\n    var formatToTextLengthDiff = originalFormat.length - rawInputValue.length;\n    if (isInCaretMode || !isInCaretMode && !this.autoCorrectParts) {\n      var segmentCharIndex = 0;\n      for (var i = 0; i < baseDate.length; i++) {\n        if (baseFormat[i] === symbol) {\n          var existing = this.getExisting(symbol);\n          if (symbol === \"y\") {\n            if (!this.hasInvalidDatePart() && this.getExisting(\"y\")) {\n              current += baseDate[i];\n            } else {\n              var invalidDatePartValue = this.getInvalidDatePartValue(symbol);\n              if (isPresent(invalidDatePartValue)) {\n                current += (invalidDatePartValue || \"\").toString()[segmentCharIndex] || \"\";\n                segmentCharIndex++;\n              } else {\n                current += existing ? baseDate[i] : '0';\n              }\n            }\n          } else {\n            current += existing ? baseDate[i] : '0';\n          }\n          if (formatToTextLengthDiff > 0) {\n            if (datePartText.length + formatToTextLengthDiff < segmentLength) {\n              datePartText += rawInputValue[i] || \"\";\n            }\n          } else {\n            datePartText += rawInputValue[i] || \"\";\n          }\n          replaced = true;\n        } else if (!replaced) {\n          prefix += baseDate[i];\n          basePrefix += baseDate[i];\n        } else {\n          suffix += baseDate[i];\n          baseSuffix += baseDate[i];\n        }\n      }\n      if (hasFixedFormat) {\n        if (convertedBaseFormat.length < rawInputValue.length) {\n          datePartText += currentChar;\n        } else if (!isDeleting && originalFormat.length > rawInputValue.length) {\n          // let the parsing to determine if the incomplete value is valid\n        }\n        if (datePartText.length > segmentLength) {\n          return extend(parseResult, {\n            value: null,\n            switchToNext: false\n          });\n        }\n      }\n      if (!hasFixedFormat || hasFixedFormat && !this.autoCorrectParts) {\n        current = \"\";\n        datePartText = \"\";\n        prefix = \"\";\n        suffix = \"\";\n        replaced = false;\n        var segmentCharIndex_1 = 0;\n        for (var i = 0; i < originalFormat.length; i++) {\n          if (originalFormat[i] === symbol) {\n            var existing = this.getExisting(symbol);\n            if (symbol === \"y\") {\n              if (!this.hasInvalidDatePart() && this.getExisting(\"y\")) {\n                current += baseDate[i];\n              } else {\n                var invalidDatePartValue = this.getInvalidDatePartValue(symbol);\n                if (isPresent(invalidDatePartValue)) {\n                  current += (invalidDatePartValue || \"\").toString()[segmentCharIndex_1] || \"\";\n                  segmentCharIndex_1++;\n                } else {\n                  current += existing ? baseDate[i] : '0';\n                }\n              }\n            } else {\n              current += existing ? baseDate[i] || \"\" : '0';\n            }\n            if (formatToTextLengthDiff > 0) {\n              if (datePartText.length + formatToTextLengthDiff < segmentLength) {\n                datePartText += rawInputValue[i] || \"\";\n              }\n            } else {\n              datePartText += rawInputValue[i] || \"\";\n            }\n            replaced = true;\n          } else if (!replaced) {\n            prefix += rawInputValue[i] || \"\";\n          } else {\n            suffix += rawInputValue[i - formatToTextLengthDiff] || \"\";\n          }\n        }\n        if (originalFormat.length < rawInputValue.length) {\n          datePartText += currentChar;\n        }\n      }\n    }\n    if (!isInCaretMode) {\n      if (this.autoCorrectParts) {\n        current = \"\";\n        datePartText = \"\";\n        prefix = \"\";\n        suffix = \"\";\n        replaced = false;\n        for (var i = 0; i < baseDate.length; i++) {\n          if (baseFormat[i] === symbol) {\n            var existing = this.getExisting(symbol);\n            current += existing ? baseDate[i] : '0';\n            replaced = true;\n          } else if (!replaced) {\n            prefix += baseDate[i];\n          } else {\n            suffix += baseDate[i];\n          }\n        }\n      } else {\n        current = resetSegmentValue ? datePartText : current;\n      }\n    }\n    var parsedDate = null;\n    var monthByChar = this.matchMonth(currentChar);\n    var dayPeriod = this.matchDayPeriod(currentChar, symbol);\n    var isZeroCurrentChar = currentChar === '0';\n    var leadingZero = this.leadingZero || {};\n    if (isZeroCurrentChar) {\n      if (datePartText === \"0\") {\n        datePartText = current;\n      }\n      var valueNumber = parseToInt(resetSegmentValue ? currentChar : (isInCaretMode ? datePartText : current) + currentChar);\n      if (valueNumber === 0 && !this.isAbbrMonth(dateParts.partMap, symbol) && symbol !== \"a\") {\n        this.incrementLeadingZero(symbol);\n      }\n    } else {\n      this.resetLeadingZero();\n    }\n    var partPattern = this.partPattern(dateParts.partMap, symbol);\n    var patternValue = partPattern ? partPattern.pattern : null;\n    var patternLength = this.patternLength(patternValue) || patternValue.length;\n    if (isInCaretMode) {\n      if (isDeleting && !datePartText) {\n        this.setExisting(symbol, false);\n        return extend(parseResult, {\n          value: null,\n          switchToNext: false\n        });\n      }\n    }\n    var currentMaxLength = current.length - 3;\n    var tryParse = true;\n    var middle = isInCaretMode ? datePartText : current;\n    for (var i = Math.max(0, currentMaxLength); i <= current.length; i++) {\n      if (!tryParse) {\n        break;\n      }\n      middle = resetSegmentValue ? currentChar : isInCaretMode ? datePartText : current.substring(i) + currentChar;\n      if (this.autoSwitchParts && symbol === 'h' && current.substring(i) === '12') {\n        middle = middle.replace('12', '0');\n      }\n      if (isInCaretMode || !this.autoCorrectParts) {\n        tryParse = false;\n        middle = unpadZero(middle);\n        // middle = padZero(segmentLength - middle.length) + middle;\n        middle = padZero(patternLength - middle.length) + middle;\n      }\n      var middleNumber = parseInt(middle, 10);\n      var candidateDateString = prefix + middle + suffix;\n      parsedDate = this.intl.parseDate(candidateDateString, this.format, this.localeId);\n      // if the format does not include year/month/day, e.g. hh:mm:ss\n      // then the exact date cannot be inferred as there is no data for it\n      // thus the today's date is used, but revert to the original date\n      // to keep other parts of the date unchanged\n      if (parsedDate && this.value && dateParts.partMap.every(function (x) {\n        return x.type !== \"year\" && x.type !== \"month\" && x.type != \"day\";\n      })) {\n        parsedDate.setFullYear(this.value.getFullYear());\n        parsedDate.setMonth(this.value.getMonth());\n        parsedDate.setDate(this.value.getDate());\n      }\n      var autoCorrectedPrefixAndSuffix = false;\n      if (isInCaretMode && !isValidDate(parsedDate)) {\n        // if part of the date is not available, e.g. \"d\"\n        // but an expanded format like \"F\" is used\n        // the element value can be \"EEEE, February 1, 2022 3:04:05 AM\"\n        // which is not parsable by intl\n        // use the base prefix and suffix, e.g. convert the candidate date string\n        // to \"Thursday, February 1, 2022 3:04:05 AM\"\n        // as \"EEEE, February...\" is not parsable\n        if (this.autoCorrectParts) {\n          parsedDate = this.intl.parseDate(basePrefix + middle + baseSuffix, this.format, this.localeId);\n          autoCorrectedPrefixAndSuffix = true;\n        }\n      }\n      var isCurrentCharParsable = !isNaN(parseInt(currentChar, 10)) || isInCaretMode && isDeleting && currentChar === \"\";\n      if (!parsedDate && !isNaN(middleNumber) && isCurrentCharParsable && this.autoCorrectParts) {\n        if (symbol === MONTH_SYMBOL && !monthByChar) {\n          // JS months start from 0 (January) instead of 1 (January)\n          var monthNumber = middleNumber - JS_MONTH_OFFSET;\n          if (monthNumber > -1 && monthNumber < 12) {\n            parsedDate = cloneDate(this.value);\n            parsedDate.setMonth(monthNumber);\n            if (parsedDate.getMonth() !== monthNumber) {\n              parsedDate = lastDayOfMonth(addMonths(parsedDate, -1));\n            }\n          }\n        }\n        if (symbol === 'y') {\n          parsedDate = createDate(parseInt(middle, 10), this.month ? this.value.getMonth() : 0, this.date ? this.value.getDate() : 1, this.hours ? this.value.getHours() : 0, this.minutes ? this.value.getMinutes() : 0, this.seconds ? this.value.getSeconds() : 0, this.milliseconds ? this.value.getMilliseconds() : 0);\n          if ((isInCaretMode && isValidDate(parsedDate) || !isInCaretMode && parsedDate) && this.date && parsedDate.getDate() !== this.value.getDate()) {\n            parsedDate = lastDayOfMonth(addMonths(parsedDate, -1));\n          }\n        }\n      }\n      if (isInCaretMode && isValidDate(parsedDate) || !isInCaretMode && parsedDate) {\n        // move to next segment if the part will overflow with next char\n        // when start from empty date (01, then 010), padded zeros should be trimmed\n        var peekResult = this.isPeekDateOverflowingDatePart({\n          useBasePrefixAndSuffix: autoCorrectedPrefixAndSuffix,\n          middle: middle,\n          patternValue: patternValue,\n          basePrefix: basePrefix,\n          baseSuffix: baseSuffix,\n          prefix: prefix,\n          suffix: suffix,\n          symbol: symbol,\n          patternLength: patternLength,\n          leadingZero: leadingZero\n        });\n        var switchToNext = peekResult.switchToNext;\n        if (this.shouldNormalizeCentury()) {\n          parsedDate = this.normalizeCentury(parsedDate);\n        }\n        if (symbol === 'H' && parsedDate.getHours() >= 12) {\n          this.setExisting('a', true);\n        }\n        this._value = parsedDate;\n        this.setExisting(symbol, true);\n        this.resetInvalidDateSymbol(symbol);\n        if (!this.autoCorrectParts) {\n          if (symbol === \"M\") {\n            if (this.getExisting(\"M\") && this.getExisting(\"y\")) {\n              // changing from 28/Feb to 29/Feb to 29/March\n              this.setExisting(\"d\", true);\n              this.resetInvalidDateSymbol(\"d\");\n            }\n          } else if (symbol === \"d\") {\n            if (this.getExisting(\"d\") && this.getExisting(\"y\")) {\n              // changing from 31/Jan to 31/Feb to 28/Feb\n              this.setExisting(\"M\", true);\n              this.resetInvalidDateSymbol(\"M\");\n            }\n          } else if (symbol === \"y\") {\n            // if the parsed date is valid, make the whole value valid\n            this.markDatePartsAsExisting();\n          }\n          if (!this.hasInvalidDatePart()) {\n            this.markDatePartsAsExisting();\n            if (!peekResult.peekedDate && peekResult.switchToNext && !this.autoCorrectParts) {\n              if (symbol === \"M\") {\n                // skip processing the month\n              } else if (symbol === \"d\") {\n                if (peekResult.parsedPeekedValue === 30 && this.value.getMonth() === MONTH_INDEX_FEBRUARY) {\n                  // the peekValue cannot be constructed\n                  // as there cannot be more than 29 days in February\n                  // still the segment should not be switched as autoCorrectParts=\"false\"\n                  // should allow typing \"30\"\n                  switchToNext = false;\n                }\n              }\n            }\n          }\n        }\n        return extend(parseResult, {\n          value: this.value,\n          switchToNext: switchToNext\n        });\n      }\n    }\n    if (monthByChar) {\n      parsedDate = this.intl.parseDate(prefix + monthByChar + suffix, this.format, this.localeId);\n      if (parsedDate) {\n        this._value = parsedDate;\n        this.setExisting(symbol, true);\n        return extend(parseResult, {\n          value: this.value,\n          switchToNext: false\n        });\n      }\n    }\n    if (dayPeriod) {\n      parsedDate = this.intl.parseDate(prefix + dayPeriod + suffix, this.format) || this.intl.parseDate(basePrefix + dayPeriod + baseSuffix, this.format);\n      if (parsedDate) {\n        this._value = parsedDate;\n        this.setExisting(symbol, true);\n        return extend(parseResult, {\n          value: this.value,\n          switchToNext: true\n        });\n      }\n    }\n    if (isZeroCurrentChar && symbol !== \"a\") {\n      this.setExisting(symbol, false);\n    }\n    if (!this.autoCorrectParts) {\n      var datePartValue = void 0;\n      var textToParse = isInCaretMode ? datePartText : middle;\n      var parsedValue = parseToInt(textToParse);\n      if (isNumber(parsedValue) && isParseableToInt(textToParse)) {\n        if (symbol === \"d\" && (parsedValue <= 0 || parsedValue > 31) || symbol === \"M\" && (parsedValue <= 0 || parsedValue > 11)) {\n          if (isInCaretMode) {\n            return extend(parseResult, {\n              value: null,\n              switchToNext: false\n            });\n          } else {\n            // the value overflows the possible value range\n            // thus reset the segment value regardless of the \"resetSegmentValue\" flag\n            // otherwise the input is ignored and you cannot change the value,\n            // e.g. \"03->(press 2)->02\" will not work and the user will be blocked on \"03\"\n            textToParse = currentChar;\n            parsedValue = parseToInt(textToParse);\n          }\n        }\n        if (!isNumber(parsedValue) || !isParseableToInt(textToParse)) {\n          return extend(parseResult, {\n            value: null,\n            switchToNext: false\n          });\n        }\n        datePartValue = symbol === \"M\" ? parsedValue - JS_MONTH_OFFSET : parsedValue;\n        var isYear = symbol === \"y\";\n        var isMonth = symbol === \"M\";\n        var isDay = symbol === \"d\";\n        var newValue = cloneDate(this._value);\n        var invalidDateParts = this._partiallyInvalidDate.invalidDateParts || {};\n        var year = isYear ? datePartValue : invalidDateParts.y.value || newValue.getFullYear();\n        /* tslint:disable:no-shadowed-variable */\n        var month = isMonth ? datePartValue : invalidDateParts.M.value || newValue.getMonth();\n        /* tslint:enable:no-shadowed-variable */\n        var day = isDay ? datePartValue : invalidDateParts.d.value || invalidDateParts.E.value || newValue.getDate();\n        var hour = invalidDateParts.h.value || invalidDateParts.H.value || newValue.getHours();\n        var minutes = invalidDateParts.m.value || newValue.getMinutes();\n        var seconds = invalidDateParts.s.value || newValue.getSeconds();\n        var milliseconds = invalidDateParts.S.value || newValue.getMilliseconds();\n        var dateCandidate = createDate(year, month, day, hour, minutes, seconds, milliseconds);\n        var dateCandidateExists = areDatePartsEqualTo(dateCandidate, year, month, day, hour, minutes, seconds, milliseconds);\n        var newValueCandidate = isYear || isMonth || isDay ? this.modifyDateSymbolWithValue(newValue, symbol, isYear ? year : isMonth ? month : day) : null;\n        var invalidDateFound = false;\n        if (isMonth && newValueCandidate) {\n          if (newValueCandidate.getMonth() === month) {\n            if (this.getExisting(\"d\")) {\n              if (dateCandidateExists) {\n                newValue = cloneDate(dateCandidate);\n                this.resetInvalidDateSymbol(symbol);\n              } else {\n                invalidDateFound = true;\n                this.setInvalidDatePart(symbol, {\n                  value: month,\n                  date: cloneDate(newValueCandidate),\n                  startDate: cloneDate(this.value)\n                });\n                this.setExisting(symbol, false);\n              }\n            } else if (dateCandidateExists) {\n              this.resetInvalidDateSymbol(symbol);\n              newValue = cloneDate(dateCandidate);\n              if (this.getExisting(\"M\") && this.getExisting(\"y\")) {\n                // changing from 28/Feb to 29/Feb to 29/March\n                this.setExisting(\"d\", true);\n                this.resetInvalidDateSymbol(\"d\");\n              }\n            } else {\n              this.resetInvalidDateSymbol(symbol);\n              newValue = cloneDate(newValueCandidate);\n            }\n          } else {\n            invalidDateFound = true;\n            this.setInvalidDatePart(symbol, {\n              value: month,\n              date: cloneDate(newValueCandidate),\n              startDate: cloneDate(this.value)\n            });\n            this.setExisting(symbol, false);\n          }\n        } else if (isDay && newValueCandidate) {\n          if (newValueCandidate.getDate() === day) {\n            if (this.getExisting(\"M\")) {\n              if (dateCandidateExists) {\n                newValue = cloneDate(dateCandidate);\n                this.resetInvalidDateSymbol(symbol);\n              } else {\n                invalidDateFound = true;\n                this.setInvalidDatePart(symbol, {\n                  value: day,\n                  date: cloneDate(newValueCandidate),\n                  startDate: cloneDate(this.value)\n                });\n                this.setExisting(symbol, false);\n              }\n            } else if (dateCandidateExists) {\n              newValue = cloneDate(dateCandidate);\n              this.resetInvalidDateSymbol(symbol);\n              if (this.getExisting(\"d\") && this.getExisting(\"y\")) {\n                // changing from 31/Jan to 31/Feb to 28/Feb\n                this.setExisting(\"M\", true);\n                this.resetInvalidDateSymbol(\"M\");\n              }\n            } else {\n              this.resetInvalidDateSymbol(symbol);\n              newValue = cloneDate(newValueCandidate);\n            }\n          } else {\n            invalidDateFound = true;\n            this.setInvalidDatePart(symbol, {\n              value: day,\n              date: cloneDate(this.value),\n              startDate: cloneDate(this.value)\n            });\n            this.setExisting(symbol, false);\n          }\n        } else if (isYear && newValueCandidate) {\n          if (newValueCandidate.getFullYear() === year) {\n            if (this.getExisting(\"d\") && this.getExisting(\"M\")) {\n              if (dateCandidateExists) {\n                newValue = cloneDate(dateCandidate);\n                this.resetInvalidDateSymbol(symbol);\n              } else {\n                invalidDateFound = true;\n                this.setInvalidDatePart(symbol, {\n                  value: year,\n                  date: cloneDate(newValueCandidate),\n                  startDate: cloneDate(this.value)\n                });\n                this.setExisting(symbol, false);\n              }\n            } else if (dateCandidateExists) {\n              this.resetInvalidDateSymbol(symbol);\n              newValue = cloneDate(dateCandidate);\n              if (this.getExisting(\"M\") && this.getExisting(\"d\")) {\n                this.setExisting(\"y\", true);\n                this.resetInvalidDateSymbol(\"y\");\n              }\n            } else {\n              this.resetInvalidDateSymbol(symbol);\n              newValue = cloneDate(newValueCandidate);\n            }\n          } else {\n            invalidDateFound = true;\n            this.setInvalidDatePart(symbol, {\n              value: year,\n              date: cloneDate(newValueCandidate),\n              startDate: cloneDate(this.value)\n            });\n            this.setExisting(symbol, false);\n          }\n        }\n        if (!invalidDateFound) {\n          this.setExisting(symbol, true);\n          if (isInCaretMode && !isValidDate(parsedDate)) {\n            var valueCandidate = this.intl.parseDate(basePrefix + middle + baseSuffix, this.format, this.localeId);\n            if (isValidDate(valueCandidate)) {\n              this._value = valueCandidate;\n            }\n          } else {\n            this._value = newValue;\n          }\n          if (this.getValue()) {\n            this.resetInvalidDate();\n          }\n        }\n        var switchToNext = false;\n        if (symbol === \"M\") {\n          if (parsedValue >= 2 || textToParse.length >= 2) {\n            switchToNext = true;\n          } else {\n            switchToNext = false;\n          }\n        } else {\n          if (hasFixedFormat) {\n            var peekDateSwitchToNext = this.isPeekDateOverflowingDatePart({\n              useBasePrefixAndSuffix: !this.autoCorrectParts,\n              middle: middle,\n              patternValue: patternValue,\n              basePrefix: basePrefix,\n              baseSuffix: baseSuffix,\n              prefix: prefix,\n              suffix: suffix,\n              symbol: symbol,\n              patternLength: patternLength,\n              leadingZero: leadingZero\n            }).switchToNext;\n            switchToNext = peekDateSwitchToNext;\n          } else {\n            switchToNext = textToParse.length > segmentLength;\n          }\n        }\n        return extend(parseResult, {\n          value: null,\n          switchToNext: switchToNext,\n          hasInvalidDatePart: invalidDateFound\n        });\n      }\n    }\n    return extend(parseResult, {\n      value: null,\n      switchToNext: false\n    });\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.symbolMap = function (symbol) {\n    return this.intl.splitDateFormat(this.format, this.localeId).reduce(dateSymbolMap, {})[symbol];\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.resetLeadingZero = function () {\n    var hasLeadingZero = this.leadingZero !== null;\n    this.setLeadingZero(null);\n    return hasLeadingZero;\n  };\n  DateObject.prototype.setLeadingZero = function (leadingZero) {\n    this.leadingZero = leadingZero;\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.getLeadingZero = function () {\n    return this.leadingZero || {};\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.normalizeCentury = function (date) {\n    if (!isPresent(date)) {\n      return date;\n    }\n    var twoDigitYear = cropTwoDigitYear(date);\n    var centuryBase = this.getNormalizedCenturyBase(twoDigitYear);\n    var normalizedDate = setYears(date, centuryBase + twoDigitYear);\n    return normalizedDate;\n  };\n  DateObject.prototype.incrementLeadingZero = function (symbol) {\n    var leadingZero = this.leadingZero || {};\n    leadingZero[symbol] = (leadingZero[symbol] || 0) + 1;\n    this.leadingZero = leadingZero;\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.isAbbrMonth = function (parts, symbol) {\n    var pattern = this.partPattern(parts, symbol);\n    return pattern.type === 'month' && pattern.names;\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.partPattern = function (parts, symbol) {\n    return parts.filter(function (part) {\n      return part.pattern.indexOf(symbol) !== -1;\n    })[0];\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.peek = function (value, pattern) {\n    var peekValue = value.replace(/^0*/, '') + '0';\n    return padZero(pattern.length - peekValue.length) + peekValue;\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.matchMonth = function (typedChar) {\n    this.typedMonthPart += typedChar.toLowerCase();\n    if (this.monthNames.length === 0) {\n      return '';\n    }\n    while (this.typedMonthPart.length > 0) {\n      for (var i = 0; i < this.monthNames.length; i++) {\n        if (this.monthNames[i].toLowerCase().indexOf(this.typedMonthPart) === 0) {\n          return this.monthNames[i];\n        }\n      }\n      var monthAsNum = parseInt(this.typedMonthPart, 10);\n      /* ensure they exact match */\n      if (monthAsNum >= 1 && monthAsNum <= 12 && monthAsNum.toString() === this.typedMonthPart) {\n        return this.monthNames[monthAsNum - 1];\n      }\n      this.typedMonthPart = this.typedMonthPart.substring(1, this.typedMonthPart.length);\n    }\n    return '';\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.matchDayPeriod = function (typedChar, symbol) {\n    var lowerChart = typedChar.toLowerCase();\n    if (symbol === 'a' && this.dayPeriods) {\n      if (this.dayPeriods.am.toLowerCase().startsWith(lowerChart)) {\n        return this.dayPeriods.am;\n      } else if (this.dayPeriods.pm.toLowerCase().startsWith(lowerChart)) {\n        return this.dayPeriods.pm;\n      }\n    }\n    return '';\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.allFormattedMonths = function (locale) {\n    if (locale === void 0) {\n      locale = \"en\";\n    }\n    var dateFormatParts = this.intl.splitDateFormat(this.format, this.localeId);\n    for (var i = 0; i < dateFormatParts.length; i++) {\n      if (dateFormatParts[i].type === 'month' && dateFormatParts[i].names) {\n        return this.intl.dateFormatNames(locale, dateFormatParts[i].names);\n      }\n    }\n    return [];\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.allDayPeriods = function (locale) {\n    if (locale === void 0) {\n      locale = \"en\";\n    }\n    var dateFormatParts = this.intl.splitDateFormat(this.format);\n    for (var i = 0; i < dateFormatParts.length; i++) {\n      if (dateFormatParts[i].type === \"dayperiod\" && dateFormatParts[i].names) {\n        return this.intl.dateFormatNames(locale, dateFormatParts[i].names);\n      }\n    }\n    return null;\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.patternLength = function (pattern) {\n    if (pattern[0] === 'y') {\n      return 4;\n    }\n    if (SHORT_PATTERN_LENGTH_REGEXP.test(pattern)) {\n      return 2;\n    }\n    return 0;\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.dateFormatString = function (date, format) {\n    var dateFormatParts = this.intl.splitDateFormat(format, this.localeId);\n    var parts = [];\n    var partMap = [];\n    for (var i = 0; i < dateFormatParts.length; i++) {\n      var partLength = this.intl.formatDate(date, {\n        pattern: dateFormatParts[i].pattern\n      }, this.localeId).length;\n      while (partLength > 0) {\n        parts.push(this.symbols[dateFormatParts[i].pattern[0]] || Constants.formatSeparator);\n        partMap.push(dateFormatParts[i]);\n        partLength--;\n      }\n    }\n    var returnValue = new Mask();\n    returnValue.symbols = parts.join('');\n    returnValue.partMap = partMap;\n    return returnValue;\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.merge = function (text, mask) {\n    // Important: right to left.\n    var resultText = '';\n    var resultFormat = '';\n    var format = mask.symbols;\n    var processTextSymbolsEnded = false;\n    var ignoreFormatSymbolsCount = 0;\n    var formattedDates = this.getFormattedInvalidDates(format);\n    for (var formatSymbolIndex = format.length - 1; formatSymbolIndex >= 0; formatSymbolIndex--) {\n      var partsForSegment = this.getPartsForSegment(mask, formatSymbolIndex);\n      if (this.knownParts.indexOf(format[formatSymbolIndex]) === -1 || this.getExisting(format[formatSymbolIndex])) {\n        if (this.autoCorrectParts) {\n          resultText = text[formatSymbolIndex] + resultText;\n        } else {\n          if (text.length !== format.length) {\n            if (processTextSymbolsEnded) {\n              resultText = text[formatSymbolIndex] + resultText;\n            } else if (ignoreFormatSymbolsCount > 0) {\n              resultText = text[formatSymbolIndex] + resultText;\n              ignoreFormatSymbolsCount--;\n              if (ignoreFormatSymbolsCount <= 0) {\n                processTextSymbolsEnded = true;\n              }\n            } else {\n              resultText = (text[formatSymbolIndex + text.length - format.length] || \"\") + resultText;\n            }\n          } else {\n            resultText = text[formatSymbolIndex] + resultText;\n          }\n        }\n        resultFormat = format[formatSymbolIndex] + resultFormat;\n      } else {\n        var symbol = format[formatSymbolIndex];\n        var formatSymbolIndexModifier = 0;\n        if (this.autoCorrectParts || !this.autoCorrectParts && !this.getInvalidDatePartValue(symbol)) {\n          while (formatSymbolIndex >= 0 && symbol === format[formatSymbolIndex]) {\n            formatSymbolIndex--;\n          }\n          formatSymbolIndex++;\n        }\n        if (this.leadingZero && this.leadingZero[symbol]) {\n          resultText = '0' + resultText;\n        } else {\n          if (!this.autoCorrectParts && this.getInvalidDatePartValue(symbol)) {\n            var datePartText = this.getInvalidDatePartValue(symbol).toString();\n            if (symbol === \"M\") {\n              datePartText = (parseToInt(this.getInvalidDatePartValue(symbol)) + JS_MONTH_OFFSET).toString();\n              if (partsForSegment.length > MONTH_PART_WITH_WORDS_THRESHOLD) {\n                resultText = formattedDates[symbol][formatSymbolIndex] + resultText;\n              } else {\n                datePartText = (parseToInt(this.getInvalidDatePartValue(symbol)) + JS_MONTH_OFFSET).toString();\n                var formattedDatePart = padZero(partsForSegment.length - datePartText.length) + datePartText;\n                resultText = formattedDatePart + resultText;\n                formatSymbolIndexModifier = partsForSegment.length - 1;\n                ignoreFormatSymbolsCount = datePartText.length - partsForSegment.length;\n              }\n            } else {\n              var formattedDatePart = padZero(partsForSegment.length - datePartText.length) + datePartText;\n              resultText = formattedDatePart + resultText;\n              formatSymbolIndexModifier = partsForSegment.length - 1;\n              ignoreFormatSymbolsCount = datePartText.length - partsForSegment.length;\n            }\n          } else {\n            resultText = this.dateFieldName(mask.partMap[formatSymbolIndex]) + resultText;\n          }\n        }\n        while (resultFormat.length < resultText.length) {\n          resultFormat = format[formatSymbolIndex] + resultFormat;\n        }\n        if (formatSymbolIndexModifier !== 0) {\n          formatSymbolIndex = formatSymbolIndex - formatSymbolIndexModifier + (text.length - format.length);\n        }\n      }\n    }\n    return {\n      text: resultText,\n      format: resultFormat\n    };\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.dateFieldName = function (part) {\n    var formatPlaceholder = this.formatPlaceholder || 'wide';\n    if (formatPlaceholder[part.type]) {\n      return formatPlaceholder[part.type];\n    }\n    if (formatPlaceholder === 'formatPattern') {\n      return part.pattern;\n    }\n    return this.intl.dateFieldName(Object.assign(part, {\n      nameType: formatPlaceholder\n    }));\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.getNormalizedCenturyBase = function (twoDigitYear) {\n    return twoDigitYear > this.twoDigitYearMax ? PREVIOUS_CENTURY_BASE : CURRENT_CENTURY_BASE;\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.shouldNormalizeCentury = function () {\n    return this.intl.splitDateFormat(this.format).some(function (part) {\n      return part.pattern === 'yy';\n    });\n  };\n  DateObject.prototype.resetInvalidDate = function () {\n    var _this = this;\n    this._partiallyInvalidDate.startDate = null;\n    Object.keys(this._partiallyInvalidDate.invalidDateParts).forEach(function (key) {\n      _this.resetInvalidDatePart(key);\n    });\n  };\n  DateObject.prototype.resetInvalidDateSymbol = function (symbol) {\n    var _this = this;\n    this.resetInvalidDatePart(symbol);\n    var shouldResetInvalidDate = true;\n    Object.keys(this._partiallyInvalidDate.invalidDateParts).forEach(function (key) {\n      if (_this._partiallyInvalidDate.invalidDateParts[key] && isPresent(_this._partiallyInvalidDate.invalidDateParts[key].value)) {\n        shouldResetInvalidDate = false;\n      }\n    });\n    if (shouldResetInvalidDate) {\n      this.resetInvalidDate();\n    }\n  };\n  DateObject.prototype.resetInvalidDatePart = function (symbol) {\n    if (this._partiallyInvalidDate.invalidDateParts[symbol]) {\n      this._partiallyInvalidDate.invalidDateParts[symbol] = {\n        value: null,\n        date: null,\n        startDateOffset: 0\n      };\n    }\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.getInvalidDatePart = function (symbol) {\n    var invalidDatePart = this._partiallyInvalidDate.invalidDateParts[symbol];\n    return invalidDatePart || {};\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.getInvalidDatePartValue = function (symbol) {\n    var invalidDatePart = this._partiallyInvalidDate.invalidDateParts[symbol];\n    return (invalidDatePart || {}).value;\n  };\n  DateObject.prototype.setInvalidDatePart = function (symbol, _a) {\n    var _b = _a.value,\n      value = _b === void 0 ? null : _b,\n      _c = _a.date,\n      date = _c === void 0 ? null : _c,\n      _d = _a.startDateOffset,\n      startDateOffset = _d === void 0 ? 0 : _d,\n      _e = _a.startDate,\n      startDate = _e === void 0 ? null : _e;\n    if (this._partiallyInvalidDate.invalidDateParts[symbol]) {\n      this._partiallyInvalidDate.invalidDateParts[symbol].value = value;\n      this._partiallyInvalidDate.invalidDateParts[symbol].date = date;\n      this._partiallyInvalidDate.invalidDateParts[symbol].startDateOffset = startDateOffset;\n      this._partiallyInvalidDate.startDate = startDate;\n    }\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.hasInvalidDatePart = function () {\n    var _this = this;\n    var hasInvalidDatePart = false;\n    Object.keys(this._partiallyInvalidDate.invalidDateParts).forEach(function (key) {\n      if (_this._partiallyInvalidDate.invalidDateParts[key] && isPresent(_this._partiallyInvalidDate.invalidDateParts[key].value)) {\n        hasInvalidDatePart = true;\n      }\n    });\n    return hasInvalidDatePart;\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.modifyDateSymbolWithOffset = function (date, symbol, offset) {\n    var newValue = cloneDate(date);\n    var timeModified = false;\n    switch (symbol) {\n      case 'y':\n        newValue.setFullYear(newValue.getFullYear() + offset);\n        break;\n      case 'M':\n        newValue = addMonths(this.value, offset);\n        break;\n      case 'd':\n      case 'E':\n        newValue.setDate(newValue.getDate() + offset);\n        break;\n      case 'h':\n      case 'H':\n        newValue.setHours(newValue.getHours() + offset);\n        timeModified = true;\n        break;\n      case 'm':\n        newValue.setMinutes(newValue.getMinutes() + offset);\n        timeModified = true;\n        break;\n      case 's':\n        newValue.setSeconds(newValue.getSeconds() + offset);\n        timeModified = true;\n        break;\n      case \"S\":\n        newValue.setMilliseconds(newValue.getMilliseconds() + offset);\n        break;\n      case 'a':\n        newValue.setHours(newValue.getHours() + 12 * offset);\n        timeModified = true;\n        break;\n      default:\n        break;\n    }\n    return {\n      date: newValue,\n      timeModified: timeModified\n    };\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.modifyDateSymbolWithValue = function (date, symbol, value) {\n    var newValue = cloneDate(date);\n    switch (symbol) {\n      case 'y':\n        newValue.setFullYear(value);\n        break;\n      case 'M':\n        newValue = addMonths(date, value - date.getMonth());\n        break;\n      case 'd':\n      case 'E':\n        newValue.setDate(value);\n        break;\n      case 'h':\n      case 'H':\n        newValue.setHours(value);\n        break;\n      case 'm':\n        newValue.setMinutes(value);\n        break;\n      case 's':\n        newValue.setSeconds(value);\n        break;\n      case \"S\":\n        newValue.setMilliseconds(value);\n        break;\n      case 'a':\n        newValue.setHours(value);\n        break;\n      default:\n        break;\n    }\n    return newValue;\n  };\n  DateObject.prototype.markDatePartsAsExisting = function () {\n    this.modifyExisting(true);\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.getPartsForSegment = function (mask, partIndex) {\n    var segmentPart = mask.partMap[partIndex];\n    var partsForSegment = [];\n    for (var maskPartIndex = partIndex; maskPartIndex < mask.partMap.length; maskPartIndex++) {\n      var part = mask.partMap[maskPartIndex];\n      if (segmentPart.type === part.type && segmentPart.pattern === part.pattern) {\n        partsForSegment.push(part);\n      } else {\n        break;\n      }\n    }\n    for (var maskPartIndex = partIndex - 1; maskPartIndex >= 0; maskPartIndex--) {\n      var part = mask.partMap[maskPartIndex];\n      if (segmentPart.type === part.type && segmentPart.pattern === part.pattern) {\n        partsForSegment.unshift(part);\n      } else {\n        break;\n      }\n    }\n    return partsForSegment;\n  };\n  /**\n   * @hidden\n   */\n  DateObject.prototype.isPeekDateOverflowingDatePart = function (_a) {\n    var useBasePrefixAndSuffix = _a.useBasePrefixAndSuffix,\n      middle = _a.middle,\n      patternValue = _a.patternValue,\n      basePrefix = _a.basePrefix,\n      baseSuffix = _a.baseSuffix,\n      prefix = _a.prefix,\n      suffix = _a.suffix,\n      symbol = _a.symbol,\n      patternLength = _a.patternLength,\n      leadingZero = _a.leadingZero;\n    // move to next segment if the part will overflow with next char\n    // when start from empty date (01, then 010), padded zeros should be trimmed\n    var peekedValue = this.peek(middle, patternValue);\n    var peekedDateString = useBasePrefixAndSuffix ? \"\".concat(basePrefix).concat(peekedValue).concat(baseSuffix) : \"\".concat(prefix).concat(peekedValue).concat(suffix);\n    var peekedDate = this.intl.parseDate(peekedDateString, this.format, this.localeId);\n    var leadingZeroOffset = (this.leadingZero || {})[symbol] || 0;\n    var patternSatisfied = leadingZeroOffset + unpadZero(middle).length >= patternLength;\n    var parsedPeekedValue = parseToInt(peekedValue);\n    var switchToNext = peekedDate === null || (leadingZero[symbol] ? patternValue.length <= middle.length : patternSatisfied);\n    return {\n      peekedDate: peekedDate,\n      peekedDateString: peekedDateString,\n      peekedValue: peekedValue,\n      parsedPeekedValue: parsedPeekedValue,\n      switchToNext: switchToNext\n    };\n  };\n  return DateObject;\n}();\nexport { DateObject };", "map": {"version": 3, "names": ["addMonths", "cloneDate", "createDate", "isEqual", "getDate", "lastDayOfMonth", "Mask", "dateSymbolMap", "padZero", "unpadZero", "extend", "isPresent", "cropTwoDigitYear", "set<PERSON>ears", "parseToInt", "clamp", "areDatePartsEqualTo", "isNumber", "isValidDate", "isParseableToInt", "Constants", "MONTH_INDEX_FEBRUARY", "DEFAULT_LEAP_YEAR", "PREVIOUS_CENTURY_BASE", "CURRENT_CENTURY_BASE", "SHORT_PATTERN_LENGTH_REGEXP", "MONTH_PART_WITH_WORDS_THRESHOLD", "MONTH_SYMBOL", "JS_MONTH_OFFSET", "DateObject", "_a", "intlService", "formatPlaceholder", "format", "_b", "cycleTime", "_c", "twoDigitYearMax", "_d", "value", "_e", "autoCorrectParts", "_f", "toggleDayPeriod", "_g", "autoSwitchParts", "year", "month", "date", "hours", "minutes", "seconds", "milliseconds", "dayperiod", "leadingZero", "typedMonthPart", "knownParts", "symbols", "_value", "getDefaultDate", "_partiallyInvalidDate", "startDate", "invalidDateParts", "startDateOffset", "setOptions", "sampleFormat", "dateFormatString", "i", "length", "setExisting", "Object", "defineProperty", "prototype", "get", "set", "Date", "resetInvalidDate", "enumerable", "configurable", "localeId", "defaultLocaleId", "cldr<PERSON>eys", "keys", "intl", "cldr", "key", "name", "calendar", "numbers", "options", "monthNames", "allFormattedMonths", "dayPeriods", "allDayPeriods", "setValue", "modifyExisting", "hasValue", "_this", "pred", "a", "p", "type", "getExisting", "pattern", "splitDateFormat", "reduce", "getValue", "createDefaultDate", "getFormattedDate", "formatDate", "getTextAndFormat", "customFormat", "text", "mask", "partiallyInvalidText", "formattedDate", "formattedDates", "getFormattedInvalidDates", "symbol", "partMap", "getInvalidDatePartValue", "partsForSegment", "getPartsForSegment", "datePartText", "toString", "formattedDatePart", "result", "merge", "formattedDatesForSymbol", "for<PERSON>ach", "getInvalidDatePart", "formattedInvalidDate", "setFullYear", "setMonth", "modifyPart", "offset", "newValue", "timeModified", "invalidDateFound", "isMonth", "isDay", "symbolExists", "invalidDate<PERSON><PERSON><PERSON><PERSON><PERSON>", "y", "getFullYear", "M", "getMonth", "day", "d", "E", "hour", "h", "H", "getHours", "m", "getMinutes", "s", "getSeconds", "S", "getMilliseconds", "resetInvalidDateSymbol", "monthValue", "dayValue", "Math", "abs", "dateCandidate", "newValueCandidate", "modifyDateSymbolWithValue", "dateCandidateExists", "markDatePartsAsExisting", "setInvalidDatePart", "setDate", "setHours", "setMinutes", "setSeconds", "setMilliseconds", "shouldNormalizeCentury", "normalizeCentury", "parsePart", "currentChar", "resetSegmentValue", "cycleSegmentValue", "rawInputValue", "rawTextValue", "isDeleting", "originalFormat", "isInCaretMode", "dateParts", "datePartsLiterals", "filter", "x", "map", "index", "datePartIndex", "literal", "flatDateParts", "datePart", "j", "shouldResetPart", "some", "parseResult", "switchToNext", "resetPart", "hasInvalidDatePart", "rawValueStartsWithLiteral", "startsWith", "rawValueEndsWithLiteral", "endsWith", "rawValueHasConsecutiveLiterals", "indexOf", "resetLeadingZero", "baseDate", "baseFormat", "replaced", "prefix", "current", "basePrefix", "baseSuffix", "suffix", "convertedBaseFormat", "hasFixedFormat", "datePartStartIndex", "datePartEndIndex", "lastIndexOf", "segmentLength", "formatToTextLengthDiff", "segmentCharIndex", "existing", "segmentCharIndex_1", "parsedDate", "monthByChar", "matchMonth", "<PERSON><PERSON><PERSON><PERSON>", "matchDayPeriod", "isZeroCurrentChar", "valueNumber", "isAbbrMonth", "incrementLeadingZero", "partPattern", "patternValue", "<PERSON><PERSON><PERSON><PERSON>", "currentMaxLength", "try<PERSON><PERSON><PERSON>", "middle", "max", "substring", "replace", "middleNumber", "parseInt", "candidateDateString", "parseDate", "every", "autoCorrectedPrefixAndSuffix", "isCurrentCharParsable", "isNaN", "monthNumber", "peekResult", "isPeekDateOverflowingDatePart", "useBasePrefixAndSuffix", "peekedDate", "parsedPeekedValue", "datePartValue", "textToParse", "parsedValue", "isYear", "valueCandidate", "peekDateSwitchToNext", "symbolMap", "hasLeadingZero", "setLeadingZero", "getLeadingZero", "twoDigitYear", "centuryBase", "getNormalizedCenturyBase", "normalizedDate", "parts", "names", "part", "peek", "peekValue", "typedChar", "toLowerCase", "monthAsNum", "lowerChart", "am", "pm", "locale", "dateFormatParts", "dateFormatNames", "test", "partLength", "push", "formatSeparator", "returnValue", "join", "resultText", "resultFormat", "processTextSymbolsEnded", "ignoreFormatSymbolsCount", "formatSymbolIndex", "formatSymbolIndexModifier", "dateFieldName", "assign", "nameType", "resetInvalidDatePart", "shouldResetInvalidDate", "invalidDate<PERSON><PERSON>", "modifyDateSymbolWithOffset", "partIndex", "segmentPart", "maskPartIndex", "unshift", "peekedValue", "peekedDateString", "concat", "leadingZeroOffset", "patternSatisfied"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-dateinputs-common/dist/es/common/dateobject.js"], "sourcesContent": ["import { addMonths, cloneDate, createDate, isEqual, getDate, lastDayOfMonth } from '@progress/kendo-date-math';\nimport { Mask } from './mask';\nimport { dateSymbolMap, padZero, unpad<PERSON>ero } from '../dateinput/utils';\nimport { extend, isPresent, cropTwoDigitYear, setYears, parseToInt, clamp, areDatePartsEqualTo, isNumber, isValidDate, isParseableToInt } from './utils';\nimport { Constants } from './constants';\nvar MONTH_INDEX_FEBRUARY = 1;\nvar DEFAULT_LEAP_YEAR = 2000;\nvar PREVIOUS_CENTURY_BASE = 1900;\nvar CURRENT_CENTURY_BASE = 2000;\nvar SHORT_PATTERN_LENGTH_REGEXP = /d|M|H|h|m|s/;\nvar MONTH_PART_WITH_WORDS_THRESHOLD = 2;\nvar MONTH_SYMBOL = \"M\";\n// JS months start from 0 (January) instead of 1 (January)\nvar JS_MONTH_OFFSET = 1;\nvar DateObject = /** @class */ (function () {\n    function DateObject(_a) {\n        var intlService = _a.intlService, formatPlaceholder = _a.formatPlaceholder, format = _a.format, _b = _a.cycleTime, cycleTime = _b === void 0 ? false : _b, _c = _a.twoDigitYearMax, twoDigitYearMax = _c === void 0 ? Constants.twoDigitYearMax : _c, _d = _a.value, value = _d === void 0 ? null : _d, _e = _a.autoCorrectParts, autoCorrectParts = _e === void 0 ? true : _e, _f = _a.toggleDayPeriod, toggleDayPeriod = _f === void 0 ? false : _f, _g = _a.autoSwitchParts, autoSwitchParts = _g === void 0 ? true : _g;\n        this.year = true;\n        this.month = true;\n        this.date = true;\n        this.hours = true;\n        this.minutes = true;\n        this.seconds = true;\n        this.milliseconds = true;\n        this.dayperiod = true;\n        this.leadingZero = null;\n        this.typedMonthPart = '';\n        this.knownParts = 'adHhmMsEyS';\n        this.symbols = {\n            'E': 'E',\n            'H': 'H',\n            'M': 'M',\n            'a': 'a',\n            'd': 'd',\n            'h': 'h',\n            'm': 'm',\n            's': 's',\n            'y': 'y',\n            'S': 'S'\n        };\n        this._value = this.getDefaultDate();\n        this.cycleTime = false;\n        this._partiallyInvalidDate = {\n            startDate: null,\n            invalidDateParts: {\n                'E': { value: null, date: null, startDateOffset: 0 },\n                'H': { value: null, date: null, startDateOffset: 0 },\n                'M': { value: null, date: null, startDateOffset: 0 },\n                'a': { value: null, date: null, startDateOffset: 0 },\n                'd': { value: null, date: null, startDateOffset: 0 },\n                'h': { value: null, date: null, startDateOffset: 0 },\n                'm': { value: null, date: null, startDateOffset: 0 },\n                's': { value: null, date: null, startDateOffset: 0 },\n                'y': { value: null, date: null, startDateOffset: 0 },\n                'S': { value: null, date: null, startDateOffset: 0 }\n            }\n        };\n        this.setOptions({\n            intlService: intlService,\n            formatPlaceholder: formatPlaceholder,\n            format: format,\n            cycleTime: cycleTime,\n            twoDigitYearMax: twoDigitYearMax,\n            value: value,\n            autoCorrectParts: autoCorrectParts,\n            toggleDayPeriod: toggleDayPeriod,\n            autoSwitchParts: autoSwitchParts\n        });\n        if (!value) {\n            this._value = this.getDefaultDate();\n            var sampleFormat = this.dateFormatString(this.value, this.format).symbols;\n            for (var i = 0; i < sampleFormat.length; i++) {\n                this.setExisting(sampleFormat[i], false);\n            }\n        }\n        else {\n            this._value = cloneDate(value);\n        }\n    }\n    Object.defineProperty(DateObject.prototype, \"value\", {\n        get: function () {\n            return this._value;\n        },\n        set: function (value) {\n            if (value && !(value instanceof Date)) {\n                // throw new Error(\"The 'value' should be a valid JavaScript Date instance.\");\n                return;\n            }\n            this._value = value;\n            this.resetInvalidDate();\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(DateObject.prototype, \"localeId\", {\n        get: function () {\n            var localeId = Constants.defaultLocaleId;\n            var cldrKeys = Object.keys(this.intl.cldr);\n            for (var i = 0; i < cldrKeys.length; i++) {\n                var key = cldrKeys[i];\n                var value = this.intl.cldr[key];\n                if (value.name && value.calendar && value.numbers &&\n                    value.name !== Constants.defaultLocaleId) {\n                    localeId = value.name;\n                    break;\n                }\n            }\n            return localeId;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * @hidden\n     */\n    DateObject.prototype.setOptions = function (options) {\n        this.intl = options.intlService;\n        this.formatPlaceholder = options.formatPlaceholder || 'wide';\n        this.format = options.format;\n        this.cycleTime = options.cycleTime;\n        this.monthNames = this.allFormattedMonths(this.localeId);\n        this.dayPeriods = this.allDayPeriods(this.localeId);\n        this.twoDigitYearMax = options.twoDigitYearMax;\n        this.autoCorrectParts = options.autoCorrectParts;\n        this.toggleDayPeriod = options.toggleDayPeriod;\n        this.autoSwitchParts = options.autoSwitchParts;\n    };\n    DateObject.prototype.setValue = function (value) {\n        if (!value) {\n            this._value = this.getDefaultDate();\n            this.modifyExisting(false);\n        }\n        else if (!isEqual(value, this._value)) {\n            this._value = cloneDate(value);\n            this.modifyExisting(true);\n        }\n        else if (isEqual(value, this._value) && this.dayPeriods) {\n            this.setExisting('a', true);\n        }\n        this.resetInvalidDate();\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.hasValue = function () {\n        var _this = this;\n        var pred = function (a, p) { return a || p.type !== 'literal' && p.type !== 'dayperiod' && _this.getExisting(p.pattern[0]); };\n        return this.intl.splitDateFormat(this.format, this.localeId).reduce(pred, false);\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.getValue = function () {\n        for (var i = 0; i < this.knownParts.length; i++) {\n            if (!this.getExisting(this.knownParts[i])) {\n                return null;\n            }\n        }\n        return cloneDate(this.value);\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.createDefaultDate = function () {\n        // use the leap year 2000 that has 29th February\n        // and a month that has 31 days\n        // so that the default date can accommodate maximum date values\n        // it is better to use a fixed date instead of new Date()\n        return createDate(DEFAULT_LEAP_YEAR, 0, 31);\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.getDefaultDate = function () {\n        return getDate(this.createDefaultDate());\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.getFormattedDate = function (format) {\n        return this.intl.formatDate(this.getValue(), format, this.localeId);\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.getTextAndFormat = function (customFormat) {\n        if (customFormat === void 0) { customFormat = \"\"; }\n        var format = customFormat || this.format;\n        var text = this.intl.formatDate(this.value, format, this.localeId);\n        var mask = this.dateFormatString(this.value, format);\n        if (!this.autoCorrectParts && this._partiallyInvalidDate.startDate) {\n            var partiallyInvalidText = \"\";\n            var formattedDate = this.intl.formatDate(this.value, format, this.localeId);\n            var formattedDates = this.getFormattedInvalidDates(format);\n            for (var i = 0; i < formattedDate.length; i++) {\n                var symbol = mask.symbols[i];\n                if (mask.partMap[i].type === \"literal\") {\n                    partiallyInvalidText += text[i];\n                }\n                else if (this.getInvalidDatePartValue(symbol)) {\n                    var partsForSegment = this.getPartsForSegment(mask, i);\n                    if (symbol === \"M\") {\n                        var datePartText = (parseToInt(this.getInvalidDatePartValue(symbol)) + JS_MONTH_OFFSET).toString();\n                        if (partsForSegment.length > MONTH_PART_WITH_WORDS_THRESHOLD) {\n                            partiallyInvalidText += formattedDates[symbol][i];\n                        }\n                        else {\n                            if (this.getInvalidDatePartValue(symbol)) {\n                                var formattedDatePart = padZero(partsForSegment.length - datePartText.length) + datePartText;\n                                partiallyInvalidText += formattedDatePart;\n                                // add -1 as the first character in the segment is at index i\n                                i += partsForSegment.length - 1;\n                            }\n                            else {\n                                partiallyInvalidText += formattedDates[symbol][i];\n                            }\n                        }\n                    }\n                    else {\n                        if (this.getInvalidDatePartValue(symbol)) {\n                            var datePartText = this.getInvalidDatePartValue(symbol).toString();\n                            var formattedDatePart = padZero(partsForSegment.length - datePartText.length) + datePartText;\n                            partiallyInvalidText += formattedDatePart;\n                            // add -1 as the first character in the segment is at index i\n                            i += partsForSegment.length - 1;\n                        }\n                        else {\n                            partiallyInvalidText += formattedDates[symbol][i];\n                        }\n                    }\n                }\n                else {\n                    partiallyInvalidText += text[i];\n                }\n            }\n            text = partiallyInvalidText;\n        }\n        var result = this.merge(text, mask);\n        return result;\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.getFormattedInvalidDates = function (customFormat) {\n        var _this = this;\n        if (customFormat === void 0) { customFormat = \"\"; }\n        var format = customFormat || this.format;\n        var formattedDatesForSymbol = {\n            'E': '',\n            'H': '',\n            'M': '',\n            'a': '',\n            'd': '',\n            'h': '',\n            'm': '',\n            's': '',\n            'y': '',\n            'S': ''\n        };\n        Object.keys(this._partiallyInvalidDate.invalidDateParts).forEach(function (key) {\n            var date = _this.getInvalidDatePart(key).date;\n            if (date) {\n                var formattedInvalidDate = _this.intl.formatDate(date, format, _this.localeId);\n                formattedDatesForSymbol[key] = formattedInvalidDate;\n            }\n        });\n        return formattedDatesForSymbol;\n    };\n    DateObject.prototype.modifyExisting = function (value) {\n        var sampleFormat = this.dateFormatString(this.value, this.format).symbols;\n        for (var i = 0; i < sampleFormat.length; i++) {\n            this.setExisting(sampleFormat[i], value);\n        }\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.getExisting = function (symbol) {\n        switch (symbol) {\n            case 'y': return this.year;\n            case 'M':\n            case 'L': return this.month;\n            case 'd': return this.date;\n            case 'E': return this.date && this.month && this.year;\n            case 'h':\n            case 'H': return this.hours;\n            case 't':\n            case 'a': return this.dayperiod;\n            case 'm': return this.minutes;\n            case 's': return this.seconds;\n            case \"S\": return this.milliseconds;\n            default:\n                return true;\n        }\n    };\n    DateObject.prototype.setExisting = function (symbol, value) {\n        switch (symbol) {\n            case 'y':\n                // allow 2/29 dates\n                this.year = value;\n                if (value === false) {\n                    this._value.setFullYear(DEFAULT_LEAP_YEAR);\n                }\n                break;\n            case 'M':\n                // make sure you can type 31 in the day part\n                this.month = value;\n                if (value === false) {\n                    if (this.autoCorrectParts) {\n                        this._value.setMonth(0);\n                    }\n                }\n                break;\n            case 'd':\n                this.date = value;\n                break;\n            case 'h':\n            case 'H':\n                this.hours = value;\n                break;\n            case 't':\n            case 'a':\n                this.dayperiod = value;\n                break;\n            case 'm':\n                this.minutes = value;\n                break;\n            case 's':\n                this.seconds = value;\n                break;\n            case \"S\":\n                this.milliseconds = value;\n                break;\n            default:\n                break;\n        }\n        if (this.getValue()) {\n            this.resetInvalidDate();\n        }\n    };\n    DateObject.prototype.modifyPart = function (symbol, offset) {\n        if (!isPresent(symbol) || !isPresent(offset) || offset === 0) {\n            return;\n        }\n        var newValue = cloneDate(this.value);\n        var timeModified = false;\n        var invalidDateFound;\n        var isMonth = symbol === \"M\";\n        var isDay = symbol === \"d\" || symbol === \"E\";\n        var symbolExists = this.getExisting(symbol);\n        if (!this.autoCorrectParts && (isDay || isMonth)) {\n            var invalidDateParts = this._partiallyInvalidDate.invalidDateParts || {};\n            var invalidDatePartValue = this.getInvalidDatePartValue(symbol);\n            var year = invalidDateParts.y.value || newValue.getFullYear();\n            var month = invalidDateParts.M.value || newValue.getMonth();\n            var day = invalidDateParts.d.value || invalidDateParts.E.value || newValue.getDate();\n            var hour = invalidDateParts.h.value || invalidDateParts.H.value || newValue.getHours();\n            var minutes = invalidDateParts.m.value || newValue.getMinutes();\n            var seconds = invalidDateParts.s.value || newValue.getSeconds();\n            var milliseconds = invalidDateParts.S.value || newValue.getMilliseconds();\n            switch (symbol) {\n                case 'y':\n                    year += offset;\n                    break;\n                case 'M':\n                    month += offset;\n                    break;\n                case 'd':\n                case 'E':\n                    day += offset;\n                    break;\n                // case 'h':\n                // case 'H': hour += offset; break;\n                // case 'm': minutes += offset; break;\n                // case 's': seconds += offset; break;\n                // case 'S': milliseconds += offset; break;\n                default: break;\n            }\n            if (symbol === \"M\") {\n                if ((month < 0 || month > 11)) {\n                    if (symbolExists) {\n                        this.setExisting(symbol, false);\n                        this.resetInvalidDateSymbol(symbol);\n                        return;\n                    }\n                }\n                if (!symbolExists) {\n                    if (month < 0) {\n                        month = clamp(11 + ((month % 11) + 1), 0, 11);\n                    }\n                    else {\n                        var monthValue = isPresent(invalidDatePartValue) ?\n                            month :\n                            ((offset - JS_MONTH_OFFSET) % 12);\n                        month = clamp(monthValue, 0, 11);\n                    }\n                    month = clamp(month, 0, 11);\n                }\n                month = clamp(month, 0, 11);\n            }\n            else if (symbol === \"d\") {\n                if (symbolExists) {\n                    if (day <= 0 || day > 31) {\n                        this.setExisting(symbol, false);\n                        this.resetInvalidDateSymbol(symbol);\n                        return;\n                    }\n                }\n                else if (!symbolExists) {\n                    if (isPresent(invalidDatePartValue)) {\n                        if (day <= 0 || day > 31) {\n                            this.setExisting(symbol, false);\n                            this.resetInvalidDateSymbol(symbol);\n                            return;\n                        }\n                    }\n                    if (offset < 0) {\n                        var dayValue = isPresent(invalidDatePartValue) ? day : 1 + (31 - Math.abs(offset % 31));\n                        day = clamp(dayValue, 1, 31);\n                    }\n                    else {\n                        var dayValue = isPresent(invalidDatePartValue) ? day : offset % 31;\n                        day = clamp(dayValue, 1, 31);\n                    }\n                    day = clamp(day, 1, 31);\n                }\n            }\n            var dateCandidate = createDate(year, month, day, hour, minutes, seconds, milliseconds);\n            var newValueCandidate = isMonth || isDay ?\n                this.modifyDateSymbolWithValue(newValue, symbol, isMonth ? month : day) :\n                null;\n            var dateCandidateExists = areDatePartsEqualTo(dateCandidate, year, month, day, hour, minutes, seconds, milliseconds);\n            if (this.getValue() && areDatePartsEqualTo(dateCandidate, year, month, day, hour, minutes, seconds, milliseconds)) {\n                newValue = cloneDate(dateCandidate);\n                this.markDatePartsAsExisting();\n            }\n            else if (isMonth && newValueCandidate) {\n                if (newValueCandidate.getMonth() === month) {\n                    if (this.getExisting(\"d\")) {\n                        if (dateCandidateExists) {\n                            newValue = cloneDate(dateCandidate);\n                            this.resetInvalidDateSymbol(symbol);\n                        }\n                        else {\n                            invalidDateFound = true;\n                            this.setInvalidDatePart(symbol, {\n                                value: month,\n                                date: cloneDate(newValueCandidate),\n                                startDateOffset: offset,\n                                startDate: cloneDate(this.value)\n                            });\n                            this.setExisting(symbol, false);\n                        }\n                    }\n                    else if (dateCandidateExists) {\n                        this.resetInvalidDateSymbol(symbol);\n                        newValue = cloneDate(dateCandidate);\n                        if (this.getExisting(\"M\") && this.getExisting(\"y\")) {\n                            // changing from 28/Feb to 29/Feb to 29/March\n                            this.setExisting(\"d\", true);\n                            this.resetInvalidDateSymbol(\"d\");\n                        }\n                    }\n                    else {\n                        this.resetInvalidDateSymbol(symbol);\n                        newValue = cloneDate(newValueCandidate);\n                    }\n                }\n                else {\n                    invalidDateFound = true;\n                    this.setInvalidDatePart(symbol, {\n                        value: month,\n                        date: cloneDate(newValueCandidate),\n                        startDateOffset: offset,\n                        startDate: cloneDate(this.value)\n                    });\n                    this.setExisting(symbol, false);\n                }\n            }\n            else if (isDay && newValueCandidate) {\n                if (newValueCandidate.getDate() === day) {\n                    if (this.getExisting(\"M\")) {\n                        if (dateCandidateExists) {\n                            newValue = cloneDate(dateCandidate);\n                            this.resetInvalidDateSymbol(symbol);\n                        }\n                        else {\n                            invalidDateFound = true;\n                            this.setInvalidDatePart(symbol, {\n                                value: day,\n                                date: cloneDate(newValueCandidate),\n                                startDateOffset: offset,\n                                startDate: cloneDate(this.value)\n                            });\n                            this.setExisting(symbol, false);\n                        }\n                    }\n                    else if (dateCandidateExists) {\n                        newValue = cloneDate(dateCandidate);\n                        this.resetInvalidDateSymbol(symbol);\n                        if (this.getExisting(\"d\") && this.getExisting(\"y\")) {\n                            // changing from 31/Jan to 31/Feb to 28/Feb\n                            this.setExisting(\"M\", true);\n                            this.resetInvalidDateSymbol(\"M\");\n                        }\n                    }\n                    else {\n                        this.resetInvalidDateSymbol(symbol);\n                        newValue = cloneDate(newValueCandidate);\n                    }\n                }\n                else {\n                    invalidDateFound = true;\n                    this.setInvalidDatePart(symbol, {\n                        value: day,\n                        date: cloneDate(this.value),\n                        startDateOffset: offset,\n                        startDate: cloneDate(this.value)\n                    });\n                    this.setExisting(symbol, false);\n                }\n            }\n        }\n        else {\n            var hours = newValue.getHours();\n            switch (symbol) {\n                case 'y':\n                    newValue.setFullYear(newValue.getFullYear() + offset);\n                    break;\n                case 'M':\n                    newValue = addMonths(this.value, offset);\n                    break;\n                case 'd':\n                case 'E':\n                    newValue.setDate(newValue.getDate() + offset);\n                    break;\n                case 'h':\n                case 'H':\n                    newValue.setHours(newValue.getHours() + offset);\n                    timeModified = true;\n                    break;\n                case 'm':\n                    newValue.setMinutes(newValue.getMinutes() + offset);\n                    timeModified = true;\n                    break;\n                case 's':\n                    newValue.setSeconds(newValue.getSeconds() + offset);\n                    timeModified = true;\n                    break;\n                case \"S\":\n                    newValue.setMilliseconds(newValue.getMilliseconds() + offset);\n                    break;\n                case 'a':\n                    if (this.toggleDayPeriod) {\n                        newValue.setHours(hours >= 12 ? hours - 12 : hours + 12);\n                    }\n                    else {\n                        newValue.setHours(newValue.getHours() + (12 * offset));\n                    }\n                    timeModified = true;\n                    break;\n                default: break;\n            }\n        }\n        if (this.shouldNormalizeCentury()) {\n            newValue = this.normalizeCentury(newValue);\n        }\n        if (timeModified && !this.cycleTime && newValue.getDate() !== this._value.getDate()) {\n            // todo: blazor has this fix, but this fails a unit test\n            // newValue.setDate(this._value.getDate());\n            // newValue.setMonth(this._value.getMonth());\n            // newValue.setFullYear(this._value.getFullYear());\n        }\n        if (!invalidDateFound) {\n            this.setExisting(symbol, true);\n            this._value = newValue;\n            if (this.getValue()) {\n                this.resetInvalidDate();\n            }\n        }\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.parsePart = function (_a) {\n        var symbol = _a.symbol, currentChar = _a.currentChar, resetSegmentValue = _a.resetSegmentValue, cycleSegmentValue = _a.cycleSegmentValue, rawInputValue = _a.rawTextValue, isDeleting = _a.isDeleting, originalFormat = _a.originalFormat;\n        var isInCaretMode = !cycleSegmentValue;\n        var dateParts = this.dateFormatString(this.value, this.format);\n        var datePartsLiterals = dateParts.partMap\n            .filter(function (x) { return x.type === \"literal\"; })\n            .map(function (x, index) {\n            return {\n                datePartIndex: index,\n                type: x.type,\n                pattern: x.pattern,\n                literal: \"\"\n            };\n        });\n        var flatDateParts = dateParts.partMap\n            .map(function (x) {\n            return {\n                type: x.type,\n                pattern: x.pattern,\n                text: \"\"\n            };\n        });\n        for (var i = 0; i < datePartsLiterals.length; i++) {\n            var datePart = datePartsLiterals[i];\n            for (var j = 0; j < datePart.pattern.length; j++) {\n                if (datePartsLiterals[i + j]) {\n                    datePartsLiterals[i + j].literal = datePart.pattern[j];\n                }\n            }\n            i += datePart.pattern.length - 1;\n        }\n        for (var i = 0; i < flatDateParts.length; i++) {\n            var datePart = flatDateParts[i];\n            for (var j = 0; j < datePart.pattern.length; j++) {\n                if (flatDateParts[i + j]) {\n                    flatDateParts[i + j].text = datePart.pattern[j];\n                }\n            }\n            i += datePart.pattern.length - 1;\n        }\n        var shouldResetPart = isInCaretMode && symbol === \"M\" && dateParts.partMap\n            .filter(function (x) { return x.type === \"month\"; })\n            .some(function (x) { return x.pattern.length > MONTH_PART_WITH_WORDS_THRESHOLD; });\n        var parseResult = {\n            value: null,\n            switchToNext: false,\n            resetPart: shouldResetPart,\n            hasInvalidDatePart: false\n        };\n        if (!currentChar) {\n            if (isInCaretMode) {\n                for (var i = 0; i < datePartsLiterals.length; i++) {\n                    var literal = datePartsLiterals[i].literal;\n                    var rawValueStartsWithLiteral = rawInputValue.startsWith(literal);\n                    var rawValueEndsWithLiteral = rawInputValue.endsWith(literal);\n                    var rawValueHasConsecutiveLiterals = rawInputValue.indexOf(literal + literal) >= 0;\n                    if (rawValueStartsWithLiteral || rawValueEndsWithLiteral || rawValueHasConsecutiveLiterals) {\n                        this.resetLeadingZero();\n                        this.setExisting(symbol, false);\n                        this.resetInvalidDateSymbol(symbol);\n                        return extend(parseResult, { value: null, switchToNext: false });\n                    }\n                }\n            }\n            else {\n                this.resetLeadingZero();\n                this.setExisting(symbol, false);\n                this.resetInvalidDateSymbol(symbol);\n                return extend(parseResult, { value: null, switchToNext: false });\n            }\n        }\n        var baseDate = this.intl.formatDate(this.value, this.format, this.localeId);\n        var baseFormat = dateParts.symbols;\n        var replaced = false;\n        var prefix = '';\n        var current = '';\n        var datePartText = '';\n        var basePrefix = '';\n        var baseSuffix = '';\n        var suffix = '';\n        var convertedBaseFormat = \"\";\n        for (var i = 0; i < flatDateParts.length; i++) {\n            convertedBaseFormat += flatDateParts[i].text;\n        }\n        var hasFixedFormat = (this.format === baseFormat) ||\n            (this.format === convertedBaseFormat) ||\n            (this.format === originalFormat) ||\n            (this.format.length === originalFormat.length);\n        var datePartStartIndex = (hasFixedFormat ? convertedBaseFormat : originalFormat).indexOf(symbol);\n        var datePartEndIndex = (hasFixedFormat ? convertedBaseFormat : originalFormat).lastIndexOf(symbol);\n        var segmentLength = datePartEndIndex - datePartStartIndex + 1;\n        var formatToTextLengthDiff = originalFormat.length - rawInputValue.length;\n        if (isInCaretMode || (!isInCaretMode && !this.autoCorrectParts)) {\n            var segmentCharIndex = 0;\n            for (var i = 0; i < baseDate.length; i++) {\n                if (baseFormat[i] === symbol) {\n                    var existing = this.getExisting(symbol);\n                    if (symbol === \"y\") {\n                        if (!this.hasInvalidDatePart() && this.getExisting(\"y\")) {\n                            current += baseDate[i];\n                        }\n                        else {\n                            var invalidDatePartValue = this.getInvalidDatePartValue(symbol);\n                            if (isPresent(invalidDatePartValue)) {\n                                current += (invalidDatePartValue || \"\").toString()[segmentCharIndex] || \"\";\n                                segmentCharIndex++;\n                            }\n                            else {\n                                current += existing ? baseDate[i] : '0';\n                            }\n                        }\n                    }\n                    else {\n                        current += existing ? baseDate[i] : '0';\n                    }\n                    if (formatToTextLengthDiff > 0) {\n                        if (datePartText.length + formatToTextLengthDiff < segmentLength) {\n                            datePartText += rawInputValue[i] || \"\";\n                        }\n                    }\n                    else {\n                        datePartText += rawInputValue[i] || \"\";\n                    }\n                    replaced = true;\n                }\n                else if (!replaced) {\n                    prefix += baseDate[i];\n                    basePrefix += baseDate[i];\n                }\n                else {\n                    suffix += baseDate[i];\n                    baseSuffix += baseDate[i];\n                }\n            }\n            if (hasFixedFormat) {\n                if (convertedBaseFormat.length < rawInputValue.length) {\n                    datePartText += currentChar;\n                }\n                else if (!isDeleting && originalFormat.length > rawInputValue.length) {\n                    // let the parsing to determine if the incomplete value is valid\n                }\n                if (datePartText.length > segmentLength) {\n                    return extend(parseResult, { value: null, switchToNext: false });\n                }\n            }\n            if (!hasFixedFormat || (hasFixedFormat && !this.autoCorrectParts)) {\n                current = \"\";\n                datePartText = \"\";\n                prefix = \"\";\n                suffix = \"\";\n                replaced = false;\n                var segmentCharIndex_1 = 0;\n                for (var i = 0; i < originalFormat.length; i++) {\n                    if (originalFormat[i] === symbol) {\n                        var existing = this.getExisting(symbol);\n                        if (symbol === \"y\") {\n                            if (!this.hasInvalidDatePart() && this.getExisting(\"y\")) {\n                                current += baseDate[i];\n                            }\n                            else {\n                                var invalidDatePartValue = this.getInvalidDatePartValue(symbol);\n                                if (isPresent(invalidDatePartValue)) {\n                                    current += (invalidDatePartValue || \"\").toString()[segmentCharIndex_1] || \"\";\n                                    segmentCharIndex_1++;\n                                }\n                                else {\n                                    current += existing ? baseDate[i] : '0';\n                                }\n                            }\n                        }\n                        else {\n                            current += existing ? baseDate[i] || \"\" : '0';\n                        }\n                        if (formatToTextLengthDiff > 0) {\n                            if (datePartText.length + formatToTextLengthDiff < segmentLength) {\n                                datePartText += rawInputValue[i] || \"\";\n                            }\n                        }\n                        else {\n                            datePartText += rawInputValue[i] || \"\";\n                        }\n                        replaced = true;\n                    }\n                    else if (!replaced) {\n                        prefix += rawInputValue[i] || \"\";\n                    }\n                    else {\n                        suffix += rawInputValue[i - formatToTextLengthDiff] || \"\";\n                    }\n                }\n                if (originalFormat.length < rawInputValue.length) {\n                    datePartText += currentChar;\n                }\n            }\n        }\n        if (!isInCaretMode) {\n            if (this.autoCorrectParts) {\n                current = \"\";\n                datePartText = \"\";\n                prefix = \"\";\n                suffix = \"\";\n                replaced = false;\n                for (var i = 0; i < baseDate.length; i++) {\n                    if (baseFormat[i] === symbol) {\n                        var existing = this.getExisting(symbol);\n                        current += existing ? baseDate[i] : '0';\n                        replaced = true;\n                    }\n                    else if (!replaced) {\n                        prefix += baseDate[i];\n                    }\n                    else {\n                        suffix += baseDate[i];\n                    }\n                }\n            }\n            else {\n                current = resetSegmentValue ? datePartText : current;\n            }\n        }\n        var parsedDate = null;\n        var monthByChar = this.matchMonth(currentChar);\n        var dayPeriod = this.matchDayPeriod(currentChar, symbol);\n        var isZeroCurrentChar = currentChar === '0';\n        var leadingZero = this.leadingZero || {};\n        if (isZeroCurrentChar) {\n            if (datePartText === \"0\") {\n                datePartText = current;\n            }\n            var valueNumber = parseToInt(resetSegmentValue ?\n                currentChar :\n                (isInCaretMode ? datePartText : current) + currentChar);\n            if (valueNumber === 0 && !this.isAbbrMonth(dateParts.partMap, symbol) && symbol !== \"a\") {\n                this.incrementLeadingZero(symbol);\n            }\n        }\n        else {\n            this.resetLeadingZero();\n        }\n        var partPattern = this.partPattern(dateParts.partMap, symbol);\n        var patternValue = partPattern ? partPattern.pattern : null;\n        var patternLength = this.patternLength(patternValue) || patternValue.length;\n        if (isInCaretMode) {\n            if (isDeleting && !datePartText) {\n                this.setExisting(symbol, false);\n                return extend(parseResult, { value: null, switchToNext: false });\n            }\n        }\n        var currentMaxLength = current.length - 3;\n        var tryParse = true;\n        var middle = isInCaretMode ? datePartText : current;\n        for (var i = Math.max(0, currentMaxLength); i <= current.length; i++) {\n            if (!tryParse) {\n                break;\n            }\n            middle = resetSegmentValue ?\n                currentChar :\n                isInCaretMode ?\n                    datePartText :\n                    (current.substring(i) + currentChar);\n            if (this.autoSwitchParts && symbol === 'h' && current.substring(i) === '12') {\n                middle = middle.replace('12', '0');\n            }\n            if (isInCaretMode || !this.autoCorrectParts) {\n                tryParse = false;\n                middle = unpadZero(middle);\n                // middle = padZero(segmentLength - middle.length) + middle;\n                middle = padZero(patternLength - middle.length) + middle;\n            }\n            var middleNumber = parseInt(middle, 10);\n            var candidateDateString = prefix + middle + suffix;\n            parsedDate = this.intl.parseDate(candidateDateString, this.format, this.localeId);\n            // if the format does not include year/month/day, e.g. hh:mm:ss\n            // then the exact date cannot be inferred as there is no data for it\n            // thus the today's date is used, but revert to the original date\n            // to keep other parts of the date unchanged\n            if (parsedDate && this.value &&\n                dateParts.partMap.every(function (x) { return x.type !== \"year\" && x.type !== \"month\" && x.type != \"day\"; })) {\n                parsedDate.setFullYear(this.value.getFullYear());\n                parsedDate.setMonth(this.value.getMonth());\n                parsedDate.setDate(this.value.getDate());\n            }\n            var autoCorrectedPrefixAndSuffix = false;\n            if (isInCaretMode && !isValidDate(parsedDate)) {\n                // if part of the date is not available, e.g. \"d\"\n                // but an expanded format like \"F\" is used\n                // the element value can be \"EEEE, February 1, 2022 3:04:05 AM\"\n                // which is not parsable by intl\n                // use the base prefix and suffix, e.g. convert the candidate date string\n                // to \"Thursday, February 1, 2022 3:04:05 AM\"\n                // as \"EEEE, February...\" is not parsable\n                if (this.autoCorrectParts) {\n                    parsedDate = this.intl.parseDate(basePrefix + middle + baseSuffix, this.format, this.localeId);\n                    autoCorrectedPrefixAndSuffix = true;\n                }\n            }\n            var isCurrentCharParsable = !isNaN(parseInt(currentChar, 10)) || (isInCaretMode && isDeleting && currentChar === \"\");\n            if (!parsedDate && !isNaN(middleNumber) && isCurrentCharParsable && this.autoCorrectParts) {\n                if (symbol === MONTH_SYMBOL && !monthByChar) {\n                    // JS months start from 0 (January) instead of 1 (January)\n                    var monthNumber = middleNumber - JS_MONTH_OFFSET;\n                    if (monthNumber > -1 && monthNumber < 12) {\n                        parsedDate = cloneDate(this.value);\n                        parsedDate.setMonth(monthNumber);\n                        if (parsedDate.getMonth() !== monthNumber) {\n                            parsedDate = lastDayOfMonth(addMonths(parsedDate, -1));\n                        }\n                    }\n                }\n                if (symbol === 'y') {\n                    parsedDate = createDate(parseInt(middle, 10), this.month ? this.value.getMonth() : 0, this.date ? this.value.getDate() : 1, this.hours ? this.value.getHours() : 0, this.minutes ? this.value.getMinutes() : 0, this.seconds ? this.value.getSeconds() : 0, this.milliseconds ? this.value.getMilliseconds() : 0);\n                    if (((isInCaretMode && isValidDate(parsedDate)) ||\n                        (!isInCaretMode && parsedDate)) && this.date && parsedDate.getDate() !== this.value.getDate()) {\n                        parsedDate = lastDayOfMonth(addMonths(parsedDate, -1));\n                    }\n                }\n            }\n            if ((isInCaretMode && isValidDate(parsedDate)) || (!isInCaretMode && parsedDate)) {\n                // move to next segment if the part will overflow with next char\n                // when start from empty date (01, then 010), padded zeros should be trimmed\n                var peekResult = this.isPeekDateOverflowingDatePart({\n                    useBasePrefixAndSuffix: autoCorrectedPrefixAndSuffix,\n                    middle: middle,\n                    patternValue: patternValue,\n                    basePrefix: basePrefix,\n                    baseSuffix: baseSuffix,\n                    prefix: prefix,\n                    suffix: suffix,\n                    symbol: symbol,\n                    patternLength: patternLength,\n                    leadingZero: leadingZero\n                });\n                var switchToNext = peekResult.switchToNext;\n                if (this.shouldNormalizeCentury()) {\n                    parsedDate = this.normalizeCentury(parsedDate);\n                }\n                if (symbol === 'H' && parsedDate.getHours() >= 12) {\n                    this.setExisting('a', true);\n                }\n                this._value = parsedDate;\n                this.setExisting(symbol, true);\n                this.resetInvalidDateSymbol(symbol);\n                if (!this.autoCorrectParts) {\n                    if (symbol === \"M\") {\n                        if (this.getExisting(\"M\") && this.getExisting(\"y\")) {\n                            // changing from 28/Feb to 29/Feb to 29/March\n                            this.setExisting(\"d\", true);\n                            this.resetInvalidDateSymbol(\"d\");\n                        }\n                    }\n                    else if (symbol === \"d\") {\n                        if (this.getExisting(\"d\") && this.getExisting(\"y\")) {\n                            // changing from 31/Jan to 31/Feb to 28/Feb\n                            this.setExisting(\"M\", true);\n                            this.resetInvalidDateSymbol(\"M\");\n                        }\n                    }\n                    else if (symbol === \"y\") {\n                        // if the parsed date is valid, make the whole value valid\n                        this.markDatePartsAsExisting();\n                    }\n                    if (!this.hasInvalidDatePart()) {\n                        this.markDatePartsAsExisting();\n                        if (!peekResult.peekedDate && peekResult.switchToNext && !this.autoCorrectParts) {\n                            if (symbol === \"M\") {\n                                // skip processing the month\n                            }\n                            else if (symbol === \"d\") {\n                                if (peekResult.parsedPeekedValue === 30 &&\n                                    this.value.getMonth() === MONTH_INDEX_FEBRUARY) {\n                                    // the peekValue cannot be constructed\n                                    // as there cannot be more than 29 days in February\n                                    // still the segment should not be switched as autoCorrectParts=\"false\"\n                                    // should allow typing \"30\"\n                                    switchToNext = false;\n                                }\n                            }\n                        }\n                    }\n                }\n                return extend(parseResult, { value: this.value, switchToNext: switchToNext });\n            }\n        }\n        if (monthByChar) {\n            parsedDate = this.intl.parseDate(prefix + monthByChar + suffix, this.format, this.localeId);\n            if (parsedDate) {\n                this._value = parsedDate;\n                this.setExisting(symbol, true);\n                return extend(parseResult, { value: this.value, switchToNext: false });\n            }\n        }\n        if (dayPeriod) {\n            parsedDate = this.intl.parseDate(prefix + dayPeriod + suffix, this.format) ||\n                this.intl.parseDate(basePrefix + dayPeriod + baseSuffix, this.format);\n            if (parsedDate) {\n                this._value = parsedDate;\n                this.setExisting(symbol, true);\n                return extend(parseResult, { value: this.value, switchToNext: true });\n            }\n        }\n        if (isZeroCurrentChar && symbol !== \"a\") {\n            this.setExisting(symbol, false);\n        }\n        if (!this.autoCorrectParts) {\n            var datePartValue = void 0;\n            var textToParse = isInCaretMode ? datePartText : middle;\n            var parsedValue = parseToInt(textToParse);\n            if (isNumber(parsedValue) && isParseableToInt(textToParse)) {\n                if ((symbol === \"d\" && (parsedValue <= 0 || parsedValue > 31)) ||\n                    (symbol === \"M\" && (parsedValue <= 0 || parsedValue > 11))) {\n                    if (isInCaretMode) {\n                        return extend(parseResult, {\n                            value: null,\n                            switchToNext: false\n                        });\n                    }\n                    else {\n                        // the value overflows the possible value range\n                        // thus reset the segment value regardless of the \"resetSegmentValue\" flag\n                        // otherwise the input is ignored and you cannot change the value,\n                        // e.g. \"03->(press 2)->02\" will not work and the user will be blocked on \"03\"\n                        textToParse = currentChar;\n                        parsedValue = parseToInt(textToParse);\n                    }\n                }\n                if (!isNumber(parsedValue) || !isParseableToInt(textToParse)) {\n                    return extend(parseResult, { value: null, switchToNext: false });\n                }\n                datePartValue = symbol === \"M\" ?\n                    parsedValue - JS_MONTH_OFFSET :\n                    parsedValue;\n                var isYear = symbol === \"y\";\n                var isMonth = symbol === \"M\";\n                var isDay = symbol === \"d\";\n                var newValue = cloneDate(this._value);\n                var invalidDateParts = this._partiallyInvalidDate.invalidDateParts || {};\n                var year = isYear ? datePartValue : invalidDateParts.y.value || newValue.getFullYear();\n                /* tslint:disable:no-shadowed-variable */\n                var month = isMonth ? datePartValue : invalidDateParts.M.value || newValue.getMonth();\n                /* tslint:enable:no-shadowed-variable */\n                var day = isDay ? datePartValue : invalidDateParts.d.value || invalidDateParts.E.value || newValue.getDate();\n                var hour = invalidDateParts.h.value || invalidDateParts.H.value || newValue.getHours();\n                var minutes = invalidDateParts.m.value || newValue.getMinutes();\n                var seconds = invalidDateParts.s.value || newValue.getSeconds();\n                var milliseconds = invalidDateParts.S.value || newValue.getMilliseconds();\n                var dateCandidate = createDate(year, month, day, hour, minutes, seconds, milliseconds);\n                var dateCandidateExists = areDatePartsEqualTo(dateCandidate, year, month, day, hour, minutes, seconds, milliseconds);\n                var newValueCandidate = isYear || isMonth || isDay ?\n                    this.modifyDateSymbolWithValue(newValue, symbol, isYear ? year : isMonth ? month : day) :\n                    null;\n                var invalidDateFound = false;\n                if (isMonth && newValueCandidate) {\n                    if (newValueCandidate.getMonth() === month) {\n                        if (this.getExisting(\"d\")) {\n                            if (dateCandidateExists) {\n                                newValue = cloneDate(dateCandidate);\n                                this.resetInvalidDateSymbol(symbol);\n                            }\n                            else {\n                                invalidDateFound = true;\n                                this.setInvalidDatePart(symbol, {\n                                    value: month,\n                                    date: cloneDate(newValueCandidate),\n                                    startDate: cloneDate(this.value)\n                                });\n                                this.setExisting(symbol, false);\n                            }\n                        }\n                        else if (dateCandidateExists) {\n                            this.resetInvalidDateSymbol(symbol);\n                            newValue = cloneDate(dateCandidate);\n                            if (this.getExisting(\"M\") && this.getExisting(\"y\")) {\n                                // changing from 28/Feb to 29/Feb to 29/March\n                                this.setExisting(\"d\", true);\n                                this.resetInvalidDateSymbol(\"d\");\n                            }\n                        }\n                        else {\n                            this.resetInvalidDateSymbol(symbol);\n                            newValue = cloneDate(newValueCandidate);\n                        }\n                    }\n                    else {\n                        invalidDateFound = true;\n                        this.setInvalidDatePart(symbol, {\n                            value: month,\n                            date: cloneDate(newValueCandidate),\n                            startDate: cloneDate(this.value)\n                        });\n                        this.setExisting(symbol, false);\n                    }\n                }\n                else if (isDay && newValueCandidate) {\n                    if (newValueCandidate.getDate() === day) {\n                        if (this.getExisting(\"M\")) {\n                            if (dateCandidateExists) {\n                                newValue = cloneDate(dateCandidate);\n                                this.resetInvalidDateSymbol(symbol);\n                            }\n                            else {\n                                invalidDateFound = true;\n                                this.setInvalidDatePart(symbol, {\n                                    value: day,\n                                    date: cloneDate(newValueCandidate),\n                                    startDate: cloneDate(this.value)\n                                });\n                                this.setExisting(symbol, false);\n                            }\n                        }\n                        else if (dateCandidateExists) {\n                            newValue = cloneDate(dateCandidate);\n                            this.resetInvalidDateSymbol(symbol);\n                            if (this.getExisting(\"d\") && this.getExisting(\"y\")) {\n                                // changing from 31/Jan to 31/Feb to 28/Feb\n                                this.setExisting(\"M\", true);\n                                this.resetInvalidDateSymbol(\"M\");\n                            }\n                        }\n                        else {\n                            this.resetInvalidDateSymbol(symbol);\n                            newValue = cloneDate(newValueCandidate);\n                        }\n                    }\n                    else {\n                        invalidDateFound = true;\n                        this.setInvalidDatePart(symbol, {\n                            value: day,\n                            date: cloneDate(this.value),\n                            startDate: cloneDate(this.value)\n                        });\n                        this.setExisting(symbol, false);\n                    }\n                }\n                else if (isYear && newValueCandidate) {\n                    if (newValueCandidate.getFullYear() === year) {\n                        if (this.getExisting(\"d\") && this.getExisting(\"M\")) {\n                            if (dateCandidateExists) {\n                                newValue = cloneDate(dateCandidate);\n                                this.resetInvalidDateSymbol(symbol);\n                            }\n                            else {\n                                invalidDateFound = true;\n                                this.setInvalidDatePart(symbol, {\n                                    value: year,\n                                    date: cloneDate(newValueCandidate),\n                                    startDate: cloneDate(this.value)\n                                });\n                                this.setExisting(symbol, false);\n                            }\n                        }\n                        else if (dateCandidateExists) {\n                            this.resetInvalidDateSymbol(symbol);\n                            newValue = cloneDate(dateCandidate);\n                            if (this.getExisting(\"M\") && this.getExisting(\"d\")) {\n                                this.setExisting(\"y\", true);\n                                this.resetInvalidDateSymbol(\"y\");\n                            }\n                        }\n                        else {\n                            this.resetInvalidDateSymbol(symbol);\n                            newValue = cloneDate(newValueCandidate);\n                        }\n                    }\n                    else {\n                        invalidDateFound = true;\n                        this.setInvalidDatePart(symbol, {\n                            value: year,\n                            date: cloneDate(newValueCandidate),\n                            startDate: cloneDate(this.value)\n                        });\n                        this.setExisting(symbol, false);\n                    }\n                }\n                if (!invalidDateFound) {\n                    this.setExisting(symbol, true);\n                    if (isInCaretMode && !isValidDate(parsedDate)) {\n                        var valueCandidate = this.intl.parseDate(basePrefix + middle + baseSuffix, this.format, this.localeId);\n                        if (isValidDate(valueCandidate)) {\n                            this._value = valueCandidate;\n                        }\n                    }\n                    else {\n                        this._value = newValue;\n                    }\n                    if (this.getValue()) {\n                        this.resetInvalidDate();\n                    }\n                }\n                var switchToNext = false;\n                if (symbol === \"M\") {\n                    if (parsedValue >= 2 || textToParse.length >= 2) {\n                        switchToNext = true;\n                    }\n                    else {\n                        switchToNext = false;\n                    }\n                }\n                else {\n                    if (hasFixedFormat) {\n                        var peekDateSwitchToNext = this.isPeekDateOverflowingDatePart({\n                            useBasePrefixAndSuffix: !this.autoCorrectParts,\n                            middle: middle,\n                            patternValue: patternValue,\n                            basePrefix: basePrefix,\n                            baseSuffix: baseSuffix,\n                            prefix: prefix,\n                            suffix: suffix,\n                            symbol: symbol,\n                            patternLength: patternLength,\n                            leadingZero: leadingZero\n                        }).switchToNext;\n                        switchToNext = peekDateSwitchToNext;\n                    }\n                    else {\n                        switchToNext = textToParse.length > segmentLength;\n                    }\n                }\n                return extend(parseResult, {\n                    value: null,\n                    switchToNext: switchToNext,\n                    hasInvalidDatePart: invalidDateFound\n                });\n            }\n        }\n        return extend(parseResult, { value: null, switchToNext: false });\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.symbolMap = function (symbol) {\n        return this.intl.splitDateFormat(this.format, this.localeId).reduce(dateSymbolMap, {})[symbol];\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.resetLeadingZero = function () {\n        var hasLeadingZero = this.leadingZero !== null;\n        this.setLeadingZero(null);\n        return hasLeadingZero;\n    };\n    DateObject.prototype.setLeadingZero = function (leadingZero) {\n        this.leadingZero = leadingZero;\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.getLeadingZero = function () {\n        return this.leadingZero || {};\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.normalizeCentury = function (date) {\n        if (!isPresent(date)) {\n            return date;\n        }\n        var twoDigitYear = cropTwoDigitYear(date);\n        var centuryBase = this.getNormalizedCenturyBase(twoDigitYear);\n        var normalizedDate = setYears(date, centuryBase + twoDigitYear);\n        return normalizedDate;\n    };\n    DateObject.prototype.incrementLeadingZero = function (symbol) {\n        var leadingZero = this.leadingZero || {};\n        leadingZero[symbol] = (leadingZero[symbol] || 0) + 1;\n        this.leadingZero = leadingZero;\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.isAbbrMonth = function (parts, symbol) {\n        var pattern = this.partPattern(parts, symbol);\n        return pattern.type === 'month' && pattern.names;\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.partPattern = function (parts, symbol) {\n        return parts.filter(function (part) { return part.pattern.indexOf(symbol) !== -1; })[0];\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.peek = function (value, pattern) {\n        var peekValue = value.replace(/^0*/, '') + '0';\n        return padZero(pattern.length - peekValue.length) + peekValue;\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.matchMonth = function (typedChar) {\n        this.typedMonthPart += typedChar.toLowerCase();\n        if (this.monthNames.length === 0) {\n            return '';\n        }\n        while (this.typedMonthPart.length > 0) {\n            for (var i = 0; i < this.monthNames.length; i++) {\n                if (this.monthNames[i].toLowerCase().indexOf(this.typedMonthPart) === 0) {\n                    return this.monthNames[i];\n                }\n            }\n            var monthAsNum = parseInt(this.typedMonthPart, 10);\n            /* ensure they exact match */\n            if (monthAsNum >= 1 && monthAsNum <= 12 && monthAsNum.toString() === this.typedMonthPart) {\n                return this.monthNames[monthAsNum - 1];\n            }\n            this.typedMonthPart = this.typedMonthPart.substring(1, this.typedMonthPart.length);\n        }\n        return '';\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.matchDayPeriod = function (typedChar, symbol) {\n        var lowerChart = typedChar.toLowerCase();\n        if (symbol === 'a' && this.dayPeriods) {\n            if (this.dayPeriods.am.toLowerCase().startsWith(lowerChart)) {\n                return this.dayPeriods.am;\n            }\n            else if (this.dayPeriods.pm.toLowerCase().startsWith(lowerChart)) {\n                return this.dayPeriods.pm;\n            }\n        }\n        return '';\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.allFormattedMonths = function (locale) {\n        if (locale === void 0) { locale = \"en\"; }\n        var dateFormatParts = this.intl.splitDateFormat(this.format, this.localeId);\n        for (var i = 0; i < dateFormatParts.length; i++) {\n            if (dateFormatParts[i].type === 'month' && dateFormatParts[i].names) {\n                return this.intl.dateFormatNames(locale, dateFormatParts[i].names);\n            }\n        }\n        return [];\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.allDayPeriods = function (locale) {\n        if (locale === void 0) { locale = \"en\"; }\n        var dateFormatParts = this.intl.splitDateFormat(this.format);\n        for (var i = 0; i < dateFormatParts.length; i++) {\n            if (dateFormatParts[i].type === \"dayperiod\" && dateFormatParts[i].names) {\n                return this.intl.dateFormatNames(locale, dateFormatParts[i].names);\n            }\n        }\n        return null;\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.patternLength = function (pattern) {\n        if (pattern[0] === 'y') {\n            return 4;\n        }\n        if (SHORT_PATTERN_LENGTH_REGEXP.test(pattern)) {\n            return 2;\n        }\n        return 0;\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.dateFormatString = function (date, format) {\n        var dateFormatParts = this.intl.splitDateFormat(format, this.localeId);\n        var parts = [];\n        var partMap = [];\n        for (var i = 0; i < dateFormatParts.length; i++) {\n            var partLength = this.intl.formatDate(date, { pattern: dateFormatParts[i].pattern }, this.localeId).length;\n            while (partLength > 0) {\n                parts.push(this.symbols[dateFormatParts[i].pattern[0]] || Constants.formatSeparator);\n                partMap.push(dateFormatParts[i]);\n                partLength--;\n            }\n        }\n        var returnValue = new Mask();\n        returnValue.symbols = parts.join('');\n        returnValue.partMap = partMap;\n        return returnValue;\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.merge = function (text, mask) {\n        // Important: right to left.\n        var resultText = '';\n        var resultFormat = '';\n        var format = mask.symbols;\n        var processTextSymbolsEnded = false;\n        var ignoreFormatSymbolsCount = 0;\n        var formattedDates = this.getFormattedInvalidDates(format);\n        for (var formatSymbolIndex = format.length - 1; formatSymbolIndex >= 0; formatSymbolIndex--) {\n            var partsForSegment = this.getPartsForSegment(mask, formatSymbolIndex);\n            if (this.knownParts.indexOf(format[formatSymbolIndex]) === -1 || this.getExisting(format[formatSymbolIndex])) {\n                if (this.autoCorrectParts) {\n                    resultText = text[formatSymbolIndex] + resultText;\n                }\n                else {\n                    if (text.length !== format.length) {\n                        if (processTextSymbolsEnded) {\n                            resultText = text[formatSymbolIndex] + resultText;\n                        }\n                        else if (ignoreFormatSymbolsCount > 0) {\n                            resultText = text[formatSymbolIndex] + resultText;\n                            ignoreFormatSymbolsCount--;\n                            if (ignoreFormatSymbolsCount <= 0) {\n                                processTextSymbolsEnded = true;\n                            }\n                        }\n                        else {\n                            resultText = (text[formatSymbolIndex + text.length - format.length] || \"\") + resultText;\n                        }\n                    }\n                    else {\n                        resultText = text[formatSymbolIndex] + resultText;\n                    }\n                }\n                resultFormat = format[formatSymbolIndex] + resultFormat;\n            }\n            else {\n                var symbol = format[formatSymbolIndex];\n                var formatSymbolIndexModifier = 0;\n                if (this.autoCorrectParts || (!this.autoCorrectParts && !this.getInvalidDatePartValue(symbol))) {\n                    while (formatSymbolIndex >= 0 && symbol === format[formatSymbolIndex]) {\n                        formatSymbolIndex--;\n                    }\n                    formatSymbolIndex++;\n                }\n                if (this.leadingZero && this.leadingZero[symbol]) {\n                    resultText = '0' + resultText;\n                }\n                else {\n                    if (!this.autoCorrectParts && this.getInvalidDatePartValue(symbol)) {\n                        var datePartText = this.getInvalidDatePartValue(symbol).toString();\n                        if (symbol === \"M\") {\n                            datePartText = (parseToInt(this.getInvalidDatePartValue(symbol)) + JS_MONTH_OFFSET).toString();\n                            if (partsForSegment.length > MONTH_PART_WITH_WORDS_THRESHOLD) {\n                                resultText = formattedDates[symbol][formatSymbolIndex] + resultText;\n                            }\n                            else {\n                                datePartText = (parseToInt(this.getInvalidDatePartValue(symbol)) + JS_MONTH_OFFSET).toString();\n                                var formattedDatePart = padZero(partsForSegment.length - datePartText.length) + datePartText;\n                                resultText = formattedDatePart + resultText;\n                                formatSymbolIndexModifier = partsForSegment.length - 1;\n                                ignoreFormatSymbolsCount = datePartText.length - partsForSegment.length;\n                            }\n                        }\n                        else {\n                            var formattedDatePart = padZero(partsForSegment.length - datePartText.length) + datePartText;\n                            resultText = formattedDatePart + resultText;\n                            formatSymbolIndexModifier = partsForSegment.length - 1;\n                            ignoreFormatSymbolsCount = datePartText.length - partsForSegment.length;\n                        }\n                    }\n                    else {\n                        resultText = this.dateFieldName(mask.partMap[formatSymbolIndex]) + resultText;\n                    }\n                }\n                while (resultFormat.length < resultText.length) {\n                    resultFormat = format[formatSymbolIndex] + resultFormat;\n                }\n                if (formatSymbolIndexModifier !== 0) {\n                    formatSymbolIndex = (formatSymbolIndex - formatSymbolIndexModifier) + (text.length - format.length);\n                }\n            }\n        }\n        return { text: resultText, format: resultFormat };\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.dateFieldName = function (part) {\n        var formatPlaceholder = this.formatPlaceholder || 'wide';\n        if (formatPlaceholder[part.type]) {\n            return formatPlaceholder[part.type];\n        }\n        if (formatPlaceholder === 'formatPattern') {\n            return part.pattern;\n        }\n        return this.intl.dateFieldName(Object.assign(part, { nameType: formatPlaceholder }));\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.getNormalizedCenturyBase = function (twoDigitYear) {\n        return twoDigitYear > this.twoDigitYearMax ?\n            PREVIOUS_CENTURY_BASE :\n            CURRENT_CENTURY_BASE;\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.shouldNormalizeCentury = function () {\n        return this.intl.splitDateFormat(this.format).some(function (part) { return part.pattern === 'yy'; });\n    };\n    DateObject.prototype.resetInvalidDate = function () {\n        var _this = this;\n        this._partiallyInvalidDate.startDate = null;\n        Object.keys(this._partiallyInvalidDate.invalidDateParts).forEach(function (key) {\n            _this.resetInvalidDatePart(key);\n        });\n    };\n    DateObject.prototype.resetInvalidDateSymbol = function (symbol) {\n        var _this = this;\n        this.resetInvalidDatePart(symbol);\n        var shouldResetInvalidDate = true;\n        Object.keys(this._partiallyInvalidDate.invalidDateParts).forEach(function (key) {\n            if (_this._partiallyInvalidDate.invalidDateParts[key] &&\n                isPresent(_this._partiallyInvalidDate.invalidDateParts[key].value)) {\n                shouldResetInvalidDate = false;\n            }\n        });\n        if (shouldResetInvalidDate) {\n            this.resetInvalidDate();\n        }\n    };\n    DateObject.prototype.resetInvalidDatePart = function (symbol) {\n        if (this._partiallyInvalidDate.invalidDateParts[symbol]) {\n            this._partiallyInvalidDate.invalidDateParts[symbol] = {\n                value: null,\n                date: null,\n                startDateOffset: 0\n            };\n        }\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.getInvalidDatePart = function (symbol) {\n        var invalidDatePart = this._partiallyInvalidDate.invalidDateParts[symbol];\n        return invalidDatePart || {};\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.getInvalidDatePartValue = function (symbol) {\n        var invalidDatePart = this._partiallyInvalidDate.invalidDateParts[symbol];\n        return (invalidDatePart || {}).value;\n    };\n    DateObject.prototype.setInvalidDatePart = function (symbol, _a) {\n        var _b = _a.value, value = _b === void 0 ? null : _b, _c = _a.date, date = _c === void 0 ? null : _c, _d = _a.startDateOffset, startDateOffset = _d === void 0 ? 0 : _d, _e = _a.startDate, startDate = _e === void 0 ? null : _e;\n        if (this._partiallyInvalidDate.invalidDateParts[symbol]) {\n            this._partiallyInvalidDate.invalidDateParts[symbol].value = value;\n            this._partiallyInvalidDate.invalidDateParts[symbol].date = date;\n            this._partiallyInvalidDate.invalidDateParts[symbol].startDateOffset = startDateOffset;\n            this._partiallyInvalidDate.startDate = startDate;\n        }\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.hasInvalidDatePart = function () {\n        var _this = this;\n        var hasInvalidDatePart = false;\n        Object.keys(this._partiallyInvalidDate.invalidDateParts).forEach(function (key) {\n            if (_this._partiallyInvalidDate.invalidDateParts[key] &&\n                isPresent(_this._partiallyInvalidDate.invalidDateParts[key].value)) {\n                hasInvalidDatePart = true;\n            }\n        });\n        return hasInvalidDatePart;\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.modifyDateSymbolWithOffset = function (date, symbol, offset) {\n        var newValue = cloneDate(date);\n        var timeModified = false;\n        switch (symbol) {\n            case 'y':\n                newValue.setFullYear(newValue.getFullYear() + offset);\n                break;\n            case 'M':\n                newValue = addMonths(this.value, offset);\n                break;\n            case 'd':\n            case 'E':\n                newValue.setDate(newValue.getDate() + offset);\n                break;\n            case 'h':\n            case 'H':\n                newValue.setHours(newValue.getHours() + offset);\n                timeModified = true;\n                break;\n            case 'm':\n                newValue.setMinutes(newValue.getMinutes() + offset);\n                timeModified = true;\n                break;\n            case 's':\n                newValue.setSeconds(newValue.getSeconds() + offset);\n                timeModified = true;\n                break;\n            case \"S\":\n                newValue.setMilliseconds(newValue.getMilliseconds() + offset);\n                break;\n            case 'a':\n                newValue.setHours(newValue.getHours() + (12 * offset));\n                timeModified = true;\n                break;\n            default: break;\n        }\n        return {\n            date: newValue,\n            timeModified: timeModified\n        };\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.modifyDateSymbolWithValue = function (date, symbol, value) {\n        var newValue = cloneDate(date);\n        switch (symbol) {\n            case 'y':\n                newValue.setFullYear(value);\n                break;\n            case 'M':\n                newValue = addMonths(date, value - date.getMonth());\n                break;\n            case 'd':\n            case 'E':\n                newValue.setDate(value);\n                break;\n            case 'h':\n            case 'H':\n                newValue.setHours(value);\n                break;\n            case 'm':\n                newValue.setMinutes(value);\n                break;\n            case 's':\n                newValue.setSeconds(value);\n                break;\n            case \"S\":\n                newValue.setMilliseconds(value);\n                break;\n            case 'a':\n                newValue.setHours(value);\n                break;\n            default: break;\n        }\n        return newValue;\n    };\n    DateObject.prototype.markDatePartsAsExisting = function () {\n        this.modifyExisting(true);\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.getPartsForSegment = function (mask, partIndex) {\n        var segmentPart = mask.partMap[partIndex];\n        var partsForSegment = [];\n        for (var maskPartIndex = partIndex; maskPartIndex < mask.partMap.length; maskPartIndex++) {\n            var part = mask.partMap[maskPartIndex];\n            if (segmentPart.type === part.type && segmentPart.pattern === part.pattern) {\n                partsForSegment.push(part);\n            }\n            else {\n                break;\n            }\n        }\n        for (var maskPartIndex = partIndex - 1; maskPartIndex >= 0; maskPartIndex--) {\n            var part = mask.partMap[maskPartIndex];\n            if (segmentPart.type === part.type && segmentPart.pattern === part.pattern) {\n                partsForSegment.unshift(part);\n            }\n            else {\n                break;\n            }\n        }\n        return partsForSegment;\n    };\n    /**\n     * @hidden\n     */\n    DateObject.prototype.isPeekDateOverflowingDatePart = function (_a) {\n        var useBasePrefixAndSuffix = _a.useBasePrefixAndSuffix, middle = _a.middle, patternValue = _a.patternValue, basePrefix = _a.basePrefix, baseSuffix = _a.baseSuffix, prefix = _a.prefix, suffix = _a.suffix, symbol = _a.symbol, patternLength = _a.patternLength, leadingZero = _a.leadingZero;\n        // move to next segment if the part will overflow with next char\n        // when start from empty date (01, then 010), padded zeros should be trimmed\n        var peekedValue = this.peek(middle, patternValue);\n        var peekedDateString = useBasePrefixAndSuffix ?\n            \"\".concat(basePrefix).concat(peekedValue).concat(baseSuffix) :\n            \"\".concat(prefix).concat(peekedValue).concat(suffix);\n        var peekedDate = this.intl.parseDate(peekedDateString, this.format, this.localeId);\n        var leadingZeroOffset = (this.leadingZero || {})[symbol] || 0;\n        var patternSatisfied = (leadingZeroOffset + unpadZero(middle).length) >= patternLength;\n        var parsedPeekedValue = parseToInt(peekedValue);\n        var switchToNext = peekedDate === null ||\n            (leadingZero[symbol] ?\n                patternValue.length <= middle.length :\n                patternSatisfied);\n        return {\n            peekedDate: peekedDate,\n            peekedDateString: peekedDateString,\n            peekedValue: peekedValue,\n            parsedPeekedValue: parsedPeekedValue,\n            switchToNext: switchToNext\n        };\n    };\n    return DateObject;\n}());\nexport { DateObject };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,SAAS,EAAEC,UAAU,EAAEC,OAAO,EAAEC,OAAO,EAAEC,cAAc,QAAQ,2BAA2B;AAC9G,SAASC,IAAI,QAAQ,QAAQ;AAC7B,SAASC,aAAa,EAAEC,OAAO,EAAEC,SAAS,QAAQ,oBAAoB;AACtE,SAASC,MAAM,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,KAAK,EAAEC,mBAAmB,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,gBAAgB,QAAQ,SAAS;AACxJ,SAASC,SAAS,QAAQ,aAAa;AACvC,IAAIC,oBAAoB,GAAG,CAAC;AAC5B,IAAIC,iBAAiB,GAAG,IAAI;AAC5B,IAAIC,qBAAqB,GAAG,IAAI;AAChC,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,2BAA2B,GAAG,aAAa;AAC/C,IAAIC,+BAA+B,GAAG,CAAC;AACvC,IAAIC,YAAY,GAAG,GAAG;AACtB;AACA,IAAIC,eAAe,GAAG,CAAC;AACvB,IAAIC,UAAU,GAAG,aAAe,YAAY;EACxC,SAASA,UAAUA,CAACC,EAAE,EAAE;IACpB,IAAIC,WAAW,GAAGD,EAAE,CAACC,WAAW;MAAEC,iBAAiB,GAAGF,EAAE,CAACE,iBAAiB;MAAEC,MAAM,GAAGH,EAAE,CAACG,MAAM;MAAEC,EAAE,GAAGJ,EAAE,CAACK,SAAS;MAAEA,SAAS,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE;MAAEE,EAAE,GAAGN,EAAE,CAACO,eAAe;MAAEA,eAAe,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAGhB,SAAS,CAACiB,eAAe,GAAGD,EAAE;MAAEE,EAAE,GAAGR,EAAE,CAACS,KAAK;MAAEA,KAAK,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,EAAE;MAAEE,EAAE,GAAGV,EAAE,CAACW,gBAAgB;MAAEA,gBAAgB,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,EAAE;MAAEE,EAAE,GAAGZ,EAAE,CAACa,eAAe;MAAEA,eAAe,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE;MAAEE,EAAE,GAAGd,EAAE,CAACe,eAAe;MAAEA,eAAe,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,EAAE;IAC3f,IAAI,CAACE,IAAI,GAAG,IAAI;IAChB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,IAAI,GAAG,IAAI;IAChB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,UAAU,GAAG,YAAY;IAC9B,IAAI,CAACC,OAAO,GAAG;MACX,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE;IACT,CAAC;IACD,IAAI,CAACC,MAAM,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACnC,IAAI,CAACxB,SAAS,GAAG,KAAK;IACtB,IAAI,CAACyB,qBAAqB,GAAG;MACzBC,SAAS,EAAE,IAAI;MACfC,gBAAgB,EAAE;QACd,GAAG,EAAE;UAAEvB,KAAK,EAAE,IAAI;UAAES,IAAI,EAAE,IAAI;UAAEe,eAAe,EAAE;QAAE,CAAC;QACpD,GAAG,EAAE;UAAExB,KAAK,EAAE,IAAI;UAAES,IAAI,EAAE,IAAI;UAAEe,eAAe,EAAE;QAAE,CAAC;QACpD,GAAG,EAAE;UAAExB,KAAK,EAAE,IAAI;UAAES,IAAI,EAAE,IAAI;UAAEe,eAAe,EAAE;QAAE,CAAC;QACpD,GAAG,EAAE;UAAExB,KAAK,EAAE,IAAI;UAAES,IAAI,EAAE,IAAI;UAAEe,eAAe,EAAE;QAAE,CAAC;QACpD,GAAG,EAAE;UAAExB,KAAK,EAAE,IAAI;UAAES,IAAI,EAAE,IAAI;UAAEe,eAAe,EAAE;QAAE,CAAC;QACpD,GAAG,EAAE;UAAExB,KAAK,EAAE,IAAI;UAAES,IAAI,EAAE,IAAI;UAAEe,eAAe,EAAE;QAAE,CAAC;QACpD,GAAG,EAAE;UAAExB,KAAK,EAAE,IAAI;UAAES,IAAI,EAAE,IAAI;UAAEe,eAAe,EAAE;QAAE,CAAC;QACpD,GAAG,EAAE;UAAExB,KAAK,EAAE,IAAI;UAAES,IAAI,EAAE,IAAI;UAAEe,eAAe,EAAE;QAAE,CAAC;QACpD,GAAG,EAAE;UAAExB,KAAK,EAAE,IAAI;UAAES,IAAI,EAAE,IAAI;UAAEe,eAAe,EAAE;QAAE,CAAC;QACpD,GAAG,EAAE;UAAExB,KAAK,EAAE,IAAI;UAAES,IAAI,EAAE,IAAI;UAAEe,eAAe,EAAE;QAAE;MACvD;IACJ,CAAC;IACD,IAAI,CAACC,UAAU,CAAC;MACZjC,WAAW,EAAEA,WAAW;MACxBC,iBAAiB,EAAEA,iBAAiB;MACpCC,MAAM,EAAEA,MAAM;MACdE,SAAS,EAAEA,SAAS;MACpBE,eAAe,EAAEA,eAAe;MAChCE,KAAK,EAAEA,KAAK;MACZE,gBAAgB,EAAEA,gBAAgB;MAClCE,eAAe,EAAEA,eAAe;MAChCE,eAAe,EAAEA;IACrB,CAAC,CAAC;IACF,IAAI,CAACN,KAAK,EAAE;MACR,IAAI,CAACmB,MAAM,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACnC,IAAIM,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAAC3B,KAAK,EAAE,IAAI,CAACN,MAAM,CAAC,CAACwB,OAAO;MACzE,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,YAAY,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QAC1C,IAAI,CAACE,WAAW,CAACJ,YAAY,CAACE,CAAC,CAAC,EAAE,KAAK,CAAC;MAC5C;IACJ,CAAC,MACI;MACD,IAAI,CAACT,MAAM,GAAGzD,SAAS,CAACsC,KAAK,CAAC;IAClC;EACJ;EACA+B,MAAM,CAACC,cAAc,CAAC1C,UAAU,CAAC2C,SAAS,EAAE,OAAO,EAAE;IACjDC,GAAG,EAAE,SAAAA,CAAA,EAAY;MACb,OAAO,IAAI,CAACf,MAAM;IACtB,CAAC;IACDgB,GAAG,EAAE,SAAAA,CAAUnC,KAAK,EAAE;MAClB,IAAIA,KAAK,IAAI,EAAEA,KAAK,YAAYoC,IAAI,CAAC,EAAE;QACnC;QACA;MACJ;MACA,IAAI,CAACjB,MAAM,GAAGnB,KAAK;MACnB,IAAI,CAACqC,gBAAgB,CAAC,CAAC;IAC3B,CAAC;IACDC,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE;EAClB,CAAC,CAAC;EACFR,MAAM,CAACC,cAAc,CAAC1C,UAAU,CAAC2C,SAAS,EAAE,UAAU,EAAE;IACpDC,GAAG,EAAE,SAAAA,CAAA,EAAY;MACb,IAAIM,QAAQ,GAAG3D,SAAS,CAAC4D,eAAe;MACxC,IAAIC,QAAQ,GAAGX,MAAM,CAACY,IAAI,CAAC,IAAI,CAACC,IAAI,CAACC,IAAI,CAAC;MAC1C,KAAK,IAAIjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,QAAQ,CAACb,MAAM,EAAED,CAAC,EAAE,EAAE;QACtC,IAAIkB,GAAG,GAAGJ,QAAQ,CAACd,CAAC,CAAC;QACrB,IAAI5B,KAAK,GAAG,IAAI,CAAC4C,IAAI,CAACC,IAAI,CAACC,GAAG,CAAC;QAC/B,IAAI9C,KAAK,CAAC+C,IAAI,IAAI/C,KAAK,CAACgD,QAAQ,IAAIhD,KAAK,CAACiD,OAAO,IAC7CjD,KAAK,CAAC+C,IAAI,KAAKlE,SAAS,CAAC4D,eAAe,EAAE;UAC1CD,QAAQ,GAAGxC,KAAK,CAAC+C,IAAI;UACrB;QACJ;MACJ;MACA,OAAOP,QAAQ;IACnB,CAAC;IACDF,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE;EAClB,CAAC,CAAC;EACF;AACJ;AACA;EACIjD,UAAU,CAAC2C,SAAS,CAACR,UAAU,GAAG,UAAUyB,OAAO,EAAE;IACjD,IAAI,CAACN,IAAI,GAAGM,OAAO,CAAC1D,WAAW;IAC/B,IAAI,CAACC,iBAAiB,GAAGyD,OAAO,CAACzD,iBAAiB,IAAI,MAAM;IAC5D,IAAI,CAACC,MAAM,GAAGwD,OAAO,CAACxD,MAAM;IAC5B,IAAI,CAACE,SAAS,GAAGsD,OAAO,CAACtD,SAAS;IAClC,IAAI,CAACuD,UAAU,GAAG,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAACZ,QAAQ,CAAC;IACxD,IAAI,CAACa,UAAU,GAAG,IAAI,CAACC,aAAa,CAAC,IAAI,CAACd,QAAQ,CAAC;IACnD,IAAI,CAAC1C,eAAe,GAAGoD,OAAO,CAACpD,eAAe;IAC9C,IAAI,CAACI,gBAAgB,GAAGgD,OAAO,CAAChD,gBAAgB;IAChD,IAAI,CAACE,eAAe,GAAG8C,OAAO,CAAC9C,eAAe;IAC9C,IAAI,CAACE,eAAe,GAAG4C,OAAO,CAAC5C,eAAe;EAClD,CAAC;EACDhB,UAAU,CAAC2C,SAAS,CAACsB,QAAQ,GAAG,UAAUvD,KAAK,EAAE;IAC7C,IAAI,CAACA,KAAK,EAAE;MACR,IAAI,CAACmB,MAAM,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACnC,IAAI,CAACoC,cAAc,CAAC,KAAK,CAAC;IAC9B,CAAC,MACI,IAAI,CAAC5F,OAAO,CAACoC,KAAK,EAAE,IAAI,CAACmB,MAAM,CAAC,EAAE;MACnC,IAAI,CAACA,MAAM,GAAGzD,SAAS,CAACsC,KAAK,CAAC;MAC9B,IAAI,CAACwD,cAAc,CAAC,IAAI,CAAC;IAC7B,CAAC,MACI,IAAI5F,OAAO,CAACoC,KAAK,EAAE,IAAI,CAACmB,MAAM,CAAC,IAAI,IAAI,CAACkC,UAAU,EAAE;MACrD,IAAI,CAACvB,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC;IAC/B;IACA,IAAI,CAACO,gBAAgB,CAAC,CAAC;EAC3B,CAAC;EACD;AACJ;AACA;EACI/C,UAAU,CAAC2C,SAAS,CAACwB,QAAQ,GAAG,YAAY;IACxC,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAIC,IAAI,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;MAAE,OAAOD,CAAC,IAAIC,CAAC,CAACC,IAAI,KAAK,SAAS,IAAID,CAAC,CAACC,IAAI,KAAK,WAAW,IAAIJ,KAAK,CAACK,WAAW,CAACF,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,CAAC;IAAE,CAAC;IAC7H,OAAO,IAAI,CAACpB,IAAI,CAACqB,eAAe,CAAC,IAAI,CAACvE,MAAM,EAAE,IAAI,CAAC8C,QAAQ,CAAC,CAAC0B,MAAM,CAACP,IAAI,EAAE,KAAK,CAAC;EACpF,CAAC;EACD;AACJ;AACA;EACIrE,UAAU,CAAC2C,SAAS,CAACkC,QAAQ,GAAG,YAAY;IACxC,KAAK,IAAIvC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACX,UAAU,CAACY,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7C,IAAI,CAAC,IAAI,CAACmC,WAAW,CAAC,IAAI,CAAC9C,UAAU,CAACW,CAAC,CAAC,CAAC,EAAE;QACvC,OAAO,IAAI;MACf;IACJ;IACA,OAAOlE,SAAS,CAAC,IAAI,CAACsC,KAAK,CAAC;EAChC,CAAC;EACD;AACJ;AACA;EACIV,UAAU,CAAC2C,SAAS,CAACmC,iBAAiB,GAAG,YAAY;IACjD;IACA;IACA;IACA;IACA,OAAOzG,UAAU,CAACoB,iBAAiB,EAAE,CAAC,EAAE,EAAE,CAAC;EAC/C,CAAC;EACD;AACJ;AACA;EACIO,UAAU,CAAC2C,SAAS,CAACb,cAAc,GAAG,YAAY;IAC9C,OAAOvD,OAAO,CAAC,IAAI,CAACuG,iBAAiB,CAAC,CAAC,CAAC;EAC5C,CAAC;EACD;AACJ;AACA;EACI9E,UAAU,CAAC2C,SAAS,CAACoC,gBAAgB,GAAG,UAAU3E,MAAM,EAAE;IACtD,OAAO,IAAI,CAACkD,IAAI,CAAC0B,UAAU,CAAC,IAAI,CAACH,QAAQ,CAAC,CAAC,EAAEzE,MAAM,EAAE,IAAI,CAAC8C,QAAQ,CAAC;EACvE,CAAC;EACD;AACJ;AACA;EACIlD,UAAU,CAAC2C,SAAS,CAACsC,gBAAgB,GAAG,UAAUC,YAAY,EAAE;IAC5D,IAAIA,YAAY,KAAK,KAAK,CAAC,EAAE;MAAEA,YAAY,GAAG,EAAE;IAAE;IAClD,IAAI9E,MAAM,GAAG8E,YAAY,IAAI,IAAI,CAAC9E,MAAM;IACxC,IAAI+E,IAAI,GAAG,IAAI,CAAC7B,IAAI,CAAC0B,UAAU,CAAC,IAAI,CAACtE,KAAK,EAAEN,MAAM,EAAE,IAAI,CAAC8C,QAAQ,CAAC;IAClE,IAAIkC,IAAI,GAAG,IAAI,CAAC/C,gBAAgB,CAAC,IAAI,CAAC3B,KAAK,EAAEN,MAAM,CAAC;IACpD,IAAI,CAAC,IAAI,CAACQ,gBAAgB,IAAI,IAAI,CAACmB,qBAAqB,CAACC,SAAS,EAAE;MAChE,IAAIqD,oBAAoB,GAAG,EAAE;MAC7B,IAAIC,aAAa,GAAG,IAAI,CAAChC,IAAI,CAAC0B,UAAU,CAAC,IAAI,CAACtE,KAAK,EAAEN,MAAM,EAAE,IAAI,CAAC8C,QAAQ,CAAC;MAC3E,IAAIqC,cAAc,GAAG,IAAI,CAACC,wBAAwB,CAACpF,MAAM,CAAC;MAC1D,KAAK,IAAIkC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgD,aAAa,CAAC/C,MAAM,EAAED,CAAC,EAAE,EAAE;QAC3C,IAAImD,MAAM,GAAGL,IAAI,CAACxD,OAAO,CAACU,CAAC,CAAC;QAC5B,IAAI8C,IAAI,CAACM,OAAO,CAACpD,CAAC,CAAC,CAACkC,IAAI,KAAK,SAAS,EAAE;UACpCa,oBAAoB,IAAIF,IAAI,CAAC7C,CAAC,CAAC;QACnC,CAAC,MACI,IAAI,IAAI,CAACqD,uBAAuB,CAACF,MAAM,CAAC,EAAE;UAC3C,IAAIG,eAAe,GAAG,IAAI,CAACC,kBAAkB,CAACT,IAAI,EAAE9C,CAAC,CAAC;UACtD,IAAImD,MAAM,KAAK,GAAG,EAAE;YAChB,IAAIK,YAAY,GAAG,CAAC7G,UAAU,CAAC,IAAI,CAAC0G,uBAAuB,CAACF,MAAM,CAAC,CAAC,GAAG1F,eAAe,EAAEgG,QAAQ,CAAC,CAAC;YAClG,IAAIH,eAAe,CAACrD,MAAM,GAAG1C,+BAA+B,EAAE;cAC1DwF,oBAAoB,IAAIE,cAAc,CAACE,MAAM,CAAC,CAACnD,CAAC,CAAC;YACrD,CAAC,MACI;cACD,IAAI,IAAI,CAACqD,uBAAuB,CAACF,MAAM,CAAC,EAAE;gBACtC,IAAIO,iBAAiB,GAAGrH,OAAO,CAACiH,eAAe,CAACrD,MAAM,GAAGuD,YAAY,CAACvD,MAAM,CAAC,GAAGuD,YAAY;gBAC5FT,oBAAoB,IAAIW,iBAAiB;gBACzC;gBACA1D,CAAC,IAAIsD,eAAe,CAACrD,MAAM,GAAG,CAAC;cACnC,CAAC,MACI;gBACD8C,oBAAoB,IAAIE,cAAc,CAACE,MAAM,CAAC,CAACnD,CAAC,CAAC;cACrD;YACJ;UACJ,CAAC,MACI;YACD,IAAI,IAAI,CAACqD,uBAAuB,CAACF,MAAM,CAAC,EAAE;cACtC,IAAIK,YAAY,GAAG,IAAI,CAACH,uBAAuB,CAACF,MAAM,CAAC,CAACM,QAAQ,CAAC,CAAC;cAClE,IAAIC,iBAAiB,GAAGrH,OAAO,CAACiH,eAAe,CAACrD,MAAM,GAAGuD,YAAY,CAACvD,MAAM,CAAC,GAAGuD,YAAY;cAC5FT,oBAAoB,IAAIW,iBAAiB;cACzC;cACA1D,CAAC,IAAIsD,eAAe,CAACrD,MAAM,GAAG,CAAC;YACnC,CAAC,MACI;cACD8C,oBAAoB,IAAIE,cAAc,CAACE,MAAM,CAAC,CAACnD,CAAC,CAAC;YACrD;UACJ;QACJ,CAAC,MACI;UACD+C,oBAAoB,IAAIF,IAAI,CAAC7C,CAAC,CAAC;QACnC;MACJ;MACA6C,IAAI,GAAGE,oBAAoB;IAC/B;IACA,IAAIY,MAAM,GAAG,IAAI,CAACC,KAAK,CAACf,IAAI,EAAEC,IAAI,CAAC;IACnC,OAAOa,MAAM;EACjB,CAAC;EACD;AACJ;AACA;EACIjG,UAAU,CAAC2C,SAAS,CAAC6C,wBAAwB,GAAG,UAAUN,YAAY,EAAE;IACpE,IAAId,KAAK,GAAG,IAAI;IAChB,IAAIc,YAAY,KAAK,KAAK,CAAC,EAAE;MAAEA,YAAY,GAAG,EAAE;IAAE;IAClD,IAAI9E,MAAM,GAAG8E,YAAY,IAAI,IAAI,CAAC9E,MAAM;IACxC,IAAI+F,uBAAuB,GAAG;MAC1B,GAAG,EAAE,EAAE;MACP,GAAG,EAAE,EAAE;MACP,GAAG,EAAE,EAAE;MACP,GAAG,EAAE,EAAE;MACP,GAAG,EAAE,EAAE;MACP,GAAG,EAAE,EAAE;MACP,GAAG,EAAE,EAAE;MACP,GAAG,EAAE,EAAE;MACP,GAAG,EAAE,EAAE;MACP,GAAG,EAAE;IACT,CAAC;IACD1D,MAAM,CAACY,IAAI,CAAC,IAAI,CAACtB,qBAAqB,CAACE,gBAAgB,CAAC,CAACmE,OAAO,CAAC,UAAU5C,GAAG,EAAE;MAC5E,IAAIrC,IAAI,GAAGiD,KAAK,CAACiC,kBAAkB,CAAC7C,GAAG,CAAC,CAACrC,IAAI;MAC7C,IAAIA,IAAI,EAAE;QACN,IAAImF,oBAAoB,GAAGlC,KAAK,CAACd,IAAI,CAAC0B,UAAU,CAAC7D,IAAI,EAAEf,MAAM,EAAEgE,KAAK,CAAClB,QAAQ,CAAC;QAC9EiD,uBAAuB,CAAC3C,GAAG,CAAC,GAAG8C,oBAAoB;MACvD;IACJ,CAAC,CAAC;IACF,OAAOH,uBAAuB;EAClC,CAAC;EACDnG,UAAU,CAAC2C,SAAS,CAACuB,cAAc,GAAG,UAAUxD,KAAK,EAAE;IACnD,IAAI0B,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAAC3B,KAAK,EAAE,IAAI,CAACN,MAAM,CAAC,CAACwB,OAAO;IACzE,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,YAAY,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MAC1C,IAAI,CAACE,WAAW,CAACJ,YAAY,CAACE,CAAC,CAAC,EAAE5B,KAAK,CAAC;IAC5C;EACJ,CAAC;EACD;AACJ;AACA;EACIV,UAAU,CAAC2C,SAAS,CAAC8B,WAAW,GAAG,UAAUgB,MAAM,EAAE;IACjD,QAAQA,MAAM;MACV,KAAK,GAAG;QAAE,OAAO,IAAI,CAACxE,IAAI;MAC1B,KAAK,GAAG;MACR,KAAK,GAAG;QAAE,OAAO,IAAI,CAACC,KAAK;MAC3B,KAAK,GAAG;QAAE,OAAO,IAAI,CAACC,IAAI;MAC1B,KAAK,GAAG;QAAE,OAAO,IAAI,CAACA,IAAI,IAAI,IAAI,CAACD,KAAK,IAAI,IAAI,CAACD,IAAI;MACrD,KAAK,GAAG;MACR,KAAK,GAAG;QAAE,OAAO,IAAI,CAACG,KAAK;MAC3B,KAAK,GAAG;MACR,KAAK,GAAG;QAAE,OAAO,IAAI,CAACI,SAAS;MAC/B,KAAK,GAAG;QAAE,OAAO,IAAI,CAACH,OAAO;MAC7B,KAAK,GAAG;QAAE,OAAO,IAAI,CAACC,OAAO;MAC7B,KAAK,GAAG;QAAE,OAAO,IAAI,CAACC,YAAY;MAClC;QACI,OAAO,IAAI;IACnB;EACJ,CAAC;EACDvB,UAAU,CAAC2C,SAAS,CAACH,WAAW,GAAG,UAAUiD,MAAM,EAAE/E,KAAK,EAAE;IACxD,QAAQ+E,MAAM;MACV,KAAK,GAAG;QACJ;QACA,IAAI,CAACxE,IAAI,GAAGP,KAAK;QACjB,IAAIA,KAAK,KAAK,KAAK,EAAE;UACjB,IAAI,CAACmB,MAAM,CAAC0E,WAAW,CAAC9G,iBAAiB,CAAC;QAC9C;QACA;MACJ,KAAK,GAAG;QACJ;QACA,IAAI,CAACyB,KAAK,GAAGR,KAAK;QAClB,IAAIA,KAAK,KAAK,KAAK,EAAE;UACjB,IAAI,IAAI,CAACE,gBAAgB,EAAE;YACvB,IAAI,CAACiB,MAAM,CAAC2E,QAAQ,CAAC,CAAC,CAAC;UAC3B;QACJ;QACA;MACJ,KAAK,GAAG;QACJ,IAAI,CAACrF,IAAI,GAAGT,KAAK;QACjB;MACJ,KAAK,GAAG;MACR,KAAK,GAAG;QACJ,IAAI,CAACU,KAAK,GAAGV,KAAK;QAClB;MACJ,KAAK,GAAG;MACR,KAAK,GAAG;QACJ,IAAI,CAACc,SAAS,GAAGd,KAAK;QACtB;MACJ,KAAK,GAAG;QACJ,IAAI,CAACW,OAAO,GAAGX,KAAK;QACpB;MACJ,KAAK,GAAG;QACJ,IAAI,CAACY,OAAO,GAAGZ,KAAK;QACpB;MACJ,KAAK,GAAG;QACJ,IAAI,CAACa,YAAY,GAAGb,KAAK;QACzB;MACJ;QACI;IACR;IACA,IAAI,IAAI,CAACmE,QAAQ,CAAC,CAAC,EAAE;MACjB,IAAI,CAAC9B,gBAAgB,CAAC,CAAC;IAC3B;EACJ,CAAC;EACD/C,UAAU,CAAC2C,SAAS,CAAC8D,UAAU,GAAG,UAAUhB,MAAM,EAAEiB,MAAM,EAAE;IACxD,IAAI,CAAC5H,SAAS,CAAC2G,MAAM,CAAC,IAAI,CAAC3G,SAAS,CAAC4H,MAAM,CAAC,IAAIA,MAAM,KAAK,CAAC,EAAE;MAC1D;IACJ;IACA,IAAIC,QAAQ,GAAGvI,SAAS,CAAC,IAAI,CAACsC,KAAK,CAAC;IACpC,IAAIkG,YAAY,GAAG,KAAK;IACxB,IAAIC,gBAAgB;IACpB,IAAIC,OAAO,GAAGrB,MAAM,KAAK,GAAG;IAC5B,IAAIsB,KAAK,GAAGtB,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG;IAC5C,IAAIuB,YAAY,GAAG,IAAI,CAACvC,WAAW,CAACgB,MAAM,CAAC;IAC3C,IAAI,CAAC,IAAI,CAAC7E,gBAAgB,KAAKmG,KAAK,IAAID,OAAO,CAAC,EAAE;MAC9C,IAAI7E,gBAAgB,GAAG,IAAI,CAACF,qBAAqB,CAACE,gBAAgB,IAAI,CAAC,CAAC;MACxE,IAAIgF,oBAAoB,GAAG,IAAI,CAACtB,uBAAuB,CAACF,MAAM,CAAC;MAC/D,IAAIxE,IAAI,GAAGgB,gBAAgB,CAACiF,CAAC,CAACxG,KAAK,IAAIiG,QAAQ,CAACQ,WAAW,CAAC,CAAC;MAC7D,IAAIjG,KAAK,GAAGe,gBAAgB,CAACmF,CAAC,CAAC1G,KAAK,IAAIiG,QAAQ,CAACU,QAAQ,CAAC,CAAC;MAC3D,IAAIC,GAAG,GAAGrF,gBAAgB,CAACsF,CAAC,CAAC7G,KAAK,IAAIuB,gBAAgB,CAACuF,CAAC,CAAC9G,KAAK,IAAIiG,QAAQ,CAACpI,OAAO,CAAC,CAAC;MACpF,IAAIkJ,IAAI,GAAGxF,gBAAgB,CAACyF,CAAC,CAAChH,KAAK,IAAIuB,gBAAgB,CAAC0F,CAAC,CAACjH,KAAK,IAAIiG,QAAQ,CAACiB,QAAQ,CAAC,CAAC;MACtF,IAAIvG,OAAO,GAAGY,gBAAgB,CAAC4F,CAAC,CAACnH,KAAK,IAAIiG,QAAQ,CAACmB,UAAU,CAAC,CAAC;MAC/D,IAAIxG,OAAO,GAAGW,gBAAgB,CAAC8F,CAAC,CAACrH,KAAK,IAAIiG,QAAQ,CAACqB,UAAU,CAAC,CAAC;MAC/D,IAAIzG,YAAY,GAAGU,gBAAgB,CAACgG,CAAC,CAACvH,KAAK,IAAIiG,QAAQ,CAACuB,eAAe,CAAC,CAAC;MACzE,QAAQzC,MAAM;QACV,KAAK,GAAG;UACJxE,IAAI,IAAIyF,MAAM;UACd;QACJ,KAAK,GAAG;UACJxF,KAAK,IAAIwF,MAAM;UACf;QACJ,KAAK,GAAG;QACR,KAAK,GAAG;UACJY,GAAG,IAAIZ,MAAM;UACb;QACJ;QACA;QACA;QACA;QACA;QACA;UAAS;MACb;MACA,IAAIjB,MAAM,KAAK,GAAG,EAAE;QAChB,IAAKvE,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAG,EAAE,EAAG;UAC3B,IAAI8F,YAAY,EAAE;YACd,IAAI,CAACxE,WAAW,CAACiD,MAAM,EAAE,KAAK,CAAC;YAC/B,IAAI,CAAC0C,sBAAsB,CAAC1C,MAAM,CAAC;YACnC;UACJ;QACJ;QACA,IAAI,CAACuB,YAAY,EAAE;UACf,IAAI9F,KAAK,GAAG,CAAC,EAAE;YACXA,KAAK,GAAGhC,KAAK,CAAC,EAAE,IAAKgC,KAAK,GAAG,EAAE,GAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;UACjD,CAAC,MACI;YACD,IAAIkH,UAAU,GAAGtJ,SAAS,CAACmI,oBAAoB,CAAC,GAC5C/F,KAAK,GACJ,CAACwF,MAAM,GAAG3G,eAAe,IAAI,EAAG;YACrCmB,KAAK,GAAGhC,KAAK,CAACkJ,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;UACpC;UACAlH,KAAK,GAAGhC,KAAK,CAACgC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;QAC/B;QACAA,KAAK,GAAGhC,KAAK,CAACgC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;MAC/B,CAAC,MACI,IAAIuE,MAAM,KAAK,GAAG,EAAE;QACrB,IAAIuB,YAAY,EAAE;UACd,IAAIM,GAAG,IAAI,CAAC,IAAIA,GAAG,GAAG,EAAE,EAAE;YACtB,IAAI,CAAC9E,WAAW,CAACiD,MAAM,EAAE,KAAK,CAAC;YAC/B,IAAI,CAAC0C,sBAAsB,CAAC1C,MAAM,CAAC;YACnC;UACJ;QACJ,CAAC,MACI,IAAI,CAACuB,YAAY,EAAE;UACpB,IAAIlI,SAAS,CAACmI,oBAAoB,CAAC,EAAE;YACjC,IAAIK,GAAG,IAAI,CAAC,IAAIA,GAAG,GAAG,EAAE,EAAE;cACtB,IAAI,CAAC9E,WAAW,CAACiD,MAAM,EAAE,KAAK,CAAC;cAC/B,IAAI,CAAC0C,sBAAsB,CAAC1C,MAAM,CAAC;cACnC;YACJ;UACJ;UACA,IAAIiB,MAAM,GAAG,CAAC,EAAE;YACZ,IAAI2B,QAAQ,GAAGvJ,SAAS,CAACmI,oBAAoB,CAAC,GAAGK,GAAG,GAAG,CAAC,IAAI,EAAE,GAAGgB,IAAI,CAACC,GAAG,CAAC7B,MAAM,GAAG,EAAE,CAAC,CAAC;YACvFY,GAAG,GAAGpI,KAAK,CAACmJ,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC;UAChC,CAAC,MACI;YACD,IAAIA,QAAQ,GAAGvJ,SAAS,CAACmI,oBAAoB,CAAC,GAAGK,GAAG,GAAGZ,MAAM,GAAG,EAAE;YAClEY,GAAG,GAAGpI,KAAK,CAACmJ,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC;UAChC;UACAf,GAAG,GAAGpI,KAAK,CAACoI,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;QAC3B;MACJ;MACA,IAAIkB,aAAa,GAAGnK,UAAU,CAAC4C,IAAI,EAAEC,KAAK,EAAEoG,GAAG,EAAEG,IAAI,EAAEpG,OAAO,EAAEC,OAAO,EAAEC,YAAY,CAAC;MACtF,IAAIkH,iBAAiB,GAAG3B,OAAO,IAAIC,KAAK,GACpC,IAAI,CAAC2B,yBAAyB,CAAC/B,QAAQ,EAAElB,MAAM,EAAEqB,OAAO,GAAG5F,KAAK,GAAGoG,GAAG,CAAC,GACvE,IAAI;MACR,IAAIqB,mBAAmB,GAAGxJ,mBAAmB,CAACqJ,aAAa,EAAEvH,IAAI,EAAEC,KAAK,EAAEoG,GAAG,EAAEG,IAAI,EAAEpG,OAAO,EAAEC,OAAO,EAAEC,YAAY,CAAC;MACpH,IAAI,IAAI,CAACsD,QAAQ,CAAC,CAAC,IAAI1F,mBAAmB,CAACqJ,aAAa,EAAEvH,IAAI,EAAEC,KAAK,EAAEoG,GAAG,EAAEG,IAAI,EAAEpG,OAAO,EAAEC,OAAO,EAAEC,YAAY,CAAC,EAAE;QAC/GoF,QAAQ,GAAGvI,SAAS,CAACoK,aAAa,CAAC;QACnC,IAAI,CAACI,uBAAuB,CAAC,CAAC;MAClC,CAAC,MACI,IAAI9B,OAAO,IAAI2B,iBAAiB,EAAE;QACnC,IAAIA,iBAAiB,CAACpB,QAAQ,CAAC,CAAC,KAAKnG,KAAK,EAAE;UACxC,IAAI,IAAI,CAACuD,WAAW,CAAC,GAAG,CAAC,EAAE;YACvB,IAAIkE,mBAAmB,EAAE;cACrBhC,QAAQ,GAAGvI,SAAS,CAACoK,aAAa,CAAC;cACnC,IAAI,CAACL,sBAAsB,CAAC1C,MAAM,CAAC;YACvC,CAAC,MACI;cACDoB,gBAAgB,GAAG,IAAI;cACvB,IAAI,CAACgC,kBAAkB,CAACpD,MAAM,EAAE;gBAC5B/E,KAAK,EAAEQ,KAAK;gBACZC,IAAI,EAAE/C,SAAS,CAACqK,iBAAiB,CAAC;gBAClCvG,eAAe,EAAEwE,MAAM;gBACvB1E,SAAS,EAAE5D,SAAS,CAAC,IAAI,CAACsC,KAAK;cACnC,CAAC,CAAC;cACF,IAAI,CAAC8B,WAAW,CAACiD,MAAM,EAAE,KAAK,CAAC;YACnC;UACJ,CAAC,MACI,IAAIkD,mBAAmB,EAAE;YAC1B,IAAI,CAACR,sBAAsB,CAAC1C,MAAM,CAAC;YACnCkB,QAAQ,GAAGvI,SAAS,CAACoK,aAAa,CAAC;YACnC,IAAI,IAAI,CAAC/D,WAAW,CAAC,GAAG,CAAC,IAAI,IAAI,CAACA,WAAW,CAAC,GAAG,CAAC,EAAE;cAChD;cACA,IAAI,CAACjC,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC;cAC3B,IAAI,CAAC2F,sBAAsB,CAAC,GAAG,CAAC;YACpC;UACJ,CAAC,MACI;YACD,IAAI,CAACA,sBAAsB,CAAC1C,MAAM,CAAC;YACnCkB,QAAQ,GAAGvI,SAAS,CAACqK,iBAAiB,CAAC;UAC3C;QACJ,CAAC,MACI;UACD5B,gBAAgB,GAAG,IAAI;UACvB,IAAI,CAACgC,kBAAkB,CAACpD,MAAM,EAAE;YAC5B/E,KAAK,EAAEQ,KAAK;YACZC,IAAI,EAAE/C,SAAS,CAACqK,iBAAiB,CAAC;YAClCvG,eAAe,EAAEwE,MAAM;YACvB1E,SAAS,EAAE5D,SAAS,CAAC,IAAI,CAACsC,KAAK;UACnC,CAAC,CAAC;UACF,IAAI,CAAC8B,WAAW,CAACiD,MAAM,EAAE,KAAK,CAAC;QACnC;MACJ,CAAC,MACI,IAAIsB,KAAK,IAAI0B,iBAAiB,EAAE;QACjC,IAAIA,iBAAiB,CAAClK,OAAO,CAAC,CAAC,KAAK+I,GAAG,EAAE;UACrC,IAAI,IAAI,CAAC7C,WAAW,CAAC,GAAG,CAAC,EAAE;YACvB,IAAIkE,mBAAmB,EAAE;cACrBhC,QAAQ,GAAGvI,SAAS,CAACoK,aAAa,CAAC;cACnC,IAAI,CAACL,sBAAsB,CAAC1C,MAAM,CAAC;YACvC,CAAC,MACI;cACDoB,gBAAgB,GAAG,IAAI;cACvB,IAAI,CAACgC,kBAAkB,CAACpD,MAAM,EAAE;gBAC5B/E,KAAK,EAAE4G,GAAG;gBACVnG,IAAI,EAAE/C,SAAS,CAACqK,iBAAiB,CAAC;gBAClCvG,eAAe,EAAEwE,MAAM;gBACvB1E,SAAS,EAAE5D,SAAS,CAAC,IAAI,CAACsC,KAAK;cACnC,CAAC,CAAC;cACF,IAAI,CAAC8B,WAAW,CAACiD,MAAM,EAAE,KAAK,CAAC;YACnC;UACJ,CAAC,MACI,IAAIkD,mBAAmB,EAAE;YAC1BhC,QAAQ,GAAGvI,SAAS,CAACoK,aAAa,CAAC;YACnC,IAAI,CAACL,sBAAsB,CAAC1C,MAAM,CAAC;YACnC,IAAI,IAAI,CAAChB,WAAW,CAAC,GAAG,CAAC,IAAI,IAAI,CAACA,WAAW,CAAC,GAAG,CAAC,EAAE;cAChD;cACA,IAAI,CAACjC,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC;cAC3B,IAAI,CAAC2F,sBAAsB,CAAC,GAAG,CAAC;YACpC;UACJ,CAAC,MACI;YACD,IAAI,CAACA,sBAAsB,CAAC1C,MAAM,CAAC;YACnCkB,QAAQ,GAAGvI,SAAS,CAACqK,iBAAiB,CAAC;UAC3C;QACJ,CAAC,MACI;UACD5B,gBAAgB,GAAG,IAAI;UACvB,IAAI,CAACgC,kBAAkB,CAACpD,MAAM,EAAE;YAC5B/E,KAAK,EAAE4G,GAAG;YACVnG,IAAI,EAAE/C,SAAS,CAAC,IAAI,CAACsC,KAAK,CAAC;YAC3BwB,eAAe,EAAEwE,MAAM;YACvB1E,SAAS,EAAE5D,SAAS,CAAC,IAAI,CAACsC,KAAK;UACnC,CAAC,CAAC;UACF,IAAI,CAAC8B,WAAW,CAACiD,MAAM,EAAE,KAAK,CAAC;QACnC;MACJ;IACJ,CAAC,MACI;MACD,IAAIrE,KAAK,GAAGuF,QAAQ,CAACiB,QAAQ,CAAC,CAAC;MAC/B,QAAQnC,MAAM;QACV,KAAK,GAAG;UACJkB,QAAQ,CAACJ,WAAW,CAACI,QAAQ,CAACQ,WAAW,CAAC,CAAC,GAAGT,MAAM,CAAC;UACrD;QACJ,KAAK,GAAG;UACJC,QAAQ,GAAGxI,SAAS,CAAC,IAAI,CAACuC,KAAK,EAAEgG,MAAM,CAAC;UACxC;QACJ,KAAK,GAAG;QACR,KAAK,GAAG;UACJC,QAAQ,CAACmC,OAAO,CAACnC,QAAQ,CAACpI,OAAO,CAAC,CAAC,GAAGmI,MAAM,CAAC;UAC7C;QACJ,KAAK,GAAG;QACR,KAAK,GAAG;UACJC,QAAQ,CAACoC,QAAQ,CAACpC,QAAQ,CAACiB,QAAQ,CAAC,CAAC,GAAGlB,MAAM,CAAC;UAC/CE,YAAY,GAAG,IAAI;UACnB;QACJ,KAAK,GAAG;UACJD,QAAQ,CAACqC,UAAU,CAACrC,QAAQ,CAACmB,UAAU,CAAC,CAAC,GAAGpB,MAAM,CAAC;UACnDE,YAAY,GAAG,IAAI;UACnB;QACJ,KAAK,GAAG;UACJD,QAAQ,CAACsC,UAAU,CAACtC,QAAQ,CAACqB,UAAU,CAAC,CAAC,GAAGtB,MAAM,CAAC;UACnDE,YAAY,GAAG,IAAI;UACnB;QACJ,KAAK,GAAG;UACJD,QAAQ,CAACuC,eAAe,CAACvC,QAAQ,CAACuB,eAAe,CAAC,CAAC,GAAGxB,MAAM,CAAC;UAC7D;QACJ,KAAK,GAAG;UACJ,IAAI,IAAI,CAAC5F,eAAe,EAAE;YACtB6F,QAAQ,CAACoC,QAAQ,CAAC3H,KAAK,IAAI,EAAE,GAAGA,KAAK,GAAG,EAAE,GAAGA,KAAK,GAAG,EAAE,CAAC;UAC5D,CAAC,MACI;YACDuF,QAAQ,CAACoC,QAAQ,CAACpC,QAAQ,CAACiB,QAAQ,CAAC,CAAC,GAAI,EAAE,GAAGlB,MAAO,CAAC;UAC1D;UACAE,YAAY,GAAG,IAAI;UACnB;QACJ;UAAS;MACb;IACJ;IACA,IAAI,IAAI,CAACuC,sBAAsB,CAAC,CAAC,EAAE;MAC/BxC,QAAQ,GAAG,IAAI,CAACyC,gBAAgB,CAACzC,QAAQ,CAAC;IAC9C;IACA,IAAIC,YAAY,IAAI,CAAC,IAAI,CAACtG,SAAS,IAAIqG,QAAQ,CAACpI,OAAO,CAAC,CAAC,KAAK,IAAI,CAACsD,MAAM,CAACtD,OAAO,CAAC,CAAC,EAAE;MACjF;MACA;MACA;MACA;IAAA;IAEJ,IAAI,CAACsI,gBAAgB,EAAE;MACnB,IAAI,CAACrE,WAAW,CAACiD,MAAM,EAAE,IAAI,CAAC;MAC9B,IAAI,CAAC5D,MAAM,GAAG8E,QAAQ;MACtB,IAAI,IAAI,CAAC9B,QAAQ,CAAC,CAAC,EAAE;QACjB,IAAI,CAAC9B,gBAAgB,CAAC,CAAC;MAC3B;IACJ;EACJ,CAAC;EACD;AACJ;AACA;EACI/C,UAAU,CAAC2C,SAAS,CAAC0G,SAAS,GAAG,UAAUpJ,EAAE,EAAE;IAC3C,IAAIwF,MAAM,GAAGxF,EAAE,CAACwF,MAAM;MAAE6D,WAAW,GAAGrJ,EAAE,CAACqJ,WAAW;MAAEC,iBAAiB,GAAGtJ,EAAE,CAACsJ,iBAAiB;MAAEC,iBAAiB,GAAGvJ,EAAE,CAACuJ,iBAAiB;MAAEC,aAAa,GAAGxJ,EAAE,CAACyJ,YAAY;MAAEC,UAAU,GAAG1J,EAAE,CAAC0J,UAAU;MAAEC,cAAc,GAAG3J,EAAE,CAAC2J,cAAc;IACzO,IAAIC,aAAa,GAAG,CAACL,iBAAiB;IACtC,IAAIM,SAAS,GAAG,IAAI,CAACzH,gBAAgB,CAAC,IAAI,CAAC3B,KAAK,EAAE,IAAI,CAACN,MAAM,CAAC;IAC9D,IAAI2J,iBAAiB,GAAGD,SAAS,CAACpE,OAAO,CACpCsE,MAAM,CAAC,UAAUC,CAAC,EAAE;MAAE,OAAOA,CAAC,CAACzF,IAAI,KAAK,SAAS;IAAE,CAAC,CAAC,CACrD0F,GAAG,CAAC,UAAUD,CAAC,EAAEE,KAAK,EAAE;MACzB,OAAO;QACHC,aAAa,EAAED,KAAK;QACpB3F,IAAI,EAAEyF,CAAC,CAACzF,IAAI;QACZE,OAAO,EAAEuF,CAAC,CAACvF,OAAO;QAClB2F,OAAO,EAAE;MACb,CAAC;IACL,CAAC,CAAC;IACF,IAAIC,aAAa,GAAGR,SAAS,CAACpE,OAAO,CAChCwE,GAAG,CAAC,UAAUD,CAAC,EAAE;MAClB,OAAO;QACHzF,IAAI,EAAEyF,CAAC,CAACzF,IAAI;QACZE,OAAO,EAAEuF,CAAC,CAACvF,OAAO;QAClBS,IAAI,EAAE;MACV,CAAC;IACL,CAAC,CAAC;IACF,KAAK,IAAI7C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyH,iBAAiB,CAACxH,MAAM,EAAED,CAAC,EAAE,EAAE;MAC/C,IAAIiI,QAAQ,GAAGR,iBAAiB,CAACzH,CAAC,CAAC;MACnC,KAAK,IAAIkI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,QAAQ,CAAC7F,OAAO,CAACnC,MAAM,EAAEiI,CAAC,EAAE,EAAE;QAC9C,IAAIT,iBAAiB,CAACzH,CAAC,GAAGkI,CAAC,CAAC,EAAE;UAC1BT,iBAAiB,CAACzH,CAAC,GAAGkI,CAAC,CAAC,CAACH,OAAO,GAAGE,QAAQ,CAAC7F,OAAO,CAAC8F,CAAC,CAAC;QAC1D;MACJ;MACAlI,CAAC,IAAIiI,QAAQ,CAAC7F,OAAO,CAACnC,MAAM,GAAG,CAAC;IACpC;IACA,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgI,aAAa,CAAC/H,MAAM,EAAED,CAAC,EAAE,EAAE;MAC3C,IAAIiI,QAAQ,GAAGD,aAAa,CAAChI,CAAC,CAAC;MAC/B,KAAK,IAAIkI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,QAAQ,CAAC7F,OAAO,CAACnC,MAAM,EAAEiI,CAAC,EAAE,EAAE;QAC9C,IAAIF,aAAa,CAAChI,CAAC,GAAGkI,CAAC,CAAC,EAAE;UACtBF,aAAa,CAAChI,CAAC,GAAGkI,CAAC,CAAC,CAACrF,IAAI,GAAGoF,QAAQ,CAAC7F,OAAO,CAAC8F,CAAC,CAAC;QACnD;MACJ;MACAlI,CAAC,IAAIiI,QAAQ,CAAC7F,OAAO,CAACnC,MAAM,GAAG,CAAC;IACpC;IACA,IAAIkI,eAAe,GAAGZ,aAAa,IAAIpE,MAAM,KAAK,GAAG,IAAIqE,SAAS,CAACpE,OAAO,CACrEsE,MAAM,CAAC,UAAUC,CAAC,EAAE;MAAE,OAAOA,CAAC,CAACzF,IAAI,KAAK,OAAO;IAAE,CAAC,CAAC,CACnDkG,IAAI,CAAC,UAAUT,CAAC,EAAE;MAAE,OAAOA,CAAC,CAACvF,OAAO,CAACnC,MAAM,GAAG1C,+BAA+B;IAAE,CAAC,CAAC;IACtF,IAAI8K,WAAW,GAAG;MACdjK,KAAK,EAAE,IAAI;MACXkK,YAAY,EAAE,KAAK;MACnBC,SAAS,EAAEJ,eAAe;MAC1BK,kBAAkB,EAAE;IACxB,CAAC;IACD,IAAI,CAACxB,WAAW,EAAE;MACd,IAAIO,aAAa,EAAE;QACf,KAAK,IAAIvH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyH,iBAAiB,CAACxH,MAAM,EAAED,CAAC,EAAE,EAAE;UAC/C,IAAI+H,OAAO,GAAGN,iBAAiB,CAACzH,CAAC,CAAC,CAAC+H,OAAO;UAC1C,IAAIU,yBAAyB,GAAGtB,aAAa,CAACuB,UAAU,CAACX,OAAO,CAAC;UACjE,IAAIY,uBAAuB,GAAGxB,aAAa,CAACyB,QAAQ,CAACb,OAAO,CAAC;UAC7D,IAAIc,8BAA8B,GAAG1B,aAAa,CAAC2B,OAAO,CAACf,OAAO,GAAGA,OAAO,CAAC,IAAI,CAAC;UAClF,IAAIU,yBAAyB,IAAIE,uBAAuB,IAAIE,8BAA8B,EAAE;YACxF,IAAI,CAACE,gBAAgB,CAAC,CAAC;YACvB,IAAI,CAAC7I,WAAW,CAACiD,MAAM,EAAE,KAAK,CAAC;YAC/B,IAAI,CAAC0C,sBAAsB,CAAC1C,MAAM,CAAC;YACnC,OAAO5G,MAAM,CAAC8L,WAAW,EAAE;cAAEjK,KAAK,EAAE,IAAI;cAAEkK,YAAY,EAAE;YAAM,CAAC,CAAC;UACpE;QACJ;MACJ,CAAC,MACI;QACD,IAAI,CAACS,gBAAgB,CAAC,CAAC;QACvB,IAAI,CAAC7I,WAAW,CAACiD,MAAM,EAAE,KAAK,CAAC;QAC/B,IAAI,CAAC0C,sBAAsB,CAAC1C,MAAM,CAAC;QACnC,OAAO5G,MAAM,CAAC8L,WAAW,EAAE;UAAEjK,KAAK,EAAE,IAAI;UAAEkK,YAAY,EAAE;QAAM,CAAC,CAAC;MACpE;IACJ;IACA,IAAIU,QAAQ,GAAG,IAAI,CAAChI,IAAI,CAAC0B,UAAU,CAAC,IAAI,CAACtE,KAAK,EAAE,IAAI,CAACN,MAAM,EAAE,IAAI,CAAC8C,QAAQ,CAAC;IAC3E,IAAIqI,UAAU,GAAGzB,SAAS,CAAClI,OAAO;IAClC,IAAI4J,QAAQ,GAAG,KAAK;IACpB,IAAIC,MAAM,GAAG,EAAE;IACf,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAI5F,YAAY,GAAG,EAAE;IACrB,IAAI6F,UAAU,GAAG,EAAE;IACnB,IAAIC,UAAU,GAAG,EAAE;IACnB,IAAIC,MAAM,GAAG,EAAE;IACf,IAAIC,mBAAmB,GAAG,EAAE;IAC5B,KAAK,IAAIxJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgI,aAAa,CAAC/H,MAAM,EAAED,CAAC,EAAE,EAAE;MAC3CwJ,mBAAmB,IAAIxB,aAAa,CAAChI,CAAC,CAAC,CAAC6C,IAAI;IAChD;IACA,IAAI4G,cAAc,GAAI,IAAI,CAAC3L,MAAM,KAAKmL,UAAU,IAC3C,IAAI,CAACnL,MAAM,KAAK0L,mBAAoB,IACpC,IAAI,CAAC1L,MAAM,KAAKwJ,cAAe,IAC/B,IAAI,CAACxJ,MAAM,CAACmC,MAAM,KAAKqH,cAAc,CAACrH,MAAO;IAClD,IAAIyJ,kBAAkB,GAAG,CAACD,cAAc,GAAGD,mBAAmB,GAAGlC,cAAc,EAAEwB,OAAO,CAAC3F,MAAM,CAAC;IAChG,IAAIwG,gBAAgB,GAAG,CAACF,cAAc,GAAGD,mBAAmB,GAAGlC,cAAc,EAAEsC,WAAW,CAACzG,MAAM,CAAC;IAClG,IAAI0G,aAAa,GAAGF,gBAAgB,GAAGD,kBAAkB,GAAG,CAAC;IAC7D,IAAII,sBAAsB,GAAGxC,cAAc,CAACrH,MAAM,GAAGkH,aAAa,CAAClH,MAAM;IACzE,IAAIsH,aAAa,IAAK,CAACA,aAAa,IAAI,CAAC,IAAI,CAACjJ,gBAAiB,EAAE;MAC7D,IAAIyL,gBAAgB,GAAG,CAAC;MACxB,KAAK,IAAI/J,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgJ,QAAQ,CAAC/I,MAAM,EAAED,CAAC,EAAE,EAAE;QACtC,IAAIiJ,UAAU,CAACjJ,CAAC,CAAC,KAAKmD,MAAM,EAAE;UAC1B,IAAI6G,QAAQ,GAAG,IAAI,CAAC7H,WAAW,CAACgB,MAAM,CAAC;UACvC,IAAIA,MAAM,KAAK,GAAG,EAAE;YAChB,IAAI,CAAC,IAAI,CAACqF,kBAAkB,CAAC,CAAC,IAAI,IAAI,CAACrG,WAAW,CAAC,GAAG,CAAC,EAAE;cACrDiH,OAAO,IAAIJ,QAAQ,CAAChJ,CAAC,CAAC;YAC1B,CAAC,MACI;cACD,IAAI2E,oBAAoB,GAAG,IAAI,CAACtB,uBAAuB,CAACF,MAAM,CAAC;cAC/D,IAAI3G,SAAS,CAACmI,oBAAoB,CAAC,EAAE;gBACjCyE,OAAO,IAAI,CAACzE,oBAAoB,IAAI,EAAE,EAAElB,QAAQ,CAAC,CAAC,CAACsG,gBAAgB,CAAC,IAAI,EAAE;gBAC1EA,gBAAgB,EAAE;cACtB,CAAC,MACI;gBACDX,OAAO,IAAIY,QAAQ,GAAGhB,QAAQ,CAAChJ,CAAC,CAAC,GAAG,GAAG;cAC3C;YACJ;UACJ,CAAC,MACI;YACDoJ,OAAO,IAAIY,QAAQ,GAAGhB,QAAQ,CAAChJ,CAAC,CAAC,GAAG,GAAG;UAC3C;UACA,IAAI8J,sBAAsB,GAAG,CAAC,EAAE;YAC5B,IAAItG,YAAY,CAACvD,MAAM,GAAG6J,sBAAsB,GAAGD,aAAa,EAAE;cAC9DrG,YAAY,IAAI2D,aAAa,CAACnH,CAAC,CAAC,IAAI,EAAE;YAC1C;UACJ,CAAC,MACI;YACDwD,YAAY,IAAI2D,aAAa,CAACnH,CAAC,CAAC,IAAI,EAAE;UAC1C;UACAkJ,QAAQ,GAAG,IAAI;QACnB,CAAC,MACI,IAAI,CAACA,QAAQ,EAAE;UAChBC,MAAM,IAAIH,QAAQ,CAAChJ,CAAC,CAAC;UACrBqJ,UAAU,IAAIL,QAAQ,CAAChJ,CAAC,CAAC;QAC7B,CAAC,MACI;UACDuJ,MAAM,IAAIP,QAAQ,CAAChJ,CAAC,CAAC;UACrBsJ,UAAU,IAAIN,QAAQ,CAAChJ,CAAC,CAAC;QAC7B;MACJ;MACA,IAAIyJ,cAAc,EAAE;QAChB,IAAID,mBAAmB,CAACvJ,MAAM,GAAGkH,aAAa,CAAClH,MAAM,EAAE;UACnDuD,YAAY,IAAIwD,WAAW;QAC/B,CAAC,MACI,IAAI,CAACK,UAAU,IAAIC,cAAc,CAACrH,MAAM,GAAGkH,aAAa,CAAClH,MAAM,EAAE;UAClE;QAAA;QAEJ,IAAIuD,YAAY,CAACvD,MAAM,GAAG4J,aAAa,EAAE;UACrC,OAAOtN,MAAM,CAAC8L,WAAW,EAAE;YAAEjK,KAAK,EAAE,IAAI;YAAEkK,YAAY,EAAE;UAAM,CAAC,CAAC;QACpE;MACJ;MACA,IAAI,CAACmB,cAAc,IAAKA,cAAc,IAAI,CAAC,IAAI,CAACnL,gBAAiB,EAAE;QAC/D8K,OAAO,GAAG,EAAE;QACZ5F,YAAY,GAAG,EAAE;QACjB2F,MAAM,GAAG,EAAE;QACXI,MAAM,GAAG,EAAE;QACXL,QAAQ,GAAG,KAAK;QAChB,IAAIe,kBAAkB,GAAG,CAAC;QAC1B,KAAK,IAAIjK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsH,cAAc,CAACrH,MAAM,EAAED,CAAC,EAAE,EAAE;UAC5C,IAAIsH,cAAc,CAACtH,CAAC,CAAC,KAAKmD,MAAM,EAAE;YAC9B,IAAI6G,QAAQ,GAAG,IAAI,CAAC7H,WAAW,CAACgB,MAAM,CAAC;YACvC,IAAIA,MAAM,KAAK,GAAG,EAAE;cAChB,IAAI,CAAC,IAAI,CAACqF,kBAAkB,CAAC,CAAC,IAAI,IAAI,CAACrG,WAAW,CAAC,GAAG,CAAC,EAAE;gBACrDiH,OAAO,IAAIJ,QAAQ,CAAChJ,CAAC,CAAC;cAC1B,CAAC,MACI;gBACD,IAAI2E,oBAAoB,GAAG,IAAI,CAACtB,uBAAuB,CAACF,MAAM,CAAC;gBAC/D,IAAI3G,SAAS,CAACmI,oBAAoB,CAAC,EAAE;kBACjCyE,OAAO,IAAI,CAACzE,oBAAoB,IAAI,EAAE,EAAElB,QAAQ,CAAC,CAAC,CAACwG,kBAAkB,CAAC,IAAI,EAAE;kBAC5EA,kBAAkB,EAAE;gBACxB,CAAC,MACI;kBACDb,OAAO,IAAIY,QAAQ,GAAGhB,QAAQ,CAAChJ,CAAC,CAAC,GAAG,GAAG;gBAC3C;cACJ;YACJ,CAAC,MACI;cACDoJ,OAAO,IAAIY,QAAQ,GAAGhB,QAAQ,CAAChJ,CAAC,CAAC,IAAI,EAAE,GAAG,GAAG;YACjD;YACA,IAAI8J,sBAAsB,GAAG,CAAC,EAAE;cAC5B,IAAItG,YAAY,CAACvD,MAAM,GAAG6J,sBAAsB,GAAGD,aAAa,EAAE;gBAC9DrG,YAAY,IAAI2D,aAAa,CAACnH,CAAC,CAAC,IAAI,EAAE;cAC1C;YACJ,CAAC,MACI;cACDwD,YAAY,IAAI2D,aAAa,CAACnH,CAAC,CAAC,IAAI,EAAE;YAC1C;YACAkJ,QAAQ,GAAG,IAAI;UACnB,CAAC,MACI,IAAI,CAACA,QAAQ,EAAE;YAChBC,MAAM,IAAIhC,aAAa,CAACnH,CAAC,CAAC,IAAI,EAAE;UACpC,CAAC,MACI;YACDuJ,MAAM,IAAIpC,aAAa,CAACnH,CAAC,GAAG8J,sBAAsB,CAAC,IAAI,EAAE;UAC7D;QACJ;QACA,IAAIxC,cAAc,CAACrH,MAAM,GAAGkH,aAAa,CAAClH,MAAM,EAAE;UAC9CuD,YAAY,IAAIwD,WAAW;QAC/B;MACJ;IACJ;IACA,IAAI,CAACO,aAAa,EAAE;MAChB,IAAI,IAAI,CAACjJ,gBAAgB,EAAE;QACvB8K,OAAO,GAAG,EAAE;QACZ5F,YAAY,GAAG,EAAE;QACjB2F,MAAM,GAAG,EAAE;QACXI,MAAM,GAAG,EAAE;QACXL,QAAQ,GAAG,KAAK;QAChB,KAAK,IAAIlJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgJ,QAAQ,CAAC/I,MAAM,EAAED,CAAC,EAAE,EAAE;UACtC,IAAIiJ,UAAU,CAACjJ,CAAC,CAAC,KAAKmD,MAAM,EAAE;YAC1B,IAAI6G,QAAQ,GAAG,IAAI,CAAC7H,WAAW,CAACgB,MAAM,CAAC;YACvCiG,OAAO,IAAIY,QAAQ,GAAGhB,QAAQ,CAAChJ,CAAC,CAAC,GAAG,GAAG;YACvCkJ,QAAQ,GAAG,IAAI;UACnB,CAAC,MACI,IAAI,CAACA,QAAQ,EAAE;YAChBC,MAAM,IAAIH,QAAQ,CAAChJ,CAAC,CAAC;UACzB,CAAC,MACI;YACDuJ,MAAM,IAAIP,QAAQ,CAAChJ,CAAC,CAAC;UACzB;QACJ;MACJ,CAAC,MACI;QACDoJ,OAAO,GAAGnC,iBAAiB,GAAGzD,YAAY,GAAG4F,OAAO;MACxD;IACJ;IACA,IAAIc,UAAU,GAAG,IAAI;IACrB,IAAIC,WAAW,GAAG,IAAI,CAACC,UAAU,CAACpD,WAAW,CAAC;IAC9C,IAAIqD,SAAS,GAAG,IAAI,CAACC,cAAc,CAACtD,WAAW,EAAE7D,MAAM,CAAC;IACxD,IAAIoH,iBAAiB,GAAGvD,WAAW,KAAK,GAAG;IAC3C,IAAI7H,WAAW,GAAG,IAAI,CAACA,WAAW,IAAI,CAAC,CAAC;IACxC,IAAIoL,iBAAiB,EAAE;MACnB,IAAI/G,YAAY,KAAK,GAAG,EAAE;QACtBA,YAAY,GAAG4F,OAAO;MAC1B;MACA,IAAIoB,WAAW,GAAG7N,UAAU,CAACsK,iBAAiB,GAC1CD,WAAW,GACX,CAACO,aAAa,GAAG/D,YAAY,GAAG4F,OAAO,IAAIpC,WAAW,CAAC;MAC3D,IAAIwD,WAAW,KAAK,CAAC,IAAI,CAAC,IAAI,CAACC,WAAW,CAACjD,SAAS,CAACpE,OAAO,EAAED,MAAM,CAAC,IAAIA,MAAM,KAAK,GAAG,EAAE;QACrF,IAAI,CAACuH,oBAAoB,CAACvH,MAAM,CAAC;MACrC;IACJ,CAAC,MACI;MACD,IAAI,CAAC4F,gBAAgB,CAAC,CAAC;IAC3B;IACA,IAAI4B,WAAW,GAAG,IAAI,CAACA,WAAW,CAACnD,SAAS,CAACpE,OAAO,EAAED,MAAM,CAAC;IAC7D,IAAIyH,YAAY,GAAGD,WAAW,GAAGA,WAAW,CAACvI,OAAO,GAAG,IAAI;IAC3D,IAAIyI,aAAa,GAAG,IAAI,CAACA,aAAa,CAACD,YAAY,CAAC,IAAIA,YAAY,CAAC3K,MAAM;IAC3E,IAAIsH,aAAa,EAAE;MACf,IAAIF,UAAU,IAAI,CAAC7D,YAAY,EAAE;QAC7B,IAAI,CAACtD,WAAW,CAACiD,MAAM,EAAE,KAAK,CAAC;QAC/B,OAAO5G,MAAM,CAAC8L,WAAW,EAAE;UAAEjK,KAAK,EAAE,IAAI;UAAEkK,YAAY,EAAE;QAAM,CAAC,CAAC;MACpE;IACJ;IACA,IAAIwC,gBAAgB,GAAG1B,OAAO,CAACnJ,MAAM,GAAG,CAAC;IACzC,IAAI8K,QAAQ,GAAG,IAAI;IACnB,IAAIC,MAAM,GAAGzD,aAAa,GAAG/D,YAAY,GAAG4F,OAAO;IACnD,KAAK,IAAIpJ,CAAC,GAAGgG,IAAI,CAACiF,GAAG,CAAC,CAAC,EAAEH,gBAAgB,CAAC,EAAE9K,CAAC,IAAIoJ,OAAO,CAACnJ,MAAM,EAAED,CAAC,EAAE,EAAE;MAClE,IAAI,CAAC+K,QAAQ,EAAE;QACX;MACJ;MACAC,MAAM,GAAG/D,iBAAiB,GACtBD,WAAW,GACXO,aAAa,GACT/D,YAAY,GACX4F,OAAO,CAAC8B,SAAS,CAAClL,CAAC,CAAC,GAAGgH,WAAY;MAC5C,IAAI,IAAI,CAACtI,eAAe,IAAIyE,MAAM,KAAK,GAAG,IAAIiG,OAAO,CAAC8B,SAAS,CAAClL,CAAC,CAAC,KAAK,IAAI,EAAE;QACzEgL,MAAM,GAAGA,MAAM,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;MACtC;MACA,IAAI5D,aAAa,IAAI,CAAC,IAAI,CAACjJ,gBAAgB,EAAE;QACzCyM,QAAQ,GAAG,KAAK;QAChBC,MAAM,GAAG1O,SAAS,CAAC0O,MAAM,CAAC;QAC1B;QACAA,MAAM,GAAG3O,OAAO,CAACwO,aAAa,GAAGG,MAAM,CAAC/K,MAAM,CAAC,GAAG+K,MAAM;MAC5D;MACA,IAAII,YAAY,GAAGC,QAAQ,CAACL,MAAM,EAAE,EAAE,CAAC;MACvC,IAAIM,mBAAmB,GAAGnC,MAAM,GAAG6B,MAAM,GAAGzB,MAAM;MAClDW,UAAU,GAAG,IAAI,CAAClJ,IAAI,CAACuK,SAAS,CAACD,mBAAmB,EAAE,IAAI,CAACxN,MAAM,EAAE,IAAI,CAAC8C,QAAQ,CAAC;MACjF;MACA;MACA;MACA;MACA,IAAIsJ,UAAU,IAAI,IAAI,CAAC9L,KAAK,IACxBoJ,SAAS,CAACpE,OAAO,CAACoI,KAAK,CAAC,UAAU7D,CAAC,EAAE;QAAE,OAAOA,CAAC,CAACzF,IAAI,KAAK,MAAM,IAAIyF,CAAC,CAACzF,IAAI,KAAK,OAAO,IAAIyF,CAAC,CAACzF,IAAI,IAAI,KAAK;MAAE,CAAC,CAAC,EAAE;QAC9GgI,UAAU,CAACjG,WAAW,CAAC,IAAI,CAAC7F,KAAK,CAACyG,WAAW,CAAC,CAAC,CAAC;QAChDqF,UAAU,CAAChG,QAAQ,CAAC,IAAI,CAAC9F,KAAK,CAAC2G,QAAQ,CAAC,CAAC,CAAC;QAC1CmF,UAAU,CAAC1D,OAAO,CAAC,IAAI,CAACpI,KAAK,CAACnC,OAAO,CAAC,CAAC,CAAC;MAC5C;MACA,IAAIwP,4BAA4B,GAAG,KAAK;MACxC,IAAIlE,aAAa,IAAI,CAACxK,WAAW,CAACmN,UAAU,CAAC,EAAE;QAC3C;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI,IAAI,CAAC5L,gBAAgB,EAAE;UACvB4L,UAAU,GAAG,IAAI,CAAClJ,IAAI,CAACuK,SAAS,CAAClC,UAAU,GAAG2B,MAAM,GAAG1B,UAAU,EAAE,IAAI,CAACxL,MAAM,EAAE,IAAI,CAAC8C,QAAQ,CAAC;UAC9F6K,4BAA4B,GAAG,IAAI;QACvC;MACJ;MACA,IAAIC,qBAAqB,GAAG,CAACC,KAAK,CAACN,QAAQ,CAACrE,WAAW,EAAE,EAAE,CAAC,CAAC,IAAKO,aAAa,IAAIF,UAAU,IAAIL,WAAW,KAAK,EAAG;MACpH,IAAI,CAACkD,UAAU,IAAI,CAACyB,KAAK,CAACP,YAAY,CAAC,IAAIM,qBAAqB,IAAI,IAAI,CAACpN,gBAAgB,EAAE;QACvF,IAAI6E,MAAM,KAAK3F,YAAY,IAAI,CAAC2M,WAAW,EAAE;UACzC;UACA,IAAIyB,WAAW,GAAGR,YAAY,GAAG3N,eAAe;UAChD,IAAImO,WAAW,GAAG,CAAC,CAAC,IAAIA,WAAW,GAAG,EAAE,EAAE;YACtC1B,UAAU,GAAGpO,SAAS,CAAC,IAAI,CAACsC,KAAK,CAAC;YAClC8L,UAAU,CAAChG,QAAQ,CAAC0H,WAAW,CAAC;YAChC,IAAI1B,UAAU,CAACnF,QAAQ,CAAC,CAAC,KAAK6G,WAAW,EAAE;cACvC1B,UAAU,GAAGhO,cAAc,CAACL,SAAS,CAACqO,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;YAC1D;UACJ;QACJ;QACA,IAAI/G,MAAM,KAAK,GAAG,EAAE;UAChB+G,UAAU,GAAGnO,UAAU,CAACsP,QAAQ,CAACL,MAAM,EAAE,EAAE,CAAC,EAAE,IAAI,CAACpM,KAAK,GAAG,IAAI,CAACR,KAAK,CAAC2G,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAClG,IAAI,GAAG,IAAI,CAACT,KAAK,CAACnC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC6C,KAAK,GAAG,IAAI,CAACV,KAAK,CAACkH,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAACvG,OAAO,GAAG,IAAI,CAACX,KAAK,CAACoH,UAAU,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAACxG,OAAO,GAAG,IAAI,CAACZ,KAAK,CAACsH,UAAU,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAACzG,YAAY,GAAG,IAAI,CAACb,KAAK,CAACwH,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC;UACjT,IAAI,CAAE2B,aAAa,IAAIxK,WAAW,CAACmN,UAAU,CAAC,IACzC,CAAC3C,aAAa,IAAI2C,UAAW,KAAK,IAAI,CAACrL,IAAI,IAAIqL,UAAU,CAACjO,OAAO,CAAC,CAAC,KAAK,IAAI,CAACmC,KAAK,CAACnC,OAAO,CAAC,CAAC,EAAE;YAC/FiO,UAAU,GAAGhO,cAAc,CAACL,SAAS,CAACqO,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;UAC1D;QACJ;MACJ;MACA,IAAK3C,aAAa,IAAIxK,WAAW,CAACmN,UAAU,CAAC,IAAM,CAAC3C,aAAa,IAAI2C,UAAW,EAAE;QAC9E;QACA;QACA,IAAI2B,UAAU,GAAG,IAAI,CAACC,6BAA6B,CAAC;UAChDC,sBAAsB,EAAEN,4BAA4B;UACpDT,MAAM,EAAEA,MAAM;UACdJ,YAAY,EAAEA,YAAY;UAC1BvB,UAAU,EAAEA,UAAU;UACtBC,UAAU,EAAEA,UAAU;UACtBH,MAAM,EAAEA,MAAM;UACdI,MAAM,EAAEA,MAAM;UACdpG,MAAM,EAAEA,MAAM;UACd0H,aAAa,EAAEA,aAAa;UAC5B1L,WAAW,EAAEA;QACjB,CAAC,CAAC;QACF,IAAImJ,YAAY,GAAGuD,UAAU,CAACvD,YAAY;QAC1C,IAAI,IAAI,CAACzB,sBAAsB,CAAC,CAAC,EAAE;UAC/BqD,UAAU,GAAG,IAAI,CAACpD,gBAAgB,CAACoD,UAAU,CAAC;QAClD;QACA,IAAI/G,MAAM,KAAK,GAAG,IAAI+G,UAAU,CAAC5E,QAAQ,CAAC,CAAC,IAAI,EAAE,EAAE;UAC/C,IAAI,CAACpF,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC;QAC/B;QACA,IAAI,CAACX,MAAM,GAAG2K,UAAU;QACxB,IAAI,CAAChK,WAAW,CAACiD,MAAM,EAAE,IAAI,CAAC;QAC9B,IAAI,CAAC0C,sBAAsB,CAAC1C,MAAM,CAAC;QACnC,IAAI,CAAC,IAAI,CAAC7E,gBAAgB,EAAE;UACxB,IAAI6E,MAAM,KAAK,GAAG,EAAE;YAChB,IAAI,IAAI,CAAChB,WAAW,CAAC,GAAG,CAAC,IAAI,IAAI,CAACA,WAAW,CAAC,GAAG,CAAC,EAAE;cAChD;cACA,IAAI,CAACjC,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC;cAC3B,IAAI,CAAC2F,sBAAsB,CAAC,GAAG,CAAC;YACpC;UACJ,CAAC,MACI,IAAI1C,MAAM,KAAK,GAAG,EAAE;YACrB,IAAI,IAAI,CAAChB,WAAW,CAAC,GAAG,CAAC,IAAI,IAAI,CAACA,WAAW,CAAC,GAAG,CAAC,EAAE;cAChD;cACA,IAAI,CAACjC,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC;cAC3B,IAAI,CAAC2F,sBAAsB,CAAC,GAAG,CAAC;YACpC;UACJ,CAAC,MACI,IAAI1C,MAAM,KAAK,GAAG,EAAE;YACrB;YACA,IAAI,CAACmD,uBAAuB,CAAC,CAAC;UAClC;UACA,IAAI,CAAC,IAAI,CAACkC,kBAAkB,CAAC,CAAC,EAAE;YAC5B,IAAI,CAAClC,uBAAuB,CAAC,CAAC;YAC9B,IAAI,CAACuF,UAAU,CAACG,UAAU,IAAIH,UAAU,CAACvD,YAAY,IAAI,CAAC,IAAI,CAAChK,gBAAgB,EAAE;cAC7E,IAAI6E,MAAM,KAAK,GAAG,EAAE;gBAChB;cAAA,CACH,MACI,IAAIA,MAAM,KAAK,GAAG,EAAE;gBACrB,IAAI0I,UAAU,CAACI,iBAAiB,KAAK,EAAE,IACnC,IAAI,CAAC7N,KAAK,CAAC2G,QAAQ,CAAC,CAAC,KAAK7H,oBAAoB,EAAE;kBAChD;kBACA;kBACA;kBACA;kBACAoL,YAAY,GAAG,KAAK;gBACxB;cACJ;YACJ;UACJ;QACJ;QACA,OAAO/L,MAAM,CAAC8L,WAAW,EAAE;UAAEjK,KAAK,EAAE,IAAI,CAACA,KAAK;UAAEkK,YAAY,EAAEA;QAAa,CAAC,CAAC;MACjF;IACJ;IACA,IAAI6B,WAAW,EAAE;MACbD,UAAU,GAAG,IAAI,CAAClJ,IAAI,CAACuK,SAAS,CAACpC,MAAM,GAAGgB,WAAW,GAAGZ,MAAM,EAAE,IAAI,CAACzL,MAAM,EAAE,IAAI,CAAC8C,QAAQ,CAAC;MAC3F,IAAIsJ,UAAU,EAAE;QACZ,IAAI,CAAC3K,MAAM,GAAG2K,UAAU;QACxB,IAAI,CAAChK,WAAW,CAACiD,MAAM,EAAE,IAAI,CAAC;QAC9B,OAAO5G,MAAM,CAAC8L,WAAW,EAAE;UAAEjK,KAAK,EAAE,IAAI,CAACA,KAAK;UAAEkK,YAAY,EAAE;QAAM,CAAC,CAAC;MAC1E;IACJ;IACA,IAAI+B,SAAS,EAAE;MACXH,UAAU,GAAG,IAAI,CAAClJ,IAAI,CAACuK,SAAS,CAACpC,MAAM,GAAGkB,SAAS,GAAGd,MAAM,EAAE,IAAI,CAACzL,MAAM,CAAC,IACtE,IAAI,CAACkD,IAAI,CAACuK,SAAS,CAAClC,UAAU,GAAGgB,SAAS,GAAGf,UAAU,EAAE,IAAI,CAACxL,MAAM,CAAC;MACzE,IAAIoM,UAAU,EAAE;QACZ,IAAI,CAAC3K,MAAM,GAAG2K,UAAU;QACxB,IAAI,CAAChK,WAAW,CAACiD,MAAM,EAAE,IAAI,CAAC;QAC9B,OAAO5G,MAAM,CAAC8L,WAAW,EAAE;UAAEjK,KAAK,EAAE,IAAI,CAACA,KAAK;UAAEkK,YAAY,EAAE;QAAK,CAAC,CAAC;MACzE;IACJ;IACA,IAAIiC,iBAAiB,IAAIpH,MAAM,KAAK,GAAG,EAAE;MACrC,IAAI,CAACjD,WAAW,CAACiD,MAAM,EAAE,KAAK,CAAC;IACnC;IACA,IAAI,CAAC,IAAI,CAAC7E,gBAAgB,EAAE;MACxB,IAAI4N,aAAa,GAAG,KAAK,CAAC;MAC1B,IAAIC,WAAW,GAAG5E,aAAa,GAAG/D,YAAY,GAAGwH,MAAM;MACvD,IAAIoB,WAAW,GAAGzP,UAAU,CAACwP,WAAW,CAAC;MACzC,IAAIrP,QAAQ,CAACsP,WAAW,CAAC,IAAIpP,gBAAgB,CAACmP,WAAW,CAAC,EAAE;QACxD,IAAKhJ,MAAM,KAAK,GAAG,KAAKiJ,WAAW,IAAI,CAAC,IAAIA,WAAW,GAAG,EAAE,CAAC,IACxDjJ,MAAM,KAAK,GAAG,KAAKiJ,WAAW,IAAI,CAAC,IAAIA,WAAW,GAAG,EAAE,CAAE,EAAE;UAC5D,IAAI7E,aAAa,EAAE;YACf,OAAOhL,MAAM,CAAC8L,WAAW,EAAE;cACvBjK,KAAK,EAAE,IAAI;cACXkK,YAAY,EAAE;YAClB,CAAC,CAAC;UACN,CAAC,MACI;YACD;YACA;YACA;YACA;YACA6D,WAAW,GAAGnF,WAAW;YACzBoF,WAAW,GAAGzP,UAAU,CAACwP,WAAW,CAAC;UACzC;QACJ;QACA,IAAI,CAACrP,QAAQ,CAACsP,WAAW,CAAC,IAAI,CAACpP,gBAAgB,CAACmP,WAAW,CAAC,EAAE;UAC1D,OAAO5P,MAAM,CAAC8L,WAAW,EAAE;YAAEjK,KAAK,EAAE,IAAI;YAAEkK,YAAY,EAAE;UAAM,CAAC,CAAC;QACpE;QACA4D,aAAa,GAAG/I,MAAM,KAAK,GAAG,GAC1BiJ,WAAW,GAAG3O,eAAe,GAC7B2O,WAAW;QACf,IAAIC,MAAM,GAAGlJ,MAAM,KAAK,GAAG;QAC3B,IAAIqB,OAAO,GAAGrB,MAAM,KAAK,GAAG;QAC5B,IAAIsB,KAAK,GAAGtB,MAAM,KAAK,GAAG;QAC1B,IAAIkB,QAAQ,GAAGvI,SAAS,CAAC,IAAI,CAACyD,MAAM,CAAC;QACrC,IAAII,gBAAgB,GAAG,IAAI,CAACF,qBAAqB,CAACE,gBAAgB,IAAI,CAAC,CAAC;QACxE,IAAIhB,IAAI,GAAG0N,MAAM,GAAGH,aAAa,GAAGvM,gBAAgB,CAACiF,CAAC,CAACxG,KAAK,IAAIiG,QAAQ,CAACQ,WAAW,CAAC,CAAC;QACtF;QACA,IAAIjG,KAAK,GAAG4F,OAAO,GAAG0H,aAAa,GAAGvM,gBAAgB,CAACmF,CAAC,CAAC1G,KAAK,IAAIiG,QAAQ,CAACU,QAAQ,CAAC,CAAC;QACrF;QACA,IAAIC,GAAG,GAAGP,KAAK,GAAGyH,aAAa,GAAGvM,gBAAgB,CAACsF,CAAC,CAAC7G,KAAK,IAAIuB,gBAAgB,CAACuF,CAAC,CAAC9G,KAAK,IAAIiG,QAAQ,CAACpI,OAAO,CAAC,CAAC;QAC5G,IAAIkJ,IAAI,GAAGxF,gBAAgB,CAACyF,CAAC,CAAChH,KAAK,IAAIuB,gBAAgB,CAAC0F,CAAC,CAACjH,KAAK,IAAIiG,QAAQ,CAACiB,QAAQ,CAAC,CAAC;QACtF,IAAIvG,OAAO,GAAGY,gBAAgB,CAAC4F,CAAC,CAACnH,KAAK,IAAIiG,QAAQ,CAACmB,UAAU,CAAC,CAAC;QAC/D,IAAIxG,OAAO,GAAGW,gBAAgB,CAAC8F,CAAC,CAACrH,KAAK,IAAIiG,QAAQ,CAACqB,UAAU,CAAC,CAAC;QAC/D,IAAIzG,YAAY,GAAGU,gBAAgB,CAACgG,CAAC,CAACvH,KAAK,IAAIiG,QAAQ,CAACuB,eAAe,CAAC,CAAC;QACzE,IAAIM,aAAa,GAAGnK,UAAU,CAAC4C,IAAI,EAAEC,KAAK,EAAEoG,GAAG,EAAEG,IAAI,EAAEpG,OAAO,EAAEC,OAAO,EAAEC,YAAY,CAAC;QACtF,IAAIoH,mBAAmB,GAAGxJ,mBAAmB,CAACqJ,aAAa,EAAEvH,IAAI,EAAEC,KAAK,EAAEoG,GAAG,EAAEG,IAAI,EAAEpG,OAAO,EAAEC,OAAO,EAAEC,YAAY,CAAC;QACpH,IAAIkH,iBAAiB,GAAGkG,MAAM,IAAI7H,OAAO,IAAIC,KAAK,GAC9C,IAAI,CAAC2B,yBAAyB,CAAC/B,QAAQ,EAAElB,MAAM,EAAEkJ,MAAM,GAAG1N,IAAI,GAAG6F,OAAO,GAAG5F,KAAK,GAAGoG,GAAG,CAAC,GACvF,IAAI;QACR,IAAIT,gBAAgB,GAAG,KAAK;QAC5B,IAAIC,OAAO,IAAI2B,iBAAiB,EAAE;UAC9B,IAAIA,iBAAiB,CAACpB,QAAQ,CAAC,CAAC,KAAKnG,KAAK,EAAE;YACxC,IAAI,IAAI,CAACuD,WAAW,CAAC,GAAG,CAAC,EAAE;cACvB,IAAIkE,mBAAmB,EAAE;gBACrBhC,QAAQ,GAAGvI,SAAS,CAACoK,aAAa,CAAC;gBACnC,IAAI,CAACL,sBAAsB,CAAC1C,MAAM,CAAC;cACvC,CAAC,MACI;gBACDoB,gBAAgB,GAAG,IAAI;gBACvB,IAAI,CAACgC,kBAAkB,CAACpD,MAAM,EAAE;kBAC5B/E,KAAK,EAAEQ,KAAK;kBACZC,IAAI,EAAE/C,SAAS,CAACqK,iBAAiB,CAAC;kBAClCzG,SAAS,EAAE5D,SAAS,CAAC,IAAI,CAACsC,KAAK;gBACnC,CAAC,CAAC;gBACF,IAAI,CAAC8B,WAAW,CAACiD,MAAM,EAAE,KAAK,CAAC;cACnC;YACJ,CAAC,MACI,IAAIkD,mBAAmB,EAAE;cAC1B,IAAI,CAACR,sBAAsB,CAAC1C,MAAM,CAAC;cACnCkB,QAAQ,GAAGvI,SAAS,CAACoK,aAAa,CAAC;cACnC,IAAI,IAAI,CAAC/D,WAAW,CAAC,GAAG,CAAC,IAAI,IAAI,CAACA,WAAW,CAAC,GAAG,CAAC,EAAE;gBAChD;gBACA,IAAI,CAACjC,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC;gBAC3B,IAAI,CAAC2F,sBAAsB,CAAC,GAAG,CAAC;cACpC;YACJ,CAAC,MACI;cACD,IAAI,CAACA,sBAAsB,CAAC1C,MAAM,CAAC;cACnCkB,QAAQ,GAAGvI,SAAS,CAACqK,iBAAiB,CAAC;YAC3C;UACJ,CAAC,MACI;YACD5B,gBAAgB,GAAG,IAAI;YACvB,IAAI,CAACgC,kBAAkB,CAACpD,MAAM,EAAE;cAC5B/E,KAAK,EAAEQ,KAAK;cACZC,IAAI,EAAE/C,SAAS,CAACqK,iBAAiB,CAAC;cAClCzG,SAAS,EAAE5D,SAAS,CAAC,IAAI,CAACsC,KAAK;YACnC,CAAC,CAAC;YACF,IAAI,CAAC8B,WAAW,CAACiD,MAAM,EAAE,KAAK,CAAC;UACnC;QACJ,CAAC,MACI,IAAIsB,KAAK,IAAI0B,iBAAiB,EAAE;UACjC,IAAIA,iBAAiB,CAAClK,OAAO,CAAC,CAAC,KAAK+I,GAAG,EAAE;YACrC,IAAI,IAAI,CAAC7C,WAAW,CAAC,GAAG,CAAC,EAAE;cACvB,IAAIkE,mBAAmB,EAAE;gBACrBhC,QAAQ,GAAGvI,SAAS,CAACoK,aAAa,CAAC;gBACnC,IAAI,CAACL,sBAAsB,CAAC1C,MAAM,CAAC;cACvC,CAAC,MACI;gBACDoB,gBAAgB,GAAG,IAAI;gBACvB,IAAI,CAACgC,kBAAkB,CAACpD,MAAM,EAAE;kBAC5B/E,KAAK,EAAE4G,GAAG;kBACVnG,IAAI,EAAE/C,SAAS,CAACqK,iBAAiB,CAAC;kBAClCzG,SAAS,EAAE5D,SAAS,CAAC,IAAI,CAACsC,KAAK;gBACnC,CAAC,CAAC;gBACF,IAAI,CAAC8B,WAAW,CAACiD,MAAM,EAAE,KAAK,CAAC;cACnC;YACJ,CAAC,MACI,IAAIkD,mBAAmB,EAAE;cAC1BhC,QAAQ,GAAGvI,SAAS,CAACoK,aAAa,CAAC;cACnC,IAAI,CAACL,sBAAsB,CAAC1C,MAAM,CAAC;cACnC,IAAI,IAAI,CAAChB,WAAW,CAAC,GAAG,CAAC,IAAI,IAAI,CAACA,WAAW,CAAC,GAAG,CAAC,EAAE;gBAChD;gBACA,IAAI,CAACjC,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC;gBAC3B,IAAI,CAAC2F,sBAAsB,CAAC,GAAG,CAAC;cACpC;YACJ,CAAC,MACI;cACD,IAAI,CAACA,sBAAsB,CAAC1C,MAAM,CAAC;cACnCkB,QAAQ,GAAGvI,SAAS,CAACqK,iBAAiB,CAAC;YAC3C;UACJ,CAAC,MACI;YACD5B,gBAAgB,GAAG,IAAI;YACvB,IAAI,CAACgC,kBAAkB,CAACpD,MAAM,EAAE;cAC5B/E,KAAK,EAAE4G,GAAG;cACVnG,IAAI,EAAE/C,SAAS,CAAC,IAAI,CAACsC,KAAK,CAAC;cAC3BsB,SAAS,EAAE5D,SAAS,CAAC,IAAI,CAACsC,KAAK;YACnC,CAAC,CAAC;YACF,IAAI,CAAC8B,WAAW,CAACiD,MAAM,EAAE,KAAK,CAAC;UACnC;QACJ,CAAC,MACI,IAAIkJ,MAAM,IAAIlG,iBAAiB,EAAE;UAClC,IAAIA,iBAAiB,CAACtB,WAAW,CAAC,CAAC,KAAKlG,IAAI,EAAE;YAC1C,IAAI,IAAI,CAACwD,WAAW,CAAC,GAAG,CAAC,IAAI,IAAI,CAACA,WAAW,CAAC,GAAG,CAAC,EAAE;cAChD,IAAIkE,mBAAmB,EAAE;gBACrBhC,QAAQ,GAAGvI,SAAS,CAACoK,aAAa,CAAC;gBACnC,IAAI,CAACL,sBAAsB,CAAC1C,MAAM,CAAC;cACvC,CAAC,MACI;gBACDoB,gBAAgB,GAAG,IAAI;gBACvB,IAAI,CAACgC,kBAAkB,CAACpD,MAAM,EAAE;kBAC5B/E,KAAK,EAAEO,IAAI;kBACXE,IAAI,EAAE/C,SAAS,CAACqK,iBAAiB,CAAC;kBAClCzG,SAAS,EAAE5D,SAAS,CAAC,IAAI,CAACsC,KAAK;gBACnC,CAAC,CAAC;gBACF,IAAI,CAAC8B,WAAW,CAACiD,MAAM,EAAE,KAAK,CAAC;cACnC;YACJ,CAAC,MACI,IAAIkD,mBAAmB,EAAE;cAC1B,IAAI,CAACR,sBAAsB,CAAC1C,MAAM,CAAC;cACnCkB,QAAQ,GAAGvI,SAAS,CAACoK,aAAa,CAAC;cACnC,IAAI,IAAI,CAAC/D,WAAW,CAAC,GAAG,CAAC,IAAI,IAAI,CAACA,WAAW,CAAC,GAAG,CAAC,EAAE;gBAChD,IAAI,CAACjC,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC;gBAC3B,IAAI,CAAC2F,sBAAsB,CAAC,GAAG,CAAC;cACpC;YACJ,CAAC,MACI;cACD,IAAI,CAACA,sBAAsB,CAAC1C,MAAM,CAAC;cACnCkB,QAAQ,GAAGvI,SAAS,CAACqK,iBAAiB,CAAC;YAC3C;UACJ,CAAC,MACI;YACD5B,gBAAgB,GAAG,IAAI;YACvB,IAAI,CAACgC,kBAAkB,CAACpD,MAAM,EAAE;cAC5B/E,KAAK,EAAEO,IAAI;cACXE,IAAI,EAAE/C,SAAS,CAACqK,iBAAiB,CAAC;cAClCzG,SAAS,EAAE5D,SAAS,CAAC,IAAI,CAACsC,KAAK;YACnC,CAAC,CAAC;YACF,IAAI,CAAC8B,WAAW,CAACiD,MAAM,EAAE,KAAK,CAAC;UACnC;QACJ;QACA,IAAI,CAACoB,gBAAgB,EAAE;UACnB,IAAI,CAACrE,WAAW,CAACiD,MAAM,EAAE,IAAI,CAAC;UAC9B,IAAIoE,aAAa,IAAI,CAACxK,WAAW,CAACmN,UAAU,CAAC,EAAE;YAC3C,IAAIoC,cAAc,GAAG,IAAI,CAACtL,IAAI,CAACuK,SAAS,CAAClC,UAAU,GAAG2B,MAAM,GAAG1B,UAAU,EAAE,IAAI,CAACxL,MAAM,EAAE,IAAI,CAAC8C,QAAQ,CAAC;YACtG,IAAI7D,WAAW,CAACuP,cAAc,CAAC,EAAE;cAC7B,IAAI,CAAC/M,MAAM,GAAG+M,cAAc;YAChC;UACJ,CAAC,MACI;YACD,IAAI,CAAC/M,MAAM,GAAG8E,QAAQ;UAC1B;UACA,IAAI,IAAI,CAAC9B,QAAQ,CAAC,CAAC,EAAE;YACjB,IAAI,CAAC9B,gBAAgB,CAAC,CAAC;UAC3B;QACJ;QACA,IAAI6H,YAAY,GAAG,KAAK;QACxB,IAAInF,MAAM,KAAK,GAAG,EAAE;UAChB,IAAIiJ,WAAW,IAAI,CAAC,IAAID,WAAW,CAAClM,MAAM,IAAI,CAAC,EAAE;YAC7CqI,YAAY,GAAG,IAAI;UACvB,CAAC,MACI;YACDA,YAAY,GAAG,KAAK;UACxB;QACJ,CAAC,MACI;UACD,IAAImB,cAAc,EAAE;YAChB,IAAI8C,oBAAoB,GAAG,IAAI,CAACT,6BAA6B,CAAC;cAC1DC,sBAAsB,EAAE,CAAC,IAAI,CAACzN,gBAAgB;cAC9C0M,MAAM,EAAEA,MAAM;cACdJ,YAAY,EAAEA,YAAY;cAC1BvB,UAAU,EAAEA,UAAU;cACtBC,UAAU,EAAEA,UAAU;cACtBH,MAAM,EAAEA,MAAM;cACdI,MAAM,EAAEA,MAAM;cACdpG,MAAM,EAAEA,MAAM;cACd0H,aAAa,EAAEA,aAAa;cAC5B1L,WAAW,EAAEA;YACjB,CAAC,CAAC,CAACmJ,YAAY;YACfA,YAAY,GAAGiE,oBAAoB;UACvC,CAAC,MACI;YACDjE,YAAY,GAAG6D,WAAW,CAAClM,MAAM,GAAG4J,aAAa;UACrD;QACJ;QACA,OAAOtN,MAAM,CAAC8L,WAAW,EAAE;UACvBjK,KAAK,EAAE,IAAI;UACXkK,YAAY,EAAEA,YAAY;UAC1BE,kBAAkB,EAAEjE;QACxB,CAAC,CAAC;MACN;IACJ;IACA,OAAOhI,MAAM,CAAC8L,WAAW,EAAE;MAAEjK,KAAK,EAAE,IAAI;MAAEkK,YAAY,EAAE;IAAM,CAAC,CAAC;EACpE,CAAC;EACD;AACJ;AACA;EACI5K,UAAU,CAAC2C,SAAS,CAACmM,SAAS,GAAG,UAAUrJ,MAAM,EAAE;IAC/C,OAAO,IAAI,CAACnC,IAAI,CAACqB,eAAe,CAAC,IAAI,CAACvE,MAAM,EAAE,IAAI,CAAC8C,QAAQ,CAAC,CAAC0B,MAAM,CAAClG,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC+G,MAAM,CAAC;EAClG,CAAC;EACD;AACJ;AACA;EACIzF,UAAU,CAAC2C,SAAS,CAAC0I,gBAAgB,GAAG,YAAY;IAChD,IAAI0D,cAAc,GAAG,IAAI,CAACtN,WAAW,KAAK,IAAI;IAC9C,IAAI,CAACuN,cAAc,CAAC,IAAI,CAAC;IACzB,OAAOD,cAAc;EACzB,CAAC;EACD/O,UAAU,CAAC2C,SAAS,CAACqM,cAAc,GAAG,UAAUvN,WAAW,EAAE;IACzD,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC,CAAC;EACD;AACJ;AACA;EACIzB,UAAU,CAAC2C,SAAS,CAACsM,cAAc,GAAG,YAAY;IAC9C,OAAO,IAAI,CAACxN,WAAW,IAAI,CAAC,CAAC;EACjC,CAAC;EACD;AACJ;AACA;EACIzB,UAAU,CAAC2C,SAAS,CAACyG,gBAAgB,GAAG,UAAUjI,IAAI,EAAE;IACpD,IAAI,CAACrC,SAAS,CAACqC,IAAI,CAAC,EAAE;MAClB,OAAOA,IAAI;IACf;IACA,IAAI+N,YAAY,GAAGnQ,gBAAgB,CAACoC,IAAI,CAAC;IACzC,IAAIgO,WAAW,GAAG,IAAI,CAACC,wBAAwB,CAACF,YAAY,CAAC;IAC7D,IAAIG,cAAc,GAAGrQ,QAAQ,CAACmC,IAAI,EAAEgO,WAAW,GAAGD,YAAY,CAAC;IAC/D,OAAOG,cAAc;EACzB,CAAC;EACDrP,UAAU,CAAC2C,SAAS,CAACqK,oBAAoB,GAAG,UAAUvH,MAAM,EAAE;IAC1D,IAAIhE,WAAW,GAAG,IAAI,CAACA,WAAW,IAAI,CAAC,CAAC;IACxCA,WAAW,CAACgE,MAAM,CAAC,GAAG,CAAChE,WAAW,CAACgE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;IACpD,IAAI,CAAChE,WAAW,GAAGA,WAAW;EAClC,CAAC;EACD;AACJ;AACA;EACIzB,UAAU,CAAC2C,SAAS,CAACoK,WAAW,GAAG,UAAUuC,KAAK,EAAE7J,MAAM,EAAE;IACxD,IAAIf,OAAO,GAAG,IAAI,CAACuI,WAAW,CAACqC,KAAK,EAAE7J,MAAM,CAAC;IAC7C,OAAOf,OAAO,CAACF,IAAI,KAAK,OAAO,IAAIE,OAAO,CAAC6K,KAAK;EACpD,CAAC;EACD;AACJ;AACA;EACIvP,UAAU,CAAC2C,SAAS,CAACsK,WAAW,GAAG,UAAUqC,KAAK,EAAE7J,MAAM,EAAE;IACxD,OAAO6J,KAAK,CAACtF,MAAM,CAAC,UAAUwF,IAAI,EAAE;MAAE,OAAOA,IAAI,CAAC9K,OAAO,CAAC0G,OAAO,CAAC3F,MAAM,CAAC,KAAK,CAAC,CAAC;IAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3F,CAAC;EACD;AACJ;AACA;EACIzF,UAAU,CAAC2C,SAAS,CAAC8M,IAAI,GAAG,UAAU/O,KAAK,EAAEgE,OAAO,EAAE;IAClD,IAAIgL,SAAS,GAAGhP,KAAK,CAAC+M,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG;IAC9C,OAAO9O,OAAO,CAAC+F,OAAO,CAACnC,MAAM,GAAGmN,SAAS,CAACnN,MAAM,CAAC,GAAGmN,SAAS;EACjE,CAAC;EACD;AACJ;AACA;EACI1P,UAAU,CAAC2C,SAAS,CAAC+J,UAAU,GAAG,UAAUiD,SAAS,EAAE;IACnD,IAAI,CAACjO,cAAc,IAAIiO,SAAS,CAACC,WAAW,CAAC,CAAC;IAC9C,IAAI,IAAI,CAAC/L,UAAU,CAACtB,MAAM,KAAK,CAAC,EAAE;MAC9B,OAAO,EAAE;IACb;IACA,OAAO,IAAI,CAACb,cAAc,CAACa,MAAM,GAAG,CAAC,EAAE;MACnC,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACuB,UAAU,CAACtB,MAAM,EAAED,CAAC,EAAE,EAAE;QAC7C,IAAI,IAAI,CAACuB,UAAU,CAACvB,CAAC,CAAC,CAACsN,WAAW,CAAC,CAAC,CAACxE,OAAO,CAAC,IAAI,CAAC1J,cAAc,CAAC,KAAK,CAAC,EAAE;UACrE,OAAO,IAAI,CAACmC,UAAU,CAACvB,CAAC,CAAC;QAC7B;MACJ;MACA,IAAIuN,UAAU,GAAGlC,QAAQ,CAAC,IAAI,CAACjM,cAAc,EAAE,EAAE,CAAC;MAClD;MACA,IAAImO,UAAU,IAAI,CAAC,IAAIA,UAAU,IAAI,EAAE,IAAIA,UAAU,CAAC9J,QAAQ,CAAC,CAAC,KAAK,IAAI,CAACrE,cAAc,EAAE;QACtF,OAAO,IAAI,CAACmC,UAAU,CAACgM,UAAU,GAAG,CAAC,CAAC;MAC1C;MACA,IAAI,CAACnO,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC8L,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC9L,cAAc,CAACa,MAAM,CAAC;IACtF;IACA,OAAO,EAAE;EACb,CAAC;EACD;AACJ;AACA;EACIvC,UAAU,CAAC2C,SAAS,CAACiK,cAAc,GAAG,UAAU+C,SAAS,EAAElK,MAAM,EAAE;IAC/D,IAAIqK,UAAU,GAAGH,SAAS,CAACC,WAAW,CAAC,CAAC;IACxC,IAAInK,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC1B,UAAU,EAAE;MACnC,IAAI,IAAI,CAACA,UAAU,CAACgM,EAAE,CAACH,WAAW,CAAC,CAAC,CAAC5E,UAAU,CAAC8E,UAAU,CAAC,EAAE;QACzD,OAAO,IAAI,CAAC/L,UAAU,CAACgM,EAAE;MAC7B,CAAC,MACI,IAAI,IAAI,CAAChM,UAAU,CAACiM,EAAE,CAACJ,WAAW,CAAC,CAAC,CAAC5E,UAAU,CAAC8E,UAAU,CAAC,EAAE;QAC9D,OAAO,IAAI,CAAC/L,UAAU,CAACiM,EAAE;MAC7B;IACJ;IACA,OAAO,EAAE;EACb,CAAC;EACD;AACJ;AACA;EACIhQ,UAAU,CAAC2C,SAAS,CAACmB,kBAAkB,GAAG,UAAUmM,MAAM,EAAE;IACxD,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;MAAEA,MAAM,GAAG,IAAI;IAAE;IACxC,IAAIC,eAAe,GAAG,IAAI,CAAC5M,IAAI,CAACqB,eAAe,CAAC,IAAI,CAACvE,MAAM,EAAE,IAAI,CAAC8C,QAAQ,CAAC;IAC3E,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4N,eAAe,CAAC3N,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7C,IAAI4N,eAAe,CAAC5N,CAAC,CAAC,CAACkC,IAAI,KAAK,OAAO,IAAI0L,eAAe,CAAC5N,CAAC,CAAC,CAACiN,KAAK,EAAE;QACjE,OAAO,IAAI,CAACjM,IAAI,CAAC6M,eAAe,CAACF,MAAM,EAAEC,eAAe,CAAC5N,CAAC,CAAC,CAACiN,KAAK,CAAC;MACtE;IACJ;IACA,OAAO,EAAE;EACb,CAAC;EACD;AACJ;AACA;EACIvP,UAAU,CAAC2C,SAAS,CAACqB,aAAa,GAAG,UAAUiM,MAAM,EAAE;IACnD,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;MAAEA,MAAM,GAAG,IAAI;IAAE;IACxC,IAAIC,eAAe,GAAG,IAAI,CAAC5M,IAAI,CAACqB,eAAe,CAAC,IAAI,CAACvE,MAAM,CAAC;IAC5D,KAAK,IAAIkC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4N,eAAe,CAAC3N,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7C,IAAI4N,eAAe,CAAC5N,CAAC,CAAC,CAACkC,IAAI,KAAK,WAAW,IAAI0L,eAAe,CAAC5N,CAAC,CAAC,CAACiN,KAAK,EAAE;QACrE,OAAO,IAAI,CAACjM,IAAI,CAAC6M,eAAe,CAACF,MAAM,EAAEC,eAAe,CAAC5N,CAAC,CAAC,CAACiN,KAAK,CAAC;MACtE;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACD;AACJ;AACA;EACIvP,UAAU,CAAC2C,SAAS,CAACwK,aAAa,GAAG,UAAUzI,OAAO,EAAE;IACpD,IAAIA,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACpB,OAAO,CAAC;IACZ;IACA,IAAI9E,2BAA2B,CAACwQ,IAAI,CAAC1L,OAAO,CAAC,EAAE;MAC3C,OAAO,CAAC;IACZ;IACA,OAAO,CAAC;EACZ,CAAC;EACD;AACJ;AACA;EACI1E,UAAU,CAAC2C,SAAS,CAACN,gBAAgB,GAAG,UAAUlB,IAAI,EAAEf,MAAM,EAAE;IAC5D,IAAI8P,eAAe,GAAG,IAAI,CAAC5M,IAAI,CAACqB,eAAe,CAACvE,MAAM,EAAE,IAAI,CAAC8C,QAAQ,CAAC;IACtE,IAAIoM,KAAK,GAAG,EAAE;IACd,IAAI5J,OAAO,GAAG,EAAE;IAChB,KAAK,IAAIpD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4N,eAAe,CAAC3N,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7C,IAAI+N,UAAU,GAAG,IAAI,CAAC/M,IAAI,CAAC0B,UAAU,CAAC7D,IAAI,EAAE;QAAEuD,OAAO,EAAEwL,eAAe,CAAC5N,CAAC,CAAC,CAACoC;MAAQ,CAAC,EAAE,IAAI,CAACxB,QAAQ,CAAC,CAACX,MAAM;MAC1G,OAAO8N,UAAU,GAAG,CAAC,EAAE;QACnBf,KAAK,CAACgB,IAAI,CAAC,IAAI,CAAC1O,OAAO,CAACsO,eAAe,CAAC5N,CAAC,CAAC,CAACoC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAInF,SAAS,CAACgR,eAAe,CAAC;QACpF7K,OAAO,CAAC4K,IAAI,CAACJ,eAAe,CAAC5N,CAAC,CAAC,CAAC;QAChC+N,UAAU,EAAE;MAChB;IACJ;IACA,IAAIG,WAAW,GAAG,IAAI/R,IAAI,CAAC,CAAC;IAC5B+R,WAAW,CAAC5O,OAAO,GAAG0N,KAAK,CAACmB,IAAI,CAAC,EAAE,CAAC;IACpCD,WAAW,CAAC9K,OAAO,GAAGA,OAAO;IAC7B,OAAO8K,WAAW;EACtB,CAAC;EACD;AACJ;AACA;EACIxQ,UAAU,CAAC2C,SAAS,CAACuD,KAAK,GAAG,UAAUf,IAAI,EAAEC,IAAI,EAAE;IAC/C;IACA,IAAIsL,UAAU,GAAG,EAAE;IACnB,IAAIC,YAAY,GAAG,EAAE;IACrB,IAAIvQ,MAAM,GAAGgF,IAAI,CAACxD,OAAO;IACzB,IAAIgP,uBAAuB,GAAG,KAAK;IACnC,IAAIC,wBAAwB,GAAG,CAAC;IAChC,IAAItL,cAAc,GAAG,IAAI,CAACC,wBAAwB,CAACpF,MAAM,CAAC;IAC1D,KAAK,IAAI0Q,iBAAiB,GAAG1Q,MAAM,CAACmC,MAAM,GAAG,CAAC,EAAEuO,iBAAiB,IAAI,CAAC,EAAEA,iBAAiB,EAAE,EAAE;MACzF,IAAIlL,eAAe,GAAG,IAAI,CAACC,kBAAkB,CAACT,IAAI,EAAE0L,iBAAiB,CAAC;MACtE,IAAI,IAAI,CAACnP,UAAU,CAACyJ,OAAO,CAAChL,MAAM,CAAC0Q,iBAAiB,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAACrM,WAAW,CAACrE,MAAM,CAAC0Q,iBAAiB,CAAC,CAAC,EAAE;QAC1G,IAAI,IAAI,CAAClQ,gBAAgB,EAAE;UACvB8P,UAAU,GAAGvL,IAAI,CAAC2L,iBAAiB,CAAC,GAAGJ,UAAU;QACrD,CAAC,MACI;UACD,IAAIvL,IAAI,CAAC5C,MAAM,KAAKnC,MAAM,CAACmC,MAAM,EAAE;YAC/B,IAAIqO,uBAAuB,EAAE;cACzBF,UAAU,GAAGvL,IAAI,CAAC2L,iBAAiB,CAAC,GAAGJ,UAAU;YACrD,CAAC,MACI,IAAIG,wBAAwB,GAAG,CAAC,EAAE;cACnCH,UAAU,GAAGvL,IAAI,CAAC2L,iBAAiB,CAAC,GAAGJ,UAAU;cACjDG,wBAAwB,EAAE;cAC1B,IAAIA,wBAAwB,IAAI,CAAC,EAAE;gBAC/BD,uBAAuB,GAAG,IAAI;cAClC;YACJ,CAAC,MACI;cACDF,UAAU,GAAG,CAACvL,IAAI,CAAC2L,iBAAiB,GAAG3L,IAAI,CAAC5C,MAAM,GAAGnC,MAAM,CAACmC,MAAM,CAAC,IAAI,EAAE,IAAImO,UAAU;YAC3F;UACJ,CAAC,MACI;YACDA,UAAU,GAAGvL,IAAI,CAAC2L,iBAAiB,CAAC,GAAGJ,UAAU;UACrD;QACJ;QACAC,YAAY,GAAGvQ,MAAM,CAAC0Q,iBAAiB,CAAC,GAAGH,YAAY;MAC3D,CAAC,MACI;QACD,IAAIlL,MAAM,GAAGrF,MAAM,CAAC0Q,iBAAiB,CAAC;QACtC,IAAIC,yBAAyB,GAAG,CAAC;QACjC,IAAI,IAAI,CAACnQ,gBAAgB,IAAK,CAAC,IAAI,CAACA,gBAAgB,IAAI,CAAC,IAAI,CAAC+E,uBAAuB,CAACF,MAAM,CAAE,EAAE;UAC5F,OAAOqL,iBAAiB,IAAI,CAAC,IAAIrL,MAAM,KAAKrF,MAAM,CAAC0Q,iBAAiB,CAAC,EAAE;YACnEA,iBAAiB,EAAE;UACvB;UACAA,iBAAiB,EAAE;QACvB;QACA,IAAI,IAAI,CAACrP,WAAW,IAAI,IAAI,CAACA,WAAW,CAACgE,MAAM,CAAC,EAAE;UAC9CiL,UAAU,GAAG,GAAG,GAAGA,UAAU;QACjC,CAAC,MACI;UACD,IAAI,CAAC,IAAI,CAAC9P,gBAAgB,IAAI,IAAI,CAAC+E,uBAAuB,CAACF,MAAM,CAAC,EAAE;YAChE,IAAIK,YAAY,GAAG,IAAI,CAACH,uBAAuB,CAACF,MAAM,CAAC,CAACM,QAAQ,CAAC,CAAC;YAClE,IAAIN,MAAM,KAAK,GAAG,EAAE;cAChBK,YAAY,GAAG,CAAC7G,UAAU,CAAC,IAAI,CAAC0G,uBAAuB,CAACF,MAAM,CAAC,CAAC,GAAG1F,eAAe,EAAEgG,QAAQ,CAAC,CAAC;cAC9F,IAAIH,eAAe,CAACrD,MAAM,GAAG1C,+BAA+B,EAAE;gBAC1D6Q,UAAU,GAAGnL,cAAc,CAACE,MAAM,CAAC,CAACqL,iBAAiB,CAAC,GAAGJ,UAAU;cACvE,CAAC,MACI;gBACD5K,YAAY,GAAG,CAAC7G,UAAU,CAAC,IAAI,CAAC0G,uBAAuB,CAACF,MAAM,CAAC,CAAC,GAAG1F,eAAe,EAAEgG,QAAQ,CAAC,CAAC;gBAC9F,IAAIC,iBAAiB,GAAGrH,OAAO,CAACiH,eAAe,CAACrD,MAAM,GAAGuD,YAAY,CAACvD,MAAM,CAAC,GAAGuD,YAAY;gBAC5F4K,UAAU,GAAG1K,iBAAiB,GAAG0K,UAAU;gBAC3CK,yBAAyB,GAAGnL,eAAe,CAACrD,MAAM,GAAG,CAAC;gBACtDsO,wBAAwB,GAAG/K,YAAY,CAACvD,MAAM,GAAGqD,eAAe,CAACrD,MAAM;cAC3E;YACJ,CAAC,MACI;cACD,IAAIyD,iBAAiB,GAAGrH,OAAO,CAACiH,eAAe,CAACrD,MAAM,GAAGuD,YAAY,CAACvD,MAAM,CAAC,GAAGuD,YAAY;cAC5F4K,UAAU,GAAG1K,iBAAiB,GAAG0K,UAAU;cAC3CK,yBAAyB,GAAGnL,eAAe,CAACrD,MAAM,GAAG,CAAC;cACtDsO,wBAAwB,GAAG/K,YAAY,CAACvD,MAAM,GAAGqD,eAAe,CAACrD,MAAM;YAC3E;UACJ,CAAC,MACI;YACDmO,UAAU,GAAG,IAAI,CAACM,aAAa,CAAC5L,IAAI,CAACM,OAAO,CAACoL,iBAAiB,CAAC,CAAC,GAAGJ,UAAU;UACjF;QACJ;QACA,OAAOC,YAAY,CAACpO,MAAM,GAAGmO,UAAU,CAACnO,MAAM,EAAE;UAC5CoO,YAAY,GAAGvQ,MAAM,CAAC0Q,iBAAiB,CAAC,GAAGH,YAAY;QAC3D;QACA,IAAII,yBAAyB,KAAK,CAAC,EAAE;UACjCD,iBAAiB,GAAIA,iBAAiB,GAAGC,yBAAyB,IAAK5L,IAAI,CAAC5C,MAAM,GAAGnC,MAAM,CAACmC,MAAM,CAAC;QACvG;MACJ;IACJ;IACA,OAAO;MAAE4C,IAAI,EAAEuL,UAAU;MAAEtQ,MAAM,EAAEuQ;IAAa,CAAC;EACrD,CAAC;EACD;AACJ;AACA;EACI3Q,UAAU,CAAC2C,SAAS,CAACqO,aAAa,GAAG,UAAUxB,IAAI,EAAE;IACjD,IAAIrP,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,IAAI,MAAM;IACxD,IAAIA,iBAAiB,CAACqP,IAAI,CAAChL,IAAI,CAAC,EAAE;MAC9B,OAAOrE,iBAAiB,CAACqP,IAAI,CAAChL,IAAI,CAAC;IACvC;IACA,IAAIrE,iBAAiB,KAAK,eAAe,EAAE;MACvC,OAAOqP,IAAI,CAAC9K,OAAO;IACvB;IACA,OAAO,IAAI,CAACpB,IAAI,CAAC0N,aAAa,CAACvO,MAAM,CAACwO,MAAM,CAACzB,IAAI,EAAE;MAAE0B,QAAQ,EAAE/Q;IAAkB,CAAC,CAAC,CAAC;EACxF,CAAC;EACD;AACJ;AACA;EACIH,UAAU,CAAC2C,SAAS,CAACyM,wBAAwB,GAAG,UAAUF,YAAY,EAAE;IACpE,OAAOA,YAAY,GAAG,IAAI,CAAC1O,eAAe,GACtCd,qBAAqB,GACrBC,oBAAoB;EAC5B,CAAC;EACD;AACJ;AACA;EACIK,UAAU,CAAC2C,SAAS,CAACwG,sBAAsB,GAAG,YAAY;IACtD,OAAO,IAAI,CAAC7F,IAAI,CAACqB,eAAe,CAAC,IAAI,CAACvE,MAAM,CAAC,CAACsK,IAAI,CAAC,UAAU8E,IAAI,EAAE;MAAE,OAAOA,IAAI,CAAC9K,OAAO,KAAK,IAAI;IAAE,CAAC,CAAC;EACzG,CAAC;EACD1E,UAAU,CAAC2C,SAAS,CAACI,gBAAgB,GAAG,YAAY;IAChD,IAAIqB,KAAK,GAAG,IAAI;IAChB,IAAI,CAACrC,qBAAqB,CAACC,SAAS,GAAG,IAAI;IAC3CS,MAAM,CAACY,IAAI,CAAC,IAAI,CAACtB,qBAAqB,CAACE,gBAAgB,CAAC,CAACmE,OAAO,CAAC,UAAU5C,GAAG,EAAE;MAC5EY,KAAK,CAAC+M,oBAAoB,CAAC3N,GAAG,CAAC;IACnC,CAAC,CAAC;EACN,CAAC;EACDxD,UAAU,CAAC2C,SAAS,CAACwF,sBAAsB,GAAG,UAAU1C,MAAM,EAAE;IAC5D,IAAIrB,KAAK,GAAG,IAAI;IAChB,IAAI,CAAC+M,oBAAoB,CAAC1L,MAAM,CAAC;IACjC,IAAI2L,sBAAsB,GAAG,IAAI;IACjC3O,MAAM,CAACY,IAAI,CAAC,IAAI,CAACtB,qBAAqB,CAACE,gBAAgB,CAAC,CAACmE,OAAO,CAAC,UAAU5C,GAAG,EAAE;MAC5E,IAAIY,KAAK,CAACrC,qBAAqB,CAACE,gBAAgB,CAACuB,GAAG,CAAC,IACjD1E,SAAS,CAACsF,KAAK,CAACrC,qBAAqB,CAACE,gBAAgB,CAACuB,GAAG,CAAC,CAAC9C,KAAK,CAAC,EAAE;QACpE0Q,sBAAsB,GAAG,KAAK;MAClC;IACJ,CAAC,CAAC;IACF,IAAIA,sBAAsB,EAAE;MACxB,IAAI,CAACrO,gBAAgB,CAAC,CAAC;IAC3B;EACJ,CAAC;EACD/C,UAAU,CAAC2C,SAAS,CAACwO,oBAAoB,GAAG,UAAU1L,MAAM,EAAE;IAC1D,IAAI,IAAI,CAAC1D,qBAAqB,CAACE,gBAAgB,CAACwD,MAAM,CAAC,EAAE;MACrD,IAAI,CAAC1D,qBAAqB,CAACE,gBAAgB,CAACwD,MAAM,CAAC,GAAG;QAClD/E,KAAK,EAAE,IAAI;QACXS,IAAI,EAAE,IAAI;QACVe,eAAe,EAAE;MACrB,CAAC;IACL;EACJ,CAAC;EACD;AACJ;AACA;EACIlC,UAAU,CAAC2C,SAAS,CAAC0D,kBAAkB,GAAG,UAAUZ,MAAM,EAAE;IACxD,IAAI4L,eAAe,GAAG,IAAI,CAACtP,qBAAqB,CAACE,gBAAgB,CAACwD,MAAM,CAAC;IACzE,OAAO4L,eAAe,IAAI,CAAC,CAAC;EAChC,CAAC;EACD;AACJ;AACA;EACIrR,UAAU,CAAC2C,SAAS,CAACgD,uBAAuB,GAAG,UAAUF,MAAM,EAAE;IAC7D,IAAI4L,eAAe,GAAG,IAAI,CAACtP,qBAAqB,CAACE,gBAAgB,CAACwD,MAAM,CAAC;IACzE,OAAO,CAAC4L,eAAe,IAAI,CAAC,CAAC,EAAE3Q,KAAK;EACxC,CAAC;EACDV,UAAU,CAAC2C,SAAS,CAACkG,kBAAkB,GAAG,UAAUpD,MAAM,EAAExF,EAAE,EAAE;IAC5D,IAAII,EAAE,GAAGJ,EAAE,CAACS,KAAK;MAAEA,KAAK,GAAGL,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,EAAE;MAAEE,EAAE,GAAGN,EAAE,CAACkB,IAAI;MAAEA,IAAI,GAAGZ,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,EAAE;MAAEE,EAAE,GAAGR,EAAE,CAACiC,eAAe;MAAEA,eAAe,GAAGzB,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,EAAE;MAAEE,EAAE,GAAGV,EAAE,CAAC+B,SAAS;MAAEA,SAAS,GAAGrB,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,EAAE;IACjO,IAAI,IAAI,CAACoB,qBAAqB,CAACE,gBAAgB,CAACwD,MAAM,CAAC,EAAE;MACrD,IAAI,CAAC1D,qBAAqB,CAACE,gBAAgB,CAACwD,MAAM,CAAC,CAAC/E,KAAK,GAAGA,KAAK;MACjE,IAAI,CAACqB,qBAAqB,CAACE,gBAAgB,CAACwD,MAAM,CAAC,CAACtE,IAAI,GAAGA,IAAI;MAC/D,IAAI,CAACY,qBAAqB,CAACE,gBAAgB,CAACwD,MAAM,CAAC,CAACvD,eAAe,GAAGA,eAAe;MACrF,IAAI,CAACH,qBAAqB,CAACC,SAAS,GAAGA,SAAS;IACpD;EACJ,CAAC;EACD;AACJ;AACA;EACIhC,UAAU,CAAC2C,SAAS,CAACmI,kBAAkB,GAAG,YAAY;IAClD,IAAI1G,KAAK,GAAG,IAAI;IAChB,IAAI0G,kBAAkB,GAAG,KAAK;IAC9BrI,MAAM,CAACY,IAAI,CAAC,IAAI,CAACtB,qBAAqB,CAACE,gBAAgB,CAAC,CAACmE,OAAO,CAAC,UAAU5C,GAAG,EAAE;MAC5E,IAAIY,KAAK,CAACrC,qBAAqB,CAACE,gBAAgB,CAACuB,GAAG,CAAC,IACjD1E,SAAS,CAACsF,KAAK,CAACrC,qBAAqB,CAACE,gBAAgB,CAACuB,GAAG,CAAC,CAAC9C,KAAK,CAAC,EAAE;QACpEoK,kBAAkB,GAAG,IAAI;MAC7B;IACJ,CAAC,CAAC;IACF,OAAOA,kBAAkB;EAC7B,CAAC;EACD;AACJ;AACA;EACI9K,UAAU,CAAC2C,SAAS,CAAC2O,0BAA0B,GAAG,UAAUnQ,IAAI,EAAEsE,MAAM,EAAEiB,MAAM,EAAE;IAC9E,IAAIC,QAAQ,GAAGvI,SAAS,CAAC+C,IAAI,CAAC;IAC9B,IAAIyF,YAAY,GAAG,KAAK;IACxB,QAAQnB,MAAM;MACV,KAAK,GAAG;QACJkB,QAAQ,CAACJ,WAAW,CAACI,QAAQ,CAACQ,WAAW,CAAC,CAAC,GAAGT,MAAM,CAAC;QACrD;MACJ,KAAK,GAAG;QACJC,QAAQ,GAAGxI,SAAS,CAAC,IAAI,CAACuC,KAAK,EAAEgG,MAAM,CAAC;QACxC;MACJ,KAAK,GAAG;MACR,KAAK,GAAG;QACJC,QAAQ,CAACmC,OAAO,CAACnC,QAAQ,CAACpI,OAAO,CAAC,CAAC,GAAGmI,MAAM,CAAC;QAC7C;MACJ,KAAK,GAAG;MACR,KAAK,GAAG;QACJC,QAAQ,CAACoC,QAAQ,CAACpC,QAAQ,CAACiB,QAAQ,CAAC,CAAC,GAAGlB,MAAM,CAAC;QAC/CE,YAAY,GAAG,IAAI;QACnB;MACJ,KAAK,GAAG;QACJD,QAAQ,CAACqC,UAAU,CAACrC,QAAQ,CAACmB,UAAU,CAAC,CAAC,GAAGpB,MAAM,CAAC;QACnDE,YAAY,GAAG,IAAI;QACnB;MACJ,KAAK,GAAG;QACJD,QAAQ,CAACsC,UAAU,CAACtC,QAAQ,CAACqB,UAAU,CAAC,CAAC,GAAGtB,MAAM,CAAC;QACnDE,YAAY,GAAG,IAAI;QACnB;MACJ,KAAK,GAAG;QACJD,QAAQ,CAACuC,eAAe,CAACvC,QAAQ,CAACuB,eAAe,CAAC,CAAC,GAAGxB,MAAM,CAAC;QAC7D;MACJ,KAAK,GAAG;QACJC,QAAQ,CAACoC,QAAQ,CAACpC,QAAQ,CAACiB,QAAQ,CAAC,CAAC,GAAI,EAAE,GAAGlB,MAAO,CAAC;QACtDE,YAAY,GAAG,IAAI;QACnB;MACJ;QAAS;IACb;IACA,OAAO;MACHzF,IAAI,EAAEwF,QAAQ;MACdC,YAAY,EAAEA;IAClB,CAAC;EACL,CAAC;EACD;AACJ;AACA;EACI5G,UAAU,CAAC2C,SAAS,CAAC+F,yBAAyB,GAAG,UAAUvH,IAAI,EAAEsE,MAAM,EAAE/E,KAAK,EAAE;IAC5E,IAAIiG,QAAQ,GAAGvI,SAAS,CAAC+C,IAAI,CAAC;IAC9B,QAAQsE,MAAM;MACV,KAAK,GAAG;QACJkB,QAAQ,CAACJ,WAAW,CAAC7F,KAAK,CAAC;QAC3B;MACJ,KAAK,GAAG;QACJiG,QAAQ,GAAGxI,SAAS,CAACgD,IAAI,EAAET,KAAK,GAAGS,IAAI,CAACkG,QAAQ,CAAC,CAAC,CAAC;QACnD;MACJ,KAAK,GAAG;MACR,KAAK,GAAG;QACJV,QAAQ,CAACmC,OAAO,CAACpI,KAAK,CAAC;QACvB;MACJ,KAAK,GAAG;MACR,KAAK,GAAG;QACJiG,QAAQ,CAACoC,QAAQ,CAACrI,KAAK,CAAC;QACxB;MACJ,KAAK,GAAG;QACJiG,QAAQ,CAACqC,UAAU,CAACtI,KAAK,CAAC;QAC1B;MACJ,KAAK,GAAG;QACJiG,QAAQ,CAACsC,UAAU,CAACvI,KAAK,CAAC;QAC1B;MACJ,KAAK,GAAG;QACJiG,QAAQ,CAACuC,eAAe,CAACxI,KAAK,CAAC;QAC/B;MACJ,KAAK,GAAG;QACJiG,QAAQ,CAACoC,QAAQ,CAACrI,KAAK,CAAC;QACxB;MACJ;QAAS;IACb;IACA,OAAOiG,QAAQ;EACnB,CAAC;EACD3G,UAAU,CAAC2C,SAAS,CAACiG,uBAAuB,GAAG,YAAY;IACvD,IAAI,CAAC1E,cAAc,CAAC,IAAI,CAAC;EAC7B,CAAC;EACD;AACJ;AACA;EACIlE,UAAU,CAAC2C,SAAS,CAACkD,kBAAkB,GAAG,UAAUT,IAAI,EAAEmM,SAAS,EAAE;IACjE,IAAIC,WAAW,GAAGpM,IAAI,CAACM,OAAO,CAAC6L,SAAS,CAAC;IACzC,IAAI3L,eAAe,GAAG,EAAE;IACxB,KAAK,IAAI6L,aAAa,GAAGF,SAAS,EAAEE,aAAa,GAAGrM,IAAI,CAACM,OAAO,CAACnD,MAAM,EAAEkP,aAAa,EAAE,EAAE;MACtF,IAAIjC,IAAI,GAAGpK,IAAI,CAACM,OAAO,CAAC+L,aAAa,CAAC;MACtC,IAAID,WAAW,CAAChN,IAAI,KAAKgL,IAAI,CAAChL,IAAI,IAAIgN,WAAW,CAAC9M,OAAO,KAAK8K,IAAI,CAAC9K,OAAO,EAAE;QACxEkB,eAAe,CAAC0K,IAAI,CAACd,IAAI,CAAC;MAC9B,CAAC,MACI;QACD;MACJ;IACJ;IACA,KAAK,IAAIiC,aAAa,GAAGF,SAAS,GAAG,CAAC,EAAEE,aAAa,IAAI,CAAC,EAAEA,aAAa,EAAE,EAAE;MACzE,IAAIjC,IAAI,GAAGpK,IAAI,CAACM,OAAO,CAAC+L,aAAa,CAAC;MACtC,IAAID,WAAW,CAAChN,IAAI,KAAKgL,IAAI,CAAChL,IAAI,IAAIgN,WAAW,CAAC9M,OAAO,KAAK8K,IAAI,CAAC9K,OAAO,EAAE;QACxEkB,eAAe,CAAC8L,OAAO,CAAClC,IAAI,CAAC;MACjC,CAAC,MACI;QACD;MACJ;IACJ;IACA,OAAO5J,eAAe;EAC1B,CAAC;EACD;AACJ;AACA;EACI5F,UAAU,CAAC2C,SAAS,CAACyL,6BAA6B,GAAG,UAAUnO,EAAE,EAAE;IAC/D,IAAIoO,sBAAsB,GAAGpO,EAAE,CAACoO,sBAAsB;MAAEf,MAAM,GAAGrN,EAAE,CAACqN,MAAM;MAAEJ,YAAY,GAAGjN,EAAE,CAACiN,YAAY;MAAEvB,UAAU,GAAG1L,EAAE,CAAC0L,UAAU;MAAEC,UAAU,GAAG3L,EAAE,CAAC2L,UAAU;MAAEH,MAAM,GAAGxL,EAAE,CAACwL,MAAM;MAAEI,MAAM,GAAG5L,EAAE,CAAC4L,MAAM;MAAEpG,MAAM,GAAGxF,EAAE,CAACwF,MAAM;MAAE0H,aAAa,GAAGlN,EAAE,CAACkN,aAAa;MAAE1L,WAAW,GAAGxB,EAAE,CAACwB,WAAW;IAC9R;IACA;IACA,IAAIkQ,WAAW,GAAG,IAAI,CAAClC,IAAI,CAACnC,MAAM,EAAEJ,YAAY,CAAC;IACjD,IAAI0E,gBAAgB,GAAGvD,sBAAsB,GACzC,EAAE,CAACwD,MAAM,CAAClG,UAAU,CAAC,CAACkG,MAAM,CAACF,WAAW,CAAC,CAACE,MAAM,CAACjG,UAAU,CAAC,GAC5D,EAAE,CAACiG,MAAM,CAACpG,MAAM,CAAC,CAACoG,MAAM,CAACF,WAAW,CAAC,CAACE,MAAM,CAAChG,MAAM,CAAC;IACxD,IAAIyC,UAAU,GAAG,IAAI,CAAChL,IAAI,CAACuK,SAAS,CAAC+D,gBAAgB,EAAE,IAAI,CAACxR,MAAM,EAAE,IAAI,CAAC8C,QAAQ,CAAC;IAClF,IAAI4O,iBAAiB,GAAG,CAAC,IAAI,CAACrQ,WAAW,IAAI,CAAC,CAAC,EAAEgE,MAAM,CAAC,IAAI,CAAC;IAC7D,IAAIsM,gBAAgB,GAAID,iBAAiB,GAAGlT,SAAS,CAAC0O,MAAM,CAAC,CAAC/K,MAAM,IAAK4K,aAAa;IACtF,IAAIoB,iBAAiB,GAAGtP,UAAU,CAAC0S,WAAW,CAAC;IAC/C,IAAI/G,YAAY,GAAG0D,UAAU,KAAK,IAAI,KACjC7M,WAAW,CAACgE,MAAM,CAAC,GAChByH,YAAY,CAAC3K,MAAM,IAAI+K,MAAM,CAAC/K,MAAM,GACpCwP,gBAAgB,CAAC;IACzB,OAAO;MACHzD,UAAU,EAAEA,UAAU;MACtBsD,gBAAgB,EAAEA,gBAAgB;MAClCD,WAAW,EAAEA,WAAW;MACxBpD,iBAAiB,EAAEA,iBAAiB;MACpC3D,YAAY,EAAEA;IAClB,CAAC;EACL,CAAC;EACD,OAAO5K,UAAU;AACrB,CAAC,CAAC,CAAE;AACJ,SAASA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}