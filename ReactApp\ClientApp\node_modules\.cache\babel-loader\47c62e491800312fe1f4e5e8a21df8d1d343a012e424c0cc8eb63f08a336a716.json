{"ast": null, "code": "import ownerDocument from './owner-document';\nvar getDocument = function (element) {\n  return ownerDocument(element).documentElement;\n};\nexport default getDocument;", "map": {"version": 3, "names": ["ownerDocument", "getDocument", "element", "documentElement"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-popup-common/dist/es/document.js"], "sourcesContent": ["import ownerDocument from './owner-document';\n\nvar getDocument = function (element) { return ownerDocument(element).documentElement; };\n\nexport default getDocument;\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,kBAAkB;AAE5C,IAAIC,WAAW,GAAG,SAAAA,CAAUC,OAAO,EAAE;EAAE,OAAOF,aAAa,CAACE,OAAO,CAAC,CAACC,eAAe;AAAE,CAAC;AAEvF,eAAeF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}