{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { DOM_KENDO_ITEM_ID_FIELD as i, DOM_KENDO_TREEVIEW_GUID_FIELD as r } from \"./utils/consts.mjs\";\nconst s = 6;\nclass h {\n  /**\n   * @param event - The event that will be analyzed.\n   */\n  constructor(t) {\n    this.event = t, this.initialized = !1, this.destItemId = \"\", this.destTreeViewGuid = \"\", this.itemId = t.itemHierarchicalIndex, this.treeViewGuid = t.target.guid;\n  }\n  /**\n   * The method which initializes the analyzer.\n   * Invoke the method before you call any other methods.\n   *\n   * @returns - The analyzer object of the `drag` event.\n   */\n  init() {\n    return this.initialized || (this.setDestimationMeta(document.elementFromPoint(this.event.clientX, this.event.clientY)), this.initialized = !0), this;\n  }\n  /**\n   * Returns `true` if dropping is allowed. Otherwise, returns `false`.\n   */\n  get isDropAllowed() {\n    return this.initialized && this.destItemId && this.destTreeViewGuid ? !`${this.destTreeViewGuid}_${this.destItemId}_`.startsWith(`${this.treeViewGuid}_${this.itemId}_`) : !1;\n  }\n  /**\n   * Returns an object which contains:\n   * * The `itemHierarchicalIndex` of the destination item (the item below the dragged item) and\n   * * The `guid` of the destination TreeView (the TreeView which renders the destination item).\n   */\n  get destinationMeta() {\n    return {\n      itemHierarchicalIndex: this.destItemId,\n      treeViewGuid: this.destTreeViewGuid\n    };\n  }\n  /**\n   * Returns the specific drop operation.\n   *\n   * @returns - The following values are returned:\n   * * `before`&mdash;Indicates that the dragged item is positioned at the beginning of the destination item.\n   * * `after`&mdash;Indicates that the dragged item is positioned at the end of the destination item.\n   * * `child`&mdash;Indicates that the dragged item is positioned in the middle of the destination item.\n   * * `undefined`&mdash;Indicates that dropping is not allowed.\n   */\n  getDropOperation() {\n    if (this.initialized && this.isDropAllowed) {\n      const {\n        top: t,\n        height: e\n      } = this.destDomNodeWithMeta.getBoundingClientRect();\n      return t + e - this.event.clientY < s ? \"after\" : this.event.clientY - t < s ? \"before\" : \"child\";\n    }\n  }\n  setDestimationMeta(t) {\n    let e = t;\n    for (; e && !e[i];) e = e.parentNode;\n    e && e[i] && (this.destDomNodeWithMeta = e, this.destItemId = e[i], this.destTreeViewGuid = e[r]);\n  }\n}\nexport { h as TreeViewDragAnalyzer };", "map": {"version": 3, "names": ["DOM_KENDO_ITEM_ID_FIELD", "i", "DOM_KENDO_TREEVIEW_GUID_FIELD", "r", "s", "h", "constructor", "t", "event", "initialized", "destItemId", "destTreeViewGuid", "itemId", "itemHierarchicalIndex", "treeViewGuid", "target", "guid", "init", "setDestimationMeta", "document", "elementFromPoint", "clientX", "clientY", "isDropAllowed", "startsWith", "destinationMeta", "getDropOperation", "top", "height", "e", "destDomNodeWithMeta", "getBoundingClientRect", "parentNode", "TreeViewDragAnalyzer"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-treeview/TreeViewDragAnalyzer.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { DOM_KENDO_ITEM_ID_FIELD as i, DOM_KENDO_TREEVIEW_GUID_FIELD as r } from \"./utils/consts.mjs\";\nconst s = 6;\nclass h {\n  /**\n   * @param event - The event that will be analyzed.\n   */\n  constructor(t) {\n    this.event = t, this.initialized = !1, this.destItemId = \"\", this.destTreeViewGuid = \"\", this.itemId = t.itemHierarchicalIndex, this.treeViewGuid = t.target.guid;\n  }\n  /**\n   * The method which initializes the analyzer.\n   * Invoke the method before you call any other methods.\n   *\n   * @returns - The analyzer object of the `drag` event.\n   */\n  init() {\n    return this.initialized || (this.setDestimationMeta(document.elementFromPoint(this.event.clientX, this.event.clientY)), this.initialized = !0), this;\n  }\n  /**\n   * Returns `true` if dropping is allowed. Otherwise, returns `false`.\n   */\n  get isDropAllowed() {\n    return this.initialized && this.destItemId && this.destTreeViewGuid ? !`${this.destTreeViewGuid}_${this.destItemId}_`.startsWith(`${this.treeViewGuid}_${this.itemId}_`) : !1;\n  }\n  /**\n   * Returns an object which contains:\n   * * The `itemHierarchicalIndex` of the destination item (the item below the dragged item) and\n   * * The `guid` of the destination TreeView (the TreeView which renders the destination item).\n   */\n  get destinationMeta() {\n    return { itemHierarchicalIndex: this.destItemId, treeViewGuid: this.destTreeViewGuid };\n  }\n  /**\n   * Returns the specific drop operation.\n   *\n   * @returns - The following values are returned:\n   * * `before`&mdash;Indicates that the dragged item is positioned at the beginning of the destination item.\n   * * `after`&mdash;Indicates that the dragged item is positioned at the end of the destination item.\n   * * `child`&mdash;Indicates that the dragged item is positioned in the middle of the destination item.\n   * * `undefined`&mdash;Indicates that dropping is not allowed.\n   */\n  getDropOperation() {\n    if (this.initialized && this.isDropAllowed) {\n      const { top: t, height: e } = this.destDomNodeWithMeta.getBoundingClientRect();\n      return t + e - this.event.clientY < s ? \"after\" : this.event.clientY - t < s ? \"before\" : \"child\";\n    }\n  }\n  setDestimationMeta(t) {\n    let e = t;\n    for (; e && !e[i]; )\n      e = e.parentNode;\n    e && e[i] && (this.destDomNodeWithMeta = e, this.destItemId = e[i], this.destTreeViewGuid = e[r]);\n  }\n}\nexport {\n  h as TreeViewDragAnalyzer\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,uBAAuB,IAAIC,CAAC,EAAEC,6BAA6B,IAAIC,CAAC,QAAQ,oBAAoB;AACrG,MAAMC,CAAC,GAAG,CAAC;AACX,MAAMC,CAAC,CAAC;EACN;AACF;AACA;EACEC,WAAWA,CAACC,CAAC,EAAE;IACb,IAAI,CAACC,KAAK,GAAGD,CAAC,EAAE,IAAI,CAACE,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAACC,UAAU,GAAG,EAAE,EAAE,IAAI,CAACC,gBAAgB,GAAG,EAAE,EAAE,IAAI,CAACC,MAAM,GAAGL,CAAC,CAACM,qBAAqB,EAAE,IAAI,CAACC,YAAY,GAAGP,CAAC,CAACQ,MAAM,CAACC,IAAI;EACnK;EACA;AACF;AACA;AACA;AACA;AACA;EACEC,IAAIA,CAAA,EAAG;IACL,OAAO,IAAI,CAACR,WAAW,KAAK,IAAI,CAACS,kBAAkB,CAACC,QAAQ,CAACC,gBAAgB,CAAC,IAAI,CAACZ,KAAK,CAACa,OAAO,EAAE,IAAI,CAACb,KAAK,CAACc,OAAO,CAAC,CAAC,EAAE,IAAI,CAACb,WAAW,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI;EACtJ;EACA;AACF;AACA;EACE,IAAIc,aAAaA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACd,WAAW,IAAI,IAAI,CAACC,UAAU,IAAI,IAAI,CAACC,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACA,gBAAgB,IAAI,IAAI,CAACD,UAAU,GAAG,CAACc,UAAU,CAAC,GAAG,IAAI,CAACV,YAAY,IAAI,IAAI,CAACF,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;EAC/K;EACA;AACF;AACA;AACA;AACA;EACE,IAAIa,eAAeA,CAAA,EAAG;IACpB,OAAO;MAAEZ,qBAAqB,EAAE,IAAI,CAACH,UAAU;MAAEI,YAAY,EAAE,IAAI,CAACH;IAAiB,CAAC;EACxF;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEe,gBAAgBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACjB,WAAW,IAAI,IAAI,CAACc,aAAa,EAAE;MAC1C,MAAM;QAAEI,GAAG,EAAEpB,CAAC;QAAEqB,MAAM,EAAEC;MAAE,CAAC,GAAG,IAAI,CAACC,mBAAmB,CAACC,qBAAqB,CAAC,CAAC;MAC9E,OAAOxB,CAAC,GAAGsB,CAAC,GAAG,IAAI,CAACrB,KAAK,CAACc,OAAO,GAAGlB,CAAC,GAAG,OAAO,GAAG,IAAI,CAACI,KAAK,CAACc,OAAO,GAAGf,CAAC,GAAGH,CAAC,GAAG,QAAQ,GAAG,OAAO;IACnG;EACF;EACAc,kBAAkBA,CAACX,CAAC,EAAE;IACpB,IAAIsB,CAAC,GAAGtB,CAAC;IACT,OAAOsB,CAAC,IAAI,CAACA,CAAC,CAAC5B,CAAC,CAAC,GACf4B,CAAC,GAAGA,CAAC,CAACG,UAAU;IAClBH,CAAC,IAAIA,CAAC,CAAC5B,CAAC,CAAC,KAAK,IAAI,CAAC6B,mBAAmB,GAAGD,CAAC,EAAE,IAAI,CAACnB,UAAU,GAAGmB,CAAC,CAAC5B,CAAC,CAAC,EAAE,IAAI,CAACU,gBAAgB,GAAGkB,CAAC,CAAC1B,CAAC,CAAC,CAAC;EACnG;AACF;AACA,SACEE,CAAC,IAAI4B,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}