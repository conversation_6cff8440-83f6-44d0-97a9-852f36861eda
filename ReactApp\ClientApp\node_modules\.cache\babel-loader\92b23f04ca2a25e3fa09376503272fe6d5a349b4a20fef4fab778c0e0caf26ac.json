{"ast": null, "code": "import { Class } from '../common';\nvar QuadRoot = function (Class) {\n  function QuadRoot() {\n    Class.call(this);\n    this.shapes = [];\n  }\n  if (Class) QuadRoot.__proto__ = Class;\n  QuadRoot.prototype = Object.create(Class && Class.prototype);\n  QuadRoot.prototype.constructor = QuadRoot;\n  QuadRoot.prototype._add = function _add(shape, bbox) {\n    this.shapes.push({\n      bbox: bbox,\n      shape: shape\n    });\n    shape._quadNode = this;\n  };\n  QuadRoot.prototype.pointShapes = function pointShapes(point) {\n    var shapes = this.shapes;\n    var length = shapes.length;\n    var result = [];\n    for (var idx = 0; idx < length; idx++) {\n      if (shapes[idx].bbox.containsPoint(point)) {\n        result.push(shapes[idx].shape);\n      }\n    }\n    return result;\n  };\n  QuadRoot.prototype.insert = function insert(shape, bbox) {\n    this._add(shape, bbox);\n  };\n  QuadRoot.prototype.remove = function remove(shape) {\n    var shapes = this.shapes;\n    var length = shapes.length;\n    for (var idx = 0; idx < length; idx++) {\n      if (shapes[idx].shape === shape) {\n        shapes.splice(idx, 1);\n        break;\n      }\n    }\n  };\n  return QuadRoot;\n}(Class);\nexport default QuadRoot;", "map": {"version": 3, "names": ["Class", "QuadRoot", "call", "shapes", "__proto__", "prototype", "Object", "create", "constructor", "_add", "shape", "bbox", "push", "_quadNode", "pointShapes", "point", "length", "result", "idx", "containsPoint", "insert", "remove", "splice"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/search/quad-root.js"], "sourcesContent": ["import { Class } from '../common';\n\nvar QuadRoot = (function (Class) {\n    function QuadRoot() {\n        Class.call(this);\n\n        this.shapes = [];\n    }\n\n    if ( Class ) QuadRoot.__proto__ = Class;\n    QuadRoot.prototype = Object.create( Class && Class.prototype );\n    QuadRoot.prototype.constructor = QuadRoot;\n\n    QuadRoot.prototype._add = function _add (shape, bbox) {\n        this.shapes.push({\n            bbox: bbox,\n            shape: shape\n        });\n        shape._quadNode = this;\n    };\n\n    QuadRoot.prototype.pointShapes = function pointShapes (point) {\n        var shapes = this.shapes;\n        var length = shapes.length;\n        var result = [];\n        for (var idx = 0; idx < length; idx++) {\n            if (shapes[idx].bbox.containsPoint(point)) {\n                result.push(shapes[idx].shape);\n            }\n        }\n        return result;\n    };\n\n    QuadRoot.prototype.insert = function insert (shape, bbox) {\n        this._add(shape, bbox);\n    };\n\n    QuadRoot.prototype.remove = function remove (shape) {\n        var shapes = this.shapes;\n        var length = shapes.length;\n\n        for (var idx = 0; idx < length; idx++) {\n            if (shapes[idx].shape === shape) {\n                shapes.splice(idx, 1);\n                break;\n            }\n        }\n    };\n\n    return QuadRoot;\n}(Class));\n\nexport default QuadRoot;"], "mappings": "AAAA,SAASA,KAAK,QAAQ,WAAW;AAEjC,IAAIC,QAAQ,GAAI,UAAUD,KAAK,EAAE;EAC7B,SAASC,QAAQA,CAAA,EAAG;IAChBD,KAAK,CAACE,IAAI,CAAC,IAAI,CAAC;IAEhB,IAAI,CAACC,MAAM,GAAG,EAAE;EACpB;EAEA,IAAKH,KAAK,EAAGC,QAAQ,CAACG,SAAS,GAAGJ,KAAK;EACvCC,QAAQ,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAEP,KAAK,IAAIA,KAAK,CAACK,SAAU,CAAC;EAC9DJ,QAAQ,CAACI,SAAS,CAACG,WAAW,GAAGP,QAAQ;EAEzCA,QAAQ,CAACI,SAAS,CAACI,IAAI,GAAG,SAASA,IAAIA,CAAEC,KAAK,EAAEC,IAAI,EAAE;IAClD,IAAI,CAACR,MAAM,CAACS,IAAI,CAAC;MACbD,IAAI,EAAEA,IAAI;MACVD,KAAK,EAAEA;IACX,CAAC,CAAC;IACFA,KAAK,CAACG,SAAS,GAAG,IAAI;EAC1B,CAAC;EAEDZ,QAAQ,CAACI,SAAS,CAACS,WAAW,GAAG,SAASA,WAAWA,CAAEC,KAAK,EAAE;IAC1D,IAAIZ,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB,IAAIa,MAAM,GAAGb,MAAM,CAACa,MAAM;IAC1B,IAAIC,MAAM,GAAG,EAAE;IACf,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGF,MAAM,EAAEE,GAAG,EAAE,EAAE;MACnC,IAAIf,MAAM,CAACe,GAAG,CAAC,CAACP,IAAI,CAACQ,aAAa,CAACJ,KAAK,CAAC,EAAE;QACvCE,MAAM,CAACL,IAAI,CAACT,MAAM,CAACe,GAAG,CAAC,CAACR,KAAK,CAAC;MAClC;IACJ;IACA,OAAOO,MAAM;EACjB,CAAC;EAEDhB,QAAQ,CAACI,SAAS,CAACe,MAAM,GAAG,SAASA,MAAMA,CAAEV,KAAK,EAAEC,IAAI,EAAE;IACtD,IAAI,CAACF,IAAI,CAACC,KAAK,EAAEC,IAAI,CAAC;EAC1B,CAAC;EAEDV,QAAQ,CAACI,SAAS,CAACgB,MAAM,GAAG,SAASA,MAAMA,CAAEX,KAAK,EAAE;IAChD,IAAIP,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB,IAAIa,MAAM,GAAGb,MAAM,CAACa,MAAM;IAE1B,KAAK,IAAIE,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGF,MAAM,EAAEE,GAAG,EAAE,EAAE;MACnC,IAAIf,MAAM,CAACe,GAAG,CAAC,CAACR,KAAK,KAAKA,KAAK,EAAE;QAC7BP,MAAM,CAACmB,MAAM,CAACJ,GAAG,EAAE,CAAC,CAAC;QACrB;MACJ;IACJ;EACJ,CAAC;EAED,OAAOjB,QAAQ;AACnB,CAAC,CAACD,KAAK,CAAE;AAET,eAAeC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}