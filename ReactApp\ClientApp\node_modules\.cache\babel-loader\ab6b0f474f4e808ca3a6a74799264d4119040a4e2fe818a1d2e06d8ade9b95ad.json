{"ast": null, "code": "import { defined } from '../util';\nvar GRADIENT = \"Gradient\";\nvar paintable = function (TBase) {\n  return function (TBase) {\n    function anonymous() {\n      TBase.apply(this, arguments);\n    }\n    if (TBase) anonymous.__proto__ = TBase;\n    anonymous.prototype = Object.create(TBase && TBase.prototype);\n    anonymous.prototype.constructor = anonymous;\n    anonymous.prototype.fill = function fill(color, opacity) {\n      var options = this.options;\n      if (defined(color)) {\n        if (color && color.nodeType !== GRADIENT) {\n          var newFill = {\n            color: color\n          };\n          if (defined(opacity)) {\n            newFill.opacity = opacity;\n          }\n          options.set(\"fill\", newFill);\n        } else {\n          options.set(\"fill\", color);\n        }\n        return this;\n      }\n      return options.get(\"fill\");\n    };\n    anonymous.prototype.stroke = function stroke(color, width, opacity) {\n      if (defined(color)) {\n        this.options.set(\"stroke.color\", color);\n        if (defined(width)) {\n          this.options.set(\"stroke.width\", width);\n        }\n        if (defined(opacity)) {\n          this.options.set(\"stroke.opacity\", opacity);\n        }\n        return this;\n      }\n      return this.options.get(\"stroke\");\n    };\n    return anonymous;\n  }(TBase);\n};\nexport default paintable;", "map": {"version": 3, "names": ["defined", "GRADIENT", "paintable", "TBase", "anonymous", "apply", "arguments", "__proto__", "prototype", "Object", "create", "constructor", "fill", "color", "opacity", "options", "nodeType", "newFill", "set", "get", "stroke", "width"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/mixins/paintable.js"], "sourcesContent": ["import { defined } from '../util';\n\nvar GRADIENT = \"Gradient\";\n\nvar paintable = function (TBase) { return (\n    (function (TBase) {\n        function anonymous () {\n            TBase.apply(this, arguments);\n        }\n\n        if ( TBase ) anonymous.__proto__ = TBase;\n        anonymous.prototype = Object.create( TBase && TBase.prototype );\n        anonymous.prototype.constructor = anonymous;\n\n        anonymous.prototype.fill = function fill (color, opacity) {\n            var options = this.options;\n\n            if (defined(color)) {\n                if (color && color.nodeType !== GRADIENT) {\n                    var newFill = {\n                        color: color\n                    };\n                    if (defined(opacity)) {\n                        newFill.opacity = opacity;\n                    }\n                    options.set(\"fill\", newFill);\n                } else {\n                    options.set(\"fill\", color);\n                }\n\n                return this;\n            }\n\n            return options.get(\"fill\");\n        };\n\n        anonymous.prototype.stroke = function stroke (color, width, opacity) {\n            if (defined(color)) {\n                this.options.set(\"stroke.color\", color);\n\n                if (defined(width)) {\n                    this.options.set(\"stroke.width\", width);\n                }\n\n                if (defined(opacity)) {\n                    this.options.set(\"stroke.opacity\", opacity);\n                }\n\n                return this;\n            }\n\n            return this.options.get(\"stroke\");\n        };\n\n        return anonymous;\n    }(TBase))\n); };\n\nexport default paintable;\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,SAAS;AAEjC,IAAIC,QAAQ,GAAG,UAAU;AAEzB,IAAIC,SAAS,GAAG,SAAAA,CAAUC,KAAK,EAAE;EAAE,OAC9B,UAAUA,KAAK,EAAE;IACd,SAASC,SAASA,CAAA,EAAI;MAClBD,KAAK,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAChC;IAEA,IAAKH,KAAK,EAAGC,SAAS,CAACG,SAAS,GAAGJ,KAAK;IACxCC,SAAS,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAEP,KAAK,IAAIA,KAAK,CAACK,SAAU,CAAC;IAC/DJ,SAAS,CAACI,SAAS,CAACG,WAAW,GAAGP,SAAS;IAE3CA,SAAS,CAACI,SAAS,CAACI,IAAI,GAAG,SAASA,IAAIA,CAAEC,KAAK,EAAEC,OAAO,EAAE;MACtD,IAAIC,OAAO,GAAG,IAAI,CAACA,OAAO;MAE1B,IAAIf,OAAO,CAACa,KAAK,CAAC,EAAE;QAChB,IAAIA,KAAK,IAAIA,KAAK,CAACG,QAAQ,KAAKf,QAAQ,EAAE;UACtC,IAAIgB,OAAO,GAAG;YACVJ,KAAK,EAAEA;UACX,CAAC;UACD,IAAIb,OAAO,CAACc,OAAO,CAAC,EAAE;YAClBG,OAAO,CAACH,OAAO,GAAGA,OAAO;UAC7B;UACAC,OAAO,CAACG,GAAG,CAAC,MAAM,EAAED,OAAO,CAAC;QAChC,CAAC,MAAM;UACHF,OAAO,CAACG,GAAG,CAAC,MAAM,EAAEL,KAAK,CAAC;QAC9B;QAEA,OAAO,IAAI;MACf;MAEA,OAAOE,OAAO,CAACI,GAAG,CAAC,MAAM,CAAC;IAC9B,CAAC;IAEDf,SAAS,CAACI,SAAS,CAACY,MAAM,GAAG,SAASA,MAAMA,CAAEP,KAAK,EAAEQ,KAAK,EAAEP,OAAO,EAAE;MACjE,IAAId,OAAO,CAACa,KAAK,CAAC,EAAE;QAChB,IAAI,CAACE,OAAO,CAACG,GAAG,CAAC,cAAc,EAAEL,KAAK,CAAC;QAEvC,IAAIb,OAAO,CAACqB,KAAK,CAAC,EAAE;UAChB,IAAI,CAACN,OAAO,CAACG,GAAG,CAAC,cAAc,EAAEG,KAAK,CAAC;QAC3C;QAEA,IAAIrB,OAAO,CAACc,OAAO,CAAC,EAAE;UAClB,IAAI,CAACC,OAAO,CAACG,GAAG,CAAC,gBAAgB,EAAEJ,OAAO,CAAC;QAC/C;QAEA,OAAO,IAAI;MACf;MAEA,OAAO,IAAI,CAACC,OAAO,CAACI,GAAG,CAAC,QAAQ,CAAC;IACrC,CAAC;IAED,OAAOf,SAAS;EACpB,CAAC,CAACD,KAAK,CAAC;AACT,CAAC;AAEJ,eAAeD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}