{"ast": null, "code": "import { createDate } from './create-date';\n/**\n * A function which returns the passed date with a midnight time portion.\n *\n * @param date - The initial date.\n * @returns - The date with a midnight time portion.\n *\n * @example\n * ```ts-no-run\n * getDate(new Date(2016, 0, 15, 14, 30, 30)); // 2016-01-15 00:00:00\n * ```\n */\nexport var getDate = function (date) {\n  return createDate(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0);\n};", "map": {"version": 3, "names": ["createDate", "getDate", "date", "getFullYear", "getMonth"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/get-date.js"], "sourcesContent": ["import { createDate } from './create-date';\n/**\n * A function which returns the passed date with a midnight time portion.\n *\n * @param date - The initial date.\n * @returns - The date with a midnight time portion.\n *\n * @example\n * ```ts-no-run\n * getDate(new Date(2016, 0, 15, 14, 30, 30)); // 2016-01-15 00:00:00\n * ```\n */\nexport var getDate = function (date) {\n    return createDate(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0);\n};\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,OAAO,GAAG,SAAAA,CAAUC,IAAI,EAAE;EACjC,OAAOF,UAAU,CAACE,IAAI,CAACC,WAAW,CAAC,CAAC,EAAED,IAAI,CAACE,QAAQ,CAAC,CAAC,EAAEF,IAAI,CAACD,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACnF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}