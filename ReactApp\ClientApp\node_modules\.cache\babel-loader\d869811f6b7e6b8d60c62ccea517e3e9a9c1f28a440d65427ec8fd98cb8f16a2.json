{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst n = l => t => Math.floor(t / l),\n  o = l => t => t * l;\nclass s {\n  constructor(t) {\n    this.dom = t, this.divideByMagnitude = null, this.powerByMagnitude = null, this.navigator = null, this.view = null, this.monthScrolled = !1, this.navScrolled = !1;\n  }\n  configure(t) {\n    const i = Math.max(this.dom.viewHeight(t) / this.dom.navigationItemHeight, 1);\n    this.divideByMagnitude = n(i), this.powerByMagnitude = o(i);\n  }\n  sync(t, i, e) {\n    if (!(!t || !i)) {\n      if (this.navigator = t, this.view = i, e.target === this.navigator.element) {\n        if (this.monthScrolled) {\n          this.monthScrolled = !1;\n          return;\n        }\n        this.navScrolled = !0, this.scrollSiblingOf(this.navigator.element);\n      }\n      if (e.target === this.view.element) {\n        if (this.navScrolled) {\n          this.navScrolled = !1;\n          return;\n        }\n        this.monthScrolled = !0, this.scrollSiblingOf(this.view.element);\n      }\n    }\n  }\n  scrollSiblingOf(t) {\n    const i = this.siblingComponent(t),\n      e = this.calculateScroll(i, t.scrollTop);\n    i.scrollTo(e);\n  }\n  siblingComponent(t) {\n    return this.navigator.element === t ? this.view : this.navigator;\n  }\n  calculateScroll(t, i) {\n    const e = t === this.navigator ? this.divideByMagnitude : this.powerByMagnitude;\n    return e ? e(i) : 0;\n  }\n}\nexport { s as ScrollSyncService };", "map": {"version": 3, "names": ["n", "l", "t", "Math", "floor", "o", "s", "constructor", "dom", "divideByMagnitude", "powerByMagnitude", "navigator", "view", "monthScrolled", "navScrolled", "configure", "i", "max", "viewHeight", "navigationItemHeight", "sync", "e", "target", "element", "scrollSiblingOf", "siblingComponent", "calculateScroll", "scrollTop", "scrollTo", "ScrollSyncService"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dateinputs/calendar/services/ScrollSyncService.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst n = (l) => (t) => Math.floor(t / l), o = (l) => (t) => t * l;\nclass s {\n  constructor(t) {\n    this.dom = t, this.divideByMagnitude = null, this.powerByMagnitude = null, this.navigator = null, this.view = null, this.monthScrolled = !1, this.navScrolled = !1;\n  }\n  configure(t) {\n    const i = Math.max(this.dom.viewHeight(t) / this.dom.navigationItemHeight, 1);\n    this.divideByMagnitude = n(i), this.powerByMagnitude = o(i);\n  }\n  sync(t, i, e) {\n    if (!(!t || !i)) {\n      if (this.navigator = t, this.view = i, e.target === this.navigator.element) {\n        if (this.monthScrolled) {\n          this.monthScrolled = !1;\n          return;\n        }\n        this.navScrolled = !0, this.scrollSiblingOf(this.navigator.element);\n      }\n      if (e.target === this.view.element) {\n        if (this.navScrolled) {\n          this.navScrolled = !1;\n          return;\n        }\n        this.monthScrolled = !0, this.scrollSiblingOf(this.view.element);\n      }\n    }\n  }\n  scrollSiblingOf(t) {\n    const i = this.siblingComponent(t), e = this.calculateScroll(i, t.scrollTop);\n    i.scrollTo(e);\n  }\n  siblingComponent(t) {\n    return this.navigator.element === t ? this.view : this.navigator;\n  }\n  calculateScroll(t, i) {\n    const e = t === this.navigator ? this.divideByMagnitude : this.powerByMagnitude;\n    return e ? e(i) : 0;\n  }\n}\nexport {\n  s as ScrollSyncService\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAIC,CAAC,IAAMC,CAAC,IAAKC,IAAI,CAACC,KAAK,CAACF,CAAC,GAAGD,CAAC,CAAC;EAAEI,CAAC,GAAIJ,CAAC,IAAMC,CAAC,IAAKA,CAAC,GAAGD,CAAC;AAClE,MAAMK,CAAC,CAAC;EACNC,WAAWA,CAACL,CAAC,EAAE;IACb,IAAI,CAACM,GAAG,GAAGN,CAAC,EAAE,IAAI,CAACO,iBAAiB,GAAG,IAAI,EAAE,IAAI,CAACC,gBAAgB,GAAG,IAAI,EAAE,IAAI,CAACC,SAAS,GAAG,IAAI,EAAE,IAAI,CAACC,IAAI,GAAG,IAAI,EAAE,IAAI,CAACC,aAAa,GAAG,CAAC,CAAC,EAAE,IAAI,CAACC,WAAW,GAAG,CAAC,CAAC;EACpK;EACAC,SAASA,CAACb,CAAC,EAAE;IACX,MAAMc,CAAC,GAAGb,IAAI,CAACc,GAAG,CAAC,IAAI,CAACT,GAAG,CAACU,UAAU,CAAChB,CAAC,CAAC,GAAG,IAAI,CAACM,GAAG,CAACW,oBAAoB,EAAE,CAAC,CAAC;IAC7E,IAAI,CAACV,iBAAiB,GAAGT,CAAC,CAACgB,CAAC,CAAC,EAAE,IAAI,CAACN,gBAAgB,GAAGL,CAAC,CAACW,CAAC,CAAC;EAC7D;EACAI,IAAIA,CAAClB,CAAC,EAAEc,CAAC,EAAEK,CAAC,EAAE;IACZ,IAAI,EAAE,CAACnB,CAAC,IAAI,CAACc,CAAC,CAAC,EAAE;MACf,IAAI,IAAI,CAACL,SAAS,GAAGT,CAAC,EAAE,IAAI,CAACU,IAAI,GAAGI,CAAC,EAAEK,CAAC,CAACC,MAAM,KAAK,IAAI,CAACX,SAAS,CAACY,OAAO,EAAE;QAC1E,IAAI,IAAI,CAACV,aAAa,EAAE;UACtB,IAAI,CAACA,aAAa,GAAG,CAAC,CAAC;UACvB;QACF;QACA,IAAI,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAACU,eAAe,CAAC,IAAI,CAACb,SAAS,CAACY,OAAO,CAAC;MACrE;MACA,IAAIF,CAAC,CAACC,MAAM,KAAK,IAAI,CAACV,IAAI,CAACW,OAAO,EAAE;QAClC,IAAI,IAAI,CAACT,WAAW,EAAE;UACpB,IAAI,CAACA,WAAW,GAAG,CAAC,CAAC;UACrB;QACF;QACA,IAAI,CAACD,aAAa,GAAG,CAAC,CAAC,EAAE,IAAI,CAACW,eAAe,CAAC,IAAI,CAACZ,IAAI,CAACW,OAAO,CAAC;MAClE;IACF;EACF;EACAC,eAAeA,CAACtB,CAAC,EAAE;IACjB,MAAMc,CAAC,GAAG,IAAI,CAACS,gBAAgB,CAACvB,CAAC,CAAC;MAAEmB,CAAC,GAAG,IAAI,CAACK,eAAe,CAACV,CAAC,EAAEd,CAAC,CAACyB,SAAS,CAAC;IAC5EX,CAAC,CAACY,QAAQ,CAACP,CAAC,CAAC;EACf;EACAI,gBAAgBA,CAACvB,CAAC,EAAE;IAClB,OAAO,IAAI,CAACS,SAAS,CAACY,OAAO,KAAKrB,CAAC,GAAG,IAAI,CAACU,IAAI,GAAG,IAAI,CAACD,SAAS;EAClE;EACAe,eAAeA,CAACxB,CAAC,EAAEc,CAAC,EAAE;IACpB,MAAMK,CAAC,GAAGnB,CAAC,KAAK,IAAI,CAACS,SAAS,GAAG,IAAI,CAACF,iBAAiB,GAAG,IAAI,CAACC,gBAAgB;IAC/E,OAAOW,CAAC,GAAGA,CAAC,CAACL,CAAC,CAAC,GAAG,CAAC;EACrB;AACF;AACA,SACEV,CAAC,IAAIuB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}