{"ast": null, "code": "/**\n * this is a set which automatically forgets\n * a given entry when a new entry is set and the ttl\n * of the old one is over\n */\nvar ObliviousSet = /** @class */function () {\n  function ObliviousSet(ttl) {\n    this.ttl = ttl;\n    this.map = new Map();\n    /**\n     * Creating calls to setTimeout() is expensive,\n     * so we only do that if there is not timeout already open.\n     */\n    this._to = false;\n  }\n  ObliviousSet.prototype.has = function (value) {\n    return this.map.has(value);\n  };\n  ObliviousSet.prototype.add = function (value) {\n    var _this = this;\n    this.map.set(value, now());\n    /**\n     * When a new value is added,\n     * start the cleanup at the next tick\n     * to not block the cpu for more important stuff\n     * that might happen.\n     */\n    if (!this._to) {\n      this._to = true;\n      setTimeout(function () {\n        _this._to = false;\n        removeTooOldValues(_this);\n      }, 0);\n    }\n  };\n  ObliviousSet.prototype.clear = function () {\n    this.map.clear();\n  };\n  return ObliviousSet;\n}();\nexport { ObliviousSet };\n/**\n * Removes all entries from the set\n * where the TTL has expired\n */\nexport function removeTooOldValues(obliviousSet) {\n  var olderThen = now() - obliviousSet.ttl;\n  var iterator = obliviousSet.map[Symbol.iterator]();\n  /**\n   * Because we can assume the new values are added at the bottom,\n   * we start from the top and stop as soon as we reach a non-too-old value.\n   */\n  while (true) {\n    var next = iterator.next().value;\n    if (!next) {\n      return; // no more elements\n    }\n    var value = next[0];\n    var time = next[1];\n    if (time < olderThen) {\n      obliviousSet.map.delete(value);\n    } else {\n      // We reached a value that is not old enough\n      return;\n    }\n  }\n}\nexport function now() {\n  return new Date().getTime();\n}", "map": {"version": 3, "names": ["ObliviousSet", "ttl", "map", "Map", "_to", "prototype", "has", "value", "add", "_this", "set", "now", "setTimeout", "removeTooOldValues", "clear", "obliviousSet", "<PERSON><PERSON><PERSON>", "iterator", "Symbol", "next", "time", "delete", "Date", "getTime"], "sources": ["D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\node_modules\\oblivious-set\\src\\index.ts"], "sourcesContent": ["\n/**\n * this is a set which automatically forgets\n * a given entry when a new entry is set and the ttl\n * of the old one is over\n */\nexport class ObliviousSet<T = any> {\n    public readonly map = new Map();\n\n    /**\n     * Creating calls to setTimeout() is expensive,\n     * so we only do that if there is not timeout already open.\n     */\n    public _to: boolean = false;\n    constructor(\n        public readonly ttl: number\n    ) { }\n\n    has(value: T): boolean {\n        return this.map.has(value);\n    }\n\n    add(value: T): void {\n        this.map.set(value, now());\n\n        /**\n         * When a new value is added,\n         * start the cleanup at the next tick\n         * to not block the cpu for more important stuff\n         * that might happen.\n         */\n        if (!this._to) {\n            this._to = true;\n            setTimeout(() => {\n                this._to = false;\n                removeTooOldValues(this);\n            }, 0);\n        }\n    }\n\n    clear() {\n        this.map.clear();\n    }\n}\n\n\n/**\n * Removes all entries from the set\n * where the TTL has expired\n */\nexport function removeTooOldValues(\n    obliviousSet: ObliviousSet\n) {\n    const olderThen = now() - obliviousSet.ttl;\n    const iterator = obliviousSet.map[Symbol.iterator]();\n\n    /**\n     * Because we can assume the new values are added at the bottom,\n     * we start from the top and stop as soon as we reach a non-too-old value.\n     */\n    while (true) {\n\n        const next = iterator.next().value;\n\n        if (!next) {\n            return; // no more elements\n        }\n        const value = next[0];\n        const time = next[1];\n        if (time < olderThen) {\n            obliviousSet.map.delete(value);\n        } else {\n            // We reached a value that is not old enough\n            return;\n        }\n    }\n}\n\nexport function now(): number {\n    return new Date().getTime();\n}\n\n\n"], "mappings": "AACA;;;;;AAKA,IAAAA,YAAA;EAQI,SAAAA,aACoBC,GAAW;IAAX,KAAAA,GAAG,GAAHA,GAAG;IARP,KAAAC,GAAG,GAAG,IAAIC,GAAG,EAAE;IAE/B;;;;IAIO,KAAAC,GAAG,GAAY,KAAK;EAGvB;EAEJJ,YAAA,CAAAK,SAAA,CAAAC,GAAG,GAAH,UAAIC,KAAQ;IACR,OAAO,IAAI,CAACL,GAAG,CAACI,GAAG,CAACC,KAAK,CAAC;EAC9B,CAAC;EAEDP,YAAA,CAAAK,SAAA,CAAAG,GAAG,GAAH,UAAID,KAAQ;IAAZ,IAAAE,KAAA;IACI,IAAI,CAACP,GAAG,CAACQ,GAAG,CAACH,KAAK,EAAEI,GAAG,EAAE,CAAC;IAE1B;;;;;;IAMA,IAAI,CAAC,IAAI,CAACP,GAAG,EAAE;MACX,IAAI,CAACA,GAAG,GAAG,IAAI;MACfQ,UAAU,CAAC;QACPH,KAAI,CAACL,GAAG,GAAG,KAAK;QAChBS,kBAAkB,CAACJ,KAAI,CAAC;MAC5B,CAAC,EAAE,CAAC,CAAC;;EAEb,CAAC;EAEDT,YAAA,CAAAK,SAAA,CAAAS,KAAK,GAAL;IACI,IAAI,CAACZ,GAAG,CAACY,KAAK,EAAE;EACpB,CAAC;EACL,OAAAd,YAAC;AAAD,CAAC,CArCD;;AAwCA;;;;AAIA,OAAM,SAAUa,kBAAkBA,CAC9BE,YAA0B;EAE1B,IAAMC,SAAS,GAAGL,GAAG,EAAE,GAAGI,YAAY,CAACd,GAAG;EAC1C,IAAMgB,QAAQ,GAAGF,YAAY,CAACb,GAAG,CAACgB,MAAM,CAACD,QAAQ,CAAC,EAAE;EAEpD;;;;EAIA,OAAO,IAAI,EAAE;IAET,IAAME,IAAI,GAAGF,QAAQ,CAACE,IAAI,EAAE,CAACZ,KAAK;IAElC,IAAI,CAACY,IAAI,EAAE;MACP,OAAO,CAAC;;IAEZ,IAAMZ,KAAK,GAAGY,IAAI,CAAC,CAAC,CAAC;IACrB,IAAMC,IAAI,GAAGD,IAAI,CAAC,CAAC,CAAC;IACpB,IAAIC,IAAI,GAAGJ,SAAS,EAAE;MAClBD,YAAY,CAACb,GAAG,CAACmB,MAAM,CAACd,KAAK,CAAC;KACjC,MAAM;MACH;MACA;;;AAGZ;AAEA,OAAM,SAAUI,GAAGA,CAAA;EACf,OAAO,IAAIW,IAAI,EAAE,CAACC,OAAO,EAAE;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}