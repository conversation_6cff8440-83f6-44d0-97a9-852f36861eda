import { createApi } from '@reduxjs/toolkit/query/react';
import { baseQueryWithReAuth } from './interceptorsSlice';
import { OperationalServiceTypes } from '@iris/discovery.fe.client';
import config from '@app/utils/config';
import { AcceptTncResponse, EvaluateTncResponse, TncTags } from '@app/types/tncTypes';
import httpVerbs from '@app/utils/http/httpVerbs';

export const waiting = (ms: number): Promise<void> => {
    return new Promise((resolve) => setTimeout(resolve, ms));
};


export const apiSlice = createApi({
    reducerPath: '/tnc',
    baseQuery: baseQueryWithReAuth,
    endpoints: (builder) => ({
        evaluateTnc: builder.query<EvaluateTncResponse, void>({
            query: () => ({
                url: `${config.api[OperationalServiceTypes.UserService].evaluateTnc}`,
            }),
        }),
        acceptTnc: builder.mutation<AcceptTncResponse, { termsAndConditionsId: number, triggerType: string }>({
            query: ({ termsAndConditionsId, triggerType }) => ({
                url: `${config.api[OperationalServiceTypes.UserService].acceptTnc}`,
                method: httpVerbs.POST,
                body: { termsAndConditionsId, triggerType },
            }),

        }),
    })
});

export const { useEvaluateTncQuery, useAcceptTncMutation } = apiSlice;
