{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as t from \"react\";\nimport e from \"prop-types\";\nconst n = class n extends t.Component {\n  /**\n   * @hidden\n   */\n  render() {\n    return null;\n  }\n};\nn.propTypes = {\n  disabled: e.bool,\n  contentClassName: e.string,\n  children: e.oneOfType([e.element, e.node]),\n  title: e.oneOfType([e.string, e.element, e.node])\n};\nlet o = n;\nexport { o as TabStripTab };", "map": {"version": 3, "names": ["t", "e", "n", "Component", "render", "propTypes", "disabled", "bool", "contentClassName", "string", "children", "oneOfType", "element", "node", "title", "o", "TabStripTab"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/tabstrip/TabStripTab.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as t from \"react\";\nimport e from \"prop-types\";\nconst n = class n extends t.Component {\n  /**\n   * @hidden\n   */\n  render() {\n    return null;\n  }\n};\nn.propTypes = {\n  disabled: e.bool,\n  contentClassName: e.string,\n  children: e.oneOfType([e.element, e.node]),\n  title: e.oneOfType([e.string, e.element, e.node])\n};\nlet o = n;\nexport {\n  o as TabStripTab\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,MAAMC,CAAC,GAAG,MAAMA,CAAC,SAASF,CAAC,CAACG,SAAS,CAAC;EACpC;AACF;AACA;EACEC,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI;EACb;AACF,CAAC;AACDF,CAAC,CAACG,SAAS,GAAG;EACZC,QAAQ,EAAEL,CAAC,CAACM,IAAI;EAChBC,gBAAgB,EAAEP,CAAC,CAACQ,MAAM;EAC1BC,QAAQ,EAAET,CAAC,CAACU,SAAS,CAAC,CAACV,CAAC,CAACW,OAAO,EAAEX,CAAC,CAACY,IAAI,CAAC,CAAC;EAC1CC,KAAK,EAAEb,CAAC,CAACU,SAAS,CAAC,CAACV,CAAC,CAACQ,MAAM,EAAER,CAAC,CAACW,OAAO,EAAEX,CAAC,CAACY,IAAI,CAAC;AAClD,CAAC;AACD,IAAIE,CAAC,GAAGb,CAAC;AACT,SACEa,CAAC,IAAIC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}