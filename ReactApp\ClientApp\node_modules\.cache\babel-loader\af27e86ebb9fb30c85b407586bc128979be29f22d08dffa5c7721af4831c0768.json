{"ast": null, "code": "import RootNode from './root-node';\nimport { Group } from '../shapes';\nimport { transform } from '../geometry';\nimport { SVG_NS } from './constants';\nexport default function exportGroup(group) {\n  var root = new RootNode({\n    skipBaseHref: true\n  });\n  var bbox = group.clippedBBox();\n  var rootGroup = group;\n  if (bbox) {\n    var origin = bbox.getOrigin();\n    var exportRoot = new Group();\n    exportRoot.transform(transform().translate(-origin.x, -origin.y));\n    exportRoot.children.push(group);\n    rootGroup = exportRoot;\n  }\n  root.load([rootGroup]);\n  var svg = \"<?xml version='1.0' ?><svg xmlns='\" + SVG_NS + \"' xmlns:xlink='http://www.w3.org/1999/xlink' version='1.1'>\" + root.render() + \"</svg>\";\n  root.destroy();\n  return svg;\n}", "map": {"version": 3, "names": ["RootNode", "Group", "transform", "SVG_NS", "exportGroup", "group", "root", "skipBaseHref", "bbox", "clippedBBox", "rootGroup", "origin", "<PERSON><PERSON><PERSON><PERSON>", "exportRoot", "translate", "x", "y", "children", "push", "load", "svg", "render", "destroy"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/svg/export-group.js"], "sourcesContent": ["import RootNode from './root-node';\nimport { Group } from '../shapes';\nimport { transform } from '../geometry';\nimport { SVG_NS } from './constants';\n\nexport default function exportGroup(group) {\n    var root = new RootNode({\n        skipBaseHref: true\n    });\n    var bbox = group.clippedBBox();\n    var rootGroup = group;\n\n    if (bbox) {\n        var origin = bbox.getOrigin();\n        var exportRoot = new Group();\n        exportRoot.transform(transform().translate(-origin.x, -origin.y));\n        exportRoot.children.push(group);\n        rootGroup = exportRoot;\n    }\n\n    root.load([ rootGroup ]);\n\n    var svg = \"<?xml version='1.0' ?><svg xmlns='\" + SVG_NS + \"' xmlns:xlink='http://www.w3.org/1999/xlink' version='1.1'>\" + (root.render()) + \"</svg>\";\n\n    root.destroy();\n\n    return svg;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,aAAa;AAClC,SAASC,KAAK,QAAQ,WAAW;AACjC,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,MAAM,QAAQ,aAAa;AAEpC,eAAe,SAASC,WAAWA,CAACC,KAAK,EAAE;EACvC,IAAIC,IAAI,GAAG,IAAIN,QAAQ,CAAC;IACpBO,YAAY,EAAE;EAClB,CAAC,CAAC;EACF,IAAIC,IAAI,GAAGH,KAAK,CAACI,WAAW,CAAC,CAAC;EAC9B,IAAIC,SAAS,GAAGL,KAAK;EAErB,IAAIG,IAAI,EAAE;IACN,IAAIG,MAAM,GAAGH,IAAI,CAACI,SAAS,CAAC,CAAC;IAC7B,IAAIC,UAAU,GAAG,IAAIZ,KAAK,CAAC,CAAC;IAC5BY,UAAU,CAACX,SAAS,CAACA,SAAS,CAAC,CAAC,CAACY,SAAS,CAAC,CAACH,MAAM,CAACI,CAAC,EAAE,CAACJ,MAAM,CAACK,CAAC,CAAC,CAAC;IACjEH,UAAU,CAACI,QAAQ,CAACC,IAAI,CAACb,KAAK,CAAC;IAC/BK,SAAS,GAAGG,UAAU;EAC1B;EAEAP,IAAI,CAACa,IAAI,CAAC,CAAET,SAAS,CAAE,CAAC;EAExB,IAAIU,GAAG,GAAG,oCAAoC,GAAGjB,MAAM,GAAG,6DAA6D,GAAIG,IAAI,CAACe,MAAM,CAAC,CAAE,GAAG,QAAQ;EAEpJf,IAAI,CAACgB,OAAO,CAAC,CAAC;EAEd,OAAOF,GAAG;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}