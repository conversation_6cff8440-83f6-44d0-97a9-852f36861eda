import { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAcceptTncMutation, useEvaluateTncQuery } from '@app/api/tncApiSlice';
import { useAppSelector } from '@app/hooks/useAppSelector';
import { errorNotification } from '@app/utils/antNotifications';

export const useTermsAndConditions = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const pdfViewerRef = useRef<any>(null);
    const [pdfDocument, setPdfDocument] = useState<any>(null);
    const tncDocument = useAppSelector((state) => state.app.tnc);

    const redirectPath = location.state?.from || '/';

    const { refetch: tncRefetch, isFetching: tncLoading } = useEvaluateTncQuery();
    const [acceptTnc, { isLoading: isAccepting }] = useAcceptTncMutation();



    const onDocumentLoad = (event: any) => {
        setPdfDocument(event.target);
    };

    const handleAgree = async () => {
        let messageText;
        try {
            await acceptTnc({ termsAndConditionsId: tncDocument.document!.termsAndConditionsId, triggerType: tncDocument?.document?.triggerType || "" }).unwrap();
            navigate(redirectPath);
        } catch (err: any) {
            messageText = 'Terms & Conditions acceptance failed';
            if (err?.status >= 400 && err?.status < 500 && err?.data?.message) {
                messageText = err?.data?.message;
            }
            errorNotification([''], messageText);
        }
    };

    const clickToolbarButtonByTitle = (title: string) => {
        const button = document.querySelector(`button[title="${title}"]`) as HTMLElement;
        button?.click();
    };

    useEffect(() => {
        if (tncDocument.isValidated || !tncDocument.document) {
            navigate(redirectPath);
        }
    }, [tncDocument, navigate, redirectPath]);


    return {
        tncDocument,
        pdfViewerRef,
        pdfDocument,
        setPdfDocument,
        tncRefetch,
        tncLoading,
        isAccepting,
        onDocumentLoad,
        handleAgree,
        clickToolbarButtonByTitle
    };
};
