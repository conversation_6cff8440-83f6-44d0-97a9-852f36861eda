{"ast": null, "code": "import { useMemo } from 'react';\n/**\n * Get sticky column offset width\n */\n\nfunction useStickyOffsets(colWidths, columnCount, direction) {\n  var stickyOffsets = useMemo(function () {\n    var leftOffsets = [];\n    var rightOffsets = [];\n    var left = 0;\n    var right = 0;\n    for (var start = 0; start < columnCount; start += 1) {\n      if (direction === 'rtl') {\n        // Left offset\n        rightOffsets[start] = right;\n        right += colWidths[start] || 0; // Right offset\n\n        var end = columnCount - start - 1;\n        leftOffsets[end] = left;\n        left += colWidths[end] || 0;\n      } else {\n        // Left offset\n        leftOffsets[start] = left;\n        left += colWidths[start] || 0; // Right offset\n\n        var _end = columnCount - start - 1;\n        rightOffsets[_end] = right;\n        right += colWidths[_end] || 0;\n      }\n    }\n    return {\n      left: leftOffsets,\n      right: rightOffsets\n    };\n  }, [colWidths, columnCount, direction]);\n  return stickyOffsets;\n}\nexport default useStickyOffsets;", "map": {"version": 3, "names": ["useMemo", "useStickyOffsets", "col<PERSON><PERSON><PERSON>", "columnCount", "direction", "stickyOffsets", "leftOffsets", "rightOffsets", "left", "right", "start", "end", "_end"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/rc-table/es/hooks/useStickyOffsets.js"], "sourcesContent": ["import { useMemo } from 'react';\n/**\n * Get sticky column offset width\n */\n\nfunction useStickyOffsets(colWidths, columnCount, direction) {\n  var stickyOffsets = useMemo(function () {\n    var leftOffsets = [];\n    var rightOffsets = [];\n    var left = 0;\n    var right = 0;\n\n    for (var start = 0; start < columnCount; start += 1) {\n      if (direction === 'rtl') {\n        // Left offset\n        rightOffsets[start] = right;\n        right += colWidths[start] || 0; // Right offset\n\n        var end = columnCount - start - 1;\n        leftOffsets[end] = left;\n        left += colWidths[end] || 0;\n      } else {\n        // Left offset\n        leftOffsets[start] = left;\n        left += colWidths[start] || 0; // Right offset\n\n        var _end = columnCount - start - 1;\n\n        rightOffsets[_end] = right;\n        right += colWidths[_end] || 0;\n      }\n    }\n\n    return {\n      left: leftOffsets,\n      right: rightOffsets\n    };\n  }, [colWidths, columnCount, direction]);\n  return stickyOffsets;\n}\n\nexport default useStickyOffsets;"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO;AAC/B;AACA;AACA;;AAEA,SAASC,gBAAgBA,CAACC,SAAS,EAAEC,WAAW,EAAEC,SAAS,EAAE;EAC3D,IAAIC,aAAa,GAAGL,OAAO,CAAC,YAAY;IACtC,IAAIM,WAAW,GAAG,EAAE;IACpB,IAAIC,YAAY,GAAG,EAAE;IACrB,IAAIC,IAAI,GAAG,CAAC;IACZ,IAAIC,KAAK,GAAG,CAAC;IAEb,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGP,WAAW,EAAEO,KAAK,IAAI,CAAC,EAAE;MACnD,IAAIN,SAAS,KAAK,KAAK,EAAE;QACvB;QACAG,YAAY,CAACG,KAAK,CAAC,GAAGD,KAAK;QAC3BA,KAAK,IAAIP,SAAS,CAACQ,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;;QAEhC,IAAIC,GAAG,GAAGR,WAAW,GAAGO,KAAK,GAAG,CAAC;QACjCJ,WAAW,CAACK,GAAG,CAAC,GAAGH,IAAI;QACvBA,IAAI,IAAIN,SAAS,CAACS,GAAG,CAAC,IAAI,CAAC;MAC7B,CAAC,MAAM;QACL;QACAL,WAAW,CAACI,KAAK,CAAC,GAAGF,IAAI;QACzBA,IAAI,IAAIN,SAAS,CAACQ,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;;QAE/B,IAAIE,IAAI,GAAGT,WAAW,GAAGO,KAAK,GAAG,CAAC;QAElCH,YAAY,CAACK,IAAI,CAAC,GAAGH,KAAK;QAC1BA,KAAK,IAAIP,SAAS,CAACU,IAAI,CAAC,IAAI,CAAC;MAC/B;IACF;IAEA,OAAO;MACLJ,IAAI,EAAEF,WAAW;MACjBG,KAAK,EAAEF;IACT,CAAC;EACH,CAAC,EAAE,CAACL,SAAS,EAAEC,WAAW,EAAEC,SAAS,CAAC,CAAC;EACvC,OAAOC,aAAa;AACtB;AAEA,eAAeJ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}