import { EvaluateTncResponse, AcceptTncResponse, TriggerType, TncStatus } from '@app/types/tncTypes';

export const mockEvaluateTnc: EvaluateTncResponse = {

  status: "PendingAcceptance",
  termsAndConditionsId: 42,
  statementOfAgreement: "Custom agreement text...",
  file: {
    fileName: "terms_and_conditions_v2.pdf",
    documentUrl: "https://filestorage.blob.core.windows.net/terms/terms_and_conditions_v2.pdf"
  },
  triggerType: TriggerType.FirstLogin
};

export const mockEvaluateTncEmpty: EvaluateTncResponse = {
  termsAndConditionsId: 0,
  statementOfAgreement: '',
  status: TncStatus.ACCEPTED
};

export const mockAcceptTncSuccess: AcceptTncResponse = {
  message: 'Terms & Conditions accepted successfully.',
};

export const mockAcceptTncError: AcceptTncResponse = {
  message: 'Invalid document ID.',
};
