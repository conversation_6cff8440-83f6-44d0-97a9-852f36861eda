{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"expandable\"];\nimport warning from \"rc-util/es/warning\";\nexport var INTERNAL_COL_DEFINE = 'RC_TABLE_INTERNAL_COL_DEFINE';\nexport function getExpandableProps(props) {\n  var expandable = props.expandable,\n    legacyExpandableConfig = _objectWithoutProperties(props, _excluded);\n  var config;\n  if ('expandable' in props) {\n    config = _objectSpread(_objectSpread({}, legacyExpandableConfig), expandable);\n  } else {\n    if (process.env.NODE_ENV !== 'production' && ['indentSize', 'expandedRowKeys', 'defaultExpandedRowKeys', 'defaultExpandAllRows', 'expandedRowRender', 'expandRowByClick', 'expandIcon', 'onExpand', 'onExpandedRowsChange', 'expandedRowClassName', 'expandIconColumnIndex', 'showExpandColumn'].some(function (prop) {\n      return prop in props;\n    })) {\n      warning(false, 'expanded related props have been moved into `expandable`.');\n    }\n    config = legacyExpandableConfig;\n  }\n  if (config.showExpandColumn === false) {\n    config.expandIconColumnIndex = -1;\n  }\n  return config;\n}", "map": {"version": 3, "names": ["_objectSpread", "_objectWithoutProperties", "_excluded", "warning", "INTERNAL_COL_DEFINE", "getExpandableProps", "props", "expandable", "legacyExpandableConfig", "config", "process", "env", "NODE_ENV", "some", "prop", "showExpandColumn", "expandIconColumnIndex"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/rc-table/es/utils/legacyUtil.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"expandable\"];\nimport warning from \"rc-util/es/warning\";\nexport var INTERNAL_COL_DEFINE = 'RC_TABLE_INTERNAL_COL_DEFINE';\nexport function getExpandableProps(props) {\n  var expandable = props.expandable,\n      legacyExpandableConfig = _objectWithoutProperties(props, _excluded);\n\n  var config;\n\n  if ('expandable' in props) {\n    config = _objectSpread(_objectSpread({}, legacyExpandableConfig), expandable);\n  } else {\n    if (process.env.NODE_ENV !== 'production' && ['indentSize', 'expandedRowKeys', 'defaultExpandedRowKeys', 'defaultExpandAllRows', 'expandedRowRender', 'expandRowByClick', 'expandIcon', 'onExpand', 'onExpandedRowsChange', 'expandedRowClassName', 'expandIconColumnIndex', 'showExpandColumn'].some(function (prop) {\n      return prop in props;\n    })) {\n      warning(false, 'expanded related props have been moved into `expandable`.');\n    }\n\n    config = legacyExpandableConfig;\n  }\n\n  if (config.showExpandColumn === false) {\n    config.expandIconColumnIndex = -1;\n  }\n\n  return config;\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,YAAY,CAAC;AAC9B,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,IAAIC,mBAAmB,GAAG,8BAA8B;AAC/D,OAAO,SAASC,kBAAkBA,CAACC,KAAK,EAAE;EACxC,IAAIC,UAAU,GAAGD,KAAK,CAACC,UAAU;IAC7BC,sBAAsB,GAAGP,wBAAwB,CAACK,KAAK,EAAEJ,SAAS,CAAC;EAEvE,IAAIO,MAAM;EAEV,IAAI,YAAY,IAAIH,KAAK,EAAE;IACzBG,MAAM,GAAGT,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEQ,sBAAsB,CAAC,EAAED,UAAU,CAAC;EAC/E,CAAC,MAAM;IACL,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAAC,YAAY,EAAE,iBAAiB,EAAE,wBAAwB,EAAE,sBAAsB,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,YAAY,EAAE,UAAU,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,uBAAuB,EAAE,kBAAkB,CAAC,CAACC,IAAI,CAAC,UAAUC,IAAI,EAAE;MACpT,OAAOA,IAAI,IAAIR,KAAK;IACtB,CAAC,CAAC,EAAE;MACFH,OAAO,CAAC,KAAK,EAAE,2DAA2D,CAAC;IAC7E;IAEAM,MAAM,GAAGD,sBAAsB;EACjC;EAEA,IAAIC,MAAM,CAACM,gBAAgB,KAAK,KAAK,EAAE;IACrCN,MAAM,CAACO,qBAAqB,GAAG,CAAC,CAAC;EACnC;EAEA,OAAOP,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}