{"ast": null, "code": "export let TriggerType = /*#__PURE__*/function (TriggerType) {\n  TriggerType[\"FirstLogin\"] = \"first_login\";\n  TriggerType[\"Updated\"] = \"updated\";\n  TriggerType[\"Monthly\"] = \"monthly\";\n  TriggerType[\"Annually\"] = \"annually\";\n  TriggerType[\"Inactivity\"] = \"inactivity\";\n  return TriggerType;\n}({});\n;\n;\nexport let TncTags = /*#__PURE__*/function (TncTags) {\n  TncTags[\"EvaluateTnc\"] = \"EvaluateTnc\";\n  return TncTags;\n}({});\n;\n;\n;\nexport let TncStatus = /*#__PURE__*/function (TncStatus) {\n  TncStatus[\"ACCEPTED\"] = \"Accepted\";\n  TncStatus[\"PENDING_ACCEPTANCE\"] = \"PendingAcceptance\";\n  return TncStatus;\n}({});", "map": {"version": 3, "names": ["TriggerType", "TncTags", "TncStatus"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/src/app/types/tncTypes.ts"], "sourcesContent": ["export enum TriggerType {\r\n    FirstLogin = 'first_login',\r\n    Updated = 'updated',\r\n    Monthly = 'monthly',\r\n    Annually = 'annually',\r\n    Inactivity = 'inactivity',\r\n};\r\n\r\nexport interface TncFile {\r\n    fileName: string;\r\n    documentUrl: string;\r\n}\r\n\r\nexport interface EvaluateTncResponse {\r\n    status: string;\r\n    termsAndConditionsId?: number;\r\n    statementOfAgreement?: string;\r\n    file?: TncFile;\r\n    triggerType?: TriggerType;\r\n};\r\n\r\nexport enum TncTags {\r\n    EvaluateTnc = 'EvaluateTnc'\r\n};\r\n\r\nexport interface AcceptTncResponse {\r\n    message: string;\r\n};\r\n\r\nexport interface TncDocument extends TncFile {\r\n    termsAndConditionsId: number;\r\n    statementOfAgreement: string;\r\n    triggerType: string;\r\n};\r\n\r\nexport interface TncDocumentState {\r\n    isValidated: boolean;\r\n    document: TncDocument | null;\r\n}\r\n\r\nexport enum TncStatus {\r\n    ACCEPTED = 'Accepted',\r\n    PENDING_ACCEPTANCE = \"PendingAcceptance\"\r\n}"], "mappings": "AAAA,WAAYA,WAAW,0BAAXA,WAAW;EAAXA,WAAW;EAAXA,WAAW;EAAXA,WAAW;EAAXA,WAAW;EAAXA,WAAW;EAAA,OAAXA,WAAW;AAAA;AAMtB;AAaA;AAED,WAAYC,OAAO,0BAAPA,OAAO;EAAPA,OAAO;EAAA,OAAPA,OAAO;AAAA;AAElB;AAIA;AAMA;AAOD,WAAYC,SAAS,0BAATA,SAAS;EAATA,SAAS;EAATA,SAAS;EAAA,OAATA,SAAS;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}