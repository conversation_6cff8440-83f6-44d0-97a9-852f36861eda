{"ast": null, "code": "import wrapElements from './wrap-elements';\nexport default function vWrap(elements, rect) {\n  return wrapElements(elements, rect, \"y\", \"x\", \"height\");\n}", "map": {"version": 3, "names": ["wrapElements", "vWrap", "elements", "rect"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/alignment/v-wrap.js"], "sourcesContent": ["import wrapElements from './wrap-elements';\n\nexport default function vWrap(elements, rect) {\n    return wrapElements(elements, rect, \"y\", \"x\", \"height\");\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,iBAAiB;AAE1C,eAAe,SAASC,KAAKA,CAACC,QAAQ,EAAEC,IAAI,EAAE;EAC1C,OAAOH,YAAY,CAACE,QAAQ,EAAEC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,CAAC;AAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}