{"ast": null, "code": "import { findRule } from './find-rule';\nimport { findZone } from './find-zone';\n/**\n * @hidden\n *\n * A function that gets the information about the zone and the rule for a specific timezone.\n *\n */\nexport var zoneAndRule = function (timezone, date) {\n  var utcTime = date.getTime();\n  var zone = findZone(timezone, utcTime);\n  return {\n    rule: findRule(zone[1], utcTime, zone[0]),\n    zone: zone\n  };\n};", "map": {"version": 3, "names": ["findRule", "findZone", "zoneAndRule", "timezone", "date", "utcTime", "getTime", "zone", "rule"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/tz/zone-and-rule.js"], "sourcesContent": ["import { findRule } from './find-rule';\nimport { findZone } from './find-zone';\n/**\n * @hidden\n *\n * A function that gets the information about the zone and the rule for a specific timezone.\n *\n */\nexport var zoneAndRule = function (timezone, date) {\n    var utcTime = date.getTime();\n    var zone = findZone(timezone, utcTime);\n    return {\n        rule: findRule(zone[1], utcTime, zone[0]),\n        zone: zone\n    };\n};\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,aAAa;AACtC,SAASC,QAAQ,QAAQ,aAAa;AACtC;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,WAAW,GAAG,SAAAA,CAAUC,QAAQ,EAAEC,IAAI,EAAE;EAC/C,IAAIC,OAAO,GAAGD,IAAI,CAACE,OAAO,CAAC,CAAC;EAC5B,IAAIC,IAAI,GAAGN,QAAQ,CAACE,QAAQ,EAAEE,OAAO,CAAC;EACtC,OAAO;IACHG,IAAI,EAAER,QAAQ,CAACO,IAAI,CAAC,CAAC,CAAC,EAAEF,OAAO,EAAEE,IAAI,CAAC,CAAC,CAAC,CAAC;IACzCA,IAAI,EAAEA;EACV,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}