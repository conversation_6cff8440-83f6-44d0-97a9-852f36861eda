{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as s from \"react\";\nimport r from \"prop-types\";\nimport { focusFirstFocusableChild as d, classNames as f } from \"@progress/kendo-react-common\";\nconst l = s.forwardRef((p, n) => {\n  const {\n      children: o,\n      className: t,\n      style: e\n    } = p,\n    a = s.useRef(null),\n    c = s.useCallback(() => {\n      a.current && d(a.current);\n    }, []),\n    i = s.useCallback(() => ({\n      element: a.current,\n      focus: c\n    }), [c]);\n  s.useImperativeHandle(n, i);\n  const m = s.useMemo(() => f(\"k-appbar-spacer\", {\n      \"k-appbar-spacer-sized\": e && e.width && e.width !== null\n    }, t), [t, e]),\n    u = s.useMemo(() => ({\n      flexBasis: e && e.width ? e.width : void 0\n    }), [e]);\n  return /* @__PURE__ */s.createElement(\"span\", {\n    className: m,\n    style: u\n  }, o);\n});\nl.propTypes = {\n  children: r.any,\n  className: r.string,\n  style: r.object\n};\nl.displayName = \"KendoAppBarSpacer\";\nexport { l as AppBarSpacer };", "map": {"version": 3, "names": ["s", "r", "focusFirstFocusableChild", "d", "classNames", "f", "l", "forwardRef", "p", "n", "children", "o", "className", "t", "style", "e", "a", "useRef", "c", "useCallback", "current", "i", "element", "focus", "useImperativeHandle", "m", "useMemo", "width", "u", "flexBasis", "createElement", "propTypes", "any", "string", "object", "displayName", "AppBarSpacer"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/appbar/AppBarSpacer.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as s from \"react\";\nimport r from \"prop-types\";\nimport { focusFirstFocusableChild as d, classNames as f } from \"@progress/kendo-react-common\";\nconst l = s.forwardRef((p, n) => {\n  const { children: o, className: t, style: e } = p, a = s.useRef(null), c = s.useCallback(() => {\n    a.current && d(a.current);\n  }, []), i = s.useCallback(\n    () => ({\n      element: a.current,\n      focus: c\n    }),\n    [c]\n  );\n  s.useImperativeHandle(n, i);\n  const m = s.useMemo(\n    () => f(\n      \"k-appbar-spacer\",\n      {\n        \"k-appbar-spacer-sized\": e && e.width && e.width !== null\n      },\n      t\n    ),\n    [t, e]\n  ), u = s.useMemo(() => ({\n    flexBasis: e && e.width ? e.width : void 0\n  }), [e]);\n  return /* @__PURE__ */ s.createElement(\"span\", { className: m, style: u }, o);\n});\nl.propTypes = {\n  children: r.any,\n  className: r.string,\n  style: r.object\n};\nl.displayName = \"KendoAppBarSpacer\";\nexport {\n  l as AppBarSpacer\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,wBAAwB,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AAC7F,MAAMC,CAAC,GAAGN,CAAC,CAACO,UAAU,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;EAC/B,MAAM;MAAEC,QAAQ,EAAEC,CAAC;MAAEC,SAAS,EAAEC,CAAC;MAAEC,KAAK,EAAEC;IAAE,CAAC,GAAGP,CAAC;IAAEQ,CAAC,GAAGhB,CAAC,CAACiB,MAAM,CAAC,IAAI,CAAC;IAAEC,CAAC,GAAGlB,CAAC,CAACmB,WAAW,CAAC,MAAM;MAC7FH,CAAC,CAACI,OAAO,IAAIjB,CAAC,CAACa,CAAC,CAACI,OAAO,CAAC;IAC3B,CAAC,EAAE,EAAE,CAAC;IAAEC,CAAC,GAAGrB,CAAC,CAACmB,WAAW,CACvB,OAAO;MACLG,OAAO,EAAEN,CAAC,CAACI,OAAO;MAClBG,KAAK,EAAEL;IACT,CAAC,CAAC,EACF,CAACA,CAAC,CACJ,CAAC;EACDlB,CAAC,CAACwB,mBAAmB,CAACf,CAAC,EAAEY,CAAC,CAAC;EAC3B,MAAMI,CAAC,GAAGzB,CAAC,CAAC0B,OAAO,CACjB,MAAMrB,CAAC,CACL,iBAAiB,EACjB;MACE,uBAAuB,EAAEU,CAAC,IAAIA,CAAC,CAACY,KAAK,IAAIZ,CAAC,CAACY,KAAK,KAAK;IACvD,CAAC,EACDd,CACF,CAAC,EACD,CAACA,CAAC,EAAEE,CAAC,CACP,CAAC;IAAEa,CAAC,GAAG5B,CAAC,CAAC0B,OAAO,CAAC,OAAO;MACtBG,SAAS,EAAEd,CAAC,IAAIA,CAAC,CAACY,KAAK,GAAGZ,CAAC,CAACY,KAAK,GAAG,KAAK;IAC3C,CAAC,CAAC,EAAE,CAACZ,CAAC,CAAC,CAAC;EACR,OAAO,eAAgBf,CAAC,CAAC8B,aAAa,CAAC,MAAM,EAAE;IAAElB,SAAS,EAAEa,CAAC;IAAEX,KAAK,EAAEc;EAAE,CAAC,EAAEjB,CAAC,CAAC;AAC/E,CAAC,CAAC;AACFL,CAAC,CAACyB,SAAS,GAAG;EACZrB,QAAQ,EAAET,CAAC,CAAC+B,GAAG;EACfpB,SAAS,EAAEX,CAAC,CAACgC,MAAM;EACnBnB,KAAK,EAAEb,CAAC,CAACiC;AACX,CAAC;AACD5B,CAAC,CAAC6B,WAAW,GAAG,mBAAmB;AACnC,SACE7B,CAAC,IAAI8B,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}