{"ast": null, "code": "export default function limitValue(value, min, max) {\n  return Math.max(Math.min(value, max), min);\n}", "map": {"version": 3, "names": ["limitValue", "value", "min", "max", "Math"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/util/limit-value.js"], "sourcesContent": ["export default function limitValue(value, min, max) {\n    return Math.max(Math.min(value, max), min);\n}"], "mappings": "AAAA,eAAe,SAASA,UAAUA,CAACC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAChD,OAAOC,IAAI,CAACD,GAAG,CAACC,IAAI,CAACF,GAAG,CAACD,KAAK,EAAEE,GAAG,CAAC,EAAED,GAAG,CAAC;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}