{"ast": null, "code": "import Node from './node';\nexport var PatternNode = function (Node) {\n  function PatternNode(pattern) {\n    Node.call(this, pattern);\n    this.id = pattern.id;\n    this.load(pattern.children);\n  }\n  if (Node) PatternNode.__proto__ = Node;\n  PatternNode.prototype = Object.create(Node && Node.prototype);\n  PatternNode.prototype.constructor = PatternNode;\n  PatternNode.prototype.template = function template() {\n    var width = this.srcElement.size().getWidth();\n    var height = this.srcElement.size().getHeight();\n    return \"<pattern id='\" + this.srcElement.id + \"' width='\" + width + \"' height='\" + height + \"' patternUnits='userSpaceOnUse'>\" + \"\" + this.renderChildren() + \"</pattern>\";\n  };\n  return PatternNode;\n}(Node);", "map": {"version": 3, "names": ["Node", "PatternNode", "pattern", "call", "id", "load", "children", "__proto__", "prototype", "Object", "create", "constructor", "template", "width", "srcElement", "size", "getWidth", "height", "getHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/svg/pattern-node.js"], "sourcesContent": ["import Node from './node';\n\nexport var PatternNode = (function (Node) {\n    function PatternNode(pattern) {\n        Node.call(this, pattern);\n\n        this.id = pattern.id;\n        this.load(pattern.children);\n    }\n\n    if ( Node ) PatternNode.__proto__ = Node;\n    PatternNode.prototype = Object.create( Node && Node.prototype );\n    PatternNode.prototype.constructor = PatternNode;\n\n    PatternNode.prototype.template = function template () {\n        var width = this.srcElement.size().getWidth();\n        var height = this.srcElement.size().getHeight();\n\n        return \"<pattern id='\" + (this.srcElement.id) + \"' width='\" + width + \"' height='\" + height + \"' patternUnits='userSpaceOnUse'>\" +\n                    \"\" + (this.renderChildren()) +\n                \"</pattern>\";\n    };\n\n    return PatternNode;\n}(Node));\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,QAAQ;AAEzB,OAAO,IAAIC,WAAW,GAAI,UAAUD,IAAI,EAAE;EACtC,SAASC,WAAWA,CAACC,OAAO,EAAE;IAC1BF,IAAI,CAACG,IAAI,CAAC,IAAI,EAAED,OAAO,CAAC;IAExB,IAAI,CAACE,EAAE,GAAGF,OAAO,CAACE,EAAE;IACpB,IAAI,CAACC,IAAI,CAACH,OAAO,CAACI,QAAQ,CAAC;EAC/B;EAEA,IAAKN,IAAI,EAAGC,WAAW,CAACM,SAAS,GAAGP,IAAI;EACxCC,WAAW,CAACO,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAEV,IAAI,IAAIA,IAAI,CAACQ,SAAU,CAAC;EAC/DP,WAAW,CAACO,SAAS,CAACG,WAAW,GAAGV,WAAW;EAE/CA,WAAW,CAACO,SAAS,CAACI,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;IAClD,IAAIC,KAAK,GAAG,IAAI,CAACC,UAAU,CAACC,IAAI,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;IAC7C,IAAIC,MAAM,GAAG,IAAI,CAACH,UAAU,CAACC,IAAI,CAAC,CAAC,CAACG,SAAS,CAAC,CAAC;IAE/C,OAAO,eAAe,GAAI,IAAI,CAACJ,UAAU,CAACV,EAAG,GAAG,WAAW,GAAGS,KAAK,GAAG,YAAY,GAAGI,MAAM,GAAG,kCAAkC,GACpH,EAAE,GAAI,IAAI,CAACE,cAAc,CAAC,CAAE,GAChC,YAAY;EACxB,CAAC;EAED,OAAOlB,WAAW;AACtB,CAAC,CAACD,IAAI,CAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}