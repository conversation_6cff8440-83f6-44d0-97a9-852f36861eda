{"ast": null, "code": "import Node from './node';\nimport renderAttr from './utils/render-attribute';\nvar GradientStopNode = function (Node) {\n  function GradientStopNode() {\n    Node.apply(this, arguments);\n  }\n  if (Node) GradientStopNode.__proto__ = Node;\n  GradientStopNode.prototype = Object.create(Node && Node.prototype);\n  GradientStopNode.prototype.constructor = GradientStopNode;\n  GradientStopNode.prototype.template = function template() {\n    return \"<stop \" + this.renderOffset() + \" \" + this.renderStyle() + \" />\";\n  };\n  GradientStopNode.prototype.renderOffset = function renderOffset() {\n    return renderAttr(\"offset\", this.srcElement.offset());\n  };\n  GradientStopNode.prototype.mapStyle = function mapStyle() {\n    var srcElement = this.srcElement;\n    return [[\"stop-color\", srcElement.color()], [\"stop-opacity\", srcElement.opacity()]];\n  };\n  GradientStopNode.prototype.optionsChange = function optionsChange(e) {\n    if (e.field === \"offset\") {\n      this.attr(e.field, e.value);\n    } else if (e.field === \"color\" || e.field === \"opacity\") {\n      this.css(\"stop-\" + e.field, e.value);\n    }\n  };\n  return GradientStopNode;\n}(Node);\nexport default GradientStopNode;", "map": {"version": 3, "names": ["Node", "renderAttr", "GradientStopNode", "apply", "arguments", "__proto__", "prototype", "Object", "create", "constructor", "template", "renderOffset", "renderStyle", "srcElement", "offset", "mapStyle", "color", "opacity", "optionsChange", "e", "field", "attr", "value", "css"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/svg/gradient-stop-node.js"], "sourcesContent": ["import Node from './node';\nimport renderAttr from './utils/render-attribute';\n\nvar GradientStopNode = (function (Node) {\n    function GradientStopNode () {\n        Node.apply(this, arguments);\n    }\n\n    if ( Node ) GradientStopNode.__proto__ = Node;\n    GradientStopNode.prototype = Object.create( Node && Node.prototype );\n    GradientStopNode.prototype.constructor = GradientStopNode;\n\n    GradientStopNode.prototype.template = function template () {\n        return (\"<stop \" + (this.renderOffset()) + \" \" + (this.renderStyle()) + \" />\");\n    };\n\n    GradientStopNode.prototype.renderOffset = function renderOffset () {\n        return renderAttr(\"offset\", this.srcElement.offset());\n    };\n\n    GradientStopNode.prototype.mapStyle = function mapStyle () {\n        var srcElement = this.srcElement;\n        return [\n            [ \"stop-color\", srcElement.color() ],\n            [ \"stop-opacity\", srcElement.opacity() ]\n        ];\n    };\n\n    GradientStopNode.prototype.optionsChange = function optionsChange (e) {\n        if (e.field === \"offset\") {\n            this.attr(e.field, e.value);\n        } else if (e.field === \"color\" || e.field === \"opacity\") {\n            this.css(\"stop-\" + e.field, e.value);\n        }\n    };\n\n    return GradientStopNode;\n}(Node));\n\nexport default GradientStopNode;"], "mappings": "AAAA,OAAOA,IAAI,MAAM,QAAQ;AACzB,OAAOC,UAAU,MAAM,0BAA0B;AAEjD,IAAIC,gBAAgB,GAAI,UAAUF,IAAI,EAAE;EACpC,SAASE,gBAAgBA,CAAA,EAAI;IACzBF,IAAI,CAACG,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EAC/B;EAEA,IAAKJ,IAAI,EAAGE,gBAAgB,CAACG,SAAS,GAAGL,IAAI;EAC7CE,gBAAgB,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAER,IAAI,IAAIA,IAAI,CAACM,SAAU,CAAC;EACpEJ,gBAAgB,CAACI,SAAS,CAACG,WAAW,GAAGP,gBAAgB;EAEzDA,gBAAgB,CAACI,SAAS,CAACI,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;IACvD,OAAQ,QAAQ,GAAI,IAAI,CAACC,YAAY,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAACC,WAAW,CAAC,CAAE,GAAG,KAAK;EACjF,CAAC;EAEDV,gBAAgB,CAACI,SAAS,CAACK,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAI;IAC/D,OAAOV,UAAU,CAAC,QAAQ,EAAE,IAAI,CAACY,UAAU,CAACC,MAAM,CAAC,CAAC,CAAC;EACzD,CAAC;EAEDZ,gBAAgB,CAACI,SAAS,CAACS,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;IACvD,IAAIF,UAAU,GAAG,IAAI,CAACA,UAAU;IAChC,OAAO,CACH,CAAE,YAAY,EAAEA,UAAU,CAACG,KAAK,CAAC,CAAC,CAAE,EACpC,CAAE,cAAc,EAAEH,UAAU,CAACI,OAAO,CAAC,CAAC,CAAE,CAC3C;EACL,CAAC;EAEDf,gBAAgB,CAACI,SAAS,CAACY,aAAa,GAAG,SAASA,aAAaA,CAAEC,CAAC,EAAE;IAClE,IAAIA,CAAC,CAACC,KAAK,KAAK,QAAQ,EAAE;MACtB,IAAI,CAACC,IAAI,CAACF,CAAC,CAACC,KAAK,EAAED,CAAC,CAACG,KAAK,CAAC;IAC/B,CAAC,MAAM,IAAIH,CAAC,CAACC,KAAK,KAAK,OAAO,IAAID,CAAC,CAACC,KAAK,KAAK,SAAS,EAAE;MACrD,IAAI,CAACG,GAAG,CAAC,OAAO,GAAGJ,CAAC,CAACC,KAAK,EAAED,CAAC,CAACG,KAAK,CAAC;IACxC;EACJ,CAAC;EAED,OAAOpB,gBAAgB;AAC3B,CAAC,CAACF,IAAI,CAAE;AAER,eAAeE,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}