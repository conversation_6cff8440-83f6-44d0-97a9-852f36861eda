{"ast": null, "code": "import Node from './node';\nimport renderAttr from './utils/render-attribute';\nvar GroupNode = function (Node) {\n  function GroupNode() {\n    Node.apply(this, arguments);\n  }\n  if (Node) GroupNode.__proto__ = Node;\n  GroupNode.prototype = Object.create(Node && Node.prototype);\n  GroupNode.prototype.constructor = GroupNode;\n  GroupNode.prototype.template = function template() {\n    return \"<g\" + (this.renderId() + this.renderTransform() + this.renderClassName() + this.renderStyle() + this.renderOpacity() + this.renderRole() + this.renderAriaLabel() + this.renderAriaRoleDescription() + this.renderAriaChecked() + this.renderDefinitions()) + \">\" + this.renderChildren() + \"</g>\";\n  };\n  GroupNode.prototype.optionsChange = function optionsChange(e) {\n    var field = e.field;\n    var value = e.value;\n    if (field === \"transform\") {\n      this.transformChange(value);\n    }\n    this.accessibilityOptionsChange(e);\n    Node.prototype.optionsChange.call(this, e);\n  };\n  return GroupNode;\n}(Node);\nexport default GroupNode;", "map": {"version": 3, "names": ["Node", "renderAttr", "GroupNode", "apply", "arguments", "__proto__", "prototype", "Object", "create", "constructor", "template", "renderId", "renderTransform", "renderClassName", "renderStyle", "renderOpacity", "renderRole", "renderAriaLabel", "renderAriaRoleDescription", "renderAriaChecked", "renderDefinitions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "optionsChange", "e", "field", "value", "transformChange", "accessibilityOptionsChange", "call"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/svg/group-node.js"], "sourcesContent": ["import Node from './node';\nimport renderAttr from './utils/render-attribute';\n\nvar GroupNode = (function (Node) {\n    function GroupNode () {\n        Node.apply(this, arguments);\n    }\n\n    if ( Node ) GroupNode.__proto__ = Node;\n    GroupNode.prototype = Object.create( Node && Node.prototype );\n    GroupNode.prototype.constructor = GroupNode;\n\n    GroupNode.prototype.template = function template () {\n        return (\"<g\" + (this.renderId() + \n            this.renderTransform() + \n            this.renderClassName() + \n            this.renderStyle() + \n            this.renderOpacity() + \n            this.renderRole() + \n            this.renderAriaLabel() + \n            this.renderAriaRoleDescription() + \n            this.renderAriaChecked() +\n            this.renderDefinitions()) + \">\" + (this.renderChildren()) + \"</g>\");\n    };\n\n    GroupNode.prototype.optionsChange = function optionsChange (e) {\n        var field = e.field;\n        var value = e.value;\n\n        if (field === \"transform\") {\n            this.transformChange(value);\n        }\n\n        this.accessibilityOptionsChange(e);\n\n        Node.prototype.optionsChange.call(this, e);\n    };\n\n    return GroupNode;\n}(Node));\n\nexport default GroupNode;\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,QAAQ;AACzB,OAAOC,UAAU,MAAM,0BAA0B;AAEjD,IAAIC,SAAS,GAAI,UAAUF,IAAI,EAAE;EAC7B,SAASE,SAASA,CAAA,EAAI;IAClBF,IAAI,CAACG,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EAC/B;EAEA,IAAKJ,IAAI,EAAGE,SAAS,CAACG,SAAS,GAAGL,IAAI;EACtCE,SAAS,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAER,IAAI,IAAIA,IAAI,CAACM,SAAU,CAAC;EAC7DJ,SAAS,CAACI,SAAS,CAACG,WAAW,GAAGP,SAAS;EAE3CA,SAAS,CAACI,SAAS,CAACI,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;IAChD,OAAQ,IAAI,IAAI,IAAI,CAACC,QAAQ,CAAC,CAAC,GAC3B,IAAI,CAACC,eAAe,CAAC,CAAC,GACtB,IAAI,CAACC,eAAe,CAAC,CAAC,GACtB,IAAI,CAACC,WAAW,CAAC,CAAC,GAClB,IAAI,CAACC,aAAa,CAAC,CAAC,GACpB,IAAI,CAACC,UAAU,CAAC,CAAC,GACjB,IAAI,CAACC,eAAe,CAAC,CAAC,GACtB,IAAI,CAACC,yBAAyB,CAAC,CAAC,GAChC,IAAI,CAACC,iBAAiB,CAAC,CAAC,GACxB,IAAI,CAACC,iBAAiB,CAAC,CAAC,CAAC,GAAG,GAAG,GAAI,IAAI,CAACC,cAAc,CAAC,CAAE,GAAG,MAAM;EAC1E,CAAC;EAEDnB,SAAS,CAACI,SAAS,CAACgB,aAAa,GAAG,SAASA,aAAaA,CAAEC,CAAC,EAAE;IAC3D,IAAIC,KAAK,GAAGD,CAAC,CAACC,KAAK;IACnB,IAAIC,KAAK,GAAGF,CAAC,CAACE,KAAK;IAEnB,IAAID,KAAK,KAAK,WAAW,EAAE;MACvB,IAAI,CAACE,eAAe,CAACD,KAAK,CAAC;IAC/B;IAEA,IAAI,CAACE,0BAA0B,CAACJ,CAAC,CAAC;IAElCvB,IAAI,CAACM,SAAS,CAACgB,aAAa,CAACM,IAAI,CAAC,IAAI,EAAEL,CAAC,CAAC;EAC9C,CAAC;EAED,OAAOrB,SAAS;AACpB,CAAC,CAACF,IAAI,CAAE;AAER,eAAeE,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}