{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as n from \"react\";\nimport { FloatingLabel as a } from \"@progress/kendo-react-labels\";\nconst c = e => {\n    const [t, o] = n.useState(!1),\n      r = () => {\n        var s;\n        e.current && o(!!((s = e.current.element) != null && s.value || e.current.text));\n      };\n    return n.useEffect(r), {\n      editorValue: t\n    };\n  },\n  i = e => {\n    const {\n        dateInput: t,\n        ...o\n      } = e,\n      r = c(t);\n    return /* @__PURE__ */n.createElement(a, {\n      ...o,\n      ...r\n    });\n  };\nexport { i as PickerFloatingLabel, c as usePickerFloatingLabel };", "map": {"version": 3, "names": ["n", "FloatingLabel", "a", "c", "e", "t", "o", "useState", "r", "s", "current", "element", "value", "text", "useEffect", "<PERSON><PERSON><PERSON><PERSON>", "i", "dateInput", "createElement", "PickerFloatingLabel", "usePickerFloatingLabel"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dateinputs/hooks/usePickerFloatingLabel.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as n from \"react\";\nimport { FloatingLabel as a } from \"@progress/kendo-react-labels\";\nconst c = (e) => {\n  const [t, o] = n.useState(!1), r = () => {\n    var s;\n    e.current && o(!!((s = e.current.element) != null && s.value || e.current.text));\n  };\n  return n.useEffect(r), {\n    editorValue: t\n  };\n}, i = (e) => {\n  const { dateInput: t, ...o } = e, r = c(t);\n  return /* @__PURE__ */ n.createElement(a, { ...o, ...r });\n};\nexport {\n  i as PickerFloatingLabel,\n  c as usePickerFloatingLabel\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,SAASC,aAAa,IAAIC,CAAC,QAAQ,8BAA8B;AACjE,MAAMC,CAAC,GAAIC,CAAC,IAAK;IACf,MAAM,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAGN,CAAC,CAACO,QAAQ,CAAC,CAAC,CAAC,CAAC;MAAEC,CAAC,GAAGA,CAAA,KAAM;QACvC,IAAIC,CAAC;QACLL,CAAC,CAACM,OAAO,IAAIJ,CAAC,CAAC,CAAC,EAAE,CAACG,CAAC,GAAGL,CAAC,CAACM,OAAO,CAACC,OAAO,KAAK,IAAI,IAAIF,CAAC,CAACG,KAAK,IAAIR,CAAC,CAACM,OAAO,CAACG,IAAI,CAAC,CAAC;MAClF,CAAC;IACD,OAAOb,CAAC,CAACc,SAAS,CAACN,CAAC,CAAC,EAAE;MACrBO,WAAW,EAAEV;IACf,CAAC;EACH,CAAC;EAAEW,CAAC,GAAIZ,CAAC,IAAK;IACZ,MAAM;QAAEa,SAAS,EAAEZ,CAAC;QAAE,GAAGC;MAAE,CAAC,GAAGF,CAAC;MAAEI,CAAC,GAAGL,CAAC,CAACE,CAAC,CAAC;IAC1C,OAAO,eAAgBL,CAAC,CAACkB,aAAa,CAAChB,CAAC,EAAE;MAAE,GAAGI,CAAC;MAAE,GAAGE;IAAE,CAAC,CAAC;EAC3D,CAAC;AACD,SACEQ,CAAC,IAAIG,mBAAmB,EACxBhB,CAAC,IAAIiB,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}