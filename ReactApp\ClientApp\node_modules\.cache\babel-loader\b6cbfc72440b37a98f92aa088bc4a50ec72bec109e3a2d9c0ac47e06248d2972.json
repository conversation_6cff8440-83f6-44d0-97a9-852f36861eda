{"ast": null, "code": "import { POINT_DIGITS } from './constants';\nimport PathNode from './path-node';\nvar MultiPathNode = function (PathNode) {\n  function MultiPathNode() {\n    PathNode.apply(this, arguments);\n  }\n  if (PathNode) MultiPathNode.__proto__ = PathNode;\n  MultiPathNode.prototype = Object.create(PathNode && PathNode.prototype);\n  MultiPathNode.prototype.constructor = MultiPathNode;\n  MultiPathNode.prototype.renderData = function renderData() {\n    return this.srcElement.toString(POINT_DIGITS) || 'undefined';\n  };\n  return MultiPathNode;\n}(PathNode);\nexport default MultiPathNode;", "map": {"version": 3, "names": ["POINT_DIGITS", "PathNode", "MultiPathNode", "apply", "arguments", "__proto__", "prototype", "Object", "create", "constructor", "renderData", "srcElement", "toString"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/svg/multi-path-node.js"], "sourcesContent": ["import { POINT_DIGITS } from './constants';\nimport PathNode from './path-node';\n\nvar MultiPathNode = (function (PathNode) {\n    function MultiPathNode () {\n        PathNode.apply(this, arguments);\n    }\n\n    if ( PathNode ) MultiPathNode.__proto__ = PathNode;\n    MultiPathNode.prototype = Object.create( PathNode && PathNode.prototype );\n    MultiPathNode.prototype.constructor = MultiPathNode;\n\n    MultiPathNode.prototype.renderData = function renderData () {\n        return this.srcElement.toString(POINT_DIGITS) || 'undefined';\n    };\n\n    return MultiPathNode;\n}(PathNode));\n\nexport default MultiPathNode;\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,aAAa;AAC1C,OAAOC,QAAQ,MAAM,aAAa;AAElC,IAAIC,aAAa,GAAI,UAAUD,QAAQ,EAAE;EACrC,SAASC,aAAaA,CAAA,EAAI;IACtBD,QAAQ,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACnC;EAEA,IAAKH,QAAQ,EAAGC,aAAa,CAACG,SAAS,GAAGJ,QAAQ;EAClDC,aAAa,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAEP,QAAQ,IAAIA,QAAQ,CAACK,SAAU,CAAC;EACzEJ,aAAa,CAACI,SAAS,CAACG,WAAW,GAAGP,aAAa;EAEnDA,aAAa,CAACI,SAAS,CAACI,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAI;IACxD,OAAO,IAAI,CAACC,UAAU,CAACC,QAAQ,CAACZ,YAAY,CAAC,IAAI,WAAW;EAChE,CAAC;EAED,OAAOE,aAAa;AACxB,CAAC,CAACD,QAAQ,CAAE;AAEZ,eAAeC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}