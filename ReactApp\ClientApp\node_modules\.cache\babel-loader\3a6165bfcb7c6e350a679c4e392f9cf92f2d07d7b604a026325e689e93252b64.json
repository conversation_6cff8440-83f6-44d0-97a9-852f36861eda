{"ast": null, "code": "/**\n * A function that calculates duration in months between two `Date` objects.\n *\n * @param start - The start date value.\n * @param end - The end date value.\n * @returns - The duration in months.\n *\n * @example\n * ```ts-no-run\n * durationInMonths(new Date(2016, 0, 1), new Date(2017, 0, 1)); // 12\n * durationInMonths(new Date(2016, 6, 1), new Date(2017, 0, 1)); // 6\n * durationInMonths(new Date(2016, 0, 1), new Date(2016, 0, 1)); // 0\n * ```\n */\nexport var durationInMonths = function (start, end) {\n  return (end.getFullYear() - start.getFullYear()) * 12 + (end.getMonth() - start.getMonth());\n};", "map": {"version": 3, "names": ["durationInMonths", "start", "end", "getFullYear", "getMonth"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/duration-in-months.js"], "sourcesContent": ["/**\n * A function that calculates duration in months between two `Date` objects.\n *\n * @param start - The start date value.\n * @param end - The end date value.\n * @returns - The duration in months.\n *\n * @example\n * ```ts-no-run\n * durationInMonths(new Date(2016, 0, 1), new Date(2017, 0, 1)); // 12\n * durationInMonths(new Date(2016, 6, 1), new Date(2017, 0, 1)); // 6\n * durationInMonths(new Date(2016, 0, 1), new Date(2016, 0, 1)); // 0\n * ```\n */\nexport var durationInMonths = function (start, end) { return (((end.getFullYear() - start.getFullYear())) * 12 + (end.getMonth() - start.getMonth())); };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIA,gBAAgB,GAAG,SAAAA,CAAUC,KAAK,EAAEC,GAAG,EAAE;EAAE,OAAQ,CAAEA,GAAG,CAACC,WAAW,CAAC,CAAC,GAAGF,KAAK,CAACE,WAAW,CAAC,CAAC,IAAK,EAAE,IAAID,GAAG,CAACE,QAAQ,CAAC,CAAC,GAAGH,KAAK,CAACG,QAAQ,CAAC,CAAC,CAAC;AAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}