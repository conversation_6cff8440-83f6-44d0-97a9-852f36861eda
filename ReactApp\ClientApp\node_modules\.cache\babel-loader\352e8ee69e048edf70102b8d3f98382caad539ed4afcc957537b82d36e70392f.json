{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as c from \"react\";\nfunction u(t, o, n) {\n  const [d, i] = c.useState(o);\n  return c.useEffect(() => {\n    d !== void 0 && t.current && i(t.current.offsetWidth);\n  }, n), d;\n}\nfunction r(t) {\n  return typeof t == \"string\" ? Number(t.replace(\"px\", \"\")) : t || void 0;\n}\nfunction W(t, o, n, d) {\n  const i = r(o.popupSettings.width),\n    s = (n.width !== void 0 && r(n.width)) !== i,\n    h = d.width !== void 0,\n    p = s ? n.width : h ? d.width : i,\n    e = r(u(t, p));\n  return s || h ? p : e && i && e > i ? e : i;\n}\nexport { W as useDropdownWidth };", "map": {"version": 3, "names": ["c", "u", "t", "o", "n", "d", "i", "useState", "useEffect", "current", "offsetWidth", "r", "Number", "replace", "W", "popupSettings", "width", "s", "h", "p", "e", "useDropdownWidth"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dropdowns/DropDownTree/useDropdownWidth.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as c from \"react\";\nfunction u(t, o, n) {\n  const [d, i] = c.useState(o);\n  return c.useEffect(() => {\n    d !== void 0 && t.current && i(t.current.offsetWidth);\n  }, n), d;\n}\nfunction r(t) {\n  return typeof t == \"string\" ? Number(t.replace(\"px\", \"\")) : t || void 0;\n}\nfunction W(t, o, n, d) {\n  const i = r(o.popupSettings.width), s = (n.width !== void 0 && r(n.width)) !== i, h = d.width !== void 0, p = s ? n.width : h ? d.width : i, e = r(u(t, p));\n  return s || h ? p : e && i && e > i ? e : i;\n}\nexport {\n  W as useDropdownWidth\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,SAASC,CAACA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAClB,MAAM,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAGN,CAAC,CAACO,QAAQ,CAACJ,CAAC,CAAC;EAC5B,OAAOH,CAAC,CAACQ,SAAS,CAAC,MAAM;IACvBH,CAAC,KAAK,KAAK,CAAC,IAAIH,CAAC,CAACO,OAAO,IAAIH,CAAC,CAACJ,CAAC,CAACO,OAAO,CAACC,WAAW,CAAC;EACvD,CAAC,EAAEN,CAAC,CAAC,EAAEC,CAAC;AACV;AACA,SAASM,CAACA,CAACT,CAAC,EAAE;EACZ,OAAO,OAAOA,CAAC,IAAI,QAAQ,GAAGU,MAAM,CAACV,CAAC,CAACW,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,GAAGX,CAAC,IAAI,KAAK,CAAC;AACzE;AACA,SAASY,CAACA,CAACZ,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACrB,MAAMC,CAAC,GAAGK,CAAC,CAACR,CAAC,CAACY,aAAa,CAACC,KAAK,CAAC;IAAEC,CAAC,GAAG,CAACb,CAAC,CAACY,KAAK,KAAK,KAAK,CAAC,IAAIL,CAAC,CAACP,CAAC,CAACY,KAAK,CAAC,MAAMV,CAAC;IAAEY,CAAC,GAAGb,CAAC,CAACW,KAAK,KAAK,KAAK,CAAC;IAAEG,CAAC,GAAGF,CAAC,GAAGb,CAAC,CAACY,KAAK,GAAGE,CAAC,GAAGb,CAAC,CAACW,KAAK,GAAGV,CAAC;IAAEc,CAAC,GAAGT,CAAC,CAACV,CAAC,CAACC,CAAC,EAAEiB,CAAC,CAAC,CAAC;EAC3J,OAAOF,CAAC,IAAIC,CAAC,GAAGC,CAAC,GAAGC,CAAC,IAAId,CAAC,IAAIc,CAAC,GAAGd,CAAC,GAAGc,CAAC,GAAGd,CAAC;AAC7C;AACA,SACEQ,CAAC,IAAIO,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}