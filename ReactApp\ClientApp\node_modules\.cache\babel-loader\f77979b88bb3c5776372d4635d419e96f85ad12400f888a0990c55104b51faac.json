{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport ResizeObserver from 'resize-observer-polyfill';\nimport contains from \"rc-util/es/Dom/contains\";\nexport function isSamePoint(prev, next) {\n  if (prev === next) return true;\n  if (!prev || !next) return false;\n  if ('pageX' in next && 'pageY' in next) {\n    return prev.pageX === next.pageX && prev.pageY === next.pageY;\n  }\n  if ('clientX' in next && 'clientY' in next) {\n    return prev.clientX === next.clientX && prev.clientY === next.clientY;\n  }\n  return false;\n}\nexport function restoreFocus(activeElement, container) {\n  // Focus back if is in the container\n  if (activeElement !== document.activeElement && contains(container, activeElement) && typeof activeElement.focus === 'function') {\n    activeElement.focus();\n  }\n}\nexport function monitorResize(element, callback) {\n  var prevWidth = null;\n  var prevHeight = null;\n  function onResize(_ref) {\n    var _ref2 = _slicedToArray(_ref, 1),\n      target = _ref2[0].target;\n    if (!document.documentElement.contains(target)) return;\n    var _target$getBoundingCl = target.getBoundingClientRect(),\n      width = _target$getBoundingCl.width,\n      height = _target$getBoundingCl.height;\n    var fixedWidth = Math.floor(width);\n    var fixedHeight = Math.floor(height);\n    if (prevWidth !== fixedWidth || prevHeight !== fixedHeight) {\n      // https://webkit.org/blog/9997/resizeobserver-in-webkit/\n      Promise.resolve().then(function () {\n        callback({\n          width: fixedWidth,\n          height: fixedHeight\n        });\n      });\n    }\n    prevWidth = fixedWidth;\n    prevHeight = fixedHeight;\n  }\n  var resizeObserver = new ResizeObserver(onResize);\n  if (element) {\n    resizeObserver.observe(element);\n  }\n  return function () {\n    resizeObserver.disconnect();\n  };\n}", "map": {"version": 3, "names": ["_slicedToArray", "ResizeObserver", "contains", "isSamePoint", "prev", "next", "pageX", "pageY", "clientX", "clientY", "restoreFocus", "activeElement", "container", "document", "focus", "monitorResize", "element", "callback", "prevWidth", "prevHeight", "onResize", "_ref", "_ref2", "target", "documentElement", "_target$getBoundingCl", "getBoundingClientRect", "width", "height", "fixedWidth", "Math", "floor", "fixedHeight", "Promise", "resolve", "then", "resizeObserver", "observe", "disconnect"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/rc-align/es/util.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport ResizeObserver from 'resize-observer-polyfill';\nimport contains from \"rc-util/es/Dom/contains\";\nexport function isSamePoint(prev, next) {\n  if (prev === next) return true;\n  if (!prev || !next) return false;\n\n  if ('pageX' in next && 'pageY' in next) {\n    return prev.pageX === next.pageX && prev.pageY === next.pageY;\n  }\n\n  if ('clientX' in next && 'clientY' in next) {\n    return prev.clientX === next.clientX && prev.clientY === next.clientY;\n  }\n\n  return false;\n}\nexport function restoreFocus(activeElement, container) {\n  // Focus back if is in the container\n  if (activeElement !== document.activeElement && contains(container, activeElement) && typeof activeElement.focus === 'function') {\n    activeElement.focus();\n  }\n}\nexport function monitorResize(element, callback) {\n  var prevWidth = null;\n  var prevHeight = null;\n\n  function onResize(_ref) {\n    var _ref2 = _slicedToArray(_ref, 1),\n        target = _ref2[0].target;\n\n    if (!document.documentElement.contains(target)) return;\n\n    var _target$getBoundingCl = target.getBoundingClientRect(),\n        width = _target$getBoundingCl.width,\n        height = _target$getBoundingCl.height;\n\n    var fixedWidth = Math.floor(width);\n    var fixedHeight = Math.floor(height);\n\n    if (prevWidth !== fixedWidth || prevHeight !== fixedHeight) {\n      // https://webkit.org/blog/9997/resizeobserver-in-webkit/\n      Promise.resolve().then(function () {\n        callback({\n          width: fixedWidth,\n          height: fixedHeight\n        });\n      });\n    }\n\n    prevWidth = fixedWidth;\n    prevHeight = fixedHeight;\n  }\n\n  var resizeObserver = new ResizeObserver(onResize);\n\n  if (element) {\n    resizeObserver.observe(element);\n  }\n\n  return function () {\n    resizeObserver.disconnect();\n  };\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,cAAc,MAAM,0BAA0B;AACrD,OAAOC,QAAQ,MAAM,yBAAyB;AAC9C,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACtC,IAAID,IAAI,KAAKC,IAAI,EAAE,OAAO,IAAI;EAC9B,IAAI,CAACD,IAAI,IAAI,CAACC,IAAI,EAAE,OAAO,KAAK;EAEhC,IAAI,OAAO,IAAIA,IAAI,IAAI,OAAO,IAAIA,IAAI,EAAE;IACtC,OAAOD,IAAI,CAACE,KAAK,KAAKD,IAAI,CAACC,KAAK,IAAIF,IAAI,CAACG,KAAK,KAAKF,IAAI,CAACE,KAAK;EAC/D;EAEA,IAAI,SAAS,IAAIF,IAAI,IAAI,SAAS,IAAIA,IAAI,EAAE;IAC1C,OAAOD,IAAI,CAACI,OAAO,KAAKH,IAAI,CAACG,OAAO,IAAIJ,IAAI,CAACK,OAAO,KAAKJ,IAAI,CAACI,OAAO;EACvE;EAEA,OAAO,KAAK;AACd;AACA,OAAO,SAASC,YAAYA,CAACC,aAAa,EAAEC,SAAS,EAAE;EACrD;EACA,IAAID,aAAa,KAAKE,QAAQ,CAACF,aAAa,IAAIT,QAAQ,CAACU,SAAS,EAAED,aAAa,CAAC,IAAI,OAAOA,aAAa,CAACG,KAAK,KAAK,UAAU,EAAE;IAC/HH,aAAa,CAACG,KAAK,CAAC,CAAC;EACvB;AACF;AACA,OAAO,SAASC,aAAaA,CAACC,OAAO,EAAEC,QAAQ,EAAE;EAC/C,IAAIC,SAAS,GAAG,IAAI;EACpB,IAAIC,UAAU,GAAG,IAAI;EAErB,SAASC,QAAQA,CAACC,IAAI,EAAE;IACtB,IAAIC,KAAK,GAAGtB,cAAc,CAACqB,IAAI,EAAE,CAAC,CAAC;MAC/BE,MAAM,GAAGD,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM;IAE5B,IAAI,CAACV,QAAQ,CAACW,eAAe,CAACtB,QAAQ,CAACqB,MAAM,CAAC,EAAE;IAEhD,IAAIE,qBAAqB,GAAGF,MAAM,CAACG,qBAAqB,CAAC,CAAC;MACtDC,KAAK,GAAGF,qBAAqB,CAACE,KAAK;MACnCC,MAAM,GAAGH,qBAAqB,CAACG,MAAM;IAEzC,IAAIC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC;IAClC,IAAIK,WAAW,GAAGF,IAAI,CAACC,KAAK,CAACH,MAAM,CAAC;IAEpC,IAAIV,SAAS,KAAKW,UAAU,IAAIV,UAAU,KAAKa,WAAW,EAAE;MAC1D;MACAC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,YAAY;QACjClB,QAAQ,CAAC;UACPU,KAAK,EAAEE,UAAU;UACjBD,MAAM,EAAEI;QACV,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IAEAd,SAAS,GAAGW,UAAU;IACtBV,UAAU,GAAGa,WAAW;EAC1B;EAEA,IAAII,cAAc,GAAG,IAAInC,cAAc,CAACmB,QAAQ,CAAC;EAEjD,IAAIJ,OAAO,EAAE;IACXoB,cAAc,CAACC,OAAO,CAACrB,OAAO,CAAC;EACjC;EAEA,OAAO,YAAY;IACjBoB,cAAc,CAACE,UAAU,CAAC,CAAC;EAC7B,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}