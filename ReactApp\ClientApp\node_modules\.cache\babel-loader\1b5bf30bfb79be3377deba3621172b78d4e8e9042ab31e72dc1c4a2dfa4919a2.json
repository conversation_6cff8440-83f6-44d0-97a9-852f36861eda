{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport { windowStage as c } from \"./StageEnum.mjs\";\nimport { Button as a } from \"@progress/kendo-react-buttons\";\nimport { useLocalization as x } from \"@progress/kendo-react-intl\";\nimport { windowMinimizeIcon as I, windowIcon as L, windowRestoreIcon as M, xIcon as N } from \"@progress/kendo-svg-icons\";\nimport { dialogsWindowMinimizeButton as w, messages as l, dialogsWindowMaximizeButton as u, dialogsWindowRestoreButton as g, dialogsWindowCloseButton as B } from \"./messages/index.mjs\";\nconst v = t => {\n    const {\n        children: o,\n        onCloseButtonClick: r,\n        onMinimizeButtonClick: m,\n        onFullScreenButtonClick: s,\n        onRestoreButtonClick: d,\n        onDoubleClick: f,\n        stage: n,\n        forwardedRef: k,\n        id: C\n      } = t,\n      i = x(),\n      E = t.minimizeButton ? /* @__PURE__ */e.createElement(t.minimizeButton, {\n        onClick: m,\n        stage: n\n      }) : /* @__PURE__ */e.createElement(a, {\n        fillMode: \"flat\",\n        icon: \"window-minimize\",\n        svgIcon: I,\n        className: \"k-window-titlebar-action\",\n        onClick: m,\n        \"aria-label\": i.toLanguageString(w, l[w])\n      }),\n      z = t.maximizeButton ? /* @__PURE__ */e.createElement(t.maximizeButton, {\n        onClick: s,\n        stage: n\n      }) : /* @__PURE__ */e.createElement(a, {\n        fillMode: \"flat\",\n        icon: \"window-maximize\",\n        svgIcon: L,\n        className: \"k-window-titlebar-action\",\n        onClick: s,\n        \"aria-label\": i.toLanguageString(u, l[u])\n      }),\n      b = t.restoreButton ? /* @__PURE__ */e.createElement(t.restoreButton, {\n        onClick: d,\n        stage: n\n      }) : /* @__PURE__ */e.createElement(a, {\n        fillMode: \"flat\",\n        icon: \"window-restore\",\n        svgIcon: M,\n        className: \"k-window-titlebar-action\",\n        onClick: d,\n        \"aria-label\": i.toLanguageString(g, l[g])\n      }),\n      R = t.closeButton ? /* @__PURE__ */e.createElement(t.closeButton, {\n        onClick: r,\n        stage: n\n      }) : /* @__PURE__ */e.createElement(a, {\n        fillMode: \"flat\",\n        icon: \"x\",\n        svgIcon: N,\n        className: \"k-window-titlebar-action\",\n        onClick: r,\n        \"aria-label\": i.toLanguageString(B, l[B])\n      });\n    return /* @__PURE__ */e.createElement(\"div\", {\n      className: \"k-window-titlebar\",\n      style: {\n        touchAction: \"none\"\n      },\n      ref: k,\n      onDoubleClick: f\n    }, /* @__PURE__ */e.createElement(\"span\", {\n      className: \"k-window-title\",\n      id: C\n    }, o || \"\"), /* @__PURE__ */e.createElement(\"div\", {\n      className: \"k-window-titlebar-actions\"\n    }, n === c.DEFAULT && E, n === c.DEFAULT && z, n !== c.DEFAULT && b, R));\n  },\n  F = e.forwardRef((t, o) => /* @__PURE__ */e.createElement(v, {\n    ...t,\n    forwardedRef: o\n  }));\nexport { F as WindowTitleBar };", "map": {"version": 3, "names": ["e", "windowStage", "c", "<PERSON><PERSON>", "a", "useLocalization", "x", "windowMinimizeIcon", "I", "windowIcon", "L", "windowRestoreIcon", "M", "xIcon", "N", "dialogsWindowMinimizeButton", "w", "messages", "l", "dialogsWindowMaximizeButton", "u", "dialogsWindowRestoreButton", "g", "dialogsWindowCloseButton", "B", "v", "t", "children", "o", "onCloseButtonClick", "r", "onMinimizeButtonClick", "m", "onFullScreenButtonClick", "s", "onRestoreButtonClick", "d", "onDoubleClick", "f", "stage", "n", "forwardedRef", "k", "id", "C", "i", "E", "minimizeButton", "createElement", "onClick", "fillMode", "icon", "svgIcon", "className", "toLanguageString", "z", "maximizeButton", "b", "restoreButton", "R", "closeButton", "style", "touchAction", "ref", "DEFAULT", "F", "forwardRef", "WindowTitleBar"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dialogs/WindowTitlebar.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport { windowStage as c } from \"./StageEnum.mjs\";\nimport { Button as a } from \"@progress/kendo-react-buttons\";\nimport { useLocalization as x } from \"@progress/kendo-react-intl\";\nimport { windowMinimizeIcon as I, windowIcon as L, windowRestoreIcon as M, xIcon as N } from \"@progress/kendo-svg-icons\";\nimport { dialogsWindowMinimizeButton as w, messages as l, dialogsWindowMaximizeButton as u, dialogsWindowRestoreButton as g, dialogsWindowCloseButton as B } from \"./messages/index.mjs\";\nconst v = (t) => {\n  const {\n    children: o,\n    onCloseButtonClick: r,\n    onMinimizeButtonClick: m,\n    onFullScreenButtonClick: s,\n    onRestoreButtonClick: d,\n    onDoubleClick: f,\n    stage: n,\n    forwardedRef: k,\n    id: C\n  } = t, i = x(), E = t.minimizeButton ? /* @__PURE__ */ e.createElement(t.minimizeButton, { onClick: m, stage: n }) : /* @__PURE__ */ e.createElement(\n    a,\n    {\n      fillMode: \"flat\",\n      icon: \"window-minimize\",\n      svgIcon: I,\n      className: \"k-window-titlebar-action\",\n      onClick: m,\n      \"aria-label\": i.toLanguageString(\n        w,\n        l[w]\n      )\n    }\n  ), z = t.maximizeButton ? /* @__PURE__ */ e.createElement(t.maximizeButton, { onClick: s, stage: n }) : /* @__PURE__ */ e.createElement(\n    a,\n    {\n      fillMode: \"flat\",\n      icon: \"window-maximize\",\n      svgIcon: L,\n      className: \"k-window-titlebar-action\",\n      onClick: s,\n      \"aria-label\": i.toLanguageString(\n        u,\n        l[u]\n      )\n    }\n  ), b = t.restoreButton ? /* @__PURE__ */ e.createElement(t.restoreButton, { onClick: d, stage: n }) : /* @__PURE__ */ e.createElement(\n    a,\n    {\n      fillMode: \"flat\",\n      icon: \"window-restore\",\n      svgIcon: M,\n      className: \"k-window-titlebar-action\",\n      onClick: d,\n      \"aria-label\": i.toLanguageString(g, l[g])\n    }\n  ), R = t.closeButton ? /* @__PURE__ */ e.createElement(t.closeButton, { onClick: r, stage: n }) : /* @__PURE__ */ e.createElement(\n    a,\n    {\n      fillMode: \"flat\",\n      icon: \"x\",\n      svgIcon: N,\n      className: \"k-window-titlebar-action\",\n      onClick: r,\n      \"aria-label\": i.toLanguageString(B, l[B])\n    }\n  );\n  return /* @__PURE__ */ e.createElement(\n    \"div\",\n    {\n      className: \"k-window-titlebar\",\n      style: { touchAction: \"none\" },\n      ref: k,\n      onDoubleClick: f\n    },\n    /* @__PURE__ */ e.createElement(\"span\", { className: \"k-window-title\", id: C }, o || \"\"),\n    /* @__PURE__ */ e.createElement(\"div\", { className: \"k-window-titlebar-actions\" }, n === c.DEFAULT && E, n === c.DEFAULT && z, n !== c.DEFAULT && b, R)\n  );\n}, F = e.forwardRef((t, o) => /* @__PURE__ */ e.createElement(v, { ...t, forwardedRef: o }));\nexport {\n  F as WindowTitleBar\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,SAASC,WAAW,IAAIC,CAAC,QAAQ,iBAAiB;AAClD,SAASC,MAAM,IAAIC,CAAC,QAAQ,+BAA+B;AAC3D,SAASC,eAAe,IAAIC,CAAC,QAAQ,4BAA4B;AACjE,SAASC,kBAAkB,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,EAAEC,iBAAiB,IAAIC,CAAC,EAAEC,KAAK,IAAIC,CAAC,QAAQ,2BAA2B;AACxH,SAASC,2BAA2B,IAAIC,CAAC,EAAEC,QAAQ,IAAIC,CAAC,EAAEC,2BAA2B,IAAIC,CAAC,EAAEC,0BAA0B,IAAIC,CAAC,EAAEC,wBAAwB,IAAIC,CAAC,QAAQ,sBAAsB;AACxL,MAAMC,CAAC,GAAIC,CAAC,IAAK;IACf,MAAM;QACJC,QAAQ,EAAEC,CAAC;QACXC,kBAAkB,EAAEC,CAAC;QACrBC,qBAAqB,EAAEC,CAAC;QACxBC,uBAAuB,EAAEC,CAAC;QAC1BC,oBAAoB,EAAEC,CAAC;QACvBC,aAAa,EAAEC,CAAC;QAChBC,KAAK,EAAEC,CAAC;QACRC,YAAY,EAAEC,CAAC;QACfC,EAAE,EAAEC;MACN,CAAC,GAAGlB,CAAC;MAAEmB,CAAC,GAAGvC,CAAC,CAAC,CAAC;MAAEwC,CAAC,GAAGpB,CAAC,CAACqB,cAAc,GAAG,eAAgB/C,CAAC,CAACgD,aAAa,CAACtB,CAAC,CAACqB,cAAc,EAAE;QAAEE,OAAO,EAAEjB,CAAC;QAAEO,KAAK,EAAEC;MAAE,CAAC,CAAC,GAAG,eAAgBxC,CAAC,CAACgD,aAAa,CAClJ5C,CAAC,EACD;QACE8C,QAAQ,EAAE,MAAM;QAChBC,IAAI,EAAE,iBAAiB;QACvBC,OAAO,EAAE5C,CAAC;QACV6C,SAAS,EAAE,0BAA0B;QACrCJ,OAAO,EAAEjB,CAAC;QACV,YAAY,EAAEa,CAAC,CAACS,gBAAgB,CAC9BtC,CAAC,EACDE,CAAC,CAACF,CAAC,CACL;MACF,CACF,CAAC;MAAEuC,CAAC,GAAG7B,CAAC,CAAC8B,cAAc,GAAG,eAAgBxD,CAAC,CAACgD,aAAa,CAACtB,CAAC,CAAC8B,cAAc,EAAE;QAAEP,OAAO,EAAEf,CAAC;QAAEK,KAAK,EAAEC;MAAE,CAAC,CAAC,GAAG,eAAgBxC,CAAC,CAACgD,aAAa,CACrI5C,CAAC,EACD;QACE8C,QAAQ,EAAE,MAAM;QAChBC,IAAI,EAAE,iBAAiB;QACvBC,OAAO,EAAE1C,CAAC;QACV2C,SAAS,EAAE,0BAA0B;QACrCJ,OAAO,EAAEf,CAAC;QACV,YAAY,EAAEW,CAAC,CAACS,gBAAgB,CAC9BlC,CAAC,EACDF,CAAC,CAACE,CAAC,CACL;MACF,CACF,CAAC;MAAEqC,CAAC,GAAG/B,CAAC,CAACgC,aAAa,GAAG,eAAgB1D,CAAC,CAACgD,aAAa,CAACtB,CAAC,CAACgC,aAAa,EAAE;QAAET,OAAO,EAAEb,CAAC;QAAEG,KAAK,EAAEC;MAAE,CAAC,CAAC,GAAG,eAAgBxC,CAAC,CAACgD,aAAa,CACnI5C,CAAC,EACD;QACE8C,QAAQ,EAAE,MAAM;QAChBC,IAAI,EAAE,gBAAgB;QACtBC,OAAO,EAAExC,CAAC;QACVyC,SAAS,EAAE,0BAA0B;QACrCJ,OAAO,EAAEb,CAAC;QACV,YAAY,EAAES,CAAC,CAACS,gBAAgB,CAAChC,CAAC,EAAEJ,CAAC,CAACI,CAAC,CAAC;MAC1C,CACF,CAAC;MAAEqC,CAAC,GAAGjC,CAAC,CAACkC,WAAW,GAAG,eAAgB5D,CAAC,CAACgD,aAAa,CAACtB,CAAC,CAACkC,WAAW,EAAE;QAAEX,OAAO,EAAEnB,CAAC;QAAES,KAAK,EAAEC;MAAE,CAAC,CAAC,GAAG,eAAgBxC,CAAC,CAACgD,aAAa,CAC/H5C,CAAC,EACD;QACE8C,QAAQ,EAAE,MAAM;QAChBC,IAAI,EAAE,GAAG;QACTC,OAAO,EAAEtC,CAAC;QACVuC,SAAS,EAAE,0BAA0B;QACrCJ,OAAO,EAAEnB,CAAC;QACV,YAAY,EAAEe,CAAC,CAACS,gBAAgB,CAAC9B,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC;MAC1C,CACF,CAAC;IACD,OAAO,eAAgBxB,CAAC,CAACgD,aAAa,CACpC,KAAK,EACL;MACEK,SAAS,EAAE,mBAAmB;MAC9BQ,KAAK,EAAE;QAAEC,WAAW,EAAE;MAAO,CAAC;MAC9BC,GAAG,EAAErB,CAAC;MACNL,aAAa,EAAEC;IACjB,CAAC,EACD,eAAgBtC,CAAC,CAACgD,aAAa,CAAC,MAAM,EAAE;MAAEK,SAAS,EAAE,gBAAgB;MAAEV,EAAE,EAAEC;IAAE,CAAC,EAAEhB,CAAC,IAAI,EAAE,CAAC,EACxF,eAAgB5B,CAAC,CAACgD,aAAa,CAAC,KAAK,EAAE;MAAEK,SAAS,EAAE;IAA4B,CAAC,EAAEb,CAAC,KAAKtC,CAAC,CAAC8D,OAAO,IAAIlB,CAAC,EAAEN,CAAC,KAAKtC,CAAC,CAAC8D,OAAO,IAAIT,CAAC,EAAEf,CAAC,KAAKtC,CAAC,CAAC8D,OAAO,IAAIP,CAAC,EAAEE,CAAC,CACxJ,CAAC;EACH,CAAC;EAAEM,CAAC,GAAGjE,CAAC,CAACkE,UAAU,CAAC,CAACxC,CAAC,EAAEE,CAAC,KAAK,eAAgB5B,CAAC,CAACgD,aAAa,CAACvB,CAAC,EAAE;IAAE,GAAGC,CAAC;IAAEe,YAAY,EAAEb;EAAE,CAAC,CAAC,CAAC;AAC5F,SACEqC,CAAC,IAAIE,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}