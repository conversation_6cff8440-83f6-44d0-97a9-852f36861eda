{"ast": null, "code": "import GradientNode from './gradient-node';\nvar LinearGradientNode = function (GradientNode) {\n  function LinearGradientNode() {\n    GradientNode.apply(this, arguments);\n  }\n  if (GradientNode) LinearGradientNode.__proto__ = GradientNode;\n  LinearGradientNode.prototype = Object.create(GradientNode && GradientNode.prototype);\n  LinearGradientNode.prototype.constructor = LinearGradientNode;\n  LinearGradientNode.prototype.template = function template() {\n    return \"<linearGradient id='\" + this.id + \"' \" + this.renderCoordinates() + \">\" + this.renderChildren() + \"</linearGradient>\";\n  };\n  LinearGradientNode.prototype.mapCoordinates = function mapCoordinates() {\n    var srcElement = this.srcElement;\n    var start = srcElement.start();\n    var end = srcElement.end();\n    var attrs = [[\"x1\", start.x], [\"y1\", start.y], [\"x2\", end.x], [\"y2\", end.y], this.mapSpace()];\n    return attrs;\n  };\n  return LinearGradientNode;\n}(GradientNode);\nexport default LinearGradientNode;", "map": {"version": 3, "names": ["GradientNode", "LinearGradientNode", "apply", "arguments", "__proto__", "prototype", "Object", "create", "constructor", "template", "id", "renderCoordinates", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mapCoordinates", "srcElement", "start", "end", "attrs", "x", "y", "mapSpace"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/svg/linear-gradient-node.js"], "sourcesContent": ["import GradientNode from './gradient-node';\n\nvar LinearGradientNode = (function (GradientNode) {\n    function LinearGradientNode () {\n        GradientNode.apply(this, arguments);\n    }\n\n    if ( GradientNode ) LinearGradientNode.__proto__ = GradientNode;\n    LinearGradientNode.prototype = Object.create( GradientNode && GradientNode.prototype );\n    LinearGradientNode.prototype.constructor = LinearGradientNode;\n\n    LinearGradientNode.prototype.template = function template () {\n        return (\"<linearGradient id='\" + (this.id) + \"' \" + (this.renderCoordinates()) + \">\" + (this.renderChildren()) + \"</linearGradient>\");\n    };\n\n    LinearGradientNode.prototype.mapCoordinates = function mapCoordinates () {\n        var srcElement = this.srcElement;\n        var start = srcElement.start();\n        var end = srcElement.end();\n        var attrs = [\n            [ \"x1\", start.x ],\n            [ \"y1\", start.y ],\n            [ \"x2\", end.x ],\n            [ \"y2\", end.y ],\n            this.mapSpace()\n        ];\n\n        return attrs;\n    };\n\n    return LinearGradientNode;\n}(GradientNode));\n\nexport default LinearGradientNode;"], "mappings": "AAAA,OAAOA,YAAY,MAAM,iBAAiB;AAE1C,IAAIC,kBAAkB,GAAI,UAAUD,YAAY,EAAE;EAC9C,SAASC,kBAAkBA,CAAA,EAAI;IAC3BD,YAAY,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACvC;EAEA,IAAKH,YAAY,EAAGC,kBAAkB,CAACG,SAAS,GAAGJ,YAAY;EAC/DC,kBAAkB,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAEP,YAAY,IAAIA,YAAY,CAACK,SAAU,CAAC;EACtFJ,kBAAkB,CAACI,SAAS,CAACG,WAAW,GAAGP,kBAAkB;EAE7DA,kBAAkB,CAACI,SAAS,CAACI,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;IACzD,OAAQ,sBAAsB,GAAI,IAAI,CAACC,EAAG,GAAG,IAAI,GAAI,IAAI,CAACC,iBAAiB,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAACC,cAAc,CAAC,CAAE,GAAG,mBAAmB;EACxI,CAAC;EAEDX,kBAAkB,CAACI,SAAS,CAACQ,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAI;IACrE,IAAIC,UAAU,GAAG,IAAI,CAACA,UAAU;IAChC,IAAIC,KAAK,GAAGD,UAAU,CAACC,KAAK,CAAC,CAAC;IAC9B,IAAIC,GAAG,GAAGF,UAAU,CAACE,GAAG,CAAC,CAAC;IAC1B,IAAIC,KAAK,GAAG,CACR,CAAE,IAAI,EAAEF,KAAK,CAACG,CAAC,CAAE,EACjB,CAAE,IAAI,EAAEH,KAAK,CAACI,CAAC,CAAE,EACjB,CAAE,IAAI,EAAEH,GAAG,CAACE,CAAC,CAAE,EACf,CAAE,IAAI,EAAEF,GAAG,CAACG,CAAC,CAAE,EACf,IAAI,CAACC,QAAQ,CAAC,CAAC,CAClB;IAED,OAAOH,KAAK;EAChB,CAAC;EAED,OAAOhB,kBAAkB;AAC7B,CAAC,CAACD,YAAY,CAAE;AAEhB,eAAeC,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}