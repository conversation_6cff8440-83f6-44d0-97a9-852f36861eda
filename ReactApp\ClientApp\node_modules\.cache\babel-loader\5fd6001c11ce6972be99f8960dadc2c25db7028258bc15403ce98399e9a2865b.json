{"ast": null, "code": "import Matrix from '../geometry/matrix';\nimport toMatrix from '../geometry/to-matrix';\nvar IDENTITY_MATRIX_HASH = Matrix.IDENTITY.toString();\nvar measurable = function (TBase) {\n  return function (TBase) {\n    function anonymous() {\n      TBase.apply(this, arguments);\n    }\n    if (TBase) anonymous.__proto__ = TBase;\n    anonymous.prototype = Object.create(TBase && TBase.prototype);\n    anonymous.prototype.constructor = anonymous;\n    anonymous.prototype.bbox = function bbox(transformation) {\n      var combinedMatrix = toMatrix(this.currentTransform(transformation));\n      var matrixHash = combinedMatrix ? combinedMatrix.toString() : IDENTITY_MATRIX_HASH;\n      var bbox;\n      if (this._bboxCache && this._matrixHash === matrixHash) {\n        bbox = this._bboxCache.clone();\n      } else {\n        bbox = this._bbox(combinedMatrix);\n        this._bboxCache = bbox ? bbox.clone() : null;\n        this._matrixHash = matrixHash;\n      }\n      var strokeWidth = this.options.get(\"stroke.width\");\n      if (strokeWidth && bbox) {\n        bbox.expand(strokeWidth / 2);\n      }\n      return bbox;\n    };\n    anonymous.prototype.geometryChange = function geometryChange() {\n      delete this._bboxCache;\n      this.trigger(\"geometryChange\", {\n        element: this\n      });\n    };\n    return anonymous;\n  }(TBase);\n};\nexport default measurable;", "map": {"version": 3, "names": ["Matrix", "toMatrix", "IDENTITY_MATRIX_HASH", "IDENTITY", "toString", "measurable", "TBase", "anonymous", "apply", "arguments", "__proto__", "prototype", "Object", "create", "constructor", "bbox", "transformation", "combinedMatrix", "currentTransform", "matrixHash", "_bboxCache", "_matrixHash", "clone", "_bbox", "strokeWidth", "options", "get", "expand", "geometryChange", "trigger", "element"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/mixins/measurable.js"], "sourcesContent": ["import Matrix from '../geometry/matrix';\nimport toMatrix from '../geometry/to-matrix';\n\nvar IDENTITY_MATRIX_HASH = Matrix.IDENTITY.toString();\n\nvar measurable = function (TBase) { return (\n    (function (TBase) {\n        function anonymous () {\n            TBase.apply(this, arguments);\n        }\n\n        if ( TBase ) anonymous.__proto__ = TBase;\n        anonymous.prototype = Object.create( TBase && TBase.prototype );\n        anonymous.prototype.constructor = anonymous;\n\n        anonymous.prototype.bbox = function bbox (transformation) {\n            var combinedMatrix = toMatrix(this.currentTransform(transformation));\n            var matrixHash = combinedMatrix ? combinedMatrix.toString() : IDENTITY_MATRIX_HASH;\n            var bbox;\n\n            if (this._bboxCache && this._matrixHash === matrixHash) {\n                bbox = this._bboxCache.clone();\n            } else {\n                bbox = this._bbox(combinedMatrix);\n                this._bboxCache = bbox ? bbox.clone() : null;\n                this._matrixHash = matrixHash;\n            }\n\n            var strokeWidth = this.options.get(\"stroke.width\");\n            if (strokeWidth && bbox) {\n                bbox.expand(strokeWidth / 2);\n            }\n\n            return bbox;\n        };\n\n        anonymous.prototype.geometryChange = function geometryChange () {\n            delete this._bboxCache;\n            this.trigger(\"geometryChange\", {\n                element: this\n            });\n        };\n\n        return anonymous;\n    }(TBase))\n); };\n\nexport default measurable;"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,QAAQ,MAAM,uBAAuB;AAE5C,IAAIC,oBAAoB,GAAGF,MAAM,CAACG,QAAQ,CAACC,QAAQ,CAAC,CAAC;AAErD,IAAIC,UAAU,GAAG,SAAAA,CAAUC,KAAK,EAAE;EAAE,OAC/B,UAAUA,KAAK,EAAE;IACd,SAASC,SAASA,CAAA,EAAI;MAClBD,KAAK,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAChC;IAEA,IAAKH,KAAK,EAAGC,SAAS,CAACG,SAAS,GAAGJ,KAAK;IACxCC,SAAS,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAEP,KAAK,IAAIA,KAAK,CAACK,SAAU,CAAC;IAC/DJ,SAAS,CAACI,SAAS,CAACG,WAAW,GAAGP,SAAS;IAE3CA,SAAS,CAACI,SAAS,CAACI,IAAI,GAAG,SAASA,IAAIA,CAAEC,cAAc,EAAE;MACtD,IAAIC,cAAc,GAAGhB,QAAQ,CAAC,IAAI,CAACiB,gBAAgB,CAACF,cAAc,CAAC,CAAC;MACpE,IAAIG,UAAU,GAAGF,cAAc,GAAGA,cAAc,CAACb,QAAQ,CAAC,CAAC,GAAGF,oBAAoB;MAClF,IAAIa,IAAI;MAER,IAAI,IAAI,CAACK,UAAU,IAAI,IAAI,CAACC,WAAW,KAAKF,UAAU,EAAE;QACpDJ,IAAI,GAAG,IAAI,CAACK,UAAU,CAACE,KAAK,CAAC,CAAC;MAClC,CAAC,MAAM;QACHP,IAAI,GAAG,IAAI,CAACQ,KAAK,CAACN,cAAc,CAAC;QACjC,IAAI,CAACG,UAAU,GAAGL,IAAI,GAAGA,IAAI,CAACO,KAAK,CAAC,CAAC,GAAG,IAAI;QAC5C,IAAI,CAACD,WAAW,GAAGF,UAAU;MACjC;MAEA,IAAIK,WAAW,GAAG,IAAI,CAACC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;MAClD,IAAIF,WAAW,IAAIT,IAAI,EAAE;QACrBA,IAAI,CAACY,MAAM,CAACH,WAAW,GAAG,CAAC,CAAC;MAChC;MAEA,OAAOT,IAAI;IACf,CAAC;IAEDR,SAAS,CAACI,SAAS,CAACiB,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAI;MAC5D,OAAO,IAAI,CAACR,UAAU;MACtB,IAAI,CAACS,OAAO,CAAC,gBAAgB,EAAE;QAC3BC,OAAO,EAAE;MACb,CAAC,CAAC;IACN,CAAC;IAED,OAAOvB,SAAS;EACpB,CAAC,CAACD,KAAK,CAAC;AACT,CAAC;AAEJ,eAAeD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}