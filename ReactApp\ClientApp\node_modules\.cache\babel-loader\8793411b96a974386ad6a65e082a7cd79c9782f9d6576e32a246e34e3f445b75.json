{"ast": null, "code": "export var KeyCode = {\n  BACKSPACE: 8,\n  DELETE: 46,\n  TAB: 9,\n  ENTER: 13,\n  ESCAPE: 27,\n  ARROW_LEFT: 37,\n  ARROW_UP: 38,\n  ARROW_RIGHT: 39,\n  ARROW_DOWN: 40,\n  SPACE: 32,\n  END: 35,\n  <PERSON>OM<PERSON>: 36,\n  PAGE_UP: 33,\n  PAGE_DOWN: 34\n};", "map": {"version": 3, "names": ["KeyCode", "BACKSPACE", "DELETE", "TAB", "ENTER", "ESCAPE", "ARROW_LEFT", "ARROW_UP", "ARROW_RIGHT", "ARROW_DOWN", "SPACE", "END", "HOME", "PAGE_UP", "PAGE_DOWN"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-dateinputs-common/dist/es/common/keycode.js"], "sourcesContent": ["export var KeyCode = {\n    BACKSPACE: 8,\n    DELETE: 46,\n    TAB: 9,\n    ENTER: 13,\n    ESCAPE: 27,\n    ARROW_LEFT: 37,\n    ARROW_UP: 38,\n    ARROW_RIGHT: 39,\n    ARROW_DOWN: 40,\n    SPACE: 32,\n    END: 35,\n    <PERSON>OM<PERSON>: 36,\n    PAGE_UP: 33,\n    PAGE_DOWN: 34\n};\n"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACjBC,SAAS,EAAE,CAAC;EACZC,MAAM,EAAE,EAAE;EACVC,GAAG,EAAE,CAAC;EACNC,KAAK,EAAE,EAAE;EACTC,MAAM,EAAE,EAAE;EACVC,UAAU,EAAE,EAAE;EACdC,QAAQ,EAAE,EAAE;EACZC,WAAW,EAAE,EAAE;EACfC,UAAU,EAAE,EAAE;EACdC,KAAK,EAAE,EAAE;EACTC,GAAG,EAAE,EAAE;EACPC,IAAI,EAAE,EAAE;EACRC,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}