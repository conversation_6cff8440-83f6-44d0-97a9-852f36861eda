{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as t from \"react\";\nimport e from \"prop-types\";\nimport { useUnstyled as R, classNames as q, uAnimation as C } from \"@progress/kendo-react-common\";\nimport { AnimationChild as b } from \"./AnimationChild.mjs\";\nimport { TransitionGroup as A } from \"react-transition-group\";\nconst x = i => {\n  const {\n      id: o,\n      style: r,\n      children: s,\n      component: a = \"div\",\n      className: l,\n      childFactory: c,\n      stackChildren: E,\n      componentChildStyle: m,\n      componentChildClassName: d,\n      ...p\n    } = i,\n    u = R(),\n    n = i.unstyled || u,\n    y = n && n.uAnimation,\n    h = {\n      id: o,\n      style: r,\n      component: a,\n      childFactory: c,\n      className: q(C.child({\n        c: y\n      }), l)\n    },\n    f = t.Children.map(s || null, N => /* @__PURE__ */t.createElement(b, {\n      ...p,\n      unstyled: n,\n      style: m,\n      className: d\n    }, N));\n  return /* @__PURE__ */t.createElement(A, {\n    ...h\n  }, f);\n};\nx.propTypes = {\n  children: e.oneOfType([e.arrayOf(e.node), e.node]),\n  childFactory: e.any,\n  className: e.string,\n  component: e.node,\n  id: e.string,\n  style: e.any,\n  transitionName: e.string.isRequired,\n  appear: e.bool.isRequired,\n  enter: e.bool.isRequired,\n  exit: e.bool.isRequired,\n  transitionEnterDuration: e.number.isRequired,\n  transitionExitDuration: e.number.isRequired\n};\nexport { x as Animation };", "map": {"version": 3, "names": ["t", "e", "useUnstyled", "R", "classNames", "q", "uAnimation", "C", "AnimationChild", "b", "TransitionGroup", "A", "x", "i", "id", "o", "style", "r", "children", "s", "component", "a", "className", "l", "childFactory", "c", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "E", "componentChildStyle", "m", "componentChildClassName", "d", "p", "u", "n", "unstyled", "y", "h", "child", "f", "Children", "map", "N", "createElement", "propTypes", "oneOfType", "arrayOf", "node", "any", "string", "transitionName", "isRequired", "appear", "bool", "enter", "exit", "transitionEnterDuration", "number", "transitionExitDuration", "Animation"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-animation/Animation.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as t from \"react\";\nimport e from \"prop-types\";\nimport { useUnstyled as R, classNames as q, uAnimation as C } from \"@progress/kendo-react-common\";\nimport { AnimationChild as b } from \"./AnimationChild.mjs\";\nimport { TransitionGroup as A } from \"react-transition-group\";\nconst x = (i) => {\n  const {\n    id: o,\n    style: r,\n    children: s,\n    component: a = \"div\",\n    className: l,\n    childFactory: c,\n    stackChildren: E,\n    componentChildStyle: m,\n    componentChildClassName: d,\n    ...p\n  } = i, u = R(), n = i.unstyled || u, y = n && n.uAnimation, h = {\n    id: o,\n    style: r,\n    component: a,\n    childFactory: c,\n    className: q(C.child({ c: y }), l)\n  }, f = t.Children.map(s || null, (N) => /* @__PURE__ */ t.createElement(b, { ...p, unstyled: n, style: m, className: d }, N));\n  return /* @__PURE__ */ t.createElement(A, { ...h }, f);\n};\nx.propTypes = {\n  children: e.oneOfType([e.arrayOf(e.node), e.node]),\n  childFactory: e.any,\n  className: e.string,\n  component: e.node,\n  id: e.string,\n  style: e.any,\n  transitionName: e.string.isRequired,\n  appear: e.bool.isRequired,\n  enter: e.bool.isRequired,\n  exit: e.bool.isRequired,\n  transitionEnterDuration: e.number.isRequired,\n  transitionExitDuration: e.number.isRequired\n};\nexport {\n  x as Animation\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,WAAW,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AACjG,SAASC,cAAc,IAAIC,CAAC,QAAQ,sBAAsB;AAC1D,SAASC,eAAe,IAAIC,CAAC,QAAQ,wBAAwB;AAC7D,MAAMC,CAAC,GAAIC,CAAC,IAAK;EACf,MAAM;MACJC,EAAE,EAAEC,CAAC;MACLC,KAAK,EAAEC,CAAC;MACRC,QAAQ,EAAEC,CAAC;MACXC,SAAS,EAAEC,CAAC,GAAG,KAAK;MACpBC,SAAS,EAAEC,CAAC;MACZC,YAAY,EAAEC,CAAC;MACfC,aAAa,EAAEC,CAAC;MAChBC,mBAAmB,EAAEC,CAAC;MACtBC,uBAAuB,EAAEC,CAAC;MAC1B,GAAGC;IACL,CAAC,GAAGnB,CAAC;IAAEoB,CAAC,GAAG9B,CAAC,CAAC,CAAC;IAAE+B,CAAC,GAAGrB,CAAC,CAACsB,QAAQ,IAAIF,CAAC;IAAEG,CAAC,GAAGF,CAAC,IAAIA,CAAC,CAAC5B,UAAU;IAAE+B,CAAC,GAAG;MAC9DvB,EAAE,EAAEC,CAAC;MACLC,KAAK,EAAEC,CAAC;MACRG,SAAS,EAAEC,CAAC;MACZG,YAAY,EAAEC,CAAC;MACfH,SAAS,EAAEjB,CAAC,CAACE,CAAC,CAAC+B,KAAK,CAAC;QAAEb,CAAC,EAAEW;MAAE,CAAC,CAAC,EAAEb,CAAC;IACnC,CAAC;IAAEgB,CAAC,GAAGvC,CAAC,CAACwC,QAAQ,CAACC,GAAG,CAACtB,CAAC,IAAI,IAAI,EAAGuB,CAAC,IAAK,eAAgB1C,CAAC,CAAC2C,aAAa,CAAClC,CAAC,EAAE;MAAE,GAAGuB,CAAC;MAAEG,QAAQ,EAAED,CAAC;MAAElB,KAAK,EAAEa,CAAC;MAAEP,SAAS,EAAES;IAAE,CAAC,EAAEW,CAAC,CAAC,CAAC;EAC7H,OAAO,eAAgB1C,CAAC,CAAC2C,aAAa,CAAChC,CAAC,EAAE;IAAE,GAAG0B;EAAE,CAAC,EAAEE,CAAC,CAAC;AACxD,CAAC;AACD3B,CAAC,CAACgC,SAAS,GAAG;EACZ1B,QAAQ,EAAEjB,CAAC,CAAC4C,SAAS,CAAC,CAAC5C,CAAC,CAAC6C,OAAO,CAAC7C,CAAC,CAAC8C,IAAI,CAAC,EAAE9C,CAAC,CAAC8C,IAAI,CAAC,CAAC;EAClDvB,YAAY,EAAEvB,CAAC,CAAC+C,GAAG;EACnB1B,SAAS,EAAErB,CAAC,CAACgD,MAAM;EACnB7B,SAAS,EAAEnB,CAAC,CAAC8C,IAAI;EACjBjC,EAAE,EAAEb,CAAC,CAACgD,MAAM;EACZjC,KAAK,EAAEf,CAAC,CAAC+C,GAAG;EACZE,cAAc,EAAEjD,CAAC,CAACgD,MAAM,CAACE,UAAU;EACnCC,MAAM,EAAEnD,CAAC,CAACoD,IAAI,CAACF,UAAU;EACzBG,KAAK,EAAErD,CAAC,CAACoD,IAAI,CAACF,UAAU;EACxBI,IAAI,EAAEtD,CAAC,CAACoD,IAAI,CAACF,UAAU;EACvBK,uBAAuB,EAAEvD,CAAC,CAACwD,MAAM,CAACN,UAAU;EAC5CO,sBAAsB,EAAEzD,CAAC,CAACwD,MAAM,CAACN;AACnC,CAAC;AACD,SACEvC,CAAC,IAAI+C,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}