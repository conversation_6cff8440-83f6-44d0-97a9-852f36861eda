{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useEffect, useRef, useState } from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport { useAcceptTncMutation, useEvaluateTncQuery } from '@app/api/tncApiSlice';\nimport { useAppSelector } from '@app/hooks/useAppSelector';\nimport { errorNotification, successNotification } from '@app/utils/antNotifications';\nexport const useTermsAndConditions = () => {\n  _s();\n  var _location$state;\n  const navigate = useNavigate();\n  const location = useLocation();\n  const pdfViewerRef = useRef(null);\n  const [pdfDocument, setPdfDocument] = useState(null);\n  const tncDocument = useAppSelector(state => state.app.tnc);\n  const redirectPath = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.from) || '/';\n  const {\n    refetch: tncRefetch,\n    isFetching: tncLoading\n  } = useEvaluateTncQuery();\n  const [acceptTnc, {\n    isLoading: isAccepting\n  }] = useAcceptTncMutation();\n  useEffect(() => {\n    if (tncDocument.isValidated || !tncDocument.document) {\n      navigate(redirectPath);\n    }\n  }, [tncDocument, navigate]);\n  const onDocumentLoad = event => {\n    setPdfDocument(event.target);\n  };\n  const handleAgree = async () => {\n    let messageText;\n    try {\n      const response = await acceptTnc({\n        termsAndConditionsId: tncDocument.document.termsAndConditionsId\n      }).unwrap();\n      messageText = response.message || 'Terms & Conditions accepted successfully.';\n      successNotification([''], messageText);\n      navigate(redirectPath);\n    } catch (err) {\n      var _err$data;\n      messageText = 'Terms & Conditions acceptance failed';\n      if ((err === null || err === void 0 ? void 0 : err.status) >= 400 && (err === null || err === void 0 ? void 0 : err.status) < 500 && err !== null && err !== void 0 && (_err$data = err.data) !== null && _err$data !== void 0 && _err$data.message) {\n        var _err$data2;\n        messageText = err === null || err === void 0 ? void 0 : (_err$data2 = err.data) === null || _err$data2 === void 0 ? void 0 : _err$data2.message;\n      }\n      errorNotification([''], messageText);\n    }\n  };\n  const clickToolbarButtonByTitle = title => {\n    // const button = document.querySelector(`button[title=\"${title}\"]`) as HTMLElement;\n    // button?.click();\n  };\n  return {\n    tncDocument,\n    pdfViewerRef,\n    pdfDocument,\n    setPdfDocument,\n    tncRefetch,\n    tncLoading,\n    isAccepting,\n    onDocumentLoad,\n    handleAgree,\n    clickToolbarButtonByTitle\n  };\n};\n_s(useTermsAndConditions, \"s5ogJPJ64R313vsJC9KSA27UR18=\", false, function () {\n  return [useNavigate, useLocation, useAppSelector, useEvaluateTncQuery, useAcceptTncMutation];\n});", "map": {"version": 3, "names": ["useEffect", "useRef", "useState", "useLocation", "useNavigate", "useAcceptTncMutation", "useEvaluateTncQuery", "useAppSelector", "errorNotification", "successNotification", "useTermsAndConditions", "_s", "_location$state", "navigate", "location", "pdfViewerRef", "pdfDocument", "setPdfDocument", "tncDocument", "state", "app", "tnc", "redirectPath", "from", "refetch", "tncRefetch", "isFetching", "tncLoading", "acceptTnc", "isLoading", "isAccepting", "isValidated", "document", "onDocumentLoad", "event", "target", "handleAgree", "messageText", "response", "termsAndConditionsId", "unwrap", "message", "err", "_err$data", "status", "data", "_err$data2", "clickToolbarButtonByTitle", "title"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/src/app/hooks/useTermsAndConditions.ts"], "sourcesContent": ["import { useEffect, useRef, useState } from 'react';\r\nimport { useLocation, useNavigate } from 'react-router-dom';\r\nimport { message } from 'antd';\r\nimport { useAcceptTncMutation, useEvaluateTncQuery } from '@app/api/tncApiSlice';\r\nimport { useAppSelector } from '@app/hooks/useAppSelector';\r\nimport { errorNotification, successNotification } from '@app/utils/antNotifications';\r\n\r\nexport const useTermsAndConditions = () => {\r\n    const navigate = useNavigate();\r\n    const location = useLocation();\r\n    const pdfViewerRef = useRef<any>(null);\r\n    const [pdfDocument, setPdfDocument] = useState<any>(null);\r\n    const tncDocument = useAppSelector((state) => state.app.tnc);\r\n\r\n    const redirectPath = location.state?.from || '/';\r\n\r\n    const { refetch: tncRefetch, isFetching: tncLoading } = useEvaluateTncQuery();\r\n    const [acceptTnc, { isLoading: isAccepting }] = useAcceptTncMutation();\r\n\r\n    useEffect(() => {\r\n        if (tncDocument.isValidated || !tncDocument.document) {\r\n            navigate(redirectPath);\r\n        }\r\n    }, [tncDocument, navigate]);\r\n\r\n    const onDocumentLoad = (event: any) => {\r\n        setPdfDocument(event.target);\r\n    };\r\n\r\n    const handleAgree = async () => {\r\n        let messageText;\r\n        try {\r\n            const response = await acceptTnc({ termsAndConditionsId: tncDocument.document!.termsAndConditionsId }).unwrap();\r\n            messageText = response.message || 'Terms & Conditions accepted successfully.';\r\n            successNotification([''], messageText);\r\n            navigate(redirectPath);\r\n        } catch (err: any) {\r\n            messageText = 'Terms & Conditions acceptance failed';\r\n            if (err?.status >= 400 && err?.status < 500 && err?.data?.message) {\r\n               messageText = err?.data?.message;\r\n            } \r\n            errorNotification([''], messageText);\r\n        }\r\n    };\r\n\r\n    const clickToolbarButtonByTitle = (title: string) => {\r\n        // const button = document.querySelector(`button[title=\"${title}\"]`) as HTMLElement;\r\n        // button?.click();\r\n    };\r\n\r\n\r\n    return {\r\n        tncDocument,\r\n        pdfViewerRef,\r\n        pdfDocument,\r\n        setPdfDocument,\r\n        tncRefetch,\r\n        tncLoading,\r\n        isAccepting,\r\n        onDocumentLoad,\r\n        handleAgree,\r\n        clickToolbarButtonByTitle\r\n    };\r\n};\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAE3D,SAASC,oBAAoB,EAAEC,mBAAmB,QAAQ,sBAAsB;AAChF,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,iBAAiB,EAAEC,mBAAmB,QAAQ,6BAA6B;AAEpF,OAAO,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA;EACvC,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAMU,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,YAAY,GAAGd,MAAM,CAAM,IAAI,CAAC;EACtC,MAAM,CAACe,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAM,IAAI,CAAC;EACzD,MAAMgB,WAAW,GAAGX,cAAc,CAAEY,KAAK,IAAKA,KAAK,CAACC,GAAG,CAACC,GAAG,CAAC;EAE5D,MAAMC,YAAY,GAAG,EAAAV,eAAA,GAAAE,QAAQ,CAACK,KAAK,cAAAP,eAAA,uBAAdA,eAAA,CAAgBW,IAAI,KAAI,GAAG;EAEhD,MAAM;IAAEC,OAAO,EAAEC,UAAU;IAAEC,UAAU,EAAEC;EAAW,CAAC,GAAGrB,mBAAmB,CAAC,CAAC;EAC7E,MAAM,CAACsB,SAAS,EAAE;IAAEC,SAAS,EAAEC;EAAY,CAAC,CAAC,GAAGzB,oBAAoB,CAAC,CAAC;EAEtEL,SAAS,CAAC,MAAM;IACZ,IAAIkB,WAAW,CAACa,WAAW,IAAI,CAACb,WAAW,CAACc,QAAQ,EAAE;MAClDnB,QAAQ,CAACS,YAAY,CAAC;IAC1B;EACJ,CAAC,EAAE,CAACJ,WAAW,EAAEL,QAAQ,CAAC,CAAC;EAE3B,MAAMoB,cAAc,GAAIC,KAAU,IAAK;IACnCjB,cAAc,CAACiB,KAAK,CAACC,MAAM,CAAC;EAChC,CAAC;EAED,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAIC,WAAW;IACf,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMV,SAAS,CAAC;QAAEW,oBAAoB,EAAErB,WAAW,CAACc,QAAQ,CAAEO;MAAqB,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;MAC/GH,WAAW,GAAGC,QAAQ,CAACG,OAAO,IAAI,2CAA2C;MAC7EhC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE4B,WAAW,CAAC;MACtCxB,QAAQ,CAACS,YAAY,CAAC;IAC1B,CAAC,CAAC,OAAOoB,GAAQ,EAAE;MAAA,IAAAC,SAAA;MACfN,WAAW,GAAG,sCAAsC;MACpD,IAAI,CAAAK,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEE,MAAM,KAAI,GAAG,IAAI,CAAAF,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEE,MAAM,IAAG,GAAG,IAAIF,GAAG,aAAHA,GAAG,gBAAAC,SAAA,GAAHD,GAAG,CAAEG,IAAI,cAAAF,SAAA,eAATA,SAAA,CAAWF,OAAO,EAAE;QAAA,IAAAK,UAAA;QAChET,WAAW,GAAGK,GAAG,aAAHA,GAAG,wBAAAI,UAAA,GAAHJ,GAAG,CAAEG,IAAI,cAAAC,UAAA,uBAATA,UAAA,CAAWL,OAAO;MACnC;MACAjC,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE6B,WAAW,CAAC;IACxC;EACJ,CAAC;EAED,MAAMU,yBAAyB,GAAIC,KAAa,IAAK;IACjD;IACA;EAAA,CACH;EAGD,OAAO;IACH9B,WAAW;IACXH,YAAY;IACZC,WAAW;IACXC,cAAc;IACdQ,UAAU;IACVE,UAAU;IACVG,WAAW;IACXG,cAAc;IACdG,WAAW;IACXW;EACJ,CAAC;AACL,CAAC;AAACpC,EAAA,CAxDWD,qBAAqB;EAAA,QACbN,WAAW,EACXD,WAAW,EAGRI,cAAc,EAIsBD,mBAAmB,EAC3BD,oBAAoB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}