{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as n from \"react\";\nimport e from \"prop-types\";\nimport { Popup as X } from \"@progress/kendo-react-popup\";\nimport { cloneDate as y } from \"@progress/kendo-date-math\";\nimport { classNames as c, uTimePicker as g, Keys as u, validatePackage as Z, canUseDOM as C, AsyncFocusBlur as J, WatermarkOverlay as Q, createPropsContext as Y, withIdHOC as $, withPropsContext as ee, withUnstyledHOC as te, withAdaptiveModeContext as ie } from \"@progress/kendo-react-common\";\nimport { clockIcon as se } from \"@progress/kendo-svg-icons\";\nimport { provideLocalizationService as oe, registerForLocalization as ne } from \"@progress/kendo-react-intl\";\nimport { packageMetadata as ae } from \"../package-metadata.mjs\";\nimport { toggleClock as f, messages as d, timePickerCancel as re, timePickerSet as I, toggleTimeSelector as T } from \"../messages/index.mjs\";\nimport { DateInput as le } from \"../dateinput/DateInput.mjs\";\nimport { TimeSelector as he } from \"./TimeSelector.mjs\";\nimport { MAX_TIME as ue, MIN_TIME as de, setTime as M, MIDNIGHT_DATE as pe } from \"../utils.mjs\";\nimport { isInRange as me, isBiggerThanMax as ce, isSmallerThanMin as ge } from \"./utils.mjs\";\nimport { PickerFloatingLabel as fe } from \"../hooks/usePickerFloatingLabel.mjs\";\nimport { Button as ve } from \"@progress/kendo-react-buttons\";\nimport { AdaptiveMode as we } from \"../common/AdaptiveMode.mjs\";\nimport { ActionSheetContent as be } from \"@progress/kendo-react-layout\";\nconst a = class a extends n.Component {\n  constructor(i) {\n    super(i), this._element = null, this._dateInput = n.createRef(), this._timeSelector = null, this.shouldFocusDateInput = !1, this.prevShow = !1, this.showLicenseWatermark = !1, this.focus = () => {\n      this.dateInput && this.dateInput.focus();\n    }, this.renderTimeSelector = () => {\n      const {\n        smoothScroll: t,\n        cancelButton: s,\n        nowButton: o,\n        disabled: l,\n        format: h,\n        steps: r,\n        unstyled: m\n      } = this.props;\n      return /* @__PURE__ */n.createElement(he, {\n        ref: this.setTimeSelectorRef,\n        mobileMode: this.mobileMode,\n        show: this.show,\n        cancelButton: s,\n        disabled: l,\n        nowButton: o,\n        format: h,\n        min: this.min,\n        max: this.max,\n        steps: r,\n        smoothScroll: t,\n        value: this.value,\n        footer: !this.mobileMode,\n        handleTimeChange: this.mobileMode && this.handleTimeChange,\n        onChange: this.handleValueChange,\n        onReject: this.handleValueReject,\n        unstyled: m\n      });\n    }, this.renderPopup = () => {\n      const {\n          popupClass: t,\n          ...s\n        } = this.popupSettings,\n        {\n          unstyled: o\n        } = this.props,\n        l = o && o.uTimePicker,\n        h = c(t),\n        r = {\n          popupClass: g.popup({\n            c: l\n          }),\n          show: this.show,\n          animate: this.element !== null,\n          anchor: this.element,\n          className: h,\n          id: this._popupId,\n          anchorAlign: {\n            horizontal: \"left\",\n            vertical: \"bottom\"\n          },\n          popupAlign: {\n            horizontal: \"left\",\n            vertical: \"top\"\n          },\n          ...s\n        };\n      return this.props.popup ? /* @__PURE__ */n.createElement(this.props.popup, {\n        ...r\n      }, this.renderTimeSelector()) : /* @__PURE__ */n.createElement(X, {\n        ...r\n      }, this.renderTimeSelector());\n    }, this.renderAdaptivePopup = () => {\n      const {\n          windowWidth: t = 0\n        } = this.state,\n        s = {\n          expand: this.show,\n          onClose: this.handleBlur,\n          title: this.props.adaptiveTitle || this.props.label,\n          subTitle: this.props.adaptiveSubtitle,\n          windowWidth: t,\n          footer: {\n            cancelText: this.localizationService.toLanguageString(f, d[re]),\n            onCancel: this.handleValueReject,\n            applyText: this.localizationService.toLanguageString(I, d[I]),\n            onApply: o => this.handleValueChange(o)\n          }\n        };\n      return /* @__PURE__ */n.createElement(we, {\n        ...s\n      }, /* @__PURE__ */n.createElement(be, null, this.renderTimeSelector()));\n    }, this.setTimeSelectorRef = t => {\n      this._timeSelector = t;\n    }, this.nextValue = (t, s) => t.value !== void 0 ? t.value : s.value, this.nextShow = (t, s) => t.show !== void 0 ? t.show : s.show, this.handleInputValueChange = t => {\n      const s = this.mergeTime(t.value);\n      this.handleValueChange({\n        ...t,\n        value: s\n      });\n    }, this.handleTimeChange = t => {\n      this.setState({\n        candidate: t.time\n      });\n    }, this.handleValueChange = t => {\n      this.setState({\n        value: y(t.value || this.state.candidate)\n      }), this.valueDuringOnChange = t.value, this.showDuringOnChange = !1, this.shouldFocusDateInput = !0;\n      const {\n          onChange: s\n        } = this.props,\n        o = this.state.candidate || this.value;\n      s && s.call(void 0, {\n        syntheticEvent: t.syntheticEvent,\n        nativeEvent: t.nativeEvent,\n        value: o,\n        show: this.show,\n        target: this\n      }), this.valueDuringOnChange = void 0, this.showDuringOnChange = void 0, this.setShow(!1);\n    }, this.handleFocus = () => {\n      this.setState({\n        focused: !0\n      });\n    }, this.handleBlur = () => {\n      this.setState({\n        focused: !1\n      }), this.setShow(!1);\n    }, this.handleValueReject = t => {\n      this.setShow(!1);\n    }, this.handleClick = t => {\n      this.props.disabled || (this.shouldFocusDateInput = !0, this.setShow(!this.show));\n    }, this.handleIconMouseDown = t => {\n      t.preventDefault();\n    }, this.handleKeyDown = t => {\n      const {\n        altKey: s,\n        keyCode: o\n      } = t;\n      if (o === u.esc) {\n        this.shouldFocusDateInput = !0, this.setShow(!1);\n        return;\n      }\n      s && (o === u.up || o === u.down) && (t.preventDefault(), t.stopPropagation(), this.shouldFocusDateInput = o === u.up, this.setShow(o === u.down));\n    }, this.showLicenseWatermark = !Z(ae, {\n      component: \"TimePicker\"\n    }), this.state = {\n      value: this.props.defaultValue || a.defaultProps.defaultValue,\n      show: this.props.defaultShow || a.defaultProps.defaultShow,\n      focused: !1,\n      candidate: null\n    }, this.normalizeTime = this.normalizeTime.bind(this), this.setShow = this.setShow.bind(this), this.mergeTime = this.mergeTime.bind(this);\n  }\n  get _popupId() {\n    return this.props.id + \"-popup-id\";\n  }\n  get document() {\n    if (C) return this.element && this.element.ownerDocument || document;\n  }\n  /**\n   * Gets the wrapping element of the TimePicker.\n   */\n  get element() {\n    return this._element;\n  }\n  /**\n   * Gets the DateInput component inside the TimePicker component.\n   */\n  get dateInput() {\n    return this._dateInput.current;\n  }\n  /**\n   * Gets the TimeSelector component inside the TimePicker component.\n   */\n  get timeSelector() {\n    return this._timeSelector;\n  }\n  /**\n   * Gets the value of the TimePicker.\n   */\n  get value() {\n    const i = this.valueDuringOnChange !== void 0 ? this.valueDuringOnChange : this.props.value !== void 0 ? this.props.value : this.state.value;\n    return i !== null ? y(i) : null;\n  }\n  /**\n   * Gets the popup state of the TimeSelector.\n   */\n  get show() {\n    return this.showDuringOnChange !== void 0 ? this.showDuringOnChange : this.props.show !== void 0 ? this.props.show : this.state.show;\n  }\n  /**\n   * Gets the `name` property of the TimePicker.\n   */\n  get name() {\n    return this.props.name;\n  }\n  /**\n   * Represents the validity state into which the TimePicker is set.\n   */\n  get validity() {\n    const i = this.value && this.normalizeTime(this.value),\n      t = this.normalizeTime(this.min),\n      s = this.normalizeTime(this.max),\n      o = me(i, t, s),\n      l = this.props.validationMessage !== void 0,\n      h = (!this.required || this.value !== null) && o,\n      r = this.props.valid !== void 0 ? this.props.valid : h;\n    return {\n      customError: l,\n      rangeOverflow: ce(i, s),\n      rangeUnderflow: ge(i, t),\n      valid: r,\n      valueMissing: this.value === null\n    };\n  }\n  /**\n   * The mobile mode of the ComboBox.\n   */\n  get mobileMode() {\n    var t;\n    return !!(this.state.windowWidth && this.props._adaptiveMode && this.state.windowWidth <= ((t = this.props._adaptiveMode) == null ? void 0 : t.medium) && this.props.adaptive);\n  }\n  /**\n   * @hidden\n   */\n  get validityStyles() {\n    return this.props.validityStyles !== void 0 ? this.props.validityStyles : a.defaultProps.validityStyles;\n  }\n  /**\n   * @hidden\n   */\n  get required() {\n    return this.props.required !== void 0 ? this.props.required : !1;\n  }\n  get popupSettings() {\n    return this.props.popupSettings || a.defaultProps.popupSettings;\n  }\n  get min() {\n    return this.props.min !== void 0 ? this.props.min : a.defaultProps.min;\n  }\n  get max() {\n    return this.props.max !== void 0 ? this.props.max : a.defaultProps.max;\n  }\n  get dateInputComp() {\n    return this.props.dateInput || a.defaultProps.dateInput;\n  }\n  get localizationService() {\n    return oe(this);\n  }\n  /**\n   * @hidden\n   */\n  componentDidMount() {\n    var i;\n    this.observerResize = C && window.ResizeObserver && new window.ResizeObserver(this.calculateMedia.bind(this)), this.show && this.forceUpdate(), (i = this.document) != null && i.body && this.observerResize && this.observerResize.observe(this.document.body);\n  }\n  /**\n   * @hidden\n   */\n  componentDidUpdate() {\n    this._timeSelector && this.show && !this.prevShow && this._timeSelector.focusActiveList(), this.mobileMode && this.show && !this.prevShow && setTimeout(() => {\n      this._timeSelector && this._timeSelector.focusActiveList();\n    }, 300), this.dateInput && this.dateInput.element && !this.show && this.shouldFocusDateInput && this.dateInput.element.focus({\n      preventScroll: !0\n    }), this.prevShow = this.show, this.shouldFocusDateInput = !1;\n  }\n  /**\n   * @hidden\n   */\n  componentWillUnmount() {\n    var i;\n    (i = this.document) != null && i.body && this.observerResize && this.observerResize.disconnect();\n  }\n  /**\n   * @hidden\n   */\n  render() {\n    const {\n        size: i = a.defaultProps.size,\n        rounded: t = a.defaultProps.rounded,\n        fillMode: s = a.defaultProps.fillMode,\n        disabled: o,\n        tabIndex: l,\n        title: h,\n        id: r,\n        className: m,\n        format: O,\n        formatPlaceholder: D,\n        width: P,\n        name: z,\n        steps: k,\n        validationMessage: x,\n        required: E,\n        validityStyles: R,\n        ariaLabelledBy: B,\n        ariaDescribedBy: A,\n        unstyled: v,\n        enableMouseWheel: F,\n        autoCorrectParts: V,\n        autoSwitchParts: L,\n        autoSwitchKeys: N,\n        allowCaretMode: _,\n        inputAttributes: q\n      } = this.props,\n      w = v && v.uTimePicker,\n      b = !this.validityStyles || this.validity.valid,\n      K = {\n        disabled: o,\n        format: O,\n        formatPlaceholder: D,\n        id: r,\n        ariaLabelledBy: B,\n        ariaDescribedBy: A,\n        max: this.normalizeTime(this.max),\n        min: this.normalizeTime(this.min),\n        name: z,\n        onChange: this.handleInputValueChange,\n        required: E,\n        steps: k,\n        tabIndex: this.show ? -1 : l,\n        title: h,\n        valid: this.validity.valid,\n        validationMessage: x,\n        validityStyles: R,\n        value: this.value && this.normalizeTime(this.value),\n        label: void 0,\n        placeholder: this.state.focused ? null : this.props.placeholder,\n        ariaHasPopup: \"dialog\",\n        ariaExpanded: this.show,\n        size: null,\n        fillMode: null,\n        rounded: null,\n        enableMouseWheel: F,\n        autoCorrectParts: V,\n        autoSwitchParts: L,\n        autoSwitchKeys: N,\n        allowCaretMode: _,\n        inputAttributes: q\n      },\n      W = this.localizationService.toLanguageString(f, d[f]),\n      U = this.localizationService.toLanguageString(T, d[T]),\n      S = /* @__PURE__ */n.createElement(J, {\n        onFocus: this.handleFocus,\n        onBlur: this.mobileMode ? void 0 : this.handleBlur,\n        onSyncBlur: this.props.onBlur,\n        onSyncFocus: this.props.onFocus\n      }, ({\n        onFocus: j,\n        onBlur: H\n      }) => /* @__PURE__ */n.createElement(n.Fragment, null, /* @__PURE__ */n.createElement(\"span\", {\n        ref: G => {\n          this._element = G;\n        },\n        className: c(g.wrapper({\n          c: w,\n          size: i,\n          rounded: t,\n          fillMode: s,\n          invalid: !b,\n          required: this.required,\n          disabled: o\n        }), m),\n        onKeyDown: this.handleKeyDown,\n        style: {\n          width: P\n        },\n        onFocus: this.mobileMode ? this.handleClick : j,\n        onBlur: H,\n        onClick: this.mobileMode ? this.handleClick : void 0\n      }, /* @__PURE__ */n.createElement(this.dateInputComp, {\n        _ref: this._dateInput,\n        ariaRole: \"combobox\",\n        ariaControls: this._popupId,\n        ...K\n      }), /* @__PURE__ */n.createElement(ve, {\n        tabIndex: -1,\n        type: \"button\",\n        icon: \"clock\",\n        svgIcon: se,\n        onMouseDown: this.handleIconMouseDown,\n        onClick: this.mobileMode ? void 0 : this.handleClick,\n        title: U,\n        className: c(g.inputButton({\n          c: w\n        })),\n        rounded: null,\n        fillMode: s,\n        \"aria-label\": W\n      }), !this.mobileMode && this.renderPopup()), this.mobileMode && this.renderAdaptivePopup(), this.showLicenseWatermark && /* @__PURE__ */n.createElement(Q, null)));\n    return this.props.label ? /* @__PURE__ */n.createElement(fe, {\n      dateInput: this._dateInput,\n      label: this.props.label,\n      editorId: r,\n      editorValid: b,\n      editorDisabled: this.props.disabled,\n      children: S,\n      style: {\n        width: this.props.width\n      }\n    }) : S;\n  }\n  normalizeTime(i) {\n    return M(pe, i);\n  }\n  setShow(i) {\n    const {\n      onOpen: t,\n      onClose: s\n    } = this.props;\n    this.show !== i && (this.setState({\n      show: i\n    }), i && t && t.call(void 0, {\n      target: this\n    }), !i && s && s.call(void 0, {\n      target: this\n    }));\n  }\n  mergeTime(i) {\n    return this.value && i ? M(this.value, i) : i;\n  }\n  calculateMedia(i) {\n    for (const t of i) this.setState({\n      windowWidth: t.target.clientWidth\n    });\n  }\n};\na.displayName = \"TimePicker\", a.propTypes = {\n  className: e.string,\n  cancelButton: e.bool,\n  nowButton: e.bool,\n  defaultShow: e.bool,\n  defaultValue: e.instanceOf(Date),\n  disabled: e.bool,\n  format: e.oneOfType([e.string, e.shape({\n    skeleton: e.string,\n    pattern: e.string,\n    date: e.oneOf([\"short\", \"medium\", \"long\", \"full\"]),\n    time: e.oneOf([\"short\", \"medium\", \"long\", \"full\"]),\n    datetime: e.oneOf([\"short\", \"medium\", \"long\", \"full\"]),\n    era: e.oneOf([\"narrow\", \"short\", \"long\"]),\n    year: e.oneOf([\"numeric\", \"2-digit\"]),\n    month: e.oneOf([\"numeric\", \"2-digit\", \"narrow\", \"short\", \"long\"]),\n    day: e.oneOf([\"numeric\", \"2-digit\"]),\n    weekday: e.oneOf([\"narrow\", \"short\", \"long\"]),\n    hour: e.oneOf([\"numeric\", \"2-digit\"]),\n    hour12: e.bool,\n    minute: e.oneOf([\"numeric\", \"2-digit\"]),\n    second: e.oneOf([\"numeric\", \"2-digit\"]),\n    timeZoneName: e.oneOf([\"short\", \"long\"])\n  })]),\n  formatPlaceholder: e.oneOfType([e.oneOf([\"wide\", \"narrow\", \"short\", \"formatPattern\"]), e.shape({\n    year: e.string,\n    month: e.string,\n    day: e.string,\n    hour: e.string,\n    minute: e.string,\n    second: e.string\n  })]),\n  id: e.string,\n  ariaLabelledBy: e.string,\n  ariaDescribedBy: e.string,\n  min: e.instanceOf(Date),\n  max: e.instanceOf(Date),\n  name: e.string,\n  popupSettings: e.shape({\n    animate: e.bool,\n    appendTo: e.any,\n    popupClass: e.string\n  }),\n  show: e.bool,\n  steps: e.shape({\n    hour: e.number,\n    minute: e.number,\n    second: e.number\n  }),\n  smoothScroll: e.bool,\n  tabIndex: e.number,\n  title: e.string,\n  value: e.instanceOf(Date),\n  width: e.oneOfType([e.number, e.string]),\n  validationMessage: e.string,\n  required: e.bool,\n  validate: e.bool,\n  valid: e.bool,\n  size: e.oneOf([null, \"small\", \"medium\", \"large\"]),\n  rounded: e.oneOf([null, \"small\", \"medium\", \"large\", \"full\"]),\n  fillMode: e.oneOf([null, \"solid\", \"flat\", \"outline\"]),\n  inputAttributes: e.object\n}, a.defaultProps = {\n  defaultShow: !1,\n  defaultValue: null,\n  disabled: !1,\n  format: \"t\",\n  max: ue,\n  min: de,\n  popupSettings: {},\n  tabIndex: 0,\n  steps: {},\n  validityStyles: !0,\n  dateInput: le,\n  size: \"medium\",\n  rounded: \"medium\",\n  fillMode: \"solid\"\n};\nlet p = a;\nconst Se = Y(),\n  ye = $(ee(Se, te(ie(p))));\nye.displayName = \"KendoReactTimePicker\";\nne(p);\nexport { ye as TimePicker, Se as TimePickerPropsContext, p as TimePickerWithoutContext };", "map": {"version": 3, "names": ["n", "e", "Popup", "X", "cloneDate", "y", "classNames", "c", "uTimePicker", "g", "Keys", "u", "validatePackage", "Z", "canUseDOM", "C", "AsyncFocusBlur", "J", "WatermarkOverlay", "Q", "createPropsContext", "Y", "withIdHOC", "$", "withPropsContext", "ee", "withUnstyledHOC", "te", "withAdaptiveModeContext", "ie", "clockIcon", "se", "provideLocalizationService", "oe", "registerForLocalization", "ne", "packageMetadata", "ae", "toggleClock", "f", "messages", "d", "timePickerCancel", "re", "timePickerSet", "I", "toggleTimeSelector", "T", "DateInput", "le", "TimeSelector", "he", "MAX_TIME", "ue", "MIN_TIME", "de", "setTime", "M", "MIDNIGHT_DATE", "pe", "isInRange", "me", "isBiggerThanMax", "ce", "isSmallerThanMin", "ge", "PickerFloatingLabel", "fe", "<PERSON><PERSON>", "ve", "AdaptiveMode", "we", "ActionSheetContent", "be", "a", "Component", "constructor", "i", "_element", "_dateInput", "createRef", "_timeSelector", "shouldFocusDateInput", "prevShow", "showLicenseWatermark", "focus", "dateInput", "renderTimeSelector", "smoothScroll", "t", "cancelButton", "s", "nowButton", "o", "disabled", "l", "format", "h", "steps", "r", "unstyled", "m", "props", "createElement", "ref", "setTimeSelectorRef", "mobileMode", "show", "min", "max", "value", "footer", "handleTimeChange", "onChange", "handleValueChange", "onReject", "handleValueReject", "renderPopup", "popupClass", "popupSettings", "popup", "animate", "element", "anchor", "className", "id", "_popupId", "anchorAlign", "horizontal", "vertical", "popupAlign", "renderAdaptivePopup", "windowWidth", "state", "expand", "onClose", "handleBlur", "title", "adaptiveTitle", "label", "subTitle", "adaptiveSubtitle", "cancelText", "localizationService", "toLanguageString", "onCancel", "applyText", "onApply", "nextValue", "nextShow", "handleInputValueChange", "mergeTime", "setState", "candidate", "time", "valueDuringOnChange", "showDuringOnChange", "call", "syntheticEvent", "nativeEvent", "target", "setShow", "handleFocus", "focused", "handleClick", "handleIconMouseDown", "preventDefault", "handleKeyDown", "altKey", "keyCode", "esc", "up", "down", "stopPropagation", "component", "defaultValue", "defaultProps", "defaultShow", "normalizeTime", "bind", "document", "ownerDocument", "current", "timeSelector", "name", "validity", "validationMessage", "required", "valid", "customError", "rangeOverflow", "rangeUnderflow", "valueMissing", "_adaptiveMode", "medium", "adaptive", "validityStyles", "dateInputComp", "componentDidMount", "observerResize", "window", "ResizeObserver", "calculateMedia", "forceUpdate", "body", "observe", "componentDidUpdate", "focusActiveList", "setTimeout", "preventScroll", "componentWillUnmount", "disconnect", "render", "size", "rounded", "fillMode", "tabIndex", "O", "formatPlaceholder", "D", "width", "P", "z", "k", "x", "E", "R", "ariaLabelledBy", "B", "ariaDescribedBy", "A", "v", "enableMouseWheel", "F", "autoCorrectParts", "V", "autoSwitchParts", "L", "autoSwitchKeys", "N", "allowCaretMode", "_", "inputAttributes", "q", "w", "b", "K", "placeholder", "aria<PERSON>as<PERSON><PERSON><PERSON>", "ariaExpanded", "W", "U", "S", "onFocus", "onBlur", "onSyncBlur", "onSyncFocus", "j", "H", "Fragment", "G", "wrapper", "invalid", "onKeyDown", "style", "onClick", "_ref", "ariaRole", "ariaControls", "type", "icon", "svgIcon", "onMouseDown", "inputButton", "editorId", "<PERSON><PERSON><PERSON><PERSON>", "editorDisabled", "children", "onOpen", "clientWidth", "displayName", "propTypes", "string", "bool", "instanceOf", "Date", "oneOfType", "shape", "skeleton", "pattern", "date", "oneOf", "datetime", "era", "year", "month", "day", "weekday", "hour", "hour12", "minute", "second", "timeZoneName", "appendTo", "any", "number", "validate", "object", "p", "Se", "ye", "TimePicker", "TimePickerPropsContext", "TimePickerWithoutContext"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dateinputs/timepicker/TimePicker.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as n from \"react\";\nimport e from \"prop-types\";\nimport { Popup as X } from \"@progress/kendo-react-popup\";\nimport { cloneDate as y } from \"@progress/kendo-date-math\";\nimport { classNames as c, uTimePicker as g, Keys as u, validatePackage as Z, canUseDOM as C, AsyncFocusBlur as J, WatermarkOverlay as Q, createPropsContext as Y, withIdHOC as $, withPropsContext as ee, withUnstyledHOC as te, withAdaptiveModeContext as ie } from \"@progress/kendo-react-common\";\nimport { clockIcon as se } from \"@progress/kendo-svg-icons\";\nimport { provideLocalizationService as oe, registerForLocalization as ne } from \"@progress/kendo-react-intl\";\nimport { packageMetadata as ae } from \"../package-metadata.mjs\";\nimport { toggleClock as f, messages as d, timePickerCancel as re, timePickerSet as I, toggleTimeSelector as T } from \"../messages/index.mjs\";\nimport { DateInput as le } from \"../dateinput/DateInput.mjs\";\nimport { TimeSelector as he } from \"./TimeSelector.mjs\";\nimport { MAX_TIME as ue, MIN_TIME as de, setTime as M, MIDNIGHT_DATE as pe } from \"../utils.mjs\";\nimport { isInRange as me, isBiggerThanMax as ce, isSmallerThanMin as ge } from \"./utils.mjs\";\nimport { PickerFloatingLabel as fe } from \"../hooks/usePickerFloatingLabel.mjs\";\nimport { Button as ve } from \"@progress/kendo-react-buttons\";\nimport { AdaptiveMode as we } from \"../common/AdaptiveMode.mjs\";\nimport { ActionSheetContent as be } from \"@progress/kendo-react-layout\";\nconst a = class a extends n.Component {\n  constructor(i) {\n    super(i), this._element = null, this._dateInput = n.createRef(), this._timeSelector = null, this.shouldFocusDateInput = !1, this.prevShow = !1, this.showLicenseWatermark = !1, this.focus = () => {\n      this.dateInput && this.dateInput.focus();\n    }, this.renderTimeSelector = () => {\n      const { smoothScroll: t, cancelButton: s, nowButton: o, disabled: l, format: h, steps: r, unstyled: m } = this.props;\n      return /* @__PURE__ */ n.createElement(\n        he,\n        {\n          ref: this.setTimeSelectorRef,\n          mobileMode: this.mobileMode,\n          show: this.show,\n          cancelButton: s,\n          disabled: l,\n          nowButton: o,\n          format: h,\n          min: this.min,\n          max: this.max,\n          steps: r,\n          smoothScroll: t,\n          value: this.value,\n          footer: !this.mobileMode,\n          handleTimeChange: this.mobileMode && this.handleTimeChange,\n          onChange: this.handleValueChange,\n          onReject: this.handleValueReject,\n          unstyled: m\n        }\n      );\n    }, this.renderPopup = () => {\n      const { popupClass: t, ...s } = this.popupSettings, { unstyled: o } = this.props, l = o && o.uTimePicker, h = c(t), r = {\n        popupClass: g.popup({ c: l }),\n        show: this.show,\n        animate: this.element !== null,\n        anchor: this.element,\n        className: h,\n        id: this._popupId,\n        anchorAlign: {\n          horizontal: \"left\",\n          vertical: \"bottom\"\n        },\n        popupAlign: {\n          horizontal: \"left\",\n          vertical: \"top\"\n        },\n        ...s\n      };\n      return this.props.popup ? /* @__PURE__ */ n.createElement(this.props.popup, { ...r }, this.renderTimeSelector()) : /* @__PURE__ */ n.createElement(X, { ...r }, this.renderTimeSelector());\n    }, this.renderAdaptivePopup = () => {\n      const { windowWidth: t = 0 } = this.state, s = {\n        expand: this.show,\n        onClose: this.handleBlur,\n        title: this.props.adaptiveTitle || this.props.label,\n        subTitle: this.props.adaptiveSubtitle,\n        windowWidth: t,\n        footer: {\n          cancelText: this.localizationService.toLanguageString(f, d[re]),\n          onCancel: this.handleValueReject,\n          applyText: this.localizationService.toLanguageString(I, d[I]),\n          onApply: (o) => this.handleValueChange(o)\n        }\n      };\n      return /* @__PURE__ */ n.createElement(we, { ...s }, /* @__PURE__ */ n.createElement(be, null, this.renderTimeSelector()));\n    }, this.setTimeSelectorRef = (t) => {\n      this._timeSelector = t;\n    }, this.nextValue = (t, s) => t.value !== void 0 ? t.value : s.value, this.nextShow = (t, s) => t.show !== void 0 ? t.show : s.show, this.handleInputValueChange = (t) => {\n      const s = this.mergeTime(t.value);\n      this.handleValueChange({ ...t, value: s });\n    }, this.handleTimeChange = (t) => {\n      this.setState({ candidate: t.time });\n    }, this.handleValueChange = (t) => {\n      this.setState({\n        value: y(t.value || this.state.candidate)\n      }), this.valueDuringOnChange = t.value, this.showDuringOnChange = !1, this.shouldFocusDateInput = !0;\n      const { onChange: s } = this.props, o = this.state.candidate || this.value;\n      s && s.call(void 0, {\n        syntheticEvent: t.syntheticEvent,\n        nativeEvent: t.nativeEvent,\n        value: o,\n        show: this.show,\n        target: this\n      }), this.valueDuringOnChange = void 0, this.showDuringOnChange = void 0, this.setShow(!1);\n    }, this.handleFocus = () => {\n      this.setState({ focused: !0 });\n    }, this.handleBlur = () => {\n      this.setState({ focused: !1 }), this.setShow(!1);\n    }, this.handleValueReject = (t) => {\n      this.setShow(!1);\n    }, this.handleClick = (t) => {\n      this.props.disabled || (this.shouldFocusDateInput = !0, this.setShow(!this.show));\n    }, this.handleIconMouseDown = (t) => {\n      t.preventDefault();\n    }, this.handleKeyDown = (t) => {\n      const { altKey: s, keyCode: o } = t;\n      if (o === u.esc) {\n        this.shouldFocusDateInput = !0, this.setShow(!1);\n        return;\n      }\n      s && (o === u.up || o === u.down) && (t.preventDefault(), t.stopPropagation(), this.shouldFocusDateInput = o === u.up, this.setShow(o === u.down));\n    }, this.showLicenseWatermark = !Z(ae, { component: \"TimePicker\" }), this.state = {\n      value: this.props.defaultValue || a.defaultProps.defaultValue,\n      show: this.props.defaultShow || a.defaultProps.defaultShow,\n      focused: !1,\n      candidate: null\n    }, this.normalizeTime = this.normalizeTime.bind(this), this.setShow = this.setShow.bind(this), this.mergeTime = this.mergeTime.bind(this);\n  }\n  get _popupId() {\n    return this.props.id + \"-popup-id\";\n  }\n  get document() {\n    if (C)\n      return this.element && this.element.ownerDocument || document;\n  }\n  /**\n   * Gets the wrapping element of the TimePicker.\n   */\n  get element() {\n    return this._element;\n  }\n  /**\n   * Gets the DateInput component inside the TimePicker component.\n   */\n  get dateInput() {\n    return this._dateInput.current;\n  }\n  /**\n   * Gets the TimeSelector component inside the TimePicker component.\n   */\n  get timeSelector() {\n    return this._timeSelector;\n  }\n  /**\n   * Gets the value of the TimePicker.\n   */\n  get value() {\n    const i = this.valueDuringOnChange !== void 0 ? this.valueDuringOnChange : this.props.value !== void 0 ? this.props.value : this.state.value;\n    return i !== null ? y(i) : null;\n  }\n  /**\n   * Gets the popup state of the TimeSelector.\n   */\n  get show() {\n    return this.showDuringOnChange !== void 0 ? this.showDuringOnChange : this.props.show !== void 0 ? this.props.show : this.state.show;\n  }\n  /**\n   * Gets the `name` property of the TimePicker.\n   */\n  get name() {\n    return this.props.name;\n  }\n  /**\n   * Represents the validity state into which the TimePicker is set.\n   */\n  get validity() {\n    const i = this.value && this.normalizeTime(this.value), t = this.normalizeTime(this.min), s = this.normalizeTime(this.max), o = me(i, t, s), l = this.props.validationMessage !== void 0, h = (!this.required || this.value !== null) && o, r = this.props.valid !== void 0 ? this.props.valid : h;\n    return {\n      customError: l,\n      rangeOverflow: ce(i, s),\n      rangeUnderflow: ge(i, t),\n      valid: r,\n      valueMissing: this.value === null\n    };\n  }\n  /**\n   * The mobile mode of the ComboBox.\n   */\n  get mobileMode() {\n    var t;\n    return !!(this.state.windowWidth && this.props._adaptiveMode && this.state.windowWidth <= ((t = this.props._adaptiveMode) == null ? void 0 : t.medium) && this.props.adaptive);\n  }\n  /**\n   * @hidden\n   */\n  get validityStyles() {\n    return this.props.validityStyles !== void 0 ? this.props.validityStyles : a.defaultProps.validityStyles;\n  }\n  /**\n   * @hidden\n   */\n  get required() {\n    return this.props.required !== void 0 ? this.props.required : !1;\n  }\n  get popupSettings() {\n    return this.props.popupSettings || a.defaultProps.popupSettings;\n  }\n  get min() {\n    return this.props.min !== void 0 ? this.props.min : a.defaultProps.min;\n  }\n  get max() {\n    return this.props.max !== void 0 ? this.props.max : a.defaultProps.max;\n  }\n  get dateInputComp() {\n    return this.props.dateInput || a.defaultProps.dateInput;\n  }\n  get localizationService() {\n    return oe(this);\n  }\n  /**\n   * @hidden\n   */\n  componentDidMount() {\n    var i;\n    this.observerResize = C && window.ResizeObserver && new window.ResizeObserver(this.calculateMedia.bind(this)), this.show && this.forceUpdate(), (i = this.document) != null && i.body && this.observerResize && this.observerResize.observe(this.document.body);\n  }\n  /**\n   * @hidden\n   */\n  componentDidUpdate() {\n    this._timeSelector && this.show && !this.prevShow && this._timeSelector.focusActiveList(), this.mobileMode && this.show && !this.prevShow && setTimeout(() => {\n      this._timeSelector && this._timeSelector.focusActiveList();\n    }, 300), this.dateInput && this.dateInput.element && !this.show && this.shouldFocusDateInput && this.dateInput.element.focus({ preventScroll: !0 }), this.prevShow = this.show, this.shouldFocusDateInput = !1;\n  }\n  /**\n   * @hidden\n   */\n  componentWillUnmount() {\n    var i;\n    (i = this.document) != null && i.body && this.observerResize && this.observerResize.disconnect();\n  }\n  /**\n   * @hidden\n   */\n  render() {\n    const {\n      size: i = a.defaultProps.size,\n      rounded: t = a.defaultProps.rounded,\n      fillMode: s = a.defaultProps.fillMode,\n      disabled: o,\n      tabIndex: l,\n      title: h,\n      id: r,\n      className: m,\n      format: O,\n      formatPlaceholder: D,\n      width: P,\n      name: z,\n      steps: k,\n      validationMessage: x,\n      required: E,\n      validityStyles: R,\n      ariaLabelledBy: B,\n      ariaDescribedBy: A,\n      unstyled: v,\n      enableMouseWheel: F,\n      autoCorrectParts: V,\n      autoSwitchParts: L,\n      autoSwitchKeys: N,\n      allowCaretMode: _,\n      inputAttributes: q\n    } = this.props, w = v && v.uTimePicker, b = !this.validityStyles || this.validity.valid, K = {\n      disabled: o,\n      format: O,\n      formatPlaceholder: D,\n      id: r,\n      ariaLabelledBy: B,\n      ariaDescribedBy: A,\n      max: this.normalizeTime(this.max),\n      min: this.normalizeTime(this.min),\n      name: z,\n      onChange: this.handleInputValueChange,\n      required: E,\n      steps: k,\n      tabIndex: this.show ? -1 : l,\n      title: h,\n      valid: this.validity.valid,\n      validationMessage: x,\n      validityStyles: R,\n      value: this.value && this.normalizeTime(this.value),\n      label: void 0,\n      placeholder: this.state.focused ? null : this.props.placeholder,\n      ariaHasPopup: \"dialog\",\n      ariaExpanded: this.show,\n      size: null,\n      fillMode: null,\n      rounded: null,\n      enableMouseWheel: F,\n      autoCorrectParts: V,\n      autoSwitchParts: L,\n      autoSwitchKeys: N,\n      allowCaretMode: _,\n      inputAttributes: q\n    }, W = this.localizationService.toLanguageString(f, d[f]), U = this.localizationService.toLanguageString(\n      T,\n      d[T]\n    ), S = /* @__PURE__ */ n.createElement(\n      J,\n      {\n        onFocus: this.handleFocus,\n        onBlur: this.mobileMode ? void 0 : this.handleBlur,\n        onSyncBlur: this.props.onBlur,\n        onSyncFocus: this.props.onFocus\n      },\n      ({ onFocus: j, onBlur: H }) => /* @__PURE__ */ n.createElement(n.Fragment, null, /* @__PURE__ */ n.createElement(\n        \"span\",\n        {\n          ref: (G) => {\n            this._element = G;\n          },\n          className: c(\n            g.wrapper({\n              c: w,\n              size: i,\n              rounded: t,\n              fillMode: s,\n              invalid: !b,\n              required: this.required,\n              disabled: o\n            }),\n            m\n          ),\n          onKeyDown: this.handleKeyDown,\n          style: { width: P },\n          onFocus: this.mobileMode ? this.handleClick : j,\n          onBlur: H,\n          onClick: this.mobileMode ? this.handleClick : void 0\n        },\n        /* @__PURE__ */ n.createElement(\n          this.dateInputComp,\n          {\n            _ref: this._dateInput,\n            ariaRole: \"combobox\",\n            ariaControls: this._popupId,\n            ...K\n          }\n        ),\n        /* @__PURE__ */ n.createElement(\n          ve,\n          {\n            tabIndex: -1,\n            type: \"button\",\n            icon: \"clock\",\n            svgIcon: se,\n            onMouseDown: this.handleIconMouseDown,\n            onClick: this.mobileMode ? void 0 : this.handleClick,\n            title: U,\n            className: c(g.inputButton({ c: w })),\n            rounded: null,\n            fillMode: s,\n            \"aria-label\": W\n          }\n        ),\n        !this.mobileMode && this.renderPopup()\n      ), this.mobileMode && this.renderAdaptivePopup(), this.showLicenseWatermark && /* @__PURE__ */ n.createElement(Q, null))\n    );\n    return this.props.label ? /* @__PURE__ */ n.createElement(\n      fe,\n      {\n        dateInput: this._dateInput,\n        label: this.props.label,\n        editorId: r,\n        editorValid: b,\n        editorDisabled: this.props.disabled,\n        children: S,\n        style: { width: this.props.width }\n      }\n    ) : S;\n  }\n  normalizeTime(i) {\n    return M(pe, i);\n  }\n  setShow(i) {\n    const { onOpen: t, onClose: s } = this.props;\n    this.show !== i && (this.setState({ show: i }), i && t && t.call(void 0, {\n      target: this\n    }), !i && s && s.call(void 0, {\n      target: this\n    }));\n  }\n  mergeTime(i) {\n    return this.value && i ? M(this.value, i) : i;\n  }\n  calculateMedia(i) {\n    for (const t of i)\n      this.setState({ windowWidth: t.target.clientWidth });\n  }\n};\na.displayName = \"TimePicker\", a.propTypes = {\n  className: e.string,\n  cancelButton: e.bool,\n  nowButton: e.bool,\n  defaultShow: e.bool,\n  defaultValue: e.instanceOf(Date),\n  disabled: e.bool,\n  format: e.oneOfType([\n    e.string,\n    e.shape({\n      skeleton: e.string,\n      pattern: e.string,\n      date: e.oneOf([\"short\", \"medium\", \"long\", \"full\"]),\n      time: e.oneOf([\"short\", \"medium\", \"long\", \"full\"]),\n      datetime: e.oneOf([\"short\", \"medium\", \"long\", \"full\"]),\n      era: e.oneOf([\"narrow\", \"short\", \"long\"]),\n      year: e.oneOf([\"numeric\", \"2-digit\"]),\n      month: e.oneOf([\"numeric\", \"2-digit\", \"narrow\", \"short\", \"long\"]),\n      day: e.oneOf([\"numeric\", \"2-digit\"]),\n      weekday: e.oneOf([\"narrow\", \"short\", \"long\"]),\n      hour: e.oneOf([\"numeric\", \"2-digit\"]),\n      hour12: e.bool,\n      minute: e.oneOf([\"numeric\", \"2-digit\"]),\n      second: e.oneOf([\"numeric\", \"2-digit\"]),\n      timeZoneName: e.oneOf([\"short\", \"long\"])\n    })\n  ]),\n  formatPlaceholder: e.oneOfType([\n    e.oneOf([\n      \"wide\",\n      \"narrow\",\n      \"short\",\n      \"formatPattern\"\n    ]),\n    e.shape({\n      year: e.string,\n      month: e.string,\n      day: e.string,\n      hour: e.string,\n      minute: e.string,\n      second: e.string\n    })\n  ]),\n  id: e.string,\n  ariaLabelledBy: e.string,\n  ariaDescribedBy: e.string,\n  min: e.instanceOf(Date),\n  max: e.instanceOf(Date),\n  name: e.string,\n  popupSettings: e.shape({\n    animate: e.bool,\n    appendTo: e.any,\n    popupClass: e.string\n  }),\n  show: e.bool,\n  steps: e.shape({\n    hour: e.number,\n    minute: e.number,\n    second: e.number\n  }),\n  smoothScroll: e.bool,\n  tabIndex: e.number,\n  title: e.string,\n  value: e.instanceOf(Date),\n  width: e.oneOfType([e.number, e.string]),\n  validationMessage: e.string,\n  required: e.bool,\n  validate: e.bool,\n  valid: e.bool,\n  size: e.oneOf([null, \"small\", \"medium\", \"large\"]),\n  rounded: e.oneOf([null, \"small\", \"medium\", \"large\", \"full\"]),\n  fillMode: e.oneOf([null, \"solid\", \"flat\", \"outline\"]),\n  inputAttributes: e.object\n}, a.defaultProps = {\n  defaultShow: !1,\n  defaultValue: null,\n  disabled: !1,\n  format: \"t\",\n  max: ue,\n  min: de,\n  popupSettings: {},\n  tabIndex: 0,\n  steps: {},\n  validityStyles: !0,\n  dateInput: le,\n  size: \"medium\",\n  rounded: \"medium\",\n  fillMode: \"solid\"\n};\nlet p = a;\nconst Se = Y(), ye = $(\n  ee(\n    Se,\n    te(ie(p))\n  )\n);\nye.displayName = \"KendoReactTimePicker\";\nne(p);\nexport {\n  ye as TimePicker,\n  Se as TimePickerPropsContext,\n  p as TimePickerWithoutContext\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,KAAK,IAAIC,CAAC,QAAQ,6BAA6B;AACxD,SAASC,SAAS,IAAIC,CAAC,QAAQ,2BAA2B;AAC1D,SAASC,UAAU,IAAIC,CAAC,EAAEC,WAAW,IAAIC,CAAC,EAAEC,IAAI,IAAIC,CAAC,EAAEC,eAAe,IAAIC,CAAC,EAAEC,SAAS,IAAIC,CAAC,EAAEC,cAAc,IAAIC,CAAC,EAAEC,gBAAgB,IAAIC,CAAC,EAAEC,kBAAkB,IAAIC,CAAC,EAAEC,SAAS,IAAIC,CAAC,EAAEC,gBAAgB,IAAIC,EAAE,EAAEC,eAAe,IAAIC,EAAE,EAAEC,uBAAuB,IAAIC,EAAE,QAAQ,8BAA8B;AACpS,SAASC,SAAS,IAAIC,EAAE,QAAQ,2BAA2B;AAC3D,SAASC,0BAA0B,IAAIC,EAAE,EAAEC,uBAAuB,IAAIC,EAAE,QAAQ,4BAA4B;AAC5G,SAASC,eAAe,IAAIC,EAAE,QAAQ,yBAAyB;AAC/D,SAASC,WAAW,IAAIC,CAAC,EAAEC,QAAQ,IAAIC,CAAC,EAAEC,gBAAgB,IAAIC,EAAE,EAAEC,aAAa,IAAIC,CAAC,EAAEC,kBAAkB,IAAIC,CAAC,QAAQ,uBAAuB;AAC5I,SAASC,SAAS,IAAIC,EAAE,QAAQ,4BAA4B;AAC5D,SAASC,YAAY,IAAIC,EAAE,QAAQ,oBAAoB;AACvD,SAASC,QAAQ,IAAIC,EAAE,EAAEC,QAAQ,IAAIC,EAAE,EAAEC,OAAO,IAAIC,CAAC,EAAEC,aAAa,IAAIC,EAAE,QAAQ,cAAc;AAChG,SAASC,SAAS,IAAIC,EAAE,EAAEC,eAAe,IAAIC,EAAE,EAAEC,gBAAgB,IAAIC,EAAE,QAAQ,aAAa;AAC5F,SAASC,mBAAmB,IAAIC,EAAE,QAAQ,qCAAqC;AAC/E,SAASC,MAAM,IAAIC,EAAE,QAAQ,+BAA+B;AAC5D,SAASC,YAAY,IAAIC,EAAE,QAAQ,4BAA4B;AAC/D,SAASC,kBAAkB,IAAIC,EAAE,QAAQ,8BAA8B;AACvE,MAAMC,CAAC,GAAG,MAAMA,CAAC,SAAS1E,CAAC,CAAC2E,SAAS,CAAC;EACpCC,WAAWA,CAACC,CAAC,EAAE;IACb,KAAK,CAACA,CAAC,CAAC,EAAE,IAAI,CAACC,QAAQ,GAAG,IAAI,EAAE,IAAI,CAACC,UAAU,GAAG/E,CAAC,CAACgF,SAAS,CAAC,CAAC,EAAE,IAAI,CAACC,aAAa,GAAG,IAAI,EAAE,IAAI,CAACC,oBAAoB,GAAG,CAAC,CAAC,EAAE,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC,EAAE,IAAI,CAACC,oBAAoB,GAAG,CAAC,CAAC,EAAE,IAAI,CAACC,KAAK,GAAG,MAAM;MACjM,IAAI,CAACC,SAAS,IAAI,IAAI,CAACA,SAAS,CAACD,KAAK,CAAC,CAAC;IAC1C,CAAC,EAAE,IAAI,CAACE,kBAAkB,GAAG,MAAM;MACjC,MAAM;QAAEC,YAAY,EAAEC,CAAC;QAAEC,YAAY,EAAEC,CAAC;QAAEC,SAAS,EAAEC,CAAC;QAAEC,QAAQ,EAAEC,CAAC;QAAEC,MAAM,EAAEC,CAAC;QAAEC,KAAK,EAAEC,CAAC;QAAEC,QAAQ,EAAEC;MAAE,CAAC,GAAG,IAAI,CAACC,KAAK;MACpH,OAAO,eAAgBtG,CAAC,CAACuG,aAAa,CACpCpD,EAAE,EACF;QACEqD,GAAG,EAAE,IAAI,CAACC,kBAAkB;QAC5BC,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3BC,IAAI,EAAE,IAAI,CAACA,IAAI;QACfjB,YAAY,EAAEC,CAAC;QACfG,QAAQ,EAAEC,CAAC;QACXH,SAAS,EAAEC,CAAC;QACZG,MAAM,EAAEC,CAAC;QACTW,GAAG,EAAE,IAAI,CAACA,GAAG;QACbC,GAAG,EAAE,IAAI,CAACA,GAAG;QACbX,KAAK,EAAEC,CAAC;QACRX,YAAY,EAAEC,CAAC;QACfqB,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBC,MAAM,EAAE,CAAC,IAAI,CAACL,UAAU;QACxBM,gBAAgB,EAAE,IAAI,CAACN,UAAU,IAAI,IAAI,CAACM,gBAAgB;QAC1DC,QAAQ,EAAE,IAAI,CAACC,iBAAiB;QAChCC,QAAQ,EAAE,IAAI,CAACC,iBAAiB;QAChChB,QAAQ,EAAEC;MACZ,CACF,CAAC;IACH,CAAC,EAAE,IAAI,CAACgB,WAAW,GAAG,MAAM;MAC1B,MAAM;UAAEC,UAAU,EAAE7B,CAAC;UAAE,GAAGE;QAAE,CAAC,GAAG,IAAI,CAAC4B,aAAa;QAAE;UAAEnB,QAAQ,EAAEP;QAAE,CAAC,GAAG,IAAI,CAACS,KAAK;QAAEP,CAAC,GAAGF,CAAC,IAAIA,CAAC,CAACrF,WAAW;QAAEyF,CAAC,GAAG1F,CAAC,CAACkF,CAAC,CAAC;QAAEU,CAAC,GAAG;UACtHmB,UAAU,EAAE7G,CAAC,CAAC+G,KAAK,CAAC;YAAEjH,CAAC,EAAEwF;UAAE,CAAC,CAAC;UAC7BY,IAAI,EAAE,IAAI,CAACA,IAAI;UACfc,OAAO,EAAE,IAAI,CAACC,OAAO,KAAK,IAAI;UAC9BC,MAAM,EAAE,IAAI,CAACD,OAAO;UACpBE,SAAS,EAAE3B,CAAC;UACZ4B,EAAE,EAAE,IAAI,CAACC,QAAQ;UACjBC,WAAW,EAAE;YACXC,UAAU,EAAE,MAAM;YAClBC,QAAQ,EAAE;UACZ,CAAC;UACDC,UAAU,EAAE;YACVF,UAAU,EAAE,MAAM;YAClBC,QAAQ,EAAE;UACZ,CAAC;UACD,GAAGtC;QACL,CAAC;MACD,OAAO,IAAI,CAACW,KAAK,CAACkB,KAAK,GAAG,eAAgBxH,CAAC,CAACuG,aAAa,CAAC,IAAI,CAACD,KAAK,CAACkB,KAAK,EAAE;QAAE,GAAGrB;MAAE,CAAC,EAAE,IAAI,CAACZ,kBAAkB,CAAC,CAAC,CAAC,GAAG,eAAgBvF,CAAC,CAACuG,aAAa,CAACpG,CAAC,EAAE;QAAE,GAAGgG;MAAE,CAAC,EAAE,IAAI,CAACZ,kBAAkB,CAAC,CAAC,CAAC;IAC5L,CAAC,EAAE,IAAI,CAAC4C,mBAAmB,GAAG,MAAM;MAClC,MAAM;UAAEC,WAAW,EAAE3C,CAAC,GAAG;QAAE,CAAC,GAAG,IAAI,CAAC4C,KAAK;QAAE1C,CAAC,GAAG;UAC7C2C,MAAM,EAAE,IAAI,CAAC3B,IAAI;UACjB4B,OAAO,EAAE,IAAI,CAACC,UAAU;UACxBC,KAAK,EAAE,IAAI,CAACnC,KAAK,CAACoC,aAAa,IAAI,IAAI,CAACpC,KAAK,CAACqC,KAAK;UACnDC,QAAQ,EAAE,IAAI,CAACtC,KAAK,CAACuC,gBAAgB;UACrCT,WAAW,EAAE3C,CAAC;UACdsB,MAAM,EAAE;YACN+B,UAAU,EAAE,IAAI,CAACC,mBAAmB,CAACC,gBAAgB,CAACzG,CAAC,EAAEE,CAAC,CAACE,EAAE,CAAC,CAAC;YAC/DsG,QAAQ,EAAE,IAAI,CAAC7B,iBAAiB;YAChC8B,SAAS,EAAE,IAAI,CAACH,mBAAmB,CAACC,gBAAgB,CAACnG,CAAC,EAAEJ,CAAC,CAACI,CAAC,CAAC,CAAC;YAC7DsG,OAAO,EAAGtD,CAAC,IAAK,IAAI,CAACqB,iBAAiB,CAACrB,CAAC;UAC1C;QACF,CAAC;MACD,OAAO,eAAgB7F,CAAC,CAACuG,aAAa,CAAChC,EAAE,EAAE;QAAE,GAAGoB;MAAE,CAAC,EAAE,eAAgB3F,CAAC,CAACuG,aAAa,CAAC9B,EAAE,EAAE,IAAI,EAAE,IAAI,CAACc,kBAAkB,CAAC,CAAC,CAAC,CAAC;IAC5H,CAAC,EAAE,IAAI,CAACkB,kBAAkB,GAAIhB,CAAC,IAAK;MAClC,IAAI,CAACR,aAAa,GAAGQ,CAAC;IACxB,CAAC,EAAE,IAAI,CAAC2D,SAAS,GAAG,CAAC3D,CAAC,EAAEE,CAAC,KAAKF,CAAC,CAACqB,KAAK,KAAK,KAAK,CAAC,GAAGrB,CAAC,CAACqB,KAAK,GAAGnB,CAAC,CAACmB,KAAK,EAAE,IAAI,CAACuC,QAAQ,GAAG,CAAC5D,CAAC,EAAEE,CAAC,KAAKF,CAAC,CAACkB,IAAI,KAAK,KAAK,CAAC,GAAGlB,CAAC,CAACkB,IAAI,GAAGhB,CAAC,CAACgB,IAAI,EAAE,IAAI,CAAC2C,sBAAsB,GAAI7D,CAAC,IAAK;MACxK,MAAME,CAAC,GAAG,IAAI,CAAC4D,SAAS,CAAC9D,CAAC,CAACqB,KAAK,CAAC;MACjC,IAAI,CAACI,iBAAiB,CAAC;QAAE,GAAGzB,CAAC;QAAEqB,KAAK,EAAEnB;MAAE,CAAC,CAAC;IAC5C,CAAC,EAAE,IAAI,CAACqB,gBAAgB,GAAIvB,CAAC,IAAK;MAChC,IAAI,CAAC+D,QAAQ,CAAC;QAAEC,SAAS,EAAEhE,CAAC,CAACiE;MAAK,CAAC,CAAC;IACtC,CAAC,EAAE,IAAI,CAACxC,iBAAiB,GAAIzB,CAAC,IAAK;MACjC,IAAI,CAAC+D,QAAQ,CAAC;QACZ1C,KAAK,EAAEzG,CAAC,CAACoF,CAAC,CAACqB,KAAK,IAAI,IAAI,CAACuB,KAAK,CAACoB,SAAS;MAC1C,CAAC,CAAC,EAAE,IAAI,CAACE,mBAAmB,GAAGlE,CAAC,CAACqB,KAAK,EAAE,IAAI,CAAC8C,kBAAkB,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC1E,oBAAoB,GAAG,CAAC,CAAC;MACpG,MAAM;UAAE+B,QAAQ,EAAEtB;QAAE,CAAC,GAAG,IAAI,CAACW,KAAK;QAAET,CAAC,GAAG,IAAI,CAACwC,KAAK,CAACoB,SAAS,IAAI,IAAI,CAAC3C,KAAK;MAC1EnB,CAAC,IAAIA,CAAC,CAACkE,IAAI,CAAC,KAAK,CAAC,EAAE;QAClBC,cAAc,EAAErE,CAAC,CAACqE,cAAc;QAChCC,WAAW,EAAEtE,CAAC,CAACsE,WAAW;QAC1BjD,KAAK,EAAEjB,CAAC;QACRc,IAAI,EAAE,IAAI,CAACA,IAAI;QACfqD,MAAM,EAAE;MACV,CAAC,CAAC,EAAE,IAAI,CAACL,mBAAmB,GAAG,KAAK,CAAC,EAAE,IAAI,CAACC,kBAAkB,GAAG,KAAK,CAAC,EAAE,IAAI,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3F,CAAC,EAAE,IAAI,CAACC,WAAW,GAAG,MAAM;MAC1B,IAAI,CAACV,QAAQ,CAAC;QAAEW,OAAO,EAAE,CAAC;MAAE,CAAC,CAAC;IAChC,CAAC,EAAE,IAAI,CAAC3B,UAAU,GAAG,MAAM;MACzB,IAAI,CAACgB,QAAQ,CAAC;QAAEW,OAAO,EAAE,CAAC;MAAE,CAAC,CAAC,EAAE,IAAI,CAACF,OAAO,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC,EAAE,IAAI,CAAC7C,iBAAiB,GAAI3B,CAAC,IAAK;MACjC,IAAI,CAACwE,OAAO,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,EAAE,IAAI,CAACG,WAAW,GAAI3E,CAAC,IAAK;MAC3B,IAAI,CAACa,KAAK,CAACR,QAAQ,KAAK,IAAI,CAACZ,oBAAoB,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC+E,OAAO,CAAC,CAAC,IAAI,CAACtD,IAAI,CAAC,CAAC;IACnF,CAAC,EAAE,IAAI,CAAC0D,mBAAmB,GAAI5E,CAAC,IAAK;MACnCA,CAAC,CAAC6E,cAAc,CAAC,CAAC;IACpB,CAAC,EAAE,IAAI,CAACC,aAAa,GAAI9E,CAAC,IAAK;MAC7B,MAAM;QAAE+E,MAAM,EAAE7E,CAAC;QAAE8E,OAAO,EAAE5E;MAAE,CAAC,GAAGJ,CAAC;MACnC,IAAII,CAAC,KAAKlF,CAAC,CAAC+J,GAAG,EAAE;QACf,IAAI,CAACxF,oBAAoB,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC+E,OAAO,CAAC,CAAC,CAAC,CAAC;QAChD;MACF;MACAtE,CAAC,KAAKE,CAAC,KAAKlF,CAAC,CAACgK,EAAE,IAAI9E,CAAC,KAAKlF,CAAC,CAACiK,IAAI,CAAC,KAAKnF,CAAC,CAAC6E,cAAc,CAAC,CAAC,EAAE7E,CAAC,CAACoF,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC3F,oBAAoB,GAAGW,CAAC,KAAKlF,CAAC,CAACgK,EAAE,EAAE,IAAI,CAACV,OAAO,CAACpE,CAAC,KAAKlF,CAAC,CAACiK,IAAI,CAAC,CAAC;IACpJ,CAAC,EAAE,IAAI,CAACxF,oBAAoB,GAAG,CAACvE,CAAC,CAACwB,EAAE,EAAE;MAAEyI,SAAS,EAAE;IAAa,CAAC,CAAC,EAAE,IAAI,CAACzC,KAAK,GAAG;MAC/EvB,KAAK,EAAE,IAAI,CAACR,KAAK,CAACyE,YAAY,IAAIrG,CAAC,CAACsG,YAAY,CAACD,YAAY;MAC7DpE,IAAI,EAAE,IAAI,CAACL,KAAK,CAAC2E,WAAW,IAAIvG,CAAC,CAACsG,YAAY,CAACC,WAAW;MAC1Dd,OAAO,EAAE,CAAC,CAAC;MACXV,SAAS,EAAE;IACb,CAAC,EAAE,IAAI,CAACyB,aAAa,GAAG,IAAI,CAACA,aAAa,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAClB,OAAO,GAAG,IAAI,CAACA,OAAO,CAACkB,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC5B,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC4B,IAAI,CAAC,IAAI,CAAC;EAC3I;EACA,IAAIrD,QAAQA,CAAA,EAAG;IACb,OAAO,IAAI,CAACxB,KAAK,CAACuB,EAAE,GAAG,WAAW;EACpC;EACA,IAAIuD,QAAQA,CAAA,EAAG;IACb,IAAIrK,CAAC,EACH,OAAO,IAAI,CAAC2G,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC2D,aAAa,IAAID,QAAQ;EACjE;EACA;AACF;AACA;EACE,IAAI1D,OAAOA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC5C,QAAQ;EACtB;EACA;AACF;AACA;EACE,IAAIQ,SAASA,CAAA,EAAG;IACd,OAAO,IAAI,CAACP,UAAU,CAACuG,OAAO;EAChC;EACA;AACF;AACA;EACE,IAAIC,YAAYA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACtG,aAAa;EAC3B;EACA;AACF;AACA;EACE,IAAI6B,KAAKA,CAAA,EAAG;IACV,MAAMjC,CAAC,GAAG,IAAI,CAAC8E,mBAAmB,KAAK,KAAK,CAAC,GAAG,IAAI,CAACA,mBAAmB,GAAG,IAAI,CAACrD,KAAK,CAACQ,KAAK,KAAK,KAAK,CAAC,GAAG,IAAI,CAACR,KAAK,CAACQ,KAAK,GAAG,IAAI,CAACuB,KAAK,CAACvB,KAAK;IAC5I,OAAOjC,CAAC,KAAK,IAAI,GAAGxE,CAAC,CAACwE,CAAC,CAAC,GAAG,IAAI;EACjC;EACA;AACF;AACA;EACE,IAAI8B,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAACiD,kBAAkB,KAAK,KAAK,CAAC,GAAG,IAAI,CAACA,kBAAkB,GAAG,IAAI,CAACtD,KAAK,CAACK,IAAI,KAAK,KAAK,CAAC,GAAG,IAAI,CAACL,KAAK,CAACK,IAAI,GAAG,IAAI,CAAC0B,KAAK,CAAC1B,IAAI;EACtI;EACA;AACF;AACA;EACE,IAAI6E,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAAClF,KAAK,CAACkF,IAAI;EACxB;EACA;AACF;AACA;EACE,IAAIC,QAAQA,CAAA,EAAG;IACb,MAAM5G,CAAC,GAAG,IAAI,CAACiC,KAAK,IAAI,IAAI,CAACoE,aAAa,CAAC,IAAI,CAACpE,KAAK,CAAC;MAAErB,CAAC,GAAG,IAAI,CAACyF,aAAa,CAAC,IAAI,CAACtE,GAAG,CAAC;MAAEjB,CAAC,GAAG,IAAI,CAACuF,aAAa,CAAC,IAAI,CAACrE,GAAG,CAAC;MAAEhB,CAAC,GAAGhC,EAAE,CAACgB,CAAC,EAAEY,CAAC,EAAEE,CAAC,CAAC;MAAEI,CAAC,GAAG,IAAI,CAACO,KAAK,CAACoF,iBAAiB,KAAK,KAAK,CAAC;MAAEzF,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC0F,QAAQ,IAAI,IAAI,CAAC7E,KAAK,KAAK,IAAI,KAAKjB,CAAC;MAAEM,CAAC,GAAG,IAAI,CAACG,KAAK,CAACsF,KAAK,KAAK,KAAK,CAAC,GAAG,IAAI,CAACtF,KAAK,CAACsF,KAAK,GAAG3F,CAAC;IAClS,OAAO;MACL4F,WAAW,EAAE9F,CAAC;MACd+F,aAAa,EAAE/H,EAAE,CAACc,CAAC,EAAEc,CAAC,CAAC;MACvBoG,cAAc,EAAE9H,EAAE,CAACY,CAAC,EAAEY,CAAC,CAAC;MACxBmG,KAAK,EAAEzF,CAAC;MACR6F,YAAY,EAAE,IAAI,CAAClF,KAAK,KAAK;IAC/B,CAAC;EACH;EACA;AACF;AACA;EACE,IAAIJ,UAAUA,CAAA,EAAG;IACf,IAAIjB,CAAC;IACL,OAAO,CAAC,EAAE,IAAI,CAAC4C,KAAK,CAACD,WAAW,IAAI,IAAI,CAAC9B,KAAK,CAAC2F,aAAa,IAAI,IAAI,CAAC5D,KAAK,CAACD,WAAW,KAAK,CAAC3C,CAAC,GAAG,IAAI,CAACa,KAAK,CAAC2F,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGxG,CAAC,CAACyG,MAAM,CAAC,IAAI,IAAI,CAAC5F,KAAK,CAAC6F,QAAQ,CAAC;EAChL;EACA;AACF;AACA;EACE,IAAIC,cAAcA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAC9F,KAAK,CAAC8F,cAAc,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC9F,KAAK,CAAC8F,cAAc,GAAG1H,CAAC,CAACsG,YAAY,CAACoB,cAAc;EACzG;EACA;AACF;AACA;EACE,IAAIT,QAAQA,CAAA,EAAG;IACb,OAAO,IAAI,CAACrF,KAAK,CAACqF,QAAQ,KAAK,KAAK,CAAC,GAAG,IAAI,CAACrF,KAAK,CAACqF,QAAQ,GAAG,CAAC,CAAC;EAClE;EACA,IAAIpE,aAAaA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACjB,KAAK,CAACiB,aAAa,IAAI7C,CAAC,CAACsG,YAAY,CAACzD,aAAa;EACjE;EACA,IAAIX,GAAGA,CAAA,EAAG;IACR,OAAO,IAAI,CAACN,KAAK,CAACM,GAAG,KAAK,KAAK,CAAC,GAAG,IAAI,CAACN,KAAK,CAACM,GAAG,GAAGlC,CAAC,CAACsG,YAAY,CAACpE,GAAG;EACxE;EACA,IAAIC,GAAGA,CAAA,EAAG;IACR,OAAO,IAAI,CAACP,KAAK,CAACO,GAAG,KAAK,KAAK,CAAC,GAAG,IAAI,CAACP,KAAK,CAACO,GAAG,GAAGnC,CAAC,CAACsG,YAAY,CAACnE,GAAG;EACxE;EACA,IAAIwF,aAAaA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC/F,KAAK,CAAChB,SAAS,IAAIZ,CAAC,CAACsG,YAAY,CAAC1F,SAAS;EACzD;EACA,IAAIyD,mBAAmBA,CAAA,EAAG;IACxB,OAAO9G,EAAE,CAAC,IAAI,CAAC;EACjB;EACA;AACF;AACA;EACEqK,iBAAiBA,CAAA,EAAG;IAClB,IAAIzH,CAAC;IACL,IAAI,CAAC0H,cAAc,GAAGxL,CAAC,IAAIyL,MAAM,CAACC,cAAc,IAAI,IAAID,MAAM,CAACC,cAAc,CAAC,IAAI,CAACC,cAAc,CAACvB,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAACxE,IAAI,IAAI,IAAI,CAACgG,WAAW,CAAC,CAAC,EAAE,CAAC9H,CAAC,GAAG,IAAI,CAACuG,QAAQ,KAAK,IAAI,IAAIvG,CAAC,CAAC+H,IAAI,IAAI,IAAI,CAACL,cAAc,IAAI,IAAI,CAACA,cAAc,CAACM,OAAO,CAAC,IAAI,CAACzB,QAAQ,CAACwB,IAAI,CAAC;EACjQ;EACA;AACF;AACA;EACEE,kBAAkBA,CAAA,EAAG;IACnB,IAAI,CAAC7H,aAAa,IAAI,IAAI,CAAC0B,IAAI,IAAI,CAAC,IAAI,CAACxB,QAAQ,IAAI,IAAI,CAACF,aAAa,CAAC8H,eAAe,CAAC,CAAC,EAAE,IAAI,CAACrG,UAAU,IAAI,IAAI,CAACC,IAAI,IAAI,CAAC,IAAI,CAACxB,QAAQ,IAAI6H,UAAU,CAAC,MAAM;MAC5J,IAAI,CAAC/H,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC8H,eAAe,CAAC,CAAC;IAC5D,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAACzH,SAAS,IAAI,IAAI,CAACA,SAAS,CAACoC,OAAO,IAAI,CAAC,IAAI,CAACf,IAAI,IAAI,IAAI,CAACzB,oBAAoB,IAAI,IAAI,CAACI,SAAS,CAACoC,OAAO,CAACrC,KAAK,CAAC;MAAE4H,aAAa,EAAE,CAAC;IAAE,CAAC,CAAC,EAAE,IAAI,CAAC9H,QAAQ,GAAG,IAAI,CAACwB,IAAI,EAAE,IAAI,CAACzB,oBAAoB,GAAG,CAAC,CAAC;EAChN;EACA;AACF;AACA;EACEgI,oBAAoBA,CAAA,EAAG;IACrB,IAAIrI,CAAC;IACL,CAACA,CAAC,GAAG,IAAI,CAACuG,QAAQ,KAAK,IAAI,IAAIvG,CAAC,CAAC+H,IAAI,IAAI,IAAI,CAACL,cAAc,IAAI,IAAI,CAACA,cAAc,CAACY,UAAU,CAAC,CAAC;EAClG;EACA;AACF;AACA;EACEC,MAAMA,CAAA,EAAG;IACP,MAAM;QACJC,IAAI,EAAExI,CAAC,GAAGH,CAAC,CAACsG,YAAY,CAACqC,IAAI;QAC7BC,OAAO,EAAE7H,CAAC,GAAGf,CAAC,CAACsG,YAAY,CAACsC,OAAO;QACnCC,QAAQ,EAAE5H,CAAC,GAAGjB,CAAC,CAACsG,YAAY,CAACuC,QAAQ;QACrCzH,QAAQ,EAAED,CAAC;QACX2H,QAAQ,EAAEzH,CAAC;QACX0C,KAAK,EAAExC,CAAC;QACR4B,EAAE,EAAE1B,CAAC;QACLyB,SAAS,EAAEvB,CAAC;QACZL,MAAM,EAAEyH,CAAC;QACTC,iBAAiB,EAAEC,CAAC;QACpBC,KAAK,EAAEC,CAAC;QACRrC,IAAI,EAAEsC,CAAC;QACP5H,KAAK,EAAE6H,CAAC;QACRrC,iBAAiB,EAAEsC,CAAC;QACpBrC,QAAQ,EAAEsC,CAAC;QACX7B,cAAc,EAAE8B,CAAC;QACjBC,cAAc,EAAEC,CAAC;QACjBC,eAAe,EAAEC,CAAC;QAClBlI,QAAQ,EAAEmI,CAAC;QACXC,gBAAgB,EAAEC,CAAC;QACnBC,gBAAgB,EAAEC,CAAC;QACnBC,eAAe,EAAEC,CAAC;QAClBC,cAAc,EAAEC,CAAC;QACjBC,cAAc,EAAEC,CAAC;QACjBC,eAAe,EAAEC;MACnB,CAAC,GAAG,IAAI,CAAC7I,KAAK;MAAE8I,CAAC,GAAGb,CAAC,IAAIA,CAAC,CAAC/N,WAAW;MAAE6O,CAAC,GAAG,CAAC,IAAI,CAACjD,cAAc,IAAI,IAAI,CAACX,QAAQ,CAACG,KAAK;MAAE0D,CAAC,GAAG;QAC3FxJ,QAAQ,EAAED,CAAC;QACXG,MAAM,EAAEyH,CAAC;QACTC,iBAAiB,EAAEC,CAAC;QACpB9F,EAAE,EAAE1B,CAAC;QACLgI,cAAc,EAAEC,CAAC;QACjBC,eAAe,EAAEC,CAAC;QAClBzH,GAAG,EAAE,IAAI,CAACqE,aAAa,CAAC,IAAI,CAACrE,GAAG,CAAC;QACjCD,GAAG,EAAE,IAAI,CAACsE,aAAa,CAAC,IAAI,CAACtE,GAAG,CAAC;QACjC4E,IAAI,EAAEsC,CAAC;QACP7G,QAAQ,EAAE,IAAI,CAACqC,sBAAsB;QACrCqC,QAAQ,EAAEsC,CAAC;QACX/H,KAAK,EAAE6H,CAAC;QACRP,QAAQ,EAAE,IAAI,CAAC7G,IAAI,GAAG,CAAC,CAAC,GAAGZ,CAAC;QAC5B0C,KAAK,EAAExC,CAAC;QACR2F,KAAK,EAAE,IAAI,CAACH,QAAQ,CAACG,KAAK;QAC1BF,iBAAiB,EAAEsC,CAAC;QACpB5B,cAAc,EAAE8B,CAAC;QACjBpH,KAAK,EAAE,IAAI,CAACA,KAAK,IAAI,IAAI,CAACoE,aAAa,CAAC,IAAI,CAACpE,KAAK,CAAC;QACnD6B,KAAK,EAAE,KAAK,CAAC;QACb4G,WAAW,EAAE,IAAI,CAAClH,KAAK,CAAC8B,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC7D,KAAK,CAACiJ,WAAW;QAC/DC,YAAY,EAAE,QAAQ;QACtBC,YAAY,EAAE,IAAI,CAAC9I,IAAI;QACvB0G,IAAI,EAAE,IAAI;QACVE,QAAQ,EAAE,IAAI;QACdD,OAAO,EAAE,IAAI;QACbkB,gBAAgB,EAAEC,CAAC;QACnBC,gBAAgB,EAAEC,CAAC;QACnBC,eAAe,EAAEC,CAAC;QAClBC,cAAc,EAAEC,CAAC;QACjBC,cAAc,EAAEC,CAAC;QACjBC,eAAe,EAAEC;MACnB,CAAC;MAAEO,CAAC,GAAG,IAAI,CAAC3G,mBAAmB,CAACC,gBAAgB,CAACzG,CAAC,EAAEE,CAAC,CAACF,CAAC,CAAC,CAAC;MAAEoN,CAAC,GAAG,IAAI,CAAC5G,mBAAmB,CAACC,gBAAgB,CACtGjG,CAAC,EACDN,CAAC,CAACM,CAAC,CACL,CAAC;MAAE6M,CAAC,GAAG,eAAgB5P,CAAC,CAACuG,aAAa,CACpCtF,CAAC,EACD;QACE4O,OAAO,EAAE,IAAI,CAAC3F,WAAW;QACzB4F,MAAM,EAAE,IAAI,CAACpJ,UAAU,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC8B,UAAU;QAClDuH,UAAU,EAAE,IAAI,CAACzJ,KAAK,CAACwJ,MAAM;QAC7BE,WAAW,EAAE,IAAI,CAAC1J,KAAK,CAACuJ;MAC1B,CAAC,EACD,CAAC;QAAEA,OAAO,EAAEI,CAAC;QAAEH,MAAM,EAAEI;MAAE,CAAC,KAAK,eAAgBlQ,CAAC,CAACuG,aAAa,CAACvG,CAAC,CAACmQ,QAAQ,EAAE,IAAI,EAAE,eAAgBnQ,CAAC,CAACuG,aAAa,CAC9G,MAAM,EACN;QACEC,GAAG,EAAG4J,CAAC,IAAK;UACV,IAAI,CAACtL,QAAQ,GAAGsL,CAAC;QACnB,CAAC;QACDxI,SAAS,EAAErH,CAAC,CACVE,CAAC,CAAC4P,OAAO,CAAC;UACR9P,CAAC,EAAE6O,CAAC;UACJ/B,IAAI,EAAExI,CAAC;UACPyI,OAAO,EAAE7H,CAAC;UACV8H,QAAQ,EAAE5H,CAAC;UACX2K,OAAO,EAAE,CAACjB,CAAC;UACX1D,QAAQ,EAAE,IAAI,CAACA,QAAQ;UACvB7F,QAAQ,EAAED;QACZ,CAAC,CAAC,EACFQ,CACF,CAAC;QACDkK,SAAS,EAAE,IAAI,CAAChG,aAAa;QAC7BiG,KAAK,EAAE;UAAE5C,KAAK,EAAEC;QAAE,CAAC;QACnBgC,OAAO,EAAE,IAAI,CAACnJ,UAAU,GAAG,IAAI,CAAC0D,WAAW,GAAG6F,CAAC;QAC/CH,MAAM,EAAEI,CAAC;QACTO,OAAO,EAAE,IAAI,CAAC/J,UAAU,GAAG,IAAI,CAAC0D,WAAW,GAAG,KAAK;MACrD,CAAC,EACD,eAAgBpK,CAAC,CAACuG,aAAa,CAC7B,IAAI,CAAC8F,aAAa,EAClB;QACEqE,IAAI,EAAE,IAAI,CAAC3L,UAAU;QACrB4L,QAAQ,EAAE,UAAU;QACpBC,YAAY,EAAE,IAAI,CAAC9I,QAAQ;QAC3B,GAAGwH;MACL,CACF,CAAC,EACD,eAAgBtP,CAAC,CAACuG,aAAa,CAC7BlC,EAAE,EACF;QACEmJ,QAAQ,EAAE,CAAC,CAAC;QACZqD,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE,OAAO;QACbC,OAAO,EAAEhP,EAAE;QACXiP,WAAW,EAAE,IAAI,CAAC3G,mBAAmB;QACrCoG,OAAO,EAAE,IAAI,CAAC/J,UAAU,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC0D,WAAW;QACpD3B,KAAK,EAAEkH,CAAC;QACR/H,SAAS,EAAErH,CAAC,CAACE,CAAC,CAACwQ,WAAW,CAAC;UAAE1Q,CAAC,EAAE6O;QAAE,CAAC,CAAC,CAAC;QACrC9B,OAAO,EAAE,IAAI;QACbC,QAAQ,EAAE5H,CAAC;QACX,YAAY,EAAE+J;MAChB,CACF,CAAC,EACD,CAAC,IAAI,CAAChJ,UAAU,IAAI,IAAI,CAACW,WAAW,CAAC,CACvC,CAAC,EAAE,IAAI,CAACX,UAAU,IAAI,IAAI,CAACyB,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC/C,oBAAoB,IAAI,eAAgBpF,CAAC,CAACuG,aAAa,CAACpF,CAAC,EAAE,IAAI,CAAC,CACzH,CAAC;IACD,OAAO,IAAI,CAACmF,KAAK,CAACqC,KAAK,GAAG,eAAgB3I,CAAC,CAACuG,aAAa,CACvDpC,EAAE,EACF;MACEmB,SAAS,EAAE,IAAI,CAACP,UAAU;MAC1B4D,KAAK,EAAE,IAAI,CAACrC,KAAK,CAACqC,KAAK;MACvBuI,QAAQ,EAAE/K,CAAC;MACXgL,WAAW,EAAE9B,CAAC;MACd+B,cAAc,EAAE,IAAI,CAAC9K,KAAK,CAACR,QAAQ;MACnCuL,QAAQ,EAAEzB,CAAC;MACXY,KAAK,EAAE;QAAE5C,KAAK,EAAE,IAAI,CAACtH,KAAK,CAACsH;MAAM;IACnC,CACF,CAAC,GAAGgC,CAAC;EACP;EACA1E,aAAaA,CAACrG,CAAC,EAAE;IACf,OAAOpB,CAAC,CAACE,EAAE,EAAEkB,CAAC,CAAC;EACjB;EACAoF,OAAOA,CAACpF,CAAC,EAAE;IACT,MAAM;MAAEyM,MAAM,EAAE7L,CAAC;MAAE8C,OAAO,EAAE5C;IAAE,CAAC,GAAG,IAAI,CAACW,KAAK;IAC5C,IAAI,CAACK,IAAI,KAAK9B,CAAC,KAAK,IAAI,CAAC2E,QAAQ,CAAC;MAAE7C,IAAI,EAAE9B;IAAE,CAAC,CAAC,EAAEA,CAAC,IAAIY,CAAC,IAAIA,CAAC,CAACoE,IAAI,CAAC,KAAK,CAAC,EAAE;MACvEG,MAAM,EAAE;IACV,CAAC,CAAC,EAAE,CAACnF,CAAC,IAAIc,CAAC,IAAIA,CAAC,CAACkE,IAAI,CAAC,KAAK,CAAC,EAAE;MAC5BG,MAAM,EAAE;IACV,CAAC,CAAC,CAAC;EACL;EACAT,SAASA,CAAC1E,CAAC,EAAE;IACX,OAAO,IAAI,CAACiC,KAAK,IAAIjC,CAAC,GAAGpB,CAAC,CAAC,IAAI,CAACqD,KAAK,EAAEjC,CAAC,CAAC,GAAGA,CAAC;EAC/C;EACA6H,cAAcA,CAAC7H,CAAC,EAAE;IAChB,KAAK,MAAMY,CAAC,IAAIZ,CAAC,EACf,IAAI,CAAC2E,QAAQ,CAAC;MAAEpB,WAAW,EAAE3C,CAAC,CAACuE,MAAM,CAACuH;IAAY,CAAC,CAAC;EACxD;AACF,CAAC;AACD7M,CAAC,CAAC8M,WAAW,GAAG,YAAY,EAAE9M,CAAC,CAAC+M,SAAS,GAAG;EAC1C7J,SAAS,EAAE3H,CAAC,CAACyR,MAAM;EACnBhM,YAAY,EAAEzF,CAAC,CAAC0R,IAAI;EACpB/L,SAAS,EAAE3F,CAAC,CAAC0R,IAAI;EACjB1G,WAAW,EAAEhL,CAAC,CAAC0R,IAAI;EACnB5G,YAAY,EAAE9K,CAAC,CAAC2R,UAAU,CAACC,IAAI,CAAC;EAChC/L,QAAQ,EAAE7F,CAAC,CAAC0R,IAAI;EAChB3L,MAAM,EAAE/F,CAAC,CAAC6R,SAAS,CAAC,CAClB7R,CAAC,CAACyR,MAAM,EACRzR,CAAC,CAAC8R,KAAK,CAAC;IACNC,QAAQ,EAAE/R,CAAC,CAACyR,MAAM;IAClBO,OAAO,EAAEhS,CAAC,CAACyR,MAAM;IACjBQ,IAAI,EAAEjS,CAAC,CAACkS,KAAK,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IAClDzI,IAAI,EAAEzJ,CAAC,CAACkS,KAAK,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IAClDC,QAAQ,EAAEnS,CAAC,CAACkS,KAAK,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IACtDE,GAAG,EAAEpS,CAAC,CAACkS,KAAK,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IACzCG,IAAI,EAAErS,CAAC,CAACkS,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IACrCI,KAAK,EAAEtS,CAAC,CAACkS,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IACjEK,GAAG,EAAEvS,CAAC,CAACkS,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IACpCM,OAAO,EAAExS,CAAC,CAACkS,KAAK,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IAC7CO,IAAI,EAAEzS,CAAC,CAACkS,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IACrCQ,MAAM,EAAE1S,CAAC,CAAC0R,IAAI;IACdiB,MAAM,EAAE3S,CAAC,CAACkS,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IACvCU,MAAM,EAAE5S,CAAC,CAACkS,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IACvCW,YAAY,EAAE7S,CAAC,CAACkS,KAAK,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC;EACzC,CAAC,CAAC,CACH,CAAC;EACFzE,iBAAiB,EAAEzN,CAAC,CAAC6R,SAAS,CAAC,CAC7B7R,CAAC,CAACkS,KAAK,CAAC,CACN,MAAM,EACN,QAAQ,EACR,OAAO,EACP,eAAe,CAChB,CAAC,EACFlS,CAAC,CAAC8R,KAAK,CAAC;IACNO,IAAI,EAAErS,CAAC,CAACyR,MAAM;IACda,KAAK,EAAEtS,CAAC,CAACyR,MAAM;IACfc,GAAG,EAAEvS,CAAC,CAACyR,MAAM;IACbgB,IAAI,EAAEzS,CAAC,CAACyR,MAAM;IACdkB,MAAM,EAAE3S,CAAC,CAACyR,MAAM;IAChBmB,MAAM,EAAE5S,CAAC,CAACyR;EACZ,CAAC,CAAC,CACH,CAAC;EACF7J,EAAE,EAAE5H,CAAC,CAACyR,MAAM;EACZvD,cAAc,EAAElO,CAAC,CAACyR,MAAM;EACxBrD,eAAe,EAAEpO,CAAC,CAACyR,MAAM;EACzB9K,GAAG,EAAE3G,CAAC,CAAC2R,UAAU,CAACC,IAAI,CAAC;EACvBhL,GAAG,EAAE5G,CAAC,CAAC2R,UAAU,CAACC,IAAI,CAAC;EACvBrG,IAAI,EAAEvL,CAAC,CAACyR,MAAM;EACdnK,aAAa,EAAEtH,CAAC,CAAC8R,KAAK,CAAC;IACrBtK,OAAO,EAAExH,CAAC,CAAC0R,IAAI;IACfoB,QAAQ,EAAE9S,CAAC,CAAC+S,GAAG;IACf1L,UAAU,EAAErH,CAAC,CAACyR;EAChB,CAAC,CAAC;EACF/K,IAAI,EAAE1G,CAAC,CAAC0R,IAAI;EACZzL,KAAK,EAAEjG,CAAC,CAAC8R,KAAK,CAAC;IACbW,IAAI,EAAEzS,CAAC,CAACgT,MAAM;IACdL,MAAM,EAAE3S,CAAC,CAACgT,MAAM;IAChBJ,MAAM,EAAE5S,CAAC,CAACgT;EACZ,CAAC,CAAC;EACFzN,YAAY,EAAEvF,CAAC,CAAC0R,IAAI;EACpBnE,QAAQ,EAAEvN,CAAC,CAACgT,MAAM;EAClBxK,KAAK,EAAExI,CAAC,CAACyR,MAAM;EACf5K,KAAK,EAAE7G,CAAC,CAAC2R,UAAU,CAACC,IAAI,CAAC;EACzBjE,KAAK,EAAE3N,CAAC,CAAC6R,SAAS,CAAC,CAAC7R,CAAC,CAACgT,MAAM,EAAEhT,CAAC,CAACyR,MAAM,CAAC,CAAC;EACxChG,iBAAiB,EAAEzL,CAAC,CAACyR,MAAM;EAC3B/F,QAAQ,EAAE1L,CAAC,CAAC0R,IAAI;EAChBuB,QAAQ,EAAEjT,CAAC,CAAC0R,IAAI;EAChB/F,KAAK,EAAE3L,CAAC,CAAC0R,IAAI;EACbtE,IAAI,EAAEpN,CAAC,CAACkS,KAAK,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;EACjD7E,OAAO,EAAErN,CAAC,CAACkS,KAAK,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;EAC5D5E,QAAQ,EAAEtN,CAAC,CAACkS,KAAK,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;EACrDjD,eAAe,EAAEjP,CAAC,CAACkT;AACrB,CAAC,EAAEzO,CAAC,CAACsG,YAAY,GAAG;EAClBC,WAAW,EAAE,CAAC,CAAC;EACfF,YAAY,EAAE,IAAI;EAClBjF,QAAQ,EAAE,CAAC,CAAC;EACZE,MAAM,EAAE,GAAG;EACXa,GAAG,EAAExD,EAAE;EACPuD,GAAG,EAAErD,EAAE;EACPgE,aAAa,EAAE,CAAC,CAAC;EACjBiG,QAAQ,EAAE,CAAC;EACXtH,KAAK,EAAE,CAAC,CAAC;EACTkG,cAAc,EAAE,CAAC,CAAC;EAClB9G,SAAS,EAAErC,EAAE;EACboK,IAAI,EAAE,QAAQ;EACdC,OAAO,EAAE,QAAQ;EACjBC,QAAQ,EAAE;AACZ,CAAC;AACD,IAAI6F,CAAC,GAAG1O,CAAC;AACT,MAAM2O,EAAE,GAAGhS,CAAC,CAAC,CAAC;EAAEiS,EAAE,GAAG/R,CAAC,CACpBE,EAAE,CACA4R,EAAE,EACF1R,EAAE,CAACE,EAAE,CAACuR,CAAC,CAAC,CACV,CACF,CAAC;AACDE,EAAE,CAAC9B,WAAW,GAAG,sBAAsB;AACvCrP,EAAE,CAACiR,CAAC,CAAC;AACL,SACEE,EAAE,IAAIC,UAAU,EAChBF,EAAE,IAAIG,sBAAsB,EAC5BJ,CAAC,IAAIK,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}