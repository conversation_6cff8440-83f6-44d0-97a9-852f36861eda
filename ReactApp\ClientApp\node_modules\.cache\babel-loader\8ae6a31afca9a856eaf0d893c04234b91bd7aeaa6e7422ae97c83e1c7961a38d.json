{"ast": null, "code": "import \"antd/es/spin/style\";\nimport _Spin from \"antd/es/spin\";\nvar _jsxFileName = \"D:\\\\Zone24x7\\\\Workspaces\\\\FrontEnd-Portal\\\\ReactApp\\\\ClientApp\\\\src\\\\app\\\\components\\\\TermsAndConditions\\\\index.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { PDFViewer } from '@progress/kendo-react-pdf-viewer';\nimport { Button } from '@progress/kendo-react-buttons';\nimport { Loader } from '@progress/kendo-react-indicators';\nimport styles from './index.module.less';\nimport { FiDownload, FiPrinter } from 'react-icons/fi';\nimport { useTermsAndConditions } from '@app/hooks/useTermsAndConditions';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Spinner = ({\n  themeColor\n}) => /*#__PURE__*/_jsxDEV(Loader, {\n  size: \"small\",\n  themeColor: themeColor,\n  type: \"converging-spinner\"\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 11,\n  columnNumber: 3\n}, this);\n_c = Spinner;\nconst TermsAndConditions = () => {\n  _s();\n  const {\n    tncDocument,\n    pdfViewerRef,\n    tncLoading,\n    pdfDocument,\n    setPdfDocument,\n    tncRefetch,\n    isAccepting,\n    onDocumentLoad,\n    handleAgree,\n    clickToolbarButtonByTitle\n  } = useTermsAndConditions();\n  if (!tncDocument || !tncDocument.document) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: styles.dialogMainContainer,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.pdfViewContainer,\n      children: /*#__PURE__*/_jsxDEV(PDFViewer, {\n        url: tncDocument.document.documentUrl,\n        onLoad: onDocumentLoad,\n        ref: pdfViewerRef,\n        tools: ['pager', 'print', 'download', 'selection', 'zoomInOut'],\n        style: {\n          height: '100%'\n        },\n        zoom: 0.9\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.agreement_text,\n      children: tncDocument.document.statementOfAgreement\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.dialogActionsBar,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.dialogActionsBar_left,\n        children: [/*#__PURE__*/_jsxDEV(Button\n        // startIcon={tncLoading ? <Spinner themeColor=\"primary\" /> : <FiRefreshCw />}\n        , {\n          disabled: tncLoading,\n          themeColor: \"primary\",\n          fillMode: \"outline\",\n          onClick: tncRefetch,\n          children: tncLoading ? /*#__PURE__*/_jsxDEV(_Spin, {\n            spinning: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 27\n          }, this) : 'Refresh'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          startIcon: /*#__PURE__*/_jsxDEV(FiDownload, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 24\n          }, this),\n          themeColor: \"primary\",\n          fillMode: \"outline\",\n          onClick: () => clickToolbarButtonByTitle('Download'),\n          disabled: tncLoading,\n          children: \"Download\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          startIcon: /*#__PURE__*/_jsxDEV(FiPrinter, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 24\n          }, this),\n          disabled: tncLoading,\n          themeColor: \"primary\",\n          fillMode: \"outline\",\n          onClick: () => clickToolbarButtonByTitle('Print'),\n          children: \"Print\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        themeColor: \"primary\",\n        fillMode: \"solid\",\n        onClick: handleAgree,\n        disabled: isAccepting || tncLoading\n        // startIcon={isAccepting ? <Spinner themeColor=\"light\" /> : undefined}\n        ,\n        children: isAccepting ? /*#__PURE__*/_jsxDEV(_Spin, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 26\n        }, this) : 'AGREE & CONTINUE'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n};\n_s(TermsAndConditions, \"2oaydOSlQBhYVkEedjZmbwZzIzs=\", false, function () {\n  return [useTermsAndConditions];\n});\n_c2 = TermsAndConditions;\nexport default TermsAndConditions;\nvar _c, _c2;\n$RefreshReg$(_c, \"Spinner\");\n$RefreshReg$(_c2, \"TermsAndConditions\");", "map": {"version": 3, "names": ["React", "PDFViewer", "<PERSON><PERSON>", "Loader", "styles", "FiDownload", "FiPrinter", "useTermsAndConditions", "jsxDEV", "_jsxDEV", "Spinner", "themeColor", "size", "type", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "TermsAndConditions", "_s", "tncDocument", "pdfViewerRef", "tncLoading", "pdfDocument", "setPdfDocument", "tncRefetch", "isAccepting", "onDocumentLoad", "handleAgree", "clickToolbarButtonByTitle", "document", "className", "dialogMainContainer", "children", "pdfViewContainer", "url", "documentUrl", "onLoad", "ref", "tools", "style", "height", "zoom", "agreement_text", "statementOfAgreement", "dialogActionsBar", "dialogActionsBar_left", "disabled", "fillMode", "onClick", "_Spin", "spinning", "startIcon", "_c2", "$RefreshReg$"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/src/app/components/TermsAndConditions/index.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { PDFViewer } from '@progress/kendo-react-pdf-viewer';\r\nimport { Button } from '@progress/kendo-react-buttons';\r\nimport { Loader, LoaderThemeColor } from '@progress/kendo-react-indicators';\r\nimport styles from './index.module.less';\r\nimport { FiRefreshCw, FiDownload, FiPrinter } from 'react-icons/fi';\r\nimport { useTermsAndConditions } from '@app/hooks/useTermsAndConditions';\r\nimport { Spin } from 'antd';\r\n\r\nconst Spinner = ({ themeColor }: { themeColor?: LoaderThemeColor }) => (\r\n  <Loader size=\"small\" themeColor={themeColor} type=\"converging-spinner\" />\r\n);\r\n\r\nconst TermsAndConditions: React.FC = () => {\r\n  const {\r\n    tncDocument,\r\n    pdfViewerRef,\r\n    tncLoading,\r\n    pdfDocument,\r\n    setPdfDocument,\r\n    tncRefetch,\r\n    isAccepting,\r\n    onDocumentLoad,\r\n    handleAgree,\r\n    clickToolbarButtonByTitle\r\n  } = useTermsAndConditions();\r\n\r\n  if (!tncDocument || !tncDocument.document) return null;\r\n\r\n  return (\r\n    <div className={styles.dialogMainContainer}>\r\n      <div className={styles.pdfViewContainer}>\r\n        <PDFViewer\r\n          url={tncDocument.document.documentUrl}\r\n          onLoad={onDocumentLoad}\r\n          ref={pdfViewerRef}\r\n          tools={['pager', 'print', 'download', 'selection', 'zoomInOut']}\r\n          style={{ height: '100%' }}\r\n          zoom={0.9}\r\n        />\r\n      </div>\r\n\r\n      <div className={styles.agreement_text}>\r\n        {tncDocument.document.statementOfAgreement}\r\n      </div>\r\n\r\n      <div className={styles.dialogActionsBar}>\r\n        <div className={styles.dialogActionsBar_left}>\r\n          <Button\r\n            // startIcon={tncLoading ? <Spinner themeColor=\"primary\" /> : <FiRefreshCw />}\r\n            disabled={tncLoading}\r\n            themeColor=\"primary\"\r\n            fillMode=\"outline\"\r\n            onClick={tncRefetch}\r\n          >\r\n            {tncLoading ? <Spin spinning /> : 'Refresh'} \r\n          </Button>\r\n\r\n          <Button\r\n            startIcon={<FiDownload />}\r\n            themeColor=\"primary\"\r\n            fillMode=\"outline\"\r\n            onClick={() => clickToolbarButtonByTitle('Download')}\r\n            disabled={tncLoading}\r\n          >\r\n            Download\r\n          </Button>\r\n\r\n          <Button\r\n            startIcon={<FiPrinter />}\r\n            disabled={tncLoading}\r\n            themeColor=\"primary\"\r\n            fillMode=\"outline\"\r\n            onClick={() => clickToolbarButtonByTitle('Print')}\r\n          >\r\n            Print\r\n          </Button>\r\n        </div>\r\n\r\n        <Button\r\n          themeColor=\"primary\"\r\n          fillMode=\"solid\"\r\n          onClick={handleAgree}\r\n          disabled={isAccepting || tncLoading}\r\n          // startIcon={isAccepting ? <Spinner themeColor=\"light\" /> : undefined}\r\n        >\r\n          {isAccepting ? <Spin /> : 'AGREE & CONTINUE'}\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TermsAndConditions;\r\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,SAASC,MAAM,QAAQ,+BAA+B;AACtD,SAASC,MAAM,QAA0B,kCAAkC;AAC3E,OAAOC,MAAM,MAAM,qBAAqB;AACxC,SAAsBC,UAAU,EAAEC,SAAS,QAAQ,gBAAgB;AACnE,SAASC,qBAAqB,QAAQ,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGzE,MAAMC,OAAO,GAAGA,CAAC;EAAEC;AAA8C,CAAC,kBAChEF,OAAA,CAACN,MAAM;EAACS,IAAI,EAAC,OAAO;EAACD,UAAU,EAAEA,UAAW;EAACE,IAAI,EAAC;AAAoB;EAAAC,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAE,CACzE;AAACC,EAAA,GAFIR,OAAO;AAIb,MAAMS,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM;IACJC,WAAW;IACXC,YAAY;IACZC,UAAU;IACVC,WAAW;IACXC,cAAc;IACdC,UAAU;IACVC,WAAW;IACXC,cAAc;IACdC,WAAW;IACXC;EACF,CAAC,GAAGvB,qBAAqB,CAAC,CAAC;EAE3B,IAAI,CAACc,WAAW,IAAI,CAACA,WAAW,CAACU,QAAQ,EAAE,OAAO,IAAI;EAEtD,oBACEtB,OAAA;IAAKuB,SAAS,EAAE5B,MAAM,CAAC6B,mBAAoB;IAAAC,QAAA,gBACzCzB,OAAA;MAAKuB,SAAS,EAAE5B,MAAM,CAAC+B,gBAAiB;MAAAD,QAAA,eACtCzB,OAAA,CAACR,SAAS;QACRmC,GAAG,EAAEf,WAAW,CAACU,QAAQ,CAACM,WAAY;QACtCC,MAAM,EAAEV,cAAe;QACvBW,GAAG,EAAEjB,YAAa;QAClBkB,KAAK,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,CAAE;QAChEC,KAAK,EAAE;UAAEC,MAAM,EAAE;QAAO,CAAE;QAC1BC,IAAI,EAAE;MAAI;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENR,OAAA;MAAKuB,SAAS,EAAE5B,MAAM,CAACwC,cAAe;MAAAV,QAAA,EACnCb,WAAW,CAACU,QAAQ,CAACc;IAAoB;MAAA/B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC,eAENR,OAAA;MAAKuB,SAAS,EAAE5B,MAAM,CAAC0C,gBAAiB;MAAAZ,QAAA,gBACtCzB,OAAA;QAAKuB,SAAS,EAAE5B,MAAM,CAAC2C,qBAAsB;QAAAb,QAAA,gBAC3CzB,OAAA,CAACP;QACC;QAAA;UACA8C,QAAQ,EAAEzB,UAAW;UACrBZ,UAAU,EAAC,SAAS;UACpBsC,QAAQ,EAAC,SAAS;UAClBC,OAAO,EAAExB,UAAW;UAAAQ,QAAA,EAEnBX,UAAU,gBAAGd,OAAA,CAAA0C,KAAA;YAAMC,QAAQ;UAAA;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAS;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eAETR,OAAA,CAACP,MAAM;UACLmD,SAAS,eAAE5C,OAAA,CAACJ,UAAU;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BN,UAAU,EAAC,SAAS;UACpBsC,QAAQ,EAAC,SAAS;UAClBC,OAAO,EAAEA,CAAA,KAAMpB,yBAAyB,CAAC,UAAU,CAAE;UACrDkB,QAAQ,EAAEzB,UAAW;UAAAW,QAAA,EACtB;QAED;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETR,OAAA,CAACP,MAAM;UACLmD,SAAS,eAAE5C,OAAA,CAACH,SAAS;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzB+B,QAAQ,EAAEzB,UAAW;UACrBZ,UAAU,EAAC,SAAS;UACpBsC,QAAQ,EAAC,SAAS;UAClBC,OAAO,EAAEA,CAAA,KAAMpB,yBAAyB,CAAC,OAAO,CAAE;UAAAI,QAAA,EACnD;QAED;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENR,OAAA,CAACP,MAAM;QACLS,UAAU,EAAC,SAAS;QACpBsC,QAAQ,EAAC,OAAO;QAChBC,OAAO,EAAErB,WAAY;QACrBmB,QAAQ,EAAErB,WAAW,IAAIJ;QACzB;QAAA;QAAAW,QAAA,EAECP,WAAW,gBAAGlB,OAAA,CAAA0C,KAAA;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,GAAG;MAAkB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACG,EAAA,CA9EID,kBAA4B;EAAA,QAY5BZ,qBAAqB;AAAA;AAAA+C,GAAA,GAZrBnC,kBAA4B;AAgFlC,eAAeA,kBAAkB;AAAC,IAAAD,EAAA,EAAAoC,GAAA;AAAAC,YAAA,CAAArC,EAAA;AAAAqC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}