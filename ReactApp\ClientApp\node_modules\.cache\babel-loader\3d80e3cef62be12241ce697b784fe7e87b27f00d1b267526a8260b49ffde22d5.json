{"ast": null, "code": "import toCubicPolynomial from './to-cubic-polynomial';\nimport solveCubicEquation from './solve-cubic-equation';\nimport calculateCurveAt from './calculate-curve-at';\nexport default function hasRootsInRange(points, point, field, rootField, range) {\n  var polynomial = toCubicPolynomial(points, rootField);\n  var roots = solveCubicEquation(polynomial[0], polynomial[1], polynomial[2], polynomial[3] - point[rootField]);\n  var intersection;\n  for (var idx = 0; idx < roots.length; idx++) {\n    if (0 <= roots[idx] && roots[idx] <= 1) {\n      intersection = calculateCurveAt(roots[idx], field, points);\n      if (Math.abs(intersection - point[field]) <= range) {\n        return true;\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["toCubicPolynomial", "solveCubicEquation", "calculateCurveAt", "hasRootsInRange", "points", "point", "field", "rootField", "range", "polynomial", "roots", "intersection", "idx", "length", "Math", "abs"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/geometry/math/has-roots-in-range.js"], "sourcesContent": ["import toCubicPolynomial from './to-cubic-polynomial';\nimport solveCubicEquation from './solve-cubic-equation';\nimport calculateCurveAt from './calculate-curve-at';\n\nexport default function hasRootsInRange(points, point, field, rootField, range) {\n    var polynomial = toCubicPolynomial(points, rootField);\n    var roots = solveCubicEquation(polynomial[0], polynomial[1], polynomial[2], polynomial[3] - point[rootField]);\n    var intersection;\n\n    for (var idx = 0; idx < roots.length; idx++) {\n        if (0 <= roots[idx] && roots[idx] <= 1) {\n            intersection = calculateCurveAt(roots[idx], field, points);\n            if (Math.abs(intersection - point[field]) <= range) {\n                return true;\n            }\n        }\n    }\n}"], "mappings": "AAAA,OAAOA,iBAAiB,MAAM,uBAAuB;AACrD,OAAOC,kBAAkB,MAAM,wBAAwB;AACvD,OAAOC,gBAAgB,MAAM,sBAAsB;AAEnD,eAAe,SAASC,eAAeA,CAACC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,KAAK,EAAE;EAC5E,IAAIC,UAAU,GAAGT,iBAAiB,CAACI,MAAM,EAAEG,SAAS,CAAC;EACrD,IAAIG,KAAK,GAAGT,kBAAkB,CAACQ,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,GAAGJ,KAAK,CAACE,SAAS,CAAC,CAAC;EAC7G,IAAII,YAAY;EAEhB,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGF,KAAK,CAACG,MAAM,EAAED,GAAG,EAAE,EAAE;IACzC,IAAI,CAAC,IAAIF,KAAK,CAACE,GAAG,CAAC,IAAIF,KAAK,CAACE,GAAG,CAAC,IAAI,CAAC,EAAE;MACpCD,YAAY,GAAGT,gBAAgB,CAACQ,KAAK,CAACE,GAAG,CAAC,EAAEN,KAAK,EAAEF,MAAM,CAAC;MAC1D,IAAIU,IAAI,CAACC,GAAG,CAACJ,YAAY,GAAGN,KAAK,CAACC,KAAK,CAAC,CAAC,IAAIE,KAAK,EAAE;QAChD,OAAO,IAAI;MACf;IACJ;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}