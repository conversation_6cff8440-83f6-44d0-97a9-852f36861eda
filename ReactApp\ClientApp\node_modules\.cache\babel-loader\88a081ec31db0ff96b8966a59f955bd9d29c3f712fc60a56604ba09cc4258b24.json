{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst o = \"data-windowid\",\n  D = 10002,\n  n = 2,\n  t = \".k-window:not(.k-dialog), .k-dialog-wrapper\";\nexport { o as DATA_DIALOGS_ID, D as DEFAULT_DIALOGS_ZINDEX, t as DIALOGS_SELECTOR, n as ZINDEX_DIALOGS_STEP };", "map": {"version": 3, "names": ["o", "D", "n", "t", "DATA_DIALOGS_ID", "DEFAULT_DIALOGS_ZINDEX", "DIALOGS_SELECTOR", "ZINDEX_DIALOGS_STEP"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dialogs/constants.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst o = \"data-windowid\", D = 10002, n = 2, t = \".k-window:not(.k-dialog), .k-dialog-wrapper\";\nexport {\n  o as DATA_DIALOGS_ID,\n  D as DEFAULT_DIALOGS_ZINDEX,\n  t as DIALOGS_SELECTOR,\n  n as ZINDEX_DIALOGS_STEP\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAG,eAAe;EAAEC,CAAC,GAAG,KAAK;EAAEC,CAAC,GAAG,CAAC;EAAEC,CAAC,GAAG,6CAA6C;AAC9F,SACEH,CAAC,IAAII,eAAe,EACpBH,CAAC,IAAII,sBAAsB,EAC3BF,CAAC,IAAIG,gBAAgB,EACrBJ,CAAC,IAAIK,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}