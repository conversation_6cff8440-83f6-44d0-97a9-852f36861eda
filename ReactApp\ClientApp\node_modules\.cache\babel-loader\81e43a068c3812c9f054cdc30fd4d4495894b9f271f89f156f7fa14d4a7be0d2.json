{"ast": null, "code": "import BaseNode from '../core/base-node';\nimport Node from './node';\nimport DefinitionNode from './definition-node';\nvar RootNode = function (Node) {\n  function RootNode(options) {\n    Node.call(this);\n    this.options = options;\n    this.defs = new DefinitionNode();\n  }\n  if (Node) RootNode.__proto__ = Node;\n  RootNode.prototype = Object.create(Node && Node.prototype);\n  RootNode.prototype.constructor = RootNode;\n  RootNode.prototype.attachTo = function attachTo(domElement) {\n    this.element = domElement;\n    this.defs.attachTo(domElement.firstElementChild);\n  };\n  RootNode.prototype.clear = function clear() {\n    BaseNode.prototype.clear.call(this);\n  };\n  RootNode.prototype.template = function template() {\n    return this.defs.render() + this.renderChildren();\n  };\n  RootNode.prototype.definitionChange = function definitionChange(e) {\n    this.defs.definitionChange(e);\n  };\n  return RootNode;\n}(Node);\nexport default RootNode;", "map": {"version": 3, "names": ["BaseNode", "Node", "DefinitionNode", "RootNode", "options", "call", "defs", "__proto__", "prototype", "Object", "create", "constructor", "attachTo", "dom<PERSON>lement", "element", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "clear", "template", "render", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "definitionChange", "e"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/svg/root-node.js"], "sourcesContent": ["import BaseNode from '../core/base-node';\nimport Node from './node';\nimport DefinitionNode from './definition-node';\n\nvar RootNode = (function (Node) {\n    function RootNode(options) {\n        Node.call(this);\n        this.options = options;\n        this.defs = new DefinitionNode();\n    }\n\n    if ( Node ) RootNode.__proto__ = Node;\n    RootNode.prototype = Object.create( Node && Node.prototype );\n    RootNode.prototype.constructor = RootNode;\n\n    RootNode.prototype.attachTo = function attachTo (domElement) {\n        this.element = domElement;\n        this.defs.attachTo(domElement.firstElementChild);\n    };\n\n    RootNode.prototype.clear = function clear () {\n        BaseNode.prototype.clear.call(this);\n    };\n\n    RootNode.prototype.template = function template () {\n        return this.defs.render() + this.renderChildren();\n    };\n\n    RootNode.prototype.definitionChange = function definitionChange (e) {\n        this.defs.definitionChange(e);\n    };\n\n    return RootNode;\n}(Node));\n\nexport default RootNode;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,mBAAmB;AACxC,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,cAAc,MAAM,mBAAmB;AAE9C,IAAIC,QAAQ,GAAI,UAAUF,IAAI,EAAE;EAC5B,SAASE,QAAQA,CAACC,OAAO,EAAE;IACvBH,IAAI,CAACI,IAAI,CAAC,IAAI,CAAC;IACf,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,IAAI,GAAG,IAAIJ,cAAc,CAAC,CAAC;EACpC;EAEA,IAAKD,IAAI,EAAGE,QAAQ,CAACI,SAAS,GAAGN,IAAI;EACrCE,QAAQ,CAACK,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAET,IAAI,IAAIA,IAAI,CAACO,SAAU,CAAC;EAC5DL,QAAQ,CAACK,SAAS,CAACG,WAAW,GAAGR,QAAQ;EAEzCA,QAAQ,CAACK,SAAS,CAACI,QAAQ,GAAG,SAASA,QAAQA,CAAEC,UAAU,EAAE;IACzD,IAAI,CAACC,OAAO,GAAGD,UAAU;IACzB,IAAI,CAACP,IAAI,CAACM,QAAQ,CAACC,UAAU,CAACE,iBAAiB,CAAC;EACpD,CAAC;EAEDZ,QAAQ,CAACK,SAAS,CAACQ,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAI;IACzChB,QAAQ,CAACQ,SAAS,CAACQ,KAAK,CAACX,IAAI,CAAC,IAAI,CAAC;EACvC,CAAC;EAEDF,QAAQ,CAACK,SAAS,CAACS,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;IAC/C,OAAO,IAAI,CAACX,IAAI,CAACY,MAAM,CAAC,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;EACrD,CAAC;EAEDhB,QAAQ,CAACK,SAAS,CAACY,gBAAgB,GAAG,SAASA,gBAAgBA,CAAEC,CAAC,EAAE;IAChE,IAAI,CAACf,IAAI,CAACc,gBAAgB,CAACC,CAAC,CAAC;EACjC,CAAC;EAED,OAAOlB,QAAQ;AACnB,CAAC,CAACF,IAAI,CAAE;AAER,eAAeE,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}