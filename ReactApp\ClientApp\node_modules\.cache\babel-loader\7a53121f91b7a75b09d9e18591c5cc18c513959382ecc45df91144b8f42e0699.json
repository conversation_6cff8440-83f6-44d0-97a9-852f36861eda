{"ast": null, "code": "import OptionsStore from '../core/options-store';\nimport Rect from '../geometry/rect';\nimport Matrix from '../geometry/matrix';\nimport createTransform from '../geometry/transform';\nimport toMatrix from '../geometry/to-matrix';\nimport HasObservers from '../core/has-observers';\nimport { defined, definitionId, isTransparent, valueOrDefault } from '../util';\nimport { PATTERN } from '../core/constants';\nvar Element = function (HasObservers) {\n  function Element(options) {\n    HasObservers.call(this);\n    this._initOptions(options);\n  }\n  if (HasObservers) Element.__proto__ = HasObservers;\n  Element.prototype = Object.create(HasObservers && HasObservers.prototype);\n  Element.prototype.constructor = Element;\n  var prototypeAccessors = {\n    nodeType: {\n      configurable: true\n    }\n  };\n  prototypeAccessors.nodeType.get = function () {\n    return \"Rect\";\n  };\n  Element.prototype._initOptions = function _initOptions(options) {\n    if (options === void 0) options = {};\n    var clip = options.clip;\n    var transform = options.transform;\n    if (transform) {\n      options.transform = createTransform(transform);\n    }\n    if (clip && !clip.id) {\n      clip.id = definitionId();\n    }\n    this.options = new OptionsStore(options);\n    this.options.addObserver(this);\n  };\n  Element.prototype.transform = function transform(value) {\n    if (defined(value)) {\n      this.options.set(\"transform\", createTransform(value));\n    } else {\n      return this.options.get(\"transform\");\n    }\n  };\n  Element.prototype.parentTransform = function parentTransform() {\n    var element = this;\n    var parentMatrix;\n    while (element.parent) {\n      element = element.parent;\n      var transformation = element.transform();\n      if (transformation) {\n        parentMatrix = transformation.matrix().multiplyCopy(parentMatrix || Matrix.unit());\n      }\n    }\n    if (parentMatrix) {\n      return createTransform(parentMatrix);\n    }\n  };\n  Element.prototype.currentTransform = function currentTransform(parentTransform) {\n    if (parentTransform === void 0) parentTransform = this.parentTransform();\n    var elementTransform = this.transform();\n    var elementMatrix = toMatrix(elementTransform);\n    var parentMatrix = toMatrix(parentTransform);\n    var combinedMatrix;\n    if (elementMatrix && parentMatrix) {\n      combinedMatrix = parentMatrix.multiplyCopy(elementMatrix);\n    } else {\n      combinedMatrix = elementMatrix || parentMatrix;\n    }\n    if (combinedMatrix) {\n      return createTransform(combinedMatrix);\n    }\n  };\n  Element.prototype.visible = function visible(value) {\n    if (defined(value)) {\n      this.options.set(\"visible\", value);\n      return this;\n    }\n    return this.options.get(\"visible\") !== false;\n  };\n  Element.prototype.clip = function clip(value) {\n    var options = this.options;\n    if (defined(value)) {\n      if (value && !value.id) {\n        value.id = definitionId();\n      }\n      options.set(\"clip\", value);\n      return this;\n    }\n    return options.get(\"clip\");\n  };\n  Element.prototype.opacity = function opacity(value) {\n    if (defined(value)) {\n      this.options.set(\"opacity\", value);\n      return this;\n    }\n    return valueOrDefault(this.options.get(\"opacity\"), 1);\n  };\n  Element.prototype.className = function className(value) {\n    if (defined(value)) {\n      this.options.set(\"className\", value);\n      return this;\n    }\n    return this.options.get(\"className\");\n  };\n  Element.prototype.clippedBBox = function clippedBBox(transformation) {\n    var bbox = this._clippedBBox(transformation);\n    if (bbox) {\n      var clip = this.clip();\n      return clip ? Rect.intersect(bbox, clip.bbox(transformation)) : bbox;\n    }\n  };\n  Element.prototype.containsPoint = function containsPoint(point, parentTransform) {\n    if (this.visible()) {\n      var transform = this.currentTransform(parentTransform);\n      var transformedPoint = point;\n      if (transform) {\n        transformedPoint = point.transformCopy(transform.matrix().invert());\n      }\n      return this._hasFill() && this._containsPoint(transformedPoint) || this._isOnPath && this._hasStroke() && this._isOnPath(transformedPoint);\n    }\n    return false;\n  };\n  Element.prototype._hasFill = function _hasFill() {\n    var fill = this.options.fill;\n    return fill && (fill.nodeType === PATTERN || !isTransparent(fill.color));\n  };\n  Element.prototype._hasStroke = function _hasStroke() {\n    var stroke = this.options.stroke;\n    return stroke && stroke.width > 0 && !isTransparent(stroke.color);\n  };\n  Element.prototype._clippedBBox = function _clippedBBox(transformation) {\n    return this.bbox(transformation);\n  };\n  Object.defineProperties(Element.prototype, prototypeAccessors);\n  return Element;\n}(HasObservers);\nexport default Element;", "map": {"version": 3, "names": ["OptionsStore", "Rect", "Matrix", "createTransform", "toMatrix", "HasObservers", "defined", "definitionId", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valueOrDefault", "PATTERN", "Element", "options", "call", "_initOptions", "__proto__", "prototype", "Object", "create", "constructor", "prototypeAccessors", "nodeType", "configurable", "get", "clip", "transform", "id", "addObserver", "value", "set", "parentTransform", "element", "parentMatrix", "parent", "transformation", "matrix", "multiplyCopy", "unit", "currentTransform", "elementTransform", "elementMatrix", "combinedMatrix", "visible", "opacity", "className", "clippedBBox", "bbox", "_clipped<PERSON>ox", "intersect", "containsPoint", "point", "transformedPoint", "transformCopy", "invert", "_hasFill", "_containsPoint", "_isOnPath", "_hasStroke", "fill", "color", "stroke", "width", "defineProperties"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/shapes/element.js"], "sourcesContent": ["import OptionsStore from '../core/options-store';\nimport Rect from '../geometry/rect';\nimport Matrix from '../geometry/matrix';\nimport createTransform from '../geometry/transform';\nimport toMatrix from '../geometry/to-matrix';\nimport HasObservers from '../core/has-observers';\nimport { defined, definitionId, isTransparent, valueOrDefault } from '../util';\nimport { PATTERN } from '../core/constants';\n\nvar Element = (function (HasObservers) {\n    function Element(options) {\n        HasObservers.call(this);\n\n        this._initOptions(options);\n    }\n\n    if ( HasObservers ) Element.__proto__ = HasObservers;\n    Element.prototype = Object.create( HasObservers && HasObservers.prototype );\n    Element.prototype.constructor = Element;\n\n    var prototypeAccessors = { nodeType: { configurable: true } };\n\n    prototypeAccessors.nodeType.get = function () {\n        return \"Rect\";\n    };\n\n    Element.prototype._initOptions = function _initOptions (options) {\n        if ( options === void 0 ) options = {};\n\n        var clip = options.clip;\n        var transform = options.transform;\n\n        if (transform) {\n            options.transform = createTransform(transform);\n        }\n\n        if (clip && !clip.id) {\n            clip.id = definitionId();\n        }\n\n        this.options = new OptionsStore(options);\n        this.options.addObserver(this);\n    };\n\n    Element.prototype.transform = function transform (value) {\n        if (defined(value)) {\n            this.options.set(\"transform\", createTransform(value));\n        } else {\n            return this.options.get(\"transform\");\n        }\n    };\n\n    Element.prototype.parentTransform = function parentTransform () {\n        var element = this;\n        var parentMatrix;\n\n        while (element.parent) {\n            element = element.parent;\n            var transformation = element.transform();\n            if (transformation) {\n                parentMatrix = transformation.matrix().multiplyCopy(parentMatrix || Matrix.unit());\n            }\n        }\n\n        if (parentMatrix) {\n            return createTransform(parentMatrix);\n        }\n    };\n\n    Element.prototype.currentTransform = function currentTransform (parentTransform) {\n        if ( parentTransform === void 0 ) parentTransform = this.parentTransform();\n\n        var elementTransform = this.transform();\n        var elementMatrix = toMatrix(elementTransform);\n\n        var parentMatrix = toMatrix(parentTransform);\n        var combinedMatrix;\n\n        if (elementMatrix && parentMatrix) {\n            combinedMatrix = parentMatrix.multiplyCopy(elementMatrix);\n        } else {\n            combinedMatrix = elementMatrix || parentMatrix;\n        }\n\n        if (combinedMatrix) {\n            return createTransform(combinedMatrix);\n        }\n    };\n\n    Element.prototype.visible = function visible (value) {\n        if (defined(value)) {\n            this.options.set(\"visible\", value);\n            return this;\n        }\n\n        return this.options.get(\"visible\") !== false;\n    };\n\n    Element.prototype.clip = function clip (value) {\n        var options = this.options;\n        if (defined(value)) {\n            if (value && !value.id) {\n                value.id = definitionId();\n            }\n            options.set(\"clip\", value);\n            return this;\n        }\n\n        return options.get(\"clip\");\n    };\n\n    Element.prototype.opacity = function opacity (value) {\n        if (defined(value)) {\n            this.options.set(\"opacity\", value);\n            return this;\n        }\n\n        return valueOrDefault(this.options.get(\"opacity\"), 1);\n    };\n\n    Element.prototype.className = function className (value) {\n        if (defined(value)) {\n            this.options.set(\"className\", value);\n            return this;\n        }\n\n        return this.options.get(\"className\");\n    };\n\n    Element.prototype.clippedBBox = function clippedBBox (transformation) {\n        var bbox = this._clippedBBox(transformation);\n        if (bbox) {\n            var clip = this.clip();\n            return clip ? Rect.intersect(bbox, clip.bbox(transformation)) : bbox;\n        }\n    };\n\n    Element.prototype.containsPoint = function containsPoint (point, parentTransform) {\n        if (this.visible()) {\n            var transform = this.currentTransform(parentTransform);\n            var transformedPoint = point;\n            if (transform) {\n                transformedPoint = point.transformCopy(transform.matrix().invert());\n            }\n            return (this._hasFill() && this._containsPoint(transformedPoint)) || (this._isOnPath && this._hasStroke() && this._isOnPath(transformedPoint));\n        }\n        return false;\n    };\n\n    Element.prototype._hasFill = function _hasFill () {\n        var fill = this.options.fill;\n        return fill && (fill.nodeType === PATTERN || !isTransparent(fill.color));\n    };\n\n    Element.prototype._hasStroke = function _hasStroke () {\n        var stroke = this.options.stroke;\n        return stroke && stroke.width > 0 && !isTransparent(stroke.color);\n    };\n\n    Element.prototype._clippedBBox = function _clippedBBox (transformation) {\n        return this.bbox(transformation);\n    };\n\n    Object.defineProperties( Element.prototype, prototypeAccessors );\n\n    return Element;\n}(HasObservers));\n\nexport default Element;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,uBAAuB;AAChD,OAAOC,IAAI,MAAM,kBAAkB;AACnC,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,eAAe,MAAM,uBAAuB;AACnD,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,YAAY,MAAM,uBAAuB;AAChD,SAASC,OAAO,EAAEC,YAAY,EAAEC,aAAa,EAAEC,cAAc,QAAQ,SAAS;AAC9E,SAASC,OAAO,QAAQ,mBAAmB;AAE3C,IAAIC,OAAO,GAAI,UAAUN,YAAY,EAAE;EACnC,SAASM,OAAOA,CAACC,OAAO,EAAE;IACtBP,YAAY,CAACQ,IAAI,CAAC,IAAI,CAAC;IAEvB,IAAI,CAACC,YAAY,CAACF,OAAO,CAAC;EAC9B;EAEA,IAAKP,YAAY,EAAGM,OAAO,CAACI,SAAS,GAAGV,YAAY;EACpDM,OAAO,CAACK,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAEb,YAAY,IAAIA,YAAY,CAACW,SAAU,CAAC;EAC3EL,OAAO,CAACK,SAAS,CAACG,WAAW,GAAGR,OAAO;EAEvC,IAAIS,kBAAkB,GAAG;IAAEC,QAAQ,EAAE;MAAEC,YAAY,EAAE;IAAK;EAAE,CAAC;EAE7DF,kBAAkB,CAACC,QAAQ,CAACE,GAAG,GAAG,YAAY;IAC1C,OAAO,MAAM;EACjB,CAAC;EAEDZ,OAAO,CAACK,SAAS,CAACF,YAAY,GAAG,SAASA,YAAYA,CAAEF,OAAO,EAAE;IAC7D,IAAKA,OAAO,KAAK,KAAK,CAAC,EAAGA,OAAO,GAAG,CAAC,CAAC;IAEtC,IAAIY,IAAI,GAAGZ,OAAO,CAACY,IAAI;IACvB,IAAIC,SAAS,GAAGb,OAAO,CAACa,SAAS;IAEjC,IAAIA,SAAS,EAAE;MACXb,OAAO,CAACa,SAAS,GAAGtB,eAAe,CAACsB,SAAS,CAAC;IAClD;IAEA,IAAID,IAAI,IAAI,CAACA,IAAI,CAACE,EAAE,EAAE;MAClBF,IAAI,CAACE,EAAE,GAAGnB,YAAY,CAAC,CAAC;IAC5B;IAEA,IAAI,CAACK,OAAO,GAAG,IAAIZ,YAAY,CAACY,OAAO,CAAC;IACxC,IAAI,CAACA,OAAO,CAACe,WAAW,CAAC,IAAI,CAAC;EAClC,CAAC;EAEDhB,OAAO,CAACK,SAAS,CAACS,SAAS,GAAG,SAASA,SAASA,CAAEG,KAAK,EAAE;IACrD,IAAItB,OAAO,CAACsB,KAAK,CAAC,EAAE;MAChB,IAAI,CAAChB,OAAO,CAACiB,GAAG,CAAC,WAAW,EAAE1B,eAAe,CAACyB,KAAK,CAAC,CAAC;IACzD,CAAC,MAAM;MACH,OAAO,IAAI,CAAChB,OAAO,CAACW,GAAG,CAAC,WAAW,CAAC;IACxC;EACJ,CAAC;EAEDZ,OAAO,CAACK,SAAS,CAACc,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAI;IAC5D,IAAIC,OAAO,GAAG,IAAI;IAClB,IAAIC,YAAY;IAEhB,OAAOD,OAAO,CAACE,MAAM,EAAE;MACnBF,OAAO,GAAGA,OAAO,CAACE,MAAM;MACxB,IAAIC,cAAc,GAAGH,OAAO,CAACN,SAAS,CAAC,CAAC;MACxC,IAAIS,cAAc,EAAE;QAChBF,YAAY,GAAGE,cAAc,CAACC,MAAM,CAAC,CAAC,CAACC,YAAY,CAACJ,YAAY,IAAI9B,MAAM,CAACmC,IAAI,CAAC,CAAC,CAAC;MACtF;IACJ;IAEA,IAAIL,YAAY,EAAE;MACd,OAAO7B,eAAe,CAAC6B,YAAY,CAAC;IACxC;EACJ,CAAC;EAEDrB,OAAO,CAACK,SAAS,CAACsB,gBAAgB,GAAG,SAASA,gBAAgBA,CAAER,eAAe,EAAE;IAC7E,IAAKA,eAAe,KAAK,KAAK,CAAC,EAAGA,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC,CAAC;IAE1E,IAAIS,gBAAgB,GAAG,IAAI,CAACd,SAAS,CAAC,CAAC;IACvC,IAAIe,aAAa,GAAGpC,QAAQ,CAACmC,gBAAgB,CAAC;IAE9C,IAAIP,YAAY,GAAG5B,QAAQ,CAAC0B,eAAe,CAAC;IAC5C,IAAIW,cAAc;IAElB,IAAID,aAAa,IAAIR,YAAY,EAAE;MAC/BS,cAAc,GAAGT,YAAY,CAACI,YAAY,CAACI,aAAa,CAAC;IAC7D,CAAC,MAAM;MACHC,cAAc,GAAGD,aAAa,IAAIR,YAAY;IAClD;IAEA,IAAIS,cAAc,EAAE;MAChB,OAAOtC,eAAe,CAACsC,cAAc,CAAC;IAC1C;EACJ,CAAC;EAED9B,OAAO,CAACK,SAAS,CAAC0B,OAAO,GAAG,SAASA,OAAOA,CAAEd,KAAK,EAAE;IACjD,IAAItB,OAAO,CAACsB,KAAK,CAAC,EAAE;MAChB,IAAI,CAAChB,OAAO,CAACiB,GAAG,CAAC,SAAS,EAAED,KAAK,CAAC;MAClC,OAAO,IAAI;IACf;IAEA,OAAO,IAAI,CAAChB,OAAO,CAACW,GAAG,CAAC,SAAS,CAAC,KAAK,KAAK;EAChD,CAAC;EAEDZ,OAAO,CAACK,SAAS,CAACQ,IAAI,GAAG,SAASA,IAAIA,CAAEI,KAAK,EAAE;IAC3C,IAAIhB,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,IAAIN,OAAO,CAACsB,KAAK,CAAC,EAAE;MAChB,IAAIA,KAAK,IAAI,CAACA,KAAK,CAACF,EAAE,EAAE;QACpBE,KAAK,CAACF,EAAE,GAAGnB,YAAY,CAAC,CAAC;MAC7B;MACAK,OAAO,CAACiB,GAAG,CAAC,MAAM,EAAED,KAAK,CAAC;MAC1B,OAAO,IAAI;IACf;IAEA,OAAOhB,OAAO,CAACW,GAAG,CAAC,MAAM,CAAC;EAC9B,CAAC;EAEDZ,OAAO,CAACK,SAAS,CAAC2B,OAAO,GAAG,SAASA,OAAOA,CAAEf,KAAK,EAAE;IACjD,IAAItB,OAAO,CAACsB,KAAK,CAAC,EAAE;MAChB,IAAI,CAAChB,OAAO,CAACiB,GAAG,CAAC,SAAS,EAAED,KAAK,CAAC;MAClC,OAAO,IAAI;IACf;IAEA,OAAOnB,cAAc,CAAC,IAAI,CAACG,OAAO,CAACW,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;EACzD,CAAC;EAEDZ,OAAO,CAACK,SAAS,CAAC4B,SAAS,GAAG,SAASA,SAASA,CAAEhB,KAAK,EAAE;IACrD,IAAItB,OAAO,CAACsB,KAAK,CAAC,EAAE;MAChB,IAAI,CAAChB,OAAO,CAACiB,GAAG,CAAC,WAAW,EAAED,KAAK,CAAC;MACpC,OAAO,IAAI;IACf;IAEA,OAAO,IAAI,CAAChB,OAAO,CAACW,GAAG,CAAC,WAAW,CAAC;EACxC,CAAC;EAEDZ,OAAO,CAACK,SAAS,CAAC6B,WAAW,GAAG,SAASA,WAAWA,CAAEX,cAAc,EAAE;IAClE,IAAIY,IAAI,GAAG,IAAI,CAACC,YAAY,CAACb,cAAc,CAAC;IAC5C,IAAIY,IAAI,EAAE;MACN,IAAItB,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC,CAAC;MACtB,OAAOA,IAAI,GAAGvB,IAAI,CAAC+C,SAAS,CAACF,IAAI,EAAEtB,IAAI,CAACsB,IAAI,CAACZ,cAAc,CAAC,CAAC,GAAGY,IAAI;IACxE;EACJ,CAAC;EAEDnC,OAAO,CAACK,SAAS,CAACiC,aAAa,GAAG,SAASA,aAAaA,CAAEC,KAAK,EAAEpB,eAAe,EAAE;IAC9E,IAAI,IAAI,CAACY,OAAO,CAAC,CAAC,EAAE;MAChB,IAAIjB,SAAS,GAAG,IAAI,CAACa,gBAAgB,CAACR,eAAe,CAAC;MACtD,IAAIqB,gBAAgB,GAAGD,KAAK;MAC5B,IAAIzB,SAAS,EAAE;QACX0B,gBAAgB,GAAGD,KAAK,CAACE,aAAa,CAAC3B,SAAS,CAACU,MAAM,CAAC,CAAC,CAACkB,MAAM,CAAC,CAAC,CAAC;MACvE;MACA,OAAQ,IAAI,CAACC,QAAQ,CAAC,CAAC,IAAI,IAAI,CAACC,cAAc,CAACJ,gBAAgB,CAAC,IAAM,IAAI,CAACK,SAAS,IAAI,IAAI,CAACC,UAAU,CAAC,CAAC,IAAI,IAAI,CAACD,SAAS,CAACL,gBAAgB,CAAE;IAClJ;IACA,OAAO,KAAK;EAChB,CAAC;EAEDxC,OAAO,CAACK,SAAS,CAACsC,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;IAC9C,IAAII,IAAI,GAAG,IAAI,CAAC9C,OAAO,CAAC8C,IAAI;IAC5B,OAAOA,IAAI,KAAKA,IAAI,CAACrC,QAAQ,KAAKX,OAAO,IAAI,CAACF,aAAa,CAACkD,IAAI,CAACC,KAAK,CAAC,CAAC;EAC5E,CAAC;EAEDhD,OAAO,CAACK,SAAS,CAACyC,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAI;IAClD,IAAIG,MAAM,GAAG,IAAI,CAAChD,OAAO,CAACgD,MAAM;IAChC,OAAOA,MAAM,IAAIA,MAAM,CAACC,KAAK,GAAG,CAAC,IAAI,CAACrD,aAAa,CAACoD,MAAM,CAACD,KAAK,CAAC;EACrE,CAAC;EAEDhD,OAAO,CAACK,SAAS,CAAC+B,YAAY,GAAG,SAASA,YAAYA,CAAEb,cAAc,EAAE;IACpE,OAAO,IAAI,CAACY,IAAI,CAACZ,cAAc,CAAC;EACpC,CAAC;EAEDjB,MAAM,CAAC6C,gBAAgB,CAAEnD,OAAO,CAACK,SAAS,EAAEI,kBAAmB,CAAC;EAEhE,OAAOT,OAAO;AAClB,CAAC,CAACN,YAAY,CAAE;AAEhB,eAAeM,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}