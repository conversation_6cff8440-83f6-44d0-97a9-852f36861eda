{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { isEqual as d } from \"@progress/kendo-date-math\";\nimport { MIDNIGHT_DATE as p } from \"../../utils.mjs\";\nimport { setMinutes as r, range as x } from \"../utils.mjs\";\nconst m = 60,\n  l = n => t => t % n,\n  M = l(m),\n  I = (n, t) => i => M(n + i * t),\n  u = (n, t) => M(m + n - t),\n  c = n => (t, i) => !i || t.getHours() === i.getHours() ? t : r(t, n),\n  L = c(0),\n  f = c(m - 1);\nclass S {\n  constructor(t) {\n    this.intl = t, this.toListItem = null, this.min = null, this.max = null, this.step = 0, this.insertUndividedMax = !1;\n  }\n  apply(t, i) {\n    return r(t, i.getMinutes());\n  }\n  configure(t) {\n    const {\n      insertUndividedMax: i = this.insertUndividedMax,\n      min: s = this.min,\n      max: h = this.max,\n      part: e,\n      step: o = this.step\n    } = t;\n    this.insertUndividedMax = i, this.toListItem = g => {\n      const a = r(p, g);\n      return {\n        text: this.intl.formatDate(a, e.pattern),\n        value: a\n      };\n    }, this.min = s, this.max = h, this.step = o;\n  }\n  data(t) {\n    const [i] = this.range(t),\n      s = I(i, this.step),\n      h = o => this.toListItem && this.toListItem(s(o)),\n      e = x(0, this.countFromMin(t)).map(h);\n    return this.addLast(e), t && this.addMissing(e, t), e;\n  }\n  isRangeChanged(t, i) {\n    return this.min !== null && this.max !== null && (!d(this.min, t) || !d(this.max, i));\n  }\n  limitRange(t, i, s) {\n    return [L(t, s), f(i, s)];\n  }\n  total(t) {\n    const i = this.insertUndividedMax && this.isLastMissing(t) ? 1 : 0,\n      s = this.isMissing(t) ? 1 : 0;\n    return this.countFromMin(t) + s + i;\n  }\n  selectedIndex(t) {\n    return Math.ceil(this.divideByStep(t));\n  }\n  valueInList(t) {\n    return t ? this.insertUndividedMax && this.lastMinute(t) === t.getMinutes() || !this.isMissing(t) : !0;\n  }\n  addLast(t, i) {\n    return this.insertUndividedMax && this.isLastMissing(i) && this.toListItem && t.push(this.toListItem(this.lastMinute(i))), t;\n  }\n  addMissing(t, i) {\n    if (this.valueInList(i)) return t;\n    if (this.toListItem) {\n      const s = this.toListItem(i.getMinutes());\n      t.splice(this.selectedIndex(i), 0, s);\n    }\n    return t;\n  }\n  countFromMin(t) {\n    const [i, s] = this.range(t);\n    return Math.floor(u(s, i) / this.step) + 1;\n  }\n  isMissing(t) {\n    return t ? this.selectedIndex(t) !== this.divideByStep(t) : !1;\n  }\n  isLastMissing(t) {\n    return this.max !== null && this.isMissing(r(this.max, this.lastMinute(t)));\n  }\n  divideByStep(t) {\n    return u(t.getMinutes(), this.min.getMinutes()) / this.step;\n  }\n  lastMinute(t) {\n    return this.range(t)[1];\n  }\n  range(t) {\n    const [i, s] = this.limitRange(this.min, this.max, t);\n    return [i.getMinutes(), s.getMinutes()];\n  }\n}\nexport { S as MinutesService };", "map": {"version": 3, "names": ["isEqual", "d", "MIDNIGHT_DATE", "p", "setMinutes", "r", "range", "x", "m", "l", "n", "t", "M", "I", "i", "u", "c", "getHours", "L", "f", "S", "constructor", "intl", "toListItem", "min", "max", "step", "insertUndividedMax", "apply", "getMinutes", "configure", "s", "h", "part", "e", "o", "g", "a", "text", "formatDate", "pattern", "value", "data", "countFromMin", "map", "addLast", "addMissing", "isRangeChanged", "limitRange", "total", "isLastMissing", "isMissing", "selectedIndex", "Math", "ceil", "divideByStep", "valueInList", "lastMinute", "push", "splice", "floor", "MinutesService"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dateinputs/timepicker/services/MinutesService.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { isEqual as d } from \"@progress/kendo-date-math\";\nimport { MIDNIGHT_DATE as p } from \"../../utils.mjs\";\nimport { setMinutes as r, range as x } from \"../utils.mjs\";\nconst m = 60, l = (n) => (t) => t % n, M = l(m), I = (n, t) => (i) => M(n + i * t), u = (n, t) => M(m + n - t), c = (n) => (t, i) => !i || t.getHours() === i.getHours() ? t : r(t, n), L = c(0), f = c(m - 1);\nclass S {\n  constructor(t) {\n    this.intl = t, this.toListItem = null, this.min = null, this.max = null, this.step = 0, this.insertUndividedMax = !1;\n  }\n  apply(t, i) {\n    return r(t, i.getMinutes());\n  }\n  configure(t) {\n    const {\n      insertUndividedMax: i = this.insertUndividedMax,\n      min: s = this.min,\n      max: h = this.max,\n      part: e,\n      step: o = this.step\n    } = t;\n    this.insertUndividedMax = i, this.toListItem = (g) => {\n      const a = r(p, g);\n      return {\n        text: this.intl.formatDate(a, e.pattern),\n        value: a\n      };\n    }, this.min = s, this.max = h, this.step = o;\n  }\n  data(t) {\n    const [i] = this.range(t), s = I(i, this.step), h = (o) => this.toListItem && this.toListItem(s(o)), e = x(0, this.countFromMin(t)).map(h);\n    return this.addLast(e), t && this.addMissing(e, t), e;\n  }\n  isRangeChanged(t, i) {\n    return this.min !== null && this.max !== null && (!d(this.min, t) || !d(this.max, i));\n  }\n  limitRange(t, i, s) {\n    return [L(t, s), f(i, s)];\n  }\n  total(t) {\n    const i = this.insertUndividedMax && this.isLastMissing(t) ? 1 : 0, s = this.isMissing(t) ? 1 : 0;\n    return this.countFromMin(t) + s + i;\n  }\n  selectedIndex(t) {\n    return Math.ceil(this.divideByStep(t));\n  }\n  valueInList(t) {\n    return t ? this.insertUndividedMax && this.lastMinute(t) === t.getMinutes() || !this.isMissing(t) : !0;\n  }\n  addLast(t, i) {\n    return this.insertUndividedMax && this.isLastMissing(i) && this.toListItem && t.push(this.toListItem(this.lastMinute(i))), t;\n  }\n  addMissing(t, i) {\n    if (this.valueInList(i))\n      return t;\n    if (this.toListItem) {\n      const s = this.toListItem(i.getMinutes());\n      t.splice(this.selectedIndex(i), 0, s);\n    }\n    return t;\n  }\n  countFromMin(t) {\n    const [i, s] = this.range(t);\n    return Math.floor(u(s, i) / this.step) + 1;\n  }\n  isMissing(t) {\n    return t ? this.selectedIndex(t) !== this.divideByStep(t) : !1;\n  }\n  isLastMissing(t) {\n    return this.max !== null && this.isMissing(r(this.max, this.lastMinute(t)));\n  }\n  divideByStep(t) {\n    return u(t.getMinutes(), this.min.getMinutes()) / this.step;\n  }\n  lastMinute(t) {\n    return this.range(t)[1];\n  }\n  range(t) {\n    const [i, s] = this.limitRange(this.min, this.max, t);\n    return [i.getMinutes(), s.getMinutes()];\n  }\n}\nexport {\n  S as MinutesService\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,OAAO,IAAIC,CAAC,QAAQ,2BAA2B;AACxD,SAASC,aAAa,IAAIC,CAAC,QAAQ,iBAAiB;AACpD,SAASC,UAAU,IAAIC,CAAC,EAAEC,KAAK,IAAIC,CAAC,QAAQ,cAAc;AAC1D,MAAMC,CAAC,GAAG,EAAE;EAAEC,CAAC,GAAIC,CAAC,IAAMC,CAAC,IAAKA,CAAC,GAAGD,CAAC;EAAEE,CAAC,GAAGH,CAAC,CAACD,CAAC,CAAC;EAAEK,CAAC,GAAGA,CAACH,CAAC,EAAEC,CAAC,KAAMG,CAAC,IAAKF,CAAC,CAACF,CAAC,GAAGI,CAAC,GAAGH,CAAC,CAAC;EAAEI,CAAC,GAAGA,CAACL,CAAC,EAAEC,CAAC,KAAKC,CAAC,CAACJ,CAAC,GAAGE,CAAC,GAAGC,CAAC,CAAC;EAAEK,CAAC,GAAIN,CAAC,IAAK,CAACC,CAAC,EAAEG,CAAC,KAAK,CAACA,CAAC,IAAIH,CAAC,CAACM,QAAQ,CAAC,CAAC,KAAKH,CAAC,CAACG,QAAQ,CAAC,CAAC,GAAGN,CAAC,GAAGN,CAAC,CAACM,CAAC,EAAED,CAAC,CAAC;EAAEQ,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC;EAAEG,CAAC,GAAGH,CAAC,CAACR,CAAC,GAAG,CAAC,CAAC;AAC9M,MAAMY,CAAC,CAAC;EACNC,WAAWA,CAACV,CAAC,EAAE;IACb,IAAI,CAACW,IAAI,GAAGX,CAAC,EAAE,IAAI,CAACY,UAAU,GAAG,IAAI,EAAE,IAAI,CAACC,GAAG,GAAG,IAAI,EAAE,IAAI,CAACC,GAAG,GAAG,IAAI,EAAE,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE,IAAI,CAACC,kBAAkB,GAAG,CAAC,CAAC;EACtH;EACAC,KAAKA,CAACjB,CAAC,EAAEG,CAAC,EAAE;IACV,OAAOT,CAAC,CAACM,CAAC,EAAEG,CAAC,CAACe,UAAU,CAAC,CAAC,CAAC;EAC7B;EACAC,SAASA,CAACnB,CAAC,EAAE;IACX,MAAM;MACJgB,kBAAkB,EAAEb,CAAC,GAAG,IAAI,CAACa,kBAAkB;MAC/CH,GAAG,EAAEO,CAAC,GAAG,IAAI,CAACP,GAAG;MACjBC,GAAG,EAAEO,CAAC,GAAG,IAAI,CAACP,GAAG;MACjBQ,IAAI,EAAEC,CAAC;MACPR,IAAI,EAAES,CAAC,GAAG,IAAI,CAACT;IACjB,CAAC,GAAGf,CAAC;IACL,IAAI,CAACgB,kBAAkB,GAAGb,CAAC,EAAE,IAAI,CAACS,UAAU,GAAIa,CAAC,IAAK;MACpD,MAAMC,CAAC,GAAGhC,CAAC,CAACF,CAAC,EAAEiC,CAAC,CAAC;MACjB,OAAO;QACLE,IAAI,EAAE,IAAI,CAAChB,IAAI,CAACiB,UAAU,CAACF,CAAC,EAAEH,CAAC,CAACM,OAAO,CAAC;QACxCC,KAAK,EAAEJ;MACT,CAAC;IACH,CAAC,EAAE,IAAI,CAACb,GAAG,GAAGO,CAAC,EAAE,IAAI,CAACN,GAAG,GAAGO,CAAC,EAAE,IAAI,CAACN,IAAI,GAAGS,CAAC;EAC9C;EACAO,IAAIA,CAAC/B,CAAC,EAAE;IACN,MAAM,CAACG,CAAC,CAAC,GAAG,IAAI,CAACR,KAAK,CAACK,CAAC,CAAC;MAAEoB,CAAC,GAAGlB,CAAC,CAACC,CAAC,EAAE,IAAI,CAACY,IAAI,CAAC;MAAEM,CAAC,GAAIG,CAAC,IAAK,IAAI,CAACZ,UAAU,IAAI,IAAI,CAACA,UAAU,CAACQ,CAAC,CAACI,CAAC,CAAC,CAAC;MAAED,CAAC,GAAG3B,CAAC,CAAC,CAAC,EAAE,IAAI,CAACoC,YAAY,CAAChC,CAAC,CAAC,CAAC,CAACiC,GAAG,CAACZ,CAAC,CAAC;IAC1I,OAAO,IAAI,CAACa,OAAO,CAACX,CAAC,CAAC,EAAEvB,CAAC,IAAI,IAAI,CAACmC,UAAU,CAACZ,CAAC,EAAEvB,CAAC,CAAC,EAAEuB,CAAC;EACvD;EACAa,cAAcA,CAACpC,CAAC,EAAEG,CAAC,EAAE;IACnB,OAAO,IAAI,CAACU,GAAG,KAAK,IAAI,IAAI,IAAI,CAACC,GAAG,KAAK,IAAI,KAAK,CAACxB,CAAC,CAAC,IAAI,CAACuB,GAAG,EAAEb,CAAC,CAAC,IAAI,CAACV,CAAC,CAAC,IAAI,CAACwB,GAAG,EAAEX,CAAC,CAAC,CAAC;EACvF;EACAkC,UAAUA,CAACrC,CAAC,EAAEG,CAAC,EAAEiB,CAAC,EAAE;IAClB,OAAO,CAACb,CAAC,CAACP,CAAC,EAAEoB,CAAC,CAAC,EAAEZ,CAAC,CAACL,CAAC,EAAEiB,CAAC,CAAC,CAAC;EAC3B;EACAkB,KAAKA,CAACtC,CAAC,EAAE;IACP,MAAMG,CAAC,GAAG,IAAI,CAACa,kBAAkB,IAAI,IAAI,CAACuB,aAAa,CAACvC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;MAAEoB,CAAC,GAAG,IAAI,CAACoB,SAAS,CAACxC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IACjG,OAAO,IAAI,CAACgC,YAAY,CAAChC,CAAC,CAAC,GAAGoB,CAAC,GAAGjB,CAAC;EACrC;EACAsC,aAAaA,CAACzC,CAAC,EAAE;IACf,OAAO0C,IAAI,CAACC,IAAI,CAAC,IAAI,CAACC,YAAY,CAAC5C,CAAC,CAAC,CAAC;EACxC;EACA6C,WAAWA,CAAC7C,CAAC,EAAE;IACb,OAAOA,CAAC,GAAG,IAAI,CAACgB,kBAAkB,IAAI,IAAI,CAAC8B,UAAU,CAAC9C,CAAC,CAAC,KAAKA,CAAC,CAACkB,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAACsB,SAAS,CAACxC,CAAC,CAAC,GAAG,CAAC,CAAC;EACxG;EACAkC,OAAOA,CAAClC,CAAC,EAAEG,CAAC,EAAE;IACZ,OAAO,IAAI,CAACa,kBAAkB,IAAI,IAAI,CAACuB,aAAa,CAACpC,CAAC,CAAC,IAAI,IAAI,CAACS,UAAU,IAAIZ,CAAC,CAAC+C,IAAI,CAAC,IAAI,CAACnC,UAAU,CAAC,IAAI,CAACkC,UAAU,CAAC3C,CAAC,CAAC,CAAC,CAAC,EAAEH,CAAC;EAC9H;EACAmC,UAAUA,CAACnC,CAAC,EAAEG,CAAC,EAAE;IACf,IAAI,IAAI,CAAC0C,WAAW,CAAC1C,CAAC,CAAC,EACrB,OAAOH,CAAC;IACV,IAAI,IAAI,CAACY,UAAU,EAAE;MACnB,MAAMQ,CAAC,GAAG,IAAI,CAACR,UAAU,CAACT,CAAC,CAACe,UAAU,CAAC,CAAC,CAAC;MACzClB,CAAC,CAACgD,MAAM,CAAC,IAAI,CAACP,aAAa,CAACtC,CAAC,CAAC,EAAE,CAAC,EAAEiB,CAAC,CAAC;IACvC;IACA,OAAOpB,CAAC;EACV;EACAgC,YAAYA,CAAChC,CAAC,EAAE;IACd,MAAM,CAACG,CAAC,EAAEiB,CAAC,CAAC,GAAG,IAAI,CAACzB,KAAK,CAACK,CAAC,CAAC;IAC5B,OAAO0C,IAAI,CAACO,KAAK,CAAC7C,CAAC,CAACgB,CAAC,EAAEjB,CAAC,CAAC,GAAG,IAAI,CAACY,IAAI,CAAC,GAAG,CAAC;EAC5C;EACAyB,SAASA,CAACxC,CAAC,EAAE;IACX,OAAOA,CAAC,GAAG,IAAI,CAACyC,aAAa,CAACzC,CAAC,CAAC,KAAK,IAAI,CAAC4C,YAAY,CAAC5C,CAAC,CAAC,GAAG,CAAC,CAAC;EAChE;EACAuC,aAAaA,CAACvC,CAAC,EAAE;IACf,OAAO,IAAI,CAACc,GAAG,KAAK,IAAI,IAAI,IAAI,CAAC0B,SAAS,CAAC9C,CAAC,CAAC,IAAI,CAACoB,GAAG,EAAE,IAAI,CAACgC,UAAU,CAAC9C,CAAC,CAAC,CAAC,CAAC;EAC7E;EACA4C,YAAYA,CAAC5C,CAAC,EAAE;IACd,OAAOI,CAAC,CAACJ,CAAC,CAACkB,UAAU,CAAC,CAAC,EAAE,IAAI,CAACL,GAAG,CAACK,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAACH,IAAI;EAC7D;EACA+B,UAAUA,CAAC9C,CAAC,EAAE;IACZ,OAAO,IAAI,CAACL,KAAK,CAACK,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB;EACAL,KAAKA,CAACK,CAAC,EAAE;IACP,MAAM,CAACG,CAAC,EAAEiB,CAAC,CAAC,GAAG,IAAI,CAACiB,UAAU,CAAC,IAAI,CAACxB,GAAG,EAAE,IAAI,CAACC,GAAG,EAAEd,CAAC,CAAC;IACrD,OAAO,CAACG,CAAC,CAACe,UAAU,CAAC,CAAC,EAAEE,CAAC,CAACF,UAAU,CAAC,CAAC,CAAC;EACzC;AACF;AACA,SACET,CAAC,IAAIyC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}