{"ast": null, "code": "import PathNode from './path-node';\nvar RectNode = function (PathNode) {\n  function RectNode() {\n    PathNode.apply(this, arguments);\n  }\n  if (PathNode) RectNode.__proto__ = PathNode;\n  RectNode.prototype = Object.create(PathNode && PathNode.prototype);\n  RectNode.prototype.constructor = RectNode;\n  RectNode.prototype.geometryChange = function geometryChange() {\n    var geometry = this.srcElement.geometry();\n    this.attr(\"x\", geometry.origin.x);\n    this.attr(\"y\", geometry.origin.y);\n    this.attr(\"width\", geometry.size.width);\n    this.attr(\"height\", geometry.size.height);\n    this.attr(\"rx\", geometry.cornerRadius[0]);\n    this.attr(\"ry\", geometry.cornerRadius[1]);\n    this.invalidate();\n  };\n  RectNode.prototype.size = function size() {\n    return this.srcElement.geometry().size;\n  };\n  RectNode.prototype.origin = function origin() {\n    return this.srcElement.geometry().origin;\n  };\n  RectNode.prototype.rx = function rx() {\n    return this.srcElement.geometry().cornerRadius[0];\n  };\n  RectNode.prototype.ry = function ry() {\n    return this.srcElement.geometry().cornerRadius[1];\n  };\n  RectNode.prototype.template = function template() {\n    return \"<rect \" + this.renderId() + \" \" + this.renderStyle() + \" \" + this.renderOpacity() + \" x='\" + this.origin().x + \"' y='\" + this.origin().y + \"' \" + \"rx='\" + this.rx() + \"' ry='\" + this.ry() + \"' \" + \"width='\" + this.size().width + \"' height='\" + this.size().height + \"' \" + this.renderStroke() + \" \" + this.renderFill() + \" \" + this.renderDefinitions() + \" \" + this.renderTransform() + this.renderClassName() + \" \" + this.renderRole() + this.renderAriaLabel() + \" \" + this.renderAriaRoleDescription() + this.renderAriaChecked() + \" />\";\n  };\n  return RectNode;\n}(PathNode);\nexport default RectNode;", "map": {"version": 3, "names": ["PathNode", "RectNode", "apply", "arguments", "__proto__", "prototype", "Object", "create", "constructor", "geometryChange", "geometry", "srcElement", "attr", "origin", "x", "y", "size", "width", "height", "cornerRadius", "invalidate", "rx", "ry", "template", "renderId", "renderStyle", "renderOpacity", "renderStroke", "renderFill", "renderDefinitions", "renderTransform", "renderClassName", "renderRole", "renderAriaLabel", "renderAriaRoleDescription", "renderAriaChecked"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/svg/rect-node.js"], "sourcesContent": ["import PathNode from './path-node';\n\nvar RectNode = (function (PathNode) {\n    function RectNode () {\n        PathNode.apply(this, arguments);\n    }\n\n    if ( PathNode ) RectNode.__proto__ = PathNode;\n    RectNode.prototype = Object.create( PathNode && PathNode.prototype );\n    RectNode.prototype.constructor = RectNode;\n\n    RectNode.prototype.geometryChange = function geometryChange () {\n        var geometry = this.srcElement.geometry();\n        this.attr(\"x\", geometry.origin.x);\n        this.attr(\"y\", geometry.origin.y);\n        this.attr(\"width\", geometry.size.width);\n        this.attr(\"height\", geometry.size.height);\n        this.attr(\"rx\", geometry.cornerRadius[0]);\n        this.attr(\"ry\", geometry.cornerRadius[1]);\n        this.invalidate();\n    };\n\n    RectNode.prototype.size = function size () {\n        return this.srcElement.geometry().size;\n    };\n\n    RectNode.prototype.origin = function origin () {\n        return this.srcElement.geometry().origin;\n    };\n\n    RectNode.prototype.rx = function rx () {\n        return this.srcElement.geometry().cornerRadius[0];\n    };\n\n    RectNode.prototype.ry = function ry () {\n        return this.srcElement.geometry().cornerRadius[1];\n    };\n\n    RectNode.prototype.template = function template () {\n        return \"<rect \" + (this.renderId()) + \" \" + (this.renderStyle()) + \" \" + (this.renderOpacity()) + \" x='\" + (this.origin().x) + \"' y='\" + (this.origin().y) + \"' \" +\n                    \"rx='\" + (this.rx()) + \"' ry='\" + (this.ry()) + \"' \" +\n                    \"width='\" + (this.size().width) + \"' height='\" + (this.size().height) + \"' \" + (this.renderStroke()) + \" \" +\n                    (this.renderFill()) + \" \" + (this.renderDefinitions()) + \" \" + (this.renderTransform()) +\n                    (this.renderClassName()) + \" \" + (this.renderRole()) +\n                    (this.renderAriaLabel()) + \" \" + (this.renderAriaRoleDescription()) +\n                    (this.renderAriaChecked()) + \" />\";\n    };\n\n    return RectNode;\n}(PathNode));\n\nexport default RectNode;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,aAAa;AAElC,IAAIC,QAAQ,GAAI,UAAUD,QAAQ,EAAE;EAChC,SAASC,QAAQA,CAAA,EAAI;IACjBD,QAAQ,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACnC;EAEA,IAAKH,QAAQ,EAAGC,QAAQ,CAACG,SAAS,GAAGJ,QAAQ;EAC7CC,QAAQ,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAEP,QAAQ,IAAIA,QAAQ,CAACK,SAAU,CAAC;EACpEJ,QAAQ,CAACI,SAAS,CAACG,WAAW,GAAGP,QAAQ;EAEzCA,QAAQ,CAACI,SAAS,CAACI,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAI;IAC3D,IAAIC,QAAQ,GAAG,IAAI,CAACC,UAAU,CAACD,QAAQ,CAAC,CAAC;IACzC,IAAI,CAACE,IAAI,CAAC,GAAG,EAAEF,QAAQ,CAACG,MAAM,CAACC,CAAC,CAAC;IACjC,IAAI,CAACF,IAAI,CAAC,GAAG,EAAEF,QAAQ,CAACG,MAAM,CAACE,CAAC,CAAC;IACjC,IAAI,CAACH,IAAI,CAAC,OAAO,EAAEF,QAAQ,CAACM,IAAI,CAACC,KAAK,CAAC;IACvC,IAAI,CAACL,IAAI,CAAC,QAAQ,EAAEF,QAAQ,CAACM,IAAI,CAACE,MAAM,CAAC;IACzC,IAAI,CAACN,IAAI,CAAC,IAAI,EAAEF,QAAQ,CAACS,YAAY,CAAC,CAAC,CAAC,CAAC;IACzC,IAAI,CAACP,IAAI,CAAC,IAAI,EAAEF,QAAQ,CAACS,YAAY,CAAC,CAAC,CAAC,CAAC;IACzC,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB,CAAC;EAEDnB,QAAQ,CAACI,SAAS,CAACW,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAI;IACvC,OAAO,IAAI,CAACL,UAAU,CAACD,QAAQ,CAAC,CAAC,CAACM,IAAI;EAC1C,CAAC;EAEDf,QAAQ,CAACI,SAAS,CAACQ,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAI;IAC3C,OAAO,IAAI,CAACF,UAAU,CAACD,QAAQ,CAAC,CAAC,CAACG,MAAM;EAC5C,CAAC;EAEDZ,QAAQ,CAACI,SAAS,CAACgB,EAAE,GAAG,SAASA,EAAEA,CAAA,EAAI;IACnC,OAAO,IAAI,CAACV,UAAU,CAACD,QAAQ,CAAC,CAAC,CAACS,YAAY,CAAC,CAAC,CAAC;EACrD,CAAC;EAEDlB,QAAQ,CAACI,SAAS,CAACiB,EAAE,GAAG,SAASA,EAAEA,CAAA,EAAI;IACnC,OAAO,IAAI,CAACX,UAAU,CAACD,QAAQ,CAAC,CAAC,CAACS,YAAY,CAAC,CAAC,CAAC;EACrD,CAAC;EAEDlB,QAAQ,CAACI,SAAS,CAACkB,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;IAC/C,OAAO,QAAQ,GAAI,IAAI,CAACC,QAAQ,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAACC,WAAW,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAACC,aAAa,CAAC,CAAE,GAAG,MAAM,GAAI,IAAI,CAACb,MAAM,CAAC,CAAC,CAACC,CAAE,GAAG,OAAO,GAAI,IAAI,CAACD,MAAM,CAAC,CAAC,CAACE,CAAE,GAAG,IAAI,GACrJ,MAAM,GAAI,IAAI,CAACM,EAAE,CAAC,CAAE,GAAG,QAAQ,GAAI,IAAI,CAACC,EAAE,CAAC,CAAE,GAAG,IAAI,GACpD,SAAS,GAAI,IAAI,CAACN,IAAI,CAAC,CAAC,CAACC,KAAM,GAAG,YAAY,GAAI,IAAI,CAACD,IAAI,CAAC,CAAC,CAACE,MAAO,GAAG,IAAI,GAAI,IAAI,CAACS,YAAY,CAAC,CAAE,GAAG,GAAG,GACzG,IAAI,CAACC,UAAU,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAACC,iBAAiB,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAACC,eAAe,CAAC,CAAE,GACtF,IAAI,CAACC,eAAe,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAACC,UAAU,CAAC,CAAE,GACnD,IAAI,CAACC,eAAe,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAACC,yBAAyB,CAAC,CAAE,GAClE,IAAI,CAACC,iBAAiB,CAAC,CAAE,GAAG,KAAK;EAClD,CAAC;EAED,OAAOlC,QAAQ;AACnB,CAAC,CAACD,QAAQ,CAAE;AAEZ,eAAeC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}