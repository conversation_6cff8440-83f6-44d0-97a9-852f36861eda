{"ast": null, "code": "import GroupNode from './group-node';\nimport traversable from '../mixins/traversable';\nimport { animationFrame, throttle } from '../common';\nvar FRAME_DELAY = 1000 / 60;\nvar RootNode = function (superclass) {\n  function RootNode(canvas, size) {\n    superclass.call(this);\n    this.canvas = canvas;\n    this.size = size;\n    this.ctx = canvas.getContext(\"2d\");\n    var invalidateHandler = this._invalidate.bind(this);\n    this.invalidate = throttle(function () {\n      animationFrame(invalidateHandler);\n    }, FRAME_DELAY);\n  }\n  if (superclass) RootNode.__proto__ = superclass;\n  RootNode.prototype = Object.create(superclass && superclass.prototype);\n  RootNode.prototype.constructor = RootNode;\n  RootNode.prototype.destroy = function destroy() {\n    superclass.prototype.destroy.call(this);\n    this.canvas = null;\n    this.ctx = null;\n  };\n  RootNode.prototype.load = function load(elements, pos, cors) {\n    this.loadElements(elements, pos, cors);\n    this._invalidate();\n  };\n  RootNode.prototype._rescale = function _rescale(scale) {\n    var ref = this;\n    var canvas = ref.canvas;\n    var size = ref.size;\n    canvas.width = size.width * scale;\n    canvas.height = size.height * scale;\n    this.ctx.scale(scale, scale);\n  };\n  RootNode.prototype._devicePixelRatio = function _devicePixelRatio() {\n    if (typeof window.devicePixelRatio === 'number') {\n      return window.devicePixelRatio;\n    }\n    return 1;\n  };\n  RootNode.prototype._invalidate = function _invalidate(options) {\n    if (!this.ctx) {\n      return;\n    }\n    var fixedScale = options && options.fixedScale;\n    var scale = fixedScale ? 1 : this._devicePixelRatio();\n    this._rescale(scale);\n    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\n    this.renderTo(this.ctx);\n  };\n  return RootNode;\n}(traversable(GroupNode, \"childNodes\"));\nexport default RootNode;", "map": {"version": 3, "names": ["GroupNode", "traversable", "animationFrame", "throttle", "FRAME_DELAY", "RootNode", "superclass", "canvas", "size", "call", "ctx", "getContext", "invalidate<PERSON><PERSON><PERSON>", "_invalidate", "bind", "invalidate", "__proto__", "prototype", "Object", "create", "constructor", "destroy", "load", "elements", "pos", "cors", "loadElements", "_rescale", "scale", "ref", "width", "height", "_devicePixelRatio", "window", "devicePixelRatio", "options", "fixedScale", "clearRect", "renderTo"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/canvas/root-node.js"], "sourcesContent": ["import GroupNode from './group-node';\nimport traversable from '../mixins/traversable';\nimport { animationFrame, throttle } from '../common';\n\n\nvar FRAME_DELAY = 1000 / 60;\n\nvar RootNode = (function (superclass) {\n    function RootNode(canvas, size) {\n        superclass.call(this);\n\n        this.canvas = canvas;\n        this.size = size;\n        this.ctx = canvas.getContext(\"2d\");\n\n        var invalidateHandler = this._invalidate.bind(this);\n        this.invalidate = throttle(function () {\n            animationFrame(invalidateHandler);\n        }, FRAME_DELAY);\n    }\n\n    if ( superclass ) RootNode.__proto__ = superclass;\n    RootNode.prototype = Object.create( superclass && superclass.prototype );\n    RootNode.prototype.constructor = RootNode;\n\n    RootNode.prototype.destroy = function destroy () {\n        superclass.prototype.destroy.call(this);\n        this.canvas = null;\n        this.ctx = null;\n    };\n\n    RootNode.prototype.load = function load (elements, pos, cors) {\n        this.loadElements(elements, pos, cors);\n        this._invalidate();\n    };\n\n    RootNode.prototype._rescale = function _rescale (scale) {\n        var ref = this;\n        var canvas = ref.canvas;\n        var size = ref.size;\n        canvas.width = size.width * scale;\n        canvas.height = size.height * scale;\n        this.ctx.scale(scale, scale);\n    };\n\n    RootNode.prototype._devicePixelRatio = function _devicePixelRatio () {\n        if (typeof window.devicePixelRatio === 'number') {\n            return window.devicePixelRatio;\n        }\n\n        return 1;\n    };\n\n    RootNode.prototype._invalidate = function _invalidate (options) {\n        if (!this.ctx) {\n            return;\n        }\n\n        var fixedScale = options && options.fixedScale;\n        var scale = fixedScale ? 1 : this._devicePixelRatio();\n        this._rescale(scale);\n\n        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\n        this.renderTo(this.ctx);\n    };\n\n    return RootNode;\n}(traversable(GroupNode, \"childNodes\")));\n\nexport default RootNode;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,cAAc;AACpC,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,SAASC,cAAc,EAAEC,QAAQ,QAAQ,WAAW;AAGpD,IAAIC,WAAW,GAAG,IAAI,GAAG,EAAE;AAE3B,IAAIC,QAAQ,GAAI,UAAUC,UAAU,EAAE;EAClC,SAASD,QAAQA,CAACE,MAAM,EAAEC,IAAI,EAAE;IAC5BF,UAAU,CAACG,IAAI,CAAC,IAAI,CAAC;IAErB,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACE,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;IAElC,IAAIC,iBAAiB,GAAG,IAAI,CAACC,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC;IACnD,IAAI,CAACC,UAAU,GAAGZ,QAAQ,CAAC,YAAY;MACnCD,cAAc,CAACU,iBAAiB,CAAC;IACrC,CAAC,EAAER,WAAW,CAAC;EACnB;EAEA,IAAKE,UAAU,EAAGD,QAAQ,CAACW,SAAS,GAAGV,UAAU;EACjDD,QAAQ,CAACY,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAEb,UAAU,IAAIA,UAAU,CAACW,SAAU,CAAC;EACxEZ,QAAQ,CAACY,SAAS,CAACG,WAAW,GAAGf,QAAQ;EAEzCA,QAAQ,CAACY,SAAS,CAACI,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;IAC7Cf,UAAU,CAACW,SAAS,CAACI,OAAO,CAACZ,IAAI,CAAC,IAAI,CAAC;IACvC,IAAI,CAACF,MAAM,GAAG,IAAI;IAClB,IAAI,CAACG,GAAG,GAAG,IAAI;EACnB,CAAC;EAEDL,QAAQ,CAACY,SAAS,CAACK,IAAI,GAAG,SAASA,IAAIA,CAAEC,QAAQ,EAAEC,GAAG,EAAEC,IAAI,EAAE;IAC1D,IAAI,CAACC,YAAY,CAACH,QAAQ,EAAEC,GAAG,EAAEC,IAAI,CAAC;IACtC,IAAI,CAACZ,WAAW,CAAC,CAAC;EACtB,CAAC;EAEDR,QAAQ,CAACY,SAAS,CAACU,QAAQ,GAAG,SAASA,QAAQA,CAAEC,KAAK,EAAE;IACpD,IAAIC,GAAG,GAAG,IAAI;IACd,IAAItB,MAAM,GAAGsB,GAAG,CAACtB,MAAM;IACvB,IAAIC,IAAI,GAAGqB,GAAG,CAACrB,IAAI;IACnBD,MAAM,CAACuB,KAAK,GAAGtB,IAAI,CAACsB,KAAK,GAAGF,KAAK;IACjCrB,MAAM,CAACwB,MAAM,GAAGvB,IAAI,CAACuB,MAAM,GAAGH,KAAK;IACnC,IAAI,CAAClB,GAAG,CAACkB,KAAK,CAACA,KAAK,EAAEA,KAAK,CAAC;EAChC,CAAC;EAEDvB,QAAQ,CAACY,SAAS,CAACe,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAI;IACjE,IAAI,OAAOC,MAAM,CAACC,gBAAgB,KAAK,QAAQ,EAAE;MAC7C,OAAOD,MAAM,CAACC,gBAAgB;IAClC;IAEA,OAAO,CAAC;EACZ,CAAC;EAED7B,QAAQ,CAACY,SAAS,CAACJ,WAAW,GAAG,SAASA,WAAWA,CAAEsB,OAAO,EAAE;IAC5D,IAAI,CAAC,IAAI,CAACzB,GAAG,EAAE;MACX;IACJ;IAEA,IAAI0B,UAAU,GAAGD,OAAO,IAAIA,OAAO,CAACC,UAAU;IAC9C,IAAIR,KAAK,GAAGQ,UAAU,GAAG,CAAC,GAAG,IAAI,CAACJ,iBAAiB,CAAC,CAAC;IACrD,IAAI,CAACL,QAAQ,CAACC,KAAK,CAAC;IAEpB,IAAI,CAAClB,GAAG,CAAC2B,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC9B,MAAM,CAACuB,KAAK,EAAE,IAAI,CAACvB,MAAM,CAACwB,MAAM,CAAC;IAC/D,IAAI,CAACO,QAAQ,CAAC,IAAI,CAAC5B,GAAG,CAAC;EAC3B,CAAC;EAED,OAAOL,QAAQ;AACnB,CAAC,CAACJ,WAAW,CAACD,SAAS,EAAE,YAAY,CAAC,CAAE;AAExC,eAAeK,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}