{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst o = {\n    vertical: \"k-bottom-nav-item-flow-vertical\",\n    horizontal: \"k-bottom-nav-item-flow-horizontal\"\n  },\n  t = {\n    fixed: \"k-pos-fixed\",\n    sticky: \"k-pos-sticky\"\n  };\nexport { o as ITEM_FLOW_CLASSES, t as POSITION_MODE_CLASSES };", "map": {"version": 3, "names": ["o", "vertical", "horizontal", "t", "fixed", "sticky", "ITEM_FLOW_CLASSES", "POSITION_MODE_CLASSES"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/bottomnavigation/models/utils.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst o = {\n  vertical: \"k-bottom-nav-item-flow-vertical\",\n  horizontal: \"k-bottom-nav-item-flow-horizontal\"\n}, t = {\n  fixed: \"k-pos-fixed\",\n  sticky: \"k-pos-sticky\"\n};\nexport {\n  o as ITEM_FLOW_CLASSES,\n  t as POSITION_MODE_CLASSES\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAG;IACRC,QAAQ,EAAE,iCAAiC;IAC3CC,UAAU,EAAE;EACd,CAAC;EAAEC,CAAC,GAAG;IACLC,KAAK,EAAE,aAAa;IACpBC,MAAM,EAAE;EACV,CAAC;AACD,SACEL,CAAC,IAAIM,iBAAiB,EACtBH,CAAC,IAAII,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}