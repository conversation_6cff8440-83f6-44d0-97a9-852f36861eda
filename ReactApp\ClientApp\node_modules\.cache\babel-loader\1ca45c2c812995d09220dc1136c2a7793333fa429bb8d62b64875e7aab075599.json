{"ast": null, "code": "export default (function (element) {\n  if (!element) {\n    return false;\n  }\n  if (element instanceof Element) {\n    if (element.offsetParent) {\n      return true;\n    }\n    if (element.getBBox) {\n      var _getBBox = element.getBBox(),\n        width = _getBBox.width,\n        height = _getBBox.height;\n      if (width || height) {\n        return true;\n      }\n    }\n    if (element.getBoundingClientRect) {\n      var _element$getBoundingC = element.getBoundingClientRect(),\n        _width = _element$getBoundingC.width,\n        _height = _element$getBoundingC.height;\n      if (_width || _height) {\n        return true;\n      }\n    }\n  }\n  return false;\n});", "map": {"version": 3, "names": ["element", "Element", "offsetParent", "getBBox", "_get<PERSON><PERSON>", "width", "height", "getBoundingClientRect", "_element$getBoundingC", "_width", "_height"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/rc-util/es/Dom/isVisible.js"], "sourcesContent": ["export default (function (element) {\n  if (!element) {\n    return false;\n  }\n  if (element instanceof Element) {\n    if (element.offsetParent) {\n      return true;\n    }\n    if (element.getBBox) {\n      var _getBBox = element.getBBox(),\n        width = _getBBox.width,\n        height = _getBBox.height;\n      if (width || height) {\n        return true;\n      }\n    }\n    if (element.getBoundingClientRect) {\n      var _element$getBoundingC = element.getBoundingClientRect(),\n        _width = _element$getBoundingC.width,\n        _height = _element$getBoundingC.height;\n      if (_width || _height) {\n        return true;\n      }\n    }\n  }\n  return false;\n});"], "mappings": "AAAA,gBAAgB,UAAUA,OAAO,EAAE;EACjC,IAAI,CAACA,OAAO,EAAE;IACZ,OAAO,KAAK;EACd;EACA,IAAIA,OAAO,YAAYC,OAAO,EAAE;IAC9B,IAAID,OAAO,CAACE,YAAY,EAAE;MACxB,OAAO,IAAI;IACb;IACA,IAAIF,OAAO,CAACG,OAAO,EAAE;MACnB,IAAIC,QAAQ,GAAGJ,OAAO,CAACG,OAAO,CAAC,CAAC;QAC9BE,KAAK,GAAGD,QAAQ,CAACC,KAAK;QACtBC,MAAM,GAAGF,QAAQ,CAACE,MAAM;MAC1B,IAAID,KAAK,IAAIC,MAAM,EAAE;QACnB,OAAO,IAAI;MACb;IACF;IACA,IAAIN,OAAO,CAACO,qBAAqB,EAAE;MACjC,IAAIC,qBAAqB,GAAGR,OAAO,CAACO,qBAAqB,CAAC,CAAC;QACzDE,MAAM,GAAGD,qBAAqB,CAACH,KAAK;QACpCK,OAAO,GAAGF,qBAAqB,CAACF,MAAM;MACxC,IAAIG,MAAM,IAAIC,OAAO,EAAE;QACrB,OAAO,IAAI;MACb;IACF;EACF;EACA,OAAO,KAAK;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}