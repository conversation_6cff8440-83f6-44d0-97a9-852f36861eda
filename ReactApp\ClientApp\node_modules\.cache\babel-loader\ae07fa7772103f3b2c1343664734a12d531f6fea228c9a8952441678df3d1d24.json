{"ast": null, "code": "import HasObservers from '../core/has-observers';\nvar push = [].push;\nvar pop = [].pop;\nvar splice = [].splice;\nvar shift = [].shift;\nvar slice = [].slice;\nvar unshift = [].unshift;\nvar ElementsArray = function (HasObservers) {\n  function ElementsArray(array) {\n    if (array === void 0) array = [];\n    HasObservers.call(this);\n    this.length = 0;\n    this._splice(0, array.length, array);\n  }\n  if (HasObservers) ElementsArray.__proto__ = HasObservers;\n  ElementsArray.prototype = Object.create(HasObservers && HasObservers.prototype);\n  ElementsArray.prototype.constructor = ElementsArray;\n  ElementsArray.prototype.elements = function elements(value) {\n    if (value) {\n      this._splice(0, this.length, value);\n      this._change();\n      return this;\n    }\n    return this.slice(0);\n  };\n  ElementsArray.prototype.push = function push$1() {\n    var elements = arguments;\n    var result = push.apply(this, elements);\n    this._add(elements);\n    return result;\n  };\n  ElementsArray.prototype.slice = function slice$1() {\n    return slice.call(this);\n  };\n  ElementsArray.prototype.pop = function pop$1() {\n    var length = this.length;\n    var result = pop.apply(this);\n    if (length) {\n      this._remove([result]);\n    }\n    return result;\n  };\n  ElementsArray.prototype.splice = function splice(index, howMany) {\n    var elements = slice.call(arguments, 2);\n    var result = this._splice(index, howMany, elements);\n    this._change();\n    return result;\n  };\n  ElementsArray.prototype.shift = function shift$1() {\n    var length = this.length;\n    var result = shift.apply(this);\n    if (length) {\n      this._remove([result]);\n    }\n    return result;\n  };\n  ElementsArray.prototype.unshift = function unshift$1() {\n    var elements = arguments;\n    var result = unshift.apply(this, elements);\n    this._add(elements);\n    return result;\n  };\n  ElementsArray.prototype.indexOf = function indexOf(element) {\n    var this$1 = this;\n    var length = this.length;\n    for (var idx = 0; idx < length; idx++) {\n      if (this$1[idx] === element) {\n        return idx;\n      }\n    }\n    return -1;\n  };\n  ElementsArray.prototype._splice = function _splice(index, howMany, elements) {\n    var result = splice.apply(this, [index, howMany].concat(elements));\n    this._clearObserver(result);\n    this._setObserver(elements);\n    return result;\n  };\n  ElementsArray.prototype._add = function _add(elements) {\n    this._setObserver(elements);\n    this._change();\n  };\n  ElementsArray.prototype._remove = function _remove(elements) {\n    this._clearObserver(elements);\n    this._change();\n  };\n  ElementsArray.prototype._setObserver = function _setObserver(elements) {\n    var this$1 = this;\n    for (var idx = 0; idx < elements.length; idx++) {\n      elements[idx].addObserver(this$1);\n    }\n  };\n  ElementsArray.prototype._clearObserver = function _clearObserver(elements) {\n    var this$1 = this;\n    for (var idx = 0; idx < elements.length; idx++) {\n      elements[idx].removeObserver(this$1);\n    }\n  };\n  ElementsArray.prototype._change = function _change() {};\n  return ElementsArray;\n}(HasObservers);\nexport default ElementsArray;", "map": {"version": 3, "names": ["HasObservers", "push", "pop", "splice", "shift", "slice", "unshift", "ElementsArray", "array", "call", "length", "_splice", "__proto__", "prototype", "Object", "create", "constructor", "elements", "value", "_change", "push$1", "arguments", "result", "apply", "_add", "slice$1", "pop$1", "_remove", "index", "how<PERSON><PERSON>", "shift$1", "unshift$1", "indexOf", "element", "this$1", "idx", "concat", "_clearObserver", "_setObserver", "addObserver", "removeObserver"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/shapes/elements-array.js"], "sourcesContent": ["import HasObservers from '../core/has-observers';\n\nvar push = [].push;\nvar pop = [].pop;\nvar splice = [].splice;\nvar shift = [].shift;\nvar slice = [].slice;\nvar unshift = [].unshift;\n\nvar ElementsArray = (function (HasObservers) {\n    function ElementsArray(array) {\n        if ( array === void 0 ) array = [];\n\n        HasObservers.call(this);\n\n        this.length = 0;\n        this._splice(0, array.length, array);\n    }\n\n    if ( HasObservers ) ElementsArray.__proto__ = HasObservers;\n    ElementsArray.prototype = Object.create( HasObservers && HasObservers.prototype );\n    ElementsArray.prototype.constructor = ElementsArray;\n\n    ElementsArray.prototype.elements = function elements (value) {\n        if (value) {\n            this._splice(0, this.length, value);\n\n            this._change();\n            return this;\n        }\n\n        return this.slice(0);\n    };\n\n    ElementsArray.prototype.push = function push$1 () {\n        var elements = arguments;\n        var result = push.apply(this, elements);\n\n        this._add(elements);\n\n        return result;\n    };\n\n    ElementsArray.prototype.slice = function slice$1 () {\n        return slice.call(this);\n    };\n\n    ElementsArray.prototype.pop = function pop$1 () {\n        var length = this.length;\n        var result = pop.apply(this);\n\n        if (length) {\n            this._remove([ result ]);\n        }\n\n        return result;\n    };\n\n    ElementsArray.prototype.splice = function splice (index, howMany) {\n        var elements = slice.call(arguments, 2);\n        var result = this._splice(index, howMany, elements);\n\n        this._change();\n\n        return result;\n    };\n\n    ElementsArray.prototype.shift = function shift$1 () {\n        var length = this.length;\n        var result = shift.apply(this);\n\n        if (length) {\n            this._remove([ result ]);\n        }\n\n        return result;\n    };\n\n    ElementsArray.prototype.unshift = function unshift$1 () {\n        var elements = arguments;\n        var result = unshift.apply(this, elements);\n\n        this._add(elements);\n\n        return result;\n    };\n\n    ElementsArray.prototype.indexOf = function indexOf (element) {\n        var this$1 = this;\n\n        var length = this.length;\n\n        for (var idx = 0; idx < length; idx++) {\n            if (this$1[idx] === element) {\n                return idx;\n            }\n        }\n        return -1;\n    };\n\n    ElementsArray.prototype._splice = function _splice (index, howMany, elements) {\n        var result = splice.apply(this, [ index, howMany ].concat(elements));\n\n        this._clearObserver(result);\n        this._setObserver(elements);\n\n        return result;\n    };\n\n    ElementsArray.prototype._add = function _add (elements) {\n        this._setObserver(elements);\n        this._change();\n    };\n\n    ElementsArray.prototype._remove = function _remove (elements) {\n        this._clearObserver(elements);\n        this._change();\n    };\n\n    ElementsArray.prototype._setObserver = function _setObserver (elements) {\n        var this$1 = this;\n\n        for (var idx = 0; idx < elements.length; idx++) {\n            elements[idx].addObserver(this$1);\n        }\n    };\n\n    ElementsArray.prototype._clearObserver = function _clearObserver (elements) {\n        var this$1 = this;\n\n        for (var idx = 0; idx < elements.length; idx++) {\n            elements[idx].removeObserver(this$1);\n        }\n    };\n\n    ElementsArray.prototype._change = function _change () {};\n\n    return ElementsArray;\n}(HasObservers));\n\nexport default ElementsArray;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,uBAAuB;AAEhD,IAAIC,IAAI,GAAG,EAAE,CAACA,IAAI;AAClB,IAAIC,GAAG,GAAG,EAAE,CAACA,GAAG;AAChB,IAAIC,MAAM,GAAG,EAAE,CAACA,MAAM;AACtB,IAAIC,KAAK,GAAG,EAAE,CAACA,KAAK;AACpB,IAAIC,KAAK,GAAG,EAAE,CAACA,KAAK;AACpB,IAAIC,OAAO,GAAG,EAAE,CAACA,OAAO;AAExB,IAAIC,aAAa,GAAI,UAAUP,YAAY,EAAE;EACzC,SAASO,aAAaA,CAACC,KAAK,EAAE;IAC1B,IAAKA,KAAK,KAAK,KAAK,CAAC,EAAGA,KAAK,GAAG,EAAE;IAElCR,YAAY,CAACS,IAAI,CAAC,IAAI,CAAC;IAEvB,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,OAAO,CAAC,CAAC,EAAEH,KAAK,CAACE,MAAM,EAAEF,KAAK,CAAC;EACxC;EAEA,IAAKR,YAAY,EAAGO,aAAa,CAACK,SAAS,GAAGZ,YAAY;EAC1DO,aAAa,CAACM,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAEf,YAAY,IAAIA,YAAY,CAACa,SAAU,CAAC;EACjFN,aAAa,CAACM,SAAS,CAACG,WAAW,GAAGT,aAAa;EAEnDA,aAAa,CAACM,SAAS,CAACI,QAAQ,GAAG,SAASA,QAAQA,CAAEC,KAAK,EAAE;IACzD,IAAIA,KAAK,EAAE;MACP,IAAI,CAACP,OAAO,CAAC,CAAC,EAAE,IAAI,CAACD,MAAM,EAAEQ,KAAK,CAAC;MAEnC,IAAI,CAACC,OAAO,CAAC,CAAC;MACd,OAAO,IAAI;IACf;IAEA,OAAO,IAAI,CAACd,KAAK,CAAC,CAAC,CAAC;EACxB,CAAC;EAEDE,aAAa,CAACM,SAAS,CAACZ,IAAI,GAAG,SAASmB,MAAMA,CAAA,EAAI;IAC9C,IAAIH,QAAQ,GAAGI,SAAS;IACxB,IAAIC,MAAM,GAAGrB,IAAI,CAACsB,KAAK,CAAC,IAAI,EAAEN,QAAQ,CAAC;IAEvC,IAAI,CAACO,IAAI,CAACP,QAAQ,CAAC;IAEnB,OAAOK,MAAM;EACjB,CAAC;EAEDf,aAAa,CAACM,SAAS,CAACR,KAAK,GAAG,SAASoB,OAAOA,CAAA,EAAI;IAChD,OAAOpB,KAAK,CAACI,IAAI,CAAC,IAAI,CAAC;EAC3B,CAAC;EAEDF,aAAa,CAACM,SAAS,CAACX,GAAG,GAAG,SAASwB,KAAKA,CAAA,EAAI;IAC5C,IAAIhB,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB,IAAIY,MAAM,GAAGpB,GAAG,CAACqB,KAAK,CAAC,IAAI,CAAC;IAE5B,IAAIb,MAAM,EAAE;MACR,IAAI,CAACiB,OAAO,CAAC,CAAEL,MAAM,CAAE,CAAC;IAC5B;IAEA,OAAOA,MAAM;EACjB,CAAC;EAEDf,aAAa,CAACM,SAAS,CAACV,MAAM,GAAG,SAASA,MAAMA,CAAEyB,KAAK,EAAEC,OAAO,EAAE;IAC9D,IAAIZ,QAAQ,GAAGZ,KAAK,CAACI,IAAI,CAACY,SAAS,EAAE,CAAC,CAAC;IACvC,IAAIC,MAAM,GAAG,IAAI,CAACX,OAAO,CAACiB,KAAK,EAAEC,OAAO,EAAEZ,QAAQ,CAAC;IAEnD,IAAI,CAACE,OAAO,CAAC,CAAC;IAEd,OAAOG,MAAM;EACjB,CAAC;EAEDf,aAAa,CAACM,SAAS,CAACT,KAAK,GAAG,SAAS0B,OAAOA,CAAA,EAAI;IAChD,IAAIpB,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB,IAAIY,MAAM,GAAGlB,KAAK,CAACmB,KAAK,CAAC,IAAI,CAAC;IAE9B,IAAIb,MAAM,EAAE;MACR,IAAI,CAACiB,OAAO,CAAC,CAAEL,MAAM,CAAE,CAAC;IAC5B;IAEA,OAAOA,MAAM;EACjB,CAAC;EAEDf,aAAa,CAACM,SAAS,CAACP,OAAO,GAAG,SAASyB,SAASA,CAAA,EAAI;IACpD,IAAId,QAAQ,GAAGI,SAAS;IACxB,IAAIC,MAAM,GAAGhB,OAAO,CAACiB,KAAK,CAAC,IAAI,EAAEN,QAAQ,CAAC;IAE1C,IAAI,CAACO,IAAI,CAACP,QAAQ,CAAC;IAEnB,OAAOK,MAAM;EACjB,CAAC;EAEDf,aAAa,CAACM,SAAS,CAACmB,OAAO,GAAG,SAASA,OAAOA,CAAEC,OAAO,EAAE;IACzD,IAAIC,MAAM,GAAG,IAAI;IAEjB,IAAIxB,MAAM,GAAG,IAAI,CAACA,MAAM;IAExB,KAAK,IAAIyB,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGzB,MAAM,EAAEyB,GAAG,EAAE,EAAE;MACnC,IAAID,MAAM,CAACC,GAAG,CAAC,KAAKF,OAAO,EAAE;QACzB,OAAOE,GAAG;MACd;IACJ;IACA,OAAO,CAAC,CAAC;EACb,CAAC;EAED5B,aAAa,CAACM,SAAS,CAACF,OAAO,GAAG,SAASA,OAAOA,CAAEiB,KAAK,EAAEC,OAAO,EAAEZ,QAAQ,EAAE;IAC1E,IAAIK,MAAM,GAAGnB,MAAM,CAACoB,KAAK,CAAC,IAAI,EAAE,CAAEK,KAAK,EAAEC,OAAO,CAAE,CAACO,MAAM,CAACnB,QAAQ,CAAC,CAAC;IAEpE,IAAI,CAACoB,cAAc,CAACf,MAAM,CAAC;IAC3B,IAAI,CAACgB,YAAY,CAACrB,QAAQ,CAAC;IAE3B,OAAOK,MAAM;EACjB,CAAC;EAEDf,aAAa,CAACM,SAAS,CAACW,IAAI,GAAG,SAASA,IAAIA,CAAEP,QAAQ,EAAE;IACpD,IAAI,CAACqB,YAAY,CAACrB,QAAQ,CAAC;IAC3B,IAAI,CAACE,OAAO,CAAC,CAAC;EAClB,CAAC;EAEDZ,aAAa,CAACM,SAAS,CAACc,OAAO,GAAG,SAASA,OAAOA,CAAEV,QAAQ,EAAE;IAC1D,IAAI,CAACoB,cAAc,CAACpB,QAAQ,CAAC;IAC7B,IAAI,CAACE,OAAO,CAAC,CAAC;EAClB,CAAC;EAEDZ,aAAa,CAACM,SAAS,CAACyB,YAAY,GAAG,SAASA,YAAYA,CAAErB,QAAQ,EAAE;IACpE,IAAIiB,MAAM,GAAG,IAAI;IAEjB,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGlB,QAAQ,CAACP,MAAM,EAAEyB,GAAG,EAAE,EAAE;MAC5ClB,QAAQ,CAACkB,GAAG,CAAC,CAACI,WAAW,CAACL,MAAM,CAAC;IACrC;EACJ,CAAC;EAED3B,aAAa,CAACM,SAAS,CAACwB,cAAc,GAAG,SAASA,cAAcA,CAAEpB,QAAQ,EAAE;IACxE,IAAIiB,MAAM,GAAG,IAAI;IAEjB,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGlB,QAAQ,CAACP,MAAM,EAAEyB,GAAG,EAAE,EAAE;MAC5ClB,QAAQ,CAACkB,GAAG,CAAC,CAACK,cAAc,CAACN,MAAM,CAAC;IACxC;EACJ,CAAC;EAED3B,aAAa,CAACM,SAAS,CAACM,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI,CAAC,CAAC;EAExD,OAAOZ,aAAa;AACxB,CAAC,CAACP,YAAY,CAAE;AAEhB,eAAeO,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}