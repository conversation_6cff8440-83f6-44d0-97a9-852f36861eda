{"ast": null, "code": "import { defined } from '../util';\nimport Point from '../geometry/point';\nfunction pointAccessor(name) {\n  var fieldName = \"_\" + name;\n  return function (value) {\n    if (defined(value)) {\n      this._observerField(fieldName, Point.create(value));\n      this.geometryChange();\n      return this;\n    }\n    return this[fieldName];\n  };\n}\nfunction definePointAccessors(fn, names) {\n  for (var i = 0; i < names.length; i++) {\n    fn[names[i]] = pointAccessor(names[i]);\n  }\n}\nvar withPoints = function (TBase, names) {\n  var result = function (TBase) {\n    function result() {\n      TBase.apply(this, arguments);\n    }\n    if (TBase) result.__proto__ = TBase;\n    result.prototype = Object.create(TBase && TBase.prototype);\n    result.prototype.constructor = result;\n    return result;\n  }(TBase);\n  definePointAccessors(result.prototype, names);\n  return result;\n};\nexport default withPoints;", "map": {"version": 3, "names": ["defined", "Point", "pointAccessor", "name", "fieldName", "value", "_observer<PERSON>ield", "create", "geometryChange", "definePointAccessors", "fn", "names", "i", "length", "withPoints", "TBase", "result", "apply", "arguments", "__proto__", "prototype", "Object", "constructor"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/mixins/with-points.js"], "sourcesContent": ["import { defined } from '../util';\nimport Point from '../geometry/point';\n\nfunction pointAccessor(name) {\n    var fieldName = \"_\" + name;\n    return function(value) {\n        if (defined(value)) {\n            this._observerField(fieldName, Point.create(value));\n            this.geometryChange();\n            return this;\n        }\n\n        return this[fieldName];\n    };\n}\n\nfunction definePointAccessors(fn, names) {\n    for (var i = 0; i < names.length; i++) {\n        fn[names[i]] = pointAccessor(names[i]);\n    }\n}\n\nvar withPoints = function (TBase, names) {\n    var result = (function (TBase) {\n        function result () {\n            TBase.apply(this, arguments);\n        }if ( TBase ) result.__proto__ = TBase;\n        result.prototype = Object.create( TBase && TBase.prototype );\n        result.prototype.constructor = result;\n\n        \n\n        return result;\n    }(TBase));\n    definePointAccessors(result.prototype, names);\n\n    return result;\n};\n\nexport default withPoints;\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,SAAS;AACjC,OAAOC,KAAK,MAAM,mBAAmB;AAErC,SAASC,aAAaA,CAACC,IAAI,EAAE;EACzB,IAAIC,SAAS,GAAG,GAAG,GAAGD,IAAI;EAC1B,OAAO,UAASE,KAAK,EAAE;IACnB,IAAIL,OAAO,CAACK,KAAK,CAAC,EAAE;MAChB,IAAI,CAACC,cAAc,CAACF,SAAS,EAAEH,KAAK,CAACM,MAAM,CAACF,KAAK,CAAC,CAAC;MACnD,IAAI,CAACG,cAAc,CAAC,CAAC;MACrB,OAAO,IAAI;IACf;IAEA,OAAO,IAAI,CAACJ,SAAS,CAAC;EAC1B,CAAC;AACL;AAEA,SAASK,oBAAoBA,CAACC,EAAE,EAAEC,KAAK,EAAE;EACrC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IACnCF,EAAE,CAACC,KAAK,CAACC,CAAC,CAAC,CAAC,GAAGV,aAAa,CAACS,KAAK,CAACC,CAAC,CAAC,CAAC;EAC1C;AACJ;AAEA,IAAIE,UAAU,GAAG,SAAAA,CAAUC,KAAK,EAAEJ,KAAK,EAAE;EACrC,IAAIK,MAAM,GAAI,UAAUD,KAAK,EAAE;IAC3B,SAASC,MAAMA,CAAA,EAAI;MACfD,KAAK,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAChC;IAAC,IAAKH,KAAK,EAAGC,MAAM,CAACG,SAAS,GAAGJ,KAAK;IACtCC,MAAM,CAACI,SAAS,GAAGC,MAAM,CAACd,MAAM,CAAEQ,KAAK,IAAIA,KAAK,CAACK,SAAU,CAAC;IAC5DJ,MAAM,CAACI,SAAS,CAACE,WAAW,GAAGN,MAAM;IAIrC,OAAOA,MAAM;EACjB,CAAC,CAACD,KAAK,CAAE;EACTN,oBAAoB,CAACO,MAAM,CAACI,SAAS,EAAET,KAAK,CAAC;EAE7C,OAAOK,MAAM;AACjB,CAAC;AAED,eAAeF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}