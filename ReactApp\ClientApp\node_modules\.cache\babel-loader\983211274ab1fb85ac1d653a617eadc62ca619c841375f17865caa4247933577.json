{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nexport default function useLazyKVMap(data, childrenColumnName, getRowKey) {\n  var mapCacheRef = React.useRef({});\n  function getRecordByKey(key) {\n    if (!mapCacheRef.current || mapCacheRef.current.data !== data || mapCacheRef.current.childrenColumnName !== childrenColumnName || mapCacheRef.current.getRowKey !== getRowKey) {\n      var kvMap = new Map();\n      /* eslint-disable no-inner-declarations */\n      function dig(records) {\n        records.forEach(function (record, index) {\n          var rowKey = getRowKey(record, index);\n          kvMap.set(rowKey, record);\n          if (record && _typeof(record) === 'object' && childrenColumnName in record) {\n            dig(record[childrenColumnName] || []);\n          }\n        });\n      }\n      /* eslint-enable */\n      dig(data);\n      mapCacheRef.current = {\n        data: data,\n        childrenColumnName: childrenColumnName,\n        kvMap: kvMap,\n        getRowKey: getRowKey\n      };\n    }\n    return mapCacheRef.current.kvMap.get(key);\n  }\n  return [getRecordByKey];\n}", "map": {"version": 3, "names": ["_typeof", "React", "useLazyKVMap", "data", "childrenColumnName", "getRowKey", "mapCacheRef", "useRef", "getRecordByKey", "key", "current", "kvMap", "Map", "dig", "records", "for<PERSON>ach", "record", "index", "<PERSON><PERSON><PERSON>", "set", "get"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/antd/es/table/hooks/useLazyKVMap.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nexport default function useLazyKVMap(data, childrenColumnName, getRowKey) {\n  var mapCacheRef = React.useRef({});\n  function getRecordByKey(key) {\n    if (!mapCacheRef.current || mapCacheRef.current.data !== data || mapCacheRef.current.childrenColumnName !== childrenColumnName || mapCacheRef.current.getRowKey !== getRowKey) {\n      var kvMap = new Map();\n      /* eslint-disable no-inner-declarations */\n      function dig(records) {\n        records.forEach(function (record, index) {\n          var rowKey = getRowKey(record, index);\n          kvMap.set(rowKey, record);\n          if (record && _typeof(record) === 'object' && childrenColumnName in record) {\n            dig(record[childrenColumnName] || []);\n          }\n        });\n      }\n      /* eslint-enable */\n      dig(data);\n      mapCacheRef.current = {\n        data: data,\n        childrenColumnName: childrenColumnName,\n        kvMap: kvMap,\n        getRowKey: getRowKey\n      };\n    }\n    return mapCacheRef.current.kvMap.get(key);\n  }\n  return [getRecordByKey];\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,YAAYA,CAACC,IAAI,EAAEC,kBAAkB,EAAEC,SAAS,EAAE;EACxE,IAAIC,WAAW,GAAGL,KAAK,CAACM,MAAM,CAAC,CAAC,CAAC,CAAC;EAClC,SAASC,cAAcA,CAACC,GAAG,EAAE;IAC3B,IAAI,CAACH,WAAW,CAACI,OAAO,IAAIJ,WAAW,CAACI,OAAO,CAACP,IAAI,KAAKA,IAAI,IAAIG,WAAW,CAACI,OAAO,CAACN,kBAAkB,KAAKA,kBAAkB,IAAIE,WAAW,CAACI,OAAO,CAACL,SAAS,KAAKA,SAAS,EAAE;MAC7K,IAAIM,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;MACrB;MACA,SAASC,GAAGA,CAACC,OAAO,EAAE;QACpBA,OAAO,CAACC,OAAO,CAAC,UAAUC,MAAM,EAAEC,KAAK,EAAE;UACvC,IAAIC,MAAM,GAAGb,SAAS,CAACW,MAAM,EAAEC,KAAK,CAAC;UACrCN,KAAK,CAACQ,GAAG,CAACD,MAAM,EAAEF,MAAM,CAAC;UACzB,IAAIA,MAAM,IAAIhB,OAAO,CAACgB,MAAM,CAAC,KAAK,QAAQ,IAAIZ,kBAAkB,IAAIY,MAAM,EAAE;YAC1EH,GAAG,CAACG,MAAM,CAACZ,kBAAkB,CAAC,IAAI,EAAE,CAAC;UACvC;QACF,CAAC,CAAC;MACJ;MACA;MACAS,GAAG,CAACV,IAAI,CAAC;MACTG,WAAW,CAACI,OAAO,GAAG;QACpBP,IAAI,EAAEA,IAAI;QACVC,kBAAkB,EAAEA,kBAAkB;QACtCO,KAAK,EAAEA,KAAK;QACZN,SAAS,EAAEA;MACb,CAAC;IACH;IACA,OAAOC,WAAW,CAACI,OAAO,CAACC,KAAK,CAACS,GAAG,CAACX,GAAG,CAAC;EAC3C;EACA,OAAO,CAACD,cAAc,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}