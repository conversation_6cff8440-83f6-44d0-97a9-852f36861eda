{"ast": null, "code": "var now = Date.now || function () {\n  return new Date().getTime();\n};\nexport default now;", "map": {"version": 3, "names": ["now", "Date", "getTime"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/util/now.js"], "sourcesContent": ["var now = Date.now || function() {\n    return new Date().getTime();\n};\n\nexport default now;\n"], "mappings": "AAAA,IAAIA,GAAG,GAAGC,IAAI,CAACD,GAAG,IAAI,YAAW;EAC7B,OAAO,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;AAC/B,CAAC;AAED,eAAeF,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}