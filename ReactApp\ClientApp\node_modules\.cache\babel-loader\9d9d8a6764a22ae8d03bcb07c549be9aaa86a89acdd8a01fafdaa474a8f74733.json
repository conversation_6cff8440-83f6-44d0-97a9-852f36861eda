{"ast": null, "code": "import { zoneAndRule } from './zone-and-rule';\n/**\n * @hidden\n *\n * A function that calculates the time offset based on zone name.\n *\n * @param timezone - The timezone name. For example, `America/Chicago`, `Europe/Sofia`.\n * @param date - A date for which the zone rule will be located.\n *\n * @return - Returns the timezone offset in minutes at the specified time.\n */\nexport var offset = function (timezone, date) {\n  if (date === void 0) {\n    date = new Date();\n  }\n  if (timezone === 'Etc/UTC' || timezone === 'Etc/GMT') {\n    return 0;\n  }\n  if (timezone === '') {\n    return date.getTimezoneOffset();\n  }\n  var _a = zoneAndRule(timezone, date),\n    rule = _a.rule,\n    zone = _a.zone;\n  return parseFloat(rule ? zone[0] - rule[6] : zone[0]);\n};", "map": {"version": 3, "names": ["zoneAndRule", "offset", "timezone", "date", "Date", "getTimezoneOffset", "_a", "rule", "zone", "parseFloat"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/tz/offset.js"], "sourcesContent": ["import { zoneAndRule } from './zone-and-rule';\n/**\n * @hidden\n *\n * A function that calculates the time offset based on zone name.\n *\n * @param timezone - The timezone name. For example, `America/Chicago`, `Europe/Sofia`.\n * @param date - A date for which the zone rule will be located.\n *\n * @return - Returns the timezone offset in minutes at the specified time.\n */\nexport var offset = function (timezone, date) {\n    if (date === void 0) { date = new Date(); }\n    if (timezone === 'Etc/UTC' || timezone === 'Etc/GMT') {\n        return 0;\n    }\n    if (timezone === '') {\n        return date.getTimezoneOffset();\n    }\n    var _a = zoneAndRule(timezone, date), rule = _a.rule, zone = _a.zone;\n    return parseFloat(rule ? zone[0] - rule[6] : zone[0]);\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,iBAAiB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,MAAM,GAAG,SAAAA,CAAUC,QAAQ,EAAEC,IAAI,EAAE;EAC1C,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAEA,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC;EAAE;EAC1C,IAAIF,QAAQ,KAAK,SAAS,IAAIA,QAAQ,KAAK,SAAS,EAAE;IAClD,OAAO,CAAC;EACZ;EACA,IAAIA,QAAQ,KAAK,EAAE,EAAE;IACjB,OAAOC,IAAI,CAACE,iBAAiB,CAAC,CAAC;EACnC;EACA,IAAIC,EAAE,GAAGN,WAAW,CAACE,QAAQ,EAAEC,IAAI,CAAC;IAAEI,IAAI,GAAGD,EAAE,CAACC,IAAI;IAAEC,IAAI,GAAGF,EAAE,CAACE,IAAI;EACpE,OAAOC,UAAU,CAACF,IAAI,GAAGC,IAAI,CAAC,CAAC,CAAC,GAAGD,IAAI,CAAC,CAAC,CAAC,GAAGC,IAAI,CAAC,CAAC,CAAC,CAAC;AACzD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}