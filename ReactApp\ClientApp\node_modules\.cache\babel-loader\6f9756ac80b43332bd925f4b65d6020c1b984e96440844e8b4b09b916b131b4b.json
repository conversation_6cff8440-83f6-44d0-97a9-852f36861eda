{"ast": null, "code": "export default function renderPath(ctx, path) {\n  var segments = path.segments;\n  if (segments.length === 0) {\n    return;\n  }\n  var segment = segments[0];\n  var anchor = segment.anchor();\n  ctx.moveTo(anchor.x, anchor.y);\n  for (var i = 1; i < segments.length; i++) {\n    segment = segments[i];\n    anchor = segment.anchor();\n    var prevSeg = segments[i - 1];\n    var prevOut = prevSeg.controlOut();\n    var controlIn = segment.controlIn();\n    if (prevOut && controlIn) {\n      ctx.bezierCurveTo(prevOut.x, prevOut.y, controlIn.x, controlIn.y, anchor.x, anchor.y);\n    } else {\n      ctx.lineTo(anchor.x, anchor.y);\n    }\n  }\n  if (path.options.closed) {\n    ctx.closePath();\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "ctx", "path", "segments", "length", "segment", "anchor", "moveTo", "x", "y", "i", "prevSeg", "prevOut", "controlOut", "controlIn", "bezierCurveTo", "lineTo", "options", "closed", "closePath"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/canvas/utils/render-path.js"], "sourcesContent": ["\nexport default function renderPath(ctx, path) {\n    var segments = path.segments;\n\n    if (segments.length === 0) {\n        return;\n    }\n\n    var segment = segments[0];\n    var anchor = segment.anchor();\n    ctx.moveTo(anchor.x, anchor.y);\n\n    for (var i = 1; i < segments.length; i++) {\n        segment = segments[i];\n        anchor = segment.anchor();\n\n        var prevSeg = segments[i - 1];\n        var prevOut = prevSeg.controlOut();\n        var controlIn = segment.controlIn();\n\n        if (prevOut && controlIn) {\n            ctx.bezierCurveTo(prevOut.x, prevOut.y,\n                controlIn.x, controlIn.y,\n                anchor.x, anchor.y);\n        } else {\n            ctx.lineTo(anchor.x, anchor.y);\n        }\n    }\n\n    if (path.options.closed) {\n        ctx.closePath();\n    }\n}\n"], "mappings": "AACA,eAAe,SAASA,UAAUA,CAACC,GAAG,EAAEC,IAAI,EAAE;EAC1C,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;EAE5B,IAAIA,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE;IACvB;EACJ;EAEA,IAAIC,OAAO,GAAGF,QAAQ,CAAC,CAAC,CAAC;EACzB,IAAIG,MAAM,GAAGD,OAAO,CAACC,MAAM,CAAC,CAAC;EAC7BL,GAAG,CAACM,MAAM,CAACD,MAAM,CAACE,CAAC,EAAEF,MAAM,CAACG,CAAC,CAAC;EAE9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,QAAQ,CAACC,MAAM,EAAEM,CAAC,EAAE,EAAE;IACtCL,OAAO,GAAGF,QAAQ,CAACO,CAAC,CAAC;IACrBJ,MAAM,GAAGD,OAAO,CAACC,MAAM,CAAC,CAAC;IAEzB,IAAIK,OAAO,GAAGR,QAAQ,CAACO,CAAC,GAAG,CAAC,CAAC;IAC7B,IAAIE,OAAO,GAAGD,OAAO,CAACE,UAAU,CAAC,CAAC;IAClC,IAAIC,SAAS,GAAGT,OAAO,CAACS,SAAS,CAAC,CAAC;IAEnC,IAAIF,OAAO,IAAIE,SAAS,EAAE;MACtBb,GAAG,CAACc,aAAa,CAACH,OAAO,CAACJ,CAAC,EAAEI,OAAO,CAACH,CAAC,EAClCK,SAAS,CAACN,CAAC,EAAEM,SAAS,CAACL,CAAC,EACxBH,MAAM,CAACE,CAAC,EAAEF,MAAM,CAACG,CAAC,CAAC;IAC3B,CAAC,MAAM;MACHR,GAAG,CAACe,MAAM,CAACV,MAAM,CAACE,CAAC,EAAEF,MAAM,CAACG,CAAC,CAAC;IAClC;EACJ;EAEA,IAAIP,IAAI,CAACe,OAAO,CAACC,MAAM,EAAE;IACrBjB,GAAG,CAACkB,SAAS,CAAC,CAAC;EACnB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}