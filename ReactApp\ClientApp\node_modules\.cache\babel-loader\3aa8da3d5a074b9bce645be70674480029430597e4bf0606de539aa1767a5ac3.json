{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst o = {\n  dayperiod: \"dayperiod\",\n  hour: \"hour\",\n  millisecond: \"millisecond\",\n  minute: \"minute\",\n  second: \"second\"\n};\nexport { o as TIME_PART };", "map": {"version": 3, "names": ["o", "dayperiod", "hour", "millisecond", "minute", "second", "TIME_PART"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dateinputs/timepicker/models/TimePart.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst o = {\n  dayperiod: \"dayperiod\",\n  hour: \"hour\",\n  millisecond: \"millisecond\",\n  minute: \"minute\",\n  second: \"second\"\n};\nexport {\n  o as TIME_PART\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAG;EACRC,SAAS,EAAE,WAAW;EACtBC,IAAI,EAAE,MAAM;EACZC,WAAW,EAAE,aAAa;EAC1BC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE;AACV,CAAC;AACD,SACEL,CAAC,IAAIM,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}