{"ast": null, "code": "var KEY_STR = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\";\nvar fromCharCode = String.fromCharCode;\nexport function encodeBase64(plainText) {\n  var input = encodeUTF8(plainText);\n  var output = \"\";\n  var chr1, chr2, chr3, enc1, enc2, enc3, enc4;\n  var i = 0;\n  while (i < input.length) {\n    chr1 = input.charCodeAt(i++);\n    chr2 = input.charCodeAt(i++);\n    chr3 = input.charCodeAt(i++);\n    enc1 = chr1 >> 2;\n    enc2 = (chr1 & 3) << 4 | chr2 >> 4;\n    enc3 = (chr2 & 15) << 2 | chr3 >> 6;\n    enc4 = chr3 & 63;\n    if (isNaN(chr2)) {\n      enc3 = enc4 = 64;\n    } else if (isNaN(chr3)) {\n      enc4 = 64;\n    }\n    output = output + KEY_STR.charAt(enc1) + KEY_STR.charAt(enc2) + KEY_STR.charAt(enc3) + KEY_STR.charAt(enc4);\n  }\n  return output;\n}\nfunction encodeUTF8(input) {\n  var output = \"\";\n  for (var i = 0; i < input.length; i++) {\n    var c = input.charCodeAt(i);\n    if (c < 0x80) {\n      // One byte\n      output += fromCharCode(c);\n    } else if (c < 0x800) {\n      // Two bytes\n      output += fromCharCode(0xC0 | c >>> 6);\n      output += fromCharCode(0x80 | c & 0x3f);\n    } else if (c < 0x10000) {\n      // Three bytes\n      output += fromCharCode(0xE0 | c >>> 12);\n      output += fromCharCode(0x80 | c >>> 6 & 0x3f);\n      output += fromCharCode(0x80 | c & 0x3f);\n    }\n  }\n  return output;\n}", "map": {"version": 3, "names": ["KEY_STR", "fromCharCode", "String", "encodeBase64", "plainText", "input", "encodeUTF8", "output", "chr1", "chr2", "chr3", "enc1", "enc2", "enc3", "enc4", "i", "length", "charCodeAt", "isNaN", "char<PERSON>t", "c"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-file-saver/dist/es/base64.js"], "sourcesContent": ["var KEY_STR = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\";\nvar fromCharCode = String.fromCharCode;\n\nexport function encodeBase64(plainText) {\n  var input = encodeUTF8(plainText);\n  var output = \"\";\n  var chr1, chr2, chr3, enc1, enc2, enc3, enc4;\n  var i = 0;\n\n  while (i < input.length) {\n    chr1 = input.charCodeAt(i++);\n    chr2 = input.charCodeAt(i++);\n    chr3 = input.charCodeAt(i++);\n\n    enc1 = chr1 >> 2;\n    enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);\n    enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);\n    enc4 = chr3 & 63;\n\n    if (isNaN(chr2)) {\n      enc3 = enc4 = 64;\n    } else if (isNaN(chr3)) {\n      enc4 = 64;\n    }\n\n    output = output +\n      KEY_STR.charAt(enc1) + KEY_STR.charAt(enc2) +\n      KEY_STR.charAt(enc3) + KEY_STR.charAt(enc4);\n  }\n\n  return output;\n}\n\nfunction encodeUTF8(input) {\n  var output = \"\";\n\n  for (var i = 0; i < input.length; i++) {\n    var c = input.charCodeAt(i);\n\n    if (c < 0x80) {\n      // One byte\n      output += fromCharCode(c);\n    } else if (c < 0x800) {\n      // Two bytes\n      output += fromCharCode(0xC0 | (c >>> 6));\n      output += fromCharCode(0x80 | (c & 0x3f));\n    } else if (c < 0x10000) {\n      // Three bytes\n      output += fromCharCode(0xE0 | (c >>> 12));\n      output += fromCharCode(0x80 | (c >>> 6 & 0x3f));\n      output += fromCharCode(0x80 | (c & 0x3f));\n    }\n  }\n\n  return output;\n}\n"], "mappings": "AAAA,IAAIA,OAAO,GAAG,mEAAmE;AACjF,IAAIC,YAAY,GAAGC,MAAM,CAACD,YAAY;AAEtC,OAAO,SAASE,YAAYA,CAACC,SAAS,EAAE;EACtC,IAAIC,KAAK,GAAGC,UAAU,CAACF,SAAS,CAAC;EACjC,IAAIG,MAAM,GAAG,EAAE;EACf,IAAIC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI;EAC5C,IAAIC,CAAC,GAAG,CAAC;EAET,OAAOA,CAAC,GAAGV,KAAK,CAACW,MAAM,EAAE;IACvBR,IAAI,GAAGH,KAAK,CAACY,UAAU,CAACF,CAAC,EAAE,CAAC;IAC5BN,IAAI,GAAGJ,KAAK,CAACY,UAAU,CAACF,CAAC,EAAE,CAAC;IAC5BL,IAAI,GAAGL,KAAK,CAACY,UAAU,CAACF,CAAC,EAAE,CAAC;IAE5BJ,IAAI,GAAGH,IAAI,IAAI,CAAC;IAChBI,IAAI,GAAI,CAACJ,IAAI,GAAG,CAAC,KAAK,CAAC,GAAKC,IAAI,IAAI,CAAE;IACtCI,IAAI,GAAI,CAACJ,IAAI,GAAG,EAAE,KAAK,CAAC,GAAKC,IAAI,IAAI,CAAE;IACvCI,IAAI,GAAGJ,IAAI,GAAG,EAAE;IAEhB,IAAIQ,KAAK,CAACT,IAAI,CAAC,EAAE;MACfI,IAAI,GAAGC,IAAI,GAAG,EAAE;IAClB,CAAC,MAAM,IAAII,KAAK,CAACR,IAAI,CAAC,EAAE;MACtBI,IAAI,GAAG,EAAE;IACX;IAEAP,MAAM,GAAGA,MAAM,GACbP,OAAO,CAACmB,MAAM,CAACR,IAAI,CAAC,GAAGX,OAAO,CAACmB,MAAM,CAACP,IAAI,CAAC,GAC3CZ,OAAO,CAACmB,MAAM,CAACN,IAAI,CAAC,GAAGb,OAAO,CAACmB,MAAM,CAACL,IAAI,CAAC;EAC/C;EAEA,OAAOP,MAAM;AACf;AAEA,SAASD,UAAUA,CAACD,KAAK,EAAE;EACzB,IAAIE,MAAM,GAAG,EAAE;EAEf,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,KAAK,CAACW,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,IAAIK,CAAC,GAAGf,KAAK,CAACY,UAAU,CAACF,CAAC,CAAC;IAE3B,IAAIK,CAAC,GAAG,IAAI,EAAE;MACZ;MACAb,MAAM,IAAIN,YAAY,CAACmB,CAAC,CAAC;IAC3B,CAAC,MAAM,IAAIA,CAAC,GAAG,KAAK,EAAE;MACpB;MACAb,MAAM,IAAIN,YAAY,CAAC,IAAI,GAAImB,CAAC,KAAK,CAAE,CAAC;MACxCb,MAAM,IAAIN,YAAY,CAAC,IAAI,GAAImB,CAAC,GAAG,IAAK,CAAC;IAC3C,CAAC,MAAM,IAAIA,CAAC,GAAG,OAAO,EAAE;MACtB;MACAb,MAAM,IAAIN,YAAY,CAAC,IAAI,GAAImB,CAAC,KAAK,EAAG,CAAC;MACzCb,MAAM,IAAIN,YAAY,CAAC,IAAI,GAAImB,CAAC,KAAK,CAAC,GAAG,IAAK,CAAC;MAC/Cb,MAAM,IAAIN,YAAY,CAAC,IAAI,GAAImB,CAAC,GAAG,IAAK,CAAC;IAC3C;EACF;EAEA,OAAOb,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}