{"ast": null, "code": "/* Copyright 2014 Opera Software ASA\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n *\n * Based on https://code.google.com/p/smhasher/wiki/MurmurHash3.\n * Hashes roughly 100 KB per millisecond on i7 3.4 GHz.\n */\nconst SEED = 0xc3d2e1f0;\n// Workaround for missing math precision in JS.\nconst MASK_HIGH = 0xffff0000;\nconst MASK_LOW = 0xffff;\nclass MurmurHash3_64 {\n  // todo: props\n  constructor(seed) {\n    // todo: props\n    this.h1 = null;\n    this.h2 = null;\n    this.h1 = seed ? seed & 0xffffffff : SEED;\n    this.h2 = seed ? seed & 0xffffffff : SEED;\n  }\n  update(input) {\n    let data, length;\n    if (typeof input === \"string\") {\n      data = new Uint8Array(input.length * 2);\n      length = 0;\n      for (let i = 0, ii = input.length; i < ii; i++) {\n        const code = input.charCodeAt(i);\n        if (code <= 0xff) {\n          data[length++] = code;\n        } else {\n          data[length++] = code >>> 8;\n          data[length++] = code & 0xff;\n        }\n      }\n    } else if (ArrayBuffer.isView(input)) {\n      data = input.slice();\n      length = data.byteLength;\n    } else {\n      throw new Error(\"Invalid data format, must be a string or TypedArray.\");\n    }\n    const blockCounts = length >> 2;\n    const tailLength = length - blockCounts * 4;\n    // We don't care about endianness here.\n    const dataUint32 = new Uint32Array(data.buffer, 0, blockCounts);\n    let k1 = 0,\n      k2 = 0;\n    let h1 = this.h1,\n      h2 = this.h2;\n    const C1 = 0xcc9e2d51,\n      C2 = 0x1b873593;\n    const C1_LOW = C1 & MASK_LOW,\n      C2_LOW = C2 & MASK_LOW;\n    for (let i = 0; i < blockCounts; i++) {\n      if (i & 1) {\n        k1 = dataUint32[i];\n        k1 = k1 * C1 & MASK_HIGH | k1 * C1_LOW & MASK_LOW;\n        k1 = k1 << 15 | k1 >>> 17;\n        k1 = k1 * C2 & MASK_HIGH | k1 * C2_LOW & MASK_LOW;\n        h1 ^= k1;\n        h1 = h1 << 13 | h1 >>> 19;\n        h1 = h1 * 5 + 0xe6546b64;\n      } else {\n        k2 = dataUint32[i];\n        k2 = k2 * C1 & MASK_HIGH | k2 * C1_LOW & MASK_LOW;\n        k2 = k2 << 15 | k2 >>> 17;\n        k2 = k2 * C2 & MASK_HIGH | k2 * C2_LOW & MASK_LOW;\n        h2 ^= k2;\n        h2 = h2 << 13 | h2 >>> 19;\n        h2 = h2 * 5 + 0xe6546b64;\n      }\n    }\n    k1 = 0;\n    switch (tailLength) {\n      case 3:\n        k1 ^= data[blockCounts * 4 + 2] << 16;\n      /* falls through */\n      case 2:\n        k1 ^= data[blockCounts * 4 + 1] << 8;\n      /* falls through */\n      case 1:\n        k1 ^= data[blockCounts * 4];\n        /* falls through */\n        k1 = k1 * C1 & MASK_HIGH | k1 * C1_LOW & MASK_LOW;\n        k1 = k1 << 15 | k1 >>> 17;\n        k1 = k1 * C2 & MASK_HIGH | k1 * C2_LOW & MASK_LOW;\n        if (blockCounts & 1) {\n          h1 ^= k1;\n        } else {\n          h2 ^= k1;\n        }\n        break;\n      default:\n        break;\n    }\n    this.h1 = h1;\n    this.h2 = h2;\n  }\n  hexdigest() {\n    let h1 = this.h1,\n      h2 = this.h2;\n    h1 ^= h2 >>> 1;\n    h1 = h1 * 0xed558ccd & MASK_HIGH | h1 * 0x8ccd & MASK_LOW;\n    h2 = h2 * 0xff51afd7 & MASK_HIGH | ((h2 << 16 | h1 >>> 16) * 0xafd7ed55 & MASK_HIGH) >>> 16;\n    h1 ^= h2 >>> 1;\n    h1 = h1 * 0x1a85ec53 & MASK_HIGH | h1 * 0xec53 & MASK_LOW;\n    h2 = h2 * 0xc4ceb9fe & MASK_HIGH | ((h2 << 16 | h1 >>> 16) * 0xb9fe1a85 & MASK_HIGH) >>> 16;\n    h1 ^= h2 >>> 1;\n    return (h1 >>> 0).toString(16).padStart(8, \"0\") + (h2 >>> 0).toString(16).padStart(8, \"0\");\n  }\n}\nexport { MurmurHash3_64 };", "map": {"version": 3, "names": ["SEED", "MASK_HIGH", "MASK_LOW", "MurmurHash3_64", "constructor", "seed", "h1", "h2", "update", "input", "data", "length", "Uint8Array", "i", "ii", "code", "charCodeAt", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "slice", "byteLength", "Error", "blockCounts", "tailLength", "dataUint32", "Uint32Array", "buffer", "k1", "k2", "C1", "C2", "C1_LOW", "C2_LOW", "hexdigest", "toString", "padStart"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-pdfviewer-common/dist/es/annotations/shared/murmurhash3.js"], "sourcesContent": ["/* Copyright 2014 Opera Software ASA\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n *\n * Based on https://code.google.com/p/smhasher/wiki/MurmurHash3.\n * Hashes roughly 100 KB per millisecond on i7 3.4 GHz.\n */\nconst SEED = 0xc3d2e1f0;\n// Workaround for missing math precision in JS.\nconst MASK_HIGH = 0xffff0000;\nconst MASK_LOW = 0xffff;\nclass MurmurHash3_64 {\n    // todo: props\n    constructor(seed) {\n        // todo: props\n        this.h1 = null;\n        this.h2 = null;\n        this.h1 = seed ? seed & 0xffffffff : SEED;\n        this.h2 = seed ? seed & 0xffffffff : SEED;\n    }\n    update(input) {\n        let data, length;\n        if (typeof input === \"string\") {\n            data = new Uint8Array(input.length * 2);\n            length = 0;\n            for (let i = 0, ii = input.length; i < ii; i++) {\n                const code = input.charCodeAt(i);\n                if (code <= 0xff) {\n                    data[length++] = code;\n                }\n                else {\n                    data[length++] = code >>> 8;\n                    data[length++] = code & 0xff;\n                }\n            }\n        }\n        else if (ArrayBuffer.isView(input)) {\n            data = input.slice();\n            length = data.byteLength;\n        }\n        else {\n            throw new Error(\"Invalid data format, must be a string or TypedArray.\");\n        }\n        const blockCounts = length >> 2;\n        const tailLength = length - blockCounts * 4;\n        // We don't care about endianness here.\n        const dataUint32 = new Uint32Array(data.buffer, 0, blockCounts);\n        let k1 = 0, k2 = 0;\n        let h1 = this.h1, h2 = this.h2;\n        const C1 = 0xcc9e2d51, C2 = 0x1b873593;\n        const C1_LOW = C1 & MASK_LOW, C2_LOW = C2 & MASK_LOW;\n        for (let i = 0; i < blockCounts; i++) {\n            if (i & 1) {\n                k1 = dataUint32[i];\n                k1 = ((k1 * C1) & MASK_HIGH) | ((k1 * C1_LOW) & MASK_LOW);\n                k1 = (k1 << 15) | (k1 >>> 17);\n                k1 = ((k1 * C2) & MASK_HIGH) | ((k1 * C2_LOW) & MASK_LOW);\n                h1 ^= k1;\n                h1 = (h1 << 13) | (h1 >>> 19);\n                h1 = h1 * 5 + 0xe6546b64;\n            }\n            else {\n                k2 = dataUint32[i];\n                k2 = ((k2 * C1) & MASK_HIGH) | ((k2 * C1_LOW) & MASK_LOW);\n                k2 = (k2 << 15) | (k2 >>> 17);\n                k2 = ((k2 * C2) & MASK_HIGH) | ((k2 * C2_LOW) & MASK_LOW);\n                h2 ^= k2;\n                h2 = (h2 << 13) | (h2 >>> 19);\n                h2 = h2 * 5 + 0xe6546b64;\n            }\n        }\n        k1 = 0;\n        switch (tailLength) {\n            case 3:\n                k1 ^= data[blockCounts * 4 + 2] << 16;\n            /* falls through */\n            case 2:\n                k1 ^= data[blockCounts * 4 + 1] << 8;\n            /* falls through */\n            case 1:\n                k1 ^= data[blockCounts * 4];\n                /* falls through */\n                k1 = ((k1 * C1) & MASK_HIGH) | ((k1 * C1_LOW) & MASK_LOW);\n                k1 = (k1 << 15) | (k1 >>> 17);\n                k1 = ((k1 * C2) & MASK_HIGH) | ((k1 * C2_LOW) & MASK_LOW);\n                if (blockCounts & 1) {\n                    h1 ^= k1;\n                }\n                else {\n                    h2 ^= k1;\n                }\n                break;\n            default: break;\n        }\n        this.h1 = h1;\n        this.h2 = h2;\n    }\n    hexdigest() {\n        let h1 = this.h1, h2 = this.h2;\n        h1 ^= h2 >>> 1;\n        h1 = ((h1 * 0xed558ccd) & MASK_HIGH) | ((h1 * 0x8ccd) & MASK_LOW);\n        h2 =\n            ((h2 * 0xff51afd7) & MASK_HIGH) |\n                (((((h2 << 16) | (h1 >>> 16)) * 0xafd7ed55) & MASK_HIGH) >>> 16);\n        h1 ^= h2 >>> 1;\n        h1 = ((h1 * 0x1a85ec53) & MASK_HIGH) | ((h1 * 0xec53) & MASK_LOW);\n        h2 =\n            ((h2 * 0xc4ceb9fe) & MASK_HIGH) |\n                (((((h2 << 16) | (h1 >>> 16)) * 0xb9fe1a85) & MASK_HIGH) >>> 16);\n        h1 ^= h2 >>> 1;\n        return ((h1 >>> 0).toString(16).padStart(8, \"0\") +\n            (h2 >>> 0).toString(16).padStart(8, \"0\"));\n    }\n}\nexport { MurmurHash3_64 };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,IAAI,GAAG,UAAU;AACvB;AACA,MAAMC,SAAS,GAAG,UAAU;AAC5B,MAAMC,QAAQ,GAAG,MAAM;AACvB,MAAMC,cAAc,CAAC;EACjB;EACAC,WAAWA,CAACC,IAAI,EAAE;IACd;IACA,IAAI,CAACC,EAAE,GAAG,IAAI;IACd,IAAI,CAACC,EAAE,GAAG,IAAI;IACd,IAAI,CAACD,EAAE,GAAGD,IAAI,GAAGA,IAAI,GAAG,UAAU,GAAGL,IAAI;IACzC,IAAI,CAACO,EAAE,GAAGF,IAAI,GAAGA,IAAI,GAAG,UAAU,GAAGL,IAAI;EAC7C;EACAQ,MAAMA,CAACC,KAAK,EAAE;IACV,IAAIC,IAAI,EAAEC,MAAM;IAChB,IAAI,OAAOF,KAAK,KAAK,QAAQ,EAAE;MAC3BC,IAAI,GAAG,IAAIE,UAAU,CAACH,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC;MACvCA,MAAM,GAAG,CAAC;MACV,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEC,EAAE,GAAGL,KAAK,CAACE,MAAM,EAAEE,CAAC,GAAGC,EAAE,EAAED,CAAC,EAAE,EAAE;QAC5C,MAAME,IAAI,GAAGN,KAAK,CAACO,UAAU,CAACH,CAAC,CAAC;QAChC,IAAIE,IAAI,IAAI,IAAI,EAAE;UACdL,IAAI,CAACC,MAAM,EAAE,CAAC,GAAGI,IAAI;QACzB,CAAC,MACI;UACDL,IAAI,CAACC,MAAM,EAAE,CAAC,GAAGI,IAAI,KAAK,CAAC;UAC3BL,IAAI,CAACC,MAAM,EAAE,CAAC,GAAGI,IAAI,GAAG,IAAI;QAChC;MACJ;IACJ,CAAC,MACI,IAAIE,WAAW,CAACC,MAAM,CAACT,KAAK,CAAC,EAAE;MAChCC,IAAI,GAAGD,KAAK,CAACU,KAAK,CAAC,CAAC;MACpBR,MAAM,GAAGD,IAAI,CAACU,UAAU;IAC5B,CAAC,MACI;MACD,MAAM,IAAIC,KAAK,CAAC,sDAAsD,CAAC;IAC3E;IACA,MAAMC,WAAW,GAAGX,MAAM,IAAI,CAAC;IAC/B,MAAMY,UAAU,GAAGZ,MAAM,GAAGW,WAAW,GAAG,CAAC;IAC3C;IACA,MAAME,UAAU,GAAG,IAAIC,WAAW,CAACf,IAAI,CAACgB,MAAM,EAAE,CAAC,EAAEJ,WAAW,CAAC;IAC/D,IAAIK,EAAE,GAAG,CAAC;MAAEC,EAAE,GAAG,CAAC;IAClB,IAAItB,EAAE,GAAG,IAAI,CAACA,EAAE;MAAEC,EAAE,GAAG,IAAI,CAACA,EAAE;IAC9B,MAAMsB,EAAE,GAAG,UAAU;MAAEC,EAAE,GAAG,UAAU;IACtC,MAAMC,MAAM,GAAGF,EAAE,GAAG3B,QAAQ;MAAE8B,MAAM,GAAGF,EAAE,GAAG5B,QAAQ;IACpD,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGS,WAAW,EAAET,CAAC,EAAE,EAAE;MAClC,IAAIA,CAAC,GAAG,CAAC,EAAE;QACPc,EAAE,GAAGH,UAAU,CAACX,CAAC,CAAC;QAClBc,EAAE,GAAKA,EAAE,GAAGE,EAAE,GAAI5B,SAAS,GAAM0B,EAAE,GAAGI,MAAM,GAAI7B,QAAS;QACzDyB,EAAE,GAAIA,EAAE,IAAI,EAAE,GAAKA,EAAE,KAAK,EAAG;QAC7BA,EAAE,GAAKA,EAAE,GAAGG,EAAE,GAAI7B,SAAS,GAAM0B,EAAE,GAAGK,MAAM,GAAI9B,QAAS;QACzDI,EAAE,IAAIqB,EAAE;QACRrB,EAAE,GAAIA,EAAE,IAAI,EAAE,GAAKA,EAAE,KAAK,EAAG;QAC7BA,EAAE,GAAGA,EAAE,GAAG,CAAC,GAAG,UAAU;MAC5B,CAAC,MACI;QACDsB,EAAE,GAAGJ,UAAU,CAACX,CAAC,CAAC;QAClBe,EAAE,GAAKA,EAAE,GAAGC,EAAE,GAAI5B,SAAS,GAAM2B,EAAE,GAAGG,MAAM,GAAI7B,QAAS;QACzD0B,EAAE,GAAIA,EAAE,IAAI,EAAE,GAAKA,EAAE,KAAK,EAAG;QAC7BA,EAAE,GAAKA,EAAE,GAAGE,EAAE,GAAI7B,SAAS,GAAM2B,EAAE,GAAGI,MAAM,GAAI9B,QAAS;QACzDK,EAAE,IAAIqB,EAAE;QACRrB,EAAE,GAAIA,EAAE,IAAI,EAAE,GAAKA,EAAE,KAAK,EAAG;QAC7BA,EAAE,GAAGA,EAAE,GAAG,CAAC,GAAG,UAAU;MAC5B;IACJ;IACAoB,EAAE,GAAG,CAAC;IACN,QAAQJ,UAAU;MACd,KAAK,CAAC;QACFI,EAAE,IAAIjB,IAAI,CAACY,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE;MACzC;MACA,KAAK,CAAC;QACFK,EAAE,IAAIjB,IAAI,CAACY,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;MACxC;MACA,KAAK,CAAC;QACFK,EAAE,IAAIjB,IAAI,CAACY,WAAW,GAAG,CAAC,CAAC;QAC3B;QACAK,EAAE,GAAKA,EAAE,GAAGE,EAAE,GAAI5B,SAAS,GAAM0B,EAAE,GAAGI,MAAM,GAAI7B,QAAS;QACzDyB,EAAE,GAAIA,EAAE,IAAI,EAAE,GAAKA,EAAE,KAAK,EAAG;QAC7BA,EAAE,GAAKA,EAAE,GAAGG,EAAE,GAAI7B,SAAS,GAAM0B,EAAE,GAAGK,MAAM,GAAI9B,QAAS;QACzD,IAAIoB,WAAW,GAAG,CAAC,EAAE;UACjBhB,EAAE,IAAIqB,EAAE;QACZ,CAAC,MACI;UACDpB,EAAE,IAAIoB,EAAE;QACZ;QACA;MACJ;QAAS;IACb;IACA,IAAI,CAACrB,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,EAAE,GAAGA,EAAE;EAChB;EACA0B,SAASA,CAAA,EAAG;IACR,IAAI3B,EAAE,GAAG,IAAI,CAACA,EAAE;MAAEC,EAAE,GAAG,IAAI,CAACA,EAAE;IAC9BD,EAAE,IAAIC,EAAE,KAAK,CAAC;IACdD,EAAE,GAAKA,EAAE,GAAG,UAAU,GAAIL,SAAS,GAAMK,EAAE,GAAG,MAAM,GAAIJ,QAAS;IACjEK,EAAE,GACIA,EAAE,GAAG,UAAU,GAAIN,SAAS,GACzB,CAAE,CAAEM,EAAE,IAAI,EAAE,GAAKD,EAAE,KAAK,EAAG,IAAI,UAAU,GAAIL,SAAS,MAAM,EAAG;IACxEK,EAAE,IAAIC,EAAE,KAAK,CAAC;IACdD,EAAE,GAAKA,EAAE,GAAG,UAAU,GAAIL,SAAS,GAAMK,EAAE,GAAG,MAAM,GAAIJ,QAAS;IACjEK,EAAE,GACIA,EAAE,GAAG,UAAU,GAAIN,SAAS,GACzB,CAAE,CAAEM,EAAE,IAAI,EAAE,GAAKD,EAAE,KAAK,EAAG,IAAI,UAAU,GAAIL,SAAS,MAAM,EAAG;IACxEK,EAAE,IAAIC,EAAE,KAAK,CAAC;IACd,OAAQ,CAACD,EAAE,KAAK,CAAC,EAAE4B,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAC5C,CAAC5B,EAAE,KAAK,CAAC,EAAE2B,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAChD;AACJ;AACA,SAAShC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}