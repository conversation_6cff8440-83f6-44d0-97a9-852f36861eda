{"ast": null, "code": "/* Copyright 2022 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar _FreeTextEditor_instances, _a, _FreeTextEditor_color, _FreeTextEditor_content, _FreeTextEditor_editorDivId, _FreeTextEditor_editModeAC, _FreeTextEditor_fontSize, _FreeTextEditor_initialData, _FreeTextEditor_updateFontSize, _FreeTextEditor_updateColor, _FreeTextEditor_extractText, _FreeTextEditor_setEditorDimensions, _FreeTextEditor_getNodeContent, _FreeTextEditor_setContent, _FreeTextEditor_serializeContent, _FreeTextEditor_deserializeContent, _FreeTextEditor_hasElementChanged;\nimport { __classPrivateFieldGet, __classPrivateFieldSet } from \"tslib\";\n/** @typedef {import(\"./annotation_editor_layer.js\").AnnotationEditorLayer} AnnotationEditorLayer */\nimport { AnnotationEditorParamsType, AnnotationEditorType, Util } from \"pdfjs-dist/legacy/build/pdf.mjs\";\n// import {\n//     AnnotationEditorUIManager,\n//     bindEvents,\n//     KeyboardManager,\n// } from \"./tools.js\";\nimport { AnnotationEditor } from \"./annotation-editor\";\nimport { bindEvents } from \"../helpers/tools\";\nimport { FreeTextAnnotationElement } from \"../annotation-layer\";\n// import { FreeTextAnnotationElement } from \"../annotation-layer\";\nconst EOL_PATTERN = /\\r\\n?|\\n/g;\nclass FreeTextEditor extends AnnotationEditor {\n  constructor(params) {\n    super(Object.assign(Object.assign({}, params), {\n      name: \"k-free-text-editor freeTextEditor\"\n    }));\n    _FreeTextEditor_instances.add(this);\n    // todo: props\n    this.editorDiv = null;\n    this.overlayDiv = null;\n    // todo: props\n    _FreeTextEditor_color.set(this, void 0);\n    _FreeTextEditor_content.set(this, \"\");\n    _FreeTextEditor_editorDivId.set(this, `${this.id}-editor`);\n    _FreeTextEditor_editModeAC.set(this, null);\n    _FreeTextEditor_fontSize.set(this, void 0);\n    _FreeTextEditor_initialData.set(this, null);\n    __classPrivateFieldSet(this, _FreeTextEditor_color, params.color || _a._defaultColor || AnnotationEditor._defaultLineColor, \"f\");\n    __classPrivateFieldSet(this, _FreeTextEditor_fontSize, params.fontSize || _a._defaultFontSize, \"f\");\n  }\n  /** @inheritdoc */\n  static initialize(l10n, uiManager) {\n    AnnotationEditor.initialize(l10n, uiManager, {\n      strings: [\"pdfjs-free-text-default-content\"]\n    });\n    const style = getComputedStyle(document.documentElement);\n    // if (typeof PDFJSDev === \"undefined\" || PDFJSDev.test(\"TESTING\")) {\n    //     const lineHeight = parseFloat(\n    //         style.getPropertyValue(\"--freetext-line-height\")\n    //     );\n    //     assert(\n    //         lineHeight === LINE_FACTOR,\n    //         \"Update the CSS variable to agree with the constant.\"\n    //     );\n    // }\n    // todo: read the variable\n    this._internalPadding = parseFloat(style.getPropertyValue(\"--freetext-padding\")) || 2;\n  }\n  /** @inheritdoc */\n  static updateDefaultParams(type, value) {\n    switch (type) {\n      case AnnotationEditorParamsType.FREETEXT_SIZE:\n        _a._defaultFontSize = value;\n        break;\n      case AnnotationEditorParamsType.FREETEXT_COLOR:\n        _a._defaultColor = value;\n        break;\n      default:\n        break;\n    }\n  }\n  /** @inheritdoc */\n  updateParams(type, value) {\n    switch (type) {\n      case AnnotationEditorParamsType.FREETEXT_SIZE:\n        __classPrivateFieldGet(this, _FreeTextEditor_instances, \"m\", _FreeTextEditor_updateFontSize).call(this, value);\n        break;\n      case AnnotationEditorParamsType.FREETEXT_COLOR:\n        __classPrivateFieldGet(this, _FreeTextEditor_instances, \"m\", _FreeTextEditor_updateColor).call(this, value);\n        break;\n      default:\n        break;\n    }\n  }\n  /** @inheritdoc */\n  static get defaultPropertiesToUpdate() {\n    return [[AnnotationEditorParamsType.FREETEXT_SIZE, _a._defaultFontSize], [AnnotationEditorParamsType.FREETEXT_COLOR, _a._defaultColor || AnnotationEditor._defaultLineColor]];\n  }\n  /** @inheritdoc */\n  get propertiesToUpdate() {\n    return [[AnnotationEditorParamsType.FREETEXT_SIZE, __classPrivateFieldGet(this, _FreeTextEditor_fontSize, \"f\")], [AnnotationEditorParamsType.FREETEXT_COLOR, __classPrivateFieldGet(this, _FreeTextEditor_color, \"f\")]];\n  }\n  /**\n   * Helper to translate the editor with the keyboard when it's empty.\n   * @param {number} x in page units.\n   * @param {number} y in page units.\n   */\n  _translateEmpty(x, y) {\n    this._uiManager.translateSelectedEditors(x, y, /* noCommit = */true);\n  }\n  getInitialTranslation() {\n    // The start of the base line is where the user clicked.\n    const scale = this.parentScale;\n    return [-_a._internalPadding * scale, -(_a._internalPadding + __classPrivateFieldGet(this, _FreeTextEditor_fontSize, \"f\")) * scale];\n  }\n  /** @inheritdoc */\n  rebuild() {\n    if (!this.parent) {\n      return;\n    }\n    super.rebuild();\n    if (this.div === null) {\n      return;\n    }\n    if (!this.isAttachedToDOM) {\n      // At some point this editor was removed and we're rebuilting it,\n      // hence we must add it to its parent.\n      this.parent.add(this);\n    }\n  }\n  enableEditMode() {\n    if (this.isInEditMode()) {\n      return;\n    }\n    this.parent.setEditingState(false);\n    this.parent.updateToolbar(AnnotationEditorType.FREETEXT);\n    super.enableEditMode();\n    this.overlayDiv.classList.remove(\"enabled\");\n    this.editorDiv.contentEditable = true;\n    this._isDraggable = false;\n    this.div.removeAttribute(\"aria-activedescendant\");\n    // if (typeof PDFJSDev === \"undefined\" || PDFJSDev.test(\"TESTING\")) {\n    //     assert(\n    //         !this.#editModeAC,\n    //         \"No `this.#editModeAC` AbortController should exist.\"\n    //     );\n    // }\n    __classPrivateFieldSet(this, _FreeTextEditor_editModeAC, new AbortController(), \"f\");\n    const signal = this._uiManager.combinedSignal(__classPrivateFieldGet(this, _FreeTextEditor_editModeAC, \"f\"));\n    this.editorDiv.addEventListener(\"keydown\", this.editorDivKeydown.bind(this), {\n      signal\n    });\n    this.editorDiv.addEventListener(\"focus\", this.editorDivFocus.bind(this), {\n      signal\n    });\n    this.editorDiv.addEventListener(\"blur\", this.editorDivBlur.bind(this), {\n      signal\n    });\n    this.editorDiv.addEventListener(\"input\", this.editorDivInput.bind(this), {\n      signal\n    });\n    this.editorDiv.addEventListener(\"paste\", this.editorDivPaste.bind(this), {\n      signal\n    });\n  }\n  disableEditMode() {\n    var _b;\n    if (!this.isInEditMode()) {\n      return;\n    }\n    this.parent.setEditingState(true);\n    super.disableEditMode();\n    // this.overlayDiv.classList.add(\"enabled\");\n    this.editorDiv.contentEditable = false;\n    this.div.setAttribute(\"aria-activedescendant\", __classPrivateFieldGet(this, _FreeTextEditor_editorDivId, \"f\"));\n    this._isDraggable = true;\n    (_b = __classPrivateFieldGet(this, _FreeTextEditor_editModeAC, \"f\")) === null || _b === void 0 ? void 0 : _b.abort();\n    __classPrivateFieldSet(this, _FreeTextEditor_editModeAC, null, \"f\");\n    // On Chrome, the focus is given to <body> when contentEditable is set to\n    // false, hence we focus the div.\n    this.div.focus({\n      preventScroll: true /* See issue #15744 */\n    });\n    // In case the blur callback hasn't been called.\n    this.isEditing = false;\n    // this.parent.div.classList.add(\"freetextEditing\");\n  }\n  focusin(event) {\n    var _b;\n    if (!this._focusEventsAllowed) {\n      return;\n    }\n    if (((_b = this._uiManager) === null || _b === void 0 ? void 0 : _b.getMode()) !== AnnotationEditorType.FREETEXT) {\n      // prevent focusing the freetext editor while in highlight mode\n      // in pdf.js this situation does free highlight annotation isntead\n      // which is not currently implemented\n      return;\n    }\n    super.focusin(event);\n    if (event.target !== this.editorDiv) {\n      this.editorDiv.focus();\n    }\n  }\n  /** @inheritdoc */\n  onceAdded() {\n    var _b;\n    if (this.width) {\n      // The editor was created in using ctrl+c.\n      return;\n    }\n    this.enableEditMode();\n    this.editorDiv.focus();\n    if ((_b = this._initialOptions) === null || _b === void 0 ? void 0 : _b.isCentered) {\n      this.center();\n    }\n    this._initialOptions = null;\n  }\n  /** @inheritdoc */\n  isEmpty() {\n    var _b, _c;\n    return !this.editorDiv || ((_c = (_b = this.editorDiv) === null || _b === void 0 ? void 0 : _b.innerText) === null || _c === void 0 ? void 0 : _c.trim()) === \"\";\n  }\n  /** @inheritdoc */\n  remove() {\n    this.isEditing = false;\n    if (this.parent) {\n      this.parent.setEditingState(true);\n      // this.parent.div.classList.add(\"freetextEditing\");\n    }\n    super.remove();\n  }\n  /**\n   * Commit the content we have in this editor.\n   * @returns {undefined}\n   */\n  commit() {\n    if (!this.isInEditMode()) {\n      return;\n    }\n    super.commit();\n    this.disableEditMode();\n    const savedText = __classPrivateFieldGet(this, _FreeTextEditor_content, \"f\");\n    const newText = __classPrivateFieldSet(this, _FreeTextEditor_content, __classPrivateFieldGet(this, _FreeTextEditor_instances, \"m\", _FreeTextEditor_extractText).call(this).trimEnd(), \"f\");\n    if (savedText === newText) {\n      return;\n    }\n    const setText = text => {\n      __classPrivateFieldSet(this, _FreeTextEditor_content, text, \"f\");\n      if (!text) {\n        this.remove();\n        return;\n      }\n      __classPrivateFieldGet(this, _FreeTextEditor_instances, \"m\", _FreeTextEditor_setContent).call(this);\n      this._uiManager.rebuild(this);\n      __classPrivateFieldGet(this, _FreeTextEditor_instances, \"m\", _FreeTextEditor_setEditorDimensions).call(this);\n    };\n    this.addCommands({\n      cmd: () => {\n        setText(newText);\n      },\n      undo: () => {\n        setText(savedText);\n      },\n      mustExec: false\n    });\n    __classPrivateFieldGet(this, _FreeTextEditor_instances, \"m\", _FreeTextEditor_setEditorDimensions).call(this);\n  }\n  /** @inheritdoc */\n  shouldGetKeyboardEvents() {\n    return this.isInEditMode();\n  }\n  /** @inheritdoc */\n  enterInEditMode() {\n    this.enableEditMode();\n    this.editorDiv.focus();\n  }\n  /**\n   * ondblclick callback.\n   * @param {MouseEvent} event\n   */\n  dblclick(event) {\n    if (!event) {\n      return;\n    }\n    this.enterInEditMode();\n  }\n  /**\n   * onkeydown callback.\n   * @param {KeyboardEvent} event\n   */\n  keydown(event) {\n    if (event.target === this.div && event.key === \"Enter\") {\n      this.enterInEditMode();\n      // Avoid to add an unwanted new line.\n      event.preventDefault();\n    }\n  }\n  editorDivKeydown() {\n    // FreeTextEditor._keyboardManager.exec(this, event);\n  }\n  editorDivFocus() {\n    this.isEditing = true;\n  }\n  editorDivBlur() {\n    this.isEditing = false;\n  }\n  editorDivInput() {\n    // this.parent.div.classList.toggle(\"freetextEditing\", this.isEmpty());\n  }\n  /** @inheritdoc */\n  disableEditing() {\n    this.editorDiv.setAttribute(\"role\", \"comment\");\n    this.editorDiv.removeAttribute(\"aria-multiline\");\n  }\n  /** @inheritdoc */\n  enableEditing() {\n    this.editorDiv.setAttribute(\"role\", \"textbox\");\n    this.editorDiv.setAttribute(\"aria-multiline\", true);\n  }\n  /** @inheritdoc */\n  render() {\n    var _b;\n    if (this.div) {\n      return this.div;\n    }\n    let baseX, baseY;\n    if (this.width) {\n      baseX = this.x;\n      baseY = this.y;\n    }\n    super.render();\n    this.editorDiv = document.createElement(\"div\");\n    this.editorDiv.className = \"k-internal internal\";\n    // todo: move to CSS\n    // this.editorDiv.style.position = \"absolute\";\n    // this.div.style.position = \"absolute\";\n    this.editorDiv.setAttribute(\"id\", __classPrivateFieldGet(this, _FreeTextEditor_editorDivId, \"f\"));\n    this.editorDiv.setAttribute(\"data-l10n-id\", \"pdfjs-free-text\");\n    this.enableEditing();\n    // AnnotationEditor._l10nPromise\n    //     .get(\"pdfjs-free-text-default-content\")\n    //     .then(msg => this.editorDiv?.setAttribute(\"default-content\", msg));\n    // todo: fix localization\n    (_b = this.editorDiv) === null || _b === void 0 ? void 0 : _b.setAttribute(\"default-content\", this._uiManager.pdfViewer.options.messages.freeTextEditorPlaceholder);\n    this.editorDiv.contentEditable = true;\n    const {\n      style\n    } = this.editorDiv;\n    style.fontSize = `calc(${__classPrivateFieldGet(this, _FreeTextEditor_fontSize, \"f\")}px * var(--scale-factor))`;\n    style.color = __classPrivateFieldGet(this, _FreeTextEditor_color, \"f\");\n    this.div.append(this.editorDiv);\n    this.overlayDiv = document.createElement(\"div\");\n    // this.overlayDiv.classList.add(\"overlay\", \"enabled\");\n    this.div.append(this.overlayDiv);\n    bindEvents(this, this.div, [\"dblclick\", \"keydown\"]);\n    if (this.width) {\n      // This editor was created in using copy (ctrl+c).\n      const [parentWidth, parentHeight] = this.parentDimensions;\n      if (this.annotationElementId) {\n        // This stuff is hard to test: if something is changed here, please\n        // test with the following PDF file:\n        //  - freetexts.pdf\n        //  - rotated_freetexts.pdf\n        // Only small variations between the original annotation and its editor\n        // are allowed.\n        // position is the position of the first glyph in the annotation\n        // and it's relative to its container.\n        const {\n          position\n        } = __classPrivateFieldGet(this, _FreeTextEditor_initialData, \"f\");\n        let [tx, ty] = this.getInitialTranslation();\n        [tx, ty] = this.pageTranslationToScreen(tx, ty);\n        const [pageWidth, pageHeight] = this.pageDimensions;\n        const [pageX, pageY] = this.pageTranslation;\n        let posX, posY;\n        switch (this.rotation) {\n          case 0:\n            posX = baseX + (position[0] - pageX) / pageWidth;\n            posY = baseY + this.height - (position[1] - pageY) / pageHeight;\n            break;\n          case 90:\n            posX = baseX + (position[0] - pageX) / pageWidth;\n            posY = baseY - (position[1] - pageY) / pageHeight;\n            [tx, ty] = [ty, -tx];\n            break;\n          case 180:\n            posX = baseX - this.width + (position[0] - pageX) / pageWidth;\n            posY = baseY - (position[1] - pageY) / pageHeight;\n            [tx, ty] = [-tx, -ty];\n            break;\n          case 270:\n            posX = baseX + (position[0] - pageX - this.height * pageHeight) / pageWidth;\n            posY = baseY + (position[1] - pageY - this.width * pageWidth) / pageHeight;\n            [tx, ty] = [-ty, tx];\n            break;\n          default:\n            break;\n        }\n        this.setAt(posX * parentWidth, posY * parentHeight, tx, ty);\n      } else {\n        this.setAt(baseX * parentWidth, baseY * parentHeight, this.width * parentWidth, this.height * parentHeight);\n      }\n      __classPrivateFieldGet(this, _FreeTextEditor_instances, \"m\", _FreeTextEditor_setContent).call(this);\n      this._isDraggable = true;\n      this.editorDiv.contentEditable = false;\n    } else {\n      this._isDraggable = false;\n      this.editorDiv.contentEditable = true;\n    }\n    // if (typeof PDFJSDev !== \"undefined\" && PDFJSDev.test(\"TESTING\")) {\n    //     this.div.setAttribute(\"annotation-id\", this.annotationElementId);\n    // }\n    return this.div;\n  }\n  editorDivPaste(event) {\n    // @ts-expect-error TS(2551):\n    const clipboardData = event.clipboardData || window.clipboardData;\n    const {\n      types\n    } = clipboardData;\n    if (types.length === 1 && types[0] === \"text/plain\") {\n      return;\n    }\n    event.preventDefault();\n    const paste = __classPrivateFieldGet(_a, _a, \"m\", _FreeTextEditor_deserializeContent).call(_a, clipboardData.getData(\"text\") || \"\").replaceAll(EOL_PATTERN, \"\\n\");\n    if (!paste) {\n      return;\n    }\n    const selection = window.getSelection();\n    if (!selection.rangeCount) {\n      return;\n    }\n    this.editorDiv.normalize();\n    selection.deleteFromDocument();\n    const range = selection.getRangeAt(0);\n    if (!paste.includes(\"\\n\")) {\n      range.insertNode(document.createTextNode(paste));\n      this.editorDiv.normalize();\n      selection.collapseToStart();\n      return;\n    }\n    // Collect the text before and after the caret.\n    const {\n      startContainer,\n      startOffset\n    } = range;\n    const bufferBefore = [];\n    const bufferAfter = [];\n    if (startContainer.nodeType === Node.TEXT_NODE) {\n      const parent = startContainer.parentElement;\n      bufferAfter.push(\n      // @ts-expect-error TS(2556):\n      startContainer.nodeValue.slice(startOffset).replaceAll(EOL_PATTERN, \"\"));\n      if (parent !== this.editorDiv) {\n        let buffer = bufferBefore;\n        for (const child of this.editorDiv.childNodes) {\n          if (child === parent) {\n            buffer = bufferAfter;\n            continue;\n          }\n          buffer.push(__classPrivateFieldGet(_a, _a, \"m\", _FreeTextEditor_getNodeContent).call(_a, child));\n        }\n      }\n      bufferBefore.push(startContainer.nodeValue.slice(0, startOffset)\n      // @ts-expect-error TS(2556):\n      .replaceAll(EOL_PATTERN, \"\"));\n    } else if (startContainer === this.editorDiv) {\n      let buffer = bufferBefore;\n      let i = 0;\n      for (const child of this.editorDiv.childNodes) {\n        if (i++ === startOffset) {\n          buffer = bufferAfter;\n        }\n        buffer.push(__classPrivateFieldGet(_a, _a, \"m\", _FreeTextEditor_getNodeContent).call(_a, child));\n      }\n    }\n    __classPrivateFieldSet(this, _FreeTextEditor_content, `${bufferBefore.join(\"\\n\")}${paste}${bufferAfter.join(\"\\n\")}`, \"f\");\n    __classPrivateFieldGet(this, _FreeTextEditor_instances, \"m\", _FreeTextEditor_setContent).call(this);\n    // Set the caret at the right position.\n    const newRange = new Range();\n    let beforeLength = bufferBefore.reduce((acc, line) => acc + line.length, 0);\n    for (const {\n      firstChild\n    } of this.editorDiv.childNodes) {\n      // Each child is either a div with a text node or a br element.\n      if (firstChild.nodeType === Node.TEXT_NODE) {\n        const length = firstChild.nodeValue.length;\n        if (beforeLength <= length) {\n          newRange.setStart(firstChild, beforeLength);\n          newRange.setEnd(firstChild, beforeLength);\n          break;\n        }\n        beforeLength -= length;\n      }\n    }\n    selection.removeAllRanges();\n    selection.addRange(newRange);\n  }\n  /** @inheritdoc */\n  get contentDiv() {\n    return this.editorDiv;\n  }\n  /** @inheritdoc */\n  static deserialize(data, parent, uiManager) {\n    let initialData = null;\n    if (data instanceof FreeTextAnnotationElement) {\n      const {\n        data: {\n          defaultAppearanceData: {\n            fontSize,\n            fontColor\n          },\n          rect,\n          rotation,\n          id\n        },\n        textContent,\n        textPosition,\n        parent: {\n          page: {\n            pageNumber\n          }\n        }\n      } = data;\n      // textContent is supposed to be an array of strings containing each line\n      // of text. However, it can be null or empty.\n      if (!textContent || textContent.length === 0) {\n        // Empty annotation.\n        return null;\n      }\n      initialData = data = {\n        annotationType: AnnotationEditorType.FREETEXT,\n        color: Array.from(fontColor),\n        fontSize,\n        value: textContent.join(\"\\n\"),\n        position: textPosition,\n        pageIndex: pageNumber - 1,\n        rect: rect.slice(0),\n        rotation,\n        id,\n        deleted: false\n      };\n    }\n    const editor = super.deserialize(data, parent, uiManager);\n    __classPrivateFieldSet(editor, _FreeTextEditor_fontSize, data.fontSize, \"f\");\n    // @ts-expect-error TS(2556):\n    __classPrivateFieldSet(editor, _FreeTextEditor_color, Util.makeHexColor(...data.color), \"f\");\n    __classPrivateFieldSet(editor, _FreeTextEditor_content, __classPrivateFieldGet(_a, _a, \"m\", _FreeTextEditor_deserializeContent).call(_a, data.value), \"f\");\n    editor.annotationElementId = data.id || null;\n    __classPrivateFieldSet(editor, _FreeTextEditor_initialData, initialData, \"f\");\n    return editor;\n  }\n  /** @inheritdoc */\n  serialize(isForCopying = false) {\n    if (this.isEmpty()) {\n      return null;\n    }\n    if (this.deleted) {\n      return {\n        pageIndex: this.pageIndex,\n        id: this.annotationElementId,\n        deleted: true\n      };\n    }\n    const padding = _a._internalPadding * this.parentScale;\n    const rect = this.getRect(padding, padding);\n    const color = AnnotationEditor._colorManager.convert(this.isAttachedToDOM ? getComputedStyle(this.editorDiv).color : __classPrivateFieldGet(this, _FreeTextEditor_color, \"f\"));\n    const serialized = {\n      annotationType: AnnotationEditorType.FREETEXT,\n      color,\n      fontSize: __classPrivateFieldGet(this, _FreeTextEditor_fontSize, \"f\"),\n      value: __classPrivateFieldGet(this, _FreeTextEditor_instances, \"m\", _FreeTextEditor_serializeContent).call(this),\n      pageIndex: this.pageIndex,\n      rect,\n      rotation: this.rotation,\n      structTreeParentId: this._structTreeParentId\n    };\n    if (isForCopying) {\n      // Don't add the id when copying because the pasted editor mustn't be\n      // linked to an existing annotation.\n      return serialized;\n    }\n    if (this.annotationElementId && !__classPrivateFieldGet(this, _FreeTextEditor_instances, \"m\", _FreeTextEditor_hasElementChanged).call(this, serialized)) {\n      return null;\n    }\n    // @ts-expect-error TS(2556):\n    serialized.id = this.annotationElementId;\n    return serialized;\n  }\n  /** @inheritdoc */\n  renderAnnotationElement(annotation) {\n    const content = super.renderAnnotationElement(annotation);\n    if (this.deleted) {\n      return content;\n    }\n    const {\n      style\n    } = content;\n    style.fontSize = `calc(${__classPrivateFieldGet(this, _FreeTextEditor_fontSize, \"f\")}px * var(--scale-factor))`;\n    style.color = __classPrivateFieldGet(this, _FreeTextEditor_color, \"f\");\n    content.replaceChildren();\n    for (const line of __classPrivateFieldGet(this, _FreeTextEditor_content, \"f\").split(\"\\n\")) {\n      const div = document.createElement(\"div\");\n      div.append(line ? document.createTextNode(line) : document.createElement(\"br\"));\n      content.append(div);\n    }\n    const padding = _a._internalPadding * this.parentScale;\n    annotation.updateEdited({\n      rect: this.getRect(padding, padding),\n      popupContent: __classPrivateFieldGet(this, _FreeTextEditor_content, \"f\")\n    });\n    return content;\n  }\n  resetAnnotationElement(annotation) {\n    super.resetAnnotationElement(annotation);\n    annotation.resetEdited();\n  }\n  // todo: this is necessary\n  // saveDocument() has checks that test \"editor instanceof AnnotationEditor\", but they fail\n  // because AnnotationEditor from \"pdfjs-dist/legacy/build/pdf.mjs\" is not exported\n  // thus replace instances of editors with their serialized version\n  toJSON() {\n    const data = this.serialize();\n    return data;\n  }\n}\n_a = FreeTextEditor, _FreeTextEditor_color = new WeakMap(), _FreeTextEditor_content = new WeakMap(), _FreeTextEditor_editorDivId = new WeakMap(), _FreeTextEditor_editModeAC = new WeakMap(), _FreeTextEditor_fontSize = new WeakMap(), _FreeTextEditor_initialData = new WeakMap(), _FreeTextEditor_instances = new WeakSet(), _FreeTextEditor_updateFontSize = function _FreeTextEditor_updateFontSize(fontSize) {\n  const setFontsize = size => {\n    this.editorDiv.style.fontSize = `calc(${size}px * var(--scale-factor))`;\n    this.translate(0, -(size - __classPrivateFieldGet(this, _FreeTextEditor_fontSize, \"f\")) * this.parentScale);\n    __classPrivateFieldSet(this, _FreeTextEditor_fontSize, size, \"f\");\n    __classPrivateFieldGet(this, _FreeTextEditor_instances, \"m\", _FreeTextEditor_setEditorDimensions).call(this);\n  };\n  const savedFontsize = __classPrivateFieldGet(this, _FreeTextEditor_fontSize, \"f\");\n  this.addCommands({\n    cmd: setFontsize.bind(this, fontSize),\n    undo: setFontsize.bind(this, savedFontsize),\n    post: this._uiManager.updateUI.bind(this._uiManager, this),\n    mustExec: true,\n    type: AnnotationEditorParamsType.FREETEXT_SIZE,\n    overwriteIfSameType: true,\n    keepUndo: true\n  });\n}, _FreeTextEditor_updateColor = function _FreeTextEditor_updateColor(color) {\n  const setColor = col => {\n    __classPrivateFieldSet(this, _FreeTextEditor_color, this.editorDiv.style.color = col, \"f\");\n  };\n  const savedColor = __classPrivateFieldGet(this, _FreeTextEditor_color, \"f\");\n  this.addCommands({\n    cmd: setColor.bind(this, color),\n    undo: setColor.bind(this, savedColor),\n    post: this._uiManager.updateUI.bind(this._uiManager, this),\n    mustExec: true,\n    type: AnnotationEditorParamsType.FREETEXT_COLOR,\n    overwriteIfSameType: true,\n    keepUndo: true\n  });\n}, _FreeTextEditor_extractText = function _FreeTextEditor_extractText() {\n  // We don't use innerText because there are some bugs with line breaks.\n  const buffer = [];\n  this.editorDiv.normalize();\n  for (const child of this.editorDiv.childNodes) {\n    buffer.push(__classPrivateFieldGet(_a, _a, \"m\", _FreeTextEditor_getNodeContent).call(_a, child));\n  }\n  return buffer.join(\"\\n\");\n}, _FreeTextEditor_setEditorDimensions = function _FreeTextEditor_setEditorDimensions() {\n  const [parentWidth, parentHeight] = this.parentDimensions;\n  let rect;\n  if (this.isAttachedToDOM) {\n    rect = this.div.getBoundingClientRect();\n  } else {\n    // This editor isn't on screen but we need to get its dimensions, so\n    // we just insert it in the DOM, get its bounding box and then remove it.\n    const {\n      currentLayer,\n      div\n    } = this;\n    const savedDisplay = div.style.display;\n    const savedVisibility = div.classList.contains(\"hidden\") || div.classList.contains(\"k-hidden\");\n    div.classList.remove(\"hidden\");\n    div.classList.remove(\"k-hidden\");\n    div.style.display = \"hidden\";\n    currentLayer.div.append(this.div);\n    rect = div.getBoundingClientRect();\n    div.remove();\n    div.style.display = savedDisplay;\n    // div.classList.toggle(\"hidden\", savedVisibility);\n    div.classList.toggle(\"k-hidden\", savedVisibility);\n  }\n  // The dimensions are relative to the rotation of the page, hence we need to\n  // take that into account (see issue #16636).\n  if (this.rotation % 180 === this.parentRotation % 180) {\n    this.width = rect.width / parentWidth;\n    this.height = rect.height / parentHeight;\n  } else {\n    this.width = rect.height / parentWidth;\n    this.height = rect.width / parentHeight;\n  }\n  this.fixAndSetPosition();\n}, _FreeTextEditor_getNodeContent = function _FreeTextEditor_getNodeContent(node) {\n  return (node.nodeType === Node.TEXT_NODE ? node.nodeValue : node.innerText).replaceAll(EOL_PATTERN, \"\");\n}, _FreeTextEditor_setContent = function _FreeTextEditor_setContent() {\n  this.editorDiv.replaceChildren();\n  if (!__classPrivateFieldGet(this, _FreeTextEditor_content, \"f\")) {\n    return;\n  }\n  for (const line of __classPrivateFieldGet(this, _FreeTextEditor_content, \"f\").split(\"\\n\")) {\n    const div = document.createElement(\"div\");\n    div.append(line ? document.createTextNode(line) : document.createElement(\"br\"));\n    this.editorDiv.append(div);\n  }\n}, _FreeTextEditor_serializeContent = function _FreeTextEditor_serializeContent() {\n  // @ts-expect-error TS(2556):\n  return __classPrivateFieldGet(this, _FreeTextEditor_content, \"f\").replaceAll(\"\\xa0\", \" \");\n}, _FreeTextEditor_deserializeContent = function _FreeTextEditor_deserializeContent(content) {\n  return content.replaceAll(\" \", \"\\xa0\");\n}, _FreeTextEditor_hasElementChanged = function _FreeTextEditor_hasElementChanged(serialized) {\n  const {\n    value,\n    fontSize,\n    color,\n    pageIndex\n  } = __classPrivateFieldGet(this, _FreeTextEditor_initialData, \"f\");\n  return this._hasBeenMoved || serialized.value !== value || serialized.fontSize !== fontSize || serialized.color.some((c, i) => c !== color[i]) || serialized.pageIndex !== pageIndex;\n};\nFreeTextEditor._freeTextDefaultContent = \"\";\nFreeTextEditor._internalPadding = 0;\n// static _defaultColor = null;\nFreeTextEditor._defaultColor = \"#000000\";\nFreeTextEditor._defaultFontSize = 10;\n// static get _keyboardManager() {\n//     const proto = FreeTextEditor.prototype;\n//     const arrowChecker = self => self.isEmpty();\n//     const small = AnnotationEditorUIManager.TRANSLATE_SMALL;\n//     const big = AnnotationEditorUIManager.TRANSLATE_BIG;\n//     return shadow(\n//         this,\n//         \"_keyboardManager\",\n//         new KeyboardManager([\n//             [\n//                 // Commit the text in case the user use ctrl+s to save the document.\n//                 // The event must bubble in order to be caught by the viewer.\n//                 // See bug 1831574.\n//                 [\"ctrl+s\", \"mac+meta+s\", \"ctrl+p\", \"mac+meta+p\"],\n//                 proto.commitOrRemove,\n//                 { bubbles: true },\n//             ],\n//             [\n//                 [\"ctrl+Enter\", \"mac+meta+Enter\", \"Escape\", \"mac+Escape\"],\n//                 proto.commitOrRemove,\n//             ],\n//             [\n//                 [\"ArrowLeft\", \"mac+ArrowLeft\"],\n//                 proto._translateEmpty,\n//                 { args: [-small, 0], checker: arrowChecker },\n//             ],\n//             [\n//                 [\"ctrl+ArrowLeft\", \"mac+shift+ArrowLeft\"],\n//                 proto._translateEmpty,\n//                 { args: [-big, 0], checker: arrowChecker },\n//             ],\n//             [\n//                 [\"ArrowRight\", \"mac+ArrowRight\"],\n//                 proto._translateEmpty,\n//                 { args: [small, 0], checker: arrowChecker },\n//             ],\n//             [\n//                 [\"ctrl+ArrowRight\", \"mac+shift+ArrowRight\"],\n//                 proto._translateEmpty,\n//                 { args: [big, 0], checker: arrowChecker },\n//             ],\n//             [\n//                 [\"ArrowUp\", \"mac+ArrowUp\"],\n//                 proto._translateEmpty,\n//                 { args: [0, -small], checker: arrowChecker },\n//             ],\n//             [\n//                 [\"ctrl+ArrowUp\", \"mac+shift+ArrowUp\"],\n//                 proto._translateEmpty,\n//                 { args: [0, -big], checker: arrowChecker },\n//             ],\n//             [\n//                 [\"ArrowDown\", \"mac+ArrowDown\"],\n//                 proto._translateEmpty,\n//                 { args: [0, small], checker: arrowChecker },\n//             ],\n//             [\n//                 [\"ctrl+ArrowDown\", \"mac+shift+ArrowDown\"],\n//                 proto._translateEmpty,\n//                 { args: [0, big], checker: arrowChecker },\n//             ],\n//         ])\n//     );\n// }\nFreeTextEditor._type = \"freetext\";\nFreeTextEditor._editorType = AnnotationEditorType.FREETEXT;\nexport { FreeTextEditor };", "map": {"version": 3, "names": ["_FreeTextEditor_instances", "_a", "_FreeTextEditor_color", "_FreeTextEditor_content", "_FreeTextEditor_editorDivId", "_FreeTextEditor_editModeAC", "_FreeTextEditor_fontSize", "_FreeTextEditor_initialData", "_FreeTextEditor_updateFontSize", "_FreeTextEditor_updateColor", "_FreeTextEditor_extractText", "_FreeTextEditor_setEditorDimensions", "_FreeTextEditor_getNodeContent", "_FreeTextEditor_setContent", "_FreeTextEditor_serializeContent", "_FreeTextEditor_deserializeContent", "_FreeTextEditor_hasElementChanged", "__classPrivateFieldGet", "__classPrivateFieldSet", "AnnotationEditorParamsType", "AnnotationEditorType", "<PERSON><PERSON>", "AnnotationEditor", "bindEvents", "FreeTextAnnotationElement", "EOL_PATTERN", "FreeTextEditor", "constructor", "params", "Object", "assign", "name", "add", "editorD<PERSON>", "overlayDiv", "set", "id", "color", "_defaultColor", "_defaultLineColor", "fontSize", "_defaultFontSize", "initialize", "l10n", "uiManager", "strings", "style", "getComputedStyle", "document", "documentElement", "_internalPadding", "parseFloat", "getPropertyValue", "updateDefaultParams", "type", "value", "FREETEXT_SIZE", "FREETEXT_COLOR", "updateParams", "call", "defaultPropertiesToUpdate", "propertiesToUpdate", "_translateEmpty", "x", "y", "_uiManager", "translateSelectedEditors", "getInitialTranslation", "scale", "parentScale", "rebuild", "parent", "div", "isAttachedToDOM", "enableEditMode", "isInEditMode", "setEditingState", "updateToolbar", "FREETEXT", "classList", "remove", "contentEditable", "_isDraggable", "removeAttribute", "AbortController", "signal", "combinedSignal", "addEventListener", "editor<PERSON><PERSON><PERSON><PERSON><PERSON>", "bind", "editorDivFocus", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "editorDivInput", "editorDivPaste", "disableEditMode", "_b", "setAttribute", "abort", "focus", "preventScroll", "isEditing", "focusin", "event", "_focusEventsAllowed", "getMode", "target", "onceAdded", "width", "_initialOptions", "isCentered", "center", "isEmpty", "_c", "innerText", "trim", "commit", "savedText", "newText", "trimEnd", "setText", "text", "addCommands", "cmd", "undo", "mustExec", "shouldGetKeyboardEvents", "enterInEditMode", "dblclick", "keydown", "key", "preventDefault", "disableEditing", "enableEditing", "render", "baseX", "baseY", "createElement", "className", "pdfViewer", "options", "messages", "freeTextEditorPlaceholder", "append", "parentWidth", "parentHeight", "parentDimensions", "annotationElementId", "position", "tx", "ty", "pageTranslationToScreen", "pageWidth", "pageHeight", "pageDimensions", "pageX", "pageY", "pageTranslation", "posX", "posY", "rotation", "height", "setAt", "clipboardData", "window", "types", "length", "paste", "getData", "replaceAll", "selection", "getSelection", "rangeCount", "normalize", "deleteFromDocument", "range", "getRangeAt", "includes", "insertNode", "createTextNode", "collapseToStart", "startContainer", "startOffset", "bufferBefore", "bufferAfter", "nodeType", "Node", "TEXT_NODE", "parentElement", "push", "nodeValue", "slice", "buffer", "child", "childNodes", "i", "join", "newRange", "Range", "<PERSON><PERSON><PERSON><PERSON>", "reduce", "acc", "line", "<PERSON><PERSON><PERSON><PERSON>", "setStart", "setEnd", "removeAllRanges", "addRange", "contentDiv", "deserialize", "data", "initialData", "defaultAppearanceData", "fontColor", "rect", "textContent", "textPosition", "page", "pageNumber", "annotationType", "Array", "from", "pageIndex", "deleted", "editor", "makeHexColor", "serialize", "isForCopying", "padding", "getRect", "_colorManager", "convert", "serialized", "structTreeParentId", "_structTreeParentId", "renderAnnotationElement", "annotation", "content", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "split", "updateEdited", "popup<PERSON><PERSON>nt", "resetAnnotationElement", "resetEdited", "toJSON", "WeakMap", "WeakSet", "setFontsize", "size", "translate", "savedFontsize", "post", "updateUI", "overwriteIfSameType", "keepUndo", "setColor", "col", "savedColor", "getBoundingClientRect", "<PERSON><PERSON><PERSON><PERSON>", "savedDisplay", "display", "savedVisibility", "contains", "toggle", "parentRotation", "fixAndSetPosition", "node", "_hasBeenMoved", "some", "c", "_freeTextDefaultContent", "_type", "_editorType"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-pdfviewer-common/dist/es/annotations/editors/free-text-editor.js"], "sourcesContent": ["/* Copyright 2022 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar _FreeTextEditor_instances, _a, _FreeTextEditor_color, _FreeTextEditor_content, _FreeTextEditor_editorDivId, _FreeTextEditor_editModeAC, _FreeTextEditor_fontSize, _FreeTextEditor_initialData, _FreeTextEditor_updateFontSize, _FreeTextEditor_updateColor, _FreeTextEditor_extractText, _FreeTextEditor_setEditorDimensions, _FreeTextEditor_getNodeContent, _FreeTextEditor_setContent, _FreeTextEditor_serializeContent, _FreeTextEditor_deserializeContent, _FreeTextEditor_hasElementChanged;\nimport { __classPrivateFieldGet, __classPrivateFieldSet } from \"tslib\";\n/** @typedef {import(\"./annotation_editor_layer.js\").AnnotationEditorLayer} AnnotationEditorLayer */\nimport { AnnotationEditorParamsType, AnnotationEditorType, Util } from \"pdfjs-dist/legacy/build/pdf.mjs\";\n// import {\n//     AnnotationEditorUIManager,\n//     bindEvents,\n//     KeyboardManager,\n// } from \"./tools.js\";\nimport { AnnotationEditor } from \"./annotation-editor\";\nimport { bindEvents } from \"../helpers/tools\";\nimport { FreeTextAnnotationElement } from \"../annotation-layer\";\n// import { FreeTextAnnotationElement } from \"../annotation-layer\";\nconst EOL_PATTERN = /\\r\\n?|\\n/g;\nclass FreeTextEditor extends AnnotationEditor {\n    constructor(params) {\n        super(Object.assign(Object.assign({}, params), { name: \"k-free-text-editor freeTextEditor\" }));\n        _FreeTextEditor_instances.add(this);\n        // todo: props\n        this.editorDiv = null;\n        this.overlayDiv = null;\n        // todo: props\n        _FreeTextEditor_color.set(this, void 0);\n        _FreeTextEditor_content.set(this, \"\");\n        _FreeTextEditor_editorDivId.set(this, `${this.id}-editor`);\n        _FreeTextEditor_editModeAC.set(this, null);\n        _FreeTextEditor_fontSize.set(this, void 0);\n        _FreeTextEditor_initialData.set(this, null);\n        __classPrivateFieldSet(this, _FreeTextEditor_color, params.color ||\n            _a._defaultColor ||\n            AnnotationEditor._defaultLineColor, \"f\");\n        __classPrivateFieldSet(this, _FreeTextEditor_fontSize, params.fontSize || _a._defaultFontSize, \"f\");\n    }\n    /** @inheritdoc */\n    static initialize(l10n, uiManager) {\n        AnnotationEditor.initialize(l10n, uiManager, {\n            strings: [\"pdfjs-free-text-default-content\"]\n        });\n        const style = getComputedStyle(document.documentElement);\n        // if (typeof PDFJSDev === \"undefined\" || PDFJSDev.test(\"TESTING\")) {\n        //     const lineHeight = parseFloat(\n        //         style.getPropertyValue(\"--freetext-line-height\")\n        //     );\n        //     assert(\n        //         lineHeight === LINE_FACTOR,\n        //         \"Update the CSS variable to agree with the constant.\"\n        //     );\n        // }\n        // todo: read the variable\n        this._internalPadding = parseFloat(style.getPropertyValue(\"--freetext-padding\")) || 2;\n    }\n    /** @inheritdoc */\n    static updateDefaultParams(type, value) {\n        switch (type) {\n            case AnnotationEditorParamsType.FREETEXT_SIZE:\n                _a._defaultFontSize = value;\n                break;\n            case AnnotationEditorParamsType.FREETEXT_COLOR:\n                _a._defaultColor = value;\n                break;\n            default: break;\n        }\n    }\n    /** @inheritdoc */\n    updateParams(type, value) {\n        switch (type) {\n            case AnnotationEditorParamsType.FREETEXT_SIZE:\n                __classPrivateFieldGet(this, _FreeTextEditor_instances, \"m\", _FreeTextEditor_updateFontSize).call(this, value);\n                break;\n            case AnnotationEditorParamsType.FREETEXT_COLOR:\n                __classPrivateFieldGet(this, _FreeTextEditor_instances, \"m\", _FreeTextEditor_updateColor).call(this, value);\n                break;\n            default: break;\n        }\n    }\n    /** @inheritdoc */\n    static get defaultPropertiesToUpdate() {\n        return [\n            [\n                AnnotationEditorParamsType.FREETEXT_SIZE,\n                _a._defaultFontSize\n            ],\n            [\n                AnnotationEditorParamsType.FREETEXT_COLOR,\n                _a._defaultColor || AnnotationEditor._defaultLineColor\n            ]\n        ];\n    }\n    /** @inheritdoc */\n    get propertiesToUpdate() {\n        return [\n            [AnnotationEditorParamsType.FREETEXT_SIZE, __classPrivateFieldGet(this, _FreeTextEditor_fontSize, \"f\")],\n            [AnnotationEditorParamsType.FREETEXT_COLOR, __classPrivateFieldGet(this, _FreeTextEditor_color, \"f\")]\n        ];\n    }\n    /**\n     * Helper to translate the editor with the keyboard when it's empty.\n     * @param {number} x in page units.\n     * @param {number} y in page units.\n     */\n    _translateEmpty(x, y) {\n        this._uiManager.translateSelectedEditors(x, y, /* noCommit = */ true);\n    }\n    getInitialTranslation() {\n        // The start of the base line is where the user clicked.\n        const scale = this.parentScale;\n        return [\n            -_a._internalPadding * scale,\n            -(_a._internalPadding + __classPrivateFieldGet(this, _FreeTextEditor_fontSize, \"f\")) * scale\n        ];\n    }\n    /** @inheritdoc */\n    rebuild() {\n        if (!this.parent) {\n            return;\n        }\n        super.rebuild();\n        if (this.div === null) {\n            return;\n        }\n        if (!this.isAttachedToDOM) {\n            // At some point this editor was removed and we're rebuilting it,\n            // hence we must add it to its parent.\n            this.parent.add(this);\n        }\n    }\n    enableEditMode() {\n        if (this.isInEditMode()) {\n            return;\n        }\n        this.parent.setEditingState(false);\n        this.parent.updateToolbar(AnnotationEditorType.FREETEXT);\n        super.enableEditMode();\n        this.overlayDiv.classList.remove(\"enabled\");\n        this.editorDiv.contentEditable = true;\n        this._isDraggable = false;\n        this.div.removeAttribute(\"aria-activedescendant\");\n        // if (typeof PDFJSDev === \"undefined\" || PDFJSDev.test(\"TESTING\")) {\n        //     assert(\n        //         !this.#editModeAC,\n        //         \"No `this.#editModeAC` AbortController should exist.\"\n        //     );\n        // }\n        __classPrivateFieldSet(this, _FreeTextEditor_editModeAC, new AbortController(), \"f\");\n        const signal = this._uiManager.combinedSignal(__classPrivateFieldGet(this, _FreeTextEditor_editModeAC, \"f\"));\n        this.editorDiv.addEventListener(\"keydown\", this.editorDivKeydown.bind(this), { signal });\n        this.editorDiv.addEventListener(\"focus\", this.editorDivFocus.bind(this), {\n            signal\n        });\n        this.editorDiv.addEventListener(\"blur\", this.editorDivBlur.bind(this), {\n            signal\n        });\n        this.editorDiv.addEventListener(\"input\", this.editorDivInput.bind(this), {\n            signal\n        });\n        this.editorDiv.addEventListener(\"paste\", this.editorDivPaste.bind(this), {\n            signal\n        });\n    }\n    disableEditMode() {\n        var _b;\n        if (!this.isInEditMode()) {\n            return;\n        }\n        this.parent.setEditingState(true);\n        super.disableEditMode();\n        // this.overlayDiv.classList.add(\"enabled\");\n        this.editorDiv.contentEditable = false;\n        this.div.setAttribute(\"aria-activedescendant\", __classPrivateFieldGet(this, _FreeTextEditor_editorDivId, \"f\"));\n        this._isDraggable = true;\n        (_b = __classPrivateFieldGet(this, _FreeTextEditor_editModeAC, \"f\")) === null || _b === void 0 ? void 0 : _b.abort();\n        __classPrivateFieldSet(this, _FreeTextEditor_editModeAC, null, \"f\");\n        // On Chrome, the focus is given to <body> when contentEditable is set to\n        // false, hence we focus the div.\n        this.div.focus({\n            preventScroll: true /* See issue #15744 */\n        });\n        // In case the blur callback hasn't been called.\n        this.isEditing = false;\n        // this.parent.div.classList.add(\"freetextEditing\");\n    }\n    focusin(event) {\n        var _b;\n        if (!this._focusEventsAllowed) {\n            return;\n        }\n        if (((_b = this._uiManager) === null || _b === void 0 ? void 0 : _b.getMode()) !== AnnotationEditorType.FREETEXT) {\n            // prevent focusing the freetext editor while in highlight mode\n            // in pdf.js this situation does free highlight annotation isntead\n            // which is not currently implemented\n            return;\n        }\n        super.focusin(event);\n        if (event.target !== this.editorDiv) {\n            this.editorDiv.focus();\n        }\n    }\n    /** @inheritdoc */\n    onceAdded() {\n        var _b;\n        if (this.width) {\n            // The editor was created in using ctrl+c.\n            return;\n        }\n        this.enableEditMode();\n        this.editorDiv.focus();\n        if ((_b = this._initialOptions) === null || _b === void 0 ? void 0 : _b.isCentered) {\n            this.center();\n        }\n        this._initialOptions = null;\n    }\n    /** @inheritdoc */\n    isEmpty() {\n        var _b, _c;\n        return !this.editorDiv || ((_c = (_b = this.editorDiv) === null || _b === void 0 ? void 0 : _b.innerText) === null || _c === void 0 ? void 0 : _c.trim()) === \"\";\n    }\n    /** @inheritdoc */\n    remove() {\n        this.isEditing = false;\n        if (this.parent) {\n            this.parent.setEditingState(true);\n            // this.parent.div.classList.add(\"freetextEditing\");\n        }\n        super.remove();\n    }\n    /**\n     * Commit the content we have in this editor.\n     * @returns {undefined}\n     */\n    commit() {\n        if (!this.isInEditMode()) {\n            return;\n        }\n        super.commit();\n        this.disableEditMode();\n        const savedText = __classPrivateFieldGet(this, _FreeTextEditor_content, \"f\");\n        const newText = (__classPrivateFieldSet(this, _FreeTextEditor_content, __classPrivateFieldGet(this, _FreeTextEditor_instances, \"m\", _FreeTextEditor_extractText).call(this).trimEnd(), \"f\"));\n        if (savedText === newText) {\n            return;\n        }\n        const setText = text => {\n            __classPrivateFieldSet(this, _FreeTextEditor_content, text, \"f\");\n            if (!text) {\n                this.remove();\n                return;\n            }\n            __classPrivateFieldGet(this, _FreeTextEditor_instances, \"m\", _FreeTextEditor_setContent).call(this);\n            this._uiManager.rebuild(this);\n            __classPrivateFieldGet(this, _FreeTextEditor_instances, \"m\", _FreeTextEditor_setEditorDimensions).call(this);\n        };\n        this.addCommands({\n            cmd: () => {\n                setText(newText);\n            },\n            undo: () => {\n                setText(savedText);\n            },\n            mustExec: false\n        });\n        __classPrivateFieldGet(this, _FreeTextEditor_instances, \"m\", _FreeTextEditor_setEditorDimensions).call(this);\n    }\n    /** @inheritdoc */\n    shouldGetKeyboardEvents() {\n        return this.isInEditMode();\n    }\n    /** @inheritdoc */\n    enterInEditMode() {\n        this.enableEditMode();\n        this.editorDiv.focus();\n    }\n    /**\n     * ondblclick callback.\n     * @param {MouseEvent} event\n     */\n    dblclick(event) {\n        if (!event) {\n            return;\n        }\n        this.enterInEditMode();\n    }\n    /**\n     * onkeydown callback.\n     * @param {KeyboardEvent} event\n     */\n    keydown(event) {\n        if (event.target === this.div && event.key === \"Enter\") {\n            this.enterInEditMode();\n            // Avoid to add an unwanted new line.\n            event.preventDefault();\n        }\n    }\n    editorDivKeydown() {\n        // FreeTextEditor._keyboardManager.exec(this, event);\n    }\n    editorDivFocus() {\n        this.isEditing = true;\n    }\n    editorDivBlur() {\n        this.isEditing = false;\n    }\n    editorDivInput() {\n        // this.parent.div.classList.toggle(\"freetextEditing\", this.isEmpty());\n    }\n    /** @inheritdoc */\n    disableEditing() {\n        this.editorDiv.setAttribute(\"role\", \"comment\");\n        this.editorDiv.removeAttribute(\"aria-multiline\");\n    }\n    /** @inheritdoc */\n    enableEditing() {\n        this.editorDiv.setAttribute(\"role\", \"textbox\");\n        this.editorDiv.setAttribute(\"aria-multiline\", true);\n    }\n    /** @inheritdoc */\n    render() {\n        var _b;\n        if (this.div) {\n            return this.div;\n        }\n        let baseX, baseY;\n        if (this.width) {\n            baseX = this.x;\n            baseY = this.y;\n        }\n        super.render();\n        this.editorDiv = document.createElement(\"div\");\n        this.editorDiv.className = \"k-internal internal\";\n        // todo: move to CSS\n        // this.editorDiv.style.position = \"absolute\";\n        // this.div.style.position = \"absolute\";\n        this.editorDiv.setAttribute(\"id\", __classPrivateFieldGet(this, _FreeTextEditor_editorDivId, \"f\"));\n        this.editorDiv.setAttribute(\"data-l10n-id\", \"pdfjs-free-text\");\n        this.enableEditing();\n        // AnnotationEditor._l10nPromise\n        //     .get(\"pdfjs-free-text-default-content\")\n        //     .then(msg => this.editorDiv?.setAttribute(\"default-content\", msg));\n        // todo: fix localization\n        (_b = this.editorDiv) === null || _b === void 0 ? void 0 : _b.setAttribute(\"default-content\", this._uiManager.pdfViewer.options.messages.freeTextEditorPlaceholder);\n        this.editorDiv.contentEditable = true;\n        const { style } = this.editorDiv;\n        style.fontSize = `calc(${__classPrivateFieldGet(this, _FreeTextEditor_fontSize, \"f\")}px * var(--scale-factor))`;\n        style.color = __classPrivateFieldGet(this, _FreeTextEditor_color, \"f\");\n        this.div.append(this.editorDiv);\n        this.overlayDiv = document.createElement(\"div\");\n        // this.overlayDiv.classList.add(\"overlay\", \"enabled\");\n        this.div.append(this.overlayDiv);\n        bindEvents(this, this.div, [\"dblclick\", \"keydown\"]);\n        if (this.width) {\n            // This editor was created in using copy (ctrl+c).\n            const [parentWidth, parentHeight] = this.parentDimensions;\n            if (this.annotationElementId) {\n                // This stuff is hard to test: if something is changed here, please\n                // test with the following PDF file:\n                //  - freetexts.pdf\n                //  - rotated_freetexts.pdf\n                // Only small variations between the original annotation and its editor\n                // are allowed.\n                // position is the position of the first glyph in the annotation\n                // and it's relative to its container.\n                const { position } = __classPrivateFieldGet(this, _FreeTextEditor_initialData, \"f\");\n                let [tx, ty] = this.getInitialTranslation();\n                [tx, ty] = this.pageTranslationToScreen(tx, ty);\n                const [pageWidth, pageHeight] = this.pageDimensions;\n                const [pageX, pageY] = this.pageTranslation;\n                let posX, posY;\n                switch (this.rotation) {\n                    case 0:\n                        posX = baseX + (position[0] - pageX) / pageWidth;\n                        posY = baseY + this.height - (position[1] - pageY) / pageHeight;\n                        break;\n                    case 90:\n                        posX = baseX + (position[0] - pageX) / pageWidth;\n                        posY = baseY - (position[1] - pageY) / pageHeight;\n                        [tx, ty] = [ty, -tx];\n                        break;\n                    case 180:\n                        posX = baseX - this.width + (position[0] - pageX) / pageWidth;\n                        posY = baseY - (position[1] - pageY) / pageHeight;\n                        [tx, ty] = [-tx, -ty];\n                        break;\n                    case 270:\n                        posX =\n                            baseX +\n                                (position[0] - pageX - this.height * pageHeight) / pageWidth;\n                        posY =\n                            baseY +\n                                (position[1] - pageY - this.width * pageWidth) / pageHeight;\n                        [tx, ty] = [-ty, tx];\n                        break;\n                    default: break;\n                }\n                this.setAt(posX * parentWidth, posY * parentHeight, tx, ty);\n            }\n            else {\n                this.setAt(baseX * parentWidth, baseY * parentHeight, this.width * parentWidth, this.height * parentHeight);\n            }\n            __classPrivateFieldGet(this, _FreeTextEditor_instances, \"m\", _FreeTextEditor_setContent).call(this);\n            this._isDraggable = true;\n            this.editorDiv.contentEditable = false;\n        }\n        else {\n            this._isDraggable = false;\n            this.editorDiv.contentEditable = true;\n        }\n        // if (typeof PDFJSDev !== \"undefined\" && PDFJSDev.test(\"TESTING\")) {\n        //     this.div.setAttribute(\"annotation-id\", this.annotationElementId);\n        // }\n        return this.div;\n    }\n    editorDivPaste(event) {\n        // @ts-expect-error TS(2551):\n        const clipboardData = event.clipboardData || window.clipboardData;\n        const { types } = clipboardData;\n        if (types.length === 1 && types[0] === \"text/plain\") {\n            return;\n        }\n        event.preventDefault();\n        const paste = __classPrivateFieldGet(_a, _a, \"m\", _FreeTextEditor_deserializeContent).call(_a, clipboardData.getData(\"text\") || \"\").replaceAll(EOL_PATTERN, \"\\n\");\n        if (!paste) {\n            return;\n        }\n        const selection = window.getSelection();\n        if (!selection.rangeCount) {\n            return;\n        }\n        this.editorDiv.normalize();\n        selection.deleteFromDocument();\n        const range = selection.getRangeAt(0);\n        if (!paste.includes(\"\\n\")) {\n            range.insertNode(document.createTextNode(paste));\n            this.editorDiv.normalize();\n            selection.collapseToStart();\n            return;\n        }\n        // Collect the text before and after the caret.\n        const { startContainer, startOffset } = range;\n        const bufferBefore = [];\n        const bufferAfter = [];\n        if (startContainer.nodeType === Node.TEXT_NODE) {\n            const parent = startContainer.parentElement;\n            bufferAfter.push(\n            // @ts-expect-error TS(2556):\n            startContainer.nodeValue.slice(startOffset).replaceAll(EOL_PATTERN, \"\"));\n            if (parent !== this.editorDiv) {\n                let buffer = bufferBefore;\n                for (const child of this.editorDiv.childNodes) {\n                    if (child === parent) {\n                        buffer = bufferAfter;\n                        continue;\n                    }\n                    buffer.push(__classPrivateFieldGet(_a, _a, \"m\", _FreeTextEditor_getNodeContent).call(_a, child));\n                }\n            }\n            bufferBefore.push(startContainer.nodeValue\n                .slice(0, startOffset)\n                // @ts-expect-error TS(2556):\n                .replaceAll(EOL_PATTERN, \"\"));\n        }\n        else if (startContainer === this.editorDiv) {\n            let buffer = bufferBefore;\n            let i = 0;\n            for (const child of this.editorDiv.childNodes) {\n                if (i++ === startOffset) {\n                    buffer = bufferAfter;\n                }\n                buffer.push(__classPrivateFieldGet(_a, _a, \"m\", _FreeTextEditor_getNodeContent).call(_a, child));\n            }\n        }\n        __classPrivateFieldSet(this, _FreeTextEditor_content, `${bufferBefore.join(\"\\n\")}${paste}${bufferAfter.join(\"\\n\")}`, \"f\");\n        __classPrivateFieldGet(this, _FreeTextEditor_instances, \"m\", _FreeTextEditor_setContent).call(this);\n        // Set the caret at the right position.\n        const newRange = new Range();\n        let beforeLength = bufferBefore.reduce((acc, line) => acc + line.length, 0);\n        for (const { firstChild } of this.editorDiv.childNodes) {\n            // Each child is either a div with a text node or a br element.\n            if (firstChild.nodeType === Node.TEXT_NODE) {\n                const length = firstChild.nodeValue.length;\n                if (beforeLength <= length) {\n                    newRange.setStart(firstChild, beforeLength);\n                    newRange.setEnd(firstChild, beforeLength);\n                    break;\n                }\n                beforeLength -= length;\n            }\n        }\n        selection.removeAllRanges();\n        selection.addRange(newRange);\n    }\n    /** @inheritdoc */\n    get contentDiv() {\n        return this.editorDiv;\n    }\n    /** @inheritdoc */\n    static deserialize(data, parent, uiManager) {\n        let initialData = null;\n        if (data instanceof FreeTextAnnotationElement) {\n            const { data: { defaultAppearanceData: { fontSize, fontColor }, rect, rotation, id }, textContent, textPosition, parent: { page: { pageNumber } } } = data;\n            // textContent is supposed to be an array of strings containing each line\n            // of text. However, it can be null or empty.\n            if (!textContent || textContent.length === 0) {\n                // Empty annotation.\n                return null;\n            }\n            initialData = data = {\n                annotationType: AnnotationEditorType.FREETEXT,\n                color: Array.from(fontColor),\n                fontSize,\n                value: textContent.join(\"\\n\"),\n                position: textPosition,\n                pageIndex: pageNumber - 1,\n                rect: rect.slice(0),\n                rotation,\n                id,\n                deleted: false\n            };\n        }\n        const editor = super.deserialize(data, parent, uiManager);\n        __classPrivateFieldSet(editor, _FreeTextEditor_fontSize, data.fontSize, \"f\");\n        // @ts-expect-error TS(2556):\n        __classPrivateFieldSet(editor, _FreeTextEditor_color, Util.makeHexColor(...data.color), \"f\");\n        __classPrivateFieldSet(editor, _FreeTextEditor_content, __classPrivateFieldGet(_a, _a, \"m\", _FreeTextEditor_deserializeContent).call(_a, data.value), \"f\");\n        editor.annotationElementId = data.id || null;\n        __classPrivateFieldSet(editor, _FreeTextEditor_initialData, initialData, \"f\");\n        return editor;\n    }\n    /** @inheritdoc */\n    serialize(isForCopying = false) {\n        if (this.isEmpty()) {\n            return null;\n        }\n        if (this.deleted) {\n            return {\n                pageIndex: this.pageIndex,\n                id: this.annotationElementId,\n                deleted: true\n            };\n        }\n        const padding = _a._internalPadding * this.parentScale;\n        const rect = this.getRect(padding, padding);\n        const color = AnnotationEditor._colorManager.convert(this.isAttachedToDOM\n            ? getComputedStyle(this.editorDiv).color\n            : __classPrivateFieldGet(this, _FreeTextEditor_color, \"f\"));\n        const serialized = {\n            annotationType: AnnotationEditorType.FREETEXT,\n            color,\n            fontSize: __classPrivateFieldGet(this, _FreeTextEditor_fontSize, \"f\"),\n            value: __classPrivateFieldGet(this, _FreeTextEditor_instances, \"m\", _FreeTextEditor_serializeContent).call(this),\n            pageIndex: this.pageIndex,\n            rect,\n            rotation: this.rotation,\n            structTreeParentId: this._structTreeParentId\n        };\n        if (isForCopying) {\n            // Don't add the id when copying because the pasted editor mustn't be\n            // linked to an existing annotation.\n            return serialized;\n        }\n        if (this.annotationElementId && !__classPrivateFieldGet(this, _FreeTextEditor_instances, \"m\", _FreeTextEditor_hasElementChanged).call(this, serialized)) {\n            return null;\n        }\n        // @ts-expect-error TS(2556):\n        serialized.id = this.annotationElementId;\n        return serialized;\n    }\n    /** @inheritdoc */\n    renderAnnotationElement(annotation) {\n        const content = super.renderAnnotationElement(annotation);\n        if (this.deleted) {\n            return content;\n        }\n        const { style } = content;\n        style.fontSize = `calc(${__classPrivateFieldGet(this, _FreeTextEditor_fontSize, \"f\")}px * var(--scale-factor))`;\n        style.color = __classPrivateFieldGet(this, _FreeTextEditor_color, \"f\");\n        content.replaceChildren();\n        for (const line of __classPrivateFieldGet(this, _FreeTextEditor_content, \"f\").split(\"\\n\")) {\n            const div = document.createElement(\"div\");\n            div.append(line ? document.createTextNode(line) : document.createElement(\"br\"));\n            content.append(div);\n        }\n        const padding = _a._internalPadding * this.parentScale;\n        annotation.updateEdited({\n            rect: this.getRect(padding, padding),\n            popupContent: __classPrivateFieldGet(this, _FreeTextEditor_content, \"f\")\n        });\n        return content;\n    }\n    resetAnnotationElement(annotation) {\n        super.resetAnnotationElement(annotation);\n        annotation.resetEdited();\n    }\n    // todo: this is necessary\n    // saveDocument() has checks that test \"editor instanceof AnnotationEditor\", but they fail\n    // because AnnotationEditor from \"pdfjs-dist/legacy/build/pdf.mjs\" is not exported\n    // thus replace instances of editors with their serialized version\n    toJSON() {\n        const data = this.serialize();\n        return data;\n    }\n}\n_a = FreeTextEditor, _FreeTextEditor_color = new WeakMap(), _FreeTextEditor_content = new WeakMap(), _FreeTextEditor_editorDivId = new WeakMap(), _FreeTextEditor_editModeAC = new WeakMap(), _FreeTextEditor_fontSize = new WeakMap(), _FreeTextEditor_initialData = new WeakMap(), _FreeTextEditor_instances = new WeakSet(), _FreeTextEditor_updateFontSize = function _FreeTextEditor_updateFontSize(fontSize) {\n    const setFontsize = size => {\n        this.editorDiv.style.fontSize = `calc(${size}px * var(--scale-factor))`;\n        this.translate(0, -(size - __classPrivateFieldGet(this, _FreeTextEditor_fontSize, \"f\")) * this.parentScale);\n        __classPrivateFieldSet(this, _FreeTextEditor_fontSize, size, \"f\");\n        __classPrivateFieldGet(this, _FreeTextEditor_instances, \"m\", _FreeTextEditor_setEditorDimensions).call(this);\n    };\n    const savedFontsize = __classPrivateFieldGet(this, _FreeTextEditor_fontSize, \"f\");\n    this.addCommands({\n        cmd: setFontsize.bind(this, fontSize),\n        undo: setFontsize.bind(this, savedFontsize),\n        post: this._uiManager.updateUI.bind(this._uiManager, this),\n        mustExec: true,\n        type: AnnotationEditorParamsType.FREETEXT_SIZE,\n        overwriteIfSameType: true,\n        keepUndo: true\n    });\n}, _FreeTextEditor_updateColor = function _FreeTextEditor_updateColor(color) {\n    const setColor = col => {\n        __classPrivateFieldSet(this, _FreeTextEditor_color, this.editorDiv.style.color = col, \"f\");\n    };\n    const savedColor = __classPrivateFieldGet(this, _FreeTextEditor_color, \"f\");\n    this.addCommands({\n        cmd: setColor.bind(this, color),\n        undo: setColor.bind(this, savedColor),\n        post: this._uiManager.updateUI.bind(this._uiManager, this),\n        mustExec: true,\n        type: AnnotationEditorParamsType.FREETEXT_COLOR,\n        overwriteIfSameType: true,\n        keepUndo: true\n    });\n}, _FreeTextEditor_extractText = function _FreeTextEditor_extractText() {\n    // We don't use innerText because there are some bugs with line breaks.\n    const buffer = [];\n    this.editorDiv.normalize();\n    for (const child of this.editorDiv.childNodes) {\n        buffer.push(__classPrivateFieldGet(_a, _a, \"m\", _FreeTextEditor_getNodeContent).call(_a, child));\n    }\n    return buffer.join(\"\\n\");\n}, _FreeTextEditor_setEditorDimensions = function _FreeTextEditor_setEditorDimensions() {\n    const [parentWidth, parentHeight] = this.parentDimensions;\n    let rect;\n    if (this.isAttachedToDOM) {\n        rect = this.div.getBoundingClientRect();\n    }\n    else {\n        // This editor isn't on screen but we need to get its dimensions, so\n        // we just insert it in the DOM, get its bounding box and then remove it.\n        const { currentLayer, div } = this;\n        const savedDisplay = div.style.display;\n        const savedVisibility = div.classList.contains(\"hidden\") || div.classList.contains(\"k-hidden\");\n        div.classList.remove(\"hidden\");\n        div.classList.remove(\"k-hidden\");\n        div.style.display = \"hidden\";\n        currentLayer.div.append(this.div);\n        rect = div.getBoundingClientRect();\n        div.remove();\n        div.style.display = savedDisplay;\n        // div.classList.toggle(\"hidden\", savedVisibility);\n        div.classList.toggle(\"k-hidden\", savedVisibility);\n    }\n    // The dimensions are relative to the rotation of the page, hence we need to\n    // take that into account (see issue #16636).\n    if (this.rotation % 180 === this.parentRotation % 180) {\n        this.width = rect.width / parentWidth;\n        this.height = rect.height / parentHeight;\n    }\n    else {\n        this.width = rect.height / parentWidth;\n        this.height = rect.width / parentHeight;\n    }\n    this.fixAndSetPosition();\n}, _FreeTextEditor_getNodeContent = function _FreeTextEditor_getNodeContent(node) {\n    return (node.nodeType === Node.TEXT_NODE ? node.nodeValue : node.innerText).replaceAll(EOL_PATTERN, \"\");\n}, _FreeTextEditor_setContent = function _FreeTextEditor_setContent() {\n    this.editorDiv.replaceChildren();\n    if (!__classPrivateFieldGet(this, _FreeTextEditor_content, \"f\")) {\n        return;\n    }\n    for (const line of __classPrivateFieldGet(this, _FreeTextEditor_content, \"f\").split(\"\\n\")) {\n        const div = document.createElement(\"div\");\n        div.append(line ? document.createTextNode(line) : document.createElement(\"br\"));\n        this.editorDiv.append(div);\n    }\n}, _FreeTextEditor_serializeContent = function _FreeTextEditor_serializeContent() {\n    // @ts-expect-error TS(2556):\n    return __classPrivateFieldGet(this, _FreeTextEditor_content, \"f\").replaceAll(\"\\xa0\", \" \");\n}, _FreeTextEditor_deserializeContent = function _FreeTextEditor_deserializeContent(content) {\n    return content.replaceAll(\" \", \"\\xa0\");\n}, _FreeTextEditor_hasElementChanged = function _FreeTextEditor_hasElementChanged(serialized) {\n    const { value, fontSize, color, pageIndex } = __classPrivateFieldGet(this, _FreeTextEditor_initialData, \"f\");\n    return (this._hasBeenMoved ||\n        serialized.value !== value ||\n        serialized.fontSize !== fontSize ||\n        serialized.color.some((c, i) => c !== color[i]) ||\n        serialized.pageIndex !== pageIndex);\n};\nFreeTextEditor._freeTextDefaultContent = \"\";\nFreeTextEditor._internalPadding = 0;\n// static _defaultColor = null;\nFreeTextEditor._defaultColor = \"#000000\";\nFreeTextEditor._defaultFontSize = 10;\n// static get _keyboardManager() {\n//     const proto = FreeTextEditor.prototype;\n//     const arrowChecker = self => self.isEmpty();\n//     const small = AnnotationEditorUIManager.TRANSLATE_SMALL;\n//     const big = AnnotationEditorUIManager.TRANSLATE_BIG;\n//     return shadow(\n//         this,\n//         \"_keyboardManager\",\n//         new KeyboardManager([\n//             [\n//                 // Commit the text in case the user use ctrl+s to save the document.\n//                 // The event must bubble in order to be caught by the viewer.\n//                 // See bug 1831574.\n//                 [\"ctrl+s\", \"mac+meta+s\", \"ctrl+p\", \"mac+meta+p\"],\n//                 proto.commitOrRemove,\n//                 { bubbles: true },\n//             ],\n//             [\n//                 [\"ctrl+Enter\", \"mac+meta+Enter\", \"Escape\", \"mac+Escape\"],\n//                 proto.commitOrRemove,\n//             ],\n//             [\n//                 [\"ArrowLeft\", \"mac+ArrowLeft\"],\n//                 proto._translateEmpty,\n//                 { args: [-small, 0], checker: arrowChecker },\n//             ],\n//             [\n//                 [\"ctrl+ArrowLeft\", \"mac+shift+ArrowLeft\"],\n//                 proto._translateEmpty,\n//                 { args: [-big, 0], checker: arrowChecker },\n//             ],\n//             [\n//                 [\"ArrowRight\", \"mac+ArrowRight\"],\n//                 proto._translateEmpty,\n//                 { args: [small, 0], checker: arrowChecker },\n//             ],\n//             [\n//                 [\"ctrl+ArrowRight\", \"mac+shift+ArrowRight\"],\n//                 proto._translateEmpty,\n//                 { args: [big, 0], checker: arrowChecker },\n//             ],\n//             [\n//                 [\"ArrowUp\", \"mac+ArrowUp\"],\n//                 proto._translateEmpty,\n//                 { args: [0, -small], checker: arrowChecker },\n//             ],\n//             [\n//                 [\"ctrl+ArrowUp\", \"mac+shift+ArrowUp\"],\n//                 proto._translateEmpty,\n//                 { args: [0, -big], checker: arrowChecker },\n//             ],\n//             [\n//                 [\"ArrowDown\", \"mac+ArrowDown\"],\n//                 proto._translateEmpty,\n//                 { args: [0, small], checker: arrowChecker },\n//             ],\n//             [\n//                 [\"ctrl+ArrowDown\", \"mac+shift+ArrowDown\"],\n//                 proto._translateEmpty,\n//                 { args: [0, big], checker: arrowChecker },\n//             ],\n//         ])\n//     );\n// }\nFreeTextEditor._type = \"freetext\";\nFreeTextEditor._editorType = AnnotationEditorType.FREETEXT;\nexport { FreeTextEditor };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,yBAAyB,EAAEC,EAAE,EAAEC,qBAAqB,EAAEC,uBAAuB,EAAEC,2BAA2B,EAAEC,0BAA0B,EAAEC,wBAAwB,EAAEC,2BAA2B,EAAEC,8BAA8B,EAAEC,2BAA2B,EAAEC,2BAA2B,EAAEC,mCAAmC,EAAEC,8BAA8B,EAAEC,0BAA0B,EAAEC,gCAAgC,EAAEC,kCAAkC,EAAEC,iCAAiC;AACre,SAASC,sBAAsB,EAAEC,sBAAsB,QAAQ,OAAO;AACtE;AACA,SAASC,0BAA0B,EAAEC,oBAAoB,EAAEC,IAAI,QAAQ,iCAAiC;AACxG;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,yBAAyB,QAAQ,qBAAqB;AAC/D;AACA,MAAMC,WAAW,GAAG,WAAW;AAC/B,MAAMC,cAAc,SAASJ,gBAAgB,CAAC;EAC1CK,WAAWA,CAACC,MAAM,EAAE;IAChB,KAAK,CAACC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,MAAM,CAAC,EAAE;MAAEG,IAAI,EAAE;IAAoC,CAAC,CAAC,CAAC;IAC9F/B,yBAAyB,CAACgC,GAAG,CAAC,IAAI,CAAC;IACnC;IACA,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB;IACAhC,qBAAqB,CAACiC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACvChC,uBAAuB,CAACgC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;IACrC/B,2BAA2B,CAAC+B,GAAG,CAAC,IAAI,EAAE,GAAG,IAAI,CAACC,EAAE,SAAS,CAAC;IAC1D/B,0BAA0B,CAAC8B,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAC1C7B,wBAAwB,CAAC6B,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC1C5B,2BAA2B,CAAC4B,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAC3CjB,sBAAsB,CAAC,IAAI,EAAEhB,qBAAqB,EAAE0B,MAAM,CAACS,KAAK,IAC5DpC,EAAE,CAACqC,aAAa,IAChBhB,gBAAgB,CAACiB,iBAAiB,EAAE,GAAG,CAAC;IAC5CrB,sBAAsB,CAAC,IAAI,EAAEZ,wBAAwB,EAAEsB,MAAM,CAACY,QAAQ,IAAIvC,EAAE,CAACwC,gBAAgB,EAAE,GAAG,CAAC;EACvG;EACA;EACA,OAAOC,UAAUA,CAACC,IAAI,EAAEC,SAAS,EAAE;IAC/BtB,gBAAgB,CAACoB,UAAU,CAACC,IAAI,EAAEC,SAAS,EAAE;MACzCC,OAAO,EAAE,CAAC,iCAAiC;IAC/C,CAAC,CAAC;IACF,MAAMC,KAAK,GAAGC,gBAAgB,CAACC,QAAQ,CAACC,eAAe,CAAC;IACxD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,gBAAgB,GAAGC,UAAU,CAACL,KAAK,CAACM,gBAAgB,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC;EACzF;EACA;EACA,OAAOC,mBAAmBA,CAACC,IAAI,EAAEC,KAAK,EAAE;IACpC,QAAQD,IAAI;MACR,KAAKnC,0BAA0B,CAACqC,aAAa;QACzCvD,EAAE,CAACwC,gBAAgB,GAAGc,KAAK;QAC3B;MACJ,KAAKpC,0BAA0B,CAACsC,cAAc;QAC1CxD,EAAE,CAACqC,aAAa,GAAGiB,KAAK;QACxB;MACJ;QAAS;IACb;EACJ;EACA;EACAG,YAAYA,CAACJ,IAAI,EAAEC,KAAK,EAAE;IACtB,QAAQD,IAAI;MACR,KAAKnC,0BAA0B,CAACqC,aAAa;QACzCvC,sBAAsB,CAAC,IAAI,EAAEjB,yBAAyB,EAAE,GAAG,EAAEQ,8BAA8B,CAAC,CAACmD,IAAI,CAAC,IAAI,EAAEJ,KAAK,CAAC;QAC9G;MACJ,KAAKpC,0BAA0B,CAACsC,cAAc;QAC1CxC,sBAAsB,CAAC,IAAI,EAAEjB,yBAAyB,EAAE,GAAG,EAAES,2BAA2B,CAAC,CAACkD,IAAI,CAAC,IAAI,EAAEJ,KAAK,CAAC;QAC3G;MACJ;QAAS;IACb;EACJ;EACA;EACA,WAAWK,yBAAyBA,CAAA,EAAG;IACnC,OAAO,CACH,CACIzC,0BAA0B,CAACqC,aAAa,EACxCvD,EAAE,CAACwC,gBAAgB,CACtB,EACD,CACItB,0BAA0B,CAACsC,cAAc,EACzCxD,EAAE,CAACqC,aAAa,IAAIhB,gBAAgB,CAACiB,iBAAiB,CACzD,CACJ;EACL;EACA;EACA,IAAIsB,kBAAkBA,CAAA,EAAG;IACrB,OAAO,CACH,CAAC1C,0BAA0B,CAACqC,aAAa,EAAEvC,sBAAsB,CAAC,IAAI,EAAEX,wBAAwB,EAAE,GAAG,CAAC,CAAC,EACvG,CAACa,0BAA0B,CAACsC,cAAc,EAAExC,sBAAsB,CAAC,IAAI,EAAEf,qBAAqB,EAAE,GAAG,CAAC,CAAC,CACxG;EACL;EACA;AACJ;AACA;AACA;AACA;EACI4D,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAClB,IAAI,CAACC,UAAU,CAACC,wBAAwB,CAACH,CAAC,EAAEC,CAAC,EAAE,gBAAiB,IAAI,CAAC;EACzE;EACAG,qBAAqBA,CAAA,EAAG;IACpB;IACA,MAAMC,KAAK,GAAG,IAAI,CAACC,WAAW;IAC9B,OAAO,CACH,CAACpE,EAAE,CAACiD,gBAAgB,GAAGkB,KAAK,EAC5B,EAAEnE,EAAE,CAACiD,gBAAgB,GAAGjC,sBAAsB,CAAC,IAAI,EAAEX,wBAAwB,EAAE,GAAG,CAAC,CAAC,GAAG8D,KAAK,CAC/F;EACL;EACA;EACAE,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;MACd;IACJ;IACA,KAAK,CAACD,OAAO,CAAC,CAAC;IACf,IAAI,IAAI,CAACE,GAAG,KAAK,IAAI,EAAE;MACnB;IACJ;IACA,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE;MACvB;MACA;MACA,IAAI,CAACF,MAAM,CAACvC,GAAG,CAAC,IAAI,CAAC;IACzB;EACJ;EACA0C,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACC,YAAY,CAAC,CAAC,EAAE;MACrB;IACJ;IACA,IAAI,CAACJ,MAAM,CAACK,eAAe,CAAC,KAAK,CAAC;IAClC,IAAI,CAACL,MAAM,CAACM,aAAa,CAACzD,oBAAoB,CAAC0D,QAAQ,CAAC;IACxD,KAAK,CAACJ,cAAc,CAAC,CAAC;IACtB,IAAI,CAACxC,UAAU,CAAC6C,SAAS,CAACC,MAAM,CAAC,SAAS,CAAC;IAC3C,IAAI,CAAC/C,SAAS,CAACgD,eAAe,GAAG,IAAI;IACrC,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACV,GAAG,CAACW,eAAe,CAAC,uBAAuB,CAAC;IACjD;IACA;IACA;IACA;IACA;IACA;IACAjE,sBAAsB,CAAC,IAAI,EAAEb,0BAA0B,EAAE,IAAI+E,eAAe,CAAC,CAAC,EAAE,GAAG,CAAC;IACpF,MAAMC,MAAM,GAAG,IAAI,CAACpB,UAAU,CAACqB,cAAc,CAACrE,sBAAsB,CAAC,IAAI,EAAEZ,0BAA0B,EAAE,GAAG,CAAC,CAAC;IAC5G,IAAI,CAAC4B,SAAS,CAACsD,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACC,gBAAgB,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE;MAAEJ;IAAO,CAAC,CAAC;IACxF,IAAI,CAACpD,SAAS,CAACsD,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACG,cAAc,CAACD,IAAI,CAAC,IAAI,CAAC,EAAE;MACrEJ;IACJ,CAAC,CAAC;IACF,IAAI,CAACpD,SAAS,CAACsD,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAACI,aAAa,CAACF,IAAI,CAAC,IAAI,CAAC,EAAE;MACnEJ;IACJ,CAAC,CAAC;IACF,IAAI,CAACpD,SAAS,CAACsD,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACK,cAAc,CAACH,IAAI,CAAC,IAAI,CAAC,EAAE;MACrEJ;IACJ,CAAC,CAAC;IACF,IAAI,CAACpD,SAAS,CAACsD,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACM,cAAc,CAACJ,IAAI,CAAC,IAAI,CAAC,EAAE;MACrEJ;IACJ,CAAC,CAAC;EACN;EACAS,eAAeA,CAAA,EAAG;IACd,IAAIC,EAAE;IACN,IAAI,CAAC,IAAI,CAACpB,YAAY,CAAC,CAAC,EAAE;MACtB;IACJ;IACA,IAAI,CAACJ,MAAM,CAACK,eAAe,CAAC,IAAI,CAAC;IACjC,KAAK,CAACkB,eAAe,CAAC,CAAC;IACvB;IACA,IAAI,CAAC7D,SAAS,CAACgD,eAAe,GAAG,KAAK;IACtC,IAAI,CAACT,GAAG,CAACwB,YAAY,CAAC,uBAAuB,EAAE/E,sBAAsB,CAAC,IAAI,EAAEb,2BAA2B,EAAE,GAAG,CAAC,CAAC;IAC9G,IAAI,CAAC8E,YAAY,GAAG,IAAI;IACxB,CAACa,EAAE,GAAG9E,sBAAsB,CAAC,IAAI,EAAEZ,0BAA0B,EAAE,GAAG,CAAC,MAAM,IAAI,IAAI0F,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,KAAK,CAAC,CAAC;IACpH/E,sBAAsB,CAAC,IAAI,EAAEb,0BAA0B,EAAE,IAAI,EAAE,GAAG,CAAC;IACnE;IACA;IACA,IAAI,CAACmE,GAAG,CAAC0B,KAAK,CAAC;MACXC,aAAa,EAAE,IAAI,CAAC;IACxB,CAAC,CAAC;IACF;IACA,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB;EACJ;EACAC,OAAOA,CAACC,KAAK,EAAE;IACX,IAAIP,EAAE;IACN,IAAI,CAAC,IAAI,CAACQ,mBAAmB,EAAE;MAC3B;IACJ;IACA,IAAI,CAAC,CAACR,EAAE,GAAG,IAAI,CAAC9B,UAAU,MAAM,IAAI,IAAI8B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACS,OAAO,CAAC,CAAC,MAAMpF,oBAAoB,CAAC0D,QAAQ,EAAE;MAC9G;MACA;MACA;MACA;IACJ;IACA,KAAK,CAACuB,OAAO,CAACC,KAAK,CAAC;IACpB,IAAIA,KAAK,CAACG,MAAM,KAAK,IAAI,CAACxE,SAAS,EAAE;MACjC,IAAI,CAACA,SAAS,CAACiE,KAAK,CAAC,CAAC;IAC1B;EACJ;EACA;EACAQ,SAASA,CAAA,EAAG;IACR,IAAIX,EAAE;IACN,IAAI,IAAI,CAACY,KAAK,EAAE;MACZ;MACA;IACJ;IACA,IAAI,CAACjC,cAAc,CAAC,CAAC;IACrB,IAAI,CAACzC,SAAS,CAACiE,KAAK,CAAC,CAAC;IACtB,IAAI,CAACH,EAAE,GAAG,IAAI,CAACa,eAAe,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACc,UAAU,EAAE;MAChF,IAAI,CAACC,MAAM,CAAC,CAAC;IACjB;IACA,IAAI,CAACF,eAAe,GAAG,IAAI;EAC/B;EACA;EACAG,OAAOA,CAAA,EAAG;IACN,IAAIhB,EAAE,EAAEiB,EAAE;IACV,OAAO,CAAC,IAAI,CAAC/E,SAAS,IAAI,CAAC,CAAC+E,EAAE,GAAG,CAACjB,EAAE,GAAG,IAAI,CAAC9D,SAAS,MAAM,IAAI,IAAI8D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACkB,SAAS,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAAC,CAAC,MAAM,EAAE;EACpK;EACA;EACAlC,MAAMA,CAAA,EAAG;IACL,IAAI,CAACoB,SAAS,GAAG,KAAK;IACtB,IAAI,IAAI,CAAC7B,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAACK,eAAe,CAAC,IAAI,CAAC;MACjC;IACJ;IACA,KAAK,CAACI,MAAM,CAAC,CAAC;EAClB;EACA;AACJ;AACA;AACA;EACImC,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACxC,YAAY,CAAC,CAAC,EAAE;MACtB;IACJ;IACA,KAAK,CAACwC,MAAM,CAAC,CAAC;IACd,IAAI,CAACrB,eAAe,CAAC,CAAC;IACtB,MAAMsB,SAAS,GAAGnG,sBAAsB,CAAC,IAAI,EAAEd,uBAAuB,EAAE,GAAG,CAAC;IAC5E,MAAMkH,OAAO,GAAInG,sBAAsB,CAAC,IAAI,EAAEf,uBAAuB,EAAEc,sBAAsB,CAAC,IAAI,EAAEjB,yBAAyB,EAAE,GAAG,EAAEU,2BAA2B,CAAC,CAACiD,IAAI,CAAC,IAAI,CAAC,CAAC2D,OAAO,CAAC,CAAC,EAAE,GAAG,CAAE;IAC5L,IAAIF,SAAS,KAAKC,OAAO,EAAE;MACvB;IACJ;IACA,MAAME,OAAO,GAAGC,IAAI,IAAI;MACpBtG,sBAAsB,CAAC,IAAI,EAAEf,uBAAuB,EAAEqH,IAAI,EAAE,GAAG,CAAC;MAChE,IAAI,CAACA,IAAI,EAAE;QACP,IAAI,CAACxC,MAAM,CAAC,CAAC;QACb;MACJ;MACA/D,sBAAsB,CAAC,IAAI,EAAEjB,yBAAyB,EAAE,GAAG,EAAEa,0BAA0B,CAAC,CAAC8C,IAAI,CAAC,IAAI,CAAC;MACnG,IAAI,CAACM,UAAU,CAACK,OAAO,CAAC,IAAI,CAAC;MAC7BrD,sBAAsB,CAAC,IAAI,EAAEjB,yBAAyB,EAAE,GAAG,EAAEW,mCAAmC,CAAC,CAACgD,IAAI,CAAC,IAAI,CAAC;IAChH,CAAC;IACD,IAAI,CAAC8D,WAAW,CAAC;MACbC,GAAG,EAAEA,CAAA,KAAM;QACPH,OAAO,CAACF,OAAO,CAAC;MACpB,CAAC;MACDM,IAAI,EAAEA,CAAA,KAAM;QACRJ,OAAO,CAACH,SAAS,CAAC;MACtB,CAAC;MACDQ,QAAQ,EAAE;IACd,CAAC,CAAC;IACF3G,sBAAsB,CAAC,IAAI,EAAEjB,yBAAyB,EAAE,GAAG,EAAEW,mCAAmC,CAAC,CAACgD,IAAI,CAAC,IAAI,CAAC;EAChH;EACA;EACAkE,uBAAuBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAAClD,YAAY,CAAC,CAAC;EAC9B;EACA;EACAmD,eAAeA,CAAA,EAAG;IACd,IAAI,CAACpD,cAAc,CAAC,CAAC;IACrB,IAAI,CAACzC,SAAS,CAACiE,KAAK,CAAC,CAAC;EAC1B;EACA;AACJ;AACA;AACA;EACI6B,QAAQA,CAACzB,KAAK,EAAE;IACZ,IAAI,CAACA,KAAK,EAAE;MACR;IACJ;IACA,IAAI,CAACwB,eAAe,CAAC,CAAC;EAC1B;EACA;AACJ;AACA;AACA;EACIE,OAAOA,CAAC1B,KAAK,EAAE;IACX,IAAIA,KAAK,CAACG,MAAM,KAAK,IAAI,CAACjC,GAAG,IAAI8B,KAAK,CAAC2B,GAAG,KAAK,OAAO,EAAE;MACpD,IAAI,CAACH,eAAe,CAAC,CAAC;MACtB;MACAxB,KAAK,CAAC4B,cAAc,CAAC,CAAC;IAC1B;EACJ;EACA1C,gBAAgBA,CAAA,EAAG;IACf;EAAA;EAEJE,cAAcA,CAAA,EAAG;IACb,IAAI,CAACU,SAAS,GAAG,IAAI;EACzB;EACAT,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACS,SAAS,GAAG,KAAK;EAC1B;EACAR,cAAcA,CAAA,EAAG;IACb;EAAA;EAEJ;EACAuC,cAAcA,CAAA,EAAG;IACb,IAAI,CAAClG,SAAS,CAAC+D,YAAY,CAAC,MAAM,EAAE,SAAS,CAAC;IAC9C,IAAI,CAAC/D,SAAS,CAACkD,eAAe,CAAC,gBAAgB,CAAC;EACpD;EACA;EACAiD,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACnG,SAAS,CAAC+D,YAAY,CAAC,MAAM,EAAE,SAAS,CAAC;IAC9C,IAAI,CAAC/D,SAAS,CAAC+D,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC;EACvD;EACA;EACAqC,MAAMA,CAAA,EAAG;IACL,IAAItC,EAAE;IACN,IAAI,IAAI,CAACvB,GAAG,EAAE;MACV,OAAO,IAAI,CAACA,GAAG;IACnB;IACA,IAAI8D,KAAK,EAAEC,KAAK;IAChB,IAAI,IAAI,CAAC5B,KAAK,EAAE;MACZ2B,KAAK,GAAG,IAAI,CAACvE,CAAC;MACdwE,KAAK,GAAG,IAAI,CAACvE,CAAC;IAClB;IACA,KAAK,CAACqE,MAAM,CAAC,CAAC;IACd,IAAI,CAACpG,SAAS,GAAGe,QAAQ,CAACwF,aAAa,CAAC,KAAK,CAAC;IAC9C,IAAI,CAACvG,SAAS,CAACwG,SAAS,GAAG,qBAAqB;IAChD;IACA;IACA;IACA,IAAI,CAACxG,SAAS,CAAC+D,YAAY,CAAC,IAAI,EAAE/E,sBAAsB,CAAC,IAAI,EAAEb,2BAA2B,EAAE,GAAG,CAAC,CAAC;IACjG,IAAI,CAAC6B,SAAS,CAAC+D,YAAY,CAAC,cAAc,EAAE,iBAAiB,CAAC;IAC9D,IAAI,CAACoC,aAAa,CAAC,CAAC;IACpB;IACA;IACA;IACA;IACA,CAACrC,EAAE,GAAG,IAAI,CAAC9D,SAAS,MAAM,IAAI,IAAI8D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,YAAY,CAAC,iBAAiB,EAAE,IAAI,CAAC/B,UAAU,CAACyE,SAAS,CAACC,OAAO,CAACC,QAAQ,CAACC,yBAAyB,CAAC;IACnK,IAAI,CAAC5G,SAAS,CAACgD,eAAe,GAAG,IAAI;IACrC,MAAM;MAAEnC;IAAM,CAAC,GAAG,IAAI,CAACb,SAAS;IAChCa,KAAK,CAACN,QAAQ,GAAG,QAAQvB,sBAAsB,CAAC,IAAI,EAAEX,wBAAwB,EAAE,GAAG,CAAC,2BAA2B;IAC/GwC,KAAK,CAACT,KAAK,GAAGpB,sBAAsB,CAAC,IAAI,EAAEf,qBAAqB,EAAE,GAAG,CAAC;IACtE,IAAI,CAACsE,GAAG,CAACsE,MAAM,CAAC,IAAI,CAAC7G,SAAS,CAAC;IAC/B,IAAI,CAACC,UAAU,GAAGc,QAAQ,CAACwF,aAAa,CAAC,KAAK,CAAC;IAC/C;IACA,IAAI,CAAChE,GAAG,CAACsE,MAAM,CAAC,IAAI,CAAC5G,UAAU,CAAC;IAChCX,UAAU,CAAC,IAAI,EAAE,IAAI,CAACiD,GAAG,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;IACnD,IAAI,IAAI,CAACmC,KAAK,EAAE;MACZ;MACA,MAAM,CAACoC,WAAW,EAAEC,YAAY,CAAC,GAAG,IAAI,CAACC,gBAAgB;MACzD,IAAI,IAAI,CAACC,mBAAmB,EAAE;QAC1B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,MAAM;UAAEC;QAAS,CAAC,GAAGlI,sBAAsB,CAAC,IAAI,EAAEV,2BAA2B,EAAE,GAAG,CAAC;QACnF,IAAI,CAAC6I,EAAE,EAAEC,EAAE,CAAC,GAAG,IAAI,CAAClF,qBAAqB,CAAC,CAAC;QAC3C,CAACiF,EAAE,EAAEC,EAAE,CAAC,GAAG,IAAI,CAACC,uBAAuB,CAACF,EAAE,EAAEC,EAAE,CAAC;QAC/C,MAAM,CAACE,SAAS,EAAEC,UAAU,CAAC,GAAG,IAAI,CAACC,cAAc;QACnD,MAAM,CAACC,KAAK,EAAEC,KAAK,CAAC,GAAG,IAAI,CAACC,eAAe;QAC3C,IAAIC,IAAI,EAAEC,IAAI;QACd,QAAQ,IAAI,CAACC,QAAQ;UACjB,KAAK,CAAC;YACFF,IAAI,GAAGvB,KAAK,GAAG,CAACa,QAAQ,CAAC,CAAC,CAAC,GAAGO,KAAK,IAAIH,SAAS;YAChDO,IAAI,GAAGvB,KAAK,GAAG,IAAI,CAACyB,MAAM,GAAG,CAACb,QAAQ,CAAC,CAAC,CAAC,GAAGQ,KAAK,IAAIH,UAAU;YAC/D;UACJ,KAAK,EAAE;YACHK,IAAI,GAAGvB,KAAK,GAAG,CAACa,QAAQ,CAAC,CAAC,CAAC,GAAGO,KAAK,IAAIH,SAAS;YAChDO,IAAI,GAAGvB,KAAK,GAAG,CAACY,QAAQ,CAAC,CAAC,CAAC,GAAGQ,KAAK,IAAIH,UAAU;YACjD,CAACJ,EAAE,EAAEC,EAAE,CAAC,GAAG,CAACA,EAAE,EAAE,CAACD,EAAE,CAAC;YACpB;UACJ,KAAK,GAAG;YACJS,IAAI,GAAGvB,KAAK,GAAG,IAAI,CAAC3B,KAAK,GAAG,CAACwC,QAAQ,CAAC,CAAC,CAAC,GAAGO,KAAK,IAAIH,SAAS;YAC7DO,IAAI,GAAGvB,KAAK,GAAG,CAACY,QAAQ,CAAC,CAAC,CAAC,GAAGQ,KAAK,IAAIH,UAAU;YACjD,CAACJ,EAAE,EAAEC,EAAE,CAAC,GAAG,CAAC,CAACD,EAAE,EAAE,CAACC,EAAE,CAAC;YACrB;UACJ,KAAK,GAAG;YACJQ,IAAI,GACAvB,KAAK,GACD,CAACa,QAAQ,CAAC,CAAC,CAAC,GAAGO,KAAK,GAAG,IAAI,CAACM,MAAM,GAAGR,UAAU,IAAID,SAAS;YACpEO,IAAI,GACAvB,KAAK,GACD,CAACY,QAAQ,CAAC,CAAC,CAAC,GAAGQ,KAAK,GAAG,IAAI,CAAChD,KAAK,GAAG4C,SAAS,IAAIC,UAAU;YACnE,CAACJ,EAAE,EAAEC,EAAE,CAAC,GAAG,CAAC,CAACA,EAAE,EAAED,EAAE,CAAC;YACpB;UACJ;YAAS;QACb;QACA,IAAI,CAACa,KAAK,CAACJ,IAAI,GAAGd,WAAW,EAAEe,IAAI,GAAGd,YAAY,EAAEI,EAAE,EAAEC,EAAE,CAAC;MAC/D,CAAC,MACI;QACD,IAAI,CAACY,KAAK,CAAC3B,KAAK,GAAGS,WAAW,EAAER,KAAK,GAAGS,YAAY,EAAE,IAAI,CAACrC,KAAK,GAAGoC,WAAW,EAAE,IAAI,CAACiB,MAAM,GAAGhB,YAAY,CAAC;MAC/G;MACA/H,sBAAsB,CAAC,IAAI,EAAEjB,yBAAyB,EAAE,GAAG,EAAEa,0BAA0B,CAAC,CAAC8C,IAAI,CAAC,IAAI,CAAC;MACnG,IAAI,CAACuB,YAAY,GAAG,IAAI;MACxB,IAAI,CAACjD,SAAS,CAACgD,eAAe,GAAG,KAAK;IAC1C,CAAC,MACI;MACD,IAAI,CAACC,YAAY,GAAG,KAAK;MACzB,IAAI,CAACjD,SAAS,CAACgD,eAAe,GAAG,IAAI;IACzC;IACA;IACA;IACA;IACA,OAAO,IAAI,CAACT,GAAG;EACnB;EACAqB,cAAcA,CAACS,KAAK,EAAE;IAClB;IACA,MAAM4D,aAAa,GAAG5D,KAAK,CAAC4D,aAAa,IAAIC,MAAM,CAACD,aAAa;IACjE,MAAM;MAAEE;IAAM,CAAC,GAAGF,aAAa;IAC/B,IAAIE,KAAK,CAACC,MAAM,KAAK,CAAC,IAAID,KAAK,CAAC,CAAC,CAAC,KAAK,YAAY,EAAE;MACjD;IACJ;IACA9D,KAAK,CAAC4B,cAAc,CAAC,CAAC;IACtB,MAAMoC,KAAK,GAAGrJ,sBAAsB,CAAChB,EAAE,EAAEA,EAAE,EAAE,GAAG,EAAEc,kCAAkC,CAAC,CAAC4C,IAAI,CAAC1D,EAAE,EAAEiK,aAAa,CAACK,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAACC,UAAU,CAAC/I,WAAW,EAAE,IAAI,CAAC;IACjK,IAAI,CAAC6I,KAAK,EAAE;MACR;IACJ;IACA,MAAMG,SAAS,GAAGN,MAAM,CAACO,YAAY,CAAC,CAAC;IACvC,IAAI,CAACD,SAAS,CAACE,UAAU,EAAE;MACvB;IACJ;IACA,IAAI,CAAC1I,SAAS,CAAC2I,SAAS,CAAC,CAAC;IAC1BH,SAAS,CAACI,kBAAkB,CAAC,CAAC;IAC9B,MAAMC,KAAK,GAAGL,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC;IACrC,IAAI,CAACT,KAAK,CAACU,QAAQ,CAAC,IAAI,CAAC,EAAE;MACvBF,KAAK,CAACG,UAAU,CAACjI,QAAQ,CAACkI,cAAc,CAACZ,KAAK,CAAC,CAAC;MAChD,IAAI,CAACrI,SAAS,CAAC2I,SAAS,CAAC,CAAC;MAC1BH,SAAS,CAACU,eAAe,CAAC,CAAC;MAC3B;IACJ;IACA;IACA,MAAM;MAAEC,cAAc;MAAEC;IAAY,CAAC,GAAGP,KAAK;IAC7C,MAAMQ,YAAY,GAAG,EAAE;IACvB,MAAMC,WAAW,GAAG,EAAE;IACtB,IAAIH,cAAc,CAACI,QAAQ,KAAKC,IAAI,CAACC,SAAS,EAAE;MAC5C,MAAMnH,MAAM,GAAG6G,cAAc,CAACO,aAAa;MAC3CJ,WAAW,CAACK,IAAI;MAChB;MACAR,cAAc,CAACS,SAAS,CAACC,KAAK,CAACT,WAAW,CAAC,CAACb,UAAU,CAAC/I,WAAW,EAAE,EAAE,CAAC,CAAC;MACxE,IAAI8C,MAAM,KAAK,IAAI,CAACtC,SAAS,EAAE;QAC3B,IAAI8J,MAAM,GAAGT,YAAY;QACzB,KAAK,MAAMU,KAAK,IAAI,IAAI,CAAC/J,SAAS,CAACgK,UAAU,EAAE;UAC3C,IAAID,KAAK,KAAKzH,MAAM,EAAE;YAClBwH,MAAM,GAAGR,WAAW;YACpB;UACJ;UACAQ,MAAM,CAACH,IAAI,CAAC3K,sBAAsB,CAAChB,EAAE,EAAEA,EAAE,EAAE,GAAG,EAAEW,8BAA8B,CAAC,CAAC+C,IAAI,CAAC1D,EAAE,EAAE+L,KAAK,CAAC,CAAC;QACpG;MACJ;MACAV,YAAY,CAACM,IAAI,CAACR,cAAc,CAACS,SAAS,CACrCC,KAAK,CAAC,CAAC,EAAET,WAAW;MACrB;MAAA,CACCb,UAAU,CAAC/I,WAAW,EAAE,EAAE,CAAC,CAAC;IACrC,CAAC,MACI,IAAI2J,cAAc,KAAK,IAAI,CAACnJ,SAAS,EAAE;MACxC,IAAI8J,MAAM,GAAGT,YAAY;MACzB,IAAIY,CAAC,GAAG,CAAC;MACT,KAAK,MAAMF,KAAK,IAAI,IAAI,CAAC/J,SAAS,CAACgK,UAAU,EAAE;QAC3C,IAAIC,CAAC,EAAE,KAAKb,WAAW,EAAE;UACrBU,MAAM,GAAGR,WAAW;QACxB;QACAQ,MAAM,CAACH,IAAI,CAAC3K,sBAAsB,CAAChB,EAAE,EAAEA,EAAE,EAAE,GAAG,EAAEW,8BAA8B,CAAC,CAAC+C,IAAI,CAAC1D,EAAE,EAAE+L,KAAK,CAAC,CAAC;MACpG;IACJ;IACA9K,sBAAsB,CAAC,IAAI,EAAEf,uBAAuB,EAAE,GAAGmL,YAAY,CAACa,IAAI,CAAC,IAAI,CAAC,GAAG7B,KAAK,GAAGiB,WAAW,CAACY,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC;IACzHlL,sBAAsB,CAAC,IAAI,EAAEjB,yBAAyB,EAAE,GAAG,EAAEa,0BAA0B,CAAC,CAAC8C,IAAI,CAAC,IAAI,CAAC;IACnG;IACA,MAAMyI,QAAQ,GAAG,IAAIC,KAAK,CAAC,CAAC;IAC5B,IAAIC,YAAY,GAAGhB,YAAY,CAACiB,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAACpC,MAAM,EAAE,CAAC,CAAC;IAC3E,KAAK,MAAM;MAAEqC;IAAW,CAAC,IAAI,IAAI,CAACzK,SAAS,CAACgK,UAAU,EAAE;MACpD;MACA,IAAIS,UAAU,CAAClB,QAAQ,KAAKC,IAAI,CAACC,SAAS,EAAE;QACxC,MAAMrB,MAAM,GAAGqC,UAAU,CAACb,SAAS,CAACxB,MAAM;QAC1C,IAAIiC,YAAY,IAAIjC,MAAM,EAAE;UACxB+B,QAAQ,CAACO,QAAQ,CAACD,UAAU,EAAEJ,YAAY,CAAC;UAC3CF,QAAQ,CAACQ,MAAM,CAACF,UAAU,EAAEJ,YAAY,CAAC;UACzC;QACJ;QACAA,YAAY,IAAIjC,MAAM;MAC1B;IACJ;IACAI,SAAS,CAACoC,eAAe,CAAC,CAAC;IAC3BpC,SAAS,CAACqC,QAAQ,CAACV,QAAQ,CAAC;EAChC;EACA;EACA,IAAIW,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC9K,SAAS;EACzB;EACA;EACA,OAAO+K,WAAWA,CAACC,IAAI,EAAE1I,MAAM,EAAE3B,SAAS,EAAE;IACxC,IAAIsK,WAAW,GAAG,IAAI;IACtB,IAAID,IAAI,YAAYzL,yBAAyB,EAAE;MAC3C,MAAM;QAAEyL,IAAI,EAAE;UAAEE,qBAAqB,EAAE;YAAE3K,QAAQ;YAAE4K;UAAU,CAAC;UAAEC,IAAI;UAAEtD,QAAQ;UAAE3H;QAAG,CAAC;QAAEkL,WAAW;QAAEC,YAAY;QAAEhJ,MAAM,EAAE;UAAEiJ,IAAI,EAAE;YAAEC;UAAW;QAAE;MAAE,CAAC,GAAGR,IAAI;MAC1J;MACA;MACA,IAAI,CAACK,WAAW,IAAIA,WAAW,CAACjD,MAAM,KAAK,CAAC,EAAE;QAC1C;QACA,OAAO,IAAI;MACf;MACA6C,WAAW,GAAGD,IAAI,GAAG;QACjBS,cAAc,EAAEtM,oBAAoB,CAAC0D,QAAQ;QAC7CzC,KAAK,EAAEsL,KAAK,CAACC,IAAI,CAACR,SAAS,CAAC;QAC5B5K,QAAQ;QACRe,KAAK,EAAE+J,WAAW,CAACnB,IAAI,CAAC,IAAI,CAAC;QAC7BhD,QAAQ,EAAEoE,YAAY;QACtBM,SAAS,EAAEJ,UAAU,GAAG,CAAC;QACzBJ,IAAI,EAAEA,IAAI,CAACvB,KAAK,CAAC,CAAC,CAAC;QACnB/B,QAAQ;QACR3H,EAAE;QACF0L,OAAO,EAAE;MACb,CAAC;IACL;IACA,MAAMC,MAAM,GAAG,KAAK,CAACf,WAAW,CAACC,IAAI,EAAE1I,MAAM,EAAE3B,SAAS,CAAC;IACzD1B,sBAAsB,CAAC6M,MAAM,EAAEzN,wBAAwB,EAAE2M,IAAI,CAACzK,QAAQ,EAAE,GAAG,CAAC;IAC5E;IACAtB,sBAAsB,CAAC6M,MAAM,EAAE7N,qBAAqB,EAAEmB,IAAI,CAAC2M,YAAY,CAAC,GAAGf,IAAI,CAAC5K,KAAK,CAAC,EAAE,GAAG,CAAC;IAC5FnB,sBAAsB,CAAC6M,MAAM,EAAE5N,uBAAuB,EAAEc,sBAAsB,CAAChB,EAAE,EAAEA,EAAE,EAAE,GAAG,EAAEc,kCAAkC,CAAC,CAAC4C,IAAI,CAAC1D,EAAE,EAAEgN,IAAI,CAAC1J,KAAK,CAAC,EAAE,GAAG,CAAC;IAC1JwK,MAAM,CAAC7E,mBAAmB,GAAG+D,IAAI,CAAC7K,EAAE,IAAI,IAAI;IAC5ClB,sBAAsB,CAAC6M,MAAM,EAAExN,2BAA2B,EAAE2M,WAAW,EAAE,GAAG,CAAC;IAC7E,OAAOa,MAAM;EACjB;EACA;EACAE,SAASA,CAACC,YAAY,GAAG,KAAK,EAAE;IAC5B,IAAI,IAAI,CAACnH,OAAO,CAAC,CAAC,EAAE;MAChB,OAAO,IAAI;IACf;IACA,IAAI,IAAI,CAAC+G,OAAO,EAAE;MACd,OAAO;QACHD,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBzL,EAAE,EAAE,IAAI,CAAC8G,mBAAmB;QAC5B4E,OAAO,EAAE;MACb,CAAC;IACL;IACA,MAAMK,OAAO,GAAGlO,EAAE,CAACiD,gBAAgB,GAAG,IAAI,CAACmB,WAAW;IACtD,MAAMgJ,IAAI,GAAG,IAAI,CAACe,OAAO,CAACD,OAAO,EAAEA,OAAO,CAAC;IAC3C,MAAM9L,KAAK,GAAGf,gBAAgB,CAAC+M,aAAa,CAACC,OAAO,CAAC,IAAI,CAAC7J,eAAe,GACnE1B,gBAAgB,CAAC,IAAI,CAACd,SAAS,CAAC,CAACI,KAAK,GACtCpB,sBAAsB,CAAC,IAAI,EAAEf,qBAAqB,EAAE,GAAG,CAAC,CAAC;IAC/D,MAAMqO,UAAU,GAAG;MACfb,cAAc,EAAEtM,oBAAoB,CAAC0D,QAAQ;MAC7CzC,KAAK;MACLG,QAAQ,EAAEvB,sBAAsB,CAAC,IAAI,EAAEX,wBAAwB,EAAE,GAAG,CAAC;MACrEiD,KAAK,EAAEtC,sBAAsB,CAAC,IAAI,EAAEjB,yBAAyB,EAAE,GAAG,EAAEc,gCAAgC,CAAC,CAAC6C,IAAI,CAAC,IAAI,CAAC;MAChHkK,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBR,IAAI;MACJtD,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvByE,kBAAkB,EAAE,IAAI,CAACC;IAC7B,CAAC;IACD,IAAIP,YAAY,EAAE;MACd;MACA;MACA,OAAOK,UAAU;IACrB;IACA,IAAI,IAAI,CAACrF,mBAAmB,IAAI,CAACjI,sBAAsB,CAAC,IAAI,EAAEjB,yBAAyB,EAAE,GAAG,EAAEgB,iCAAiC,CAAC,CAAC2C,IAAI,CAAC,IAAI,EAAE4K,UAAU,CAAC,EAAE;MACrJ,OAAO,IAAI;IACf;IACA;IACAA,UAAU,CAACnM,EAAE,GAAG,IAAI,CAAC8G,mBAAmB;IACxC,OAAOqF,UAAU;EACrB;EACA;EACAG,uBAAuBA,CAACC,UAAU,EAAE;IAChC,MAAMC,OAAO,GAAG,KAAK,CAACF,uBAAuB,CAACC,UAAU,CAAC;IACzD,IAAI,IAAI,CAACb,OAAO,EAAE;MACd,OAAOc,OAAO;IAClB;IACA,MAAM;MAAE9L;IAAM,CAAC,GAAG8L,OAAO;IACzB9L,KAAK,CAACN,QAAQ,GAAG,QAAQvB,sBAAsB,CAAC,IAAI,EAAEX,wBAAwB,EAAE,GAAG,CAAC,2BAA2B;IAC/GwC,KAAK,CAACT,KAAK,GAAGpB,sBAAsB,CAAC,IAAI,EAAEf,qBAAqB,EAAE,GAAG,CAAC;IACtE0O,OAAO,CAACC,eAAe,CAAC,CAAC;IACzB,KAAK,MAAMpC,IAAI,IAAIxL,sBAAsB,CAAC,IAAI,EAAEd,uBAAuB,EAAE,GAAG,CAAC,CAAC2O,KAAK,CAAC,IAAI,CAAC,EAAE;MACvF,MAAMtK,GAAG,GAAGxB,QAAQ,CAACwF,aAAa,CAAC,KAAK,CAAC;MACzChE,GAAG,CAACsE,MAAM,CAAC2D,IAAI,GAAGzJ,QAAQ,CAACkI,cAAc,CAACuB,IAAI,CAAC,GAAGzJ,QAAQ,CAACwF,aAAa,CAAC,IAAI,CAAC,CAAC;MAC/EoG,OAAO,CAAC9F,MAAM,CAACtE,GAAG,CAAC;IACvB;IACA,MAAM2J,OAAO,GAAGlO,EAAE,CAACiD,gBAAgB,GAAG,IAAI,CAACmB,WAAW;IACtDsK,UAAU,CAACI,YAAY,CAAC;MACpB1B,IAAI,EAAE,IAAI,CAACe,OAAO,CAACD,OAAO,EAAEA,OAAO,CAAC;MACpCa,YAAY,EAAE/N,sBAAsB,CAAC,IAAI,EAAEd,uBAAuB,EAAE,GAAG;IAC3E,CAAC,CAAC;IACF,OAAOyO,OAAO;EAClB;EACAK,sBAAsBA,CAACN,UAAU,EAAE;IAC/B,KAAK,CAACM,sBAAsB,CAACN,UAAU,CAAC;IACxCA,UAAU,CAACO,WAAW,CAAC,CAAC;EAC5B;EACA;EACA;EACA;EACA;EACAC,MAAMA,CAAA,EAAG;IACL,MAAMlC,IAAI,GAAG,IAAI,CAACgB,SAAS,CAAC,CAAC;IAC7B,OAAOhB,IAAI;EACf;AACJ;AACAhN,EAAE,GAAGyB,cAAc,EAAExB,qBAAqB,GAAG,IAAIkP,OAAO,CAAC,CAAC,EAAEjP,uBAAuB,GAAG,IAAIiP,OAAO,CAAC,CAAC,EAAEhP,2BAA2B,GAAG,IAAIgP,OAAO,CAAC,CAAC,EAAE/O,0BAA0B,GAAG,IAAI+O,OAAO,CAAC,CAAC,EAAE9O,wBAAwB,GAAG,IAAI8O,OAAO,CAAC,CAAC,EAAE7O,2BAA2B,GAAG,IAAI6O,OAAO,CAAC,CAAC,EAAEpP,yBAAyB,GAAG,IAAIqP,OAAO,CAAC,CAAC,EAAE7O,8BAA8B,GAAG,SAASA,8BAA8BA,CAACgC,QAAQ,EAAE;EAC/Y,MAAM8M,WAAW,GAAGC,IAAI,IAAI;IACxB,IAAI,CAACtN,SAAS,CAACa,KAAK,CAACN,QAAQ,GAAG,QAAQ+M,IAAI,2BAA2B;IACvE,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE,EAAED,IAAI,GAAGtO,sBAAsB,CAAC,IAAI,EAAEX,wBAAwB,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC+D,WAAW,CAAC;IAC3GnD,sBAAsB,CAAC,IAAI,EAAEZ,wBAAwB,EAAEiP,IAAI,EAAE,GAAG,CAAC;IACjEtO,sBAAsB,CAAC,IAAI,EAAEjB,yBAAyB,EAAE,GAAG,EAAEW,mCAAmC,CAAC,CAACgD,IAAI,CAAC,IAAI,CAAC;EAChH,CAAC;EACD,MAAM8L,aAAa,GAAGxO,sBAAsB,CAAC,IAAI,EAAEX,wBAAwB,EAAE,GAAG,CAAC;EACjF,IAAI,CAACmH,WAAW,CAAC;IACbC,GAAG,EAAE4H,WAAW,CAAC7J,IAAI,CAAC,IAAI,EAAEjD,QAAQ,CAAC;IACrCmF,IAAI,EAAE2H,WAAW,CAAC7J,IAAI,CAAC,IAAI,EAAEgK,aAAa,CAAC;IAC3CC,IAAI,EAAE,IAAI,CAACzL,UAAU,CAAC0L,QAAQ,CAAClK,IAAI,CAAC,IAAI,CAACxB,UAAU,EAAE,IAAI,CAAC;IAC1D2D,QAAQ,EAAE,IAAI;IACdtE,IAAI,EAAEnC,0BAA0B,CAACqC,aAAa;IAC9CoM,mBAAmB,EAAE,IAAI;IACzBC,QAAQ,EAAE;EACd,CAAC,CAAC;AACN,CAAC,EAAEpP,2BAA2B,GAAG,SAASA,2BAA2BA,CAAC4B,KAAK,EAAE;EACzE,MAAMyN,QAAQ,GAAGC,GAAG,IAAI;IACpB7O,sBAAsB,CAAC,IAAI,EAAEhB,qBAAqB,EAAE,IAAI,CAAC+B,SAAS,CAACa,KAAK,CAACT,KAAK,GAAG0N,GAAG,EAAE,GAAG,CAAC;EAC9F,CAAC;EACD,MAAMC,UAAU,GAAG/O,sBAAsB,CAAC,IAAI,EAAEf,qBAAqB,EAAE,GAAG,CAAC;EAC3E,IAAI,CAACuH,WAAW,CAAC;IACbC,GAAG,EAAEoI,QAAQ,CAACrK,IAAI,CAAC,IAAI,EAAEpD,KAAK,CAAC;IAC/BsF,IAAI,EAAEmI,QAAQ,CAACrK,IAAI,CAAC,IAAI,EAAEuK,UAAU,CAAC;IACrCN,IAAI,EAAE,IAAI,CAACzL,UAAU,CAAC0L,QAAQ,CAAClK,IAAI,CAAC,IAAI,CAACxB,UAAU,EAAE,IAAI,CAAC;IAC1D2D,QAAQ,EAAE,IAAI;IACdtE,IAAI,EAAEnC,0BAA0B,CAACsC,cAAc;IAC/CmM,mBAAmB,EAAE,IAAI;IACzBC,QAAQ,EAAE;EACd,CAAC,CAAC;AACN,CAAC,EAAEnP,2BAA2B,GAAG,SAASA,2BAA2BA,CAAA,EAAG;EACpE;EACA,MAAMqL,MAAM,GAAG,EAAE;EACjB,IAAI,CAAC9J,SAAS,CAAC2I,SAAS,CAAC,CAAC;EAC1B,KAAK,MAAMoB,KAAK,IAAI,IAAI,CAAC/J,SAAS,CAACgK,UAAU,EAAE;IAC3CF,MAAM,CAACH,IAAI,CAAC3K,sBAAsB,CAAChB,EAAE,EAAEA,EAAE,EAAE,GAAG,EAAEW,8BAA8B,CAAC,CAAC+C,IAAI,CAAC1D,EAAE,EAAE+L,KAAK,CAAC,CAAC;EACpG;EACA,OAAOD,MAAM,CAACI,IAAI,CAAC,IAAI,CAAC;AAC5B,CAAC,EAAExL,mCAAmC,GAAG,SAASA,mCAAmCA,CAAA,EAAG;EACpF,MAAM,CAACoI,WAAW,EAAEC,YAAY,CAAC,GAAG,IAAI,CAACC,gBAAgB;EACzD,IAAIoE,IAAI;EACR,IAAI,IAAI,CAAC5I,eAAe,EAAE;IACtB4I,IAAI,GAAG,IAAI,CAAC7I,GAAG,CAACyL,qBAAqB,CAAC,CAAC;EAC3C,CAAC,MACI;IACD;IACA;IACA,MAAM;MAAEC,YAAY;MAAE1L;IAAI,CAAC,GAAG,IAAI;IAClC,MAAM2L,YAAY,GAAG3L,GAAG,CAAC1B,KAAK,CAACsN,OAAO;IACtC,MAAMC,eAAe,GAAG7L,GAAG,CAACO,SAAS,CAACuL,QAAQ,CAAC,QAAQ,CAAC,IAAI9L,GAAG,CAACO,SAAS,CAACuL,QAAQ,CAAC,UAAU,CAAC;IAC9F9L,GAAG,CAACO,SAAS,CAACC,MAAM,CAAC,QAAQ,CAAC;IAC9BR,GAAG,CAACO,SAAS,CAACC,MAAM,CAAC,UAAU,CAAC;IAChCR,GAAG,CAAC1B,KAAK,CAACsN,OAAO,GAAG,QAAQ;IAC5BF,YAAY,CAAC1L,GAAG,CAACsE,MAAM,CAAC,IAAI,CAACtE,GAAG,CAAC;IACjC6I,IAAI,GAAG7I,GAAG,CAACyL,qBAAqB,CAAC,CAAC;IAClCzL,GAAG,CAACQ,MAAM,CAAC,CAAC;IACZR,GAAG,CAAC1B,KAAK,CAACsN,OAAO,GAAGD,YAAY;IAChC;IACA3L,GAAG,CAACO,SAAS,CAACwL,MAAM,CAAC,UAAU,EAAEF,eAAe,CAAC;EACrD;EACA;EACA;EACA,IAAI,IAAI,CAACtG,QAAQ,GAAG,GAAG,KAAK,IAAI,CAACyG,cAAc,GAAG,GAAG,EAAE;IACnD,IAAI,CAAC7J,KAAK,GAAG0G,IAAI,CAAC1G,KAAK,GAAGoC,WAAW;IACrC,IAAI,CAACiB,MAAM,GAAGqD,IAAI,CAACrD,MAAM,GAAGhB,YAAY;EAC5C,CAAC,MACI;IACD,IAAI,CAACrC,KAAK,GAAG0G,IAAI,CAACrD,MAAM,GAAGjB,WAAW;IACtC,IAAI,CAACiB,MAAM,GAAGqD,IAAI,CAAC1G,KAAK,GAAGqC,YAAY;EAC3C;EACA,IAAI,CAACyH,iBAAiB,CAAC,CAAC;AAC5B,CAAC,EAAE7P,8BAA8B,GAAG,SAASA,8BAA8BA,CAAC8P,IAAI,EAAE;EAC9E,OAAO,CAACA,IAAI,CAAClF,QAAQ,KAAKC,IAAI,CAACC,SAAS,GAAGgF,IAAI,CAAC7E,SAAS,GAAG6E,IAAI,CAACzJ,SAAS,EAAEuD,UAAU,CAAC/I,WAAW,EAAE,EAAE,CAAC;AAC3G,CAAC,EAAEZ,0BAA0B,GAAG,SAASA,0BAA0BA,CAAA,EAAG;EAClE,IAAI,CAACoB,SAAS,CAAC4M,eAAe,CAAC,CAAC;EAChC,IAAI,CAAC5N,sBAAsB,CAAC,IAAI,EAAEd,uBAAuB,EAAE,GAAG,CAAC,EAAE;IAC7D;EACJ;EACA,KAAK,MAAMsM,IAAI,IAAIxL,sBAAsB,CAAC,IAAI,EAAEd,uBAAuB,EAAE,GAAG,CAAC,CAAC2O,KAAK,CAAC,IAAI,CAAC,EAAE;IACvF,MAAMtK,GAAG,GAAGxB,QAAQ,CAACwF,aAAa,CAAC,KAAK,CAAC;IACzChE,GAAG,CAACsE,MAAM,CAAC2D,IAAI,GAAGzJ,QAAQ,CAACkI,cAAc,CAACuB,IAAI,CAAC,GAAGzJ,QAAQ,CAACwF,aAAa,CAAC,IAAI,CAAC,CAAC;IAC/E,IAAI,CAACvG,SAAS,CAAC6G,MAAM,CAACtE,GAAG,CAAC;EAC9B;AACJ,CAAC,EAAE1D,gCAAgC,GAAG,SAASA,gCAAgCA,CAAA,EAAG;EAC9E;EACA,OAAOG,sBAAsB,CAAC,IAAI,EAAEd,uBAAuB,EAAE,GAAG,CAAC,CAACqK,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC;AAC7F,CAAC,EAAEzJ,kCAAkC,GAAG,SAASA,kCAAkCA,CAAC6N,OAAO,EAAE;EACzF,OAAOA,OAAO,CAACpE,UAAU,CAAC,GAAG,EAAE,MAAM,CAAC;AAC1C,CAAC,EAAExJ,iCAAiC,GAAG,SAASA,iCAAiCA,CAACuN,UAAU,EAAE;EAC1F,MAAM;IAAEhL,KAAK;IAAEf,QAAQ;IAAEH,KAAK;IAAEwL;EAAU,CAAC,GAAG5M,sBAAsB,CAAC,IAAI,EAAEV,2BAA2B,EAAE,GAAG,CAAC;EAC5G,OAAQ,IAAI,CAACoQ,aAAa,IACtBpC,UAAU,CAAChL,KAAK,KAAKA,KAAK,IAC1BgL,UAAU,CAAC/L,QAAQ,KAAKA,QAAQ,IAChC+L,UAAU,CAAClM,KAAK,CAACuO,IAAI,CAAC,CAACC,CAAC,EAAE3E,CAAC,KAAK2E,CAAC,KAAKxO,KAAK,CAAC6J,CAAC,CAAC,CAAC,IAC/CqC,UAAU,CAACV,SAAS,KAAKA,SAAS;AAC1C,CAAC;AACDnM,cAAc,CAACoP,uBAAuB,GAAG,EAAE;AAC3CpP,cAAc,CAACwB,gBAAgB,GAAG,CAAC;AACnC;AACAxB,cAAc,CAACY,aAAa,GAAG,SAAS;AACxCZ,cAAc,CAACe,gBAAgB,GAAG,EAAE;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAf,cAAc,CAACqP,KAAK,GAAG,UAAU;AACjCrP,cAAc,CAACsP,WAAW,GAAG5P,oBAAoB,CAAC0D,QAAQ;AAC1D,SAASpD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}