{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst a = e => Math.max(e, 0);\nclass f {\n  constructor(t) {\n    this.offset = t;\n  }\n}\nclass d {\n  constructor(t) {\n    this.skip = t;\n  }\n}\nclass w {\n  constructor(t, s) {\n    this.onScrollAction = t, this.onPageAction = s, this.direction = \"vertical\", this.firstLoaded = 0, this.lastLoaded = 0, this.lastScrollTop = 0, this.take = 0, this.total = 0, this.rowHeightService = null, this.bottomOffset = 0, this.topOffset = 0;\n  }\n  create(t, s, h, l, i = 0, n = 0, r = \"vertical\") {\n    this.rowHeightService = t, this.firstLoaded = s, this.lastLoaded = s + h, this.take = h, this.total = l, this.lastScrollTop = 0, this.topOffset = i, this.bottomOffset = n, this.direction = r;\n    const o = this.rowsForHeight(i),\n      c = a(s - o);\n    this.onScrollAction(new f(this.rowOffset(c))), this.onPageAction(new d(c));\n  }\n  onScroll({\n    scrollLeft: t,\n    scrollTop: s,\n    offsetHeight: h,\n    offsetWidth: l\n  }) {\n    const i = this.direction === \"vertical\" ? s : t,\n      n = this.direction === \"vertical\" ? h : l;\n    if (this.lastScrollTop === i || !this.rowHeightService) return;\n    const r = this.lastScrollTop >= i;\n    this.lastScrollTop = i;\n    const o = this.rowHeightService.index(a(i - this.topOffset)),\n      c = this.rowHeightService.index(a(i + n - this.bottomOffset));\n    if (!r && c >= this.lastLoaded && this.lastLoaded < this.total && (this.firstLoaded = o, this.onScrollAction(new f(this.rowOffset(o))), this.lastLoaded = Math.min(this.firstLoaded + this.take, this.total), this.onPageAction(new d(this.firstLoaded))), r && o <= this.firstLoaded) {\n      const S = Math.floor(this.take * 0.3);\n      this.firstLoaded = a(o - S), this.onScrollAction(new f(this.rowOffset(this.firstLoaded))), this.lastLoaded = Math.min(this.firstLoaded + this.take, this.total), this.onPageAction(new d(this.firstLoaded));\n    }\n  }\n  rowOffset(t) {\n    return this.rowHeightService ? this.rowHeightService.offset(t) + this.topOffset : 0;\n  }\n  rowsForHeight(t) {\n    return this.rowHeightService ? Math.ceil(t / this.rowHeightService.height(0)) : 0;\n  }\n}\nexport { d as PageAction, f as ScrollAction, w as ScrollerService };", "map": {"version": 3, "names": ["a", "e", "Math", "max", "f", "constructor", "t", "offset", "d", "skip", "w", "s", "onScrollAction", "onPageAction", "direction", "firstLoaded", "lastLoaded", "lastScrollTop", "take", "total", "rowHeightService", "bottomOffset", "topOffset", "create", "h", "l", "i", "n", "r", "o", "rowsForHeight", "c", "rowOffset", "onScroll", "scrollLeft", "scrollTop", "offsetHeight", "offsetWidth", "index", "min", "S", "floor", "ceil", "height", "PageAction", "ScrollAction", "ScrollerService"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dateinputs/virtualization/services/ScrollerService.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst a = (e) => Math.max(e, 0);\nclass f {\n  constructor(t) {\n    this.offset = t;\n  }\n}\nclass d {\n  constructor(t) {\n    this.skip = t;\n  }\n}\nclass w {\n  constructor(t, s) {\n    this.onScrollAction = t, this.onPageAction = s, this.direction = \"vertical\", this.firstLoaded = 0, this.lastLoaded = 0, this.lastScrollTop = 0, this.take = 0, this.total = 0, this.rowHeightService = null, this.bottomOffset = 0, this.topOffset = 0;\n  }\n  create(t, s, h, l, i = 0, n = 0, r = \"vertical\") {\n    this.rowHeightService = t, this.firstLoaded = s, this.lastLoaded = s + h, this.take = h, this.total = l, this.lastScrollTop = 0, this.topOffset = i, this.bottomOffset = n, this.direction = r;\n    const o = this.rowsForHeight(i), c = a(s - o);\n    this.onScrollAction(new f(this.rowOffset(c))), this.onPageAction(new d(c));\n  }\n  onScroll({ scrollLeft: t, scrollTop: s, offsetHeight: h, offsetWidth: l }) {\n    const i = this.direction === \"vertical\" ? s : t, n = this.direction === \"vertical\" ? h : l;\n    if (this.lastScrollTop === i || !this.rowHeightService)\n      return;\n    const r = this.lastScrollTop >= i;\n    this.lastScrollTop = i;\n    const o = this.rowHeightService.index(a(i - this.topOffset)), c = this.rowHeightService.index(a(i + n - this.bottomOffset));\n    if (!r && c >= this.lastLoaded && this.lastLoaded < this.total && (this.firstLoaded = o, this.onScrollAction(new f(this.rowOffset(o))), this.lastLoaded = Math.min(this.firstLoaded + this.take, this.total), this.onPageAction(new d(this.firstLoaded))), r && o <= this.firstLoaded) {\n      const S = Math.floor(this.take * 0.3);\n      this.firstLoaded = a(o - S), this.onScrollAction(new f(this.rowOffset(this.firstLoaded))), this.lastLoaded = Math.min(this.firstLoaded + this.take, this.total), this.onPageAction(new d(this.firstLoaded));\n    }\n  }\n  rowOffset(t) {\n    return this.rowHeightService ? this.rowHeightService.offset(t) + this.topOffset : 0;\n  }\n  rowsForHeight(t) {\n    return this.rowHeightService ? Math.ceil(t / this.rowHeightService.height(0)) : 0;\n  }\n}\nexport {\n  d as PageAction,\n  f as ScrollAction,\n  w as ScrollerService\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAIC,CAAC,IAAKC,IAAI,CAACC,GAAG,CAACF,CAAC,EAAE,CAAC,CAAC;AAC/B,MAAMG,CAAC,CAAC;EACNC,WAAWA,CAACC,CAAC,EAAE;IACb,IAAI,CAACC,MAAM,GAAGD,CAAC;EACjB;AACF;AACA,MAAME,CAAC,CAAC;EACNH,WAAWA,CAACC,CAAC,EAAE;IACb,IAAI,CAACG,IAAI,GAAGH,CAAC;EACf;AACF;AACA,MAAMI,CAAC,CAAC;EACNL,WAAWA,CAACC,CAAC,EAAEK,CAAC,EAAE;IAChB,IAAI,CAACC,cAAc,GAAGN,CAAC,EAAE,IAAI,CAACO,YAAY,GAAGF,CAAC,EAAE,IAAI,CAACG,SAAS,GAAG,UAAU,EAAE,IAAI,CAACC,WAAW,GAAG,CAAC,EAAE,IAAI,CAACC,UAAU,GAAG,CAAC,EAAE,IAAI,CAACC,aAAa,GAAG,CAAC,EAAE,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE,IAAI,CAACC,KAAK,GAAG,CAAC,EAAE,IAAI,CAACC,gBAAgB,GAAG,IAAI,EAAE,IAAI,CAACC,YAAY,GAAG,CAAC,EAAE,IAAI,CAACC,SAAS,GAAG,CAAC;EACxP;EACAC,MAAMA,CAACjB,CAAC,EAAEK,CAAC,EAAEa,CAAC,EAAEC,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,UAAU,EAAE;IAC/C,IAAI,CAACR,gBAAgB,GAAGd,CAAC,EAAE,IAAI,CAACS,WAAW,GAAGJ,CAAC,EAAE,IAAI,CAACK,UAAU,GAAGL,CAAC,GAAGa,CAAC,EAAE,IAAI,CAACN,IAAI,GAAGM,CAAC,EAAE,IAAI,CAACL,KAAK,GAAGM,CAAC,EAAE,IAAI,CAACR,aAAa,GAAG,CAAC,EAAE,IAAI,CAACK,SAAS,GAAGI,CAAC,EAAE,IAAI,CAACL,YAAY,GAAGM,CAAC,EAAE,IAAI,CAACb,SAAS,GAAGc,CAAC;IAC9L,MAAMC,CAAC,GAAG,IAAI,CAACC,aAAa,CAACJ,CAAC,CAAC;MAAEK,CAAC,GAAG/B,CAAC,CAACW,CAAC,GAAGkB,CAAC,CAAC;IAC7C,IAAI,CAACjB,cAAc,CAAC,IAAIR,CAAC,CAAC,IAAI,CAAC4B,SAAS,CAACD,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAClB,YAAY,CAAC,IAAIL,CAAC,CAACuB,CAAC,CAAC,CAAC;EAC5E;EACAE,QAAQA,CAAC;IAAEC,UAAU,EAAE5B,CAAC;IAAE6B,SAAS,EAAExB,CAAC;IAAEyB,YAAY,EAAEZ,CAAC;IAAEa,WAAW,EAAEZ;EAAE,CAAC,EAAE;IACzE,MAAMC,CAAC,GAAG,IAAI,CAACZ,SAAS,KAAK,UAAU,GAAGH,CAAC,GAAGL,CAAC;MAAEqB,CAAC,GAAG,IAAI,CAACb,SAAS,KAAK,UAAU,GAAGU,CAAC,GAAGC,CAAC;IAC1F,IAAI,IAAI,CAACR,aAAa,KAAKS,CAAC,IAAI,CAAC,IAAI,CAACN,gBAAgB,EACpD;IACF,MAAMQ,CAAC,GAAG,IAAI,CAACX,aAAa,IAAIS,CAAC;IACjC,IAAI,CAACT,aAAa,GAAGS,CAAC;IACtB,MAAMG,CAAC,GAAG,IAAI,CAACT,gBAAgB,CAACkB,KAAK,CAACtC,CAAC,CAAC0B,CAAC,GAAG,IAAI,CAACJ,SAAS,CAAC,CAAC;MAAES,CAAC,GAAG,IAAI,CAACX,gBAAgB,CAACkB,KAAK,CAACtC,CAAC,CAAC0B,CAAC,GAAGC,CAAC,GAAG,IAAI,CAACN,YAAY,CAAC,CAAC;IAC3H,IAAI,CAACO,CAAC,IAAIG,CAAC,IAAI,IAAI,CAACf,UAAU,IAAI,IAAI,CAACA,UAAU,GAAG,IAAI,CAACG,KAAK,KAAK,IAAI,CAACJ,WAAW,GAAGc,CAAC,EAAE,IAAI,CAACjB,cAAc,CAAC,IAAIR,CAAC,CAAC,IAAI,CAAC4B,SAAS,CAACH,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAACb,UAAU,GAAGd,IAAI,CAACqC,GAAG,CAAC,IAAI,CAACxB,WAAW,GAAG,IAAI,CAACG,IAAI,EAAE,IAAI,CAACC,KAAK,CAAC,EAAE,IAAI,CAACN,YAAY,CAAC,IAAIL,CAAC,CAAC,IAAI,CAACO,WAAW,CAAC,CAAC,CAAC,EAAEa,CAAC,IAAIC,CAAC,IAAI,IAAI,CAACd,WAAW,EAAE;MACrR,MAAMyB,CAAC,GAAGtC,IAAI,CAACuC,KAAK,CAAC,IAAI,CAACvB,IAAI,GAAG,GAAG,CAAC;MACrC,IAAI,CAACH,WAAW,GAAGf,CAAC,CAAC6B,CAAC,GAAGW,CAAC,CAAC,EAAE,IAAI,CAAC5B,cAAc,CAAC,IAAIR,CAAC,CAAC,IAAI,CAAC4B,SAAS,CAAC,IAAI,CAACjB,WAAW,CAAC,CAAC,CAAC,EAAE,IAAI,CAACC,UAAU,GAAGd,IAAI,CAACqC,GAAG,CAAC,IAAI,CAACxB,WAAW,GAAG,IAAI,CAACG,IAAI,EAAE,IAAI,CAACC,KAAK,CAAC,EAAE,IAAI,CAACN,YAAY,CAAC,IAAIL,CAAC,CAAC,IAAI,CAACO,WAAW,CAAC,CAAC;IAC7M;EACF;EACAiB,SAASA,CAAC1B,CAAC,EAAE;IACX,OAAO,IAAI,CAACc,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACb,MAAM,CAACD,CAAC,CAAC,GAAG,IAAI,CAACgB,SAAS,GAAG,CAAC;EACrF;EACAQ,aAAaA,CAACxB,CAAC,EAAE;IACf,OAAO,IAAI,CAACc,gBAAgB,GAAGlB,IAAI,CAACwC,IAAI,CAACpC,CAAC,GAAG,IAAI,CAACc,gBAAgB,CAACuB,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;EACnF;AACF;AACA,SACEnC,CAAC,IAAIoC,UAAU,EACfxC,CAAC,IAAIyC,YAAY,EACjBnC,CAAC,IAAIoC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}