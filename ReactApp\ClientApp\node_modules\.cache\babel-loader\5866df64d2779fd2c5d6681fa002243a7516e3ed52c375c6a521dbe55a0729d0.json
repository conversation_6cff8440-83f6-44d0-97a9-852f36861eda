{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { Keys as a, treeIdUtils as n, isEnabledAndAllParentsEnabled as s, isItemExpandedAndWithChildren as c } from \"@progress/kendo-react-common\";\nfunction F(o, e, t, g, r) {\n  switch (g) {\n    case a.left:\n      return I();\n    case a.right:\n      return l();\n    case a.up:\n      return y();\n    case a.down:\n      return C();\n    case a.home:\n      return n.ZERO_LEVEL_ZERO_NODE_ID;\n    case a.end:\n      return E();\n    default:\n      return e;\n  }\n  function I() {\n    return n.isIdZeroLevel(e) ||\n    // For expanded disabled items, navigate directly to the parent.\n    // Collapse cannot happen because an event is not fired to the client.\n    r.expanded(o) && s(e, t, r) ? e : n.getDirectParentId(e);\n  }\n  function l() {\n    return c(o, r) ? n.getFirstChildId(e) : e;\n  }\n  function y() {\n    const d = Number(n.getShortId(e)),\n      u = n.getDirectParentId(e);\n    return d ? i(n.createId(d - 1, u), t, r) : n.isIdZeroLevel(e) ? e : u;\n  }\n  function C() {\n    return c(o, r) ? n.getFirstChildId(e) : p(e, t, r) || e;\n  }\n  function E() {\n    let d = (t.length - 1).toString(),\n      u = t[t.length - 1],\n      h;\n    for (; c(u, r);) h = u[r.getChildrenField()], d = n.createId(h.length - 1, d), u = h[h.length - 1];\n    return d;\n  }\n}\nfunction p(o, e, t) {\n  const g = n.getDirectParentId(o),\n    r = g ? n.getItemById(g, e, t.getChildrenField()) : void 0,\n    I = r ? r[t.getChildrenField()] : e,\n    l = Number(n.getShortId(o));\n  return l < I.length - 1 ? n.createId(l + 1, g) : r ? p(g, e, t) : void 0;\n}\nfunction i(o, e, t) {\n  const g = n.getItemById(o, e, t.getChildrenField());\n  return c(g, t) ? i(n.createId(g[t.getChildrenField()].length - 1, o), e, t) : o;\n}\nexport { F as default };", "map": {"version": 3, "names": ["Keys", "a", "treeIdUtils", "n", "isEnabledAndAllParentsEnabled", "s", "isItemExpandedAndWithChildren", "c", "F", "o", "e", "t", "g", "r", "left", "I", "right", "l", "up", "y", "down", "C", "home", "ZERO_LEVEL_ZERO_NODE_ID", "end", "E", "isIdZeroLevel", "expanded", "getDirectParentId", "getFirstChildId", "d", "Number", "getShortId", "u", "i", "createId", "p", "length", "toString", "h", "getC<PERSON>drenField", "getItemById", "default"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-treeview/utils/getItemIdUponKeyboardNavigation.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { Keys as a, treeIdUtils as n, isEnabledAndAllParentsEnabled as s, isItemExpandedAndWithChildren as c } from \"@progress/kendo-react-common\";\nfunction F(o, e, t, g, r) {\n  switch (g) {\n    case a.left:\n      return I();\n    case a.right:\n      return l();\n    case a.up:\n      return y();\n    case a.down:\n      return C();\n    case a.home:\n      return n.ZERO_LEVEL_ZERO_NODE_ID;\n    case a.end:\n      return E();\n    default:\n      return e;\n  }\n  function I() {\n    return n.isIdZeroLevel(e) || // For expanded disabled items, navigate directly to the parent.\n    // Collapse cannot happen because an event is not fired to the client.\n    r.expanded(o) && s(e, t, r) ? e : n.getDirectParentId(e);\n  }\n  function l() {\n    return c(o, r) ? n.getFirstChildId(e) : e;\n  }\n  function y() {\n    const d = Number(n.getShortId(e)), u = n.getDirectParentId(e);\n    return d ? i(n.createId(d - 1, u), t, r) : n.isIdZeroLevel(e) ? e : u;\n  }\n  function C() {\n    return c(o, r) ? n.getFirstChildId(e) : p(e, t, r) || e;\n  }\n  function E() {\n    let d = (t.length - 1).toString(), u = t[t.length - 1], h;\n    for (; c(u, r); )\n      h = u[r.getChildrenField()], d = n.createId(h.length - 1, d), u = h[h.length - 1];\n    return d;\n  }\n}\nfunction p(o, e, t) {\n  const g = n.getDirectParentId(o), r = g ? n.getItemById(g, e, t.getChildrenField()) : void 0, I = r ? r[t.getChildrenField()] : e, l = Number(n.getShortId(o));\n  return l < I.length - 1 ? n.createId(l + 1, g) : r ? p(g, e, t) : void 0;\n}\nfunction i(o, e, t) {\n  const g = n.getItemById(o, e, t.getChildrenField());\n  return c(g, t) ? i(\n    n.createId(g[t.getChildrenField()].length - 1, o),\n    e,\n    t\n  ) : o;\n}\nexport {\n  F as default\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAI,IAAIC,CAAC,EAAEC,WAAW,IAAIC,CAAC,EAAEC,6BAA6B,IAAIC,CAAC,EAAEC,6BAA6B,IAAIC,CAAC,QAAQ,8BAA8B;AAClJ,SAASC,CAACA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACxB,QAAQD,CAAC;IACP,KAAKX,CAAC,CAACa,IAAI;MACT,OAAOC,CAAC,CAAC,CAAC;IACZ,KAAKd,CAAC,CAACe,KAAK;MACV,OAAOC,CAAC,CAAC,CAAC;IACZ,KAAKhB,CAAC,CAACiB,EAAE;MACP,OAAOC,CAAC,CAAC,CAAC;IACZ,KAAKlB,CAAC,CAACmB,IAAI;MACT,OAAOC,CAAC,CAAC,CAAC;IACZ,KAAKpB,CAAC,CAACqB,IAAI;MACT,OAAOnB,CAAC,CAACoB,uBAAuB;IAClC,KAAKtB,CAAC,CAACuB,GAAG;MACR,OAAOC,CAAC,CAAC,CAAC;IACZ;MACE,OAAOf,CAAC;EACZ;EACA,SAASK,CAACA,CAAA,EAAG;IACX,OAAOZ,CAAC,CAACuB,aAAa,CAAChB,CAAC,CAAC;IAAI;IAC7B;IACAG,CAAC,CAACc,QAAQ,CAAClB,CAAC,CAAC,IAAIJ,CAAC,CAACK,CAAC,EAAEC,CAAC,EAAEE,CAAC,CAAC,GAAGH,CAAC,GAAGP,CAAC,CAACyB,iBAAiB,CAAClB,CAAC,CAAC;EAC1D;EACA,SAASO,CAACA,CAAA,EAAG;IACX,OAAOV,CAAC,CAACE,CAAC,EAAEI,CAAC,CAAC,GAAGV,CAAC,CAAC0B,eAAe,CAACnB,CAAC,CAAC,GAAGA,CAAC;EAC3C;EACA,SAASS,CAACA,CAAA,EAAG;IACX,MAAMW,CAAC,GAAGC,MAAM,CAAC5B,CAAC,CAAC6B,UAAU,CAACtB,CAAC,CAAC,CAAC;MAAEuB,CAAC,GAAG9B,CAAC,CAACyB,iBAAiB,CAAClB,CAAC,CAAC;IAC7D,OAAOoB,CAAC,GAAGI,CAAC,CAAC/B,CAAC,CAACgC,QAAQ,CAACL,CAAC,GAAG,CAAC,EAAEG,CAAC,CAAC,EAAEtB,CAAC,EAAEE,CAAC,CAAC,GAAGV,CAAC,CAACuB,aAAa,CAAChB,CAAC,CAAC,GAAGA,CAAC,GAAGuB,CAAC;EACvE;EACA,SAASZ,CAACA,CAAA,EAAG;IACX,OAAOd,CAAC,CAACE,CAAC,EAAEI,CAAC,CAAC,GAAGV,CAAC,CAAC0B,eAAe,CAACnB,CAAC,CAAC,GAAG0B,CAAC,CAAC1B,CAAC,EAAEC,CAAC,EAAEE,CAAC,CAAC,IAAIH,CAAC;EACzD;EACA,SAASe,CAACA,CAAA,EAAG;IACX,IAAIK,CAAC,GAAG,CAACnB,CAAC,CAAC0B,MAAM,GAAG,CAAC,EAAEC,QAAQ,CAAC,CAAC;MAAEL,CAAC,GAAGtB,CAAC,CAACA,CAAC,CAAC0B,MAAM,GAAG,CAAC,CAAC;MAAEE,CAAC;IACzD,OAAOhC,CAAC,CAAC0B,CAAC,EAAEpB,CAAC,CAAC,GACZ0B,CAAC,GAAGN,CAAC,CAACpB,CAAC,CAAC2B,gBAAgB,CAAC,CAAC,CAAC,EAAEV,CAAC,GAAG3B,CAAC,CAACgC,QAAQ,CAACI,CAAC,CAACF,MAAM,GAAG,CAAC,EAAEP,CAAC,CAAC,EAAEG,CAAC,GAAGM,CAAC,CAACA,CAAC,CAACF,MAAM,GAAG,CAAC,CAAC;IACnF,OAAOP,CAAC;EACV;AACF;AACA,SAASM,CAACA,CAAC3B,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAClB,MAAMC,CAAC,GAAGT,CAAC,CAACyB,iBAAiB,CAACnB,CAAC,CAAC;IAAEI,CAAC,GAAGD,CAAC,GAAGT,CAAC,CAACsC,WAAW,CAAC7B,CAAC,EAAEF,CAAC,EAAEC,CAAC,CAAC6B,gBAAgB,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;IAAEzB,CAAC,GAAGF,CAAC,GAAGA,CAAC,CAACF,CAAC,CAAC6B,gBAAgB,CAAC,CAAC,CAAC,GAAG9B,CAAC;IAAEO,CAAC,GAAGc,MAAM,CAAC5B,CAAC,CAAC6B,UAAU,CAACvB,CAAC,CAAC,CAAC;EAC9J,OAAOQ,CAAC,GAAGF,CAAC,CAACsB,MAAM,GAAG,CAAC,GAAGlC,CAAC,CAACgC,QAAQ,CAAClB,CAAC,GAAG,CAAC,EAAEL,CAAC,CAAC,GAAGC,CAAC,GAAGuB,CAAC,CAACxB,CAAC,EAAEF,CAAC,EAAEC,CAAC,CAAC,GAAG,KAAK,CAAC;AAC1E;AACA,SAASuB,CAACA,CAACzB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAClB,MAAMC,CAAC,GAAGT,CAAC,CAACsC,WAAW,CAAChC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC6B,gBAAgB,CAAC,CAAC,CAAC;EACnD,OAAOjC,CAAC,CAACK,CAAC,EAAED,CAAC,CAAC,GAAGuB,CAAC,CAChB/B,CAAC,CAACgC,QAAQ,CAACvB,CAAC,CAACD,CAAC,CAAC6B,gBAAgB,CAAC,CAAC,CAAC,CAACH,MAAM,GAAG,CAAC,EAAE5B,CAAC,CAAC,EACjDC,CAAC,EACDC,CACF,CAAC,GAAGF,CAAC;AACP;AACA,SACED,CAAC,IAAIkC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}