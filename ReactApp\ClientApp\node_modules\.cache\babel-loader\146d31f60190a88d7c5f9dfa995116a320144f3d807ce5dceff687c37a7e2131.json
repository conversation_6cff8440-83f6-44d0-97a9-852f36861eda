{"ast": null, "code": "import { encodeUTF8 } from './encode-utf';\nvar KEY_STR = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\";\nexport default function encodeBase64(input) {\n  var output = \"\";\n  var i = 0;\n  var utfInput = encodeUTF8(input);\n  while (i < utfInput.length) {\n    var chr1 = utfInput.charCodeAt(i++);\n    var chr2 = utfInput.charCodeAt(i++);\n    var chr3 = utfInput.charCodeAt(i++);\n    var enc1 = chr1 >> 2;\n    var enc2 = (chr1 & 3) << 4 | chr2 >> 4;\n    var enc3 = (chr2 & 15) << 2 | chr3 >> 6;\n    var enc4 = chr3 & 63;\n    if (isNaN(chr2)) {\n      enc3 = enc4 = 64;\n    } else if (isNaN(chr3)) {\n      enc4 = 64;\n    }\n    output = output + KEY_STR.charAt(enc1) + KEY_STR.charAt(enc2) + KEY_STR.charAt(enc3) + KEY_STR.charAt(enc4);\n  }\n  return output;\n}", "map": {"version": 3, "names": ["encodeUTF8", "KEY_STR", "encodeBase64", "input", "output", "i", "utfInput", "length", "chr1", "charCodeAt", "chr2", "chr3", "enc1", "enc2", "enc3", "enc4", "isNaN", "char<PERSON>t"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/util/encode-base64.js"], "sourcesContent": ["import { encodeUTF8 } from './encode-utf';\n\nvar KEY_STR = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\";\n\nexport default function encodeBase64(input) {\n    var output = \"\";\n    var i = 0;\n\n    var utfInput = encodeUTF8(input);\n\n    while (i < utfInput.length) {\n        var chr1 = utfInput.charCodeAt(i++);\n        var chr2 = utfInput.charCodeAt(i++);\n        var chr3 = utfInput.charCodeAt(i++);\n\n        var enc1 = chr1 >> 2;\n        var enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);\n        var enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);\n        var enc4 = chr3 & 63;\n\n        if (isNaN(chr2)) {\n            enc3 = enc4 = 64;\n        } else if (isNaN(chr3)) {\n            enc4 = 64;\n        }\n\n        output = output +\n            KEY_STR.charAt(enc1) + KEY_STR.charAt(enc2) +\n            KEY_STR.charAt(enc3) + KEY_STR.charAt(enc4);\n    }\n\n    return output;\n}"], "mappings": "AAAA,SAASA,UAAU,QAAQ,cAAc;AAEzC,IAAIC,OAAO,GAAG,mEAAmE;AAEjF,eAAe,SAASC,YAAYA,CAACC,KAAK,EAAE;EACxC,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,CAAC,GAAG,CAAC;EAET,IAAIC,QAAQ,GAAGN,UAAU,CAACG,KAAK,CAAC;EAEhC,OAAOE,CAAC,GAAGC,QAAQ,CAACC,MAAM,EAAE;IACxB,IAAIC,IAAI,GAAGF,QAAQ,CAACG,UAAU,CAACJ,CAAC,EAAE,CAAC;IACnC,IAAIK,IAAI,GAAGJ,QAAQ,CAACG,UAAU,CAACJ,CAAC,EAAE,CAAC;IACnC,IAAIM,IAAI,GAAGL,QAAQ,CAACG,UAAU,CAACJ,CAAC,EAAE,CAAC;IAEnC,IAAIO,IAAI,GAAGJ,IAAI,IAAI,CAAC;IACpB,IAAIK,IAAI,GAAI,CAACL,IAAI,GAAG,CAAC,KAAK,CAAC,GAAKE,IAAI,IAAI,CAAE;IAC1C,IAAII,IAAI,GAAI,CAACJ,IAAI,GAAG,EAAE,KAAK,CAAC,GAAKC,IAAI,IAAI,CAAE;IAC3C,IAAII,IAAI,GAAGJ,IAAI,GAAG,EAAE;IAEpB,IAAIK,KAAK,CAACN,IAAI,CAAC,EAAE;MACbI,IAAI,GAAGC,IAAI,GAAG,EAAE;IACpB,CAAC,MAAM,IAAIC,KAAK,CAACL,IAAI,CAAC,EAAE;MACpBI,IAAI,GAAG,EAAE;IACb;IAEAX,MAAM,GAAGA,MAAM,GACXH,OAAO,CAACgB,MAAM,CAACL,IAAI,CAAC,GAAGX,OAAO,CAACgB,MAAM,CAACJ,IAAI,CAAC,GAC3CZ,OAAO,CAACgB,MAAM,CAACH,IAAI,CAAC,GAAGb,OAAO,CAACgB,MAAM,CAACF,IAAI,CAAC;EACnD;EAEA,OAAOX,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}