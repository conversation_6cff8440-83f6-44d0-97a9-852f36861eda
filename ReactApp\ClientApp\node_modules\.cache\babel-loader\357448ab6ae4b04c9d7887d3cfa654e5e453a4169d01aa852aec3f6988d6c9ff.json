{"ast": null, "code": "/* Copyright 2022 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar _AnnotationEditorLayerBuilder_annotationLayer, _AnnotationEditorLayerBuilder_drawLayer, _AnnotationEditorLayerBuilder_onAppend, _AnnotationEditorLayerBuilder_textLayer, _AnnotationEditorLayerBuilder_uiManager;\nimport { __awaiter, __classPrivateFieldGet, __classPrivateFieldSet } from \"tslib\";\n/** @typedef {import(\"../src/display/api\").PDFPageProxy} PDFPageProxy */\n/** @typedef {import(\"../src/display/display_utils\").PageViewport} PageViewport */\n/** @typedef {import(\"../src/display/editor/tools.js\").AnnotationEditorUIManager} AnnotationEditorUIManager */\n/** @typedef {import(\"./text_accessibility.js\").TextAccessibilityManager} TextAccessibilityManager */\n/** @typedef {import(\"./interfaces\").IL10n} IL10n */\n/** @typedef {import(\"../src/display/annotation_layer.js\").AnnotationLayer} AnnotationLayer */\nimport { AnnotationEditorLayer } from \"./annotation-editor-layer\";\n// import { GenericL10n } from \"web-null_l10n\";\n/**\n * @typedef {Object} AnnotationEditorLayerBuilderOptions\n * @property {AnnotationEditorUIManager} [uiManager]\n * @property {PDFPageProxy} pdfPage\n * @property {IL10n} [l10n]\n * @property {TextAccessibilityManager} [accessibilityManager]\n * @property {AnnotationLayer} [annotationLayer]\n * @property {TextLayer} [textLayer]\n * @property {DrawLayer} [drawLayer]\n * @property {function} [onAppend]\n */\nclass AnnotationEditorLayerBuilder {\n  /**\n   * @param {AnnotationEditorLayerBuilderOptions} options\n   */\n  constructor(options) {\n    // todo: props\n    this.pdfPage = null;\n    this.annotationEditorLayer = null;\n    this._cancelled = null;\n    this.div = null;\n    this.accessibilityManager = null;\n    // todo: props\n    _AnnotationEditorLayerBuilder_annotationLayer.set(this, null);\n    _AnnotationEditorLayerBuilder_drawLayer.set(this, null);\n    _AnnotationEditorLayerBuilder_onAppend.set(this, null);\n    _AnnotationEditorLayerBuilder_textLayer.set(this, null);\n    _AnnotationEditorLayerBuilder_uiManager.set(this, null);\n    this.pdfPage = options.pdfPage;\n    this.accessibilityManager = options.accessibilityManager;\n    // this.l10n = options.l10n;\n    // if (typeof PDFJSDev === \"undefined\" || PDFJSDev.test(\"GENERIC\")) {\n    // this.l10n ||= new GenericL10n();\n    // }\n    this.annotationEditorLayer = null;\n    this.div = null;\n    this._cancelled = false;\n    __classPrivateFieldSet(this, _AnnotationEditorLayerBuilder_uiManager, options.uiManager, \"f\");\n    __classPrivateFieldSet(this, _AnnotationEditorLayerBuilder_annotationLayer, options.annotationLayer || null, \"f\");\n    __classPrivateFieldSet(this, _AnnotationEditorLayerBuilder_textLayer, options.textLayer || null, \"f\");\n    __classPrivateFieldSet(this, _AnnotationEditorLayerBuilder_drawLayer, options.drawLayer || null, \"f\");\n    __classPrivateFieldSet(this, _AnnotationEditorLayerBuilder_onAppend, options.onAppend || null, \"f\");\n  }\n  /**\n   * @param {PageViewport} viewport\n   * @param {string} intent (default value is 'display')\n   */\n  render(viewport_1) {\n    return __awaiter(this, arguments, void 0, function* (viewport, intent = \"display\") {\n      var _a;\n      if (intent !== \"display\") {\n        return;\n      }\n      if (this._cancelled) {\n        return;\n      }\n      const clonedViewport = viewport.clone({\n        dontFlip: true\n      });\n      if (this.div) {\n        this.annotationEditorLayer.update({\n          viewport: clonedViewport\n        });\n        this.show();\n        return;\n      }\n      // Create an AnnotationEditor layer div\n      const div = this.div = document.createElement(\"div\");\n      div.className = \"k-annotation-editor-layer\";\n      div.hidden = true;\n      div.dir = __classPrivateFieldGet(this, _AnnotationEditorLayerBuilder_uiManager, \"f\").direction;\n      (_a = __classPrivateFieldGet(this, _AnnotationEditorLayerBuilder_onAppend, \"f\")) === null || _a === void 0 ? void 0 : _a.call(this, div);\n      this.annotationEditorLayer = new AnnotationEditorLayer({\n        uiManager: __classPrivateFieldGet(this, _AnnotationEditorLayerBuilder_uiManager, \"f\"),\n        div,\n        accessibilityManager: this.accessibilityManager,\n        pageIndex: this.pdfPage.pageNumber - 1,\n        // l10n: this.l10n,\n        viewport: clonedViewport,\n        annotationLayer: __classPrivateFieldGet(this, _AnnotationEditorLayerBuilder_annotationLayer, \"f\"),\n        textLayer: __classPrivateFieldGet(this, _AnnotationEditorLayerBuilder_textLayer, \"f\"),\n        drawLayer: __classPrivateFieldGet(this, _AnnotationEditorLayerBuilder_drawLayer, \"f\")\n      });\n      const parameters = {\n        viewport: clonedViewport,\n        div,\n        annotations: null,\n        intent\n      };\n      this.annotationEditorLayer.render(parameters);\n      this.show();\n    });\n  }\n  cancel() {\n    this._cancelled = true;\n    if (!this.div) {\n      return;\n    }\n    this.annotationEditorLayer.destroy();\n  }\n  hide() {\n    if (!this.div) {\n      return;\n    }\n    this.div.hidden = true;\n  }\n  show() {\n    if (!this.div || this.annotationEditorLayer.isInvisible) {\n      return;\n    }\n    this.div.hidden = false;\n  }\n}\n_AnnotationEditorLayerBuilder_annotationLayer = new WeakMap(), _AnnotationEditorLayerBuilder_drawLayer = new WeakMap(), _AnnotationEditorLayerBuilder_onAppend = new WeakMap(), _AnnotationEditorLayerBuilder_textLayer = new WeakMap(), _AnnotationEditorLayerBuilder_uiManager = new WeakMap();\nexport { AnnotationEditorLayerBuilder };", "map": {"version": 3, "names": ["_AnnotationEditorLayerBuilder_annotationLayer", "_AnnotationEditorLayerBuilder_drawLayer", "_AnnotationEditorLayerBuilder_onAppend", "_AnnotationEditorLayerBuilder_textLayer", "_AnnotationEditorLayerBuilder_uiManager", "__awaiter", "__classPrivateFieldGet", "__classPrivateFieldSet", "AnnotationEditorLayer", "AnnotationEditorLayerBuilder", "constructor", "options", "pdfPage", "annotationEditorLayer", "_cancelled", "div", "accessibilityManager", "set", "uiManager", "annotationLayer", "textLayer", "draw<PERSON>ayer", "onAppend", "render", "viewport_1", "arguments", "viewport", "intent", "_a", "clonedViewport", "clone", "dont<PERSON><PERSON>", "update", "show", "document", "createElement", "className", "hidden", "dir", "direction", "call", "pageIndex", "pageNumber", "parameters", "annotations", "cancel", "destroy", "hide", "isInvisible", "WeakMap"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-pdfviewer-common/dist/es/annotations/annotation-editor-layer-builder.js"], "sourcesContent": ["/* Copyright 2022 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar _AnnotationEditorLayerBuilder_annotationLayer, _AnnotationEditorLayerBuilder_drawLayer, _AnnotationEditorLayerBuilder_onAppend, _AnnotationEditorLayerBuilder_textLayer, _AnnotationEditorLayerBuilder_uiManager;\nimport { __awaiter, __classPrivateFieldGet, __classPrivateFieldSet } from \"tslib\";\n/** @typedef {import(\"../src/display/api\").PDFPageProxy} PDFPageProxy */\n/** @typedef {import(\"../src/display/display_utils\").PageViewport} PageViewport */\n/** @typedef {import(\"../src/display/editor/tools.js\").AnnotationEditorUIManager} AnnotationEditorUIManager */\n/** @typedef {import(\"./text_accessibility.js\").TextAccessibilityManager} TextAccessibilityManager */\n/** @typedef {import(\"./interfaces\").IL10n} IL10n */\n/** @typedef {import(\"../src/display/annotation_layer.js\").AnnotationLayer} AnnotationLayer */\nimport { AnnotationEditorLayer } from \"./annotation-editor-layer\";\n// import { GenericL10n } from \"web-null_l10n\";\n/**\n * @typedef {Object} AnnotationEditorLayerBuilderOptions\n * @property {AnnotationEditorUIManager} [uiManager]\n * @property {PDFPageProxy} pdfPage\n * @property {IL10n} [l10n]\n * @property {TextAccessibilityManager} [accessibilityManager]\n * @property {AnnotationLayer} [annotationLayer]\n * @property {TextLayer} [textLayer]\n * @property {DrawLayer} [drawLayer]\n * @property {function} [onAppend]\n */\nclass AnnotationEditorLayerBuilder {\n    /**\n     * @param {AnnotationEditorLayerBuilderOptions} options\n     */\n    constructor(options) {\n        // todo: props\n        this.pdfPage = null;\n        this.annotationEditorLayer = null;\n        this._cancelled = null;\n        this.div = null;\n        this.accessibilityManager = null;\n        // todo: props\n        _AnnotationEditorLayerBuilder_annotationLayer.set(this, null);\n        _AnnotationEditorLayerBuilder_drawLayer.set(this, null);\n        _AnnotationEditorLayerBuilder_onAppend.set(this, null);\n        _AnnotationEditorLayerBuilder_textLayer.set(this, null);\n        _AnnotationEditorLayerBuilder_uiManager.set(this, null);\n        this.pdfPage = options.pdfPage;\n        this.accessibilityManager = options.accessibilityManager;\n        // this.l10n = options.l10n;\n        // if (typeof PDFJSDev === \"undefined\" || PDFJSDev.test(\"GENERIC\")) {\n        // this.l10n ||= new GenericL10n();\n        // }\n        this.annotationEditorLayer = null;\n        this.div = null;\n        this._cancelled = false;\n        __classPrivateFieldSet(this, _AnnotationEditorLayerBuilder_uiManager, options.uiManager, \"f\");\n        __classPrivateFieldSet(this, _AnnotationEditorLayerBuilder_annotationLayer, options.annotationLayer || null, \"f\");\n        __classPrivateFieldSet(this, _AnnotationEditorLayerBuilder_textLayer, options.textLayer || null, \"f\");\n        __classPrivateFieldSet(this, _AnnotationEditorLayerBuilder_drawLayer, options.drawLayer || null, \"f\");\n        __classPrivateFieldSet(this, _AnnotationEditorLayerBuilder_onAppend, options.onAppend || null, \"f\");\n    }\n    /**\n     * @param {PageViewport} viewport\n     * @param {string} intent (default value is 'display')\n     */\n    render(viewport_1) {\n        return __awaiter(this, arguments, void 0, function* (viewport, intent = \"display\") {\n            var _a;\n            if (intent !== \"display\") {\n                return;\n            }\n            if (this._cancelled) {\n                return;\n            }\n            const clonedViewport = viewport.clone({ dontFlip: true });\n            if (this.div) {\n                this.annotationEditorLayer.update({ viewport: clonedViewport });\n                this.show();\n                return;\n            }\n            // Create an AnnotationEditor layer div\n            const div = (this.div = document.createElement(\"div\"));\n            div.className = \"k-annotation-editor-layer\";\n            div.hidden = true;\n            div.dir = __classPrivateFieldGet(this, _AnnotationEditorLayerBuilder_uiManager, \"f\").direction;\n            (_a = __classPrivateFieldGet(this, _AnnotationEditorLayerBuilder_onAppend, \"f\")) === null || _a === void 0 ? void 0 : _a.call(this, div);\n            this.annotationEditorLayer = new AnnotationEditorLayer({\n                uiManager: __classPrivateFieldGet(this, _AnnotationEditorLayerBuilder_uiManager, \"f\"),\n                div,\n                accessibilityManager: this.accessibilityManager,\n                pageIndex: this.pdfPage.pageNumber - 1,\n                // l10n: this.l10n,\n                viewport: clonedViewport,\n                annotationLayer: __classPrivateFieldGet(this, _AnnotationEditorLayerBuilder_annotationLayer, \"f\"),\n                textLayer: __classPrivateFieldGet(this, _AnnotationEditorLayerBuilder_textLayer, \"f\"),\n                drawLayer: __classPrivateFieldGet(this, _AnnotationEditorLayerBuilder_drawLayer, \"f\")\n            });\n            const parameters = {\n                viewport: clonedViewport,\n                div,\n                annotations: null,\n                intent\n            };\n            this.annotationEditorLayer.render(parameters);\n            this.show();\n        });\n    }\n    cancel() {\n        this._cancelled = true;\n        if (!this.div) {\n            return;\n        }\n        this.annotationEditorLayer.destroy();\n    }\n    hide() {\n        if (!this.div) {\n            return;\n        }\n        this.div.hidden = true;\n    }\n    show() {\n        if (!this.div || this.annotationEditorLayer.isInvisible) {\n            return;\n        }\n        this.div.hidden = false;\n    }\n}\n_AnnotationEditorLayerBuilder_annotationLayer = new WeakMap(), _AnnotationEditorLayerBuilder_drawLayer = new WeakMap(), _AnnotationEditorLayerBuilder_onAppend = new WeakMap(), _AnnotationEditorLayerBuilder_textLayer = new WeakMap(), _AnnotationEditorLayerBuilder_uiManager = new WeakMap();\nexport { AnnotationEditorLayerBuilder };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,6CAA6C,EAAEC,uCAAuC,EAAEC,sCAAsC,EAAEC,uCAAuC,EAAEC,uCAAuC;AACpN,SAASC,SAAS,EAAEC,sBAAsB,EAAEC,sBAAsB,QAAQ,OAAO;AACjF;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,4BAA4B,CAAC;EAC/B;AACJ;AACA;EACIC,WAAWA,CAACC,OAAO,EAAE;IACjB;IACA,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,qBAAqB,GAAG,IAAI;IACjC,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,GAAG,GAAG,IAAI;IACf,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAChC;IACAhB,6CAA6C,CAACiB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAC7DhB,uCAAuC,CAACgB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IACvDf,sCAAsC,CAACe,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IACtDd,uCAAuC,CAACc,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IACvDb,uCAAuC,CAACa,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IACvD,IAAI,CAACL,OAAO,GAAGD,OAAO,CAACC,OAAO;IAC9B,IAAI,CAACI,oBAAoB,GAAGL,OAAO,CAACK,oBAAoB;IACxD;IACA;IACA;IACA;IACA,IAAI,CAACH,qBAAqB,GAAG,IAAI;IACjC,IAAI,CAACE,GAAG,GAAG,IAAI;IACf,IAAI,CAACD,UAAU,GAAG,KAAK;IACvBP,sBAAsB,CAAC,IAAI,EAAEH,uCAAuC,EAAEO,OAAO,CAACO,SAAS,EAAE,GAAG,CAAC;IAC7FX,sBAAsB,CAAC,IAAI,EAAEP,6CAA6C,EAAEW,OAAO,CAACQ,eAAe,IAAI,IAAI,EAAE,GAAG,CAAC;IACjHZ,sBAAsB,CAAC,IAAI,EAAEJ,uCAAuC,EAAEQ,OAAO,CAACS,SAAS,IAAI,IAAI,EAAE,GAAG,CAAC;IACrGb,sBAAsB,CAAC,IAAI,EAAEN,uCAAuC,EAAEU,OAAO,CAACU,SAAS,IAAI,IAAI,EAAE,GAAG,CAAC;IACrGd,sBAAsB,CAAC,IAAI,EAAEL,sCAAsC,EAAES,OAAO,CAACW,QAAQ,IAAI,IAAI,EAAE,GAAG,CAAC;EACvG;EACA;AACJ;AACA;AACA;EACIC,MAAMA,CAACC,UAAU,EAAE;IACf,OAAOnB,SAAS,CAAC,IAAI,EAAEoB,SAAS,EAAE,KAAK,CAAC,EAAE,WAAWC,QAAQ,EAAEC,MAAM,GAAG,SAAS,EAAE;MAC/E,IAAIC,EAAE;MACN,IAAID,MAAM,KAAK,SAAS,EAAE;QACtB;MACJ;MACA,IAAI,IAAI,CAACb,UAAU,EAAE;QACjB;MACJ;MACA,MAAMe,cAAc,GAAGH,QAAQ,CAACI,KAAK,CAAC;QAAEC,QAAQ,EAAE;MAAK,CAAC,CAAC;MACzD,IAAI,IAAI,CAAChB,GAAG,EAAE;QACV,IAAI,CAACF,qBAAqB,CAACmB,MAAM,CAAC;UAAEN,QAAQ,EAAEG;QAAe,CAAC,CAAC;QAC/D,IAAI,CAACI,IAAI,CAAC,CAAC;QACX;MACJ;MACA;MACA,MAAMlB,GAAG,GAAI,IAAI,CAACA,GAAG,GAAGmB,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAE;MACtDpB,GAAG,CAACqB,SAAS,GAAG,2BAA2B;MAC3CrB,GAAG,CAACsB,MAAM,GAAG,IAAI;MACjBtB,GAAG,CAACuB,GAAG,GAAGhC,sBAAsB,CAAC,IAAI,EAAEF,uCAAuC,EAAE,GAAG,CAAC,CAACmC,SAAS;MAC9F,CAACX,EAAE,GAAGtB,sBAAsB,CAAC,IAAI,EAAEJ,sCAAsC,EAAE,GAAG,CAAC,MAAM,IAAI,IAAI0B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACY,IAAI,CAAC,IAAI,EAAEzB,GAAG,CAAC;MACxI,IAAI,CAACF,qBAAqB,GAAG,IAAIL,qBAAqB,CAAC;QACnDU,SAAS,EAAEZ,sBAAsB,CAAC,IAAI,EAAEF,uCAAuC,EAAE,GAAG,CAAC;QACrFW,GAAG;QACHC,oBAAoB,EAAE,IAAI,CAACA,oBAAoB;QAC/CyB,SAAS,EAAE,IAAI,CAAC7B,OAAO,CAAC8B,UAAU,GAAG,CAAC;QACtC;QACAhB,QAAQ,EAAEG,cAAc;QACxBV,eAAe,EAAEb,sBAAsB,CAAC,IAAI,EAAEN,6CAA6C,EAAE,GAAG,CAAC;QACjGoB,SAAS,EAAEd,sBAAsB,CAAC,IAAI,EAAEH,uCAAuC,EAAE,GAAG,CAAC;QACrFkB,SAAS,EAAEf,sBAAsB,CAAC,IAAI,EAAEL,uCAAuC,EAAE,GAAG;MACxF,CAAC,CAAC;MACF,MAAM0C,UAAU,GAAG;QACfjB,QAAQ,EAAEG,cAAc;QACxBd,GAAG;QACH6B,WAAW,EAAE,IAAI;QACjBjB;MACJ,CAAC;MACD,IAAI,CAACd,qBAAqB,CAACU,MAAM,CAACoB,UAAU,CAAC;MAC7C,IAAI,CAACV,IAAI,CAAC,CAAC;IACf,CAAC,CAAC;EACN;EACAY,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC/B,UAAU,GAAG,IAAI;IACtB,IAAI,CAAC,IAAI,CAACC,GAAG,EAAE;MACX;IACJ;IACA,IAAI,CAACF,qBAAqB,CAACiC,OAAO,CAAC,CAAC;EACxC;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAAChC,GAAG,EAAE;MACX;IACJ;IACA,IAAI,CAACA,GAAG,CAACsB,MAAM,GAAG,IAAI;EAC1B;EACAJ,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAAClB,GAAG,IAAI,IAAI,CAACF,qBAAqB,CAACmC,WAAW,EAAE;MACrD;IACJ;IACA,IAAI,CAACjC,GAAG,CAACsB,MAAM,GAAG,KAAK;EAC3B;AACJ;AACArC,6CAA6C,GAAG,IAAIiD,OAAO,CAAC,CAAC,EAAEhD,uCAAuC,GAAG,IAAIgD,OAAO,CAAC,CAAC,EAAE/C,sCAAsC,GAAG,IAAI+C,OAAO,CAAC,CAAC,EAAE9C,uCAAuC,GAAG,IAAI8C,OAAO,CAAC,CAAC,EAAE7C,uCAAuC,GAAG,IAAI6C,OAAO,CAAC,CAAC;AAChS,SAASxC,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}