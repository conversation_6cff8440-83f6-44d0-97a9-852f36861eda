{"ast": null, "code": "import { cldr, getLocaleInfo } from './info';\nimport localeTerritory from './territory';\nimport { DAYS_OF_WEEK, DEFAULT_TERRITORY } from './constants';\nimport { errors } from '../errors';\nvar NoWeekData = errors.NoWeekData;\nvar NoFirstDay = errors.NoFirstDay;\nexport default function firstDay(locale) {\n  var info = getLocaleInfo(locale);\n  if (!isNaN(info.firstDay)) {\n    return info.firstDay;\n  }\n  var weekData = cldr.supplemental.weekData;\n  if (!weekData) {\n    throw NoWeekData.error();\n  }\n  var firstDay = weekData.firstDay[localeTerritory(info)] || weekData.firstDay[DEFAULT_TERRITORY];\n  if (!firstDay) {\n    throw NoFirstDay.error();\n  }\n  info.firstDay = DAYS_OF_WEEK.indexOf(firstDay);\n  return info.firstDay;\n}", "map": {"version": 3, "names": ["cldr", "getLocaleInfo", "localeTerritory", "DAYS_OF_WEEK", "DEFAULT_TERRITORY", "errors", "NoWeekData", "NoFirstDay", "firstDay", "locale", "info", "isNaN", "weekData", "supplemental", "error", "indexOf"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-intl/dist/es/cldr/first-day.js"], "sourcesContent": ["import { cldr, getLocaleInfo } from './info';\nimport localeTerritory from './territory';\n\nimport { DAYS_OF_WEEK, DEFAULT_TERRITORY } from './constants';\nimport { errors } from '../errors';\n\nvar NoWeekData = errors.NoWeekData;\nvar NoFirstDay = errors.NoFirstDay;\n\nexport default function firstDay(locale) {\n    var info = getLocaleInfo(locale);\n\n    if (!isNaN(info.firstDay)) {\n        return info.firstDay;\n    }\n\n    var weekData = cldr.supplemental.weekData;\n    if (!weekData) {\n        throw NoWeekData.error();\n    }\n\n    var firstDay = weekData.firstDay[localeTerritory(info)] || weekData.firstDay[DEFAULT_TERRITORY];\n\n    if (!firstDay) {\n        throw NoFirstDay.error();\n    }\n\n    info.firstDay = DAYS_OF_WEEK.indexOf(firstDay);\n\n    return info.firstDay;\n}\n"], "mappings": "AAAA,SAASA,IAAI,EAAEC,aAAa,QAAQ,QAAQ;AAC5C,OAAOC,eAAe,MAAM,aAAa;AAEzC,SAASC,YAAY,EAAEC,iBAAiB,QAAQ,aAAa;AAC7D,SAASC,MAAM,QAAQ,WAAW;AAElC,IAAIC,UAAU,GAAGD,MAAM,CAACC,UAAU;AAClC,IAAIC,UAAU,GAAGF,MAAM,CAACE,UAAU;AAElC,eAAe,SAASC,QAAQA,CAACC,MAAM,EAAE;EACrC,IAAIC,IAAI,GAAGT,aAAa,CAACQ,MAAM,CAAC;EAEhC,IAAI,CAACE,KAAK,CAACD,IAAI,CAACF,QAAQ,CAAC,EAAE;IACvB,OAAOE,IAAI,CAACF,QAAQ;EACxB;EAEA,IAAII,QAAQ,GAAGZ,IAAI,CAACa,YAAY,CAACD,QAAQ;EACzC,IAAI,CAACA,QAAQ,EAAE;IACX,MAAMN,UAAU,CAACQ,KAAK,CAAC,CAAC;EAC5B;EAEA,IAAIN,QAAQ,GAAGI,QAAQ,CAACJ,QAAQ,CAACN,eAAe,CAACQ,IAAI,CAAC,CAAC,IAAIE,QAAQ,CAACJ,QAAQ,CAACJ,iBAAiB,CAAC;EAE/F,IAAI,CAACI,QAAQ,EAAE;IACX,MAAMD,UAAU,CAACO,KAAK,CAAC,CAAC;EAC5B;EAEAJ,IAAI,CAACF,QAAQ,GAAGL,YAAY,CAACY,OAAO,CAACP,QAAQ,CAAC;EAE9C,OAAOE,IAAI,CAACF,QAAQ;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}