{"ast": null, "code": "import { Day } from './day.enum';\nimport { addDays } from './add-days';\nimport { createDate } from './create-date';\nimport { prevDayOfWeek } from './prev-day-of-week';\nimport { MS_PER_DAY } from './constants';\nimport { getDate } from './get-date';\nvar moveDateToWeekStart = function (date, weekStartDay) {\n  if (weekStartDay !== Day.Monday) {\n    return addDays(prevDayOfWeek(date, weekStartDay), 4);\n  }\n  return addDays(date, 4 - (date.getDay() || 7));\n};\nvar calcWeekInYear = function (date, weekStartDay) {\n  var firstWeekInYear = createDate(date.getFullYear(), 0, 1, -6);\n  var newDate = moveDateToWeekStart(date, weekStartDay);\n  var diffInMS = newDate.getTime() - firstWeekInYear.getTime();\n  var days = Math.floor(diffInMS / MS_PER_DAY);\n  return 1 + Math.floor(days / 7);\n};\n/**\n * A function that returns the number of the week within a year, which is calculated in relation to the date.\n *\n * For more information, refer to the [**ISO week date**](https://en.wikipedia.org/wiki/ISO_week_date) article.\n *\n * @param date - The date used for the week number calculation.\n * @param weekStartDay - The first day of the week. By default, the first week day is Monday.\n * @returns - The number of the week within the year.\n *\n * @example\n * ```ts-no-run\n * weekInYear(new Date(2016, 0, 1)); // Week 53, 2015\n * weekInYear(new Date(2016, 0, 5)); // Week 1, 2016\n * weekInYear(new Date(2017, 0, 1)); // Week 52, 2016\n * weekInYear(new Date(2017, 0, 2)); // Week 1, 2017\n * ```\n */\nexport var weekInYear = function (date, weekStartDay) {\n  if (weekStartDay === void 0) {\n    weekStartDay = Day.Monday;\n  }\n  date = getDate(date);\n  var prevWeekDate = addDays(date, -7);\n  var nextWeekDate = addDays(date, 7);\n  var weekNumber = calcWeekInYear(date, weekStartDay);\n  if (weekNumber === 0) {\n    return calcWeekInYear(prevWeekDate, weekStartDay) + 1;\n  }\n  if (weekNumber === 53 && calcWeekInYear(nextWeekDate, weekStartDay) > 1) {\n    return 1;\n  }\n  return weekNumber;\n};", "map": {"version": 3, "names": ["Day", "addDays", "createDate", "prevDayOfWeek", "MS_PER_DAY", "getDate", "moveDateToWeekStart", "date", "weekStartDay", "Monday", "getDay", "calcWeekInYear", "firstWeekInYear", "getFullYear", "newDate", "diffInMS", "getTime", "days", "Math", "floor", "weekInYear", "prevWeekDate", "nextWeekDate", "weekNumber"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/week-in-year.js"], "sourcesContent": ["import { Day } from './day.enum';\nimport { addDays } from './add-days';\nimport { createDate } from './create-date';\nimport { prevDayOfWeek } from './prev-day-of-week';\nimport { MS_PER_DAY } from './constants';\nimport { getDate } from './get-date';\nvar moveDateToWeekStart = function (date, weekStartDay) {\n    if (weekStartDay !== Day.Monday) {\n        return addDays(prevDayOfWeek(date, weekStartDay), 4);\n    }\n    return addDays(date, (4 - (date.getDay() || 7)));\n};\nvar calcWeekInYear = function (date, weekStartDay) {\n    var firstWeekInYear = createDate(date.getFullYear(), 0, 1, -6);\n    var newDate = moveDateToWeekStart(date, weekStartDay);\n    var diffInMS = newDate.getTime() - firstWeekInYear.getTime();\n    var days = Math.floor(diffInMS / MS_PER_DAY);\n    return 1 + Math.floor(days / 7);\n};\n/**\n * A function that returns the number of the week within a year, which is calculated in relation to the date.\n *\n * For more information, refer to the [**ISO week date**](https://en.wikipedia.org/wiki/ISO_week_date) article.\n *\n * @param date - The date used for the week number calculation.\n * @param weekStartDay - The first day of the week. By default, the first week day is Monday.\n * @returns - The number of the week within the year.\n *\n * @example\n * ```ts-no-run\n * weekInYear(new Date(2016, 0, 1)); // Week 53, 2015\n * weekInYear(new Date(2016, 0, 5)); // Week 1, 2016\n * weekInYear(new Date(2017, 0, 1)); // Week 52, 2016\n * weekInYear(new Date(2017, 0, 2)); // Week 1, 2017\n * ```\n */\nexport var weekInYear = function (date, weekStartDay) {\n    if (weekStartDay === void 0) { weekStartDay = Day.Monday; }\n    date = getDate(date);\n    var prevWeekDate = addDays(date, -7);\n    var nextWeekDate = addDays(date, 7);\n    var weekNumber = calcWeekInYear(date, weekStartDay);\n    if (weekNumber === 0) {\n        return calcWeekInYear(prevWeekDate, weekStartDay) + 1;\n    }\n    if (weekNumber === 53 && calcWeekInYear(nextWeekDate, weekStartDay) > 1) {\n        return 1;\n    }\n    return weekNumber;\n};\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,YAAY;AAChC,SAASC,OAAO,QAAQ,YAAY;AACpC,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,UAAU,QAAQ,aAAa;AACxC,SAASC,OAAO,QAAQ,YAAY;AACpC,IAAIC,mBAAmB,GAAG,SAAAA,CAAUC,IAAI,EAAEC,YAAY,EAAE;EACpD,IAAIA,YAAY,KAAKR,GAAG,CAACS,MAAM,EAAE;IAC7B,OAAOR,OAAO,CAACE,aAAa,CAACI,IAAI,EAAEC,YAAY,CAAC,EAAE,CAAC,CAAC;EACxD;EACA,OAAOP,OAAO,CAACM,IAAI,EAAG,CAAC,IAAIA,IAAI,CAACG,MAAM,CAAC,CAAC,IAAI,CAAC,CAAE,CAAC;AACpD,CAAC;AACD,IAAIC,cAAc,GAAG,SAAAA,CAAUJ,IAAI,EAAEC,YAAY,EAAE;EAC/C,IAAII,eAAe,GAAGV,UAAU,CAACK,IAAI,CAACM,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9D,IAAIC,OAAO,GAAGR,mBAAmB,CAACC,IAAI,EAAEC,YAAY,CAAC;EACrD,IAAIO,QAAQ,GAAGD,OAAO,CAACE,OAAO,CAAC,CAAC,GAAGJ,eAAe,CAACI,OAAO,CAAC,CAAC;EAC5D,IAAIC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACJ,QAAQ,GAAGX,UAAU,CAAC;EAC5C,OAAO,CAAC,GAAGc,IAAI,CAACC,KAAK,CAACF,IAAI,GAAG,CAAC,CAAC;AACnC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIG,UAAU,GAAG,SAAAA,CAAUb,IAAI,EAAEC,YAAY,EAAE;EAClD,IAAIA,YAAY,KAAK,KAAK,CAAC,EAAE;IAAEA,YAAY,GAAGR,GAAG,CAACS,MAAM;EAAE;EAC1DF,IAAI,GAAGF,OAAO,CAACE,IAAI,CAAC;EACpB,IAAIc,YAAY,GAAGpB,OAAO,CAACM,IAAI,EAAE,CAAC,CAAC,CAAC;EACpC,IAAIe,YAAY,GAAGrB,OAAO,CAACM,IAAI,EAAE,CAAC,CAAC;EACnC,IAAIgB,UAAU,GAAGZ,cAAc,CAACJ,IAAI,EAAEC,YAAY,CAAC;EACnD,IAAIe,UAAU,KAAK,CAAC,EAAE;IAClB,OAAOZ,cAAc,CAACU,YAAY,EAAEb,YAAY,CAAC,GAAG,CAAC;EACzD;EACA,IAAIe,UAAU,KAAK,EAAE,IAAIZ,cAAc,CAACW,YAAY,EAAEd,YAAY,CAAC,GAAG,CAAC,EAAE;IACrE,OAAO,CAAC;EACZ;EACA,OAAOe,UAAU;AACrB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}