{"ast": null, "code": "import { firstYearOfDecade } from './first-year-of-decade';\n/**\n * A function that calculates duration in decades between two `Date` objects.\n *\n * @param start - The start date value.\n * @param end - The end date value.\n * @returns - The duration in months.\n *\n * @example\n * ```ts-no-run\n * durationInDecades(new Date(2016, 0, 1), new Date(2136, 0, 1)); // 12\n * durationInDecades(new Date(2016, 0, 1), new Date(2016, 0, 1)); // 0\n * ```\n */\nexport var durationInDecades = function (start, end) {\n  return (firstYearOfDecade(end).getFullYear() - firstYearOfDecade(start).getFullYear()) / 10;\n};", "map": {"version": 3, "names": ["firstYearOfDecade", "durationInDecades", "start", "end", "getFullYear"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/duration-in-decades.js"], "sourcesContent": ["import { firstYearOfDecade } from './first-year-of-decade';\n/**\n * A function that calculates duration in decades between two `Date` objects.\n *\n * @param start - The start date value.\n * @param end - The end date value.\n * @returns - The duration in months.\n *\n * @example\n * ```ts-no-run\n * durationInDecades(new Date(2016, 0, 1), new Date(2136, 0, 1)); // 12\n * durationInDecades(new Date(2016, 0, 1), new Date(2016, 0, 1)); // 0\n * ```\n */\nexport var durationInDecades = function (start, end) { return ((firstYearOfDecade(end).getFullYear() - firstYearOfDecade(start).getFullYear()) / 10); };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,wBAAwB;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,iBAAiB,GAAG,SAAAA,CAAUC,KAAK,EAAEC,GAAG,EAAE;EAAE,OAAQ,CAACH,iBAAiB,CAACG,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGJ,iBAAiB,CAACE,KAAK,CAAC,CAACE,WAAW,CAAC,CAAC,IAAI,EAAE;AAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}