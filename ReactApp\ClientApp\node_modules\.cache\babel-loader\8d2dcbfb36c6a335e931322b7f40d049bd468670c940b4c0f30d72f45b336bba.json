{"ast": null, "code": "/**\n * @hidden\n */\nexport var isPresent = function (value) {\n  return value !== null && value !== undefined;\n};\n/**\n * @hidden\n */\nexport var isBlank = function (value) {\n  return value === null || value === undefined;\n};\n/**\n * @hidden\n */\nexport var isArray = function (value) {\n  return Array.isArray(value);\n};\n/**\n * @hidden\n */\nexport var isFunction = function (value) {\n  return typeof value === 'function';\n};\n/**\n * @hidden\n */\nexport var isString = function (value) {\n  return typeof value === 'string';\n};\n/**\n * @hidden\n */\nexport var isTruthy = function (value) {\n  return !!value;\n};\n/**\n * @hidden\n */\nexport var isNullOrEmptyString = function (value) {\n  return isBlank(value) || value.trim().length === 0;\n};\n/**\n * @hidden\n */\nexport var isNotNullOrEmptyString = function (value) {\n  return !isNullOrEmptyString(value);\n};\n/**\n * @hidden\n */\nexport var isNumeric = function (value) {\n  return !isNaN(value - parseFloat(value));\n};\n/**\n * @hidden\n */\nexport var isDate = function (value) {\n  return value && value.getTime;\n};", "map": {"version": 3, "names": ["isPresent", "value", "undefined", "isBlank", "isArray", "Array", "isFunction", "isString", "<PERSON><PERSON><PERSON><PERSON>", "isNullOrEmptyString", "trim", "length", "isNotNullOrEmptyString", "isNumeric", "isNaN", "parseFloat", "isDate", "getTime"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-data-query/dist/es/utils.js"], "sourcesContent": ["/**\n * @hidden\n */\nexport var isPresent = function (value) { return value !== null && value !== undefined; };\n/**\n * @hidden\n */\nexport var isBlank = function (value) { return value === null || value === undefined; };\n/**\n * @hidden\n */\nexport var isArray = function (value) { return Array.isArray(value); };\n/**\n * @hidden\n */\nexport var isFunction = function (value) { return typeof value === 'function'; };\n/**\n * @hidden\n */\nexport var isString = function (value) { return typeof value === 'string'; };\n/**\n * @hidden\n */\nexport var isTruthy = function (value) { return !!value; };\n/**\n * @hidden\n */\nexport var isNullOrEmptyString = function (value) { return isBlank(value) || value.trim().length === 0; };\n/**\n * @hidden\n */\nexport var isNotNullOrEmptyString = function (value) { return !isNullOrEmptyString(value); };\n/**\n * @hidden\n */\nexport var isNumeric = function (value) { return !isNaN(value - parseFloat(value)); };\n/**\n * @hidden\n */\nexport var isDate = function (value) { return value && value.getTime; };\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,IAAIA,SAAS,GAAG,SAAAA,CAAUC,KAAK,EAAE;EAAE,OAAOA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS;AAAE,CAAC;AACzF;AACA;AACA;AACA,OAAO,IAAIC,OAAO,GAAG,SAAAA,CAAUF,KAAK,EAAE;EAAE,OAAOA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS;AAAE,CAAC;AACvF;AACA;AACA;AACA,OAAO,IAAIE,OAAO,GAAG,SAAAA,CAAUH,KAAK,EAAE;EAAE,OAAOI,KAAK,CAACD,OAAO,CAACH,KAAK,CAAC;AAAE,CAAC;AACtE;AACA;AACA;AACA,OAAO,IAAIK,UAAU,GAAG,SAAAA,CAAUL,KAAK,EAAE;EAAE,OAAO,OAAOA,KAAK,KAAK,UAAU;AAAE,CAAC;AAChF;AACA;AACA;AACA,OAAO,IAAIM,QAAQ,GAAG,SAAAA,CAAUN,KAAK,EAAE;EAAE,OAAO,OAAOA,KAAK,KAAK,QAAQ;AAAE,CAAC;AAC5E;AACA;AACA;AACA,OAAO,IAAIO,QAAQ,GAAG,SAAAA,CAAUP,KAAK,EAAE;EAAE,OAAO,CAAC,CAACA,KAAK;AAAE,CAAC;AAC1D;AACA;AACA;AACA,OAAO,IAAIQ,mBAAmB,GAAG,SAAAA,CAAUR,KAAK,EAAE;EAAE,OAAOE,OAAO,CAACF,KAAK,CAAC,IAAIA,KAAK,CAACS,IAAI,CAAC,CAAC,CAACC,MAAM,KAAK,CAAC;AAAE,CAAC;AACzG;AACA;AACA;AACA,OAAO,IAAIC,sBAAsB,GAAG,SAAAA,CAAUX,KAAK,EAAE;EAAE,OAAO,CAACQ,mBAAmB,CAACR,KAAK,CAAC;AAAE,CAAC;AAC5F;AACA;AACA;AACA,OAAO,IAAIY,SAAS,GAAG,SAAAA,CAAUZ,KAAK,EAAE;EAAE,OAAO,CAACa,KAAK,CAACb,KAAK,GAAGc,UAAU,CAACd,KAAK,CAAC,CAAC;AAAE,CAAC;AACrF;AACA;AACA;AACA,OAAO,IAAIe,MAAM,GAAG,SAAAA,CAAUf,KAAK,EAAE;EAAE,OAAOA,KAAK,IAAIA,KAAK,CAACgB,OAAO;AAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}