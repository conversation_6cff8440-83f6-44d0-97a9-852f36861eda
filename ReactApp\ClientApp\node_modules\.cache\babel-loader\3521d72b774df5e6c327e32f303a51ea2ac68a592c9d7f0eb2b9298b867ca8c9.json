{"ast": null, "code": "export default function calculateCurveAt(t, field, points) {\n  var t1 = 1 - t;\n  return Math.pow(t1, 3) * points[0][field] + 3 * Math.pow(t1, 2) * t * points[1][field] + 3 * Math.pow(t, 2) * t1 * points[2][field] + Math.pow(t, 3) * points[3][field];\n}", "map": {"version": 3, "names": ["calculateCurveAt", "t", "field", "points", "t1", "Math", "pow"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/geometry/math/calculate-curve-at.js"], "sourcesContent": ["export default function calculateCurveAt(t, field, points) {\n    var t1 = 1 - t;\n    return Math.pow(t1, 3) * points[0][field] +\n        3 * Math.pow(t1, 2) * t * points[1][field] +\n        3 * Math.pow(t, 2) * t1 * points[2][field] +\n        Math.pow(t, 3) * points[3][field];\n}"], "mappings": "AAAA,eAAe,SAASA,gBAAgBA,CAACC,CAAC,EAAEC,KAAK,EAAEC,MAAM,EAAE;EACvD,IAAIC,EAAE,GAAG,CAAC,GAAGH,CAAC;EACd,OAAOI,IAAI,CAACC,GAAG,CAACF,EAAE,EAAE,CAAC,CAAC,GAAGD,MAAM,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC,GACrC,CAAC,GAAGG,IAAI,CAACC,GAAG,CAACF,EAAE,EAAE,CAAC,CAAC,GAAGH,CAAC,GAAGE,MAAM,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC,GAC1C,CAAC,GAAGG,IAAI,CAACC,GAAG,CAACL,CAAC,EAAE,CAAC,CAAC,GAAGG,EAAE,GAAGD,MAAM,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC,GAC1CG,IAAI,CAACC,GAAG,CAACL,CAAC,EAAE,CAAC,CAAC,GAAGE,MAAM,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}