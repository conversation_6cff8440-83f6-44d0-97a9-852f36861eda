{"ast": null, "code": "import TextMetrics from './text-metrics';\nexport default function measureText(text, style, measureBox) {\n  return TextMetrics.current.measure(text, style, measureBox);\n}", "map": {"version": 3, "names": ["TextMetrics", "measureText", "text", "style", "measureBox", "current", "measure"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/text-metrics/measure-text.js"], "sourcesContent": ["import TextMetrics from './text-metrics';\n\nexport default function measureText(text, style, measureBox) {\n    return TextMetrics.current.measure(text, style, measureBox);\n}\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,gBAAgB;AAExC,eAAe,SAASC,WAAWA,CAACC,IAAI,EAAEC,KAAK,EAAEC,UAAU,EAAE;EACzD,OAAOJ,WAAW,CAACK,OAAO,CAACC,OAAO,CAACJ,IAAI,EAAEC,KAAK,EAAEC,UAAU,CAAC;AAC/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}