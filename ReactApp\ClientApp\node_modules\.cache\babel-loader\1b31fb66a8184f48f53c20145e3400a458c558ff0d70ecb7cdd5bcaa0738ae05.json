{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as r from \"react\";\nclass n extends r.Component {\n  shouldComponentUpdate(e) {\n    return e.shouldUpdateOnDrag || !e.isDragging;\n  }\n  render() {\n    return this.props.children;\n  }\n}\nexport { n as MiddleLayerOptimization };", "map": {"version": 3, "names": ["r", "n", "Component", "shouldComponentUpdate", "e", "shouldUpdateOnDrag", "isDragging", "render", "props", "children", "MiddleLayerOptimization"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dialogs/MiddleLayerOptimization.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as r from \"react\";\nclass n extends r.Component {\n  shouldComponentUpdate(e) {\n    return e.shouldUpdateOnDrag || !e.isDragging;\n  }\n  render() {\n    return this.props.children;\n  }\n}\nexport {\n  n as MiddleLayerOptimization\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,MAAMC,CAAC,SAASD,CAAC,CAACE,SAAS,CAAC;EAC1BC,qBAAqBA,CAACC,CAAC,EAAE;IACvB,OAAOA,CAAC,CAACC,kBAAkB,IAAI,CAACD,CAAC,CAACE,UAAU;EAC9C;EACAC,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK,CAACC,QAAQ;EAC5B;AACF;AACA,SACER,CAAC,IAAIS,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}