{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as n from \"react\";\nimport t from \"prop-types\";\nimport { cloneDate as O } from \"@progress/kendo-date-math\";\nimport { Keys as T, classNames as l, uTime as h, getActiveElement as R } from \"@progress/kendo-react-common\";\nimport { provideIntlService as k, provideLocalizationService as S, registerForIntl as C, registerForLocalization as M } from \"@progress/kendo-react-intl\";\nimport { selectNow as L, messages as x, now as I } from \"../messages/index.mjs\";\nimport { TimeList as P } from \"./TimeList.mjs\";\nimport { MIN_TIME as A, MAX_TIME as K, MIDNIGHT_DATE as _ } from \"../utils.mjs\";\nimport { TIME_PART as d } from \"./models/TimePart.mjs\";\nimport { isInTimeRange as $, getNow as z, snapTime as N, generateSnappers as B, timeInRange as W } from \"./utils.mjs\";\nimport { Button as j } from \"@progress/kendo-react-buttons\";\nconst v = new RegExp(`${d.hour}|${d.minute}|${d.second}|${d.dayperiod}|literal`),\n  a = class a extends n.Component {\n    constructor(i) {\n      super(i), this._element = null, this._nowButton = null, this.dateFormatParts = [], this.timeLists = [], this.focus = (s, e) => {\n        Promise.resolve().then(() => {\n          e && this._nowButton && this._nowButton.element && this._nowButton.element.focus();\n          const o = this.timeLists[0];\n          !e && this.state.activeListIndex === -1 && !this.hasActiveButton() && o && o.element && o.focus(s);\n        });\n      }, this.timeFormatReducer = (s, e) => s + e.pattern, this.timeFormatFilter = (s, e, o) => {\n        const r = e >= 1 && o[e - 1];\n        return r && r && s.type === \"literal\" ? v.test(r.type || \"\") : v.test(s.type || \"\");\n      }, this.focusList = s => {\n        this.timeLists.length && this.timeLists.reduce(this.listReducer, []).map(e => s === 1 ? e.next : e.prev).map(e => e && e.element && e.element.focus({\n          preventScroll: !0\n        }));\n      }, this.listReducer = (s, e, o, r) => s.length || e.props.id !== this.state.activeListIndex ? s : [{\n        next: r[o + 1] || e,\n        prev: r[o - 1] || e\n      }], this.showNowButton = () => !this.hasSteps() && this.props.nowButton && $(z(), this.min, this.max), this.handleKeyDown = s => {\n        const {\n          keyCode: e\n        } = s;\n        switch (e) {\n          case T.left:\n            s.preventDefault(), this.focusList(0\n            /* Left */);\n            return;\n          case T.right:\n            s.preventDefault(), this.focusList(1\n            /* Right */);\n            return;\n          default:\n            return;\n        }\n      }, this.handleListBlur = () => {\n        this.nextTick(() => {\n          this.setState({\n            activeListIndex: -1\n          });\n        });\n      }, this.handleListFocus = s => {\n        clearTimeout(this.nextTickId), this.setState({\n          activeListIndex: s\n        });\n      }, this.handleChange = s => {\n        const {\n          onChange: e\n        } = this.props;\n        e && e.call(void 0, s);\n      }, this.snapTime = N(B(this.props.steps, this.props.min || a.defaultProps.min)), this.state = {\n        activeListIndex: -1\n      }, this.hasActiveButton = this.hasActiveButton.bind(this);\n    }\n    /**\n     * @hidden\n     */\n    get element() {\n      return this._element;\n    }\n    get value() {\n      return W(this.snapTime(O(this.props.value || _)), this.min, this.max);\n    }\n    get intl() {\n      return k(this);\n    }\n    get min() {\n      return this.snapTime(this.props.min || a.defaultProps.min);\n    }\n    get max() {\n      return this.snapTime(this.props.max || a.defaultProps.max);\n    }\n    get steps() {\n      return this.props.steps || a.defaultProps.steps;\n    }\n    get boundRange() {\n      return this.props.boundRange !== void 0 ? this.props.boundRange : a.defaultProps.boundRange;\n    }\n    /**\n     * @hidden\n     */\n    componentWillUnmount() {\n      clearTimeout(this.nextTickId);\n    }\n    componentDidMount() {\n      const {\n        onMount: i\n      } = this.props;\n      i && i.call(void 0, this.value);\n    }\n    /**\n     * @hidden\n     */\n    render() {\n      const {\n          format: i,\n          smoothScroll: s,\n          onNowClick: e,\n          className: o,\n          disabled: r,\n          mobileMode: w,\n          show: D,\n          onNowKeyDown: E,\n          unstyled: g\n        } = this.props,\n        m = g && g.uTime;\n      this.snapTime = N(B(this.steps, this.min)), this.dateFormatParts = this.intl.splitDateFormat(i || a.defaultProps.format).filter(this.timeFormatFilter);\n      const F = l(h.part({\n        c: m,\n        mobileMode: w,\n        disabled: r\n      }), o);\n      this.timeLists = [];\n      const b = S(this),\n        y = b.toLanguageString(L, x[L]);\n      return /* @__PURE__ */n.createElement(\"div\", {\n        className: F\n      }, /* @__PURE__ */n.createElement(\"div\", {\n        className: l(h.header({\n          c: m\n        }))\n      }, /* @__PURE__ */n.createElement(\"span\", {\n        className: l(h.title({\n          c: m\n        }))\n      }, this.intl.formatDate(this.value, this.dateFormatParts.reduce(this.timeFormatReducer, \"\"))), this.showNowButton() && /* @__PURE__ */n.createElement(j, {\n        type: \"button\",\n        ref: u => {\n          this._nowButton = u;\n        },\n        className: l(h.now({\n          c: m\n        })),\n        fillMode: \"flat\",\n        themeColor: \"base\",\n        title: y,\n        onKeyDown: E,\n        \"aria-label\": y,\n        onClick: e,\n        tabIndex: r ? -1 : 0\n      }, b.toLanguageString(I, x[I]))), /* @__PURE__ */n.createElement(\"div\", {\n        className: l(h.listContainer({\n          c: m\n        })),\n        onKeyDown: this.handleKeyDown\n      }, /* @__PURE__ */n.createElement(\"span\", {\n        className: l(h.highlight({\n          c: m\n        }))\n      }), this.dateFormatParts.map((u, c) => u.type !== \"literal\" ? /* @__PURE__ */n.createElement(\"div\", {\n        key: c,\n        className: l(h.listWrapper({\n          c: m,\n          focused: c === this.state.activeListIndex\n        })),\n        role: \"presentation\",\n        tabIndex: -1\n      }, /* @__PURE__ */n.createElement(\"span\", {\n        className: l(h.title({\n          c: m\n        })),\n        onMouseDown: p => {\n          p.preventDefault();\n        }\n      }, this.intl.dateFieldName(u)), /* @__PURE__ */n.createElement(P, {\n        min: this.min,\n        max: this.max,\n        boundRange: this.boundRange,\n        part: u,\n        step: u.type ? this.steps[u.type] : 1,\n        smoothScroll: s,\n        ref: p => {\n          p && this.timeLists.push(p);\n        },\n        id: c,\n        onFocus: () => {\n          this.handleListFocus(c);\n        },\n        onBlur: this.handleListBlur,\n        onChange: this.handleChange,\n        value: this.value,\n        disabled: r,\n        show: D,\n        mobileMode: w,\n        unstyled: g\n      })) : /* @__PURE__ */n.createElement(\"div\", {\n        key: c,\n        className: l(h.separator({\n          c: m\n        }))\n      }, u.pattern))));\n    }\n    nextTick(i) {\n      clearTimeout(this.nextTickId), this.nextTickId = window.setTimeout(() => i());\n    }\n    hasActiveButton() {\n      const i = R(document);\n      return this._nowButton && i === this._nowButton.element;\n    }\n    hasSteps() {\n      const i = Object.keys(this.steps);\n      return i.length !== i.reduce((s, e) => s + this.steps[e], 0);\n    }\n  };\na.propTypes = {\n  cancelButton: t.bool,\n  disabled: t.bool,\n  format: t.oneOfType([t.string, t.shape({\n    skeleton: t.string,\n    pattern: t.string,\n    date: t.oneOf([\"short\", \"medium\", \"long\", \"full\"]),\n    time: t.oneOf([\"short\", \"medium\", \"long\", \"full\"]),\n    datetime: t.oneOf([\"short\", \"medium\", \"long\", \"full\"]),\n    era: t.oneOf([\"narrow\", \"short\", \"long\"]),\n    year: t.oneOf([\"numeric\", \"2-digit\"]),\n    month: t.oneOf([\"numeric\", \"2-digit\", \"narrow\", \"short\", \"long\"]),\n    day: t.oneOf([\"numeric\", \"2-digit\"]),\n    weekday: t.oneOf([\"narrow\", \"short\", \"long\"]),\n    hour: t.oneOf([\"numeric\", \"2-digit\"]),\n    hour12: t.bool,\n    minute: t.oneOf([\"numeric\", \"2-digit\"]),\n    second: t.oneOf([\"numeric\", \"2-digit\"]),\n    timeZoneName: t.oneOf([\"short\", \"long\"])\n  })]),\n  max: t.instanceOf(Date),\n  min: t.instanceOf(Date),\n  nowButton: t.bool,\n  steps: t.shape({\n    hour: t.number,\n    minute: t.number,\n    second: t.number\n  }),\n  smoothScroll: t.bool,\n  tabIndex: t.number,\n  value: t.instanceOf(Date),\n  show: t.bool\n}, a.defaultProps = {\n  value: null,\n  disabled: !1,\n  nowButton: !0,\n  cancelButton: !0,\n  format: \"hh:mm a\",\n  min: A,\n  max: K,\n  steps: {},\n  boundRange: !1\n};\nlet f = a;\nC(f);\nM(f);\nexport { f as TimePart };", "map": {"version": 3, "names": ["n", "t", "cloneDate", "O", "Keys", "T", "classNames", "l", "uTime", "h", "getActiveElement", "R", "provideIntlService", "k", "provideLocalizationService", "S", "registerForIntl", "C", "registerForLocalization", "M", "selectNow", "L", "messages", "x", "now", "I", "TimeList", "P", "MIN_TIME", "A", "MAX_TIME", "K", "MIDNIGHT_DATE", "_", "TIME_PART", "d", "isInTimeRange", "$", "getNow", "z", "snapTime", "N", "generateSnappers", "B", "timeInRange", "W", "<PERSON><PERSON>", "j", "v", "RegExp", "hour", "minute", "second", "dayperiod", "a", "Component", "constructor", "i", "_element", "_now<PERSON><PERSON><PERSON>", "dateFormatParts", "timeLists", "focus", "s", "e", "Promise", "resolve", "then", "element", "o", "state", "activeListIndex", "hasActiveButton", "timeFormatReducer", "pattern", "timeFormatFilter", "r", "type", "test", "focusList", "length", "reduce", "listReducer", "map", "next", "prev", "preventScroll", "props", "id", "showNowButton", "hasSteps", "nowButton", "min", "max", "handleKeyDown", "keyCode", "left", "preventDefault", "right", "handleListBlur", "nextTick", "setState", "handleListFocus", "clearTimeout", "nextTickId", "handleChange", "onChange", "call", "steps", "defaultProps", "bind", "value", "intl", "boundRange", "componentWillUnmount", "componentDidMount", "onMount", "render", "format", "smoothScroll", "onNowClick", "className", "disabled", "mobileMode", "w", "show", "D", "onNowKeyDown", "E", "unstyled", "g", "m", "splitDateFormat", "filter", "F", "part", "c", "b", "y", "toLanguageString", "createElement", "header", "title", "formatDate", "ref", "u", "fillMode", "themeColor", "onKeyDown", "onClick", "tabIndex", "listContainer", "highlight", "key", "listWrapper", "focused", "role", "onMouseDown", "p", "dateFieldName", "step", "push", "onFocus", "onBlur", "separator", "window", "setTimeout", "document", "Object", "keys", "propTypes", "cancelButton", "bool", "oneOfType", "string", "shape", "skeleton", "date", "oneOf", "time", "datetime", "era", "year", "month", "day", "weekday", "hour12", "timeZoneName", "instanceOf", "Date", "number", "f", "TimePart"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dateinputs/timepicker/TimePart.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as n from \"react\";\nimport t from \"prop-types\";\nimport { cloneDate as O } from \"@progress/kendo-date-math\";\nimport { Keys as T, classNames as l, uTime as h, getActiveElement as R } from \"@progress/kendo-react-common\";\nimport { provideIntlService as k, provideLocalizationService as S, registerForIntl as C, registerForLocalization as M } from \"@progress/kendo-react-intl\";\nimport { selectNow as L, messages as x, now as I } from \"../messages/index.mjs\";\nimport { TimeList as P } from \"./TimeList.mjs\";\nimport { MIN_TIME as A, MAX_TIME as K, MIDNIGHT_DATE as _ } from \"../utils.mjs\";\nimport { TIME_PART as d } from \"./models/TimePart.mjs\";\nimport { isInTimeRange as $, getNow as z, snapTime as N, generateSnappers as B, timeInRange as W } from \"./utils.mjs\";\nimport { Button as j } from \"@progress/kendo-react-buttons\";\nconst v = new RegExp(\n  `${d.hour}|${d.minute}|${d.second}|${d.dayperiod}|literal`\n), a = class a extends n.Component {\n  constructor(i) {\n    super(i), this._element = null, this._nowButton = null, this.dateFormatParts = [], this.timeLists = [], this.focus = (s, e) => {\n      Promise.resolve().then(() => {\n        e && this._nowButton && this._nowButton.element && this._nowButton.element.focus();\n        const o = this.timeLists[0];\n        !e && this.state.activeListIndex === -1 && !this.hasActiveButton() && o && o.element && o.focus(s);\n      });\n    }, this.timeFormatReducer = (s, e) => s + e.pattern, this.timeFormatFilter = (s, e, o) => {\n      const r = e >= 1 && o[e - 1];\n      return r && r && s.type === \"literal\" ? v.test(r.type || \"\") : v.test(s.type || \"\");\n    }, this.focusList = (s) => {\n      this.timeLists.length && this.timeLists.reduce(this.listReducer, []).map((e) => s === 1 ? e.next : e.prev).map((e) => e && e.element && e.element.focus({ preventScroll: !0 }));\n    }, this.listReducer = (s, e, o, r) => s.length || e.props.id !== this.state.activeListIndex ? s : [\n      {\n        next: r[o + 1] || e,\n        prev: r[o - 1] || e\n      }\n    ], this.showNowButton = () => !this.hasSteps() && this.props.nowButton && $(z(), this.min, this.max), this.handleKeyDown = (s) => {\n      const { keyCode: e } = s;\n      switch (e) {\n        case T.left:\n          s.preventDefault(), this.focusList(\n            0\n            /* Left */\n          );\n          return;\n        case T.right:\n          s.preventDefault(), this.focusList(\n            1\n            /* Right */\n          );\n          return;\n        default:\n          return;\n      }\n    }, this.handleListBlur = () => {\n      this.nextTick(() => {\n        this.setState({ activeListIndex: -1 });\n      });\n    }, this.handleListFocus = (s) => {\n      clearTimeout(this.nextTickId), this.setState({\n        activeListIndex: s\n      });\n    }, this.handleChange = (s) => {\n      const { onChange: e } = this.props;\n      e && e.call(void 0, s);\n    }, this.snapTime = N(B(this.props.steps, this.props.min || a.defaultProps.min)), this.state = {\n      activeListIndex: -1\n    }, this.hasActiveButton = this.hasActiveButton.bind(this);\n  }\n  /**\n   * @hidden\n   */\n  get element() {\n    return this._element;\n  }\n  get value() {\n    return W(this.snapTime(O(this.props.value || _)), this.min, this.max);\n  }\n  get intl() {\n    return k(this);\n  }\n  get min() {\n    return this.snapTime(this.props.min || a.defaultProps.min);\n  }\n  get max() {\n    return this.snapTime(this.props.max || a.defaultProps.max);\n  }\n  get steps() {\n    return this.props.steps || a.defaultProps.steps;\n  }\n  get boundRange() {\n    return this.props.boundRange !== void 0 ? this.props.boundRange : a.defaultProps.boundRange;\n  }\n  /**\n   * @hidden\n   */\n  componentWillUnmount() {\n    clearTimeout(this.nextTickId);\n  }\n  componentDidMount() {\n    const { onMount: i } = this.props;\n    i && i.call(void 0, this.value);\n  }\n  /**\n   * @hidden\n   */\n  render() {\n    const { format: i, smoothScroll: s, onNowClick: e, className: o, disabled: r, mobileMode: w, show: D, onNowKeyDown: E, unstyled: g } = this.props, m = g && g.uTime;\n    this.snapTime = N(B(this.steps, this.min)), this.dateFormatParts = this.intl.splitDateFormat(i || a.defaultProps.format).filter(this.timeFormatFilter);\n    const F = l(\n      h.part({\n        c: m,\n        mobileMode: w,\n        disabled: r\n      }),\n      o\n    );\n    this.timeLists = [];\n    const b = S(this), y = b.toLanguageString(L, x[L]);\n    return /* @__PURE__ */ n.createElement(\"div\", { className: F }, /* @__PURE__ */ n.createElement(\"div\", { className: l(h.header({ c: m })) }, /* @__PURE__ */ n.createElement(\"span\", { className: l(h.title({ c: m })) }, this.intl.formatDate(this.value, this.dateFormatParts.reduce(this.timeFormatReducer, \"\"))), this.showNowButton() && /* @__PURE__ */ n.createElement(\n      j,\n      {\n        type: \"button\",\n        ref: (u) => {\n          this._nowButton = u;\n        },\n        className: l(h.now({ c: m })),\n        fillMode: \"flat\",\n        themeColor: \"base\",\n        title: y,\n        onKeyDown: E,\n        \"aria-label\": y,\n        onClick: e,\n        tabIndex: r ? -1 : 0\n      },\n      b.toLanguageString(I, x[I])\n    )), /* @__PURE__ */ n.createElement(\"div\", { className: l(h.listContainer({ c: m })), onKeyDown: this.handleKeyDown }, /* @__PURE__ */ n.createElement(\"span\", { className: l(h.highlight({ c: m })) }), this.dateFormatParts.map((u, c) => u.type !== \"literal\" ? /* @__PURE__ */ n.createElement(\n      \"div\",\n      {\n        key: c,\n        className: l(\n          h.listWrapper({\n            c: m,\n            focused: c === this.state.activeListIndex\n          })\n        ),\n        role: \"presentation\",\n        tabIndex: -1\n      },\n      /* @__PURE__ */ n.createElement(\n        \"span\",\n        {\n          className: l(h.title({ c: m })),\n          onMouseDown: (p) => {\n            p.preventDefault();\n          }\n        },\n        this.intl.dateFieldName(u)\n      ),\n      /* @__PURE__ */ n.createElement(\n        P,\n        {\n          min: this.min,\n          max: this.max,\n          boundRange: this.boundRange,\n          part: u,\n          step: u.type ? this.steps[u.type] : 1,\n          smoothScroll: s,\n          ref: (p) => {\n            p && this.timeLists.push(p);\n          },\n          id: c,\n          onFocus: () => {\n            this.handleListFocus(c);\n          },\n          onBlur: this.handleListBlur,\n          onChange: this.handleChange,\n          value: this.value,\n          disabled: r,\n          show: D,\n          mobileMode: w,\n          unstyled: g\n        }\n      )\n    ) : /* @__PURE__ */ n.createElement(\"div\", { key: c, className: l(h.separator({ c: m })) }, u.pattern))));\n  }\n  nextTick(i) {\n    clearTimeout(this.nextTickId), this.nextTickId = window.setTimeout(() => i());\n  }\n  hasActiveButton() {\n    const i = R(document);\n    return this._nowButton && i === this._nowButton.element;\n  }\n  hasSteps() {\n    const i = Object.keys(this.steps);\n    return i.length !== i.reduce((s, e) => s + this.steps[e], 0);\n  }\n};\na.propTypes = {\n  cancelButton: t.bool,\n  disabled: t.bool,\n  format: t.oneOfType([\n    t.string,\n    t.shape({\n      skeleton: t.string,\n      pattern: t.string,\n      date: t.oneOf([\"short\", \"medium\", \"long\", \"full\"]),\n      time: t.oneOf([\"short\", \"medium\", \"long\", \"full\"]),\n      datetime: t.oneOf([\"short\", \"medium\", \"long\", \"full\"]),\n      era: t.oneOf([\"narrow\", \"short\", \"long\"]),\n      year: t.oneOf([\"numeric\", \"2-digit\"]),\n      month: t.oneOf([\"numeric\", \"2-digit\", \"narrow\", \"short\", \"long\"]),\n      day: t.oneOf([\"numeric\", \"2-digit\"]),\n      weekday: t.oneOf([\"narrow\", \"short\", \"long\"]),\n      hour: t.oneOf([\"numeric\", \"2-digit\"]),\n      hour12: t.bool,\n      minute: t.oneOf([\"numeric\", \"2-digit\"]),\n      second: t.oneOf([\"numeric\", \"2-digit\"]),\n      timeZoneName: t.oneOf([\"short\", \"long\"])\n    })\n  ]),\n  max: t.instanceOf(Date),\n  min: t.instanceOf(Date),\n  nowButton: t.bool,\n  steps: t.shape({\n    hour: t.number,\n    minute: t.number,\n    second: t.number\n  }),\n  smoothScroll: t.bool,\n  tabIndex: t.number,\n  value: t.instanceOf(Date),\n  show: t.bool\n}, a.defaultProps = {\n  value: null,\n  disabled: !1,\n  nowButton: !0,\n  cancelButton: !0,\n  format: \"hh:mm a\",\n  min: A,\n  max: K,\n  steps: {},\n  boundRange: !1\n};\nlet f = a;\nC(f);\nM(f);\nexport {\n  f as TimePart\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,SAAS,IAAIC,CAAC,QAAQ,2BAA2B;AAC1D,SAASC,IAAI,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,EAAEC,KAAK,IAAIC,CAAC,EAAEC,gBAAgB,IAAIC,CAAC,QAAQ,8BAA8B;AAC5G,SAASC,kBAAkB,IAAIC,CAAC,EAAEC,0BAA0B,IAAIC,CAAC,EAAEC,eAAe,IAAIC,CAAC,EAAEC,uBAAuB,IAAIC,CAAC,QAAQ,4BAA4B;AACzJ,SAASC,SAAS,IAAIC,CAAC,EAAEC,QAAQ,IAAIC,CAAC,EAAEC,GAAG,IAAIC,CAAC,QAAQ,uBAAuB;AAC/E,SAASC,QAAQ,IAAIC,CAAC,QAAQ,gBAAgB;AAC9C,SAASC,QAAQ,IAAIC,CAAC,EAAEC,QAAQ,IAAIC,CAAC,EAAEC,aAAa,IAAIC,CAAC,QAAQ,cAAc;AAC/E,SAASC,SAAS,IAAIC,CAAC,QAAQ,uBAAuB;AACtD,SAASC,aAAa,IAAIC,CAAC,EAAEC,MAAM,IAAIC,CAAC,EAAEC,QAAQ,IAAIC,CAAC,EAAEC,gBAAgB,IAAIC,CAAC,EAAEC,WAAW,IAAIC,CAAC,QAAQ,aAAa;AACrH,SAASC,MAAM,IAAIC,CAAC,QAAQ,+BAA+B;AAC3D,MAAMC,CAAC,GAAG,IAAIC,MAAM,CAClB,GAAGd,CAAC,CAACe,IAAI,IAAIf,CAAC,CAACgB,MAAM,IAAIhB,CAAC,CAACiB,MAAM,IAAIjB,CAAC,CAACkB,SAAS,UAClD,CAAC;EAAEC,CAAC,GAAG,MAAMA,CAAC,SAAStD,CAAC,CAACuD,SAAS,CAAC;IACjCC,WAAWA,CAACC,CAAC,EAAE;MACb,KAAK,CAACA,CAAC,CAAC,EAAE,IAAI,CAACC,QAAQ,GAAG,IAAI,EAAE,IAAI,CAACC,UAAU,GAAG,IAAI,EAAE,IAAI,CAACC,eAAe,GAAG,EAAE,EAAE,IAAI,CAACC,SAAS,GAAG,EAAE,EAAE,IAAI,CAACC,KAAK,GAAG,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC7HC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;UAC3BH,CAAC,IAAI,IAAI,CAACL,UAAU,IAAI,IAAI,CAACA,UAAU,CAACS,OAAO,IAAI,IAAI,CAACT,UAAU,CAACS,OAAO,CAACN,KAAK,CAAC,CAAC;UAClF,MAAMO,CAAC,GAAG,IAAI,CAACR,SAAS,CAAC,CAAC,CAAC;UAC3B,CAACG,CAAC,IAAI,IAAI,CAACM,KAAK,CAACC,eAAe,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAACC,eAAe,CAAC,CAAC,IAAIH,CAAC,IAAIA,CAAC,CAACD,OAAO,IAAIC,CAAC,CAACP,KAAK,CAACC,CAAC,CAAC;QACpG,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,CAACU,iBAAiB,GAAG,CAACV,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAACU,OAAO,EAAE,IAAI,CAACC,gBAAgB,GAAG,CAACZ,CAAC,EAAEC,CAAC,EAAEK,CAAC,KAAK;QACxF,MAAMO,CAAC,GAAGZ,CAAC,IAAI,CAAC,IAAIK,CAAC,CAACL,CAAC,GAAG,CAAC,CAAC;QAC5B,OAAOY,CAAC,IAAIA,CAAC,IAAIb,CAAC,CAACc,IAAI,KAAK,SAAS,GAAG7B,CAAC,CAAC8B,IAAI,CAACF,CAAC,CAACC,IAAI,IAAI,EAAE,CAAC,GAAG7B,CAAC,CAAC8B,IAAI,CAACf,CAAC,CAACc,IAAI,IAAI,EAAE,CAAC;MACrF,CAAC,EAAE,IAAI,CAACE,SAAS,GAAIhB,CAAC,IAAK;QACzB,IAAI,CAACF,SAAS,CAACmB,MAAM,IAAI,IAAI,CAACnB,SAAS,CAACoB,MAAM,CAAC,IAAI,CAACC,WAAW,EAAE,EAAE,CAAC,CAACC,GAAG,CAAEnB,CAAC,IAAKD,CAAC,KAAK,CAAC,GAAGC,CAAC,CAACoB,IAAI,GAAGpB,CAAC,CAACqB,IAAI,CAAC,CAACF,GAAG,CAAEnB,CAAC,IAAKA,CAAC,IAAIA,CAAC,CAACI,OAAO,IAAIJ,CAAC,CAACI,OAAO,CAACN,KAAK,CAAC;UAAEwB,aAAa,EAAE,CAAC;QAAE,CAAC,CAAC,CAAC;MACjL,CAAC,EAAE,IAAI,CAACJ,WAAW,GAAG,CAACnB,CAAC,EAAEC,CAAC,EAAEK,CAAC,EAAEO,CAAC,KAAKb,CAAC,CAACiB,MAAM,IAAIhB,CAAC,CAACuB,KAAK,CAACC,EAAE,KAAK,IAAI,CAAClB,KAAK,CAACC,eAAe,GAAGR,CAAC,GAAG,CAChG;QACEqB,IAAI,EAAER,CAAC,CAACP,CAAC,GAAG,CAAC,CAAC,IAAIL,CAAC;QACnBqB,IAAI,EAAET,CAAC,CAACP,CAAC,GAAG,CAAC,CAAC,IAAIL;MACpB,CAAC,CACF,EAAE,IAAI,CAACyB,aAAa,GAAG,MAAM,CAAC,IAAI,CAACC,QAAQ,CAAC,CAAC,IAAI,IAAI,CAACH,KAAK,CAACI,SAAS,IAAItD,CAAC,CAACE,CAAC,CAAC,CAAC,EAAE,IAAI,CAACqD,GAAG,EAAE,IAAI,CAACC,GAAG,CAAC,EAAE,IAAI,CAACC,aAAa,GAAI/B,CAAC,IAAK;QAChI,MAAM;UAAEgC,OAAO,EAAE/B;QAAE,CAAC,GAAGD,CAAC;QACxB,QAAQC,CAAC;UACP,KAAK3D,CAAC,CAAC2F,IAAI;YACTjC,CAAC,CAACkC,cAAc,CAAC,CAAC,EAAE,IAAI,CAAClB,SAAS,CAChC;YACA,UACF,CAAC;YACD;UACF,KAAK1E,CAAC,CAAC6F,KAAK;YACVnC,CAAC,CAACkC,cAAc,CAAC,CAAC,EAAE,IAAI,CAAClB,SAAS,CAChC;YACA,WACF,CAAC;YACD;UACF;YACE;QACJ;MACF,CAAC,EAAE,IAAI,CAACoB,cAAc,GAAG,MAAM;QAC7B,IAAI,CAACC,QAAQ,CAAC,MAAM;UAClB,IAAI,CAACC,QAAQ,CAAC;YAAE9B,eAAe,EAAE,CAAC;UAAE,CAAC,CAAC;QACxC,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,CAAC+B,eAAe,GAAIvC,CAAC,IAAK;QAC/BwC,YAAY,CAAC,IAAI,CAACC,UAAU,CAAC,EAAE,IAAI,CAACH,QAAQ,CAAC;UAC3C9B,eAAe,EAAER;QACnB,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,CAAC0C,YAAY,GAAI1C,CAAC,IAAK;QAC5B,MAAM;UAAE2C,QAAQ,EAAE1C;QAAE,CAAC,GAAG,IAAI,CAACuB,KAAK;QAClCvB,CAAC,IAAIA,CAAC,CAAC2C,IAAI,CAAC,KAAK,CAAC,EAAE5C,CAAC,CAAC;MACxB,CAAC,EAAE,IAAI,CAACvB,QAAQ,GAAGC,CAAC,CAACE,CAAC,CAAC,IAAI,CAAC4C,KAAK,CAACqB,KAAK,EAAE,IAAI,CAACrB,KAAK,CAACK,GAAG,IAAItC,CAAC,CAACuD,YAAY,CAACjB,GAAG,CAAC,CAAC,EAAE,IAAI,CAACtB,KAAK,GAAG;QAC5FC,eAAe,EAAE,CAAC;MACpB,CAAC,EAAE,IAAI,CAACC,eAAe,GAAG,IAAI,CAACA,eAAe,CAACsC,IAAI,CAAC,IAAI,CAAC;IAC3D;IACA;AACF;AACA;IACE,IAAI1C,OAAOA,CAAA,EAAG;MACZ,OAAO,IAAI,CAACV,QAAQ;IACtB;IACA,IAAIqD,KAAKA,CAAA,EAAG;MACV,OAAOlE,CAAC,CAAC,IAAI,CAACL,QAAQ,CAACrC,CAAC,CAAC,IAAI,CAACoF,KAAK,CAACwB,KAAK,IAAI9E,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC2D,GAAG,EAAE,IAAI,CAACC,GAAG,CAAC;IACvE;IACA,IAAImB,IAAIA,CAAA,EAAG;MACT,OAAOnG,CAAC,CAAC,IAAI,CAAC;IAChB;IACA,IAAI+E,GAAGA,CAAA,EAAG;MACR,OAAO,IAAI,CAACpD,QAAQ,CAAC,IAAI,CAAC+C,KAAK,CAACK,GAAG,IAAItC,CAAC,CAACuD,YAAY,CAACjB,GAAG,CAAC;IAC5D;IACA,IAAIC,GAAGA,CAAA,EAAG;MACR,OAAO,IAAI,CAACrD,QAAQ,CAAC,IAAI,CAAC+C,KAAK,CAACM,GAAG,IAAIvC,CAAC,CAACuD,YAAY,CAAChB,GAAG,CAAC;IAC5D;IACA,IAAIe,KAAKA,CAAA,EAAG;MACV,OAAO,IAAI,CAACrB,KAAK,CAACqB,KAAK,IAAItD,CAAC,CAACuD,YAAY,CAACD,KAAK;IACjD;IACA,IAAIK,UAAUA,CAAA,EAAG;MACf,OAAO,IAAI,CAAC1B,KAAK,CAAC0B,UAAU,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC1B,KAAK,CAAC0B,UAAU,GAAG3D,CAAC,CAACuD,YAAY,CAACI,UAAU;IAC7F;IACA;AACF;AACA;IACEC,oBAAoBA,CAAA,EAAG;MACrBX,YAAY,CAAC,IAAI,CAACC,UAAU,CAAC;IAC/B;IACAW,iBAAiBA,CAAA,EAAG;MAClB,MAAM;QAAEC,OAAO,EAAE3D;MAAE,CAAC,GAAG,IAAI,CAAC8B,KAAK;MACjC9B,CAAC,IAAIA,CAAC,CAACkD,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAACI,KAAK,CAAC;IACjC;IACA;AACF;AACA;IACEM,MAAMA,CAAA,EAAG;MACP,MAAM;UAAEC,MAAM,EAAE7D,CAAC;UAAE8D,YAAY,EAAExD,CAAC;UAAEyD,UAAU,EAAExD,CAAC;UAAEyD,SAAS,EAAEpD,CAAC;UAAEqD,QAAQ,EAAE9C,CAAC;UAAE+C,UAAU,EAAEC,CAAC;UAAEC,IAAI,EAAEC,CAAC;UAAEC,YAAY,EAAEC,CAAC;UAAEC,QAAQ,EAAEC;QAAE,CAAC,GAAG,IAAI,CAAC3C,KAAK;QAAE4C,CAAC,GAAGD,CAAC,IAAIA,CAAC,CAAC1H,KAAK;MACnK,IAAI,CAACgC,QAAQ,GAAGC,CAAC,CAACE,CAAC,CAAC,IAAI,CAACiE,KAAK,EAAE,IAAI,CAAChB,GAAG,CAAC,CAAC,EAAE,IAAI,CAAChC,eAAe,GAAG,IAAI,CAACoD,IAAI,CAACoB,eAAe,CAAC3E,CAAC,IAAIH,CAAC,CAACuD,YAAY,CAACS,MAAM,CAAC,CAACe,MAAM,CAAC,IAAI,CAAC1D,gBAAgB,CAAC;MACtJ,MAAM2D,CAAC,GAAG/H,CAAC,CACTE,CAAC,CAAC8H,IAAI,CAAC;QACLC,CAAC,EAAEL,CAAC;QACJR,UAAU,EAAEC,CAAC;QACbF,QAAQ,EAAE9C;MACZ,CAAC,CAAC,EACFP,CACF,CAAC;MACD,IAAI,CAACR,SAAS,GAAG,EAAE;MACnB,MAAM4E,CAAC,GAAG1H,CAAC,CAAC,IAAI,CAAC;QAAE2H,CAAC,GAAGD,CAAC,CAACE,gBAAgB,CAACtH,CAAC,EAAEE,CAAC,CAACF,CAAC,CAAC,CAAC;MAClD,OAAO,eAAgBrB,CAAC,CAAC4I,aAAa,CAAC,KAAK,EAAE;QAAEnB,SAAS,EAAEa;MAAE,CAAC,EAAE,eAAgBtI,CAAC,CAAC4I,aAAa,CAAC,KAAK,EAAE;QAAEnB,SAAS,EAAElH,CAAC,CAACE,CAAC,CAACoI,MAAM,CAAC;UAAEL,CAAC,EAAEL;QAAE,CAAC,CAAC;MAAE,CAAC,EAAE,eAAgBnI,CAAC,CAAC4I,aAAa,CAAC,MAAM,EAAE;QAAEnB,SAAS,EAAElH,CAAC,CAACE,CAAC,CAACqI,KAAK,CAAC;UAAEN,CAAC,EAAEL;QAAE,CAAC,CAAC;MAAE,CAAC,EAAE,IAAI,CAACnB,IAAI,CAAC+B,UAAU,CAAC,IAAI,CAAChC,KAAK,EAAE,IAAI,CAACnD,eAAe,CAACqB,MAAM,CAAC,IAAI,CAACR,iBAAiB,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAACgB,aAAa,CAAC,CAAC,IAAI,eAAgBzF,CAAC,CAAC4I,aAAa,CAC3W7F,CAAC,EACD;QACE8B,IAAI,EAAE,QAAQ;QACdmE,GAAG,EAAGC,CAAC,IAAK;UACV,IAAI,CAACtF,UAAU,GAAGsF,CAAC;QACrB,CAAC;QACDxB,SAAS,EAAElH,CAAC,CAACE,CAAC,CAACe,GAAG,CAAC;UAAEgH,CAAC,EAAEL;QAAE,CAAC,CAAC,CAAC;QAC7Be,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE,MAAM;QAClBL,KAAK,EAAEJ,CAAC;QACRU,SAAS,EAAEpB,CAAC;QACZ,YAAY,EAAEU,CAAC;QACfW,OAAO,EAAErF,CAAC;QACVsF,QAAQ,EAAE1E,CAAC,GAAG,CAAC,CAAC,GAAG;MACrB,CAAC,EACD6D,CAAC,CAACE,gBAAgB,CAAClH,CAAC,EAAEF,CAAC,CAACE,CAAC,CAAC,CAC5B,CAAC,CAAC,EAAE,eAAgBzB,CAAC,CAAC4I,aAAa,CAAC,KAAK,EAAE;QAAEnB,SAAS,EAAElH,CAAC,CAACE,CAAC,CAAC8I,aAAa,CAAC;UAAEf,CAAC,EAAEL;QAAE,CAAC,CAAC,CAAC;QAAEiB,SAAS,EAAE,IAAI,CAACtD;MAAc,CAAC,EAAE,eAAgB9F,CAAC,CAAC4I,aAAa,CAAC,MAAM,EAAE;QAAEnB,SAAS,EAAElH,CAAC,CAACE,CAAC,CAAC+I,SAAS,CAAC;UAAEhB,CAAC,EAAEL;QAAE,CAAC,CAAC;MAAE,CAAC,CAAC,EAAE,IAAI,CAACvE,eAAe,CAACuB,GAAG,CAAC,CAAC8D,CAAC,EAAET,CAAC,KAAKS,CAAC,CAACpE,IAAI,KAAK,SAAS,GAAG,eAAgB7E,CAAC,CAAC4I,aAAa,CAChS,KAAK,EACL;QACEa,GAAG,EAAEjB,CAAC;QACNf,SAAS,EAAElH,CAAC,CACVE,CAAC,CAACiJ,WAAW,CAAC;UACZlB,CAAC,EAAEL,CAAC;UACJwB,OAAO,EAAEnB,CAAC,KAAK,IAAI,CAAClE,KAAK,CAACC;QAC5B,CAAC,CACH,CAAC;QACDqF,IAAI,EAAE,cAAc;QACpBN,QAAQ,EAAE,CAAC;MACb,CAAC,EACD,eAAgBtJ,CAAC,CAAC4I,aAAa,CAC7B,MAAM,EACN;QACEnB,SAAS,EAAElH,CAAC,CAACE,CAAC,CAACqI,KAAK,CAAC;UAAEN,CAAC,EAAEL;QAAE,CAAC,CAAC,CAAC;QAC/B0B,WAAW,EAAGC,CAAC,IAAK;UAClBA,CAAC,CAAC7D,cAAc,CAAC,CAAC;QACpB;MACF,CAAC,EACD,IAAI,CAACe,IAAI,CAAC+C,aAAa,CAACd,CAAC,CAC3B,CAAC,EACD,eAAgBjJ,CAAC,CAAC4I,aAAa,CAC7BjH,CAAC,EACD;QACEiE,GAAG,EAAE,IAAI,CAACA,GAAG;QACbC,GAAG,EAAE,IAAI,CAACA,GAAG;QACboB,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3BsB,IAAI,EAAEU,CAAC;QACPe,IAAI,EAAEf,CAAC,CAACpE,IAAI,GAAG,IAAI,CAAC+B,KAAK,CAACqC,CAAC,CAACpE,IAAI,CAAC,GAAG,CAAC;QACrC0C,YAAY,EAAExD,CAAC;QACfiF,GAAG,EAAGc,CAAC,IAAK;UACVA,CAAC,IAAI,IAAI,CAACjG,SAAS,CAACoG,IAAI,CAACH,CAAC,CAAC;QAC7B,CAAC;QACDtE,EAAE,EAAEgD,CAAC;QACL0B,OAAO,EAAEA,CAAA,KAAM;UACb,IAAI,CAAC5D,eAAe,CAACkC,CAAC,CAAC;QACzB,CAAC;QACD2B,MAAM,EAAE,IAAI,CAAChE,cAAc;QAC3BO,QAAQ,EAAE,IAAI,CAACD,YAAY;QAC3BM,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBW,QAAQ,EAAE9C,CAAC;QACXiD,IAAI,EAAEC,CAAC;QACPH,UAAU,EAAEC,CAAC;QACbK,QAAQ,EAAEC;MACZ,CACF,CACF,CAAC,GAAG,eAAgBlI,CAAC,CAAC4I,aAAa,CAAC,KAAK,EAAE;QAAEa,GAAG,EAAEjB,CAAC;QAAEf,SAAS,EAAElH,CAAC,CAACE,CAAC,CAAC2J,SAAS,CAAC;UAAE5B,CAAC,EAAEL;QAAE,CAAC,CAAC;MAAE,CAAC,EAAEc,CAAC,CAACvE,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3G;IACA0B,QAAQA,CAAC3C,CAAC,EAAE;MACV8C,YAAY,CAAC,IAAI,CAACC,UAAU,CAAC,EAAE,IAAI,CAACA,UAAU,GAAG6D,MAAM,CAACC,UAAU,CAAC,MAAM7G,CAAC,CAAC,CAAC,CAAC;IAC/E;IACAe,eAAeA,CAAA,EAAG;MAChB,MAAMf,CAAC,GAAG9C,CAAC,CAAC4J,QAAQ,CAAC;MACrB,OAAO,IAAI,CAAC5G,UAAU,IAAIF,CAAC,KAAK,IAAI,CAACE,UAAU,CAACS,OAAO;IACzD;IACAsB,QAAQA,CAAA,EAAG;MACT,MAAMjC,CAAC,GAAG+G,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC7D,KAAK,CAAC;MACjC,OAAOnD,CAAC,CAACuB,MAAM,KAAKvB,CAAC,CAACwB,MAAM,CAAC,CAAClB,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAG,IAAI,CAAC6C,KAAK,CAAC5C,CAAC,CAAC,EAAE,CAAC,CAAC;IAC9D;EACF,CAAC;AACDV,CAAC,CAACoH,SAAS,GAAG;EACZC,YAAY,EAAE1K,CAAC,CAAC2K,IAAI;EACpBlD,QAAQ,EAAEzH,CAAC,CAAC2K,IAAI;EAChBtD,MAAM,EAAErH,CAAC,CAAC4K,SAAS,CAAC,CAClB5K,CAAC,CAAC6K,MAAM,EACR7K,CAAC,CAAC8K,KAAK,CAAC;IACNC,QAAQ,EAAE/K,CAAC,CAAC6K,MAAM;IAClBpG,OAAO,EAAEzE,CAAC,CAAC6K,MAAM;IACjBG,IAAI,EAAEhL,CAAC,CAACiL,KAAK,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IAClDC,IAAI,EAAElL,CAAC,CAACiL,KAAK,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IAClDE,QAAQ,EAAEnL,CAAC,CAACiL,KAAK,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IACtDG,GAAG,EAAEpL,CAAC,CAACiL,KAAK,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IACzCI,IAAI,EAAErL,CAAC,CAACiL,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IACrCK,KAAK,EAAEtL,CAAC,CAACiL,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IACjEM,GAAG,EAAEvL,CAAC,CAACiL,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IACpCO,OAAO,EAAExL,CAAC,CAACiL,KAAK,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IAC7ChI,IAAI,EAAEjD,CAAC,CAACiL,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IACrCQ,MAAM,EAAEzL,CAAC,CAAC2K,IAAI;IACdzH,MAAM,EAAElD,CAAC,CAACiL,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IACvC9H,MAAM,EAAEnD,CAAC,CAACiL,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IACvCS,YAAY,EAAE1L,CAAC,CAACiL,KAAK,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC;EACzC,CAAC,CAAC,CACH,CAAC;EACFrF,GAAG,EAAE5F,CAAC,CAAC2L,UAAU,CAACC,IAAI,CAAC;EACvBjG,GAAG,EAAE3F,CAAC,CAAC2L,UAAU,CAACC,IAAI,CAAC;EACvBlG,SAAS,EAAE1F,CAAC,CAAC2K,IAAI;EACjBhE,KAAK,EAAE3G,CAAC,CAAC8K,KAAK,CAAC;IACb7H,IAAI,EAAEjD,CAAC,CAAC6L,MAAM;IACd3I,MAAM,EAAElD,CAAC,CAAC6L,MAAM;IAChB1I,MAAM,EAAEnD,CAAC,CAAC6L;EACZ,CAAC,CAAC;EACFvE,YAAY,EAAEtH,CAAC,CAAC2K,IAAI;EACpBtB,QAAQ,EAAErJ,CAAC,CAAC6L,MAAM;EAClB/E,KAAK,EAAE9G,CAAC,CAAC2L,UAAU,CAACC,IAAI,CAAC;EACzBhE,IAAI,EAAE5H,CAAC,CAAC2K;AACV,CAAC,EAAEtH,CAAC,CAACuD,YAAY,GAAG;EAClBE,KAAK,EAAE,IAAI;EACXW,QAAQ,EAAE,CAAC,CAAC;EACZ/B,SAAS,EAAE,CAAC,CAAC;EACbgF,YAAY,EAAE,CAAC,CAAC;EAChBrD,MAAM,EAAE,SAAS;EACjB1B,GAAG,EAAE/D,CAAC;EACNgE,GAAG,EAAE9D,CAAC;EACN6E,KAAK,EAAE,CAAC,CAAC;EACTK,UAAU,EAAE,CAAC;AACf,CAAC;AACD,IAAI8E,CAAC,GAAGzI,CAAC;AACTrC,CAAC,CAAC8K,CAAC,CAAC;AACJ5K,CAAC,CAAC4K,CAAC,CAAC;AACJ,SACEA,CAAC,IAAIC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}