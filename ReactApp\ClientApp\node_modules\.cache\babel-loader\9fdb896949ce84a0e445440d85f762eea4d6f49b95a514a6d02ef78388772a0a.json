{"ast": null, "code": "import { Class } from '../common';\nvar LRUCache = function (Class) {\n  function LRUCache(size) {\n    Class.call(this);\n    this._size = size;\n    this._length = 0;\n    this._map = {};\n  }\n  if (Class) LRUCache.__proto__ = Class;\n  LRUCache.prototype = Object.create(Class && Class.prototype);\n  LRUCache.prototype.constructor = LRUCache;\n  LRUCache.prototype.put = function put(key, value) {\n    var map = this._map;\n    var entry = {\n      key: key,\n      value: value\n    };\n    map[key] = entry;\n    if (!this._head) {\n      this._head = this._tail = entry;\n    } else {\n      this._tail.newer = entry;\n      entry.older = this._tail;\n      this._tail = entry;\n    }\n    if (this._length >= this._size) {\n      map[this._head.key] = null;\n      this._head = this._head.newer;\n      this._head.older = null;\n    } else {\n      this._length++;\n    }\n  };\n  LRUCache.prototype.get = function get(key) {\n    var entry = this._map[key];\n    if (entry) {\n      if (entry === this._head && entry !== this._tail) {\n        this._head = entry.newer;\n        this._head.older = null;\n      }\n      if (entry !== this._tail) {\n        if (entry.older) {\n          entry.older.newer = entry.newer;\n          entry.newer.older = entry.older;\n        }\n        entry.older = this._tail;\n        entry.newer = null;\n        this._tail.newer = entry;\n        this._tail = entry;\n      }\n      return entry.value;\n    }\n  };\n  return LRUCache;\n}(Class);\nexport default LRUCache;", "map": {"version": 3, "names": ["Class", "L<PERSON><PERSON><PERSON>", "size", "call", "_size", "_length", "_map", "__proto__", "prototype", "Object", "create", "constructor", "put", "key", "value", "map", "entry", "_head", "_tail", "newer", "older", "get"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/text-metrics/lru-cache.js"], "sourcesContent": ["import { Class } from '../common';\n\nvar LRUCache = (function (Class) {\n    function LRUCache(size) {\n        Class.call(this);\n\n        this._size = size;\n        this._length = 0;\n        this._map = {};\n    }\n\n    if ( Class ) LRUCache.__proto__ = Class;\n    LRUCache.prototype = Object.create( Class && Class.prototype );\n    LRUCache.prototype.constructor = LRUCache;\n\n    LRUCache.prototype.put = function put (key, value) {\n        var map = this._map;\n        var entry = { key: key, value: value };\n\n        map[key] = entry;\n\n        if (!this._head) {\n            this._head = this._tail = entry;\n        } else {\n            this._tail.newer = entry;\n            entry.older = this._tail;\n            this._tail = entry;\n        }\n\n        if (this._length >= this._size) {\n            map[this._head.key] = null;\n            this._head = this._head.newer;\n            this._head.older = null;\n        } else {\n            this._length++;\n        }\n    };\n\n    LRUCache.prototype.get = function get (key) {\n        var entry = this._map[key];\n\n        if (entry) {\n            if (entry === this._head && entry !== this._tail) {\n                this._head = entry.newer;\n                this._head.older = null;\n            }\n\n            if (entry !== this._tail) {\n                if (entry.older) {\n                    entry.older.newer = entry.newer;\n                    entry.newer.older = entry.older;\n                }\n\n                entry.older = this._tail;\n                entry.newer = null;\n\n                this._tail.newer = entry;\n                this._tail = entry;\n            }\n\n            return entry.value;\n        }\n    };\n\n    return LRUCache;\n}(Class));\n\nexport default LRUCache;"], "mappings": "AAAA,SAASA,KAAK,QAAQ,WAAW;AAEjC,IAAIC,QAAQ,GAAI,UAAUD,KAAK,EAAE;EAC7B,SAASC,QAAQA,CAACC,IAAI,EAAE;IACpBF,KAAK,CAACG,IAAI,CAAC,IAAI,CAAC;IAEhB,IAAI,CAACC,KAAK,GAAGF,IAAI;IACjB,IAAI,CAACG,OAAO,GAAG,CAAC;IAChB,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC;EAClB;EAEA,IAAKN,KAAK,EAAGC,QAAQ,CAACM,SAAS,GAAGP,KAAK;EACvCC,QAAQ,CAACO,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAEV,KAAK,IAAIA,KAAK,CAACQ,SAAU,CAAC;EAC9DP,QAAQ,CAACO,SAAS,CAACG,WAAW,GAAGV,QAAQ;EAEzCA,QAAQ,CAACO,SAAS,CAACI,GAAG,GAAG,SAASA,GAAGA,CAAEC,GAAG,EAAEC,KAAK,EAAE;IAC/C,IAAIC,GAAG,GAAG,IAAI,CAACT,IAAI;IACnB,IAAIU,KAAK,GAAG;MAAEH,GAAG,EAAEA,GAAG;MAAEC,KAAK,EAAEA;IAAM,CAAC;IAEtCC,GAAG,CAACF,GAAG,CAAC,GAAGG,KAAK;IAEhB,IAAI,CAAC,IAAI,CAACC,KAAK,EAAE;MACb,IAAI,CAACA,KAAK,GAAG,IAAI,CAACC,KAAK,GAAGF,KAAK;IACnC,CAAC,MAAM;MACH,IAAI,CAACE,KAAK,CAACC,KAAK,GAAGH,KAAK;MACxBA,KAAK,CAACI,KAAK,GAAG,IAAI,CAACF,KAAK;MACxB,IAAI,CAACA,KAAK,GAAGF,KAAK;IACtB;IAEA,IAAI,IAAI,CAACX,OAAO,IAAI,IAAI,CAACD,KAAK,EAAE;MAC5BW,GAAG,CAAC,IAAI,CAACE,KAAK,CAACJ,GAAG,CAAC,GAAG,IAAI;MAC1B,IAAI,CAACI,KAAK,GAAG,IAAI,CAACA,KAAK,CAACE,KAAK;MAC7B,IAAI,CAACF,KAAK,CAACG,KAAK,GAAG,IAAI;IAC3B,CAAC,MAAM;MACH,IAAI,CAACf,OAAO,EAAE;IAClB;EACJ,CAAC;EAEDJ,QAAQ,CAACO,SAAS,CAACa,GAAG,GAAG,SAASA,GAAGA,CAAER,GAAG,EAAE;IACxC,IAAIG,KAAK,GAAG,IAAI,CAACV,IAAI,CAACO,GAAG,CAAC;IAE1B,IAAIG,KAAK,EAAE;MACP,IAAIA,KAAK,KAAK,IAAI,CAACC,KAAK,IAAID,KAAK,KAAK,IAAI,CAACE,KAAK,EAAE;QAC9C,IAAI,CAACD,KAAK,GAAGD,KAAK,CAACG,KAAK;QACxB,IAAI,CAACF,KAAK,CAACG,KAAK,GAAG,IAAI;MAC3B;MAEA,IAAIJ,KAAK,KAAK,IAAI,CAACE,KAAK,EAAE;QACtB,IAAIF,KAAK,CAACI,KAAK,EAAE;UACbJ,KAAK,CAACI,KAAK,CAACD,KAAK,GAAGH,KAAK,CAACG,KAAK;UAC/BH,KAAK,CAACG,KAAK,CAACC,KAAK,GAAGJ,KAAK,CAACI,KAAK;QACnC;QAEAJ,KAAK,CAACI,KAAK,GAAG,IAAI,CAACF,KAAK;QACxBF,KAAK,CAACG,KAAK,GAAG,IAAI;QAElB,IAAI,CAACD,KAAK,CAACC,KAAK,GAAGH,KAAK;QACxB,IAAI,CAACE,KAAK,GAAGF,KAAK;MACtB;MAEA,OAAOA,KAAK,CAACF,KAAK;IACtB;EACJ,CAAC;EAED,OAAOb,QAAQ;AACnB,CAAC,CAACD,KAAK,CAAE;AAET,eAAeC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}