{"ast": null, "code": "export const clamp = (value, min, max) => Math.min(max, Math.max(min, value));", "map": {"version": 3, "names": ["clamp", "value", "min", "max", "Math"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-pdfviewer-common/dist/es/common/math.js"], "sourcesContent": ["export const clamp = (value, min, max) => Math.min(max, Math.max(min, value));\n"], "mappings": "AAAA,OAAO,MAAMA,KAAK,GAAGA,CAACC,KAAK,EAAEC,GAAG,EAAEC,GAAG,KAAKC,IAAI,CAACF,GAAG,CAACC,GAAG,EAAEC,IAAI,CAACD,GAAG,CAACD,GAAG,EAAED,KAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}