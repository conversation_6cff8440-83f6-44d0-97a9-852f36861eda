{"ast": null, "code": "import * as React from 'react';\nimport KeyCode from \"rc-util/es/KeyCode\";\nvar onKeyDown = function onKeyDown(event) {\n  var keyCode = event.keyCode;\n  if (keyCode === KeyCode.ENTER) {\n    event.stopPropagation();\n  }\n};\nvar FilterDropdownMenuWrapper = function FilterDropdownMenuWrapper(props) {\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: props.className,\n    onClick: function onClick(e) {\n      return e.stopPropagation();\n    },\n    onKeyDown: onKeyDown\n  }, props.children);\n};\nexport default FilterDropdownMenuWrapper;", "map": {"version": 3, "names": ["React", "KeyCode", "onKeyDown", "event", "keyCode", "ENTER", "stopPropagation", "FilterDropdownMenuWrapper", "props", "createElement", "className", "onClick", "e", "children"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/antd/es/table/hooks/useFilter/FilterWrapper.js"], "sourcesContent": ["import * as React from 'react';\nimport KeyCode from \"rc-util/es/KeyCode\";\nvar onKeyDown = function onKeyDown(event) {\n  var keyCode = event.keyCode;\n  if (keyCode === KeyCode.ENTER) {\n    event.stopPropagation();\n  }\n};\nvar FilterDropdownMenuWrapper = function FilterDropdownMenuWrapper(props) {\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: props.className,\n    onClick: function onClick(e) {\n      return e.stopPropagation();\n    },\n    onKeyDown: onKeyDown\n  }, props.children);\n};\nexport default FilterDropdownMenuWrapper;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,oBAAoB;AACxC,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,KAAK,EAAE;EACxC,IAAIC,OAAO,GAAGD,KAAK,CAACC,OAAO;EAC3B,IAAIA,OAAO,KAAKH,OAAO,CAACI,KAAK,EAAE;IAC7BF,KAAK,CAACG,eAAe,CAAC,CAAC;EACzB;AACF,CAAC;AACD,IAAIC,yBAAyB,GAAG,SAASA,yBAAyBA,CAACC,KAAK,EAAE;EACxE,OAAO,aAAaR,KAAK,CAACS,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAEF,KAAK,CAACE,SAAS;IAC1BC,OAAO,EAAE,SAASA,OAAOA,CAACC,CAAC,EAAE;MAC3B,OAAOA,CAAC,CAACN,eAAe,CAAC,CAAC;IAC5B,CAAC;IACDJ,SAAS,EAAEA;EACb,CAAC,EAAEM,KAAK,CAACK,QAAQ,CAAC;AACpB,CAAC;AACD,eAAeN,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}