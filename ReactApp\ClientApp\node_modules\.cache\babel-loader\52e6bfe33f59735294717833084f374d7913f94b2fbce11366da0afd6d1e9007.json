{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as c from \"react\";\nimport e from \"prop-types\";\nimport { classNames as a } from \"@progress/kendo-react-common\";\nconst i = n => {\n  const o = {\n      layout: \"end\",\n      ...n\n    },\n    {\n      layout: t,\n      children: s\n    } = o,\n    r = a(\"k-actions\", \"k-window-actions\", \"k-actions-horizontal\", \"k-hstack\", {\n      \"k-justify-content-start\": t === \"start\",\n      \"k-justify-content-center\": t === \"center\",\n      \"k-justify-content-end\": t === \"end\",\n      \"k-justify-content-stretch\": t === \"stretched\"\n    });\n  return /* @__PURE__ */c.createElement(\"div\", {\n    className: r\n  }, s);\n};\ni.propTypes = {\n  children: e.any,\n  layout: e.oneOf([\"start\", \"center\", \"end\", \"stretched\"])\n};\nexport { i as WindowActionsBar };", "map": {"version": 3, "names": ["c", "e", "classNames", "a", "i", "n", "o", "layout", "t", "children", "s", "r", "createElement", "className", "propTypes", "any", "oneOf", "WindowActionsBar"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dialogs/WindowActionsBar.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as c from \"react\";\nimport e from \"prop-types\";\nimport { classNames as a } from \"@progress/kendo-react-common\";\nconst i = (n) => {\n  const o = {\n    layout: \"end\",\n    ...n\n  }, { layout: t, children: s } = o, r = a(\"k-actions\", \"k-window-actions\", \"k-actions-horizontal\", \"k-hstack\", {\n    \"k-justify-content-start\": t === \"start\",\n    \"k-justify-content-center\": t === \"center\",\n    \"k-justify-content-end\": t === \"end\",\n    \"k-justify-content-stretch\": t === \"stretched\"\n  });\n  return /* @__PURE__ */ c.createElement(\"div\", { className: r }, s);\n};\ni.propTypes = {\n  children: e.any,\n  layout: e.oneOf([\"start\", \"center\", \"end\", \"stretched\"])\n};\nexport {\n  i as WindowActionsBar\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AAC9D,MAAMC,CAAC,GAAIC,CAAC,IAAK;EACf,MAAMC,CAAC,GAAG;MACRC,MAAM,EAAE,KAAK;MACb,GAAGF;IACL,CAAC;IAAE;MAAEE,MAAM,EAAEC,CAAC;MAAEC,QAAQ,EAAEC;IAAE,CAAC,GAAGJ,CAAC;IAAEK,CAAC,GAAGR,CAAC,CAAC,WAAW,EAAE,kBAAkB,EAAE,sBAAsB,EAAE,UAAU,EAAE;MAC5G,yBAAyB,EAAEK,CAAC,KAAK,OAAO;MACxC,0BAA0B,EAAEA,CAAC,KAAK,QAAQ;MAC1C,uBAAuB,EAAEA,CAAC,KAAK,KAAK;MACpC,2BAA2B,EAAEA,CAAC,KAAK;IACrC,CAAC,CAAC;EACF,OAAO,eAAgBR,CAAC,CAACY,aAAa,CAAC,KAAK,EAAE;IAAEC,SAAS,EAAEF;EAAE,CAAC,EAAED,CAAC,CAAC;AACpE,CAAC;AACDN,CAAC,CAACU,SAAS,GAAG;EACZL,QAAQ,EAAER,CAAC,CAACc,GAAG;EACfR,MAAM,EAAEN,CAAC,CAACe,KAAK,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,CAAC;AACzD,CAAC;AACD,SACEZ,CAAC,IAAIa,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}