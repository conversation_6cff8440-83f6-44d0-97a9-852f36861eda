{"ast": null, "code": "/* Copyright 2012 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar _TextLayerBuilder_instances, _a, _TextLayerBuilder_onAppend, _TextLayerBuilder_textLayer, _TextLayerBuilder_textLayers, _TextLayerBuilder_selectionChangeAbortController, _TextLayerBuilder_bindMouse, _TextLayerBuilder_removeGlobalSelectionListener, _TextLayerBuilder_enableGlobalSelectionListener;\nimport { __awaiter, __classPrivateFieldGet, __classPrivateFieldSet } from \"tslib\";\nimport { TextLayer } from \"pdfjs-dist/legacy/build/pdf.mjs\";\nimport { addClass } from \"../common/dom\";\n/**\n * The text layer builder provides text selection functionality for the PDF.\n * It does this by creating overlay divs over the PDF's text. These divs\n * contain text that matches the PDF text they are overlaying.\n */\nclass TextLayerBuilder {\n  constructor({\n    pdfPage,\n    // highlighter = null,\n    accessibilityManager,\n    // enablePermissions = false,\n    // todo: fix styles\n    styles,\n    onAppend = null\n  }) {\n    _TextLayerBuilder_instances.add(this);\n    // todo: props\n    this.pdfPage = null;\n    // highlighter = null;\n    this.div = null;\n    this.accessibilityManager = null;\n    // todo: props\n    // #enablePermissions = false;\n    _TextLayerBuilder_onAppend.set(this, null);\n    // #renderingDone = false;\n    _TextLayerBuilder_textLayer.set(this, null);\n    this.pdfPage = pdfPage;\n    // this.highlighter = highlighter;\n    this.accessibilityManager = accessibilityManager;\n    // this.#enablePermissions = enablePermissions === true;\n    __classPrivateFieldSet(this, _TextLayerBuilder_onAppend, onAppend, \"f\");\n    this.div = document.createElement(\"div\");\n    this.div.tabIndex = 0;\n    // this.div.className = \"textLayer\";\n    // todo: fix classes\n    this.div.classList.add(\"k-text-layer\");\n    // todo: fix styles\n    Object.keys(styles).forEach(key => this.div.style[key] = styles[key]);\n  }\n  /**\n   * Renders the text layer.\n   * @param {PageViewport} viewport\n   * @param {Object} [textContentParams]\n   */\n  render(viewport_1) {\n    return __awaiter(this, arguments, void 0, function* (viewport, textContentParams = null) {\n      var _b, _c, _d;\n      // if (this.#renderingDone && this.#textLayer) {\n      if (__classPrivateFieldGet(this, _TextLayerBuilder_textLayer, \"f\")) {\n        __classPrivateFieldGet(this, _TextLayerBuilder_textLayer, \"f\").update({\n          viewport,\n          onBefore: this.hide.bind(this)\n        });\n        this.show();\n        return;\n      }\n      this.cancel();\n      __classPrivateFieldSet(this, _TextLayerBuilder_textLayer, new TextLayer({\n        textContentSource: this.pdfPage.streamTextContent(textContentParams || {\n          // includeMarkedContent: true,\n          // setting this to false requires removing \"await\"\n          // in page.ts when calling this.#renderTextLayer()\n          includeMarkedContent: false,\n          disableNormalization: true\n        }),\n        container: this.div,\n        viewport\n      }), \"f\");\n      // const { textDivs, textContentItemsStr } = this.#textLayer;\n      const {\n        textDivs\n      } = __classPrivateFieldGet(this, _TextLayerBuilder_textLayer, \"f\");\n      // this.highlighter?.setTextMapping(textDivs, textContentItemsStr);\n      (_b = this.accessibilityManager) === null || _b === void 0 ? void 0 : _b.setTextMapping(textDivs);\n      yield __classPrivateFieldGet(this, _TextLayerBuilder_textLayer, \"f\").render();\n      // this.#renderingDone = true;\n      // todo: manually add \"k-marked-content\" class\n      // as pdf.js text layer cannot render it\n      const markedContentElements = Array.from(this.div.querySelectorAll(\".markedContent\") || []) || [];\n      markedContentElements.forEach(x => addClass(\"k-marked-content\", x));\n      if (markedContentElements && markedContentElements.length > 0) {\n        const endOfContent = document.createElement(\"div\");\n        endOfContent.className = \"endOfContent\";\n        this.div.append(endOfContent);\n        __classPrivateFieldGet(this, _TextLayerBuilder_instances, \"m\", _TextLayerBuilder_bindMouse).call(this, endOfContent);\n      }\n      // Ensure that the textLayer is appended to the DOM *before* handling\n      // e.g. a pending search operation.\n      (_c = __classPrivateFieldGet(this, _TextLayerBuilder_onAppend, \"f\")) === null || _c === void 0 ? void 0 : _c.call(this, this.div);\n      // this.highlighter?.enable();\n      (_d = this.accessibilityManager) === null || _d === void 0 ? void 0 : _d.enable();\n    });\n  }\n  hide() {\n    // if (!this.div.hidden && this.#renderingDone) {\n    if (!this.div.hidden) {\n      // We turn off the highlighter in order to avoid to scroll into view an\n      // element of the text layer which could be hidden.\n      // this.highlighter?.disable();\n      this.div.hidden = true;\n    }\n  }\n  show() {\n    // if (this.div.hidden && this.#renderingDone) {\n    if (this.div.hidden) {\n      this.div.hidden = false;\n      // this.highlighter?.enable();\n    }\n  }\n  /**\n   * Cancel rendering of the text layer.\n   */\n  cancel() {\n    var _b, _c;\n    (_b = __classPrivateFieldGet(this, _TextLayerBuilder_textLayer, \"f\")) === null || _b === void 0 ? void 0 : _b.cancel();\n    __classPrivateFieldSet(this, _TextLayerBuilder_textLayer, null, \"f\");\n    // this.highlighter?.disable();\n    (_c = this.accessibilityManager) === null || _c === void 0 ? void 0 : _c.disable();\n    __classPrivateFieldGet(_a, _a, \"m\", _TextLayerBuilder_removeGlobalSelectionListener).call(_a, this.div);\n  }\n}\n_a = TextLayerBuilder, _TextLayerBuilder_onAppend = new WeakMap(), _TextLayerBuilder_textLayer = new WeakMap(), _TextLayerBuilder_instances = new WeakSet(), _TextLayerBuilder_bindMouse = function _TextLayerBuilder_bindMouse(end) {\n  const {\n    div\n  } = this;\n  // div.addEventListener(\"mousedown\", () => {\n  //     div.classList.add(\"selecting\");\n  // });\n  // div.addEventListener(\"copy\", event => {\n  //     if (!this.#enablePermissions) {\n  //         const selection = document.getSelection();\n  //         event.clipboardData.setData(\n  //             \"text/plain\",\n  //             removeNullCharacters(normalizeUnicode(selection.toString()))\n  //         );\n  //     }\n  //     event.preventDefault();\n  //     event.stopPropagation();\n  // });\n  __classPrivateFieldGet(_a, _a, \"f\", _TextLayerBuilder_textLayers).set(div, end);\n  __classPrivateFieldGet(_a, _a, \"m\", _TextLayerBuilder_enableGlobalSelectionListener).call(_a);\n}, _TextLayerBuilder_removeGlobalSelectionListener = function _TextLayerBuilder_removeGlobalSelectionListener(textLayerDiv) {\n  var _b;\n  __classPrivateFieldGet(this, _a, \"f\", _TextLayerBuilder_textLayers).delete(textLayerDiv);\n  if (__classPrivateFieldGet(this, _a, \"f\", _TextLayerBuilder_textLayers).size === 0) {\n    (_b = __classPrivateFieldGet(this, _a, \"f\", _TextLayerBuilder_selectionChangeAbortController)) === null || _b === void 0 ? void 0 : _b.abort();\n    __classPrivateFieldSet(this, _a, null, \"f\", _TextLayerBuilder_selectionChangeAbortController);\n  }\n}, _TextLayerBuilder_enableGlobalSelectionListener = function _TextLayerBuilder_enableGlobalSelectionListener() {\n  // if (this.#selectionChangeAbortController) {\n  //     // document-level event listeners already installed\n  //     return;\n  // }\n  // this.#selectionChangeAbortController = new AbortController();\n  // const { signal } = this.#selectionChangeAbortController;\n  // const reset = (end, textLayer) => {\n  //     if (typeof PDFJSDev === \"undefined\" || !PDFJSDev.test(\"MOZCENTRAL\")) {\n  //         textLayer.append(end);\n  //         end.style.width = \"\";\n  //         end.style.height = \"\";\n  //     }\n  //     textLayer.classList.remove(\"selecting\");\n  // };\n  // let isPointerDown = false;\n  // document.addEventListener(\n  //     \"pointerdown\",\n  //     () => {\n  //         isPointerDown = true;\n  //     },\n  //     { signal }\n  // );\n  // document.addEventListener(\n  //     \"pointerup\",\n  //     () => {\n  //         isPointerDown = false;\n  //         this.#textLayers.forEach(reset);\n  //     },\n  //     { signal }\n  // );\n  // window.addEventListener(\n  //     \"blur\",\n  //     () => {\n  //         isPointerDown = false;\n  //         this.#textLayers.forEach(reset);\n  //     },\n  //     { signal }\n  // );\n  // document.addEventListener(\n  //     \"keyup\",\n  //     () => {\n  //         if (!isPointerDown) {\n  //             this.#textLayers.forEach(reset);\n  //         }\n  //     },\n  //     { signal }\n  // );\n  // if (typeof PDFJSDev === \"undefined\" || !PDFJSDev.test(\"MOZCENTRAL\")) {\n  //     // eslint-disable-next-line no-var\n  //     var isFirefox, prevRange;\n  // }\n  // document.addEventListener(\n  //     \"selectionchange\",\n  //     () => {\n  //         const selection = document.getSelection();\n  //         if (selection.rangeCount === 0) {\n  //             this.#textLayers.forEach(reset);\n  //             return;\n  //         }\n  //         // Even though the spec says that .rangeCount should be 0 or 1, Firefox\n  //         // creates multiple ranges when selecting across multiple pages.\n  //         // Make sure to collect all the .textLayer elements where the selection\n  //         // is happening.\n  //         const activeTextLayers = new Set();\n  //         for (let i = 0; i < selection.rangeCount; i++) {\n  //             const range = selection.getRangeAt(i);\n  //             for (const textLayerDiv of this.#textLayers.keys()) {\n  //                 if (\n  //                     !activeTextLayers.has(textLayerDiv) &&\n  //                     range.intersectsNode(textLayerDiv)\n  //                 ) {\n  //                     activeTextLayers.add(textLayerDiv);\n  //                 }\n  //             }\n  //         }\n  //         for (const [textLayerDiv, endDiv] of this.#textLayers) {\n  //             if (activeTextLayers.has(textLayerDiv)) {\n  //                 textLayerDiv.classList.add(\"selecting\");\n  //             } else {\n  //                 reset(endDiv, textLayerDiv);\n  //             }\n  //         }\n  //         if (typeof PDFJSDev !== \"undefined\" && PDFJSDev.test(\"MOZCENTRAL\")) {\n  //             return;\n  //         }\n  //         if (typeof PDFJSDev === \"undefined\" || !PDFJSDev.test(\"CHROME\")) {\n  //             isFirefox ??=\n  //                 getComputedStyle(\n  //                     this.#textLayers.values().next().value\n  //                 ).getPropertyValue(\"-moz-user-select\") === \"none\";\n  //             if (isFirefox) {\n  //                 return;\n  //             }\n  //         }\n  //         // In non-Firefox browsers, when hovering over an empty space (thus,\n  //         // on .endOfContent), the selection will expand to cover all the\n  //         // text between the current selection and .endOfContent. By moving\n  //         // .endOfContent to right after (or before, depending on which side\n  //         // of the selection the user is moving), we limit the selection jump\n  //         // to at most cover the enteirety of the <span> where the selection\n  //         // is being modified.\n  //         const range = selection.getRangeAt(0);\n  //         const modifyStart =\n  //             prevRange &&\n  //             (range.compareBoundaryPoints(Range.END_TO_END, prevRange) === 0 ||\n  //                 range.compareBoundaryPoints(Range.START_TO_END, prevRange) === 0);\n  //         let anchor = modifyStart ? range.startContainer : range.endContainer;\n  //         if (anchor.nodeType === Node.TEXT_NODE) {\n  //             anchor = anchor.parentNode;\n  //         }\n  //         const parentTextLayer = anchor.parentElement.closest(\".textLayer\");\n  //         const endDiv = this.#textLayers.get(parentTextLayer);\n  //         if (endDiv) {\n  //             endDiv.style.width = parentTextLayer.style.width;\n  //             endDiv.style.height = parentTextLayer.style.height;\n  //             anchor.parentElement.insertBefore(\n  //                 endDiv,\n  //                 modifyStart ? anchor : anchor.nextSibling\n  //             );\n  //         }\n  //         prevRange = range.cloneRange();\n  //     },\n  //     { signal }\n  // );\n};\n_TextLayerBuilder_textLayers = {\n  value: new Map()\n};\n_TextLayerBuilder_selectionChangeAbortController = {\n  value: null\n};\nexport { TextLayerBuilder };", "map": {"version": 3, "names": ["_TextLayerBuilder_instances", "_a", "_TextLayerBuilder_onAppend", "_TextLayerBuilder_textLayer", "_TextLayerBuilder_textLayers", "_TextLayerBuilder_selectionChangeAbortController", "_TextLayerBuilder_bindMouse", "_TextLayerBuilder_removeGlobalSelectionListener", "_TextLayerBuilder_enableGlobalSelectionListener", "__awaiter", "__classPrivateFieldGet", "__classPrivateFieldSet", "TextLayer", "addClass", "TextLayerBuilder", "constructor", "pdfPage", "accessibilityManager", "styles", "onAppend", "add", "div", "set", "document", "createElement", "tabIndex", "classList", "Object", "keys", "for<PERSON>ach", "key", "style", "render", "viewport_1", "arguments", "viewport", "textContentParams", "_b", "_c", "_d", "update", "onBefore", "hide", "bind", "show", "cancel", "textContentSource", "streamTextContent", "includeMarkedContent", "disableNormalization", "container", "textDivs", "setTextMapping", "markedContentElements", "Array", "from", "querySelectorAll", "x", "length", "endOfContent", "className", "append", "call", "enable", "hidden", "disable", "WeakMap", "WeakSet", "end", "textLayerDiv", "delete", "size", "abort", "value", "Map"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-pdfviewer-common/dist/es/text/text-layer-builder.js"], "sourcesContent": ["/* Copyright 2012 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar _TextLayerBuilder_instances, _a, _TextLayerBuilder_onAppend, _TextLayerBuilder_textLayer, _TextLayerBuilder_textLayers, _TextLayerBuilder_selectionChangeAbortController, _TextLayerBuilder_bindMouse, _TextLayerBuilder_removeGlobalSelectionListener, _TextLayerBuilder_enableGlobalSelectionListener;\nimport { __awaiter, __classPrivateFieldGet, __classPrivateFieldSet } from \"tslib\";\nimport { TextLayer } from \"pdfjs-dist/legacy/build/pdf.mjs\";\nimport { addClass } from \"../common/dom\";\n/**\n * The text layer builder provides text selection functionality for the PDF.\n * It does this by creating overlay divs over the PDF's text. These divs\n * contain text that matches the PDF text they are overlaying.\n */\nclass TextLayerBuilder {\n    constructor({ pdfPage, \n    // highlighter = null,\n    accessibilityManager, \n    // enablePermissions = false,\n    // todo: fix styles\n    styles, onAppend = null }) {\n        _TextLayerBuilder_instances.add(this);\n        // todo: props\n        this.pdfPage = null;\n        // highlighter = null;\n        this.div = null;\n        this.accessibilityManager = null;\n        // todo: props\n        // #enablePermissions = false;\n        _TextLayerBuilder_onAppend.set(this, null);\n        // #renderingDone = false;\n        _TextLayerBuilder_textLayer.set(this, null);\n        this.pdfPage = pdfPage;\n        // this.highlighter = highlighter;\n        this.accessibilityManager = accessibilityManager;\n        // this.#enablePermissions = enablePermissions === true;\n        __classPrivateFieldSet(this, _TextLayerBuilder_onAppend, onAppend, \"f\");\n        this.div = document.createElement(\"div\");\n        this.div.tabIndex = 0;\n        // this.div.className = \"textLayer\";\n        // todo: fix classes\n        this.div.classList.add(\"k-text-layer\");\n        // todo: fix styles\n        Object.keys(styles).forEach((key) => (this.div.style[key] = styles[key]));\n    }\n    /**\n     * Renders the text layer.\n     * @param {PageViewport} viewport\n     * @param {Object} [textContentParams]\n     */\n    render(viewport_1) {\n        return __awaiter(this, arguments, void 0, function* (viewport, textContentParams = null) {\n            var _b, _c, _d;\n            // if (this.#renderingDone && this.#textLayer) {\n            if (__classPrivateFieldGet(this, _TextLayerBuilder_textLayer, \"f\")) {\n                __classPrivateFieldGet(this, _TextLayerBuilder_textLayer, \"f\").update({\n                    viewport,\n                    onBefore: this.hide.bind(this)\n                });\n                this.show();\n                return;\n            }\n            this.cancel();\n            __classPrivateFieldSet(this, _TextLayerBuilder_textLayer, new TextLayer({\n                textContentSource: this.pdfPage.streamTextContent(textContentParams || {\n                    // includeMarkedContent: true,\n                    // setting this to false requires removing \"await\"\n                    // in page.ts when calling this.#renderTextLayer()\n                    includeMarkedContent: false,\n                    disableNormalization: true\n                }),\n                container: this.div,\n                viewport\n            }), \"f\");\n            // const { textDivs, textContentItemsStr } = this.#textLayer;\n            const { textDivs } = __classPrivateFieldGet(this, _TextLayerBuilder_textLayer, \"f\");\n            // this.highlighter?.setTextMapping(textDivs, textContentItemsStr);\n            (_b = this.accessibilityManager) === null || _b === void 0 ? void 0 : _b.setTextMapping(textDivs);\n            yield __classPrivateFieldGet(this, _TextLayerBuilder_textLayer, \"f\").render();\n            // this.#renderingDone = true;\n            // todo: manually add \"k-marked-content\" class\n            // as pdf.js text layer cannot render it\n            const markedContentElements = Array.from(this.div.querySelectorAll(\".markedContent\") || []) || [];\n            markedContentElements.forEach(x => addClass(\"k-marked-content\", x));\n            if (markedContentElements && markedContentElements.length > 0) {\n                const endOfContent = document.createElement(\"div\");\n                endOfContent.className = \"endOfContent\";\n                this.div.append(endOfContent);\n                __classPrivateFieldGet(this, _TextLayerBuilder_instances, \"m\", _TextLayerBuilder_bindMouse).call(this, endOfContent);\n            }\n            // Ensure that the textLayer is appended to the DOM *before* handling\n            // e.g. a pending search operation.\n            (_c = __classPrivateFieldGet(this, _TextLayerBuilder_onAppend, \"f\")) === null || _c === void 0 ? void 0 : _c.call(this, this.div);\n            // this.highlighter?.enable();\n            (_d = this.accessibilityManager) === null || _d === void 0 ? void 0 : _d.enable();\n        });\n    }\n    hide() {\n        // if (!this.div.hidden && this.#renderingDone) {\n        if (!this.div.hidden) {\n            // We turn off the highlighter in order to avoid to scroll into view an\n            // element of the text layer which could be hidden.\n            // this.highlighter?.disable();\n            this.div.hidden = true;\n        }\n    }\n    show() {\n        // if (this.div.hidden && this.#renderingDone) {\n        if (this.div.hidden) {\n            this.div.hidden = false;\n            // this.highlighter?.enable();\n        }\n    }\n    /**\n     * Cancel rendering of the text layer.\n     */\n    cancel() {\n        var _b, _c;\n        (_b = __classPrivateFieldGet(this, _TextLayerBuilder_textLayer, \"f\")) === null || _b === void 0 ? void 0 : _b.cancel();\n        __classPrivateFieldSet(this, _TextLayerBuilder_textLayer, null, \"f\");\n        // this.highlighter?.disable();\n        (_c = this.accessibilityManager) === null || _c === void 0 ? void 0 : _c.disable();\n        __classPrivateFieldGet(_a, _a, \"m\", _TextLayerBuilder_removeGlobalSelectionListener).call(_a, this.div);\n    }\n}\n_a = TextLayerBuilder, _TextLayerBuilder_onAppend = new WeakMap(), _TextLayerBuilder_textLayer = new WeakMap(), _TextLayerBuilder_instances = new WeakSet(), _TextLayerBuilder_bindMouse = function _TextLayerBuilder_bindMouse(end) {\n    const { div } = this;\n    // div.addEventListener(\"mousedown\", () => {\n    //     div.classList.add(\"selecting\");\n    // });\n    // div.addEventListener(\"copy\", event => {\n    //     if (!this.#enablePermissions) {\n    //         const selection = document.getSelection();\n    //         event.clipboardData.setData(\n    //             \"text/plain\",\n    //             removeNullCharacters(normalizeUnicode(selection.toString()))\n    //         );\n    //     }\n    //     event.preventDefault();\n    //     event.stopPropagation();\n    // });\n    __classPrivateFieldGet(_a, _a, \"f\", _TextLayerBuilder_textLayers).set(div, end);\n    __classPrivateFieldGet(_a, _a, \"m\", _TextLayerBuilder_enableGlobalSelectionListener).call(_a);\n}, _TextLayerBuilder_removeGlobalSelectionListener = function _TextLayerBuilder_removeGlobalSelectionListener(textLayerDiv) {\n    var _b;\n    __classPrivateFieldGet(this, _a, \"f\", _TextLayerBuilder_textLayers).delete(textLayerDiv);\n    if (__classPrivateFieldGet(this, _a, \"f\", _TextLayerBuilder_textLayers).size === 0) {\n        (_b = __classPrivateFieldGet(this, _a, \"f\", _TextLayerBuilder_selectionChangeAbortController)) === null || _b === void 0 ? void 0 : _b.abort();\n        __classPrivateFieldSet(this, _a, null, \"f\", _TextLayerBuilder_selectionChangeAbortController);\n    }\n}, _TextLayerBuilder_enableGlobalSelectionListener = function _TextLayerBuilder_enableGlobalSelectionListener() {\n    // if (this.#selectionChangeAbortController) {\n    //     // document-level event listeners already installed\n    //     return;\n    // }\n    // this.#selectionChangeAbortController = new AbortController();\n    // const { signal } = this.#selectionChangeAbortController;\n    // const reset = (end, textLayer) => {\n    //     if (typeof PDFJSDev === \"undefined\" || !PDFJSDev.test(\"MOZCENTRAL\")) {\n    //         textLayer.append(end);\n    //         end.style.width = \"\";\n    //         end.style.height = \"\";\n    //     }\n    //     textLayer.classList.remove(\"selecting\");\n    // };\n    // let isPointerDown = false;\n    // document.addEventListener(\n    //     \"pointerdown\",\n    //     () => {\n    //         isPointerDown = true;\n    //     },\n    //     { signal }\n    // );\n    // document.addEventListener(\n    //     \"pointerup\",\n    //     () => {\n    //         isPointerDown = false;\n    //         this.#textLayers.forEach(reset);\n    //     },\n    //     { signal }\n    // );\n    // window.addEventListener(\n    //     \"blur\",\n    //     () => {\n    //         isPointerDown = false;\n    //         this.#textLayers.forEach(reset);\n    //     },\n    //     { signal }\n    // );\n    // document.addEventListener(\n    //     \"keyup\",\n    //     () => {\n    //         if (!isPointerDown) {\n    //             this.#textLayers.forEach(reset);\n    //         }\n    //     },\n    //     { signal }\n    // );\n    // if (typeof PDFJSDev === \"undefined\" || !PDFJSDev.test(\"MOZCENTRAL\")) {\n    //     // eslint-disable-next-line no-var\n    //     var isFirefox, prevRange;\n    // }\n    // document.addEventListener(\n    //     \"selectionchange\",\n    //     () => {\n    //         const selection = document.getSelection();\n    //         if (selection.rangeCount === 0) {\n    //             this.#textLayers.forEach(reset);\n    //             return;\n    //         }\n    //         // Even though the spec says that .rangeCount should be 0 or 1, Firefox\n    //         // creates multiple ranges when selecting across multiple pages.\n    //         // Make sure to collect all the .textLayer elements where the selection\n    //         // is happening.\n    //         const activeTextLayers = new Set();\n    //         for (let i = 0; i < selection.rangeCount; i++) {\n    //             const range = selection.getRangeAt(i);\n    //             for (const textLayerDiv of this.#textLayers.keys()) {\n    //                 if (\n    //                     !activeTextLayers.has(textLayerDiv) &&\n    //                     range.intersectsNode(textLayerDiv)\n    //                 ) {\n    //                     activeTextLayers.add(textLayerDiv);\n    //                 }\n    //             }\n    //         }\n    //         for (const [textLayerDiv, endDiv] of this.#textLayers) {\n    //             if (activeTextLayers.has(textLayerDiv)) {\n    //                 textLayerDiv.classList.add(\"selecting\");\n    //             } else {\n    //                 reset(endDiv, textLayerDiv);\n    //             }\n    //         }\n    //         if (typeof PDFJSDev !== \"undefined\" && PDFJSDev.test(\"MOZCENTRAL\")) {\n    //             return;\n    //         }\n    //         if (typeof PDFJSDev === \"undefined\" || !PDFJSDev.test(\"CHROME\")) {\n    //             isFirefox ??=\n    //                 getComputedStyle(\n    //                     this.#textLayers.values().next().value\n    //                 ).getPropertyValue(\"-moz-user-select\") === \"none\";\n    //             if (isFirefox) {\n    //                 return;\n    //             }\n    //         }\n    //         // In non-Firefox browsers, when hovering over an empty space (thus,\n    //         // on .endOfContent), the selection will expand to cover all the\n    //         // text between the current selection and .endOfContent. By moving\n    //         // .endOfContent to right after (or before, depending on which side\n    //         // of the selection the user is moving), we limit the selection jump\n    //         // to at most cover the enteirety of the <span> where the selection\n    //         // is being modified.\n    //         const range = selection.getRangeAt(0);\n    //         const modifyStart =\n    //             prevRange &&\n    //             (range.compareBoundaryPoints(Range.END_TO_END, prevRange) === 0 ||\n    //                 range.compareBoundaryPoints(Range.START_TO_END, prevRange) === 0);\n    //         let anchor = modifyStart ? range.startContainer : range.endContainer;\n    //         if (anchor.nodeType === Node.TEXT_NODE) {\n    //             anchor = anchor.parentNode;\n    //         }\n    //         const parentTextLayer = anchor.parentElement.closest(\".textLayer\");\n    //         const endDiv = this.#textLayers.get(parentTextLayer);\n    //         if (endDiv) {\n    //             endDiv.style.width = parentTextLayer.style.width;\n    //             endDiv.style.height = parentTextLayer.style.height;\n    //             anchor.parentElement.insertBefore(\n    //                 endDiv,\n    //                 modifyStart ? anchor : anchor.nextSibling\n    //             );\n    //         }\n    //         prevRange = range.cloneRange();\n    //     },\n    //     { signal }\n    // );\n};\n_TextLayerBuilder_textLayers = { value: new Map() };\n_TextLayerBuilder_selectionChangeAbortController = { value: null };\nexport { TextLayerBuilder };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,2BAA2B,EAAEC,EAAE,EAAEC,0BAA0B,EAAEC,2BAA2B,EAAEC,4BAA4B,EAAEC,gDAAgD,EAAEC,2BAA2B,EAAEC,+CAA+C,EAAEC,+CAA+C;AAC3S,SAASC,SAAS,EAAEC,sBAAsB,EAAEC,sBAAsB,QAAQ,OAAO;AACjF,SAASC,SAAS,QAAQ,iCAAiC;AAC3D,SAASC,QAAQ,QAAQ,eAAe;AACxC;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACnBC,WAAWA,CAAC;IAAEC,OAAO;IACrB;IACAC,oBAAoB;IACpB;IACA;IACAC,MAAM;IAAEC,QAAQ,GAAG;EAAK,CAAC,EAAE;IACvBnB,2BAA2B,CAACoB,GAAG,CAAC,IAAI,CAAC;IACrC;IACA,IAAI,CAACJ,OAAO,GAAG,IAAI;IACnB;IACA,IAAI,CAACK,GAAG,GAAG,IAAI;IACf,IAAI,CAACJ,oBAAoB,GAAG,IAAI;IAChC;IACA;IACAf,0BAA0B,CAACoB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAC1C;IACAnB,2BAA2B,CAACmB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAC3C,IAAI,CAACN,OAAO,GAAGA,OAAO;IACtB;IACA,IAAI,CAACC,oBAAoB,GAAGA,oBAAoB;IAChD;IACAN,sBAAsB,CAAC,IAAI,EAAET,0BAA0B,EAAEiB,QAAQ,EAAE,GAAG,CAAC;IACvE,IAAI,CAACE,GAAG,GAAGE,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACxC,IAAI,CAACH,GAAG,CAACI,QAAQ,GAAG,CAAC;IACrB;IACA;IACA,IAAI,CAACJ,GAAG,CAACK,SAAS,CAACN,GAAG,CAAC,cAAc,CAAC;IACtC;IACAO,MAAM,CAACC,IAAI,CAACV,MAAM,CAAC,CAACW,OAAO,CAAEC,GAAG,IAAM,IAAI,CAACT,GAAG,CAACU,KAAK,CAACD,GAAG,CAAC,GAAGZ,MAAM,CAACY,GAAG,CAAE,CAAC;EAC7E;EACA;AACJ;AACA;AACA;AACA;EACIE,MAAMA,CAACC,UAAU,EAAE;IACf,OAAOxB,SAAS,CAAC,IAAI,EAAEyB,SAAS,EAAE,KAAK,CAAC,EAAE,WAAWC,QAAQ,EAAEC,iBAAiB,GAAG,IAAI,EAAE;MACrF,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE;MACd;MACA,IAAI7B,sBAAsB,CAAC,IAAI,EAAEP,2BAA2B,EAAE,GAAG,CAAC,EAAE;QAChEO,sBAAsB,CAAC,IAAI,EAAEP,2BAA2B,EAAE,GAAG,CAAC,CAACqC,MAAM,CAAC;UAClEL,QAAQ;UACRM,QAAQ,EAAE,IAAI,CAACC,IAAI,CAACC,IAAI,CAAC,IAAI;QACjC,CAAC,CAAC;QACF,IAAI,CAACC,IAAI,CAAC,CAAC;QACX;MACJ;MACA,IAAI,CAACC,MAAM,CAAC,CAAC;MACblC,sBAAsB,CAAC,IAAI,EAAER,2BAA2B,EAAE,IAAIS,SAAS,CAAC;QACpEkC,iBAAiB,EAAE,IAAI,CAAC9B,OAAO,CAAC+B,iBAAiB,CAACX,iBAAiB,IAAI;UACnE;UACA;UACA;UACAY,oBAAoB,EAAE,KAAK;UAC3BC,oBAAoB,EAAE;QAC1B,CAAC,CAAC;QACFC,SAAS,EAAE,IAAI,CAAC7B,GAAG;QACnBc;MACJ,CAAC,CAAC,EAAE,GAAG,CAAC;MACR;MACA,MAAM;QAAEgB;MAAS,CAAC,GAAGzC,sBAAsB,CAAC,IAAI,EAAEP,2BAA2B,EAAE,GAAG,CAAC;MACnF;MACA,CAACkC,EAAE,GAAG,IAAI,CAACpB,oBAAoB,MAAM,IAAI,IAAIoB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACe,cAAc,CAACD,QAAQ,CAAC;MACjG,MAAMzC,sBAAsB,CAAC,IAAI,EAAEP,2BAA2B,EAAE,GAAG,CAAC,CAAC6B,MAAM,CAAC,CAAC;MAC7E;MACA;MACA;MACA,MAAMqB,qBAAqB,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAClC,GAAG,CAACmC,gBAAgB,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE;MACjGH,qBAAqB,CAACxB,OAAO,CAAC4B,CAAC,IAAI5C,QAAQ,CAAC,kBAAkB,EAAE4C,CAAC,CAAC,CAAC;MACnE,IAAIJ,qBAAqB,IAAIA,qBAAqB,CAACK,MAAM,GAAG,CAAC,EAAE;QAC3D,MAAMC,YAAY,GAAGpC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAClDmC,YAAY,CAACC,SAAS,GAAG,cAAc;QACvC,IAAI,CAACvC,GAAG,CAACwC,MAAM,CAACF,YAAY,CAAC;QAC7BjD,sBAAsB,CAAC,IAAI,EAAEV,2BAA2B,EAAE,GAAG,EAAEM,2BAA2B,CAAC,CAACwD,IAAI,CAAC,IAAI,EAAEH,YAAY,CAAC;MACxH;MACA;MACA;MACA,CAACrB,EAAE,GAAG5B,sBAAsB,CAAC,IAAI,EAAER,0BAA0B,EAAE,GAAG,CAAC,MAAM,IAAI,IAAIoC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwB,IAAI,CAAC,IAAI,EAAE,IAAI,CAACzC,GAAG,CAAC;MACjI;MACA,CAACkB,EAAE,GAAG,IAAI,CAACtB,oBAAoB,MAAM,IAAI,IAAIsB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwB,MAAM,CAAC,CAAC;IACrF,CAAC,CAAC;EACN;EACArB,IAAIA,CAAA,EAAG;IACH;IACA,IAAI,CAAC,IAAI,CAACrB,GAAG,CAAC2C,MAAM,EAAE;MAClB;MACA;MACA;MACA,IAAI,CAAC3C,GAAG,CAAC2C,MAAM,GAAG,IAAI;IAC1B;EACJ;EACApB,IAAIA,CAAA,EAAG;IACH;IACA,IAAI,IAAI,CAACvB,GAAG,CAAC2C,MAAM,EAAE;MACjB,IAAI,CAAC3C,GAAG,CAAC2C,MAAM,GAAG,KAAK;MACvB;IACJ;EACJ;EACA;AACJ;AACA;EACInB,MAAMA,CAAA,EAAG;IACL,IAAIR,EAAE,EAAEC,EAAE;IACV,CAACD,EAAE,GAAG3B,sBAAsB,CAAC,IAAI,EAAEP,2BAA2B,EAAE,GAAG,CAAC,MAAM,IAAI,IAAIkC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACQ,MAAM,CAAC,CAAC;IACtHlC,sBAAsB,CAAC,IAAI,EAAER,2BAA2B,EAAE,IAAI,EAAE,GAAG,CAAC;IACpE;IACA,CAACmC,EAAE,GAAG,IAAI,CAACrB,oBAAoB,MAAM,IAAI,IAAIqB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC2B,OAAO,CAAC,CAAC;IAClFvD,sBAAsB,CAACT,EAAE,EAAEA,EAAE,EAAE,GAAG,EAAEM,+CAA+C,CAAC,CAACuD,IAAI,CAAC7D,EAAE,EAAE,IAAI,CAACoB,GAAG,CAAC;EAC3G;AACJ;AACApB,EAAE,GAAGa,gBAAgB,EAAEZ,0BAA0B,GAAG,IAAIgE,OAAO,CAAC,CAAC,EAAE/D,2BAA2B,GAAG,IAAI+D,OAAO,CAAC,CAAC,EAAElE,2BAA2B,GAAG,IAAImE,OAAO,CAAC,CAAC,EAAE7D,2BAA2B,GAAG,SAASA,2BAA2BA,CAAC8D,GAAG,EAAE;EACjO,MAAM;IAAE/C;EAAI,CAAC,GAAG,IAAI;EACpB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAX,sBAAsB,CAACT,EAAE,EAAEA,EAAE,EAAE,GAAG,EAAEG,4BAA4B,CAAC,CAACkB,GAAG,CAACD,GAAG,EAAE+C,GAAG,CAAC;EAC/E1D,sBAAsB,CAACT,EAAE,EAAEA,EAAE,EAAE,GAAG,EAAEO,+CAA+C,CAAC,CAACsD,IAAI,CAAC7D,EAAE,CAAC;AACjG,CAAC,EAAEM,+CAA+C,GAAG,SAASA,+CAA+CA,CAAC8D,YAAY,EAAE;EACxH,IAAIhC,EAAE;EACN3B,sBAAsB,CAAC,IAAI,EAAET,EAAE,EAAE,GAAG,EAAEG,4BAA4B,CAAC,CAACkE,MAAM,CAACD,YAAY,CAAC;EACxF,IAAI3D,sBAAsB,CAAC,IAAI,EAAET,EAAE,EAAE,GAAG,EAAEG,4BAA4B,CAAC,CAACmE,IAAI,KAAK,CAAC,EAAE;IAChF,CAAClC,EAAE,GAAG3B,sBAAsB,CAAC,IAAI,EAAET,EAAE,EAAE,GAAG,EAAEI,gDAAgD,CAAC,MAAM,IAAI,IAAIgC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmC,KAAK,CAAC,CAAC;IAC9I7D,sBAAsB,CAAC,IAAI,EAAEV,EAAE,EAAE,IAAI,EAAE,GAAG,EAAEI,gDAAgD,CAAC;EACjG;AACJ,CAAC,EAAEG,+CAA+C,GAAG,SAASA,+CAA+CA,CAAA,EAAG;EAC5G;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AAAA,CACH;AACDJ,4BAA4B,GAAG;EAAEqE,KAAK,EAAE,IAAIC,GAAG,CAAC;AAAE,CAAC;AACnDrE,gDAAgD,GAAG;EAAEoE,KAAK,EAAE;AAAK,CAAC;AAClE,SAAS3D,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}