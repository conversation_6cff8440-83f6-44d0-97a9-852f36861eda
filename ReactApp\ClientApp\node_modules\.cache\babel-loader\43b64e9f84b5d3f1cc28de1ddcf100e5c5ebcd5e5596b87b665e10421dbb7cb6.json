{"ast": null, "code": "import { isPresent } from '../utils';\nimport { compose } from '../funcs';\n// tslint:enable:max-line-length\nvar set = function (field, target, value) {\n  target[field] = value;\n  return target;\n};\nvar toCamelCase = function (str) {\n  return str.replace(/(^[A-Z])/g, function (_, g1) {\n    return g1.toLowerCase();\n  });\n};\nvar prop = function (fieldName) {\n  return function (obj) {\n    var value = obj[fieldName];\n    if (isPresent(value)) {\n      return value;\n    }\n    return obj[toCamelCase(fieldName)];\n  };\n};\nvar member = prop(\"Member\");\nvar aggregateMethodName = prop(\"AggregateMethodName\");\nvar value = prop(\"Value\");\nvar convert = function (mapper) {\n  return function (values) {\n    return Object.keys(values).reduce(mapper.bind(null, values), {});\n  };\n};\nvar translateAggregate = convert(function (source, acc, field) {\n  return set(field.toLowerCase(), acc, source[field]);\n});\nvar translateAggregates = convert(function (source, acc, field) {\n  return set(field, acc, translateAggregate(source[field]));\n});\nvar valueOrDefault = function (value, defaultValue) {\n  return isPresent(value) ? value : defaultValue;\n};\nvar normalizeGroup = function (group) {\n  return {\n    aggregates: group.Aggregates || group.aggregates,\n    field: group.Member || group.member || group.field,\n    hasSubgroups: group.HasSubgroups || group.hasSubgroups || false,\n    items: group.Items || group.items,\n    value: valueOrDefault(group.Key, valueOrDefault(group.key, group.value))\n  };\n};\nvar translateGroup = compose(function (_a) {\n  var field = _a.field,\n    hasSubgroups = _a.hasSubgroups,\n    value = _a.value,\n    aggregates = _a.aggregates,\n    items = _a.items;\n  return {\n    aggregates: translateAggregates(aggregates),\n    field: field,\n    items: hasSubgroups ? items.map(translateGroup) : items,\n    value: value\n  };\n}, normalizeGroup);\n// tslint:disable:max-line-length\n/**\n * Converts the grouped result, which is returned into the `Data` field of the UI for ASP.NET MVC `ToDataSourceResult` method, to a comparable format.\n * @param data - The value of the `Data` field of the response.\n * @returns {GroupResult[]} - The converted result.\n */\nexport var translateDataSourceResultGroups = function (data) {\n  return data.map(translateGroup);\n};\n/**\n * Converts the `AggregateResults` field content, which is returned by the UI for ASP.NET MVC `ToDataSourceResult` method, to a comparable format.\n * @param data - The value of the `AggregateResults` field of the response.\n * @returns {AggregateResult} - The converted result.\n */\n// tslint:enable:max-line-length\nexport var translateAggregateResults = function (data) {\n  return (data || []).reduce(function (acc, x) {\n    return set(member(x), acc, set(aggregateMethodName(x).toLowerCase(), acc[member(x)] || {}, value(x)));\n  }, {});\n};", "map": {"version": 3, "names": ["isPresent", "compose", "set", "field", "target", "value", "toCamelCase", "str", "replace", "_", "g1", "toLowerCase", "prop", "fieldName", "obj", "member", "aggregateMethodName", "convert", "mapper", "values", "Object", "keys", "reduce", "bind", "translateAggregate", "source", "acc", "translateAggregates", "valueOrDefault", "defaultValue", "normalizeGroup", "group", "aggregates", "Aggregates", "Member", "hasSubgroups", "HasSubgroups", "items", "Items", "Key", "key", "translateGroup", "_a", "map", "translateDataSourceResultGroups", "data", "translateAggregateResults", "x"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-data-query/dist/es/mvc/deserialization.js"], "sourcesContent": ["import { isPresent } from '../utils';\nimport { compose } from '../funcs';\n// tslint:enable:max-line-length\nvar set = function (field, target, value) {\n    target[field] = value;\n    return target;\n};\nvar toCamelCase = function (str) { return str.replace(/(^[A-Z])/g, function (_, g1) { return g1.toLowerCase(); }); };\nvar prop = function (fieldName) { return function (obj) {\n    var value = obj[fieldName];\n    if (isPresent(value)) {\n        return value;\n    }\n    return obj[toCamelCase(fieldName)];\n}; };\nvar member = prop(\"Member\");\nvar aggregateMethodName = prop(\"AggregateMethodName\");\nvar value = prop(\"Value\");\nvar convert = function (mapper) { return function (values) { return Object.keys(values).reduce(mapper.bind(null, values), {}); }; };\nvar translateAggregate = convert(function (source, acc, field) { return set(field.toLowerCase(), acc, source[field]); });\nvar translateAggregates = convert(function (source, acc, field) { return set(field, acc, translateAggregate(source[field])); });\nvar valueOrDefault = function (value, defaultValue) { return isPresent(value) ? value : defaultValue; };\nvar normalizeGroup = function (group) { return ({\n    aggregates: group.Aggregates || group.aggregates,\n    field: group.Member || group.member || group.field,\n    hasSubgroups: group.HasSubgroups || group.hasSubgroups || false,\n    items: group.Items || group.items,\n    value: valueOrDefault(group.Key, valueOrDefault(group.key, group.value))\n}); };\nvar translateGroup = compose(function (_a) {\n    var field = _a.field, hasSubgroups = _a.hasSubgroups, value = _a.value, aggregates = _a.aggregates, items = _a.items;\n    return ({\n        aggregates: translateAggregates(aggregates),\n        field: field,\n        items: hasSubgroups ? items.map(translateGroup) : items,\n        value: value\n    });\n}, normalizeGroup);\n// tslint:disable:max-line-length\n/**\n * Converts the grouped result, which is returned into the `Data` field of the UI for ASP.NET MVC `ToDataSourceResult` method, to a comparable format.\n * @param data - The value of the `Data` field of the response.\n * @returns {GroupResult[]} - The converted result.\n */\nexport var translateDataSourceResultGroups = function (data) { return data.map(translateGroup); };\n/**\n * Converts the `AggregateResults` field content, which is returned by the UI for ASP.NET MVC `ToDataSourceResult` method, to a comparable format.\n * @param data - The value of the `AggregateResults` field of the response.\n * @returns {AggregateResult} - The converted result.\n */\n// tslint:enable:max-line-length\nexport var translateAggregateResults = function (data) { return ((data || []).reduce(function (acc, x) { return set(member(x), acc, set(aggregateMethodName(x).toLowerCase(), acc[member(x)] || {}, value(x))); }, {})); };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,UAAU;AACpC,SAASC,OAAO,QAAQ,UAAU;AAClC;AACA,IAAIC,GAAG,GAAG,SAAAA,CAAUC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAE;EACtCD,MAAM,CAACD,KAAK,CAAC,GAAGE,KAAK;EACrB,OAAOD,MAAM;AACjB,CAAC;AACD,IAAIE,WAAW,GAAG,SAAAA,CAAUC,GAAG,EAAE;EAAE,OAAOA,GAAG,CAACC,OAAO,CAAC,WAAW,EAAE,UAAUC,CAAC,EAAEC,EAAE,EAAE;IAAE,OAAOA,EAAE,CAACC,WAAW,CAAC,CAAC;EAAE,CAAC,CAAC;AAAE,CAAC;AACpH,IAAIC,IAAI,GAAG,SAAAA,CAAUC,SAAS,EAAE;EAAE,OAAO,UAAUC,GAAG,EAAE;IACpD,IAAIT,KAAK,GAAGS,GAAG,CAACD,SAAS,CAAC;IAC1B,IAAIb,SAAS,CAACK,KAAK,CAAC,EAAE;MAClB,OAAOA,KAAK;IAChB;IACA,OAAOS,GAAG,CAACR,WAAW,CAACO,SAAS,CAAC,CAAC;EACtC,CAAC;AAAE,CAAC;AACJ,IAAIE,MAAM,GAAGH,IAAI,CAAC,QAAQ,CAAC;AAC3B,IAAII,mBAAmB,GAAGJ,IAAI,CAAC,qBAAqB,CAAC;AACrD,IAAIP,KAAK,GAAGO,IAAI,CAAC,OAAO,CAAC;AACzB,IAAIK,OAAO,GAAG,SAAAA,CAAUC,MAAM,EAAE;EAAE,OAAO,UAAUC,MAAM,EAAE;IAAE,OAAOC,MAAM,CAACC,IAAI,CAACF,MAAM,CAAC,CAACG,MAAM,CAACJ,MAAM,CAACK,IAAI,CAAC,IAAI,EAAEJ,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,CAAC;AAAE,CAAC;AACnI,IAAIK,kBAAkB,GAAGP,OAAO,CAAC,UAAUQ,MAAM,EAAEC,GAAG,EAAEvB,KAAK,EAAE;EAAE,OAAOD,GAAG,CAACC,KAAK,CAACQ,WAAW,CAAC,CAAC,EAAEe,GAAG,EAAED,MAAM,CAACtB,KAAK,CAAC,CAAC;AAAE,CAAC,CAAC;AACxH,IAAIwB,mBAAmB,GAAGV,OAAO,CAAC,UAAUQ,MAAM,EAAEC,GAAG,EAAEvB,KAAK,EAAE;EAAE,OAAOD,GAAG,CAACC,KAAK,EAAEuB,GAAG,EAAEF,kBAAkB,CAACC,MAAM,CAACtB,KAAK,CAAC,CAAC,CAAC;AAAE,CAAC,CAAC;AAC/H,IAAIyB,cAAc,GAAG,SAAAA,CAAUvB,KAAK,EAAEwB,YAAY,EAAE;EAAE,OAAO7B,SAAS,CAACK,KAAK,CAAC,GAAGA,KAAK,GAAGwB,YAAY;AAAE,CAAC;AACvG,IAAIC,cAAc,GAAG,SAAAA,CAAUC,KAAK,EAAE;EAAE,OAAQ;IAC5CC,UAAU,EAAED,KAAK,CAACE,UAAU,IAAIF,KAAK,CAACC,UAAU;IAChD7B,KAAK,EAAE4B,KAAK,CAACG,MAAM,IAAIH,KAAK,CAAChB,MAAM,IAAIgB,KAAK,CAAC5B,KAAK;IAClDgC,YAAY,EAAEJ,KAAK,CAACK,YAAY,IAAIL,KAAK,CAACI,YAAY,IAAI,KAAK;IAC/DE,KAAK,EAAEN,KAAK,CAACO,KAAK,IAAIP,KAAK,CAACM,KAAK;IACjChC,KAAK,EAAEuB,cAAc,CAACG,KAAK,CAACQ,GAAG,EAAEX,cAAc,CAACG,KAAK,CAACS,GAAG,EAAET,KAAK,CAAC1B,KAAK,CAAC;EAC3E,CAAC;AAAG,CAAC;AACL,IAAIoC,cAAc,GAAGxC,OAAO,CAAC,UAAUyC,EAAE,EAAE;EACvC,IAAIvC,KAAK,GAAGuC,EAAE,CAACvC,KAAK;IAAEgC,YAAY,GAAGO,EAAE,CAACP,YAAY;IAAE9B,KAAK,GAAGqC,EAAE,CAACrC,KAAK;IAAE2B,UAAU,GAAGU,EAAE,CAACV,UAAU;IAAEK,KAAK,GAAGK,EAAE,CAACL,KAAK;EACpH,OAAQ;IACJL,UAAU,EAAEL,mBAAmB,CAACK,UAAU,CAAC;IAC3C7B,KAAK,EAAEA,KAAK;IACZkC,KAAK,EAAEF,YAAY,GAAGE,KAAK,CAACM,GAAG,CAACF,cAAc,CAAC,GAAGJ,KAAK;IACvDhC,KAAK,EAAEA;EACX,CAAC;AACL,CAAC,EAAEyB,cAAc,CAAC;AAClB;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIc,+BAA+B,GAAG,SAAAA,CAAUC,IAAI,EAAE;EAAE,OAAOA,IAAI,CAACF,GAAG,CAACF,cAAc,CAAC;AAAE,CAAC;AACjG;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIK,yBAAyB,GAAG,SAAAA,CAAUD,IAAI,EAAE;EAAE,OAAQ,CAACA,IAAI,IAAI,EAAE,EAAEvB,MAAM,CAAC,UAAUI,GAAG,EAAEqB,CAAC,EAAE;IAAE,OAAO7C,GAAG,CAACa,MAAM,CAACgC,CAAC,CAAC,EAAErB,GAAG,EAAExB,GAAG,CAACc,mBAAmB,CAAC+B,CAAC,CAAC,CAACpC,WAAW,CAAC,CAAC,EAAEe,GAAG,CAACX,MAAM,CAACgC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE1C,KAAK,CAAC0C,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}