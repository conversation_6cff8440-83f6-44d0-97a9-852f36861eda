{"ast": null, "code": "/* Copyright 2022 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar _AnnotationEditorLayer_instances, _a, _AnnotationEditorLayer_accessibilityManager, _AnnotationEditorLayer_allowClick, _AnnotationEditorLayer_annotationLayer, _AnnotationEditorLayer_clickAC, _AnnotationEditorLayer_editorFocusTimeoutId, _AnnotationEditorLayer_editors, _AnnotationEditorLayer_hadPointerDown, _AnnotationEditorLayer_isCleaningUp, _AnnotationEditorLayer_isDisabling, _AnnotationEditorLayer_textLayer, _AnnotationEditorLayer_textSelectionAC, _AnnotationEditorLayer_uiManager, _AnnotationEditorLayer_editorTypes, _AnnotationEditorLayer_textLayerPointerDown, _AnnotationEditorLayer_currentEditorType_get, _AnnotationEditorLayer_createNewEditor, _AnnotationEditorLayer_getCenterPoint, _AnnotationEditorLayer_cleanup;\nimport { __classPrivateFieldGet, __classPrivateFieldSet } from \"tslib\";\n/** @typedef {import(\"./tools.js\").AnnotationEditorUIManager} AnnotationEditorUIManager */\n/** @typedef {import(\"../display_utils.js\").PageViewport} PageViewport */\n/** @typedef {import(\"../../../web/text_accessibility.js\").TextAccessibilityManager} TextAccessibilityManager */\n/** @typedef {import(\"../../../web/interfaces\").IL10n} IL10n */\n/** @typedef {import(\"../annotation_layer.js\").AnnotationLayer} AnnotationLayer */\n/** @typedef {import(\"../draw_layer.js\").DrawLayer} DrawLayer */\nimport { FreeTextEditor } from \"./editors/free-text-editor\";\nimport { AnnotationEditorType, FeatureTest, setLayerDimensions } from \"pdfjs-dist/legacy/build/pdf.mjs\";\nimport { AnnotationEditor } from \"./editors/annotation-editor\";\nimport { HighlightEditor } from \"./editors/highlight-editor\";\nclass AnnotationEditorLayer {\n  /**\n   * @param {AnnotationEditorLayerOptions} options\n   */\n  constructor({\n    uiManager,\n    pageIndex,\n    div,\n    accessibilityManager,\n    annotationLayer,\n    drawLayer,\n    textLayer,\n    viewport\n    // l10n\n  }) {\n    _AnnotationEditorLayer_instances.add(this);\n    // todo: props\n    this.drawLayer = null;\n    this.pageIndex = 0;\n    this.div = null;\n    this.viewport = null;\n    // todo: props\n    _AnnotationEditorLayer_accessibilityManager.set(this, void 0);\n    _AnnotationEditorLayer_allowClick.set(this, false);\n    _AnnotationEditorLayer_annotationLayer.set(this, null);\n    _AnnotationEditorLayer_clickAC.set(this, null);\n    _AnnotationEditorLayer_editorFocusTimeoutId.set(this, null);\n    _AnnotationEditorLayer_editors.set(this, new Map());\n    _AnnotationEditorLayer_hadPointerDown.set(this, false);\n    _AnnotationEditorLayer_isCleaningUp.set(this, false);\n    _AnnotationEditorLayer_isDisabling.set(this, false);\n    _AnnotationEditorLayer_textLayer.set(this, null);\n    _AnnotationEditorLayer_textSelectionAC.set(this, null);\n    _AnnotationEditorLayer_uiManager.set(this, null);\n    const editorTypes = [...__classPrivateFieldGet(_a, _a, \"f\", _AnnotationEditorLayer_editorTypes).values()];\n    if (!_a._initialized) {\n      _a._initialized = true;\n      for (const editorType of editorTypes) {\n        // editorType.initialize(l10n, uiManager);\n        editorType.initialize({}, uiManager);\n      }\n    }\n    uiManager.registerEditorTypes(editorTypes);\n    __classPrivateFieldSet(this, _AnnotationEditorLayer_uiManager, uiManager, \"f\");\n    this.pageIndex = pageIndex;\n    this.div = div;\n    __classPrivateFieldSet(this, _AnnotationEditorLayer_accessibilityManager, accessibilityManager, \"f\");\n    __classPrivateFieldSet(this, _AnnotationEditorLayer_annotationLayer, annotationLayer, \"f\");\n    this.viewport = viewport;\n    __classPrivateFieldSet(this, _AnnotationEditorLayer_textLayer, textLayer, \"f\");\n    this.drawLayer = drawLayer;\n    __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").addLayer(this);\n    // if (!this.#annotationLayer || !this.#textLayer) {\n    // }\n  }\n  // todo: ported from AnnotationEditorLayerBuilder\n  hide() {\n    if (!this.div) {\n      return;\n    }\n    this.div.hidden = true;\n  }\n  show() {\n    if (!this.div || this.isInvisible) {\n      return;\n    }\n    this.div.hidden = false;\n  }\n  // todo: ported from AnnotationEditorLayerBuilder\n  get isEmpty() {\n    return __classPrivateFieldGet(this, _AnnotationEditorLayer_editors, \"f\").size === 0;\n  }\n  get isInvisible() {\n    return this.isEmpty && __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").getMode() === AnnotationEditorType.NONE;\n  }\n  /**\n   * Update the toolbar if it's required to reflect the tool currently used.\n   * @param {number} mode\n   */\n  // updateToolbar(mode) {\n  updateToolbar() {\n    // this.#uiManager.updateToolbar(mode);\n  }\n  /**\n   * The mode has changed: it must be updated.\n   * @param {number} mode\n   */\n  updateMode(mode = __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").getMode()) {\n    __classPrivateFieldGet(this, _AnnotationEditorLayer_instances, \"m\", _AnnotationEditorLayer_cleanup).call(this);\n    switch (mode) {\n      case AnnotationEditorType.NONE:\n        this.disableTextSelection();\n        this.togglePointerEvents(false);\n        this.toggleAnnotationLayerPointerEvents(true);\n        this.disableClick();\n        return;\n      case AnnotationEditorType.INK:\n        // We always want to have an ink editor ready to draw in.\n        this.addInkEditorIfNeeded(false);\n        this.disableTextSelection();\n        this.togglePointerEvents(true);\n        this.disableClick();\n        break;\n      case AnnotationEditorType.HIGHLIGHT:\n        this.enableTextSelection();\n        this.togglePointerEvents(false);\n        this.disableClick();\n        break;\n      default:\n        this.disableTextSelection();\n        this.togglePointerEvents(true);\n        this.enableClick();\n    }\n    this.toggleAnnotationLayerPointerEvents(false);\n    const {\n      classList\n    } = this.div;\n    for (const editorType of __classPrivateFieldGet(_a, _a, \"f\", _AnnotationEditorLayer_editorTypes).values()) {\n      classList.toggle(`${editorType._type}Editing`, mode === editorType._editorType);\n    }\n    this.div.hidden = false;\n  }\n  hasTextLayer(textLayer) {\n    var _b;\n    return textLayer === ((_b = __classPrivateFieldGet(this, _AnnotationEditorLayer_textLayer, \"f\")) === null || _b === void 0 ? void 0 : _b.div);\n    // return textLayer === this.#textLayer || textLayer === this.#textLayer?.div;\n  }\n  addInkEditorIfNeeded(isCommitting) {\n    if (__classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").getMode() !== AnnotationEditorType.INK) {\n      // We don't want to add an ink editor if we're not in ink mode!\n      return;\n    }\n    if (!isCommitting) {\n      // We're removing an editor but an empty one can already exist so in this\n      // case we don't need to create a new one.\n      for (const editor of __classPrivateFieldGet(this, _AnnotationEditorLayer_editors, \"f\").values()) {\n        if (editor.isEmpty()) {\n          editor.setInBackground();\n          return;\n        }\n      }\n    }\n    const newEditor = this.createAndAddNewEditor({\n      offsetX: 0,\n      offsetY: 0\n    }, /* isCentered = */false);\n    newEditor.setInBackground();\n  }\n  /**\n   * Set the editing state.\n   * @param {boolean} isEditing\n   */\n  setEditingState(isEditing) {\n    __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").setEditingState(isEditing);\n  }\n  /**\n   * Add some commands into the CommandManager (undo/redo stuff).\n   * @param {Object} params\n   */\n  addCommands(params) {\n    __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").addCommands(params);\n  }\n  toggleDrawing(enabled = false) {\n    this.div.classList.toggle(\"drawing\", !enabled);\n    // this.div.classList.toggle(\"k-drawing\", !enabled);\n  }\n  togglePointerEvents(enabled = false) {\n    // this.div.classList.toggle(\"disabled\", !enabled);\n    this.div.classList.toggle(\"k-annotation-editor-layer-disabled\", !enabled);\n  }\n  toggleAnnotationLayerPointerEvents(enabled = false) {\n    var _b;\n    // this.#annotationLayer?.div.classList.toggle(\"disabled\", !enabled);\n    (_b = __classPrivateFieldGet(this, _AnnotationEditorLayer_annotationLayer, \"f\")) === null || _b === void 0 ? void 0 : _b.div.classList.toggle(\"k-annotation-editor-layer-disabled\", !enabled);\n  }\n  /**\n   * Enable pointer events on the main div in order to enable\n   * editor creation.\n   */\n  enable() {\n    this.div.tabIndex = 0;\n    this.togglePointerEvents(true);\n    const annotationElementIds = new Set();\n    for (const editor of __classPrivateFieldGet(this, _AnnotationEditorLayer_editors, \"f\").values()) {\n      editor.enableEditing();\n      editor.show(true);\n      if (editor.annotationElementId) {\n        __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").removeChangedExistingAnnotation(editor);\n        annotationElementIds.add(editor.annotationElementId);\n      }\n    }\n    if (!__classPrivateFieldGet(this, _AnnotationEditorLayer_annotationLayer, \"f\")) {\n      return;\n    }\n    const editables = __classPrivateFieldGet(this, _AnnotationEditorLayer_annotationLayer, \"f\").getEditableAnnotations();\n    for (const editable of editables) {\n      // The element must be hidden whatever its state is.\n      editable.hide();\n      if (__classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").isDeletedAnnotationElement(editable.data.id)) {\n        continue;\n      }\n      if (annotationElementIds.has(editable.data.id)) {\n        continue;\n      }\n      const editor = this.deserialize(editable);\n      if (!editor) {\n        continue;\n      }\n      this.addOrRebuild(editor);\n      editor.enableEditing();\n    }\n  }\n  /**\n   * Disable editor creation.\n   */\n  disable() {\n    var _b;\n    __classPrivateFieldSet(this, _AnnotationEditorLayer_isDisabling, true, \"f\");\n    this.div.tabIndex = -1;\n    this.togglePointerEvents(false);\n    const changedAnnotations = new Map();\n    const resetAnnotations = new Map();\n    for (const editor of __classPrivateFieldGet(this, _AnnotationEditorLayer_editors, \"f\").values()) {\n      editor.disableEditing();\n      if (!editor.annotationElementId) {\n        continue;\n      }\n      if (editor.serialize() !== null) {\n        changedAnnotations.set(editor.annotationElementId, editor);\n        continue;\n      } else {\n        resetAnnotations.set(editor.annotationElementId, editor);\n      }\n      (_b = this.getEditableAnnotation(editor.annotationElementId)) === null || _b === void 0 ? void 0 : _b.show();\n      editor.remove();\n    }\n    if (__classPrivateFieldGet(this, _AnnotationEditorLayer_annotationLayer, \"f\")) {\n      // Show the annotations that were hidden in enable().\n      const editables = __classPrivateFieldGet(this, _AnnotationEditorLayer_annotationLayer, \"f\").getEditableAnnotations();\n      for (const editable of editables) {\n        const {\n          id\n        } = editable.data;\n        if (__classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").isDeletedAnnotationElement(id)) {\n          continue;\n        }\n        let editor = resetAnnotations.get(id);\n        if (editor) {\n          editor.resetAnnotationElement(editable);\n          editor.show(false);\n          editable.show();\n          continue;\n        }\n        editor = changedAnnotations.get(id);\n        if (editor) {\n          __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").addChangedExistingAnnotation(editor);\n          editor.renderAnnotationElement(editable);\n          editor.show(false);\n        }\n        editable.show();\n      }\n    }\n    __classPrivateFieldGet(this, _AnnotationEditorLayer_instances, \"m\", _AnnotationEditorLayer_cleanup).call(this);\n    if (this.isEmpty) {\n      this.div.hidden = true;\n    }\n    const {\n      classList\n    } = this.div;\n    for (const editorType of __classPrivateFieldGet(_a, _a, \"f\", _AnnotationEditorLayer_editorTypes).values()) {\n      classList.remove(`${editorType._type}Editing`);\n    }\n    this.disableTextSelection();\n    this.toggleAnnotationLayerPointerEvents(true);\n    __classPrivateFieldSet(this, _AnnotationEditorLayer_isDisabling, false, \"f\");\n  }\n  getEditableAnnotation(id) {\n    var _b;\n    return ((_b = __classPrivateFieldGet(this, _AnnotationEditorLayer_annotationLayer, \"f\")) === null || _b === void 0 ? void 0 : _b.getEditableAnnotation(id)) || null;\n  }\n  /**\n   * Set the current editor.\n   * @param {AnnotationEditor} editor\n   */\n  setActiveEditor(editor) {\n    const currentActive = __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").getActive();\n    if (currentActive === editor) {\n      return;\n    }\n    __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").setActiveEditor(editor);\n  }\n  enableTextSelection() {\n    var _b;\n    this.div.tabIndex = -1;\n    if (((_b = __classPrivateFieldGet(this, _AnnotationEditorLayer_textLayer, \"f\")) === null || _b === void 0 ? void 0 : _b.div) && !__classPrivateFieldGet(this, _AnnotationEditorLayer_textSelectionAC, \"f\")) {\n      __classPrivateFieldSet(this, _AnnotationEditorLayer_textSelectionAC, new AbortController(), \"f\");\n      const signal = __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").combinedSignal(__classPrivateFieldGet(this, _AnnotationEditorLayer_textSelectionAC, \"f\"));\n      __classPrivateFieldGet(this, _AnnotationEditorLayer_textLayer, \"f\").div.addEventListener(\"pointerdown\", __classPrivateFieldGet(this, _AnnotationEditorLayer_instances, \"m\", _AnnotationEditorLayer_textLayerPointerDown).bind(this), {\n        signal\n      });\n      // this.#textLayer.div.classList.add(\"highlighting\");\n    }\n  }\n  disableTextSelection() {\n    var _b;\n    this.div.tabIndex = 0;\n    if (((_b = __classPrivateFieldGet(this, _AnnotationEditorLayer_textLayer, \"f\")) === null || _b === void 0 ? void 0 : _b.div) && __classPrivateFieldGet(this, _AnnotationEditorLayer_textSelectionAC, \"f\")) {\n      __classPrivateFieldGet(this, _AnnotationEditorLayer_textSelectionAC, \"f\").abort();\n      __classPrivateFieldSet(this, _AnnotationEditorLayer_textSelectionAC, null, \"f\");\n      __classPrivateFieldGet(this, _AnnotationEditorLayer_textLayer, \"f\").div.classList.remove(\"highlighting\");\n    }\n  }\n  enableClick() {\n    if (__classPrivateFieldGet(this, _AnnotationEditorLayer_clickAC, \"f\")) {\n      return;\n    }\n    __classPrivateFieldSet(this, _AnnotationEditorLayer_clickAC, new AbortController(), \"f\");\n    const signal = __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").combinedSignal(__classPrivateFieldGet(this, _AnnotationEditorLayer_clickAC, \"f\"));\n    this.div.addEventListener(\"pointerdown\", this.pointerdown.bind(this), {\n      signal\n    });\n    this.div.addEventListener(\"pointerup\", this.pointerup.bind(this), {\n      signal\n    });\n  }\n  disableClick() {\n    var _b;\n    (_b = __classPrivateFieldGet(this, _AnnotationEditorLayer_clickAC, \"f\")) === null || _b === void 0 ? void 0 : _b.abort();\n    __classPrivateFieldSet(this, _AnnotationEditorLayer_clickAC, null, \"f\");\n  }\n  attach(editor) {\n    __classPrivateFieldGet(this, _AnnotationEditorLayer_editors, \"f\").set(editor.id, editor);\n    const {\n      annotationElementId\n    } = editor;\n    if (annotationElementId && __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").isDeletedAnnotationElement(annotationElementId)) {\n      __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").removeDeletedAnnotationElement(editor);\n    }\n  }\n  detach(editor) {\n    var _b;\n    __classPrivateFieldGet(this, _AnnotationEditorLayer_editors, \"f\").delete(editor.id);\n    (_b = __classPrivateFieldGet(this, _AnnotationEditorLayer_accessibilityManager, \"f\")) === null || _b === void 0 ? void 0 : _b.removePointerInTextLayer(editor.contentDiv);\n    if (!__classPrivateFieldGet(this, _AnnotationEditorLayer_isDisabling, \"f\") && editor.annotationElementId) {\n      __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").addDeletedAnnotationElement(editor);\n    }\n  }\n  /**\n   * Remove an editor.\n   * @param {AnnotationEditor} editor\n   */\n  remove(editor) {\n    this.detach(editor);\n    __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").removeEditor(editor);\n    editor.div.remove();\n    editor.isAttachedToDOM = false;\n    if (!__classPrivateFieldGet(this, _AnnotationEditorLayer_isCleaningUp, \"f\")) {\n      this.addInkEditorIfNeeded(/* isCommitting = */false);\n    }\n  }\n  /**\n   * An editor can have a different parent, for example after having\n   * being dragged and droped from a page to another.\n   * @param {AnnotationEditor} editor\n   */\n  changeParent(editor) {\n    var _b;\n    if (editor.parent === this) {\n      return;\n    }\n    if (editor.parent && editor.annotationElementId) {\n      __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").addDeletedAnnotationElement(editor.annotationElementId);\n      AnnotationEditor.deleteAnnotationElement(editor);\n      editor.annotationElementId = null;\n    }\n    this.attach(editor);\n    (_b = editor.parent) === null || _b === void 0 ? void 0 : _b.detach(editor);\n    editor.setParent(this);\n    if (editor.div && editor.isAttachedToDOM) {\n      editor.div.remove();\n      this.div.append(editor.div);\n    }\n  }\n  /**\n   * Add a new editor in the current view.\n   * @param {AnnotationEditor} editor\n   */\n  add(editor) {\n    if (editor.parent === this && editor.isAttachedToDOM) {\n      return;\n    }\n    this.changeParent(editor);\n    __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").addEditor(editor);\n    this.attach(editor);\n    if (!editor.isAttachedToDOM) {\n      const div = editor.render();\n      this.div.append(div);\n      editor.isAttachedToDOM = true;\n    }\n    // The editor will be correctly moved into the DOM (see fixAndSetPosition).\n    editor.fixAndSetPosition();\n    editor.onceAdded();\n    __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").addToAnnotationStorage(editor);\n    // editor._reportTelemetry(editor.telemetryInitialData);\n    // todo: this differs from pdf.js\n    // this.setActiveEditor(editor);\n  }\n  moveEditorInDOM(editor) {\n    var _b;\n    if (!editor.isAttachedToDOM) {\n      return;\n    }\n    const {\n      activeElement\n    } = document;\n    if (editor.div.contains(activeElement) && !__classPrivateFieldGet(this, _AnnotationEditorLayer_editorFocusTimeoutId, \"f\")) {\n      // When the div is moved in the DOM the focus can move somewhere else,\n      // so we want to be sure that the focus will stay on the editor but we\n      // don't want to call any focus callbacks, hence we disable them and only\n      // re-enable them when the editor has the focus.\n      editor._focusEventsAllowed = false;\n      __classPrivateFieldSet(this, _AnnotationEditorLayer_editorFocusTimeoutId, setTimeout(() => {\n        __classPrivateFieldSet(this, _AnnotationEditorLayer_editorFocusTimeoutId, null, \"f\");\n        if (!editor.div.contains(document.activeElement)) {\n          editor.div.addEventListener(\"focusin\", () => {\n            editor._focusEventsAllowed = true;\n          }, {\n            once: true,\n            signal: __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\")._signal\n          });\n          activeElement.focus();\n        } else {\n          editor._focusEventsAllowed = true;\n        }\n      }, 0), \"f\");\n    }\n    editor._structTreeParentId = (_b = __classPrivateFieldGet(this, _AnnotationEditorLayer_accessibilityManager, \"f\")) === null || _b === void 0 ? void 0 : _b.moveElementInDOM(this.div, editor.div, editor.contentDiv, /* isRemovable = */true);\n  }\n  /**\n   * Add or rebuild depending if it has been removed or not.\n   * @param {AnnotationEditor} editor\n   */\n  addOrRebuild(editor) {\n    if (editor.needsToBeRebuilt()) {\n      editor.parent || (editor.parent = this);\n      editor.rebuild();\n      editor.show();\n    } else {\n      this.add(editor);\n    }\n  }\n  /**\n   * Add a new editor and make this addition undoable.\n   * @param {AnnotationEditor} editor\n   */\n  addUndoableEditor(editor) {\n    const cmd = () => editor._uiManager.rebuild(editor);\n    const undo = () => {\n      editor.remove();\n    };\n    this.addCommands({\n      cmd,\n      undo,\n      mustExec: false\n    });\n  }\n  /**\n   * Get an id for an editor.\n   * @returns {string}\n   */\n  getNextId() {\n    return __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").getId();\n  }\n  combinedSignal(abortController) {\n    return __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").combinedSignal(abortController);\n  }\n  canCreateNewEmptyEditor() {\n    var _b;\n    return (_b = __classPrivateFieldGet(this, _AnnotationEditorLayer_instances, \"a\", _AnnotationEditorLayer_currentEditorType_get)) === null || _b === void 0 ? void 0 : _b.canCreateNewEmptyEditor();\n  }\n  /**\n   * Paste some content into a new editor.\n   * @param {number} mode\n   * @param {Object} params\n   */\n  pasteEditor(mode, params) {\n    __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").updateToolbar(mode);\n    __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").updateMode(mode);\n    const {\n      offsetX,\n      offsetY\n    } = __classPrivateFieldGet(this, _AnnotationEditorLayer_instances, \"m\", _AnnotationEditorLayer_getCenterPoint).call(this);\n    const id = this.getNextId();\n    const editor = __classPrivateFieldGet(this, _AnnotationEditorLayer_instances, \"m\", _AnnotationEditorLayer_createNewEditor).call(this, Object.assign({\n      parent: this,\n      id,\n      x: offsetX,\n      y: offsetY,\n      uiManager: __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\"),\n      isCentered: true\n    }, params));\n    if (editor) {\n      this.add(editor);\n    }\n  }\n  /**\n   * Create a new editor\n   * @param {Object} data\n   * @returns {AnnotationEditor | null}\n   */\n  deserialize(data) {\n    var _b, _c;\n    return ((_c = __classPrivateFieldGet(_a, _a, \"f\", _AnnotationEditorLayer_editorTypes).get((_b = data.annotationType) !== null && _b !== void 0 ? _b : data.annotationEditorType)) === null || _c === void 0 ? void 0 : _c.deserialize(data, this, __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\"))) || null;\n  }\n  /**\n   * Create and add a new editor.\n   * @param {PointerEvent} event\n   * @param {boolean} isCentered\n   * @param [Object] data\n   * @returns {AnnotationEditor}\n   */\n  createAndAddNewEditor(event, isCentered, data = {}) {\n    const id = this.getNextId();\n    const editor = __classPrivateFieldGet(this, _AnnotationEditorLayer_instances, \"m\", _AnnotationEditorLayer_createNewEditor).call(this, Object.assign({\n      parent: this,\n      id,\n      x: event.offsetX,\n      y: event.offsetY,\n      uiManager: __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\"),\n      isCentered\n    }, data));\n    if (editor) {\n      this.add(editor);\n    }\n    return editor;\n  }\n  /**\n   * Create and add a new editor.\n   */\n  addNewEditor() {\n    this.createAndAddNewEditor(__classPrivateFieldGet(this, _AnnotationEditorLayer_instances, \"m\", _AnnotationEditorLayer_getCenterPoint).call(this), /* isCentered = */true);\n  }\n  /**\n   * Set the last selected editor.\n   * @param {AnnotationEditor} editor\n   */\n  setSelected(editor) {\n    __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").setSelected(editor);\n  }\n  /**\n   * Add or remove an editor the current selection.\n   * @param {AnnotationEditor} editor\n   */\n  toggleSelected(editor) {\n    __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").toggleSelected(editor);\n  }\n  /**\n   * Check if the editor is selected.\n   * @param {AnnotationEditor} editor\n   */\n  isSelected(editor) {\n    return __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").isSelected(editor);\n  }\n  /**\n   * Unselect an editor.\n   * @param {AnnotationEditor} editor\n   */\n  unselect(editor) {\n    __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").unselect(editor);\n  }\n  /**\n   * Pointerup callback.\n   * @param {PointerEvent} event\n   */\n  pointerup(event) {\n    const {\n      isMac\n    } = FeatureTest.platform;\n    if (event.button !== 0 || event.ctrlKey && isMac) {\n      // Don't create an editor on right click.\n      return;\n    }\n    if (event.target !== this.div) {\n      return;\n    }\n    if (!__classPrivateFieldGet(this, _AnnotationEditorLayer_hadPointerDown, \"f\")) {\n      // It can happen when the user starts a drag inside a text editor\n      // and then releases the mouse button outside of it. In such a case\n      // we don't want to create a new editor, hence we check that a pointerdown\n      // occurred on this div previously.\n      return;\n    }\n    __classPrivateFieldSet(this, _AnnotationEditorLayer_hadPointerDown, false, \"f\");\n    if (!__classPrivateFieldGet(this, _AnnotationEditorLayer_allowClick, \"f\")) {\n      __classPrivateFieldSet(this, _AnnotationEditorLayer_allowClick, true, \"f\");\n      return;\n    }\n    if (__classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").getMode() === AnnotationEditorType.STAMP) {\n      __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").unselectAll();\n      return;\n    }\n    this.createAndAddNewEditor(event, /* isCentered = */false);\n  }\n  /**\n   * Pointerdown callback.\n   * @param {PointerEvent} event\n   */\n  pointerdown(event) {\n    if (__classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").getMode() === AnnotationEditorType.HIGHLIGHT) {\n      this.enableTextSelection();\n    }\n    if (__classPrivateFieldGet(this, _AnnotationEditorLayer_hadPointerDown, \"f\")) {\n      // It's possible to have a second pointerdown event before a pointerup one\n      // when the user puts a finger on a touchscreen and then add a second one\n      // to start a pinch-to-zoom gesture.\n      // That said, in case it's possible to have two pointerdown events with\n      // a mouse, we don't want to create a new editor in such a case either.\n      __classPrivateFieldSet(this, _AnnotationEditorLayer_hadPointerDown, false, \"f\");\n      return;\n    }\n    const {\n      isMac\n    } = FeatureTest.platform;\n    if (event.button !== 0 || event.ctrlKey && isMac) {\n      // Do nothing on right click.\n      return;\n    }\n    if (event.target !== this.div) {\n      return;\n    }\n    __classPrivateFieldSet(this, _AnnotationEditorLayer_hadPointerDown, true, \"f\");\n    const editor = __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").getActive();\n    __classPrivateFieldSet(this, _AnnotationEditorLayer_allowClick, !editor || editor.isEmpty(), \"f\");\n  }\n  /**\n   *\n   * @param {AnnotationEditor} editor\n   * @param {number} x\n   * @param {number} y\n   * @returns\n   */\n  findNewParent(editor, x, y) {\n    const layer = __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").findParent(x, y);\n    if (layer === null || layer === this) {\n      return false;\n    }\n    layer.changeParent(editor);\n    return true;\n  }\n  /**\n   * Destroy the main editor.\n   */\n  destroy() {\n    var _b, _c;\n    if (((_b = __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").getActive()) === null || _b === void 0 ? void 0 : _b.parent) === this) {\n      // We need to commit the current editor before destroying the layer.\n      __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").commitOrRemove();\n      __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").setActiveEditor(null);\n    }\n    if (__classPrivateFieldGet(this, _AnnotationEditorLayer_editorFocusTimeoutId, \"f\")) {\n      clearTimeout(__classPrivateFieldGet(this, _AnnotationEditorLayer_editorFocusTimeoutId, \"f\"));\n      __classPrivateFieldSet(this, _AnnotationEditorLayer_editorFocusTimeoutId, null, \"f\");\n    }\n    for (const editor of __classPrivateFieldGet(this, _AnnotationEditorLayer_editors, \"f\").values()) {\n      (_c = __classPrivateFieldGet(this, _AnnotationEditorLayer_accessibilityManager, \"f\")) === null || _c === void 0 ? void 0 : _c.removePointerInTextLayer(editor.contentDiv);\n      editor.setParent(null);\n      editor.isAttachedToDOM = false;\n      editor.div.remove();\n    }\n    this.div = null;\n    __classPrivateFieldGet(this, _AnnotationEditorLayer_editors, \"f\").clear();\n    __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").removeLayer(this);\n  }\n  /**\n   * Render the main editor.\n   * @param {RenderEditorLayerOptions} parameters\n   */\n  render({\n    viewport\n  }) {\n    this.viewport = viewport;\n    setLayerDimensions(this.div, viewport);\n    for (const editor of __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").getEditors(this.pageIndex)) {\n      this.add(editor);\n      editor.rebuild();\n    }\n    // We're maybe rendering a layer which was invisible when we started to edit\n    // so we must set the different callbacks for it.\n    this.updateMode();\n  }\n  // /**\n  //  * Update the main editor.\n  //  * @param {RenderEditorLayerOptions} parameters\n  //  */\n  update({\n    viewport\n  }) {\n    // Editors have their dimensions/positions in percent so to avoid any\n    // issues (see #15582), we must commit the current one before changing\n    // the viewport.\n    __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").commitOrRemove();\n    __classPrivateFieldGet(this, _AnnotationEditorLayer_instances, \"m\", _AnnotationEditorLayer_cleanup).call(this);\n    const oldRotation = this.viewport.rotation;\n    const rotation = viewport.rotation;\n    this.viewport = viewport;\n    // setLayerDimensions(this.div, { rotation });\n    // todo: fix parameters\n    // setLayerDimensions(this.div, { rotation } as PageViewport);\n    setLayerDimensions(this.div, viewport);\n    if (oldRotation !== rotation) {\n      for (const editor of __classPrivateFieldGet(this, _AnnotationEditorLayer_editors, \"f\").values()) {\n        editor.rotate(rotation);\n      }\n    }\n    this.addInkEditorIfNeeded(/* isCommitting = */false);\n  }\n  /**\n   * Get page dimensions.\n   * @returns {Object} dimensions.\n   */\n  get pageDimensions() {\n    const {\n      pageWidth,\n      pageHeight\n    } = this.viewport.rawDims;\n    return [pageWidth, pageHeight];\n  }\n  get scale() {\n    return __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").viewParameters.realScale;\n  }\n}\n_a = AnnotationEditorLayer, _AnnotationEditorLayer_accessibilityManager = new WeakMap(), _AnnotationEditorLayer_allowClick = new WeakMap(), _AnnotationEditorLayer_annotationLayer = new WeakMap(), _AnnotationEditorLayer_clickAC = new WeakMap(), _AnnotationEditorLayer_editorFocusTimeoutId = new WeakMap(), _AnnotationEditorLayer_editors = new WeakMap(), _AnnotationEditorLayer_hadPointerDown = new WeakMap(), _AnnotationEditorLayer_isCleaningUp = new WeakMap(), _AnnotationEditorLayer_isDisabling = new WeakMap(), _AnnotationEditorLayer_textLayer = new WeakMap(), _AnnotationEditorLayer_textSelectionAC = new WeakMap(), _AnnotationEditorLayer_uiManager = new WeakMap(), _AnnotationEditorLayer_instances = new WeakSet(), _AnnotationEditorLayer_textLayerPointerDown = function _AnnotationEditorLayer_textLayerPointerDown(event) {\n  // Unselect all the editors in order to let the user select some text\n  // without being annoyed by an editor toolbar.\n  __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").unselectAll();\n  const {\n    target\n  } = event;\n  if (target === __classPrivateFieldGet(this, _AnnotationEditorLayer_textLayer, \"f\").div || target.classList.contains(\"endOfContent\") && __classPrivateFieldGet(this, _AnnotationEditorLayer_textLayer, \"f\").div.contains(target)) {\n    const {\n      isMac\n    } = FeatureTest.platform;\n    if (event.button !== 0 || event.ctrlKey && isMac) {\n      // Do nothing on right click.\n      return;\n    }\n    __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").showAllEditors(\"highlight\", true\n    // /* updateButton = */ true\n    );\n    // if (this.#uiManager.getMode() === AnnotationEditorType.HIGHLIGHT) {\n    if (__classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").getMode() === AnnotationEditorType.NONE) {\n      // todo: do not continue with free highlight\n      return;\n    }\n    // this.#textLayer.div.classList.add(\"free\");\n    // todo: intentionally prevent free highlight\n    // this.toggleDrawing();\n    // HighlightEditor.startHighlighting(\n    //     this,\n    //     this.#uiManager.direction === \"ltr\",\n    //     event\n    // );\n    __classPrivateFieldGet(this, _AnnotationEditorLayer_textLayer, \"f\").div.addEventListener(\"pointerup\", () => {\n      __classPrivateFieldGet(this, _AnnotationEditorLayer_textLayer, \"f\").div.classList.remove(\"free\");\n      this.toggleDrawing(true);\n    }, {\n      once: true,\n      signal: __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\")._signal\n    });\n    event.preventDefault();\n  }\n}, _AnnotationEditorLayer_currentEditorType_get = function _AnnotationEditorLayer_currentEditorType_get() {\n  return __classPrivateFieldGet(_a, _a, \"f\", _AnnotationEditorLayer_editorTypes).get(__classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").getMode());\n}, _AnnotationEditorLayer_createNewEditor = function _AnnotationEditorLayer_createNewEditor(params) {\n  const editorType = __classPrivateFieldGet(this, _AnnotationEditorLayer_instances, \"a\", _AnnotationEditorLayer_currentEditorType_get);\n  // return editorType ? new editorType.prototype.constructor(params) : null;\n  return editorType ? new editorType(params) : null;\n}, _AnnotationEditorLayer_getCenterPoint = function _AnnotationEditorLayer_getCenterPoint() {\n  const {\n    x,\n    y,\n    width,\n    height\n  } = this.div.getBoundingClientRect();\n  const tlX = Math.max(0, x);\n  const tlY = Math.max(0, y);\n  const brX = Math.min(window.innerWidth, x + width);\n  const brY = Math.min(window.innerHeight, y + height);\n  const centerX = (tlX + brX) / 2 - x;\n  const centerY = (tlY + brY) / 2 - y;\n  const [offsetX, offsetY] = this.viewport.rotation % 180 === 0 ? [centerX, centerY] : [centerY, centerX];\n  return {\n    offsetX,\n    offsetY\n  };\n}, _AnnotationEditorLayer_cleanup = function _AnnotationEditorLayer_cleanup() {\n  // When we're cleaning up, some editors are removed but we don't want\n  // to add a new one which will induce an addition in this.#editors, hence\n  // an infinite loop.\n  __classPrivateFieldSet(this, _AnnotationEditorLayer_isCleaningUp, true, \"f\");\n  for (const editor of __classPrivateFieldGet(this, _AnnotationEditorLayer_editors, \"f\").values()) {\n    if (editor.isEmpty()) {\n      editor.remove();\n    }\n  }\n  __classPrivateFieldSet(this, _AnnotationEditorLayer_isCleaningUp, false, \"f\");\n};\nAnnotationEditorLayer._initialized = false;\n_AnnotationEditorLayer_editorTypes = {\n  value: new Map(\n  // [FreeTextEditor, InkEditor, StampEditor, HighlightEditor].map(type => [\n  [FreeTextEditor, HighlightEditor].map(type => [type._editorType, type]))\n};\nexport { AnnotationEditorLayer };", "map": {"version": 3, "names": ["_AnnotationEditorLayer_instances", "_a", "_AnnotationEditorLayer_accessibilityManager", "_AnnotationEditorLayer_allowClick", "_AnnotationEditorLayer_annotationLayer", "_AnnotationEditorLayer_clickAC", "_AnnotationEditorLayer_editorFocusTimeoutId", "_AnnotationEditorLayer_editors", "_AnnotationE<PERSON>or<PERSON><PERSON>er_hadPointerDown", "_AnnotationEditorLayer_isCleaningUp", "_AnnotationEditorLayer_isDisabling", "_AnnotationEditorLayer_textLayer", "_AnnotationEditorLayer_textSelectionAC", "_AnnotationEditorLayer_uiManager", "_AnnotationEditorLayer_editorTypes", "_AnnotationEditorLayer_textLayerPointerDown", "_AnnotationEditorLayer_currentEditorType_get", "_AnnotationEditorLayer_createNewEditor", "_AnnotationEditorLayer_getCenterPoint", "_AnnotationEditor<PERSON>ayer_cleanup", "__classPrivateFieldGet", "__classPrivateFieldSet", "FreeTextEditor", "AnnotationEditorType", "FeatureTest", "setLayerDimensions", "AnnotationEditor", "HighlightEditor", "AnnotationEditorLayer", "constructor", "uiManager", "pageIndex", "div", "accessibilityManager", "annotationLayer", "draw<PERSON>ayer", "textLayer", "viewport", "add", "set", "Map", "editorTypes", "values", "_initialized", "editorType", "initialize", "registerEditorTypes", "add<PERSON><PERSON>er", "hide", "hidden", "show", "isInvisible", "isEmpty", "size", "getMode", "NONE", "updateToolbar", "updateMode", "mode", "call", "disableTextSelection", "togglePointerEvents", "toggleAnnotationLayerPointerEvents", "disableClick", "INK", "addInkEditorIfNeeded", "HIGHLIGHT", "enableTextSelection", "enableClick", "classList", "toggle", "_type", "_editorType", "hasTextLayer", "_b", "isCommitting", "editor", "setInBackground", "newEditor", "createAndAddNewEditor", "offsetX", "offsetY", "setEditingState", "isEditing", "addCommands", "params", "toggleDrawing", "enabled", "enable", "tabIndex", "annotationElementIds", "Set", "enableEditing", "annotationElementId", "removeChangedExistingAnnotation", "editables", "getEditableAnnotations", "editable", "isDeletedAnnotationElement", "data", "id", "has", "deserialize", "addOrRebuild", "disable", "changedAnnotations", "resetAnnotations", "disableEditing", "serialize", "getEditableAnnotation", "remove", "get", "resetAnnotationElement", "addChangedExistingAnnotation", "renderAnnotationElement", "setActiveEditor", "currentActive", "getActive", "AbortController", "signal", "combinedSignal", "addEventListener", "bind", "abort", "pointerdown", "pointerup", "attach", "removeDeletedAnnotationElement", "detach", "delete", "removePointerInTextLayer", "contentDiv", "addDeletedAnnotationElement", "removeEditor", "isAttachedToDOM", "changeParent", "parent", "deleteAnnotationElement", "setParent", "append", "addEditor", "render", "fixAndSetPosition", "onceAdded", "addToAnnotationStorage", "moveEditorInDOM", "activeElement", "document", "contains", "_focusEventsAllowed", "setTimeout", "once", "_signal", "focus", "_structTreeParentId", "moveElementInDOM", "needsToBeRebuilt", "rebuild", "addUndoableEditor", "cmd", "_uiManager", "undo", "mustExec", "getNextId", "getId", "abortController", "canCreateNewEmptyEditor", "pasteEditor", "Object", "assign", "x", "y", "isCentered", "_c", "annotationType", "annotationEditorType", "event", "addNewEditor", "setSelected", "toggleSelected", "isSelected", "unselect", "isMac", "platform", "button", "ctrl<PERSON>ey", "target", "STAMP", "unselectAll", "find<PERSON><PERSON><PERSON><PERSON><PERSON>", "layer", "findParent", "destroy", "commitOr<PERSON><PERSON>ove", "clearTimeout", "clear", "<PERSON><PERSON><PERSON>er", "getEditors", "update", "oldRotation", "rotation", "rotate", "pageDimensions", "pageWidth", "pageHeight", "rawDims", "scale", "viewParameters", "realScale", "WeakMap", "WeakSet", "showAllEditors", "preventDefault", "width", "height", "getBoundingClientRect", "tlX", "Math", "max", "tlY", "brX", "min", "window", "innerWidth", "brY", "innerHeight", "centerX", "centerY", "value", "map", "type"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-pdfviewer-common/dist/es/annotations/annotation-editor-layer.js"], "sourcesContent": ["/* Copyright 2022 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar _AnnotationEditorLayer_instances, _a, _AnnotationEditorLayer_accessibilityManager, _AnnotationEditorLayer_allowClick, _AnnotationEditorLayer_annotationLayer, _AnnotationEditorLayer_clickAC, _AnnotationEditorLayer_editorFocusTimeoutId, _AnnotationEditorLayer_editors, _AnnotationEditorLayer_hadPointerDown, _AnnotationEditorLayer_isCleaningUp, _AnnotationEditorLayer_isDisabling, _AnnotationEditorLayer_textLayer, _AnnotationEditorLayer_textSelectionAC, _AnnotationEditorLayer_uiManager, _AnnotationEditorLayer_editorTypes, _AnnotationEditorLayer_textLayerPointerDown, _AnnotationEditorLayer_currentEditorType_get, _AnnotationEditorLayer_createNewEditor, _AnnotationEditorLayer_getCenterPoint, _AnnotationEditorLayer_cleanup;\nimport { __classPrivateFieldGet, __classPrivateFieldSet } from \"tslib\";\n/** @typedef {import(\"./tools.js\").AnnotationEditorUIManager} AnnotationEditorUIManager */\n/** @typedef {import(\"../display_utils.js\").PageViewport} PageViewport */\n/** @typedef {import(\"../../../web/text_accessibility.js\").TextAccessibilityManager} TextAccessibilityManager */\n/** @typedef {import(\"../../../web/interfaces\").IL10n} IL10n */\n/** @typedef {import(\"../annotation_layer.js\").AnnotationLayer} AnnotationLayer */\n/** @typedef {import(\"../draw_layer.js\").DrawLayer} DrawLayer */\nimport { FreeTextEditor } from \"./editors/free-text-editor\";\nimport { AnnotationEditorType, FeatureTest, setLayerDimensions } from \"pdfjs-dist/legacy/build/pdf.mjs\";\nimport { AnnotationEditor } from \"./editors/annotation-editor\";\nimport { HighlightEditor } from \"./editors/highlight-editor\";\nclass AnnotationEditorLayer {\n    /**\n     * @param {AnnotationEditorLayerOptions} options\n     */\n    constructor({ uiManager, pageIndex, div, accessibilityManager, annotationLayer, drawLayer, textLayer, viewport\n    // l10n\n     }) {\n        _AnnotationEditorLayer_instances.add(this);\n        // todo: props\n        this.drawLayer = null;\n        this.pageIndex = 0;\n        this.div = null;\n        this.viewport = null;\n        // todo: props\n        _AnnotationEditorLayer_accessibilityManager.set(this, void 0);\n        _AnnotationEditorLayer_allowClick.set(this, false);\n        _AnnotationEditorLayer_annotationLayer.set(this, null);\n        _AnnotationEditorLayer_clickAC.set(this, null);\n        _AnnotationEditorLayer_editorFocusTimeoutId.set(this, null);\n        _AnnotationEditorLayer_editors.set(this, new Map());\n        _AnnotationEditorLayer_hadPointerDown.set(this, false);\n        _AnnotationEditorLayer_isCleaningUp.set(this, false);\n        _AnnotationEditorLayer_isDisabling.set(this, false);\n        _AnnotationEditorLayer_textLayer.set(this, null);\n        _AnnotationEditorLayer_textSelectionAC.set(this, null);\n        _AnnotationEditorLayer_uiManager.set(this, null);\n        const editorTypes = [...__classPrivateFieldGet(_a, _a, \"f\", _AnnotationEditorLayer_editorTypes).values()];\n        if (!_a._initialized) {\n            _a._initialized = true;\n            for (const editorType of editorTypes) {\n                // editorType.initialize(l10n, uiManager);\n                editorType.initialize({}, uiManager);\n            }\n        }\n        uiManager.registerEditorTypes(editorTypes);\n        __classPrivateFieldSet(this, _AnnotationEditorLayer_uiManager, uiManager, \"f\");\n        this.pageIndex = pageIndex;\n        this.div = div;\n        __classPrivateFieldSet(this, _AnnotationEditorLayer_accessibilityManager, accessibilityManager, \"f\");\n        __classPrivateFieldSet(this, _AnnotationEditorLayer_annotationLayer, annotationLayer, \"f\");\n        this.viewport = viewport;\n        __classPrivateFieldSet(this, _AnnotationEditorLayer_textLayer, textLayer, \"f\");\n        this.drawLayer = drawLayer;\n        __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").addLayer(this);\n        // if (!this.#annotationLayer || !this.#textLayer) {\n        // }\n    }\n    // todo: ported from AnnotationEditorLayerBuilder\n    hide() {\n        if (!this.div) {\n            return;\n        }\n        this.div.hidden = true;\n    }\n    show() {\n        if (!this.div || this.isInvisible) {\n            return;\n        }\n        this.div.hidden = false;\n    }\n    // todo: ported from AnnotationEditorLayerBuilder\n    get isEmpty() {\n        return __classPrivateFieldGet(this, _AnnotationEditorLayer_editors, \"f\").size === 0;\n    }\n    get isInvisible() {\n        return (this.isEmpty && __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").getMode() === AnnotationEditorType.NONE);\n    }\n    /**\n     * Update the toolbar if it's required to reflect the tool currently used.\n     * @param {number} mode\n     */\n    // updateToolbar(mode) {\n    updateToolbar() {\n        // this.#uiManager.updateToolbar(mode);\n    }\n    /**\n     * The mode has changed: it must be updated.\n     * @param {number} mode\n     */\n    updateMode(mode = __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").getMode()) {\n        __classPrivateFieldGet(this, _AnnotationEditorLayer_instances, \"m\", _AnnotationEditorLayer_cleanup).call(this);\n        switch (mode) {\n            case AnnotationEditorType.NONE:\n                this.disableTextSelection();\n                this.togglePointerEvents(false);\n                this.toggleAnnotationLayerPointerEvents(true);\n                this.disableClick();\n                return;\n            case AnnotationEditorType.INK:\n                // We always want to have an ink editor ready to draw in.\n                this.addInkEditorIfNeeded(false);\n                this.disableTextSelection();\n                this.togglePointerEvents(true);\n                this.disableClick();\n                break;\n            case AnnotationEditorType.HIGHLIGHT:\n                this.enableTextSelection();\n                this.togglePointerEvents(false);\n                this.disableClick();\n                break;\n            default:\n                this.disableTextSelection();\n                this.togglePointerEvents(true);\n                this.enableClick();\n        }\n        this.toggleAnnotationLayerPointerEvents(false);\n        const { classList } = this.div;\n        for (const editorType of __classPrivateFieldGet(_a, _a, \"f\", _AnnotationEditorLayer_editorTypes).values()) {\n            classList.toggle(`${editorType._type}Editing`, mode === editorType._editorType);\n        }\n        this.div.hidden = false;\n    }\n    hasTextLayer(textLayer) {\n        var _b;\n        return textLayer === ((_b = __classPrivateFieldGet(this, _AnnotationEditorLayer_textLayer, \"f\")) === null || _b === void 0 ? void 0 : _b.div);\n        // return textLayer === this.#textLayer || textLayer === this.#textLayer?.div;\n    }\n    addInkEditorIfNeeded(isCommitting) {\n        if (__classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").getMode() !== AnnotationEditorType.INK) {\n            // We don't want to add an ink editor if we're not in ink mode!\n            return;\n        }\n        if (!isCommitting) {\n            // We're removing an editor but an empty one can already exist so in this\n            // case we don't need to create a new one.\n            for (const editor of __classPrivateFieldGet(this, _AnnotationEditorLayer_editors, \"f\").values()) {\n                if (editor.isEmpty()) {\n                    editor.setInBackground();\n                    return;\n                }\n            }\n        }\n        const newEditor = this.createAndAddNewEditor({ offsetX: 0, offsetY: 0 }, \n        /* isCentered = */ false);\n        newEditor.setInBackground();\n    }\n    /**\n     * Set the editing state.\n     * @param {boolean} isEditing\n     */\n    setEditingState(isEditing) {\n        __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").setEditingState(isEditing);\n    }\n    /**\n     * Add some commands into the CommandManager (undo/redo stuff).\n     * @param {Object} params\n     */\n    addCommands(params) {\n        __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").addCommands(params);\n    }\n    toggleDrawing(enabled = false) {\n        this.div.classList.toggle(\"drawing\", !enabled);\n        // this.div.classList.toggle(\"k-drawing\", !enabled);\n    }\n    togglePointerEvents(enabled = false) {\n        // this.div.classList.toggle(\"disabled\", !enabled);\n        this.div.classList.toggle(\"k-annotation-editor-layer-disabled\", !enabled);\n    }\n    toggleAnnotationLayerPointerEvents(enabled = false) {\n        var _b;\n        // this.#annotationLayer?.div.classList.toggle(\"disabled\", !enabled);\n        (_b = __classPrivateFieldGet(this, _AnnotationEditorLayer_annotationLayer, \"f\")) === null || _b === void 0 ? void 0 : _b.div.classList.toggle(\"k-annotation-editor-layer-disabled\", !enabled);\n    }\n    /**\n     * Enable pointer events on the main div in order to enable\n     * editor creation.\n     */\n    enable() {\n        this.div.tabIndex = 0;\n        this.togglePointerEvents(true);\n        const annotationElementIds = new Set();\n        for (const editor of __classPrivateFieldGet(this, _AnnotationEditorLayer_editors, \"f\").values()) {\n            editor.enableEditing();\n            editor.show(true);\n            if (editor.annotationElementId) {\n                __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").removeChangedExistingAnnotation(editor);\n                annotationElementIds.add(editor.annotationElementId);\n            }\n        }\n        if (!__classPrivateFieldGet(this, _AnnotationEditorLayer_annotationLayer, \"f\")) {\n            return;\n        }\n        const editables = __classPrivateFieldGet(this, _AnnotationEditorLayer_annotationLayer, \"f\").getEditableAnnotations();\n        for (const editable of editables) {\n            // The element must be hidden whatever its state is.\n            editable.hide();\n            if (__classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").isDeletedAnnotationElement(editable.data.id)) {\n                continue;\n            }\n            if (annotationElementIds.has(editable.data.id)) {\n                continue;\n            }\n            const editor = this.deserialize(editable);\n            if (!editor) {\n                continue;\n            }\n            this.addOrRebuild(editor);\n            editor.enableEditing();\n        }\n    }\n    /**\n     * Disable editor creation.\n     */\n    disable() {\n        var _b;\n        __classPrivateFieldSet(this, _AnnotationEditorLayer_isDisabling, true, \"f\");\n        this.div.tabIndex = -1;\n        this.togglePointerEvents(false);\n        const changedAnnotations = new Map();\n        const resetAnnotations = new Map();\n        for (const editor of __classPrivateFieldGet(this, _AnnotationEditorLayer_editors, \"f\").values()) {\n            editor.disableEditing();\n            if (!editor.annotationElementId) {\n                continue;\n            }\n            if (editor.serialize() !== null) {\n                changedAnnotations.set(editor.annotationElementId, editor);\n                continue;\n            }\n            else {\n                resetAnnotations.set(editor.annotationElementId, editor);\n            }\n            (_b = this.getEditableAnnotation(editor.annotationElementId)) === null || _b === void 0 ? void 0 : _b.show();\n            editor.remove();\n        }\n        if (__classPrivateFieldGet(this, _AnnotationEditorLayer_annotationLayer, \"f\")) {\n            // Show the annotations that were hidden in enable().\n            const editables = __classPrivateFieldGet(this, _AnnotationEditorLayer_annotationLayer, \"f\").getEditableAnnotations();\n            for (const editable of editables) {\n                const { id } = editable.data;\n                if (__classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").isDeletedAnnotationElement(id)) {\n                    continue;\n                }\n                let editor = resetAnnotations.get(id);\n                if (editor) {\n                    editor.resetAnnotationElement(editable);\n                    editor.show(false);\n                    editable.show();\n                    continue;\n                }\n                editor = changedAnnotations.get(id);\n                if (editor) {\n                    __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").addChangedExistingAnnotation(editor);\n                    editor.renderAnnotationElement(editable);\n                    editor.show(false);\n                }\n                editable.show();\n            }\n        }\n        __classPrivateFieldGet(this, _AnnotationEditorLayer_instances, \"m\", _AnnotationEditorLayer_cleanup).call(this);\n        if (this.isEmpty) {\n            this.div.hidden = true;\n        }\n        const { classList } = this.div;\n        for (const editorType of __classPrivateFieldGet(_a, _a, \"f\", _AnnotationEditorLayer_editorTypes).values()) {\n            classList.remove(`${editorType._type}Editing`);\n        }\n        this.disableTextSelection();\n        this.toggleAnnotationLayerPointerEvents(true);\n        __classPrivateFieldSet(this, _AnnotationEditorLayer_isDisabling, false, \"f\");\n    }\n    getEditableAnnotation(id) {\n        var _b;\n        return ((_b = __classPrivateFieldGet(this, _AnnotationEditorLayer_annotationLayer, \"f\")) === null || _b === void 0 ? void 0 : _b.getEditableAnnotation(id)) || null;\n    }\n    /**\n     * Set the current editor.\n     * @param {AnnotationEditor} editor\n     */\n    setActiveEditor(editor) {\n        const currentActive = __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").getActive();\n        if (currentActive === editor) {\n            return;\n        }\n        __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").setActiveEditor(editor);\n    }\n    enableTextSelection() {\n        var _b;\n        this.div.tabIndex = -1;\n        if (((_b = __classPrivateFieldGet(this, _AnnotationEditorLayer_textLayer, \"f\")) === null || _b === void 0 ? void 0 : _b.div) && !__classPrivateFieldGet(this, _AnnotationEditorLayer_textSelectionAC, \"f\")) {\n            __classPrivateFieldSet(this, _AnnotationEditorLayer_textSelectionAC, new AbortController(), \"f\");\n            const signal = __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").combinedSignal(__classPrivateFieldGet(this, _AnnotationEditorLayer_textSelectionAC, \"f\"));\n            __classPrivateFieldGet(this, _AnnotationEditorLayer_textLayer, \"f\").div.addEventListener(\"pointerdown\", __classPrivateFieldGet(this, _AnnotationEditorLayer_instances, \"m\", _AnnotationEditorLayer_textLayerPointerDown).bind(this), { signal });\n            // this.#textLayer.div.classList.add(\"highlighting\");\n        }\n    }\n    disableTextSelection() {\n        var _b;\n        this.div.tabIndex = 0;\n        if (((_b = __classPrivateFieldGet(this, _AnnotationEditorLayer_textLayer, \"f\")) === null || _b === void 0 ? void 0 : _b.div) && __classPrivateFieldGet(this, _AnnotationEditorLayer_textSelectionAC, \"f\")) {\n            __classPrivateFieldGet(this, _AnnotationEditorLayer_textSelectionAC, \"f\").abort();\n            __classPrivateFieldSet(this, _AnnotationEditorLayer_textSelectionAC, null, \"f\");\n            __classPrivateFieldGet(this, _AnnotationEditorLayer_textLayer, \"f\").div.classList.remove(\"highlighting\");\n        }\n    }\n    enableClick() {\n        if (__classPrivateFieldGet(this, _AnnotationEditorLayer_clickAC, \"f\")) {\n            return;\n        }\n        __classPrivateFieldSet(this, _AnnotationEditorLayer_clickAC, new AbortController(), \"f\");\n        const signal = __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").combinedSignal(__classPrivateFieldGet(this, _AnnotationEditorLayer_clickAC, \"f\"));\n        this.div.addEventListener(\"pointerdown\", this.pointerdown.bind(this), {\n            signal\n        });\n        this.div.addEventListener(\"pointerup\", this.pointerup.bind(this), {\n            signal\n        });\n    }\n    disableClick() {\n        var _b;\n        (_b = __classPrivateFieldGet(this, _AnnotationEditorLayer_clickAC, \"f\")) === null || _b === void 0 ? void 0 : _b.abort();\n        __classPrivateFieldSet(this, _AnnotationEditorLayer_clickAC, null, \"f\");\n    }\n    attach(editor) {\n        __classPrivateFieldGet(this, _AnnotationEditorLayer_editors, \"f\").set(editor.id, editor);\n        const { annotationElementId } = editor;\n        if (annotationElementId &&\n            __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").isDeletedAnnotationElement(annotationElementId)) {\n            __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").removeDeletedAnnotationElement(editor);\n        }\n    }\n    detach(editor) {\n        var _b;\n        __classPrivateFieldGet(this, _AnnotationEditorLayer_editors, \"f\").delete(editor.id);\n        (_b = __classPrivateFieldGet(this, _AnnotationEditorLayer_accessibilityManager, \"f\")) === null || _b === void 0 ? void 0 : _b.removePointerInTextLayer(editor.contentDiv);\n        if (!__classPrivateFieldGet(this, _AnnotationEditorLayer_isDisabling, \"f\") && editor.annotationElementId) {\n            __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").addDeletedAnnotationElement(editor);\n        }\n    }\n    /**\n     * Remove an editor.\n     * @param {AnnotationEditor} editor\n     */\n    remove(editor) {\n        this.detach(editor);\n        __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").removeEditor(editor);\n        editor.div.remove();\n        editor.isAttachedToDOM = false;\n        if (!__classPrivateFieldGet(this, _AnnotationEditorLayer_isCleaningUp, \"f\")) {\n            this.addInkEditorIfNeeded(/* isCommitting = */ false);\n        }\n    }\n    /**\n     * An editor can have a different parent, for example after having\n     * being dragged and droped from a page to another.\n     * @param {AnnotationEditor} editor\n     */\n    changeParent(editor) {\n        var _b;\n        if (editor.parent === this) {\n            return;\n        }\n        if (editor.parent && editor.annotationElementId) {\n            __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").addDeletedAnnotationElement(editor.annotationElementId);\n            AnnotationEditor.deleteAnnotationElement(editor);\n            editor.annotationElementId = null;\n        }\n        this.attach(editor);\n        (_b = editor.parent) === null || _b === void 0 ? void 0 : _b.detach(editor);\n        editor.setParent(this);\n        if (editor.div && editor.isAttachedToDOM) {\n            editor.div.remove();\n            this.div.append(editor.div);\n        }\n    }\n    /**\n     * Add a new editor in the current view.\n     * @param {AnnotationEditor} editor\n     */\n    add(editor) {\n        if (editor.parent === this && editor.isAttachedToDOM) {\n            return;\n        }\n        this.changeParent(editor);\n        __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").addEditor(editor);\n        this.attach(editor);\n        if (!editor.isAttachedToDOM) {\n            const div = editor.render();\n            this.div.append(div);\n            editor.isAttachedToDOM = true;\n        }\n        // The editor will be correctly moved into the DOM (see fixAndSetPosition).\n        editor.fixAndSetPosition();\n        editor.onceAdded();\n        __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").addToAnnotationStorage(editor);\n        // editor._reportTelemetry(editor.telemetryInitialData);\n        // todo: this differs from pdf.js\n        // this.setActiveEditor(editor);\n    }\n    moveEditorInDOM(editor) {\n        var _b;\n        if (!editor.isAttachedToDOM) {\n            return;\n        }\n        const { activeElement } = document;\n        if (editor.div.contains(activeElement) && !__classPrivateFieldGet(this, _AnnotationEditorLayer_editorFocusTimeoutId, \"f\")) {\n            // When the div is moved in the DOM the focus can move somewhere else,\n            // so we want to be sure that the focus will stay on the editor but we\n            // don't want to call any focus callbacks, hence we disable them and only\n            // re-enable them when the editor has the focus.\n            editor._focusEventsAllowed = false;\n            __classPrivateFieldSet(this, _AnnotationEditorLayer_editorFocusTimeoutId, setTimeout(() => {\n                __classPrivateFieldSet(this, _AnnotationEditorLayer_editorFocusTimeoutId, null, \"f\");\n                if (!editor.div.contains(document.activeElement)) {\n                    editor.div.addEventListener(\"focusin\", () => {\n                        editor._focusEventsAllowed = true;\n                    }, { once: true, signal: __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\")._signal });\n                    activeElement.focus();\n                }\n                else {\n                    editor._focusEventsAllowed = true;\n                }\n            }, 0), \"f\");\n        }\n        editor._structTreeParentId = (_b = __classPrivateFieldGet(this, _AnnotationEditorLayer_accessibilityManager, \"f\")) === null || _b === void 0 ? void 0 : _b.moveElementInDOM(this.div, editor.div, editor.contentDiv, \n        /* isRemovable = */ true);\n    }\n    /**\n     * Add or rebuild depending if it has been removed or not.\n     * @param {AnnotationEditor} editor\n     */\n    addOrRebuild(editor) {\n        if (editor.needsToBeRebuilt()) {\n            editor.parent || (editor.parent = this);\n            editor.rebuild();\n            editor.show();\n        }\n        else {\n            this.add(editor);\n        }\n    }\n    /**\n     * Add a new editor and make this addition undoable.\n     * @param {AnnotationEditor} editor\n     */\n    addUndoableEditor(editor) {\n        const cmd = () => editor._uiManager.rebuild(editor);\n        const undo = () => {\n            editor.remove();\n        };\n        this.addCommands({ cmd, undo, mustExec: false });\n    }\n    /**\n     * Get an id for an editor.\n     * @returns {string}\n     */\n    getNextId() {\n        return __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").getId();\n    }\n    combinedSignal(abortController) {\n        return __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").combinedSignal(abortController);\n    }\n    canCreateNewEmptyEditor() {\n        var _b;\n        return (_b = __classPrivateFieldGet(this, _AnnotationEditorLayer_instances, \"a\", _AnnotationEditorLayer_currentEditorType_get)) === null || _b === void 0 ? void 0 : _b.canCreateNewEmptyEditor();\n    }\n    /**\n     * Paste some content into a new editor.\n     * @param {number} mode\n     * @param {Object} params\n     */\n    pasteEditor(mode, params) {\n        __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").updateToolbar(mode);\n        __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").updateMode(mode);\n        const { offsetX, offsetY } = __classPrivateFieldGet(this, _AnnotationEditorLayer_instances, \"m\", _AnnotationEditorLayer_getCenterPoint).call(this);\n        const id = this.getNextId();\n        const editor = __classPrivateFieldGet(this, _AnnotationEditorLayer_instances, \"m\", _AnnotationEditorLayer_createNewEditor).call(this, Object.assign({ parent: this, id, x: offsetX, y: offsetY, uiManager: __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\"), isCentered: true }, params));\n        if (editor) {\n            this.add(editor);\n        }\n    }\n    /**\n     * Create a new editor\n     * @param {Object} data\n     * @returns {AnnotationEditor | null}\n     */\n    deserialize(data) {\n        var _b, _c;\n        return (((_c = __classPrivateFieldGet(_a, _a, \"f\", _AnnotationEditorLayer_editorTypes)\n            .get((_b = data.annotationType) !== null && _b !== void 0 ? _b : data.annotationEditorType)) === null || _c === void 0 ? void 0 : _c.deserialize(data, this, __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\"))) || null);\n    }\n    /**\n     * Create and add a new editor.\n     * @param {PointerEvent} event\n     * @param {boolean} isCentered\n     * @param [Object] data\n     * @returns {AnnotationEditor}\n     */\n    createAndAddNewEditor(event, isCentered, data = {}) {\n        const id = this.getNextId();\n        const editor = __classPrivateFieldGet(this, _AnnotationEditorLayer_instances, \"m\", _AnnotationEditorLayer_createNewEditor).call(this, Object.assign({ parent: this, id, x: event.offsetX, y: event.offsetY, uiManager: __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\"), isCentered }, data));\n        if (editor) {\n            this.add(editor);\n        }\n        return editor;\n    }\n    /**\n     * Create and add a new editor.\n     */\n    addNewEditor() {\n        this.createAndAddNewEditor(__classPrivateFieldGet(this, _AnnotationEditorLayer_instances, \"m\", _AnnotationEditorLayer_getCenterPoint).call(this), /* isCentered = */ true);\n    }\n    /**\n     * Set the last selected editor.\n     * @param {AnnotationEditor} editor\n     */\n    setSelected(editor) {\n        __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").setSelected(editor);\n    }\n    /**\n     * Add or remove an editor the current selection.\n     * @param {AnnotationEditor} editor\n     */\n    toggleSelected(editor) {\n        __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").toggleSelected(editor);\n    }\n    /**\n     * Check if the editor is selected.\n     * @param {AnnotationEditor} editor\n     */\n    isSelected(editor) {\n        return __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").isSelected(editor);\n    }\n    /**\n     * Unselect an editor.\n     * @param {AnnotationEditor} editor\n     */\n    unselect(editor) {\n        __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").unselect(editor);\n    }\n    /**\n     * Pointerup callback.\n     * @param {PointerEvent} event\n     */\n    pointerup(event) {\n        const { isMac } = FeatureTest.platform;\n        if (event.button !== 0 || (event.ctrlKey && isMac)) {\n            // Don't create an editor on right click.\n            return;\n        }\n        if (event.target !== this.div) {\n            return;\n        }\n        if (!__classPrivateFieldGet(this, _AnnotationEditorLayer_hadPointerDown, \"f\")) {\n            // It can happen when the user starts a drag inside a text editor\n            // and then releases the mouse button outside of it. In such a case\n            // we don't want to create a new editor, hence we check that a pointerdown\n            // occurred on this div previously.\n            return;\n        }\n        __classPrivateFieldSet(this, _AnnotationEditorLayer_hadPointerDown, false, \"f\");\n        if (!__classPrivateFieldGet(this, _AnnotationEditorLayer_allowClick, \"f\")) {\n            __classPrivateFieldSet(this, _AnnotationEditorLayer_allowClick, true, \"f\");\n            return;\n        }\n        if (__classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").getMode() === AnnotationEditorType.STAMP) {\n            __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").unselectAll();\n            return;\n        }\n        this.createAndAddNewEditor(event, /* isCentered = */ false);\n    }\n    /**\n     * Pointerdown callback.\n     * @param {PointerEvent} event\n     */\n    pointerdown(event) {\n        if (__classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").getMode() === AnnotationEditorType.HIGHLIGHT) {\n            this.enableTextSelection();\n        }\n        if (__classPrivateFieldGet(this, _AnnotationEditorLayer_hadPointerDown, \"f\")) {\n            // It's possible to have a second pointerdown event before a pointerup one\n            // when the user puts a finger on a touchscreen and then add a second one\n            // to start a pinch-to-zoom gesture.\n            // That said, in case it's possible to have two pointerdown events with\n            // a mouse, we don't want to create a new editor in such a case either.\n            __classPrivateFieldSet(this, _AnnotationEditorLayer_hadPointerDown, false, \"f\");\n            return;\n        }\n        const { isMac } = FeatureTest.platform;\n        if (event.button !== 0 || (event.ctrlKey && isMac)) {\n            // Do nothing on right click.\n            return;\n        }\n        if (event.target !== this.div) {\n            return;\n        }\n        __classPrivateFieldSet(this, _AnnotationEditorLayer_hadPointerDown, true, \"f\");\n        const editor = __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").getActive();\n        __classPrivateFieldSet(this, _AnnotationEditorLayer_allowClick, !editor || editor.isEmpty(), \"f\");\n    }\n    /**\n     *\n     * @param {AnnotationEditor} editor\n     * @param {number} x\n     * @param {number} y\n     * @returns\n     */\n    findNewParent(editor, x, y) {\n        const layer = __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").findParent(x, y);\n        if (layer === null || layer === this) {\n            return false;\n        }\n        layer.changeParent(editor);\n        return true;\n    }\n    /**\n     * Destroy the main editor.\n     */\n    destroy() {\n        var _b, _c;\n        if (((_b = __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").getActive()) === null || _b === void 0 ? void 0 : _b.parent) === this) {\n            // We need to commit the current editor before destroying the layer.\n            __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").commitOrRemove();\n            __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").setActiveEditor(null);\n        }\n        if (__classPrivateFieldGet(this, _AnnotationEditorLayer_editorFocusTimeoutId, \"f\")) {\n            clearTimeout(__classPrivateFieldGet(this, _AnnotationEditorLayer_editorFocusTimeoutId, \"f\"));\n            __classPrivateFieldSet(this, _AnnotationEditorLayer_editorFocusTimeoutId, null, \"f\");\n        }\n        for (const editor of __classPrivateFieldGet(this, _AnnotationEditorLayer_editors, \"f\").values()) {\n            (_c = __classPrivateFieldGet(this, _AnnotationEditorLayer_accessibilityManager, \"f\")) === null || _c === void 0 ? void 0 : _c.removePointerInTextLayer(editor.contentDiv);\n            editor.setParent(null);\n            editor.isAttachedToDOM = false;\n            editor.div.remove();\n        }\n        this.div = null;\n        __classPrivateFieldGet(this, _AnnotationEditorLayer_editors, \"f\").clear();\n        __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").removeLayer(this);\n    }\n    /**\n     * Render the main editor.\n     * @param {RenderEditorLayerOptions} parameters\n     */\n    render({ viewport }) {\n        this.viewport = viewport;\n        setLayerDimensions(this.div, viewport);\n        for (const editor of __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").getEditors(this.pageIndex)) {\n            this.add(editor);\n            editor.rebuild();\n        }\n        // We're maybe rendering a layer which was invisible when we started to edit\n        // so we must set the different callbacks for it.\n        this.updateMode();\n    }\n    // /**\n    //  * Update the main editor.\n    //  * @param {RenderEditorLayerOptions} parameters\n    //  */\n    update({ viewport }) {\n        // Editors have their dimensions/positions in percent so to avoid any\n        // issues (see #15582), we must commit the current one before changing\n        // the viewport.\n        __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").commitOrRemove();\n        __classPrivateFieldGet(this, _AnnotationEditorLayer_instances, \"m\", _AnnotationEditorLayer_cleanup).call(this);\n        const oldRotation = this.viewport.rotation;\n        const rotation = viewport.rotation;\n        this.viewport = viewport;\n        // setLayerDimensions(this.div, { rotation });\n        // todo: fix parameters\n        // setLayerDimensions(this.div, { rotation } as PageViewport);\n        setLayerDimensions(this.div, viewport);\n        if (oldRotation !== rotation) {\n            for (const editor of __classPrivateFieldGet(this, _AnnotationEditorLayer_editors, \"f\").values()) {\n                editor.rotate(rotation);\n            }\n        }\n        this.addInkEditorIfNeeded(/* isCommitting = */ false);\n    }\n    /**\n     * Get page dimensions.\n     * @returns {Object} dimensions.\n     */\n    get pageDimensions() {\n        const { pageWidth, pageHeight } = this.viewport.rawDims;\n        return [pageWidth, pageHeight];\n    }\n    get scale() {\n        return __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").viewParameters.realScale;\n    }\n}\n_a = AnnotationEditorLayer, _AnnotationEditorLayer_accessibilityManager = new WeakMap(), _AnnotationEditorLayer_allowClick = new WeakMap(), _AnnotationEditorLayer_annotationLayer = new WeakMap(), _AnnotationEditorLayer_clickAC = new WeakMap(), _AnnotationEditorLayer_editorFocusTimeoutId = new WeakMap(), _AnnotationEditorLayer_editors = new WeakMap(), _AnnotationEditorLayer_hadPointerDown = new WeakMap(), _AnnotationEditorLayer_isCleaningUp = new WeakMap(), _AnnotationEditorLayer_isDisabling = new WeakMap(), _AnnotationEditorLayer_textLayer = new WeakMap(), _AnnotationEditorLayer_textSelectionAC = new WeakMap(), _AnnotationEditorLayer_uiManager = new WeakMap(), _AnnotationEditorLayer_instances = new WeakSet(), _AnnotationEditorLayer_textLayerPointerDown = function _AnnotationEditorLayer_textLayerPointerDown(event) {\n    // Unselect all the editors in order to let the user select some text\n    // without being annoyed by an editor toolbar.\n    __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").unselectAll();\n    const { target } = event;\n    if (target === __classPrivateFieldGet(this, _AnnotationEditorLayer_textLayer, \"f\").div ||\n        (target.classList.contains(\"endOfContent\") && __classPrivateFieldGet(this, _AnnotationEditorLayer_textLayer, \"f\").div.contains(target))) {\n        const { isMac } = FeatureTest.platform;\n        if (event.button !== 0 || (event.ctrlKey && isMac)) {\n            // Do nothing on right click.\n            return;\n        }\n        __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").showAllEditors(\"highlight\", true\n        // /* updateButton = */ true\n        );\n        // if (this.#uiManager.getMode() === AnnotationEditorType.HIGHLIGHT) {\n        if (__classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").getMode() === AnnotationEditorType.NONE) {\n            // todo: do not continue with free highlight\n            return;\n        }\n        // this.#textLayer.div.classList.add(\"free\");\n        // todo: intentionally prevent free highlight\n        // this.toggleDrawing();\n        // HighlightEditor.startHighlighting(\n        //     this,\n        //     this.#uiManager.direction === \"ltr\",\n        //     event\n        // );\n        __classPrivateFieldGet(this, _AnnotationEditorLayer_textLayer, \"f\").div.addEventListener(\"pointerup\", () => {\n            __classPrivateFieldGet(this, _AnnotationEditorLayer_textLayer, \"f\").div.classList.remove(\"free\");\n            this.toggleDrawing(true);\n        }, { once: true, signal: __classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\")._signal });\n        event.preventDefault();\n    }\n}, _AnnotationEditorLayer_currentEditorType_get = function _AnnotationEditorLayer_currentEditorType_get() {\n    return __classPrivateFieldGet(_a, _a, \"f\", _AnnotationEditorLayer_editorTypes).get(__classPrivateFieldGet(this, _AnnotationEditorLayer_uiManager, \"f\").getMode());\n}, _AnnotationEditorLayer_createNewEditor = function _AnnotationEditorLayer_createNewEditor(params) {\n    const editorType = __classPrivateFieldGet(this, _AnnotationEditorLayer_instances, \"a\", _AnnotationEditorLayer_currentEditorType_get);\n    // return editorType ? new editorType.prototype.constructor(params) : null;\n    return editorType ? new editorType(params) : null;\n}, _AnnotationEditorLayer_getCenterPoint = function _AnnotationEditorLayer_getCenterPoint() {\n    const { x, y, width, height } = this.div.getBoundingClientRect();\n    const tlX = Math.max(0, x);\n    const tlY = Math.max(0, y);\n    const brX = Math.min(window.innerWidth, x + width);\n    const brY = Math.min(window.innerHeight, y + height);\n    const centerX = (tlX + brX) / 2 - x;\n    const centerY = (tlY + brY) / 2 - y;\n    const [offsetX, offsetY] = this.viewport.rotation % 180 === 0\n        ? [centerX, centerY]\n        : [centerY, centerX];\n    return { offsetX, offsetY };\n}, _AnnotationEditorLayer_cleanup = function _AnnotationEditorLayer_cleanup() {\n    // When we're cleaning up, some editors are removed but we don't want\n    // to add a new one which will induce an addition in this.#editors, hence\n    // an infinite loop.\n    __classPrivateFieldSet(this, _AnnotationEditorLayer_isCleaningUp, true, \"f\");\n    for (const editor of __classPrivateFieldGet(this, _AnnotationEditorLayer_editors, \"f\").values()) {\n        if (editor.isEmpty()) {\n            editor.remove();\n        }\n    }\n    __classPrivateFieldSet(this, _AnnotationEditorLayer_isCleaningUp, false, \"f\");\n};\nAnnotationEditorLayer._initialized = false;\n_AnnotationEditorLayer_editorTypes = { value: new Map(\n    // [FreeTextEditor, InkEditor, StampEditor, HighlightEditor].map(type => [\n    [FreeTextEditor, HighlightEditor].map(type => [\n        type._editorType,\n        type\n    ])) };\nexport { AnnotationEditorLayer };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,gCAAgC,EAAEC,EAAE,EAAEC,2CAA2C,EAAEC,iCAAiC,EAAEC,sCAAsC,EAAEC,8BAA8B,EAAEC,2CAA2C,EAAEC,8BAA8B,EAAEC,qCAAqC,EAAEC,mCAAmC,EAAEC,kCAAkC,EAAEC,gCAAgC,EAAEC,sCAAsC,EAAEC,gCAAgC,EAAEC,kCAAkC,EAAEC,2CAA2C,EAAEC,4CAA4C,EAAEC,sCAAsC,EAAEC,qCAAqC,EAAEC,8BAA8B;AACvtB,SAASC,sBAAsB,EAAEC,sBAAsB,QAAQ,OAAO;AACtE;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,oBAAoB,EAAEC,WAAW,EAAEC,kBAAkB,QAAQ,iCAAiC;AACvG,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,MAAMC,qBAAqB,CAAC;EACxB;AACJ;AACA;EACIC,WAAWA,CAAC;IAAEC,SAAS;IAAEC,SAAS;IAAEC,GAAG;IAAEC,oBAAoB;IAAEC,eAAe;IAAEC,SAAS;IAAEC,SAAS;IAAEC;IACtG;EACC,CAAC,EAAE;IACArC,gCAAgC,CAACsC,GAAG,CAAC,IAAI,CAAC;IAC1C;IACA,IAAI,CAACH,SAAS,GAAG,IAAI;IACrB,IAAI,CAACJ,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,GAAG,GAAG,IAAI;IACf,IAAI,CAACK,QAAQ,GAAG,IAAI;IACpB;IACAnC,2CAA2C,CAACqC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC7DpC,iCAAiC,CAACoC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC;IAClDnC,sCAAsC,CAACmC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IACtDlC,8BAA8B,CAACkC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAC9CjC,2CAA2C,CAACiC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAC3DhC,8BAA8B,CAACgC,GAAG,CAAC,IAAI,EAAE,IAAIC,GAAG,CAAC,CAAC,CAAC;IACnDhC,qCAAqC,CAAC+B,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC;IACtD9B,mCAAmC,CAAC8B,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC;IACpD7B,kCAAkC,CAAC6B,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC;IACnD5B,gCAAgC,CAAC4B,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAChD3B,sCAAsC,CAAC2B,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IACtD1B,gCAAgC,CAAC0B,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAChD,MAAME,WAAW,GAAG,CAAC,GAAGrB,sBAAsB,CAACnB,EAAE,EAAEA,EAAE,EAAE,GAAG,EAAEa,kCAAkC,CAAC,CAAC4B,MAAM,CAAC,CAAC,CAAC;IACzG,IAAI,CAACzC,EAAE,CAAC0C,YAAY,EAAE;MAClB1C,EAAE,CAAC0C,YAAY,GAAG,IAAI;MACtB,KAAK,MAAMC,UAAU,IAAIH,WAAW,EAAE;QAClC;QACAG,UAAU,CAACC,UAAU,CAAC,CAAC,CAAC,EAAEf,SAAS,CAAC;MACxC;IACJ;IACAA,SAAS,CAACgB,mBAAmB,CAACL,WAAW,CAAC;IAC1CpB,sBAAsB,CAAC,IAAI,EAAER,gCAAgC,EAAEiB,SAAS,EAAE,GAAG,CAAC;IAC9E,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,GAAG,GAAGA,GAAG;IACdX,sBAAsB,CAAC,IAAI,EAAEnB,2CAA2C,EAAE+B,oBAAoB,EAAE,GAAG,CAAC;IACpGZ,sBAAsB,CAAC,IAAI,EAAEjB,sCAAsC,EAAE8B,eAAe,EAAE,GAAG,CAAC;IAC1F,IAAI,CAACG,QAAQ,GAAGA,QAAQ;IACxBhB,sBAAsB,CAAC,IAAI,EAAEV,gCAAgC,EAAEyB,SAAS,EAAE,GAAG,CAAC;IAC9E,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1Bf,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACkC,QAAQ,CAAC,IAAI,CAAC;IAClF;IACA;EACJ;EACA;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAAChB,GAAG,EAAE;MACX;IACJ;IACA,IAAI,CAACA,GAAG,CAACiB,MAAM,GAAG,IAAI;EAC1B;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAAClB,GAAG,IAAI,IAAI,CAACmB,WAAW,EAAE;MAC/B;IACJ;IACA,IAAI,CAACnB,GAAG,CAACiB,MAAM,GAAG,KAAK;EAC3B;EACA;EACA,IAAIG,OAAOA,CAAA,EAAG;IACV,OAAOhC,sBAAsB,CAAC,IAAI,EAAEb,8BAA8B,EAAE,GAAG,CAAC,CAAC8C,IAAI,KAAK,CAAC;EACvF;EACA,IAAIF,WAAWA,CAAA,EAAG;IACd,OAAQ,IAAI,CAACC,OAAO,IAAIhC,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACyC,OAAO,CAAC,CAAC,KAAK/B,oBAAoB,CAACgC,IAAI;EACvI;EACA;AACJ;AACA;AACA;EACI;EACAC,aAAaA,CAAA,EAAG;IACZ;EAAA;EAEJ;AACJ;AACA;AACA;EACIC,UAAUA,CAACC,IAAI,GAAGtC,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACyC,OAAO,CAAC,CAAC,EAAE;IAC7FlC,sBAAsB,CAAC,IAAI,EAAEpB,gCAAgC,EAAE,GAAG,EAAEmB,8BAA8B,CAAC,CAACwC,IAAI,CAAC,IAAI,CAAC;IAC9G,QAAQD,IAAI;MACR,KAAKnC,oBAAoB,CAACgC,IAAI;QAC1B,IAAI,CAACK,oBAAoB,CAAC,CAAC;QAC3B,IAAI,CAACC,mBAAmB,CAAC,KAAK,CAAC;QAC/B,IAAI,CAACC,kCAAkC,CAAC,IAAI,CAAC;QAC7C,IAAI,CAACC,YAAY,CAAC,CAAC;QACnB;MACJ,KAAKxC,oBAAoB,CAACyC,GAAG;QACzB;QACA,IAAI,CAACC,oBAAoB,CAAC,KAAK,CAAC;QAChC,IAAI,CAACL,oBAAoB,CAAC,CAAC;QAC3B,IAAI,CAACC,mBAAmB,CAAC,IAAI,CAAC;QAC9B,IAAI,CAACE,YAAY,CAAC,CAAC;QACnB;MACJ,KAAKxC,oBAAoB,CAAC2C,SAAS;QAC/B,IAAI,CAACC,mBAAmB,CAAC,CAAC;QAC1B,IAAI,CAACN,mBAAmB,CAAC,KAAK,CAAC;QAC/B,IAAI,CAACE,YAAY,CAAC,CAAC;QACnB;MACJ;QACI,IAAI,CAACH,oBAAoB,CAAC,CAAC;QAC3B,IAAI,CAACC,mBAAmB,CAAC,IAAI,CAAC;QAC9B,IAAI,CAACO,WAAW,CAAC,CAAC;IAC1B;IACA,IAAI,CAACN,kCAAkC,CAAC,KAAK,CAAC;IAC9C,MAAM;MAAEO;IAAU,CAAC,GAAG,IAAI,CAACrC,GAAG;IAC9B,KAAK,MAAMY,UAAU,IAAIxB,sBAAsB,CAACnB,EAAE,EAAEA,EAAE,EAAE,GAAG,EAAEa,kCAAkC,CAAC,CAAC4B,MAAM,CAAC,CAAC,EAAE;MACvG2B,SAAS,CAACC,MAAM,CAAC,GAAG1B,UAAU,CAAC2B,KAAK,SAAS,EAAEb,IAAI,KAAKd,UAAU,CAAC4B,WAAW,CAAC;IACnF;IACA,IAAI,CAACxC,GAAG,CAACiB,MAAM,GAAG,KAAK;EAC3B;EACAwB,YAAYA,CAACrC,SAAS,EAAE;IACpB,IAAIsC,EAAE;IACN,OAAOtC,SAAS,MAAM,CAACsC,EAAE,GAAGtD,sBAAsB,CAAC,IAAI,EAAET,gCAAgC,EAAE,GAAG,CAAC,MAAM,IAAI,IAAI+D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC1C,GAAG,CAAC;IAC7I;EACJ;EACAiC,oBAAoBA,CAACU,YAAY,EAAE;IAC/B,IAAIvD,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACyC,OAAO,CAAC,CAAC,KAAK/B,oBAAoB,CAACyC,GAAG,EAAE;MAC5G;MACA;IACJ;IACA,IAAI,CAACW,YAAY,EAAE;MACf;MACA;MACA,KAAK,MAAMC,MAAM,IAAIxD,sBAAsB,CAAC,IAAI,EAAEb,8BAA8B,EAAE,GAAG,CAAC,CAACmC,MAAM,CAAC,CAAC,EAAE;QAC7F,IAAIkC,MAAM,CAACxB,OAAO,CAAC,CAAC,EAAE;UAClBwB,MAAM,CAACC,eAAe,CAAC,CAAC;UACxB;QACJ;MACJ;IACJ;IACA,MAAMC,SAAS,GAAG,IAAI,CAACC,qBAAqB,CAAC;MAAEC,OAAO,EAAE,CAAC;MAAEC,OAAO,EAAE;IAAE,CAAC,EACvE,kBAAmB,KAAK,CAAC;IACzBH,SAAS,CAACD,eAAe,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;EACIK,eAAeA,CAACC,SAAS,EAAE;IACvB/D,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACqE,eAAe,CAACC,SAAS,CAAC;EAClG;EACA;AACJ;AACA;AACA;EACIC,WAAWA,CAACC,MAAM,EAAE;IAChBjE,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACuE,WAAW,CAACC,MAAM,CAAC;EAC3F;EACAC,aAAaA,CAACC,OAAO,GAAG,KAAK,EAAE;IAC3B,IAAI,CAACvD,GAAG,CAACqC,SAAS,CAACC,MAAM,CAAC,SAAS,EAAE,CAACiB,OAAO,CAAC;IAC9C;EACJ;EACA1B,mBAAmBA,CAAC0B,OAAO,GAAG,KAAK,EAAE;IACjC;IACA,IAAI,CAACvD,GAAG,CAACqC,SAAS,CAACC,MAAM,CAAC,oCAAoC,EAAE,CAACiB,OAAO,CAAC;EAC7E;EACAzB,kCAAkCA,CAACyB,OAAO,GAAG,KAAK,EAAE;IAChD,IAAIb,EAAE;IACN;IACA,CAACA,EAAE,GAAGtD,sBAAsB,CAAC,IAAI,EAAEhB,sCAAsC,EAAE,GAAG,CAAC,MAAM,IAAI,IAAIsE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC1C,GAAG,CAACqC,SAAS,CAACC,MAAM,CAAC,oCAAoC,EAAE,CAACiB,OAAO,CAAC;EACjM;EACA;AACJ;AACA;AACA;EACIC,MAAMA,CAAA,EAAG;IACL,IAAI,CAACxD,GAAG,CAACyD,QAAQ,GAAG,CAAC;IACrB,IAAI,CAAC5B,mBAAmB,CAAC,IAAI,CAAC;IAC9B,MAAM6B,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtC,KAAK,MAAMf,MAAM,IAAIxD,sBAAsB,CAAC,IAAI,EAAEb,8BAA8B,EAAE,GAAG,CAAC,CAACmC,MAAM,CAAC,CAAC,EAAE;MAC7FkC,MAAM,CAACgB,aAAa,CAAC,CAAC;MACtBhB,MAAM,CAAC1B,IAAI,CAAC,IAAI,CAAC;MACjB,IAAI0B,MAAM,CAACiB,mBAAmB,EAAE;QAC5BzE,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACiF,+BAA+B,CAAClB,MAAM,CAAC;QAC3Gc,oBAAoB,CAACpD,GAAG,CAACsC,MAAM,CAACiB,mBAAmB,CAAC;MACxD;IACJ;IACA,IAAI,CAACzE,sBAAsB,CAAC,IAAI,EAAEhB,sCAAsC,EAAE,GAAG,CAAC,EAAE;MAC5E;IACJ;IACA,MAAM2F,SAAS,GAAG3E,sBAAsB,CAAC,IAAI,EAAEhB,sCAAsC,EAAE,GAAG,CAAC,CAAC4F,sBAAsB,CAAC,CAAC;IACpH,KAAK,MAAMC,QAAQ,IAAIF,SAAS,EAAE;MAC9B;MACAE,QAAQ,CAACjD,IAAI,CAAC,CAAC;MACf,IAAI5B,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACqF,0BAA0B,CAACD,QAAQ,CAACE,IAAI,CAACC,EAAE,CAAC,EAAE;QAClH;MACJ;MACA,IAAIV,oBAAoB,CAACW,GAAG,CAACJ,QAAQ,CAACE,IAAI,CAACC,EAAE,CAAC,EAAE;QAC5C;MACJ;MACA,MAAMxB,MAAM,GAAG,IAAI,CAAC0B,WAAW,CAACL,QAAQ,CAAC;MACzC,IAAI,CAACrB,MAAM,EAAE;QACT;MACJ;MACA,IAAI,CAAC2B,YAAY,CAAC3B,MAAM,CAAC;MACzBA,MAAM,CAACgB,aAAa,CAAC,CAAC;IAC1B;EACJ;EACA;AACJ;AACA;EACIY,OAAOA,CAAA,EAAG;IACN,IAAI9B,EAAE;IACNrD,sBAAsB,CAAC,IAAI,EAAEX,kCAAkC,EAAE,IAAI,EAAE,GAAG,CAAC;IAC3E,IAAI,CAACsB,GAAG,CAACyD,QAAQ,GAAG,CAAC,CAAC;IACtB,IAAI,CAAC5B,mBAAmB,CAAC,KAAK,CAAC;IAC/B,MAAM4C,kBAAkB,GAAG,IAAIjE,GAAG,CAAC,CAAC;IACpC,MAAMkE,gBAAgB,GAAG,IAAIlE,GAAG,CAAC,CAAC;IAClC,KAAK,MAAMoC,MAAM,IAAIxD,sBAAsB,CAAC,IAAI,EAAEb,8BAA8B,EAAE,GAAG,CAAC,CAACmC,MAAM,CAAC,CAAC,EAAE;MAC7FkC,MAAM,CAAC+B,cAAc,CAAC,CAAC;MACvB,IAAI,CAAC/B,MAAM,CAACiB,mBAAmB,EAAE;QAC7B;MACJ;MACA,IAAIjB,MAAM,CAACgC,SAAS,CAAC,CAAC,KAAK,IAAI,EAAE;QAC7BH,kBAAkB,CAAClE,GAAG,CAACqC,MAAM,CAACiB,mBAAmB,EAAEjB,MAAM,CAAC;QAC1D;MACJ,CAAC,MACI;QACD8B,gBAAgB,CAACnE,GAAG,CAACqC,MAAM,CAACiB,mBAAmB,EAAEjB,MAAM,CAAC;MAC5D;MACA,CAACF,EAAE,GAAG,IAAI,CAACmC,qBAAqB,CAACjC,MAAM,CAACiB,mBAAmB,CAAC,MAAM,IAAI,IAAInB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACxB,IAAI,CAAC,CAAC;MAC5G0B,MAAM,CAACkC,MAAM,CAAC,CAAC;IACnB;IACA,IAAI1F,sBAAsB,CAAC,IAAI,EAAEhB,sCAAsC,EAAE,GAAG,CAAC,EAAE;MAC3E;MACA,MAAM2F,SAAS,GAAG3E,sBAAsB,CAAC,IAAI,EAAEhB,sCAAsC,EAAE,GAAG,CAAC,CAAC4F,sBAAsB,CAAC,CAAC;MACpH,KAAK,MAAMC,QAAQ,IAAIF,SAAS,EAAE;QAC9B,MAAM;UAAEK;QAAG,CAAC,GAAGH,QAAQ,CAACE,IAAI;QAC5B,IAAI/E,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACqF,0BAA0B,CAACE,EAAE,CAAC,EAAE;UACpG;QACJ;QACA,IAAIxB,MAAM,GAAG8B,gBAAgB,CAACK,GAAG,CAACX,EAAE,CAAC;QACrC,IAAIxB,MAAM,EAAE;UACRA,MAAM,CAACoC,sBAAsB,CAACf,QAAQ,CAAC;UACvCrB,MAAM,CAAC1B,IAAI,CAAC,KAAK,CAAC;UAClB+C,QAAQ,CAAC/C,IAAI,CAAC,CAAC;UACf;QACJ;QACA0B,MAAM,GAAG6B,kBAAkB,CAACM,GAAG,CAACX,EAAE,CAAC;QACnC,IAAIxB,MAAM,EAAE;UACRxD,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACoG,4BAA4B,CAACrC,MAAM,CAAC;UACxGA,MAAM,CAACsC,uBAAuB,CAACjB,QAAQ,CAAC;UACxCrB,MAAM,CAAC1B,IAAI,CAAC,KAAK,CAAC;QACtB;QACA+C,QAAQ,CAAC/C,IAAI,CAAC,CAAC;MACnB;IACJ;IACA9B,sBAAsB,CAAC,IAAI,EAAEpB,gCAAgC,EAAE,GAAG,EAAEmB,8BAA8B,CAAC,CAACwC,IAAI,CAAC,IAAI,CAAC;IAC9G,IAAI,IAAI,CAACP,OAAO,EAAE;MACd,IAAI,CAACpB,GAAG,CAACiB,MAAM,GAAG,IAAI;IAC1B;IACA,MAAM;MAAEoB;IAAU,CAAC,GAAG,IAAI,CAACrC,GAAG;IAC9B,KAAK,MAAMY,UAAU,IAAIxB,sBAAsB,CAACnB,EAAE,EAAEA,EAAE,EAAE,GAAG,EAAEa,kCAAkC,CAAC,CAAC4B,MAAM,CAAC,CAAC,EAAE;MACvG2B,SAAS,CAACyC,MAAM,CAAC,GAAGlE,UAAU,CAAC2B,KAAK,SAAS,CAAC;IAClD;IACA,IAAI,CAACX,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACE,kCAAkC,CAAC,IAAI,CAAC;IAC7CzC,sBAAsB,CAAC,IAAI,EAAEX,kCAAkC,EAAE,KAAK,EAAE,GAAG,CAAC;EAChF;EACAmG,qBAAqBA,CAACT,EAAE,EAAE;IACtB,IAAI1B,EAAE;IACN,OAAO,CAAC,CAACA,EAAE,GAAGtD,sBAAsB,CAAC,IAAI,EAAEhB,sCAAsC,EAAE,GAAG,CAAC,MAAM,IAAI,IAAIsE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmC,qBAAqB,CAACT,EAAE,CAAC,KAAK,IAAI;EACvK;EACA;AACJ;AACA;AACA;EACIe,eAAeA,CAACvC,MAAM,EAAE;IACpB,MAAMwC,aAAa,GAAGhG,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACwG,SAAS,CAAC,CAAC;IACrG,IAAID,aAAa,KAAKxC,MAAM,EAAE;MAC1B;IACJ;IACAxD,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACsG,eAAe,CAACvC,MAAM,CAAC;EAC/F;EACAT,mBAAmBA,CAAA,EAAG;IAClB,IAAIO,EAAE;IACN,IAAI,CAAC1C,GAAG,CAACyD,QAAQ,GAAG,CAAC,CAAC;IACtB,IAAI,CAAC,CAACf,EAAE,GAAGtD,sBAAsB,CAAC,IAAI,EAAET,gCAAgC,EAAE,GAAG,CAAC,MAAM,IAAI,IAAI+D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC1C,GAAG,KAAK,CAACZ,sBAAsB,CAAC,IAAI,EAAER,sCAAsC,EAAE,GAAG,CAAC,EAAE;MACxMS,sBAAsB,CAAC,IAAI,EAAET,sCAAsC,EAAE,IAAI0G,eAAe,CAAC,CAAC,EAAE,GAAG,CAAC;MAChG,MAAMC,MAAM,GAAGnG,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAAC2G,cAAc,CAACpG,sBAAsB,CAAC,IAAI,EAAER,sCAAsC,EAAE,GAAG,CAAC,CAAC;MAC5KQ,sBAAsB,CAAC,IAAI,EAAET,gCAAgC,EAAE,GAAG,CAAC,CAACqB,GAAG,CAACyF,gBAAgB,CAAC,aAAa,EAAErG,sBAAsB,CAAC,IAAI,EAAEpB,gCAAgC,EAAE,GAAG,EAAEe,2CAA2C,CAAC,CAAC2G,IAAI,CAAC,IAAI,CAAC,EAAE;QAAEH;MAAO,CAAC,CAAC;MAChP;IACJ;EACJ;EACA3D,oBAAoBA,CAAA,EAAG;IACnB,IAAIc,EAAE;IACN,IAAI,CAAC1C,GAAG,CAACyD,QAAQ,GAAG,CAAC;IACrB,IAAI,CAAC,CAACf,EAAE,GAAGtD,sBAAsB,CAAC,IAAI,EAAET,gCAAgC,EAAE,GAAG,CAAC,MAAM,IAAI,IAAI+D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC1C,GAAG,KAAKZ,sBAAsB,CAAC,IAAI,EAAER,sCAAsC,EAAE,GAAG,CAAC,EAAE;MACvMQ,sBAAsB,CAAC,IAAI,EAAER,sCAAsC,EAAE,GAAG,CAAC,CAAC+G,KAAK,CAAC,CAAC;MACjFtG,sBAAsB,CAAC,IAAI,EAAET,sCAAsC,EAAE,IAAI,EAAE,GAAG,CAAC;MAC/EQ,sBAAsB,CAAC,IAAI,EAAET,gCAAgC,EAAE,GAAG,CAAC,CAACqB,GAAG,CAACqC,SAAS,CAACyC,MAAM,CAAC,cAAc,CAAC;IAC5G;EACJ;EACA1C,WAAWA,CAAA,EAAG;IACV,IAAIhD,sBAAsB,CAAC,IAAI,EAAEf,8BAA8B,EAAE,GAAG,CAAC,EAAE;MACnE;IACJ;IACAgB,sBAAsB,CAAC,IAAI,EAAEhB,8BAA8B,EAAE,IAAIiH,eAAe,CAAC,CAAC,EAAE,GAAG,CAAC;IACxF,MAAMC,MAAM,GAAGnG,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAAC2G,cAAc,CAACpG,sBAAsB,CAAC,IAAI,EAAEf,8BAA8B,EAAE,GAAG,CAAC,CAAC;IACpK,IAAI,CAAC2B,GAAG,CAACyF,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACG,WAAW,CAACF,IAAI,CAAC,IAAI,CAAC,EAAE;MAClEH;IACJ,CAAC,CAAC;IACF,IAAI,CAACvF,GAAG,CAACyF,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACI,SAAS,CAACH,IAAI,CAAC,IAAI,CAAC,EAAE;MAC9DH;IACJ,CAAC,CAAC;EACN;EACAxD,YAAYA,CAAA,EAAG;IACX,IAAIW,EAAE;IACN,CAACA,EAAE,GAAGtD,sBAAsB,CAAC,IAAI,EAAEf,8BAA8B,EAAE,GAAG,CAAC,MAAM,IAAI,IAAIqE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiD,KAAK,CAAC,CAAC;IACxHtG,sBAAsB,CAAC,IAAI,EAAEhB,8BAA8B,EAAE,IAAI,EAAE,GAAG,CAAC;EAC3E;EACAyH,MAAMA,CAAClD,MAAM,EAAE;IACXxD,sBAAsB,CAAC,IAAI,EAAEb,8BAA8B,EAAE,GAAG,CAAC,CAACgC,GAAG,CAACqC,MAAM,CAACwB,EAAE,EAAExB,MAAM,CAAC;IACxF,MAAM;MAAEiB;IAAoB,CAAC,GAAGjB,MAAM;IACtC,IAAIiB,mBAAmB,IACnBzE,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACqF,0BAA0B,CAACL,mBAAmB,CAAC,EAAE;MACrHzE,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACkH,8BAA8B,CAACnD,MAAM,CAAC;IAC9G;EACJ;EACAoD,MAAMA,CAACpD,MAAM,EAAE;IACX,IAAIF,EAAE;IACNtD,sBAAsB,CAAC,IAAI,EAAEb,8BAA8B,EAAE,GAAG,CAAC,CAAC0H,MAAM,CAACrD,MAAM,CAACwB,EAAE,CAAC;IACnF,CAAC1B,EAAE,GAAGtD,sBAAsB,CAAC,IAAI,EAAElB,2CAA2C,EAAE,GAAG,CAAC,MAAM,IAAI,IAAIwE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwD,wBAAwB,CAACtD,MAAM,CAACuD,UAAU,CAAC;IACzK,IAAI,CAAC/G,sBAAsB,CAAC,IAAI,EAAEV,kCAAkC,EAAE,GAAG,CAAC,IAAIkE,MAAM,CAACiB,mBAAmB,EAAE;MACtGzE,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACuH,2BAA2B,CAACxD,MAAM,CAAC;IAC3G;EACJ;EACA;AACJ;AACA;AACA;EACIkC,MAAMA,CAAClC,MAAM,EAAE;IACX,IAAI,CAACoD,MAAM,CAACpD,MAAM,CAAC;IACnBxD,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACwH,YAAY,CAACzD,MAAM,CAAC;IACxFA,MAAM,CAAC5C,GAAG,CAAC8E,MAAM,CAAC,CAAC;IACnBlC,MAAM,CAAC0D,eAAe,GAAG,KAAK;IAC9B,IAAI,CAAClH,sBAAsB,CAAC,IAAI,EAAEX,mCAAmC,EAAE,GAAG,CAAC,EAAE;MACzE,IAAI,CAACwD,oBAAoB,CAAC,oBAAqB,KAAK,CAAC;IACzD;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIsE,YAAYA,CAAC3D,MAAM,EAAE;IACjB,IAAIF,EAAE;IACN,IAAIE,MAAM,CAAC4D,MAAM,KAAK,IAAI,EAAE;MACxB;IACJ;IACA,IAAI5D,MAAM,CAAC4D,MAAM,IAAI5D,MAAM,CAACiB,mBAAmB,EAAE;MAC7CzE,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACuH,2BAA2B,CAACxD,MAAM,CAACiB,mBAAmB,CAAC;MAC3HnE,gBAAgB,CAAC+G,uBAAuB,CAAC7D,MAAM,CAAC;MAChDA,MAAM,CAACiB,mBAAmB,GAAG,IAAI;IACrC;IACA,IAAI,CAACiC,MAAM,CAAClD,MAAM,CAAC;IACnB,CAACF,EAAE,GAAGE,MAAM,CAAC4D,MAAM,MAAM,IAAI,IAAI9D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsD,MAAM,CAACpD,MAAM,CAAC;IAC3EA,MAAM,CAAC8D,SAAS,CAAC,IAAI,CAAC;IACtB,IAAI9D,MAAM,CAAC5C,GAAG,IAAI4C,MAAM,CAAC0D,eAAe,EAAE;MACtC1D,MAAM,CAAC5C,GAAG,CAAC8E,MAAM,CAAC,CAAC;MACnB,IAAI,CAAC9E,GAAG,CAAC2G,MAAM,CAAC/D,MAAM,CAAC5C,GAAG,CAAC;IAC/B;EACJ;EACA;AACJ;AACA;AACA;EACIM,GAAGA,CAACsC,MAAM,EAAE;IACR,IAAIA,MAAM,CAAC4D,MAAM,KAAK,IAAI,IAAI5D,MAAM,CAAC0D,eAAe,EAAE;MAClD;IACJ;IACA,IAAI,CAACC,YAAY,CAAC3D,MAAM,CAAC;IACzBxD,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAAC+H,SAAS,CAAChE,MAAM,CAAC;IACrF,IAAI,CAACkD,MAAM,CAAClD,MAAM,CAAC;IACnB,IAAI,CAACA,MAAM,CAAC0D,eAAe,EAAE;MACzB,MAAMtG,GAAG,GAAG4C,MAAM,CAACiE,MAAM,CAAC,CAAC;MAC3B,IAAI,CAAC7G,GAAG,CAAC2G,MAAM,CAAC3G,GAAG,CAAC;MACpB4C,MAAM,CAAC0D,eAAe,GAAG,IAAI;IACjC;IACA;IACA1D,MAAM,CAACkE,iBAAiB,CAAC,CAAC;IAC1BlE,MAAM,CAACmE,SAAS,CAAC,CAAC;IAClB3H,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACmI,sBAAsB,CAACpE,MAAM,CAAC;IAClG;IACA;IACA;EACJ;EACAqE,eAAeA,CAACrE,MAAM,EAAE;IACpB,IAAIF,EAAE;IACN,IAAI,CAACE,MAAM,CAAC0D,eAAe,EAAE;MACzB;IACJ;IACA,MAAM;MAAEY;IAAc,CAAC,GAAGC,QAAQ;IAClC,IAAIvE,MAAM,CAAC5C,GAAG,CAACoH,QAAQ,CAACF,aAAa,CAAC,IAAI,CAAC9H,sBAAsB,CAAC,IAAI,EAAEd,2CAA2C,EAAE,GAAG,CAAC,EAAE;MACvH;MACA;MACA;MACA;MACAsE,MAAM,CAACyE,mBAAmB,GAAG,KAAK;MAClChI,sBAAsB,CAAC,IAAI,EAAEf,2CAA2C,EAAEgJ,UAAU,CAAC,MAAM;QACvFjI,sBAAsB,CAAC,IAAI,EAAEf,2CAA2C,EAAE,IAAI,EAAE,GAAG,CAAC;QACpF,IAAI,CAACsE,MAAM,CAAC5C,GAAG,CAACoH,QAAQ,CAACD,QAAQ,CAACD,aAAa,CAAC,EAAE;UAC9CtE,MAAM,CAAC5C,GAAG,CAACyF,gBAAgB,CAAC,SAAS,EAAE,MAAM;YACzC7C,MAAM,CAACyE,mBAAmB,GAAG,IAAI;UACrC,CAAC,EAAE;YAAEE,IAAI,EAAE,IAAI;YAAEhC,MAAM,EAAEnG,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAAC2I;UAAQ,CAAC,CAAC;UACvGN,aAAa,CAACO,KAAK,CAAC,CAAC;QACzB,CAAC,MACI;UACD7E,MAAM,CAACyE,mBAAmB,GAAG,IAAI;QACrC;MACJ,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;IACf;IACAzE,MAAM,CAAC8E,mBAAmB,GAAG,CAAChF,EAAE,GAAGtD,sBAAsB,CAAC,IAAI,EAAElB,2CAA2C,EAAE,GAAG,CAAC,MAAM,IAAI,IAAIwE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiF,gBAAgB,CAAC,IAAI,CAAC3H,GAAG,EAAE4C,MAAM,CAAC5C,GAAG,EAAE4C,MAAM,CAACuD,UAAU,EACnN,mBAAoB,IAAI,CAAC;EAC7B;EACA;AACJ;AACA;AACA;EACI5B,YAAYA,CAAC3B,MAAM,EAAE;IACjB,IAAIA,MAAM,CAACgF,gBAAgB,CAAC,CAAC,EAAE;MAC3BhF,MAAM,CAAC4D,MAAM,KAAK5D,MAAM,CAAC4D,MAAM,GAAG,IAAI,CAAC;MACvC5D,MAAM,CAACiF,OAAO,CAAC,CAAC;MAChBjF,MAAM,CAAC1B,IAAI,CAAC,CAAC;IACjB,CAAC,MACI;MACD,IAAI,CAACZ,GAAG,CAACsC,MAAM,CAAC;IACpB;EACJ;EACA;AACJ;AACA;AACA;EACIkF,iBAAiBA,CAAClF,MAAM,EAAE;IACtB,MAAMmF,GAAG,GAAGA,CAAA,KAAMnF,MAAM,CAACoF,UAAU,CAACH,OAAO,CAACjF,MAAM,CAAC;IACnD,MAAMqF,IAAI,GAAGA,CAAA,KAAM;MACfrF,MAAM,CAACkC,MAAM,CAAC,CAAC;IACnB,CAAC;IACD,IAAI,CAAC1B,WAAW,CAAC;MAAE2E,GAAG;MAAEE,IAAI;MAAEC,QAAQ,EAAE;IAAM,CAAC,CAAC;EACpD;EACA;AACJ;AACA;AACA;EACIC,SAASA,CAAA,EAAG;IACR,OAAO/I,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACuJ,KAAK,CAAC,CAAC;EACtF;EACA5C,cAAcA,CAAC6C,eAAe,EAAE;IAC5B,OAAOjJ,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAAC2G,cAAc,CAAC6C,eAAe,CAAC;EAC9G;EACAC,uBAAuBA,CAAA,EAAG;IACtB,IAAI5F,EAAE;IACN,OAAO,CAACA,EAAE,GAAGtD,sBAAsB,CAAC,IAAI,EAAEpB,gCAAgC,EAAE,GAAG,EAAEgB,4CAA4C,CAAC,MAAM,IAAI,IAAI0D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4F,uBAAuB,CAAC,CAAC;EACrM;EACA;AACJ;AACA;AACA;AACA;EACIC,WAAWA,CAAC7G,IAAI,EAAE2B,MAAM,EAAE;IACtBjE,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAAC2C,aAAa,CAACE,IAAI,CAAC;IACvFtC,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAAC4C,UAAU,CAACC,IAAI,CAAC;IACpF,MAAM;MAAEsB,OAAO;MAAEC;IAAQ,CAAC,GAAG7D,sBAAsB,CAAC,IAAI,EAAEpB,gCAAgC,EAAE,GAAG,EAAEkB,qCAAqC,CAAC,CAACyC,IAAI,CAAC,IAAI,CAAC;IAClJ,MAAMyC,EAAE,GAAG,IAAI,CAAC+D,SAAS,CAAC,CAAC;IAC3B,MAAMvF,MAAM,GAAGxD,sBAAsB,CAAC,IAAI,EAAEpB,gCAAgC,EAAE,GAAG,EAAEiB,sCAAsC,CAAC,CAAC0C,IAAI,CAAC,IAAI,EAAE6G,MAAM,CAACC,MAAM,CAAC;MAAEjC,MAAM,EAAE,IAAI;MAAEpC,EAAE;MAAEsE,CAAC,EAAE1F,OAAO;MAAE2F,CAAC,EAAE1F,OAAO;MAAEnD,SAAS,EAAEV,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC;MAAE+J,UAAU,EAAE;IAAK,CAAC,EAAEvF,MAAM,CAAC,CAAC;IAC5S,IAAIT,MAAM,EAAE;MACR,IAAI,CAACtC,GAAG,CAACsC,MAAM,CAAC;IACpB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI0B,WAAWA,CAACH,IAAI,EAAE;IACd,IAAIzB,EAAE,EAAEmG,EAAE;IACV,OAAQ,CAAC,CAACA,EAAE,GAAGzJ,sBAAsB,CAACnB,EAAE,EAAEA,EAAE,EAAE,GAAG,EAAEa,kCAAkC,CAAC,CACjFiG,GAAG,CAAC,CAACrC,EAAE,GAAGyB,IAAI,CAAC2E,cAAc,MAAM,IAAI,IAAIpG,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGyB,IAAI,CAAC4E,oBAAoB,CAAC,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACvE,WAAW,CAACH,IAAI,EAAE,IAAI,EAAE/E,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAAC,KAAK,IAAI;EAClP;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIkE,qBAAqBA,CAACiG,KAAK,EAAEJ,UAAU,EAAEzE,IAAI,GAAG,CAAC,CAAC,EAAE;IAChD,MAAMC,EAAE,GAAG,IAAI,CAAC+D,SAAS,CAAC,CAAC;IAC3B,MAAMvF,MAAM,GAAGxD,sBAAsB,CAAC,IAAI,EAAEpB,gCAAgC,EAAE,GAAG,EAAEiB,sCAAsC,CAAC,CAAC0C,IAAI,CAAC,IAAI,EAAE6G,MAAM,CAACC,MAAM,CAAC;MAAEjC,MAAM,EAAE,IAAI;MAAEpC,EAAE;MAAEsE,CAAC,EAAEM,KAAK,CAAChG,OAAO;MAAE2F,CAAC,EAAEK,KAAK,CAAC/F,OAAO;MAAEnD,SAAS,EAAEV,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC;MAAE+J;IAAW,CAAC,EAAEzE,IAAI,CAAC,CAAC;IAChT,IAAIvB,MAAM,EAAE;MACR,IAAI,CAACtC,GAAG,CAACsC,MAAM,CAAC;IACpB;IACA,OAAOA,MAAM;EACjB;EACA;AACJ;AACA;EACIqG,YAAYA,CAAA,EAAG;IACX,IAAI,CAAClG,qBAAqB,CAAC3D,sBAAsB,CAAC,IAAI,EAAEpB,gCAAgC,EAAE,GAAG,EAAEkB,qCAAqC,CAAC,CAACyC,IAAI,CAAC,IAAI,CAAC,EAAE,kBAAmB,IAAI,CAAC;EAC9K;EACA;AACJ;AACA;AACA;EACIuH,WAAWA,CAACtG,MAAM,EAAE;IAChBxD,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACqK,WAAW,CAACtG,MAAM,CAAC;EAC3F;EACA;AACJ;AACA;AACA;EACIuG,cAAcA,CAACvG,MAAM,EAAE;IACnBxD,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACsK,cAAc,CAACvG,MAAM,CAAC;EAC9F;EACA;AACJ;AACA;AACA;EACIwG,UAAUA,CAACxG,MAAM,EAAE;IACf,OAAOxD,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACuK,UAAU,CAACxG,MAAM,CAAC;EACjG;EACA;AACJ;AACA;AACA;EACIyG,QAAQA,CAACzG,MAAM,EAAE;IACbxD,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACwK,QAAQ,CAACzG,MAAM,CAAC;EACxF;EACA;AACJ;AACA;AACA;EACIiD,SAASA,CAACmD,KAAK,EAAE;IACb,MAAM;MAAEM;IAAM,CAAC,GAAG9J,WAAW,CAAC+J,QAAQ;IACtC,IAAIP,KAAK,CAACQ,MAAM,KAAK,CAAC,IAAKR,KAAK,CAACS,OAAO,IAAIH,KAAM,EAAE;MAChD;MACA;IACJ;IACA,IAAIN,KAAK,CAACU,MAAM,KAAK,IAAI,CAAC1J,GAAG,EAAE;MAC3B;IACJ;IACA,IAAI,CAACZ,sBAAsB,CAAC,IAAI,EAAEZ,qCAAqC,EAAE,GAAG,CAAC,EAAE;MAC3E;MACA;MACA;MACA;MACA;IACJ;IACAa,sBAAsB,CAAC,IAAI,EAAEb,qCAAqC,EAAE,KAAK,EAAE,GAAG,CAAC;IAC/E,IAAI,CAACY,sBAAsB,CAAC,IAAI,EAAEjB,iCAAiC,EAAE,GAAG,CAAC,EAAE;MACvEkB,sBAAsB,CAAC,IAAI,EAAElB,iCAAiC,EAAE,IAAI,EAAE,GAAG,CAAC;MAC1E;IACJ;IACA,IAAIiB,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACyC,OAAO,CAAC,CAAC,KAAK/B,oBAAoB,CAACoK,KAAK,EAAE;MAC9GvK,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAAC+K,WAAW,CAAC,CAAC;MACjF;IACJ;IACA,IAAI,CAAC7G,qBAAqB,CAACiG,KAAK,EAAE,kBAAmB,KAAK,CAAC;EAC/D;EACA;AACJ;AACA;AACA;EACIpD,WAAWA,CAACoD,KAAK,EAAE;IACf,IAAI5J,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACyC,OAAO,CAAC,CAAC,KAAK/B,oBAAoB,CAAC2C,SAAS,EAAE;MAClH,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC9B;IACA,IAAI/C,sBAAsB,CAAC,IAAI,EAAEZ,qCAAqC,EAAE,GAAG,CAAC,EAAE;MAC1E;MACA;MACA;MACA;MACA;MACAa,sBAAsB,CAAC,IAAI,EAAEb,qCAAqC,EAAE,KAAK,EAAE,GAAG,CAAC;MAC/E;IACJ;IACA,MAAM;MAAE8K;IAAM,CAAC,GAAG9J,WAAW,CAAC+J,QAAQ;IACtC,IAAIP,KAAK,CAACQ,MAAM,KAAK,CAAC,IAAKR,KAAK,CAACS,OAAO,IAAIH,KAAM,EAAE;MAChD;MACA;IACJ;IACA,IAAIN,KAAK,CAACU,MAAM,KAAK,IAAI,CAAC1J,GAAG,EAAE;MAC3B;IACJ;IACAX,sBAAsB,CAAC,IAAI,EAAEb,qCAAqC,EAAE,IAAI,EAAE,GAAG,CAAC;IAC9E,MAAMoE,MAAM,GAAGxD,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACwG,SAAS,CAAC,CAAC;IAC9FhG,sBAAsB,CAAC,IAAI,EAAElB,iCAAiC,EAAE,CAACyE,MAAM,IAAIA,MAAM,CAACxB,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC;EACrG;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIyI,aAAaA,CAACjH,MAAM,EAAE8F,CAAC,EAAEC,CAAC,EAAE;IACxB,MAAMmB,KAAK,GAAG1K,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACkL,UAAU,CAACrB,CAAC,EAAEC,CAAC,CAAC;IAClG,IAAImB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,IAAI,EAAE;MAClC,OAAO,KAAK;IAChB;IACAA,KAAK,CAACvD,YAAY,CAAC3D,MAAM,CAAC;IAC1B,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACIoH,OAAOA,CAAA,EAAG;IACN,IAAItH,EAAE,EAAEmG,EAAE;IACV,IAAI,CAAC,CAACnG,EAAE,GAAGtD,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACwG,SAAS,CAAC,CAAC,MAAM,IAAI,IAAI3C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC8D,MAAM,MAAM,IAAI,EAAE;MAClJ;MACApH,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACoL,cAAc,CAAC,CAAC;MACpF7K,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACsG,eAAe,CAAC,IAAI,CAAC;IAC7F;IACA,IAAI/F,sBAAsB,CAAC,IAAI,EAAEd,2CAA2C,EAAE,GAAG,CAAC,EAAE;MAChF4L,YAAY,CAAC9K,sBAAsB,CAAC,IAAI,EAAEd,2CAA2C,EAAE,GAAG,CAAC,CAAC;MAC5Fe,sBAAsB,CAAC,IAAI,EAAEf,2CAA2C,EAAE,IAAI,EAAE,GAAG,CAAC;IACxF;IACA,KAAK,MAAMsE,MAAM,IAAIxD,sBAAsB,CAAC,IAAI,EAAEb,8BAA8B,EAAE,GAAG,CAAC,CAACmC,MAAM,CAAC,CAAC,EAAE;MAC7F,CAACmI,EAAE,GAAGzJ,sBAAsB,CAAC,IAAI,EAAElB,2CAA2C,EAAE,GAAG,CAAC,MAAM,IAAI,IAAI2K,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC3C,wBAAwB,CAACtD,MAAM,CAACuD,UAAU,CAAC;MACzKvD,MAAM,CAAC8D,SAAS,CAAC,IAAI,CAAC;MACtB9D,MAAM,CAAC0D,eAAe,GAAG,KAAK;MAC9B1D,MAAM,CAAC5C,GAAG,CAAC8E,MAAM,CAAC,CAAC;IACvB;IACA,IAAI,CAAC9E,GAAG,GAAG,IAAI;IACfZ,sBAAsB,CAAC,IAAI,EAAEb,8BAA8B,EAAE,GAAG,CAAC,CAAC4L,KAAK,CAAC,CAAC;IACzE/K,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACuL,WAAW,CAAC,IAAI,CAAC;EACzF;EACA;AACJ;AACA;AACA;EACIvD,MAAMA,CAAC;IAAExG;EAAS,CAAC,EAAE;IACjB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxBZ,kBAAkB,CAAC,IAAI,CAACO,GAAG,EAAEK,QAAQ,CAAC;IACtC,KAAK,MAAMuC,MAAM,IAAIxD,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACwL,UAAU,CAAC,IAAI,CAACtK,SAAS,CAAC,EAAE;MACjH,IAAI,CAACO,GAAG,CAACsC,MAAM,CAAC;MAChBA,MAAM,CAACiF,OAAO,CAAC,CAAC;IACpB;IACA;IACA;IACA,IAAI,CAACpG,UAAU,CAAC,CAAC;EACrB;EACA;EACA;EACA;EACA;EACA6I,MAAMA,CAAC;IAAEjK;EAAS,CAAC,EAAE;IACjB;IACA;IACA;IACAjB,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACoL,cAAc,CAAC,CAAC;IACpF7K,sBAAsB,CAAC,IAAI,EAAEpB,gCAAgC,EAAE,GAAG,EAAEmB,8BAA8B,CAAC,CAACwC,IAAI,CAAC,IAAI,CAAC;IAC9G,MAAM4I,WAAW,GAAG,IAAI,CAAClK,QAAQ,CAACmK,QAAQ;IAC1C,MAAMA,QAAQ,GAAGnK,QAAQ,CAACmK,QAAQ;IAClC,IAAI,CAACnK,QAAQ,GAAGA,QAAQ;IACxB;IACA;IACA;IACAZ,kBAAkB,CAAC,IAAI,CAACO,GAAG,EAAEK,QAAQ,CAAC;IACtC,IAAIkK,WAAW,KAAKC,QAAQ,EAAE;MAC1B,KAAK,MAAM5H,MAAM,IAAIxD,sBAAsB,CAAC,IAAI,EAAEb,8BAA8B,EAAE,GAAG,CAAC,CAACmC,MAAM,CAAC,CAAC,EAAE;QAC7FkC,MAAM,CAAC6H,MAAM,CAACD,QAAQ,CAAC;MAC3B;IACJ;IACA,IAAI,CAACvI,oBAAoB,CAAC,oBAAqB,KAAK,CAAC;EACzD;EACA;AACJ;AACA;AACA;EACI,IAAIyI,cAAcA,CAAA,EAAG;IACjB,MAAM;MAAEC,SAAS;MAAEC;IAAW,CAAC,GAAG,IAAI,CAACvK,QAAQ,CAACwK,OAAO;IACvD,OAAO,CAACF,SAAS,EAAEC,UAAU,CAAC;EAClC;EACA,IAAIE,KAAKA,CAAA,EAAG;IACR,OAAO1L,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACkM,cAAc,CAACC,SAAS;EACvG;AACJ;AACA/M,EAAE,GAAG2B,qBAAqB,EAAE1B,2CAA2C,GAAG,IAAI+M,OAAO,CAAC,CAAC,EAAE9M,iCAAiC,GAAG,IAAI8M,OAAO,CAAC,CAAC,EAAE7M,sCAAsC,GAAG,IAAI6M,OAAO,CAAC,CAAC,EAAE5M,8BAA8B,GAAG,IAAI4M,OAAO,CAAC,CAAC,EAAE3M,2CAA2C,GAAG,IAAI2M,OAAO,CAAC,CAAC,EAAE1M,8BAA8B,GAAG,IAAI0M,OAAO,CAAC,CAAC,EAAEzM,qCAAqC,GAAG,IAAIyM,OAAO,CAAC,CAAC,EAAExM,mCAAmC,GAAG,IAAIwM,OAAO,CAAC,CAAC,EAAEvM,kCAAkC,GAAG,IAAIuM,OAAO,CAAC,CAAC,EAAEtM,gCAAgC,GAAG,IAAIsM,OAAO,CAAC,CAAC,EAAErM,sCAAsC,GAAG,IAAIqM,OAAO,CAAC,CAAC,EAAEpM,gCAAgC,GAAG,IAAIoM,OAAO,CAAC,CAAC,EAAEjN,gCAAgC,GAAG,IAAIkN,OAAO,CAAC,CAAC,EAAEnM,2CAA2C,GAAG,SAASA,2CAA2CA,CAACiK,KAAK,EAAE;EACrzB;EACA;EACA5J,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAAC+K,WAAW,CAAC,CAAC;EACjF,MAAM;IAAEF;EAAO,CAAC,GAAGV,KAAK;EACxB,IAAIU,MAAM,KAAKtK,sBAAsB,CAAC,IAAI,EAAET,gCAAgC,EAAE,GAAG,CAAC,CAACqB,GAAG,IACjF0J,MAAM,CAACrH,SAAS,CAAC+E,QAAQ,CAAC,cAAc,CAAC,IAAIhI,sBAAsB,CAAC,IAAI,EAAET,gCAAgC,EAAE,GAAG,CAAC,CAACqB,GAAG,CAACoH,QAAQ,CAACsC,MAAM,CAAE,EAAE;IACzI,MAAM;MAAEJ;IAAM,CAAC,GAAG9J,WAAW,CAAC+J,QAAQ;IACtC,IAAIP,KAAK,CAACQ,MAAM,KAAK,CAAC,IAAKR,KAAK,CAACS,OAAO,IAAIH,KAAM,EAAE;MAChD;MACA;IACJ;IACAlK,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACsM,cAAc,CAAC,WAAW,EAAE;IAChG;IACA,CAAC;IACD;IACA,IAAI/L,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACyC,OAAO,CAAC,CAAC,KAAK/B,oBAAoB,CAACgC,IAAI,EAAE;MAC7G;MACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAnC,sBAAsB,CAAC,IAAI,EAAET,gCAAgC,EAAE,GAAG,CAAC,CAACqB,GAAG,CAACyF,gBAAgB,CAAC,WAAW,EAAE,MAAM;MACxGrG,sBAAsB,CAAC,IAAI,EAAET,gCAAgC,EAAE,GAAG,CAAC,CAACqB,GAAG,CAACqC,SAAS,CAACyC,MAAM,CAAC,MAAM,CAAC;MAChG,IAAI,CAACxB,aAAa,CAAC,IAAI,CAAC;IAC5B,CAAC,EAAE;MAAEiE,IAAI,EAAE,IAAI;MAAEhC,MAAM,EAAEnG,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAAC2I;IAAQ,CAAC,CAAC;IACvGwB,KAAK,CAACoC,cAAc,CAAC,CAAC;EAC1B;AACJ,CAAC,EAAEpM,4CAA4C,GAAG,SAASA,4CAA4CA,CAAA,EAAG;EACtG,OAAOI,sBAAsB,CAACnB,EAAE,EAAEA,EAAE,EAAE,GAAG,EAAEa,kCAAkC,CAAC,CAACiG,GAAG,CAAC3F,sBAAsB,CAAC,IAAI,EAAEP,gCAAgC,EAAE,GAAG,CAAC,CAACyC,OAAO,CAAC,CAAC,CAAC;AACrK,CAAC,EAAErC,sCAAsC,GAAG,SAASA,sCAAsCA,CAACoE,MAAM,EAAE;EAChG,MAAMzC,UAAU,GAAGxB,sBAAsB,CAAC,IAAI,EAAEpB,gCAAgC,EAAE,GAAG,EAAEgB,4CAA4C,CAAC;EACpI;EACA,OAAO4B,UAAU,GAAG,IAAIA,UAAU,CAACyC,MAAM,CAAC,GAAG,IAAI;AACrD,CAAC,EAAEnE,qCAAqC,GAAG,SAASA,qCAAqCA,CAAA,EAAG;EACxF,MAAM;IAAEwJ,CAAC;IAAEC,CAAC;IAAE0C,KAAK;IAAEC;EAAO,CAAC,GAAG,IAAI,CAACtL,GAAG,CAACuL,qBAAqB,CAAC,CAAC;EAChE,MAAMC,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEhD,CAAC,CAAC;EAC1B,MAAMiD,GAAG,GAAGF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE/C,CAAC,CAAC;EAC1B,MAAMiD,GAAG,GAAGH,IAAI,CAACI,GAAG,CAACC,MAAM,CAACC,UAAU,EAAErD,CAAC,GAAG2C,KAAK,CAAC;EAClD,MAAMW,GAAG,GAAGP,IAAI,CAACI,GAAG,CAACC,MAAM,CAACG,WAAW,EAAEtD,CAAC,GAAG2C,MAAM,CAAC;EACpD,MAAMY,OAAO,GAAG,CAACV,GAAG,GAAGI,GAAG,IAAI,CAAC,GAAGlD,CAAC;EACnC,MAAMyD,OAAO,GAAG,CAACR,GAAG,GAAGK,GAAG,IAAI,CAAC,GAAGrD,CAAC;EACnC,MAAM,CAAC3F,OAAO,EAAEC,OAAO,CAAC,GAAG,IAAI,CAAC5C,QAAQ,CAACmK,QAAQ,GAAG,GAAG,KAAK,CAAC,GACvD,CAAC0B,OAAO,EAAEC,OAAO,CAAC,GAClB,CAACA,OAAO,EAAED,OAAO,CAAC;EACxB,OAAO;IAAElJ,OAAO;IAAEC;EAAQ,CAAC;AAC/B,CAAC,EAAE9D,8BAA8B,GAAG,SAASA,8BAA8BA,CAAA,EAAG;EAC1E;EACA;EACA;EACAE,sBAAsB,CAAC,IAAI,EAAEZ,mCAAmC,EAAE,IAAI,EAAE,GAAG,CAAC;EAC5E,KAAK,MAAMmE,MAAM,IAAIxD,sBAAsB,CAAC,IAAI,EAAEb,8BAA8B,EAAE,GAAG,CAAC,CAACmC,MAAM,CAAC,CAAC,EAAE;IAC7F,IAAIkC,MAAM,CAACxB,OAAO,CAAC,CAAC,EAAE;MAClBwB,MAAM,CAACkC,MAAM,CAAC,CAAC;IACnB;EACJ;EACAzF,sBAAsB,CAAC,IAAI,EAAEZ,mCAAmC,EAAE,KAAK,EAAE,GAAG,CAAC;AACjF,CAAC;AACDmB,qBAAqB,CAACe,YAAY,GAAG,KAAK;AAC1C7B,kCAAkC,GAAG;EAAEsN,KAAK,EAAE,IAAI5L,GAAG;EACjD;EACA,CAAClB,cAAc,EAAEK,eAAe,CAAC,CAAC0M,GAAG,CAACC,IAAI,IAAI,CAC1CA,IAAI,CAAC9J,WAAW,EAChB8J,IAAI,CACP,CAAC;AAAE,CAAC;AACT,SAAS1M,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}