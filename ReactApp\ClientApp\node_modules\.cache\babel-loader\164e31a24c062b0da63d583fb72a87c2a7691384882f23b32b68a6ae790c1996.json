{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport { getMotion } from \"../utils/legacyUtil\";\nexport default function Mask(props) {\n  var prefixCls = props.prefixCls,\n    visible = props.visible,\n    zIndex = props.zIndex,\n    mask = props.mask,\n    maskMotion = props.maskMotion,\n    maskAnimation = props.maskAnimation,\n    maskTransitionName = props.maskTransitionName;\n  if (!mask) {\n    return null;\n  }\n  var motion = {};\n  if (maskMotion || maskTransitionName || maskAnimation) {\n    motion = _objectSpread({\n      motionAppear: true\n    }, getMotion({\n      motion: maskMotion,\n      prefixCls: prefixCls,\n      transitionName: maskTransitionName,\n      animation: maskAnimation\n    }));\n  }\n  return /*#__PURE__*/React.createElement(CSSMotion, _extends({}, motion, {\n    visible: visible,\n    removeOnLeave: true\n  }), function (_ref) {\n    var className = _ref.className;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      style: {\n        zIndex: zIndex\n      },\n      className: classNames(\"\".concat(prefixCls, \"-mask\"), className)\n    });\n  });\n}", "map": {"version": 3, "names": ["_extends", "_objectSpread", "React", "classNames", "CSSMotion", "getMotion", "Mask", "props", "prefixCls", "visible", "zIndex", "mask", "maskMotion", "maskAnimation", "maskTransitionName", "motion", "motionAppear", "transitionName", "animation", "createElement", "removeOnLeave", "_ref", "className", "style", "concat"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/rc-trigger/es/Popup/Mask.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport { getMotion } from \"../utils/legacyUtil\";\nexport default function Mask(props) {\n  var prefixCls = props.prefixCls,\n      visible = props.visible,\n      zIndex = props.zIndex,\n      mask = props.mask,\n      maskMotion = props.maskMotion,\n      maskAnimation = props.maskAnimation,\n      maskTransitionName = props.maskTransitionName;\n\n  if (!mask) {\n    return null;\n  }\n\n  var motion = {};\n\n  if (maskMotion || maskTransitionName || maskAnimation) {\n    motion = _objectSpread({\n      motionAppear: true\n    }, getMotion({\n      motion: maskMotion,\n      prefixCls: prefixCls,\n      transitionName: maskTransitionName,\n      animation: maskAnimation\n    }));\n  }\n\n  return /*#__PURE__*/React.createElement(CSSMotion, _extends({}, motion, {\n    visible: visible,\n    removeOnLeave: true\n  }), function (_ref) {\n    var className = _ref.className;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      style: {\n        zIndex: zIndex\n      },\n      className: classNames(\"\".concat(prefixCls, \"-mask\"), className)\n    });\n  });\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,eAAe,SAASC,IAAIA,CAACC,KAAK,EAAE;EAClC,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC3BC,OAAO,GAAGF,KAAK,CAACE,OAAO;IACvBC,MAAM,GAAGH,KAAK,CAACG,MAAM;IACrBC,IAAI,GAAGJ,KAAK,CAACI,IAAI;IACjBC,UAAU,GAAGL,KAAK,CAACK,UAAU;IAC7BC,aAAa,GAAGN,KAAK,CAACM,aAAa;IACnCC,kBAAkB,GAAGP,KAAK,CAACO,kBAAkB;EAEjD,IAAI,CAACH,IAAI,EAAE;IACT,OAAO,IAAI;EACb;EAEA,IAAII,MAAM,GAAG,CAAC,CAAC;EAEf,IAAIH,UAAU,IAAIE,kBAAkB,IAAID,aAAa,EAAE;IACrDE,MAAM,GAAGd,aAAa,CAAC;MACrBe,YAAY,EAAE;IAChB,CAAC,EAAEX,SAAS,CAAC;MACXU,MAAM,EAAEH,UAAU;MAClBJ,SAAS,EAAEA,SAAS;MACpBS,cAAc,EAAEH,kBAAkB;MAClCI,SAAS,EAAEL;IACb,CAAC,CAAC,CAAC;EACL;EAEA,OAAO,aAAaX,KAAK,CAACiB,aAAa,CAACf,SAAS,EAAEJ,QAAQ,CAAC,CAAC,CAAC,EAAEe,MAAM,EAAE;IACtEN,OAAO,EAAEA,OAAO;IAChBW,aAAa,EAAE;EACjB,CAAC,CAAC,EAAE,UAAUC,IAAI,EAAE;IAClB,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC9B,OAAO,aAAapB,KAAK,CAACiB,aAAa,CAAC,KAAK,EAAE;MAC7CI,KAAK,EAAE;QACLb,MAAM,EAAEA;MACV,CAAC;MACDY,SAAS,EAAEnB,UAAU,CAAC,EAAE,CAACqB,MAAM,CAAChB,SAAS,EAAE,OAAO,CAAC,EAAEc,SAAS;IAChE,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}