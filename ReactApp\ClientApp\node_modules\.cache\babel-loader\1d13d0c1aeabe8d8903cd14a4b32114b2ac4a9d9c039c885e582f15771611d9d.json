{"ast": null, "code": "/**\n * Checks if a `cache` value for `key` exists.\n *\n * @private\n * @param {Object} cache The cache to query.\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction cacheHas(cache, key) {\n  return cache.has(key);\n}\nmodule.exports = cacheHas;", "map": {"version": 3, "names": ["cacheHas", "cache", "key", "has", "module", "exports"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/lodash/_cacheHas.js"], "sourcesContent": ["/**\n * Checks if a `cache` value for `key` exists.\n *\n * @private\n * @param {Object} cache The cache to query.\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction cacheHas(cache, key) {\n  return cache.has(key);\n}\n\nmodule.exports = cacheHas;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC5B,OAAOD,KAAK,CAACE,GAAG,CAACD,GAAG,CAAC;AACvB;AAEAE,MAAM,CAACC,OAAO,GAAGL,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}