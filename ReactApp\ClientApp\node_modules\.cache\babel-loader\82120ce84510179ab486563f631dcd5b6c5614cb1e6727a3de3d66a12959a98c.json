{"ast": null, "code": "import { scrollableRoot } from './utils';\n/** @hidden */\nexport var autoScroll = function (scrollableParent, vel) {\n  if (!scrollableParent) {\n    return;\n  }\n  var yIsScrollable;\n  var xIsScrollable;\n  var isRootNode = scrollableParent === scrollableRoot(scrollableParent);\n  if (isRootNode) {\n    yIsScrollable = document.body.scrollHeight > window.innerHeight;\n    xIsScrollable = document.body.scrollWidth > window.innerWidth;\n  } else {\n    yIsScrollable = scrollableParent.offsetHeight <= scrollableParent.scrollHeight;\n    xIsScrollable = scrollableParent.offsetWidth <= scrollableParent.scrollWidth;\n  }\n  var yDelta = scrollableParent.scrollTop + vel.y;\n  var yInBounds = yIsScrollable && yDelta > 0 && yDelta < scrollableParent.scrollHeight;\n  var xDelta = scrollableParent.scrollLeft + vel.x;\n  var xInBounds = xIsScrollable && xDelta > 0 && xDelta < scrollableParent.scrollWidth;\n  if (yInBounds) {\n    scrollableParent.scrollTop += vel.y;\n  } else if (yIsScrollable && yDelta < 0) {\n    scrollableParent.scrollTop = 0;\n  }\n  if (xInBounds) {\n    scrollableParent.scrollLeft += vel.x;\n  } else if (xIsScrollable && xDelta < 0) {\n    scrollableParent.scrollLeft = 0;\n  }\n};", "map": {"version": 3, "names": ["scrollableRoot", "autoScroll", "scrollableParent", "vel", "yIsScrollable", "xIsScrollable", "isRootNode", "document", "body", "scrollHeight", "window", "innerHeight", "scrollWidth", "innerWidth", "offsetHeight", "offsetWidth", "y<PERSON><PERSON><PERSON>", "scrollTop", "y", "yInBounds", "xDelta", "scrollLeft", "x", "xInBounds"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-draggable-common/dist/es/auto-scroll.js"], "sourcesContent": ["import { scrollableRoot } from './utils';\n/** @hidden */\nexport var autoScroll = function (scrollableParent, vel) {\n    if (!scrollableParent) {\n        return;\n    }\n    var yIsScrollable;\n    var xIsScrollable;\n    var isRootNode = scrollableParent === scrollableRoot(scrollableParent);\n    if (isRootNode) {\n        yIsScrollable = document.body.scrollHeight > window.innerHeight;\n        xIsScrollable = document.body.scrollWidth > window.innerWidth;\n    }\n    else {\n        yIsScrollable = scrollableParent.offsetHeight <= scrollableParent.scrollHeight;\n        xIsScrollable = scrollableParent.offsetWidth <= scrollableParent.scrollWidth;\n    }\n    var yDelta = scrollableParent.scrollTop + vel.y;\n    var yInBounds = yIsScrollable && yDelta > 0 && yDelta < scrollableParent.scrollHeight;\n    var xDelta = scrollableParent.scrollLeft + vel.x;\n    var xInBounds = xIsScrollable && xDelta > 0 && xDelta < scrollableParent.scrollWidth;\n    if (yInBounds) {\n        scrollableParent.scrollTop += vel.y;\n    }\n    else if (yIsScrollable && yDelta < 0) {\n        scrollableParent.scrollTop = 0;\n    }\n    if (xInBounds) {\n        scrollableParent.scrollLeft += vel.x;\n    }\n    else if (xIsScrollable && xDelta < 0) {\n        scrollableParent.scrollLeft = 0;\n    }\n};\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,SAAS;AACxC;AACA,OAAO,IAAIC,UAAU,GAAG,SAAAA,CAAUC,gBAAgB,EAAEC,GAAG,EAAE;EACrD,IAAI,CAACD,gBAAgB,EAAE;IACnB;EACJ;EACA,IAAIE,aAAa;EACjB,IAAIC,aAAa;EACjB,IAAIC,UAAU,GAAGJ,gBAAgB,KAAKF,cAAc,CAACE,gBAAgB,CAAC;EACtE,IAAII,UAAU,EAAE;IACZF,aAAa,GAAGG,QAAQ,CAACC,IAAI,CAACC,YAAY,GAAGC,MAAM,CAACC,WAAW;IAC/DN,aAAa,GAAGE,QAAQ,CAACC,IAAI,CAACI,WAAW,GAAGF,MAAM,CAACG,UAAU;EACjE,CAAC,MACI;IACDT,aAAa,GAAGF,gBAAgB,CAACY,YAAY,IAAIZ,gBAAgB,CAACO,YAAY;IAC9EJ,aAAa,GAAGH,gBAAgB,CAACa,WAAW,IAAIb,gBAAgB,CAACU,WAAW;EAChF;EACA,IAAII,MAAM,GAAGd,gBAAgB,CAACe,SAAS,GAAGd,GAAG,CAACe,CAAC;EAC/C,IAAIC,SAAS,GAAGf,aAAa,IAAIY,MAAM,GAAG,CAAC,IAAIA,MAAM,GAAGd,gBAAgB,CAACO,YAAY;EACrF,IAAIW,MAAM,GAAGlB,gBAAgB,CAACmB,UAAU,GAAGlB,GAAG,CAACmB,CAAC;EAChD,IAAIC,SAAS,GAAGlB,aAAa,IAAIe,MAAM,GAAG,CAAC,IAAIA,MAAM,GAAGlB,gBAAgB,CAACU,WAAW;EACpF,IAAIO,SAAS,EAAE;IACXjB,gBAAgB,CAACe,SAAS,IAAId,GAAG,CAACe,CAAC;EACvC,CAAC,MACI,IAAId,aAAa,IAAIY,MAAM,GAAG,CAAC,EAAE;IAClCd,gBAAgB,CAACe,SAAS,GAAG,CAAC;EAClC;EACA,IAAIM,SAAS,EAAE;IACXrB,gBAAgB,CAACmB,UAAU,IAAIlB,GAAG,CAACmB,CAAC;EACxC,CAAC,MACI,IAAIjB,aAAa,IAAIe,MAAM,GAAG,CAAC,EAAE;IAClClB,gBAAgB,CAACmB,UAAU,GAAG,CAAC;EACnC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}