{"ast": null, "code": "import renderAttr from './render-attribute';\nexport default function renderAllAttr(attrs) {\n  var output = \"\";\n  for (var i = 0; i < attrs.length; i++) {\n    output += renderAttr(attrs[i][0], attrs[i][1]);\n  }\n  return output;\n}", "map": {"version": 3, "names": ["renderAttr", "renderAllAttr", "attrs", "output", "i", "length"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/svg/utils/render-all-attributes.js"], "sourcesContent": ["import renderAttr from './render-attribute';\n\nexport default function renderAllAttr(attrs) {\n    var output = \"\";\n    for (var i = 0; i < attrs.length; i++) {\n        output += renderAttr(attrs[i][0], attrs[i][1]);\n    }\n\n    return output;\n}"], "mappings": "AAAA,OAAOA,UAAU,MAAM,oBAAoB;AAE3C,eAAe,SAASC,aAAaA,CAACC,KAAK,EAAE;EACzC,IAAIC,MAAM,GAAG,EAAE;EACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACnCD,MAAM,IAAIH,UAAU,CAACE,KAAK,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEF,KAAK,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClD;EAEA,OAAOD,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}