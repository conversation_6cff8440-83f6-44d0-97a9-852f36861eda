{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as n from \"react\";\nimport { useId as l, getActiveElement as r, classNames as f } from \"@progress/kendo-react-common\";\nimport { Popup as d } from \"@progress/kendo-react-popup\";\nimport { Menu as w } from \"../menu/components/Menu.mjs\";\nconst h = e => {\n    const o = n.useRef(null),\n      [m, c] = n.useState(e.show),\n      u = l(e.id),\n      {\n        animate: a = v.animate\n      } = e;\n    return n.useEffect(() => {\n      c(e.show), e.show && setTimeout(() => {\n        const t = o.current && o.current.element,\n          s = t && t.querySelector(\".k-menu-item\"),\n          i = r(t == null ? void 0 : t.ownerDocument);\n        s && s !== i && s.focus();\n      });\n    }, [e.show]), /* @__PURE__ */n.createElement(d, {\n      show: m,\n      offset: e.offset,\n      popupClass: \"k-menu-popup k-overflow-visible\",\n      animate: a\n    }, /* @__PURE__ */n.createElement(w, {\n      id: u,\n      vertical: !0,\n      ...e,\n      ref: o,\n      className: f(\"k-context-menu\", e.className),\n      onClose: e.onClose,\n      role: \"menu\"\n    }, e.children));\n  },\n  v = {\n    animate: {\n      openDuration: 300,\n      closeDuration: 300\n    }\n  };\nh.displayName = \"KendoReactContextMenu\";\nexport { h as ContextMenu };", "map": {"version": 3, "names": ["n", "useId", "l", "getActiveElement", "r", "classNames", "f", "Popup", "d", "<PERSON><PERSON>", "w", "h", "e", "o", "useRef", "m", "c", "useState", "show", "u", "id", "animate", "a", "v", "useEffect", "setTimeout", "t", "current", "element", "s", "querySelector", "i", "ownerDocument", "focus", "createElement", "offset", "popupClass", "vertical", "ref", "className", "onClose", "role", "children", "openDuration", "closeDuration", "displayName", "ContextMenu"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/contextmenu/ContextMenu.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as n from \"react\";\nimport { useId as l, getActiveElement as r, classNames as f } from \"@progress/kendo-react-common\";\nimport { Popup as d } from \"@progress/kendo-react-popup\";\nimport { Menu as w } from \"../menu/components/Menu.mjs\";\nconst h = (e) => {\n  const o = n.useRef(null), [m, c] = n.useState(e.show), u = l(e.id), { animate: a = v.animate } = e;\n  return n.useEffect(() => {\n    c(e.show), e.show && setTimeout(() => {\n      const t = o.current && o.current.element, s = t && t.querySelector(\".k-menu-item\"), i = r(t == null ? void 0 : t.ownerDocument);\n      s && s !== i && s.focus();\n    });\n  }, [e.show]), /* @__PURE__ */ n.createElement(d, { show: m, offset: e.offset, popupClass: \"k-menu-popup k-overflow-visible\", animate: a }, /* @__PURE__ */ n.createElement(\n    w,\n    {\n      id: u,\n      vertical: !0,\n      ...e,\n      ref: o,\n      className: f(\"k-context-menu\", e.className),\n      onClose: e.onClose,\n      role: \"menu\"\n    },\n    e.children\n  ));\n}, v = {\n  animate: { openDuration: 300, closeDuration: 300 }\n};\nh.displayName = \"KendoReactContextMenu\";\nexport {\n  h as ContextMenu\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,SAASC,KAAK,IAAIC,CAAC,EAAEC,gBAAgB,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AACjG,SAASC,KAAK,IAAIC,CAAC,QAAQ,6BAA6B;AACxD,SAASC,IAAI,IAAIC,CAAC,QAAQ,6BAA6B;AACvD,MAAMC,CAAC,GAAIC,CAAC,IAAK;IACf,MAAMC,CAAC,GAAGb,CAAC,CAACc,MAAM,CAAC,IAAI,CAAC;MAAE,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAGhB,CAAC,CAACiB,QAAQ,CAACL,CAAC,CAACM,IAAI,CAAC;MAAEC,CAAC,GAAGjB,CAAC,CAACU,CAAC,CAACQ,EAAE,CAAC;MAAE;QAAEC,OAAO,EAAEC,CAAC,GAAGC,CAAC,CAACF;MAAQ,CAAC,GAAGT,CAAC;IAClG,OAAOZ,CAAC,CAACwB,SAAS,CAAC,MAAM;MACvBR,CAAC,CAACJ,CAAC,CAACM,IAAI,CAAC,EAAEN,CAAC,CAACM,IAAI,IAAIO,UAAU,CAAC,MAAM;QACpC,MAAMC,CAAC,GAAGb,CAAC,CAACc,OAAO,IAAId,CAAC,CAACc,OAAO,CAACC,OAAO;UAAEC,CAAC,GAAGH,CAAC,IAAIA,CAAC,CAACI,aAAa,CAAC,cAAc,CAAC;UAAEC,CAAC,GAAG3B,CAAC,CAACsB,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACM,aAAa,CAAC;QAC/HH,CAAC,IAAIA,CAAC,KAAKE,CAAC,IAAIF,CAAC,CAACI,KAAK,CAAC,CAAC;MAC3B,CAAC,CAAC;IACJ,CAAC,EAAE,CAACrB,CAAC,CAACM,IAAI,CAAC,CAAC,EAAE,eAAgBlB,CAAC,CAACkC,aAAa,CAAC1B,CAAC,EAAE;MAAEU,IAAI,EAAEH,CAAC;MAAEoB,MAAM,EAAEvB,CAAC,CAACuB,MAAM;MAAEC,UAAU,EAAE,iCAAiC;MAAEf,OAAO,EAAEC;IAAE,CAAC,EAAE,eAAgBtB,CAAC,CAACkC,aAAa,CACxKxB,CAAC,EACD;MACEU,EAAE,EAAED,CAAC;MACLkB,QAAQ,EAAE,CAAC,CAAC;MACZ,GAAGzB,CAAC;MACJ0B,GAAG,EAAEzB,CAAC;MACN0B,SAAS,EAAEjC,CAAC,CAAC,gBAAgB,EAAEM,CAAC,CAAC2B,SAAS,CAAC;MAC3CC,OAAO,EAAE5B,CAAC,CAAC4B,OAAO;MAClBC,IAAI,EAAE;IACR,CAAC,EACD7B,CAAC,CAAC8B,QACJ,CAAC,CAAC;EACJ,CAAC;EAAEnB,CAAC,GAAG;IACLF,OAAO,EAAE;MAAEsB,YAAY,EAAE,GAAG;MAAEC,aAAa,EAAE;IAAI;EACnD,CAAC;AACDjC,CAAC,CAACkC,WAAW,GAAG,uBAAuB;AACvC,SACElC,CAAC,IAAImC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}