{"ast": null, "code": "export default function eventElement(e) {\n  if (e === void 0) e = {};\n  return e.touch ? e.touch.initialTouch : e.target;\n}", "map": {"version": 3, "names": ["eventElement", "e", "touch", "initialTouch", "target"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/util/event-element.js"], "sourcesContent": ["export default function eventElement(e) {\n    if ( e === void 0 ) e = {};\n\n    return e.touch ? e.touch.initialTouch : e.target;\n}"], "mappings": "AAAA,eAAe,SAASA,YAAYA,CAACC,CAAC,EAAE;EACpC,IAAKA,CAAC,KAAK,KAAK,CAAC,EAAGA,CAAC,GAAG,CAAC,CAAC;EAE1B,OAAOA,CAAC,CAACC,KAAK,GAAGD,CAAC,CAACC,KAAK,CAACC,YAAY,GAAGF,CAAC,CAACG,MAAM;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}