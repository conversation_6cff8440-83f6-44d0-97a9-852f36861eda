{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as t from \"react\";\nimport { Button as o } from \"@progress/kendo-react-buttons\";\nimport { xIcon as r } from \"@progress/kendo-svg-icons\";\nconst s = ({\n  children: e,\n  onCloseButtonClick: a,\n  id: i,\n  closeIcon: l\n}) => /* @__PURE__ */t.createElement(\"div\", {\n  className: \"k-window-titlebar k-dialog-titlebar\",\n  id: i\n}, /* @__PURE__ */t.createElement(\"span\", {\n  className: \"k-window-title k-dialog-title\"\n}, e), l && /* @__PURE__ */t.createElement(\"div\", {\n  className: \"k-window-titlebar-actions k-dialog-titlebar-actions\"\n}, /* @__PURE__ */t.createElement(o, {\n  role: \"button\",\n  \"aria-label\": \"Close\",\n  onClick: a,\n  icon: \"x\",\n  svgIcon: r,\n  fillMode: \"flat\",\n  className: \"k-window-titlebar-action k-dialog-titlebar-action\"\n})));\nexport { s as DialogTitleBar };", "map": {"version": 3, "names": ["t", "<PERSON><PERSON>", "o", "xIcon", "r", "s", "children", "e", "onCloseButtonClick", "a", "id", "i", "closeIcon", "l", "createElement", "className", "role", "onClick", "icon", "svgIcon", "fillMode", "DialogTitleBar"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dialogs/DialogTitleBar.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as t from \"react\";\nimport { Button as o } from \"@progress/kendo-react-buttons\";\nimport { xIcon as r } from \"@progress/kendo-svg-icons\";\nconst s = ({\n  children: e,\n  onCloseButtonClick: a,\n  id: i,\n  closeIcon: l\n}) => /* @__PURE__ */ t.createElement(\"div\", { className: \"k-window-titlebar k-dialog-titlebar\", id: i }, /* @__PURE__ */ t.createElement(\"span\", { className: \"k-window-title k-dialog-title\" }, e), l && /* @__PURE__ */ t.createElement(\"div\", { className: \"k-window-titlebar-actions k-dialog-titlebar-actions\" }, /* @__PURE__ */ t.createElement(\n  o,\n  {\n    role: \"button\",\n    \"aria-label\": \"Close\",\n    onClick: a,\n    icon: \"x\",\n    svgIcon: r,\n    fillMode: \"flat\",\n    className: \"k-window-titlebar-action k-dialog-titlebar-action\"\n  }\n)));\nexport {\n  s as DialogTitleBar\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,SAASC,MAAM,IAAIC,CAAC,QAAQ,+BAA+B;AAC3D,SAASC,KAAK,IAAIC,CAAC,QAAQ,2BAA2B;AACtD,MAAMC,CAAC,GAAGA,CAAC;EACTC,QAAQ,EAAEC,CAAC;EACXC,kBAAkB,EAAEC,CAAC;EACrBC,EAAE,EAAEC,CAAC;EACLC,SAAS,EAAEC;AACb,CAAC,KAAK,eAAgBb,CAAC,CAACc,aAAa,CAAC,KAAK,EAAE;EAAEC,SAAS,EAAE,qCAAqC;EAAEL,EAAE,EAAEC;AAAE,CAAC,EAAE,eAAgBX,CAAC,CAACc,aAAa,CAAC,MAAM,EAAE;EAAEC,SAAS,EAAE;AAAgC,CAAC,EAAER,CAAC,CAAC,EAAEM,CAAC,IAAI,eAAgBb,CAAC,CAACc,aAAa,CAAC,KAAK,EAAE;EAAEC,SAAS,EAAE;AAAsD,CAAC,EAAE,eAAgBf,CAAC,CAACc,aAAa,CACrVZ,CAAC,EACD;EACEc,IAAI,EAAE,QAAQ;EACd,YAAY,EAAE,OAAO;EACrBC,OAAO,EAAER,CAAC;EACVS,IAAI,EAAE,GAAG;EACTC,OAAO,EAAEf,CAAC;EACVgB,QAAQ,EAAE,MAAM;EAChBL,SAAS,EAAE;AACb,CACF,CAAC,CAAC,CAAC;AACH,SACEV,CAAC,IAAIgB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}