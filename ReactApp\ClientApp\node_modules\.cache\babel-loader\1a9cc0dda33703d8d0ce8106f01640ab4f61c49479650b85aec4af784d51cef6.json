{"ast": null, "code": "import BaseNode from '../core/base-node';\nimport NODE_MAP from './node-map';\nimport { defined } from '../util';\nvar Node = function (BaseNode) {\n  function Node(srcElement) {\n    BaseNode.call(this, srcElement);\n    if (srcElement) {\n      this.initClip();\n    }\n  }\n  if (BaseNode) Node.__proto__ = BaseNode;\n  Node.prototype = Object.create(BaseNode && BaseNode.prototype);\n  Node.prototype.constructor = Node;\n  Node.prototype.initClip = function initClip() {\n    var clip = this.srcElement.clip();\n    if (clip) {\n      this.clip = clip;\n      clip.addObserver(this);\n    }\n  };\n  Node.prototype.clear = function clear() {\n    if (this.srcElement) {\n      this.srcElement.removeObserver(this);\n    }\n    this.clearClip();\n    BaseNode.prototype.clear.call(this);\n  };\n  Node.prototype.clearClip = function clearClip() {\n    if (this.clip) {\n      this.clip.removeObserver(this);\n      delete this.clip;\n    }\n  };\n  Node.prototype.setClip = function setClip(ctx) {\n    if (this.clip) {\n      ctx.beginPath();\n      var clipNode = new NODE_MAP[this.clip.nodeType](this.clip);\n      clipNode.renderPoints(ctx, this.clip);\n      ctx.clip(\"evenodd\");\n    }\n  };\n  Node.prototype.optionsChange = function optionsChange(e) {\n    if (e.field === \"clip\") {\n      this.clearClip();\n      this.initClip();\n    }\n    BaseNode.prototype.optionsChange.call(this, e);\n  };\n  Node.prototype.setTransform = function setTransform(ctx) {\n    if (this.srcElement) {\n      var transform = this.srcElement.transform();\n      if (transform) {\n        ctx.transform.apply(ctx, transform.matrix().toArray(6));\n      }\n    }\n  };\n  Node.prototype.loadElements = function loadElements(elements, pos, cors) {\n    var this$1 = this;\n    for (var i = 0; i < elements.length; i++) {\n      var srcElement = elements[i];\n      var children = srcElement.children;\n      var childNode = new NODE_MAP[srcElement.nodeType](srcElement, cors);\n      if (children && children.length > 0) {\n        childNode.load(children, pos, cors);\n      }\n      if (defined(pos)) {\n        this$1.insertAt(childNode, pos);\n      } else {\n        this$1.append(childNode);\n      }\n    }\n  };\n  Node.prototype.load = function load(elements, pos, cors) {\n    this.loadElements(elements, pos, cors);\n    this.invalidate();\n  };\n  Node.prototype.setOpacity = function setOpacity(ctx) {\n    if (this.srcElement) {\n      var opacity = this.srcElement.opacity();\n      if (defined(opacity)) {\n        this.globalAlpha(ctx, opacity);\n      }\n    }\n  };\n  Node.prototype.globalAlpha = function globalAlpha(ctx, value) {\n    var opactity = value;\n    if (opactity && ctx.globalAlpha) {\n      opactity *= ctx.globalAlpha;\n    }\n    ctx.globalAlpha = opactity;\n  };\n  Node.prototype.visible = function visible() {\n    var src = this.srcElement;\n    return !src || src && src.options.visible !== false;\n  };\n  return Node;\n}(BaseNode);\nexport default Node;", "map": {"version": 3, "names": ["BaseNode", "NODE_MAP", "defined", "Node", "srcElement", "call", "initClip", "__proto__", "prototype", "Object", "create", "constructor", "clip", "addObserver", "clear", "removeObserver", "clearClip", "setClip", "ctx", "beginPath", "clipNode", "nodeType", "renderPoints", "optionsChange", "e", "field", "setTransform", "transform", "apply", "matrix", "toArray", "loadElements", "elements", "pos", "cors", "this$1", "i", "length", "children", "childNode", "load", "insertAt", "append", "invalidate", "setOpacity", "opacity", "globalAlpha", "value", "opactity", "visible", "src", "options"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/canvas/node.js"], "sourcesContent": ["import BaseNode from '../core/base-node';\nimport NODE_MAP from './node-map';\nimport { defined } from '../util';\n\nvar Node = (function (BaseNode) {\n    function Node(srcElement) {\n        BaseNode.call(this, srcElement);\n        if (srcElement) {\n            this.initClip();\n        }\n    }\n\n    if ( BaseNode ) Node.__proto__ = BaseNode;\n    Node.prototype = Object.create( BaseNode && BaseNode.prototype );\n    Node.prototype.constructor = Node;\n\n    Node.prototype.initClip = function initClip () {\n        var clip = this.srcElement.clip();\n        if (clip) {\n            this.clip = clip;\n            clip.addObserver(this);\n        }\n    };\n\n    Node.prototype.clear = function clear () {\n        if (this.srcElement) {\n            this.srcElement.removeObserver(this);\n        }\n\n        this.clearClip();\n\n        BaseNode.prototype.clear.call(this);\n    };\n\n    Node.prototype.clearClip = function clearClip () {\n        if (this.clip) {\n            this.clip.removeObserver(this);\n            delete this.clip;\n        }\n    };\n\n    Node.prototype.setClip = function setClip (ctx) {\n        if (this.clip) {\n            ctx.beginPath();\n\n            var clipNode = new NODE_MAP[this.clip.nodeType](this.clip);\n            clipNode.renderPoints(ctx, this.clip);\n\n            ctx.clip(\"evenodd\");\n        }\n    };\n\n    Node.prototype.optionsChange = function optionsChange (e) {\n        if (e.field === \"clip\") {\n            this.clearClip();\n            this.initClip();\n        }\n\n        BaseNode.prototype.optionsChange.call(this, e);\n    };\n\n    Node.prototype.setTransform = function setTransform (ctx) {\n        if (this.srcElement) {\n            var transform = this.srcElement.transform();\n            if (transform) {\n                ctx.transform.apply(ctx, transform.matrix().toArray(6));\n            }\n        }\n    };\n\n    Node.prototype.loadElements = function loadElements (elements, pos, cors) {\n        var this$1 = this;\n\n        for (var i = 0; i < elements.length; i++) {\n            var srcElement = elements[i];\n            var children = srcElement.children;\n\n            var childNode = new NODE_MAP[srcElement.nodeType](srcElement, cors);\n\n            if (children && children.length > 0) {\n                childNode.load(children, pos, cors);\n            }\n\n            if (defined(pos)) {\n                this$1.insertAt(childNode, pos);\n            } else {\n                this$1.append(childNode);\n            }\n        }\n    };\n\n    Node.prototype.load = function load (elements, pos, cors) {\n        this.loadElements(elements, pos, cors);\n\n        this.invalidate();\n    };\n\n    Node.prototype.setOpacity = function setOpacity (ctx) {\n        if (this.srcElement) {\n            var opacity = this.srcElement.opacity();\n            if (defined(opacity)) {\n                this.globalAlpha(ctx, opacity);\n            }\n        }\n    };\n\n    Node.prototype.globalAlpha = function globalAlpha (ctx, value) {\n        var opactity = value;\n        if (opactity && ctx.globalAlpha) {\n            opactity *= ctx.globalAlpha;\n        }\n        ctx.globalAlpha = opactity;\n    };\n\n    Node.prototype.visible = function visible () {\n        var src = this.srcElement;\n        return !src || (src && src.options.visible !== false);\n    };\n\n    return Node;\n}(BaseNode));\n\nexport default Node;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,mBAAmB;AACxC,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,OAAO,QAAQ,SAAS;AAEjC,IAAIC,IAAI,GAAI,UAAUH,QAAQ,EAAE;EAC5B,SAASG,IAAIA,CAACC,UAAU,EAAE;IACtBJ,QAAQ,CAACK,IAAI,CAAC,IAAI,EAAED,UAAU,CAAC;IAC/B,IAAIA,UAAU,EAAE;MACZ,IAAI,CAACE,QAAQ,CAAC,CAAC;IACnB;EACJ;EAEA,IAAKN,QAAQ,EAAGG,IAAI,CAACI,SAAS,GAAGP,QAAQ;EACzCG,IAAI,CAACK,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAEV,QAAQ,IAAIA,QAAQ,CAACQ,SAAU,CAAC;EAChEL,IAAI,CAACK,SAAS,CAACG,WAAW,GAAGR,IAAI;EAEjCA,IAAI,CAACK,SAAS,CAACF,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;IAC3C,IAAIM,IAAI,GAAG,IAAI,CAACR,UAAU,CAACQ,IAAI,CAAC,CAAC;IACjC,IAAIA,IAAI,EAAE;MACN,IAAI,CAACA,IAAI,GAAGA,IAAI;MAChBA,IAAI,CAACC,WAAW,CAAC,IAAI,CAAC;IAC1B;EACJ,CAAC;EAEDV,IAAI,CAACK,SAAS,CAACM,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAI;IACrC,IAAI,IAAI,CAACV,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACW,cAAc,CAAC,IAAI,CAAC;IACxC;IAEA,IAAI,CAACC,SAAS,CAAC,CAAC;IAEhBhB,QAAQ,CAACQ,SAAS,CAACM,KAAK,CAACT,IAAI,CAAC,IAAI,CAAC;EACvC,CAAC;EAEDF,IAAI,CAACK,SAAS,CAACQ,SAAS,GAAG,SAASA,SAASA,CAAA,EAAI;IAC7C,IAAI,IAAI,CAACJ,IAAI,EAAE;MACX,IAAI,CAACA,IAAI,CAACG,cAAc,CAAC,IAAI,CAAC;MAC9B,OAAO,IAAI,CAACH,IAAI;IACpB;EACJ,CAAC;EAEDT,IAAI,CAACK,SAAS,CAACS,OAAO,GAAG,SAASA,OAAOA,CAAEC,GAAG,EAAE;IAC5C,IAAI,IAAI,CAACN,IAAI,EAAE;MACXM,GAAG,CAACC,SAAS,CAAC,CAAC;MAEf,IAAIC,QAAQ,GAAG,IAAInB,QAAQ,CAAC,IAAI,CAACW,IAAI,CAACS,QAAQ,CAAC,CAAC,IAAI,CAACT,IAAI,CAAC;MAC1DQ,QAAQ,CAACE,YAAY,CAACJ,GAAG,EAAE,IAAI,CAACN,IAAI,CAAC;MAErCM,GAAG,CAACN,IAAI,CAAC,SAAS,CAAC;IACvB;EACJ,CAAC;EAEDT,IAAI,CAACK,SAAS,CAACe,aAAa,GAAG,SAASA,aAAaA,CAAEC,CAAC,EAAE;IACtD,IAAIA,CAAC,CAACC,KAAK,KAAK,MAAM,EAAE;MACpB,IAAI,CAACT,SAAS,CAAC,CAAC;MAChB,IAAI,CAACV,QAAQ,CAAC,CAAC;IACnB;IAEAN,QAAQ,CAACQ,SAAS,CAACe,aAAa,CAAClB,IAAI,CAAC,IAAI,EAAEmB,CAAC,CAAC;EAClD,CAAC;EAEDrB,IAAI,CAACK,SAAS,CAACkB,YAAY,GAAG,SAASA,YAAYA,CAAER,GAAG,EAAE;IACtD,IAAI,IAAI,CAACd,UAAU,EAAE;MACjB,IAAIuB,SAAS,GAAG,IAAI,CAACvB,UAAU,CAACuB,SAAS,CAAC,CAAC;MAC3C,IAAIA,SAAS,EAAE;QACXT,GAAG,CAACS,SAAS,CAACC,KAAK,CAACV,GAAG,EAAES,SAAS,CAACE,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC;MAC3D;IACJ;EACJ,CAAC;EAED3B,IAAI,CAACK,SAAS,CAACuB,YAAY,GAAG,SAASA,YAAYA,CAAEC,QAAQ,EAAEC,GAAG,EAAEC,IAAI,EAAE;IACtE,IAAIC,MAAM,GAAG,IAAI;IAEjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,QAAQ,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;MACtC,IAAIhC,UAAU,GAAG4B,QAAQ,CAACI,CAAC,CAAC;MAC5B,IAAIE,QAAQ,GAAGlC,UAAU,CAACkC,QAAQ;MAElC,IAAIC,SAAS,GAAG,IAAItC,QAAQ,CAACG,UAAU,CAACiB,QAAQ,CAAC,CAACjB,UAAU,EAAE8B,IAAI,CAAC;MAEnE,IAAII,QAAQ,IAAIA,QAAQ,CAACD,MAAM,GAAG,CAAC,EAAE;QACjCE,SAAS,CAACC,IAAI,CAACF,QAAQ,EAAEL,GAAG,EAAEC,IAAI,CAAC;MACvC;MAEA,IAAIhC,OAAO,CAAC+B,GAAG,CAAC,EAAE;QACdE,MAAM,CAACM,QAAQ,CAACF,SAAS,EAAEN,GAAG,CAAC;MACnC,CAAC,MAAM;QACHE,MAAM,CAACO,MAAM,CAACH,SAAS,CAAC;MAC5B;IACJ;EACJ,CAAC;EAEDpC,IAAI,CAACK,SAAS,CAACgC,IAAI,GAAG,SAASA,IAAIA,CAAER,QAAQ,EAAEC,GAAG,EAAEC,IAAI,EAAE;IACtD,IAAI,CAACH,YAAY,CAACC,QAAQ,EAAEC,GAAG,EAAEC,IAAI,CAAC;IAEtC,IAAI,CAACS,UAAU,CAAC,CAAC;EACrB,CAAC;EAEDxC,IAAI,CAACK,SAAS,CAACoC,UAAU,GAAG,SAASA,UAAUA,CAAE1B,GAAG,EAAE;IAClD,IAAI,IAAI,CAACd,UAAU,EAAE;MACjB,IAAIyC,OAAO,GAAG,IAAI,CAACzC,UAAU,CAACyC,OAAO,CAAC,CAAC;MACvC,IAAI3C,OAAO,CAAC2C,OAAO,CAAC,EAAE;QAClB,IAAI,CAACC,WAAW,CAAC5B,GAAG,EAAE2B,OAAO,CAAC;MAClC;IACJ;EACJ,CAAC;EAED1C,IAAI,CAACK,SAAS,CAACsC,WAAW,GAAG,SAASA,WAAWA,CAAE5B,GAAG,EAAE6B,KAAK,EAAE;IAC3D,IAAIC,QAAQ,GAAGD,KAAK;IACpB,IAAIC,QAAQ,IAAI9B,GAAG,CAAC4B,WAAW,EAAE;MAC7BE,QAAQ,IAAI9B,GAAG,CAAC4B,WAAW;IAC/B;IACA5B,GAAG,CAAC4B,WAAW,GAAGE,QAAQ;EAC9B,CAAC;EAED7C,IAAI,CAACK,SAAS,CAACyC,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;IACzC,IAAIC,GAAG,GAAG,IAAI,CAAC9C,UAAU;IACzB,OAAO,CAAC8C,GAAG,IAAKA,GAAG,IAAIA,GAAG,CAACC,OAAO,CAACF,OAAO,KAAK,KAAM;EACzD,CAAC;EAED,OAAO9C,IAAI;AACf,CAAC,CAACH,QAAQ,CAAE;AAEZ,eAAeG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}