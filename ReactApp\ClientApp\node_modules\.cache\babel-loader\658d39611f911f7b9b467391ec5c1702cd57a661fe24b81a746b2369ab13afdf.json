{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { useUnstyled as d, classNames as n, uDropDownsBase as l } from \"@progress/kendo-react-common\";\nimport * as u from \"react\";\nconst v = t => {\n  const {\n      group: c,\n      virtual: o,\n      render: e,\n      isMultiColumn: s = !1,\n      id: m\n    } = t,\n    r = d(),\n    i = r && r.uDropDownsBase,\n    a = /* @__PURE__ */u.createElement(\"li\", {\n      id: m,\n      role: \"group\",\n      className: n(l.groupLi({\n        c: i,\n        isMultiColumn: s\n      })),\n      style: s ? {\n        boxSizing: \"inherit\",\n        position: o ? \"relative\" : \"unset\"\n      } : {\n        position: o ? \"relative\" : \"unset\"\n      }\n    }, /* @__PURE__ */u.createElement(\"span\", {\n      className: e ? void 0 : n(l.groupItemText({\n        c: i,\n        isMultiColumn: s\n      }))\n    }, c));\n  return e !== void 0 ? e.call(void 0, a, t) : a;\n};\nexport { v as default };", "map": {"version": 3, "names": ["useUnstyled", "d", "classNames", "n", "uDropDownsBase", "l", "u", "v", "t", "group", "c", "virtual", "o", "render", "e", "isMultiColumn", "s", "id", "m", "r", "i", "a", "createElement", "role", "className", "groupLi", "style", "boxSizing", "position", "groupItemText", "call", "default"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dropdowns/common/ListGroupItem.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { useUnstyled as d, classNames as n, uDropDownsBase as l } from \"@progress/kendo-react-common\";\nimport * as u from \"react\";\nconst v = (t) => {\n  const { group: c, virtual: o, render: e, isMultiColumn: s = !1, id: m } = t, r = d(), i = r && r.uDropDownsBase, a = /* @__PURE__ */ u.createElement(\n    \"li\",\n    {\n      id: m,\n      role: \"group\",\n      className: n(l.groupLi({ c: i, isMultiColumn: s })),\n      style: s ? { boxSizing: \"inherit\", position: o ? \"relative\" : \"unset\" } : { position: o ? \"relative\" : \"unset\" }\n    },\n    /* @__PURE__ */ u.createElement(\n      \"span\",\n      {\n        className: e ? void 0 : n(l.groupItemText({ c: i, isMultiColumn: s }))\n      },\n      c\n    )\n  );\n  return e !== void 0 ? e.call(void 0, a, t) : a;\n};\nexport {\n  v as default\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,WAAW,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,EAAEC,cAAc,IAAIC,CAAC,QAAQ,8BAA8B;AACrG,OAAO,KAAKC,CAAC,MAAM,OAAO;AAC1B,MAAMC,CAAC,GAAIC,CAAC,IAAK;EACf,MAAM;MAAEC,KAAK,EAAEC,CAAC;MAAEC,OAAO,EAAEC,CAAC;MAAEC,MAAM,EAAEC,CAAC;MAAEC,aAAa,EAAEC,CAAC,GAAG,CAAC,CAAC;MAAEC,EAAE,EAAEC;IAAE,CAAC,GAAGV,CAAC;IAAEW,CAAC,GAAGlB,CAAC,CAAC,CAAC;IAAEmB,CAAC,GAAGD,CAAC,IAAIA,CAAC,CAACf,cAAc;IAAEiB,CAAC,GAAG,eAAgBf,CAAC,CAACgB,aAAa,CAClJ,IAAI,EACJ;MACEL,EAAE,EAAEC,CAAC;MACLK,IAAI,EAAE,OAAO;MACbC,SAAS,EAAErB,CAAC,CAACE,CAAC,CAACoB,OAAO,CAAC;QAAEf,CAAC,EAAEU,CAAC;QAAEL,aAAa,EAAEC;MAAE,CAAC,CAAC,CAAC;MACnDU,KAAK,EAAEV,CAAC,GAAG;QAAEW,SAAS,EAAE,SAAS;QAAEC,QAAQ,EAAEhB,CAAC,GAAG,UAAU,GAAG;MAAQ,CAAC,GAAG;QAAEgB,QAAQ,EAAEhB,CAAC,GAAG,UAAU,GAAG;MAAQ;IACjH,CAAC,EACD,eAAgBN,CAAC,CAACgB,aAAa,CAC7B,MAAM,EACN;MACEE,SAAS,EAAEV,CAAC,GAAG,KAAK,CAAC,GAAGX,CAAC,CAACE,CAAC,CAACwB,aAAa,CAAC;QAAEnB,CAAC,EAAEU,CAAC;QAAEL,aAAa,EAAEC;MAAE,CAAC,CAAC;IACvE,CAAC,EACDN,CACF,CACF,CAAC;EACD,OAAOI,CAAC,KAAK,KAAK,CAAC,GAAGA,CAAC,CAACgB,IAAI,CAAC,KAAK,CAAC,EAAET,CAAC,EAAEb,CAAC,CAAC,GAAGa,CAAC;AAChD,CAAC;AACD,SACEd,CAAC,IAAIwB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}