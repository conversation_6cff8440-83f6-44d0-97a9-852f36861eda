{"ast": null, "code": "import GeometryCircle from '../geometry/circle';\nimport paintable from '../mixins/paintable';\nimport measurable from '../mixins/measurable';\nimport withGeometry from '../mixins/with-geometry';\nimport Element from './element';\nimport { defined } from '../util';\nvar DEFAULT_STROKE = \"#000\";\nvar Circle = function (superclass) {\n  function Circle(geometry, options) {\n    if (geometry === void 0) geometry = new GeometryCircle();\n    if (options === void 0) options = {};\n    superclass.call(this, options);\n    this.geometry(geometry);\n    if (!defined(this.options.stroke)) {\n      this.stroke(DEFAULT_STROKE);\n    }\n  }\n  if (superclass) Circle.__proto__ = superclass;\n  Circle.prototype = Object.create(superclass && superclass.prototype);\n  Circle.prototype.constructor = Circle;\n  var prototypeAccessors = {\n    nodeType: {\n      configurable: true\n    }\n  };\n  prototypeAccessors.nodeType.get = function () {\n    return \"Circle\";\n  };\n  Circle.prototype.rawBBox = function rawBBox() {\n    return this._geometry.bbox();\n  };\n  Circle.prototype._bbox = function _bbox(matrix) {\n    return this._geometry.bbox(matrix);\n  };\n  Circle.prototype._containsPoint = function _containsPoint(point) {\n    return this.geometry().containsPoint(point);\n  };\n  Circle.prototype._isOnPath = function _isOnPath(point) {\n    return this.geometry()._isOnPath(point, this.options.stroke.width / 2);\n  };\n  Object.defineProperties(Circle.prototype, prototypeAccessors);\n  return Circle;\n}(paintable(measurable(withGeometry(Element))));\nexport default Circle;", "map": {"version": 3, "names": ["GeometryCircle", "paintable", "measurable", "withGeometry", "Element", "defined", "DEFAULT_STROKE", "Circle", "superclass", "geometry", "options", "call", "stroke", "__proto__", "prototype", "Object", "create", "constructor", "prototypeAccessors", "nodeType", "configurable", "get", "rawBBox", "_geometry", "bbox", "_bbox", "matrix", "_containsPoint", "point", "containsPoint", "_isOnPath", "width", "defineProperties"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/shapes/circle.js"], "sourcesContent": ["import GeometryCircle from '../geometry/circle';\nimport paintable from '../mixins/paintable';\nimport measurable from '../mixins/measurable';\nimport withGeometry from '../mixins/with-geometry';\nimport Element from './element';\nimport { defined } from '../util';\n\nvar DEFAULT_STROKE = \"#000\";\n\nvar Circle = (function (superclass) {\n    function Circle(geometry, options) {\n        if ( geometry === void 0 ) geometry = new GeometryCircle();\n        if ( options === void 0 ) options = {};\n\n        superclass.call(this, options);\n\n        this.geometry(geometry);\n\n        if (!defined(this.options.stroke)) {\n            this.stroke(DEFAULT_STROKE);\n        }\n    }\n\n    if ( superclass ) Circle.__proto__ = superclass;\n    Circle.prototype = Object.create( superclass && superclass.prototype );\n    Circle.prototype.constructor = Circle;\n\n    var prototypeAccessors = { nodeType: { configurable: true } };\n\n    prototypeAccessors.nodeType.get = function () {\n        return \"Circle\";\n    };\n\n    Circle.prototype.rawBBox = function rawBBox () {\n        return this._geometry.bbox();\n    };\n\n    Circle.prototype._bbox = function _bbox (matrix) {\n        return this._geometry.bbox(matrix);\n    };\n\n    Circle.prototype._containsPoint = function _containsPoint (point) {\n        return this.geometry().containsPoint(point);\n    };\n\n    Circle.prototype._isOnPath = function _isOnPath (point) {\n        return this.geometry()._isOnPath(point, this.options.stroke.width / 2);\n    };\n\n    Object.defineProperties( Circle.prototype, prototypeAccessors );\n\n    return Circle;\n}(paintable(measurable(withGeometry(Element)))));\n\nexport default Circle;\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,oBAAoB;AAC/C,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,UAAU,MAAM,sBAAsB;AAC7C,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASC,OAAO,QAAQ,SAAS;AAEjC,IAAIC,cAAc,GAAG,MAAM;AAE3B,IAAIC,MAAM,GAAI,UAAUC,UAAU,EAAE;EAChC,SAASD,MAAMA,CAACE,QAAQ,EAAEC,OAAO,EAAE;IAC/B,IAAKD,QAAQ,KAAK,KAAK,CAAC,EAAGA,QAAQ,GAAG,IAAIT,cAAc,CAAC,CAAC;IAC1D,IAAKU,OAAO,KAAK,KAAK,CAAC,EAAGA,OAAO,GAAG,CAAC,CAAC;IAEtCF,UAAU,CAACG,IAAI,CAAC,IAAI,EAAED,OAAO,CAAC;IAE9B,IAAI,CAACD,QAAQ,CAACA,QAAQ,CAAC;IAEvB,IAAI,CAACJ,OAAO,CAAC,IAAI,CAACK,OAAO,CAACE,MAAM,CAAC,EAAE;MAC/B,IAAI,CAACA,MAAM,CAACN,cAAc,CAAC;IAC/B;EACJ;EAEA,IAAKE,UAAU,EAAGD,MAAM,CAACM,SAAS,GAAGL,UAAU;EAC/CD,MAAM,CAACO,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAER,UAAU,IAAIA,UAAU,CAACM,SAAU,CAAC;EACtEP,MAAM,CAACO,SAAS,CAACG,WAAW,GAAGV,MAAM;EAErC,IAAIW,kBAAkB,GAAG;IAAEC,QAAQ,EAAE;MAAEC,YAAY,EAAE;IAAK;EAAE,CAAC;EAE7DF,kBAAkB,CAACC,QAAQ,CAACE,GAAG,GAAG,YAAY;IAC1C,OAAO,QAAQ;EACnB,CAAC;EAEDd,MAAM,CAACO,SAAS,CAACQ,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;IAC3C,OAAO,IAAI,CAACC,SAAS,CAACC,IAAI,CAAC,CAAC;EAChC,CAAC;EAEDjB,MAAM,CAACO,SAAS,CAACW,KAAK,GAAG,SAASA,KAAKA,CAAEC,MAAM,EAAE;IAC7C,OAAO,IAAI,CAACH,SAAS,CAACC,IAAI,CAACE,MAAM,CAAC;EACtC,CAAC;EAEDnB,MAAM,CAACO,SAAS,CAACa,cAAc,GAAG,SAASA,cAAcA,CAAEC,KAAK,EAAE;IAC9D,OAAO,IAAI,CAACnB,QAAQ,CAAC,CAAC,CAACoB,aAAa,CAACD,KAAK,CAAC;EAC/C,CAAC;EAEDrB,MAAM,CAACO,SAAS,CAACgB,SAAS,GAAG,SAASA,SAASA,CAAEF,KAAK,EAAE;IACpD,OAAO,IAAI,CAACnB,QAAQ,CAAC,CAAC,CAACqB,SAAS,CAACF,KAAK,EAAE,IAAI,CAAClB,OAAO,CAACE,MAAM,CAACmB,KAAK,GAAG,CAAC,CAAC;EAC1E,CAAC;EAEDhB,MAAM,CAACiB,gBAAgB,CAAEzB,MAAM,CAACO,SAAS,EAAEI,kBAAmB,CAAC;EAE/D,OAAOX,MAAM;AACjB,CAAC,CAACN,SAAS,CAACC,UAAU,CAACC,YAAY,CAACC,OAAO,CAAC,CAAC,CAAC,CAAE;AAEhD,eAAeG,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}