{"ast": null, "code": "/* Copyright 2022 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar _CommandManager_commands, _CommandManager_locked, _CommandManager_maxSize, _CommandManager_position;\nimport { __classPrivateFieldGet, __classPrivateFieldSet } from \"tslib\";\n/*\n * Class to handle undo/redo.\n * Commands are just saved in a buffer.\n * If we hit some memory issues we could likely use a circular buffer.\n * It has to be used as a singleton.\n */\nexport class CommandManager {\n  constructor(maxSize = 128) {\n    _CommandManager_commands.set(this, []);\n    _CommandManager_locked.set(this, false);\n    _CommandManager_maxSize.set(this, void 0);\n    _CommandManager_position.set(this, -1);\n    __classPrivateFieldSet(this, _CommandManager_maxSize, maxSize, \"f\");\n  }\n  /**\n   * @typedef {Object} addOptions\n   * @property {function} cmd\n   * @property {function} undo\n   * @property {function} [post]\n   * @property {boolean} mustExec\n   * @property {number} type\n   * @property {boolean} overwriteIfSameType\n   * @property {boolean} keepUndo\n   */\n  /*\n   * Add a new couple of commands to be used in case of redo/undo.\n   * @param {addOptions} options\n   */\n  add({\n    cmd,\n    undo,\n    post,\n    mustExec,\n    type = NaN,\n    overwriteIfSameType = false,\n    keepUndo = false\n  }) {\n    if (mustExec) {\n      cmd();\n    }\n    if (__classPrivateFieldGet(this, _CommandManager_locked, \"f\")) {\n      return;\n    }\n    const save = {\n      cmd,\n      undo,\n      post,\n      type\n    };\n    if (__classPrivateFieldGet(this, _CommandManager_position, \"f\") === -1) {\n      if (__classPrivateFieldGet(this, _CommandManager_commands, \"f\").length > 0) {\n        // All the commands have been undone and then a new one is added\n        // hence we clear the queue.\n        __classPrivateFieldGet(this, _CommandManager_commands, \"f\").length = 0;\n      }\n      __classPrivateFieldSet(this, _CommandManager_position, 0, \"f\");\n      __classPrivateFieldGet(this, _CommandManager_commands, \"f\").push(save);\n      return;\n    }\n    if (overwriteIfSameType && __classPrivateFieldGet(this, _CommandManager_commands, \"f\")[__classPrivateFieldGet(this, _CommandManager_position, \"f\")].type === type) {\n      // For example when we change a color we don't want to\n      // be able to undo all the steps, hence we only want to\n      // keep the last undoable action in this sequence of actions.\n      if (keepUndo) {\n        save.undo = __classPrivateFieldGet(this, _CommandManager_commands, \"f\")[__classPrivateFieldGet(this, _CommandManager_position, \"f\")].undo;\n      }\n      __classPrivateFieldGet(this, _CommandManager_commands, \"f\")[__classPrivateFieldGet(this, _CommandManager_position, \"f\")] = save;\n      return;\n    }\n    const next = __classPrivateFieldGet(this, _CommandManager_position, \"f\") + 1;\n    if (next === __classPrivateFieldGet(this, _CommandManager_maxSize, \"f\")) {\n      __classPrivateFieldGet(this, _CommandManager_commands, \"f\").splice(0, 1);\n    } else {\n      __classPrivateFieldSet(this, _CommandManager_position, next, \"f\");\n      if (next < __classPrivateFieldGet(this, _CommandManager_commands, \"f\").length) {\n        __classPrivateFieldGet(this, _CommandManager_commands, \"f\").splice(next);\n      }\n    }\n    __classPrivateFieldGet(this, _CommandManager_commands, \"f\").push(save);\n  }\n  /**\n   * Undo the last command.\n   */\n  undo() {\n    if (__classPrivateFieldGet(this, _CommandManager_position, \"f\") === -1) {\n      // Nothing to undo.\n      return;\n    }\n    // Avoid to insert something during the undo execution.\n    __classPrivateFieldSet(this, _CommandManager_locked, true, \"f\");\n    const {\n      undo,\n      post\n    } = __classPrivateFieldGet(this, _CommandManager_commands, \"f\")[__classPrivateFieldGet(this, _CommandManager_position, \"f\")];\n    undo();\n    post === null || post === void 0 ? void 0 : post();\n    __classPrivateFieldSet(this, _CommandManager_locked, false, \"f\");\n    __classPrivateFieldSet(this, _CommandManager_position, __classPrivateFieldGet(this, _CommandManager_position, \"f\") - 1, \"f\");\n  }\n  /**\n   * Redo the last command.\n   */\n  redo() {\n    if (__classPrivateFieldGet(this, _CommandManager_position, \"f\") < __classPrivateFieldGet(this, _CommandManager_commands, \"f\").length - 1) {\n      __classPrivateFieldSet(this, _CommandManager_position, __classPrivateFieldGet(this, _CommandManager_position, \"f\") + 1, \"f\");\n      // Avoid to insert something during the redo execution.\n      __classPrivateFieldSet(this, _CommandManager_locked, true, \"f\");\n      const {\n        cmd,\n        post\n      } = __classPrivateFieldGet(this, _CommandManager_commands, \"f\")[__classPrivateFieldGet(this, _CommandManager_position, \"f\")];\n      cmd();\n      post === null || post === void 0 ? void 0 : post();\n      __classPrivateFieldSet(this, _CommandManager_locked, false, \"f\");\n    }\n  }\n  /**\n   * Check if there is something to undo.\n   * @returns {boolean}\n   */\n  hasSomethingToUndo() {\n    return __classPrivateFieldGet(this, _CommandManager_position, \"f\") !== -1;\n  }\n  /**\n   * Check if there is something to redo.\n   * @returns {boolean}\n   */\n  hasSomethingToRedo() {\n    return __classPrivateFieldGet(this, _CommandManager_position, \"f\") < __classPrivateFieldGet(this, _CommandManager_commands, \"f\").length - 1;\n  }\n  destroy() {\n    __classPrivateFieldSet(this, _CommandManager_commands, null, \"f\");\n  }\n}\n_CommandManager_commands = new WeakMap(), _CommandManager_locked = new WeakMap(), _CommandManager_maxSize = new WeakMap(), _CommandManager_position = new WeakMap();", "map": {"version": 3, "names": ["_CommandManager_commands", "_CommandManager_locked", "_CommandManager_maxSize", "_CommandManager_position", "__classPrivateFieldGet", "__classPrivateFieldSet", "CommandManager", "constructor", "maxSize", "set", "add", "cmd", "undo", "post", "mustExec", "type", "NaN", "overwriteIfSameType", "keepUndo", "save", "length", "push", "next", "splice", "redo", "hasSomethingToUndo", "hasSomethingToRedo", "destroy", "WeakMap"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-pdfviewer-common/dist/es/annotations/helpers/command-manager.js"], "sourcesContent": ["/* Copyright 2022 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar _CommandManager_commands, _CommandManager_locked, _CommandManager_maxSize, _CommandManager_position;\nimport { __classPrivateFieldGet, __classPrivateFieldSet } from \"tslib\";\n/*\n * Class to handle undo/redo.\n * Commands are just saved in a buffer.\n * If we hit some memory issues we could likely use a circular buffer.\n * It has to be used as a singleton.\n */\nexport class CommandManager {\n    constructor(maxSize = 128) {\n        _CommandManager_commands.set(this, []);\n        _CommandManager_locked.set(this, false);\n        _CommandManager_maxSize.set(this, void 0);\n        _CommandManager_position.set(this, -1);\n        __classPrivateFieldSet(this, _CommandManager_maxSize, maxSize, \"f\");\n    }\n    /**\n     * @typedef {Object} addOptions\n     * @property {function} cmd\n     * @property {function} undo\n     * @property {function} [post]\n     * @property {boolean} mustExec\n     * @property {number} type\n     * @property {boolean} overwriteIfSameType\n     * @property {boolean} keepUndo\n     */\n    /*\n     * Add a new couple of commands to be used in case of redo/undo.\n     * @param {addOptions} options\n     */\n    add({ cmd, undo, post, mustExec, type = NaN, overwriteIfSameType = false, keepUndo = false }) {\n        if (mustExec) {\n            cmd();\n        }\n        if (__classPrivateFieldGet(this, _CommandManager_locked, \"f\")) {\n            return;\n        }\n        const save = { cmd, undo, post, type };\n        if (__classPrivateFieldGet(this, _CommandManager_position, \"f\") === -1) {\n            if (__classPrivateFieldGet(this, _CommandManager_commands, \"f\").length > 0) {\n                // All the commands have been undone and then a new one is added\n                // hence we clear the queue.\n                __classPrivateFieldGet(this, _CommandManager_commands, \"f\").length = 0;\n            }\n            __classPrivateFieldSet(this, _CommandManager_position, 0, \"f\");\n            __classPrivateFieldGet(this, _CommandManager_commands, \"f\").push(save);\n            return;\n        }\n        if (overwriteIfSameType && __classPrivateFieldGet(this, _CommandManager_commands, \"f\")[__classPrivateFieldGet(this, _CommandManager_position, \"f\")].type === type) {\n            // For example when we change a color we don't want to\n            // be able to undo all the steps, hence we only want to\n            // keep the last undoable action in this sequence of actions.\n            if (keepUndo) {\n                save.undo = __classPrivateFieldGet(this, _CommandManager_commands, \"f\")[__classPrivateFieldGet(this, _CommandManager_position, \"f\")].undo;\n            }\n            __classPrivateFieldGet(this, _CommandManager_commands, \"f\")[__classPrivateFieldGet(this, _CommandManager_position, \"f\")] = save;\n            return;\n        }\n        const next = __classPrivateFieldGet(this, _CommandManager_position, \"f\") + 1;\n        if (next === __classPrivateFieldGet(this, _CommandManager_maxSize, \"f\")) {\n            __classPrivateFieldGet(this, _CommandManager_commands, \"f\").splice(0, 1);\n        }\n        else {\n            __classPrivateFieldSet(this, _CommandManager_position, next, \"f\");\n            if (next < __classPrivateFieldGet(this, _CommandManager_commands, \"f\").length) {\n                __classPrivateFieldGet(this, _CommandManager_commands, \"f\").splice(next);\n            }\n        }\n        __classPrivateFieldGet(this, _CommandManager_commands, \"f\").push(save);\n    }\n    /**\n     * Undo the last command.\n     */\n    undo() {\n        if (__classPrivateFieldGet(this, _CommandManager_position, \"f\") === -1) {\n            // Nothing to undo.\n            return;\n        }\n        // Avoid to insert something during the undo execution.\n        __classPrivateFieldSet(this, _CommandManager_locked, true, \"f\");\n        const { undo, post } = __classPrivateFieldGet(this, _CommandManager_commands, \"f\")[__classPrivateFieldGet(this, _CommandManager_position, \"f\")];\n        undo();\n        post === null || post === void 0 ? void 0 : post();\n        __classPrivateFieldSet(this, _CommandManager_locked, false, \"f\");\n        __classPrivateFieldSet(this, _CommandManager_position, __classPrivateFieldGet(this, _CommandManager_position, \"f\") - 1, \"f\");\n    }\n    /**\n     * Redo the last command.\n     */\n    redo() {\n        if (__classPrivateFieldGet(this, _CommandManager_position, \"f\") < __classPrivateFieldGet(this, _CommandManager_commands, \"f\").length - 1) {\n            __classPrivateFieldSet(this, _CommandManager_position, __classPrivateFieldGet(this, _CommandManager_position, \"f\") + 1, \"f\");\n            // Avoid to insert something during the redo execution.\n            __classPrivateFieldSet(this, _CommandManager_locked, true, \"f\");\n            const { cmd, post } = __classPrivateFieldGet(this, _CommandManager_commands, \"f\")[__classPrivateFieldGet(this, _CommandManager_position, \"f\")];\n            cmd();\n            post === null || post === void 0 ? void 0 : post();\n            __classPrivateFieldSet(this, _CommandManager_locked, false, \"f\");\n        }\n    }\n    /**\n     * Check if there is something to undo.\n     * @returns {boolean}\n     */\n    hasSomethingToUndo() {\n        return __classPrivateFieldGet(this, _CommandManager_position, \"f\") !== -1;\n    }\n    /**\n     * Check if there is something to redo.\n     * @returns {boolean}\n     */\n    hasSomethingToRedo() {\n        return __classPrivateFieldGet(this, _CommandManager_position, \"f\") < __classPrivateFieldGet(this, _CommandManager_commands, \"f\").length - 1;\n    }\n    destroy() {\n        __classPrivateFieldSet(this, _CommandManager_commands, null, \"f\");\n    }\n}\n_CommandManager_commands = new WeakMap(), _CommandManager_locked = new WeakMap(), _CommandManager_maxSize = new WeakMap(), _CommandManager_position = new WeakMap();\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,wBAAwB,EAAEC,sBAAsB,EAAEC,uBAAuB,EAAEC,wBAAwB;AACvG,SAASC,sBAAsB,EAAEC,sBAAsB,QAAQ,OAAO;AACtE;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,cAAc,CAAC;EACxBC,WAAWA,CAACC,OAAO,GAAG,GAAG,EAAE;IACvBR,wBAAwB,CAACS,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;IACtCR,sBAAsB,CAACQ,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC;IACvCP,uBAAuB,CAACO,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACzCN,wBAAwB,CAACM,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IACtCJ,sBAAsB,CAAC,IAAI,EAAEH,uBAAuB,EAAEM,OAAO,EAAE,GAAG,CAAC;EACvE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI;AACJ;AACA;AACA;EACIE,GAAGA,CAAC;IAAEC,GAAG;IAAEC,IAAI;IAAEC,IAAI;IAAEC,QAAQ;IAAEC,IAAI,GAAGC,GAAG;IAAEC,mBAAmB,GAAG,KAAK;IAAEC,QAAQ,GAAG;EAAM,CAAC,EAAE;IAC1F,IAAIJ,QAAQ,EAAE;MACVH,GAAG,CAAC,CAAC;IACT;IACA,IAAIP,sBAAsB,CAAC,IAAI,EAAEH,sBAAsB,EAAE,GAAG,CAAC,EAAE;MAC3D;IACJ;IACA,MAAMkB,IAAI,GAAG;MAAER,GAAG;MAAEC,IAAI;MAAEC,IAAI;MAAEE;IAAK,CAAC;IACtC,IAAIX,sBAAsB,CAAC,IAAI,EAAED,wBAAwB,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MACpE,IAAIC,sBAAsB,CAAC,IAAI,EAAEJ,wBAAwB,EAAE,GAAG,CAAC,CAACoB,MAAM,GAAG,CAAC,EAAE;QACxE;QACA;QACAhB,sBAAsB,CAAC,IAAI,EAAEJ,wBAAwB,EAAE,GAAG,CAAC,CAACoB,MAAM,GAAG,CAAC;MAC1E;MACAf,sBAAsB,CAAC,IAAI,EAAEF,wBAAwB,EAAE,CAAC,EAAE,GAAG,CAAC;MAC9DC,sBAAsB,CAAC,IAAI,EAAEJ,wBAAwB,EAAE,GAAG,CAAC,CAACqB,IAAI,CAACF,IAAI,CAAC;MACtE;IACJ;IACA,IAAIF,mBAAmB,IAAIb,sBAAsB,CAAC,IAAI,EAAEJ,wBAAwB,EAAE,GAAG,CAAC,CAACI,sBAAsB,CAAC,IAAI,EAAED,wBAAwB,EAAE,GAAG,CAAC,CAAC,CAACY,IAAI,KAAKA,IAAI,EAAE;MAC/J;MACA;MACA;MACA,IAAIG,QAAQ,EAAE;QACVC,IAAI,CAACP,IAAI,GAAGR,sBAAsB,CAAC,IAAI,EAAEJ,wBAAwB,EAAE,GAAG,CAAC,CAACI,sBAAsB,CAAC,IAAI,EAAED,wBAAwB,EAAE,GAAG,CAAC,CAAC,CAACS,IAAI;MAC7I;MACAR,sBAAsB,CAAC,IAAI,EAAEJ,wBAAwB,EAAE,GAAG,CAAC,CAACI,sBAAsB,CAAC,IAAI,EAAED,wBAAwB,EAAE,GAAG,CAAC,CAAC,GAAGgB,IAAI;MAC/H;IACJ;IACA,MAAMG,IAAI,GAAGlB,sBAAsB,CAAC,IAAI,EAAED,wBAAwB,EAAE,GAAG,CAAC,GAAG,CAAC;IAC5E,IAAImB,IAAI,KAAKlB,sBAAsB,CAAC,IAAI,EAAEF,uBAAuB,EAAE,GAAG,CAAC,EAAE;MACrEE,sBAAsB,CAAC,IAAI,EAAEJ,wBAAwB,EAAE,GAAG,CAAC,CAACuB,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5E,CAAC,MACI;MACDlB,sBAAsB,CAAC,IAAI,EAAEF,wBAAwB,EAAEmB,IAAI,EAAE,GAAG,CAAC;MACjE,IAAIA,IAAI,GAAGlB,sBAAsB,CAAC,IAAI,EAAEJ,wBAAwB,EAAE,GAAG,CAAC,CAACoB,MAAM,EAAE;QAC3EhB,sBAAsB,CAAC,IAAI,EAAEJ,wBAAwB,EAAE,GAAG,CAAC,CAACuB,MAAM,CAACD,IAAI,CAAC;MAC5E;IACJ;IACAlB,sBAAsB,CAAC,IAAI,EAAEJ,wBAAwB,EAAE,GAAG,CAAC,CAACqB,IAAI,CAACF,IAAI,CAAC;EAC1E;EACA;AACJ;AACA;EACIP,IAAIA,CAAA,EAAG;IACH,IAAIR,sBAAsB,CAAC,IAAI,EAAED,wBAAwB,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MACpE;MACA;IACJ;IACA;IACAE,sBAAsB,CAAC,IAAI,EAAEJ,sBAAsB,EAAE,IAAI,EAAE,GAAG,CAAC;IAC/D,MAAM;MAAEW,IAAI;MAAEC;IAAK,CAAC,GAAGT,sBAAsB,CAAC,IAAI,EAAEJ,wBAAwB,EAAE,GAAG,CAAC,CAACI,sBAAsB,CAAC,IAAI,EAAED,wBAAwB,EAAE,GAAG,CAAC,CAAC;IAC/IS,IAAI,CAAC,CAAC;IACNC,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC,CAAC;IAClDR,sBAAsB,CAAC,IAAI,EAAEJ,sBAAsB,EAAE,KAAK,EAAE,GAAG,CAAC;IAChEI,sBAAsB,CAAC,IAAI,EAAEF,wBAAwB,EAAEC,sBAAsB,CAAC,IAAI,EAAED,wBAAwB,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC;EAChI;EACA;AACJ;AACA;EACIqB,IAAIA,CAAA,EAAG;IACH,IAAIpB,sBAAsB,CAAC,IAAI,EAAED,wBAAwB,EAAE,GAAG,CAAC,GAAGC,sBAAsB,CAAC,IAAI,EAAEJ,wBAAwB,EAAE,GAAG,CAAC,CAACoB,MAAM,GAAG,CAAC,EAAE;MACtIf,sBAAsB,CAAC,IAAI,EAAEF,wBAAwB,EAAEC,sBAAsB,CAAC,IAAI,EAAED,wBAAwB,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC;MAC5H;MACAE,sBAAsB,CAAC,IAAI,EAAEJ,sBAAsB,EAAE,IAAI,EAAE,GAAG,CAAC;MAC/D,MAAM;QAAEU,GAAG;QAAEE;MAAK,CAAC,GAAGT,sBAAsB,CAAC,IAAI,EAAEJ,wBAAwB,EAAE,GAAG,CAAC,CAACI,sBAAsB,CAAC,IAAI,EAAED,wBAAwB,EAAE,GAAG,CAAC,CAAC;MAC9IQ,GAAG,CAAC,CAAC;MACLE,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC,CAAC;MAClDR,sBAAsB,CAAC,IAAI,EAAEJ,sBAAsB,EAAE,KAAK,EAAE,GAAG,CAAC;IACpE;EACJ;EACA;AACJ;AACA;AACA;EACIwB,kBAAkBA,CAAA,EAAG;IACjB,OAAOrB,sBAAsB,CAAC,IAAI,EAAED,wBAAwB,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;EAC7E;EACA;AACJ;AACA;AACA;EACIuB,kBAAkBA,CAAA,EAAG;IACjB,OAAOtB,sBAAsB,CAAC,IAAI,EAAED,wBAAwB,EAAE,GAAG,CAAC,GAAGC,sBAAsB,CAAC,IAAI,EAAEJ,wBAAwB,EAAE,GAAG,CAAC,CAACoB,MAAM,GAAG,CAAC;EAC/I;EACAO,OAAOA,CAAA,EAAG;IACNtB,sBAAsB,CAAC,IAAI,EAAEL,wBAAwB,EAAE,IAAI,EAAE,GAAG,CAAC;EACrE;AACJ;AACAA,wBAAwB,GAAG,IAAI4B,OAAO,CAAC,CAAC,EAAE3B,sBAAsB,GAAG,IAAI2B,OAAO,CAAC,CAAC,EAAE1B,uBAAuB,GAAG,IAAI0B,OAAO,CAAC,CAAC,EAAEzB,wBAAwB,GAAG,IAAIyB,OAAO,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}