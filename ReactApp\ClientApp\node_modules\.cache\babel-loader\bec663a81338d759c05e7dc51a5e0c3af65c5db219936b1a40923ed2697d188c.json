{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport o from \"prop-types\";\nimport { classNames as n } from \"@progress/kendo-react-common\";\nimport { cardActionsLayout as i, cardOrientation as a } from \"./interfaces/Enums.mjs\";\nconst s = r => {\n  const t = {\n    layout: i.START,\n    orientation: a.HORIZONTAL,\n    ...r\n  };\n  return /* @__PURE__ */e.createElement(\"div\", {\n    style: t.style,\n    className: n(\"k-card-actions\", \"k-actions\", t.className, `k-actions-${t.layout}`, t.orientation !== a.VERTICAL ? \"k-actions-horizontal\" : \"k-actions-vertical\")\n  }, t.children);\n};\ns.propTypes = {\n  layout: o.oneOf([\"stretched\", \"start\", \"center\", \"end\"]),\n  orientation: o.oneOf([\"horizontal\", \"vertical\"])\n};\nexport { s as CardActions };", "map": {"version": 3, "names": ["e", "o", "classNames", "n", "cardActionsLayout", "i", "cardOrientation", "a", "s", "r", "t", "layout", "START", "orientation", "HORIZONTAL", "createElement", "style", "className", "VERTICAL", "children", "propTypes", "oneOf", "CardActions"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/card/CardActions.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport o from \"prop-types\";\nimport { classNames as n } from \"@progress/kendo-react-common\";\nimport { cardActionsLayout as i, cardOrientation as a } from \"./interfaces/Enums.mjs\";\nconst s = (r) => {\n  const t = {\n    layout: i.START,\n    orientation: a.HORIZONTAL,\n    ...r\n  };\n  return /* @__PURE__ */ e.createElement(\n    \"div\",\n    {\n      style: t.style,\n      className: n(\n        \"k-card-actions\",\n        \"k-actions\",\n        t.className,\n        `k-actions-${t.layout}`,\n        t.orientation !== a.VERTICAL ? \"k-actions-horizontal\" : \"k-actions-vertical\"\n      )\n    },\n    t.children\n  );\n};\ns.propTypes = {\n  layout: o.oneOf([\"stretched\", \"start\", \"center\", \"end\"]),\n  orientation: o.oneOf([\"horizontal\", \"vertical\"])\n};\nexport {\n  s as CardActions\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AAC9D,SAASC,iBAAiB,IAAIC,CAAC,EAAEC,eAAe,IAAIC,CAAC,QAAQ,wBAAwB;AACrF,MAAMC,CAAC,GAAIC,CAAC,IAAK;EACf,MAAMC,CAAC,GAAG;IACRC,MAAM,EAAEN,CAAC,CAACO,KAAK;IACfC,WAAW,EAAEN,CAAC,CAACO,UAAU;IACzB,GAAGL;EACL,CAAC;EACD,OAAO,eAAgBT,CAAC,CAACe,aAAa,CACpC,KAAK,EACL;IACEC,KAAK,EAAEN,CAAC,CAACM,KAAK;IACdC,SAAS,EAAEd,CAAC,CACV,gBAAgB,EAChB,WAAW,EACXO,CAAC,CAACO,SAAS,EACX,aAAaP,CAAC,CAACC,MAAM,EAAE,EACvBD,CAAC,CAACG,WAAW,KAAKN,CAAC,CAACW,QAAQ,GAAG,sBAAsB,GAAG,oBAC1D;EACF,CAAC,EACDR,CAAC,CAACS,QACJ,CAAC;AACH,CAAC;AACDX,CAAC,CAACY,SAAS,GAAG;EACZT,MAAM,EAAEV,CAAC,CAACoB,KAAK,CAAC,CAAC,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;EACxDR,WAAW,EAAEZ,CAAC,CAACoB,KAAK,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC;AACjD,CAAC;AACD,SACEb,CAAC,IAAIc,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}