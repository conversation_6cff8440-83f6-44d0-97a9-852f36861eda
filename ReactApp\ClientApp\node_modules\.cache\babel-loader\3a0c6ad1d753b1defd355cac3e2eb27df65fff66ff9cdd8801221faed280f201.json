{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nvar _typeof = require(\"@babel/runtime/helpers/typeof\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = useNotification;\nvar _toConsumableArray2 = _interopRequireDefault(require(\"@babel/runtime/helpers/toConsumableArray\"));\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _slicedToArray2 = _interopRequireDefault(require(\"@babel/runtime/helpers/slicedToArray\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _Notice = _interopRequireDefault(require(\"./Notice\"));\nfunction _getRequireWildcardCache(nodeInterop) {\n  if (typeof WeakMap !== \"function\") return null;\n  var cacheBabelInterop = new WeakMap();\n  var cacheNodeInterop = new WeakMap();\n  return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) {\n    return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n  })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n  if (!nodeInterop && obj && obj.__esModule) {\n    return obj;\n  }\n  if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") {\n    return {\n      default: obj\n    };\n  }\n  var cache = _getRequireWildcardCache(nodeInterop);\n  if (cache && cache.has(obj)) {\n    return cache.get(obj);\n  }\n  var newObj = {};\n  var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var key in obj) {\n    if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n      var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n      if (desc && (desc.get || desc.set)) {\n        Object.defineProperty(newObj, key, desc);\n      } else {\n        newObj[key] = obj[key];\n      }\n    }\n  }\n  newObj.default = obj;\n  if (cache) {\n    cache.set(obj, newObj);\n  }\n  return newObj;\n}\nfunction useNotification(notificationInstance) {\n  var createdRef = React.useRef({});\n  var _React$useState = React.useState([]),\n    _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),\n    elements = _React$useState2[0],\n    setElements = _React$useState2[1];\n  function notify(noticeProps) {\n    var firstMount = true;\n    notificationInstance.add(noticeProps, function (div, props) {\n      var key = props.key;\n      if (div && (!createdRef.current[key] || firstMount)) {\n        var noticeEle = /*#__PURE__*/React.createElement(_Notice.default, (0, _extends2.default)({}, props, {\n          holder: div\n        }));\n        createdRef.current[key] = noticeEle;\n        setElements(function (originElements) {\n          var index = originElements.findIndex(function (ele) {\n            return ele.key === props.key;\n          });\n          if (index === -1) {\n            return [].concat((0, _toConsumableArray2.default)(originElements), [noticeEle]);\n          }\n          var cloneList = (0, _toConsumableArray2.default)(originElements);\n          cloneList[index] = noticeEle;\n          return cloneList;\n        });\n      }\n      firstMount = false;\n    });\n  }\n  return [notify, /*#__PURE__*/React.createElement(React.Fragment, null, elements)];\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "_typeof", "Object", "defineProperty", "exports", "value", "default", "useNotification", "_toConsumableArray2", "_extends2", "_slicedToArray2", "React", "_interopRequireWildcard", "_Notice", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "notificationInstance", "createdRef", "useRef", "_React$useState", "useState", "_React$useState2", "elements", "setElements", "notify", "noticeProps", "firstMount", "add", "div", "props", "current", "noticeEle", "createElement", "holder", "originElements", "index", "findIndex", "ele", "concat", "cloneList", "Fragment"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/rc-notification/lib/useNotification.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nvar _typeof = require(\"@babel/runtime/helpers/typeof\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = useNotification;\nvar _toConsumableArray2 = _interopRequireDefault(require(\"@babel/runtime/helpers/toConsumableArray\"));\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _slicedToArray2 = _interopRequireDefault(require(\"@babel/runtime/helpers/slicedToArray\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _Notice = _interopRequireDefault(require(\"./Notice\"));\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\nfunction useNotification(notificationInstance) {\n  var createdRef = React.useRef({});\n  var _React$useState = React.useState([]),\n    _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),\n    elements = _React$useState2[0],\n    setElements = _React$useState2[1];\n  function notify(noticeProps) {\n    var firstMount = true;\n    notificationInstance.add(noticeProps, function (div, props) {\n      var key = props.key;\n      if (div && (!createdRef.current[key] || firstMount)) {\n        var noticeEle = /*#__PURE__*/React.createElement(_Notice.default, (0, _extends2.default)({}, props, {\n          holder: div\n        }));\n        createdRef.current[key] = noticeEle;\n        setElements(function (originElements) {\n          var index = originElements.findIndex(function (ele) {\n            return ele.key === props.key;\n          });\n          if (index === -1) {\n            return [].concat((0, _toConsumableArray2.default)(originElements), [noticeEle]);\n          }\n          var cloneList = (0, _toConsumableArray2.default)(originElements);\n          cloneList[index] = noticeEle;\n          return cloneList;\n        });\n      }\n      firstMount = false;\n    });\n  }\n  return [notify, /*#__PURE__*/React.createElement(React.Fragment, null, elements)];\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AACpF,IAAIC,OAAO,GAAGD,OAAO,CAAC,+BAA+B,CAAC;AACtDE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,eAAe;AACjC,IAAIC,mBAAmB,GAAGT,sBAAsB,CAACC,OAAO,CAAC,0CAA0C,CAAC,CAAC;AACrG,IAAIS,SAAS,GAAGV,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIU,eAAe,GAAGX,sBAAsB,CAACC,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAC7F,IAAIW,KAAK,GAAGC,uBAAuB,CAACZ,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIa,OAAO,GAAGd,sBAAsB,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC;AACzD,SAASc,wBAAwBA,CAACC,WAAW,EAAE;EAAE,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAIC,iBAAiB,GAAG,IAAID,OAAO,CAAC,CAAC;EAAE,IAAIE,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;EAAE,OAAO,CAACF,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,WAAW,EAAE;IAAE,OAAOA,WAAW,GAAGG,gBAAgB,GAAGD,iBAAiB;EAAE,CAAC,EAAEF,WAAW,CAAC;AAAE;AAC9U,SAASH,uBAAuBA,CAACO,GAAG,EAAEJ,WAAW,EAAE;EAAE,IAAI,CAACA,WAAW,IAAII,GAAG,IAAIA,GAAG,CAACC,UAAU,EAAE;IAAE,OAAOD,GAAG;EAAE;EAAE,IAAIA,GAAG,KAAK,IAAI,IAAIlB,OAAO,CAACkB,GAAG,CAAC,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;IAAE,OAAO;MAAEb,OAAO,EAAEa;IAAI,CAAC;EAAE;EAAE,IAAIE,KAAK,GAAGP,wBAAwB,CAACC,WAAW,CAAC;EAAE,IAAIM,KAAK,IAAIA,KAAK,CAACC,GAAG,CAACH,GAAG,CAAC,EAAE;IAAE,OAAOE,KAAK,CAACE,GAAG,CAACJ,GAAG,CAAC;EAAE;EAAE,IAAIK,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIC,qBAAqB,GAAGvB,MAAM,CAACC,cAAc,IAAID,MAAM,CAACwB,wBAAwB;EAAE,KAAK,IAAIC,GAAG,IAAIR,GAAG,EAAE;IAAE,IAAIQ,GAAG,KAAK,SAAS,IAAIzB,MAAM,CAAC0B,SAAS,CAACC,cAAc,CAACC,IAAI,CAACX,GAAG,EAAEQ,GAAG,CAAC,EAAE;MAAE,IAAII,IAAI,GAAGN,qBAAqB,GAAGvB,MAAM,CAACwB,wBAAwB,CAACP,GAAG,EAAEQ,GAAG,CAAC,GAAG,IAAI;MAAE,IAAII,IAAI,KAAKA,IAAI,CAACR,GAAG,IAAIQ,IAAI,CAACC,GAAG,CAAC,EAAE;QAAE9B,MAAM,CAACC,cAAc,CAACqB,MAAM,EAAEG,GAAG,EAAEI,IAAI,CAAC;MAAE,CAAC,MAAM;QAAEP,MAAM,CAACG,GAAG,CAAC,GAAGR,GAAG,CAACQ,GAAG,CAAC;MAAE;IAAE;EAAE;EAAEH,MAAM,CAAClB,OAAO,GAAGa,GAAG;EAAE,IAAIE,KAAK,EAAE;IAAEA,KAAK,CAACW,GAAG,CAACb,GAAG,EAAEK,MAAM,CAAC;EAAE;EAAE,OAAOA,MAAM;AAAE;AACryB,SAASjB,eAAeA,CAAC0B,oBAAoB,EAAE;EAC7C,IAAIC,UAAU,GAAGvB,KAAK,CAACwB,MAAM,CAAC,CAAC,CAAC,CAAC;EACjC,IAAIC,eAAe,GAAGzB,KAAK,CAAC0B,QAAQ,CAAC,EAAE,CAAC;IACtCC,gBAAgB,GAAG,CAAC,CAAC,EAAE5B,eAAe,CAACJ,OAAO,EAAE8B,eAAe,EAAE,CAAC,CAAC;IACnEG,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC9BE,WAAW,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACnC,SAASG,MAAMA,CAACC,WAAW,EAAE;IAC3B,IAAIC,UAAU,GAAG,IAAI;IACrBV,oBAAoB,CAACW,GAAG,CAACF,WAAW,EAAE,UAAUG,GAAG,EAAEC,KAAK,EAAE;MAC1D,IAAInB,GAAG,GAAGmB,KAAK,CAACnB,GAAG;MACnB,IAAIkB,GAAG,KAAK,CAACX,UAAU,CAACa,OAAO,CAACpB,GAAG,CAAC,IAAIgB,UAAU,CAAC,EAAE;QACnD,IAAIK,SAAS,GAAG,aAAarC,KAAK,CAACsC,aAAa,CAACpC,OAAO,CAACP,OAAO,EAAE,CAAC,CAAC,EAAEG,SAAS,CAACH,OAAO,EAAE,CAAC,CAAC,EAAEwC,KAAK,EAAE;UAClGI,MAAM,EAAEL;QACV,CAAC,CAAC,CAAC;QACHX,UAAU,CAACa,OAAO,CAACpB,GAAG,CAAC,GAAGqB,SAAS;QACnCR,WAAW,CAAC,UAAUW,cAAc,EAAE;UACpC,IAAIC,KAAK,GAAGD,cAAc,CAACE,SAAS,CAAC,UAAUC,GAAG,EAAE;YAClD,OAAOA,GAAG,CAAC3B,GAAG,KAAKmB,KAAK,CAACnB,GAAG;UAC9B,CAAC,CAAC;UACF,IAAIyB,KAAK,KAAK,CAAC,CAAC,EAAE;YAChB,OAAO,EAAE,CAACG,MAAM,CAAC,CAAC,CAAC,EAAE/C,mBAAmB,CAACF,OAAO,EAAE6C,cAAc,CAAC,EAAE,CAACH,SAAS,CAAC,CAAC;UACjF;UACA,IAAIQ,SAAS,GAAG,CAAC,CAAC,EAAEhD,mBAAmB,CAACF,OAAO,EAAE6C,cAAc,CAAC;UAChEK,SAAS,CAACJ,KAAK,CAAC,GAAGJ,SAAS;UAC5B,OAAOQ,SAAS;QAClB,CAAC,CAAC;MACJ;MACAb,UAAU,GAAG,KAAK;IACpB,CAAC,CAAC;EACJ;EACA,OAAO,CAACF,MAAM,EAAE,aAAa9B,KAAK,CAACsC,aAAa,CAACtC,KAAK,CAAC8C,QAAQ,EAAE,IAAI,EAAElB,QAAQ,CAAC,CAAC;AACnF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}