{"ast": null, "code": "import { Direction } from \"./direction.enum\";\nimport { dayOfWeek } from './day-of-week';\n/**\n * A function which returns a date by a specific week name. For example, `Day.Monday`.\n *\n * @param date - The date to calculate from.\n * @param weekDay - The `Day` enum specifying the desired week day.\n * @returns - A `Date` instance.\n *\n * @example\n * ```ts-no-run\n * nextDayOfWeek(new Date(2016, 0, 1), Day.Wednesday); // 2016-01-06, Wednesday\n * ```\n */\nexport var nextDayOfWeek = function (date, weekDay) {\n  return dayOfWeek(date, weekDay, Direction.Forward);\n};", "map": {"version": 3, "names": ["Direction", "dayOfWeek", "nextDayOfWeek", "date", "weekDay", "Forward"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/next-day-of-week.js"], "sourcesContent": ["import { Direction } from \"./direction.enum\";\nimport { dayOfWeek } from './day-of-week';\n/**\n * A function which returns a date by a specific week name. For example, `Day.Monday`.\n *\n * @param date - The date to calculate from.\n * @param weekDay - The `Day` enum specifying the desired week day.\n * @returns - A `Date` instance.\n *\n * @example\n * ```ts-no-run\n * nextDayOfWeek(new Date(2016, 0, 1), Day.Wednesday); // 2016-01-06, Wednesday\n * ```\n */\nexport var nextDayOfWeek = function (date, weekDay) {\n    return dayOfWeek(date, weekDay, Direction.Forward);\n};\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,SAAS,QAAQ,eAAe;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,aAAa,GAAG,SAAAA,CAAUC,IAAI,EAAEC,OAAO,EAAE;EAChD,OAAOH,SAAS,CAACE,IAAI,EAAEC,OAAO,EAAEJ,SAAS,CAACK,OAAO,CAAC;AACtD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}