{"ast": null, "code": "import { parseInlineStyles } from '@progress/kendo-common';\nexport var setStyle = function (element, styleString) {\n  var styles = parseInlineStyles(styleString);\n  Object.keys(styles).forEach(function (key) {\n    element.style[key] = styles[key];\n  });\n};\nvar styleAttr = 'data-style';\nexport var replaceStyleAttr = function (html) {\n  return (html || '').replace(/\\sstyle=/g, ' ' + styleAttr + '=');\n};\nexport var restoreStyleAttr = function (container) {\n  Array.from(container.querySelectorAll('[' + styleAttr + ']')).forEach(function (element) {\n    var styleString = element.getAttribute(styleAttr);\n    element.removeAttribute(styleAttr);\n    setStyle(element, styleString);\n  });\n};\nexport var setInnerHTML = function (container, html) {\n  container.innerHTML = replaceStyleAttr(html);\n  restoreStyleAttr(container);\n};", "map": {"version": 3, "names": ["parseInlineStyles", "setStyle", "element", "styleString", "styles", "Object", "keys", "for<PERSON>ach", "key", "style", "styleAttr", "replaceStyleAttr", "html", "replace", "restoreStyleAttr", "container", "Array", "from", "querySelectorAll", "getAttribute", "removeAttribute", "setInnerHTML", "innerHTML"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/util/element-set-styles-safe.js"], "sourcesContent": ["import { parseInlineStyles } from '@progress/kendo-common';\r\n\r\nexport var setStyle = function (element, styleString) {\r\n    var styles = parseInlineStyles(styleString);\r\n    Object.keys(styles).forEach(function (key) {\r\n        element.style[key] = styles[key];\r\n    });\r\n};\r\n\r\nvar styleAttr = 'data-style';\r\nexport var replaceStyleAttr = function (html) { return (html || '').replace(/\\sstyle=/g, ' ' + styleAttr + '='); };\r\nexport var restoreStyleAttr = function (container) {\r\n    Array.from(container.querySelectorAll('[' + styleAttr +']')).forEach(function (element) {\r\n        var styleString = element.getAttribute(styleAttr);\r\n        element.removeAttribute(styleAttr);\r\n        setStyle(element, styleString);\r\n    });\r\n};\r\n\r\nexport var setInnerHTML = function (container, html) {\r\n    container.innerHTML = replaceStyleAttr(html);\r\n    restoreStyleAttr(container);\r\n};\r\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,wBAAwB;AAE1D,OAAO,IAAIC,QAAQ,GAAG,SAAAA,CAAUC,OAAO,EAAEC,WAAW,EAAE;EAClD,IAAIC,MAAM,GAAGJ,iBAAiB,CAACG,WAAW,CAAC;EAC3CE,MAAM,CAACC,IAAI,CAACF,MAAM,CAAC,CAACG,OAAO,CAAC,UAAUC,GAAG,EAAE;IACvCN,OAAO,CAACO,KAAK,CAACD,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;EACpC,CAAC,CAAC;AACN,CAAC;AAED,IAAIE,SAAS,GAAG,YAAY;AAC5B,OAAO,IAAIC,gBAAgB,GAAG,SAAAA,CAAUC,IAAI,EAAE;EAAE,OAAO,CAACA,IAAI,IAAI,EAAE,EAAEC,OAAO,CAAC,WAAW,EAAE,GAAG,GAAGH,SAAS,GAAG,GAAG,CAAC;AAAE,CAAC;AAClH,OAAO,IAAII,gBAAgB,GAAG,SAAAA,CAAUC,SAAS,EAAE;EAC/CC,KAAK,CAACC,IAAI,CAACF,SAAS,CAACG,gBAAgB,CAAC,GAAG,GAAGR,SAAS,GAAE,GAAG,CAAC,CAAC,CAACH,OAAO,CAAC,UAAUL,OAAO,EAAE;IACpF,IAAIC,WAAW,GAAGD,OAAO,CAACiB,YAAY,CAACT,SAAS,CAAC;IACjDR,OAAO,CAACkB,eAAe,CAACV,SAAS,CAAC;IAClCT,QAAQ,CAACC,OAAO,EAAEC,WAAW,CAAC;EAClC,CAAC,CAAC;AACN,CAAC;AAED,OAAO,IAAIkB,YAAY,GAAG,SAAAA,CAAUN,SAAS,EAAEH,IAAI,EAAE;EACjDG,SAAS,CAACO,SAAS,GAAGX,gBAAgB,CAACC,IAAI,CAAC;EAC5CE,gBAAgB,CAACC,SAAS,CAAC;AAC/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}