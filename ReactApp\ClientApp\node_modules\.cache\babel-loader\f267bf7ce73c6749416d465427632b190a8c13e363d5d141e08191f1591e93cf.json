{"ast": null, "code": "/* eslint-disable no-multi-spaces, key-spacing, indent, camelcase, space-before-blocks, eqeqeq, brace-style */\n/* eslint-disable space-infix-ops, space-before-function-paren, array-bracket-spacing, object-curly-spacing */\n/* eslint-disable no-nested-ternary, max-params, default-case, no-else-return, no-empty */\n/* eslint-disable no-param-reassign, no-var, block-scoped-var */\n\nimport { createPromise } from \"../util\";\nimport { parseColor as utils_parseColor, saveAs as util_saveAs, support } from \"../common\";\nimport * as PDF from \"./core\";\nimport { Group, Path, MultiPath, Gradient, RadialGradient, Pattern } from '../drawing';\nimport * as geo from \"../geometry\";\nvar DEFAULT_IMAGE_DPI = 300;\nvar TEXT_RENDERING_MODE = PDF.TEXT_RENDERING_MODE;\nvar DASH_PATTERNS = {\n  dash: [4],\n  dashDot: [4, 2, 1, 2],\n  dot: [1, 2],\n  longDash: [8, 2],\n  longDashDot: [8, 2, 1, 2],\n  longDashDotDot: [8, 2, 1, 2, 1, 2],\n  solid: []\n};\nvar LINE_CAP = {\n  butt: 0,\n  round: 1,\n  square: 2\n};\nvar LINE_JOIN = {\n  miter: 0,\n  round: 1,\n  bevel: 2\n};\nfunction render(group, callback) {\n  var fonts = [],\n    images = {},\n    options = group.options;\n  function getOption(name, defval, hash) {\n    if (!hash) {\n      hash = options;\n    }\n    if (hash.pdf && hash.pdf[name] != null) {\n      return hash.pdf[name];\n    }\n    return defval;\n  }\n  var multiPage = getOption(\"multiPage\");\n  var imgDPI = getOption(\"imgDPI\", DEFAULT_IMAGE_DPI);\n  PDF.clearImageCache();\n  var handlers = {\n    Image: function (element) {\n      var url = element.src();\n      var size = element.bbox().size;\n      if (imgDPI) {\n        var prev = images[url];\n        size = {\n          width: Math.ceil(size.width * imgDPI / 72),\n          height: Math.ceil(size.height * imgDPI / 72)\n        };\n        if (prev) {\n          size.width = Math.max(prev.width, size.width);\n          size.height = Math.max(prev.height, size.height);\n        }\n      }\n      images[url] = size;\n    },\n    Text: function (element) {\n      var style = PDF.parseFontDef(element.options.font);\n      var url = PDF.getFontURL(style);\n      if (fonts.indexOf(url) < 0) {\n        fonts.push(url);\n      }\n    }\n  };\n  group.traverse(function (element) {\n    dispatch(handlers, element);\n    var fill = element.fill && element.fill();\n    if (fill instanceof Pattern) {\n      fill.traverse(function (child) {\n        dispatch(handlers, child);\n      });\n    }\n  });\n  function doIt() {\n    if (--count > 0) {\n      return;\n    }\n    var pdf = new PDF.Document({\n      producer: getOption(\"producer\"),\n      title: getOption(\"title\"),\n      author: getOption(\"author\"),\n      subject: getOption(\"subject\"),\n      keywords: getOption(\"keywords\"),\n      creator: getOption(\"creator\"),\n      date: getOption(\"date\"),\n      autoPrint: getOption(\"autoPrint\")\n    });\n    function drawPage(group) {\n      var options = group.options;\n      var tmp = optimize(group);\n      var bbox = tmp.bbox;\n      group = tmp.root;\n      // var tmp, bbox;\n\n      var paperSize = getOption(\"paperSize\", getOption(\"paperSize\", \"auto\"), options),\n        addMargin = false;\n      if (paperSize == \"auto\") {\n        if (bbox) {\n          var size = bbox.getSize();\n          paperSize = [size.width, size.height];\n          addMargin = true;\n          var origin = bbox.getOrigin();\n          tmp = new Group();\n          tmp.transform(new geo.Matrix(1, 0, 0, 1, -origin.x, -origin.y));\n          tmp.append(group);\n          group = tmp;\n        } else {\n          paperSize = \"A4\";\n        }\n      }\n      var page;\n      page = pdf.addPage({\n        paperSize: paperSize,\n        margin: getOption(\"margin\", getOption(\"margin\"), options),\n        addMargin: addMargin,\n        landscape: getOption(\"landscape\", getOption(\"landscape\", false), options)\n      });\n      drawElement(group, page, pdf);\n    }\n    if (multiPage) {\n      group.children.forEach(drawPage);\n    } else {\n      drawPage(group);\n    }\n    callback(pdf.render(), pdf);\n  }\n  var count = 2;\n  PDF.loadFonts(fonts, doIt);\n  PDF.loadImages(images, doIt, {\n    jpegQuality: getOption(\"jpegQuality\", 0.92),\n    keepPNG: getOption(\"keepPNG\", false)\n  });\n}\nfunction toDataURL(group, callback) {\n  render(group, function (data) {\n    callback(\"data:application/pdf;base64,\" + data.base64());\n  });\n}\nfunction toBlob(group, callback) {\n  render(group, function (data) {\n    callback(new window.Blob([data.get()], {\n      type: \"application/pdf\"\n    }));\n  });\n}\nfunction saveAs(group, filename, proxy, callback) {\n  // XXX: Safari has Blob, but does not support the download attribute\n  //      so we'd end up converting to dataURL and using the proxy anyway.\n  if (window.Blob && !support.browser.safari) {\n    toBlob(group, function (blob) {\n      util_saveAs({\n        dataURI: blob,\n        fileName: filename\n      });\n      if (callback) {\n        callback(blob);\n      }\n    });\n  } else {\n    toDataURL(group, function (dataURL) {\n      util_saveAs({\n        dataURI: dataURL,\n        fileName: filename,\n        proxyURL: proxy\n      });\n      if (callback) {\n        callback(dataURL);\n      }\n    });\n  }\n}\nfunction dispatch(handlers, element) {\n  var handler = handlers[element.nodeType];\n  if (handler) {\n    return handler.call.apply(handler, arguments);\n  }\n  return element;\n}\nfunction drawElement(element, page, pdf) {\n  if (element.options._pdfDebug) {\n    page.comment(\"BEGIN: \" + element.options._pdfDebug);\n  }\n  var transform = element.transform();\n  var opacity = element.opacity();\n  page.save();\n  if (opacity != null && opacity < 1) {\n    page.setOpacity(opacity);\n  }\n  setStrokeOptions(element, page, pdf);\n  setFillOptions(element, page, pdf);\n  if (transform) {\n    var m = transform.matrix();\n    page.transform(m.a, m.b, m.c, m.d, m.e, m.f);\n  }\n  setClipping(element, page, pdf);\n  dispatch({\n    Path: drawPath,\n    MultiPath: drawMultiPath,\n    Circle: drawCircle,\n    Arc: drawArc,\n    Text: drawText,\n    Image: drawImage,\n    Group: drawGroup,\n    Rect: drawRect\n  }, element, page, pdf);\n  page.restore();\n  if (element.options._pdfDebug) {\n    page.comment(\"END: \" + element.options._pdfDebug);\n  }\n}\nfunction setStrokeOptions(element, page) {\n  var stroke = element.stroke && element.stroke();\n  if (!stroke) {\n    return;\n  }\n  var color = stroke.color;\n  if (color) {\n    color = parseColor(color);\n    if (color == null) {\n      return; // no stroke\n    }\n    page.setStrokeColor(color.r, color.g, color.b);\n    if (color.a != 1) {\n      page.setStrokeOpacity(color.a);\n    }\n  }\n  var width = stroke.width;\n  if (width != null) {\n    if (width === 0) {\n      return; // no stroke\n    }\n    page.setLineWidth(width);\n  }\n  var dashType = stroke.dashType;\n  if (dashType) {\n    page.setDashPattern(DASH_PATTERNS[dashType], 0);\n  }\n  var lineCap = stroke.lineCap;\n  if (lineCap) {\n    page.setLineCap(LINE_CAP[lineCap]);\n  }\n  var lineJoin = stroke.lineJoin;\n  if (lineJoin) {\n    page.setLineJoin(LINE_JOIN[lineJoin]);\n  }\n  var opacity = stroke.opacity;\n  if (opacity != null) {\n    page.setStrokeOpacity(opacity);\n  }\n}\nfunction setFillOptions(element, page) {\n  var fill = element.fill && element.fill();\n  if (!fill) {\n    return;\n  }\n  if (fill instanceof Gradient || fill instanceof Pattern) {\n    return;\n  }\n  var color = fill.color;\n  if (color) {\n    color = parseColor(color);\n    if (color == null) {\n      return; // no fill\n    }\n    page.setFillColor(color.r, color.g, color.b);\n    if (color.a != 1) {\n      page.setFillOpacity(color.a);\n    }\n  }\n  var opacity = fill.opacity;\n  if (opacity != null) {\n    page.setFillOpacity(opacity);\n  }\n}\nfunction setClipping(element, page, pdf) {\n  // XXX: only Path supported at the moment.\n  var clip = element.clip();\n  if (clip) {\n    _drawPath(clip, page, pdf);\n    page.clip();\n    // page.setStrokeColor(Math.random(), Math.random(), Math.random());\n    // page.setLineWidth(1);\n    // page.stroke();\n  }\n}\nfunction shouldDraw(thing) {\n  return thing && (thing instanceof Gradient || thing instanceof Pattern || thing.color && !/^(none|transparent)$/i.test(thing.color) && (thing.width == null || thing.width > 0) && (thing.opacity == null || thing.opacity > 0));\n}\nfunction maybeGradient(element, page, pdf, stroke) {\n  var fill = element.fill();\n  if (fill instanceof Gradient) {\n    if (stroke) {\n      page.clipStroke();\n    } else {\n      page.clip();\n    }\n    var isRadial = fill instanceof RadialGradient;\n    var start, end;\n    if (isRadial) {\n      start = {\n        x: fill.center().x,\n        y: fill.center().y,\n        r: 0\n      };\n      end = {\n        x: fill.center().x,\n        y: fill.center().y,\n        r: fill.radius()\n      };\n    } else {\n      start = {\n        x: fill.start().x,\n        y: fill.start().y\n      };\n      end = {\n        x: fill.end().x,\n        y: fill.end().y\n      };\n    }\n    var stops = fill.stops.elements().map(function (stop) {\n      var offset = stop.offset();\n      if (/%$/.test(offset)) {\n        offset = parseFloat(offset) / 100;\n      } else {\n        offset = parseFloat(offset);\n      }\n      var color = parseColor(stop.color());\n      color.a *= stop.opacity();\n      return {\n        offset: offset,\n        color: color\n      };\n    });\n\n    // Duplicats first and last stop to fix\n    // https://github.com/telerik/kendo-ui-core/issues/1782\n    stops.unshift(stops[0]);\n    stops.push(stops[stops.length - 1]);\n    var gradient = {\n      userSpace: fill.userSpace(),\n      type: isRadial ? \"radial\" : \"linear\",\n      start: start,\n      end: end,\n      stops: stops\n    };\n    var box = element.rawBBox();\n    var tl = box.topLeft(),\n      size = box.getSize();\n    box = {\n      left: tl.x,\n      top: tl.y,\n      width: size.width,\n      height: size.height\n    };\n    page.gradient(gradient, box);\n    return true;\n  }\n}\nfunction maybePattern(element, page, pdf, stroke) {\n  var fill = element.fill();\n  if (fill instanceof Pattern) {\n    if (stroke) {\n      page.clipStroke();\n    } else {\n      page.clip();\n    }\n    var box = element.rawBBox();\n    var tl = box.topLeft(),\n      size = box.getSize();\n    var strokeWidth = element.stroke() ? element.stroke().width : 0;\n    page.pattern(fill, {\n      left: tl.x + strokeWidth / 2,\n      top: tl.y + strokeWidth / 2,\n      width: size.width - strokeWidth,\n      height: size.height - strokeWidth\n    }, drawPattern);\n    return true;\n  }\n}\nfunction maybeFillStroke(element, page, pdf) {\n  if (shouldDraw(element.fill()) && shouldDraw(element.stroke())) {\n    if (!maybeGradient(element, page, pdf, true) && !maybePattern(element, page, pdf, true)) {\n      page.fillStroke();\n    }\n  } else if (shouldDraw(element.fill())) {\n    if (!maybeGradient(element, page, pdf, false) && !maybePattern(element, page, pdf, false)) {\n      page.fill();\n    }\n  } else if (shouldDraw(element.stroke())) {\n    page.stroke();\n  } else {\n    // we should not get here; the path should have been\n    // optimized away.  but let's be prepared.\n    page.nop();\n  }\n}\nfunction maybeDrawRect(path, page) {\n  var segments = path.segments;\n  if (segments.length == 4 && path.options.closed) {\n    // detect if this path looks like a rectangle parallel to the axis\n    var a = [];\n    for (var i = 0; i < segments.length; ++i) {\n      if (segments[i].controlIn()) {\n        // has curve?\n        return false;\n      }\n      a[i] = segments[i].anchor();\n    }\n    // it's a rectangle if the y/x/y/x or x/y/x/y coords of\n    // consecutive points are the same.\n    var isRect = a[0].y == a[1].y && a[1].x == a[2].x && a[2].y == a[3].y && a[3].x == a[0].x || a[0].x == a[1].x && a[1].y == a[2].y && a[2].x == a[3].x && a[3].y == a[0].y;\n    if (isRect) {\n      // this saves a bunch of instructions in PDF:\n      // moveTo, lineTo, lineTo, lineTo, close -> rect.\n      page.rect(a[0].x, a[0].y, a[2].x - a[0].x /*width*/, a[2].y - a[0].y /*height*/);\n      return true;\n    }\n  }\n}\nfunction _drawPath(element, page, pdf) {\n  var segments = element.segments;\n  if (segments.length === 0) {\n    return;\n  }\n  if (!maybeDrawRect(element, page, pdf)) {\n    for (var prev, i = 0; i < segments.length; ++i) {\n      var seg = segments[i];\n      var anchor = seg.anchor();\n      if (!prev) {\n        page.moveTo(anchor.x, anchor.y);\n      } else {\n        var prevOut = prev.controlOut();\n        var controlIn = seg.controlIn();\n        if (prevOut && controlIn) {\n          page.bezier(prevOut.x, prevOut.y, controlIn.x, controlIn.y, anchor.x, anchor.y);\n        } else {\n          page.lineTo(anchor.x, anchor.y);\n        }\n      }\n      prev = seg;\n    }\n    if (element.options.closed) {\n      page.close();\n    }\n  }\n}\nfunction drawPath(element, page, pdf) {\n  _drawPath(element, page, pdf);\n  maybeFillStroke(element, page, pdf);\n}\nfunction drawMultiPath(element, page, pdf) {\n  var paths = element.paths;\n  for (var i = 0; i < paths.length; ++i) {\n    _drawPath(paths[i], page, pdf);\n  }\n  maybeFillStroke(element, page, pdf);\n}\nfunction drawCircle(element, page, pdf) {\n  var g = element.geometry();\n  page.circle(g.center.x, g.center.y, g.radius);\n  maybeFillStroke(element, page, pdf);\n}\nfunction drawArc(element, page, pdf) {\n  var points = element.geometry().curvePoints();\n  page.moveTo(points[0].x, points[0].y);\n  for (var i = 1; i < points.length;) {\n    page.bezier(points[i].x, points[i++].y, points[i].x, points[i++].y, points[i].x, points[i++].y);\n  }\n  maybeFillStroke(element, page, pdf);\n}\nfunction drawText(element, page) {\n  var style = PDF.parseFontDef(element.options.font);\n  var pos = element._position;\n  var mode;\n  page.transform(1, 0, 0, -1, pos.x, pos.y + style.fontSize);\n  var draw = function (renderMode) {\n    page.beginText();\n    page.setFont(PDF.getFontURL(style), style.fontSize);\n    page.setTextRenderingMode(renderMode);\n    page.showText(element.content(), element._pdfRect ? element._pdfRect.width() : null);\n  };\n  if (element.fill() && element.stroke()) {\n    mode = TEXT_RENDERING_MODE.fillAndStroke;\n    if (element.options.paintOrder === \"stroke\") {\n      draw(TEXT_RENDERING_MODE.stroke);\n      mode = TEXT_RENDERING_MODE.fill;\n    }\n  } else if (element.fill()) {\n    mode = TEXT_RENDERING_MODE.fill;\n  } else if (element.stroke()) {\n    mode = TEXT_RENDERING_MODE.stroke;\n  }\n  draw(mode);\n  page.endText();\n}\nfunction drawPattern(pattern, page, pdf) {\n  var children = pattern.children;\n  for (var i = 0; i < children.length; ++i) {\n    drawElement(children[i], page, pdf);\n  }\n}\nfunction drawGroup(element, page, pdf) {\n  if (element._pdfLink) {\n    page.addLink(element._pdfLink.url, element._pdfLink);\n  }\n  var children = element.children;\n  for (var i = 0; i < children.length; ++i) {\n    drawElement(children[i], page, pdf);\n  }\n}\nfunction drawImage(element, page) {\n  var url = element.src();\n  if (!url) {\n    return;\n  }\n  var rect = element.rect();\n  var tl = rect.getOrigin();\n  var sz = rect.getSize();\n  page.transform(sz.width, 0, 0, -sz.height, tl.x, tl.y + sz.height);\n  page.drawImage(url);\n}\nfunction drawRect(element, page, pdf) {\n  var geometry = element.geometry();\n  var ref = geometry.cornerRadius;\n  var rx = ref[0];\n  var ry = ref[1];\n  if (rx === 0 && ry === 0) {\n    page.rect(geometry.origin.x, geometry.origin.y, geometry.size.width, geometry.size.height);\n    maybeFillStroke(element, page, pdf);\n  } else {\n    drawPath(Path.fromRect(geometry, element.options), page, pdf);\n  }\n}\nfunction parseColor(value) {\n  var color = utils_parseColor(value, true);\n  return color ? color.toRGB() : null;\n}\nfunction optimize(root) {\n  var clipbox = false;\n  var matrix = geo.Matrix.unit();\n  var currentBox = null;\n  var changed;\n  do {\n    changed = false;\n    root = opt(root);\n  } while (root && changed);\n  return {\n    root: root,\n    bbox: currentBox\n  };\n  function change(newShape) {\n    changed = true;\n    return newShape;\n  }\n  function visible(shape) {\n    return shape.visible() && shape.opacity() > 0 && (shouldDraw(shape.fill()) || shouldDraw(shape.stroke()));\n  }\n  function optArray(a) {\n    var b = [];\n    for (var i = 0; i < a.length; ++i) {\n      var el = opt(a[i]);\n      if (el != null) {\n        b.push(el);\n      }\n    }\n    return b;\n  }\n  function withClipping(shape, f) {\n    var saveclipbox = clipbox;\n    var savematrix = matrix;\n    if (shape.transform()) {\n      matrix = matrix.multiplyCopy(shape.transform().matrix());\n    }\n    var clip = shape.clip();\n    if (clip && typeof clip.bbox === 'function') {\n      clip = clip.bbox();\n      if (clip) {\n        clip = clip.bbox(matrix);\n        clipbox = clipbox ? geo.Rect.intersect(clipbox, clip) : clip;\n      }\n    }\n    try {\n      return f();\n    } finally {\n      clipbox = saveclipbox;\n      matrix = savematrix;\n    }\n  }\n  function inClipbox(shape) {\n    if (clipbox == null) {\n      return false;\n    }\n    var box = shape.rawBBox();\n    if (box) {\n      box = box.bbox(matrix);\n    }\n    if (clipbox && box) {\n      box = geo.Rect.intersect(box, clipbox);\n    }\n    return box;\n  }\n  function opt(shape) {\n    return withClipping(shape, function () {\n      if (!(shape instanceof Group || shape instanceof MultiPath)) {\n        var box = inClipbox(shape);\n        if (!box) {\n          return change(null);\n        }\n        currentBox = currentBox ? geo.Rect.union(currentBox, box) : box;\n      }\n      return dispatch({\n        Path: function (shape) {\n          if (shape.segments.length === 0 || !visible(shape)) {\n            return change(null);\n          }\n          return shape;\n        },\n        MultiPath: function (shape) {\n          if (!visible(shape)) {\n            return change(null);\n          }\n          var el = new MultiPath(shape.options);\n          el.paths = optArray(shape.paths);\n          if (el.paths.length === 0) {\n            return change(null);\n          }\n          return el;\n        },\n        Circle: function (shape) {\n          if (!visible(shape)) {\n            return change(null);\n          }\n          return shape;\n        },\n        Arc: function (shape) {\n          if (!visible(shape)) {\n            return change(null);\n          }\n          return shape;\n        },\n        Text: function (shape) {\n          if (!/\\S/.test(shape.content()) || !visible(shape)) {\n            return change(null);\n          }\n          return shape;\n        },\n        Image: function (shape) {\n          if (!(shape.visible() && shape.opacity() > 0)) {\n            return change(null);\n          }\n          return shape;\n        },\n        Group: function (shape) {\n          if (!(shape.visible() && shape.opacity() > 0)) {\n            return change(null);\n          }\n          var el = new Group(shape.options);\n          el.children = optArray(shape.children);\n          el._pdfLink = shape._pdfLink;\n          if (shape !== root && el.children.length === 0 && !shape._pdfLink) {\n            return change(null);\n          }\n          return el;\n        },\n        Rect: function (shape) {\n          if (!visible(shape)) {\n            return change(null);\n          }\n          return shape;\n        }\n      }, shape);\n    });\n  }\n}\nfunction exportPDF(group, options) {\n  var promise = createPromise();\n  for (var i in options) {\n    if (i == \"margin\" && group.options.pdf && group.options.pdf._ignoreMargin) {\n      // This hackish option is set by the page breaking code in drawDOM.  The idea is\n      // that margin is already taken into account there (that's required to do proper\n      // page breaking) and we don't want to set it again here, as it would double the\n      // top-left margin, and truncate the content on bottom/right.\n      continue;\n    }\n    group.options.set(\"pdf.\" + i, options[i]);\n  }\n  toDataURL(group, promise.resolve);\n  return promise;\n}\n\n// XXX: the duplication is in order to keep exportPDF return a data\n// URI, which is what previous versions do.  Currently only IE9 does\n// not support Blob, IMO we should switch to Blob by default\n// everywhere.\nfunction exportPDFToBlob(group, options) {\n  var promise = createPromise();\n  for (var i in options) {\n    if (i == \"margin\" && group.options.pdf && group.options.pdf._ignoreMargin) {\n      // This hackish option is set by the page breaking code in drawDOM.  The idea is\n      // that margin is already taken into account there (that's required to do proper\n      // page breaking) and we don't want to set it again here, as it would double the\n      // top-left margin, and truncate the content on bottom/right.\n      continue;\n    }\n    group.options.set(\"pdf.\" + i, options[i]);\n  }\n  if (window.Blob && !support.browser.safari) {\n    toBlob(group, promise.resolve);\n  } else {\n    toDataURL(group, promise.resolve);\n  }\n  return promise;\n}\nexport { exportPDF, exportPDFToBlob, saveAs, toDataURL, toBlob, render };", "map": {"version": 3, "names": ["createPromise", "parseColor", "utils_parseColor", "saveAs", "util_saveAs", "support", "PDF", "Group", "Path", "MultiPath", "Gradient", "RadialGrad<PERSON>", "Pattern", "geo", "DEFAULT_IMAGE_DPI", "TEXT_RENDERING_MODE", "DASH_PATTERNS", "dash", "dashDot", "dot", "longDash", "longDashDot", "longDashDotDot", "solid", "LINE_CAP", "butt", "round", "square", "LINE_JOIN", "miter", "bevel", "render", "group", "callback", "fonts", "images", "options", "getOption", "name", "defval", "hash", "pdf", "multiPage", "imgDPI", "clearImageCache", "handlers", "Image", "element", "url", "src", "size", "bbox", "prev", "width", "Math", "ceil", "height", "max", "Text", "style", "parseFontDef", "font", "getFontURL", "indexOf", "push", "traverse", "dispatch", "fill", "child", "doIt", "count", "Document", "producer", "title", "author", "subject", "keywords", "creator", "date", "autoPrint", "drawPage", "tmp", "optimize", "root", "paperSize", "add<PERSON><PERSON>gin", "getSize", "origin", "<PERSON><PERSON><PERSON><PERSON>", "transform", "Matrix", "x", "y", "append", "page", "addPage", "margin", "landscape", "drawElement", "children", "for<PERSON>ach", "loadFonts", "loadImages", "jpegQuality", "keepPNG", "toDataURL", "data", "base64", "toBlob", "window", "Blob", "get", "type", "filename", "proxy", "browser", "safari", "blob", "dataURI", "fileName", "dataURL", "proxyURL", "handler", "nodeType", "call", "apply", "arguments", "_pdfDebug", "comment", "opacity", "save", "setOpacity", "setStrokeOptions", "setFillOptions", "m", "matrix", "a", "b", "c", "d", "e", "f", "setClipping", "drawPath", "drawMultiPath", "Circle", "drawCircle", "Arc", "drawArc", "drawText", "drawImage", "drawGroup", "Rect", "drawRect", "restore", "stroke", "color", "setStrokeColor", "r", "g", "setStrokeOpacity", "setLineWidth", "dashType", "setDashPattern", "lineCap", "setLineCap", "lineJoin", "setLineJoin", "setFillColor", "setFillOpacity", "clip", "_drawPath", "shouldDraw", "thing", "test", "maybe<PERSON>rad<PERSON>", "clipStroke", "isRadial", "start", "end", "center", "radius", "stops", "elements", "map", "stop", "offset", "parseFloat", "unshift", "length", "gradient", "userSpace", "box", "rawBBox", "tl", "topLeft", "left", "top", "maybePattern", "strokeWidth", "pattern", "drawPattern", "maybeFillStroke", "fillStroke", "nop", "maybeDrawRect", "path", "segments", "closed", "i", "controlIn", "anchor", "isRect", "rect", "seg", "moveTo", "prevOut", "controlOut", "bezier", "lineTo", "close", "paths", "geometry", "circle", "points", "curvePoints", "pos", "_position", "mode", "fontSize", "draw", "renderMode", "beginText", "setFont", "setTextRenderingMode", "showText", "content", "_pdfRect", "fillAndStroke", "paintOrder", "endText", "_pdfLink", "addLink", "sz", "ref", "cornerRadius", "rx", "ry", "fromRect", "value", "toRGB", "clipbox", "unit", "currentBox", "changed", "opt", "change", "newShape", "visible", "shape", "optArray", "el", "withClipping", "saveclipbox", "savematrix", "multiplyCopy", "intersect", "inClipbox", "union", "exportPDF", "promise", "_ignore<PERSON><PERSON><PERSON>", "set", "resolve", "exportPDFToBlob"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/pdf/drawing.js"], "sourcesContent": ["/* eslint-disable no-multi-spaces, key-spacing, indent, camelcase, space-before-blocks, eqeqeq, brace-style */\n/* eslint-disable space-infix-ops, space-before-function-paren, array-bracket-spacing, object-curly-spacing */\n/* eslint-disable no-nested-ternary, max-params, default-case, no-else-return, no-empty */\n/* eslint-disable no-param-reassign, no-var, block-scoped-var */\n\nimport { createPromise } from \"../util\";\nimport { parseColor as utils_parseColor, saveAs as util_saveAs, support } from \"../common\";\nimport * as PDF from \"./core\";\nimport { Group, Path, MultiPath, Gradient, RadialGradient, Pattern } from '../drawing';\nimport * as geo from \"../geometry\";\n\nvar DEFAULT_IMAGE_DPI = 300;\nvar TEXT_RENDERING_MODE = PDF.TEXT_RENDERING_MODE;\n\nvar DASH_PATTERNS = {\n    dash           : [ 4 ],\n    dashDot        : [ 4, 2, 1, 2 ],\n    dot            : [ 1, 2 ],\n    longDash       : [ 8, 2 ],\n    longDashDot    : [ 8, 2, 1, 2 ],\n    longDashDotDot : [ 8, 2, 1, 2, 1, 2 ],\n    solid          : []\n};\n\nvar LINE_CAP = {\n    butt   : 0,\n    round  : 1,\n    square : 2\n};\n\nvar LINE_JOIN = {\n    miter : 0,\n    round : 1,\n    bevel : 2\n};\n\nfunction render(group, callback) {\n    var fonts = [], images = {}, options = group.options;\n\n    function getOption(name, defval, hash) {\n        if (!hash) {\n            hash = options;\n        }\n        if (hash.pdf && hash.pdf[name] != null) {\n            return hash.pdf[name];\n        }\n        return defval;\n    }\n\n    var multiPage = getOption(\"multiPage\");\n    var imgDPI = getOption(\"imgDPI\", DEFAULT_IMAGE_DPI);\n\n    PDF.clearImageCache();\n\n    var handlers = {\n        Image: function(element) {\n            var url = element.src();\n            var size = element.bbox().size;\n            if (imgDPI) {\n                var prev = images[url];\n                size = {\n                  width: Math.ceil(size.width * imgDPI / 72),\n                  height: Math.ceil(size.height * imgDPI / 72)\n                };\n\n                if (prev) {\n                  size.width = Math.max(prev.width, size.width);\n                  size.height = Math.max(prev.height, size.height);\n                }\n            }\n\n            images[url] = size;\n        },\n        Text: function(element) {\n            var style = PDF.parseFontDef(element.options.font);\n            var url = PDF.getFontURL(style);\n            if (fonts.indexOf(url) < 0) {\n                fonts.push(url);\n            }\n        }\n    };\n\n    group.traverse(function(element) {\n        dispatch(handlers, element);\n\n        var fill = element.fill && element.fill();\n        if (fill instanceof Pattern) {\n            fill.traverse(function(child) {\n                dispatch(handlers, child);\n            });\n        }\n    });\n\n    function doIt() {\n        if (--count > 0) {\n            return;\n        }\n\n        var pdf = new (PDF.Document)({\n            producer  : getOption(\"producer\"),\n            title     : getOption(\"title\"),\n            author    : getOption(\"author\"),\n            subject   : getOption(\"subject\"),\n            keywords  : getOption(\"keywords\"),\n            creator   : getOption(\"creator\"),\n            date      : getOption(\"date\"),\n\n            autoPrint : getOption(\"autoPrint\")\n        });\n\n        function drawPage(group) {\n            var options = group.options;\n\n            var tmp = optimize(group);\n            var bbox = tmp.bbox;\n            group = tmp.root;\n            // var tmp, bbox;\n\n            var paperSize = getOption(\"paperSize\", getOption(\"paperSize\", \"auto\"), options), addMargin = false;\n            if (paperSize == \"auto\") {\n                if (bbox) {\n                    var size = bbox.getSize();\n                    paperSize = [ size.width, size.height ];\n                    addMargin = true;\n                    var origin = bbox.getOrigin();\n                    tmp = new Group();\n                    tmp.transform(new geo.Matrix(1, 0, 0, 1, -origin.x, -origin.y));\n                    tmp.append(group);\n                    group = tmp;\n                }\n                else {\n                    paperSize = \"A4\";\n                }\n            }\n\n            var page;\n            page = pdf.addPage({\n                paperSize : paperSize,\n                margin    : getOption(\"margin\", getOption(\"margin\"), options),\n                addMargin : addMargin,\n                landscape : getOption(\"landscape\", getOption(\"landscape\", false), options)\n            });\n            drawElement(group, page, pdf);\n        }\n\n        if (multiPage) {\n            group.children.forEach(drawPage);\n        } else {\n            drawPage(group);\n        }\n\n        callback(pdf.render(), pdf);\n    }\n\n    var count = 2;\n    PDF.loadFonts(fonts, doIt);\n    PDF.loadImages(images, doIt, {\n        jpegQuality : getOption(\"jpegQuality\", 0.92),\n        keepPNG     : getOption(\"keepPNG\", false)\n    });\n}\n\nfunction toDataURL(group, callback) {\n    render(group, function(data){\n        callback(\"data:application/pdf;base64,\" + data.base64());\n    });\n}\n\nfunction toBlob(group, callback) {\n    render(group, function(data){\n        callback(new window.Blob([ data.get() ], { type: \"application/pdf\" }));\n    });\n}\n\nfunction saveAs(group, filename, proxy, callback) {\n    // XXX: Safari has Blob, but does not support the download attribute\n    //      so we'd end up converting to dataURL and using the proxy anyway.\n    if (window.Blob && !support.browser.safari) {\n        toBlob(group, function(blob){\n            util_saveAs({ dataURI: blob, fileName: filename });\n            if (callback) {\n                callback(blob);\n            }\n        });\n    } else {\n        toDataURL(group, function(dataURL){\n            util_saveAs({ dataURI: dataURL, fileName: filename, proxyURL: proxy });\n            if (callback) {\n                callback(dataURL);\n            }\n        });\n    }\n}\n\nfunction dispatch(handlers, element) {\n    var handler = handlers[element.nodeType];\n    if (handler) {\n        return handler.call.apply(handler, arguments);\n    }\n    return element;\n}\n\nfunction drawElement(element, page, pdf) {\n    if (element.options._pdfDebug) {\n        page.comment(\"BEGIN: \" + element.options._pdfDebug);\n    }\n\n    var transform = element.transform();\n    var opacity = element.opacity();\n\n    page.save();\n\n    if (opacity != null && opacity < 1) {\n        page.setOpacity(opacity);\n    }\n\n    setStrokeOptions(element, page, pdf);\n    setFillOptions(element, page, pdf);\n\n    if (transform) {\n        var m = transform.matrix();\n        page.transform(m.a, m.b, m.c, m.d, m.e, m.f);\n    }\n\n    setClipping(element, page, pdf);\n\n    dispatch({\n        Path      : drawPath,\n        MultiPath : drawMultiPath,\n        Circle    : drawCircle,\n        Arc       : drawArc,\n        Text      : drawText,\n        Image     : drawImage,\n        Group     : drawGroup,\n        Rect      : drawRect\n    }, element, page, pdf);\n\n    page.restore();\n\n    if (element.options._pdfDebug) {\n        page.comment(\"END: \" + element.options._pdfDebug);\n    }\n}\n\nfunction setStrokeOptions(element, page) {\n    var stroke = element.stroke && element.stroke();\n    if (!stroke) {\n        return;\n    }\n\n    var color = stroke.color;\n    if (color) {\n        color = parseColor(color);\n        if (color == null) {\n            return; // no stroke\n        }\n        page.setStrokeColor(color.r, color.g, color.b);\n        if (color.a != 1) {\n            page.setStrokeOpacity(color.a);\n        }\n    }\n\n    var width = stroke.width;\n    if (width != null) {\n        if (width === 0) {\n            return; // no stroke\n        }\n        page.setLineWidth(width);\n    }\n\n    var dashType = stroke.dashType;\n    if (dashType) {\n        page.setDashPattern(DASH_PATTERNS[dashType], 0);\n    }\n\n    var lineCap = stroke.lineCap;\n    if (lineCap) {\n        page.setLineCap(LINE_CAP[lineCap]);\n    }\n\n    var lineJoin = stroke.lineJoin;\n    if (lineJoin) {\n        page.setLineJoin(LINE_JOIN[lineJoin]);\n    }\n\n    var opacity = stroke.opacity;\n    if (opacity != null) {\n        page.setStrokeOpacity(opacity);\n    }\n}\n\nfunction setFillOptions(element, page) {\n    var fill = element.fill && element.fill();\n    if (!fill) {\n        return;\n    }\n\n    if (fill instanceof Gradient || fill instanceof Pattern) {\n        return;\n    }\n\n    var color = fill.color;\n    if (color) {\n        color = parseColor(color);\n        if (color == null) {\n            return; // no fill\n        }\n        page.setFillColor(color.r, color.g, color.b);\n        if (color.a != 1) {\n            page.setFillOpacity(color.a);\n        }\n    }\n\n    var opacity = fill.opacity;\n    if (opacity != null) {\n        page.setFillOpacity(opacity);\n    }\n}\n\nfunction setClipping(element, page, pdf) {\n    // XXX: only Path supported at the moment.\n    var clip = element.clip();\n    if (clip) {\n        _drawPath(clip, page, pdf);\n        page.clip();\n        // page.setStrokeColor(Math.random(), Math.random(), Math.random());\n        // page.setLineWidth(1);\n        // page.stroke();\n    }\n}\n\nfunction shouldDraw(thing) {\n    return (thing &&\n            (thing instanceof Gradient ||\n            thing instanceof Pattern ||\n             (thing.color && !/^(none|transparent)$/i.test(thing.color) &&\n              (thing.width == null || thing.width > 0) &&\n              (thing.opacity == null || thing.opacity > 0))));\n}\n\nfunction maybeGradient(element, page, pdf, stroke) {\n    var fill = element.fill();\n    if (fill instanceof Gradient) {\n        if (stroke) {\n            page.clipStroke();\n        } else {\n            page.clip();\n        }\n        var isRadial = fill instanceof RadialGradient;\n        var start, end;\n        if (isRadial) {\n            start = { x: fill.center().x , y: fill.center().y , r: 0 };\n            end   = { x: fill.center().x , y: fill.center().y , r: fill.radius() };\n        } else {\n            start = { x: fill.start().x , y: fill.start().y };\n            end   = { x: fill.end().x   , y: fill.end().y   };\n        }\n\n        var stops = fill.stops.elements().map(function(stop){\n            var offset = stop.offset();\n            if (/%$/.test(offset)) {\n                offset = parseFloat(offset) / 100;\n            } else {\n                offset = parseFloat(offset);\n            }\n            var color = parseColor(stop.color());\n            color.a *= stop.opacity();\n            return {\n                offset: offset,\n                color: color\n            };\n        });\n\n        // Duplicats first and last stop to fix\n        // https://github.com/telerik/kendo-ui-core/issues/1782\n        stops.unshift(stops[0]);\n        stops.push(stops[stops.length - 1]);\n\n        var gradient = {\n            userSpace : fill.userSpace(),\n            type      : isRadial ? \"radial\" : \"linear\",\n            start     : start,\n            end       : end,\n            stops     : stops\n        };\n        var box = element.rawBBox();\n        var tl = box.topLeft(), size = box.getSize();\n        box = {\n            left   : tl.x,\n            top    : tl.y,\n            width  : size.width,\n            height : size.height\n        };\n        page.gradient(gradient, box);\n        return true;\n    }\n}\n\nfunction maybePattern(element, page, pdf, stroke) {\n    var fill = element.fill();\n    if (fill instanceof Pattern) {\n        if (stroke) {\n            page.clipStroke();\n        } else {\n            page.clip();\n        }\n\n        var box = element.rawBBox();\n        var tl = box.topLeft(), size = box.getSize();\n        var strokeWidth = element.stroke() ? element.stroke().width : 0;\n\n        page.pattern(fill, {\n            left   : tl.x + strokeWidth / 2,\n            top    : tl.y + strokeWidth / 2,\n            width  : size.width - strokeWidth,\n            height : size.height - strokeWidth\n        }, drawPattern);\n        return true;\n    }\n}\n\nfunction maybeFillStroke(element, page, pdf) {\n    if (shouldDraw(element.fill()) && shouldDraw(element.stroke())) {\n        if (!maybeGradient(element, page, pdf, true) && !maybePattern(element, page, pdf, true)) {\n            page.fillStroke();\n        }\n    } else if (shouldDraw(element.fill())) {\n        if (!maybeGradient(element, page, pdf, false) && !maybePattern(element, page, pdf, false)) {\n            page.fill();\n        }\n    } else if (shouldDraw(element.stroke())) {\n        page.stroke();\n    } else {\n        // we should not get here; the path should have been\n        // optimized away.  but let's be prepared.\n        page.nop();\n    }\n}\n\nfunction maybeDrawRect(path, page) {\n    var segments = path.segments;\n    if (segments.length == 4 && path.options.closed) {\n        // detect if this path looks like a rectangle parallel to the axis\n        var a = [];\n        for (var i = 0; i < segments.length; ++i) {\n            if (segments[i].controlIn()) { // has curve?\n                return false;\n            }\n            a[i] = segments[i].anchor();\n        }\n        // it's a rectangle if the y/x/y/x or x/y/x/y coords of\n        // consecutive points are the same.\n        var isRect = (\n            a[0].y == a[1].y && a[1].x == a[2].x && a[2].y == a[3].y && a[3].x == a[0].x\n        ) || (\n            a[0].x == a[1].x && a[1].y == a[2].y && a[2].x == a[3].x && a[3].y == a[0].y\n        );\n        if (isRect) {\n            // this saves a bunch of instructions in PDF:\n            // moveTo, lineTo, lineTo, lineTo, close -> rect.\n            page.rect(a[0].x, a[0].y,\n                      a[2].x - a[0].x /*width*/,\n                      a[2].y - a[0].y /*height*/);\n            return true;\n        }\n    }\n}\n\nfunction _drawPath(element, page, pdf) {\n    var segments = element.segments;\n    if (segments.length === 0) {\n        return;\n    }\n    if (!maybeDrawRect(element, page, pdf)) {\n        for (var prev, i = 0; i < segments.length; ++i) {\n            var seg = segments[i];\n            var anchor = seg.anchor();\n            if (!prev) {\n                page.moveTo(anchor.x, anchor.y);\n            } else {\n                var prevOut = prev.controlOut();\n                var controlIn = seg.controlIn();\n                if (prevOut && controlIn) {\n                    page.bezier(\n                        prevOut.x   , prevOut.y,\n                        controlIn.x , controlIn.y,\n                        anchor.x    , anchor.y\n                    );\n                } else {\n                    page.lineTo(anchor.x, anchor.y);\n                }\n            }\n            prev = seg;\n        }\n        if (element.options.closed) {\n            page.close();\n        }\n    }\n}\n\nfunction drawPath(element, page, pdf) {\n    _drawPath(element, page, pdf);\n    maybeFillStroke(element, page, pdf);\n}\n\nfunction drawMultiPath(element, page, pdf) {\n    var paths = element.paths;\n    for (var i = 0; i < paths.length; ++i) {\n        _drawPath(paths[i], page, pdf);\n    }\n    maybeFillStroke(element, page, pdf);\n}\n\nfunction drawCircle(element, page, pdf) {\n    var g = element.geometry();\n    page.circle(g.center.x, g.center.y, g.radius);\n    maybeFillStroke(element, page, pdf);\n}\n\nfunction drawArc(element, page, pdf) {\n    var points = element.geometry().curvePoints();\n    page.moveTo(points[0].x, points[0].y);\n    for (var i = 1; i < points.length;) {\n        page.bezier(\n            points[i].x, points[i++].y,\n            points[i].x, points[i++].y,\n            points[i].x, points[i++].y\n        );\n    }\n    maybeFillStroke(element, page, pdf);\n}\n\nfunction drawText(element, page) {\n    var style = PDF.parseFontDef(element.options.font);\n    var pos = element._position;\n    var mode;\n\n    page.transform(1, 0, 0, -1, pos.x, pos.y + style.fontSize);\n\n    var draw = function (renderMode) {\n        page.beginText();\n        page.setFont(PDF.getFontURL(style), style.fontSize);\n        page.setTextRenderingMode(renderMode);\n        page.showText(element.content(), element._pdfRect ? element._pdfRect.width() : null);\n    };\n\n    if (element.fill() && element.stroke()) {\n        mode = TEXT_RENDERING_MODE.fillAndStroke;\n        if (element.options.paintOrder === \"stroke\") {\n            draw(TEXT_RENDERING_MODE.stroke);\n            mode = TEXT_RENDERING_MODE.fill;\n        }\n    } else if (element.fill()) {\n        mode = TEXT_RENDERING_MODE.fill;\n    } else if (element.stroke()) {\n        mode = TEXT_RENDERING_MODE.stroke;\n    }\n\n    draw(mode);\n    page.endText();\n}\n\nfunction drawPattern(pattern, page, pdf) {\n    var children = pattern.children;\n    for (var i = 0; i < children.length; ++i) {\n        drawElement(children[i], page, pdf);\n    }\n}\n\nfunction drawGroup(element, page, pdf) {\n    if (element._pdfLink) {\n        page.addLink(element._pdfLink.url, element._pdfLink);\n    }\n    var children = element.children;\n    for (var i = 0; i < children.length; ++i) {\n        drawElement(children[i], page, pdf);\n    }\n}\n\nfunction drawImage(element, page) {\n    var url = element.src();\n    if (!url) {\n        return;\n    }\n\n    var rect = element.rect();\n    var tl = rect.getOrigin();\n    var sz = rect.getSize();\n    page.transform(sz.width, 0, 0, -sz.height, tl.x, tl.y + sz.height);\n    page.drawImage(url);\n}\n\nfunction drawRect(element, page, pdf) {\n    var geometry = element.geometry();\n    var ref = geometry.cornerRadius;\n    var rx = ref[0];\n    var ry = ref[1];\n    if (rx === 0 && ry === 0) {\n        page.rect(geometry.origin.x, geometry.origin.y, geometry.size.width, geometry.size.height);\n        maybeFillStroke(element, page, pdf);\n    } else {\n        drawPath(Path.fromRect(geometry, element.options), page, pdf);\n    }\n}\n\nfunction parseColor(value) {\n    var color = utils_parseColor(value, true);\n    return color ? color.toRGB() : null;\n}\n\nfunction optimize(root) {\n    var clipbox = false;\n    var matrix = geo.Matrix.unit();\n    var currentBox = null;\n    var changed;\n    do {\n        changed = false;\n        root = opt(root);\n    } while (root && changed);\n    return { root: root, bbox: currentBox };\n\n    function change(newShape) {\n        changed = true;\n        return newShape;\n    }\n\n    function visible(shape) {\n        return (shape.visible() && shape.opacity() > 0 &&\n                ( shouldDraw(shape.fill()) ||\n                  shouldDraw(shape.stroke()) ));\n    }\n\n    function optArray(a) {\n        var b = [];\n        for (var i = 0; i < a.length; ++i) {\n            var el = opt(a[i]);\n            if (el != null) {\n                b.push(el);\n            }\n        }\n        return b;\n    }\n\n    function withClipping(shape, f) {\n        var saveclipbox = clipbox;\n        var savematrix = matrix;\n\n        if (shape.transform()) {\n            matrix = matrix.multiplyCopy(shape.transform().matrix());\n        }\n\n        var clip = shape.clip();\n        if (clip && typeof clip.bbox === 'function') {\n            clip = clip.bbox();\n            if (clip) {\n                clip = clip.bbox(matrix);\n                clipbox = clipbox ? geo.Rect.intersect(clipbox, clip) : clip;\n            }\n        }\n\n        try {\n            return f();\n        }\n        finally {\n            clipbox = saveclipbox;\n            matrix = savematrix;\n        }\n    }\n\n    function inClipbox(shape) {\n        if (clipbox == null) {\n            return false;\n        }\n\n        var box = shape.rawBBox();\n        if (box) {\n            box = box.bbox(matrix);\n        }\n\n        if (clipbox && box) {\n            box = geo.Rect.intersect(box, clipbox);\n        }\n        return box;\n    }\n\n    function opt(shape) {\n        return withClipping(shape, function(){\n            if (!(shape instanceof Group || shape instanceof MultiPath)) {\n                var box = inClipbox(shape);\n                if (!box) {\n                    return change(null);\n                }\n                currentBox = currentBox ? geo.Rect.union(currentBox, box) : box;\n            }\n            return dispatch({\n                Path: function(shape) {\n                    if (shape.segments.length === 0 || !visible(shape)) {\n                        return change(null);\n                    }\n                    return shape;\n                },\n                MultiPath: function(shape) {\n                    if (!visible(shape)) {\n                        return change(null);\n                    }\n                    var el = new MultiPath(shape.options);\n                    el.paths = optArray(shape.paths);\n                    if (el.paths.length === 0) {\n                        return change(null);\n                    }\n                    return el;\n                },\n                Circle: function(shape) {\n                    if (!visible(shape)) {\n                        return change(null);\n                    }\n                    return shape;\n                },\n                Arc: function(shape) {\n                    if (!visible(shape)) {\n                        return change(null);\n                    }\n                    return shape;\n                },\n                Text: function(shape) {\n                    if (!/\\S/.test(shape.content()) || !visible(shape)) {\n                        return change(null);\n                    }\n                    return shape;\n                },\n                Image: function(shape) {\n                    if (!(shape.visible() && shape.opacity() > 0)) {\n                        return change(null);\n                    }\n                    return shape;\n                },\n                Group: function(shape) {\n                    if (!(shape.visible() && shape.opacity() > 0)) {\n                        return change(null);\n                    }\n                    var el = new Group(shape.options);\n                    el.children = optArray(shape.children);\n                    el._pdfLink = shape._pdfLink;\n                    if (shape !== root && el.children.length === 0 && !shape._pdfLink) {\n                        return change(null);\n                    }\n                    return el;\n                },\n                Rect: function(shape) {\n                    if (!visible(shape)) {\n                        return change(null);\n                    }\n                    return shape;\n                }\n            }, shape);\n        });\n    }\n}\n\nfunction exportPDF(group, options) {\n    var promise = createPromise();\n\n    for (var i in options) {\n        if (i == \"margin\" && group.options.pdf && group.options.pdf._ignoreMargin) {\n            // This hackish option is set by the page breaking code in drawDOM.  The idea is\n            // that margin is already taken into account there (that's required to do proper\n            // page breaking) and we don't want to set it again here, as it would double the\n            // top-left margin, and truncate the content on bottom/right.\n            continue;\n        }\n        group.options.set(\"pdf.\" + i, options[i]);\n    }\n\n    toDataURL(group, promise.resolve);\n\n    return promise;\n}\n\n// XXX: the duplication is in order to keep exportPDF return a data\n// URI, which is what previous versions do.  Currently only IE9 does\n// not support Blob, IMO we should switch to Blob by default\n// everywhere.\nfunction exportPDFToBlob(group, options) {\n    var promise = createPromise();\n\n    for (var i in options) {\n        if (i == \"margin\" && group.options.pdf && group.options.pdf._ignoreMargin) {\n            // This hackish option is set by the page breaking code in drawDOM.  The idea is\n            // that margin is already taken into account there (that's required to do proper\n            // page breaking) and we don't want to set it again here, as it would double the\n            // top-left margin, and truncate the content on bottom/right.\n            continue;\n        }\n        group.options.set(\"pdf.\" + i, options[i]);\n    }\n\n    if (window.Blob && !support.browser.safari) {\n        toBlob(group, promise.resolve);\n    } else {\n        toDataURL(group, promise.resolve);\n    }\n\n    return promise;\n}\n\n\nexport { exportPDF, exportPDFToBlob, saveAs, toDataURL, toBlob, render };\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,SAASA,aAAa,QAAQ,SAAS;AACvC,SAASC,UAAU,IAAIC,gBAAgB,EAAEC,MAAM,IAAIC,WAAW,EAAEC,OAAO,QAAQ,WAAW;AAC1F,OAAO,KAAKC,GAAG,MAAM,QAAQ;AAC7B,SAASC,KAAK,EAAEC,IAAI,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,OAAO,QAAQ,YAAY;AACtF,OAAO,KAAKC,GAAG,MAAM,aAAa;AAElC,IAAIC,iBAAiB,GAAG,GAAG;AAC3B,IAAIC,mBAAmB,GAAGT,GAAG,CAACS,mBAAmB;AAEjD,IAAIC,aAAa,GAAG;EAChBC,IAAI,EAAa,CAAE,CAAC,CAAE;EACtBC,OAAO,EAAU,CAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAE;EAC/BC,GAAG,EAAc,CAAE,CAAC,EAAE,CAAC,CAAE;EACzBC,QAAQ,EAAS,CAAE,CAAC,EAAE,CAAC,CAAE;EACzBC,WAAW,EAAM,CAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAE;EAC/BC,cAAc,EAAG,CAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAE;EACrCC,KAAK,EAAY;AACrB,CAAC;AAED,IAAIC,QAAQ,GAAG;EACXC,IAAI,EAAK,CAAC;EACVC,KAAK,EAAI,CAAC;EACVC,MAAM,EAAG;AACb,CAAC;AAED,IAAIC,SAAS,GAAG;EACZC,KAAK,EAAG,CAAC;EACTH,KAAK,EAAG,CAAC;EACTI,KAAK,EAAG;AACZ,CAAC;AAED,SAASC,MAAMA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EAC7B,IAAIC,KAAK,GAAG,EAAE;IAAEC,MAAM,GAAG,CAAC,CAAC;IAAEC,OAAO,GAAGJ,KAAK,CAACI,OAAO;EAEpD,SAASC,SAASA,CAACC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAE;IACnC,IAAI,CAACA,IAAI,EAAE;MACPA,IAAI,GAAGJ,OAAO;IAClB;IACA,IAAII,IAAI,CAACC,GAAG,IAAID,IAAI,CAACC,GAAG,CAACH,IAAI,CAAC,IAAI,IAAI,EAAE;MACpC,OAAOE,IAAI,CAACC,GAAG,CAACH,IAAI,CAAC;IACzB;IACA,OAAOC,MAAM;EACjB;EAEA,IAAIG,SAAS,GAAGL,SAAS,CAAC,WAAW,CAAC;EACtC,IAAIM,MAAM,GAAGN,SAAS,CAAC,QAAQ,EAAEvB,iBAAiB,CAAC;EAEnDR,GAAG,CAACsC,eAAe,CAAC,CAAC;EAErB,IAAIC,QAAQ,GAAG;IACXC,KAAK,EAAE,SAAAA,CAASC,OAAO,EAAE;MACrB,IAAIC,GAAG,GAAGD,OAAO,CAACE,GAAG,CAAC,CAAC;MACvB,IAAIC,IAAI,GAAGH,OAAO,CAACI,IAAI,CAAC,CAAC,CAACD,IAAI;MAC9B,IAAIP,MAAM,EAAE;QACR,IAAIS,IAAI,GAAGjB,MAAM,CAACa,GAAG,CAAC;QACtBE,IAAI,GAAG;UACLG,KAAK,EAAEC,IAAI,CAACC,IAAI,CAACL,IAAI,CAACG,KAAK,GAAGV,MAAM,GAAG,EAAE,CAAC;UAC1Ca,MAAM,EAAEF,IAAI,CAACC,IAAI,CAACL,IAAI,CAACM,MAAM,GAAGb,MAAM,GAAG,EAAE;QAC7C,CAAC;QAED,IAAIS,IAAI,EAAE;UACRF,IAAI,CAACG,KAAK,GAAGC,IAAI,CAACG,GAAG,CAACL,IAAI,CAACC,KAAK,EAAEH,IAAI,CAACG,KAAK,CAAC;UAC7CH,IAAI,CAACM,MAAM,GAAGF,IAAI,CAACG,GAAG,CAACL,IAAI,CAACI,MAAM,EAAEN,IAAI,CAACM,MAAM,CAAC;QAClD;MACJ;MAEArB,MAAM,CAACa,GAAG,CAAC,GAAGE,IAAI;IACtB,CAAC;IACDQ,IAAI,EAAE,SAAAA,CAASX,OAAO,EAAE;MACpB,IAAIY,KAAK,GAAGrD,GAAG,CAACsD,YAAY,CAACb,OAAO,CAACX,OAAO,CAACyB,IAAI,CAAC;MAClD,IAAIb,GAAG,GAAG1C,GAAG,CAACwD,UAAU,CAACH,KAAK,CAAC;MAC/B,IAAIzB,KAAK,CAAC6B,OAAO,CAACf,GAAG,CAAC,GAAG,CAAC,EAAE;QACxBd,KAAK,CAAC8B,IAAI,CAAChB,GAAG,CAAC;MACnB;IACJ;EACJ,CAAC;EAEDhB,KAAK,CAACiC,QAAQ,CAAC,UAASlB,OAAO,EAAE;IAC7BmB,QAAQ,CAACrB,QAAQ,EAAEE,OAAO,CAAC;IAE3B,IAAIoB,IAAI,GAAGpB,OAAO,CAACoB,IAAI,IAAIpB,OAAO,CAACoB,IAAI,CAAC,CAAC;IACzC,IAAIA,IAAI,YAAYvD,OAAO,EAAE;MACzBuD,IAAI,CAACF,QAAQ,CAAC,UAASG,KAAK,EAAE;QAC1BF,QAAQ,CAACrB,QAAQ,EAAEuB,KAAK,CAAC;MAC7B,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;EAEF,SAASC,IAAIA,CAAA,EAAG;IACZ,IAAI,EAAEC,KAAK,GAAG,CAAC,EAAE;MACb;IACJ;IAEA,IAAI7B,GAAG,GAAG,IAAKnC,GAAG,CAACiE,QAAQ,CAAE;MACzBC,QAAQ,EAAInC,SAAS,CAAC,UAAU,CAAC;MACjCoC,KAAK,EAAOpC,SAAS,CAAC,OAAO,CAAC;MAC9BqC,MAAM,EAAMrC,SAAS,CAAC,QAAQ,CAAC;MAC/BsC,OAAO,EAAKtC,SAAS,CAAC,SAAS,CAAC;MAChCuC,QAAQ,EAAIvC,SAAS,CAAC,UAAU,CAAC;MACjCwC,OAAO,EAAKxC,SAAS,CAAC,SAAS,CAAC;MAChCyC,IAAI,EAAQzC,SAAS,CAAC,MAAM,CAAC;MAE7B0C,SAAS,EAAG1C,SAAS,CAAC,WAAW;IACrC,CAAC,CAAC;IAEF,SAAS2C,QAAQA,CAAChD,KAAK,EAAE;MACrB,IAAII,OAAO,GAAGJ,KAAK,CAACI,OAAO;MAE3B,IAAI6C,GAAG,GAAGC,QAAQ,CAAClD,KAAK,CAAC;MACzB,IAAImB,IAAI,GAAG8B,GAAG,CAAC9B,IAAI;MACnBnB,KAAK,GAAGiD,GAAG,CAACE,IAAI;MAChB;;MAEA,IAAIC,SAAS,GAAG/C,SAAS,CAAC,WAAW,EAAEA,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC,EAAED,OAAO,CAAC;QAAEiD,SAAS,GAAG,KAAK;MAClG,IAAID,SAAS,IAAI,MAAM,EAAE;QACrB,IAAIjC,IAAI,EAAE;UACN,IAAID,IAAI,GAAGC,IAAI,CAACmC,OAAO,CAAC,CAAC;UACzBF,SAAS,GAAG,CAAElC,IAAI,CAACG,KAAK,EAAEH,IAAI,CAACM,MAAM,CAAE;UACvC6B,SAAS,GAAG,IAAI;UAChB,IAAIE,MAAM,GAAGpC,IAAI,CAACqC,SAAS,CAAC,CAAC;UAC7BP,GAAG,GAAG,IAAI1E,KAAK,CAAC,CAAC;UACjB0E,GAAG,CAACQ,SAAS,CAAC,IAAI5E,GAAG,CAAC6E,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAACH,MAAM,CAACI,CAAC,EAAE,CAACJ,MAAM,CAACK,CAAC,CAAC,CAAC;UAC/DX,GAAG,CAACY,MAAM,CAAC7D,KAAK,CAAC;UACjBA,KAAK,GAAGiD,GAAG;QACf,CAAC,MACI;UACDG,SAAS,GAAG,IAAI;QACpB;MACJ;MAEA,IAAIU,IAAI;MACRA,IAAI,GAAGrD,GAAG,CAACsD,OAAO,CAAC;QACfX,SAAS,EAAGA,SAAS;QACrBY,MAAM,EAAM3D,SAAS,CAAC,QAAQ,EAAEA,SAAS,CAAC,QAAQ,CAAC,EAAED,OAAO,CAAC;QAC7DiD,SAAS,EAAGA,SAAS;QACrBY,SAAS,EAAG5D,SAAS,CAAC,WAAW,EAAEA,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC,EAAED,OAAO;MAC7E,CAAC,CAAC;MACF8D,WAAW,CAAClE,KAAK,EAAE8D,IAAI,EAAErD,GAAG,CAAC;IACjC;IAEA,IAAIC,SAAS,EAAE;MACXV,KAAK,CAACmE,QAAQ,CAACC,OAAO,CAACpB,QAAQ,CAAC;IACpC,CAAC,MAAM;MACHA,QAAQ,CAAChD,KAAK,CAAC;IACnB;IAEAC,QAAQ,CAACQ,GAAG,CAACV,MAAM,CAAC,CAAC,EAAEU,GAAG,CAAC;EAC/B;EAEA,IAAI6B,KAAK,GAAG,CAAC;EACbhE,GAAG,CAAC+F,SAAS,CAACnE,KAAK,EAAEmC,IAAI,CAAC;EAC1B/D,GAAG,CAACgG,UAAU,CAACnE,MAAM,EAAEkC,IAAI,EAAE;IACzBkC,WAAW,EAAGlE,SAAS,CAAC,aAAa,EAAE,IAAI,CAAC;IAC5CmE,OAAO,EAAOnE,SAAS,CAAC,SAAS,EAAE,KAAK;EAC5C,CAAC,CAAC;AACN;AAEA,SAASoE,SAASA,CAACzE,KAAK,EAAEC,QAAQ,EAAE;EAChCF,MAAM,CAACC,KAAK,EAAE,UAAS0E,IAAI,EAAC;IACxBzE,QAAQ,CAAC,8BAA8B,GAAGyE,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC;EAC5D,CAAC,CAAC;AACN;AAEA,SAASC,MAAMA,CAAC5E,KAAK,EAAEC,QAAQ,EAAE;EAC7BF,MAAM,CAACC,KAAK,EAAE,UAAS0E,IAAI,EAAC;IACxBzE,QAAQ,CAAC,IAAI4E,MAAM,CAACC,IAAI,CAAC,CAAEJ,IAAI,CAACK,GAAG,CAAC,CAAC,CAAE,EAAE;MAAEC,IAAI,EAAE;IAAkB,CAAC,CAAC,CAAC;EAC1E,CAAC,CAAC;AACN;AAEA,SAAS7G,MAAMA,CAAC6B,KAAK,EAAEiF,QAAQ,EAAEC,KAAK,EAAEjF,QAAQ,EAAE;EAC9C;EACA;EACA,IAAI4E,MAAM,CAACC,IAAI,IAAI,CAACzG,OAAO,CAAC8G,OAAO,CAACC,MAAM,EAAE;IACxCR,MAAM,CAAC5E,KAAK,EAAE,UAASqF,IAAI,EAAC;MACxBjH,WAAW,CAAC;QAAEkH,OAAO,EAAED,IAAI;QAAEE,QAAQ,EAAEN;MAAS,CAAC,CAAC;MAClD,IAAIhF,QAAQ,EAAE;QACVA,QAAQ,CAACoF,IAAI,CAAC;MAClB;IACJ,CAAC,CAAC;EACN,CAAC,MAAM;IACHZ,SAAS,CAACzE,KAAK,EAAE,UAASwF,OAAO,EAAC;MAC9BpH,WAAW,CAAC;QAAEkH,OAAO,EAAEE,OAAO;QAAED,QAAQ,EAAEN,QAAQ;QAAEQ,QAAQ,EAAEP;MAAM,CAAC,CAAC;MACtE,IAAIjF,QAAQ,EAAE;QACVA,QAAQ,CAACuF,OAAO,CAAC;MACrB;IACJ,CAAC,CAAC;EACN;AACJ;AAEA,SAAStD,QAAQA,CAACrB,QAAQ,EAAEE,OAAO,EAAE;EACjC,IAAI2E,OAAO,GAAG7E,QAAQ,CAACE,OAAO,CAAC4E,QAAQ,CAAC;EACxC,IAAID,OAAO,EAAE;IACT,OAAOA,OAAO,CAACE,IAAI,CAACC,KAAK,CAACH,OAAO,EAAEI,SAAS,CAAC;EACjD;EACA,OAAO/E,OAAO;AAClB;AAEA,SAASmD,WAAWA,CAACnD,OAAO,EAAE+C,IAAI,EAAErD,GAAG,EAAE;EACrC,IAAIM,OAAO,CAACX,OAAO,CAAC2F,SAAS,EAAE;IAC3BjC,IAAI,CAACkC,OAAO,CAAC,SAAS,GAAGjF,OAAO,CAACX,OAAO,CAAC2F,SAAS,CAAC;EACvD;EAEA,IAAItC,SAAS,GAAG1C,OAAO,CAAC0C,SAAS,CAAC,CAAC;EACnC,IAAIwC,OAAO,GAAGlF,OAAO,CAACkF,OAAO,CAAC,CAAC;EAE/BnC,IAAI,CAACoC,IAAI,CAAC,CAAC;EAEX,IAAID,OAAO,IAAI,IAAI,IAAIA,OAAO,GAAG,CAAC,EAAE;IAChCnC,IAAI,CAACqC,UAAU,CAACF,OAAO,CAAC;EAC5B;EAEAG,gBAAgB,CAACrF,OAAO,EAAE+C,IAAI,EAAErD,GAAG,CAAC;EACpC4F,cAAc,CAACtF,OAAO,EAAE+C,IAAI,EAAErD,GAAG,CAAC;EAElC,IAAIgD,SAAS,EAAE;IACX,IAAI6C,CAAC,GAAG7C,SAAS,CAAC8C,MAAM,CAAC,CAAC;IAC1BzC,IAAI,CAACL,SAAS,CAAC6C,CAAC,CAACE,CAAC,EAAEF,CAAC,CAACG,CAAC,EAAEH,CAAC,CAACI,CAAC,EAAEJ,CAAC,CAACK,CAAC,EAAEL,CAAC,CAACM,CAAC,EAAEN,CAAC,CAACO,CAAC,CAAC;EAChD;EAEAC,WAAW,CAAC/F,OAAO,EAAE+C,IAAI,EAAErD,GAAG,CAAC;EAE/ByB,QAAQ,CAAC;IACL1D,IAAI,EAAQuI,QAAQ;IACpBtI,SAAS,EAAGuI,aAAa;IACzBC,MAAM,EAAMC,UAAU;IACtBC,GAAG,EAASC,OAAO;IACnB1F,IAAI,EAAQ2F,QAAQ;IACpBvG,KAAK,EAAOwG,SAAS;IACrB/I,KAAK,EAAOgJ,SAAS;IACrBC,IAAI,EAAQC;EAChB,CAAC,EAAE1G,OAAO,EAAE+C,IAAI,EAAErD,GAAG,CAAC;EAEtBqD,IAAI,CAAC4D,OAAO,CAAC,CAAC;EAEd,IAAI3G,OAAO,CAACX,OAAO,CAAC2F,SAAS,EAAE;IAC3BjC,IAAI,CAACkC,OAAO,CAAC,OAAO,GAAGjF,OAAO,CAACX,OAAO,CAAC2F,SAAS,CAAC;EACrD;AACJ;AAEA,SAASK,gBAAgBA,CAACrF,OAAO,EAAE+C,IAAI,EAAE;EACrC,IAAI6D,MAAM,GAAG5G,OAAO,CAAC4G,MAAM,IAAI5G,OAAO,CAAC4G,MAAM,CAAC,CAAC;EAC/C,IAAI,CAACA,MAAM,EAAE;IACT;EACJ;EAEA,IAAIC,KAAK,GAAGD,MAAM,CAACC,KAAK;EACxB,IAAIA,KAAK,EAAE;IACPA,KAAK,GAAG3J,UAAU,CAAC2J,KAAK,CAAC;IACzB,IAAIA,KAAK,IAAI,IAAI,EAAE;MACf,OAAO,CAAC;IACZ;IACA9D,IAAI,CAAC+D,cAAc,CAACD,KAAK,CAACE,CAAC,EAAEF,KAAK,CAACG,CAAC,EAAEH,KAAK,CAACnB,CAAC,CAAC;IAC9C,IAAImB,KAAK,CAACpB,CAAC,IAAI,CAAC,EAAE;MACd1C,IAAI,CAACkE,gBAAgB,CAACJ,KAAK,CAACpB,CAAC,CAAC;IAClC;EACJ;EAEA,IAAInF,KAAK,GAAGsG,MAAM,CAACtG,KAAK;EACxB,IAAIA,KAAK,IAAI,IAAI,EAAE;IACf,IAAIA,KAAK,KAAK,CAAC,EAAE;MACb,OAAO,CAAC;IACZ;IACAyC,IAAI,CAACmE,YAAY,CAAC5G,KAAK,CAAC;EAC5B;EAEA,IAAI6G,QAAQ,GAAGP,MAAM,CAACO,QAAQ;EAC9B,IAAIA,QAAQ,EAAE;IACVpE,IAAI,CAACqE,cAAc,CAACnJ,aAAa,CAACkJ,QAAQ,CAAC,EAAE,CAAC,CAAC;EACnD;EAEA,IAAIE,OAAO,GAAGT,MAAM,CAACS,OAAO;EAC5B,IAAIA,OAAO,EAAE;IACTtE,IAAI,CAACuE,UAAU,CAAC7I,QAAQ,CAAC4I,OAAO,CAAC,CAAC;EACtC;EAEA,IAAIE,QAAQ,GAAGX,MAAM,CAACW,QAAQ;EAC9B,IAAIA,QAAQ,EAAE;IACVxE,IAAI,CAACyE,WAAW,CAAC3I,SAAS,CAAC0I,QAAQ,CAAC,CAAC;EACzC;EAEA,IAAIrC,OAAO,GAAG0B,MAAM,CAAC1B,OAAO;EAC5B,IAAIA,OAAO,IAAI,IAAI,EAAE;IACjBnC,IAAI,CAACkE,gBAAgB,CAAC/B,OAAO,CAAC;EAClC;AACJ;AAEA,SAASI,cAAcA,CAACtF,OAAO,EAAE+C,IAAI,EAAE;EACnC,IAAI3B,IAAI,GAAGpB,OAAO,CAACoB,IAAI,IAAIpB,OAAO,CAACoB,IAAI,CAAC,CAAC;EACzC,IAAI,CAACA,IAAI,EAAE;IACP;EACJ;EAEA,IAAIA,IAAI,YAAYzD,QAAQ,IAAIyD,IAAI,YAAYvD,OAAO,EAAE;IACrD;EACJ;EAEA,IAAIgJ,KAAK,GAAGzF,IAAI,CAACyF,KAAK;EACtB,IAAIA,KAAK,EAAE;IACPA,KAAK,GAAG3J,UAAU,CAAC2J,KAAK,CAAC;IACzB,IAAIA,KAAK,IAAI,IAAI,EAAE;MACf,OAAO,CAAC;IACZ;IACA9D,IAAI,CAAC0E,YAAY,CAACZ,KAAK,CAACE,CAAC,EAAEF,KAAK,CAACG,CAAC,EAAEH,KAAK,CAACnB,CAAC,CAAC;IAC5C,IAAImB,KAAK,CAACpB,CAAC,IAAI,CAAC,EAAE;MACd1C,IAAI,CAAC2E,cAAc,CAACb,KAAK,CAACpB,CAAC,CAAC;IAChC;EACJ;EAEA,IAAIP,OAAO,GAAG9D,IAAI,CAAC8D,OAAO;EAC1B,IAAIA,OAAO,IAAI,IAAI,EAAE;IACjBnC,IAAI,CAAC2E,cAAc,CAACxC,OAAO,CAAC;EAChC;AACJ;AAEA,SAASa,WAAWA,CAAC/F,OAAO,EAAE+C,IAAI,EAAErD,GAAG,EAAE;EACrC;EACA,IAAIiI,IAAI,GAAG3H,OAAO,CAAC2H,IAAI,CAAC,CAAC;EACzB,IAAIA,IAAI,EAAE;IACNC,SAAS,CAACD,IAAI,EAAE5E,IAAI,EAAErD,GAAG,CAAC;IAC1BqD,IAAI,CAAC4E,IAAI,CAAC,CAAC;IACX;IACA;IACA;EACJ;AACJ;AAEA,SAASE,UAAUA,CAACC,KAAK,EAAE;EACvB,OAAQA,KAAK,KACJA,KAAK,YAAYnK,QAAQ,IAC1BmK,KAAK,YAAYjK,OAAO,IACtBiK,KAAK,CAACjB,KAAK,IAAI,CAAC,uBAAuB,CAACkB,IAAI,CAACD,KAAK,CAACjB,KAAK,CAAC,KACxDiB,KAAK,CAACxH,KAAK,IAAI,IAAI,IAAIwH,KAAK,CAACxH,KAAK,GAAG,CAAC,CAAC,KACvCwH,KAAK,CAAC5C,OAAO,IAAI,IAAI,IAAI4C,KAAK,CAAC5C,OAAO,GAAG,CAAC,CAAE,CAAC;AAC5D;AAEA,SAAS8C,aAAaA,CAAChI,OAAO,EAAE+C,IAAI,EAAErD,GAAG,EAAEkH,MAAM,EAAE;EAC/C,IAAIxF,IAAI,GAAGpB,OAAO,CAACoB,IAAI,CAAC,CAAC;EACzB,IAAIA,IAAI,YAAYzD,QAAQ,EAAE;IAC1B,IAAIiJ,MAAM,EAAE;MACR7D,IAAI,CAACkF,UAAU,CAAC,CAAC;IACrB,CAAC,MAAM;MACHlF,IAAI,CAAC4E,IAAI,CAAC,CAAC;IACf;IACA,IAAIO,QAAQ,GAAG9G,IAAI,YAAYxD,cAAc;IAC7C,IAAIuK,KAAK,EAAEC,GAAG;IACd,IAAIF,QAAQ,EAAE;MACVC,KAAK,GAAG;QAAEvF,CAAC,EAAExB,IAAI,CAACiH,MAAM,CAAC,CAAC,CAACzF,CAAC;QAAGC,CAAC,EAAEzB,IAAI,CAACiH,MAAM,CAAC,CAAC,CAACxF,CAAC;QAAGkE,CAAC,EAAE;MAAE,CAAC;MAC1DqB,GAAG,GAAK;QAAExF,CAAC,EAAExB,IAAI,CAACiH,MAAM,CAAC,CAAC,CAACzF,CAAC;QAAGC,CAAC,EAAEzB,IAAI,CAACiH,MAAM,CAAC,CAAC,CAACxF,CAAC;QAAGkE,CAAC,EAAE3F,IAAI,CAACkH,MAAM,CAAC;MAAE,CAAC;IAC1E,CAAC,MAAM;MACHH,KAAK,GAAG;QAAEvF,CAAC,EAAExB,IAAI,CAAC+G,KAAK,CAAC,CAAC,CAACvF,CAAC;QAAGC,CAAC,EAAEzB,IAAI,CAAC+G,KAAK,CAAC,CAAC,CAACtF;MAAE,CAAC;MACjDuF,GAAG,GAAK;QAAExF,CAAC,EAAExB,IAAI,CAACgH,GAAG,CAAC,CAAC,CAACxF,CAAC;QAAKC,CAAC,EAAEzB,IAAI,CAACgH,GAAG,CAAC,CAAC,CAACvF;MAAI,CAAC;IACrD;IAEA,IAAI0F,KAAK,GAAGnH,IAAI,CAACmH,KAAK,CAACC,QAAQ,CAAC,CAAC,CAACC,GAAG,CAAC,UAASC,IAAI,EAAC;MAChD,IAAIC,MAAM,GAAGD,IAAI,CAACC,MAAM,CAAC,CAAC;MAC1B,IAAI,IAAI,CAACZ,IAAI,CAACY,MAAM,CAAC,EAAE;QACnBA,MAAM,GAAGC,UAAU,CAACD,MAAM,CAAC,GAAG,GAAG;MACrC,CAAC,MAAM;QACHA,MAAM,GAAGC,UAAU,CAACD,MAAM,CAAC;MAC/B;MACA,IAAI9B,KAAK,GAAG3J,UAAU,CAACwL,IAAI,CAAC7B,KAAK,CAAC,CAAC,CAAC;MACpCA,KAAK,CAACpB,CAAC,IAAIiD,IAAI,CAACxD,OAAO,CAAC,CAAC;MACzB,OAAO;QACHyD,MAAM,EAAEA,MAAM;QACd9B,KAAK,EAAEA;MACX,CAAC;IACL,CAAC,CAAC;;IAEF;IACA;IACA0B,KAAK,CAACM,OAAO,CAACN,KAAK,CAAC,CAAC,CAAC,CAAC;IACvBA,KAAK,CAACtH,IAAI,CAACsH,KAAK,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC,CAAC;IAEnC,IAAIC,QAAQ,GAAG;MACXC,SAAS,EAAG5H,IAAI,CAAC4H,SAAS,CAAC,CAAC;MAC5B/E,IAAI,EAAQiE,QAAQ,GAAG,QAAQ,GAAG,QAAQ;MAC1CC,KAAK,EAAOA,KAAK;MACjBC,GAAG,EAASA,GAAG;MACfG,KAAK,EAAOA;IAChB,CAAC;IACD,IAAIU,GAAG,GAAGjJ,OAAO,CAACkJ,OAAO,CAAC,CAAC;IAC3B,IAAIC,EAAE,GAAGF,GAAG,CAACG,OAAO,CAAC,CAAC;MAAEjJ,IAAI,GAAG8I,GAAG,CAAC1G,OAAO,CAAC,CAAC;IAC5C0G,GAAG,GAAG;MACFI,IAAI,EAAKF,EAAE,CAACvG,CAAC;MACb0G,GAAG,EAAMH,EAAE,CAACtG,CAAC;MACbvC,KAAK,EAAIH,IAAI,CAACG,KAAK;MACnBG,MAAM,EAAGN,IAAI,CAACM;IAClB,CAAC;IACDsC,IAAI,CAACgG,QAAQ,CAACA,QAAQ,EAAEE,GAAG,CAAC;IAC5B,OAAO,IAAI;EACf;AACJ;AAEA,SAASM,YAAYA,CAACvJ,OAAO,EAAE+C,IAAI,EAAErD,GAAG,EAAEkH,MAAM,EAAE;EAC9C,IAAIxF,IAAI,GAAGpB,OAAO,CAACoB,IAAI,CAAC,CAAC;EACzB,IAAIA,IAAI,YAAYvD,OAAO,EAAE;IACzB,IAAI+I,MAAM,EAAE;MACR7D,IAAI,CAACkF,UAAU,CAAC,CAAC;IACrB,CAAC,MAAM;MACHlF,IAAI,CAAC4E,IAAI,CAAC,CAAC;IACf;IAEA,IAAIsB,GAAG,GAAGjJ,OAAO,CAACkJ,OAAO,CAAC,CAAC;IAC3B,IAAIC,EAAE,GAAGF,GAAG,CAACG,OAAO,CAAC,CAAC;MAAEjJ,IAAI,GAAG8I,GAAG,CAAC1G,OAAO,CAAC,CAAC;IAC5C,IAAIiH,WAAW,GAAGxJ,OAAO,CAAC4G,MAAM,CAAC,CAAC,GAAG5G,OAAO,CAAC4G,MAAM,CAAC,CAAC,CAACtG,KAAK,GAAG,CAAC;IAE/DyC,IAAI,CAAC0G,OAAO,CAACrI,IAAI,EAAE;MACfiI,IAAI,EAAKF,EAAE,CAACvG,CAAC,GAAG4G,WAAW,GAAG,CAAC;MAC/BF,GAAG,EAAMH,EAAE,CAACtG,CAAC,GAAG2G,WAAW,GAAG,CAAC;MAC/BlJ,KAAK,EAAIH,IAAI,CAACG,KAAK,GAAGkJ,WAAW;MACjC/I,MAAM,EAAGN,IAAI,CAACM,MAAM,GAAG+I;IAC3B,CAAC,EAAEE,WAAW,CAAC;IACf,OAAO,IAAI;EACf;AACJ;AAEA,SAASC,eAAeA,CAAC3J,OAAO,EAAE+C,IAAI,EAAErD,GAAG,EAAE;EACzC,IAAImI,UAAU,CAAC7H,OAAO,CAACoB,IAAI,CAAC,CAAC,CAAC,IAAIyG,UAAU,CAAC7H,OAAO,CAAC4G,MAAM,CAAC,CAAC,CAAC,EAAE;IAC5D,IAAI,CAACoB,aAAa,CAAChI,OAAO,EAAE+C,IAAI,EAAErD,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC6J,YAAY,CAACvJ,OAAO,EAAE+C,IAAI,EAAErD,GAAG,EAAE,IAAI,CAAC,EAAE;MACrFqD,IAAI,CAAC6G,UAAU,CAAC,CAAC;IACrB;EACJ,CAAC,MAAM,IAAI/B,UAAU,CAAC7H,OAAO,CAACoB,IAAI,CAAC,CAAC,CAAC,EAAE;IACnC,IAAI,CAAC4G,aAAa,CAAChI,OAAO,EAAE+C,IAAI,EAAErD,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC6J,YAAY,CAACvJ,OAAO,EAAE+C,IAAI,EAAErD,GAAG,EAAE,KAAK,CAAC,EAAE;MACvFqD,IAAI,CAAC3B,IAAI,CAAC,CAAC;IACf;EACJ,CAAC,MAAM,IAAIyG,UAAU,CAAC7H,OAAO,CAAC4G,MAAM,CAAC,CAAC,CAAC,EAAE;IACrC7D,IAAI,CAAC6D,MAAM,CAAC,CAAC;EACjB,CAAC,MAAM;IACH;IACA;IACA7D,IAAI,CAAC8G,GAAG,CAAC,CAAC;EACd;AACJ;AAEA,SAASC,aAAaA,CAACC,IAAI,EAAEhH,IAAI,EAAE;EAC/B,IAAIiH,QAAQ,GAAGD,IAAI,CAACC,QAAQ;EAC5B,IAAIA,QAAQ,CAAClB,MAAM,IAAI,CAAC,IAAIiB,IAAI,CAAC1K,OAAO,CAAC4K,MAAM,EAAE;IAC7C;IACA,IAAIxE,CAAC,GAAG,EAAE;IACV,KAAK,IAAIyE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAAClB,MAAM,EAAE,EAAEoB,CAAC,EAAE;MACtC,IAAIF,QAAQ,CAACE,CAAC,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE;QAAE;QAC3B,OAAO,KAAK;MAChB;MACA1E,CAAC,CAACyE,CAAC,CAAC,GAAGF,QAAQ,CAACE,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC;IAC/B;IACA;IACA;IACA,IAAIC,MAAM,GACN5E,CAAC,CAAC,CAAC,CAAC,CAAC5C,CAAC,IAAI4C,CAAC,CAAC,CAAC,CAAC,CAAC5C,CAAC,IAAI4C,CAAC,CAAC,CAAC,CAAC,CAAC7C,CAAC,IAAI6C,CAAC,CAAC,CAAC,CAAC,CAAC7C,CAAC,IAAI6C,CAAC,CAAC,CAAC,CAAC,CAAC5C,CAAC,IAAI4C,CAAC,CAAC,CAAC,CAAC,CAAC5C,CAAC,IAAI4C,CAAC,CAAC,CAAC,CAAC,CAAC7C,CAAC,IAAI6C,CAAC,CAAC,CAAC,CAAC,CAAC7C,CAAC,IAE5E6C,CAAC,CAAC,CAAC,CAAC,CAAC7C,CAAC,IAAI6C,CAAC,CAAC,CAAC,CAAC,CAAC7C,CAAC,IAAI6C,CAAC,CAAC,CAAC,CAAC,CAAC5C,CAAC,IAAI4C,CAAC,CAAC,CAAC,CAAC,CAAC5C,CAAC,IAAI4C,CAAC,CAAC,CAAC,CAAC,CAAC7C,CAAC,IAAI6C,CAAC,CAAC,CAAC,CAAC,CAAC7C,CAAC,IAAI6C,CAAC,CAAC,CAAC,CAAC,CAAC5C,CAAC,IAAI4C,CAAC,CAAC,CAAC,CAAC,CAAC5C,CAC9E;IACD,IAAIwH,MAAM,EAAE;MACR;MACA;MACAtH,IAAI,CAACuH,IAAI,CAAC7E,CAAC,CAAC,CAAC,CAAC,CAAC7C,CAAC,EAAE6C,CAAC,CAAC,CAAC,CAAC,CAAC5C,CAAC,EACd4C,CAAC,CAAC,CAAC,CAAC,CAAC7C,CAAC,GAAG6C,CAAC,CAAC,CAAC,CAAC,CAAC7C,CAAC,CAAC,WAChB6C,CAAC,CAAC,CAAC,CAAC,CAAC5C,CAAC,GAAG4C,CAAC,CAAC,CAAC,CAAC,CAAC5C,CAAC,CAAC,UAAU,CAAC;MACrC,OAAO,IAAI;IACf;EACJ;AACJ;AAEA,SAAS+E,SAASA,CAAC5H,OAAO,EAAE+C,IAAI,EAAErD,GAAG,EAAE;EACnC,IAAIsK,QAAQ,GAAGhK,OAAO,CAACgK,QAAQ;EAC/B,IAAIA,QAAQ,CAAClB,MAAM,KAAK,CAAC,EAAE;IACvB;EACJ;EACA,IAAI,CAACgB,aAAa,CAAC9J,OAAO,EAAE+C,IAAI,EAAErD,GAAG,CAAC,EAAE;IACpC,KAAK,IAAIW,IAAI,EAAE6J,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAAClB,MAAM,EAAE,EAAEoB,CAAC,EAAE;MAC5C,IAAIK,GAAG,GAAGP,QAAQ,CAACE,CAAC,CAAC;MACrB,IAAIE,MAAM,GAAGG,GAAG,CAACH,MAAM,CAAC,CAAC;MACzB,IAAI,CAAC/J,IAAI,EAAE;QACP0C,IAAI,CAACyH,MAAM,CAACJ,MAAM,CAACxH,CAAC,EAAEwH,MAAM,CAACvH,CAAC,CAAC;MACnC,CAAC,MAAM;QACH,IAAI4H,OAAO,GAAGpK,IAAI,CAACqK,UAAU,CAAC,CAAC;QAC/B,IAAIP,SAAS,GAAGI,GAAG,CAACJ,SAAS,CAAC,CAAC;QAC/B,IAAIM,OAAO,IAAIN,SAAS,EAAE;UACtBpH,IAAI,CAAC4H,MAAM,CACPF,OAAO,CAAC7H,CAAC,EAAK6H,OAAO,CAAC5H,CAAC,EACvBsH,SAAS,CAACvH,CAAC,EAAGuH,SAAS,CAACtH,CAAC,EACzBuH,MAAM,CAACxH,CAAC,EAAMwH,MAAM,CAACvH,CACzB,CAAC;QACL,CAAC,MAAM;UACHE,IAAI,CAAC6H,MAAM,CAACR,MAAM,CAACxH,CAAC,EAAEwH,MAAM,CAACvH,CAAC,CAAC;QACnC;MACJ;MACAxC,IAAI,GAAGkK,GAAG;IACd;IACA,IAAIvK,OAAO,CAACX,OAAO,CAAC4K,MAAM,EAAE;MACxBlH,IAAI,CAAC8H,KAAK,CAAC,CAAC;IAChB;EACJ;AACJ;AAEA,SAAS7E,QAAQA,CAAChG,OAAO,EAAE+C,IAAI,EAAErD,GAAG,EAAE;EAClCkI,SAAS,CAAC5H,OAAO,EAAE+C,IAAI,EAAErD,GAAG,CAAC;EAC7BiK,eAAe,CAAC3J,OAAO,EAAE+C,IAAI,EAAErD,GAAG,CAAC;AACvC;AAEA,SAASuG,aAAaA,CAACjG,OAAO,EAAE+C,IAAI,EAAErD,GAAG,EAAE;EACvC,IAAIoL,KAAK,GAAG9K,OAAO,CAAC8K,KAAK;EACzB,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,KAAK,CAAChC,MAAM,EAAE,EAAEoB,CAAC,EAAE;IACnCtC,SAAS,CAACkD,KAAK,CAACZ,CAAC,CAAC,EAAEnH,IAAI,EAAErD,GAAG,CAAC;EAClC;EACAiK,eAAe,CAAC3J,OAAO,EAAE+C,IAAI,EAAErD,GAAG,CAAC;AACvC;AAEA,SAASyG,UAAUA,CAACnG,OAAO,EAAE+C,IAAI,EAAErD,GAAG,EAAE;EACpC,IAAIsH,CAAC,GAAGhH,OAAO,CAAC+K,QAAQ,CAAC,CAAC;EAC1BhI,IAAI,CAACiI,MAAM,CAAChE,CAAC,CAACqB,MAAM,CAACzF,CAAC,EAAEoE,CAAC,CAACqB,MAAM,CAACxF,CAAC,EAAEmE,CAAC,CAACsB,MAAM,CAAC;EAC7CqB,eAAe,CAAC3J,OAAO,EAAE+C,IAAI,EAAErD,GAAG,CAAC;AACvC;AAEA,SAAS2G,OAAOA,CAACrG,OAAO,EAAE+C,IAAI,EAAErD,GAAG,EAAE;EACjC,IAAIuL,MAAM,GAAGjL,OAAO,CAAC+K,QAAQ,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC;EAC7CnI,IAAI,CAACyH,MAAM,CAACS,MAAM,CAAC,CAAC,CAAC,CAACrI,CAAC,EAAEqI,MAAM,CAAC,CAAC,CAAC,CAACpI,CAAC,CAAC;EACrC,KAAK,IAAIqH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,MAAM,CAACnC,MAAM,GAAG;IAChC/F,IAAI,CAAC4H,MAAM,CACPM,MAAM,CAACf,CAAC,CAAC,CAACtH,CAAC,EAAEqI,MAAM,CAACf,CAAC,EAAE,CAAC,CAACrH,CAAC,EAC1BoI,MAAM,CAACf,CAAC,CAAC,CAACtH,CAAC,EAAEqI,MAAM,CAACf,CAAC,EAAE,CAAC,CAACrH,CAAC,EAC1BoI,MAAM,CAACf,CAAC,CAAC,CAACtH,CAAC,EAAEqI,MAAM,CAACf,CAAC,EAAE,CAAC,CAACrH,CAC7B,CAAC;EACL;EACA8G,eAAe,CAAC3J,OAAO,EAAE+C,IAAI,EAAErD,GAAG,CAAC;AACvC;AAEA,SAAS4G,QAAQA,CAACtG,OAAO,EAAE+C,IAAI,EAAE;EAC7B,IAAInC,KAAK,GAAGrD,GAAG,CAACsD,YAAY,CAACb,OAAO,CAACX,OAAO,CAACyB,IAAI,CAAC;EAClD,IAAIqK,GAAG,GAAGnL,OAAO,CAACoL,SAAS;EAC3B,IAAIC,IAAI;EAERtI,IAAI,CAACL,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAEyI,GAAG,CAACvI,CAAC,EAAEuI,GAAG,CAACtI,CAAC,GAAGjC,KAAK,CAAC0K,QAAQ,CAAC;EAE1D,IAAIC,IAAI,GAAG,SAAAA,CAAUC,UAAU,EAAE;IAC7BzI,IAAI,CAAC0I,SAAS,CAAC,CAAC;IAChB1I,IAAI,CAAC2I,OAAO,CAACnO,GAAG,CAACwD,UAAU,CAACH,KAAK,CAAC,EAAEA,KAAK,CAAC0K,QAAQ,CAAC;IACnDvI,IAAI,CAAC4I,oBAAoB,CAACH,UAAU,CAAC;IACrCzI,IAAI,CAAC6I,QAAQ,CAAC5L,OAAO,CAAC6L,OAAO,CAAC,CAAC,EAAE7L,OAAO,CAAC8L,QAAQ,GAAG9L,OAAO,CAAC8L,QAAQ,CAACxL,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC;EACxF,CAAC;EAED,IAAIN,OAAO,CAACoB,IAAI,CAAC,CAAC,IAAIpB,OAAO,CAAC4G,MAAM,CAAC,CAAC,EAAE;IACpCyE,IAAI,GAAGrN,mBAAmB,CAAC+N,aAAa;IACxC,IAAI/L,OAAO,CAACX,OAAO,CAAC2M,UAAU,KAAK,QAAQ,EAAE;MACzCT,IAAI,CAACvN,mBAAmB,CAAC4I,MAAM,CAAC;MAChCyE,IAAI,GAAGrN,mBAAmB,CAACoD,IAAI;IACnC;EACJ,CAAC,MAAM,IAAIpB,OAAO,CAACoB,IAAI,CAAC,CAAC,EAAE;IACvBiK,IAAI,GAAGrN,mBAAmB,CAACoD,IAAI;EACnC,CAAC,MAAM,IAAIpB,OAAO,CAAC4G,MAAM,CAAC,CAAC,EAAE;IACzByE,IAAI,GAAGrN,mBAAmB,CAAC4I,MAAM;EACrC;EAEA2E,IAAI,CAACF,IAAI,CAAC;EACVtI,IAAI,CAACkJ,OAAO,CAAC,CAAC;AAClB;AAEA,SAASvC,WAAWA,CAACD,OAAO,EAAE1G,IAAI,EAAErD,GAAG,EAAE;EACrC,IAAI0D,QAAQ,GAAGqG,OAAO,CAACrG,QAAQ;EAC/B,KAAK,IAAI8G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9G,QAAQ,CAAC0F,MAAM,EAAE,EAAEoB,CAAC,EAAE;IACtC/G,WAAW,CAACC,QAAQ,CAAC8G,CAAC,CAAC,EAAEnH,IAAI,EAAErD,GAAG,CAAC;EACvC;AACJ;AAEA,SAAS8G,SAASA,CAACxG,OAAO,EAAE+C,IAAI,EAAErD,GAAG,EAAE;EACnC,IAAIM,OAAO,CAACkM,QAAQ,EAAE;IAClBnJ,IAAI,CAACoJ,OAAO,CAACnM,OAAO,CAACkM,QAAQ,CAACjM,GAAG,EAAED,OAAO,CAACkM,QAAQ,CAAC;EACxD;EACA,IAAI9I,QAAQ,GAAGpD,OAAO,CAACoD,QAAQ;EAC/B,KAAK,IAAI8G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9G,QAAQ,CAAC0F,MAAM,EAAE,EAAEoB,CAAC,EAAE;IACtC/G,WAAW,CAACC,QAAQ,CAAC8G,CAAC,CAAC,EAAEnH,IAAI,EAAErD,GAAG,CAAC;EACvC;AACJ;AAEA,SAAS6G,SAASA,CAACvG,OAAO,EAAE+C,IAAI,EAAE;EAC9B,IAAI9C,GAAG,GAAGD,OAAO,CAACE,GAAG,CAAC,CAAC;EACvB,IAAI,CAACD,GAAG,EAAE;IACN;EACJ;EAEA,IAAIqK,IAAI,GAAGtK,OAAO,CAACsK,IAAI,CAAC,CAAC;EACzB,IAAInB,EAAE,GAAGmB,IAAI,CAAC7H,SAAS,CAAC,CAAC;EACzB,IAAI2J,EAAE,GAAG9B,IAAI,CAAC/H,OAAO,CAAC,CAAC;EACvBQ,IAAI,CAACL,SAAS,CAAC0J,EAAE,CAAC9L,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC8L,EAAE,CAAC3L,MAAM,EAAE0I,EAAE,CAACvG,CAAC,EAAEuG,EAAE,CAACtG,CAAC,GAAGuJ,EAAE,CAAC3L,MAAM,CAAC;EAClEsC,IAAI,CAACwD,SAAS,CAACtG,GAAG,CAAC;AACvB;AAEA,SAASyG,QAAQA,CAAC1G,OAAO,EAAE+C,IAAI,EAAErD,GAAG,EAAE;EAClC,IAAIqL,QAAQ,GAAG/K,OAAO,CAAC+K,QAAQ,CAAC,CAAC;EACjC,IAAIsB,GAAG,GAAGtB,QAAQ,CAACuB,YAAY;EAC/B,IAAIC,EAAE,GAAGF,GAAG,CAAC,CAAC,CAAC;EACf,IAAIG,EAAE,GAAGH,GAAG,CAAC,CAAC,CAAC;EACf,IAAIE,EAAE,KAAK,CAAC,IAAIC,EAAE,KAAK,CAAC,EAAE;IACtBzJ,IAAI,CAACuH,IAAI,CAACS,QAAQ,CAACvI,MAAM,CAACI,CAAC,EAAEmI,QAAQ,CAACvI,MAAM,CAACK,CAAC,EAAEkI,QAAQ,CAAC5K,IAAI,CAACG,KAAK,EAAEyK,QAAQ,CAAC5K,IAAI,CAACM,MAAM,CAAC;IAC1FkJ,eAAe,CAAC3J,OAAO,EAAE+C,IAAI,EAAErD,GAAG,CAAC;EACvC,CAAC,MAAM;IACHsG,QAAQ,CAACvI,IAAI,CAACgP,QAAQ,CAAC1B,QAAQ,EAAE/K,OAAO,CAACX,OAAO,CAAC,EAAE0D,IAAI,EAAErD,GAAG,CAAC;EACjE;AACJ;AAEA,SAASxC,UAAUA,CAACwP,KAAK,EAAE;EACvB,IAAI7F,KAAK,GAAG1J,gBAAgB,CAACuP,KAAK,EAAE,IAAI,CAAC;EACzC,OAAO7F,KAAK,GAAGA,KAAK,CAAC8F,KAAK,CAAC,CAAC,GAAG,IAAI;AACvC;AAEA,SAASxK,QAAQA,CAACC,IAAI,EAAE;EACpB,IAAIwK,OAAO,GAAG,KAAK;EACnB,IAAIpH,MAAM,GAAG1H,GAAG,CAAC6E,MAAM,CAACkK,IAAI,CAAC,CAAC;EAC9B,IAAIC,UAAU,GAAG,IAAI;EACrB,IAAIC,OAAO;EACX,GAAG;IACCA,OAAO,GAAG,KAAK;IACf3K,IAAI,GAAG4K,GAAG,CAAC5K,IAAI,CAAC;EACpB,CAAC,QAAQA,IAAI,IAAI2K,OAAO;EACxB,OAAO;IAAE3K,IAAI,EAAEA,IAAI;IAAEhC,IAAI,EAAE0M;EAAW,CAAC;EAEvC,SAASG,MAAMA,CAACC,QAAQ,EAAE;IACtBH,OAAO,GAAG,IAAI;IACd,OAAOG,QAAQ;EACnB;EAEA,SAASC,OAAOA,CAACC,KAAK,EAAE;IACpB,OAAQA,KAAK,CAACD,OAAO,CAAC,CAAC,IAAIC,KAAK,CAAClI,OAAO,CAAC,CAAC,GAAG,CAAC,KACpC2C,UAAU,CAACuF,KAAK,CAAChM,IAAI,CAAC,CAAC,CAAC,IACxByG,UAAU,CAACuF,KAAK,CAACxG,MAAM,CAAC,CAAC,CAAC,CAAE;EAC1C;EAEA,SAASyG,QAAQA,CAAC5H,CAAC,EAAE;IACjB,IAAIC,CAAC,GAAG,EAAE;IACV,KAAK,IAAIwE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzE,CAAC,CAACqD,MAAM,EAAE,EAAEoB,CAAC,EAAE;MAC/B,IAAIoD,EAAE,GAAGN,GAAG,CAACvH,CAAC,CAACyE,CAAC,CAAC,CAAC;MAClB,IAAIoD,EAAE,IAAI,IAAI,EAAE;QACZ5H,CAAC,CAACzE,IAAI,CAACqM,EAAE,CAAC;MACd;IACJ;IACA,OAAO5H,CAAC;EACZ;EAEA,SAAS6H,YAAYA,CAACH,KAAK,EAAEtH,CAAC,EAAE;IAC5B,IAAI0H,WAAW,GAAGZ,OAAO;IACzB,IAAIa,UAAU,GAAGjI,MAAM;IAEvB,IAAI4H,KAAK,CAAC1K,SAAS,CAAC,CAAC,EAAE;MACnB8C,MAAM,GAAGA,MAAM,CAACkI,YAAY,CAACN,KAAK,CAAC1K,SAAS,CAAC,CAAC,CAAC8C,MAAM,CAAC,CAAC,CAAC;IAC5D;IAEA,IAAImC,IAAI,GAAGyF,KAAK,CAACzF,IAAI,CAAC,CAAC;IACvB,IAAIA,IAAI,IAAI,OAAOA,IAAI,CAACvH,IAAI,KAAK,UAAU,EAAE;MACzCuH,IAAI,GAAGA,IAAI,CAACvH,IAAI,CAAC,CAAC;MAClB,IAAIuH,IAAI,EAAE;QACNA,IAAI,GAAGA,IAAI,CAACvH,IAAI,CAACoF,MAAM,CAAC;QACxBoH,OAAO,GAAGA,OAAO,GAAG9O,GAAG,CAAC2I,IAAI,CAACkH,SAAS,CAACf,OAAO,EAAEjF,IAAI,CAAC,GAAGA,IAAI;MAChE;IACJ;IAEA,IAAI;MACA,OAAO7B,CAAC,CAAC,CAAC;IACd,CAAC,SACO;MACJ8G,OAAO,GAAGY,WAAW;MACrBhI,MAAM,GAAGiI,UAAU;IACvB;EACJ;EAEA,SAASG,SAASA,CAACR,KAAK,EAAE;IACtB,IAAIR,OAAO,IAAI,IAAI,EAAE;MACjB,OAAO,KAAK;IAChB;IAEA,IAAI3D,GAAG,GAAGmE,KAAK,CAAClE,OAAO,CAAC,CAAC;IACzB,IAAID,GAAG,EAAE;MACLA,GAAG,GAAGA,GAAG,CAAC7I,IAAI,CAACoF,MAAM,CAAC;IAC1B;IAEA,IAAIoH,OAAO,IAAI3D,GAAG,EAAE;MAChBA,GAAG,GAAGnL,GAAG,CAAC2I,IAAI,CAACkH,SAAS,CAAC1E,GAAG,EAAE2D,OAAO,CAAC;IAC1C;IACA,OAAO3D,GAAG;EACd;EAEA,SAAS+D,GAAGA,CAACI,KAAK,EAAE;IAChB,OAAOG,YAAY,CAACH,KAAK,EAAE,YAAU;MACjC,IAAI,EAAEA,KAAK,YAAY5P,KAAK,IAAI4P,KAAK,YAAY1P,SAAS,CAAC,EAAE;QACzD,IAAIuL,GAAG,GAAG2E,SAAS,CAACR,KAAK,CAAC;QAC1B,IAAI,CAACnE,GAAG,EAAE;UACN,OAAOgE,MAAM,CAAC,IAAI,CAAC;QACvB;QACAH,UAAU,GAAGA,UAAU,GAAGhP,GAAG,CAAC2I,IAAI,CAACoH,KAAK,CAACf,UAAU,EAAE7D,GAAG,CAAC,GAAGA,GAAG;MACnE;MACA,OAAO9H,QAAQ,CAAC;QACZ1D,IAAI,EAAE,SAAAA,CAAS2P,KAAK,EAAE;UAClB,IAAIA,KAAK,CAACpD,QAAQ,CAAClB,MAAM,KAAK,CAAC,IAAI,CAACqE,OAAO,CAACC,KAAK,CAAC,EAAE;YAChD,OAAOH,MAAM,CAAC,IAAI,CAAC;UACvB;UACA,OAAOG,KAAK;QAChB,CAAC;QACD1P,SAAS,EAAE,SAAAA,CAAS0P,KAAK,EAAE;UACvB,IAAI,CAACD,OAAO,CAACC,KAAK,CAAC,EAAE;YACjB,OAAOH,MAAM,CAAC,IAAI,CAAC;UACvB;UACA,IAAIK,EAAE,GAAG,IAAI5P,SAAS,CAAC0P,KAAK,CAAC/N,OAAO,CAAC;UACrCiO,EAAE,CAACxC,KAAK,GAAGuC,QAAQ,CAACD,KAAK,CAACtC,KAAK,CAAC;UAChC,IAAIwC,EAAE,CAACxC,KAAK,CAAChC,MAAM,KAAK,CAAC,EAAE;YACvB,OAAOmE,MAAM,CAAC,IAAI,CAAC;UACvB;UACA,OAAOK,EAAE;QACb,CAAC;QACDpH,MAAM,EAAE,SAAAA,CAASkH,KAAK,EAAE;UACpB,IAAI,CAACD,OAAO,CAACC,KAAK,CAAC,EAAE;YACjB,OAAOH,MAAM,CAAC,IAAI,CAAC;UACvB;UACA,OAAOG,KAAK;QAChB,CAAC;QACDhH,GAAG,EAAE,SAAAA,CAASgH,KAAK,EAAE;UACjB,IAAI,CAACD,OAAO,CAACC,KAAK,CAAC,EAAE;YACjB,OAAOH,MAAM,CAAC,IAAI,CAAC;UACvB;UACA,OAAOG,KAAK;QAChB,CAAC;QACDzM,IAAI,EAAE,SAAAA,CAASyM,KAAK,EAAE;UAClB,IAAI,CAAC,IAAI,CAACrF,IAAI,CAACqF,KAAK,CAACvB,OAAO,CAAC,CAAC,CAAC,IAAI,CAACsB,OAAO,CAACC,KAAK,CAAC,EAAE;YAChD,OAAOH,MAAM,CAAC,IAAI,CAAC;UACvB;UACA,OAAOG,KAAK;QAChB,CAAC;QACDrN,KAAK,EAAE,SAAAA,CAASqN,KAAK,EAAE;UACnB,IAAI,EAAEA,KAAK,CAACD,OAAO,CAAC,CAAC,IAAIC,KAAK,CAAClI,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;YAC3C,OAAO+H,MAAM,CAAC,IAAI,CAAC;UACvB;UACA,OAAOG,KAAK;QAChB,CAAC;QACD5P,KAAK,EAAE,SAAAA,CAAS4P,KAAK,EAAE;UACnB,IAAI,EAAEA,KAAK,CAACD,OAAO,CAAC,CAAC,IAAIC,KAAK,CAAClI,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;YAC3C,OAAO+H,MAAM,CAAC,IAAI,CAAC;UACvB;UACA,IAAIK,EAAE,GAAG,IAAI9P,KAAK,CAAC4P,KAAK,CAAC/N,OAAO,CAAC;UACjCiO,EAAE,CAAClK,QAAQ,GAAGiK,QAAQ,CAACD,KAAK,CAAChK,QAAQ,CAAC;UACtCkK,EAAE,CAACpB,QAAQ,GAAGkB,KAAK,CAAClB,QAAQ;UAC5B,IAAIkB,KAAK,KAAKhL,IAAI,IAAIkL,EAAE,CAAClK,QAAQ,CAAC0F,MAAM,KAAK,CAAC,IAAI,CAACsE,KAAK,CAAClB,QAAQ,EAAE;YAC/D,OAAOe,MAAM,CAAC,IAAI,CAAC;UACvB;UACA,OAAOK,EAAE;QACb,CAAC;QACD7G,IAAI,EAAE,SAAAA,CAAS2G,KAAK,EAAE;UAClB,IAAI,CAACD,OAAO,CAACC,KAAK,CAAC,EAAE;YACjB,OAAOH,MAAM,CAAC,IAAI,CAAC;UACvB;UACA,OAAOG,KAAK;QAChB;MACJ,CAAC,EAAEA,KAAK,CAAC;IACb,CAAC,CAAC;EACN;AACJ;AAEA,SAASU,SAASA,CAAC7O,KAAK,EAAEI,OAAO,EAAE;EAC/B,IAAI0O,OAAO,GAAG9Q,aAAa,CAAC,CAAC;EAE7B,KAAK,IAAIiN,CAAC,IAAI7K,OAAO,EAAE;IACnB,IAAI6K,CAAC,IAAI,QAAQ,IAAIjL,KAAK,CAACI,OAAO,CAACK,GAAG,IAAIT,KAAK,CAACI,OAAO,CAACK,GAAG,CAACsO,aAAa,EAAE;MACvE;MACA;MACA;MACA;MACA;IACJ;IACA/O,KAAK,CAACI,OAAO,CAAC4O,GAAG,CAAC,MAAM,GAAG/D,CAAC,EAAE7K,OAAO,CAAC6K,CAAC,CAAC,CAAC;EAC7C;EAEAxG,SAAS,CAACzE,KAAK,EAAE8O,OAAO,CAACG,OAAO,CAAC;EAEjC,OAAOH,OAAO;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASI,eAAeA,CAAClP,KAAK,EAAEI,OAAO,EAAE;EACrC,IAAI0O,OAAO,GAAG9Q,aAAa,CAAC,CAAC;EAE7B,KAAK,IAAIiN,CAAC,IAAI7K,OAAO,EAAE;IACnB,IAAI6K,CAAC,IAAI,QAAQ,IAAIjL,KAAK,CAACI,OAAO,CAACK,GAAG,IAAIT,KAAK,CAACI,OAAO,CAACK,GAAG,CAACsO,aAAa,EAAE;MACvE;MACA;MACA;MACA;MACA;IACJ;IACA/O,KAAK,CAACI,OAAO,CAAC4O,GAAG,CAAC,MAAM,GAAG/D,CAAC,EAAE7K,OAAO,CAAC6K,CAAC,CAAC,CAAC;EAC7C;EAEA,IAAIpG,MAAM,CAACC,IAAI,IAAI,CAACzG,OAAO,CAAC8G,OAAO,CAACC,MAAM,EAAE;IACxCR,MAAM,CAAC5E,KAAK,EAAE8O,OAAO,CAACG,OAAO,CAAC;EAClC,CAAC,MAAM;IACHxK,SAAS,CAACzE,KAAK,EAAE8O,OAAO,CAACG,OAAO,CAAC;EACrC;EAEA,OAAOH,OAAO;AAClB;AAGA,SAASD,SAAS,EAAEK,eAAe,EAAE/Q,MAAM,EAAEsG,SAAS,EAAEG,MAAM,EAAE7E,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}