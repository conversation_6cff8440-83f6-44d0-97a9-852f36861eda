{"ast": null, "code": "import { addDays } from './add-days';\n/**\n * A function that adds and subtracts weeks from a Date object.\n *\n * @param date - The initial date value.\n * @param offset - The number of weeks to add/subtract from the date.\n * @returns - A new `Date` instance.\n *\n * @example\n * ```ts-no-run\n * addWeeks(new Date(2016, 5, 1), 3); // 2016-6-22\n * addWeeks(new Date(2016, 5, 1), -3); // 2015-5-11\n * ```\n */\nexport var addWeeks = function (date, offset) {\n  return addDays(date, offset * 7);\n};", "map": {"version": 3, "names": ["addDays", "addWeeks", "date", "offset"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/add-weeks.js"], "sourcesContent": ["import { addDays } from './add-days';\n/**\n * A function that adds and subtracts weeks from a Date object.\n *\n * @param date - The initial date value.\n * @param offset - The number of weeks to add/subtract from the date.\n * @returns - A new `Date` instance.\n *\n * @example\n * ```ts-no-run\n * addWeeks(new Date(2016, 5, 1), 3); // 2016-6-22\n * addWeeks(new Date(2016, 5, 1), -3); // 2015-5-11\n * ```\n */\nexport var addWeeks = function (date, offset) {\n    return addDays(date, offset * 7);\n};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,QAAQ,GAAG,SAAAA,CAAUC,IAAI,EAAEC,MAAM,EAAE;EAC1C,OAAOH,OAAO,CAACE,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC;AACpC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}