{"ast": null, "code": "import Node from './node';\nimport { defined, isTransparent } from '../util';\nimport { DASH_ARRAYS, SOLID, BUTT, PATTERN } from '../core/constants';\nimport { NONE, POINT_DIGITS } from './constants';\nimport renderAllAttr from './utils/render-all-attributes';\nimport renderAttr from './utils/render-attribute';\nvar ATTRIBUTE_MAP = {\n  \"fill.opacity\": \"fill-opacity\",\n  \"stroke.color\": \"stroke\",\n  \"stroke.width\": \"stroke-width\",\n  \"stroke.opacity\": \"stroke-opacity\"\n};\nvar PathNode = function (Node) {\n  function PathNode() {\n    Node.apply(this, arguments);\n  }\n  if (Node) PathNode.__proto__ = Node;\n  PathNode.prototype = Object.create(Node && Node.prototype);\n  PathNode.prototype.constructor = PathNode;\n  PathNode.prototype.geometryChange = function geometryChange() {\n    this.attr(\"d\", this.renderData());\n    this.invalidate();\n  };\n  PathNode.prototype.optionsChange = function optionsChange(e) {\n    switch (e.field) {\n      case \"fill\":\n        if (e.value) {\n          this.allAttr(this.mapFill(e.value));\n        } else {\n          this.removeAttr(\"fill\");\n        }\n        break;\n      case \"fill.color\":\n        this.allAttr(this.mapFill({\n          color: e.value\n        }));\n        break;\n      case \"stroke\":\n        if (e.value) {\n          this.allAttr(this.mapStroke(e.value));\n        } else {\n          this.removeAttr(\"stroke\");\n        }\n        break;\n      case \"transform\":\n        this.transformChange(e.value);\n        break;\n      default:\n        var name = ATTRIBUTE_MAP[e.field];\n        if (name) {\n          this.attr(name, e.value);\n        }\n        break;\n    }\n    this.accessibilityOptionsChange(e);\n    Node.prototype.optionsChange.call(this, e);\n  };\n  PathNode.prototype.content = function content() {\n    if (this.element) {\n      this.element.textContent = this.srcElement.content();\n    }\n  };\n  PathNode.prototype.renderData = function renderData() {\n    return this.srcElement.toString(POINT_DIGITS) || undefined;\n  };\n  PathNode.prototype.mapStroke = function mapStroke(stroke) {\n    var attrs = [];\n    if (stroke && !isTransparent(stroke.color)) {\n      attrs.push([\"stroke\", stroke.color]);\n      attrs.push([\"stroke-width\", stroke.width]);\n      attrs.push([\"stroke-linecap\", this.renderLinecap(stroke)]);\n      attrs.push([\"stroke-linejoin\", stroke.lineJoin]);\n      if (defined(stroke.opacity)) {\n        attrs.push([\"stroke-opacity\", stroke.opacity]);\n      }\n      if (defined(stroke.dashType)) {\n        attrs.push([\"stroke-dasharray\", this.renderDashType(stroke)]);\n      }\n    } else {\n      attrs.push([\"stroke\", NONE]);\n    }\n    return attrs;\n  };\n  PathNode.prototype.renderStroke = function renderStroke() {\n    return renderAllAttr(this.mapStroke(this.srcElement.options.stroke));\n  };\n  PathNode.prototype.renderDashType = function renderDashType(stroke) {\n    var dashType = stroke.dashType;\n    var width = stroke.width;\n    if (width === void 0) width = 1;\n    if (dashType && dashType !== SOLID) {\n      var dashArray = DASH_ARRAYS[dashType.toLowerCase()];\n      var result = [];\n      for (var i = 0; i < dashArray.length; i++) {\n        result.push(dashArray[i] * width);\n      }\n      return result.join(\" \");\n    }\n  };\n  PathNode.prototype.renderLinecap = function renderLinecap(stroke) {\n    var dashType = stroke.dashType;\n    var lineCap = stroke.lineCap;\n    return dashType && dashType !== SOLID ? BUTT : lineCap;\n  };\n  PathNode.prototype.mapFill = function mapFill(fill) {\n    var attrs = [];\n    if (!(fill && (fill.nodeType === \"Gradient\" || fill.nodeType === PATTERN))) {\n      if (fill && !isTransparent(fill.color)) {\n        attrs.push([\"fill\", fill.color]);\n        if (defined(fill.opacity)) {\n          attrs.push([\"fill-opacity\", fill.opacity]);\n        }\n      } else {\n        attrs.push([\"fill\", NONE]);\n      }\n    }\n    return attrs;\n  };\n  PathNode.prototype.renderFill = function renderFill() {\n    return renderAllAttr(this.mapFill(this.srcElement.options.fill));\n  };\n  PathNode.prototype.template = function template() {\n    return \"<path \" + this.renderId() + \" \" + this.renderStyle() + \" \" + this.renderOpacity() + \" \" + renderAttr('d', this.renderData()) + \"\" + this.renderStroke() + this.renderFill() + this.renderDefinitions() + this.renderTransform() + this.renderClassName() + \" \" + this.renderRole() + this.renderAriaLabel() + \" \" + this.renderAriaRoleDescription() + this.renderAriaChecked() + \" ></path>\";\n  };\n  return PathNode;\n}(Node);\nexport default PathNode;", "map": {"version": 3, "names": ["Node", "defined", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DASH_ARRAYS", "SOLID", "BUTT", "PATTERN", "NONE", "POINT_DIGITS", "renderAllAttr", "renderAttr", "ATTRIBUTE_MAP", "PathNode", "apply", "arguments", "__proto__", "prototype", "Object", "create", "constructor", "geometryChange", "attr", "renderData", "invalidate", "optionsChange", "e", "field", "value", "allAttr", "mapFill", "removeAttr", "color", "mapStroke", "transformChange", "name", "accessibilityOptionsChange", "call", "content", "element", "textContent", "srcElement", "toString", "undefined", "stroke", "attrs", "push", "width", "renderLinecap", "lineJoin", "opacity", "dashType", "renderDashType", "renderStroke", "options", "dashArray", "toLowerCase", "result", "i", "length", "join", "lineCap", "fill", "nodeType", "renderFill", "template", "renderId", "renderStyle", "renderOpacity", "renderDefinitions", "renderTransform", "renderClassName", "renderRole", "renderAriaLabel", "renderAriaRoleDescription", "renderAriaChecked"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/svg/path-node.js"], "sourcesContent": ["import Node from './node';\nimport { defined, isTransparent } from '../util';\nimport { DASH_ARRAYS, SOLID, BUTT, PATTERN } from '../core/constants';\nimport { NONE, POINT_DIGITS } from './constants';\nimport renderAllAttr from './utils/render-all-attributes';\nimport renderAttr from './utils/render-attribute';\n\nvar ATTRIBUTE_MAP = {\n    \"fill.opacity\": \"fill-opacity\",\n    \"stroke.color\": \"stroke\",\n    \"stroke.width\": \"stroke-width\",\n    \"stroke.opacity\": \"stroke-opacity\"\n};\n\nvar PathNode = (function (Node) {\n    function PathNode () {\n        Node.apply(this, arguments);\n    }\n\n    if ( Node ) PathNode.__proto__ = Node;\n    PathNode.prototype = Object.create( Node && Node.prototype );\n    PathNode.prototype.constructor = PathNode;\n\n    PathNode.prototype.geometryChange = function geometryChange () {\n        this.attr(\"d\", this.renderData());\n        this.invalidate();\n    };\n\n    PathNode.prototype.optionsChange = function optionsChange (e) {\n        switch (e.field) {\n        case \"fill\":\n            if (e.value) {\n                this.allAttr(this.mapFill(e.value));\n            } else {\n                this.removeAttr(\"fill\");\n            }\n            break;\n\n        case \"fill.color\":\n            this.allAttr(this.mapFill({ color: e.value }));\n            break;\n\n        case \"stroke\":\n            if (e.value) {\n                this.allAttr(this.mapStroke(e.value));\n            } else {\n                this.removeAttr(\"stroke\");\n            }\n            break;\n\n        case \"transform\":\n            this.transformChange(e.value);\n            break;\n\n        default:\n            var name = ATTRIBUTE_MAP[e.field];\n            if (name) {\n                this.attr(name, e.value);\n            }\n            break;\n        }\n\n        this.accessibilityOptionsChange(e);\n\n        Node.prototype.optionsChange.call(this, e);\n    };\n\n    PathNode.prototype.content = function content () {\n        if (this.element) {\n            this.element.textContent = this.srcElement.content();\n        }\n    };\n\n    PathNode.prototype.renderData = function renderData () {\n        return this.srcElement.toString(POINT_DIGITS) || undefined;\n    };\n\n    PathNode.prototype.mapStroke = function mapStroke (stroke) {\n        var attrs = [];\n\n        if (stroke && !isTransparent(stroke.color)) {\n            attrs.push([ \"stroke\", stroke.color ]);\n            attrs.push([ \"stroke-width\", stroke.width ]);\n            attrs.push([ \"stroke-linecap\", this.renderLinecap(stroke) ]);\n            attrs.push([ \"stroke-linejoin\", stroke.lineJoin ]);\n\n            if (defined(stroke.opacity)) {\n                attrs.push([ \"stroke-opacity\", stroke.opacity ]);\n            }\n\n            if (defined(stroke.dashType)) {\n                attrs.push([ \"stroke-dasharray\", this.renderDashType(stroke) ]);\n            }\n        } else {\n            attrs.push([ \"stroke\", NONE ]);\n        }\n\n        return attrs;\n    };\n\n    PathNode.prototype.renderStroke = function renderStroke () {\n        return renderAllAttr(\n            this.mapStroke(this.srcElement.options.stroke)\n        );\n    };\n\n    PathNode.prototype.renderDashType = function renderDashType (stroke) {\n        var dashType = stroke.dashType;\n        var width = stroke.width; if ( width === void 0 ) width = 1;\n\n        if (dashType && dashType !== SOLID) {\n            var dashArray = DASH_ARRAYS[dashType.toLowerCase()];\n            var result = [];\n\n            for (var i = 0; i < dashArray.length; i++) {\n                result.push(dashArray[i] * width);\n            }\n\n            return result.join(\" \");\n        }\n    };\n\n    PathNode.prototype.renderLinecap = function renderLinecap (stroke) {\n        var dashType = stroke.dashType;\n        var lineCap = stroke.lineCap;\n\n        return (dashType && dashType !== SOLID) ? BUTT : lineCap;\n    };\n\n    PathNode.prototype.mapFill = function mapFill (fill) {\n        var attrs = [];\n        if (!(fill && (fill.nodeType === \"Gradient\" || fill.nodeType === PATTERN))) {\n            if (fill && !isTransparent(fill.color)) {\n                attrs.push([ \"fill\", fill.color ]);\n\n                if (defined(fill.opacity)) {\n                    attrs.push([ \"fill-opacity\", fill.opacity ]);\n                }\n            } else {\n                attrs.push([ \"fill\", NONE ]);\n            }\n        }\n\n        return attrs;\n    };\n\n    PathNode.prototype.renderFill = function renderFill () {\n        return renderAllAttr(\n            this.mapFill(this.srcElement.options.fill)\n        );\n    };\n\n    PathNode.prototype.template = function template () {\n        return \"<path \" + (this.renderId()) + \" \" + (this.renderStyle()) + \" \" + (this.renderOpacity()) + \" \" + (renderAttr('d', this.renderData())) +\n                \"\" + (this.renderStroke()) + (this.renderFill()) + (this.renderDefinitions()) + (this.renderTransform()) +\n                (this.renderClassName()) + \" \" + (this.renderRole()) +\n                (this.renderAriaLabel()) + \" \" + (this.renderAriaRoleDescription()) +\n                (this.renderAriaChecked()) + \" ></path>\";\n    };\n\n    return PathNode;\n}(Node));\n\nexport default PathNode;\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,QAAQ;AACzB,SAASC,OAAO,EAAEC,aAAa,QAAQ,SAAS;AAChD,SAASC,WAAW,EAAEC,KAAK,EAAEC,IAAI,EAAEC,OAAO,QAAQ,mBAAmB;AACrE,SAASC,IAAI,EAAEC,YAAY,QAAQ,aAAa;AAChD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,UAAU,MAAM,0BAA0B;AAEjD,IAAIC,aAAa,GAAG;EAChB,cAAc,EAAE,cAAc;EAC9B,cAAc,EAAE,QAAQ;EACxB,cAAc,EAAE,cAAc;EAC9B,gBAAgB,EAAE;AACtB,CAAC;AAED,IAAIC,QAAQ,GAAI,UAAUZ,IAAI,EAAE;EAC5B,SAASY,QAAQA,CAAA,EAAI;IACjBZ,IAAI,CAACa,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EAC/B;EAEA,IAAKd,IAAI,EAAGY,QAAQ,CAACG,SAAS,GAAGf,IAAI;EACrCY,QAAQ,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAElB,IAAI,IAAIA,IAAI,CAACgB,SAAU,CAAC;EAC5DJ,QAAQ,CAACI,SAAS,CAACG,WAAW,GAAGP,QAAQ;EAEzCA,QAAQ,CAACI,SAAS,CAACI,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAI;IAC3D,IAAI,CAACC,IAAI,CAAC,GAAG,EAAE,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC;IACjC,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB,CAAC;EAEDX,QAAQ,CAACI,SAAS,CAACQ,aAAa,GAAG,SAASA,aAAaA,CAAEC,CAAC,EAAE;IAC1D,QAAQA,CAAC,CAACC,KAAK;MACf,KAAK,MAAM;QACP,IAAID,CAAC,CAACE,KAAK,EAAE;UACT,IAAI,CAACC,OAAO,CAAC,IAAI,CAACC,OAAO,CAACJ,CAAC,CAACE,KAAK,CAAC,CAAC;QACvC,CAAC,MAAM;UACH,IAAI,CAACG,UAAU,CAAC,MAAM,CAAC;QAC3B;QACA;MAEJ,KAAK,YAAY;QACb,IAAI,CAACF,OAAO,CAAC,IAAI,CAACC,OAAO,CAAC;UAAEE,KAAK,EAAEN,CAAC,CAACE;QAAM,CAAC,CAAC,CAAC;QAC9C;MAEJ,KAAK,QAAQ;QACT,IAAIF,CAAC,CAACE,KAAK,EAAE;UACT,IAAI,CAACC,OAAO,CAAC,IAAI,CAACI,SAAS,CAACP,CAAC,CAACE,KAAK,CAAC,CAAC;QACzC,CAAC,MAAM;UACH,IAAI,CAACG,UAAU,CAAC,QAAQ,CAAC;QAC7B;QACA;MAEJ,KAAK,WAAW;QACZ,IAAI,CAACG,eAAe,CAACR,CAAC,CAACE,KAAK,CAAC;QAC7B;MAEJ;QACI,IAAIO,IAAI,GAAGvB,aAAa,CAACc,CAAC,CAACC,KAAK,CAAC;QACjC,IAAIQ,IAAI,EAAE;UACN,IAAI,CAACb,IAAI,CAACa,IAAI,EAAET,CAAC,CAACE,KAAK,CAAC;QAC5B;QACA;IACJ;IAEA,IAAI,CAACQ,0BAA0B,CAACV,CAAC,CAAC;IAElCzB,IAAI,CAACgB,SAAS,CAACQ,aAAa,CAACY,IAAI,CAAC,IAAI,EAAEX,CAAC,CAAC;EAC9C,CAAC;EAEDb,QAAQ,CAACI,SAAS,CAACqB,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;IAC7C,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,WAAW,GAAG,IAAI,CAACC,UAAU,CAACH,OAAO,CAAC,CAAC;IACxD;EACJ,CAAC;EAEDzB,QAAQ,CAACI,SAAS,CAACM,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAI;IACnD,OAAO,IAAI,CAACkB,UAAU,CAACC,QAAQ,CAACjC,YAAY,CAAC,IAAIkC,SAAS;EAC9D,CAAC;EAED9B,QAAQ,CAACI,SAAS,CAACgB,SAAS,GAAG,SAASA,SAASA,CAAEW,MAAM,EAAE;IACvD,IAAIC,KAAK,GAAG,EAAE;IAEd,IAAID,MAAM,IAAI,CAACzC,aAAa,CAACyC,MAAM,CAACZ,KAAK,CAAC,EAAE;MACxCa,KAAK,CAACC,IAAI,CAAC,CAAE,QAAQ,EAAEF,MAAM,CAACZ,KAAK,CAAE,CAAC;MACtCa,KAAK,CAACC,IAAI,CAAC,CAAE,cAAc,EAAEF,MAAM,CAACG,KAAK,CAAE,CAAC;MAC5CF,KAAK,CAACC,IAAI,CAAC,CAAE,gBAAgB,EAAE,IAAI,CAACE,aAAa,CAACJ,MAAM,CAAC,CAAE,CAAC;MAC5DC,KAAK,CAACC,IAAI,CAAC,CAAE,iBAAiB,EAAEF,MAAM,CAACK,QAAQ,CAAE,CAAC;MAElD,IAAI/C,OAAO,CAAC0C,MAAM,CAACM,OAAO,CAAC,EAAE;QACzBL,KAAK,CAACC,IAAI,CAAC,CAAE,gBAAgB,EAAEF,MAAM,CAACM,OAAO,CAAE,CAAC;MACpD;MAEA,IAAIhD,OAAO,CAAC0C,MAAM,CAACO,QAAQ,CAAC,EAAE;QAC1BN,KAAK,CAACC,IAAI,CAAC,CAAE,kBAAkB,EAAE,IAAI,CAACM,cAAc,CAACR,MAAM,CAAC,CAAE,CAAC;MACnE;IACJ,CAAC,MAAM;MACHC,KAAK,CAACC,IAAI,CAAC,CAAE,QAAQ,EAAEtC,IAAI,CAAE,CAAC;IAClC;IAEA,OAAOqC,KAAK;EAChB,CAAC;EAEDhC,QAAQ,CAACI,SAAS,CAACoC,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAI;IACvD,OAAO3C,aAAa,CAChB,IAAI,CAACuB,SAAS,CAAC,IAAI,CAACQ,UAAU,CAACa,OAAO,CAACV,MAAM,CACjD,CAAC;EACL,CAAC;EAED/B,QAAQ,CAACI,SAAS,CAACmC,cAAc,GAAG,SAASA,cAAcA,CAAER,MAAM,EAAE;IACjE,IAAIO,QAAQ,GAAGP,MAAM,CAACO,QAAQ;IAC9B,IAAIJ,KAAK,GAAGH,MAAM,CAACG,KAAK;IAAE,IAAKA,KAAK,KAAK,KAAK,CAAC,EAAGA,KAAK,GAAG,CAAC;IAE3D,IAAII,QAAQ,IAAIA,QAAQ,KAAK9C,KAAK,EAAE;MAChC,IAAIkD,SAAS,GAAGnD,WAAW,CAAC+C,QAAQ,CAACK,WAAW,CAAC,CAAC,CAAC;MACnD,IAAIC,MAAM,GAAG,EAAE;MAEf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;QACvCD,MAAM,CAACX,IAAI,CAACS,SAAS,CAACG,CAAC,CAAC,GAAGX,KAAK,CAAC;MACrC;MAEA,OAAOU,MAAM,CAACG,IAAI,CAAC,GAAG,CAAC;IAC3B;EACJ,CAAC;EAED/C,QAAQ,CAACI,SAAS,CAAC+B,aAAa,GAAG,SAASA,aAAaA,CAAEJ,MAAM,EAAE;IAC/D,IAAIO,QAAQ,GAAGP,MAAM,CAACO,QAAQ;IAC9B,IAAIU,OAAO,GAAGjB,MAAM,CAACiB,OAAO;IAE5B,OAAQV,QAAQ,IAAIA,QAAQ,KAAK9C,KAAK,GAAIC,IAAI,GAAGuD,OAAO;EAC5D,CAAC;EAEDhD,QAAQ,CAACI,SAAS,CAACa,OAAO,GAAG,SAASA,OAAOA,CAAEgC,IAAI,EAAE;IACjD,IAAIjB,KAAK,GAAG,EAAE;IACd,IAAI,EAAEiB,IAAI,KAAKA,IAAI,CAACC,QAAQ,KAAK,UAAU,IAAID,IAAI,CAACC,QAAQ,KAAKxD,OAAO,CAAC,CAAC,EAAE;MACxE,IAAIuD,IAAI,IAAI,CAAC3D,aAAa,CAAC2D,IAAI,CAAC9B,KAAK,CAAC,EAAE;QACpCa,KAAK,CAACC,IAAI,CAAC,CAAE,MAAM,EAAEgB,IAAI,CAAC9B,KAAK,CAAE,CAAC;QAElC,IAAI9B,OAAO,CAAC4D,IAAI,CAACZ,OAAO,CAAC,EAAE;UACvBL,KAAK,CAACC,IAAI,CAAC,CAAE,cAAc,EAAEgB,IAAI,CAACZ,OAAO,CAAE,CAAC;QAChD;MACJ,CAAC,MAAM;QACHL,KAAK,CAACC,IAAI,CAAC,CAAE,MAAM,EAAEtC,IAAI,CAAE,CAAC;MAChC;IACJ;IAEA,OAAOqC,KAAK;EAChB,CAAC;EAEDhC,QAAQ,CAACI,SAAS,CAAC+C,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAI;IACnD,OAAOtD,aAAa,CAChB,IAAI,CAACoB,OAAO,CAAC,IAAI,CAACW,UAAU,CAACa,OAAO,CAACQ,IAAI,CAC7C,CAAC;EACL,CAAC;EAEDjD,QAAQ,CAACI,SAAS,CAACgD,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;IAC/C,OAAO,QAAQ,GAAI,IAAI,CAACC,QAAQ,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAACC,WAAW,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAACC,aAAa,CAAC,CAAE,GAAG,GAAG,GAAIzD,UAAU,CAAC,GAAG,EAAE,IAAI,CAACY,UAAU,CAAC,CAAC,CAAE,GACpI,EAAE,GAAI,IAAI,CAAC8B,YAAY,CAAC,CAAE,GAAI,IAAI,CAACW,UAAU,CAAC,CAAE,GAAI,IAAI,CAACK,iBAAiB,CAAC,CAAE,GAAI,IAAI,CAACC,eAAe,CAAC,CAAE,GACvG,IAAI,CAACC,eAAe,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAACC,UAAU,CAAC,CAAE,GACnD,IAAI,CAACC,eAAe,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAACC,yBAAyB,CAAC,CAAE,GAClE,IAAI,CAACC,iBAAiB,CAAC,CAAE,GAAG,WAAW;EACpD,CAAC;EAED,OAAO9D,QAAQ;AACnB,CAAC,CAACZ,IAAI,CAAE;AAER,eAAeY,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}