{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport CaretDownOutlined from \"@ant-design/icons/es/icons/CaretDownOutlined\";\nimport CaretUpOutlined from \"@ant-design/icons/es/icons/CaretUpOutlined\";\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport Tooltip from '../../tooltip';\nimport { getColumnKey, getColumnPos, renderColumnTitle, safeColumnTitle } from '../util';\nvar ASCEND = 'ascend';\nvar DESCEND = 'descend';\nfunction getMultiplePriority(column) {\n  if (_typeof(column.sorter) === 'object' && typeof column.sorter.multiple === 'number') {\n    return column.sorter.multiple;\n  }\n  return false;\n}\nfunction getSortFunction(sorter) {\n  if (typeof sorter === 'function') {\n    return sorter;\n  }\n  if (sorter && _typeof(sorter) === 'object' && sorter.compare) {\n    return sorter.compare;\n  }\n  return false;\n}\nfunction nextSortDirection(sortDirections, current) {\n  if (!current) {\n    return sortDirections[0];\n  }\n  return sortDirections[sortDirections.indexOf(current) + 1];\n}\nfunction collectSortStates(columns, init, pos) {\n  var sortStates = [];\n  function pushState(column, columnPos) {\n    sortStates.push({\n      column: column,\n      key: getColumnKey(column, columnPos),\n      multiplePriority: getMultiplePriority(column),\n      sortOrder: column.sortOrder\n    });\n  }\n  (columns || []).forEach(function (column, index) {\n    var columnPos = getColumnPos(index, pos);\n    if (column.children) {\n      if ('sortOrder' in column) {\n        // Controlled\n        pushState(column, columnPos);\n      }\n      sortStates = [].concat(_toConsumableArray(sortStates), _toConsumableArray(collectSortStates(column.children, init, columnPos)));\n    } else if (column.sorter) {\n      if ('sortOrder' in column) {\n        // Controlled\n        pushState(column, columnPos);\n      } else if (init && column.defaultSortOrder) {\n        // Default sorter\n        sortStates.push({\n          column: column,\n          key: getColumnKey(column, columnPos),\n          multiplePriority: getMultiplePriority(column),\n          sortOrder: column.defaultSortOrder\n        });\n      }\n    }\n  });\n  return sortStates;\n}\nfunction injectSorter(prefixCls, columns, sorterStates, triggerSorter, defaultSortDirections, tableLocale, tableShowSorterTooltip, pos) {\n  return (columns || []).map(function (column, index) {\n    var columnPos = getColumnPos(index, pos);\n    var newColumn = column;\n    if (newColumn.sorter) {\n      var sortDirections = newColumn.sortDirections || defaultSortDirections;\n      var showSorterTooltip = newColumn.showSorterTooltip === undefined ? tableShowSorterTooltip : newColumn.showSorterTooltip;\n      var columnKey = getColumnKey(newColumn, columnPos);\n      var sorterState = sorterStates.find(function (_ref) {\n        var key = _ref.key;\n        return key === columnKey;\n      });\n      var sorterOrder = sorterState ? sorterState.sortOrder : null;\n      var nextSortOrder = nextSortDirection(sortDirections, sorterOrder);\n      var upNode = sortDirections.includes(ASCEND) && (/*#__PURE__*/React.createElement(CaretUpOutlined, {\n        className: classNames(\"\".concat(prefixCls, \"-column-sorter-up\"), {\n          active: sorterOrder === ASCEND\n        }),\n        role: \"presentation\"\n      }));\n      var downNode = sortDirections.includes(DESCEND) && (/*#__PURE__*/React.createElement(CaretDownOutlined, {\n        className: classNames(\"\".concat(prefixCls, \"-column-sorter-down\"), {\n          active: sorterOrder === DESCEND\n        }),\n        role: \"presentation\"\n      }));\n      var _ref2 = tableLocale || {},\n        cancelSort = _ref2.cancelSort,\n        triggerAsc = _ref2.triggerAsc,\n        triggerDesc = _ref2.triggerDesc;\n      var sortTip = cancelSort;\n      if (nextSortOrder === DESCEND) {\n        sortTip = triggerDesc;\n      } else if (nextSortOrder === ASCEND) {\n        sortTip = triggerAsc;\n      }\n      var tooltipProps = _typeof(showSorterTooltip) === 'object' ? showSorterTooltip : {\n        title: sortTip\n      };\n      newColumn = _extends(_extends({}, newColumn), {\n        className: classNames(newColumn.className, _defineProperty({}, \"\".concat(prefixCls, \"-column-sort\"), sorterOrder)),\n        title: function title(renderProps) {\n          var renderSortTitle = /*#__PURE__*/React.createElement(\"div\", {\n            className: \"\".concat(prefixCls, \"-column-sorters\")\n          }, /*#__PURE__*/React.createElement(\"span\", {\n            className: \"\".concat(prefixCls, \"-column-title\")\n          }, renderColumnTitle(column.title, renderProps)), /*#__PURE__*/React.createElement(\"span\", {\n            className: classNames(\"\".concat(prefixCls, \"-column-sorter\"), _defineProperty({}, \"\".concat(prefixCls, \"-column-sorter-full\"), !!(upNode && downNode)))\n          }, /*#__PURE__*/React.createElement(\"span\", {\n            className: \"\".concat(prefixCls, \"-column-sorter-inner\")\n          }, upNode, downNode)));\n          return showSorterTooltip ? (/*#__PURE__*/React.createElement(Tooltip, _extends({}, tooltipProps), renderSortTitle)) : renderSortTitle;\n        },\n        onHeaderCell: function onHeaderCell(col) {\n          var cell = column.onHeaderCell && column.onHeaderCell(col) || {};\n          var originOnClick = cell.onClick;\n          var originOKeyDown = cell.onKeyDown;\n          cell.onClick = function (event) {\n            triggerSorter({\n              column: column,\n              key: columnKey,\n              sortOrder: nextSortOrder,\n              multiplePriority: getMultiplePriority(column)\n            });\n            originOnClick === null || originOnClick === void 0 ? void 0 : originOnClick(event);\n          };\n          cell.onKeyDown = function (event) {\n            if (event.keyCode === KeyCode.ENTER) {\n              triggerSorter({\n                column: column,\n                key: columnKey,\n                sortOrder: nextSortOrder,\n                multiplePriority: getMultiplePriority(column)\n              });\n              originOKeyDown === null || originOKeyDown === void 0 ? void 0 : originOKeyDown(event);\n            }\n          };\n          var renderTitle = safeColumnTitle(column.title, {});\n          var displayTitle = renderTitle === null || renderTitle === void 0 ? void 0 : renderTitle.toString();\n          // Inform the screen-reader so it can tell the visually impaired user which column is sorted\n          if (sorterOrder) {\n            cell['aria-sort'] = sorterOrder === 'ascend' ? 'ascending' : 'descending';\n          } else {\n            cell['aria-label'] = displayTitle || '';\n          }\n          cell.className = classNames(cell.className, \"\".concat(prefixCls, \"-column-has-sorters\"));\n          cell.tabIndex = 0;\n          if (column.ellipsis) {\n            cell.title = (renderTitle !== null && renderTitle !== void 0 ? renderTitle : '').toString();\n          }\n          return cell;\n        }\n      });\n    }\n    if ('children' in newColumn) {\n      newColumn = _extends(_extends({}, newColumn), {\n        children: injectSorter(prefixCls, newColumn.children, sorterStates, triggerSorter, defaultSortDirections, tableLocale, tableShowSorterTooltip, columnPos)\n      });\n    }\n    return newColumn;\n  });\n}\nfunction stateToInfo(sorterStates) {\n  var column = sorterStates.column,\n    sortOrder = sorterStates.sortOrder;\n  return {\n    column: column,\n    order: sortOrder,\n    field: column.dataIndex,\n    columnKey: column.key\n  };\n}\nfunction generateSorterInfo(sorterStates) {\n  var list = sorterStates.filter(function (_ref3) {\n    var sortOrder = _ref3.sortOrder;\n    return sortOrder;\n  }).map(stateToInfo);\n  // =========== Legacy compatible support ===========\n  // https://github.com/ant-design/ant-design/pull/19226\n  if (list.length === 0 && sorterStates.length) {\n    return _extends(_extends({}, stateToInfo(sorterStates[sorterStates.length - 1])), {\n      column: undefined\n    });\n  }\n  if (list.length <= 1) {\n    return list[0] || {};\n  }\n  return list;\n}\nexport function getSortData(data, sortStates, childrenColumnName) {\n  var innerSorterStates = sortStates.slice().sort(function (a, b) {\n    return b.multiplePriority - a.multiplePriority;\n  });\n  var cloneData = data.slice();\n  var runningSorters = innerSorterStates.filter(function (_ref4) {\n    var sorter = _ref4.column.sorter,\n      sortOrder = _ref4.sortOrder;\n    return getSortFunction(sorter) && sortOrder;\n  });\n  // Skip if no sorter needed\n  if (!runningSorters.length) {\n    return cloneData;\n  }\n  return cloneData.sort(function (record1, record2) {\n    for (var i = 0; i < runningSorters.length; i += 1) {\n      var sorterState = runningSorters[i];\n      var sorter = sorterState.column.sorter,\n        sortOrder = sorterState.sortOrder;\n      var compareFn = getSortFunction(sorter);\n      if (compareFn && sortOrder) {\n        var compareResult = compareFn(record1, record2, sortOrder);\n        if (compareResult !== 0) {\n          return sortOrder === ASCEND ? compareResult : -compareResult;\n        }\n      }\n    }\n    return 0;\n  }).map(function (record) {\n    var subRecords = record[childrenColumnName];\n    if (subRecords) {\n      return _extends(_extends({}, record), _defineProperty({}, childrenColumnName, getSortData(subRecords, sortStates, childrenColumnName)));\n    }\n    return record;\n  });\n}\nexport default function useFilterSorter(_ref5) {\n  var prefixCls = _ref5.prefixCls,\n    mergedColumns = _ref5.mergedColumns,\n    onSorterChange = _ref5.onSorterChange,\n    sortDirections = _ref5.sortDirections,\n    tableLocale = _ref5.tableLocale,\n    showSorterTooltip = _ref5.showSorterTooltip;\n  var _React$useState = React.useState(collectSortStates(mergedColumns, true)),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    sortStates = _React$useState2[0],\n    setSortStates = _React$useState2[1];\n  var mergedSorterStates = React.useMemo(function () {\n    var validate = true;\n    var collectedStates = collectSortStates(mergedColumns, false);\n    // Return if not controlled\n    if (!collectedStates.length) {\n      return sortStates;\n    }\n    var validateStates = [];\n    function patchStates(state) {\n      if (validate) {\n        validateStates.push(state);\n      } else {\n        validateStates.push(_extends(_extends({}, state), {\n          sortOrder: null\n        }));\n      }\n    }\n    var multipleMode = null;\n    collectedStates.forEach(function (state) {\n      if (multipleMode === null) {\n        patchStates(state);\n        if (state.sortOrder) {\n          if (state.multiplePriority === false) {\n            validate = false;\n          } else {\n            multipleMode = true;\n          }\n        }\n      } else if (multipleMode && state.multiplePriority !== false) {\n        patchStates(state);\n      } else {\n        validate = false;\n        patchStates(state);\n      }\n    });\n    return validateStates;\n  }, [mergedColumns, sortStates]);\n  // Get render columns title required props\n  var columnTitleSorterProps = React.useMemo(function () {\n    var sortColumns = mergedSorterStates.map(function (_ref6) {\n      var column = _ref6.column,\n        sortOrder = _ref6.sortOrder;\n      return {\n        column: column,\n        order: sortOrder\n      };\n    });\n    return {\n      sortColumns: sortColumns,\n      // Legacy\n      sortColumn: sortColumns[0] && sortColumns[0].column,\n      sortOrder: sortColumns[0] && sortColumns[0].order\n    };\n  }, [mergedSorterStates]);\n  function triggerSorter(sortState) {\n    var newSorterStates;\n    if (sortState.multiplePriority === false || !mergedSorterStates.length || mergedSorterStates[0].multiplePriority === false) {\n      newSorterStates = [sortState];\n    } else {\n      newSorterStates = [].concat(_toConsumableArray(mergedSorterStates.filter(function (_ref7) {\n        var key = _ref7.key;\n        return key !== sortState.key;\n      })), [sortState]);\n    }\n    setSortStates(newSorterStates);\n    onSorterChange(generateSorterInfo(newSorterStates), newSorterStates);\n  }\n  var transformColumns = function transformColumns(innerColumns) {\n    return injectSorter(prefixCls, innerColumns, mergedSorterStates, triggerSorter, sortDirections, tableLocale, showSorterTooltip);\n  };\n  var getSorters = function getSorters() {\n    return generateSorterInfo(mergedSorterStates);\n  };\n  return [transformColumns, mergedSorterStates, columnTitleSorterProps, getSorters];\n}", "map": {"version": 3, "names": ["_slicedToArray", "_defineProperty", "_extends", "_toConsumableArray", "_typeof", "CaretDownOutlined", "CaretUpOutlined", "classNames", "KeyCode", "React", "<PERSON><PERSON><PERSON>", "getColumnKey", "getColumnPos", "renderColumnTitle", "safeColumnTitle", "ASCEND", "DESCEND", "getMultiplePriority", "column", "sorter", "multiple", "getSortFunction", "compare", "nextSortDirection", "sortDirections", "current", "indexOf", "collectSortStates", "columns", "init", "pos", "sortStates", "pushState", "columnPos", "push", "key", "multiplePriority", "sortOrder", "for<PERSON>ach", "index", "children", "concat", "defaultSortOrder", "injectSorter", "prefixCls", "sorterStates", "triggerSorter", "defaultSortDirections", "tableLocale", "tableShowSorterTooltip", "map", "newColumn", "showSorterTooltip", "undefined", "column<PERSON>ey", "sorterState", "find", "_ref", "sorterOrder", "nextSortOrder", "upNode", "includes", "createElement", "className", "active", "role", "downNode", "_ref2", "cancelSort", "triggerAsc", "triggerDesc", "sortTip", "tooltipProps", "title", "renderProps", "renderSortTitle", "onHeaderCell", "col", "cell", "originOnClick", "onClick", "originOKeyDown", "onKeyDown", "event", "keyCode", "ENTER", "renderTitle", "displayTitle", "toString", "tabIndex", "ellipsis", "stateToInfo", "order", "field", "dataIndex", "generateSorterInfo", "list", "filter", "_ref3", "length", "getSortData", "data", "childrenColumnName", "innerSorterStates", "slice", "sort", "a", "b", "cloneData", "running<PERSON><PERSON><PERSON>", "_ref4", "record1", "record2", "i", "compareFn", "compareResult", "record", "subRecords", "use<PERSON>ilter<PERSON><PERSON>er", "_ref5", "mergedColumns", "onSorterChange", "_React$useState", "useState", "_React$useState2", "setSortStates", "mergedSorterStates", "useMemo", "validate", "collectedStates", "validateStates", "patchStates", "state", "multipleMode", "columnTitleSorterProps", "sortColumns", "_ref6", "sortColumn", "sortState", "newSorterStates", "_ref7", "transformColumns", "innerColumns", "getSorters"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/antd/es/table/hooks/useSorter.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport CaretDownOutlined from \"@ant-design/icons/es/icons/CaretDownOutlined\";\nimport CaretUpOutlined from \"@ant-design/icons/es/icons/CaretUpOutlined\";\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport Tooltip from '../../tooltip';\nimport { getColumnKey, getColumnPos, renderColumnTitle, safeColumnTitle } from '../util';\nvar ASCEND = 'ascend';\nvar DESCEND = 'descend';\nfunction getMultiplePriority(column) {\n  if (_typeof(column.sorter) === 'object' && typeof column.sorter.multiple === 'number') {\n    return column.sorter.multiple;\n  }\n  return false;\n}\nfunction getSortFunction(sorter) {\n  if (typeof sorter === 'function') {\n    return sorter;\n  }\n  if (sorter && _typeof(sorter) === 'object' && sorter.compare) {\n    return sorter.compare;\n  }\n  return false;\n}\nfunction nextSortDirection(sortDirections, current) {\n  if (!current) {\n    return sortDirections[0];\n  }\n  return sortDirections[sortDirections.indexOf(current) + 1];\n}\nfunction collectSortStates(columns, init, pos) {\n  var sortStates = [];\n  function pushState(column, columnPos) {\n    sortStates.push({\n      column: column,\n      key: getColumnKey(column, columnPos),\n      multiplePriority: getMultiplePriority(column),\n      sortOrder: column.sortOrder\n    });\n  }\n  (columns || []).forEach(function (column, index) {\n    var columnPos = getColumnPos(index, pos);\n    if (column.children) {\n      if ('sortOrder' in column) {\n        // Controlled\n        pushState(column, columnPos);\n      }\n      sortStates = [].concat(_toConsumableArray(sortStates), _toConsumableArray(collectSortStates(column.children, init, columnPos)));\n    } else if (column.sorter) {\n      if ('sortOrder' in column) {\n        // Controlled\n        pushState(column, columnPos);\n      } else if (init && column.defaultSortOrder) {\n        // Default sorter\n        sortStates.push({\n          column: column,\n          key: getColumnKey(column, columnPos),\n          multiplePriority: getMultiplePriority(column),\n          sortOrder: column.defaultSortOrder\n        });\n      }\n    }\n  });\n  return sortStates;\n}\nfunction injectSorter(prefixCls, columns, sorterStates, triggerSorter, defaultSortDirections, tableLocale, tableShowSorterTooltip, pos) {\n  return (columns || []).map(function (column, index) {\n    var columnPos = getColumnPos(index, pos);\n    var newColumn = column;\n    if (newColumn.sorter) {\n      var sortDirections = newColumn.sortDirections || defaultSortDirections;\n      var showSorterTooltip = newColumn.showSorterTooltip === undefined ? tableShowSorterTooltip : newColumn.showSorterTooltip;\n      var columnKey = getColumnKey(newColumn, columnPos);\n      var sorterState = sorterStates.find(function (_ref) {\n        var key = _ref.key;\n        return key === columnKey;\n      });\n      var sorterOrder = sorterState ? sorterState.sortOrder : null;\n      var nextSortOrder = nextSortDirection(sortDirections, sorterOrder);\n      var upNode = sortDirections.includes(ASCEND) && ( /*#__PURE__*/React.createElement(CaretUpOutlined, {\n        className: classNames(\"\".concat(prefixCls, \"-column-sorter-up\"), {\n          active: sorterOrder === ASCEND\n        }),\n        role: \"presentation\"\n      }));\n      var downNode = sortDirections.includes(DESCEND) && ( /*#__PURE__*/React.createElement(CaretDownOutlined, {\n        className: classNames(\"\".concat(prefixCls, \"-column-sorter-down\"), {\n          active: sorterOrder === DESCEND\n        }),\n        role: \"presentation\"\n      }));\n      var _ref2 = tableLocale || {},\n        cancelSort = _ref2.cancelSort,\n        triggerAsc = _ref2.triggerAsc,\n        triggerDesc = _ref2.triggerDesc;\n      var sortTip = cancelSort;\n      if (nextSortOrder === DESCEND) {\n        sortTip = triggerDesc;\n      } else if (nextSortOrder === ASCEND) {\n        sortTip = triggerAsc;\n      }\n      var tooltipProps = _typeof(showSorterTooltip) === 'object' ? showSorterTooltip : {\n        title: sortTip\n      };\n      newColumn = _extends(_extends({}, newColumn), {\n        className: classNames(newColumn.className, _defineProperty({}, \"\".concat(prefixCls, \"-column-sort\"), sorterOrder)),\n        title: function title(renderProps) {\n          var renderSortTitle = /*#__PURE__*/React.createElement(\"div\", {\n            className: \"\".concat(prefixCls, \"-column-sorters\")\n          }, /*#__PURE__*/React.createElement(\"span\", {\n            className: \"\".concat(prefixCls, \"-column-title\")\n          }, renderColumnTitle(column.title, renderProps)), /*#__PURE__*/React.createElement(\"span\", {\n            className: classNames(\"\".concat(prefixCls, \"-column-sorter\"), _defineProperty({}, \"\".concat(prefixCls, \"-column-sorter-full\"), !!(upNode && downNode)))\n          }, /*#__PURE__*/React.createElement(\"span\", {\n            className: \"\".concat(prefixCls, \"-column-sorter-inner\")\n          }, upNode, downNode)));\n          return showSorterTooltip ? ( /*#__PURE__*/React.createElement(Tooltip, _extends({}, tooltipProps), renderSortTitle)) : renderSortTitle;\n        },\n        onHeaderCell: function onHeaderCell(col) {\n          var cell = column.onHeaderCell && column.onHeaderCell(col) || {};\n          var originOnClick = cell.onClick;\n          var originOKeyDown = cell.onKeyDown;\n          cell.onClick = function (event) {\n            triggerSorter({\n              column: column,\n              key: columnKey,\n              sortOrder: nextSortOrder,\n              multiplePriority: getMultiplePriority(column)\n            });\n            originOnClick === null || originOnClick === void 0 ? void 0 : originOnClick(event);\n          };\n          cell.onKeyDown = function (event) {\n            if (event.keyCode === KeyCode.ENTER) {\n              triggerSorter({\n                column: column,\n                key: columnKey,\n                sortOrder: nextSortOrder,\n                multiplePriority: getMultiplePriority(column)\n              });\n              originOKeyDown === null || originOKeyDown === void 0 ? void 0 : originOKeyDown(event);\n            }\n          };\n          var renderTitle = safeColumnTitle(column.title, {});\n          var displayTitle = renderTitle === null || renderTitle === void 0 ? void 0 : renderTitle.toString();\n          // Inform the screen-reader so it can tell the visually impaired user which column is sorted\n          if (sorterOrder) {\n            cell['aria-sort'] = sorterOrder === 'ascend' ? 'ascending' : 'descending';\n          } else {\n            cell['aria-label'] = displayTitle || '';\n          }\n          cell.className = classNames(cell.className, \"\".concat(prefixCls, \"-column-has-sorters\"));\n          cell.tabIndex = 0;\n          if (column.ellipsis) {\n            cell.title = (renderTitle !== null && renderTitle !== void 0 ? renderTitle : '').toString();\n          }\n          return cell;\n        }\n      });\n    }\n    if ('children' in newColumn) {\n      newColumn = _extends(_extends({}, newColumn), {\n        children: injectSorter(prefixCls, newColumn.children, sorterStates, triggerSorter, defaultSortDirections, tableLocale, tableShowSorterTooltip, columnPos)\n      });\n    }\n    return newColumn;\n  });\n}\nfunction stateToInfo(sorterStates) {\n  var column = sorterStates.column,\n    sortOrder = sorterStates.sortOrder;\n  return {\n    column: column,\n    order: sortOrder,\n    field: column.dataIndex,\n    columnKey: column.key\n  };\n}\nfunction generateSorterInfo(sorterStates) {\n  var list = sorterStates.filter(function (_ref3) {\n    var sortOrder = _ref3.sortOrder;\n    return sortOrder;\n  }).map(stateToInfo);\n  // =========== Legacy compatible support ===========\n  // https://github.com/ant-design/ant-design/pull/19226\n  if (list.length === 0 && sorterStates.length) {\n    return _extends(_extends({}, stateToInfo(sorterStates[sorterStates.length - 1])), {\n      column: undefined\n    });\n  }\n  if (list.length <= 1) {\n    return list[0] || {};\n  }\n  return list;\n}\nexport function getSortData(data, sortStates, childrenColumnName) {\n  var innerSorterStates = sortStates.slice().sort(function (a, b) {\n    return b.multiplePriority - a.multiplePriority;\n  });\n  var cloneData = data.slice();\n  var runningSorters = innerSorterStates.filter(function (_ref4) {\n    var sorter = _ref4.column.sorter,\n      sortOrder = _ref4.sortOrder;\n    return getSortFunction(sorter) && sortOrder;\n  });\n  // Skip if no sorter needed\n  if (!runningSorters.length) {\n    return cloneData;\n  }\n  return cloneData.sort(function (record1, record2) {\n    for (var i = 0; i < runningSorters.length; i += 1) {\n      var sorterState = runningSorters[i];\n      var sorter = sorterState.column.sorter,\n        sortOrder = sorterState.sortOrder;\n      var compareFn = getSortFunction(sorter);\n      if (compareFn && sortOrder) {\n        var compareResult = compareFn(record1, record2, sortOrder);\n        if (compareResult !== 0) {\n          return sortOrder === ASCEND ? compareResult : -compareResult;\n        }\n      }\n    }\n    return 0;\n  }).map(function (record) {\n    var subRecords = record[childrenColumnName];\n    if (subRecords) {\n      return _extends(_extends({}, record), _defineProperty({}, childrenColumnName, getSortData(subRecords, sortStates, childrenColumnName)));\n    }\n    return record;\n  });\n}\nexport default function useFilterSorter(_ref5) {\n  var prefixCls = _ref5.prefixCls,\n    mergedColumns = _ref5.mergedColumns,\n    onSorterChange = _ref5.onSorterChange,\n    sortDirections = _ref5.sortDirections,\n    tableLocale = _ref5.tableLocale,\n    showSorterTooltip = _ref5.showSorterTooltip;\n  var _React$useState = React.useState(collectSortStates(mergedColumns, true)),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    sortStates = _React$useState2[0],\n    setSortStates = _React$useState2[1];\n  var mergedSorterStates = React.useMemo(function () {\n    var validate = true;\n    var collectedStates = collectSortStates(mergedColumns, false);\n    // Return if not controlled\n    if (!collectedStates.length) {\n      return sortStates;\n    }\n    var validateStates = [];\n    function patchStates(state) {\n      if (validate) {\n        validateStates.push(state);\n      } else {\n        validateStates.push(_extends(_extends({}, state), {\n          sortOrder: null\n        }));\n      }\n    }\n    var multipleMode = null;\n    collectedStates.forEach(function (state) {\n      if (multipleMode === null) {\n        patchStates(state);\n        if (state.sortOrder) {\n          if (state.multiplePriority === false) {\n            validate = false;\n          } else {\n            multipleMode = true;\n          }\n        }\n      } else if (multipleMode && state.multiplePriority !== false) {\n        patchStates(state);\n      } else {\n        validate = false;\n        patchStates(state);\n      }\n    });\n    return validateStates;\n  }, [mergedColumns, sortStates]);\n  // Get render columns title required props\n  var columnTitleSorterProps = React.useMemo(function () {\n    var sortColumns = mergedSorterStates.map(function (_ref6) {\n      var column = _ref6.column,\n        sortOrder = _ref6.sortOrder;\n      return {\n        column: column,\n        order: sortOrder\n      };\n    });\n    return {\n      sortColumns: sortColumns,\n      // Legacy\n      sortColumn: sortColumns[0] && sortColumns[0].column,\n      sortOrder: sortColumns[0] && sortColumns[0].order\n    };\n  }, [mergedSorterStates]);\n  function triggerSorter(sortState) {\n    var newSorterStates;\n    if (sortState.multiplePriority === false || !mergedSorterStates.length || mergedSorterStates[0].multiplePriority === false) {\n      newSorterStates = [sortState];\n    } else {\n      newSorterStates = [].concat(_toConsumableArray(mergedSorterStates.filter(function (_ref7) {\n        var key = _ref7.key;\n        return key !== sortState.key;\n      })), [sortState]);\n    }\n    setSortStates(newSorterStates);\n    onSorterChange(generateSorterInfo(newSorterStates), newSorterStates);\n  }\n  var transformColumns = function transformColumns(innerColumns) {\n    return injectSorter(prefixCls, innerColumns, mergedSorterStates, triggerSorter, sortDirections, tableLocale, showSorterTooltip);\n  };\n  var getSorters = function getSorters() {\n    return generateSorterInfo(mergedSorterStates);\n  };\n  return [transformColumns, mergedSorterStates, columnTitleSorterProps, getSorters];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,iBAAiB,MAAM,8CAA8C;AAC5E,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,YAAY,EAAEC,iBAAiB,EAAEC,eAAe,QAAQ,SAAS;AACxF,IAAIC,MAAM,GAAG,QAAQ;AACrB,IAAIC,OAAO,GAAG,SAAS;AACvB,SAASC,mBAAmBA,CAACC,MAAM,EAAE;EACnC,IAAId,OAAO,CAACc,MAAM,CAACC,MAAM,CAAC,KAAK,QAAQ,IAAI,OAAOD,MAAM,CAACC,MAAM,CAACC,QAAQ,KAAK,QAAQ,EAAE;IACrF,OAAOF,MAAM,CAACC,MAAM,CAACC,QAAQ;EAC/B;EACA,OAAO,KAAK;AACd;AACA,SAASC,eAAeA,CAACF,MAAM,EAAE;EAC/B,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM;EACf;EACA,IAAIA,MAAM,IAAIf,OAAO,CAACe,MAAM,CAAC,KAAK,QAAQ,IAAIA,MAAM,CAACG,OAAO,EAAE;IAC5D,OAAOH,MAAM,CAACG,OAAO;EACvB;EACA,OAAO,KAAK;AACd;AACA,SAASC,iBAAiBA,CAACC,cAAc,EAAEC,OAAO,EAAE;EAClD,IAAI,CAACA,OAAO,EAAE;IACZ,OAAOD,cAAc,CAAC,CAAC,CAAC;EAC1B;EACA,OAAOA,cAAc,CAACA,cAAc,CAACE,OAAO,CAACD,OAAO,CAAC,GAAG,CAAC,CAAC;AAC5D;AACA,SAASE,iBAAiBA,CAACC,OAAO,EAAEC,IAAI,EAAEC,GAAG,EAAE;EAC7C,IAAIC,UAAU,GAAG,EAAE;EACnB,SAASC,SAASA,CAACd,MAAM,EAAEe,SAAS,EAAE;IACpCF,UAAU,CAACG,IAAI,CAAC;MACdhB,MAAM,EAAEA,MAAM;MACdiB,GAAG,EAAExB,YAAY,CAACO,MAAM,EAAEe,SAAS,CAAC;MACpCG,gBAAgB,EAAEnB,mBAAmB,CAACC,MAAM,CAAC;MAC7CmB,SAAS,EAAEnB,MAAM,CAACmB;IACpB,CAAC,CAAC;EACJ;EACA,CAACT,OAAO,IAAI,EAAE,EAAEU,OAAO,CAAC,UAAUpB,MAAM,EAAEqB,KAAK,EAAE;IAC/C,IAAIN,SAAS,GAAGrB,YAAY,CAAC2B,KAAK,EAAET,GAAG,CAAC;IACxC,IAAIZ,MAAM,CAACsB,QAAQ,EAAE;MACnB,IAAI,WAAW,IAAItB,MAAM,EAAE;QACzB;QACAc,SAAS,CAACd,MAAM,EAAEe,SAAS,CAAC;MAC9B;MACAF,UAAU,GAAG,EAAE,CAACU,MAAM,CAACtC,kBAAkB,CAAC4B,UAAU,CAAC,EAAE5B,kBAAkB,CAACwB,iBAAiB,CAACT,MAAM,CAACsB,QAAQ,EAAEX,IAAI,EAAEI,SAAS,CAAC,CAAC,CAAC;IACjI,CAAC,MAAM,IAAIf,MAAM,CAACC,MAAM,EAAE;MACxB,IAAI,WAAW,IAAID,MAAM,EAAE;QACzB;QACAc,SAAS,CAACd,MAAM,EAAEe,SAAS,CAAC;MAC9B,CAAC,MAAM,IAAIJ,IAAI,IAAIX,MAAM,CAACwB,gBAAgB,EAAE;QAC1C;QACAX,UAAU,CAACG,IAAI,CAAC;UACdhB,MAAM,EAAEA,MAAM;UACdiB,GAAG,EAAExB,YAAY,CAACO,MAAM,EAAEe,SAAS,CAAC;UACpCG,gBAAgB,EAAEnB,mBAAmB,CAACC,MAAM,CAAC;UAC7CmB,SAAS,EAAEnB,MAAM,CAACwB;QACpB,CAAC,CAAC;MACJ;IACF;EACF,CAAC,CAAC;EACF,OAAOX,UAAU;AACnB;AACA,SAASY,YAAYA,CAACC,SAAS,EAAEhB,OAAO,EAAEiB,YAAY,EAAEC,aAAa,EAAEC,qBAAqB,EAAEC,WAAW,EAAEC,sBAAsB,EAAEnB,GAAG,EAAE;EACtI,OAAO,CAACF,OAAO,IAAI,EAAE,EAAEsB,GAAG,CAAC,UAAUhC,MAAM,EAAEqB,KAAK,EAAE;IAClD,IAAIN,SAAS,GAAGrB,YAAY,CAAC2B,KAAK,EAAET,GAAG,CAAC;IACxC,IAAIqB,SAAS,GAAGjC,MAAM;IACtB,IAAIiC,SAAS,CAAChC,MAAM,EAAE;MACpB,IAAIK,cAAc,GAAG2B,SAAS,CAAC3B,cAAc,IAAIuB,qBAAqB;MACtE,IAAIK,iBAAiB,GAAGD,SAAS,CAACC,iBAAiB,KAAKC,SAAS,GAAGJ,sBAAsB,GAAGE,SAAS,CAACC,iBAAiB;MACxH,IAAIE,SAAS,GAAG3C,YAAY,CAACwC,SAAS,EAAElB,SAAS,CAAC;MAClD,IAAIsB,WAAW,GAAGV,YAAY,CAACW,IAAI,CAAC,UAAUC,IAAI,EAAE;QAClD,IAAItB,GAAG,GAAGsB,IAAI,CAACtB,GAAG;QAClB,OAAOA,GAAG,KAAKmB,SAAS;MAC1B,CAAC,CAAC;MACF,IAAII,WAAW,GAAGH,WAAW,GAAGA,WAAW,CAAClB,SAAS,GAAG,IAAI;MAC5D,IAAIsB,aAAa,GAAGpC,iBAAiB,CAACC,cAAc,EAAEkC,WAAW,CAAC;MAClE,IAAIE,MAAM,GAAGpC,cAAc,CAACqC,QAAQ,CAAC9C,MAAM,CAAC,KAAM,aAAaN,KAAK,CAACqD,aAAa,CAACxD,eAAe,EAAE;QAClGyD,SAAS,EAAExD,UAAU,CAAC,EAAE,CAACkC,MAAM,CAACG,SAAS,EAAE,mBAAmB,CAAC,EAAE;UAC/DoB,MAAM,EAAEN,WAAW,KAAK3C;QAC1B,CAAC,CAAC;QACFkD,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;MACH,IAAIC,QAAQ,GAAG1C,cAAc,CAACqC,QAAQ,CAAC7C,OAAO,CAAC,KAAM,aAAaP,KAAK,CAACqD,aAAa,CAACzD,iBAAiB,EAAE;QACvG0D,SAAS,EAAExD,UAAU,CAAC,EAAE,CAACkC,MAAM,CAACG,SAAS,EAAE,qBAAqB,CAAC,EAAE;UACjEoB,MAAM,EAAEN,WAAW,KAAK1C;QAC1B,CAAC,CAAC;QACFiD,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;MACH,IAAIE,KAAK,GAAGnB,WAAW,IAAI,CAAC,CAAC;QAC3BoB,UAAU,GAAGD,KAAK,CAACC,UAAU;QAC7BC,UAAU,GAAGF,KAAK,CAACE,UAAU;QAC7BC,WAAW,GAAGH,KAAK,CAACG,WAAW;MACjC,IAAIC,OAAO,GAAGH,UAAU;MACxB,IAAIT,aAAa,KAAK3C,OAAO,EAAE;QAC7BuD,OAAO,GAAGD,WAAW;MACvB,CAAC,MAAM,IAAIX,aAAa,KAAK5C,MAAM,EAAE;QACnCwD,OAAO,GAAGF,UAAU;MACtB;MACA,IAAIG,YAAY,GAAGpE,OAAO,CAACgD,iBAAiB,CAAC,KAAK,QAAQ,GAAGA,iBAAiB,GAAG;QAC/EqB,KAAK,EAAEF;MACT,CAAC;MACDpB,SAAS,GAAGjD,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEiD,SAAS,CAAC,EAAE;QAC5CY,SAAS,EAAExD,UAAU,CAAC4C,SAAS,CAACY,SAAS,EAAE9D,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACwC,MAAM,CAACG,SAAS,EAAE,cAAc,CAAC,EAAEc,WAAW,CAAC,CAAC;QAClHe,KAAK,EAAE,SAASA,KAAKA,CAACC,WAAW,EAAE;UACjC,IAAIC,eAAe,GAAG,aAAalE,KAAK,CAACqD,aAAa,CAAC,KAAK,EAAE;YAC5DC,SAAS,EAAE,EAAE,CAACtB,MAAM,CAACG,SAAS,EAAE,iBAAiB;UACnD,CAAC,EAAE,aAAanC,KAAK,CAACqD,aAAa,CAAC,MAAM,EAAE;YAC1CC,SAAS,EAAE,EAAE,CAACtB,MAAM,CAACG,SAAS,EAAE,eAAe;UACjD,CAAC,EAAE/B,iBAAiB,CAACK,MAAM,CAACuD,KAAK,EAAEC,WAAW,CAAC,CAAC,EAAE,aAAajE,KAAK,CAACqD,aAAa,CAAC,MAAM,EAAE;YACzFC,SAAS,EAAExD,UAAU,CAAC,EAAE,CAACkC,MAAM,CAACG,SAAS,EAAE,gBAAgB,CAAC,EAAE3C,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACwC,MAAM,CAACG,SAAS,EAAE,qBAAqB,CAAC,EAAE,CAAC,EAAEgB,MAAM,IAAIM,QAAQ,CAAC,CAAC;UACxJ,CAAC,EAAE,aAAazD,KAAK,CAACqD,aAAa,CAAC,MAAM,EAAE;YAC1CC,SAAS,EAAE,EAAE,CAACtB,MAAM,CAACG,SAAS,EAAE,sBAAsB;UACxD,CAAC,EAAEgB,MAAM,EAAEM,QAAQ,CAAC,CAAC,CAAC;UACtB,OAAOd,iBAAiB,IAAK,aAAa3C,KAAK,CAACqD,aAAa,CAACpD,OAAO,EAAER,QAAQ,CAAC,CAAC,CAAC,EAAEsE,YAAY,CAAC,EAAEG,eAAe,CAAC,IAAIA,eAAe;QACxI,CAAC;QACDC,YAAY,EAAE,SAASA,YAAYA,CAACC,GAAG,EAAE;UACvC,IAAIC,IAAI,GAAG5D,MAAM,CAAC0D,YAAY,IAAI1D,MAAM,CAAC0D,YAAY,CAACC,GAAG,CAAC,IAAI,CAAC,CAAC;UAChE,IAAIE,aAAa,GAAGD,IAAI,CAACE,OAAO;UAChC,IAAIC,cAAc,GAAGH,IAAI,CAACI,SAAS;UACnCJ,IAAI,CAACE,OAAO,GAAG,UAAUG,KAAK,EAAE;YAC9BrC,aAAa,CAAC;cACZ5B,MAAM,EAAEA,MAAM;cACdiB,GAAG,EAAEmB,SAAS;cACdjB,SAAS,EAAEsB,aAAa;cACxBvB,gBAAgB,EAAEnB,mBAAmB,CAACC,MAAM;YAC9C,CAAC,CAAC;YACF6D,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACI,KAAK,CAAC;UACpF,CAAC;UACDL,IAAI,CAACI,SAAS,GAAG,UAAUC,KAAK,EAAE;YAChC,IAAIA,KAAK,CAACC,OAAO,KAAK5E,OAAO,CAAC6E,KAAK,EAAE;cACnCvC,aAAa,CAAC;gBACZ5B,MAAM,EAAEA,MAAM;gBACdiB,GAAG,EAAEmB,SAAS;gBACdjB,SAAS,EAAEsB,aAAa;gBACxBvB,gBAAgB,EAAEnB,mBAAmB,CAACC,MAAM;cAC9C,CAAC,CAAC;cACF+D,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACE,KAAK,CAAC;YACvF;UACF,CAAC;UACD,IAAIG,WAAW,GAAGxE,eAAe,CAACI,MAAM,CAACuD,KAAK,EAAE,CAAC,CAAC,CAAC;UACnD,IAAIc,YAAY,GAAGD,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACE,QAAQ,CAAC,CAAC;UACnG;UACA,IAAI9B,WAAW,EAAE;YACfoB,IAAI,CAAC,WAAW,CAAC,GAAGpB,WAAW,KAAK,QAAQ,GAAG,WAAW,GAAG,YAAY;UAC3E,CAAC,MAAM;YACLoB,IAAI,CAAC,YAAY,CAAC,GAAGS,YAAY,IAAI,EAAE;UACzC;UACAT,IAAI,CAACf,SAAS,GAAGxD,UAAU,CAACuE,IAAI,CAACf,SAAS,EAAE,EAAE,CAACtB,MAAM,CAACG,SAAS,EAAE,qBAAqB,CAAC,CAAC;UACxFkC,IAAI,CAACW,QAAQ,GAAG,CAAC;UACjB,IAAIvE,MAAM,CAACwE,QAAQ,EAAE;YACnBZ,IAAI,CAACL,KAAK,GAAG,CAACa,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAG,EAAE,EAAEE,QAAQ,CAAC,CAAC;UAC7F;UACA,OAAOV,IAAI;QACb;MACF,CAAC,CAAC;IACJ;IACA,IAAI,UAAU,IAAI3B,SAAS,EAAE;MAC3BA,SAAS,GAAGjD,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEiD,SAAS,CAAC,EAAE;QAC5CX,QAAQ,EAAEG,YAAY,CAACC,SAAS,EAAEO,SAAS,CAACX,QAAQ,EAAEK,YAAY,EAAEC,aAAa,EAAEC,qBAAqB,EAAEC,WAAW,EAAEC,sBAAsB,EAAEhB,SAAS;MAC1J,CAAC,CAAC;IACJ;IACA,OAAOkB,SAAS;EAClB,CAAC,CAAC;AACJ;AACA,SAASwC,WAAWA,CAAC9C,YAAY,EAAE;EACjC,IAAI3B,MAAM,GAAG2B,YAAY,CAAC3B,MAAM;IAC9BmB,SAAS,GAAGQ,YAAY,CAACR,SAAS;EACpC,OAAO;IACLnB,MAAM,EAAEA,MAAM;IACd0E,KAAK,EAAEvD,SAAS;IAChBwD,KAAK,EAAE3E,MAAM,CAAC4E,SAAS;IACvBxC,SAAS,EAAEpC,MAAM,CAACiB;EACpB,CAAC;AACH;AACA,SAAS4D,kBAAkBA,CAAClD,YAAY,EAAE;EACxC,IAAImD,IAAI,GAAGnD,YAAY,CAACoD,MAAM,CAAC,UAAUC,KAAK,EAAE;IAC9C,IAAI7D,SAAS,GAAG6D,KAAK,CAAC7D,SAAS;IAC/B,OAAOA,SAAS;EAClB,CAAC,CAAC,CAACa,GAAG,CAACyC,WAAW,CAAC;EACnB;EACA;EACA,IAAIK,IAAI,CAACG,MAAM,KAAK,CAAC,IAAItD,YAAY,CAACsD,MAAM,EAAE;IAC5C,OAAOjG,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEyF,WAAW,CAAC9C,YAAY,CAACA,YAAY,CAACsD,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;MAChFjF,MAAM,EAAEmC;IACV,CAAC,CAAC;EACJ;EACA,IAAI2C,IAAI,CAACG,MAAM,IAAI,CAAC,EAAE;IACpB,OAAOH,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;EACtB;EACA,OAAOA,IAAI;AACb;AACA,OAAO,SAASI,WAAWA,CAACC,IAAI,EAAEtE,UAAU,EAAEuE,kBAAkB,EAAE;EAChE,IAAIC,iBAAiB,GAAGxE,UAAU,CAACyE,KAAK,CAAC,CAAC,CAACC,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IAC9D,OAAOA,CAAC,CAACvE,gBAAgB,GAAGsE,CAAC,CAACtE,gBAAgB;EAChD,CAAC,CAAC;EACF,IAAIwE,SAAS,GAAGP,IAAI,CAACG,KAAK,CAAC,CAAC;EAC5B,IAAIK,cAAc,GAAGN,iBAAiB,CAACN,MAAM,CAAC,UAAUa,KAAK,EAAE;IAC7D,IAAI3F,MAAM,GAAG2F,KAAK,CAAC5F,MAAM,CAACC,MAAM;MAC9BkB,SAAS,GAAGyE,KAAK,CAACzE,SAAS;IAC7B,OAAOhB,eAAe,CAACF,MAAM,CAAC,IAAIkB,SAAS;EAC7C,CAAC,CAAC;EACF;EACA,IAAI,CAACwE,cAAc,CAACV,MAAM,EAAE;IAC1B,OAAOS,SAAS;EAClB;EACA,OAAOA,SAAS,CAACH,IAAI,CAAC,UAAUM,OAAO,EAAEC,OAAO,EAAE;IAChD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,cAAc,CAACV,MAAM,EAAEc,CAAC,IAAI,CAAC,EAAE;MACjD,IAAI1D,WAAW,GAAGsD,cAAc,CAACI,CAAC,CAAC;MACnC,IAAI9F,MAAM,GAAGoC,WAAW,CAACrC,MAAM,CAACC,MAAM;QACpCkB,SAAS,GAAGkB,WAAW,CAAClB,SAAS;MACnC,IAAI6E,SAAS,GAAG7F,eAAe,CAACF,MAAM,CAAC;MACvC,IAAI+F,SAAS,IAAI7E,SAAS,EAAE;QAC1B,IAAI8E,aAAa,GAAGD,SAAS,CAACH,OAAO,EAAEC,OAAO,EAAE3E,SAAS,CAAC;QAC1D,IAAI8E,aAAa,KAAK,CAAC,EAAE;UACvB,OAAO9E,SAAS,KAAKtB,MAAM,GAAGoG,aAAa,GAAG,CAACA,aAAa;QAC9D;MACF;IACF;IACA,OAAO,CAAC;EACV,CAAC,CAAC,CAACjE,GAAG,CAAC,UAAUkE,MAAM,EAAE;IACvB,IAAIC,UAAU,GAAGD,MAAM,CAACd,kBAAkB,CAAC;IAC3C,IAAIe,UAAU,EAAE;MACd,OAAOnH,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEkH,MAAM,CAAC,EAAEnH,eAAe,CAAC,CAAC,CAAC,EAAEqG,kBAAkB,EAAEF,WAAW,CAACiB,UAAU,EAAEtF,UAAU,EAAEuE,kBAAkB,CAAC,CAAC,CAAC;IACzI;IACA,OAAOc,MAAM;EACf,CAAC,CAAC;AACJ;AACA,eAAe,SAASE,eAAeA,CAACC,KAAK,EAAE;EAC7C,IAAI3E,SAAS,GAAG2E,KAAK,CAAC3E,SAAS;IAC7B4E,aAAa,GAAGD,KAAK,CAACC,aAAa;IACnCC,cAAc,GAAGF,KAAK,CAACE,cAAc;IACrCjG,cAAc,GAAG+F,KAAK,CAAC/F,cAAc;IACrCwB,WAAW,GAAGuE,KAAK,CAACvE,WAAW;IAC/BI,iBAAiB,GAAGmE,KAAK,CAACnE,iBAAiB;EAC7C,IAAIsE,eAAe,GAAGjH,KAAK,CAACkH,QAAQ,CAAChG,iBAAiB,CAAC6F,aAAa,EAAE,IAAI,CAAC,CAAC;IAC1EI,gBAAgB,GAAG5H,cAAc,CAAC0H,eAAe,EAAE,CAAC,CAAC;IACrD3F,UAAU,GAAG6F,gBAAgB,CAAC,CAAC,CAAC;IAChCC,aAAa,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EACrC,IAAIE,kBAAkB,GAAGrH,KAAK,CAACsH,OAAO,CAAC,YAAY;IACjD,IAAIC,QAAQ,GAAG,IAAI;IACnB,IAAIC,eAAe,GAAGtG,iBAAiB,CAAC6F,aAAa,EAAE,KAAK,CAAC;IAC7D;IACA,IAAI,CAACS,eAAe,CAAC9B,MAAM,EAAE;MAC3B,OAAOpE,UAAU;IACnB;IACA,IAAImG,cAAc,GAAG,EAAE;IACvB,SAASC,WAAWA,CAACC,KAAK,EAAE;MAC1B,IAAIJ,QAAQ,EAAE;QACZE,cAAc,CAAChG,IAAI,CAACkG,KAAK,CAAC;MAC5B,CAAC,MAAM;QACLF,cAAc,CAAChG,IAAI,CAAChC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEkI,KAAK,CAAC,EAAE;UAChD/F,SAAS,EAAE;QACb,CAAC,CAAC,CAAC;MACL;IACF;IACA,IAAIgG,YAAY,GAAG,IAAI;IACvBJ,eAAe,CAAC3F,OAAO,CAAC,UAAU8F,KAAK,EAAE;MACvC,IAAIC,YAAY,KAAK,IAAI,EAAE;QACzBF,WAAW,CAACC,KAAK,CAAC;QAClB,IAAIA,KAAK,CAAC/F,SAAS,EAAE;UACnB,IAAI+F,KAAK,CAAChG,gBAAgB,KAAK,KAAK,EAAE;YACpC4F,QAAQ,GAAG,KAAK;UAClB,CAAC,MAAM;YACLK,YAAY,GAAG,IAAI;UACrB;QACF;MACF,CAAC,MAAM,IAAIA,YAAY,IAAID,KAAK,CAAChG,gBAAgB,KAAK,KAAK,EAAE;QAC3D+F,WAAW,CAACC,KAAK,CAAC;MACpB,CAAC,MAAM;QACLJ,QAAQ,GAAG,KAAK;QAChBG,WAAW,CAACC,KAAK,CAAC;MACpB;IACF,CAAC,CAAC;IACF,OAAOF,cAAc;EACvB,CAAC,EAAE,CAACV,aAAa,EAAEzF,UAAU,CAAC,CAAC;EAC/B;EACA,IAAIuG,sBAAsB,GAAG7H,KAAK,CAACsH,OAAO,CAAC,YAAY;IACrD,IAAIQ,WAAW,GAAGT,kBAAkB,CAAC5E,GAAG,CAAC,UAAUsF,KAAK,EAAE;MACxD,IAAItH,MAAM,GAAGsH,KAAK,CAACtH,MAAM;QACvBmB,SAAS,GAAGmG,KAAK,CAACnG,SAAS;MAC7B,OAAO;QACLnB,MAAM,EAAEA,MAAM;QACd0E,KAAK,EAAEvD;MACT,CAAC;IACH,CAAC,CAAC;IACF,OAAO;MACLkG,WAAW,EAAEA,WAAW;MACxB;MACAE,UAAU,EAAEF,WAAW,CAAC,CAAC,CAAC,IAAIA,WAAW,CAAC,CAAC,CAAC,CAACrH,MAAM;MACnDmB,SAAS,EAAEkG,WAAW,CAAC,CAAC,CAAC,IAAIA,WAAW,CAAC,CAAC,CAAC,CAAC3C;IAC9C,CAAC;EACH,CAAC,EAAE,CAACkC,kBAAkB,CAAC,CAAC;EACxB,SAAShF,aAAaA,CAAC4F,SAAS,EAAE;IAChC,IAAIC,eAAe;IACnB,IAAID,SAAS,CAACtG,gBAAgB,KAAK,KAAK,IAAI,CAAC0F,kBAAkB,CAAC3B,MAAM,IAAI2B,kBAAkB,CAAC,CAAC,CAAC,CAAC1F,gBAAgB,KAAK,KAAK,EAAE;MAC1HuG,eAAe,GAAG,CAACD,SAAS,CAAC;IAC/B,CAAC,MAAM;MACLC,eAAe,GAAG,EAAE,CAAClG,MAAM,CAACtC,kBAAkB,CAAC2H,kBAAkB,CAAC7B,MAAM,CAAC,UAAU2C,KAAK,EAAE;QACxF,IAAIzG,GAAG,GAAGyG,KAAK,CAACzG,GAAG;QACnB,OAAOA,GAAG,KAAKuG,SAAS,CAACvG,GAAG;MAC9B,CAAC,CAAC,CAAC,EAAE,CAACuG,SAAS,CAAC,CAAC;IACnB;IACAb,aAAa,CAACc,eAAe,CAAC;IAC9BlB,cAAc,CAAC1B,kBAAkB,CAAC4C,eAAe,CAAC,EAAEA,eAAe,CAAC;EACtE;EACA,IAAIE,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,YAAY,EAAE;IAC7D,OAAOnG,YAAY,CAACC,SAAS,EAAEkG,YAAY,EAAEhB,kBAAkB,EAAEhF,aAAa,EAAEtB,cAAc,EAAEwB,WAAW,EAAEI,iBAAiB,CAAC;EACjI,CAAC;EACD,IAAI2F,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrC,OAAOhD,kBAAkB,CAAC+B,kBAAkB,CAAC;EAC/C,CAAC;EACD,OAAO,CAACe,gBAAgB,EAAEf,kBAAkB,EAAEQ,sBAAsB,EAAES,UAAU,CAAC;AACnF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}