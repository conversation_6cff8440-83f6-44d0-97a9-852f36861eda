{"ast": null, "code": "import { DEFAULT_LOCALE } from '../common/constants';\nimport isNumber from '../common/is-number';\nimport datePattern from './date-pattern';\nimport dateNameType from './date-name-type';\nimport { dateFormatRegExp, DATE_FIELD_MAP } from './constants';\nimport { localeInfo } from '../cldr';\nvar NAME_TYPES = {\n  month: {\n    type: 'months',\n    minLength: 3,\n    standAlone: 'L'\n  },\n  quarter: {\n    type: 'quarters',\n    minLength: 3,\n    standAlone: 'q'\n  },\n  weekday: {\n    type: 'days',\n    minLength: {\n      E: 0,\n      c: 3,\n      e: 3\n    },\n    standAlone: 'c'\n  },\n  dayperiod: {\n    type: 'dayPeriods',\n    minLength: 0\n  },\n  era: {\n    type: 'eras',\n    minLength: 0\n  }\n};\nvar LITERAL = 'literal';\nfunction addLiteral(parts, value) {\n  var lastPart = parts[parts.length - 1];\n  if (lastPart && lastPart.type === LITERAL) {\n    lastPart.pattern += value;\n  } else {\n    parts.push({\n      type: LITERAL,\n      pattern: value\n    });\n  }\n}\nfunction isHour12(pattern) {\n  return pattern === 'h' || pattern === 'K';\n}\nexport default function splitDateFormat(format, locale) {\n  if (locale === void 0) locale = DEFAULT_LOCALE;\n  var info = localeInfo(locale);\n  var pattern = datePattern(format, info);\n  var parts = [];\n  var lastIndex = dateFormatRegExp.lastIndex = 0;\n  var match = dateFormatRegExp.exec(pattern);\n  while (match) {\n    var value = match[0];\n    if (lastIndex < match.index) {\n      addLiteral(parts, pattern.substring(lastIndex, match.index));\n    }\n    if (value.startsWith('\"') || value.startsWith(\"'\")) {\n      addLiteral(parts, value);\n    } else {\n      var specifier = value[0];\n      var type = DATE_FIELD_MAP[specifier];\n      var part = {\n        type: type,\n        pattern: value\n      };\n      if (type === 'hour') {\n        part.hour12 = isHour12(value);\n      }\n      var names = NAME_TYPES[type];\n      if (names) {\n        var minLength = isNumber(names.minLength) ? names.minLength : names.minLength[specifier];\n        var patternLength = value.length;\n        if (patternLength >= minLength) {\n          part.names = {\n            type: names.type,\n            nameType: dateNameType(patternLength),\n            standAlone: names.standAlone === specifier\n          };\n        }\n      }\n      parts.push(part);\n    }\n    lastIndex = dateFormatRegExp.lastIndex;\n    match = dateFormatRegExp.exec(pattern);\n  }\n  if (lastIndex < pattern.length) {\n    addLiteral(parts, pattern.substring(lastIndex));\n  }\n  return parts;\n}", "map": {"version": 3, "names": ["DEFAULT_LOCALE", "isNumber", "datePattern", "dateNameType", "dateFormatRegExp", "DATE_FIELD_MAP", "localeInfo", "NAME_TYPES", "month", "type", "<PERSON><PERSON><PERSON><PERSON>", "standAlone", "quarter", "weekday", "E", "c", "e", "dayperiod", "era", "LITERAL", "addLiteral", "parts", "value", "lastPart", "length", "pattern", "push", "isHour12", "splitDateFormat", "format", "locale", "info", "lastIndex", "match", "exec", "index", "substring", "startsWith", "specifier", "part", "hour12", "names", "<PERSON><PERSON><PERSON><PERSON>", "nameType"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-intl/dist/es/dates/split-date-format.js"], "sourcesContent": ["import { DEFAULT_LOCALE } from '../common/constants';\nimport isNumber from '../common/is-number';\nimport datePattern from './date-pattern';\nimport dateNameType from './date-name-type';\nimport { dateFormatRegExp, DATE_FIELD_MAP } from './constants';\nimport { localeInfo } from '../cldr';\n\nvar NAME_TYPES = {\n    month: {\n        type: 'months',\n        minLength: 3,\n        standAlone: 'L'\n    },\n\n    quarter: {\n        type: 'quarters',\n        minLength: 3,\n        standAlone: 'q'\n    },\n\n    weekday: {\n        type: 'days',\n        minLength: {\n            E: 0,\n            c: 3,\n            e: 3\n        },\n        standAlone: 'c'\n    },\n\n    dayperiod: {\n        type: 'dayPeriods',\n        minLength: 0\n    },\n\n    era: {\n        type: 'eras',\n        minLength: 0\n    }\n};\n\nvar LITERAL = 'literal';\n\nfunction addLiteral(parts, value) {\n    var lastPart = parts[parts.length - 1];\n    if (lastPart && lastPart.type === LITERAL) {\n        lastPart.pattern += value;\n    } else {\n        parts.push({\n            type: LITERAL,\n            pattern: value\n        });\n    }\n}\n\nfunction isHour12(pattern) {\n    return pattern === 'h' || pattern === 'K';\n}\n\nexport default function splitDateFormat(format, locale) {\n    if ( locale === void 0 ) locale = DEFAULT_LOCALE;\n\n    var info = localeInfo(locale);\n    var pattern = datePattern(format, info);\n    var parts = [];\n    var lastIndex = dateFormatRegExp.lastIndex = 0;\n    var match = dateFormatRegExp.exec(pattern);\n\n    while (match) {\n        var value = match[0];\n\n        if (lastIndex < match.index) {\n            addLiteral(parts, pattern.substring(lastIndex, match.index));\n        }\n\n        if (value.startsWith('\"') || value.startsWith(\"'\")) {\n            addLiteral(parts, value);\n        } else {\n            var specifier = value[0];\n            var type = DATE_FIELD_MAP[specifier];\n            var part = {\n                type: type,\n                pattern: value\n            };\n\n            if (type === 'hour') {\n                part.hour12 = isHour12(value);\n            }\n\n            var names = NAME_TYPES[type];\n\n            if (names) {\n                var minLength = isNumber(names.minLength) ? names.minLength : names.minLength[specifier];\n                var patternLength = value.length;\n\n                if (patternLength >= minLength) {\n                    part.names = {\n                        type: names.type,\n                        nameType: dateNameType(patternLength),\n                        standAlone: names.standAlone === specifier\n                    };\n                }\n            }\n\n            parts.push(part);\n        }\n\n        lastIndex = dateFormatRegExp.lastIndex;\n        match = dateFormatRegExp.exec(pattern);\n    }\n\n    if (lastIndex < pattern.length) {\n        addLiteral(parts, pattern.substring(lastIndex));\n    }\n\n    return parts;\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,qBAAqB;AACpD,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,YAAY,MAAM,kBAAkB;AAC3C,SAASC,gBAAgB,EAAEC,cAAc,QAAQ,aAAa;AAC9D,SAASC,UAAU,QAAQ,SAAS;AAEpC,IAAIC,UAAU,GAAG;EACbC,KAAK,EAAE;IACHC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,CAAC;IACZC,UAAU,EAAE;EAChB,CAAC;EAEDC,OAAO,EAAE;IACLH,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAE,CAAC;IACZC,UAAU,EAAE;EAChB,CAAC;EAEDE,OAAO,EAAE;IACLJ,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE;MACPI,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE;IACP,CAAC;IACDL,UAAU,EAAE;EAChB,CAAC;EAEDM,SAAS,EAAE;IACPR,IAAI,EAAE,YAAY;IAClBC,SAAS,EAAE;EACf,CAAC;EAEDQ,GAAG,EAAE;IACDT,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE;EACf;AACJ,CAAC;AAED,IAAIS,OAAO,GAAG,SAAS;AAEvB,SAASC,UAAUA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAC9B,IAAIC,QAAQ,GAAGF,KAAK,CAACA,KAAK,CAACG,MAAM,GAAG,CAAC,CAAC;EACtC,IAAID,QAAQ,IAAIA,QAAQ,CAACd,IAAI,KAAKU,OAAO,EAAE;IACvCI,QAAQ,CAACE,OAAO,IAAIH,KAAK;EAC7B,CAAC,MAAM;IACHD,KAAK,CAACK,IAAI,CAAC;MACPjB,IAAI,EAAEU,OAAO;MACbM,OAAO,EAAEH;IACb,CAAC,CAAC;EACN;AACJ;AAEA,SAASK,QAAQA,CAACF,OAAO,EAAE;EACvB,OAAOA,OAAO,KAAK,GAAG,IAAIA,OAAO,KAAK,GAAG;AAC7C;AAEA,eAAe,SAASG,eAAeA,CAACC,MAAM,EAAEC,MAAM,EAAE;EACpD,IAAKA,MAAM,KAAK,KAAK,CAAC,EAAGA,MAAM,GAAG9B,cAAc;EAEhD,IAAI+B,IAAI,GAAGzB,UAAU,CAACwB,MAAM,CAAC;EAC7B,IAAIL,OAAO,GAAGvB,WAAW,CAAC2B,MAAM,EAAEE,IAAI,CAAC;EACvC,IAAIV,KAAK,GAAG,EAAE;EACd,IAAIW,SAAS,GAAG5B,gBAAgB,CAAC4B,SAAS,GAAG,CAAC;EAC9C,IAAIC,KAAK,GAAG7B,gBAAgB,CAAC8B,IAAI,CAACT,OAAO,CAAC;EAE1C,OAAOQ,KAAK,EAAE;IACV,IAAIX,KAAK,GAAGW,KAAK,CAAC,CAAC,CAAC;IAEpB,IAAID,SAAS,GAAGC,KAAK,CAACE,KAAK,EAAE;MACzBf,UAAU,CAACC,KAAK,EAAEI,OAAO,CAACW,SAAS,CAACJ,SAAS,EAAEC,KAAK,CAACE,KAAK,CAAC,CAAC;IAChE;IAEA,IAAIb,KAAK,CAACe,UAAU,CAAC,GAAG,CAAC,IAAIf,KAAK,CAACe,UAAU,CAAC,GAAG,CAAC,EAAE;MAChDjB,UAAU,CAACC,KAAK,EAAEC,KAAK,CAAC;IAC5B,CAAC,MAAM;MACH,IAAIgB,SAAS,GAAGhB,KAAK,CAAC,CAAC,CAAC;MACxB,IAAIb,IAAI,GAAGJ,cAAc,CAACiC,SAAS,CAAC;MACpC,IAAIC,IAAI,GAAG;QACP9B,IAAI,EAAEA,IAAI;QACVgB,OAAO,EAAEH;MACb,CAAC;MAED,IAAIb,IAAI,KAAK,MAAM,EAAE;QACjB8B,IAAI,CAACC,MAAM,GAAGb,QAAQ,CAACL,KAAK,CAAC;MACjC;MAEA,IAAImB,KAAK,GAAGlC,UAAU,CAACE,IAAI,CAAC;MAE5B,IAAIgC,KAAK,EAAE;QACP,IAAI/B,SAAS,GAAGT,QAAQ,CAACwC,KAAK,CAAC/B,SAAS,CAAC,GAAG+B,KAAK,CAAC/B,SAAS,GAAG+B,KAAK,CAAC/B,SAAS,CAAC4B,SAAS,CAAC;QACxF,IAAII,aAAa,GAAGpB,KAAK,CAACE,MAAM;QAEhC,IAAIkB,aAAa,IAAIhC,SAAS,EAAE;UAC5B6B,IAAI,CAACE,KAAK,GAAG;YACThC,IAAI,EAAEgC,KAAK,CAAChC,IAAI;YAChBkC,QAAQ,EAAExC,YAAY,CAACuC,aAAa,CAAC;YACrC/B,UAAU,EAAE8B,KAAK,CAAC9B,UAAU,KAAK2B;UACrC,CAAC;QACL;MACJ;MAEAjB,KAAK,CAACK,IAAI,CAACa,IAAI,CAAC;IACpB;IAEAP,SAAS,GAAG5B,gBAAgB,CAAC4B,SAAS;IACtCC,KAAK,GAAG7B,gBAAgB,CAAC8B,IAAI,CAACT,OAAO,CAAC;EAC1C;EAEA,IAAIO,SAAS,GAAGP,OAAO,CAACD,MAAM,EAAE;IAC5BJ,UAAU,CAACC,KAAK,EAAEI,OAAO,CAACW,SAAS,CAACJ,SAAS,CAAC,CAAC;EACnD;EAEA,OAAOX,KAAK;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}