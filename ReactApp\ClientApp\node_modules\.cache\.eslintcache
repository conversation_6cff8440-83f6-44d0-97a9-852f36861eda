[{"D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\index.tsx": "1", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\serviceWorker.ts": "2", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\App.tsx": "3", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\store\\store.ts": "4", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\config\\index.ts": "5", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\appSlice.ts": "6", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\fileApiSlice.ts": "7", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\sitesApiSlice.ts": "8", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\userApiSlice.ts": "9", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\changeUrl.ts": "10", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\firmApiSlice.ts": "11", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\fileManagementApiSlice.ts": "12", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\oktaAuthClient\\OktaAuthClient.ts": "13", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\AppInitialization.tsx": "14", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\routes\\RouteSwitch.tsx": "15", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\uploaderSlice.ts": "16", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\routes\\index.ts": "17", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\Constants\\index.ts": "18", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\config\\endpoints.ts": "19", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\logger\\index.ts": "20", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\uploadService.ts": "21", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\interceptorsSlice.ts": "22", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\files\\formatDownloadFileName.ts": "23", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\http\\httpVerbs.ts": "24", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\hooks\\useAppSelector.ts": "25", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\oktaAuthClient\\PrivateRoute.tsx": "26", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\index.ts": "27", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\Login\\LoginCallbackPage.tsx": "28", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\antNotifications\\index.ts": "29", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\Login\\index.tsx": "30", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\Logout\\index.tsx": "31", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\TenentSelectionPage\\index.tsx": "32", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\PageNotFound\\index.tsx": "33", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\layouts\\MasterLayout\\index.tsx": "34", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\SubmittedFiles\\index.tsx": "35", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\PublishedFiles\\index.tsx": "36", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\http\\index.ts": "37", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\layouts\\CommonLayout\\index.tsx": "38", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\hooks\\useWindowDimensions.ts": "39", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\OktaError\\index.tsx": "40", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\DownloadModal\\types.ts": "41", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\SideMenu\\SideMenuContainer.tsx": "42", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileCard\\FileCard.tsx": "43", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\utils\\confirmDiscard.tsx": "44", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\index.tsx": "45", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\Header\\index.tsx": "46", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\PageTitle\\index.tsx": "47", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\Login\\index.tsx": "48", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\PageContent\\index.tsx": "49", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\DownloadModal\\index.tsx": "50", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\Logout\\index.tsx": "51", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\TenatSelection\\index.tsx": "52", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\Breadcrumbs\\index.tsx": "53", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\CustomModal\\index.tsx": "54", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\InfinityList\\index.tsx": "55", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\validators\\index.ts": "56", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\SiteSelection\\index.tsx": "57", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\SubmittedFiles\\FilterArea\\index.tsx": "58", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\FileDetailsModal\\index.tsx": "59", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\PublishedFiles\\FilterArea\\index.tsx": "60", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\hooks\\useLocalStorage.ts": "61", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\menus\\menuConfig.ts": "62", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\http\\statusCodes.ts": "63", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\SideMenu\\utils\\mapMenuConfig.ts": "64", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\constants\\errors.ts": "65", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\hooks\\useUploader.ts": "66", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\SideMenu\\index.tsx": "67", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\UploadProgress\\index.tsx": "68", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\DragAndDrop\\index.tsx": "69", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\hooks\\useFileList.ts": "70", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\forms\\UploaderSubmit\\UploaderSubmitContainer.tsx": "71", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\array\\index.ts": "72", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\regex\\index.ts": "73", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\InfinitySelect\\index.tsx": "74", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\files\\formatSize.ts": "75", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\UploadProgress\\ProgressingFile.tsx": "76", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\forms\\UploaderSubmit\\index.tsx": "77", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\forms\\UploaderSubmit\\FileList.tsx": "78", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\Search\\index.tsx": "79", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\tncApiSlice.ts": "80", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\mocks\\tnc.mock.ts": "81", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\TermsAndConditions\\index.tsx": "82", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\Dev\\KendoPlaygroundPage\\index.ts": "83", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\Dev\\KendoPlaygroundPage\\KendoPlaygroundPage.tsx": "84", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\TermsAndConditions\\index.tsx": "85", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\hooks\\useTermsAndConditions.ts": "86", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\Dev\\KendoPlaygroundPage\\sample-pdf.ts": "87", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\types\\tncTypes.ts": "88"}, {"size": 1046, "mtime": 1748943068564, "results": "89", "hashOfConfig": "90"}, {"size": 5315, "mtime": 1748258178144, "results": "91", "hashOfConfig": "92"}, {"size": 1597, "mtime": 1748942855355, "results": "93", "hashOfConfig": "92"}, {"size": 2962, "mtime": 1748943068518, "results": "94", "hashOfConfig": "92"}, {"size": 63, "mtime": 1748258178144, "results": "95", "hashOfConfig": "92"}, {"size": 5435, "mtime": 1749722750177, "results": "96", "hashOfConfig": "90"}, {"size": 8229, "mtime": 1748258178038, "results": "97", "hashOfConfig": "92"}, {"size": 981, "mtime": 1748258178038, "results": "98", "hashOfConfig": "92"}, {"size": 1033, "mtime": 1748258178038, "results": "99", "hashOfConfig": "92"}, {"size": 694, "mtime": 1748258178038, "results": "100", "hashOfConfig": "92"}, {"size": 761, "mtime": 1748258178038, "results": "101", "hashOfConfig": "92"}, {"size": 2369, "mtime": 1748258178038, "results": "102", "hashOfConfig": "92"}, {"size": 671, "mtime": 1748258178144, "results": "103", "hashOfConfig": "92"}, {"size": 4701, "mtime": 1749702989444, "results": "104", "hashOfConfig": "90"}, {"size": 1557, "mtime": 1748943068480, "results": "105", "hashOfConfig": "92"}, {"size": 943, "mtime": 1748258178069, "results": "106", "hashOfConfig": "92"}, {"size": 1233, "mtime": 1748943068515, "results": "107", "hashOfConfig": "92"}, {"size": 479, "mtime": 1748258178130, "results": "108", "hashOfConfig": "92"}, {"size": 2245, "mtime": 1749703790835, "results": "109", "hashOfConfig": "90"}, {"size": 2643, "mtime": 1748258178144, "results": "110", "hashOfConfig": "92"}, {"size": 1242, "mtime": 1748258178038, "results": "111", "hashOfConfig": "92"}, {"size": 803, "mtime": 1748258178038, "results": "112", "hashOfConfig": "92"}, {"size": 265, "mtime": 1748258178144, "results": "113", "hashOfConfig": "92"}, {"size": 158, "mtime": 1748258178144, "results": "114", "hashOfConfig": "92"}, {"size": 286, "mtime": 1748258178112, "results": "115", "hashOfConfig": "92"}, {"size": 2051, "mtime": 1749703790835, "results": "116", "hashOfConfig": "90"}, {"size": 2349, "mtime": 1748258178144, "results": "117", "hashOfConfig": "92"}, {"size": 639, "mtime": 1748258178112, "results": "118", "hashOfConfig": "92"}, {"size": 1647, "mtime": 1748258178130, "results": "119", "hashOfConfig": "92"}, {"size": 177, "mtime": 1748258178112, "results": "120", "hashOfConfig": "92"}, {"size": 270, "mtime": 1748258178112, "results": "121", "hashOfConfig": "92"}, {"size": 314, "mtime": 1748258178130, "results": "122", "hashOfConfig": "92"}, {"size": 936, "mtime": 1748258178112, "results": "123", "hashOfConfig": "92"}, {"size": 2781, "mtime": 1748258178112, "results": "124", "hashOfConfig": "92"}, {"size": 21733, "mtime": 1748258178130, "results": "125", "hashOfConfig": "92"}, {"size": 12623, "mtime": 1748353101192, "results": "126", "hashOfConfig": "92"}, {"size": 2697, "mtime": 1748258178144, "results": "127", "hashOfConfig": "92"}, {"size": 420, "mtime": 1748258178112, "results": "128", "hashOfConfig": "92"}, {"size": 577, "mtime": 1748258178112, "results": "129", "hashOfConfig": "92"}, {"size": 1787, "mtime": 1748258178081, "results": "130", "hashOfConfig": "92"}, {"size": 375, "mtime": 1748258178049, "results": "131", "hashOfConfig": "92"}, {"size": 765, "mtime": 1748258178096, "results": "132", "hashOfConfig": "92"}, {"size": 2189, "mtime": 1748258178049, "results": "133", "hashOfConfig": "92"}, {"size": 592, "mtime": 1748258178071, "results": "134", "hashOfConfig": "92"}, {"size": 7544, "mtime": 1748258178067, "results": "135", "hashOfConfig": "92"}, {"size": 3521, "mtime": 1748857140786, "results": "136", "hashOfConfig": "92"}, {"size": 508, "mtime": 1748258178096, "results": "137", "hashOfConfig": "92"}, {"size": 541, "mtime": 1748258178081, "results": "138", "hashOfConfig": "92"}, {"size": 465, "mtime": 1748258178081, "results": "139", "hashOfConfig": "92"}, {"size": 3806, "mtime": 1748258178049, "results": "140", "hashOfConfig": "92"}, {"size": 1009, "mtime": 1748258178081, "results": "141", "hashOfConfig": "92"}, {"size": 2673, "mtime": 1748258178096, "results": "142", "hashOfConfig": "92"}, {"size": 2771, "mtime": 1748258178049, "results": "143", "hashOfConfig": "92"}, {"size": 888, "mtime": 1748258178049, "results": "144", "hashOfConfig": "92"}, {"size": 16198, "mtime": 1748258178071, "results": "145", "hashOfConfig": "92"}, {"size": 785, "mtime": 1748258178071, "results": "146", "hashOfConfig": "92"}, {"size": 3061, "mtime": 1748258178096, "results": "147", "hashOfConfig": "92"}, {"size": 4066, "mtime": 1748258178130, "results": "148", "hashOfConfig": "92"}, {"size": 2611, "mtime": 1748258178049, "results": "149", "hashOfConfig": "92"}, {"size": 3498, "mtime": 1748330843125, "results": "150", "hashOfConfig": "92"}, {"size": 870, "mtime": 1748258178112, "results": "151", "hashOfConfig": "92"}, {"size": 1548, "mtime": 1748943068443, "results": "152", "hashOfConfig": "92"}, {"size": 36, "mtime": 1748258178144, "results": "153", "hashOfConfig": "92"}, {"size": 1127, "mtime": 1748943068389, "results": "154", "hashOfConfig": "92"}, {"size": 742, "mtime": 1748258178049, "results": "155", "hashOfConfig": "92"}, {"size": 5254, "mtime": 1748258178065, "results": "156", "hashOfConfig": "92"}, {"size": 6727, "mtime": 1748942855360, "results": "157", "hashOfConfig": "92"}, {"size": 2958, "mtime": 1748258178049, "results": "158", "hashOfConfig": "92"}, {"size": 2859, "mtime": 1748258178049, "results": "159", "hashOfConfig": "92"}, {"size": 2113, "mtime": 1748258178065, "results": "160", "hashOfConfig": "92"}, {"size": 2037, "mtime": 1748258178096, "results": "161", "hashOfConfig": "92"}, {"size": 657, "mtime": 1748258178144, "results": "162", "hashOfConfig": "92"}, {"size": 764, "mtime": 1748258178144, "results": "163", "hashOfConfig": "92"}, {"size": 7602, "mtime": 1748258178081, "results": "164", "hashOfConfig": "92"}, {"size": 590, "mtime": 1748258178144, "results": "165", "hashOfConfig": "92"}, {"size": 1481, "mtime": 1748258178049, "results": "166", "hashOfConfig": "92"}, {"size": 2970, "mtime": 1748258178112, "results": "167", "hashOfConfig": "92"}, {"size": 3476, "mtime": 1748258178096, "results": "168", "hashOfConfig": "92"}, {"size": 654, "mtime": 1748258178096, "results": "169", "hashOfConfig": "170"}, {"size": 1331, "mtime": 1749703790766, "results": "171", "hashOfConfig": "90"}, {"size": 902, "mtime": 1748943068446, "results": "172", "hashOfConfig": "92"}, {"size": 342, "mtime": 1748943068478, "results": "173", "hashOfConfig": "92"}, {"size": 95, "mtime": 1748943068450, "results": "174", "hashOfConfig": "92"}, {"size": 806, "mtime": 1748943068449, "results": "175", "hashOfConfig": "92"}, {"size": 4288, "mtime": 1749727049322, "results": "176", "hashOfConfig": "90"}, {"size": 2182, "mtime": 1749722630145, "results": "177", "hashOfConfig": "90"}, {"size": 526788, "mtime": 1748943068472, "results": "178", "hashOfConfig": "92"}, {"size": 934, "mtime": 1749703790830, "results": "179", "hashOfConfig": "90"}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "o45rsp", {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "pkc4kd", {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "mels26", {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\index.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\serviceWorker.ts", ["444", "445"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\App.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\store\\store.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\config\\index.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\appSlice.ts", ["446"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\fileApiSlice.ts", ["447", "448"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\sitesApiSlice.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\userApiSlice.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\changeUrl.ts", ["449"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\firmApiSlice.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\fileManagementApiSlice.ts", ["450", "451", "452"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\oktaAuthClient\\OktaAuthClient.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\AppInitialization.tsx", ["453", "454", "455", "456", "457", "458", "459"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\routes\\RouteSwitch.tsx", ["460"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\uploaderSlice.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\routes\\index.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\Constants\\index.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\config\\endpoints.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\logger\\index.ts", ["461"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\uploadService.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\interceptorsSlice.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\files\\formatDownloadFileName.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\http\\httpVerbs.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\hooks\\useAppSelector.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\oktaAuthClient\\PrivateRoute.tsx", ["462", "463"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\index.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\Login\\LoginCallbackPage.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\antNotifications\\index.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\Login\\index.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\Logout\\index.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\TenentSelectionPage\\index.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\PageNotFound\\index.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\layouts\\MasterLayout\\index.tsx", ["464", "465", "466", "467", "468"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\SubmittedFiles\\index.tsx", ["469", "470", "471", "472", "473"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\PublishedFiles\\index.tsx", ["474", "475"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\http\\index.ts", ["476"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\layouts\\CommonLayout\\index.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\hooks\\useWindowDimensions.ts", ["477"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\OktaError\\index.tsx", ["478"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\DownloadModal\\types.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\SideMenu\\SideMenuContainer.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileCard\\FileCard.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\utils\\confirmDiscard.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\index.tsx", ["479", "480", "481"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\Header\\index.tsx", ["482", "483"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\PageTitle\\index.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\Login\\index.tsx", ["484"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\PageContent\\index.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\DownloadModal\\index.tsx", ["485", "486"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\Logout\\index.tsx", ["487"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\TenatSelection\\index.tsx", ["488", "489"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\Breadcrumbs\\index.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\CustomModal\\index.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\InfinityList\\index.tsx", ["490", "491", "492", "493", "494", "495", "496", "497", "498"], ["499"], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\validators\\index.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\SiteSelection\\index.tsx", ["500", "501", "502", "503", "504"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\SubmittedFiles\\FilterArea\\index.tsx", ["505", "506", "507", "508", "509", "510", "511", "512", "513", "514"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\FileDetailsModal\\index.tsx", ["515"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\PublishedFiles\\FilterArea\\index.tsx", ["516", "517", "518", "519", "520", "521", "522", "523", "524", "525"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\hooks\\useLocalStorage.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\menus\\menuConfig.ts", ["526", "527", "528"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\http\\statusCodes.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\SideMenu\\utils\\mapMenuConfig.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\constants\\errors.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\hooks\\useUploader.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\SideMenu\\index.tsx", ["529"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\UploadProgress\\index.tsx", ["530"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\DragAndDrop\\index.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\hooks\\useFileList.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\forms\\UploaderSubmit\\UploaderSubmitContainer.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\array\\index.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\regex\\index.ts", ["531", "532", "533", "534", "535", "536", "537", "538", "539", "540", "541", "542", "543", "544", "545", "546"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\InfinitySelect\\index.tsx", ["547", "548", "549", "550"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\files\\formatSize.ts", ["551"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\UploadProgress\\ProgressingFile.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\forms\\UploaderSubmit\\index.tsx", ["552", "553"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\forms\\UploaderSubmit\\FileList.tsx", ["554"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\Search\\index.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\tncApiSlice.ts", ["555"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\mocks\\tnc.mock.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\TermsAndConditions\\index.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\Dev\\KendoPlaygroundPage\\index.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\Dev\\KendoPlaygroundPage\\KendoPlaygroundPage.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\TermsAndConditions\\index.tsx", ["556"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\hooks\\useTermsAndConditions.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\Dev\\KendoPlaygroundPage\\sample-pdf.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\types\\tncTypes.ts", [], [], {"ruleId": "557", "severity": 1, "message": "558", "line": 49, "column": 80, "nodeType": "559", "messageId": "560", "endLine": 49, "endColumn": 81}, {"ruleId": "557", "severity": 1, "message": "558", "line": 74, "column": 81, "nodeType": "559", "messageId": "560", "endLine": 74, "endColumn": 82}, {"ruleId": "561", "severity": 1, "message": "562", "line": 8, "column": 31, "nodeType": "563", "messageId": "564", "endLine": 8, "endColumn": 42}, {"ruleId": "561", "severity": 1, "message": "565", "line": 66, "column": 16, "nodeType": "563", "messageId": "564", "endLine": 66, "endColumn": 23}, {"ruleId": "561", "severity": 1, "message": "566", "line": 66, "column": 25, "nodeType": "563", "messageId": "564", "endLine": 66, "endColumn": 29}, {"ruleId": "567", "severity": 1, "message": "568", "line": 15, "column": 72, "nodeType": "559", "messageId": "569", "endLine": 15, "endColumn": 74}, {"ruleId": "561", "severity": 1, "message": "570", "line": 13, "column": 8, "nodeType": "563", "messageId": "564", "endLine": 13, "endColumn": 30}, {"ruleId": "561", "severity": 1, "message": "571", "line": 17, "column": 8, "nodeType": "563", "messageId": "564", "endLine": 17, "endColumn": 16}, {"ruleId": "561", "severity": 1, "message": "572", "line": 19, "column": 10, "nodeType": "563", "messageId": "564", "endLine": 19, "endColumn": 16}, {"ruleId": "573", "severity": 1, "message": "574", "line": 44, "column": 18, "nodeType": "575", "endLine": 50, "endColumn": 8}, {"ruleId": "561", "severity": 1, "message": "576", "line": 77, "column": 18, "nodeType": "563", "messageId": "564", "endLine": 77, "endColumn": 33}, {"ruleId": "561", "severity": 1, "message": "577", "line": 78, "column": 18, "nodeType": "563", "messageId": "564", "endLine": 78, "endColumn": 32}, {"ruleId": "567", "severity": 1, "message": "568", "line": 82, "column": 31, "nodeType": "559", "messageId": "569", "endLine": 82, "endColumn": 33}, {"ruleId": "573", "severity": 1, "message": "578", "line": 95, "column": 6, "nodeType": "579", "endLine": 95, "endColumn": 13, "suggestions": "580"}, {"ruleId": "567", "severity": 1, "message": "568", "line": 102, "column": 36, "nodeType": "559", "messageId": "569", "endLine": 102, "endColumn": 38}, {"ruleId": "573", "severity": 1, "message": "581", "line": 120, "column": 6, "nodeType": "579", "endLine": 120, "endColumn": 53, "suggestions": "582"}, {"ruleId": "561", "severity": 1, "message": "583", "line": 3, "column": 8, "nodeType": "563", "messageId": "564", "endLine": 3, "endColumn": 23}, {"ruleId": "584", "severity": 1, "message": "585", "line": 105, "column": 1, "nodeType": "586", "endLine": 105, "endColumn": 29}, {"ruleId": "573", "severity": 1, "message": "587", "line": 34, "column": 6, "nodeType": "579", "endLine": 34, "endColumn": 57, "suggestions": "588"}, {"ruleId": "573", "severity": 1, "message": "589", "line": 34, "column": 17, "nodeType": "590", "endLine": 34, "endColumn": 28}, {"ruleId": "561", "severity": 1, "message": "591", "line": 18, "column": 10, "nodeType": "563", "messageId": "564", "endLine": 18, "endColumn": 16}, {"ruleId": "561", "severity": 1, "message": "592", "line": 18, "column": 24, "nodeType": "563", "messageId": "564", "endLine": 18, "endColumn": 37}, {"ruleId": "561", "severity": 1, "message": "593", "line": 19, "column": 20, "nodeType": "563", "messageId": "564", "endLine": 19, "endColumn": 29}, {"ruleId": "594", "severity": 1, "message": "595", "line": 38, "column": 127, "nodeType": "596", "messageId": "597", "endLine": 38, "endColumn": 128, "suggestions": "598"}, {"ruleId": "594", "severity": 1, "message": "595", "line": 48, "column": 127, "nodeType": "596", "messageId": "597", "endLine": 48, "endColumn": 128, "suggestions": "599"}, {"ruleId": "561", "severity": 1, "message": "600", "line": 1, "column": 10, "nodeType": "563", "messageId": "564", "endLine": 1, "endColumn": 25}, {"ruleId": "561", "severity": 1, "message": "601", "line": 1, "column": 75, "nodeType": "563", "messageId": "564", "endLine": 1, "endColumn": 87}, {"ruleId": "561", "severity": 1, "message": "602", "line": 29, "column": 63, "nodeType": "563", "messageId": "564", "endLine": 29, "endColumn": 70}, {"ruleId": "567", "severity": 1, "message": "603", "line": 84, "column": 22, "nodeType": "559", "messageId": "569", "endLine": 84, "endColumn": 24}, {"ruleId": "604", "severity": 1, "message": "605", "line": 458, "column": 15, "nodeType": "606", "endLine": 465, "endColumn": 16}, {"ruleId": "567", "severity": 1, "message": "603", "line": 61, "column": 22, "nodeType": "559", "messageId": "569", "endLine": 61, "endColumn": 24}, {"ruleId": "604", "severity": 1, "message": "605", "line": 260, "column": 15, "nodeType": "606", "endLine": 267, "endColumn": 16}, {"ruleId": "567", "severity": 1, "message": "568", "line": 47, "column": 67, "nodeType": "559", "messageId": "569", "endLine": 47, "endColumn": 69}, {"ruleId": "584", "severity": 1, "message": "607", "line": 11, "column": 1, "nodeType": "586", "endLine": 21, "endColumn": 3}, {"ruleId": "573", "severity": 1, "message": "574", "line": 23, "column": 18, "nodeType": "575", "endLine": 23, "endColumn": 48}, {"ruleId": "561", "severity": 1, "message": "608", "line": 9, "column": 36, "nodeType": "563", "messageId": "564", "endLine": 9, "endColumn": 44}, {"ruleId": "561", "severity": 1, "message": "609", "line": 10, "column": 10, "nodeType": "563", "messageId": "564", "endLine": 10, "endColumn": 20}, {"ruleId": "573", "severity": 1, "message": "610", "line": 58, "column": 6, "nodeType": "579", "endLine": 58, "endColumn": 13, "suggestions": "611"}, {"ruleId": "561", "severity": 1, "message": "612", "line": 2, "column": 26, "nodeType": "563", "messageId": "564", "endLine": 2, "endColumn": 30}, {"ruleId": "573", "severity": 1, "message": "613", "line": 30, "column": 6, "nodeType": "579", "endLine": 30, "endColumn": 34, "suggestions": "614"}, {"ruleId": "573", "severity": 1, "message": "615", "line": 13, "column": 6, "nodeType": "579", "endLine": 13, "endColumn": 27, "suggestions": "616"}, {"ruleId": "573", "severity": 1, "message": "617", "line": 43, "column": 5, "nodeType": "579", "endLine": 43, "endColumn": 34, "suggestions": "618"}, {"ruleId": "573", "severity": 1, "message": "619", "line": 49, "column": 6, "nodeType": "579", "endLine": 49, "endColumn": 8, "suggestions": "620"}, {"ruleId": "573", "severity": 1, "message": "615", "line": 12, "column": 6, "nodeType": "579", "endLine": 12, "endColumn": 17, "suggestions": "621"}, {"ruleId": "622", "severity": 1, "message": "623", "line": 31, "column": 11, "nodeType": "606", "endLine": 31, "endColumn": 63}, {"ruleId": "622", "severity": 1, "message": "623", "line": 38, "column": 17, "nodeType": "606", "endLine": 38, "endColumn": 76}, {"ruleId": "561", "severity": 1, "message": "624", "line": 34, "column": 20, "nodeType": "563", "messageId": "564", "endLine": 34, "endColumn": 31}, {"ruleId": "567", "severity": 1, "message": "568", "line": 51, "column": 33, "nodeType": "559", "messageId": "569", "endLine": 51, "endColumn": 35}, {"ruleId": "625", "severity": 1, "message": "626", "line": 92, "column": 49, "nodeType": "627", "messageId": "628", "endLine": 92, "endColumn": 51}, {"ruleId": "573", "severity": 1, "message": "629", "line": 115, "column": 6, "nodeType": "579", "endLine": 115, "endColumn": 18, "suggestions": "630"}, {"ruleId": "625", "severity": 1, "message": "626", "line": 126, "column": 40, "nodeType": "627", "messageId": "628", "endLine": 126, "endColumn": 42}, {"ruleId": "573", "severity": 1, "message": "631", "line": 132, "column": 6, "nodeType": "579", "endLine": 132, "endColumn": 38, "suggestions": "632"}, {"ruleId": "573", "severity": 1, "message": "633", "line": 150, "column": 6, "nodeType": "579", "endLine": 150, "endColumn": 49, "suggestions": "634"}, {"ruleId": "573", "severity": 1, "message": "635", "line": 154, "column": 6, "nodeType": "579", "endLine": 154, "endColumn": 24, "suggestions": "636"}, {"ruleId": "573", "severity": 1, "message": "637", "line": 238, "column": 6, "nodeType": "579", "endLine": 238, "endColumn": 36, "suggestions": "638"}, {"ruleId": "584", "severity": 1, "message": "607", "line": 24, "column": 1, "nodeType": "586", "endLine": 405, "endColumn": 3, "suppressions": "639"}, {"ruleId": "561", "severity": 1, "message": "640", "line": 7, "column": 17, "nodeType": "563", "messageId": "564", "endLine": 7, "endColumn": 28}, {"ruleId": "561", "severity": 1, "message": "641", "line": 18, "column": 36, "nodeType": "563", "messageId": "564", "endLine": 18, "endColumn": 45}, {"ruleId": "561", "severity": 1, "message": "642", "line": 18, "column": 47, "nodeType": "563", "messageId": "564", "endLine": 18, "endColumn": 52}, {"ruleId": "561", "severity": 1, "message": "643", "line": 22, "column": 9, "nodeType": "563", "messageId": "564", "endLine": 22, "endColumn": 23}, {"ruleId": "573", "severity": 1, "message": "644", "line": 72, "column": 5, "nodeType": "579", "endLine": 72, "endColumn": 7, "suggestions": "645"}, {"ruleId": "561", "severity": 1, "message": "646", "line": 1, "column": 10, "nodeType": "563", "messageId": "564", "endLine": 1, "endColumn": 16}, {"ruleId": "561", "severity": 1, "message": "647", "line": 1, "column": 18, "nodeType": "563", "messageId": "564", "endLine": 1, "endColumn": 21}, {"ruleId": "561", "severity": 1, "message": "648", "line": 1, "column": 23, "nodeType": "563", "messageId": "564", "endLine": 1, "endColumn": 26}, {"ruleId": "561", "severity": 1, "message": "649", "line": 1, "column": 28, "nodeType": "563", "messageId": "564", "endLine": 1, "endColumn": 34}, {"ruleId": "561", "severity": 1, "message": "602", "line": 3, "column": 10, "nodeType": "563", "messageId": "564", "endLine": 3, "endColumn": 17}, {"ruleId": "561", "severity": 1, "message": "650", "line": 4, "column": 10, "nodeType": "563", "messageId": "564", "endLine": 4, "endColumn": 24}, {"ruleId": "561", "severity": 1, "message": "651", "line": 4, "column": 26, "nodeType": "563", "messageId": "564", "endLine": 4, "endColumn": 38}, {"ruleId": "561", "severity": 1, "message": "652", "line": 5, "column": 8, "nodeType": "563", "messageId": "564", "endLine": 5, "endColumn": 14}, {"ruleId": "561", "severity": 1, "message": "653", "line": 6, "column": 8, "nodeType": "563", "messageId": "564", "endLine": 6, "endColumn": 14}, {"ruleId": "561", "severity": 1, "message": "654", "line": 7, "column": 10, "nodeType": "563", "messageId": "564", "endLine": 7, "endColumn": 20}, {"ruleId": "573", "severity": 1, "message": "655", "line": 58, "column": 6, "nodeType": "579", "endLine": 58, "endColumn": 16, "suggestions": "656"}, {"ruleId": "561", "severity": 1, "message": "646", "line": 1, "column": 10, "nodeType": "563", "messageId": "564", "endLine": 1, "endColumn": 16}, {"ruleId": "561", "severity": 1, "message": "647", "line": 1, "column": 18, "nodeType": "563", "messageId": "564", "endLine": 1, "endColumn": 21}, {"ruleId": "561", "severity": 1, "message": "648", "line": 1, "column": 23, "nodeType": "563", "messageId": "564", "endLine": 1, "endColumn": 26}, {"ruleId": "561", "severity": 1, "message": "649", "line": 1, "column": 28, "nodeType": "563", "messageId": "564", "endLine": 1, "endColumn": 34}, {"ruleId": "561", "severity": 1, "message": "602", "line": 1, "column": 36, "nodeType": "563", "messageId": "564", "endLine": 1, "endColumn": 43}, {"ruleId": "561", "severity": 1, "message": "650", "line": 3, "column": 10, "nodeType": "563", "messageId": "564", "endLine": 3, "endColumn": 24}, {"ruleId": "561", "severity": 1, "message": "651", "line": 3, "column": 26, "nodeType": "563", "messageId": "564", "endLine": 3, "endColumn": 38}, {"ruleId": "561", "severity": 1, "message": "652", "line": 4, "column": 8, "nodeType": "563", "messageId": "564", "endLine": 4, "endColumn": 14}, {"ruleId": "561", "severity": 1, "message": "653", "line": 5, "column": 8, "nodeType": "563", "messageId": "564", "endLine": 5, "endColumn": 14}, {"ruleId": "561", "severity": 1, "message": "654", "line": 6, "column": 10, "nodeType": "563", "messageId": "564", "endLine": 6, "endColumn": 20}, {"ruleId": "561", "severity": 1, "message": "657", "line": 4, "column": 7, "nodeType": "563", "messageId": "564", "endLine": 4, "endColumn": 41}, {"ruleId": "561", "severity": 1, "message": "658", "line": 28, "column": 7, "nodeType": "563", "messageId": "564", "endLine": 28, "endColumn": 46}, {"ruleId": "561", "severity": 1, "message": "659", "line": 36, "column": 7, "nodeType": "563", "messageId": "564", "endLine": 36, "endColumn": 44}, {"ruleId": "573", "severity": 1, "message": "660", "line": 33, "column": 5, "nodeType": "579", "endLine": 33, "endColumn": 7, "suggestions": "661"}, {"ruleId": "573", "severity": 1, "message": "662", "line": 22, "column": 6, "nodeType": "579", "endLine": 22, "endColumn": 36, "suggestions": "663"}, {"ruleId": "594", "severity": 1, "message": "664", "line": 2, "column": 60, "nodeType": "665", "messageId": "597", "endLine": 2, "endColumn": 61, "suggestions": "666"}, {"ruleId": "594", "severity": 1, "message": "664", "line": 4, "column": 70, "nodeType": "665", "messageId": "597", "endLine": 4, "endColumn": 71, "suggestions": "667"}, {"ruleId": "594", "severity": 1, "message": "668", "line": 4, "column": 83, "nodeType": "665", "messageId": "597", "endLine": 4, "endColumn": 84, "suggestions": "669"}, {"ruleId": "594", "severity": 1, "message": "670", "line": 4, "column": 87, "nodeType": "665", "messageId": "597", "endLine": 4, "endColumn": 88, "suggestions": "671"}, {"ruleId": "594", "severity": 1, "message": "672", "line": 4, "column": 89, "nodeType": "665", "messageId": "597", "endLine": 4, "endColumn": 90, "suggestions": "673"}, {"ruleId": "594", "severity": 1, "message": "674", "line": 4, "column": 91, "nodeType": "665", "messageId": "597", "endLine": 4, "endColumn": 92, "suggestions": "675"}, {"ruleId": "594", "severity": 1, "message": "676", "line": 4, "column": 93, "nodeType": "665", "messageId": "597", "endLine": 4, "endColumn": 94, "suggestions": "677"}, {"ruleId": "594", "severity": 1, "message": "678", "line": 6, "column": 44, "nodeType": "665", "messageId": "597", "endLine": 6, "endColumn": 45, "suggestions": "679"}, {"ruleId": "594", "severity": 1, "message": "678", "line": 6, "column": 69, "nodeType": "665", "messageId": "597", "endLine": 6, "endColumn": 70, "suggestions": "680"}, {"ruleId": "594", "severity": 1, "message": "678", "line": 6, "column": 78, "nodeType": "665", "messageId": "597", "endLine": 6, "endColumn": 79, "suggestions": "681"}, {"ruleId": "594", "severity": 1, "message": "678", "line": 6, "column": 82, "nodeType": "665", "messageId": "597", "endLine": 6, "endColumn": 83, "suggestions": "682"}, {"ruleId": "594", "severity": 1, "message": "670", "line": 7, "column": 46, "nodeType": "665", "messageId": "597", "endLine": 7, "endColumn": 47, "suggestions": "683"}, {"ruleId": "594", "severity": 1, "message": "672", "line": 7, "column": 48, "nodeType": "665", "messageId": "597", "endLine": 7, "endColumn": 49, "suggestions": "684"}, {"ruleId": "594", "severity": 1, "message": "668", "line": 7, "column": 60, "nodeType": "665", "messageId": "597", "endLine": 7, "endColumn": 61, "suggestions": "685"}, {"ruleId": "594", "severity": 1, "message": "686", "line": 7, "column": 62, "nodeType": "665", "messageId": "597", "endLine": 7, "endColumn": 63, "suggestions": "687"}, {"ruleId": "594", "severity": 1, "message": "688", "line": 7, "column": 65, "nodeType": "665", "messageId": "597", "endLine": 7, "endColumn": 66, "suggestions": "689"}, {"ruleId": "584", "severity": 1, "message": "607", "line": 44, "column": 1, "nodeType": "586", "endLine": 240, "endColumn": 3}, {"ruleId": "573", "severity": 1, "message": "690", "line": 78, "column": 6, "nodeType": "579", "endLine": 78, "endColumn": 8, "suggestions": "691"}, {"ruleId": "573", "severity": 1, "message": "692", "line": 107, "column": 6, "nodeType": "579", "endLine": 107, "endColumn": 19, "suggestions": "693"}, {"ruleId": "573", "severity": 1, "message": "694", "line": 107, "column": 7, "nodeType": "665", "endLine": 107, "endColumn": 10}, {"ruleId": "584", "severity": 1, "message": "607", "line": 2, "column": 1, "nodeType": "586", "endLine": 11, "endColumn": 3}, {"ruleId": "561", "severity": 1, "message": "649", "line": 3, "column": 26, "nodeType": "563", "messageId": "564", "endLine": 3, "endColumn": 32}, {"ruleId": "561", "severity": 1, "message": "602", "line": 4, "column": 8, "nodeType": "563", "messageId": "564", "endLine": 4, "endColumn": 15}, {"ruleId": "573", "severity": 1, "message": "695", "line": 75, "column": 6, "nodeType": "579", "endLine": 75, "endColumn": 22, "suggestions": "696"}, {"ruleId": "561", "severity": 1, "message": "697", "line": 5, "column": 50, "nodeType": "563", "messageId": "564", "endLine": 5, "endColumn": 57}, {"ruleId": "573", "severity": 1, "message": "698", "line": 53, "column": 6, "nodeType": "579", "endLine": 53, "endColumn": 8, "suggestions": "699"}, "no-useless-concat", "Unexpected string concatenation of literals.", "BinaryExpression", "unexpectedConcat", "@typescript-eslint/no-unused-vars", "'TncDocument' is defined but never used.", "Identifier", "unusedVar", "'baseUrl' is assigned a value but never used.", "'path' is assigned a value but never used.", "eqeqeq", "Expected '===' and instead saw '=='.", "unexpected", "'formatDownloadFileName' is defined but never used.", "'download' is defined but never used.", "'upload' is defined but never used.", "react-hooks/exhaustive-deps", "Assignments to the 'interval' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "CallExpression", "'userPermissions' is assigned a value but never used.", "'userPreference' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'isLoading'. Either include them or remove the dependency array.", "ArrayExpression", ["700"], "React Hook useEffect has missing dependencies: 'authState?.accessToken?.claims.tid', 'dispatch', 'fetchEndpointsAndUser', 'fetchMenusAndSites', 'fetchTenantContext', and 'menuItems.length'. Either include them or remove the dependency array.", ["701"], "'RouteConfigType' is defined but never used.", "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration", "React Hook useEffect has a missing dependency: 'authState'. Either include it or remove the dependency array.", ["702"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "UnaryExpression", "'tenant' is assigned a value but never used.", "'tenantContext' is assigned a value but never used.", "'authState' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\'.", "TemplateElement", "unnecessaryEscape", ["703", "704"], ["705", "706"], "'ControlOutlined' is defined but never used.", "'SyncOutlined' is defined but never used.", "'Tooltip' is defined but never used.", "Expected '!==' and instead saw '!='.", "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "Assign arrow function to a variable before exporting as module default", "'useState' is defined but never used.", "'FileRecord' is defined but never used.", "React Hook useEffect has a missing dependency: 'fileUpload'. Either include it or remove the dependency array.", ["707"], "'Menu' is defined but never used.", "React Hook useEffect has missing dependencies: 'authState?.accessToken?.claims.tid' and 'triggerGetFirmLogoAndName'. Either include them or remove the dependency array.", ["708"], "React Hook useEffect has a missing dependency: 'navigate'. Either include it or remove the dependency array.", ["709"], "React Hook useCallback has missing dependencies: 'bulkDownload' and 'downloadAndSaveFile'. Either include them or remove the dependency array. If 'downloadAndSaveFile' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["710"], "React Hook useEffect has a missing dependency: 'triggerFileDownload'. Either include it or remove the dependency array.", ["711"], ["712"], "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'setKeyValue' is assigned a value but never used.", "array-callback-return", "Array.prototype.map() expects a value to be returned at the end of arrow function.", "ArrowFunctionExpression", "expectedAtEnd", "React Hook useEffect has missing dependencies: 'checkAll', 'items', 'previouslyCheckedList', 'previouslySelectedDropdownValue', 'props.checkedList', 'props.hasCheckbox', 'props.selectedList', and 'selectedDropdownValue'. Either include them or remove the dependency array. If 'setCheckedList' needs the current value of 'props.checkedList', you can also switch to useReducer instead of useState and read 'props.checkedList' in the reducer.", ["713"], "React Hook useEffect has missing dependencies: 'historyOptions', 'props', 'selectedDropdownValue', and 'totalRecordCount'. Either include them or remove the dependency array. However, 'props' will change when *any* prop changes, so the preferred fix is to destructure the 'props' object outside of the useEffect call and refer to those specific props inside useEffect.", ["714"], "React Hook useEffect has missing dependencies: 'currentPage', 'loadMore', 'pageCount', and 'props'. Either include them or remove the dependency array. However, 'props' will change when *any* prop changes, so the preferred fix is to destructure the 'props' object outside of the useEffect call and refer to those specific props inside useEffect.", ["715"], "React Hook useEffect has a missing dependency: 'props'. Either include it or remove the dependency array. However, 'props' will change when *any* prop changes, so the preferred fix is to destructure the 'props' object outside of the useEffect call and refer to those specific props inside useEffect.", ["716"], "React Hook React.useMemo has a missing dependency: 'props'. Either include it or remove the dependency array. Outer scope values like 'config.inputDebounceInterval' aren't valid dependencies because mutating them doesn't re-render the component.", ["717"], ["718"], "'useCallback' is defined but never used.", "'isLoading' is assigned a value but never used.", "'error' is assigned a value but never used.", "'mode' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getPaginatedRecords'. Either include it or remove the dependency array.", ["719"], "'Button' is defined but never used.", "'Col' is defined but never used.", "'Row' is defined but never used.", "'Select' is defined but never used.", "'SearchOutlined' is defined but never used.", "'PlusOutlined' is defined but never used.", "'Search' is defined but never used.", "'styles' is defined but never used.", "'BiSortDown' is defined but never used.", "React Hook useEffect has a missing dependency: 'onLastFile'. Either include it or remove the dependency array. If 'onLastFile' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["720"], "'RequestsMenuConfig' is assigned a value but never used.", "'AnnouncementsMenuConfig' is assigned a value but never used.", "'DiscussionsMenuConfig' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'triggerGetFirmLogoAndName'. Either include it or remove the dependency array.", ["721"], "React Hook useEffect has a missing dependency: 'hasErrored'. Either include it or remove the dependency array.", ["722"], "Unnecessary escape character: \\..", "Literal", ["723", "724"], ["725", "726"], "Unnecessary escape character: \\$.", ["727", "728"], "Unnecessary escape character: \\(.", ["729", "730"], "Unnecessary escape character: \\).", ["731", "732"], "Unnecessary escape character: \\*.", ["733", "734"], "Unnecessary escape character: \\+.", ["735", "736"], "Unnecessary escape character: \\\".", ["737", "738"], ["739", "740"], ["741", "742"], ["743", "744"], ["745", "746"], ["747", "748"], ["749", "750"], "Unnecessary escape character: \\?.", ["751", "752"], "Unnecessary escape character: \\/.", ["753", "754"], "React Hook useEffect has a missing dependency: 'fetchInitialData'. Either include it or remove the dependency array.", ["755"], "React Hook React.useMemo has a missing dependency: 'props'. Either include it or remove the dependency array. However, 'props' will change when *any* prop changes, so the preferred fix is to destructure the 'props' object outside of the useMemo call and refer to those specific props inside React.useMemo.", ["756"], "The 500 literal is not a valid dependency because it never changes. You can safely remove it.", "React Hook useEffect has missing dependencies: 'onDataChange' and 'onTitleUpdate'. Either include them or remove the dependency array. If 'onDataChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["757"], "'TncTags' is defined but never used.", "React Hook useEffect has missing dependencies: 'pdfBlobUrl' and 'tncDocument.document?.documentUrl'. Either include them or remove the dependency array.", ["758"], {"desc": "759", "fix": "760"}, {"desc": "761", "fix": "762"}, {"desc": "763", "fix": "764"}, {"messageId": "765", "fix": "766", "desc": "767"}, {"messageId": "768", "fix": "769", "desc": "770"}, {"messageId": "765", "fix": "771", "desc": "767"}, {"messageId": "768", "fix": "772", "desc": "770"}, {"desc": "773", "fix": "774"}, {"desc": "775", "fix": "776"}, {"desc": "777", "fix": "778"}, {"desc": "779", "fix": "780"}, {"desc": "781", "fix": "782"}, {"desc": "783", "fix": "784"}, {"desc": "785", "fix": "786"}, {"desc": "787", "fix": "788"}, {"desc": "789", "fix": "790"}, {"desc": "791", "fix": "792"}, {"desc": "793", "fix": "794"}, {"kind": "795", "justification": "796"}, {"desc": "797", "fix": "798"}, {"desc": "799", "fix": "800"}, {"desc": "801", "fix": "802"}, {"desc": "803", "fix": "804"}, {"messageId": "765", "fix": "805", "desc": "767"}, {"messageId": "768", "fix": "806", "desc": "770"}, {"messageId": "765", "fix": "807", "desc": "767"}, {"messageId": "768", "fix": "808", "desc": "770"}, {"messageId": "765", "fix": "809", "desc": "767"}, {"messageId": "768", "fix": "810", "desc": "770"}, {"messageId": "765", "fix": "811", "desc": "767"}, {"messageId": "768", "fix": "812", "desc": "770"}, {"messageId": "765", "fix": "813", "desc": "767"}, {"messageId": "768", "fix": "814", "desc": "770"}, {"messageId": "765", "fix": "815", "desc": "767"}, {"messageId": "768", "fix": "816", "desc": "770"}, {"messageId": "765", "fix": "817", "desc": "767"}, {"messageId": "768", "fix": "818", "desc": "770"}, {"messageId": "765", "fix": "819", "desc": "767"}, {"messageId": "768", "fix": "820", "desc": "770"}, {"messageId": "765", "fix": "821", "desc": "767"}, {"messageId": "768", "fix": "822", "desc": "770"}, {"messageId": "765", "fix": "823", "desc": "767"}, {"messageId": "768", "fix": "824", "desc": "770"}, {"messageId": "765", "fix": "825", "desc": "767"}, {"messageId": "768", "fix": "826", "desc": "770"}, {"messageId": "765", "fix": "827", "desc": "767"}, {"messageId": "768", "fix": "828", "desc": "770"}, {"messageId": "765", "fix": "829", "desc": "767"}, {"messageId": "768", "fix": "830", "desc": "770"}, {"messageId": "765", "fix": "831", "desc": "767"}, {"messageId": "768", "fix": "832", "desc": "770"}, {"messageId": "765", "fix": "833", "desc": "767"}, {"messageId": "768", "fix": "834", "desc": "770"}, {"messageId": "765", "fix": "835", "desc": "767"}, {"messageId": "768", "fix": "836", "desc": "770"}, {"desc": "837", "fix": "838"}, {"desc": "839", "fix": "840"}, {"desc": "841", "fix": "842"}, {"desc": "843", "fix": "844"}, "Update the dependencies array to be: [dispatch, error, isLoading]", {"range": "845", "text": "846"}, "Update the dependencies array to be: [authState?.isAuthenticated, tenant, endPoints, authState?.accessToken?.claims.tid, dispatch, menuItems.length, fetchEndpointsAndUser, fetchMenusAndSites, fetchTenantContext]", {"range": "847", "text": "848"}, "Update the dependencies array to be: [oktaAuth, authState?.isAuthenticated, authState]", {"range": "849", "text": "850"}, "removeEscape", {"range": "851", "text": "796"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "852", "text": "853"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "854", "text": "796"}, {"range": "855", "text": "853"}, "Update the dependencies array to be: [fileUpload, files]", {"range": "856", "text": "857"}, "Update the dependencies array to be: [authState?.accessToken?.claims.tid, authState?.isAuthenticated, triggerGetFirmLogoAndName]", {"range": "858", "text": "859"}, "Update the dependencies array to be: [authState, navigate, oktaAuth]", {"range": "860", "text": "861"}, "Update the dependencies array to be: [bulkDownload, downloadAndSaveFile, downloadType, selectedFiles]", {"range": "862", "text": "863"}, "Update the dependencies array to be: [triggerFileDownload]", {"range": "864", "text": "865"}, "Update the dependencies array to be: [authState, navigate]", {"range": "866", "text": "867"}, "Update the dependencies array to be: [checkAll, items, previouslyCheckedList, previouslySelectedDropdownValue, props.checkedList, props.data, props.hasCheckbox, props.selectedList, selectedDropdownValue]", {"range": "868", "text": "869"}, "Update the dependencies array to be: [checkedList, historyOptions, props, props.deletedList, selectedDropdownValue, totalRecordCount]", {"range": "870", "text": "871"}, "Update the dependencies array to be: [props.removedItem, items, props.isLoading, props, pageCount, currentPage, loadMore]", {"range": "872", "text": "873"}, "Update the dependencies array to be: [props, totalRecordCount]", {"range": "874", "text": "875"}, "Update the dependencies array to be: [props]", {"range": "876", "text": "877"}, "directive", "", "Update the dependencies array to be: [getPaginatedRecords]", {"range": "878", "text": "879"}, "Update the dependencies array to be: [fileList, onLastFile]", {"range": "880", "text": "881"}, "Update the dependencies array to be: [triggerGetFirmLogoAndName]", {"range": "882", "text": "883"}, "Update the dependencies array to be: [completed, hasErrored, onComplete, total]", {"range": "884", "text": "885"}, {"range": "886", "text": "796"}, {"range": "887", "text": "853"}, {"range": "888", "text": "796"}, {"range": "889", "text": "853"}, {"range": "890", "text": "796"}, {"range": "891", "text": "853"}, {"range": "892", "text": "796"}, {"range": "893", "text": "853"}, {"range": "894", "text": "796"}, {"range": "895", "text": "853"}, {"range": "896", "text": "796"}, {"range": "897", "text": "853"}, {"range": "898", "text": "796"}, {"range": "899", "text": "853"}, {"range": "900", "text": "796"}, {"range": "901", "text": "853"}, {"range": "902", "text": "796"}, {"range": "903", "text": "853"}, {"range": "904", "text": "796"}, {"range": "905", "text": "853"}, {"range": "906", "text": "796"}, {"range": "907", "text": "853"}, {"range": "908", "text": "796"}, {"range": "909", "text": "853"}, {"range": "910", "text": "796"}, {"range": "911", "text": "853"}, {"range": "912", "text": "796"}, {"range": "913", "text": "853"}, {"range": "914", "text": "796"}, {"range": "915", "text": "853"}, {"range": "916", "text": "796"}, {"range": "917", "text": "853"}, "Update the dependencies array to be: [fetchInitialData]", {"range": "918", "text": "919"}, "Update the dependencies array to be: [options, props]", {"range": "920", "text": "921"}, "Update the dependencies array to be: [forManageFiles, onDataChange, onTitleUpdate]", {"range": "922", "text": "923"}, "Update the dependencies array to be: [pdfBlobUrl, tncDocument.document?.documentUrl]", {"range": "924", "text": "925"}, [3630, 3637], "[dispatch, error, isLoading]", [4462, 4509], "[authState?.isAuthenticated, tenant, endPoints, authState?.accessToken?.claims.tid, dispatch, menuItems.length, fetchEndpointsAndUser, fetchMenusAndSites, fetchTenantContext]", [1317, 1368], "[oktaAuth, authState?.isAuthenticated, authState]", [1745, 1746], [1745, 1745], "\\", [2318, 2319], [2318, 2318], [1988, 1995], "[fileUpload, files]", [1343, 1371], "[authState?.accessToken?.claims.tid, authState?.isAuthenticated, triggerGetFirmLogoAndName]", [452, 473], "[authState, navigate, oktaAuth]", [1624, 1653], "[bulkDownload, downloadAndSaveFile, downloadType, selectedFiles]", [1764, 1766], "[triggerFileDownload]", [382, 393], "[authState, navigate]", [4845, 4857], "[checkAll, items, previouslyCheckedList, previouslySelectedDropdownValue, props.checkedList, props.data, props.hasCheckbox, props.selectedList, selectedDropdownValue]", [5571, 5603], "[checkedList, historyOptions, props, props.deletedList, selectedDropdownV<PERSON><PERSON>, totalRecordCount]", [6049, 6092], "[props.removedItem, items, props.isLoading, props, pageCount, currentPage, loadMore]", [6189, 6207], "[props, totalRecordCount]", [9387, 9417], "[props]", [2303, 2305], "[getPaginatedRecords]", [2038, 2048], "[fileList, onLastFile]", [1419, 1421], "[triggerGetFirmLogoAndName]", [1039, 1069], "[completed, hasErrored, onComplete, total]", [104, 105], [104, 104], [286, 287], [286, 286], [299, 300], [299, 299], [303, 304], [303, 303], [305, 306], [305, 305], [307, 308], [307, 307], [309, 310], [309, 309], [400, 401], [400, 400], [425, 426], [425, 425], [434, 435], [434, 434], [438, 439], [438, 438], [581, 582], [581, 581], [583, 584], [583, 583], [595, 596], [595, 595], [597, 598], [597, 597], [600, 601], [600, 600], [2454, 2456], "[fetchInitialData]", [3463, 3476], "[options, props]", [2563, 2579], "[forManageFiles, onDataChange, onTitleUpdate]", [2381, 2383], "[pdfBlobUrl, tncDocument.document?.documentUrl]"]