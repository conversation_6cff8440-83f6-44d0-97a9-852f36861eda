{"ast": null, "code": "var detectBrowser = function () {\n  var ua = window && window.navigator.userAgent;\n  if (!ua) {\n    return false;\n  }\n  var browser = false;\n  var match = [];\n  var browserRxs = {\n    edge: /(edge)[ \\/]([\\w.]+)/i,\n    webkit: /(chrome|crios)[ \\/]([\\w.]+)/i,\n    safari: /(webkit)[ \\/]([\\w.]+)/i,\n    opera: /(opera)(?:.*version|)[ \\/]([\\w.]+)/i,\n    msie: /(msie\\s|trident.*? rv:)([\\w.]+)/i,\n    mozilla: /(mozilla)(?:.*? rv:([\\w.]+)|)/i\n  };\n  for (var agent in browserRxs) {\n    if (browserRxs.hasOwnProperty(agent)) {\n      match = ua.match(browserRxs[agent]);\n      if (match) {\n        browser = {};\n        browser[agent] = true;\n        browser[match[1].toLowerCase().split(\" \")[0].split(\"/\")[0]] = true;\n        browser.version = parseInt(document.DOCUMENT_NODE || match[2], 10);\n        break;\n      }\n    }\n  }\n  return browser;\n};\n/** @hidden */\nexport var getDocument = function (element) {\n  return element ? element.ownerDocument || window.document : window.document;\n};\n/** @hidden */\nexport var getWindow = function (element) {\n  var document = getDocument(element);\n  return document ? document.defaultView || window : window;\n};\n/** @hidden */\nexport var scrollableRoot = function (element) {\n  var support = {\n    browser: detectBrowser()\n  };\n  var document = getDocument(element);\n  return support.browser.edge || support.browser.safari ? document.body : document.documentElement;\n};\n/** @hidden */\nexport var isScrollable = function (el) {\n  if (el && el.className && typeof el.className === 'string' && el.className.indexOf('k-auto-scrollable') > -1) {\n    return true;\n  }\n  var overflow = window.getComputedStyle(el, 'overflow').overflow;\n  return overflow.indexOf('auto') > -1 || overflow.indexOf('scroll') > -1;\n};\n/** @hidden */\nexport var getScrollableParent = function (el) {\n  var root = scrollableRoot(el);\n  if (!el || el === document.body || el === document.documentElement) {\n    return root;\n  }\n  var parent = el;\n  while (parent && parent !== document.body && parent.nodeType !== Node.DOCUMENT_FRAGMENT_NODE && parent.nodeType !== Node.DOCUMENT_NODE && !isScrollable(parent)) {\n    parent = parent.parentNode;\n  }\n  if (parent && (parent === document.body || parent.nodeType === Node.DOCUMENT_FRAGMENT_NODE)) {\n    return root;\n  }\n  return parent;\n};\n/** @hidden */\nexport var autoScrollVelocity = function (mouseX, mouseY, rect) {\n  var velocity = {\n    x: 0,\n    y: 0\n  };\n  var AUTO_SCROLL_AREA = 50;\n  if (mouseX - rect.left < AUTO_SCROLL_AREA) {\n    velocity.x = -(AUTO_SCROLL_AREA - (mouseX - rect.left));\n  } else if (rect.right - mouseX < AUTO_SCROLL_AREA) {\n    velocity.x = AUTO_SCROLL_AREA - (rect.right - mouseX);\n  }\n  if (mouseY - rect.top < AUTO_SCROLL_AREA) {\n    velocity.y = -(AUTO_SCROLL_AREA - (mouseY - rect.top));\n  } else if (rect.bottom - mouseY < AUTO_SCROLL_AREA) {\n    velocity.y = AUTO_SCROLL_AREA - (rect.bottom - mouseY);\n  }\n  return velocity;\n};\n/** @hidden */\nexport var scrollableViewPort = function (el, window) {\n  var root = scrollableRoot(el);\n  if (el === root) {\n    return {\n      top: root.scrollTop,\n      left: root.scrollLeft,\n      bottom: root.scrollTop + window.innerHeight,\n      right: root.scrollLeft + window.innerWidth\n    };\n  } else {\n    var rect = el.getBoundingClientRect();\n    return {\n      bottom: rect.top + rect.height,\n      right: rect.left + rect.width,\n      left: rect.left,\n      top: rect.top\n    };\n  }\n};\n/** @hidden */\nexport var isPointerInsideContainer = function (x, y, container) {\n  var rect = container.getBoundingClientRect();\n  return rect.top <= y && rect.left <= x && y <= rect.bottom && x <= rect.right;\n};", "map": {"version": 3, "names": ["detectBrowser", "ua", "window", "navigator", "userAgent", "browser", "match", "browserRxs", "edge", "webkit", "safari", "opera", "msie", "mozilla", "agent", "hasOwnProperty", "toLowerCase", "split", "version", "parseInt", "document", "DOCUMENT_NODE", "getDocument", "element", "ownerDocument", "getWindow", "defaultView", "scrollableRoot", "support", "body", "documentElement", "isScrollable", "el", "className", "indexOf", "overflow", "getComputedStyle", "getScrollableParent", "root", "parent", "nodeType", "Node", "DOCUMENT_FRAGMENT_NODE", "parentNode", "autoScrollVelocity", "mouseX", "mouseY", "rect", "velocity", "x", "y", "AUTO_SCROLL_AREA", "left", "right", "top", "bottom", "scrollableViewPort", "scrollTop", "scrollLeft", "innerHeight", "innerWidth", "getBoundingClientRect", "height", "width", "isPointerInsideContainer", "container"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-draggable-common/dist/es/utils/index.js"], "sourcesContent": ["var detectBrowser = function () {\n    var ua = window && window.navigator.userAgent;\n    if (!ua) {\n        return false;\n    }\n    var browser = false;\n    var match = [];\n    var browserRxs = {\n        edge: /(edge)[ \\/]([\\w.]+)/i,\n        webkit: /(chrome|crios)[ \\/]([\\w.]+)/i,\n        safari: /(webkit)[ \\/]([\\w.]+)/i,\n        opera: /(opera)(?:.*version|)[ \\/]([\\w.]+)/i,\n        msie: /(msie\\s|trident.*? rv:)([\\w.]+)/i,\n        mozilla: /(mozilla)(?:.*? rv:([\\w.]+)|)/i\n    };\n    for (var agent in browserRxs) {\n        if (browserRxs.hasOwnProperty(agent)) {\n            match = ua.match(browserRxs[agent]);\n            if (match) {\n                browser = {};\n                browser[agent] = true;\n                browser[match[1].toLowerCase().split(\" \")[0].split(\"/\")[0]] = true;\n                browser.version = parseInt(document.DOCUMENT_NODE || match[2], 10);\n                break;\n            }\n        }\n    }\n    return browser;\n};\n/** @hidden */\nexport var getDocument = function (element) {\n    return element ? element.ownerDocument || window.document : window.document;\n};\n/** @hidden */\nexport var getWindow = function (element) {\n    var document = getDocument(element);\n    return document\n        ? document.defaultView || window\n        : window;\n};\n/** @hidden */\nexport var scrollableRoot = function (element) {\n    var support = { browser: detectBrowser() };\n    var document = getDocument(element);\n    return (support.browser.edge || support.browser.safari) ? document.body : document.documentElement;\n};\n/** @hidden */\nexport var isScrollable = function (el) {\n    if (el && el.className && typeof (el.className) === 'string' && el.className.indexOf('k-auto-scrollable') > -1) {\n        return true;\n    }\n    var overflow = window.getComputedStyle(el, 'overflow').overflow;\n    return overflow.indexOf('auto') > -1 || overflow.indexOf('scroll') > -1;\n};\n/** @hidden */\nexport var getScrollableParent = function (el) {\n    var root = scrollableRoot(el);\n    if (!el || el === document.body || el === document.documentElement) {\n        return root;\n    }\n    var parent = el;\n    while (parent\n        && parent !== document.body\n        && parent.nodeType !== Node.DOCUMENT_FRAGMENT_NODE\n        && parent.nodeType !== Node.DOCUMENT_NODE\n        && !isScrollable(parent)) {\n        parent = parent.parentNode;\n    }\n    if (parent && (parent === document.body || parent.nodeType === Node.DOCUMENT_FRAGMENT_NODE)) {\n        return root;\n    }\n    return parent;\n};\n/** @hidden */\nexport var autoScrollVelocity = function (mouseX, mouseY, rect) {\n    var velocity = { x: 0, y: 0 };\n    var AUTO_SCROLL_AREA = 50;\n    if (mouseX - rect.left < AUTO_SCROLL_AREA) {\n        velocity.x = -(AUTO_SCROLL_AREA - (mouseX - rect.left));\n    }\n    else if (rect.right - mouseX < AUTO_SCROLL_AREA) {\n        velocity.x = AUTO_SCROLL_AREA - (rect.right - mouseX);\n    }\n    if (mouseY - rect.top < AUTO_SCROLL_AREA) {\n        velocity.y = -(AUTO_SCROLL_AREA - (mouseY - rect.top));\n    }\n    else if (rect.bottom - mouseY < AUTO_SCROLL_AREA) {\n        velocity.y = AUTO_SCROLL_AREA - (rect.bottom - mouseY);\n    }\n    return velocity;\n};\n/** @hidden */\nexport var scrollableViewPort = function (el, window) {\n    var root = scrollableRoot(el);\n    if (el === root) {\n        return {\n            top: root.scrollTop,\n            left: root.scrollLeft,\n            bottom: root.scrollTop + window.innerHeight,\n            right: root.scrollLeft + window.innerWidth\n        };\n    }\n    else {\n        var rect = el.getBoundingClientRect();\n        return {\n            bottom: rect.top + rect.height,\n            right: rect.left + rect.width,\n            left: rect.left,\n            top: rect.top\n        };\n    }\n};\n/** @hidden */\nexport var isPointerInsideContainer = function (x, y, container) {\n    var rect = container.getBoundingClientRect();\n    return (rect.top <= y &&\n        rect.left <= x &&\n        y <= rect.bottom &&\n        x <= rect.right);\n};\n"], "mappings": "AAAA,IAAIA,aAAa,GAAG,SAAAA,CAAA,EAAY;EAC5B,IAAIC,EAAE,GAAGC,MAAM,IAAIA,MAAM,CAACC,SAAS,CAACC,SAAS;EAC7C,IAAI,CAACH,EAAE,EAAE;IACL,OAAO,KAAK;EAChB;EACA,IAAII,OAAO,GAAG,KAAK;EACnB,IAAIC,KAAK,GAAG,EAAE;EACd,IAAIC,UAAU,GAAG;IACbC,IAAI,EAAE,sBAAsB;IAC5BC,MAAM,EAAE,8BAA8B;IACtCC,MAAM,EAAE,wBAAwB;IAChCC,KAAK,EAAE,qCAAqC;IAC5CC,IAAI,EAAE,kCAAkC;IACxCC,OAAO,EAAE;EACb,CAAC;EACD,KAAK,IAAIC,KAAK,IAAIP,UAAU,EAAE;IAC1B,IAAIA,UAAU,CAACQ,cAAc,CAACD,KAAK,CAAC,EAAE;MAClCR,KAAK,GAAGL,EAAE,CAACK,KAAK,CAACC,UAAU,CAACO,KAAK,CAAC,CAAC;MACnC,IAAIR,KAAK,EAAE;QACPD,OAAO,GAAG,CAAC,CAAC;QACZA,OAAO,CAACS,KAAK,CAAC,GAAG,IAAI;QACrBT,OAAO,CAACC,KAAK,CAAC,CAAC,CAAC,CAACU,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;QAClEZ,OAAO,CAACa,OAAO,GAAGC,QAAQ,CAACC,QAAQ,CAACC,aAAa,IAAIf,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAClE;MACJ;IACJ;EACJ;EACA,OAAOD,OAAO;AAClB,CAAC;AACD;AACA,OAAO,IAAIiB,WAAW,GAAG,SAAAA,CAAUC,OAAO,EAAE;EACxC,OAAOA,OAAO,GAAGA,OAAO,CAACC,aAAa,IAAItB,MAAM,CAACkB,QAAQ,GAAGlB,MAAM,CAACkB,QAAQ;AAC/E,CAAC;AACD;AACA,OAAO,IAAIK,SAAS,GAAG,SAAAA,CAAUF,OAAO,EAAE;EACtC,IAAIH,QAAQ,GAAGE,WAAW,CAACC,OAAO,CAAC;EACnC,OAAOH,QAAQ,GACTA,QAAQ,CAACM,WAAW,IAAIxB,MAAM,GAC9BA,MAAM;AAChB,CAAC;AACD;AACA,OAAO,IAAIyB,cAAc,GAAG,SAAAA,CAAUJ,OAAO,EAAE;EAC3C,IAAIK,OAAO,GAAG;IAAEvB,OAAO,EAAEL,aAAa,CAAC;EAAE,CAAC;EAC1C,IAAIoB,QAAQ,GAAGE,WAAW,CAACC,OAAO,CAAC;EACnC,OAAQK,OAAO,CAACvB,OAAO,CAACG,IAAI,IAAIoB,OAAO,CAACvB,OAAO,CAACK,MAAM,GAAIU,QAAQ,CAACS,IAAI,GAAGT,QAAQ,CAACU,eAAe;AACtG,CAAC;AACD;AACA,OAAO,IAAIC,YAAY,GAAG,SAAAA,CAAUC,EAAE,EAAE;EACpC,IAAIA,EAAE,IAAIA,EAAE,CAACC,SAAS,IAAI,OAAQD,EAAE,CAACC,SAAU,KAAK,QAAQ,IAAID,EAAE,CAACC,SAAS,CAACC,OAAO,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,EAAE;IAC5G,OAAO,IAAI;EACf;EACA,IAAIC,QAAQ,GAAGjC,MAAM,CAACkC,gBAAgB,CAACJ,EAAE,EAAE,UAAU,CAAC,CAACG,QAAQ;EAC/D,OAAOA,QAAQ,CAACD,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAIC,QAAQ,CAACD,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC3E,CAAC;AACD;AACA,OAAO,IAAIG,mBAAmB,GAAG,SAAAA,CAAUL,EAAE,EAAE;EAC3C,IAAIM,IAAI,GAAGX,cAAc,CAACK,EAAE,CAAC;EAC7B,IAAI,CAACA,EAAE,IAAIA,EAAE,KAAKZ,QAAQ,CAACS,IAAI,IAAIG,EAAE,KAAKZ,QAAQ,CAACU,eAAe,EAAE;IAChE,OAAOQ,IAAI;EACf;EACA,IAAIC,MAAM,GAAGP,EAAE;EACf,OAAOO,MAAM,IACNA,MAAM,KAAKnB,QAAQ,CAACS,IAAI,IACxBU,MAAM,CAACC,QAAQ,KAAKC,IAAI,CAACC,sBAAsB,IAC/CH,MAAM,CAACC,QAAQ,KAAKC,IAAI,CAACpB,aAAa,IACtC,CAACU,YAAY,CAACQ,MAAM,CAAC,EAAE;IAC1BA,MAAM,GAAGA,MAAM,CAACI,UAAU;EAC9B;EACA,IAAIJ,MAAM,KAAKA,MAAM,KAAKnB,QAAQ,CAACS,IAAI,IAAIU,MAAM,CAACC,QAAQ,KAAKC,IAAI,CAACC,sBAAsB,CAAC,EAAE;IACzF,OAAOJ,IAAI;EACf;EACA,OAAOC,MAAM;AACjB,CAAC;AACD;AACA,OAAO,IAAIK,kBAAkB,GAAG,SAAAA,CAAUC,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAE;EAC5D,IAAIC,QAAQ,GAAG;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC;EAC7B,IAAIC,gBAAgB,GAAG,EAAE;EACzB,IAAIN,MAAM,GAAGE,IAAI,CAACK,IAAI,GAAGD,gBAAgB,EAAE;IACvCH,QAAQ,CAACC,CAAC,GAAG,EAAEE,gBAAgB,IAAIN,MAAM,GAAGE,IAAI,CAACK,IAAI,CAAC,CAAC;EAC3D,CAAC,MACI,IAAIL,IAAI,CAACM,KAAK,GAAGR,MAAM,GAAGM,gBAAgB,EAAE;IAC7CH,QAAQ,CAACC,CAAC,GAAGE,gBAAgB,IAAIJ,IAAI,CAACM,KAAK,GAAGR,MAAM,CAAC;EACzD;EACA,IAAIC,MAAM,GAAGC,IAAI,CAACO,GAAG,GAAGH,gBAAgB,EAAE;IACtCH,QAAQ,CAACE,CAAC,GAAG,EAAEC,gBAAgB,IAAIL,MAAM,GAAGC,IAAI,CAACO,GAAG,CAAC,CAAC;EAC1D,CAAC,MACI,IAAIP,IAAI,CAACQ,MAAM,GAAGT,MAAM,GAAGK,gBAAgB,EAAE;IAC9CH,QAAQ,CAACE,CAAC,GAAGC,gBAAgB,IAAIJ,IAAI,CAACQ,MAAM,GAAGT,MAAM,CAAC;EAC1D;EACA,OAAOE,QAAQ;AACnB,CAAC;AACD;AACA,OAAO,IAAIQ,kBAAkB,GAAG,SAAAA,CAAUxB,EAAE,EAAE9B,MAAM,EAAE;EAClD,IAAIoC,IAAI,GAAGX,cAAc,CAACK,EAAE,CAAC;EAC7B,IAAIA,EAAE,KAAKM,IAAI,EAAE;IACb,OAAO;MACHgB,GAAG,EAAEhB,IAAI,CAACmB,SAAS;MACnBL,IAAI,EAAEd,IAAI,CAACoB,UAAU;MACrBH,MAAM,EAAEjB,IAAI,CAACmB,SAAS,GAAGvD,MAAM,CAACyD,WAAW;MAC3CN,KAAK,EAAEf,IAAI,CAACoB,UAAU,GAAGxD,MAAM,CAAC0D;IACpC,CAAC;EACL,CAAC,MACI;IACD,IAAIb,IAAI,GAAGf,EAAE,CAAC6B,qBAAqB,CAAC,CAAC;IACrC,OAAO;MACHN,MAAM,EAAER,IAAI,CAACO,GAAG,GAAGP,IAAI,CAACe,MAAM;MAC9BT,KAAK,EAAEN,IAAI,CAACK,IAAI,GAAGL,IAAI,CAACgB,KAAK;MAC7BX,IAAI,EAAEL,IAAI,CAACK,IAAI;MACfE,GAAG,EAAEP,IAAI,CAACO;IACd,CAAC;EACL;AACJ,CAAC;AACD;AACA,OAAO,IAAIU,wBAAwB,GAAG,SAAAA,CAAUf,CAAC,EAAEC,CAAC,EAAEe,SAAS,EAAE;EAC7D,IAAIlB,IAAI,GAAGkB,SAAS,CAACJ,qBAAqB,CAAC,CAAC;EAC5C,OAAQd,IAAI,CAACO,GAAG,IAAIJ,CAAC,IACjBH,IAAI,CAACK,IAAI,IAAIH,CAAC,IACdC,CAAC,IAAIH,IAAI,CAACQ,MAAM,IAChBN,CAAC,IAAIF,IAAI,CAACM,KAAK;AACvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}