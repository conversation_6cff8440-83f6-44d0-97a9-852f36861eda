{"ast": null, "code": "/**\n * Enumeration which represents the week days.\n */\nexport var Day;\n(function (Day) {\n  /**\n   * The Sunday value with an underlying `0` number value.\n   */\n  Day[Day[\"Sunday\"] = 0] = \"Sunday\";\n  /**\n   * The Monday value with an underlying `1` number value.\n   */\n  Day[Day[\"Monday\"] = 1] = \"Monday\";\n  /**\n   * The Tuesday value with an underlying `2` number value.\n   */\n  Day[Day[\"Tuesday\"] = 2] = \"Tuesday\";\n  /**\n   * The Wednesday value with an underlying `3` number value.\n   */\n  Day[Day[\"Wednesday\"] = 3] = \"Wednesday\";\n  /**\n   * The Thursday value with an underlying `4` number value.\n   */\n  Day[Day[\"Thursday\"] = 4] = \"Thursday\";\n  /**\n   * The Friday value with an underlying `5` number value.\n   */\n  Day[Day[\"Friday\"] = 5] = \"Friday\";\n  /**\n   * The Saturday value with an underlying `6` number value.\n   */\n  Day[Day[\"Saturday\"] = 6] = \"Saturday\";\n})(Day || (Day = {}));", "map": {"version": 3, "names": ["Day"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/day.enum.js"], "sourcesContent": ["/**\n * Enumeration which represents the week days.\n */\nexport var Day;\n(function (Day) {\n    /**\n     * The Sunday value with an underlying `0` number value.\n     */\n    Day[Day[\"Sunday\"] = 0] = \"Sunday\";\n    /**\n     * The Monday value with an underlying `1` number value.\n     */\n    Day[Day[\"Monday\"] = 1] = \"Monday\";\n    /**\n     * The Tuesday value with an underlying `2` number value.\n     */\n    Day[Day[\"Tuesday\"] = 2] = \"Tuesday\";\n    /**\n     * The Wednesday value with an underlying `3` number value.\n     */\n    Day[Day[\"Wednesday\"] = 3] = \"Wednesday\";\n    /**\n     * The Thursday value with an underlying `4` number value.\n     */\n    Day[Day[\"Thursday\"] = 4] = \"Thursday\";\n    /**\n     * The Friday value with an underlying `5` number value.\n     */\n    Day[Day[\"Friday\"] = 5] = \"Friday\";\n    /**\n     * The Saturday value with an underlying `6` number value.\n     */\n    Day[Day[\"Saturday\"] = 6] = \"Saturday\";\n})(Day || (Day = {}));\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,IAAIA,GAAG;AACd,CAAC,UAAUA,GAAG,EAAE;EACZ;AACJ;AACA;EACIA,GAAG,CAACA,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EACjC;AACJ;AACA;EACIA,GAAG,CAACA,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EACjC;AACJ;AACA;EACIA,GAAG,CAACA,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACnC;AACJ;AACA;EACIA,GAAG,CAACA,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EACvC;AACJ;AACA;EACIA,GAAG,CAACA,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;EACrC;AACJ;AACA;EACIA,GAAG,CAACA,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EACjC;AACJ;AACA;EACIA,GAAG,CAACA,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;AACzC,CAAC,EAAEA,GAAG,KAAKA,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}