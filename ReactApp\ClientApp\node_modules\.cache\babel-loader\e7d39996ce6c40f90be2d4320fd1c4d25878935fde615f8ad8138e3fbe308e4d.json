{"ast": null, "code": "import \"antd/es/skeleton/style\";\nimport _Skeleton from \"antd/es/skeleton\";\nvar _jsxFileName = \"D:\\\\Zone24x7\\\\Workspaces\\\\FrontEnd-Portal\\\\ReactApp\\\\ClientApp\\\\src\\\\app\\\\utils\\\\oktaAuthClient\\\\PrivateRoute.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { useOktaAuth } from '@okta/okta-react';\nimport { toRelativeUrl } from '@okta/okta-auth-js';\nimport { Outlet, useLocation, useNavigate } from 'react-router-dom';\nimport { selectAppInitializing } from '@app/appSlice';\nimport { useAppDispatch, useAppSelector } from '@app/hooks/useAppSelector';\nimport { useEvaluateTncQuery } from '@app/api/tncApiSlice';\nimport { TncStatus } from '@app/types/tncTypes';\nimport { Fragment as _Fragment, jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport function PrivateRoute() {\n  _s();\n  const {\n    tenant,\n    endPoints\n  } = useAppSelector(selectAppInitializing);\n  const {\n    oktaAuth,\n    authState\n  } = useOktaAuth();\n  const dispatch = useAppDispatch();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const isTncValidated = useAppSelector(state => state.app.tnc.isValidated);\n  const {\n    data: tncData,\n    isLoading: tncLoading,\n    error: tncError\n  } = useEvaluateTncQuery(undefined, {\n    skip: !(authState !== null && authState !== void 0 && authState.isAuthenticated) || !endPoints || isTncValidated\n  });\n  useEffect(() => {\n    if (!authState) {\n      return;\n    }\n    if (!(authState !== null && authState !== void 0 && authState.isAuthenticated)) {\n      const originalUri = toRelativeUrl(window.location.href, window.location.origin);\n      oktaAuth.setOriginalUri(originalUri);\n      oktaAuth.signInWithRedirect();\n    }\n  }, [oktaAuth, !!authState, authState === null || authState === void 0 ? void 0 : authState.isAuthenticated]);\n  useEffect(() => {\n    if (authState !== null && authState !== void 0 && authState.isAuthenticated && !isTncValidated && !tncLoading && !tncError && tncData) {\n      if (tncData.status === TncStatus.PENDING_ACCEPTANCE) {\n        if (location.pathname !== '/termsAndConditions') {\n          navigate('/termsAndConditions', {\n            state: {\n              from: location.pathname\n            }\n          });\n        }\n      }\n    }\n  }, [authState === null || authState === void 0 ? void 0 : authState.isAuthenticated, tncData, tncLoading, tncError, dispatch, navigate, location.pathname, isTncValidated]);\n  if (!authState || !(authState !== null && authState !== void 0 && authState.isAuthenticated) || !tenant) {\n    return /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false);\n  }\n  if (!endPoints || tncLoading || !isTncValidated) {\n    return /*#__PURE__*/_jsxDEV(_Skeleton, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 10\n  }, this);\n}\n_s(PrivateRoute, \"xBtjj8EOavOHd11WI/V7qrWj1nA=\", false, function () {\n  return [useAppSelector, useOktaAuth, useAppDispatch, useNavigate, useLocation, useAppSelector, useEvaluateTncQuery];\n});\n_c = PrivateRoute;\nvar _c;\n$RefreshReg$(_c, \"PrivateRoute\");", "map": {"version": 3, "names": ["React", "useEffect", "useOktaAuth", "toRelativeUrl", "Outlet", "useLocation", "useNavigate", "selectAppInitializing", "useAppDispatch", "useAppSelector", "useEvaluateTncQuery", "TncStatus", "Fragment", "_Fragment", "jsxDEV", "_jsxDEV", "PrivateRoute", "_s", "tenant", "endPoints", "oktaAuth", "authState", "dispatch", "navigate", "location", "isTncValidated", "state", "app", "tnc", "isValidated", "data", "tncData", "isLoading", "tncLoading", "error", "tncError", "undefined", "skip", "isAuthenticated", "originalUri", "window", "href", "origin", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "signInWithRedirect", "status", "PENDING_ACCEPTANCE", "pathname", "from", "_Skeleton", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/src/app/utils/oktaAuthClient/PrivateRoute.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\r\nimport { useOktaAuth } from '@okta/okta-react';\r\nimport { toRelativeUrl } from '@okta/okta-auth-js';\r\nimport { Skeleton } from 'antd';\r\nimport { Outlet, useLocation, useNavigate } from 'react-router-dom';\r\nimport { selectAppInitializing } from '@app/appSlice';\r\nimport { useAppDispatch, useAppSelector } from '@app/hooks/useAppSelector';\r\nimport { useEvaluateTncQuery } from '@app/api/tncApiSlice';\r\nimport { TncStatus } from '@app/types/tncTypes';\r\n\r\nexport function PrivateRoute() {\r\n  const { tenant, endPoints } = useAppSelector(selectAppInitializing);\r\n  const { oktaAuth, authState } = useOktaAuth();\r\n  const dispatch = useAppDispatch();\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n\r\n  const isTncValidated = useAppSelector((state) => state.app.tnc.isValidated);\r\n\r\n  const { data: tncData, isLoading: tncLoading, error: tncError } = useEvaluateTncQuery(undefined, {\r\n    skip: !authState?.isAuthenticated || !endPoints || isTncValidated,\r\n  });\r\n\r\n  useEffect(() => {\r\n    if (!authState) {\r\n      return;\r\n    }\r\n\r\n    if (!authState?.isAuthenticated) {\r\n      const originalUri = toRelativeUrl(window.location.href, window.location.origin);\r\n      oktaAuth.setOriginalUri(originalUri);\r\n      oktaAuth.signInWithRedirect();\r\n    }\r\n  }, [oktaAuth, !!authState, authState?.isAuthenticated]);\r\n\r\n  useEffect(() => {\r\n    if (authState?.isAuthenticated && !isTncValidated && !tncLoading && !tncError && tncData) {\r\n      if (tncData.status === TncStatus.PENDING_ACCEPTANCE) {\r\n        if (location.pathname !== '/termsAndConditions') {\r\n          navigate('/termsAndConditions', { state: { from: location.pathname } });\r\n        }\r\n      }\r\n    }\r\n  }, [authState?.isAuthenticated, tncData, tncLoading, tncError, dispatch, navigate, location.pathname, isTncValidated]);\r\n\r\n  if (!authState || !authState?.isAuthenticated || !tenant) {\r\n    return <></>;\r\n  }\r\n\r\n  if (!endPoints || tncLoading || !isTncValidated) {\r\n    return <Skeleton />;\r\n  }\r\n\r\n  return <Outlet />;\r\n}\r\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,aAAa,QAAQ,oBAAoB;AAElD,SAASC,MAAM,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACnE,SAASC,qBAAqB,QAAQ,eAAe;AACrD,SAASC,cAAc,EAAEC,cAAc,QAAQ,2BAA2B;AAC1E,SAASC,mBAAmB,QAAQ,sBAAsB;AAC1D,SAASC,SAAS,QAAQ,qBAAqB;AAAC,SAAAC,QAAA,IAAAC,SAAA,EAAAC,MAAA,IAAAC,OAAA;AAEhD,OAAO,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EAC7B,MAAM;IAAEC,MAAM;IAAEC;EAAU,CAAC,GAAGV,cAAc,CAACF,qBAAqB,CAAC;EACnE,MAAM;IAAEa,QAAQ;IAAEC;EAAU,CAAC,GAAGnB,WAAW,CAAC,CAAC;EAC7C,MAAMoB,QAAQ,GAAGd,cAAc,CAAC,CAAC;EACjC,MAAMe,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAMkB,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAE9B,MAAMoB,cAAc,GAAGhB,cAAc,CAAEiB,KAAK,IAAKA,KAAK,CAACC,GAAG,CAACC,GAAG,CAACC,WAAW,CAAC;EAE3E,MAAM;IAAEC,IAAI,EAAEC,OAAO;IAAEC,SAAS,EAAEC,UAAU;IAAEC,KAAK,EAAEC;EAAS,CAAC,GAAGzB,mBAAmB,CAAC0B,SAAS,EAAE;IAC/FC,IAAI,EAAE,EAAChB,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEiB,eAAe,KAAI,CAACnB,SAAS,IAAIM;EACrD,CAAC,CAAC;EAEFxB,SAAS,CAAC,MAAM;IACd,IAAI,CAACoB,SAAS,EAAE;MACd;IACF;IAEA,IAAI,EAACA,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEiB,eAAe,GAAE;MAC/B,MAAMC,WAAW,GAAGpC,aAAa,CAACqC,MAAM,CAAChB,QAAQ,CAACiB,IAAI,EAAED,MAAM,CAAChB,QAAQ,CAACkB,MAAM,CAAC;MAC/EtB,QAAQ,CAACuB,cAAc,CAACJ,WAAW,CAAC;MACpCnB,QAAQ,CAACwB,kBAAkB,CAAC,CAAC;IAC/B;EACF,CAAC,EAAE,CAACxB,QAAQ,EAAE,CAAC,CAACC,SAAS,EAAEA,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEiB,eAAe,CAAC,CAAC;EAEvDrC,SAAS,CAAC,MAAM;IACd,IAAIoB,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEiB,eAAe,IAAI,CAACb,cAAc,IAAI,CAACQ,UAAU,IAAI,CAACE,QAAQ,IAAIJ,OAAO,EAAE;MACxF,IAAIA,OAAO,CAACc,MAAM,KAAKlC,SAAS,CAACmC,kBAAkB,EAAE;QACnD,IAAItB,QAAQ,CAACuB,QAAQ,KAAK,qBAAqB,EAAE;UAC/CxB,QAAQ,CAAC,qBAAqB,EAAE;YAAEG,KAAK,EAAE;cAAEsB,IAAI,EAAExB,QAAQ,CAACuB;YAAS;UAAE,CAAC,CAAC;QACzE;MACF;IACF;EACF,CAAC,EAAE,CAAC1B,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEiB,eAAe,EAAEP,OAAO,EAAEE,UAAU,EAAEE,QAAQ,EAAEb,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,CAACuB,QAAQ,EAAEtB,cAAc,CAAC,CAAC;EAEtH,IAAI,CAACJ,SAAS,IAAI,EAACA,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEiB,eAAe,KAAI,CAACpB,MAAM,EAAE;IACxD,oBAAOH,OAAA,CAAAF,SAAA,mBAAI,CAAC;EACd;EAEA,IAAI,CAACM,SAAS,IAAIc,UAAU,IAAI,CAACR,cAAc,EAAE;IAC/C,oBAAOV,OAAA,CAAAkC,SAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAW,CAAC;EACrB;EAEA,oBAAOtC,OAAA,CAACX,MAAM;IAAA8C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACnB;AAACpC,EAAA,CA5CeD,YAAY;EAAA,QACIP,cAAc,EACZP,WAAW,EAC1BM,cAAc,EACdF,WAAW,EACXD,WAAW,EAELI,cAAc,EAE6BC,mBAAmB;AAAA;AAAA4C,EAAA,GATvEtC,YAAY;AAAA,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}