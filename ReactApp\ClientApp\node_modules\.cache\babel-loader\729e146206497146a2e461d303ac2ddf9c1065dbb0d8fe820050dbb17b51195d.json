{"ast": null, "code": "import PathNode from './path-node';\nimport { createPromise } from '../util';\nvar ImageNode = function (PathNode) {\n  function ImageNode(srcElement, cors) {\n    PathNode.call(this, srcElement);\n    this.onLoad = this.onLoad.bind(this);\n    this.onError = this.onError.bind(this);\n    this.loading = createPromise();\n    var img = this.img = new Image();\n    var src = srcElement.src();\n    if (cors && !/^data:/i.test(src)) {\n      img.crossOrigin = cors;\n    }\n    if (src) {\n      img.src = src;\n    }\n    if (img.complete) {\n      this.onLoad();\n    } else {\n      img.onload = this.onLoad;\n      img.onerror = this.onError;\n    }\n  }\n  if (PathNode) ImageNode.__proto__ = PathNode;\n  ImageNode.prototype = Object.create(PathNode && PathNode.prototype);\n  ImageNode.prototype.constructor = ImageNode;\n  ImageNode.prototype.renderTo = function renderTo(ctx) {\n    if (this.loading.state() === \"resolved\") {\n      ctx.save();\n      this.setTransform(ctx);\n      this.setClip(ctx);\n      this.drawImage(ctx);\n      ctx.restore();\n    }\n  };\n  ImageNode.prototype.optionsChange = function optionsChange(e) {\n    if (e.field === \"src\") {\n      this.loading = createPromise();\n      this.img.src = this.srcElement.src();\n    } else {\n      PathNode.prototype.optionsChange.call(this, e);\n    }\n  };\n  ImageNode.prototype.onLoad = function onLoad() {\n    this.loading.resolve();\n    this.invalidate();\n  };\n  ImageNode.prototype.onError = function onError() {\n    this.loading.reject(new Error(\"Unable to load image '\" + this.img.src + \"'. Check for connectivity and verify CORS headers.\"));\n  };\n  ImageNode.prototype.drawImage = function drawImage(ctx) {\n    var rect = this.srcElement.rect();\n    var topLeft = rect.topLeft();\n    ctx.drawImage(this.img, topLeft.x, topLeft.y, rect.width(), rect.height());\n  };\n  return ImageNode;\n}(PathNode);\nexport default ImageNode;", "map": {"version": 3, "names": ["PathNode", "createPromise", "ImageNode", "srcElement", "cors", "call", "onLoad", "bind", "onError", "loading", "img", "Image", "src", "test", "crossOrigin", "complete", "onload", "onerror", "__proto__", "prototype", "Object", "create", "constructor", "renderTo", "ctx", "state", "save", "setTransform", "setClip", "drawImage", "restore", "optionsChange", "e", "field", "resolve", "invalidate", "reject", "Error", "rect", "topLeft", "x", "y", "width", "height"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/canvas/image-node.js"], "sourcesContent": ["import PathNode from './path-node';\nimport { createPromise } from '../util';\n\nvar ImageNode = (function (PathNode) {\n    function ImageNode(srcElement, cors) {\n        PathNode.call(this, srcElement);\n\n        this.onLoad = this.onLoad.bind(this);\n        this.onError = this.onError.bind(this);\n\n        this.loading = createPromise();\n\n        var img = this.img = new Image();\n        var src = srcElement.src();\n\n        if (cors && !(/^data:/i.test(src))) {\n            img.crossOrigin = cors;\n        }\n\n        if (src) {\n            img.src = src;\n        }\n\n        if (img.complete) {\n            this.onLoad();\n        } else {\n            img.onload = this.onLoad;\n            img.onerror = this.onError;\n        }\n    }\n\n    if ( PathNode ) ImageNode.__proto__ = PathNode;\n    ImageNode.prototype = Object.create( PathNode && PathNode.prototype );\n    ImageNode.prototype.constructor = ImageNode;\n\n    ImageNode.prototype.renderTo = function renderTo (ctx) {\n        if (this.loading.state() === \"resolved\") {\n            ctx.save();\n\n            this.setTransform(ctx);\n            this.setClip(ctx);\n\n            this.drawImage(ctx);\n\n            ctx.restore();\n        }\n    };\n\n    ImageNode.prototype.optionsChange = function optionsChange (e) {\n        if (e.field === \"src\") {\n            this.loading = createPromise();\n            this.img.src = this.srcElement.src();\n        } else {\n            PathNode.prototype.optionsChange.call(this, e);\n        }\n    };\n\n    ImageNode.prototype.onLoad = function onLoad () {\n        this.loading.resolve();\n        this.invalidate();\n    };\n\n    ImageNode.prototype.onError = function onError () {\n        this.loading.reject(new Error(\n            \"Unable to load image '\" + this.img.src +\n            \"'. Check for connectivity and verify CORS headers.\"\n        ));\n    };\n\n    ImageNode.prototype.drawImage = function drawImage (ctx) {\n        var rect = this.srcElement.rect();\n        var topLeft = rect.topLeft();\n\n        ctx.drawImage(\n            this.img, topLeft.x, topLeft.y, rect.width(), rect.height()\n        );\n    };\n\n    return ImageNode;\n}(PathNode));\n\nexport default ImageNode;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,aAAa;AAClC,SAASC,aAAa,QAAQ,SAAS;AAEvC,IAAIC,SAAS,GAAI,UAAUF,QAAQ,EAAE;EACjC,SAASE,SAASA,CAACC,UAAU,EAAEC,IAAI,EAAE;IACjCJ,QAAQ,CAACK,IAAI,CAAC,IAAI,EAAEF,UAAU,CAAC;IAE/B,IAAI,CAACG,MAAM,GAAG,IAAI,CAACA,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACC,OAAO,GAAG,IAAI,CAACA,OAAO,CAACD,IAAI,CAAC,IAAI,CAAC;IAEtC,IAAI,CAACE,OAAO,GAAGR,aAAa,CAAC,CAAC;IAE9B,IAAIS,GAAG,GAAG,IAAI,CAACA,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IAChC,IAAIC,GAAG,GAAGT,UAAU,CAACS,GAAG,CAAC,CAAC;IAE1B,IAAIR,IAAI,IAAI,CAAE,SAAS,CAACS,IAAI,CAACD,GAAG,CAAE,EAAE;MAChCF,GAAG,CAACI,WAAW,GAAGV,IAAI;IAC1B;IAEA,IAAIQ,GAAG,EAAE;MACLF,GAAG,CAACE,GAAG,GAAGA,GAAG;IACjB;IAEA,IAAIF,GAAG,CAACK,QAAQ,EAAE;MACd,IAAI,CAACT,MAAM,CAAC,CAAC;IACjB,CAAC,MAAM;MACHI,GAAG,CAACM,MAAM,GAAG,IAAI,CAACV,MAAM;MACxBI,GAAG,CAACO,OAAO,GAAG,IAAI,CAACT,OAAO;IAC9B;EACJ;EAEA,IAAKR,QAAQ,EAAGE,SAAS,CAACgB,SAAS,GAAGlB,QAAQ;EAC9CE,SAAS,CAACiB,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAErB,QAAQ,IAAIA,QAAQ,CAACmB,SAAU,CAAC;EACrEjB,SAAS,CAACiB,SAAS,CAACG,WAAW,GAAGpB,SAAS;EAE3CA,SAAS,CAACiB,SAAS,CAACI,QAAQ,GAAG,SAASA,QAAQA,CAAEC,GAAG,EAAE;IACnD,IAAI,IAAI,CAACf,OAAO,CAACgB,KAAK,CAAC,CAAC,KAAK,UAAU,EAAE;MACrCD,GAAG,CAACE,IAAI,CAAC,CAAC;MAEV,IAAI,CAACC,YAAY,CAACH,GAAG,CAAC;MACtB,IAAI,CAACI,OAAO,CAACJ,GAAG,CAAC;MAEjB,IAAI,CAACK,SAAS,CAACL,GAAG,CAAC;MAEnBA,GAAG,CAACM,OAAO,CAAC,CAAC;IACjB;EACJ,CAAC;EAED5B,SAAS,CAACiB,SAAS,CAACY,aAAa,GAAG,SAASA,aAAaA,CAAEC,CAAC,EAAE;IAC3D,IAAIA,CAAC,CAACC,KAAK,KAAK,KAAK,EAAE;MACnB,IAAI,CAACxB,OAAO,GAAGR,aAAa,CAAC,CAAC;MAC9B,IAAI,CAACS,GAAG,CAACE,GAAG,GAAG,IAAI,CAACT,UAAU,CAACS,GAAG,CAAC,CAAC;IACxC,CAAC,MAAM;MACHZ,QAAQ,CAACmB,SAAS,CAACY,aAAa,CAAC1B,IAAI,CAAC,IAAI,EAAE2B,CAAC,CAAC;IAClD;EACJ,CAAC;EAED9B,SAAS,CAACiB,SAAS,CAACb,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAI;IAC5C,IAAI,CAACG,OAAO,CAACyB,OAAO,CAAC,CAAC;IACtB,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB,CAAC;EAEDjC,SAAS,CAACiB,SAAS,CAACX,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;IAC9C,IAAI,CAACC,OAAO,CAAC2B,MAAM,CAAC,IAAIC,KAAK,CACzB,wBAAwB,GAAG,IAAI,CAAC3B,GAAG,CAACE,GAAG,GACvC,oDACJ,CAAC,CAAC;EACN,CAAC;EAEDV,SAAS,CAACiB,SAAS,CAACU,SAAS,GAAG,SAASA,SAASA,CAAEL,GAAG,EAAE;IACrD,IAAIc,IAAI,GAAG,IAAI,CAACnC,UAAU,CAACmC,IAAI,CAAC,CAAC;IACjC,IAAIC,OAAO,GAAGD,IAAI,CAACC,OAAO,CAAC,CAAC;IAE5Bf,GAAG,CAACK,SAAS,CACT,IAAI,CAACnB,GAAG,EAAE6B,OAAO,CAACC,CAAC,EAAED,OAAO,CAACE,CAAC,EAAEH,IAAI,CAACI,KAAK,CAAC,CAAC,EAAEJ,IAAI,CAACK,MAAM,CAAC,CAC9D,CAAC;EACL,CAAC;EAED,OAAOzC,SAAS;AACpB,CAAC,CAACF,QAAQ,CAAE;AAEZ,eAAeE,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}