{"ast": null, "code": "import ElementsArray from './elements-array';\nvar GeometryElementsArray = function (ElementsArray) {\n  function GeometryElementsArray() {\n    ElementsArray.apply(this, arguments);\n  }\n  if (ElementsArray) GeometryElementsArray.__proto__ = ElementsArray;\n  GeometryElementsArray.prototype = Object.create(ElementsArray && ElementsArray.prototype);\n  GeometryElementsArray.prototype.constructor = GeometryElementsArray;\n  GeometryElementsArray.prototype._change = function _change() {\n    this.geometryChange();\n  };\n  return GeometryElementsArray;\n}(ElementsArray);\nexport default GeometryElementsArray;", "map": {"version": 3, "names": ["ElementsArray", "GeometryElementsArray", "apply", "arguments", "__proto__", "prototype", "Object", "create", "constructor", "_change", "geometryChange"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/shapes/geometry-elements-array.js"], "sourcesContent": ["import ElementsArray from './elements-array';\n\nvar GeometryElementsArray = (function (ElementsArray) {\n    function GeometryElementsArray () {\n        ElementsArray.apply(this, arguments);\n    }\n\n    if ( ElementsArray ) GeometryElementsArray.__proto__ = ElementsArray;\n    GeometryElementsArray.prototype = Object.create( ElementsArray && ElementsArray.prototype );\n    GeometryElementsArray.prototype.constructor = GeometryElementsArray;\n\n    GeometryElementsArray.prototype._change = function _change () {\n        this.geometryChange();\n    };\n\n    return GeometryElementsArray;\n}(ElementsArray));\n\nexport default GeometryElementsArray;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,kBAAkB;AAE5C,IAAIC,qBAAqB,GAAI,UAAUD,aAAa,EAAE;EAClD,SAASC,qBAAqBA,CAAA,EAAI;IAC9BD,aAAa,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACxC;EAEA,IAAKH,aAAa,EAAGC,qBAAqB,CAACG,SAAS,GAAGJ,aAAa;EACpEC,qBAAqB,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAEP,aAAa,IAAIA,aAAa,CAACK,SAAU,CAAC;EAC3FJ,qBAAqB,CAACI,SAAS,CAACG,WAAW,GAAGP,qBAAqB;EAEnEA,qBAAqB,CAACI,SAAS,CAACI,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;IAC1D,IAAI,CAACC,cAAc,CAAC,CAAC;EACzB,CAAC;EAED,OAAOT,qBAAqB;AAChC,CAAC,CAACD,aAAa,CAAE;AAEjB,eAAeC,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}