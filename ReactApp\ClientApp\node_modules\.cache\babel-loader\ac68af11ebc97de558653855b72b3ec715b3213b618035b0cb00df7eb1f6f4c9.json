{"ast": null, "code": "import stackElements from './stack-elements';\nimport createStackElements from './create-stack-elements';\nexport default function vStack(elements) {\n  stackElements(createStackElements(elements), \"y\", \"x\", \"height\");\n}", "map": {"version": 3, "names": ["stackElements", "createStackElements", "vStack", "elements"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/alignment/v-stack.js"], "sourcesContent": ["import stackElements from './stack-elements';\nimport createStackElements from './create-stack-elements';\n\nexport default function vStack(elements) {\n    stackElements(createStackElements(elements), \"y\", \"x\", \"height\");\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,mBAAmB,MAAM,yBAAyB;AAEzD,eAAe,SAASC,MAAMA,CAACC,QAAQ,EAAE;EACrCH,aAAa,CAACC,mBAAmB,CAACE,QAAQ,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,CAAC;AACpE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}