{"ast": null, "code": "import { TreeNode } from 'rc-tree';\nimport TreePure from './Tree';\nimport DirectoryTree from './DirectoryTree';\nvar Tree = TreePure;\nTree.DirectoryTree = DirectoryTree;\nTree.TreeNode = TreeNode;\nexport default Tree;", "map": {"version": 3, "names": ["TreeNode", "TreePure", "DirectoryTree", "Tree"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/antd/es/tree/index.js"], "sourcesContent": ["import { TreeNode } from 'rc-tree';\nimport TreePure from './Tree';\nimport DirectoryTree from './DirectoryTree';\nvar Tree = TreePure;\nTree.DirectoryTree = DirectoryTree;\nTree.TreeNode = TreeNode;\nexport default Tree;"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,SAAS;AAClC,OAAOC,QAAQ,MAAM,QAAQ;AAC7B,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,IAAIC,IAAI,GAAGF,QAAQ;AACnBE,IAAI,CAACD,aAAa,GAAGA,aAAa;AAClCC,IAAI,CAACH,QAAQ,GAAGA,QAAQ;AACxB,eAAeG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}