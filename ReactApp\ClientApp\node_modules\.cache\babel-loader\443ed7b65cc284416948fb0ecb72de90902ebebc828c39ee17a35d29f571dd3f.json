{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as a from \"react\";\nimport r from \"prop-types\";\nimport { classNames as t } from \"@progress/kendo-react-common\";\nconst m = e => {\n  const s = {\n    src: \"\",\n    ...e\n  };\n  return /* @__PURE__ */a.createElement(\"img\", {\n    style: s.style,\n    className: t(\"k-card-media\", s.className),\n    src: s.src,\n    alt: s.alt\n  });\n};\nm.propTypes = {\n  className: r.string,\n  src: r.string\n};\nexport { m as CardImage };", "map": {"version": 3, "names": ["a", "r", "classNames", "t", "m", "e", "s", "src", "createElement", "style", "className", "alt", "propTypes", "string", "CardImage"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/card/CardImage.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as a from \"react\";\nimport r from \"prop-types\";\nimport { classNames as t } from \"@progress/kendo-react-common\";\nconst m = (e) => {\n  const s = {\n    src: \"\",\n    ...e\n  };\n  return /* @__PURE__ */ a.createElement(\n    \"img\",\n    {\n      style: s.style,\n      className: t(\"k-card-media\", s.className),\n      src: s.src,\n      alt: s.alt\n    }\n  );\n};\nm.propTypes = {\n  className: r.string,\n  src: r.string\n};\nexport {\n  m as CardImage\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AAC9D,MAAMC,CAAC,GAAIC,CAAC,IAAK;EACf,MAAMC,CAAC,GAAG;IACRC,GAAG,EAAE,EAAE;IACP,GAAGF;EACL,CAAC;EACD,OAAO,eAAgBL,CAAC,CAACQ,aAAa,CACpC,KAAK,EACL;IACEC,KAAK,EAAEH,CAAC,CAACG,KAAK;IACdC,SAAS,EAAEP,CAAC,CAAC,cAAc,EAAEG,CAAC,CAACI,SAAS,CAAC;IACzCH,GAAG,EAAED,CAAC,CAACC,GAAG;IACVI,GAAG,EAAEL,CAAC,CAACK;EACT,CACF,CAAC;AACH,CAAC;AACDP,CAAC,CAACQ,SAAS,GAAG;EACZF,SAAS,EAAET,CAAC,CAACY,MAAM;EACnBN,GAAG,EAAEN,CAAC,CAACY;AACT,CAAC;AACD,SACET,CAAC,IAAIU,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}