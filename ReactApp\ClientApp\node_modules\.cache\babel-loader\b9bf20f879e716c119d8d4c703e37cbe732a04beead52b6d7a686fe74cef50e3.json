{"ast": null, "code": "import translateToPoint from './translate-to-point';\nimport stackElements from './stack-elements';\nfunction getStacks(elements, rect, sizeField) {\n  var maxSize = rect.size[sizeField];\n  var stacks = [];\n  var stack = [];\n  var stackSize = 0;\n  var element, bbox;\n  var addElementToStack = function () {\n    stack.push({\n      element: element,\n      bbox: bbox\n    });\n  };\n  for (var idx = 0; idx < elements.length; idx++) {\n    element = elements[idx];\n    bbox = element.clippedBBox();\n    if (bbox) {\n      var size = bbox.size[sizeField];\n      if (stackSize + size > maxSize) {\n        if (stack.length) {\n          stacks.push(stack);\n          stack = [];\n          addElementToStack();\n          stackSize = size;\n        } else {\n          addElementToStack();\n          stacks.push(stack);\n          stack = [];\n          stackSize = 0;\n        }\n      } else {\n        addElementToStack();\n        stackSize += size;\n      }\n    }\n  }\n  if (stack.length) {\n    stacks.push(stack);\n  }\n  return stacks;\n}\nexport default function wrapElements(elements, rect, axis, otherAxis, sizeField) {\n  var stacks = getStacks(elements, rect, sizeField);\n  var origin = rect.origin.clone();\n  var result = [];\n  for (var idx = 0; idx < stacks.length; idx++) {\n    var stack = stacks[idx];\n    var startElement = stack[0];\n    origin[otherAxis] = startElement.bbox.origin[otherAxis];\n    translateToPoint(origin, startElement.bbox, startElement.element);\n    startElement.bbox.origin[axis] = origin[axis];\n    stackElements(stack, axis, otherAxis, sizeField);\n    result.push([]);\n    for (var elementIdx = 0; elementIdx < stack.length; elementIdx++) {\n      result[idx].push(stack[elementIdx].element);\n    }\n  }\n  return result;\n}", "map": {"version": 3, "names": ["translateToPoint", "stackElements", "getStacks", "elements", "rect", "sizeField", "maxSize", "size", "stacks", "stack", "stackSize", "element", "bbox", "addElementToStack", "push", "idx", "length", "clippedBBox", "wrapElements", "axis", "otherAxis", "origin", "clone", "result", "startElement", "elementIdx"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/alignment/wrap-elements.js"], "sourcesContent": ["import translateToPoint from './translate-to-point';\nimport stackElements from './stack-elements';\n\nfunction getStacks(elements, rect, sizeField) {\n    var maxSize = rect.size[sizeField];\n    var stacks = [];\n    var stack = [];\n    var stackSize = 0;\n    var element, bbox;\n\n    var addElementToStack = function() {\n        stack.push({\n            element: element,\n            bbox: bbox\n        });\n    };\n\n    for (var idx = 0; idx < elements.length; idx++) {\n        element = elements[idx];\n\n        bbox = element.clippedBBox();\n        if (bbox) {\n            var size = bbox.size[sizeField];\n            if (stackSize + size > maxSize) {\n                if (stack.length) {\n                    stacks.push(stack);\n                    stack = [];\n                    addElementToStack();\n                    stackSize = size;\n                } else {\n                    addElementToStack();\n                    stacks.push(stack);\n                    stack = [];\n                    stackSize = 0;\n                }\n            } else {\n                addElementToStack();\n                stackSize += size;\n            }\n        }\n    }\n\n    if (stack.length) {\n        stacks.push(stack);\n    }\n\n    return stacks;\n}\n\nexport default function wrapElements(elements, rect, axis, otherAxis, sizeField) {\n    var stacks = getStacks(elements, rect, sizeField);\n    var origin = rect.origin.clone();\n    var result = [];\n\n    for (var idx = 0; idx < stacks.length; idx++) {\n        var stack = stacks[idx];\n        var startElement = stack[0];\n        origin[otherAxis] = startElement.bbox.origin[otherAxis];\n        translateToPoint(origin, startElement.bbox, startElement.element);\n        startElement.bbox.origin[axis] = origin[axis];\n        stackElements(stack, axis, otherAxis, sizeField);\n        result.push([]);\n        for (var elementIdx = 0; elementIdx < stack.length; elementIdx++) {\n            result[idx].push(stack[elementIdx].element);\n        }\n    }\n    return result;\n}"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,sBAAsB;AACnD,OAAOC,aAAa,MAAM,kBAAkB;AAE5C,SAASC,SAASA,CAACC,QAAQ,EAAEC,IAAI,EAAEC,SAAS,EAAE;EAC1C,IAAIC,OAAO,GAAGF,IAAI,CAACG,IAAI,CAACF,SAAS,CAAC;EAClC,IAAIG,MAAM,GAAG,EAAE;EACf,IAAIC,KAAK,GAAG,EAAE;EACd,IAAIC,SAAS,GAAG,CAAC;EACjB,IAAIC,OAAO,EAAEC,IAAI;EAEjB,IAAIC,iBAAiB,GAAG,SAAAA,CAAA,EAAW;IAC/BJ,KAAK,CAACK,IAAI,CAAC;MACPH,OAAO,EAAEA,OAAO;MAChBC,IAAI,EAAEA;IACV,CAAC,CAAC;EACN,CAAC;EAED,KAAK,IAAIG,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGZ,QAAQ,CAACa,MAAM,EAAED,GAAG,EAAE,EAAE;IAC5CJ,OAAO,GAAGR,QAAQ,CAACY,GAAG,CAAC;IAEvBH,IAAI,GAAGD,OAAO,CAACM,WAAW,CAAC,CAAC;IAC5B,IAAIL,IAAI,EAAE;MACN,IAAIL,IAAI,GAAGK,IAAI,CAACL,IAAI,CAACF,SAAS,CAAC;MAC/B,IAAIK,SAAS,GAAGH,IAAI,GAAGD,OAAO,EAAE;QAC5B,IAAIG,KAAK,CAACO,MAAM,EAAE;UACdR,MAAM,CAACM,IAAI,CAACL,KAAK,CAAC;UAClBA,KAAK,GAAG,EAAE;UACVI,iBAAiB,CAAC,CAAC;UACnBH,SAAS,GAAGH,IAAI;QACpB,CAAC,MAAM;UACHM,iBAAiB,CAAC,CAAC;UACnBL,MAAM,CAACM,IAAI,CAACL,KAAK,CAAC;UAClBA,KAAK,GAAG,EAAE;UACVC,SAAS,GAAG,CAAC;QACjB;MACJ,CAAC,MAAM;QACHG,iBAAiB,CAAC,CAAC;QACnBH,SAAS,IAAIH,IAAI;MACrB;IACJ;EACJ;EAEA,IAAIE,KAAK,CAACO,MAAM,EAAE;IACdR,MAAM,CAACM,IAAI,CAACL,KAAK,CAAC;EACtB;EAEA,OAAOD,MAAM;AACjB;AAEA,eAAe,SAASU,YAAYA,CAACf,QAAQ,EAAEC,IAAI,EAAEe,IAAI,EAAEC,SAAS,EAAEf,SAAS,EAAE;EAC7E,IAAIG,MAAM,GAAGN,SAAS,CAACC,QAAQ,EAAEC,IAAI,EAAEC,SAAS,CAAC;EACjD,IAAIgB,MAAM,GAAGjB,IAAI,CAACiB,MAAM,CAACC,KAAK,CAAC,CAAC;EAChC,IAAIC,MAAM,GAAG,EAAE;EAEf,KAAK,IAAIR,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGP,MAAM,CAACQ,MAAM,EAAED,GAAG,EAAE,EAAE;IAC1C,IAAIN,KAAK,GAAGD,MAAM,CAACO,GAAG,CAAC;IACvB,IAAIS,YAAY,GAAGf,KAAK,CAAC,CAAC,CAAC;IAC3BY,MAAM,CAACD,SAAS,CAAC,GAAGI,YAAY,CAACZ,IAAI,CAACS,MAAM,CAACD,SAAS,CAAC;IACvDpB,gBAAgB,CAACqB,MAAM,EAAEG,YAAY,CAACZ,IAAI,EAAEY,YAAY,CAACb,OAAO,CAAC;IACjEa,YAAY,CAACZ,IAAI,CAACS,MAAM,CAACF,IAAI,CAAC,GAAGE,MAAM,CAACF,IAAI,CAAC;IAC7ClB,aAAa,CAACQ,KAAK,EAAEU,IAAI,EAAEC,SAAS,EAAEf,SAAS,CAAC;IAChDkB,MAAM,CAACT,IAAI,CAAC,EAAE,CAAC;IACf,KAAK,IAAIW,UAAU,GAAG,CAAC,EAAEA,UAAU,GAAGhB,KAAK,CAACO,MAAM,EAAES,UAAU,EAAE,EAAE;MAC9DF,MAAM,CAACR,GAAG,CAAC,CAACD,IAAI,CAACL,KAAK,CAACgB,UAAU,CAAC,CAACd,OAAO,CAAC;IAC/C;EACJ;EACA,OAAOY,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}