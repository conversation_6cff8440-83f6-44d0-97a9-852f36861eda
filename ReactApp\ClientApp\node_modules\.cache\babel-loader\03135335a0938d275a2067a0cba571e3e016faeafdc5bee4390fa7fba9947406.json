{"ast": null, "code": "export default function elementOffset(element) {\n  var box = element.getBoundingClientRect();\n  var documentElement = document.documentElement;\n  return {\n    top: box.top + (window.pageYOffset || documentElement.scrollTop) - (documentElement.clientTop || 0),\n    left: box.left + (window.pageXOffset || documentElement.scrollLeft) - (documentElement.clientLeft || 0)\n  };\n}", "map": {"version": 3, "names": ["elementOffset", "element", "box", "getBoundingClientRect", "documentElement", "document", "top", "window", "pageYOffset", "scrollTop", "clientTop", "left", "pageXOffset", "scrollLeft", "clientLeft"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/util/element-offset.js"], "sourcesContent": ["export default function elementOffset(element) {\n    var box = element.getBoundingClientRect();\n\n    var documentElement = document.documentElement;\n\n    return {\n        top: box.top + (window.pageYOffset || documentElement.scrollTop) - (documentElement.clientTop || 0),\n        left: box.left + (window.pageXOffset || documentElement.scrollLeft) - (documentElement.clientLeft || 0)\n    };\n}"], "mappings": "AAAA,eAAe,SAASA,aAAaA,CAACC,OAAO,EAAE;EAC3C,IAAIC,GAAG,GAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC;EAEzC,IAAIC,eAAe,GAAGC,QAAQ,CAACD,eAAe;EAE9C,OAAO;IACHE,GAAG,EAAEJ,GAAG,CAACI,GAAG,IAAIC,MAAM,CAACC,WAAW,IAAIJ,eAAe,CAACK,SAAS,CAAC,IAAIL,eAAe,CAACM,SAAS,IAAI,CAAC,CAAC;IACnGC,IAAI,EAAET,GAAG,CAACS,IAAI,IAAIJ,MAAM,CAACK,WAAW,IAAIR,eAAe,CAACS,UAAU,CAAC,IAAIT,eAAe,CAACU,UAAU,IAAI,CAAC;EAC1G,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}