{"ast": null, "code": "import defined from './defined';\nexport default function valueOrDefault(value, defaultValue) {\n  return defined(value) ? value : defaultValue;\n}", "map": {"version": 3, "names": ["defined", "valueOrDefault", "value", "defaultValue"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/util/value-or-default.js"], "sourcesContent": ["import defined from './defined';\n\nexport default function valueOrDefault(value, defaultValue) {\n    return defined(value) ? value : defaultValue;\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,WAAW;AAE/B,eAAe,SAASC,cAAcA,CAACC,KAAK,EAAEC,YAAY,EAAE;EACxD,OAAOH,OAAO,CAACE,KAAK,CAAC,GAAGA,KAAK,GAAGC,YAAY;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}