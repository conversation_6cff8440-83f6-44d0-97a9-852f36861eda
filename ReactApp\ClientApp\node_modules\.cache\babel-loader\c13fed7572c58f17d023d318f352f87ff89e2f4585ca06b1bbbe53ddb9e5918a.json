{"ast": null, "code": "import { normalizeYear } from './normalize-year';\n/**\n * A function that returns a `Date` object of the first decade in a century.\n *\n * @param date - The start date value.\n * @returns - The first year in a century.\n *\n * @example\n * ```ts-no-run\n * firstDecadeOfCentury(new Date(2017, 0, 1)); // 2000-1-1\n * firstDecadeOfCentury(new Date(2007, 10, 22)); // 2000-11-22\n * firstDecadeOfCentury(new Date(2126, 0, 1)); // 2100-1-1\n * ```\n */\nexport var firstDecadeOfCentury = function (value) {\n  return normalizeYear(value, function (y) {\n    return y - y % 100;\n  });\n};", "map": {"version": 3, "names": ["normalizeYear", "firstDecadeOfCentury", "value", "y"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/first-decade-of-century.js"], "sourcesContent": ["import { normalizeYear } from './normalize-year';\n/**\n * A function that returns a `Date` object of the first decade in a century.\n *\n * @param date - The start date value.\n * @returns - The first year in a century.\n *\n * @example\n * ```ts-no-run\n * firstDecadeOfCentury(new Date(2017, 0, 1)); // 2000-1-1\n * firstDecadeOfCentury(new Date(2007, 10, 22)); // 2000-11-22\n * firstDecadeOfCentury(new Date(2126, 0, 1)); // 2100-1-1\n * ```\n */\nexport var firstDecadeOfCentury = function (value) { return (normalizeYear(value, function (y) { return y - (y % 100); })); };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,kBAAkB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,oBAAoB,GAAG,SAAAA,CAAUC,KAAK,EAAE;EAAE,OAAQF,aAAa,CAACE,KAAK,EAAE,UAAUC,CAAC,EAAE;IAAE,OAAOA,CAAC,GAAIA,CAAC,GAAG,GAAI;EAAE,CAAC,CAAC;AAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}