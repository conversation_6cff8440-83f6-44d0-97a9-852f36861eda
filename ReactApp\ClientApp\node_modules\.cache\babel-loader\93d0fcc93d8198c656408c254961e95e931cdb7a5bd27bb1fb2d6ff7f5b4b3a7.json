{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst o = \"dialogs.windowMaximizeButton\",\n  i = \"dialogs.windowMinimizeButton\",\n  t = \"dialogs.windowRestoreButton\",\n  n = \"dialogs.windowCloseButton\",\n  s = {\n    [o]: \"maximize\",\n    [i]: \"minimize\",\n    [t]: \"restore\",\n    [n]: \"close\"\n  };\nexport { n as dialogsWindowCloseButton, o as dialogsWindowMaximizeButton, i as dialogsWindowMinimizeButton, t as dialogsWindowRestoreButton, s as messages };", "map": {"version": 3, "names": ["o", "i", "t", "n", "s", "dialogsWindowCloseButton", "dialogsWindowMaximizeButton", "dialogsWindowMinimizeButton", "dialogsWindowRestoreButton", "messages"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dialogs/messages/index.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst o = \"dialogs.windowMaximizeButton\", i = \"dialogs.windowMinimizeButton\", t = \"dialogs.windowRestoreButton\", n = \"dialogs.windowCloseButton\", s = {\n  [o]: \"maximize\",\n  [i]: \"minimize\",\n  [t]: \"restore\",\n  [n]: \"close\"\n};\nexport {\n  n as dialogsWindowCloseButton,\n  o as dialogsWindowMaximizeButton,\n  i as dialogsWindowMinimizeButton,\n  t as dialogsWindowRestoreButton,\n  s as messages\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAG,8BAA8B;EAAEC,CAAC,GAAG,8BAA8B;EAAEC,CAAC,GAAG,6BAA6B;EAAEC,CAAC,GAAG,2BAA2B;EAAEC,CAAC,GAAG;IACpJ,CAACJ,CAAC,GAAG,UAAU;IACf,CAACC,CAAC,GAAG,UAAU;IACf,CAACC,CAAC,GAAG,SAAS;IACd,CAACC,CAAC,GAAG;EACP,CAAC;AACD,SACEA,CAAC,IAAIE,wBAAwB,EAC7BL,CAAC,IAAIM,2BAA2B,EAChCL,CAAC,IAAIM,2BAA2B,EAChCL,CAAC,IAAIM,0BAA0B,EAC/BJ,CAAC,IAAIK,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}