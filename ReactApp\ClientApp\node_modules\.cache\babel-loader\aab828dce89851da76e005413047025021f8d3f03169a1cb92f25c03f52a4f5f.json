{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = contains;\nfunction contains(root, n) {\n  if (!root) {\n    return false;\n  }\n\n  // Use native if support\n  if (root.contains) {\n    return root.contains(n);\n  }\n\n  // `document.contains` not support with IE11\n  var node = n;\n  while (node) {\n    if (node === root) {\n      return true;\n    }\n    node = node.parentNode;\n  }\n  return false;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "contains", "root", "n", "node", "parentNode"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/rc-util/lib/Dom/contains.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = contains;\nfunction contains(root, n) {\n  if (!root) {\n    return false;\n  }\n\n  // Use native if support\n  if (root.contains) {\n    return root.contains(n);\n  }\n\n  // `document.contains` not support with IE11\n  var node = n;\n  while (node) {\n    if (node === root) {\n      return true;\n    }\n    node = node.parentNode;\n  }\n  return false;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAC1B,SAASA,QAAQA,CAACC,IAAI,EAAEC,CAAC,EAAE;EACzB,IAAI,CAACD,IAAI,EAAE;IACT,OAAO,KAAK;EACd;;EAEA;EACA,IAAIA,IAAI,CAACD,QAAQ,EAAE;IACjB,OAAOC,IAAI,CAACD,QAAQ,CAACE,CAAC,CAAC;EACzB;;EAEA;EACA,IAAIC,IAAI,GAAGD,CAAC;EACZ,OAAOC,IAAI,EAAE;IACX,IAAIA,IAAI,KAAKF,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IACAE,IAAI,GAAGA,IAAI,CAACC,UAAU;EACxB;EACA,OAAO,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}