{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst d = (s, t) => s.length === 0 ? \"\" : `<path d=\"${s.reduce((n, c, o, h) => o === 0 ?\n  // if first point\n  `M ${c[0]},${c[1]}` :\n  // else\n  `${n} ${t(c, o, h)}`, \"\")}\" fill=\"none\" stroke=\"white\" stroke-width=\"1\"/>`,\n  u = (s, t) => {\n    const e = t[0] - s[0],\n      n = t[1] - s[1];\n    return {\n      length: Math.sqrt(Math.pow(e, 2) + Math.pow(n, 2)),\n      angle: Math.atan2(n, e)\n    };\n  },\n  w = s => (t, e, n, c) => {\n    const o = e || t,\n      h = n || t,\n      l = 0.1,\n      a = s(o, h),\n      r = a.angle + (c ? Math.PI : 0),\n      $ = a.length * l,\n      g = t[0] + Math.cos(r) * $,\n      M = t[1] + Math.sin(r) * $;\n    return [g, M];\n  },\n  m = s => (t, e, n) => {\n    const [c, o] = s(n[e - 1], n[e - 2], t),\n      [h, l] = s(t, n[e - 1], n[e + 1], !0);\n    return `C ${c},${o} ${h},${l} ${t[0]},${t[1]}`;\n  };\nexport { m as bezierCommand, w as controlPoint, u as line, d as svgPath };", "map": {"version": 3, "names": ["d", "s", "t", "length", "reduce", "n", "c", "o", "h", "u", "e", "Math", "sqrt", "pow", "angle", "atan2", "w", "l", "a", "r", "PI", "$", "g", "cos", "M", "sin", "m", "bezierCommand", "controlPoint", "line", "svgPath"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-inputs/colors/utils/svg-calc.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst d = (s, t) => s.length === 0 ? \"\" : `<path d=\"${s.reduce(\n  (n, c, o, h) => o === 0 ? (\n    // if first point\n    `M ${c[0]},${c[1]}`\n  ) : (\n    // else\n    `${n} ${t(c, o, h)}`\n  ),\n  \"\"\n)}\" fill=\"none\" stroke=\"white\" stroke-width=\"1\"/>`, u = (s, t) => {\n  const e = t[0] - s[0], n = t[1] - s[1];\n  return {\n    length: Math.sqrt(Math.pow(e, 2) + Math.pow(n, 2)),\n    angle: Math.atan2(n, e)\n  };\n}, w = (s) => (t, e, n, c) => {\n  const o = e || t, h = n || t, l = 0.1, a = s(o, h), r = a.angle + (c ? Math.PI : 0), $ = a.length * l, g = t[0] + Math.cos(r) * $, M = t[1] + Math.sin(r) * $;\n  return [g, M];\n}, m = (s) => (t, e, n) => {\n  const [c, o] = s(n[e - 1], n[e - 2], t), [h, l] = s(t, n[e - 1], n[e + 1], !0);\n  return `C ${c},${o} ${h},${l} ${t[0]},${t[1]}`;\n};\nexport {\n  m as bezierCommand,\n  w as controlPoint,\n  u as line,\n  d as svgPath\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,MAAM,KAAK,CAAC,GAAG,EAAE,GAAG,YAAYF,CAAC,CAACG,MAAM,CAC5D,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAKD,CAAC,KAAK,CAAC;EACrB;EACA,KAAKD,CAAC,CAAC,CAAC,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,EAAE;EAEnB;EACA,GAAGD,CAAC,IAAIH,CAAC,CAACI,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,EACnB,EACD,EACF,CAAC,iDAAiD;EAAEC,CAAC,GAAGA,CAACR,CAAC,EAAEC,CAAC,KAAK;IAChE,MAAMQ,CAAC,GAAGR,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC;MAAEI,CAAC,GAAGH,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC;IACtC,OAAO;MACLE,MAAM,EAAEQ,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,GAAG,CAACH,CAAC,EAAE,CAAC,CAAC,GAAGC,IAAI,CAACE,GAAG,CAACR,CAAC,EAAE,CAAC,CAAC,CAAC;MAClDS,KAAK,EAAEH,IAAI,CAACI,KAAK,CAACV,CAAC,EAAEK,CAAC;IACxB,CAAC;EACH,CAAC;EAAEM,CAAC,GAAIf,CAAC,IAAK,CAACC,CAAC,EAAEQ,CAAC,EAAEL,CAAC,EAAEC,CAAC,KAAK;IAC5B,MAAMC,CAAC,GAAGG,CAAC,IAAIR,CAAC;MAAEM,CAAC,GAAGH,CAAC,IAAIH,CAAC;MAAEe,CAAC,GAAG,GAAG;MAAEC,CAAC,GAAGjB,CAAC,CAACM,CAAC,EAAEC,CAAC,CAAC;MAAEW,CAAC,GAAGD,CAAC,CAACJ,KAAK,IAAIR,CAAC,GAAGK,IAAI,CAACS,EAAE,GAAG,CAAC,CAAC;MAAEC,CAAC,GAAGH,CAAC,CAACf,MAAM,GAAGc,CAAC;MAAEK,CAAC,GAAGpB,CAAC,CAAC,CAAC,CAAC,GAAGS,IAAI,CAACY,GAAG,CAACJ,CAAC,CAAC,GAAGE,CAAC;MAAEG,CAAC,GAAGtB,CAAC,CAAC,CAAC,CAAC,GAAGS,IAAI,CAACc,GAAG,CAACN,CAAC,CAAC,GAAGE,CAAC;IAC7J,OAAO,CAACC,CAAC,EAAEE,CAAC,CAAC;EACf,CAAC;EAAEE,CAAC,GAAIzB,CAAC,IAAK,CAACC,CAAC,EAAEQ,CAAC,EAAEL,CAAC,KAAK;IACzB,MAAM,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAGN,CAAC,CAACI,CAAC,CAACK,CAAC,GAAG,CAAC,CAAC,EAAEL,CAAC,CAACK,CAAC,GAAG,CAAC,CAAC,EAAER,CAAC,CAAC;MAAE,CAACM,CAAC,EAAES,CAAC,CAAC,GAAGhB,CAAC,CAACC,CAAC,EAAEG,CAAC,CAACK,CAAC,GAAG,CAAC,CAAC,EAAEL,CAAC,CAACK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9E,OAAO,KAAKJ,CAAC,IAAIC,CAAC,IAAIC,CAAC,IAAIS,CAAC,IAAIf,CAAC,CAAC,CAAC,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,EAAE;EAChD,CAAC;AACD,SACEwB,CAAC,IAAIC,aAAa,EAClBX,CAAC,IAAIY,YAAY,EACjBnB,CAAC,IAAIoB,IAAI,EACT7B,CAAC,IAAI8B,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}