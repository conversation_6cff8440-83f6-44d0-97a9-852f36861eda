{"ast": null, "code": "import { addDays } from './add-days';\nimport { createDate } from './create-date';\n/**\n * A function which returns the last date of the month.\n *\n * @param date - The initial date.\n * @returns - The last date of the initial date month.\n *\n * @example\n * ```ts-no-run\n * lastDayOfMonth(new Date(2016, 0, 15)); // 2016-01-31\n * ```\n */\nexport var lastDayOfMonth = function (date) {\n  var newDate = createDate(date.getFullYear(), date.getMonth() + 1, 1, date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n  return addDays(newDate, -1);\n};", "map": {"version": 3, "names": ["addDays", "createDate", "lastDayOfMonth", "date", "newDate", "getFullYear", "getMonth", "getHours", "getMinutes", "getSeconds", "getMilliseconds"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/last-day-of-month.js"], "sourcesContent": ["import { addDays } from './add-days';\nimport { createDate } from './create-date';\n/**\n * A function which returns the last date of the month.\n *\n * @param date - The initial date.\n * @returns - The last date of the initial date month.\n *\n * @example\n * ```ts-no-run\n * lastDayOfMonth(new Date(2016, 0, 15)); // 2016-01-31\n * ```\n */\nexport var lastDayOfMonth = function (date) {\n    var newDate = createDate(date.getFullYear(), date.getMonth() + 1, 1, date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n    return addDays(newDate, -1);\n};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,UAAU,QAAQ,eAAe;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,cAAc,GAAG,SAAAA,CAAUC,IAAI,EAAE;EACxC,IAAIC,OAAO,GAAGH,UAAU,CAACE,IAAI,CAACE,WAAW,CAAC,CAAC,EAAEF,IAAI,CAACG,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAEH,IAAI,CAACI,QAAQ,CAAC,CAAC,EAAEJ,IAAI,CAACK,UAAU,CAAC,CAAC,EAAEL,IAAI,CAACM,UAAU,CAAC,CAAC,EAAEN,IAAI,CAACO,eAAe,CAAC,CAAC,CAAC;EACnJ,OAAOV,OAAO,CAACI,OAAO,EAAE,CAAC,CAAC,CAAC;AAC/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}