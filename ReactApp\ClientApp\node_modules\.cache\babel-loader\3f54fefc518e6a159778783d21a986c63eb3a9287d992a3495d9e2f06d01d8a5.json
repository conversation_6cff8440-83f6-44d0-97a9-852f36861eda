{"ast": null, "code": "/**\n * Represents the list of supported [`FilterDescriptor`]({% slug api_kendo-data-query_filterdescriptor %}) operators.\n * Allows restricting `FilterDescriptor.operator` definition to available values only.\n */\nexport var FilterOperator;\n(function (FilterOperator) {\n  /**\n   * The `contains` operator.\n   */\n  FilterOperator[\"Contains\"] = \"contains\";\n  /**\n   * The `doesnotcontain` operator.\n   */\n  FilterOperator[\"DoesNotContain\"] = \"doesnotcontain\";\n  /**\n   * The `doesnotendwith` operator.\n   */\n  FilterOperator[\"DoesNotEndWith\"] = \"doesnotendwith\";\n  /**\n   * The `doesnotstartwith` operator.\n   */\n  FilterOperator[\"DoesNotStartWith\"] = \"doesnotstartwith\";\n  /**\n   * The `endswith` operator.\n   */\n  FilterOperator[\"EndsWith\"] = \"endswith\";\n  /**\n   * The `eq` operator.\n   */\n  FilterOperator[\"EqualTo\"] = \"eq\";\n  /**\n   * The `gt` operator.\n   */\n  FilterOperator[\"GreaterThan\"] = \"gt\";\n  /**\n   * The `gte` operator.\n   */\n  FilterOperator[\"GreaterThanOrEqual\"] = \"gte\";\n  /**\n   * The `isempty` operator.\n   */\n  FilterOperator[\"IsEmpty\"] = \"isempty\";\n  /**\n   * The `isnotempty` operator.\n   */\n  FilterOperator[\"IsNotEmpty\"] = \"isnotempty\";\n  /**\n   * The `isnotnull` operator.\n   */\n  FilterOperator[\"IsNotNull\"] = \"isnotnull\";\n  /**\n   * The `isnull` operator.\n   */\n  FilterOperator[\"IsNull\"] = \"isnull\";\n  /**\n   * The `lt` operator.\n   */\n  FilterOperator[\"LessThan\"] = \"lt\";\n  /**\n   * The `lte` operator.\n   */\n  FilterOperator[\"LessThanOrEqual\"] = \"lte\";\n  /**\n   * The `neq` operator.\n   */\n  FilterOperator[\"NotEqualTo\"] = \"neq\";\n  /**\n   * The `startswith` operator.\n   */\n  FilterOperator[\"StartsWith\"] = \"startswith\";\n})(FilterOperator || (FilterOperator = {}));", "map": {"version": 3, "names": ["FilterOperator"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-data-query/dist/es/filtering/operators.enum.js"], "sourcesContent": ["/**\n * Represents the list of supported [`FilterDescriptor`]({% slug api_kendo-data-query_filterdescriptor %}) operators.\n * Allows restricting `FilterDescriptor.operator` definition to available values only.\n */\nexport var FilterOperator;\n(function (FilterOperator) {\n    /**\n     * The `contains` operator.\n     */\n    FilterOperator[\"Contains\"] = \"contains\";\n    /**\n     * The `doesnotcontain` operator.\n     */\n    FilterOperator[\"DoesNotContain\"] = \"doesnotcontain\";\n    /**\n     * The `doesnotendwith` operator.\n     */\n    FilterOperator[\"DoesNotEndWith\"] = \"doesnotendwith\";\n    /**\n     * The `doesnotstartwith` operator.\n     */\n    FilterOperator[\"DoesNotStartWith\"] = \"doesnotstartwith\";\n    /**\n     * The `endswith` operator.\n     */\n    FilterOperator[\"EndsWith\"] = \"endswith\";\n    /**\n     * The `eq` operator.\n     */\n    FilterOperator[\"EqualTo\"] = \"eq\";\n    /**\n     * The `gt` operator.\n     */\n    FilterOperator[\"GreaterThan\"] = \"gt\";\n    /**\n     * The `gte` operator.\n     */\n    FilterOperator[\"GreaterThanOrEqual\"] = \"gte\";\n    /**\n     * The `isempty` operator.\n     */\n    FilterOperator[\"IsEmpty\"] = \"isempty\";\n    /**\n     * The `isnotempty` operator.\n     */\n    FilterOperator[\"IsNotEmpty\"] = \"isnotempty\";\n    /**\n     * The `isnotnull` operator.\n     */\n    FilterOperator[\"IsNotNull\"] = \"isnotnull\";\n    /**\n     * The `isnull` operator.\n     */\n    FilterOperator[\"IsNull\"] = \"isnull\";\n    /**\n     * The `lt` operator.\n     */\n    FilterOperator[\"LessThan\"] = \"lt\";\n    /**\n     * The `lte` operator.\n     */\n    FilterOperator[\"LessThanOrEqual\"] = \"lte\";\n    /**\n     * The `neq` operator.\n     */\n    FilterOperator[\"NotEqualTo\"] = \"neq\";\n    /**\n     * The `startswith` operator.\n     */\n    FilterOperator[\"StartsWith\"] = \"startswith\";\n})(FilterOperator || (FilterOperator = {}));\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,IAAIA,cAAc;AACzB,CAAC,UAAUA,cAAc,EAAE;EACvB;AACJ;AACA;EACIA,cAAc,CAAC,UAAU,CAAC,GAAG,UAAU;EACvC;AACJ;AACA;EACIA,cAAc,CAAC,gBAAgB,CAAC,GAAG,gBAAgB;EACnD;AACJ;AACA;EACIA,cAAc,CAAC,gBAAgB,CAAC,GAAG,gBAAgB;EACnD;AACJ;AACA;EACIA,cAAc,CAAC,kBAAkB,CAAC,GAAG,kBAAkB;EACvD;AACJ;AACA;EACIA,cAAc,CAAC,UAAU,CAAC,GAAG,UAAU;EACvC;AACJ;AACA;EACIA,cAAc,CAAC,SAAS,CAAC,GAAG,IAAI;EAChC;AACJ;AACA;EACIA,cAAc,CAAC,aAAa,CAAC,GAAG,IAAI;EACpC;AACJ;AACA;EACIA,cAAc,CAAC,oBAAoB,CAAC,GAAG,KAAK;EAC5C;AACJ;AACA;EACIA,cAAc,CAAC,SAAS,CAAC,GAAG,SAAS;EACrC;AACJ;AACA;EACIA,cAAc,CAAC,YAAY,CAAC,GAAG,YAAY;EAC3C;AACJ;AACA;EACIA,cAAc,CAAC,WAAW,CAAC,GAAG,WAAW;EACzC;AACJ;AACA;EACIA,cAAc,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACnC;AACJ;AACA;EACIA,cAAc,CAAC,UAAU,CAAC,GAAG,IAAI;EACjC;AACJ;AACA;EACIA,cAAc,CAAC,iBAAiB,CAAC,GAAG,KAAK;EACzC;AACJ;AACA;EACIA,cAAc,CAAC,YAAY,CAAC,GAAG,KAAK;EACpC;AACJ;AACA;EACIA,cAAc,CAAC,YAAY,CAAC,GAAG,YAAY;AAC/C,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}