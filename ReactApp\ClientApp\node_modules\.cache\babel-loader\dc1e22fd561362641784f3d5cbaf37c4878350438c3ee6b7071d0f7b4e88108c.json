{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport HolderOutlined from \"@ant-design/icons/es/icons/HolderOutlined\";\nimport classNames from 'classnames';\nimport RcTree from 'rc-tree';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport collapseMotion from '../_util/motion';\nimport dropIndicatorRender from './utils/dropIndicator';\nimport renderSwitcherIcon from './utils/iconUtil';\nvar Tree = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction,\n    virtual = _React$useContext.virtual;\n  var customizePrefixCls = props.prefixCls,\n    className = props.className,\n    _props$showIcon = props.showIcon,\n    showIcon = _props$showIcon === void 0 ? false : _props$showIcon,\n    showLine = props.showLine,\n    _switcherIcon = props.switcherIcon,\n    _props$blockNode = props.blockNode,\n    blockNode = _props$blockNode === void 0 ? false : _props$blockNode,\n    children = props.children,\n    _props$checkable = props.checkable,\n    checkable = _props$checkable === void 0 ? false : _props$checkable,\n    _props$selectable = props.selectable,\n    selectable = _props$selectable === void 0 ? true : _props$selectable,\n    draggable = props.draggable,\n    _props$motion = props.motion,\n    motion = _props$motion === void 0 ? _extends(_extends({}, collapseMotion), {\n      motionAppear: false\n    }) : _props$motion;\n  var prefixCls = getPrefixCls('tree', customizePrefixCls);\n  var newProps = _extends(_extends({}, props), {\n    checkable: checkable,\n    selectable: selectable,\n    showIcon: showIcon,\n    motion: motion,\n    blockNode: blockNode,\n    showLine: Boolean(showLine),\n    dropIndicatorRender: dropIndicatorRender\n  });\n  var draggableConfig = React.useMemo(function () {\n    if (!draggable) {\n      return false;\n    }\n    var mergedDraggable = {};\n    switch (_typeof(draggable)) {\n      case 'function':\n        mergedDraggable.nodeDraggable = draggable;\n        break;\n      case 'object':\n        mergedDraggable = _extends({}, draggable);\n        break;\n      default:\n        break;\n      // Do nothing\n    }\n    if (mergedDraggable.icon !== false) {\n      mergedDraggable.icon = mergedDraggable.icon || /*#__PURE__*/React.createElement(HolderOutlined, null);\n    }\n    return mergedDraggable;\n  }, [draggable]);\n  return /*#__PURE__*/React.createElement(RcTree, _extends({\n    itemHeight: 20,\n    ref: ref,\n    virtual: virtual\n  }, newProps, {\n    prefixCls: prefixCls,\n    className: classNames(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-icon-hide\"), !showIcon), \"\".concat(prefixCls, \"-block-node\"), blockNode), \"\".concat(prefixCls, \"-unselectable\"), !selectable), \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), className),\n    direction: direction,\n    checkable: checkable ? /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-checkbox-inner\")\n    }) : checkable,\n    selectable: selectable,\n    switcherIcon: function switcherIcon(nodeProps) {\n      return renderSwitcherIcon(prefixCls, _switcherIcon, showLine, nodeProps);\n    },\n    draggable: draggableConfig\n  }), children);\n});\nexport default Tree;", "map": {"version": 3, "names": ["_defineProperty", "_typeof", "_extends", "Holder<PERSON><PERSON><PERSON>", "classNames", "<PERSON><PERSON><PERSON><PERSON>", "React", "ConfigContext", "collapseMotion", "dropIndicatorRender", "renderSwitcherIcon", "Tree", "forwardRef", "props", "ref", "_React$useContext", "useContext", "getPrefixCls", "direction", "virtual", "customizePrefixCls", "prefixCls", "className", "_props$showIcon", "showIcon", "showLine", "_switcherIcon", "switcherIcon", "_props$blockNode", "blockNode", "children", "_props$checkable", "checkable", "_props$selectable", "selectable", "draggable", "_props$motion", "motion", "motionAppear", "newProps", "Boolean", "draggableConfig", "useMemo", "mergedDraggable", "nodeDraggable", "icon", "createElement", "itemHeight", "concat", "nodeProps"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/antd/es/tree/Tree.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport HolderOutlined from \"@ant-design/icons/es/icons/HolderOutlined\";\nimport classNames from 'classnames';\nimport RcTree from 'rc-tree';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport collapseMotion from '../_util/motion';\nimport dropIndicatorRender from './utils/dropIndicator';\nimport renderSwitcherIcon from './utils/iconUtil';\nvar Tree = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction,\n    virtual = _React$useContext.virtual;\n  var customizePrefixCls = props.prefixCls,\n    className = props.className,\n    _props$showIcon = props.showIcon,\n    showIcon = _props$showIcon === void 0 ? false : _props$showIcon,\n    showLine = props.showLine,\n    _switcherIcon = props.switcherIcon,\n    _props$blockNode = props.blockNode,\n    blockNode = _props$blockNode === void 0 ? false : _props$blockNode,\n    children = props.children,\n    _props$checkable = props.checkable,\n    checkable = _props$checkable === void 0 ? false : _props$checkable,\n    _props$selectable = props.selectable,\n    selectable = _props$selectable === void 0 ? true : _props$selectable,\n    draggable = props.draggable,\n    _props$motion = props.motion,\n    motion = _props$motion === void 0 ? _extends(_extends({}, collapseMotion), {\n      motionAppear: false\n    }) : _props$motion;\n  var prefixCls = getPrefixCls('tree', customizePrefixCls);\n  var newProps = _extends(_extends({}, props), {\n    checkable: checkable,\n    selectable: selectable,\n    showIcon: showIcon,\n    motion: motion,\n    blockNode: blockNode,\n    showLine: Boolean(showLine),\n    dropIndicatorRender: dropIndicatorRender\n  });\n  var draggableConfig = React.useMemo(function () {\n    if (!draggable) {\n      return false;\n    }\n    var mergedDraggable = {};\n    switch (_typeof(draggable)) {\n      case 'function':\n        mergedDraggable.nodeDraggable = draggable;\n        break;\n      case 'object':\n        mergedDraggable = _extends({}, draggable);\n        break;\n      default:\n        break;\n      // Do nothing\n    }\n    if (mergedDraggable.icon !== false) {\n      mergedDraggable.icon = mergedDraggable.icon || /*#__PURE__*/React.createElement(HolderOutlined, null);\n    }\n    return mergedDraggable;\n  }, [draggable]);\n  return /*#__PURE__*/React.createElement(RcTree, _extends({\n    itemHeight: 20,\n    ref: ref,\n    virtual: virtual\n  }, newProps, {\n    prefixCls: prefixCls,\n    className: classNames(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-icon-hide\"), !showIcon), \"\".concat(prefixCls, \"-block-node\"), blockNode), \"\".concat(prefixCls, \"-unselectable\"), !selectable), \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), className),\n    direction: direction,\n    checkable: checkable ? /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-checkbox-inner\")\n    }) : checkable,\n    selectable: selectable,\n    switcherIcon: function switcherIcon(nodeProps) {\n      return renderSwitcherIcon(prefixCls, _switcherIcon, showLine, nodeProps);\n    },\n    draggable: draggableConfig\n  }), children);\n});\nexport default Tree;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,2CAA2C;AACtE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,MAAM,MAAM,SAAS;AAC5B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,cAAc,MAAM,iBAAiB;AAC5C,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,OAAOC,kBAAkB,MAAM,kBAAkB;AACjD,IAAIC,IAAI,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC7D,IAAIC,iBAAiB,GAAGT,KAAK,CAACU,UAAU,CAACT,aAAa,CAAC;IACrDU,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;IACvCC,OAAO,GAAGJ,iBAAiB,CAACI,OAAO;EACrC,IAAIC,kBAAkB,GAAGP,KAAK,CAACQ,SAAS;IACtCC,SAAS,GAAGT,KAAK,CAACS,SAAS;IAC3BC,eAAe,GAAGV,KAAK,CAACW,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,eAAe;IAC/DE,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IACzBC,aAAa,GAAGb,KAAK,CAACc,YAAY;IAClCC,gBAAgB,GAAGf,KAAK,CAACgB,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,gBAAgB;IAClEE,QAAQ,GAAGjB,KAAK,CAACiB,QAAQ;IACzBC,gBAAgB,GAAGlB,KAAK,CAACmB,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,gBAAgB;IAClEE,iBAAiB,GAAGpB,KAAK,CAACqB,UAAU;IACpCA,UAAU,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,iBAAiB;IACpEE,SAAS,GAAGtB,KAAK,CAACsB,SAAS;IAC3BC,aAAa,GAAGvB,KAAK,CAACwB,MAAM;IAC5BA,MAAM,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAGlC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEM,cAAc,CAAC,EAAE;MACzE8B,YAAY,EAAE;IAChB,CAAC,CAAC,GAAGF,aAAa;EACpB,IAAIf,SAAS,GAAGJ,YAAY,CAAC,MAAM,EAAEG,kBAAkB,CAAC;EACxD,IAAImB,QAAQ,GAAGrC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEW,KAAK,CAAC,EAAE;IAC3CmB,SAAS,EAAEA,SAAS;IACpBE,UAAU,EAAEA,UAAU;IACtBV,QAAQ,EAAEA,QAAQ;IAClBa,MAAM,EAAEA,MAAM;IACdR,SAAS,EAAEA,SAAS;IACpBJ,QAAQ,EAAEe,OAAO,CAACf,QAAQ,CAAC;IAC3BhB,mBAAmB,EAAEA;EACvB,CAAC,CAAC;EACF,IAAIgC,eAAe,GAAGnC,KAAK,CAACoC,OAAO,CAAC,YAAY;IAC9C,IAAI,CAACP,SAAS,EAAE;MACd,OAAO,KAAK;IACd;IACA,IAAIQ,eAAe,GAAG,CAAC,CAAC;IACxB,QAAQ1C,OAAO,CAACkC,SAAS,CAAC;MACxB,KAAK,UAAU;QACbQ,eAAe,CAACC,aAAa,GAAGT,SAAS;QACzC;MACF,KAAK,QAAQ;QACXQ,eAAe,GAAGzC,QAAQ,CAAC,CAAC,CAAC,EAAEiC,SAAS,CAAC;QACzC;MACF;QACE;MACF;IACF;IACA,IAAIQ,eAAe,CAACE,IAAI,KAAK,KAAK,EAAE;MAClCF,eAAe,CAACE,IAAI,GAAGF,eAAe,CAACE,IAAI,IAAI,aAAavC,KAAK,CAACwC,aAAa,CAAC3C,cAAc,EAAE,IAAI,CAAC;IACvG;IACA,OAAOwC,eAAe;EACxB,CAAC,EAAE,CAACR,SAAS,CAAC,CAAC;EACf,OAAO,aAAa7B,KAAK,CAACwC,aAAa,CAACzC,MAAM,EAAEH,QAAQ,CAAC;IACvD6C,UAAU,EAAE,EAAE;IACdjC,GAAG,EAAEA,GAAG;IACRK,OAAO,EAAEA;EACX,CAAC,EAAEoB,QAAQ,EAAE;IACXlB,SAAS,EAAEA,SAAS;IACpBC,SAAS,EAAElB,UAAU,CAACJ,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACgD,MAAM,CAAC3B,SAAS,EAAE,YAAY,CAAC,EAAE,CAACG,QAAQ,CAAC,EAAE,EAAE,CAACwB,MAAM,CAAC3B,SAAS,EAAE,aAAa,CAAC,EAAEQ,SAAS,CAAC,EAAE,EAAE,CAACmB,MAAM,CAAC3B,SAAS,EAAE,eAAe,CAAC,EAAE,CAACa,UAAU,CAAC,EAAE,EAAE,CAACc,MAAM,CAAC3B,SAAS,EAAE,MAAM,CAAC,EAAEH,SAAS,KAAK,KAAK,CAAC,EAAEI,SAAS,CAAC;IAC9SJ,SAAS,EAAEA,SAAS;IACpBc,SAAS,EAAEA,SAAS,GAAG,aAAa1B,KAAK,CAACwC,aAAa,CAAC,MAAM,EAAE;MAC9DxB,SAAS,EAAE,EAAE,CAAC0B,MAAM,CAAC3B,SAAS,EAAE,iBAAiB;IACnD,CAAC,CAAC,GAAGW,SAAS;IACdE,UAAU,EAAEA,UAAU;IACtBP,YAAY,EAAE,SAASA,YAAYA,CAACsB,SAAS,EAAE;MAC7C,OAAOvC,kBAAkB,CAACW,SAAS,EAAEK,aAAa,EAAED,QAAQ,EAAEwB,SAAS,CAAC;IAC1E,CAAC;IACDd,SAAS,EAAEM;EACb,CAAC,CAAC,EAAEX,QAAQ,CAAC;AACf,CAAC,CAAC;AACF,eAAenB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}