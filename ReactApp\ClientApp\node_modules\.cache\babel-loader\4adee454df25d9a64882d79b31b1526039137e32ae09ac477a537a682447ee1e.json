{"ast": null, "code": "import Class from '../class';\nimport namedColors from './named-colors';\nimport { Bytes, RGB, HSV, HSL } from './parse-color';\nvar DARK_TRESHOLD = 180;\nvar Color = function (Class) {\n  function Color(value) {\n    var this$1 = this;\n    Class.call(this);\n    if (arguments.length === 1) {\n      var formats = Color.formats;\n      var resolvedColor = this.resolveColor(value);\n      for (var idx = 0; idx < formats.length; idx++) {\n        var formatRegex = formats[idx].re;\n        var processor = formats[idx].process;\n        var parts = formatRegex.exec(resolvedColor);\n        if (parts) {\n          var channels = processor(parts);\n          this$1.r = channels[0];\n          this$1.g = channels[1];\n          this$1.b = channels[2];\n        }\n      }\n    } else {\n      this.r = arguments[0];\n      this.g = arguments[1];\n      this.b = arguments[2];\n    }\n    this.r = this.normalizeByte(this.r);\n    this.g = this.normalizeByte(this.g);\n    this.b = this.normalizeByte(this.b);\n  }\n  if (Class) Color.__proto__ = Class;\n  Color.prototype = Object.create(Class && Class.prototype);\n  Color.prototype.constructor = Color;\n  Color.prototype.toHex = function toHex() {\n    var pad = this.padDigit;\n    var r = this.r.toString(16);\n    var g = this.g.toString(16);\n    var b = this.b.toString(16);\n    return \"#\" + pad(r) + pad(g) + pad(b);\n  };\n  Color.prototype.resolveColor = function resolveColor(value) {\n    var color = value || \"black\";\n    if (color.charAt(0) === \"#\") {\n      color = color.substr(1, 6);\n    }\n    color = color.replace(/ /g, \"\");\n    color = color.toLowerCase();\n    color = Color.namedColors[color] || color;\n    return color;\n  };\n  Color.prototype.normalizeByte = function normalizeByte(value) {\n    if (value < 0 || isNaN(value)) {\n      return 0;\n    }\n    return value > 255 ? 255 : value;\n  };\n  Color.prototype.padDigit = function padDigit(value) {\n    return value.length === 1 ? \"0\" + value : value;\n  };\n  Color.prototype.brightness = function brightness(value) {\n    var round = Math.round;\n    this.r = round(this.normalizeByte(this.r * value));\n    this.g = round(this.normalizeByte(this.g * value));\n    this.b = round(this.normalizeByte(this.b * value));\n    return this;\n  };\n  Color.prototype.percBrightness = function percBrightness() {\n    return Math.sqrt(0.241 * this.r * this.r + 0.691 * this.g * this.g + 0.068 * this.b * this.b);\n  };\n  Color.prototype.isDark = function isDark() {\n    return this.percBrightness() < DARK_TRESHOLD;\n  };\n  Color.fromBytes = function fromBytes(r, g, b, a) {\n    return new Bytes(r, g, b, a != null ? a : 1);\n  };\n  Color.fromRGB = function fromRGB(r, g, b, a) {\n    return new RGB(r, g, b, a != null ? a : 1);\n  };\n  Color.fromHSV = function fromHSV(h, s, v, a) {\n    return new HSV(h, s, v, a != null ? a : 1);\n  };\n  Color.fromHSL = function fromHSL(h, s, l, a) {\n    return new HSL(h, s, l, a != null ? a : 1);\n  };\n  return Color;\n}(Class);\nColor.formats = [{\n  re: /^rgb\\((\\d{1,3}),\\s*(\\d{1,3}),\\s*(\\d{1,3})\\)$/,\n  process: function (parts) {\n    return [parseInt(parts[1], 10), parseInt(parts[2], 10), parseInt(parts[3], 10)];\n  }\n}, {\n  re: /^(\\w{2})(\\w{2})(\\w{2})$/,\n  process: function (parts) {\n    return [parseInt(parts[1], 16), parseInt(parts[2], 16), parseInt(parts[3], 16)];\n  }\n}, {\n  re: /^(\\w{1})(\\w{1})(\\w{1})$/,\n  process: function (parts) {\n    return [parseInt(parts[1] + parts[1], 16), parseInt(parts[2] + parts[2], 16), parseInt(parts[3] + parts[3], 16)];\n  }\n}];\nColor.namedColors = namedColors;\nexport default Color;", "map": {"version": 3, "names": ["Class", "namedColors", "Bytes", "RGB", "HSV", "HSL", "DARK_TRESHOLD", "Color", "value", "this$1", "call", "arguments", "length", "formats", "resolvedColor", "resolveColor", "idx", "formatRegex", "re", "processor", "process", "parts", "exec", "channels", "r", "g", "b", "normalizeByte", "__proto__", "prototype", "Object", "create", "constructor", "toHex", "pad", "pad<PERSON><PERSON><PERSON>", "toString", "color", "char<PERSON>t", "substr", "replace", "toLowerCase", "isNaN", "brightness", "round", "Math", "percBrightness", "sqrt", "isDark", "fromBytes", "a", "fromRGB", "fromHSV", "h", "s", "v", "fromHSL", "l", "parseInt"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/common/color/color.js"], "sourcesContent": ["import Class from '../class';\nimport namedColors from './named-colors';\nimport { Bytes, RGB, HSV, HSL } from './parse-color';\n\nvar DARK_TRESHOLD = 180;\n\nvar Color = (function (Class) {\n    function Color(value) {\n        var this$1 = this;\n\n        Class.call(this);\n\n        if (arguments.length === 1) {\n            var formats = Color.formats;\n            var resolvedColor = this.resolveColor(value);\n\n            for (var idx = 0; idx < formats.length; idx++) {\n                var formatRegex = formats[idx].re;\n                var processor = formats[idx].process;\n                var parts = formatRegex.exec(resolvedColor);\n\n                if (parts) {\n                    var channels = processor(parts);\n                    this$1.r = channels[0];\n                    this$1.g = channels[1];\n                    this$1.b = channels[2];\n                }\n            }\n        } else {\n            this.r = arguments[0];\n            this.g = arguments[1];\n            this.b = arguments[2];\n        }\n\n        this.r = this.normalizeByte(this.r);\n        this.g = this.normalizeByte(this.g);\n        this.b = this.normalizeByte(this.b);\n    }\n\n    if ( Class ) Color.__proto__ = Class;\n    Color.prototype = Object.create( Class && Class.prototype );\n    Color.prototype.constructor = Color;\n\n    Color.prototype.toHex = function toHex () {\n        var pad = this.padDigit;\n        var r = this.r.toString(16);\n        var g = this.g.toString(16);\n        var b = this.b.toString(16);\n\n        return \"#\" + pad(r) + pad(g) + pad(b);\n    };\n\n    Color.prototype.resolveColor = function resolveColor (value) {\n        var color = value || \"black\";\n\n        if (color.charAt(0) === \"#\") {\n            color = color.substr(1, 6);\n        }\n\n        color = color.replace(/ /g, \"\");\n        color = color.toLowerCase();\n        color = Color.namedColors[color] || color;\n\n        return color;\n    };\n\n    Color.prototype.normalizeByte = function normalizeByte (value) {\n        if (value < 0 || isNaN(value)) {\n            return 0;\n        }\n\n        return value > 255 ? 255 : value;\n    };\n\n    Color.prototype.padDigit = function padDigit (value) {\n        return (value.length === 1) ? \"0\" + value : value;\n    };\n\n    Color.prototype.brightness = function brightness (value) {\n        var round = Math.round;\n\n        this.r = round(this.normalizeByte(this.r * value));\n        this.g = round(this.normalizeByte(this.g * value));\n        this.b = round(this.normalizeByte(this.b * value));\n\n        return this;\n    };\n\n    Color.prototype.percBrightness = function percBrightness () {\n        return Math.sqrt(0.241 * this.r * this.r + 0.691 * this.g * this.g + 0.068 * this.b * this.b);\n    };\n\n    Color.prototype.isDark = function isDark () {\n        return this.percBrightness() < DARK_TRESHOLD;\n    };\n\n    Color.fromBytes = function fromBytes (r, g, b, a) {\n        return new Bytes(r, g, b, a != null ? a : 1);\n    };\n\n    Color.fromRGB = function fromRGB (r, g, b, a) {\n        return new RGB(r, g, b, a != null ? a : 1);\n    };\n\n    Color.fromHSV = function fromHSV (h, s, v, a) {\n        return new HSV(h, s, v, a != null ? a : 1);\n    };\n\n    Color.fromHSL = function fromHSL (h, s, l, a) {\n        return new HSL(h, s, l, a != null ? a : 1);\n    };\n\n    return Color;\n}(Class));\n\nColor.formats = [ {\n    re: /^rgb\\((\\d{1,3}),\\s*(\\d{1,3}),\\s*(\\d{1,3})\\)$/,\n    process: function(parts) {\n        return [\n            parseInt(parts[1], 10), parseInt(parts[2], 10), parseInt(parts[3], 10)\n        ];\n    }\n}, {\n    re: /^(\\w{2})(\\w{2})(\\w{2})$/,\n    process: function(parts) {\n        return [\n            parseInt(parts[1], 16), parseInt(parts[2], 16), parseInt(parts[3], 16)\n        ];\n    }\n}, {\n    re: /^(\\w{1})(\\w{1})(\\w{1})$/,\n    process: function(parts) {\n        return [\n            parseInt(parts[1] + parts[1], 16),\n            parseInt(parts[2] + parts[2], 16),\n            parseInt(parts[3] + parts[3], 16)\n        ];\n    }\n} ];\n\nColor.namedColors = namedColors;\n\nexport default Color;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,UAAU;AAC5B,OAAOC,WAAW,MAAM,gBAAgB;AACxC,SAASC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,QAAQ,eAAe;AAEpD,IAAIC,aAAa,GAAG,GAAG;AAEvB,IAAIC,KAAK,GAAI,UAAUP,KAAK,EAAE;EAC1B,SAASO,KAAKA,CAACC,KAAK,EAAE;IAClB,IAAIC,MAAM,GAAG,IAAI;IAEjBT,KAAK,CAACU,IAAI,CAAC,IAAI,CAAC;IAEhB,IAAIC,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;MACxB,IAAIC,OAAO,GAAGN,KAAK,CAACM,OAAO;MAC3B,IAAIC,aAAa,GAAG,IAAI,CAACC,YAAY,CAACP,KAAK,CAAC;MAE5C,KAAK,IAAIQ,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGH,OAAO,CAACD,MAAM,EAAEI,GAAG,EAAE,EAAE;QAC3C,IAAIC,WAAW,GAAGJ,OAAO,CAACG,GAAG,CAAC,CAACE,EAAE;QACjC,IAAIC,SAAS,GAAGN,OAAO,CAACG,GAAG,CAAC,CAACI,OAAO;QACpC,IAAIC,KAAK,GAAGJ,WAAW,CAACK,IAAI,CAACR,aAAa,CAAC;QAE3C,IAAIO,KAAK,EAAE;UACP,IAAIE,QAAQ,GAAGJ,SAAS,CAACE,KAAK,CAAC;UAC/BZ,MAAM,CAACe,CAAC,GAAGD,QAAQ,CAAC,CAAC,CAAC;UACtBd,MAAM,CAACgB,CAAC,GAAGF,QAAQ,CAAC,CAAC,CAAC;UACtBd,MAAM,CAACiB,CAAC,GAAGH,QAAQ,CAAC,CAAC,CAAC;QAC1B;MACJ;IACJ,CAAC,MAAM;MACH,IAAI,CAACC,CAAC,GAAGb,SAAS,CAAC,CAAC,CAAC;MACrB,IAAI,CAACc,CAAC,GAAGd,SAAS,CAAC,CAAC,CAAC;MACrB,IAAI,CAACe,CAAC,GAAGf,SAAS,CAAC,CAAC,CAAC;IACzB;IAEA,IAAI,CAACa,CAAC,GAAG,IAAI,CAACG,aAAa,CAAC,IAAI,CAACH,CAAC,CAAC;IACnC,IAAI,CAACC,CAAC,GAAG,IAAI,CAACE,aAAa,CAAC,IAAI,CAACF,CAAC,CAAC;IACnC,IAAI,CAACC,CAAC,GAAG,IAAI,CAACC,aAAa,CAAC,IAAI,CAACD,CAAC,CAAC;EACvC;EAEA,IAAK1B,KAAK,EAAGO,KAAK,CAACqB,SAAS,GAAG5B,KAAK;EACpCO,KAAK,CAACsB,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAE/B,KAAK,IAAIA,KAAK,CAAC6B,SAAU,CAAC;EAC3DtB,KAAK,CAACsB,SAAS,CAACG,WAAW,GAAGzB,KAAK;EAEnCA,KAAK,CAACsB,SAAS,CAACI,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAI;IACtC,IAAIC,GAAG,GAAG,IAAI,CAACC,QAAQ;IACvB,IAAIX,CAAC,GAAG,IAAI,CAACA,CAAC,CAACY,QAAQ,CAAC,EAAE,CAAC;IAC3B,IAAIX,CAAC,GAAG,IAAI,CAACA,CAAC,CAACW,QAAQ,CAAC,EAAE,CAAC;IAC3B,IAAIV,CAAC,GAAG,IAAI,CAACA,CAAC,CAACU,QAAQ,CAAC,EAAE,CAAC;IAE3B,OAAO,GAAG,GAAGF,GAAG,CAACV,CAAC,CAAC,GAAGU,GAAG,CAACT,CAAC,CAAC,GAAGS,GAAG,CAACR,CAAC,CAAC;EACzC,CAAC;EAEDnB,KAAK,CAACsB,SAAS,CAACd,YAAY,GAAG,SAASA,YAAYA,CAAEP,KAAK,EAAE;IACzD,IAAI6B,KAAK,GAAG7B,KAAK,IAAI,OAAO;IAE5B,IAAI6B,KAAK,CAACC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACzBD,KAAK,GAAGA,KAAK,CAACE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IAC9B;IAEAF,KAAK,GAAGA,KAAK,CAACG,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;IAC/BH,KAAK,GAAGA,KAAK,CAACI,WAAW,CAAC,CAAC;IAC3BJ,KAAK,GAAG9B,KAAK,CAACN,WAAW,CAACoC,KAAK,CAAC,IAAIA,KAAK;IAEzC,OAAOA,KAAK;EAChB,CAAC;EAED9B,KAAK,CAACsB,SAAS,CAACF,aAAa,GAAG,SAASA,aAAaA,CAAEnB,KAAK,EAAE;IAC3D,IAAIA,KAAK,GAAG,CAAC,IAAIkC,KAAK,CAAClC,KAAK,CAAC,EAAE;MAC3B,OAAO,CAAC;IACZ;IAEA,OAAOA,KAAK,GAAG,GAAG,GAAG,GAAG,GAAGA,KAAK;EACpC,CAAC;EAEDD,KAAK,CAACsB,SAAS,CAACM,QAAQ,GAAG,SAASA,QAAQA,CAAE3B,KAAK,EAAE;IACjD,OAAQA,KAAK,CAACI,MAAM,KAAK,CAAC,GAAI,GAAG,GAAGJ,KAAK,GAAGA,KAAK;EACrD,CAAC;EAEDD,KAAK,CAACsB,SAAS,CAACc,UAAU,GAAG,SAASA,UAAUA,CAAEnC,KAAK,EAAE;IACrD,IAAIoC,KAAK,GAAGC,IAAI,CAACD,KAAK;IAEtB,IAAI,CAACpB,CAAC,GAAGoB,KAAK,CAAC,IAAI,CAACjB,aAAa,CAAC,IAAI,CAACH,CAAC,GAAGhB,KAAK,CAAC,CAAC;IAClD,IAAI,CAACiB,CAAC,GAAGmB,KAAK,CAAC,IAAI,CAACjB,aAAa,CAAC,IAAI,CAACF,CAAC,GAAGjB,KAAK,CAAC,CAAC;IAClD,IAAI,CAACkB,CAAC,GAAGkB,KAAK,CAAC,IAAI,CAACjB,aAAa,CAAC,IAAI,CAACD,CAAC,GAAGlB,KAAK,CAAC,CAAC;IAElD,OAAO,IAAI;EACf,CAAC;EAEDD,KAAK,CAACsB,SAAS,CAACiB,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAI;IACxD,OAAOD,IAAI,CAACE,IAAI,CAAC,KAAK,GAAG,IAAI,CAACvB,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG,KAAK,GAAG,IAAI,CAACC,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG,KAAK,GAAG,IAAI,CAACC,CAAC,GAAG,IAAI,CAACA,CAAC,CAAC;EACjG,CAAC;EAEDnB,KAAK,CAACsB,SAAS,CAACmB,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAI;IACxC,OAAO,IAAI,CAACF,cAAc,CAAC,CAAC,GAAGxC,aAAa;EAChD,CAAC;EAEDC,KAAK,CAAC0C,SAAS,GAAG,SAASA,SAASA,CAAEzB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEwB,CAAC,EAAE;IAC9C,OAAO,IAAIhD,KAAK,CAACsB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEwB,CAAC,IAAI,IAAI,GAAGA,CAAC,GAAG,CAAC,CAAC;EAChD,CAAC;EAED3C,KAAK,CAAC4C,OAAO,GAAG,SAASA,OAAOA,CAAE3B,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEwB,CAAC,EAAE;IAC1C,OAAO,IAAI/C,GAAG,CAACqB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEwB,CAAC,IAAI,IAAI,GAAGA,CAAC,GAAG,CAAC,CAAC;EAC9C,CAAC;EAED3C,KAAK,CAAC6C,OAAO,GAAG,SAASA,OAAOA,CAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEL,CAAC,EAAE;IAC1C,OAAO,IAAI9C,GAAG,CAACiD,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEL,CAAC,IAAI,IAAI,GAAGA,CAAC,GAAG,CAAC,CAAC;EAC9C,CAAC;EAED3C,KAAK,CAACiD,OAAO,GAAG,SAASA,OAAOA,CAAEH,CAAC,EAAEC,CAAC,EAAEG,CAAC,EAAEP,CAAC,EAAE;IAC1C,OAAO,IAAI7C,GAAG,CAACgD,CAAC,EAAEC,CAAC,EAAEG,CAAC,EAAEP,CAAC,IAAI,IAAI,GAAGA,CAAC,GAAG,CAAC,CAAC;EAC9C,CAAC;EAED,OAAO3C,KAAK;AAChB,CAAC,CAACP,KAAK,CAAE;AAETO,KAAK,CAACM,OAAO,GAAG,CAAE;EACdK,EAAE,EAAE,8CAA8C;EAClDE,OAAO,EAAE,SAAAA,CAASC,KAAK,EAAE;IACrB,OAAO,CACHqC,QAAQ,CAACrC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAEqC,QAAQ,CAACrC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAEqC,QAAQ,CAACrC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CACzE;EACL;AACJ,CAAC,EAAE;EACCH,EAAE,EAAE,yBAAyB;EAC7BE,OAAO,EAAE,SAAAA,CAASC,KAAK,EAAE;IACrB,OAAO,CACHqC,QAAQ,CAACrC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAEqC,QAAQ,CAACrC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAEqC,QAAQ,CAACrC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CACzE;EACL;AACJ,CAAC,EAAE;EACCH,EAAE,EAAE,yBAAyB;EAC7BE,OAAO,EAAE,SAAAA,CAASC,KAAK,EAAE;IACrB,OAAO,CACHqC,QAAQ,CAACrC,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EACjCqC,QAAQ,CAACrC,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EACjCqC,QAAQ,CAACrC,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CACpC;EACL;AACJ,CAAC,CAAE;AAEHd,KAAK,CAACN,WAAW,GAAGA,WAAW;AAE/B,eAAeM,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}