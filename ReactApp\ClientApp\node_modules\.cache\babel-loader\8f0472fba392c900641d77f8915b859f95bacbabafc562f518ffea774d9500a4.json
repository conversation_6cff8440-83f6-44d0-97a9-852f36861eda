{"ast": null, "code": "import PathNode from './path-node';\nimport renderPath from './utils/render-path';\nvar MultiPathNode = function (PathNode) {\n  function MultiPathNode() {\n    PathNode.apply(this, arguments);\n  }\n  if (PathNode) MultiPathNode.__proto__ = PathNode;\n  MultiPathNode.prototype = Object.create(PathNode && PathNode.prototype);\n  MultiPathNode.prototype.constructor = MultiPathNode;\n  MultiPathNode.prototype.renderPoints = function renderPoints(ctx) {\n    var paths = this.srcElement.paths;\n    for (var i = 0; i < paths.length; i++) {\n      renderPath(ctx, paths[i]);\n    }\n  };\n  return MultiPathNode;\n}(PathNode);\nexport default MultiPathNode;", "map": {"version": 3, "names": ["PathNode", "<PERSON><PERSON><PERSON>", "MultiPathNode", "apply", "arguments", "__proto__", "prototype", "Object", "create", "constructor", "renderPoints", "ctx", "paths", "srcElement", "i", "length"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/canvas/multi-path-node.js"], "sourcesContent": ["import PathNode from './path-node';\nimport renderPath from './utils/render-path';\n\nvar MultiPathNode = (function (PathNode) {\n    function MultiPathNode () {\n        PathNode.apply(this, arguments);\n    }\n\n    if ( PathNode ) MultiPathNode.__proto__ = PathNode;\n    MultiPathNode.prototype = Object.create( PathNode && PathNode.prototype );\n    MultiPathNode.prototype.constructor = MultiPathNode;\n\n    MultiPathNode.prototype.renderPoints = function renderPoints (ctx) {\n        var paths = this.srcElement.paths;\n        for (var i = 0; i < paths.length; i++) {\n            renderPath(ctx, paths[i]);\n        }\n    };\n\n    return MultiPathNode;\n}(PathNode));\n\nexport default MultiPathNode;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,aAAa;AAClC,OAAOC,UAAU,MAAM,qBAAqB;AAE5C,IAAIC,aAAa,GAAI,UAAUF,QAAQ,EAAE;EACrC,SAASE,aAAaA,CAAA,EAAI;IACtBF,QAAQ,CAACG,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACnC;EAEA,IAAKJ,QAAQ,EAAGE,aAAa,CAACG,SAAS,GAAGL,QAAQ;EAClDE,aAAa,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAER,QAAQ,IAAIA,QAAQ,CAACM,SAAU,CAAC;EACzEJ,aAAa,CAACI,SAAS,CAACG,WAAW,GAAGP,aAAa;EAEnDA,aAAa,CAACI,SAAS,CAACI,YAAY,GAAG,SAASA,YAAYA,CAAEC,GAAG,EAAE;IAC/D,IAAIC,KAAK,GAAG,IAAI,CAACC,UAAU,CAACD,KAAK;IACjC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACnCb,UAAU,CAACU,GAAG,EAAEC,KAAK,CAACE,CAAC,CAAC,CAAC;IAC7B;EACJ,CAAC;EAED,OAAOZ,aAAa;AACxB,CAAC,CAACF,QAAQ,CAAE;AAEZ,eAAeE,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}