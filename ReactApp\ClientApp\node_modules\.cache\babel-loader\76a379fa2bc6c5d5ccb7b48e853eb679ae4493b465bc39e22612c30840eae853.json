{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { isArray as I, resolveItemsIds as g, updateItem as C, getNestedValue as E } from \"@progress/kendo-react-common\";\nimport { EXPAND_FIELD as D, SELECT_FIELD as L, CHECK_FIELD as o, CHILDREN_FIELD as N, CHECK_INDETERMINATE_FIELD as _ } from \"./utils/consts.mjs\";\nfunction A(n, t) {\n  if (!n || !n.length) return [];\n  let e = n;\n  const l = t.cloneField || \"cloned\",\n    f = t.expandField || D,\n    s = t.selectField || L,\n    c = t.checkField || o,\n    i = t.childrenField || N;\n  return e = r(e, f, t.expand, l, i), e = r(e, s, t.select, l, i), e = r(e, c, t.check, l, i), p(e, i, t.check), e;\n}\nfunction r(n, t, e, l, f) {\n  if (e) {\n    const {\n        ids: s,\n        field: c\n      } = m(e, t),\n      i = !I(e) && e.idField ? g(s, e.idField, n, f) : s;\n    return h(n, i, c, l, f);\n  }\n  return n;\n}\nfunction m(n, t) {\n  let e, l;\n  return I(n) ? (e = n, l = t) : (e = n.ids || [], l = n.operationField || t), {\n    ids: e,\n    field: l\n  };\n}\nfunction h(n, t, e, l, f) {\n  let s = n;\n  return t.forEach(c => {\n    s = C(s, c, i => F(e, i), l, f);\n  }), s;\n}\nfunction F(n, t) {\n  const e = (n || \"\").split(\".\");\n  let l = t;\n  for (let f = 0; f < e.length; f++) {\n    const s = e[f];\n    if (f === e.length - 1) l[s] = !0;else if (l[s] !== void 0) l[s] = {\n      ...l[s]\n    }, l = l[s];else return;\n  }\n}\nfunction p(n, t, e) {\n  if (e && !I(e) && e.applyCheckIndeterminate) {\n    const {\n        field: l\n      } = m(e, o),\n      f = e.checkIndeterminateField || _;\n    for (let s = 0; s < n.length; s++) {\n      const c = n[s],\n        i = c[t];\n      i && u(i, E(l, c) ? [] : [c], t, l, f);\n    }\n  }\n}\nfunction u(n, t, e, l, f) {\n  let s = !1;\n  for (let c = 0; c < n.length; c++) {\n    const i = n[c];\n    if (E(l, i)) {\n      if (!s) for (let d = 0; d < t.length; d++) F(f, t[d]);\n      s = !0, i[e] && u(i[e], [], e, l, f);\n    } else i[e] && u(i[e], s ? [i] : t.concat([i]), e, l, f);\n  }\n}\nexport { A as processTreeViewItems };", "map": {"version": 3, "names": ["isArray", "I", "resolveItemsIds", "g", "updateItem", "C", "getNestedValue", "E", "EXPAND_FIELD", "D", "SELECT_FIELD", "L", "CHECK_FIELD", "o", "CHILDREN_FIELD", "N", "CHECK_INDETERMINATE_FIELD", "_", "A", "n", "t", "length", "e", "l", "cloneField", "f", "expandField", "s", "selectField", "c", "checkField", "i", "childrenField", "r", "expand", "select", "check", "p", "ids", "field", "m", "idField", "h", "operationField", "for<PERSON>ach", "F", "split", "applyCheckIndeterminate", "checkIndeterminateField", "u", "d", "concat", "processTreeViewItems"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-treeview/processTreeViewItems.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { isArray as I, resolveItemsIds as g, updateItem as C, getNestedValue as E } from \"@progress/kendo-react-common\";\nimport { EXPAND_FIELD as D, SELECT_FIELD as L, CHECK_FIELD as o, CHILDREN_FIELD as N, CHECK_INDETERMINATE_FIELD as _ } from \"./utils/consts.mjs\";\nfunction A(n, t) {\n  if (!n || !n.length)\n    return [];\n  let e = n;\n  const l = t.cloneField || \"cloned\", f = t.expandField || D, s = t.selectField || L, c = t.checkField || o, i = t.childrenField || N;\n  return e = r(e, f, t.expand, l, i), e = r(e, s, t.select, l, i), e = r(e, c, t.check, l, i), p(e, i, t.check), e;\n}\nfunction r(n, t, e, l, f) {\n  if (e) {\n    const { ids: s, field: c } = m(e, t), i = !I(e) && e.idField ? g(s, e.idField, n, f) : s;\n    return h(n, i, c, l, f);\n  }\n  return n;\n}\nfunction m(n, t) {\n  let e, l;\n  return I(n) ? (e = n, l = t) : (e = n.ids || [], l = n.operationField || t), { ids: e, field: l };\n}\nfunction h(n, t, e, l, f) {\n  let s = n;\n  return t.forEach((c) => {\n    s = C(s, c, (i) => F(e, i), l, f);\n  }), s;\n}\nfunction F(n, t) {\n  const e = (n || \"\").split(\".\");\n  let l = t;\n  for (let f = 0; f < e.length; f++) {\n    const s = e[f];\n    if (f === e.length - 1)\n      l[s] = !0;\n    else if (l[s] !== void 0)\n      l[s] = { ...l[s] }, l = l[s];\n    else\n      return;\n  }\n}\nfunction p(n, t, e) {\n  if (e && !I(e) && e.applyCheckIndeterminate) {\n    const { field: l } = m(e, o), f = e.checkIndeterminateField || _;\n    for (let s = 0; s < n.length; s++) {\n      const c = n[s], i = c[t];\n      i && u(\n        i,\n        E(l, c) ? [] : [c],\n        t,\n        l,\n        f\n      );\n    }\n  }\n}\nfunction u(n, t, e, l, f) {\n  let s = !1;\n  for (let c = 0; c < n.length; c++) {\n    const i = n[c];\n    if (E(l, i)) {\n      if (!s)\n        for (let d = 0; d < t.length; d++)\n          F(f, t[d]);\n      s = !0, i[e] && u(\n        i[e],\n        [],\n        e,\n        l,\n        f\n      );\n    } else\n      i[e] && u(\n        i[e],\n        s ? [i] : t.concat([i]),\n        e,\n        l,\n        f\n      );\n  }\n}\nexport {\n  A as processTreeViewItems\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,OAAO,IAAIC,CAAC,EAAEC,eAAe,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,EAAEC,cAAc,IAAIC,CAAC,QAAQ,8BAA8B;AACvH,SAASC,YAAY,IAAIC,CAAC,EAAEC,YAAY,IAAIC,CAAC,EAAEC,WAAW,IAAIC,CAAC,EAAEC,cAAc,IAAIC,CAAC,EAAEC,yBAAyB,IAAIC,CAAC,QAAQ,oBAAoB;AAChJ,SAASC,CAACA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACf,IAAI,CAACD,CAAC,IAAI,CAACA,CAAC,CAACE,MAAM,EACjB,OAAO,EAAE;EACX,IAAIC,CAAC,GAAGH,CAAC;EACT,MAAMI,CAAC,GAAGH,CAAC,CAACI,UAAU,IAAI,QAAQ;IAAEC,CAAC,GAAGL,CAAC,CAACM,WAAW,IAAIjB,CAAC;IAAEkB,CAAC,GAAGP,CAAC,CAACQ,WAAW,IAAIjB,CAAC;IAAEkB,CAAC,GAAGT,CAAC,CAACU,UAAU,IAAIjB,CAAC;IAAEkB,CAAC,GAAGX,CAAC,CAACY,aAAa,IAAIjB,CAAC;EACnI,OAAOO,CAAC,GAAGW,CAAC,CAACX,CAAC,EAAEG,CAAC,EAAEL,CAAC,CAACc,MAAM,EAAEX,CAAC,EAAEQ,CAAC,CAAC,EAAET,CAAC,GAAGW,CAAC,CAACX,CAAC,EAAEK,CAAC,EAAEP,CAAC,CAACe,MAAM,EAAEZ,CAAC,EAAEQ,CAAC,CAAC,EAAET,CAAC,GAAGW,CAAC,CAACX,CAAC,EAAEO,CAAC,EAAET,CAAC,CAACgB,KAAK,EAAEb,CAAC,EAAEQ,CAAC,CAAC,EAAEM,CAAC,CAACf,CAAC,EAAES,CAAC,EAAEX,CAAC,CAACgB,KAAK,CAAC,EAAEd,CAAC;AAClH;AACA,SAASW,CAACA,CAACd,CAAC,EAAEC,CAAC,EAAEE,CAAC,EAAEC,CAAC,EAAEE,CAAC,EAAE;EACxB,IAAIH,CAAC,EAAE;IACL,MAAM;QAAEgB,GAAG,EAAEX,CAAC;QAAEY,KAAK,EAAEV;MAAE,CAAC,GAAGW,CAAC,CAAClB,CAAC,EAAEF,CAAC,CAAC;MAAEW,CAAC,GAAG,CAAC9B,CAAC,CAACqB,CAAC,CAAC,IAAIA,CAAC,CAACmB,OAAO,GAAGtC,CAAC,CAACwB,CAAC,EAAEL,CAAC,CAACmB,OAAO,EAAEtB,CAAC,EAAEM,CAAC,CAAC,GAAGE,CAAC;IACxF,OAAOe,CAAC,CAACvB,CAAC,EAAEY,CAAC,EAAEF,CAAC,EAAEN,CAAC,EAAEE,CAAC,CAAC;EACzB;EACA,OAAON,CAAC;AACV;AACA,SAASqB,CAACA,CAACrB,CAAC,EAAEC,CAAC,EAAE;EACf,IAAIE,CAAC,EAAEC,CAAC;EACR,OAAOtB,CAAC,CAACkB,CAAC,CAAC,IAAIG,CAAC,GAAGH,CAAC,EAAEI,CAAC,GAAGH,CAAC,KAAKE,CAAC,GAAGH,CAAC,CAACmB,GAAG,IAAI,EAAE,EAAEf,CAAC,GAAGJ,CAAC,CAACwB,cAAc,IAAIvB,CAAC,CAAC,EAAE;IAAEkB,GAAG,EAAEhB,CAAC;IAAEiB,KAAK,EAAEhB;EAAE,CAAC;AACnG;AACA,SAASmB,CAACA,CAACvB,CAAC,EAAEC,CAAC,EAAEE,CAAC,EAAEC,CAAC,EAAEE,CAAC,EAAE;EACxB,IAAIE,CAAC,GAAGR,CAAC;EACT,OAAOC,CAAC,CAACwB,OAAO,CAAEf,CAAC,IAAK;IACtBF,CAAC,GAAGtB,CAAC,CAACsB,CAAC,EAAEE,CAAC,EAAGE,CAAC,IAAKc,CAAC,CAACvB,CAAC,EAAES,CAAC,CAAC,EAAER,CAAC,EAAEE,CAAC,CAAC;EACnC,CAAC,CAAC,EAAEE,CAAC;AACP;AACA,SAASkB,CAACA,CAAC1B,CAAC,EAAEC,CAAC,EAAE;EACf,MAAME,CAAC,GAAG,CAACH,CAAC,IAAI,EAAE,EAAE2B,KAAK,CAAC,GAAG,CAAC;EAC9B,IAAIvB,CAAC,GAAGH,CAAC;EACT,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,CAACD,MAAM,EAAEI,CAAC,EAAE,EAAE;IACjC,MAAME,CAAC,GAAGL,CAAC,CAACG,CAAC,CAAC;IACd,IAAIA,CAAC,KAAKH,CAAC,CAACD,MAAM,GAAG,CAAC,EACpBE,CAAC,CAACI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KACP,IAAIJ,CAAC,CAACI,CAAC,CAAC,KAAK,KAAK,CAAC,EACtBJ,CAAC,CAACI,CAAC,CAAC,GAAG;MAAE,GAAGJ,CAAC,CAACI,CAAC;IAAE,CAAC,EAAEJ,CAAC,GAAGA,CAAC,CAACI,CAAC,CAAC,CAAC,KAE7B;EACJ;AACF;AACA,SAASU,CAACA,CAAClB,CAAC,EAAEC,CAAC,EAAEE,CAAC,EAAE;EAClB,IAAIA,CAAC,IAAI,CAACrB,CAAC,CAACqB,CAAC,CAAC,IAAIA,CAAC,CAACyB,uBAAuB,EAAE;IAC3C,MAAM;QAAER,KAAK,EAAEhB;MAAE,CAAC,GAAGiB,CAAC,CAAClB,CAAC,EAAET,CAAC,CAAC;MAAEY,CAAC,GAAGH,CAAC,CAAC0B,uBAAuB,IAAI/B,CAAC;IAChE,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,CAAC,CAACE,MAAM,EAAEM,CAAC,EAAE,EAAE;MACjC,MAAME,CAAC,GAAGV,CAAC,CAACQ,CAAC,CAAC;QAAEI,CAAC,GAAGF,CAAC,CAACT,CAAC,CAAC;MACxBW,CAAC,IAAIkB,CAAC,CACJlB,CAAC,EACDxB,CAAC,CAACgB,CAAC,EAAEM,CAAC,CAAC,GAAG,EAAE,GAAG,CAACA,CAAC,CAAC,EAClBT,CAAC,EACDG,CAAC,EACDE,CACF,CAAC;IACH;EACF;AACF;AACA,SAASwB,CAACA,CAAC9B,CAAC,EAAEC,CAAC,EAAEE,CAAC,EAAEC,CAAC,EAAEE,CAAC,EAAE;EACxB,IAAIE,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,CAAC,CAACE,MAAM,EAAEQ,CAAC,EAAE,EAAE;IACjC,MAAME,CAAC,GAAGZ,CAAC,CAACU,CAAC,CAAC;IACd,IAAItB,CAAC,CAACgB,CAAC,EAAEQ,CAAC,CAAC,EAAE;MACX,IAAI,CAACJ,CAAC,EACJ,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9B,CAAC,CAACC,MAAM,EAAE6B,CAAC,EAAE,EAC/BL,CAAC,CAACpB,CAAC,EAAEL,CAAC,CAAC8B,CAAC,CAAC,CAAC;MACdvB,CAAC,GAAG,CAAC,CAAC,EAAEI,CAAC,CAACT,CAAC,CAAC,IAAI2B,CAAC,CACflB,CAAC,CAACT,CAAC,CAAC,EACJ,EAAE,EACFA,CAAC,EACDC,CAAC,EACDE,CACF,CAAC;IACH,CAAC,MACCM,CAAC,CAACT,CAAC,CAAC,IAAI2B,CAAC,CACPlB,CAAC,CAACT,CAAC,CAAC,EACJK,CAAC,GAAG,CAACI,CAAC,CAAC,GAAGX,CAAC,CAAC+B,MAAM,CAAC,CAACpB,CAAC,CAAC,CAAC,EACvBT,CAAC,EACDC,CAAC,EACDE,CACF,CAAC;EACL;AACF;AACA,SACEP,CAAC,IAAIkC,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}