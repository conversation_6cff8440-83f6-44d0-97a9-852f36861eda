{"ast": null, "code": "import { __spreadArray } from \"tslib\";\nimport { cloneDate } from '@progress/kendo-date-math';\nvar isObject = function (value) {\n  return value && typeof value === \"object\" && !Array.isArray(value);\n};\nvar isHtmlElement = function (element) {\n  return element instanceof HTMLElement;\n};\nvar dateSetter = function (method) {\n  return function (date, value) {\n    var clone = cloneDate(date);\n    clone[method](value);\n    return clone;\n  };\n};\n/**\n * @hidden\n */\nexport var isPresent = function (value) {\n  return value !== undefined && value !== null;\n};\n/**\n * @hidden\n */\nexport var isDocumentAvailable = function () {\n  return !!document;\n};\n/**\n * @hidden\n */\nexport var isNumber = function (value) {\n  return isPresent(value) && typeof value === \"number\" && !Number.isNaN(value);\n};\n/**\n * @hidden\n */\nexport var parseToInt = function (value) {\n  return parseInt(value, 10);\n};\n/**\n * @hidden\n */\nexport var isParseableToInt = function (value) {\n  return isNumber(parseToInt(value)) && /^[0-9]+$/.test(value);\n};\n/**\n * @hidden\n */\nexport var clamp = function (value, min, max) {\n  return Math.min(max, Math.max(min, value));\n};\n/**\n * @hidden\n */\n// eslint-disable-next-line @typescript-eslint/ban-ts-comment\n// @ts-ignore\nexport var extend = function () {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  return Object.assign.apply(Object, args);\n};\n/**\n * @hidden\n */\nexport var deepExtend = function (target) {\n  var _a, _b;\n  var sources = [];\n  for (var _i = 1; _i < arguments.length; _i++) {\n    sources[_i - 1] = arguments[_i];\n  }\n  if (!sources.length) {\n    return target;\n  }\n  var source = sources.shift();\n  if (isObject(target) && isObject(source)) {\n    for (var key in source) {\n      if (!Object.prototype.hasOwnProperty.call(source, key) || key === \"__proto__\" || key === \"constructor\" || key === \"prototype\") {\n        continue;\n      }\n      if (isHtmlElement(source[key])) {\n        target[key] = source[key];\n      } else if (isObject(source[key]) && !(source[key] instanceof Date)) {\n        if (!target[key] || !isObject(target[key])) {\n          extend(target, (_a = {}, _a[key] = {}, _a));\n        }\n        deepExtend(target[key], source[key]);\n      } else {\n        extend(target, (_b = {}, _b[key] = source[key], _b));\n      }\n    }\n  }\n  return deepExtend.apply(void 0, __spreadArray([target], sources, false));\n};\n/**\n * @hidden\n */\n// eslint-disable-next-line\nexport var noop = function () {};\n/**\n * @hidden\n */\nexport var isFunction = function (fn) {\n  return typeof fn === \"function\";\n};\n/**\n * @hidden\n */\nexport var cropTwoDigitYear = function (date) {\n  if (!isPresent(date) || isNaN(date.getTime())) {\n    return 0;\n  }\n  return Number(date.getFullYear().toString().slice(-2));\n};\n/**\n * @hidden\n */\nexport var setYears = dateSetter('setFullYear');\n/**\n * @hidden\n */\nexport var millisecondDigitsInFormat = function (format) {\n  var result = format && format.match(/S+(\\1)/);\n  return result ? result[0].length : 0;\n};\n/**\n * @hidden\n */\nexport var millisecondStepFor = function (digits) {\n  return Math.pow(10, 3 - digits);\n};\n/**\n * @hidden\n */\nexport var areDatePartsEqualTo = function (date, year, month, day, hour, minutes, seconds, milliseconds) {\n  if (date && date.getFullYear() === year && date.getMonth() === month && date.getDate() === day && date.getHours() === hour && date.getMinutes() === minutes && date.getSeconds() === seconds && date.getMilliseconds() === milliseconds) {\n    return true;\n  }\n  return false;\n};\n/**\n * @hidden\n */\nexport var isValidDate = function (value) {\n  return isPresent(value) && value.getTime && isNumber(value.getTime());\n};\n/**\n * @hidden\n */\nexport var isIOS = function () {\n  return /iPad|iPhone|iPod/.test(navigator.userAgent) || navigator.maxTouchPoints && navigator.maxTouchPoints > 2 && /Macintosh/i.test(navigator.userAgent);\n};", "map": {"version": 3, "names": ["__spread<PERSON><PERSON>y", "cloneDate", "isObject", "value", "Array", "isArray", "isHtmlElement", "element", "HTMLElement", "dateSetter", "method", "date", "clone", "isPresent", "undefined", "isDocumentAvailable", "document", "isNumber", "Number", "isNaN", "parseToInt", "parseInt", "isParseableToInt", "test", "clamp", "min", "max", "Math", "extend", "args", "_i", "arguments", "length", "Object", "assign", "apply", "deepExtend", "target", "_a", "_b", "sources", "source", "shift", "key", "prototype", "hasOwnProperty", "call", "Date", "noop", "isFunction", "fn", "cropTwoDigitYear", "getTime", "getFullYear", "toString", "slice", "set<PERSON>ears", "millisecondDigitsInFormat", "format", "result", "match", "millisecondStepFor", "digits", "pow", "areDatePartsEqualTo", "year", "month", "day", "hour", "minutes", "seconds", "milliseconds", "getMonth", "getDate", "getHours", "getMinutes", "getSeconds", "getMilliseconds", "isValidDate", "isIOS", "navigator", "userAgent", "maxTouchPoints"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-dateinputs-common/dist/es/common/utils.js"], "sourcesContent": ["import { __spreadArray } from \"tslib\";\nimport { cloneDate } from '@progress/kendo-date-math';\nvar isObject = function (value) { return value && typeof (value) === \"object\" && !Array.isArray(value); };\nvar isHtmlElement = function (element) { return element instanceof HTMLElement; };\nvar dateSetter = function (method) { return function (date, value) {\n    var clone = cloneDate(date);\n    clone[method](value);\n    return clone;\n}; };\n/**\n * @hidden\n */\nexport var isPresent = function (value) { return value !== undefined && value !== null; };\n/**\n * @hidden\n */\nexport var isDocumentAvailable = function () { return !!document; };\n/**\n * @hidden\n */\nexport var isNumber = function (value) { return isPresent(value) && typeof (value) === \"number\" && !Number.isNaN(value); };\n/**\n * @hidden\n */\nexport var parseToInt = function (value) { return parseInt(value, 10); };\n/**\n * @hidden\n */\nexport var isParseableToInt = function (value) { return isNumber(parseToInt(value)) && /^[0-9]+$/.test(value); };\n/**\n * @hidden\n */\nexport var clamp = function (value, min, max) { return Math.min(max, Math.max(min, value)); };\n/**\n * @hidden\n */\n// eslint-disable-next-line @typescript-eslint/ban-ts-comment\n// @ts-ignore\nexport var extend = function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    return Object.assign.apply(Object, args);\n};\n/**\n * @hidden\n */\nexport var deepExtend = function (target) {\n    var _a, _b;\n    var sources = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        sources[_i - 1] = arguments[_i];\n    }\n    if (!sources.length) {\n        return target;\n    }\n    var source = sources.shift();\n    if (isObject(target) && isObject(source)) {\n        for (var key in source) {\n            if (!Object.prototype.hasOwnProperty.call(source, key) ||\n                key === \"__proto__\" ||\n                key === \"constructor\" ||\n                key === \"prototype\") {\n                continue;\n            }\n            if (isHtmlElement(source[key])) {\n                target[key] = source[key];\n            }\n            else if (isObject(source[key]) && !(source[key] instanceof Date)) {\n                if (!target[key] || !isObject(target[key])) {\n                    extend(target, (_a = {}, _a[key] = {}, _a));\n                }\n                deepExtend(target[key], source[key]);\n            }\n            else {\n                extend(target, (_b = {}, _b[key] = source[key], _b));\n            }\n        }\n    }\n    return deepExtend.apply(void 0, __spreadArray([target], sources, false));\n};\n/**\n * @hidden\n */\n// eslint-disable-next-line\nexport var noop = function () { };\n/**\n * @hidden\n */\nexport var isFunction = function (fn) { return typeof (fn) === \"function\"; };\n/**\n * @hidden\n */\nexport var cropTwoDigitYear = function (date) {\n    if (!isPresent(date) || isNaN(date.getTime())) {\n        return 0;\n    }\n    return Number(date\n        .getFullYear()\n        .toString()\n        .slice(-2));\n};\n/**\n * @hidden\n */\nexport var setYears = dateSetter('setFullYear');\n/**\n * @hidden\n */\nexport var millisecondDigitsInFormat = function (format) {\n    var result = format && format.match(/S+(\\1)/);\n    return result ? result[0].length : 0;\n};\n/**\n * @hidden\n */\nexport var millisecondStepFor = function (digits) {\n    return Math.pow(10, 3 - digits);\n};\n/**\n * @hidden\n */\nexport var areDatePartsEqualTo = function (date, year, month, day, hour, minutes, seconds, milliseconds) {\n    if (date &&\n        date.getFullYear() === year &&\n        date.getMonth() === month &&\n        date.getDate() === day &&\n        date.getHours() === hour &&\n        date.getMinutes() === minutes &&\n        date.getSeconds() === seconds &&\n        date.getMilliseconds() === milliseconds) {\n        return true;\n    }\n    return false;\n};\n/**\n * @hidden\n */\nexport var isValidDate = function (value) { return isPresent(value) && value.getTime && isNumber(value.getTime()); };\n/**\n * @hidden\n */\nexport var isIOS = function () { return /iPad|iPhone|iPod/.test(navigator.userAgent) || (navigator.maxTouchPoints &&\n    navigator.maxTouchPoints > 2 &&\n    /Macintosh/i.test(navigator.userAgent)); };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;AACrC,SAASC,SAAS,QAAQ,2BAA2B;AACrD,IAAIC,QAAQ,GAAG,SAAAA,CAAUC,KAAK,EAAE;EAAE,OAAOA,KAAK,IAAI,OAAQA,KAAM,KAAK,QAAQ,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC;AAAE,CAAC;AACzG,IAAIG,aAAa,GAAG,SAAAA,CAAUC,OAAO,EAAE;EAAE,OAAOA,OAAO,YAAYC,WAAW;AAAE,CAAC;AACjF,IAAIC,UAAU,GAAG,SAAAA,CAAUC,MAAM,EAAE;EAAE,OAAO,UAAUC,IAAI,EAAER,KAAK,EAAE;IAC/D,IAAIS,KAAK,GAAGX,SAAS,CAACU,IAAI,CAAC;IAC3BC,KAAK,CAACF,MAAM,CAAC,CAACP,KAAK,CAAC;IACpB,OAAOS,KAAK;EAChB,CAAC;AAAE,CAAC;AACJ;AACA;AACA;AACA,OAAO,IAAIC,SAAS,GAAG,SAAAA,CAAUV,KAAK,EAAE;EAAE,OAAOA,KAAK,KAAKW,SAAS,IAAIX,KAAK,KAAK,IAAI;AAAE,CAAC;AACzF;AACA;AACA;AACA,OAAO,IAAIY,mBAAmB,GAAG,SAAAA,CAAA,EAAY;EAAE,OAAO,CAAC,CAACC,QAAQ;AAAE,CAAC;AACnE;AACA;AACA;AACA,OAAO,IAAIC,QAAQ,GAAG,SAAAA,CAAUd,KAAK,EAAE;EAAE,OAAOU,SAAS,CAACV,KAAK,CAAC,IAAI,OAAQA,KAAM,KAAK,QAAQ,IAAI,CAACe,MAAM,CAACC,KAAK,CAAChB,KAAK,CAAC;AAAE,CAAC;AAC1H;AACA;AACA;AACA,OAAO,IAAIiB,UAAU,GAAG,SAAAA,CAAUjB,KAAK,EAAE;EAAE,OAAOkB,QAAQ,CAAClB,KAAK,EAAE,EAAE,CAAC;AAAE,CAAC;AACxE;AACA;AACA;AACA,OAAO,IAAImB,gBAAgB,GAAG,SAAAA,CAAUnB,KAAK,EAAE;EAAE,OAAOc,QAAQ,CAACG,UAAU,CAACjB,KAAK,CAAC,CAAC,IAAI,UAAU,CAACoB,IAAI,CAACpB,KAAK,CAAC;AAAE,CAAC;AAChH;AACA;AACA;AACA,OAAO,IAAIqB,KAAK,GAAG,SAAAA,CAAUrB,KAAK,EAAEsB,GAAG,EAAEC,GAAG,EAAE;EAAE,OAAOC,IAAI,CAACF,GAAG,CAACC,GAAG,EAAEC,IAAI,CAACD,GAAG,CAACD,GAAG,EAAEtB,KAAK,CAAC,CAAC;AAAE,CAAC;AAC7F;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIyB,MAAM,GAAG,SAAAA,CAAA,EAAY;EAC5B,IAAIC,IAAI,GAAG,EAAE;EACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EAC5B;EACA,OAAOG,MAAM,CAACC,MAAM,CAACC,KAAK,CAACF,MAAM,EAAEJ,IAAI,CAAC;AAC5C,CAAC;AACD;AACA;AACA;AACA,OAAO,IAAIO,UAAU,GAAG,SAAAA,CAAUC,MAAM,EAAE;EACtC,IAAIC,EAAE,EAAEC,EAAE;EACV,IAAIC,OAAO,GAAG,EAAE;EAChB,KAAK,IAAIV,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CU,OAAO,CAACV,EAAE,GAAG,CAAC,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EACnC;EACA,IAAI,CAACU,OAAO,CAACR,MAAM,EAAE;IACjB,OAAOK,MAAM;EACjB;EACA,IAAII,MAAM,GAAGD,OAAO,CAACE,KAAK,CAAC,CAAC;EAC5B,IAAIxC,QAAQ,CAACmC,MAAM,CAAC,IAAInC,QAAQ,CAACuC,MAAM,CAAC,EAAE;IACtC,KAAK,IAAIE,GAAG,IAAIF,MAAM,EAAE;MACpB,IAAI,CAACR,MAAM,CAACW,SAAS,CAACC,cAAc,CAACC,IAAI,CAACL,MAAM,EAAEE,GAAG,CAAC,IAClDA,GAAG,KAAK,WAAW,IACnBA,GAAG,KAAK,aAAa,IACrBA,GAAG,KAAK,WAAW,EAAE;QACrB;MACJ;MACA,IAAIrC,aAAa,CAACmC,MAAM,CAACE,GAAG,CAAC,CAAC,EAAE;QAC5BN,MAAM,CAACM,GAAG,CAAC,GAAGF,MAAM,CAACE,GAAG,CAAC;MAC7B,CAAC,MACI,IAAIzC,QAAQ,CAACuC,MAAM,CAACE,GAAG,CAAC,CAAC,IAAI,EAAEF,MAAM,CAACE,GAAG,CAAC,YAAYI,IAAI,CAAC,EAAE;QAC9D,IAAI,CAACV,MAAM,CAACM,GAAG,CAAC,IAAI,CAACzC,QAAQ,CAACmC,MAAM,CAACM,GAAG,CAAC,CAAC,EAAE;UACxCf,MAAM,CAACS,MAAM,GAAGC,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,CAACK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEL,EAAE,CAAC,CAAC;QAC/C;QACAF,UAAU,CAACC,MAAM,CAACM,GAAG,CAAC,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;MACxC,CAAC,MACI;QACDf,MAAM,CAACS,MAAM,GAAGE,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,CAACI,GAAG,CAAC,GAAGF,MAAM,CAACE,GAAG,CAAC,EAAEJ,EAAE,CAAC,CAAC;MACxD;IACJ;EACJ;EACA,OAAOH,UAAU,CAACD,KAAK,CAAC,KAAK,CAAC,EAAEnC,aAAa,CAAC,CAACqC,MAAM,CAAC,EAAEG,OAAO,EAAE,KAAK,CAAC,CAAC;AAC5E,CAAC;AACD;AACA;AACA;AACA;AACA,OAAO,IAAIQ,IAAI,GAAG,SAAAA,CAAA,EAAY,CAAE,CAAC;AACjC;AACA;AACA;AACA,OAAO,IAAIC,UAAU,GAAG,SAAAA,CAAUC,EAAE,EAAE;EAAE,OAAO,OAAQA,EAAG,KAAK,UAAU;AAAE,CAAC;AAC5E;AACA;AACA;AACA,OAAO,IAAIC,gBAAgB,GAAG,SAAAA,CAAUxC,IAAI,EAAE;EAC1C,IAAI,CAACE,SAAS,CAACF,IAAI,CAAC,IAAIQ,KAAK,CAACR,IAAI,CAACyC,OAAO,CAAC,CAAC,CAAC,EAAE;IAC3C,OAAO,CAAC;EACZ;EACA,OAAOlC,MAAM,CAACP,IAAI,CACb0C,WAAW,CAAC,CAAC,CACbC,QAAQ,CAAC,CAAC,CACVC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACnB,CAAC;AACD;AACA;AACA;AACA,OAAO,IAAIC,QAAQ,GAAG/C,UAAU,CAAC,aAAa,CAAC;AAC/C;AACA;AACA;AACA,OAAO,IAAIgD,yBAAyB,GAAG,SAAAA,CAAUC,MAAM,EAAE;EACrD,IAAIC,MAAM,GAAGD,MAAM,IAAIA,MAAM,CAACE,KAAK,CAAC,QAAQ,CAAC;EAC7C,OAAOD,MAAM,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC3B,MAAM,GAAG,CAAC;AACxC,CAAC;AACD;AACA;AACA;AACA,OAAO,IAAI6B,kBAAkB,GAAG,SAAAA,CAAUC,MAAM,EAAE;EAC9C,OAAOnC,IAAI,CAACoC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAGD,MAAM,CAAC;AACnC,CAAC;AACD;AACA;AACA;AACA,OAAO,IAAIE,mBAAmB,GAAG,SAAAA,CAAUrD,IAAI,EAAEsD,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,IAAI,EAAEC,OAAO,EAAEC,OAAO,EAAEC,YAAY,EAAE;EACrG,IAAI5D,IAAI,IACJA,IAAI,CAAC0C,WAAW,CAAC,CAAC,KAAKY,IAAI,IAC3BtD,IAAI,CAAC6D,QAAQ,CAAC,CAAC,KAAKN,KAAK,IACzBvD,IAAI,CAAC8D,OAAO,CAAC,CAAC,KAAKN,GAAG,IACtBxD,IAAI,CAAC+D,QAAQ,CAAC,CAAC,KAAKN,IAAI,IACxBzD,IAAI,CAACgE,UAAU,CAAC,CAAC,KAAKN,OAAO,IAC7B1D,IAAI,CAACiE,UAAU,CAAC,CAAC,KAAKN,OAAO,IAC7B3D,IAAI,CAACkE,eAAe,CAAC,CAAC,KAAKN,YAAY,EAAE;IACzC,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB,CAAC;AACD;AACA;AACA;AACA,OAAO,IAAIO,WAAW,GAAG,SAAAA,CAAU3E,KAAK,EAAE;EAAE,OAAOU,SAAS,CAACV,KAAK,CAAC,IAAIA,KAAK,CAACiD,OAAO,IAAInC,QAAQ,CAACd,KAAK,CAACiD,OAAO,CAAC,CAAC,CAAC;AAAE,CAAC;AACpH;AACA;AACA;AACA,OAAO,IAAI2B,KAAK,GAAG,SAAAA,CAAA,EAAY;EAAE,OAAO,kBAAkB,CAACxD,IAAI,CAACyD,SAAS,CAACC,SAAS,CAAC,IAAKD,SAAS,CAACE,cAAc,IAC7GF,SAAS,CAACE,cAAc,GAAG,CAAC,IAC5B,YAAY,CAAC3D,IAAI,CAACyD,SAAS,CAACC,SAAS,CAAE;AAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}