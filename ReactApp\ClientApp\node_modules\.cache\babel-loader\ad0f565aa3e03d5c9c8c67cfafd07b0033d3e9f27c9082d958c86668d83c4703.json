{"ast": null, "code": "import { defined } from '../../util';\nexport default function renderStyle(attrs) {\n  var output = \"\";\n  for (var i = 0; i < attrs.length; i++) {\n    var value = attrs[i][1];\n    if (defined(value)) {\n      output += attrs[i][0] + \":\" + value + \";\";\n    }\n  }\n  if (output !== \"\") {\n    return output;\n  }\n}", "map": {"version": 3, "names": ["defined", "renderStyle", "attrs", "output", "i", "length", "value"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/svg/utils/render-style.js"], "sourcesContent": ["import { defined } from '../../util';\n\nexport default function renderStyle(attrs) {\n    var output = \"\";\n    for (var i = 0; i < attrs.length; i++) {\n        var value = attrs[i][1];\n        if (defined(value)) {\n            output += attrs[i][0] + \":\" + value + \";\";\n        }\n    }\n\n    if (output !== \"\") {\n        return output;\n    }\n}"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AAEpC,eAAe,SAASC,WAAWA,CAACC,KAAK,EAAE;EACvC,IAAIC,MAAM,GAAG,EAAE;EACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACnC,IAAIE,KAAK,GAAGJ,KAAK,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,IAAIJ,OAAO,CAACM,KAAK,CAAC,EAAE;MAChBH,MAAM,IAAID,KAAK,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGE,KAAK,GAAG,GAAG;IAC7C;EACJ;EAEA,IAAIH,MAAM,KAAK,EAAE,EAAE;IACf,OAAOA,MAAM;EACjB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}