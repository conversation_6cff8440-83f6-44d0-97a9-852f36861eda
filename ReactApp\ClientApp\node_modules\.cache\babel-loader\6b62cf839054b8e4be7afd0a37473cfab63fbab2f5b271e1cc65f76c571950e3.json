{"ast": null, "code": "import { cldr } from './info';\nimport loadNumbersInfo from './load-numbers';\nimport loadCalendarInfo from './load-dates';\nimport localeTerritory from './territory';\nimport loadUnits from './load-units';\nfunction loadLocale(locale, info) {\n  for (var field in info) {\n    if (field === \"numbers\") {\n      loadNumbersInfo(locale, info[field]);\n    } else if (field === \"dates\") {\n      loadCalendarInfo(locale, info[field]);\n    }\n  }\n}\nexport default function load() {\n  var arguments$1 = arguments;\n  var length = arguments.length;\n  for (var idx = 0; idx < length; idx++) {\n    var entry = arguments$1[idx];\n    if (entry.main) {\n      var locale = Object.keys(entry.main)[0];\n      var info = entry.main[locale];\n      var localeInfo = cldr[locale] = cldr[locale] || {};\n      if (info.units) {\n        loadUnits(localeInfo, info.units);\n      } else {\n        localeInfo.name = localeInfo.name || locale;\n        localeInfo.identity = localeInfo.identity || info.identity;\n        localeTerritory(localeInfo);\n        loadLocale(locale, info);\n      }\n    } else if (entry.supplemental) {\n      if (entry.supplemental.weekData) {\n        cldr.supplemental.weekData = {\n          firstDay: entry.supplemental.weekData.firstDay,\n          weekendStart: entry.supplemental.weekData.weekendStart,\n          weekendEnd: entry.supplemental.weekData.weekendEnd\n        };\n      } else if (entry.supplemental.likelySubtags) {\n        cldr.supplemental.likelySubtags = Object.assign(cldr.supplemental.likelySubtags, entry.supplemental.likelySubtags);\n      } else if (entry.supplemental.currencyData) {\n        var currencyData = cldr.supplemental.currencyData;\n        currencyData.region = Object.assign(currencyData.region || {}, entry.supplemental.currencyData.region);\n        currencyData.fractions = Object.assign(currencyData.fractions || {}, entry.supplemental.currencyData.fractions);\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["cldr", "loadNumbersInfo", "loadCalendarInfo", "localeTerritory", "loadUnits", "loadLocale", "locale", "info", "field", "load", "arguments$1", "arguments", "length", "idx", "entry", "main", "Object", "keys", "localeInfo", "units", "name", "identity", "supplemental", "weekData", "firstDay", "weekendStart", "weekendEnd", "likelySubtags", "assign", "currencyData", "region", "fractions"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-intl/dist/es/cldr/load.js"], "sourcesContent": ["import { cldr } from './info';\nimport loadNumbersInfo from './load-numbers';\nimport loadCalendarInfo from './load-dates';\nimport localeTerritory from './territory';\nimport loadUnits from './load-units';\n\nfunction loadLocale(locale, info) {\n    for (var field in info) {\n        if (field === \"numbers\") {\n            loadNumbersInfo(locale, info[field]);\n        } else if (field === \"dates\") {\n            loadCalendarInfo(locale, info[field]);\n        }\n    }\n}\n\nexport default function load() {\n    var arguments$1 = arguments;\n\n    var length = arguments.length;\n    for (var idx = 0; idx < length; idx++) {\n        var entry = arguments$1[idx];\n        if (entry.main) {\n            var locale = Object.keys(entry.main)[0];\n            var info = entry.main[locale];\n            var localeInfo = cldr[locale] = cldr[locale] || {};\n\n            if (info.units) {\n                loadUnits(localeInfo, info.units);\n            } else {\n                localeInfo.name = localeInfo.name || locale;\n                localeInfo.identity = localeInfo.identity || info.identity;\n\n                localeTerritory(localeInfo);\n                loadLocale(locale, info);\n            }\n        } else if (entry.supplemental) {\n            if (entry.supplemental.weekData) {\n                cldr.supplemental.weekData = {\n                    firstDay: entry.supplemental.weekData.firstDay,\n                    weekendStart: entry.supplemental.weekData.weekendStart,\n                    weekendEnd: entry.supplemental.weekData.weekendEnd\n                };\n            } else if (entry.supplemental.likelySubtags) {\n                cldr.supplemental.likelySubtags = Object.assign(cldr.supplemental.likelySubtags, entry.supplemental.likelySubtags);\n            } else if (entry.supplemental.currencyData) {\n                var currencyData = cldr.supplemental.currencyData;\n                currencyData.region = Object.assign(currencyData.region || {}, entry.supplemental.currencyData.region);\n                currencyData.fractions = Object.assign(currencyData.fractions || {}, entry.supplemental.currencyData.fractions);\n            }\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,QAAQ;AAC7B,OAAOC,eAAe,MAAM,gBAAgB;AAC5C,OAAOC,gBAAgB,MAAM,cAAc;AAC3C,OAAOC,eAAe,MAAM,aAAa;AACzC,OAAOC,SAAS,MAAM,cAAc;AAEpC,SAASC,UAAUA,CAACC,MAAM,EAAEC,IAAI,EAAE;EAC9B,KAAK,IAAIC,KAAK,IAAID,IAAI,EAAE;IACpB,IAAIC,KAAK,KAAK,SAAS,EAAE;MACrBP,eAAe,CAACK,MAAM,EAAEC,IAAI,CAACC,KAAK,CAAC,CAAC;IACxC,CAAC,MAAM,IAAIA,KAAK,KAAK,OAAO,EAAE;MAC1BN,gBAAgB,CAACI,MAAM,EAAEC,IAAI,CAACC,KAAK,CAAC,CAAC;IACzC;EACJ;AACJ;AAEA,eAAe,SAASC,IAAIA,CAAA,EAAG;EAC3B,IAAIC,WAAW,GAAGC,SAAS;EAE3B,IAAIC,MAAM,GAAGD,SAAS,CAACC,MAAM;EAC7B,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGD,MAAM,EAAEC,GAAG,EAAE,EAAE;IACnC,IAAIC,KAAK,GAAGJ,WAAW,CAACG,GAAG,CAAC;IAC5B,IAAIC,KAAK,CAACC,IAAI,EAAE;MACZ,IAAIT,MAAM,GAAGU,MAAM,CAACC,IAAI,CAACH,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;MACvC,IAAIR,IAAI,GAAGO,KAAK,CAACC,IAAI,CAACT,MAAM,CAAC;MAC7B,IAAIY,UAAU,GAAGlB,IAAI,CAACM,MAAM,CAAC,GAAGN,IAAI,CAACM,MAAM,CAAC,IAAI,CAAC,CAAC;MAElD,IAAIC,IAAI,CAACY,KAAK,EAAE;QACZf,SAAS,CAACc,UAAU,EAAEX,IAAI,CAACY,KAAK,CAAC;MACrC,CAAC,MAAM;QACHD,UAAU,CAACE,IAAI,GAAGF,UAAU,CAACE,IAAI,IAAId,MAAM;QAC3CY,UAAU,CAACG,QAAQ,GAAGH,UAAU,CAACG,QAAQ,IAAId,IAAI,CAACc,QAAQ;QAE1DlB,eAAe,CAACe,UAAU,CAAC;QAC3Bb,UAAU,CAACC,MAAM,EAAEC,IAAI,CAAC;MAC5B;IACJ,CAAC,MAAM,IAAIO,KAAK,CAACQ,YAAY,EAAE;MAC3B,IAAIR,KAAK,CAACQ,YAAY,CAACC,QAAQ,EAAE;QAC7BvB,IAAI,CAACsB,YAAY,CAACC,QAAQ,GAAG;UACzBC,QAAQ,EAAEV,KAAK,CAACQ,YAAY,CAACC,QAAQ,CAACC,QAAQ;UAC9CC,YAAY,EAAEX,KAAK,CAACQ,YAAY,CAACC,QAAQ,CAACE,YAAY;UACtDC,UAAU,EAAEZ,KAAK,CAACQ,YAAY,CAACC,QAAQ,CAACG;QAC5C,CAAC;MACL,CAAC,MAAM,IAAIZ,KAAK,CAACQ,YAAY,CAACK,aAAa,EAAE;QACzC3B,IAAI,CAACsB,YAAY,CAACK,aAAa,GAAGX,MAAM,CAACY,MAAM,CAAC5B,IAAI,CAACsB,YAAY,CAACK,aAAa,EAAEb,KAAK,CAACQ,YAAY,CAACK,aAAa,CAAC;MACtH,CAAC,MAAM,IAAIb,KAAK,CAACQ,YAAY,CAACO,YAAY,EAAE;QACxC,IAAIA,YAAY,GAAG7B,IAAI,CAACsB,YAAY,CAACO,YAAY;QACjDA,YAAY,CAACC,MAAM,GAAGd,MAAM,CAACY,MAAM,CAACC,YAAY,CAACC,MAAM,IAAI,CAAC,CAAC,EAAEhB,KAAK,CAACQ,YAAY,CAACO,YAAY,CAACC,MAAM,CAAC;QACtGD,YAAY,CAACE,SAAS,GAAGf,MAAM,CAACY,MAAM,CAACC,YAAY,CAACE,SAAS,IAAI,CAAC,CAAC,EAAEjB,KAAK,CAACQ,YAAY,CAACO,YAAY,CAACE,SAAS,CAAC;MACnH;IACJ;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}