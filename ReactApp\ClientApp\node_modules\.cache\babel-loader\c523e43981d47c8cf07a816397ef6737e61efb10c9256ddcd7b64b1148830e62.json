{"ast": null, "code": "import { deg } from '../../util';\nimport transform from '../transform';\nexport default function isOutOfEndPoint(endPoint, controlPoint, point) {\n  var angle = deg(Math.atan2(controlPoint.y - endPoint.y, controlPoint.x - endPoint.x));\n  var rotatedPoint = point.transformCopy(transform().rotate(-angle, endPoint));\n  return rotatedPoint.x < endPoint.x;\n}", "map": {"version": 3, "names": ["deg", "transform", "isOutOfEndPoint", "endPoint", "controlPoint", "point", "angle", "Math", "atan2", "y", "x", "rotatedPoint", "transformCopy", "rotate"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/geometry/math/is-out-of-end-point.js"], "sourcesContent": ["import { deg } from '../../util';\nimport transform from '../transform';\n\nexport default function isOutOfEndPoint(endPoint, controlPoint, point) {\n    var angle = deg(Math.atan2(controlPoint.y - endPoint.y, controlPoint.x - endPoint.x));\n    var rotatedPoint = point.transformCopy(transform().rotate(-angle, endPoint));\n\n    return rotatedPoint.x < endPoint.x;\n}"], "mappings": "AAAA,SAASA,GAAG,QAAQ,YAAY;AAChC,OAAOC,SAAS,MAAM,cAAc;AAEpC,eAAe,SAASC,eAAeA,CAACC,QAAQ,EAAEC,YAAY,EAAEC,KAAK,EAAE;EACnE,IAAIC,KAAK,GAAGN,GAAG,CAACO,IAAI,CAACC,KAAK,CAACJ,YAAY,CAACK,CAAC,GAAGN,QAAQ,CAACM,CAAC,EAAEL,YAAY,CAACM,CAAC,GAAGP,QAAQ,CAACO,CAAC,CAAC,CAAC;EACrF,IAAIC,YAAY,GAAGN,KAAK,CAACO,aAAa,CAACX,SAAS,CAAC,CAAC,CAACY,MAAM,CAAC,CAACP,KAAK,EAAEH,QAAQ,CAAC,CAAC;EAE5E,OAAOQ,YAAY,CAACD,CAAC,GAAGP,QAAQ,CAACO,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}