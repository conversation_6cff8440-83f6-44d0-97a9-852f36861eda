{"ast": null, "code": "import { defined } from '../../util';\nexport default function renderAttr(name, value) {\n  return defined(value) && value !== null ? \" \" + name + \"=\\\"\" + value + \"\\\" \" : \"\";\n}", "map": {"version": 3, "names": ["defined", "renderAttr", "name", "value"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/svg/utils/render-attribute.js"], "sourcesContent": ["import { defined } from '../../util';\n\nexport default function renderAttr(name, value) {\n    return (defined(value) && value !== null) ? (\" \" + name + \"=\\\"\" + value + \"\\\" \") : \"\";\n}"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AAEpC,eAAe,SAASC,UAAUA,CAACC,IAAI,EAAEC,KAAK,EAAE;EAC5C,OAAQH,OAAO,CAACG,KAAK,CAAC,IAAIA,KAAK,KAAK,IAAI,GAAK,GAAG,GAAGD,IAAI,GAAG,KAAK,GAAGC,KAAK,GAAG,KAAK,GAAI,EAAE;AACzF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}