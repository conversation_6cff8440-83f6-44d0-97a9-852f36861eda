{"ast": null, "code": "import { adjustDST } from './adjust-dst';\nimport { cloneDate } from './clone-date';\n/**\n * A function that adds and subtracts days from a `Date` object.\n *\n * @param date - The initial date value.\n * @param offset - The number of days to add and subtract from the date.\n * @returns - A new `Date` instance.\n *\n * @example\n * ```ts-no-run\n * addDays(new Date(2016, 0, 1), 5); // 2016-1-6\n * addDays(new Date(2016, 0, 1), -5); // 2015-12-26\n * ```\n */\nexport var addDays = function (date, offset) {\n  var newDate = cloneDate(date);\n  newDate.setDate(newDate.getDate() + offset);\n  return adjustDST(newDate, date.getHours());\n};", "map": {"version": 3, "names": ["adjustDST", "cloneDate", "addDays", "date", "offset", "newDate", "setDate", "getDate", "getHours"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/add-days.js"], "sourcesContent": ["import { adjustDST } from './adjust-dst';\nimport { cloneDate } from './clone-date';\n/**\n * A function that adds and subtracts days from a `Date` object.\n *\n * @param date - The initial date value.\n * @param offset - The number of days to add and subtract from the date.\n * @returns - A new `Date` instance.\n *\n * @example\n * ```ts-no-run\n * addDays(new Date(2016, 0, 1), 5); // 2016-1-6\n * addDays(new Date(2016, 0, 1), -5); // 2015-12-26\n * ```\n */\nexport var addDays = function (date, offset) {\n    var newDate = cloneDate(date);\n    newDate.setDate(newDate.getDate() + offset);\n    return adjustDST(newDate, date.getHours());\n};\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,cAAc;AACxC,SAASC,SAAS,QAAQ,cAAc;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,OAAO,GAAG,SAAAA,CAAUC,IAAI,EAAEC,MAAM,EAAE;EACzC,IAAIC,OAAO,GAAGJ,SAAS,CAACE,IAAI,CAAC;EAC7BE,OAAO,CAACC,OAAO,CAACD,OAAO,CAACE,OAAO,CAAC,CAAC,GAAGH,MAAM,CAAC;EAC3C,OAAOJ,SAAS,CAACK,OAAO,EAAEF,IAAI,CAACK,QAAQ,CAAC,CAAC,CAAC;AAC9C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}