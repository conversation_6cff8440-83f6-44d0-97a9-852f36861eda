{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nvar I = /* @__PURE__ */(E => (E.DEFAULT = \"DEFAULT\", E.FULLSCREEN = \"FULLSCREEN\", E.MINIMIZED = \"MINIMIZED\", E))(I || {});\nexport { I as windowStage };", "map": {"version": 3, "names": ["I", "E", "DEFAULT", "FULLSCREEN", "MINIMIZED", "windowStage"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dialogs/StageEnum.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nvar I = /* @__PURE__ */ ((E) => (E.DEFAULT = \"DEFAULT\", E.FULLSCREEN = \"FULLSCREEN\", E.MINIMIZED = \"MINIMIZED\", E))(I || {});\nexport {\n  I as windowStage\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,CAAC,GAAG,eAAgB,CAAEC,CAAC,KAAMA,CAAC,CAACC,OAAO,GAAG,SAAS,EAAED,CAAC,CAACE,UAAU,GAAG,YAAY,EAAEF,CAAC,CAACG,SAAS,GAAG,WAAW,EAAEH,CAAC,CAAC,EAAED,CAAC,IAAI,CAAC,CAAC,CAAC;AAC5H,SACEA,CAAC,IAAIK,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}