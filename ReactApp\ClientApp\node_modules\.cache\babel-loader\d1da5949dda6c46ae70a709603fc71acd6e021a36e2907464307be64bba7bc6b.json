{"ast": null, "code": "import { fieldList } from './field-list';\nvar setterCache = {};\n// tslint:disable-next-line:no-string-literal\nsetterCache['undefined'] = function (obj) {\n  return obj;\n};\nvar defaultValue = function (nextField, options) {\n  return options && options.arrays && !isNaN(Number(nextField)) ? [] : {};\n};\n/**\n * @hidden\n */\nexport function setter(field) {\n  if (setterCache[field]) {\n    return setterCache[field];\n  }\n  var fields = fieldList(field);\n  setterCache[field] = function (obj, value, options) {\n    var root = obj;\n    var depth = fields.length - 1;\n    for (var idx = 0; idx < depth && root; idx++) {\n      root = root[fields[idx]] = root[fields[idx]] || defaultValue(fields[idx + 1], options);\n    }\n    root[fields[depth]] = value;\n  };\n  return setterCache[field];\n}", "map": {"version": 3, "names": ["fieldList", "setter<PERSON><PERSON>", "obj", "defaultValue", "nextField", "options", "arrays", "isNaN", "Number", "setter", "field", "fields", "value", "root", "depth", "length", "idx"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-common/dist/es/accessors/setter.js"], "sourcesContent": ["import { fieldList } from './field-list';\nvar setterCache = {};\n// tslint:disable-next-line:no-string-literal\nsetterCache['undefined'] = function (obj) { return obj; };\nvar defaultValue = function (nextField, options) {\n    return options && options.arrays && !isNaN(Number(nextField)) ? [] : {};\n};\n/**\n * @hidden\n */\nexport function setter(field) {\n    if (setterCache[field]) {\n        return setterCache[field];\n    }\n    var fields = fieldList(field);\n    setterCache[field] = function (obj, value, options) {\n        var root = obj;\n        var depth = fields.length - 1;\n        for (var idx = 0; idx < depth && root; idx++) {\n            root = root[fields[idx]] = root[fields[idx]] || defaultValue(fields[idx + 1], options);\n        }\n        root[fields[depth]] = value;\n    };\n    return setterCache[field];\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,cAAc;AACxC,IAAIC,WAAW,GAAG,CAAC,CAAC;AACpB;AACAA,WAAW,CAAC,WAAW,CAAC,GAAG,UAAUC,GAAG,EAAE;EAAE,OAAOA,GAAG;AAAE,CAAC;AACzD,IAAIC,YAAY,GAAG,SAAAA,CAAUC,SAAS,EAAEC,OAAO,EAAE;EAC7C,OAAOA,OAAO,IAAIA,OAAO,CAACC,MAAM,IAAI,CAACC,KAAK,CAACC,MAAM,CAACJ,SAAS,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAC3E,CAAC;AACD;AACA;AACA;AACA,OAAO,SAASK,MAAMA,CAACC,KAAK,EAAE;EAC1B,IAAIT,WAAW,CAACS,KAAK,CAAC,EAAE;IACpB,OAAOT,WAAW,CAACS,KAAK,CAAC;EAC7B;EACA,IAAIC,MAAM,GAAGX,SAAS,CAACU,KAAK,CAAC;EAC7BT,WAAW,CAACS,KAAK,CAAC,GAAG,UAAUR,GAAG,EAAEU,KAAK,EAAEP,OAAO,EAAE;IAChD,IAAIQ,IAAI,GAAGX,GAAG;IACd,IAAIY,KAAK,GAAGH,MAAM,CAACI,MAAM,GAAG,CAAC;IAC7B,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGF,KAAK,IAAID,IAAI,EAAEG,GAAG,EAAE,EAAE;MAC1CH,IAAI,GAAGA,IAAI,CAACF,MAAM,CAACK,GAAG,CAAC,CAAC,GAAGH,IAAI,CAACF,MAAM,CAACK,GAAG,CAAC,CAAC,IAAIb,YAAY,CAACQ,MAAM,CAACK,GAAG,GAAG,CAAC,CAAC,EAAEX,OAAO,CAAC;IAC1F;IACAQ,IAAI,CAACF,MAAM,CAACG,KAAK,CAAC,CAAC,GAAGF,KAAK;EAC/B,CAAC;EACD,OAAOX,WAAW,CAACS,KAAK,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}