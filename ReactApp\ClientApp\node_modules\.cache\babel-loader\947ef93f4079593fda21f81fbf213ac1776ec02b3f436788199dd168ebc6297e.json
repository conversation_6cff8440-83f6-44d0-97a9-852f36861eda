{"ast": null, "code": "export default function isNumber(value) {\n  return typeof value === \"number\";\n}", "map": {"version": 3, "names": ["isNumber", "value"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-intl/dist/es/common/is-number.js"], "sourcesContent": ["export default function isNumber(value) {\n    return typeof value === \"number\";\n}"], "mappings": "AAAA,eAAe,SAASA,QAAQA,CAACC,KAAK,EAAE;EACpC,OAAO,OAAOA,KAAK,KAAK,QAAQ;AACpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}