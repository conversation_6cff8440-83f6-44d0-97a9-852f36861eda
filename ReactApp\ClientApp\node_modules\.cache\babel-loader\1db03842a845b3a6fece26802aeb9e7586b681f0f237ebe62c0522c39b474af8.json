{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst n = (t, e) => t === e.length - 1,\n  s = (t, e) => {\n    const o = n(t, e);\n    return t === 0 && !o ? \"top\" : o ? \"bot\" : \"mid\";\n  };\nexport { s as getNodePosition };", "map": {"version": 3, "names": ["n", "t", "e", "length", "s", "o", "getNodePosition"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-treeview/utils/utils.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst n = (t, e) => t === e.length - 1, s = (t, e) => {\n  const o = n(t, e);\n  return t === 0 && !o ? \"top\" : o ? \"bot\" : \"mid\";\n};\nexport {\n  s as getNodePosition\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,KAAKC,CAAC,CAACC,MAAM,GAAG,CAAC;EAAEC,CAAC,GAAGA,CAACH,CAAC,EAAEC,CAAC,KAAK;IACpD,MAAMG,CAAC,GAAGL,CAAC,CAACC,CAAC,EAAEC,CAAC,CAAC;IACjB,OAAOD,CAAC,KAAK,CAAC,IAAI,CAACI,CAAC,GAAG,KAAK,GAAGA,CAAC,GAAG,KAAK,GAAG,KAAK;EAClD,CAAC;AACD,SACED,CAAC,IAAIE,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}