{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst e = \"stepper.optionalText\",\n  o = \"stepper.progBarAriaLabel\",\n  r = {\n    [e]: \"(Optional)\",\n    [o]: \"progressbar\"\n  };\nexport { r as messages, e as optionalText, o as progBarAriaLabel };", "map": {"version": 3, "names": ["e", "o", "r", "messages", "optionalText", "progBarAriaLabel"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/stepper/messages/index.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst e = \"stepper.optionalText\", o = \"stepper.progBarAriaLabel\", r = {\n  [e]: \"(Optional)\",\n  [o]: \"progressbar\"\n};\nexport {\n  r as messages,\n  e as optionalText,\n  o as progBarAriaLabel\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAG,sBAAsB;EAAEC,CAAC,GAAG,0BAA0B;EAAEC,CAAC,GAAG;IACpE,CAACF,CAAC,GAAG,YAAY;IACjB,CAACC,CAAC,GAAG;EACP,CAAC;AACD,SACEC,CAAC,IAAIC,QAAQ,EACbH,CAAC,IAAII,YAAY,EACjBH,CAAC,IAAII,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}