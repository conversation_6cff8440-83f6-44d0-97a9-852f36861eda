{"ast": null, "code": "import { cldr, getLocaleInfo } from './info';\nimport { errors } from '../errors';\nimport localeTerritory from './territory';\nimport parseRangeDate from './parse-range-date';\n\n/* eslint-disable consistent-return */\n\nvar NoCurrency = errors.NoCurrency;\nvar NoCurrencyDisplay = errors.NoCurrencyDisplay;\nvar NoSupplementalCurrency = errors.NoSupplementalCurrency;\nvar NoCurrencyRegion = errors.NoCurrencyRegion;\nvar NoValidCurrency = errors.NoValidCurrency;\nvar DEFAULT_CURRENCY_FRACTIONS = 2;\nvar SYMBOL = \"symbol\";\nvar INVALID_CURRENCY_CODE = 'XXX';\nvar GLOBAL_CURRENCIES = {\n  '001': 'USD',\n  // 001 refers to world. not sure if it is correct to assume USD but seems better than throw an error\n  '150': 'EUR' // 150 territory for Europe\n};\nfunction getCurrencyInfo(locale, currency, throwIfNoValid) {\n  var info = getLocaleInfo(locale);\n  var currencies = info.numbers.currencies;\n  if (!currencies) {\n    if (throwIfNoValid) {\n      throw NoCurrency.error();\n    }\n    return;\n  }\n  var currencyDisplayInfo = currencies[currency];\n  if (!currencyDisplayInfo) {\n    if (throwIfNoValid) {\n      throw NoCurrencyDisplay.error();\n    }\n    return;\n  }\n  return currencyDisplayInfo;\n}\nfunction lengthComparer(a, b) {\n  return b.length - a.length;\n}\nfunction regionCurrency(regionCurrencies) {\n  var latestValidUntil, latestValidUntilRange;\n  var latestStillValid, latestStillValidDate;\n  for (var idx = 0; idx < regionCurrencies.length; idx++) {\n    var currency = regionCurrencies[idx];\n    var code = Object.keys(currency)[0];\n    var info = currency[code];\n    if (code !== INVALID_CURRENCY_CODE && info._tender !== 'false' && info._from) {\n      if (!info._to) {\n        var stillValidDate = parseRangeDate(info._from);\n        if (!latestStillValidDate || latestStillValidDate < stillValidDate) {\n          latestStillValid = code;\n          latestStillValidDate = stillValidDate;\n        }\n      } else if (!latestStillValid) {\n        var validFrom = parseRangeDate(info._from);\n        var validTo = parseRangeDate(info._to);\n        if (!latestValidUntilRange || latestValidUntilRange.to < validTo || latestValidUntilRange.from < validFrom) {\n          latestValidUntil = code;\n          latestValidUntilRange = {\n            from: validFrom,\n            to: validTo\n          };\n        }\n      }\n    }\n  }\n  return latestStillValid || latestValidUntil;\n}\nexport function currencyDisplays(locale, currency, throwIfNoValid) {\n  if (throwIfNoValid === void 0) throwIfNoValid = true;\n  var currencyInfo = getCurrencyInfo(locale, currency, throwIfNoValid);\n  if (!currencyInfo) {\n    return;\n  }\n  if (!currencyInfo.displays) {\n    var displays = [currency];\n    for (var field in currencyInfo) {\n      displays.push(currencyInfo[field]);\n    }\n    displays.sort(lengthComparer);\n    currencyInfo.displays = displays;\n  }\n  return currencyInfo.displays;\n}\nexport function currencyDisplay(locale, options) {\n  var value = options.value;\n  var currency = options.currency;\n  var currencyDisplay = options.currencyDisplay;\n  if (currencyDisplay === void 0) currencyDisplay = SYMBOL;\n  if (currencyDisplay === \"code\") {\n    return currency;\n  }\n  var currencyInfo = getCurrencyInfo(locale, currency, true);\n  var result;\n  if (currencyDisplay === SYMBOL) {\n    result = currencyInfo[\"symbol-alt-narrow\"] || currencyInfo[SYMBOL] || currency;\n  } else {\n    if (typeof value === \"undefined\" || value !== 1) {\n      result = currencyInfo[\"displayName-count-other\"];\n    } else {\n      result = currencyInfo[\"displayName-count-one\"];\n    }\n  }\n  return result;\n}\nexport function currencyFractionOptions(code) {\n  var minimumFractionDigits = DEFAULT_CURRENCY_FRACTIONS;\n  var maximumFractionDigits = DEFAULT_CURRENCY_FRACTIONS;\n  var fractions = ((cldr.supplemental.currencyData || {}).fractions || {})[code];\n  if (fractions && fractions._digits) {\n    maximumFractionDigits = minimumFractionDigits = parseInt(fractions._digits, 10);\n  }\n  return {\n    minimumFractionDigits: minimumFractionDigits,\n    maximumFractionDigits: maximumFractionDigits\n  };\n}\nexport function territoryCurrencyCode(territory, throwIfNoValid) {\n  if (throwIfNoValid === void 0) throwIfNoValid = true;\n  if (GLOBAL_CURRENCIES[territory]) {\n    return GLOBAL_CURRENCIES[territory];\n  }\n  var currencyData = cldr.supplemental.currencyData;\n  if (!currencyData) {\n    if (throwIfNoValid) {\n      throw NoSupplementalCurrency.error();\n    }\n    return;\n  }\n  var regionCurrencies = currencyData.region[territory];\n  if (!regionCurrencies) {\n    if (throwIfNoValid) {\n      throw NoCurrencyRegion.error(territory);\n    }\n    return;\n  }\n  var currencyCode = regionCurrency(regionCurrencies);\n  return currencyCode;\n}\nexport function localeCurrency(locale, throwIfNoValid) {\n  var info = getLocaleInfo(locale);\n  var numbers = info.numbers;\n  if (!numbers.localeCurrency) {\n    var currency = territoryCurrencyCode(localeTerritory(info), throwIfNoValid);\n    if (!currency && throwIfNoValid) {\n      throw NoValidCurrency.error(info.name);\n    }\n    numbers.localeCurrency = currency;\n  }\n  return numbers.localeCurrency;\n}", "map": {"version": 3, "names": ["cldr", "getLocaleInfo", "errors", "localeTerritory", "parseRangeDate", "NoCurrency", "NoCurrencyDisplay", "NoSupplementalCurrency", "NoCurrencyRegion", "NoValidCurrency", "DEFAULT_CURRENCY_FRACTIONS", "SYMBOL", "INVALID_CURRENCY_CODE", "GLOBAL_CURRENCIES", "getCurrencyInfo", "locale", "currency", "throwIfNoValid", "info", "currencies", "numbers", "error", "currencyDisplayInfo", "lengthComparer", "a", "b", "length", "regionCurrency", "regionCurrencies", "latestValidUntil", "latestValidUntilRange", "latestStillValid", "latestStillValidDate", "idx", "code", "Object", "keys", "_tender", "_from", "_to", "stillValidDate", "validFrom", "validTo", "to", "from", "currencyDisplays", "currencyInfo", "displays", "field", "push", "sort", "currencyDisplay", "options", "value", "result", "currencyFractionOptions", "minimumFractionDigits", "maximumFractionDigits", "fractions", "supplemental", "currencyData", "_digits", "parseInt", "territoryCurrencyCode", "territory", "region", "currencyCode", "localeCurrency", "name"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-intl/dist/es/cldr/currency.js"], "sourcesContent": ["import { cldr, getLocaleInfo } from './info';\nimport { errors } from '../errors';\nimport localeTerritory from './territory';\nimport parseRangeDate from './parse-range-date';\n\n/* eslint-disable consistent-return */\n\nvar NoCurrency = errors.NoCurrency;\nvar NoCurrencyDisplay = errors.NoCurrencyDisplay;\nvar NoSupplementalCurrency = errors.NoSupplementalCurrency;\nvar NoCurrencyRegion = errors.NoCurrencyRegion;\nvar NoValidCurrency = errors.NoValidCurrency;\n\nvar DEFAULT_CURRENCY_FRACTIONS = 2;\nvar SYMBOL = \"symbol\";\nvar INVALID_CURRENCY_CODE = 'XXX';\n\nvar GLOBAL_CURRENCIES = {\n    '001': 'USD', // 001 refers to world. not sure if it is correct to assume USD but seems better than throw an error\n    '150': 'EUR' // 150 territory for Europe\n\n};\n\nfunction getCurrencyInfo(locale, currency, throwIfNoValid) {\n    var info = getLocaleInfo(locale);\n    var currencies = info.numbers.currencies;\n    if (!currencies) {\n        if (throwIfNoValid) {\n            throw NoCurrency.error();\n        }\n\n        return;\n    }\n\n    var currencyDisplayInfo = currencies[currency];\n\n    if (!currencyDisplayInfo) {\n        if (throwIfNoValid) {\n            throw NoCurrencyDisplay.error();\n        }\n\n        return;\n    }\n\n    return currencyDisplayInfo;\n}\n\nfunction lengthComparer(a, b) {\n    return b.length - a.length;\n}\n\nfunction regionCurrency(regionCurrencies) {\n    var latestValidUntil, latestValidUntilRange;\n    var latestStillValid, latestStillValidDate;\n\n    for (var idx = 0; idx < regionCurrencies.length; idx++) {\n        var currency = regionCurrencies[idx];\n        var code = Object.keys(currency)[0];\n        var info = currency[code];\n        if (code !== INVALID_CURRENCY_CODE && info._tender !== 'false' && info._from) {\n            if (!info._to) {\n                var stillValidDate = parseRangeDate(info._from);\n                if (!latestStillValidDate || latestStillValidDate < stillValidDate) {\n                    latestStillValid = code;\n                    latestStillValidDate = stillValidDate;\n                }\n            } else if (!latestStillValid) {\n                var validFrom = parseRangeDate(info._from);\n                var validTo = parseRangeDate(info._to);\n                if (!latestValidUntilRange || latestValidUntilRange.to < validTo || latestValidUntilRange.from < validFrom) {\n                    latestValidUntil = code;\n                    latestValidUntilRange = {\n                        from: validFrom,\n                        to: validTo\n                    };\n                }\n            }\n        }\n    }\n\n    return latestStillValid || latestValidUntil;\n}\n\nexport function currencyDisplays(locale, currency, throwIfNoValid) {\n    if ( throwIfNoValid === void 0 ) throwIfNoValid = true;\n\n    var currencyInfo = getCurrencyInfo(locale, currency, throwIfNoValid);\n    if (!currencyInfo) {\n        return;\n    }\n\n    if (!currencyInfo.displays) {\n        var displays = [ currency ];\n        for (var field in currencyInfo) {\n            displays.push(currencyInfo[field]);\n        }\n        displays.sort(lengthComparer);\n        currencyInfo.displays = displays;\n    }\n\n    return currencyInfo.displays;\n}\n\nexport function currencyDisplay(locale, options) {\n    var value = options.value;\n    var currency = options.currency;\n    var currencyDisplay = options.currencyDisplay; if ( currencyDisplay === void 0 ) currencyDisplay = SYMBOL;\n\n    if (currencyDisplay === \"code\") {\n        return currency;\n    }\n\n    var currencyInfo = getCurrencyInfo(locale, currency, true);\n    var result;\n\n    if (currencyDisplay === SYMBOL) {\n        result = currencyInfo[\"symbol-alt-narrow\"] || currencyInfo[SYMBOL] || currency;\n    } else {\n        if (typeof value === \"undefined\" || value !== 1) {\n            result = currencyInfo[\"displayName-count-other\"];\n        } else {\n            result = currencyInfo[\"displayName-count-one\"];\n        }\n    }\n\n    return result;\n}\n\nexport function currencyFractionOptions(code) {\n    var minimumFractionDigits = DEFAULT_CURRENCY_FRACTIONS;\n    var maximumFractionDigits = DEFAULT_CURRENCY_FRACTIONS;\n\n    var fractions = ((cldr.supplemental.currencyData || {}).fractions || {})[code];\n\n    if (fractions && fractions._digits) {\n        maximumFractionDigits = minimumFractionDigits = parseInt(fractions._digits, 10);\n    }\n\n    return {\n        minimumFractionDigits: minimumFractionDigits,\n        maximumFractionDigits: maximumFractionDigits\n    };\n}\n\nexport function territoryCurrencyCode(territory, throwIfNoValid) {\n    if ( throwIfNoValid === void 0 ) throwIfNoValid = true;\n\n    if (GLOBAL_CURRENCIES[territory]) {\n        return GLOBAL_CURRENCIES[territory];\n    }\n\n    var currencyData = cldr.supplemental.currencyData;\n    if (!currencyData) {\n        if (throwIfNoValid) {\n            throw NoSupplementalCurrency.error();\n        }\n\n        return;\n    }\n\n    var regionCurrencies = currencyData.region[territory];\n\n    if (!regionCurrencies) {\n        if (throwIfNoValid) {\n            throw NoCurrencyRegion.error(territory);\n        }\n\n        return;\n    }\n\n    var currencyCode = regionCurrency(regionCurrencies);\n\n    return currencyCode;\n}\n\nexport function localeCurrency(locale, throwIfNoValid) {\n    var info = getLocaleInfo(locale);\n    var numbers = info.numbers;\n\n    if (!numbers.localeCurrency) {\n        var currency = territoryCurrencyCode(localeTerritory(info), throwIfNoValid);\n\n        if (!currency && throwIfNoValid) {\n            throw NoValidCurrency.error(info.name);\n        }\n\n        numbers.localeCurrency = currency;\n    }\n\n    return numbers.localeCurrency;\n}\n"], "mappings": "AAAA,SAASA,IAAI,EAAEC,aAAa,QAAQ,QAAQ;AAC5C,SAASC,MAAM,QAAQ,WAAW;AAClC,OAAOC,eAAe,MAAM,aAAa;AACzC,OAAOC,cAAc,MAAM,oBAAoB;;AAE/C;;AAEA,IAAIC,UAAU,GAAGH,MAAM,CAACG,UAAU;AAClC,IAAIC,iBAAiB,GAAGJ,MAAM,CAACI,iBAAiB;AAChD,IAAIC,sBAAsB,GAAGL,MAAM,CAACK,sBAAsB;AAC1D,IAAIC,gBAAgB,GAAGN,MAAM,CAACM,gBAAgB;AAC9C,IAAIC,eAAe,GAAGP,MAAM,CAACO,eAAe;AAE5C,IAAIC,0BAA0B,GAAG,CAAC;AAClC,IAAIC,MAAM,GAAG,QAAQ;AACrB,IAAIC,qBAAqB,GAAG,KAAK;AAEjC,IAAIC,iBAAiB,GAAG;EACpB,KAAK,EAAE,KAAK;EAAE;EACd,KAAK,EAAE,KAAK,CAAC;AAEjB,CAAC;AAED,SAASC,eAAeA,CAACC,MAAM,EAAEC,QAAQ,EAAEC,cAAc,EAAE;EACvD,IAAIC,IAAI,GAAGjB,aAAa,CAACc,MAAM,CAAC;EAChC,IAAII,UAAU,GAAGD,IAAI,CAACE,OAAO,CAACD,UAAU;EACxC,IAAI,CAACA,UAAU,EAAE;IACb,IAAIF,cAAc,EAAE;MAChB,MAAMZ,UAAU,CAACgB,KAAK,CAAC,CAAC;IAC5B;IAEA;EACJ;EAEA,IAAIC,mBAAmB,GAAGH,UAAU,CAACH,QAAQ,CAAC;EAE9C,IAAI,CAACM,mBAAmB,EAAE;IACtB,IAAIL,cAAc,EAAE;MAChB,MAAMX,iBAAiB,CAACe,KAAK,CAAC,CAAC;IACnC;IAEA;EACJ;EAEA,OAAOC,mBAAmB;AAC9B;AAEA,SAASC,cAAcA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC1B,OAAOA,CAAC,CAACC,MAAM,GAAGF,CAAC,CAACE,MAAM;AAC9B;AAEA,SAASC,cAAcA,CAACC,gBAAgB,EAAE;EACtC,IAAIC,gBAAgB,EAAEC,qBAAqB;EAC3C,IAAIC,gBAAgB,EAAEC,oBAAoB;EAE1C,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGL,gBAAgB,CAACF,MAAM,EAAEO,GAAG,EAAE,EAAE;IACpD,IAAIjB,QAAQ,GAAGY,gBAAgB,CAACK,GAAG,CAAC;IACpC,IAAIC,IAAI,GAAGC,MAAM,CAACC,IAAI,CAACpB,QAAQ,CAAC,CAAC,CAAC,CAAC;IACnC,IAAIE,IAAI,GAAGF,QAAQ,CAACkB,IAAI,CAAC;IACzB,IAAIA,IAAI,KAAKtB,qBAAqB,IAAIM,IAAI,CAACmB,OAAO,KAAK,OAAO,IAAInB,IAAI,CAACoB,KAAK,EAAE;MAC1E,IAAI,CAACpB,IAAI,CAACqB,GAAG,EAAE;QACX,IAAIC,cAAc,GAAGpC,cAAc,CAACc,IAAI,CAACoB,KAAK,CAAC;QAC/C,IAAI,CAACN,oBAAoB,IAAIA,oBAAoB,GAAGQ,cAAc,EAAE;UAChET,gBAAgB,GAAGG,IAAI;UACvBF,oBAAoB,GAAGQ,cAAc;QACzC;MACJ,CAAC,MAAM,IAAI,CAACT,gBAAgB,EAAE;QAC1B,IAAIU,SAAS,GAAGrC,cAAc,CAACc,IAAI,CAACoB,KAAK,CAAC;QAC1C,IAAII,OAAO,GAAGtC,cAAc,CAACc,IAAI,CAACqB,GAAG,CAAC;QACtC,IAAI,CAACT,qBAAqB,IAAIA,qBAAqB,CAACa,EAAE,GAAGD,OAAO,IAAIZ,qBAAqB,CAACc,IAAI,GAAGH,SAAS,EAAE;UACxGZ,gBAAgB,GAAGK,IAAI;UACvBJ,qBAAqB,GAAG;YACpBc,IAAI,EAAEH,SAAS;YACfE,EAAE,EAAED;UACR,CAAC;QACL;MACJ;IACJ;EACJ;EAEA,OAAOX,gBAAgB,IAAIF,gBAAgB;AAC/C;AAEA,OAAO,SAASgB,gBAAgBA,CAAC9B,MAAM,EAAEC,QAAQ,EAAEC,cAAc,EAAE;EAC/D,IAAKA,cAAc,KAAK,KAAK,CAAC,EAAGA,cAAc,GAAG,IAAI;EAEtD,IAAI6B,YAAY,GAAGhC,eAAe,CAACC,MAAM,EAAEC,QAAQ,EAAEC,cAAc,CAAC;EACpE,IAAI,CAAC6B,YAAY,EAAE;IACf;EACJ;EAEA,IAAI,CAACA,YAAY,CAACC,QAAQ,EAAE;IACxB,IAAIA,QAAQ,GAAG,CAAE/B,QAAQ,CAAE;IAC3B,KAAK,IAAIgC,KAAK,IAAIF,YAAY,EAAE;MAC5BC,QAAQ,CAACE,IAAI,CAACH,YAAY,CAACE,KAAK,CAAC,CAAC;IACtC;IACAD,QAAQ,CAACG,IAAI,CAAC3B,cAAc,CAAC;IAC7BuB,YAAY,CAACC,QAAQ,GAAGA,QAAQ;EACpC;EAEA,OAAOD,YAAY,CAACC,QAAQ;AAChC;AAEA,OAAO,SAASI,eAAeA,CAACpC,MAAM,EAAEqC,OAAO,EAAE;EAC7C,IAAIC,KAAK,GAAGD,OAAO,CAACC,KAAK;EACzB,IAAIrC,QAAQ,GAAGoC,OAAO,CAACpC,QAAQ;EAC/B,IAAImC,eAAe,GAAGC,OAAO,CAACD,eAAe;EAAE,IAAKA,eAAe,KAAK,KAAK,CAAC,EAAGA,eAAe,GAAGxC,MAAM;EAEzG,IAAIwC,eAAe,KAAK,MAAM,EAAE;IAC5B,OAAOnC,QAAQ;EACnB;EAEA,IAAI8B,YAAY,GAAGhC,eAAe,CAACC,MAAM,EAAEC,QAAQ,EAAE,IAAI,CAAC;EAC1D,IAAIsC,MAAM;EAEV,IAAIH,eAAe,KAAKxC,MAAM,EAAE;IAC5B2C,MAAM,GAAGR,YAAY,CAAC,mBAAmB,CAAC,IAAIA,YAAY,CAACnC,MAAM,CAAC,IAAIK,QAAQ;EAClF,CAAC,MAAM;IACH,IAAI,OAAOqC,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,CAAC,EAAE;MAC7CC,MAAM,GAAGR,YAAY,CAAC,yBAAyB,CAAC;IACpD,CAAC,MAAM;MACHQ,MAAM,GAAGR,YAAY,CAAC,uBAAuB,CAAC;IAClD;EACJ;EAEA,OAAOQ,MAAM;AACjB;AAEA,OAAO,SAASC,uBAAuBA,CAACrB,IAAI,EAAE;EAC1C,IAAIsB,qBAAqB,GAAG9C,0BAA0B;EACtD,IAAI+C,qBAAqB,GAAG/C,0BAA0B;EAEtD,IAAIgD,SAAS,GAAG,CAAC,CAAC1D,IAAI,CAAC2D,YAAY,CAACC,YAAY,IAAI,CAAC,CAAC,EAAEF,SAAS,IAAI,CAAC,CAAC,EAAExB,IAAI,CAAC;EAE9E,IAAIwB,SAAS,IAAIA,SAAS,CAACG,OAAO,EAAE;IAChCJ,qBAAqB,GAAGD,qBAAqB,GAAGM,QAAQ,CAACJ,SAAS,CAACG,OAAO,EAAE,EAAE,CAAC;EACnF;EAEA,OAAO;IACHL,qBAAqB,EAAEA,qBAAqB;IAC5CC,qBAAqB,EAAEA;EAC3B,CAAC;AACL;AAEA,OAAO,SAASM,qBAAqBA,CAACC,SAAS,EAAE/C,cAAc,EAAE;EAC7D,IAAKA,cAAc,KAAK,KAAK,CAAC,EAAGA,cAAc,GAAG,IAAI;EAEtD,IAAIJ,iBAAiB,CAACmD,SAAS,CAAC,EAAE;IAC9B,OAAOnD,iBAAiB,CAACmD,SAAS,CAAC;EACvC;EAEA,IAAIJ,YAAY,GAAG5D,IAAI,CAAC2D,YAAY,CAACC,YAAY;EACjD,IAAI,CAACA,YAAY,EAAE;IACf,IAAI3C,cAAc,EAAE;MAChB,MAAMV,sBAAsB,CAACc,KAAK,CAAC,CAAC;IACxC;IAEA;EACJ;EAEA,IAAIO,gBAAgB,GAAGgC,YAAY,CAACK,MAAM,CAACD,SAAS,CAAC;EAErD,IAAI,CAACpC,gBAAgB,EAAE;IACnB,IAAIX,cAAc,EAAE;MAChB,MAAMT,gBAAgB,CAACa,KAAK,CAAC2C,SAAS,CAAC;IAC3C;IAEA;EACJ;EAEA,IAAIE,YAAY,GAAGvC,cAAc,CAACC,gBAAgB,CAAC;EAEnD,OAAOsC,YAAY;AACvB;AAEA,OAAO,SAASC,cAAcA,CAACpD,MAAM,EAAEE,cAAc,EAAE;EACnD,IAAIC,IAAI,GAAGjB,aAAa,CAACc,MAAM,CAAC;EAChC,IAAIK,OAAO,GAAGF,IAAI,CAACE,OAAO;EAE1B,IAAI,CAACA,OAAO,CAAC+C,cAAc,EAAE;IACzB,IAAInD,QAAQ,GAAG+C,qBAAqB,CAAC5D,eAAe,CAACe,IAAI,CAAC,EAAED,cAAc,CAAC;IAE3E,IAAI,CAACD,QAAQ,IAAIC,cAAc,EAAE;MAC7B,MAAMR,eAAe,CAACY,KAAK,CAACH,IAAI,CAACkD,IAAI,CAAC;IAC1C;IAEAhD,OAAO,CAAC+C,cAAc,GAAGnD,QAAQ;EACrC;EAEA,OAAOI,OAAO,CAAC+C,cAAc;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}