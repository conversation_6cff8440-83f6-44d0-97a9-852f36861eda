{"ast": null, "code": "/* eslint-disable no-console */\n\nexport default function logToConsole(message) {\n  var console = window.console;\n  if (typeof console != \"undefined\" && console.log) {\n    console.log(message);\n  }\n}", "map": {"version": 3, "names": ["logToConsole", "message", "console", "window", "log"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/common/log-to-console.js"], "sourcesContent": ["/* eslint-disable no-console */\n\nexport default function logToConsole(message) {\n    var console = window.console;\n\n    if (typeof(console) != \"undefined\" && console.log) {\n        console.log(message);\n    }\n}"], "mappings": "AAAA;;AAEA,eAAe,SAASA,YAAYA,CAACC,OAAO,EAAE;EAC1C,IAAIC,OAAO,GAAGC,MAAM,CAACD,OAAO;EAE5B,IAAI,OAAOA,OAAQ,IAAI,WAAW,IAAIA,OAAO,CAACE,GAAG,EAAE;IAC/CF,OAAO,CAACE,GAAG,CAACH,OAAO,CAAC;EACxB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}