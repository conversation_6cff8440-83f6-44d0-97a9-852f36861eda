{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as u from \"react\";\nimport t from \"prop-types\";\nimport { Animation as h } from \"./Animation.mjs\";\nconst m = {\n    position: \"absolute\",\n    top: \"0\",\n    left: \"0\"\n  },\n  f = i => {\n    const {\n      appear: e = n.appear,\n      enter: r = n.enter,\n      exit: o = n.exit,\n      transitionEnterDuration: a = n.transitionEnterDuration,\n      transitionExitDuration: s = n.transitionExitDuration,\n      stackChildren: c = n.stackChildren,\n      direction: p = n.direction,\n      children: d,\n      ...l\n    } = i;\n    return /* @__PURE__ */u.createElement(h, {\n      appear: e,\n      enter: r,\n      exit: o,\n      transitionEnterDuration: a,\n      transitionExitDuration: s,\n      stackChildren: c,\n      ...l,\n      transitionName: `push-${p}`,\n      animationExitingStyle: i.stackChildren ? m : void 0\n    }, d);\n  },\n  n = {\n    appear: !1,\n    enter: !0,\n    exit: !0,\n    transitionEnterDuration: 300,\n    transitionExitDuration: 300,\n    direction: \"right\",\n    stackChildren: !1\n  };\nf.propTypes = {\n  children: t.oneOfType([t.arrayOf(t.node), t.node]),\n  childFactory: t.any,\n  className: t.string,\n  direction: t.oneOf([\"up\", \"down\", \"left\", \"right\"]),\n  component: t.node,\n  id: t.string,\n  style: t.any,\n  stackChildren: t.bool\n};\nexport { f as Push };", "map": {"version": 3, "names": ["u", "t", "Animation", "h", "m", "position", "top", "left", "f", "i", "appear", "e", "n", "enter", "r", "exit", "o", "transitionEnterDuration", "a", "transitionExitDuration", "s", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "c", "direction", "p", "children", "d", "l", "createElement", "transitionName", "animationExitingStyle", "propTypes", "oneOfType", "arrayOf", "node", "childFactory", "any", "className", "string", "oneOf", "component", "id", "style", "bool", "<PERSON><PERSON>"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-animation/Push.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as u from \"react\";\nimport t from \"prop-types\";\nimport { Animation as h } from \"./Animation.mjs\";\nconst m = { position: \"absolute\", top: \"0\", left: \"0\" }, f = (i) => {\n  const {\n    appear: e = n.appear,\n    enter: r = n.enter,\n    exit: o = n.exit,\n    transitionEnterDuration: a = n.transitionEnterDuration,\n    transitionExitDuration: s = n.transitionExitDuration,\n    stackChildren: c = n.stackChildren,\n    direction: p = n.direction,\n    children: d,\n    ...l\n  } = i;\n  return /* @__PURE__ */ u.createElement(\n    h,\n    {\n      appear: e,\n      enter: r,\n      exit: o,\n      transitionEnterDuration: a,\n      transitionExitDuration: s,\n      stackChildren: c,\n      ...l,\n      transitionName: `push-${p}`,\n      animationExitingStyle: i.stackChildren ? m : void 0\n    },\n    d\n  );\n}, n = {\n  appear: !1,\n  enter: !0,\n  exit: !0,\n  transitionEnterDuration: 300,\n  transitionExitDuration: 300,\n  direction: \"right\",\n  stackChildren: !1\n};\nf.propTypes = {\n  children: t.oneOfType([t.arrayOf(t.node), t.node]),\n  childFactory: t.any,\n  className: t.string,\n  direction: t.oneOf([\"up\", \"down\", \"left\", \"right\"]),\n  component: t.node,\n  id: t.string,\n  style: t.any,\n  stackChildren: t.bool\n};\nexport {\n  f as Push\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,SAAS,IAAIC,CAAC,QAAQ,iBAAiB;AAChD,MAAMC,CAAC,GAAG;IAAEC,QAAQ,EAAE,UAAU;IAAEC,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAI,CAAC;EAAEC,CAAC,GAAIC,CAAC,IAAK;IAClE,MAAM;MACJC,MAAM,EAAEC,CAAC,GAAGC,CAAC,CAACF,MAAM;MACpBG,KAAK,EAAEC,CAAC,GAAGF,CAAC,CAACC,KAAK;MAClBE,IAAI,EAAEC,CAAC,GAAGJ,CAAC,CAACG,IAAI;MAChBE,uBAAuB,EAAEC,CAAC,GAAGN,CAAC,CAACK,uBAAuB;MACtDE,sBAAsB,EAAEC,CAAC,GAAGR,CAAC,CAACO,sBAAsB;MACpDE,aAAa,EAAEC,CAAC,GAAGV,CAAC,CAACS,aAAa;MAClCE,SAAS,EAAEC,CAAC,GAAGZ,CAAC,CAACW,SAAS;MAC1BE,QAAQ,EAAEC,CAAC;MACX,GAAGC;IACL,CAAC,GAAGlB,CAAC;IACL,OAAO,eAAgBT,CAAC,CAAC4B,aAAa,CACpCzB,CAAC,EACD;MACEO,MAAM,EAAEC,CAAC;MACTE,KAAK,EAAEC,CAAC;MACRC,IAAI,EAAEC,CAAC;MACPC,uBAAuB,EAAEC,CAAC;MAC1BC,sBAAsB,EAAEC,CAAC;MACzBC,aAAa,EAAEC,CAAC;MAChB,GAAGK,CAAC;MACJE,cAAc,EAAE,QAAQL,CAAC,EAAE;MAC3BM,qBAAqB,EAAErB,CAAC,CAACY,aAAa,GAAGjB,CAAC,GAAG,KAAK;IACpD,CAAC,EACDsB,CACF,CAAC;EACH,CAAC;EAAEd,CAAC,GAAG;IACLF,MAAM,EAAE,CAAC,CAAC;IACVG,KAAK,EAAE,CAAC,CAAC;IACTE,IAAI,EAAE,CAAC,CAAC;IACRE,uBAAuB,EAAE,GAAG;IAC5BE,sBAAsB,EAAE,GAAG;IAC3BI,SAAS,EAAE,OAAO;IAClBF,aAAa,EAAE,CAAC;EAClB,CAAC;AACDb,CAAC,CAACuB,SAAS,GAAG;EACZN,QAAQ,EAAExB,CAAC,CAAC+B,SAAS,CAAC,CAAC/B,CAAC,CAACgC,OAAO,CAAChC,CAAC,CAACiC,IAAI,CAAC,EAAEjC,CAAC,CAACiC,IAAI,CAAC,CAAC;EAClDC,YAAY,EAAElC,CAAC,CAACmC,GAAG;EACnBC,SAAS,EAAEpC,CAAC,CAACqC,MAAM;EACnBf,SAAS,EAAEtB,CAAC,CAACsC,KAAK,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;EACnDC,SAAS,EAAEvC,CAAC,CAACiC,IAAI;EACjBO,EAAE,EAAExC,CAAC,CAACqC,MAAM;EACZI,KAAK,EAAEzC,CAAC,CAACmC,GAAG;EACZf,aAAa,EAAEpB,CAAC,CAAC0C;AACnB,CAAC;AACD,SACEnC,CAAC,IAAIoC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}