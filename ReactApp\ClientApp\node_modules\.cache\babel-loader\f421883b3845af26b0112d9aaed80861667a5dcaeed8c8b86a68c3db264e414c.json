{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport { useLocalization as m } from \"@progress/kendo-react-intl\";\nimport { clear as t, messages as i } from \"../messages/index.mjs\";\nimport { useUnstyled as u, classNames as p, uDropDownsBase as f, IconWrap as d } from \"@progress/kendo-react-common\";\nimport { xIcon as D } from \"@progress/kendo-svg-icons\";\nconst b = n => {\n  const a = m(),\n    o = u(),\n    s = o && o.uDropDownsBase,\n    r = a.toLanguageString(t, i[t]),\n    c = l => l.preventDefault();\n  return /* @__PURE__ */e.createElement(\"span\", {\n    className: p(f.clearButton({\n      c: s\n    })),\n    role: \"button\",\n    onClick: n.onClick,\n    onMouseDown: c,\n    tabIndex: -1,\n    title: r,\n    key: \"clearbutton\"\n  }, /* @__PURE__ */e.createElement(d, {\n    name: \"x\",\n    icon: D\n  }));\n};\nexport { b as default };", "map": {"version": 3, "names": ["e", "useLocalization", "m", "clear", "t", "messages", "i", "useUnstyled", "u", "classNames", "p", "uDropDownsBase", "f", "IconWrap", "d", "xIcon", "D", "b", "n", "a", "o", "s", "r", "toLanguageString", "c", "l", "preventDefault", "createElement", "className", "clearButton", "role", "onClick", "onMouseDown", "tabIndex", "title", "key", "name", "icon", "default"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dropdowns/common/ClearButton.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport { useLocalization as m } from \"@progress/kendo-react-intl\";\nimport { clear as t, messages as i } from \"../messages/index.mjs\";\nimport { useUnstyled as u, classNames as p, uDropDownsBase as f, IconWrap as d } from \"@progress/kendo-react-common\";\nimport { xIcon as D } from \"@progress/kendo-svg-icons\";\nconst b = (n) => {\n  const a = m(), o = u(), s = o && o.uDropDownsBase, r = a.toLanguageString(t, i[t]), c = (l) => l.preventDefault();\n  return /* @__PURE__ */ e.createElement(\n    \"span\",\n    {\n      className: p(f.clearButton({ c: s })),\n      role: \"button\",\n      onClick: n.onClick,\n      onMouseDown: c,\n      tabIndex: -1,\n      title: r,\n      key: \"clearbutton\"\n    },\n    /* @__PURE__ */ e.createElement(d, { name: \"x\", icon: D })\n  );\n};\nexport {\n  b as default\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,SAASC,eAAe,IAAIC,CAAC,QAAQ,4BAA4B;AACjE,SAASC,KAAK,IAAIC,CAAC,EAAEC,QAAQ,IAAIC,CAAC,QAAQ,uBAAuB;AACjE,SAASC,WAAW,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,EAAEC,cAAc,IAAIC,CAAC,EAAEC,QAAQ,IAAIC,CAAC,QAAQ,8BAA8B;AACpH,SAASC,KAAK,IAAIC,CAAC,QAAQ,2BAA2B;AACtD,MAAMC,CAAC,GAAIC,CAAC,IAAK;EACf,MAAMC,CAAC,GAAGjB,CAAC,CAAC,CAAC;IAAEkB,CAAC,GAAGZ,CAAC,CAAC,CAAC;IAAEa,CAAC,GAAGD,CAAC,IAAIA,CAAC,CAACT,cAAc;IAAEW,CAAC,GAAGH,CAAC,CAACI,gBAAgB,CAACnB,CAAC,EAAEE,CAAC,CAACF,CAAC,CAAC,CAAC;IAAEoB,CAAC,GAAIC,CAAC,IAAKA,CAAC,CAACC,cAAc,CAAC,CAAC;EACjH,OAAO,eAAgB1B,CAAC,CAAC2B,aAAa,CACpC,MAAM,EACN;IACEC,SAAS,EAAElB,CAAC,CAACE,CAAC,CAACiB,WAAW,CAAC;MAAEL,CAAC,EAAEH;IAAE,CAAC,CAAC,CAAC;IACrCS,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAEb,CAAC,CAACa,OAAO;IAClBC,WAAW,EAAER,CAAC;IACdS,QAAQ,EAAE,CAAC,CAAC;IACZC,KAAK,EAAEZ,CAAC;IACRa,GAAG,EAAE;EACP,CAAC,EACD,eAAgBnC,CAAC,CAAC2B,aAAa,CAACb,CAAC,EAAE;IAAEsB,IAAI,EAAE,GAAG;IAAEC,IAAI,EAAErB;EAAE,CAAC,CAC3D,CAAC;AACH,CAAC;AACD,SACEC,CAAC,IAAIqB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}