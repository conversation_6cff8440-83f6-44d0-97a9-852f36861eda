{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { Parser as c } from \"./parsers.mjs\";\nimport { Result as r } from \"./result.mjs\";\nconst s = n => new c(e => new r(n, e)),\n  a = (n, e) => n.chain(o => e.map(t => o.concat([t]))),\n  w = n => n.reduce((e, o) => a(e, o), s([])),\n  l = n => new c(e => {\n    let o = new r([], e);\n    for (; !e.eof();) o = o.concat(n.run(e));\n    return o;\n  });\nexport { l as greedy, w as sequence };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "c", "Result", "r", "s", "n", "e", "a", "chain", "o", "map", "t", "concat", "w", "reduce", "l", "eof", "run", "greedy", "sequence"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-inputs/maskedtextbox/parsing/combinators.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { Parser as c } from \"./parsers.mjs\";\nimport { Result as r } from \"./result.mjs\";\nconst s = (n) => new c((e) => new r(n, e)), a = (n, e) => n.chain((o) => e.map((t) => o.concat([t]))), w = (n) => n.reduce((e, o) => a(e, o), s([])), l = (n) => new c((e) => {\n  let o = new r([], e);\n  for (; !e.eof(); )\n    o = o.concat(n.run(e));\n  return o;\n});\nexport {\n  l as greedy,\n  w as sequence\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAM,IAAIC,CAAC,QAAQ,eAAe;AAC3C,SAASC,MAAM,IAAIC,CAAC,QAAQ,cAAc;AAC1C,MAAMC,CAAC,GAAIC,CAAC,IAAK,IAAIJ,CAAC,CAAEK,CAAC,IAAK,IAAIH,CAAC,CAACE,CAAC,EAAEC,CAAC,CAAC,CAAC;EAAEC,CAAC,GAAGA,CAACF,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACG,KAAK,CAAEC,CAAC,IAAKH,CAAC,CAACI,GAAG,CAAEC,CAAC,IAAKF,CAAC,CAACG,MAAM,CAAC,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC;EAAEE,CAAC,GAAIR,CAAC,IAAKA,CAAC,CAACS,MAAM,CAAC,CAACR,CAAC,EAAEG,CAAC,KAAKF,CAAC,CAACD,CAAC,EAAEG,CAAC,CAAC,EAAEL,CAAC,CAAC,EAAE,CAAC,CAAC;EAAEW,CAAC,GAAIV,CAAC,IAAK,IAAIJ,CAAC,CAAEK,CAAC,IAAK;IAC5K,IAAIG,CAAC,GAAG,IAAIN,CAAC,CAAC,EAAE,EAAEG,CAAC,CAAC;IACpB,OAAO,CAACA,CAAC,CAACU,GAAG,CAAC,CAAC,GACbP,CAAC,GAAGA,CAAC,CAACG,MAAM,CAACP,CAAC,CAACY,GAAG,CAACX,CAAC,CAAC,CAAC;IACxB,OAAOG,CAAC;EACV,CAAC,CAAC;AACF,SACEM,CAAC,IAAIG,MAAM,EACXL,CAAC,IAAIM,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}