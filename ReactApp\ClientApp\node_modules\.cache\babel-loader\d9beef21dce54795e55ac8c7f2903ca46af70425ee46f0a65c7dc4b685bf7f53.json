{"ast": null, "code": "var REPLACE_REGEX = /\\r?\\n|\\r|\\t/g;\nvar SPACE = ' ';\nfunction normalizeText(text) {\n  return String(text).replace(REPLACE_REGEX, SPACE);\n}\nfunction objectKey(object) {\n  var parts = [];\n  for (var key in object) {\n    parts.push(key + object[key]);\n  }\n  return parts.sort().join(\"\");\n}\n\n// Computes FNV-1 hash\n// See http://en.wikipedia.org/wiki/Fowler%E2%80%93Noll%E2%80%93Vo_hash_function\nfunction hashKey(str) {\n  // 32-bit FNV-1 offset basis\n  // See http://isthe.com/chongo/tech/comp/fnv/#FNV-param\n  var hash = 0x811C9DC5;\n  for (var i = 0; i < str.length; ++i) {\n    hash += (hash << 1) + (hash << 4) + (hash << 7) + (hash << 8) + (hash << 24);\n    hash ^= str.charCodeAt(i);\n  }\n  return hash >>> 0;\n}\nexport { objectKey, hashKey, normalizeText };", "map": {"version": 3, "names": ["REPLACE_REGEX", "SPACE", "normalizeText", "text", "String", "replace", "object<PERSON>ey", "object", "parts", "key", "push", "sort", "join", "hash<PERSON><PERSON>", "str", "hash", "i", "length", "charCodeAt"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/text-metrics/util.js"], "sourcesContent": ["var REPLACE_REGEX = /\\r?\\n|\\r|\\t/g;\nvar SPACE = ' ';\n\nfunction normalizeText(text) {\n    return String(text).replace(REPLACE_REGEX, SPACE);\n}\n\nfunction objectKey(object) {\n    var parts = [];\n    for (var key in object) {\n        parts.push(key + object[key]);\n    }\n\n    return parts.sort().join(\"\");\n}\n\n// Computes FNV-1 hash\n// See http://en.wikipedia.org/wiki/Fowler%E2%80%93Noll%E2%80%93Vo_hash_function\nfunction hashKey(str) {\n    // 32-bit FNV-1 offset basis\n    // See http://isthe.com/chongo/tech/comp/fnv/#FNV-param\n    var hash = 0x811C9DC5;\n\n    for (var i = 0; i < str.length; ++i) {\n        hash += (hash << 1) + (hash << 4) + (hash << 7) + (hash << 8) + (hash << 24);\n        hash ^= str.charCodeAt(i);\n    }\n\n    return hash >>> 0;\n}\n\nexport { objectKey, hashKey, normalizeText };"], "mappings": "AAAA,IAAIA,aAAa,GAAG,cAAc;AAClC,IAAIC,KAAK,GAAG,GAAG;AAEf,SAASC,aAAaA,CAACC,IAAI,EAAE;EACzB,OAAOC,MAAM,CAACD,IAAI,CAAC,CAACE,OAAO,CAACL,aAAa,EAAEC,KAAK,CAAC;AACrD;AAEA,SAASK,SAASA,CAACC,MAAM,EAAE;EACvB,IAAIC,KAAK,GAAG,EAAE;EACd,KAAK,IAAIC,GAAG,IAAIF,MAAM,EAAE;IACpBC,KAAK,CAACE,IAAI,CAACD,GAAG,GAAGF,MAAM,CAACE,GAAG,CAAC,CAAC;EACjC;EAEA,OAAOD,KAAK,CAACG,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;AAChC;;AAEA;AACA;AACA,SAASC,OAAOA,CAACC,GAAG,EAAE;EAClB;EACA;EACA,IAAIC,IAAI,GAAG,UAAU;EAErB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,CAACG,MAAM,EAAE,EAAED,CAAC,EAAE;IACjCD,IAAI,IAAI,CAACA,IAAI,IAAI,CAAC,KAAKA,IAAI,IAAI,CAAC,CAAC,IAAIA,IAAI,IAAI,CAAC,CAAC,IAAIA,IAAI,IAAI,CAAC,CAAC,IAAIA,IAAI,IAAI,EAAE,CAAC;IAC5EA,IAAI,IAAID,GAAG,CAACI,UAAU,CAACF,CAAC,CAAC;EAC7B;EAEA,OAAOD,IAAI,KAAK,CAAC;AACrB;AAEA,SAAST,SAAS,EAAEO,OAAO,EAAEX,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}