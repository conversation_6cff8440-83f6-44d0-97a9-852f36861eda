{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst r = \"prevArrow.title\",\n  t = \"nextArrow.title\",\n  e = {\n    [r]: \"Previous tab arrow\",\n    [t]: \"Next tab arrow\"\n  };\nexport { e as messages, t as nextArrowTitle, r as prevArrowTitle };", "map": {"version": 3, "names": ["r", "t", "e", "messages", "nextArrowTitle", "prevArrowTitle"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/tabstrip/messages/index.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst r = \"prevArrow.title\", t = \"nextArrow.title\", e = {\n  [r]: \"Previous tab arrow\",\n  [t]: \"Next tab arrow\"\n};\nexport {\n  e as messages,\n  t as nextArrowTitle,\n  r as prevArrowTitle\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAG,iBAAiB;EAAEC,CAAC,GAAG,iBAAiB;EAAEC,CAAC,GAAG;IACtD,CAACF,CAAC,GAAG,oBAAoB;IACzB,CAACC,CAAC,GAAG;EACP,CAAC;AACD,SACEC,CAAC,IAAIC,QAAQ,EACbF,CAAC,IAAIG,cAAc,EACnBJ,CAAC,IAAIK,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}