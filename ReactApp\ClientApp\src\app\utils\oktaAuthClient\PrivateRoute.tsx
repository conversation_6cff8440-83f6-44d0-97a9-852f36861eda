import React, { useEffect } from 'react';
import { useOktaAuth } from '@okta/okta-react';
import { toRelativeUrl } from '@okta/okta-auth-js';
import { Skeleton } from 'antd';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { selectAppInitializing } from '@app/appSlice';
import { useAppDispatch, useAppSelector } from '@app/hooks/useAppSelector';
import { useEvaluateTncQuery } from '@app/api/tncApiSlice';
import { TncStatus } from '@app/types/tncTypes';

export function PrivateRoute() {
  const { tenant, endPoints } = useAppSelector(selectAppInitializing);
  const { oktaAuth, authState } = useOktaAuth();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  const isTncValidated = useAppSelector((state) => state.app.tnc.isValidated);

  const { data: tncData, isLoading: tncLoading, error: tncError } = useEvaluateTncQuery(undefined, {
    skip: !authState?.isAuthenticated || !endPoints || isTncValidated,
  });

  useEffect(() => {
    if (!authState) {
      return;
    }

    if (!authState?.isAuthenticated) {
      const originalUri = toRelativeUrl(window.location.href, window.location.origin);
      oktaAuth.setOriginalUri(originalUri);
      oktaAuth.signInWithRedirect();
    }
  }, [oktaAuth, !!authState, authState?.isAuthenticated]);

  useEffect(() => {
    if (authState?.isAuthenticated && !isTncValidated && !tncLoading && !tncError && tncData) {
      if (tncData.status === TncStatus.PENDING_ACCEPTANCE) {
        if (location.pathname !== '/termsAndConditions') {
          navigate('/termsAndConditions', { state: { from: location.pathname } });
        }
      }
    }
  }, [authState?.isAuthenticated, tncData, tncLoading, tncError, dispatch, navigate, location.pathname, isTncValidated]);

  if (!authState || !authState?.isAuthenticated || !tenant) {
    return <></>;
  }

  if (!endPoints || tncLoading || !isTncValidated) {
    return <Skeleton />;
  }

  return <Outlet />;
}
