{"ast": null, "code": "import PathNode from './path-node';\nvar TextNode = function (PathNode) {\n  function TextNode() {\n    PathNode.apply(this, arguments);\n  }\n  if (PathNode) TextNode.__proto__ = PathNode;\n  TextNode.prototype = Object.create(PathNode && PathNode.prototype);\n  TextNode.prototype.constructor = TextNode;\n  TextNode.prototype.renderTo = function renderTo(ctx) {\n    var text = this.srcElement;\n    var pos = text.position();\n    var size = text.measure();\n    ctx.save();\n    this.setTransform(ctx);\n    this.setClip(ctx);\n    this.setOpacity(ctx);\n    ctx.beginPath();\n    ctx.font = text.options.font;\n    ctx.textAlign = 'left';\n    if (text.options.paintOrder === 'stroke') {\n      this.stroke(ctx, text, pos, size);\n      this.fill(ctx, text, pos, size);\n    } else {\n      this.fill(ctx, text, pos, size);\n      this.stroke(ctx, text, pos, size);\n    }\n    ctx.restore();\n  };\n  TextNode.prototype.stroke = function stroke(ctx, text, pos, size) {\n    if (this.setStroke(ctx)) {\n      this.setLineDash(ctx);\n      ctx.strokeText(text.content(), pos.x, pos.y + size.baseline);\n    }\n  };\n  TextNode.prototype.fill = function fill(ctx, text, pos, size) {\n    if (this.setFill(ctx)) {\n      ctx.fillText(text.content(), pos.x, pos.y + size.baseline);\n    }\n  };\n  return TextNode;\n}(PathNode);\nexport default TextNode;", "map": {"version": 3, "names": ["PathNode", "TextNode", "apply", "arguments", "__proto__", "prototype", "Object", "create", "constructor", "renderTo", "ctx", "text", "srcElement", "pos", "position", "size", "measure", "save", "setTransform", "setClip", "setOpacity", "beginPath", "font", "options", "textAlign", "paintOrder", "stroke", "fill", "restore", "setStroke", "setLineDash", "strokeText", "content", "x", "y", "baseline", "setFill", "fillText"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/canvas/text-node.js"], "sourcesContent": ["import PathNode from './path-node';\n\nvar TextNode = (function (PathNode) {\n    function TextNode () {\n        PathNode.apply(this, arguments);\n    }\n\n    if ( PathNode ) TextNode.__proto__ = PathNode;\n    TextNode.prototype = Object.create( PathNode && PathNode.prototype );\n    TextNode.prototype.constructor = TextNode;\n\n    TextNode.prototype.renderTo = function renderTo (ctx) {\n        var text = this.srcElement;\n        var pos = text.position();\n        var size = text.measure();\n\n        ctx.save();\n\n        this.setTransform(ctx);\n        this.setClip(ctx);\n        this.setOpacity(ctx);\n\n        ctx.beginPath();\n\n        ctx.font = text.options.font;\n        ctx.textAlign = 'left';\n\n        if (text.options.paintOrder === 'stroke') {\n            this.stroke(ctx, text, pos, size);\n            this.fill(ctx, text, pos, size);\n        } else {\n            this.fill(ctx, text, pos, size);\n            this.stroke(ctx, text, pos, size);\n        }\n\n        ctx.restore();\n    };\n\n    TextNode.prototype.stroke = function stroke (ctx, text, pos, size) {\n        if (this.setStroke(ctx)) {\n            this.setLineDash(ctx);\n            ctx.strokeText(text.content(), pos.x, pos.y + size.baseline);\n        }\n    };\n\n    TextNode.prototype.fill = function fill (ctx, text, pos, size) {\n        if (this.setFill(ctx)) {\n            ctx.fillText(text.content(), pos.x, pos.y + size.baseline);\n        }\n    };\n\n    return TextNode;\n}(PathNode));\n\n\nexport default TextNode;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,aAAa;AAElC,IAAIC,QAAQ,GAAI,UAAUD,QAAQ,EAAE;EAChC,SAASC,QAAQA,CAAA,EAAI;IACjBD,QAAQ,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACnC;EAEA,IAAKH,QAAQ,EAAGC,QAAQ,CAACG,SAAS,GAAGJ,QAAQ;EAC7CC,QAAQ,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAEP,QAAQ,IAAIA,QAAQ,CAACK,SAAU,CAAC;EACpEJ,QAAQ,CAACI,SAAS,CAACG,WAAW,GAAGP,QAAQ;EAEzCA,QAAQ,CAACI,SAAS,CAACI,QAAQ,GAAG,SAASA,QAAQA,CAAEC,GAAG,EAAE;IAClD,IAAIC,IAAI,GAAG,IAAI,CAACC,UAAU;IAC1B,IAAIC,GAAG,GAAGF,IAAI,CAACG,QAAQ,CAAC,CAAC;IACzB,IAAIC,IAAI,GAAGJ,IAAI,CAACK,OAAO,CAAC,CAAC;IAEzBN,GAAG,CAACO,IAAI,CAAC,CAAC;IAEV,IAAI,CAACC,YAAY,CAACR,GAAG,CAAC;IACtB,IAAI,CAACS,OAAO,CAACT,GAAG,CAAC;IACjB,IAAI,CAACU,UAAU,CAACV,GAAG,CAAC;IAEpBA,GAAG,CAACW,SAAS,CAAC,CAAC;IAEfX,GAAG,CAACY,IAAI,GAAGX,IAAI,CAACY,OAAO,CAACD,IAAI;IAC5BZ,GAAG,CAACc,SAAS,GAAG,MAAM;IAEtB,IAAIb,IAAI,CAACY,OAAO,CAACE,UAAU,KAAK,QAAQ,EAAE;MACtC,IAAI,CAACC,MAAM,CAAChB,GAAG,EAAEC,IAAI,EAAEE,GAAG,EAAEE,IAAI,CAAC;MACjC,IAAI,CAACY,IAAI,CAACjB,GAAG,EAAEC,IAAI,EAAEE,GAAG,EAAEE,IAAI,CAAC;IACnC,CAAC,MAAM;MACH,IAAI,CAACY,IAAI,CAACjB,GAAG,EAAEC,IAAI,EAAEE,GAAG,EAAEE,IAAI,CAAC;MAC/B,IAAI,CAACW,MAAM,CAAChB,GAAG,EAAEC,IAAI,EAAEE,GAAG,EAAEE,IAAI,CAAC;IACrC;IAEAL,GAAG,CAACkB,OAAO,CAAC,CAAC;EACjB,CAAC;EAED3B,QAAQ,CAACI,SAAS,CAACqB,MAAM,GAAG,SAASA,MAAMA,CAAEhB,GAAG,EAAEC,IAAI,EAAEE,GAAG,EAAEE,IAAI,EAAE;IAC/D,IAAI,IAAI,CAACc,SAAS,CAACnB,GAAG,CAAC,EAAE;MACrB,IAAI,CAACoB,WAAW,CAACpB,GAAG,CAAC;MACrBA,GAAG,CAACqB,UAAU,CAACpB,IAAI,CAACqB,OAAO,CAAC,CAAC,EAAEnB,GAAG,CAACoB,CAAC,EAAEpB,GAAG,CAACqB,CAAC,GAAGnB,IAAI,CAACoB,QAAQ,CAAC;IAChE;EACJ,CAAC;EAEDlC,QAAQ,CAACI,SAAS,CAACsB,IAAI,GAAG,SAASA,IAAIA,CAAEjB,GAAG,EAAEC,IAAI,EAAEE,GAAG,EAAEE,IAAI,EAAE;IAC3D,IAAI,IAAI,CAACqB,OAAO,CAAC1B,GAAG,CAAC,EAAE;MACnBA,GAAG,CAAC2B,QAAQ,CAAC1B,IAAI,CAACqB,OAAO,CAAC,CAAC,EAAEnB,GAAG,CAACoB,CAAC,EAAEpB,GAAG,CAACqB,CAAC,GAAGnB,IAAI,CAACoB,QAAQ,CAAC;IAC9D;EACJ,CAAC;EAED,OAAOlC,QAAQ;AACnB,CAAC,CAACD,QAAQ,CAAE;AAGZ,eAAeC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}