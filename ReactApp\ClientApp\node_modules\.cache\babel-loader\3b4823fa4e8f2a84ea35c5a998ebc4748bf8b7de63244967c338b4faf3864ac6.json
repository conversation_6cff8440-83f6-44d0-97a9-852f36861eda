{"ast": null, "code": "import { isPresent } from './utils';\nvar getterCache = {};\nvar FIELD_REGEX = /\\[(?:(\\d+)|['\"](.*?)['\"])\\]|((?:(?!\\[.*?\\]|\\.).)+)/g;\n// tslint:disable-next-line:no-string-literal\ngetterCache['undefined'] = function (obj) {\n  return obj;\n};\n/**\n * @hidden\n */\nexport var getter = function (field, safe) {\n  var key = field + safe;\n  if (getterCache[key]) {\n    return getterCache[key];\n  }\n  var fields = [];\n  field.replace(FIELD_REGEX, function (_, index, indexAccessor, field) {\n    fields.push(isPresent(index) ? index : indexAccessor || field);\n    return undefined;\n  });\n  getterCache[key] = function (obj) {\n    var result = obj;\n    for (var idx = 0; idx < fields.length; idx++) {\n      result = result[fields[idx]];\n      if (!isPresent(result) && safe) {\n        return result;\n      }\n    }\n    return result;\n  };\n  return getterCache[key];\n};", "map": {"version": 3, "names": ["isPresent", "getter<PERSON>ache", "FIELD_REGEX", "obj", "getter", "field", "safe", "key", "fields", "replace", "_", "index", "indexAccessor", "push", "undefined", "result", "idx", "length"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-data-query/dist/es/accessor.js"], "sourcesContent": ["import { isPresent } from './utils';\nvar getterCache = {};\nvar FIELD_REGEX = /\\[(?:(\\d+)|['\"](.*?)['\"])\\]|((?:(?!\\[.*?\\]|\\.).)+)/g;\n// tslint:disable-next-line:no-string-literal\ngetterCache['undefined'] = function (obj) { return obj; };\n/**\n * @hidden\n */\nexport var getter = function (field, safe) {\n    var key = field + safe;\n    if (getterCache[key]) {\n        return getterCache[key];\n    }\n    var fields = [];\n    field.replace(FIELD_REGEX, function (_, index, indexAccessor, field) {\n        fields.push(isPresent(index) ? index : (indexAccessor || field));\n        return undefined;\n    });\n    getterCache[key] = function (obj) {\n        var result = obj;\n        for (var idx = 0; idx < fields.length; idx++) {\n            result = result[fields[idx]];\n            if (!isPresent(result) && safe) {\n                return result;\n            }\n        }\n        return result;\n    };\n    return getterCache[key];\n};\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,SAAS;AACnC,IAAIC,WAAW,GAAG,CAAC,CAAC;AACpB,IAAIC,WAAW,GAAG,qDAAqD;AACvE;AACAD,WAAW,CAAC,WAAW,CAAC,GAAG,UAAUE,GAAG,EAAE;EAAE,OAAOA,GAAG;AAAE,CAAC;AACzD;AACA;AACA;AACA,OAAO,IAAIC,MAAM,GAAG,SAAAA,CAAUC,KAAK,EAAEC,IAAI,EAAE;EACvC,IAAIC,GAAG,GAAGF,KAAK,GAAGC,IAAI;EACtB,IAAIL,WAAW,CAACM,GAAG,CAAC,EAAE;IAClB,OAAON,WAAW,CAACM,GAAG,CAAC;EAC3B;EACA,IAAIC,MAAM,GAAG,EAAE;EACfH,KAAK,CAACI,OAAO,CAACP,WAAW,EAAE,UAAUQ,CAAC,EAAEC,KAAK,EAAEC,aAAa,EAAEP,KAAK,EAAE;IACjEG,MAAM,CAACK,IAAI,CAACb,SAAS,CAACW,KAAK,CAAC,GAAGA,KAAK,GAAIC,aAAa,IAAIP,KAAM,CAAC;IAChE,OAAOS,SAAS;EACpB,CAAC,CAAC;EACFb,WAAW,CAACM,GAAG,CAAC,GAAG,UAAUJ,GAAG,EAAE;IAC9B,IAAIY,MAAM,GAAGZ,GAAG;IAChB,KAAK,IAAIa,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGR,MAAM,CAACS,MAAM,EAAED,GAAG,EAAE,EAAE;MAC1CD,MAAM,GAAGA,MAAM,CAACP,MAAM,CAACQ,GAAG,CAAC,CAAC;MAC5B,IAAI,CAAChB,SAAS,CAACe,MAAM,CAAC,IAAIT,IAAI,EAAE;QAC5B,OAAOS,MAAM;MACjB;IACJ;IACA,OAAOA,MAAM;EACjB,CAAC;EACD,OAAOd,WAAW,CAACM,GAAG,CAAC;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}