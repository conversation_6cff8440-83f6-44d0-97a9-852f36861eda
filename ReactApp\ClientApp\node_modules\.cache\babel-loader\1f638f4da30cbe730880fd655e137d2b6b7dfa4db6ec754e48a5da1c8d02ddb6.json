{"ast": null, "code": "export var DECIMAL = \"decimal\";\nexport var CURRENCY = \"currency\";\nexport var ACCOUNTING = \"accounting\";\nexport var PERCENT = \"percent\";\nexport var SCIENTIFIC = \"scientific\";\nexport var CURRENCY_PLACEHOLDER = \"$\";\nexport var PERCENT_PLACEHOLDER = \"%\";\nexport var NUMBER_PLACEHOLDER = \"n\";\nexport var LIST_SEPARATOR = \";\";\nexport var GROUP_SEPARATOR = \",\";\nexport var POINT = \".\";\nexport var EMPTY = \"\";\nexport var DEFAULT_LOCALE = \"en\";", "map": {"version": 3, "names": ["DECIMAL", "CURRENCY", "ACCOUNTING", "PERCENT", "SCIENTIFIC", "CURRENCY_PLACEHOLDER", "PERCENT_PLACEHOLDER", "NUMBER_PLACEHOLDER", "LIST_SEPARATOR", "GROUP_SEPARATOR", "POINT", "EMPTY", "DEFAULT_LOCALE"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-intl/dist/es/common/constants.js"], "sourcesContent": ["export var DECIMAL = \"decimal\";\nexport var CURRENCY = \"currency\";\nexport var ACCOUNTING = \"accounting\";\nexport var PERCENT = \"percent\";\nexport var SCIENTIFIC = \"scientific\";\n\nexport var CURRENCY_PLACEHOLDER = \"$\";\nexport var PERCENT_PLACEHOLDER = \"%\";\nexport var NUMBER_PLACEHOLDER = \"n\";\n\nexport var LIST_SEPARATOR = \";\";\nexport var GROUP_SEPARATOR = \",\";\n\nexport var POINT = \".\";\nexport var EMPTY = \"\";\n\nexport var DEFAULT_LOCALE = \"en\";\n\n"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG,SAAS;AAC9B,OAAO,IAAIC,QAAQ,GAAG,UAAU;AAChC,OAAO,IAAIC,UAAU,GAAG,YAAY;AACpC,OAAO,IAAIC,OAAO,GAAG,SAAS;AAC9B,OAAO,IAAIC,UAAU,GAAG,YAAY;AAEpC,OAAO,IAAIC,oBAAoB,GAAG,GAAG;AACrC,OAAO,IAAIC,mBAAmB,GAAG,GAAG;AACpC,OAAO,IAAIC,kBAAkB,GAAG,GAAG;AAEnC,OAAO,IAAIC,cAAc,GAAG,GAAG;AAC/B,OAAO,IAAIC,eAAe,GAAG,GAAG;AAEhC,OAAO,IAAIC,KAAK,GAAG,GAAG;AACtB,OAAO,IAAIC,KAAK,GAAG,EAAE;AAErB,OAAO,IAAIC,cAAc,GAAG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}