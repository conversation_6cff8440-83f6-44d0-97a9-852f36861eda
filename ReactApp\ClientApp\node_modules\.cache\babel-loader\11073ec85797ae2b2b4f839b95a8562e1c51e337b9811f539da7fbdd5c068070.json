{"ast": null, "code": "import { PERCENT_PLACEHOLDER, CURRENCY_PLACEHOLDER, CURRENCY, PERCENT, EMPTY } from '../common/constants';\nimport formatCurrencySymbol from './format-currency-symbol';\nvar literalRegExp = /(\\\\.)|(['][^']*[']?)|([\"][^\"]*[\"]?)/g;\nvar PLACEHOLDER = \"__??__\";\nexport function setStyleOptions(formatOptions, info) {\n  var format = formatOptions.format;\n\n  //multiply number if the format has percent\n  if (format.indexOf(PERCENT_PLACEHOLDER) !== -1) {\n    formatOptions.style = PERCENT;\n    formatOptions.symbol = info.numbers.symbols.percentSign;\n    formatOptions.number *= 100;\n  }\n  if (format.indexOf(CURRENCY_PLACEHOLDER) !== -1) {\n    formatOptions.style = CURRENCY;\n    formatOptions.symbol = formatCurrencySymbol(info);\n  }\n}\nexport function setFormatLiterals(formatOptions) {\n  var format = formatOptions.format;\n  if (format.indexOf(\"'\") > -1 || format.indexOf(\"\\\"\") > -1 || format.indexOf(\"\\\\\") > -1) {\n    var literals = formatOptions.literals = [];\n    formatOptions.format = format.replace(literalRegExp, function (match) {\n      var quoteChar = match.charAt(0).replace(\"\\\\\", EMPTY);\n      var literal = match.slice(1).replace(quoteChar, EMPTY);\n      literals.push(literal);\n      return PLACEHOLDER;\n    });\n  }\n}\nexport function replaceLiterals(number, literals) {\n  var result = number;\n  if (literals) {\n    var length = literals.length;\n    for (var idx = 0; idx < length; idx++) {\n      result = result.replace(PLACEHOLDER, literals[idx]);\n    }\n  }\n  return result;\n}", "map": {"version": 3, "names": ["PERCENT_PLACEHOLDER", "CURRENCY_PLACEHOLDER", "CURRENCY", "PERCENT", "EMPTY", "formatCurrencySymbol", "literalRegExp", "PLACEHOLDER", "setStyleOptions", "formatOptions", "info", "format", "indexOf", "style", "symbol", "numbers", "symbols", "percentSign", "number", "setFormatLiterals", "literals", "replace", "match", "quoteChar", "char<PERSON>t", "literal", "slice", "push", "replaceLiterals", "result", "length", "idx"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-intl/dist/es/numbers/utils.js"], "sourcesContent": ["import { PERCENT_PLACEHOLDER, CURRENCY_PLACEHOLDER, CURRENCY, PERCENT, EMPTY } from '../common/constants';\nimport formatCurrencySymbol from './format-currency-symbol';\n\nvar literalRegExp = /(\\\\.)|(['][^']*[']?)|([\"][^\"]*[\"]?)/g;\nvar PLACEHOLDER = \"__??__\";\n\nexport function setStyleOptions(formatOptions, info) {\n    var format = formatOptions.format;\n\n    //multiply number if the format has percent\n    if (format.indexOf(PERCENT_PLACEHOLDER) !== -1) {\n        formatOptions.style = PERCENT;\n        formatOptions.symbol = info.numbers.symbols.percentSign;\n        formatOptions.number *= 100;\n    }\n\n    if (format.indexOf(CURRENCY_PLACEHOLDER) !== -1) {\n        formatOptions.style = CURRENCY;\n        formatOptions.symbol = formatCurrencySymbol(info);\n    }\n}\n\nexport function setFormatLiterals(formatOptions) {\n    var format = formatOptions.format;\n    if (format.indexOf(\"'\") > -1 || format.indexOf(\"\\\"\") > -1 || format.indexOf(\"\\\\\") > -1) {\n        var literals = formatOptions.literals = [];\n        formatOptions.format = format.replace(literalRegExp, function(match) {\n            var quoteChar = match.charAt(0).replace(\"\\\\\", EMPTY);\n            var literal = match.slice(1).replace(quoteChar, EMPTY);\n\n            literals.push(literal);\n\n            return PLACEHOLDER;\n        });\n    }\n}\n\nexport function replaceLiterals(number, literals) {\n    var result = number;\n    if (literals) {\n        var length = literals.length;\n        for (var idx = 0; idx < length; idx++) {\n            result = result.replace(PLACEHOLDER, literals[idx]);\n        }\n    }\n    return result;\n}"], "mappings": "AAAA,SAASA,mBAAmB,EAAEC,oBAAoB,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,KAAK,QAAQ,qBAAqB;AACzG,OAAOC,oBAAoB,MAAM,0BAA0B;AAE3D,IAAIC,aAAa,GAAG,sCAAsC;AAC1D,IAAIC,WAAW,GAAG,QAAQ;AAE1B,OAAO,SAASC,eAAeA,CAACC,aAAa,EAAEC,IAAI,EAAE;EACjD,IAAIC,MAAM,GAAGF,aAAa,CAACE,MAAM;;EAEjC;EACA,IAAIA,MAAM,CAACC,OAAO,CAACZ,mBAAmB,CAAC,KAAK,CAAC,CAAC,EAAE;IAC5CS,aAAa,CAACI,KAAK,GAAGV,OAAO;IAC7BM,aAAa,CAACK,MAAM,GAAGJ,IAAI,CAACK,OAAO,CAACC,OAAO,CAACC,WAAW;IACvDR,aAAa,CAACS,MAAM,IAAI,GAAG;EAC/B;EAEA,IAAIP,MAAM,CAACC,OAAO,CAACX,oBAAoB,CAAC,KAAK,CAAC,CAAC,EAAE;IAC7CQ,aAAa,CAACI,KAAK,GAAGX,QAAQ;IAC9BO,aAAa,CAACK,MAAM,GAAGT,oBAAoB,CAACK,IAAI,CAAC;EACrD;AACJ;AAEA,OAAO,SAASS,iBAAiBA,CAACV,aAAa,EAAE;EAC7C,IAAIE,MAAM,GAAGF,aAAa,CAACE,MAAM;EACjC,IAAIA,MAAM,CAACC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAID,MAAM,CAACC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAID,MAAM,CAACC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;IACpF,IAAIQ,QAAQ,GAAGX,aAAa,CAACW,QAAQ,GAAG,EAAE;IAC1CX,aAAa,CAACE,MAAM,GAAGA,MAAM,CAACU,OAAO,CAACf,aAAa,EAAE,UAASgB,KAAK,EAAE;MACjE,IAAIC,SAAS,GAAGD,KAAK,CAACE,MAAM,CAAC,CAAC,CAAC,CAACH,OAAO,CAAC,IAAI,EAAEjB,KAAK,CAAC;MACpD,IAAIqB,OAAO,GAAGH,KAAK,CAACI,KAAK,CAAC,CAAC,CAAC,CAACL,OAAO,CAACE,SAAS,EAAEnB,KAAK,CAAC;MAEtDgB,QAAQ,CAACO,IAAI,CAACF,OAAO,CAAC;MAEtB,OAAOlB,WAAW;IACtB,CAAC,CAAC;EACN;AACJ;AAEA,OAAO,SAASqB,eAAeA,CAACV,MAAM,EAAEE,QAAQ,EAAE;EAC9C,IAAIS,MAAM,GAAGX,MAAM;EACnB,IAAIE,QAAQ,EAAE;IACV,IAAIU,MAAM,GAAGV,QAAQ,CAACU,MAAM;IAC5B,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGD,MAAM,EAAEC,GAAG,EAAE,EAAE;MACnCF,MAAM,GAAGA,MAAM,CAACR,OAAO,CAACd,WAAW,EAAEa,QAAQ,CAACW,GAAG,CAAC,CAAC;IACvD;EACJ;EACA,OAAOF,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}