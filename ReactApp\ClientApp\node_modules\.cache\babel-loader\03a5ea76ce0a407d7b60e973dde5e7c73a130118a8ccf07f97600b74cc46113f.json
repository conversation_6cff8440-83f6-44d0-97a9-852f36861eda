{"ast": null, "code": "/* Copyright 2022 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar _DrawLayerBuilder_drawLayer;\nimport { __awaiter, __classPrivateFieldGet, __classPrivateFieldSet } from \"tslib\";\nimport { DrawLayer } from \"./draw-layer\";\n/**\n * @typedef {Object} DrawLayerBuilderOptions\n * @property {number} pageIndex\n */\nclass DrawLayerBuilder {\n  /**\n   * @param {DrawLayerBuilderOptions} options\n   */\n  constructor(options) {\n    // todo: props\n    this.pageIndex = null;\n    this._cancelled = null;\n    // todo: props\n    _DrawLayerBuilder_drawLayer.set(this, null);\n    this.pageIndex = options.pageIndex;\n  }\n  /**\n   * @param {string} intent (default value is 'display')\n   */\n  render() {\n    return __awaiter(this, arguments, void 0, function* (intent = \"display\") {\n      // if (intent !== \"display\" || this.#drawLayer || this._cancelled) {\n      if (intent !== \"display\" || __classPrivateFieldGet(this, _DrawLayerBuilder_drawLayer, \"f\")) {\n        return;\n      }\n      __classPrivateFieldSet(this, _DrawLayerBuilder_drawLayer, new DrawLayer({\n        pageIndex: this.pageIndex\n      }), \"f\");\n    });\n  }\n  cancel() {\n    // this._cancelled = true;\n    if (!__classPrivateFieldGet(this, _DrawLayerBuilder_drawLayer, \"f\")) {\n      return;\n    }\n    __classPrivateFieldGet(this, _DrawLayerBuilder_drawLayer, \"f\").destroy();\n    __classPrivateFieldSet(this, _DrawLayerBuilder_drawLayer, null, \"f\");\n  }\n  setParent(parent) {\n    var _a;\n    (_a = __classPrivateFieldGet(this, _DrawLayerBuilder_drawLayer, \"f\")) === null || _a === void 0 ? void 0 : _a.setParent(parent);\n  }\n  getDrawLayer() {\n    return __classPrivateFieldGet(this, _DrawLayerBuilder_drawLayer, \"f\");\n  }\n}\n_DrawLayerBuilder_drawLayer = new WeakMap();\nexport { DrawLayerBuilder };", "map": {"version": 3, "names": ["_DrawLayerBuilder_drawLayer", "__awaiter", "__classPrivateFieldGet", "__classPrivateFieldSet", "Draw<PERSON>ayer", "DrawLayerBuilder", "constructor", "options", "pageIndex", "_cancelled", "set", "render", "arguments", "intent", "cancel", "destroy", "setParent", "parent", "_a", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "WeakMap"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-pdfviewer-common/dist/es/annotations/draw-layer-builder.js"], "sourcesContent": ["/* Copyright 2022 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar _DrawLayerBuilder_drawLayer;\nimport { __awaiter, __classPrivateFieldGet, __classPrivateFieldSet } from \"tslib\";\nimport { DrawLayer } from \"./draw-layer\";\n/**\n * @typedef {Object} DrawLayerBuilderOptions\n * @property {number} pageIndex\n */\nclass DrawLayerBuilder {\n    /**\n     * @param {DrawLayerBuilderOptions} options\n     */\n    constructor(options) {\n        // todo: props\n        this.pageIndex = null;\n        this._cancelled = null;\n        // todo: props\n        _DrawLayerBuilder_drawLayer.set(this, null);\n        this.pageIndex = options.pageIndex;\n    }\n    /**\n     * @param {string} intent (default value is 'display')\n     */\n    render() {\n        return __awaiter(this, arguments, void 0, function* (intent = \"display\") {\n            // if (intent !== \"display\" || this.#drawLayer || this._cancelled) {\n            if (intent !== \"display\" || __classPrivateFieldGet(this, _DrawLayerBuilder_drawLayer, \"f\")) {\n                return;\n            }\n            __classPrivateFieldSet(this, _DrawLayerBuilder_drawLayer, new DrawLayer({\n                pageIndex: this.pageIndex\n            }), \"f\");\n        });\n    }\n    cancel() {\n        // this._cancelled = true;\n        if (!__classPrivateFieldGet(this, _DrawLayerBuilder_drawLayer, \"f\")) {\n            return;\n        }\n        __classPrivateFieldGet(this, _DrawLayerBuilder_drawLayer, \"f\").destroy();\n        __classPrivateFieldSet(this, _DrawLayerBuilder_drawLayer, null, \"f\");\n    }\n    setParent(parent) {\n        var _a;\n        (_a = __classPrivateFieldGet(this, _DrawLayerBuilder_drawLayer, \"f\")) === null || _a === void 0 ? void 0 : _a.setParent(parent);\n    }\n    getDrawLayer() {\n        return __classPrivateFieldGet(this, _DrawLayerBuilder_drawLayer, \"f\");\n    }\n}\n_DrawLayerBuilder_drawLayer = new WeakMap();\nexport { DrawLayerBuilder };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,2BAA2B;AAC/B,SAASC,SAAS,EAAEC,sBAAsB,EAAEC,sBAAsB,QAAQ,OAAO;AACjF,SAASC,SAAS,QAAQ,cAAc;AACxC;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACnB;AACJ;AACA;EACIC,WAAWA,CAACC,OAAO,EAAE;IACjB;IACA,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB;IACAT,2BAA2B,CAACU,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAC3C,IAAI,CAACF,SAAS,GAAGD,OAAO,CAACC,SAAS;EACtC;EACA;AACJ;AACA;EACIG,MAAMA,CAAA,EAAG;IACL,OAAOV,SAAS,CAAC,IAAI,EAAEW,SAAS,EAAE,KAAK,CAAC,EAAE,WAAWC,MAAM,GAAG,SAAS,EAAE;MACrE;MACA,IAAIA,MAAM,KAAK,SAAS,IAAIX,sBAAsB,CAAC,IAAI,EAAEF,2BAA2B,EAAE,GAAG,CAAC,EAAE;QACxF;MACJ;MACAG,sBAAsB,CAAC,IAAI,EAAEH,2BAA2B,EAAE,IAAII,SAAS,CAAC;QACpEI,SAAS,EAAE,IAAI,CAACA;MACpB,CAAC,CAAC,EAAE,GAAG,CAAC;IACZ,CAAC,CAAC;EACN;EACAM,MAAMA,CAAA,EAAG;IACL;IACA,IAAI,CAACZ,sBAAsB,CAAC,IAAI,EAAEF,2BAA2B,EAAE,GAAG,CAAC,EAAE;MACjE;IACJ;IACAE,sBAAsB,CAAC,IAAI,EAAEF,2BAA2B,EAAE,GAAG,CAAC,CAACe,OAAO,CAAC,CAAC;IACxEZ,sBAAsB,CAAC,IAAI,EAAEH,2BAA2B,EAAE,IAAI,EAAE,GAAG,CAAC;EACxE;EACAgB,SAASA,CAACC,MAAM,EAAE;IACd,IAAIC,EAAE;IACN,CAACA,EAAE,GAAGhB,sBAAsB,CAAC,IAAI,EAAEF,2BAA2B,EAAE,GAAG,CAAC,MAAM,IAAI,IAAIkB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACF,SAAS,CAACC,MAAM,CAAC;EACnI;EACAE,YAAYA,CAAA,EAAG;IACX,OAAOjB,sBAAsB,CAAC,IAAI,EAAEF,2BAA2B,EAAE,GAAG,CAAC;EACzE;AACJ;AACAA,2BAA2B,GAAG,IAAIoB,OAAO,CAAC,CAAC;AAC3C,SAASf,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}