{"ast": null, "code": "import ComplexNumber from './complex-number';\nimport { PRECISION } from '../constants';\nimport { round } from '../../util';\nfunction numberSign(x) {\n  return x < 0 ? -1 : 1;\n}\nfunction solveQuadraticEquation(a, b, c) {\n  var squareRoot = Math.sqrt(Math.pow(b, 2) - 4 * a * c);\n  return [(-b + squareRoot) / (2 * a), (-b - squareRoot) / (2 * a)];\n}\n\n//Cardano's formula\nexport default function solveCubicEquation(a, b, c, d) {\n  if (a === 0) {\n    return solveQuadraticEquation(b, c, d);\n  }\n  var p = (3 * a * c - Math.pow(b, 2)) / (3 * Math.pow(a, 2));\n  var q = (2 * Math.pow(b, 3) - 9 * a * b * c + 27 * Math.pow(a, 2) * d) / (27 * Math.pow(a, 3));\n  var Q = Math.pow(p / 3, 3) + Math.pow(q / 2, 2);\n  var i = new ComplexNumber(0, 1);\n  var b3a = -b / (3 * a);\n  var x1, x2, y1, y2, y3, z1, z2;\n  if (Q < 0) {\n    x1 = new ComplexNumber(-q / 2, Math.sqrt(-Q)).nthRoot(3);\n    x2 = new ComplexNumber(-q / 2, -Math.sqrt(-Q)).nthRoot(3);\n  } else {\n    x1 = -q / 2 + Math.sqrt(Q);\n    x1 = new ComplexNumber(numberSign(x1) * Math.pow(Math.abs(x1), 1 / 3));\n    x2 = -q / 2 - Math.sqrt(Q);\n    x2 = new ComplexNumber(numberSign(x2) * Math.pow(Math.abs(x2), 1 / 3));\n  }\n  y1 = x1.add(x2);\n  z1 = x1.add(x2).multiplyConstant(-1 / 2);\n  z2 = x1.add(x2.negate()).multiplyConstant(Math.sqrt(3) / 2);\n  y2 = z1.add(i.multiply(z2));\n  y3 = z1.add(i.negate().multiply(z2));\n  var result = [];\n  if (y1.isReal()) {\n    result.push(round(y1.real + b3a, PRECISION));\n  }\n  if (y2.isReal()) {\n    result.push(round(y2.real + b3a, PRECISION));\n  }\n  if (y3.isReal()) {\n    result.push(round(y3.real + b3a, PRECISION));\n  }\n  return result;\n}", "map": {"version": 3, "names": ["ComplexNumber", "PRECISION", "round", "numberSign", "x", "solveQuadraticEquation", "a", "b", "c", "squareRoot", "Math", "sqrt", "pow", "solveCubicEquation", "d", "p", "q", "Q", "i", "b3a", "x1", "x2", "y1", "y2", "y3", "z1", "z2", "nthRoot", "abs", "add", "multiplyConstant", "negate", "multiply", "result", "isReal", "push", "real"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/geometry/math/solve-cubic-equation.js"], "sourcesContent": ["import ComplexNumber from './complex-number';\nimport { PRECISION } from '../constants';\nimport { round } from '../../util';\n\nfunction numberSign(x) {\n    return x < 0 ? -1 : 1;\n}\n\nfunction solveQuadraticEquation(a, b, c) {\n    var squareRoot = Math.sqrt(Math.pow(b, 2) - 4 * a * c);\n    return [\n        (-b + squareRoot) / (2 * a),\n        (-b - squareRoot) / (2 * a)\n    ];\n}\n\n//Cardano's formula\nexport default function solveCubicEquation(a, b, c, d) {\n    if (a === 0) {\n        return solveQuadraticEquation(b, c, d);\n    }\n\n    var p = (3 * a * c - Math.pow(b, 2)) / (3 * Math.pow(a, 2));\n    var q = (2 * Math.pow(b, 3) - 9 * a * b * c + 27 * Math.pow(a, 2) * d) / (27 * Math.pow(a, 3));\n    var Q = Math.pow(p / 3, 3) + Math.pow(q / 2, 2);\n    var i = new ComplexNumber(0,1);\n    var b3a = -b / (3 * a);\n    var x1, x2, y1, y2, y3, z1, z2;\n\n    if (Q < 0) {\n        x1 = new ComplexNumber(-q / 2, Math.sqrt(-Q)).nthRoot(3);\n        x2 = new ComplexNumber(-q / 2, - Math.sqrt(-Q)).nthRoot(3);\n    } else {\n        x1 = -q / 2 + Math.sqrt(Q);\n        x1 = new ComplexNumber(numberSign(x1) * Math.pow(Math.abs(x1), 1 / 3));\n        x2 = -q / 2 - Math.sqrt(Q);\n        x2 = new ComplexNumber(numberSign(x2) * Math.pow(Math.abs(x2), 1 / 3));\n    }\n\n    y1 = x1.add(x2);\n\n    z1 = x1.add(x2).multiplyConstant(-1 / 2);\n    z2 = x1.add(x2.negate()).multiplyConstant(Math.sqrt(3) / 2);\n\n    y2 = z1.add(i.multiply(z2));\n    y3 = z1.add(i.negate().multiply(z2));\n\n    var result = [];\n\n    if (y1.isReal()) {\n        result.push(round(y1.real + b3a, PRECISION));\n    }\n    if (y2.isReal()) {\n        result.push(round(y2.real + b3a, PRECISION));\n    }\n    if (y3.isReal()) {\n        result.push(round(y3.real + b3a, PRECISION));\n    }\n\n    return result;\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,kBAAkB;AAC5C,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,KAAK,QAAQ,YAAY;AAElC,SAASC,UAAUA,CAACC,CAAC,EAAE;EACnB,OAAOA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;AACzB;AAEA,SAASC,sBAAsBA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACrC,IAAIC,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,GAAG,CAACL,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGE,CAAC,CAAC;EACtD,OAAO,CACH,CAAC,CAACD,CAAC,GAAGE,UAAU,KAAK,CAAC,GAAGH,CAAC,CAAC,EAC3B,CAAC,CAACC,CAAC,GAAGE,UAAU,KAAK,CAAC,GAAGH,CAAC,CAAC,CAC9B;AACL;;AAEA;AACA,eAAe,SAASO,kBAAkBA,CAACP,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEM,CAAC,EAAE;EACnD,IAAIR,CAAC,KAAK,CAAC,EAAE;IACT,OAAOD,sBAAsB,CAACE,CAAC,EAAEC,CAAC,EAAEM,CAAC,CAAC;EAC1C;EAEA,IAAIC,CAAC,GAAG,CAAC,CAAC,GAAGT,CAAC,GAAGE,CAAC,GAAGE,IAAI,CAACE,GAAG,CAACL,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,GAAGG,IAAI,CAACE,GAAG,CAACN,CAAC,EAAE,CAAC,CAAC,CAAC;EAC3D,IAAIU,CAAC,GAAG,CAAC,CAAC,GAAGN,IAAI,CAACE,GAAG,CAACL,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAGC,CAAC,GAAG,EAAE,GAAGE,IAAI,CAACE,GAAG,CAACN,CAAC,EAAE,CAAC,CAAC,GAAGQ,CAAC,KAAK,EAAE,GAAGJ,IAAI,CAACE,GAAG,CAACN,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9F,IAAIW,CAAC,GAAGP,IAAI,CAACE,GAAG,CAACG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAGL,IAAI,CAACE,GAAG,CAACI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;EAC/C,IAAIE,CAAC,GAAG,IAAIlB,aAAa,CAAC,CAAC,EAAC,CAAC,CAAC;EAC9B,IAAImB,GAAG,GAAG,CAACZ,CAAC,IAAI,CAAC,GAAGD,CAAC,CAAC;EACtB,IAAIc,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EAE9B,IAAIT,CAAC,GAAG,CAAC,EAAE;IACPG,EAAE,GAAG,IAAIpB,aAAa,CAAC,CAACgB,CAAC,GAAG,CAAC,EAAEN,IAAI,CAACC,IAAI,CAAC,CAACM,CAAC,CAAC,CAAC,CAACU,OAAO,CAAC,CAAC,CAAC;IACxDN,EAAE,GAAG,IAAIrB,aAAa,CAAC,CAACgB,CAAC,GAAG,CAAC,EAAE,CAAEN,IAAI,CAACC,IAAI,CAAC,CAACM,CAAC,CAAC,CAAC,CAACU,OAAO,CAAC,CAAC,CAAC;EAC9D,CAAC,MAAM;IACHP,EAAE,GAAG,CAACJ,CAAC,GAAG,CAAC,GAAGN,IAAI,CAACC,IAAI,CAACM,CAAC,CAAC;IAC1BG,EAAE,GAAG,IAAIpB,aAAa,CAACG,UAAU,CAACiB,EAAE,CAAC,GAAGV,IAAI,CAACE,GAAG,CAACF,IAAI,CAACkB,GAAG,CAACR,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IACtEC,EAAE,GAAG,CAACL,CAAC,GAAG,CAAC,GAAGN,IAAI,CAACC,IAAI,CAACM,CAAC,CAAC;IAC1BI,EAAE,GAAG,IAAIrB,aAAa,CAACG,UAAU,CAACkB,EAAE,CAAC,GAAGX,IAAI,CAACE,GAAG,CAACF,IAAI,CAACkB,GAAG,CAACP,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;EAC1E;EAEAC,EAAE,GAAGF,EAAE,CAACS,GAAG,CAACR,EAAE,CAAC;EAEfI,EAAE,GAAGL,EAAE,CAACS,GAAG,CAACR,EAAE,CAAC,CAACS,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACxCJ,EAAE,GAAGN,EAAE,CAACS,GAAG,CAACR,EAAE,CAACU,MAAM,CAAC,CAAC,CAAC,CAACD,gBAAgB,CAACpB,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAE3DY,EAAE,GAAGE,EAAE,CAACI,GAAG,CAACX,CAAC,CAACc,QAAQ,CAACN,EAAE,CAAC,CAAC;EAC3BF,EAAE,GAAGC,EAAE,CAACI,GAAG,CAACX,CAAC,CAACa,MAAM,CAAC,CAAC,CAACC,QAAQ,CAACN,EAAE,CAAC,CAAC;EAEpC,IAAIO,MAAM,GAAG,EAAE;EAEf,IAAIX,EAAE,CAACY,MAAM,CAAC,CAAC,EAAE;IACbD,MAAM,CAACE,IAAI,CAACjC,KAAK,CAACoB,EAAE,CAACc,IAAI,GAAGjB,GAAG,EAAElB,SAAS,CAAC,CAAC;EAChD;EACA,IAAIsB,EAAE,CAACW,MAAM,CAAC,CAAC,EAAE;IACbD,MAAM,CAACE,IAAI,CAACjC,KAAK,CAACqB,EAAE,CAACa,IAAI,GAAGjB,GAAG,EAAElB,SAAS,CAAC,CAAC;EAChD;EACA,IAAIuB,EAAE,CAACU,MAAM,CAAC,CAAC,EAAE;IACbD,MAAM,CAACE,IAAI,CAACjC,KAAK,CAACsB,EAAE,CAACY,IAAI,GAAGjB,GAAG,EAAElB,SAAS,CAAC,CAAC;EAChD;EAEA,OAAOgC,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}