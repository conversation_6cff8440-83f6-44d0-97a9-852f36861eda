{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { useCustomComponent as o } from \"@progress/kendo-react-common\";\nconst m = o;\nexport { m as default };", "map": {"version": 3, "names": ["useCustomComponent", "o", "m", "default"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dropdowns/common/withCustomComponent.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { useCustomComponent as o } from \"@progress/kendo-react-common\";\nconst m = o;\nexport {\n  m as default\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,kBAAkB,IAAIC,CAAC,QAAQ,8BAA8B;AACtE,MAAMC,CAAC,GAAGD,CAAC;AACX,SACEC,CAAC,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}