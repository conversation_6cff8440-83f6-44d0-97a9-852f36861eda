{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { cloneDate as c } from \"@progress/kendo-date-math\";\nconst o = (s, t) => {\n    const n = c(s);\n    return n.setHours(t), n;\n  },\n  m = s => s !== null && s < 12,\n  g = s => s !== null && (!s || s > 11),\n  l = (s, t, n) => !t && !n || s >= t && s <= n,\n  d = (s, t, n) => !t && !n || s >= t || s <= n;\nclass H {\n  constructor(t) {\n    this.intl = t, this.min = null, this.max = null, this.part = null;\n  }\n  /**\n   * @hidden\n   */\n  apply(t, n) {\n    const i = t.getHours(),\n      e = m(i),\n      r = m(n.getHours());\n    if (e && r || !e && !r) return t;\n    const [a, h = 24] = this.normalizedRange(),\n      u = i + (r ? -12 : 12);\n    return o(t, Math.min(Math.max(a, u), h || 24));\n  }\n  /**\n   * @hidden\n   */\n  configure(t) {\n    const {\n      min: n = this.min,\n      max: i = this.max,\n      part: e = this.part\n    } = t;\n    this.min = n, this.max = i, this.part = e;\n  }\n  /**\n   * @hidden\n   */\n  data(t) {\n    const n = this.part && this.part.names;\n    if (!n || !this.min) return [];\n    const i = [],\n      [e, r] = this.normalizedRange(),\n      a = this.intl.dateFormatNames(n);\n    return m(e) && i.push({\n      text: a.am,\n      value: o(this.min, e)\n    }), g(r) && i.push({\n      text: a.pm,\n      value: o(this.min, Math.max(12, r))\n    }), this.min.getHours() !== e ? i.reverse() : i;\n  }\n  /**\n   * @hidden\n   */\n  isRangeChanged(t, n) {\n    return !1;\n  }\n  /**\n   * @hidden\n   */\n  limitRange(t, n, i) {\n    return [t, n];\n  }\n  /**\n   * @hidden\n   */\n  total() {\n    const [t, n] = this.normalizedRange();\n    return !t && !n ? 2 : t > 11 || n < 12 ? 1 : 2;\n  }\n  /**\n   * @hidden\n   */\n  selectedIndex(t) {\n    if (!this.valueInList(t) || !this.min) return -1;\n    const n = Math.floor(t.getHours() / 12);\n    return this.min.getHours() === this.normalizedRange()[0] ? n : n === 0 ? 1 : 0;\n  }\n  /**\n   * @hidden\n   */\n  valueInList(t) {\n    return !this.min || !this.max ? !1 : (this.min.getHours() !== this.normalizedRange()[0] ? d : l)(t.getHours(), this.min.getHours(), this.max.getHours());\n  }\n  normalizedRange() {\n    const t = this.min.getHours(),\n      n = this.max.getHours();\n    return [Math.min(t, n), Math.max(t, n)];\n  }\n}\nexport { H as DayPeriodService };", "map": {"version": 3, "names": ["cloneDate", "c", "o", "s", "t", "n", "setHours", "m", "g", "l", "d", "H", "constructor", "intl", "min", "max", "part", "apply", "i", "getHours", "e", "r", "a", "h", "normalizedRange", "u", "Math", "configure", "data", "names", "dateFormatNames", "push", "text", "am", "value", "pm", "reverse", "isRangeChanged", "limitRange", "total", "selectedIndex", "valueInList", "floor", "DayPeriodService"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dateinputs/timepicker/services/DayPeriodService.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { cloneDate as c } from \"@progress/kendo-date-math\";\nconst o = (s, t) => {\n  const n = c(s);\n  return n.setHours(t), n;\n}, m = (s) => s !== null && s < 12, g = (s) => s !== null && (!s || s > 11), l = (s, t, n) => !t && !n || s >= t && s <= n, d = (s, t, n) => !t && !n || s >= t || s <= n;\nclass H {\n  constructor(t) {\n    this.intl = t, this.min = null, this.max = null, this.part = null;\n  }\n  /**\n   * @hidden\n   */\n  apply(t, n) {\n    const i = t.getHours(), e = m(i), r = m(n.getHours());\n    if (e && r || !e && !r)\n      return t;\n    const [a, h = 24] = this.normalizedRange(), u = i + (r ? -12 : 12);\n    return o(t, Math.min(Math.max(a, u), h || 24));\n  }\n  /**\n   * @hidden\n   */\n  configure(t) {\n    const { min: n = this.min, max: i = this.max, part: e = this.part } = t;\n    this.min = n, this.max = i, this.part = e;\n  }\n  /**\n   * @hidden\n   */\n  data(t) {\n    const n = this.part && this.part.names;\n    if (!n || !this.min)\n      return [];\n    const i = [], [e, r] = this.normalizedRange(), a = this.intl.dateFormatNames(n);\n    return m(e) && i.push({ text: a.am, value: o(this.min, e) }), g(r) && i.push({ text: a.pm, value: o(this.min, Math.max(12, r)) }), this.min.getHours() !== e ? i.reverse() : i;\n  }\n  /**\n   * @hidden\n   */\n  isRangeChanged(t, n) {\n    return !1;\n  }\n  /**\n   * @hidden\n   */\n  limitRange(t, n, i) {\n    return [t, n];\n  }\n  /**\n   * @hidden\n   */\n  total() {\n    const [t, n] = this.normalizedRange();\n    return !t && !n ? 2 : t > 11 || n < 12 ? 1 : 2;\n  }\n  /**\n   * @hidden\n   */\n  selectedIndex(t) {\n    if (!this.valueInList(t) || !this.min)\n      return -1;\n    const n = Math.floor(t.getHours() / 12);\n    return this.min.getHours() === this.normalizedRange()[0] ? n : n === 0 ? 1 : 0;\n  }\n  /**\n   * @hidden\n   */\n  valueInList(t) {\n    return !this.min || !this.max ? !1 : (this.min.getHours() !== this.normalizedRange()[0] ? d : l)(t.getHours(), this.min.getHours(), this.max.getHours());\n  }\n  normalizedRange() {\n    const t = this.min.getHours(), n = this.max.getHours();\n    return [Math.min(t, n), Math.max(t, n)];\n  }\n}\nexport {\n  H as DayPeriodService\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,IAAIC,CAAC,QAAQ,2BAA2B;AAC1D,MAAMC,CAAC,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAK;IAClB,MAAMC,CAAC,GAAGJ,CAAC,CAACE,CAAC,CAAC;IACd,OAAOE,CAAC,CAACC,QAAQ,CAACF,CAAC,CAAC,EAAEC,CAAC;EACzB,CAAC;EAAEE,CAAC,GAAIJ,CAAC,IAAKA,CAAC,KAAK,IAAI,IAAIA,CAAC,GAAG,EAAE;EAAEK,CAAC,GAAIL,CAAC,IAAKA,CAAC,KAAK,IAAI,KAAK,CAACA,CAAC,IAAIA,CAAC,GAAG,EAAE,CAAC;EAAEM,CAAC,GAAGA,CAACN,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK,CAACD,CAAC,IAAI,CAACC,CAAC,IAAIF,CAAC,IAAIC,CAAC,IAAID,CAAC,IAAIE,CAAC;EAAEK,CAAC,GAAGA,CAACP,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK,CAACD,CAAC,IAAI,CAACC,CAAC,IAAIF,CAAC,IAAIC,CAAC,IAAID,CAAC,IAAIE,CAAC;AACzK,MAAMM,CAAC,CAAC;EACNC,WAAWA,CAACR,CAAC,EAAE;IACb,IAAI,CAACS,IAAI,GAAGT,CAAC,EAAE,IAAI,CAACU,GAAG,GAAG,IAAI,EAAE,IAAI,CAACC,GAAG,GAAG,IAAI,EAAE,IAAI,CAACC,IAAI,GAAG,IAAI;EACnE;EACA;AACF;AACA;EACEC,KAAKA,CAACb,CAAC,EAAEC,CAAC,EAAE;IACV,MAAMa,CAAC,GAAGd,CAAC,CAACe,QAAQ,CAAC,CAAC;MAAEC,CAAC,GAAGb,CAAC,CAACW,CAAC,CAAC;MAAEG,CAAC,GAAGd,CAAC,CAACF,CAAC,CAACc,QAAQ,CAAC,CAAC,CAAC;IACrD,IAAIC,CAAC,IAAIC,CAAC,IAAI,CAACD,CAAC,IAAI,CAACC,CAAC,EACpB,OAAOjB,CAAC;IACV,MAAM,CAACkB,CAAC,EAAEC,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;MAAEC,CAAC,GAAGP,CAAC,IAAIG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC;IAClE,OAAOnB,CAAC,CAACE,CAAC,EAAEsB,IAAI,CAACZ,GAAG,CAACY,IAAI,CAACX,GAAG,CAACO,CAAC,EAAEG,CAAC,CAAC,EAAEF,CAAC,IAAI,EAAE,CAAC,CAAC;EAChD;EACA;AACF;AACA;EACEI,SAASA,CAACvB,CAAC,EAAE;IACX,MAAM;MAAEU,GAAG,EAAET,CAAC,GAAG,IAAI,CAACS,GAAG;MAAEC,GAAG,EAAEG,CAAC,GAAG,IAAI,CAACH,GAAG;MAAEC,IAAI,EAAEI,CAAC,GAAG,IAAI,CAACJ;IAAK,CAAC,GAAGZ,CAAC;IACvE,IAAI,CAACU,GAAG,GAAGT,CAAC,EAAE,IAAI,CAACU,GAAG,GAAGG,CAAC,EAAE,IAAI,CAACF,IAAI,GAAGI,CAAC;EAC3C;EACA;AACF;AACA;EACEQ,IAAIA,CAACxB,CAAC,EAAE;IACN,MAAMC,CAAC,GAAG,IAAI,CAACW,IAAI,IAAI,IAAI,CAACA,IAAI,CAACa,KAAK;IACtC,IAAI,CAACxB,CAAC,IAAI,CAAC,IAAI,CAACS,GAAG,EACjB,OAAO,EAAE;IACX,MAAMI,CAAC,GAAG,EAAE;MAAE,CAACE,CAAC,EAAEC,CAAC,CAAC,GAAG,IAAI,CAACG,eAAe,CAAC,CAAC;MAAEF,CAAC,GAAG,IAAI,CAACT,IAAI,CAACiB,eAAe,CAACzB,CAAC,CAAC;IAC/E,OAAOE,CAAC,CAACa,CAAC,CAAC,IAAIF,CAAC,CAACa,IAAI,CAAC;MAAEC,IAAI,EAAEV,CAAC,CAACW,EAAE;MAAEC,KAAK,EAAEhC,CAAC,CAAC,IAAI,CAACY,GAAG,EAAEM,CAAC;IAAE,CAAC,CAAC,EAAEZ,CAAC,CAACa,CAAC,CAAC,IAAIH,CAAC,CAACa,IAAI,CAAC;MAAEC,IAAI,EAAEV,CAAC,CAACa,EAAE;MAAED,KAAK,EAAEhC,CAAC,CAAC,IAAI,CAACY,GAAG,EAAEY,IAAI,CAACX,GAAG,CAAC,EAAE,EAAEM,CAAC,CAAC;IAAE,CAAC,CAAC,EAAE,IAAI,CAACP,GAAG,CAACK,QAAQ,CAAC,CAAC,KAAKC,CAAC,GAAGF,CAAC,CAACkB,OAAO,CAAC,CAAC,GAAGlB,CAAC;EAChL;EACA;AACF;AACA;EACEmB,cAAcA,CAACjC,CAAC,EAAEC,CAAC,EAAE;IACnB,OAAO,CAAC,CAAC;EACX;EACA;AACF;AACA;EACEiC,UAAUA,CAAClC,CAAC,EAAEC,CAAC,EAAEa,CAAC,EAAE;IAClB,OAAO,CAACd,CAAC,EAAEC,CAAC,CAAC;EACf;EACA;AACF;AACA;EACEkC,KAAKA,CAAA,EAAG;IACN,MAAM,CAACnC,CAAC,EAAEC,CAAC,CAAC,GAAG,IAAI,CAACmB,eAAe,CAAC,CAAC;IACrC,OAAO,CAACpB,CAAC,IAAI,CAACC,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAG,EAAE,IAAIC,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC;EAChD;EACA;AACF;AACA;EACEmC,aAAaA,CAACpC,CAAC,EAAE;IACf,IAAI,CAAC,IAAI,CAACqC,WAAW,CAACrC,CAAC,CAAC,IAAI,CAAC,IAAI,CAACU,GAAG,EACnC,OAAO,CAAC,CAAC;IACX,MAAMT,CAAC,GAAGqB,IAAI,CAACgB,KAAK,CAACtC,CAAC,CAACe,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC;IACvC,OAAO,IAAI,CAACL,GAAG,CAACK,QAAQ,CAAC,CAAC,KAAK,IAAI,CAACK,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGnB,CAAC,GAAGA,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;EAChF;EACA;AACF;AACA;EACEoC,WAAWA,CAACrC,CAAC,EAAE;IACb,OAAO,CAAC,IAAI,CAACU,GAAG,IAAI,CAAC,IAAI,CAACC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAACD,GAAG,CAACK,QAAQ,CAAC,CAAC,KAAK,IAAI,CAACK,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGd,CAAC,GAAGD,CAAC,EAAEL,CAAC,CAACe,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACL,GAAG,CAACK,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACJ,GAAG,CAACI,QAAQ,CAAC,CAAC,CAAC;EAC1J;EACAK,eAAeA,CAAA,EAAG;IAChB,MAAMpB,CAAC,GAAG,IAAI,CAACU,GAAG,CAACK,QAAQ,CAAC,CAAC;MAAEd,CAAC,GAAG,IAAI,CAACU,GAAG,CAACI,QAAQ,CAAC,CAAC;IACtD,OAAO,CAACO,IAAI,CAACZ,GAAG,CAACV,CAAC,EAAEC,CAAC,CAAC,EAAEqB,IAAI,CAACX,GAAG,CAACX,CAAC,EAAEC,CAAC,CAAC,CAAC;EACzC;AACF;AACA,SACEM,CAAC,IAAIgC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}