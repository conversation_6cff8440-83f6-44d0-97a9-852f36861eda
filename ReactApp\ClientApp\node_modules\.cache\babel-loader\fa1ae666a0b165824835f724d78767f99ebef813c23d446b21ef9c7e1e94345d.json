{"ast": null, "code": "/* Copyright 2015 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// import {\n//     BaseCanvasFactory,\n//     BaseCMapReaderFactory,\n//     BaseFilterFactory,\n//     BaseStandardFontDataFactory,\n//     BaseSVGFactory,\n// } from \"./base_factory.js\";\n// import {\n//     BaseException,\n//     FeatureTest,\n//     shadow,\n//     stringToBytes,\n//     Util,\n//     warn,\n// } from \"../shared/util.js\";\n// const SVG_NS = \"http://www.w3.org/2000/svg\";\n// class PixelsPerInch {\n//     static CSS = 96.0;\n//     static PDF = 72.0;\n//     static PDF_TO_CSS_UNITS = this.CSS / this.PDF;\n// }\n// /**\n//  * FilterFactory aims to create some SVG filters we can use when drawing an\n//  * image (or whatever) on a canvas.\n//  * Filters aren't applied with ctx.putImageData because it just overwrites the\n//  * underlying pixels.\n//  * With these filters, it's possible for example to apply some transfer maps on\n//  * an image without the need to apply them on the pixel arrays: the renderer\n//  * does the magic for us.\n//  */\n// class DOMFilterFactory extends BaseFilterFactory {\n//     #baseUrl;\n//     #_cache;\n//     #_defs;\n//     #docId;\n//     #document;\n//     #_hcmCache;\n//     #id = 0;\n//     constructor({ docId, ownerDocument = globalThis.document } = {}) {\n//         super();\n//         this.#docId = docId;\n//         this.#document = ownerDocument;\n//     }\n//     get #cache() {\n//         return (this.#_cache ||= new Map());\n//     }\n//     get #hcmCache() {\n//         return (this.#_hcmCache ||= new Map());\n//     }\n//     get #defs() {\n//         if (!this.#_defs) {\n//             const div = this.#document.createElement(\"div\");\n//             const { style } = div;\n//             style.visibility = \"hidden\";\n//             style.contain = \"strict\";\n//             style.width = style.height = 0;\n//             style.position = \"absolute\";\n//             style.top = style.left = 0;\n//             style.zIndex = -1;\n//             const svg = this.#document.createElementNS(SVG_NS, \"svg\");\n//             svg.setAttribute(\"width\", 0);\n//             svg.setAttribute(\"height\", 0);\n//             this.#_defs = this.#document.createElementNS(SVG_NS, \"defs\");\n//             div.append(svg);\n//             svg.append(this.#_defs);\n//             this.#document.body.append(div);\n//         }\n//         return this.#_defs;\n//     }\n//     #createTables(maps) {\n//         if (maps.length === 1) {\n//             const mapR = maps[0];\n//             const buffer = new Array(256);\n//             for (let i = 0; i < 256; i++) {\n//                 buffer[i] = mapR[i] / 255;\n//             }\n//             const table = buffer.join(\",\");\n//             return [table, table, table];\n//         }\n//         const [mapR, mapG, mapB] = maps;\n//         const bufferR = new Array(256);\n//         const bufferG = new Array(256);\n//         const bufferB = new Array(256);\n//         for (let i = 0; i < 256; i++) {\n//             bufferR[i] = mapR[i] / 255;\n//             bufferG[i] = mapG[i] / 255;\n//             bufferB[i] = mapB[i] / 255;\n//         }\n//         return [bufferR.join(\",\"), bufferG.join(\",\"), bufferB.join(\",\")];\n//     }\n//     #createUrl(id) {\n//         if (this.#baseUrl === undefined) {\n//             // Unless a `<base>`-element is present a relative URL should work.\n//             this.#baseUrl = \"\";\n//             const url = this.#document.URL;\n//             if (url !== this.#document.baseURI) {\n//                 if (isDataScheme(url)) {\n//                     warn('#createUrl: ignore \"data:\"-URL for performance reasons.');\n//                 } else {\n//                     this.#baseUrl = url.split(\"#\", 1)[0];\n//                 }\n//             }\n//         }\n//         return `url(${this.#baseUrl}#${id})`;\n//     }\n//     addFilter(maps) {\n//         if (!maps) {\n//             return \"none\";\n//         }\n//         // When a page is zoomed the page is re-drawn but the maps are likely\n//         // the same.\n//         let value = this.#cache.get(maps);\n//         if (value) {\n//             return value;\n//         }\n//         const [tableR, tableG, tableB] = this.#createTables(maps);\n//         const key = maps.length === 1 ? tableR : `${tableR}${tableG}${tableB}`;\n//         value = this.#cache.get(key);\n//         if (value) {\n//             this.#cache.set(maps, value);\n//             return value;\n//         }\n//         // We create a SVG filter: feComponentTransferElement\n//         //  https://www.w3.org/TR/SVG11/filters.html#feComponentTransferElement\n//         const id = `g_${this.#docId}_transfer_map_${this.#id++}`;\n//         const url = this.#createUrl(id);\n//         this.#cache.set(maps, url);\n//         this.#cache.set(key, url);\n//         const filter = this.#createFilter(id);\n//         this.#addTransferMapConversion(tableR, tableG, tableB, filter);\n//         return url;\n//     }\n//     addHCMFilter(fgColor, bgColor) {\n//         const key = `${fgColor}-${bgColor}`;\n//         const filterName = \"base\";\n//         let info = this.#hcmCache.get(filterName);\n//         if (info?.key === key) {\n//             return info.url;\n//         }\n//         if (info) {\n//             info.filter?.remove();\n//             info.key = key;\n//             info.url = \"none\";\n//             info.filter = null;\n//         } else {\n//             info = {\n//                 key,\n//                 url: \"none\",\n//                 filter: null,\n//             };\n//             this.#hcmCache.set(filterName, info);\n//         }\n//         if (!fgColor || !bgColor) {\n//             return info.url;\n//         }\n//         const fgRGB = this.#getRGB(fgColor);\n//         fgColor = Util.makeHexColor(...fgRGB);\n//         const bgRGB = this.#getRGB(bgColor);\n//         bgColor = Util.makeHexColor(...bgRGB);\n//         this.#defs.style.color = \"\";\n//         if (\n//             (fgColor === \"#000000\" && bgColor === \"#ffffff\") ||\n//             fgColor === bgColor\n//         ) {\n//             return info.url;\n//         }\n//         // https://developer.mozilla.org/en-US/docs/Web/Accessibility/Understanding_Colors_and_Luminance\n//         //\n//         // Relative luminance:\n//         // https://www.w3.org/TR/WCAG20/#relativeluminancedef\n//         //\n//         // We compute the rounded luminance of the default background color.\n//         // Then for every color in the pdf, if its rounded luminance is the\n//         // same as the background one then it's replaced by the new\n//         // background color else by the foreground one.\n//         const map = new Array(256);\n//         for (let i = 0; i <= 255; i++) {\n//             const x = i / 255;\n//             map[i] = x <= 0.03928 ? x / 12.92 : ((x + 0.055) / 1.055) ** 2.4;\n//         }\n//         const table = map.join(\",\");\n//         const id = `g_${this.#docId}_hcm_filter`;\n//         const filter = (info.filter = this.#createFilter(id));\n//         this.#addTransferMapConversion(table, table, table, filter);\n//         this.#addGrayConversion(filter);\n//         const getSteps = (c, n) => {\n//             const start = fgRGB[c] / 255;\n//             const end = bgRGB[c] / 255;\n//             const arr = new Array(n + 1);\n//             for (let i = 0; i <= n; i++) {\n//                 arr[i] = start + (i / n) * (end - start);\n//             }\n//             return arr.join(\",\");\n//         };\n//         this.#addTransferMapConversion(\n//             getSteps(0, 5),\n//             getSteps(1, 5),\n//             getSteps(2, 5),\n//             filter\n//         );\n//         info.url = this.#createUrl(id);\n//         return info.url;\n//     }\n//     addAlphaFilter(map) {\n//         // When a page is zoomed the page is re-drawn but the maps are likely\n//         // the same.\n//         let value = this.#cache.get(map);\n//         if (value) {\n//             return value;\n//         }\n//         const [tableA] = this.#createTables([map]);\n//         const key = `alpha_${tableA}`;\n//         value = this.#cache.get(key);\n//         if (value) {\n//             this.#cache.set(map, value);\n//             return value;\n//         }\n//         const id = `g_${this.#docId}_alpha_map_${this.#id++}`;\n//         const url = this.#createUrl(id);\n//         this.#cache.set(map, url);\n//         this.#cache.set(key, url);\n//         const filter = this.#createFilter(id);\n//         this.#addTransferMapAlphaConversion(tableA, filter);\n//         return url;\n//     }\n//     addLuminosityFilter(map) {\n//         // When a page is zoomed the page is re-drawn but the maps are likely\n//         // the same.\n//         let value = this.#cache.get(map || \"luminosity\");\n//         if (value) {\n//             return value;\n//         }\n//         let tableA, key;\n//         if (map) {\n//             [tableA] = this.#createTables([map]);\n//             key = `luminosity_${tableA}`;\n//         } else {\n//             key = \"luminosity\";\n//         }\n//         value = this.#cache.get(key);\n//         if (value) {\n//             this.#cache.set(map, value);\n//             return value;\n//         }\n//         const id = `g_${this.#docId}_luminosity_map_${this.#id++}`;\n//         const url = this.#createUrl(id);\n//         this.#cache.set(map, url);\n//         this.#cache.set(key, url);\n//         const filter = this.#createFilter(id);\n//         this.#addLuminosityConversion(filter);\n//         if (map) {\n//             this.#addTransferMapAlphaConversion(tableA, filter);\n//         }\n//         return url;\n//     }\n//     addHighlightHCMFilter(filterName, fgColor, bgColor, newFgColor, newBgColor) {\n//         const key = `${fgColor}-${bgColor}-${newFgColor}-${newBgColor}`;\n//         let info = this.#hcmCache.get(filterName);\n//         if (info?.key === key) {\n//             return info.url;\n//         }\n//         if (info) {\n//             info.filter?.remove();\n//             info.key = key;\n//             info.url = \"none\";\n//             info.filter = null;\n//         } else {\n//             info = {\n//                 key,\n//                 url: \"none\",\n//                 filter: null,\n//             };\n//             this.#hcmCache.set(filterName, info);\n//         }\n//         if (!fgColor || !bgColor) {\n//             return info.url;\n//         }\n//         const [fgRGB, bgRGB] = [fgColor, bgColor].map(this.#getRGB.bind(this));\n//         let fgGray = Math.round(\n//             0.2126 * fgRGB[0] + 0.7152 * fgRGB[1] + 0.0722 * fgRGB[2]\n//         );\n//         let bgGray = Math.round(\n//             0.2126 * bgRGB[0] + 0.7152 * bgRGB[1] + 0.0722 * bgRGB[2]\n//         );\n//         let [newFgRGB, newBgRGB] = [newFgColor, newBgColor].map(\n//             this.#getRGB.bind(this)\n//         );\n//         if (bgGray < fgGray) {\n//             [fgGray, bgGray, newFgRGB, newBgRGB] = [\n//                 bgGray,\n//                 fgGray,\n//                 newBgRGB,\n//                 newFgRGB,\n//             ];\n//         }\n//         this.#defs.style.color = \"\";\n//         // Now we can create the filters to highlight some canvas parts.\n//         // The colors in the pdf will almost be Canvas and CanvasText, hence we\n//         // want to filter them to finally get Highlight and HighlightText.\n//         // Since we're in HCM the background color and the foreground color should\n//         // be really different when converted to grayscale (if they're not then it\n//         // means that we've a poor contrast). Once the canvas colors are converted\n//         // to grayscale we can easily map them on their new colors.\n//         // The grayscale step is important because if we've something like:\n//         //   fgColor = #FF....\n//         //   bgColor = #FF....\n//         //   then we are enable to map the red component on the new red components\n//         //   which can be different.\n//         const getSteps = (fg, bg, n) => {\n//             const arr = new Array(256);\n//             const step = (bgGray - fgGray) / n;\n//             const newStart = fg / 255;\n//             const newStep = (bg - fg) / (255 * n);\n//             let prev = 0;\n//             for (let i = 0; i <= n; i++) {\n//                 const k = Math.round(fgGray + i * step);\n//                 const value = newStart + i * newStep;\n//                 for (let j = prev; j <= k; j++) {\n//                     arr[j] = value;\n//                 }\n//                 prev = k + 1;\n//             }\n//             for (let i = prev; i < 256; i++) {\n//                 arr[i] = arr[prev - 1];\n//             }\n//             return arr.join(\",\");\n//         };\n//         const id = `g_${this.#docId}_hcm_${filterName}_filter`;\n//         const filter = (info.filter = this.#createFilter(id));\n//         this.#addGrayConversion(filter);\n//         this.#addTransferMapConversion(\n//             getSteps(newFgRGB[0], newBgRGB[0], 5),\n//             getSteps(newFgRGB[1], newBgRGB[1], 5),\n//             getSteps(newFgRGB[2], newBgRGB[2], 5),\n//             filter\n//         );\n//         info.url = this.#createUrl(id);\n//         return info.url;\n//     }\n//     destroy(keepHCM = false) {\n//         if (keepHCM && this.#hcmCache.size !== 0) {\n//             return;\n//         }\n//         if (this.#_defs) {\n//             this.#_defs.parentNode.parentNode.remove();\n//             this.#_defs = null;\n//         }\n//         if (this.#_cache) {\n//             this.#_cache.clear();\n//             this.#_cache = null;\n//         }\n//         this.#id = 0;\n//     }\n//     #addLuminosityConversion(filter) {\n//         const feColorMatrix = this.#document.createElementNS(\n//             SVG_NS,\n//             \"feColorMatrix\"\n//         );\n//         feColorMatrix.setAttribute(\"type\", \"matrix\");\n//         feColorMatrix.setAttribute(\n//             \"values\",\n//             \"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0.59 0.11 0 0\"\n//         );\n//         filter.append(feColorMatrix);\n//     }\n//     #addGrayConversion(filter) {\n//         const feColorMatrix = this.#document.createElementNS(\n//             SVG_NS,\n//             \"feColorMatrix\"\n//         );\n//         feColorMatrix.setAttribute(\"type\", \"matrix\");\n//         feColorMatrix.setAttribute(\n//             \"values\",\n//             \"0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0 0 0 1 0\"\n//         );\n//         filter.append(feColorMatrix);\n//     }\n//     #createFilter(id) {\n//         const filter = this.#document.createElementNS(SVG_NS, \"filter\");\n//         filter.setAttribute(\"color-interpolation-filters\", \"sRGB\");\n//         filter.setAttribute(\"id\", id);\n//         this.#defs.append(filter);\n//         return filter;\n//     }\n//     #appendFeFunc(feComponentTransfer, func, table) {\n//         const feFunc = this.#document.createElementNS(SVG_NS, func);\n//         feFunc.setAttribute(\"type\", \"discrete\");\n//         feFunc.setAttribute(\"tableValues\", table);\n//         feComponentTransfer.append(feFunc);\n//     }\n//     #addTransferMapConversion(rTable, gTable, bTable, filter) {\n//         const feComponentTransfer = this.#document.createElementNS(\n//             SVG_NS,\n//             \"feComponentTransfer\"\n//         );\n//         filter.append(feComponentTransfer);\n//         this.#appendFeFunc(feComponentTransfer, \"feFuncR\", rTable);\n//         this.#appendFeFunc(feComponentTransfer, \"feFuncG\", gTable);\n//         this.#appendFeFunc(feComponentTransfer, \"feFuncB\", bTable);\n//     }\n//     #addTransferMapAlphaConversion(aTable, filter) {\n//         const feComponentTransfer = this.#document.createElementNS(\n//             SVG_NS,\n//             \"feComponentTransfer\"\n//         );\n//         filter.append(feComponentTransfer);\n//         this.#appendFeFunc(feComponentTransfer, \"feFuncA\", aTable);\n//     }\n//     #getRGB(color) {\n//         this.#defs.style.color = color;\n//         return getRGB(getComputedStyle(this.#defs).getPropertyValue(\"color\"));\n//     }\n// }\n// class DOMCanvasFactory extends BaseCanvasFactory {\n//     constructor({ ownerDocument = globalThis.document, enableHWA = false } = {}) {\n//         super({ enableHWA });\n//         this._document = ownerDocument;\n//     }\n//     /**\n//      * @ignore\n//      */\n//     _createCanvas(width, height) {\n//         const canvas = this._document.createElement(\"canvas\");\n//         canvas.width = width;\n//         canvas.height = height;\n//         return canvas;\n//     }\n// }\n// async function fetchData(url, type = \"text\") {\n//     if (\n//         (typeof PDFJSDev !== \"undefined\" && PDFJSDev.test(\"MOZCENTRAL\")) ||\n//         isValidFetchUrl(url, document.baseURI)\n//     ) {\n//         const response = await fetch(url);\n//         if (!response.ok) {\n//             throw new Error(response.statusText);\n//         }\n//         switch (type) {\n//             case \"arraybuffer\":\n//                 return response.arrayBuffer();\n//             case \"blob\":\n//                 return response.blob();\n//             case \"json\":\n//                 return response.json();\n//         }\n//         return response.text();\n//     }\n//     // The Fetch API is not supported.\n//     return new Promise((resolve, reject) => {\n//         const request = new XMLHttpRequest();\n//         request.open(\"GET\", url, /* async = */ true);\n//         request.responseType = type;\n//         request.onreadystatechange = () => {\n//             if (request.readyState !== XMLHttpRequest.DONE) {\n//                 return;\n//             }\n//             if (request.status === 200 || request.status === 0) {\n//                 switch (type) {\n//                     case \"arraybuffer\":\n//                     case \"blob\":\n//                     case \"json\":\n//                         resolve(request.response);\n//                         return;\n//                 }\n//                 resolve(request.responseText);\n//                 return;\n//             }\n//             reject(new Error(request.statusText));\n//         };\n//         request.send(null);\n//     });\n// }\n// class DOMCMapReaderFactory extends BaseCMapReaderFactory {\n//     /**\n//      * @ignore\n//      */\n//     _fetchData(url, compressionType) {\n//         return fetchData(\n//             url,\n//         /* type = */ this.isCompressed ? \"arraybuffer\" : \"text\"\n//         ).then(data => ({\n//             cMapData:\n//                 data instanceof ArrayBuffer\n//                     ? new Uint8Array(data)\n//                     : stringToBytes(data),\n//             compressionType,\n//         }));\n//     }\n// }\n// class DOMStandardFontDataFactory extends BaseStandardFontDataFactory {\n//     /**\n//      * @ignore\n//      */\n//     _fetchData(url) {\n//         return fetchData(url, /* type = */ \"arraybuffer\").then(\n//             data => new Uint8Array(data)\n//         );\n//     }\n// }\n// class DOMSVGFactory extends BaseSVGFactory {\n//     /**\n//      * @ignore\n//      */\n//     _createSVG(type) {\n//         return document.createElementNS(SVG_NS, type);\n//     }\n// }\n// /**\n//  * @typedef {Object} PageViewportParameters\n//  * @property {Array<number>} viewBox - The xMin, yMin, xMax and\n//  *   yMax coordinates.\n//  * @property {number} scale - The scale of the viewport.\n//  * @property {number} rotation - The rotation, in degrees, of the viewport.\n//  * @property {number} [offsetX] - The horizontal, i.e. x-axis, offset. The\n//  *   default value is `0`.\n//  * @property {number} [offsetY] - The vertical, i.e. y-axis, offset. The\n//  *   default value is `0`.\n//  * @property {boolean} [dontFlip] - If true, the y-axis will not be flipped.\n//  *   The default value is `false`.\n//  */\n// /**\n//  * @typedef {Object} PageViewportCloneParameters\n//  * @property {number} [scale] - The scale, overriding the one in the cloned\n//  *   viewport. The default value is `this.scale`.\n//  * @property {number} [rotation] - The rotation, in degrees, overriding the one\n//  *   in the cloned viewport. The default value is `this.rotation`.\n//  * @property {number} [offsetX] - The horizontal, i.e. x-axis, offset.\n//  *   The default value is `this.offsetX`.\n//  * @property {number} [offsetY] - The vertical, i.e. y-axis, offset.\n//  *   The default value is `this.offsetY`.\n//  * @property {boolean} [dontFlip] - If true, the x-axis will not be flipped.\n//  *   The default value is `false`.\n//  */\n// /**\n//  * PDF page viewport created based on scale, rotation and offset.\n//  */\n// class PageViewport {\n//     /**\n//      * @param {PageViewportParameters}\n//      */\n//     constructor({\n//         viewBox,\n//         scale,\n//         rotation,\n//         offsetX = 0,\n//         offsetY = 0,\n//         dontFlip = false,\n//     }) {\n//         this.viewBox = viewBox;\n//         this.scale = scale;\n//         this.rotation = rotation;\n//         this.offsetX = offsetX;\n//         this.offsetY = offsetY;\n//         // creating transform to convert pdf coordinate system to the normal\n//         // canvas like coordinates taking in account scale and rotation\n//         const centerX = (viewBox[2] + viewBox[0]) / 2;\n//         const centerY = (viewBox[3] + viewBox[1]) / 2;\n//         let rotateA, rotateB, rotateC, rotateD;\n//         // Normalize the rotation, by clamping it to the [0, 360) range.\n//         rotation %= 360;\n//         if (rotation < 0) {\n//             rotation += 360;\n//         }\n//         switch (rotation) {\n//             case 180:\n//                 rotateA = -1;\n//                 rotateB = 0;\n//                 rotateC = 0;\n//                 rotateD = 1;\n//                 break;\n//             case 90:\n//                 rotateA = 0;\n//                 rotateB = 1;\n//                 rotateC = 1;\n//                 rotateD = 0;\n//                 break;\n//             case 270:\n//                 rotateA = 0;\n//                 rotateB = -1;\n//                 rotateC = -1;\n//                 rotateD = 0;\n//                 break;\n//             case 0:\n//                 rotateA = 1;\n//                 rotateB = 0;\n//                 rotateC = 0;\n//                 rotateD = -1;\n//                 break;\n//             default:\n//                 throw new Error(\n//                     \"PageViewport: Invalid rotation, must be a multiple of 90 degrees.\"\n//                 );\n//         }\n//         if (dontFlip) {\n//             rotateC = -rotateC;\n//             rotateD = -rotateD;\n//         }\n//         let offsetCanvasX, offsetCanvasY;\n//         let width, height;\n//         if (rotateA === 0) {\n//             offsetCanvasX = Math.abs(centerY - viewBox[1]) * scale + offsetX;\n//             offsetCanvasY = Math.abs(centerX - viewBox[0]) * scale + offsetY;\n//             width = (viewBox[3] - viewBox[1]) * scale;\n//             height = (viewBox[2] - viewBox[0]) * scale;\n//         } else {\n//             offsetCanvasX = Math.abs(centerX - viewBox[0]) * scale + offsetX;\n//             offsetCanvasY = Math.abs(centerY - viewBox[1]) * scale + offsetY;\n//             width = (viewBox[2] - viewBox[0]) * scale;\n//             height = (viewBox[3] - viewBox[1]) * scale;\n//         }\n//         // creating transform for the following operations:\n//         // translate(-centerX, -centerY), rotate and flip vertically,\n//         // scale, and translate(offsetCanvasX, offsetCanvasY)\n//         this.transform = [\n//             rotateA * scale,\n//             rotateB * scale,\n//             rotateC * scale,\n//             rotateD * scale,\n//             offsetCanvasX - rotateA * scale * centerX - rotateC * scale * centerY,\n//             offsetCanvasY - rotateB * scale * centerX - rotateD * scale * centerY,\n//         ];\n//         this.width = width;\n//         this.height = height;\n//     }\n//     /**\n//      * The original, un-scaled, viewport dimensions.\n//      * @type {Object}\n//      */\n//     get rawDims() {\n//         const { viewBox } = this;\n//         return shadow(this, \"rawDims\", {\n//             pageWidth: viewBox[2] - viewBox[0],\n//             pageHeight: viewBox[3] - viewBox[1],\n//             pageX: viewBox[0],\n//             pageY: viewBox[1],\n//         });\n//     }\n//     /**\n//      * Clones viewport, with optional additional properties.\n//      * @param {PageViewportCloneParameters} [params]\n//      * @returns {PageViewport} Cloned viewport.\n//      */\n//     clone({\n//         scale = this.scale,\n//         rotation = this.rotation,\n//         offsetX = this.offsetX,\n//         offsetY = this.offsetY,\n//         dontFlip = false,\n//     } = {}) {\n//         return new PageViewport({\n//             viewBox: this.viewBox.slice(),\n//             scale,\n//             rotation,\n//             offsetX,\n//             offsetY,\n//             dontFlip,\n//         });\n//     }\n//     /**\n//      * Converts PDF point to the viewport coordinates. For examples, useful for\n//      * converting PDF location into canvas pixel coordinates.\n//      * @param {number} x - The x-coordinate.\n//      * @param {number} y - The y-coordinate.\n//      * @returns {Array} Array containing `x`- and `y`-coordinates of the\n//      *   point in the viewport coordinate space.\n//      * @see {@link convertToPdfPoint}\n//      * @see {@link convertToViewportRectangle}\n//      */\n//     convertToViewportPoint(x, y) {\n//         return Util.applyTransform([x, y], this.transform);\n//     }\n//     /**\n//      * Converts PDF rectangle to the viewport coordinates.\n//      * @param {Array} rect - The xMin, yMin, xMax and yMax coordinates.\n//      * @returns {Array} Array containing corresponding coordinates of the\n//      *   rectangle in the viewport coordinate space.\n//      * @see {@link convertToViewportPoint}\n//      */\n//     convertToViewportRectangle(rect) {\n//         const topLeft = Util.applyTransform([rect[0], rect[1]], this.transform);\n//         const bottomRight = Util.applyTransform([rect[2], rect[3]], this.transform);\n//         return [topLeft[0], topLeft[1], bottomRight[0], bottomRight[1]];\n//     }\n//     /**\n//      * Converts viewport coordinates to the PDF location. For examples, useful\n//      * for converting canvas pixel location into PDF one.\n//      * @param {number} x - The x-coordinate.\n//      * @param {number} y - The y-coordinate.\n//      * @returns {Array} Array containing `x`- and `y`-coordinates of the\n//      *   point in the PDF coordinate space.\n//      * @see {@link convertToViewportPoint}\n//      */\n//     convertToPdfPoint(x, y) {\n//         return Util.applyInverseTransform([x, y], this.transform);\n//     }\n// }\n// class RenderingCancelledException extends BaseException {\n//     constructor(msg, extraDelay = 0) {\n//         super(msg, \"RenderingCancelledException\");\n//         this.extraDelay = extraDelay;\n//     }\n// }\n// function isDataScheme(url) {\n//     const ii = url.length;\n//     let i = 0;\n//     while (i < ii && url[i].trim() === \"\") {\n//         i++;\n//     }\n//     return url.substring(i, i + 5).toLowerCase() === \"data:\";\n// }\n// function isPdfFile(filename) {\n//     return typeof filename === \"string\" && /\\.pdf$/i.test(filename);\n// }\n// /**\n//  * Gets the filename from a given URL.\n//  * @param {string} url\n//  * @returns {string}\n//  */\n// function getFilenameFromUrl(url) {\n//     [url] = url.split(/[#?]/, 1);\n//     return url.substring(url.lastIndexOf(\"/\") + 1);\n// }\n// /**\n//  * Returns the filename or guessed filename from the url (see issue 3455).\n//  * @param {string} url - The original PDF location.\n//  * @param {string} defaultFilename - The value returned if the filename is\n//  *   unknown, or the protocol is unsupported.\n//  * @returns {string} Guessed PDF filename.\n//  */\n// function getPdfFilenameFromUrl(url, defaultFilename = \"document.pdf\") {\n//     if (typeof url !== \"string\") {\n//         return defaultFilename;\n//     }\n//     if (isDataScheme(url)) {\n//         warn('getPdfFilenameFromUrl: ignore \"data:\"-URL for performance reasons.');\n//         return defaultFilename;\n//     }\n//     const reURI = /^(?:(?:[^:]+:)?\\/\\/[^/]+)?([^?#]*)(\\?[^#]*)?(#.*)?$/;\n//     //              SCHEME        HOST        1.PATH  2.QUERY   3.REF\n//     // Pattern to get last matching NAME.pdf\n//     const reFilename = /[^/?#=]+\\.pdf\\b(?!.*\\.pdf\\b)/i;\n//     const splitURI = reURI.exec(url);\n//     let suggestedFilename =\n//         reFilename.exec(splitURI[1]) ||\n//         reFilename.exec(splitURI[2]) ||\n//         reFilename.exec(splitURI[3]);\n//     if (suggestedFilename) {\n//         suggestedFilename = suggestedFilename[0];\n//         if (suggestedFilename.includes(\"%\")) {\n//             // URL-encoded %2Fpath%2Fto%2Ffile.pdf should be file.pdf\n//             try {\n//                 suggestedFilename = reFilename.exec(\n//                     decodeURIComponent(suggestedFilename)\n//                 )[0];\n//             } catch {\n//                 // Possible (extremely rare) errors:\n//                 // URIError \"Malformed URI\", e.g. for \"%AA.pdf\"\n//                 // TypeError \"null has no properties\", e.g. for \"%2F.pdf\"\n//             }\n//         }\n//     }\n//     return suggestedFilename || defaultFilename;\n// }\n// class StatTimer {\n//     started = Object.create(null);\n//     times = [];\n//     time(name) {\n//         if (name in this.started) {\n//             warn(`Timer is already running for ${name}`);\n//         }\n//         this.started[name] = Date.now();\n//     }\n//     timeEnd(name) {\n//         if (!(name in this.started)) {\n//             warn(`Timer has not been started for ${name}`);\n//         }\n//         this.times.push({\n//             name,\n//             start: this.started[name],\n//             end: Date.now(),\n//         });\n//         // Remove timer from started so it can be called again.\n//         delete this.started[name];\n//     }\n//     toString() {\n//         // Find the longest name for padding purposes.\n//         const outBuf = [];\n//         let longest = 0;\n//         for (const { name } of this.times) {\n//             longest = Math.max(name.length, longest);\n//         }\n//         for (const { name, start, end } of this.times) {\n//             outBuf.push(`${name.padEnd(longest)} ${end - start}ms\\n`);\n//         }\n//         return outBuf.join(\"\");\n//     }\n// }\n// function isValidFetchUrl(url, baseUrl) {\n//     if (typeof PDFJSDev !== \"undefined\" && PDFJSDev.test(\"MOZCENTRAL\")) {\n//         throw new Error(\"Not implemented: isValidFetchUrl\");\n//     }\n//     try {\n//         const { protocol } = baseUrl ? new URL(url, baseUrl) : new URL(url);\n//         // The Fetch API only supports the http/https protocols, and not file/ftp.\n//         return protocol === \"http:\" || protocol === \"https:\";\n//     } catch {\n//         return false; // `new URL()` will throw on incorrect data.\n//     }\n// }\n/**\n * Event handler to suppress context menu.\n */\nexport function noContextMenu(e) {\n  e.preventDefault();\n}\n// // Deprecated API function -- display regardless of the `verbosity` setting.\n// function deprecated(details) {\n//     console.log(\"Deprecated API usage: \" + details);\n// }\n// let pdfDateStringRegex;\n// class PDFDateString {\n//     /**\n//      * Convert a PDF date string to a JavaScript `Date` object.\n//      *\n//      * The PDF date string format is described in section 7.9.4 of the official\n//      * PDF 32000-1:2008 specification. However, in the PDF 1.7 reference (sixth\n//      * edition) Adobe describes the same format including a trailing apostrophe.\n//      * This syntax in incorrect, but Adobe Acrobat creates PDF files that contain\n//      * them. We ignore all apostrophes as they are not necessary for date parsing.\n//      *\n//      * Moreover, Adobe Acrobat doesn't handle changing the date to universal time\n//      * and doesn't use the user's time zone (effectively ignoring the HH' and mm'\n//      * parts of the date string).\n//      *\n//      * @param {string} input\n//      * @returns {Date|null}\n//      */\n//     static toDateObject(input) {\n//         if (!input || typeof input !== \"string\") {\n//             return null;\n//         }\n//         // Lazily initialize the regular expression.\n//         pdfDateStringRegex ||= new RegExp(\n//             \"^D:\" + // Prefix (required)\n//             \"(\\\\d{4})\" + // Year (required)\n//             \"(\\\\d{2})?\" + // Month (optional)\n//             \"(\\\\d{2})?\" + // Day (optional)\n//             \"(\\\\d{2})?\" + // Hour (optional)\n//             \"(\\\\d{2})?\" + // Minute (optional)\n//             \"(\\\\d{2})?\" + // Second (optional)\n//             \"([Z|+|-])?\" + // Universal time relation (optional)\n//             \"(\\\\d{2})?\" + // Offset hour (optional)\n//             \"'?\" + // Splitting apostrophe (optional)\n//             \"(\\\\d{2})?\" + // Offset minute (optional)\n//             \"'?\" // Trailing apostrophe (optional)\n//         );\n//         // Optional fields that don't satisfy the requirements from the regular\n//         // expression (such as incorrect digit counts or numbers that are out of\n//         // range) will fall back the defaults from the specification.\n//         const matches = pdfDateStringRegex.exec(input);\n//         if (!matches) {\n//             return null;\n//         }\n//         // JavaScript's `Date` object expects the month to be between 0 and 11\n//         // instead of 1 and 12, so we have to correct for that.\n//         const year = parseInt(matches[1], 10);\n//         let month = parseInt(matches[2], 10);\n//         month = month >= 1 && month <= 12 ? month - 1 : 0;\n//         let day = parseInt(matches[3], 10);\n//         day = day >= 1 && day <= 31 ? day : 1;\n//         let hour = parseInt(matches[4], 10);\n//         hour = hour >= 0 && hour <= 23 ? hour : 0;\n//         let minute = parseInt(matches[5], 10);\n//         minute = minute >= 0 && minute <= 59 ? minute : 0;\n//         let second = parseInt(matches[6], 10);\n//         second = second >= 0 && second <= 59 ? second : 0;\n//         const universalTimeRelation = matches[7] || \"Z\";\n//         let offsetHour = parseInt(matches[8], 10);\n//         offsetHour = offsetHour >= 0 && offsetHour <= 23 ? offsetHour : 0;\n//         let offsetMinute = parseInt(matches[9], 10) || 0;\n//         offsetMinute = offsetMinute >= 0 && offsetMinute <= 59 ? offsetMinute : 0;\n//         // Universal time relation 'Z' means that the local time is equal to the\n//         // universal time, whereas the relations '+'/'-' indicate that the local\n//         // time is later respectively earlier than the universal time. Every date\n//         // is normalized to universal time.\n//         if (universalTimeRelation === \"-\") {\n//             hour += offsetHour;\n//             minute += offsetMinute;\n//         } else if (universalTimeRelation === \"+\") {\n//             hour -= offsetHour;\n//             minute -= offsetMinute;\n//         }\n//         return new Date(Date.UTC(year, month, day, hour, minute, second));\n//     }\n// }\n// /**\n//  * NOTE: This is (mostly) intended to support printing of XFA forms.\n//  */\n// function getXfaPageViewport(xfaPage, { scale = 1, rotation = 0 }) {\n//     const { width, height } = xfaPage.attributes.style;\n//     const viewBox = [0, 0, parseInt(width), parseInt(height)];\n//     return new PageViewport({\n//         viewBox,\n//         scale,\n//         rotation,\n//     });\n// }\nexport function getRGB(color) {\n  if (color.startsWith(\"#\")) {\n    const colorRGB = parseInt(color.slice(1), 16);\n    return [(colorRGB & 0xff0000) >> 16, (colorRGB & 0x00ff00) >> 8, colorRGB & 0x0000ff];\n  }\n  if (color.startsWith(\"rgb(\")) {\n    // getComputedStyle(...).color returns a `rgb(R, G, B)` color.\n    return color.slice(/* \"rgb(\".length */4, -1) // Strip out \"rgb(\" and \")\".\n    .split(\",\").map(x => parseInt(x, 10));\n  }\n  if (color.startsWith(\"rgba(\")) {\n    return color.slice(/* \"rgba(\".length */5, -1) // Strip out \"rgba(\" and \")\".\n    .split(\",\").map(x => parseInt(x, 10)).slice(0, 3);\n  }\n  // warn(`Not a valid color format: \"${color}\"`);\n  return [0, 0, 0];\n}\nexport function getColorValues(colors) {\n  const span = document.createElement(\"span\");\n  span.style.visibility = \"hidden\";\n  document.body.append(span);\n  for (const name of colors.keys()) {\n    span.style.color = name;\n    const computedColor = window.getComputedStyle(span).color;\n    colors.set(name, getRGB(computedColor));\n  }\n  span.remove();\n}\n// function getCurrentTransform(ctx) {\n//     const { a, b, c, d, e, f } = ctx.getTransform();\n//     return [a, b, c, d, e, f];\n// }\n// function getCurrentTransformInverse(ctx) {\n//     const { a, b, c, d, e, f } = ctx.getTransform().invertSelf();\n//     return [a, b, c, d, e, f];\n// }\n// /**\n//  * @param {HTMLDivElement} div\n//  * @param {PageViewport} viewport\n//  * @param {boolean} mustFlip\n//  * @param {boolean} mustRotate\n//  */\n// function setLayerDimensions(\n//     div,\n//     viewport,\n//     mustFlip = false,\n//     mustRotate = true\n// ) {\n//     if (viewport instanceof PageViewport) {\n//         const { pageWidth, pageHeight } = viewport.rawDims;\n//         const { style } = div;\n//         const useRound = FeatureTest.isCSSRoundSupported;\n//         const w = `var(--scale-factor) * ${pageWidth}px`,\n//             h = `var(--scale-factor) * ${pageHeight}px`;\n//         const widthStr = useRound ? `round(${w}, 1px)` : `calc(${w})`,\n//             heightStr = useRound ? `round(${h}, 1px)` : `calc(${h})`;\n//         if (!mustFlip || viewport.rotation % 180 === 0) {\n//             style.width = widthStr;\n//             style.height = heightStr;\n//         } else {\n//             style.width = heightStr;\n//             style.height = widthStr;\n//         }\n//     }\n//     if (mustRotate) {\n//         div.setAttribute(\"data-main-rotation\", viewport.rotation);\n//     }\n// }\n// export {\n//     // deprecated,\n//     // DOMCanvasFactory,\n//     // DOMCMapReaderFactory,\n//     // DOMFilterFactory,\n//     // DOMStandardFontDataFactory,\n//     // DOMSVGFactory,\n//     // fetchData,\n//     // getColorValues,\n//     // getCurrentTransform,\n//     // getCurrentTransformInverse,\n//     // getFilenameFromUrl,\n//     // getPdfFilenameFromUrl,\n//     // getRGB,\n//     // getXfaPageViewport,\n//     // isDataScheme,\n//     // isPdfFile,\n//     // isValidFetchUrl,\n//     // noContextMenu,\n//     // PageViewport,\n//     // PDFDateString,\n//     // PixelsPerInch,\n//     // RenderingCancelledException,\n//     setLayerDimensions,\n//     // StatTimer,\n// };", "map": {"version": 3, "names": ["noContextMenu", "e", "preventDefault", "getRGB", "color", "startsWith", "colorRGB", "parseInt", "slice", "split", "map", "x", "getColorValues", "colors", "span", "document", "createElement", "style", "visibility", "body", "append", "name", "keys", "computedColor", "window", "getComputedStyle", "set", "remove"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-pdfviewer-common/dist/es/annotations/shared/display_utils.js"], "sourcesContent": ["/* Copyright 2015 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// import {\n//     BaseCanvasFactory,\n//     BaseCMapReaderFactory,\n//     BaseFilterFactory,\n//     BaseStandardFontDataFactory,\n//     BaseSVGFactory,\n// } from \"./base_factory.js\";\n// import {\n//     BaseException,\n//     FeatureTest,\n//     shadow,\n//     stringToBytes,\n//     Util,\n//     warn,\n// } from \"../shared/util.js\";\n// const SVG_NS = \"http://www.w3.org/2000/svg\";\n// class PixelsPerInch {\n//     static CSS = 96.0;\n//     static PDF = 72.0;\n//     static PDF_TO_CSS_UNITS = this.CSS / this.PDF;\n// }\n// /**\n//  * FilterFactory aims to create some SVG filters we can use when drawing an\n//  * image (or whatever) on a canvas.\n//  * Filters aren't applied with ctx.putImageData because it just overwrites the\n//  * underlying pixels.\n//  * With these filters, it's possible for example to apply some transfer maps on\n//  * an image without the need to apply them on the pixel arrays: the renderer\n//  * does the magic for us.\n//  */\n// class DOMFilterFactory extends BaseFilterFactory {\n//     #baseUrl;\n//     #_cache;\n//     #_defs;\n//     #docId;\n//     #document;\n//     #_hcmCache;\n//     #id = 0;\n//     constructor({ docId, ownerDocument = globalThis.document } = {}) {\n//         super();\n//         this.#docId = docId;\n//         this.#document = ownerDocument;\n//     }\n//     get #cache() {\n//         return (this.#_cache ||= new Map());\n//     }\n//     get #hcmCache() {\n//         return (this.#_hcmCache ||= new Map());\n//     }\n//     get #defs() {\n//         if (!this.#_defs) {\n//             const div = this.#document.createElement(\"div\");\n//             const { style } = div;\n//             style.visibility = \"hidden\";\n//             style.contain = \"strict\";\n//             style.width = style.height = 0;\n//             style.position = \"absolute\";\n//             style.top = style.left = 0;\n//             style.zIndex = -1;\n//             const svg = this.#document.createElementNS(SVG_NS, \"svg\");\n//             svg.setAttribute(\"width\", 0);\n//             svg.setAttribute(\"height\", 0);\n//             this.#_defs = this.#document.createElementNS(SVG_NS, \"defs\");\n//             div.append(svg);\n//             svg.append(this.#_defs);\n//             this.#document.body.append(div);\n//         }\n//         return this.#_defs;\n//     }\n//     #createTables(maps) {\n//         if (maps.length === 1) {\n//             const mapR = maps[0];\n//             const buffer = new Array(256);\n//             for (let i = 0; i < 256; i++) {\n//                 buffer[i] = mapR[i] / 255;\n//             }\n//             const table = buffer.join(\",\");\n//             return [table, table, table];\n//         }\n//         const [mapR, mapG, mapB] = maps;\n//         const bufferR = new Array(256);\n//         const bufferG = new Array(256);\n//         const bufferB = new Array(256);\n//         for (let i = 0; i < 256; i++) {\n//             bufferR[i] = mapR[i] / 255;\n//             bufferG[i] = mapG[i] / 255;\n//             bufferB[i] = mapB[i] / 255;\n//         }\n//         return [bufferR.join(\",\"), bufferG.join(\",\"), bufferB.join(\",\")];\n//     }\n//     #createUrl(id) {\n//         if (this.#baseUrl === undefined) {\n//             // Unless a `<base>`-element is present a relative URL should work.\n//             this.#baseUrl = \"\";\n//             const url = this.#document.URL;\n//             if (url !== this.#document.baseURI) {\n//                 if (isDataScheme(url)) {\n//                     warn('#createUrl: ignore \"data:\"-URL for performance reasons.');\n//                 } else {\n//                     this.#baseUrl = url.split(\"#\", 1)[0];\n//                 }\n//             }\n//         }\n//         return `url(${this.#baseUrl}#${id})`;\n//     }\n//     addFilter(maps) {\n//         if (!maps) {\n//             return \"none\";\n//         }\n//         // When a page is zoomed the page is re-drawn but the maps are likely\n//         // the same.\n//         let value = this.#cache.get(maps);\n//         if (value) {\n//             return value;\n//         }\n//         const [tableR, tableG, tableB] = this.#createTables(maps);\n//         const key = maps.length === 1 ? tableR : `${tableR}${tableG}${tableB}`;\n//         value = this.#cache.get(key);\n//         if (value) {\n//             this.#cache.set(maps, value);\n//             return value;\n//         }\n//         // We create a SVG filter: feComponentTransferElement\n//         //  https://www.w3.org/TR/SVG11/filters.html#feComponentTransferElement\n//         const id = `g_${this.#docId}_transfer_map_${this.#id++}`;\n//         const url = this.#createUrl(id);\n//         this.#cache.set(maps, url);\n//         this.#cache.set(key, url);\n//         const filter = this.#createFilter(id);\n//         this.#addTransferMapConversion(tableR, tableG, tableB, filter);\n//         return url;\n//     }\n//     addHCMFilter(fgColor, bgColor) {\n//         const key = `${fgColor}-${bgColor}`;\n//         const filterName = \"base\";\n//         let info = this.#hcmCache.get(filterName);\n//         if (info?.key === key) {\n//             return info.url;\n//         }\n//         if (info) {\n//             info.filter?.remove();\n//             info.key = key;\n//             info.url = \"none\";\n//             info.filter = null;\n//         } else {\n//             info = {\n//                 key,\n//                 url: \"none\",\n//                 filter: null,\n//             };\n//             this.#hcmCache.set(filterName, info);\n//         }\n//         if (!fgColor || !bgColor) {\n//             return info.url;\n//         }\n//         const fgRGB = this.#getRGB(fgColor);\n//         fgColor = Util.makeHexColor(...fgRGB);\n//         const bgRGB = this.#getRGB(bgColor);\n//         bgColor = Util.makeHexColor(...bgRGB);\n//         this.#defs.style.color = \"\";\n//         if (\n//             (fgColor === \"#000000\" && bgColor === \"#ffffff\") ||\n//             fgColor === bgColor\n//         ) {\n//             return info.url;\n//         }\n//         // https://developer.mozilla.org/en-US/docs/Web/Accessibility/Understanding_Colors_and_Luminance\n//         //\n//         // Relative luminance:\n//         // https://www.w3.org/TR/WCAG20/#relativeluminancedef\n//         //\n//         // We compute the rounded luminance of the default background color.\n//         // Then for every color in the pdf, if its rounded luminance is the\n//         // same as the background one then it's replaced by the new\n//         // background color else by the foreground one.\n//         const map = new Array(256);\n//         for (let i = 0; i <= 255; i++) {\n//             const x = i / 255;\n//             map[i] = x <= 0.03928 ? x / 12.92 : ((x + 0.055) / 1.055) ** 2.4;\n//         }\n//         const table = map.join(\",\");\n//         const id = `g_${this.#docId}_hcm_filter`;\n//         const filter = (info.filter = this.#createFilter(id));\n//         this.#addTransferMapConversion(table, table, table, filter);\n//         this.#addGrayConversion(filter);\n//         const getSteps = (c, n) => {\n//             const start = fgRGB[c] / 255;\n//             const end = bgRGB[c] / 255;\n//             const arr = new Array(n + 1);\n//             for (let i = 0; i <= n; i++) {\n//                 arr[i] = start + (i / n) * (end - start);\n//             }\n//             return arr.join(\",\");\n//         };\n//         this.#addTransferMapConversion(\n//             getSteps(0, 5),\n//             getSteps(1, 5),\n//             getSteps(2, 5),\n//             filter\n//         );\n//         info.url = this.#createUrl(id);\n//         return info.url;\n//     }\n//     addAlphaFilter(map) {\n//         // When a page is zoomed the page is re-drawn but the maps are likely\n//         // the same.\n//         let value = this.#cache.get(map);\n//         if (value) {\n//             return value;\n//         }\n//         const [tableA] = this.#createTables([map]);\n//         const key = `alpha_${tableA}`;\n//         value = this.#cache.get(key);\n//         if (value) {\n//             this.#cache.set(map, value);\n//             return value;\n//         }\n//         const id = `g_${this.#docId}_alpha_map_${this.#id++}`;\n//         const url = this.#createUrl(id);\n//         this.#cache.set(map, url);\n//         this.#cache.set(key, url);\n//         const filter = this.#createFilter(id);\n//         this.#addTransferMapAlphaConversion(tableA, filter);\n//         return url;\n//     }\n//     addLuminosityFilter(map) {\n//         // When a page is zoomed the page is re-drawn but the maps are likely\n//         // the same.\n//         let value = this.#cache.get(map || \"luminosity\");\n//         if (value) {\n//             return value;\n//         }\n//         let tableA, key;\n//         if (map) {\n//             [tableA] = this.#createTables([map]);\n//             key = `luminosity_${tableA}`;\n//         } else {\n//             key = \"luminosity\";\n//         }\n//         value = this.#cache.get(key);\n//         if (value) {\n//             this.#cache.set(map, value);\n//             return value;\n//         }\n//         const id = `g_${this.#docId}_luminosity_map_${this.#id++}`;\n//         const url = this.#createUrl(id);\n//         this.#cache.set(map, url);\n//         this.#cache.set(key, url);\n//         const filter = this.#createFilter(id);\n//         this.#addLuminosityConversion(filter);\n//         if (map) {\n//             this.#addTransferMapAlphaConversion(tableA, filter);\n//         }\n//         return url;\n//     }\n//     addHighlightHCMFilter(filterName, fgColor, bgColor, newFgColor, newBgColor) {\n//         const key = `${fgColor}-${bgColor}-${newFgColor}-${newBgColor}`;\n//         let info = this.#hcmCache.get(filterName);\n//         if (info?.key === key) {\n//             return info.url;\n//         }\n//         if (info) {\n//             info.filter?.remove();\n//             info.key = key;\n//             info.url = \"none\";\n//             info.filter = null;\n//         } else {\n//             info = {\n//                 key,\n//                 url: \"none\",\n//                 filter: null,\n//             };\n//             this.#hcmCache.set(filterName, info);\n//         }\n//         if (!fgColor || !bgColor) {\n//             return info.url;\n//         }\n//         const [fgRGB, bgRGB] = [fgColor, bgColor].map(this.#getRGB.bind(this));\n//         let fgGray = Math.round(\n//             0.2126 * fgRGB[0] + 0.7152 * fgRGB[1] + 0.0722 * fgRGB[2]\n//         );\n//         let bgGray = Math.round(\n//             0.2126 * bgRGB[0] + 0.7152 * bgRGB[1] + 0.0722 * bgRGB[2]\n//         );\n//         let [newFgRGB, newBgRGB] = [newFgColor, newBgColor].map(\n//             this.#getRGB.bind(this)\n//         );\n//         if (bgGray < fgGray) {\n//             [fgGray, bgGray, newFgRGB, newBgRGB] = [\n//                 bgGray,\n//                 fgGray,\n//                 newBgRGB,\n//                 newFgRGB,\n//             ];\n//         }\n//         this.#defs.style.color = \"\";\n//         // Now we can create the filters to highlight some canvas parts.\n//         // The colors in the pdf will almost be Canvas and CanvasText, hence we\n//         // want to filter them to finally get Highlight and HighlightText.\n//         // Since we're in HCM the background color and the foreground color should\n//         // be really different when converted to grayscale (if they're not then it\n//         // means that we've a poor contrast). Once the canvas colors are converted\n//         // to grayscale we can easily map them on their new colors.\n//         // The grayscale step is important because if we've something like:\n//         //   fgColor = #FF....\n//         //   bgColor = #FF....\n//         //   then we are enable to map the red component on the new red components\n//         //   which can be different.\n//         const getSteps = (fg, bg, n) => {\n//             const arr = new Array(256);\n//             const step = (bgGray - fgGray) / n;\n//             const newStart = fg / 255;\n//             const newStep = (bg - fg) / (255 * n);\n//             let prev = 0;\n//             for (let i = 0; i <= n; i++) {\n//                 const k = Math.round(fgGray + i * step);\n//                 const value = newStart + i * newStep;\n//                 for (let j = prev; j <= k; j++) {\n//                     arr[j] = value;\n//                 }\n//                 prev = k + 1;\n//             }\n//             for (let i = prev; i < 256; i++) {\n//                 arr[i] = arr[prev - 1];\n//             }\n//             return arr.join(\",\");\n//         };\n//         const id = `g_${this.#docId}_hcm_${filterName}_filter`;\n//         const filter = (info.filter = this.#createFilter(id));\n//         this.#addGrayConversion(filter);\n//         this.#addTransferMapConversion(\n//             getSteps(newFgRGB[0], newBgRGB[0], 5),\n//             getSteps(newFgRGB[1], newBgRGB[1], 5),\n//             getSteps(newFgRGB[2], newBgRGB[2], 5),\n//             filter\n//         );\n//         info.url = this.#createUrl(id);\n//         return info.url;\n//     }\n//     destroy(keepHCM = false) {\n//         if (keepHCM && this.#hcmCache.size !== 0) {\n//             return;\n//         }\n//         if (this.#_defs) {\n//             this.#_defs.parentNode.parentNode.remove();\n//             this.#_defs = null;\n//         }\n//         if (this.#_cache) {\n//             this.#_cache.clear();\n//             this.#_cache = null;\n//         }\n//         this.#id = 0;\n//     }\n//     #addLuminosityConversion(filter) {\n//         const feColorMatrix = this.#document.createElementNS(\n//             SVG_NS,\n//             \"feColorMatrix\"\n//         );\n//         feColorMatrix.setAttribute(\"type\", \"matrix\");\n//         feColorMatrix.setAttribute(\n//             \"values\",\n//             \"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0.59 0.11 0 0\"\n//         );\n//         filter.append(feColorMatrix);\n//     }\n//     #addGrayConversion(filter) {\n//         const feColorMatrix = this.#document.createElementNS(\n//             SVG_NS,\n//             \"feColorMatrix\"\n//         );\n//         feColorMatrix.setAttribute(\"type\", \"matrix\");\n//         feColorMatrix.setAttribute(\n//             \"values\",\n//             \"0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0 0 0 1 0\"\n//         );\n//         filter.append(feColorMatrix);\n//     }\n//     #createFilter(id) {\n//         const filter = this.#document.createElementNS(SVG_NS, \"filter\");\n//         filter.setAttribute(\"color-interpolation-filters\", \"sRGB\");\n//         filter.setAttribute(\"id\", id);\n//         this.#defs.append(filter);\n//         return filter;\n//     }\n//     #appendFeFunc(feComponentTransfer, func, table) {\n//         const feFunc = this.#document.createElementNS(SVG_NS, func);\n//         feFunc.setAttribute(\"type\", \"discrete\");\n//         feFunc.setAttribute(\"tableValues\", table);\n//         feComponentTransfer.append(feFunc);\n//     }\n//     #addTransferMapConversion(rTable, gTable, bTable, filter) {\n//         const feComponentTransfer = this.#document.createElementNS(\n//             SVG_NS,\n//             \"feComponentTransfer\"\n//         );\n//         filter.append(feComponentTransfer);\n//         this.#appendFeFunc(feComponentTransfer, \"feFuncR\", rTable);\n//         this.#appendFeFunc(feComponentTransfer, \"feFuncG\", gTable);\n//         this.#appendFeFunc(feComponentTransfer, \"feFuncB\", bTable);\n//     }\n//     #addTransferMapAlphaConversion(aTable, filter) {\n//         const feComponentTransfer = this.#document.createElementNS(\n//             SVG_NS,\n//             \"feComponentTransfer\"\n//         );\n//         filter.append(feComponentTransfer);\n//         this.#appendFeFunc(feComponentTransfer, \"feFuncA\", aTable);\n//     }\n//     #getRGB(color) {\n//         this.#defs.style.color = color;\n//         return getRGB(getComputedStyle(this.#defs).getPropertyValue(\"color\"));\n//     }\n// }\n// class DOMCanvasFactory extends BaseCanvasFactory {\n//     constructor({ ownerDocument = globalThis.document, enableHWA = false } = {}) {\n//         super({ enableHWA });\n//         this._document = ownerDocument;\n//     }\n//     /**\n//      * @ignore\n//      */\n//     _createCanvas(width, height) {\n//         const canvas = this._document.createElement(\"canvas\");\n//         canvas.width = width;\n//         canvas.height = height;\n//         return canvas;\n//     }\n// }\n// async function fetchData(url, type = \"text\") {\n//     if (\n//         (typeof PDFJSDev !== \"undefined\" && PDFJSDev.test(\"MOZCENTRAL\")) ||\n//         isValidFetchUrl(url, document.baseURI)\n//     ) {\n//         const response = await fetch(url);\n//         if (!response.ok) {\n//             throw new Error(response.statusText);\n//         }\n//         switch (type) {\n//             case \"arraybuffer\":\n//                 return response.arrayBuffer();\n//             case \"blob\":\n//                 return response.blob();\n//             case \"json\":\n//                 return response.json();\n//         }\n//         return response.text();\n//     }\n//     // The Fetch API is not supported.\n//     return new Promise((resolve, reject) => {\n//         const request = new XMLHttpRequest();\n//         request.open(\"GET\", url, /* async = */ true);\n//         request.responseType = type;\n//         request.onreadystatechange = () => {\n//             if (request.readyState !== XMLHttpRequest.DONE) {\n//                 return;\n//             }\n//             if (request.status === 200 || request.status === 0) {\n//                 switch (type) {\n//                     case \"arraybuffer\":\n//                     case \"blob\":\n//                     case \"json\":\n//                         resolve(request.response);\n//                         return;\n//                 }\n//                 resolve(request.responseText);\n//                 return;\n//             }\n//             reject(new Error(request.statusText));\n//         };\n//         request.send(null);\n//     });\n// }\n// class DOMCMapReaderFactory extends BaseCMapReaderFactory {\n//     /**\n//      * @ignore\n//      */\n//     _fetchData(url, compressionType) {\n//         return fetchData(\n//             url,\n//         /* type = */ this.isCompressed ? \"arraybuffer\" : \"text\"\n//         ).then(data => ({\n//             cMapData:\n//                 data instanceof ArrayBuffer\n//                     ? new Uint8Array(data)\n//                     : stringToBytes(data),\n//             compressionType,\n//         }));\n//     }\n// }\n// class DOMStandardFontDataFactory extends BaseStandardFontDataFactory {\n//     /**\n//      * @ignore\n//      */\n//     _fetchData(url) {\n//         return fetchData(url, /* type = */ \"arraybuffer\").then(\n//             data => new Uint8Array(data)\n//         );\n//     }\n// }\n// class DOMSVGFactory extends BaseSVGFactory {\n//     /**\n//      * @ignore\n//      */\n//     _createSVG(type) {\n//         return document.createElementNS(SVG_NS, type);\n//     }\n// }\n// /**\n//  * @typedef {Object} PageViewportParameters\n//  * @property {Array<number>} viewBox - The xMin, yMin, xMax and\n//  *   yMax coordinates.\n//  * @property {number} scale - The scale of the viewport.\n//  * @property {number} rotation - The rotation, in degrees, of the viewport.\n//  * @property {number} [offsetX] - The horizontal, i.e. x-axis, offset. The\n//  *   default value is `0`.\n//  * @property {number} [offsetY] - The vertical, i.e. y-axis, offset. The\n//  *   default value is `0`.\n//  * @property {boolean} [dontFlip] - If true, the y-axis will not be flipped.\n//  *   The default value is `false`.\n//  */\n// /**\n//  * @typedef {Object} PageViewportCloneParameters\n//  * @property {number} [scale] - The scale, overriding the one in the cloned\n//  *   viewport. The default value is `this.scale`.\n//  * @property {number} [rotation] - The rotation, in degrees, overriding the one\n//  *   in the cloned viewport. The default value is `this.rotation`.\n//  * @property {number} [offsetX] - The horizontal, i.e. x-axis, offset.\n//  *   The default value is `this.offsetX`.\n//  * @property {number} [offsetY] - The vertical, i.e. y-axis, offset.\n//  *   The default value is `this.offsetY`.\n//  * @property {boolean} [dontFlip] - If true, the x-axis will not be flipped.\n//  *   The default value is `false`.\n//  */\n// /**\n//  * PDF page viewport created based on scale, rotation and offset.\n//  */\n// class PageViewport {\n//     /**\n//      * @param {PageViewportParameters}\n//      */\n//     constructor({\n//         viewBox,\n//         scale,\n//         rotation,\n//         offsetX = 0,\n//         offsetY = 0,\n//         dontFlip = false,\n//     }) {\n//         this.viewBox = viewBox;\n//         this.scale = scale;\n//         this.rotation = rotation;\n//         this.offsetX = offsetX;\n//         this.offsetY = offsetY;\n//         // creating transform to convert pdf coordinate system to the normal\n//         // canvas like coordinates taking in account scale and rotation\n//         const centerX = (viewBox[2] + viewBox[0]) / 2;\n//         const centerY = (viewBox[3] + viewBox[1]) / 2;\n//         let rotateA, rotateB, rotateC, rotateD;\n//         // Normalize the rotation, by clamping it to the [0, 360) range.\n//         rotation %= 360;\n//         if (rotation < 0) {\n//             rotation += 360;\n//         }\n//         switch (rotation) {\n//             case 180:\n//                 rotateA = -1;\n//                 rotateB = 0;\n//                 rotateC = 0;\n//                 rotateD = 1;\n//                 break;\n//             case 90:\n//                 rotateA = 0;\n//                 rotateB = 1;\n//                 rotateC = 1;\n//                 rotateD = 0;\n//                 break;\n//             case 270:\n//                 rotateA = 0;\n//                 rotateB = -1;\n//                 rotateC = -1;\n//                 rotateD = 0;\n//                 break;\n//             case 0:\n//                 rotateA = 1;\n//                 rotateB = 0;\n//                 rotateC = 0;\n//                 rotateD = -1;\n//                 break;\n//             default:\n//                 throw new Error(\n//                     \"PageViewport: Invalid rotation, must be a multiple of 90 degrees.\"\n//                 );\n//         }\n//         if (dontFlip) {\n//             rotateC = -rotateC;\n//             rotateD = -rotateD;\n//         }\n//         let offsetCanvasX, offsetCanvasY;\n//         let width, height;\n//         if (rotateA === 0) {\n//             offsetCanvasX = Math.abs(centerY - viewBox[1]) * scale + offsetX;\n//             offsetCanvasY = Math.abs(centerX - viewBox[0]) * scale + offsetY;\n//             width = (viewBox[3] - viewBox[1]) * scale;\n//             height = (viewBox[2] - viewBox[0]) * scale;\n//         } else {\n//             offsetCanvasX = Math.abs(centerX - viewBox[0]) * scale + offsetX;\n//             offsetCanvasY = Math.abs(centerY - viewBox[1]) * scale + offsetY;\n//             width = (viewBox[2] - viewBox[0]) * scale;\n//             height = (viewBox[3] - viewBox[1]) * scale;\n//         }\n//         // creating transform for the following operations:\n//         // translate(-centerX, -centerY), rotate and flip vertically,\n//         // scale, and translate(offsetCanvasX, offsetCanvasY)\n//         this.transform = [\n//             rotateA * scale,\n//             rotateB * scale,\n//             rotateC * scale,\n//             rotateD * scale,\n//             offsetCanvasX - rotateA * scale * centerX - rotateC * scale * centerY,\n//             offsetCanvasY - rotateB * scale * centerX - rotateD * scale * centerY,\n//         ];\n//         this.width = width;\n//         this.height = height;\n//     }\n//     /**\n//      * The original, un-scaled, viewport dimensions.\n//      * @type {Object}\n//      */\n//     get rawDims() {\n//         const { viewBox } = this;\n//         return shadow(this, \"rawDims\", {\n//             pageWidth: viewBox[2] - viewBox[0],\n//             pageHeight: viewBox[3] - viewBox[1],\n//             pageX: viewBox[0],\n//             pageY: viewBox[1],\n//         });\n//     }\n//     /**\n//      * Clones viewport, with optional additional properties.\n//      * @param {PageViewportCloneParameters} [params]\n//      * @returns {PageViewport} Cloned viewport.\n//      */\n//     clone({\n//         scale = this.scale,\n//         rotation = this.rotation,\n//         offsetX = this.offsetX,\n//         offsetY = this.offsetY,\n//         dontFlip = false,\n//     } = {}) {\n//         return new PageViewport({\n//             viewBox: this.viewBox.slice(),\n//             scale,\n//             rotation,\n//             offsetX,\n//             offsetY,\n//             dontFlip,\n//         });\n//     }\n//     /**\n//      * Converts PDF point to the viewport coordinates. For examples, useful for\n//      * converting PDF location into canvas pixel coordinates.\n//      * @param {number} x - The x-coordinate.\n//      * @param {number} y - The y-coordinate.\n//      * @returns {Array} Array containing `x`- and `y`-coordinates of the\n//      *   point in the viewport coordinate space.\n//      * @see {@link convertToPdfPoint}\n//      * @see {@link convertToViewportRectangle}\n//      */\n//     convertToViewportPoint(x, y) {\n//         return Util.applyTransform([x, y], this.transform);\n//     }\n//     /**\n//      * Converts PDF rectangle to the viewport coordinates.\n//      * @param {Array} rect - The xMin, yMin, xMax and yMax coordinates.\n//      * @returns {Array} Array containing corresponding coordinates of the\n//      *   rectangle in the viewport coordinate space.\n//      * @see {@link convertToViewportPoint}\n//      */\n//     convertToViewportRectangle(rect) {\n//         const topLeft = Util.applyTransform([rect[0], rect[1]], this.transform);\n//         const bottomRight = Util.applyTransform([rect[2], rect[3]], this.transform);\n//         return [topLeft[0], topLeft[1], bottomRight[0], bottomRight[1]];\n//     }\n//     /**\n//      * Converts viewport coordinates to the PDF location. For examples, useful\n//      * for converting canvas pixel location into PDF one.\n//      * @param {number} x - The x-coordinate.\n//      * @param {number} y - The y-coordinate.\n//      * @returns {Array} Array containing `x`- and `y`-coordinates of the\n//      *   point in the PDF coordinate space.\n//      * @see {@link convertToViewportPoint}\n//      */\n//     convertToPdfPoint(x, y) {\n//         return Util.applyInverseTransform([x, y], this.transform);\n//     }\n// }\n// class RenderingCancelledException extends BaseException {\n//     constructor(msg, extraDelay = 0) {\n//         super(msg, \"RenderingCancelledException\");\n//         this.extraDelay = extraDelay;\n//     }\n// }\n// function isDataScheme(url) {\n//     const ii = url.length;\n//     let i = 0;\n//     while (i < ii && url[i].trim() === \"\") {\n//         i++;\n//     }\n//     return url.substring(i, i + 5).toLowerCase() === \"data:\";\n// }\n// function isPdfFile(filename) {\n//     return typeof filename === \"string\" && /\\.pdf$/i.test(filename);\n// }\n// /**\n//  * Gets the filename from a given URL.\n//  * @param {string} url\n//  * @returns {string}\n//  */\n// function getFilenameFromUrl(url) {\n//     [url] = url.split(/[#?]/, 1);\n//     return url.substring(url.lastIndexOf(\"/\") + 1);\n// }\n// /**\n//  * Returns the filename or guessed filename from the url (see issue 3455).\n//  * @param {string} url - The original PDF location.\n//  * @param {string} defaultFilename - The value returned if the filename is\n//  *   unknown, or the protocol is unsupported.\n//  * @returns {string} Guessed PDF filename.\n//  */\n// function getPdfFilenameFromUrl(url, defaultFilename = \"document.pdf\") {\n//     if (typeof url !== \"string\") {\n//         return defaultFilename;\n//     }\n//     if (isDataScheme(url)) {\n//         warn('getPdfFilenameFromUrl: ignore \"data:\"-URL for performance reasons.');\n//         return defaultFilename;\n//     }\n//     const reURI = /^(?:(?:[^:]+:)?\\/\\/[^/]+)?([^?#]*)(\\?[^#]*)?(#.*)?$/;\n//     //              SCHEME        HOST        1.PATH  2.QUERY   3.REF\n//     // Pattern to get last matching NAME.pdf\n//     const reFilename = /[^/?#=]+\\.pdf\\b(?!.*\\.pdf\\b)/i;\n//     const splitURI = reURI.exec(url);\n//     let suggestedFilename =\n//         reFilename.exec(splitURI[1]) ||\n//         reFilename.exec(splitURI[2]) ||\n//         reFilename.exec(splitURI[3]);\n//     if (suggestedFilename) {\n//         suggestedFilename = suggestedFilename[0];\n//         if (suggestedFilename.includes(\"%\")) {\n//             // URL-encoded %2Fpath%2Fto%2Ffile.pdf should be file.pdf\n//             try {\n//                 suggestedFilename = reFilename.exec(\n//                     decodeURIComponent(suggestedFilename)\n//                 )[0];\n//             } catch {\n//                 // Possible (extremely rare) errors:\n//                 // URIError \"Malformed URI\", e.g. for \"%AA.pdf\"\n//                 // TypeError \"null has no properties\", e.g. for \"%2F.pdf\"\n//             }\n//         }\n//     }\n//     return suggestedFilename || defaultFilename;\n// }\n// class StatTimer {\n//     started = Object.create(null);\n//     times = [];\n//     time(name) {\n//         if (name in this.started) {\n//             warn(`Timer is already running for ${name}`);\n//         }\n//         this.started[name] = Date.now();\n//     }\n//     timeEnd(name) {\n//         if (!(name in this.started)) {\n//             warn(`Timer has not been started for ${name}`);\n//         }\n//         this.times.push({\n//             name,\n//             start: this.started[name],\n//             end: Date.now(),\n//         });\n//         // Remove timer from started so it can be called again.\n//         delete this.started[name];\n//     }\n//     toString() {\n//         // Find the longest name for padding purposes.\n//         const outBuf = [];\n//         let longest = 0;\n//         for (const { name } of this.times) {\n//             longest = Math.max(name.length, longest);\n//         }\n//         for (const { name, start, end } of this.times) {\n//             outBuf.push(`${name.padEnd(longest)} ${end - start}ms\\n`);\n//         }\n//         return outBuf.join(\"\");\n//     }\n// }\n// function isValidFetchUrl(url, baseUrl) {\n//     if (typeof PDFJSDev !== \"undefined\" && PDFJSDev.test(\"MOZCENTRAL\")) {\n//         throw new Error(\"Not implemented: isValidFetchUrl\");\n//     }\n//     try {\n//         const { protocol } = baseUrl ? new URL(url, baseUrl) : new URL(url);\n//         // The Fetch API only supports the http/https protocols, and not file/ftp.\n//         return protocol === \"http:\" || protocol === \"https:\";\n//     } catch {\n//         return false; // `new URL()` will throw on incorrect data.\n//     }\n// }\n/**\n * Event handler to suppress context menu.\n */\nexport function noContextMenu(e) {\n    e.preventDefault();\n}\n// // Deprecated API function -- display regardless of the `verbosity` setting.\n// function deprecated(details) {\n//     console.log(\"Deprecated API usage: \" + details);\n// }\n// let pdfDateStringRegex;\n// class PDFDateString {\n//     /**\n//      * Convert a PDF date string to a JavaScript `Date` object.\n//      *\n//      * The PDF date string format is described in section 7.9.4 of the official\n//      * PDF 32000-1:2008 specification. However, in the PDF 1.7 reference (sixth\n//      * edition) Adobe describes the same format including a trailing apostrophe.\n//      * This syntax in incorrect, but Adobe Acrobat creates PDF files that contain\n//      * them. We ignore all apostrophes as they are not necessary for date parsing.\n//      *\n//      * Moreover, Adobe Acrobat doesn't handle changing the date to universal time\n//      * and doesn't use the user's time zone (effectively ignoring the HH' and mm'\n//      * parts of the date string).\n//      *\n//      * @param {string} input\n//      * @returns {Date|null}\n//      */\n//     static toDateObject(input) {\n//         if (!input || typeof input !== \"string\") {\n//             return null;\n//         }\n//         // Lazily initialize the regular expression.\n//         pdfDateStringRegex ||= new RegExp(\n//             \"^D:\" + // Prefix (required)\n//             \"(\\\\d{4})\" + // Year (required)\n//             \"(\\\\d{2})?\" + // Month (optional)\n//             \"(\\\\d{2})?\" + // Day (optional)\n//             \"(\\\\d{2})?\" + // Hour (optional)\n//             \"(\\\\d{2})?\" + // Minute (optional)\n//             \"(\\\\d{2})?\" + // Second (optional)\n//             \"([Z|+|-])?\" + // Universal time relation (optional)\n//             \"(\\\\d{2})?\" + // Offset hour (optional)\n//             \"'?\" + // Splitting apostrophe (optional)\n//             \"(\\\\d{2})?\" + // Offset minute (optional)\n//             \"'?\" // Trailing apostrophe (optional)\n//         );\n//         // Optional fields that don't satisfy the requirements from the regular\n//         // expression (such as incorrect digit counts or numbers that are out of\n//         // range) will fall back the defaults from the specification.\n//         const matches = pdfDateStringRegex.exec(input);\n//         if (!matches) {\n//             return null;\n//         }\n//         // JavaScript's `Date` object expects the month to be between 0 and 11\n//         // instead of 1 and 12, so we have to correct for that.\n//         const year = parseInt(matches[1], 10);\n//         let month = parseInt(matches[2], 10);\n//         month = month >= 1 && month <= 12 ? month - 1 : 0;\n//         let day = parseInt(matches[3], 10);\n//         day = day >= 1 && day <= 31 ? day : 1;\n//         let hour = parseInt(matches[4], 10);\n//         hour = hour >= 0 && hour <= 23 ? hour : 0;\n//         let minute = parseInt(matches[5], 10);\n//         minute = minute >= 0 && minute <= 59 ? minute : 0;\n//         let second = parseInt(matches[6], 10);\n//         second = second >= 0 && second <= 59 ? second : 0;\n//         const universalTimeRelation = matches[7] || \"Z\";\n//         let offsetHour = parseInt(matches[8], 10);\n//         offsetHour = offsetHour >= 0 && offsetHour <= 23 ? offsetHour : 0;\n//         let offsetMinute = parseInt(matches[9], 10) || 0;\n//         offsetMinute = offsetMinute >= 0 && offsetMinute <= 59 ? offsetMinute : 0;\n//         // Universal time relation 'Z' means that the local time is equal to the\n//         // universal time, whereas the relations '+'/'-' indicate that the local\n//         // time is later respectively earlier than the universal time. Every date\n//         // is normalized to universal time.\n//         if (universalTimeRelation === \"-\") {\n//             hour += offsetHour;\n//             minute += offsetMinute;\n//         } else if (universalTimeRelation === \"+\") {\n//             hour -= offsetHour;\n//             minute -= offsetMinute;\n//         }\n//         return new Date(Date.UTC(year, month, day, hour, minute, second));\n//     }\n// }\n// /**\n//  * NOTE: This is (mostly) intended to support printing of XFA forms.\n//  */\n// function getXfaPageViewport(xfaPage, { scale = 1, rotation = 0 }) {\n//     const { width, height } = xfaPage.attributes.style;\n//     const viewBox = [0, 0, parseInt(width), parseInt(height)];\n//     return new PageViewport({\n//         viewBox,\n//         scale,\n//         rotation,\n//     });\n// }\nexport function getRGB(color) {\n    if (color.startsWith(\"#\")) {\n        const colorRGB = parseInt(color.slice(1), 16);\n        return [\n            (colorRGB & 0xff0000) >> 16,\n            (colorRGB & 0x00ff00) >> 8,\n            colorRGB & 0x0000ff\n        ];\n    }\n    if (color.startsWith(\"rgb(\")) {\n        // getComputedStyle(...).color returns a `rgb(R, G, B)` color.\n        return color\n            .slice(/* \"rgb(\".length */ 4, -1) // Strip out \"rgb(\" and \")\".\n            .split(\",\")\n            .map(x => parseInt(x, 10));\n    }\n    if (color.startsWith(\"rgba(\")) {\n        return color\n            .slice(/* \"rgba(\".length */ 5, -1) // Strip out \"rgba(\" and \")\".\n            .split(\",\")\n            .map(x => parseInt(x, 10))\n            .slice(0, 3);\n    }\n    // warn(`Not a valid color format: \"${color}\"`);\n    return [0, 0, 0];\n}\nexport function getColorValues(colors) {\n    const span = document.createElement(\"span\");\n    span.style.visibility = \"hidden\";\n    document.body.append(span);\n    for (const name of colors.keys()) {\n        span.style.color = name;\n        const computedColor = window.getComputedStyle(span).color;\n        colors.set(name, getRGB(computedColor));\n    }\n    span.remove();\n}\n// function getCurrentTransform(ctx) {\n//     const { a, b, c, d, e, f } = ctx.getTransform();\n//     return [a, b, c, d, e, f];\n// }\n// function getCurrentTransformInverse(ctx) {\n//     const { a, b, c, d, e, f } = ctx.getTransform().invertSelf();\n//     return [a, b, c, d, e, f];\n// }\n// /**\n//  * @param {HTMLDivElement} div\n//  * @param {PageViewport} viewport\n//  * @param {boolean} mustFlip\n//  * @param {boolean} mustRotate\n//  */\n// function setLayerDimensions(\n//     div,\n//     viewport,\n//     mustFlip = false,\n//     mustRotate = true\n// ) {\n//     if (viewport instanceof PageViewport) {\n//         const { pageWidth, pageHeight } = viewport.rawDims;\n//         const { style } = div;\n//         const useRound = FeatureTest.isCSSRoundSupported;\n//         const w = `var(--scale-factor) * ${pageWidth}px`,\n//             h = `var(--scale-factor) * ${pageHeight}px`;\n//         const widthStr = useRound ? `round(${w}, 1px)` : `calc(${w})`,\n//             heightStr = useRound ? `round(${h}, 1px)` : `calc(${h})`;\n//         if (!mustFlip || viewport.rotation % 180 === 0) {\n//             style.width = widthStr;\n//             style.height = heightStr;\n//         } else {\n//             style.width = heightStr;\n//             style.height = widthStr;\n//         }\n//     }\n//     if (mustRotate) {\n//         div.setAttribute(\"data-main-rotation\", viewport.rotation);\n//     }\n// }\n// export {\n//     // deprecated,\n//     // DOMCanvasFactory,\n//     // DOMCMapReaderFactory,\n//     // DOMFilterFactory,\n//     // DOMStandardFontDataFactory,\n//     // DOMSVGFactory,\n//     // fetchData,\n//     // getColorValues,\n//     // getCurrentTransform,\n//     // getCurrentTransformInverse,\n//     // getFilenameFromUrl,\n//     // getPdfFilenameFromUrl,\n//     // getRGB,\n//     // getXfaPageViewport,\n//     // isDataScheme,\n//     // isPdfFile,\n//     // isValidFetchUrl,\n//     // noContextMenu,\n//     // PageViewport,\n//     // PDFDateString,\n//     // PixelsPerInch,\n//     // RenderingCancelledException,\n//     setLayerDimensions,\n//     // StatTimer,\n// };\n"], "mappings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aAAaA,CAACC,CAAC,EAAE;EAC7BA,CAAC,CAACC,cAAc,CAAC,CAAC;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,MAAMA,CAACC,KAAK,EAAE;EAC1B,IAAIA,KAAK,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;IACvB,MAAMC,QAAQ,GAAGC,QAAQ,CAACH,KAAK,CAACI,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC7C,OAAO,CACH,CAACF,QAAQ,GAAG,QAAQ,KAAK,EAAE,EAC3B,CAACA,QAAQ,GAAG,QAAQ,KAAK,CAAC,EAC1BA,QAAQ,GAAG,QAAQ,CACtB;EACL;EACA,IAAIF,KAAK,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;IAC1B;IACA,OAAOD,KAAK,CACPI,KAAK,CAAC,mBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAAA,CACjCC,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAACC,CAAC,IAAIJ,QAAQ,CAACI,CAAC,EAAE,EAAE,CAAC,CAAC;EAClC;EACA,IAAIP,KAAK,CAACC,UAAU,CAAC,OAAO,CAAC,EAAE;IAC3B,OAAOD,KAAK,CACPI,KAAK,CAAC,oBAAqB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAAA,CAClCC,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAACC,CAAC,IAAIJ,QAAQ,CAACI,CAAC,EAAE,EAAE,CAAC,CAAC,CACzBH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACpB;EACA;EACA,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACpB;AACA,OAAO,SAASI,cAAcA,CAACC,MAAM,EAAE;EACnC,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;EAC3CF,IAAI,CAACG,KAAK,CAACC,UAAU,GAAG,QAAQ;EAChCH,QAAQ,CAACI,IAAI,CAACC,MAAM,CAACN,IAAI,CAAC;EAC1B,KAAK,MAAMO,IAAI,IAAIR,MAAM,CAACS,IAAI,CAAC,CAAC,EAAE;IAC9BR,IAAI,CAACG,KAAK,CAACb,KAAK,GAAGiB,IAAI;IACvB,MAAME,aAAa,GAAGC,MAAM,CAACC,gBAAgB,CAACX,IAAI,CAAC,CAACV,KAAK;IACzDS,MAAM,CAACa,GAAG,CAACL,IAAI,EAAElB,MAAM,CAACoB,aAAa,CAAC,CAAC;EAC3C;EACAT,IAAI,CAACa,MAAM,CAAC,CAAC;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}