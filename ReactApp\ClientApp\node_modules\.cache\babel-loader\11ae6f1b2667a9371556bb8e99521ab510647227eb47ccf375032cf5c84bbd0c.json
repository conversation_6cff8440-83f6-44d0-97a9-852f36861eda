{"ast": null, "code": "function pow(p) {\n  if (p) {\n    return Math.pow(10, p);\n  }\n  return 1;\n}\nexport default function round(value, precision) {\n  var power = pow(precision);\n  return Math.round(value * power) / power;\n}", "map": {"version": 3, "names": ["pow", "p", "Math", "round", "value", "precision", "power"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/util/round.js"], "sourcesContent": ["function pow(p) {\n    if (p) {\n        return Math.pow(10, p);\n    }\n\n    return 1;\n}\n\nexport default function round(value, precision) {\n    var power = pow(precision);\n    return Math.round(value * power) / power;\n}"], "mappings": "AAAA,SAASA,GAAGA,CAACC,CAAC,EAAE;EACZ,IAAIA,CAAC,EAAE;IACH,OAAOC,IAAI,CAACF,GAAG,CAAC,EAAE,EAAEC,CAAC,CAAC;EAC1B;EAEA,OAAO,CAAC;AACZ;AAEA,eAAe,SAASE,KAAKA,CAACC,KAAK,EAAEC,SAAS,EAAE;EAC5C,IAAIC,KAAK,GAAGN,GAAG,CAACK,SAAS,CAAC;EAC1B,OAAOH,IAAI,CAACC,KAAK,CAACC,KAAK,GAAGE,KAAK,CAAC,GAAGA,KAAK;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}