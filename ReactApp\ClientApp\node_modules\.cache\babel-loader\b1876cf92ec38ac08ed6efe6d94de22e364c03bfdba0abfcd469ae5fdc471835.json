{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as n from \"react\";\nimport { Draggable as a } from \"@progress/kendo-react-common\";\nconst o = [\"n\", \"e\", \"s\", \"w\", \"se\", \"sw\", \"ne\", \"nw\"];\nclass c extends n.Component {\n  render() {\n    return /* @__PURE__ */n.createElement(\"div\", {\n      className: \"k-resize-handles-wrapper\"\n    }, \" \", o.map((t, r) => /* @__PURE__ */n.createElement(a, {\n      key: r,\n      onDrag: s => {\n        const {\n          event: e\n        } = s;\n        e.originalEvent.preventDefault(), this.props.onResize(e, {\n          end: !1,\n          direction: t\n        });\n      },\n      onRelease: s => {\n        const {\n          event: e\n        } = s;\n        e.originalEvent.preventDefault(), this.props.onResize(e, {\n          end: !0,\n          direction: t\n        });\n      }\n    }, /* @__PURE__ */n.createElement(\"div\", {\n      className: \"k-resize-handle k-resize-\" + t,\n      style: {\n        display: \"block\",\n        touchAction: \"none\",\n        userSelect: \"none\"\n      }\n    }))));\n  }\n}\nexport { c as ResizeHandlers };", "map": {"version": 3, "names": ["n", "Draggable", "a", "o", "c", "Component", "render", "createElement", "className", "map", "t", "r", "key", "onDrag", "s", "event", "e", "originalEvent", "preventDefault", "props", "onResize", "end", "direction", "onRelease", "style", "display", "touchAction", "userSelect", "ResizeHandlers"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dialogs/WindowResizeHandlers.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as n from \"react\";\nimport { Draggable as a } from \"@progress/kendo-react-common\";\nconst o = [\"n\", \"e\", \"s\", \"w\", \"se\", \"sw\", \"ne\", \"nw\"];\nclass c extends n.Component {\n  render() {\n    return /* @__PURE__ */ n.createElement(\"div\", { className: \"k-resize-handles-wrapper\" }, \" \", o.map((t, r) => /* @__PURE__ */ n.createElement(\n      a,\n      {\n        key: r,\n        onDrag: (s) => {\n          const { event: e } = s;\n          e.originalEvent.preventDefault(), this.props.onResize(e, { end: !1, direction: t });\n        },\n        onRelease: (s) => {\n          const { event: e } = s;\n          e.originalEvent.preventDefault(), this.props.onResize(e, { end: !0, direction: t });\n        }\n      },\n      /* @__PURE__ */ n.createElement(\n        \"div\",\n        {\n          className: \"k-resize-handle k-resize-\" + t,\n          style: { display: \"block\", touchAction: \"none\", userSelect: \"none\" }\n        }\n      )\n    )));\n  }\n}\nexport {\n  c as ResizeHandlers\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,SAASC,SAAS,IAAIC,CAAC,QAAQ,8BAA8B;AAC7D,MAAMC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AACtD,MAAMC,CAAC,SAASJ,CAAC,CAACK,SAAS,CAAC;EAC1BC,MAAMA,CAAA,EAAG;IACP,OAAO,eAAgBN,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE;MAAEC,SAAS,EAAE;IAA2B,CAAC,EAAE,GAAG,EAAEL,CAAC,CAACM,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,eAAgBX,CAAC,CAACO,aAAa,CAC3IL,CAAC,EACD;MACEU,GAAG,EAAED,CAAC;MACNE,MAAM,EAAGC,CAAC,IAAK;QACb,MAAM;UAAEC,KAAK,EAAEC;QAAE,CAAC,GAAGF,CAAC;QACtBE,CAAC,CAACC,aAAa,CAACC,cAAc,CAAC,CAAC,EAAE,IAAI,CAACC,KAAK,CAACC,QAAQ,CAACJ,CAAC,EAAE;UAAEK,GAAG,EAAE,CAAC,CAAC;UAAEC,SAAS,EAAEZ;QAAE,CAAC,CAAC;MACrF,CAAC;MACDa,SAAS,EAAGT,CAAC,IAAK;QAChB,MAAM;UAAEC,KAAK,EAAEC;QAAE,CAAC,GAAGF,CAAC;QACtBE,CAAC,CAACC,aAAa,CAACC,cAAc,CAAC,CAAC,EAAE,IAAI,CAACC,KAAK,CAACC,QAAQ,CAACJ,CAAC,EAAE;UAAEK,GAAG,EAAE,CAAC,CAAC;UAAEC,SAAS,EAAEZ;QAAE,CAAC,CAAC;MACrF;IACF,CAAC,EACD,eAAgBV,CAAC,CAACO,aAAa,CAC7B,KAAK,EACL;MACEC,SAAS,EAAE,2BAA2B,GAAGE,CAAC;MAC1Cc,KAAK,EAAE;QAAEC,OAAO,EAAE,OAAO;QAAEC,WAAW,EAAE,MAAM;QAAEC,UAAU,EAAE;MAAO;IACrE,CACF,CACF,CAAC,CAAC,CAAC;EACL;AACF;AACA,SACEvB,CAAC,IAAIwB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}