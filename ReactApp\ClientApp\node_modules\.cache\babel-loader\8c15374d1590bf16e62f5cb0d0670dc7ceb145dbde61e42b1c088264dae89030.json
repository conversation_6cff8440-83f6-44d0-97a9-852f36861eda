{"ast": null, "code": "import elementStyles from './element-styles';\nimport defined from './defined';\nfunction getPixels(value) {\n  if (isNaN(value)) {\n    return value;\n  }\n  return value + \"px\";\n}\nexport default function elementSize(element, size) {\n  if (size) {\n    var width = size.width;\n    var height = size.height;\n    if (defined(width)) {\n      element.style.width = getPixels(width);\n    }\n    if (defined(height)) {\n      element.style.height = getPixels(height);\n    }\n  } else {\n    var size$1 = elementStyles(element, ['width', 'height']);\n    return {\n      width: parseInt(size$1.width, 10),\n      height: parseInt(size$1.height, 10)\n    };\n  }\n}", "map": {"version": 3, "names": ["elementStyles", "defined", "getPixels", "value", "isNaN", "elementSize", "element", "size", "width", "height", "style", "size$1", "parseInt"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/util/element-size.js"], "sourcesContent": ["import elementStyles from './element-styles';\nimport defined from './defined';\n\nfunction getPixels(value) {\n    if (isNaN(value)) {\n        return value;\n    }\n    return value + \"px\";\n}\n\nexport default function elementSize(element, size) {\n    if (size) {\n        var width = size.width;\n        var height = size.height;\n\n        if (defined(width)) {\n            element.style.width = getPixels(width);\n        }\n\n        if (defined(height)) {\n            element.style.height = getPixels(height);\n        }\n\n    } else {\n        var size$1 = elementStyles(element, [ 'width', 'height' ]);\n\n        return {\n            width: parseInt(size$1.width, 10),\n            height: parseInt(size$1.height, 10)\n        };\n    }\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,OAAO,MAAM,WAAW;AAE/B,SAASC,SAASA,CAACC,KAAK,EAAE;EACtB,IAAIC,KAAK,CAACD,KAAK,CAAC,EAAE;IACd,OAAOA,KAAK;EAChB;EACA,OAAOA,KAAK,GAAG,IAAI;AACvB;AAEA,eAAe,SAASE,WAAWA,CAACC,OAAO,EAAEC,IAAI,EAAE;EAC/C,IAAIA,IAAI,EAAE;IACN,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IACtB,IAAIC,MAAM,GAAGF,IAAI,CAACE,MAAM;IAExB,IAAIR,OAAO,CAACO,KAAK,CAAC,EAAE;MAChBF,OAAO,CAACI,KAAK,CAACF,KAAK,GAAGN,SAAS,CAACM,KAAK,CAAC;IAC1C;IAEA,IAAIP,OAAO,CAACQ,MAAM,CAAC,EAAE;MACjBH,OAAO,CAACI,KAAK,CAACD,MAAM,GAAGP,SAAS,CAACO,MAAM,CAAC;IAC5C;EAEJ,CAAC,MAAM;IACH,IAAIE,MAAM,GAAGX,aAAa,CAACM,OAAO,EAAE,CAAE,OAAO,EAAE,QAAQ,CAAE,CAAC;IAE1D,OAAO;MACHE,KAAK,EAAEI,QAAQ,CAACD,MAAM,CAACH,KAAK,EAAE,EAAE,CAAC;MACjCC,MAAM,EAAEG,QAAQ,CAACD,MAAM,CAACF,MAAM,EAAE,EAAE;IACtC,CAAC;EACL;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}