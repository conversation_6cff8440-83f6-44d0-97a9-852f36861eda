{"ast": null, "code": "import { __spreadArray } from \"tslib\";\nimport { isPresent, isString } from './utils';\nimport { composeSortDescriptors } from './sorting/sort-array.operator';\nimport { groupBy, normalizeGroups } from './grouping/group.operators';\nimport { normalizeFilters } from './filtering/filter.operators';\nimport { compileFilter } from './filtering/filter-expression.factory';\nimport { exec, skip, take, filter, concat } from './transducers';\nimport { getter } from './accessor';\nimport { compose } from './funcs';\n/**\n * Orders the specified array according to the provided sort descriptors.\n *\n * @param {T[]} data - The data to be sorted.\n * @param {SortDescriptor[]} descriptors - The descriptors by which the data will be sorted.\n * @returns {T[]} - The sorted data.\n *\n * @example\n * ```ts\n * import { orderBy } from '@progress/kendo-data-query';\n *\n * const data = [\n *     { name: \"Pork\", category: \"Food\", subcategory: \"Meat\" },\n *     { name: \"Pepper\", category: \"Food\", subcategory: \"Vegetables\" },\n *     { name: \"Beef\", category: \"Food\", subcategory: \"Meat\" }\n * ];\n *\n * const sortDesc: SortDescriptor[] = [{ field: \"name\", dir: \"asc\" }];\n *\n * const result = orderBy(data, sortDesc);\n * ```\n */\nexport var orderBy = function (data, descriptors) {\n  if (descriptors.some(function (x) {\n    return isPresent(x.dir) || isPresent(x.compare);\n  })) {\n    data = data.slice(0);\n    var comparer = composeSortDescriptors(descriptors);\n    data.sort(comparer);\n  }\n  return data;\n};\nvar defaultComparer = function (a, b) {\n  return a === b;\n};\nvar normalizeComparer = function (comparer) {\n  if (isString(comparer)) {\n    var accessor_1 = getter(comparer);\n    comparer = function (a, b) {\n      return accessor_1(a) === accessor_1(b);\n    };\n  }\n  return comparer;\n};\nvar _distinct = function (data, comparer) {\n  return data.filter(function (x, idx, xs) {\n    return xs.findIndex(comparer.bind(null, x)) === idx;\n  });\n};\n/**\n * Reduces the provided array so it contains only unique values.\n *\n * @param {T[]} data - The array that will be reduced.\n * @param {(Comparer | string)} comparer - An optional custom comparer function or the field name that will be used for comparison.\n * @returns {T[]} - The reduced data.\n *\n * @example\n * ```ts\n * import { distinct } from '@progress/kendo-data-query';\n *\n * const data = [\n *     { name: \"Pork\", category: \"Food\", subcategory: \"Meat\" },\n *     { name: \"Pepper\", category: \"Food\", subcategory: \"Vegetables\" },\n *     { name: \"Beef\", category: \"Food\", subcategory: \"Meat\" }\n * ];\n *\n * const result = distinct(data, \"subcategory\");\n *\n * // output:\n * // result => [\n * //     { name: \"Pork\", category: \"Food\", subcategory: \"Meat\" },\n * //     { name: \"Pepper\", category: \"Food\", subcategory: \"Vegetables\" }\n * // ];\n * ```\n */\nexport var distinct = function (data, comparer) {\n  if (comparer === void 0) {\n    comparer = defaultComparer;\n  }\n  return _distinct(data, normalizeComparer(comparer));\n};\n/**\n * @hidden\n */\nexport var count = function (data, predicate) {\n  var counter = 0;\n  for (var idx = 0, length_1 = data.length; idx < length_1; idx++) {\n    if (predicate(data[idx])) {\n      counter++;\n    }\n  }\n  return counter;\n};\n/**\n * @hidden\n */\nexport var limit = function (data, predicate) {\n  if (predicate) {\n    return data.filter(predicate);\n  }\n  return data;\n};\n/**\n * Applies the specified operation descriptors to the data.\n *\n * @param {T[]} data - The data to be processed.\n * @param {State} state - The operation descriptors that will be applied to the data.\n * @returns {DataResult} - The processed data.\n *\n * @example\n * ```ts\n *\n * const result = process(data, {\n *     skip: 10,\n *     take: 20,\n *     group: [{\n *       field: 'category.categoryName',\n *             aggregates: [\n *                   { aggregate: \"sum\", field: \"unitPrice\" },\n *                   { aggregate: \"sum\", field: \"unitsInStock\" }\n *             ]\n *       }],\n *     sort: [{ field: 'productName', dir: 'desc' }],\n *     filter: {\n *         logic: \"or\",\n *         filters: [\n *           { field: \"discontinued\", operator: \"eq\", value: true },\n *           { field: \"unitPrice\", operator: \"lt\", value: 22 }\n *         ]\n *     }\n * });\n *\n * ```\n */\nexport var process = function (data, state) {\n  var skipCount = state.skip,\n    takeCount = state.take,\n    filterDescriptor = state.filter,\n    sort = state.sort,\n    group = state.group;\n  var sortDescriptors = __spreadArray(__spreadArray([], normalizeGroups(group || []), true), sort || [], true);\n  if (sortDescriptors.length) {\n    data = orderBy(data, sortDescriptors);\n  }\n  var hasFilters = isPresent(filterDescriptor) && filter.length;\n  var hasGroups = isPresent(group) && group.length;\n  if (!hasFilters && !hasGroups) {\n    return {\n      data: takeCount ? data.slice(skipCount, skipCount + takeCount) : data,\n      total: data.length\n    };\n  }\n  var total;\n  var transformers = [];\n  var predicate;\n  if (hasFilters) {\n    predicate = compileFilter(normalizeFilters(filterDescriptor));\n    total = count(data, predicate);\n    transformers.push(filter(predicate));\n  } else {\n    total = data.length;\n  }\n  if (isPresent(skipCount) && isPresent(takeCount)) {\n    transformers.push(skip(skipCount));\n    transformers.push(take(takeCount));\n  }\n  if (transformers.length) {\n    var transform = compose.apply(void 0, transformers);\n    var result = hasGroups ? groupBy(data, group, transform, limit(data, predicate)) : exec(transform(concat), [], data);\n    return {\n      data: result,\n      total: total\n    };\n  }\n  return {\n    data: hasGroups ? groupBy(data, group) : data,\n    total: total\n  };\n};", "map": {"version": 3, "names": ["__spread<PERSON><PERSON>y", "isPresent", "isString", "composeSortDescriptors", "groupBy", "normalizeGroups", "normalizeFilters", "compileFilter", "exec", "skip", "take", "filter", "concat", "getter", "compose", "orderBy", "data", "descriptors", "some", "x", "dir", "compare", "slice", "comparer", "sort", "defaultComparer", "a", "b", "normalizeComparer", "accessor_1", "_distinct", "idx", "xs", "findIndex", "bind", "distinct", "count", "predicate", "counter", "length_1", "length", "limit", "process", "state", "skip<PERSON><PERSON>nt", "takeCount", "filterDescriptor", "group", "sortDescriptors", "hasFilters", "hasGroups", "total", "transformers", "push", "transform", "apply", "result"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-data-query/dist/es/array.operators.js"], "sourcesContent": ["import { __spreadArray } from \"tslib\";\nimport { isPresent, isString } from './utils';\nimport { composeSortDescriptors } from './sorting/sort-array.operator';\nimport { groupBy, normalizeGroups } from './grouping/group.operators';\nimport { normalizeFilters } from './filtering/filter.operators';\nimport { compileFilter } from './filtering/filter-expression.factory';\nimport { exec, skip, take, filter, concat } from './transducers';\nimport { getter } from './accessor';\nimport { compose } from './funcs';\n/**\n * Orders the specified array according to the provided sort descriptors.\n *\n * @param {T[]} data - The data to be sorted.\n * @param {SortDescriptor[]} descriptors - The descriptors by which the data will be sorted.\n * @returns {T[]} - The sorted data.\n *\n * @example\n * ```ts\n * import { orderBy } from '@progress/kendo-data-query';\n *\n * const data = [\n *     { name: \"Pork\", category: \"Food\", subcategory: \"Meat\" },\n *     { name: \"Pepper\", category: \"Food\", subcategory: \"Vegetables\" },\n *     { name: \"Beef\", category: \"Food\", subcategory: \"Meat\" }\n * ];\n *\n * const sortDesc: SortDescriptor[] = [{ field: \"name\", dir: \"asc\" }];\n *\n * const result = orderBy(data, sortDesc);\n * ```\n */\nexport var orderBy = function (data, descriptors) {\n    if (descriptors.some(function (x) { return isPresent(x.dir) || isPresent(x.compare); })) {\n        data = data.slice(0);\n        var comparer = composeSortDescriptors(descriptors);\n        data.sort(comparer);\n    }\n    return data;\n};\nvar defaultComparer = function (a, b) { return a === b; };\nvar normalizeComparer = function (comparer) {\n    if (isString(comparer)) {\n        var accessor_1 = getter(comparer);\n        comparer = function (a, b) { return accessor_1(a) === accessor_1(b); };\n    }\n    return comparer;\n};\nvar _distinct = function (data, comparer) {\n    return data.filter(function (x, idx, xs) { return xs.findIndex(comparer.bind(null, x)) === idx; });\n};\n/**\n * Reduces the provided array so it contains only unique values.\n *\n * @param {T[]} data - The array that will be reduced.\n * @param {(Comparer | string)} comparer - An optional custom comparer function or the field name that will be used for comparison.\n * @returns {T[]} - The reduced data.\n *\n * @example\n * ```ts\n * import { distinct } from '@progress/kendo-data-query';\n *\n * const data = [\n *     { name: \"Pork\", category: \"Food\", subcategory: \"Meat\" },\n *     { name: \"Pepper\", category: \"Food\", subcategory: \"Vegetables\" },\n *     { name: \"Beef\", category: \"Food\", subcategory: \"Meat\" }\n * ];\n *\n * const result = distinct(data, \"subcategory\");\n *\n * // output:\n * // result => [\n * //     { name: \"Pork\", category: \"Food\", subcategory: \"Meat\" },\n * //     { name: \"Pepper\", category: \"Food\", subcategory: \"Vegetables\" }\n * // ];\n * ```\n */\nexport var distinct = function (data, comparer) {\n    if (comparer === void 0) { comparer = defaultComparer; }\n    return _distinct(data, normalizeComparer(comparer));\n};\n/**\n * @hidden\n */\nexport var count = function (data, predicate) {\n    var counter = 0;\n    for (var idx = 0, length_1 = data.length; idx < length_1; idx++) {\n        if (predicate(data[idx])) {\n            counter++;\n        }\n    }\n    return counter;\n};\n/**\n * @hidden\n */\nexport var limit = function (data, predicate) {\n    if (predicate) {\n        return data.filter(predicate);\n    }\n    return data;\n};\n/**\n * Applies the specified operation descriptors to the data.\n *\n * @param {T[]} data - The data to be processed.\n * @param {State} state - The operation descriptors that will be applied to the data.\n * @returns {DataResult} - The processed data.\n *\n * @example\n * ```ts\n *\n * const result = process(data, {\n *     skip: 10,\n *     take: 20,\n *     group: [{\n *       field: 'category.categoryName',\n *             aggregates: [\n *                   { aggregate: \"sum\", field: \"unitPrice\" },\n *                   { aggregate: \"sum\", field: \"unitsInStock\" }\n *             ]\n *       }],\n *     sort: [{ field: 'productName', dir: 'desc' }],\n *     filter: {\n *         logic: \"or\",\n *         filters: [\n *           { field: \"discontinued\", operator: \"eq\", value: true },\n *           { field: \"unitPrice\", operator: \"lt\", value: 22 }\n *         ]\n *     }\n * });\n *\n * ```\n */\nexport var process = function (data, state) {\n    var skipCount = state.skip, takeCount = state.take, filterDescriptor = state.filter, sort = state.sort, group = state.group;\n    var sortDescriptors = __spreadArray(__spreadArray([], normalizeGroups(group || []), true), sort || [], true);\n    if (sortDescriptors.length) {\n        data = orderBy(data, sortDescriptors);\n    }\n    var hasFilters = isPresent(filterDescriptor) && filter.length;\n    var hasGroups = isPresent(group) && group.length;\n    if (!hasFilters && !hasGroups) {\n        return {\n            data: takeCount ? data.slice(skipCount, skipCount + takeCount) : data,\n            total: data.length\n        };\n    }\n    var total;\n    var transformers = [];\n    var predicate;\n    if (hasFilters) {\n        predicate = compileFilter(normalizeFilters(filterDescriptor));\n        total = count(data, predicate);\n        transformers.push(filter(predicate));\n    }\n    else {\n        total = data.length;\n    }\n    if (isPresent(skipCount) && isPresent(takeCount)) {\n        transformers.push(skip(skipCount));\n        transformers.push(take(takeCount));\n    }\n    if (transformers.length) {\n        var transform = compose.apply(void 0, transformers);\n        var result = hasGroups ?\n            groupBy(data, group, transform, limit(data, predicate)) :\n            exec(transform(concat), [], data);\n        return { data: result, total: total };\n    }\n    return {\n        data: hasGroups ? groupBy(data, group) : data,\n        total: total\n    };\n};\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;AACrC,SAASC,SAAS,EAAEC,QAAQ,QAAQ,SAAS;AAC7C,SAASC,sBAAsB,QAAQ,+BAA+B;AACtE,SAASC,OAAO,EAAEC,eAAe,QAAQ,4BAA4B;AACrE,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,aAAa,QAAQ,uCAAuC;AACrE,SAASC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,QAAQ,eAAe;AAChE,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,OAAO,QAAQ,SAAS;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,OAAO,GAAG,SAAAA,CAAUC,IAAI,EAAEC,WAAW,EAAE;EAC9C,IAAIA,WAAW,CAACC,IAAI,CAAC,UAAUC,CAAC,EAAE;IAAE,OAAOlB,SAAS,CAACkB,CAAC,CAACC,GAAG,CAAC,IAAInB,SAAS,CAACkB,CAAC,CAACE,OAAO,CAAC;EAAE,CAAC,CAAC,EAAE;IACrFL,IAAI,GAAGA,IAAI,CAACM,KAAK,CAAC,CAAC,CAAC;IACpB,IAAIC,QAAQ,GAAGpB,sBAAsB,CAACc,WAAW,CAAC;IAClDD,IAAI,CAACQ,IAAI,CAACD,QAAQ,CAAC;EACvB;EACA,OAAOP,IAAI;AACf,CAAC;AACD,IAAIS,eAAe,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAOD,CAAC,KAAKC,CAAC;AAAE,CAAC;AACzD,IAAIC,iBAAiB,GAAG,SAAAA,CAAUL,QAAQ,EAAE;EACxC,IAAIrB,QAAQ,CAACqB,QAAQ,CAAC,EAAE;IACpB,IAAIM,UAAU,GAAGhB,MAAM,CAACU,QAAQ,CAAC;IACjCA,QAAQ,GAAG,SAAAA,CAAUG,CAAC,EAAEC,CAAC,EAAE;MAAE,OAAOE,UAAU,CAACH,CAAC,CAAC,KAAKG,UAAU,CAACF,CAAC,CAAC;IAAE,CAAC;EAC1E;EACA,OAAOJ,QAAQ;AACnB,CAAC;AACD,IAAIO,SAAS,GAAG,SAAAA,CAAUd,IAAI,EAAEO,QAAQ,EAAE;EACtC,OAAOP,IAAI,CAACL,MAAM,CAAC,UAAUQ,CAAC,EAAEY,GAAG,EAAEC,EAAE,EAAE;IAAE,OAAOA,EAAE,CAACC,SAAS,CAACV,QAAQ,CAACW,IAAI,CAAC,IAAI,EAAEf,CAAC,CAAC,CAAC,KAAKY,GAAG;EAAE,CAAC,CAAC;AACtG,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAII,QAAQ,GAAG,SAAAA,CAAUnB,IAAI,EAAEO,QAAQ,EAAE;EAC5C,IAAIA,QAAQ,KAAK,KAAK,CAAC,EAAE;IAAEA,QAAQ,GAAGE,eAAe;EAAE;EACvD,OAAOK,SAAS,CAACd,IAAI,EAAEY,iBAAiB,CAACL,QAAQ,CAAC,CAAC;AACvD,CAAC;AACD;AACA;AACA;AACA,OAAO,IAAIa,KAAK,GAAG,SAAAA,CAAUpB,IAAI,EAAEqB,SAAS,EAAE;EAC1C,IAAIC,OAAO,GAAG,CAAC;EACf,KAAK,IAAIP,GAAG,GAAG,CAAC,EAAEQ,QAAQ,GAAGvB,IAAI,CAACwB,MAAM,EAAET,GAAG,GAAGQ,QAAQ,EAAER,GAAG,EAAE,EAAE;IAC7D,IAAIM,SAAS,CAACrB,IAAI,CAACe,GAAG,CAAC,CAAC,EAAE;MACtBO,OAAO,EAAE;IACb;EACJ;EACA,OAAOA,OAAO;AAClB,CAAC;AACD;AACA;AACA;AACA,OAAO,IAAIG,KAAK,GAAG,SAAAA,CAAUzB,IAAI,EAAEqB,SAAS,EAAE;EAC1C,IAAIA,SAAS,EAAE;IACX,OAAOrB,IAAI,CAACL,MAAM,CAAC0B,SAAS,CAAC;EACjC;EACA,OAAOrB,IAAI;AACf,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAI0B,OAAO,GAAG,SAAAA,CAAU1B,IAAI,EAAE2B,KAAK,EAAE;EACxC,IAAIC,SAAS,GAAGD,KAAK,CAAClC,IAAI;IAAEoC,SAAS,GAAGF,KAAK,CAACjC,IAAI;IAAEoC,gBAAgB,GAAGH,KAAK,CAAChC,MAAM;IAAEa,IAAI,GAAGmB,KAAK,CAACnB,IAAI;IAAEuB,KAAK,GAAGJ,KAAK,CAACI,KAAK;EAC3H,IAAIC,eAAe,GAAGhD,aAAa,CAACA,aAAa,CAAC,EAAE,EAAEK,eAAe,CAAC0C,KAAK,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,EAAEvB,IAAI,IAAI,EAAE,EAAE,IAAI,CAAC;EAC5G,IAAIwB,eAAe,CAACR,MAAM,EAAE;IACxBxB,IAAI,GAAGD,OAAO,CAACC,IAAI,EAAEgC,eAAe,CAAC;EACzC;EACA,IAAIC,UAAU,GAAGhD,SAAS,CAAC6C,gBAAgB,CAAC,IAAInC,MAAM,CAAC6B,MAAM;EAC7D,IAAIU,SAAS,GAAGjD,SAAS,CAAC8C,KAAK,CAAC,IAAIA,KAAK,CAACP,MAAM;EAChD,IAAI,CAACS,UAAU,IAAI,CAACC,SAAS,EAAE;IAC3B,OAAO;MACHlC,IAAI,EAAE6B,SAAS,GAAG7B,IAAI,CAACM,KAAK,CAACsB,SAAS,EAAEA,SAAS,GAAGC,SAAS,CAAC,GAAG7B,IAAI;MACrEmC,KAAK,EAAEnC,IAAI,CAACwB;IAChB,CAAC;EACL;EACA,IAAIW,KAAK;EACT,IAAIC,YAAY,GAAG,EAAE;EACrB,IAAIf,SAAS;EACb,IAAIY,UAAU,EAAE;IACZZ,SAAS,GAAG9B,aAAa,CAACD,gBAAgB,CAACwC,gBAAgB,CAAC,CAAC;IAC7DK,KAAK,GAAGf,KAAK,CAACpB,IAAI,EAAEqB,SAAS,CAAC;IAC9Be,YAAY,CAACC,IAAI,CAAC1C,MAAM,CAAC0B,SAAS,CAAC,CAAC;EACxC,CAAC,MACI;IACDc,KAAK,GAAGnC,IAAI,CAACwB,MAAM;EACvB;EACA,IAAIvC,SAAS,CAAC2C,SAAS,CAAC,IAAI3C,SAAS,CAAC4C,SAAS,CAAC,EAAE;IAC9CO,YAAY,CAACC,IAAI,CAAC5C,IAAI,CAACmC,SAAS,CAAC,CAAC;IAClCQ,YAAY,CAACC,IAAI,CAAC3C,IAAI,CAACmC,SAAS,CAAC,CAAC;EACtC;EACA,IAAIO,YAAY,CAACZ,MAAM,EAAE;IACrB,IAAIc,SAAS,GAAGxC,OAAO,CAACyC,KAAK,CAAC,KAAK,CAAC,EAAEH,YAAY,CAAC;IACnD,IAAII,MAAM,GAAGN,SAAS,GAClB9C,OAAO,CAACY,IAAI,EAAE+B,KAAK,EAAEO,SAAS,EAAEb,KAAK,CAACzB,IAAI,EAAEqB,SAAS,CAAC,CAAC,GACvD7B,IAAI,CAAC8C,SAAS,CAAC1C,MAAM,CAAC,EAAE,EAAE,EAAEI,IAAI,CAAC;IACrC,OAAO;MAAEA,IAAI,EAAEwC,MAAM;MAAEL,KAAK,EAAEA;IAAM,CAAC;EACzC;EACA,OAAO;IACHnC,IAAI,EAAEkC,SAAS,GAAG9C,OAAO,CAACY,IAAI,EAAE+B,KAAK,CAAC,GAAG/B,IAAI;IAC7CmC,KAAK,EAAEA;EACX,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}