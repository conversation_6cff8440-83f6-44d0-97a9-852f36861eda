{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as s from \"react\";\nimport { IconWrap as l, classNames as r, toIconName as p } from \"@progress/kendo-react-common\";\nimport { plusIcon as c, insertTopIcon as m, insertBottomIcon as d, insertMiddleIcon as h, cancelIcon as u } from \"@progress/kendo-svg-icons\";\nconst e = class e extends s.PureComponent {\n  constructor() {\n    super(...arguments), this.state = {\n      visible: !1,\n      top: 0,\n      left: 0,\n      text: \"\",\n      operationClassName: \"cancel\"\n    };\n  }\n  /**\n   * @hidden\n   */\n  render() {\n    const t = {\n      top: this.state.top + \"px\",\n      left: this.state.left + \"px\"\n    };\n    return this.state.visible && /* @__PURE__ */s.createElement(\"div\", {\n      className: \"k-header k-drag-clue\",\n      style: {\n        ...this.props.style,\n        ...t\n      }\n    }, /* @__PURE__ */s.createElement(l, {\n      className: r(\"k-drag-status\"),\n      name: this.state.operationClassName && p(this.state.operationClassName),\n      icon: this.state.operationClassName === \"k-i-plus\" ? c : this.state.operationClassName === \"k-i-insert-up\" ? m : this.state.operationClassName === \"k-i-insert-down\" ? d : this.state.operationClassName === \"k-i-insert-middle\" ? h : u\n    }), this.state.text);\n  }\n  /**\n   * Displays the TreeViewDragClue component.\n   *\n   * @param top - The `top` CSS position of the component.\n   * @param left - The `left` CSS position of the component.\n   * @param text - The text of the component.\n   * @param operationClassName - The CSS class name which is related to the specific drop operation.\n   */\n  show(t, i, o, n) {\n    this.setState({\n      visible: !0,\n      top: t,\n      left: i,\n      text: o,\n      operationClassName: n\n    });\n  }\n  /**\n   * Hides the TreeViewDragClue component.\n   */\n  hide() {\n    this.setState({\n      visible: !1\n    });\n  }\n};\ne.defaultProps = {\n  style: {\n    display: \"block\",\n    position: \"absolute\",\n    zIndex: 2e4,\n    padding: \"4px 6px\"\n  }\n};\nlet a = e;\nexport { a as TreeViewDragClue };", "map": {"version": 3, "names": ["s", "IconWrap", "l", "classNames", "r", "toIconName", "p", "plusIcon", "c", "insertTopIcon", "m", "insertBottomIcon", "d", "insertMiddleIcon", "h", "cancelIcon", "u", "e", "PureComponent", "constructor", "arguments", "state", "visible", "top", "left", "text", "operationClassName", "render", "t", "createElement", "className", "style", "props", "name", "icon", "show", "i", "o", "n", "setState", "hide", "defaultProps", "display", "position", "zIndex", "padding", "a", "TreeViewDragClue"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-treeview/TreeViewDragClue.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as s from \"react\";\nimport { IconWrap as l, classNames as r, toIconName as p } from \"@progress/kendo-react-common\";\nimport { plusIcon as c, insertTopIcon as m, insertBottomIcon as d, insertMiddleIcon as h, cancelIcon as u } from \"@progress/kendo-svg-icons\";\nconst e = class e extends s.PureComponent {\n  constructor() {\n    super(...arguments), this.state = {\n      visible: !1,\n      top: 0,\n      left: 0,\n      text: \"\",\n      operationClassName: \"cancel\"\n    };\n  }\n  /**\n   * @hidden\n   */\n  render() {\n    const t = { top: this.state.top + \"px\", left: this.state.left + \"px\" };\n    return this.state.visible && /* @__PURE__ */ s.createElement(\"div\", { className: \"k-header k-drag-clue\", style: { ...this.props.style, ...t } }, /* @__PURE__ */ s.createElement(\n      l,\n      {\n        className: r(\"k-drag-status\"),\n        name: this.state.operationClassName && p(this.state.operationClassName),\n        icon: this.state.operationClassName === \"k-i-plus\" ? c : this.state.operationClassName === \"k-i-insert-up\" ? m : this.state.operationClassName === \"k-i-insert-down\" ? d : this.state.operationClassName === \"k-i-insert-middle\" ? h : u\n      }\n    ), this.state.text);\n  }\n  /**\n   * Displays the TreeViewDragClue component.\n   *\n   * @param top - The `top` CSS position of the component.\n   * @param left - The `left` CSS position of the component.\n   * @param text - The text of the component.\n   * @param operationClassName - The CSS class name which is related to the specific drop operation.\n   */\n  show(t, i, o, n) {\n    this.setState({ visible: !0, top: t, left: i, text: o, operationClassName: n });\n  }\n  /**\n   * Hides the TreeViewDragClue component.\n   */\n  hide() {\n    this.setState({ visible: !1 });\n  }\n};\ne.defaultProps = { style: { display: \"block\", position: \"absolute\", zIndex: 2e4, padding: \"4px 6px\" } };\nlet a = e;\nexport {\n  a as TreeViewDragClue\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,SAASC,QAAQ,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AAC9F,SAASC,QAAQ,IAAIC,CAAC,EAAEC,aAAa,IAAIC,CAAC,EAAEC,gBAAgB,IAAIC,CAAC,EAAEC,gBAAgB,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,QAAQ,2BAA2B;AAC5I,MAAMC,CAAC,GAAG,MAAMA,CAAC,SAASjB,CAAC,CAACkB,aAAa,CAAC;EACxCC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,GAAGC,SAAS,CAAC,EAAE,IAAI,CAACC,KAAK,GAAG;MAChCC,OAAO,EAAE,CAAC,CAAC;MACXC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRC,kBAAkB,EAAE;IACtB,CAAC;EACH;EACA;AACF;AACA;EACEC,MAAMA,CAAA,EAAG;IACP,MAAMC,CAAC,GAAG;MAAEL,GAAG,EAAE,IAAI,CAACF,KAAK,CAACE,GAAG,GAAG,IAAI;MAAEC,IAAI,EAAE,IAAI,CAACH,KAAK,CAACG,IAAI,GAAG;IAAK,CAAC;IACtE,OAAO,IAAI,CAACH,KAAK,CAACC,OAAO,IAAI,eAAgBtB,CAAC,CAAC6B,aAAa,CAAC,KAAK,EAAE;MAAEC,SAAS,EAAE,sBAAsB;MAAEC,KAAK,EAAE;QAAE,GAAG,IAAI,CAACC,KAAK,CAACD,KAAK;QAAE,GAAGH;MAAE;IAAE,CAAC,EAAE,eAAgB5B,CAAC,CAAC6B,aAAa,CAC9K3B,CAAC,EACD;MACE4B,SAAS,EAAE1B,CAAC,CAAC,eAAe,CAAC;MAC7B6B,IAAI,EAAE,IAAI,CAACZ,KAAK,CAACK,kBAAkB,IAAIpB,CAAC,CAAC,IAAI,CAACe,KAAK,CAACK,kBAAkB,CAAC;MACvEQ,IAAI,EAAE,IAAI,CAACb,KAAK,CAACK,kBAAkB,KAAK,UAAU,GAAGlB,CAAC,GAAG,IAAI,CAACa,KAAK,CAACK,kBAAkB,KAAK,eAAe,GAAGhB,CAAC,GAAG,IAAI,CAACW,KAAK,CAACK,kBAAkB,KAAK,iBAAiB,GAAGd,CAAC,GAAG,IAAI,CAACS,KAAK,CAACK,kBAAkB,KAAK,mBAAmB,GAAGZ,CAAC,GAAGE;IACzO,CACF,CAAC,EAAE,IAAI,CAACK,KAAK,CAACI,IAAI,CAAC;EACrB;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEU,IAAIA,CAACP,CAAC,EAAEQ,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;IACf,IAAI,CAACC,QAAQ,CAAC;MAAEjB,OAAO,EAAE,CAAC,CAAC;MAAEC,GAAG,EAAEK,CAAC;MAAEJ,IAAI,EAAEY,CAAC;MAAEX,IAAI,EAAEY,CAAC;MAAEX,kBAAkB,EAAEY;IAAE,CAAC,CAAC;EACjF;EACA;AACF;AACA;EACEE,IAAIA,CAAA,EAAG;IACL,IAAI,CAACD,QAAQ,CAAC;MAAEjB,OAAO,EAAE,CAAC;IAAE,CAAC,CAAC;EAChC;AACF,CAAC;AACDL,CAAC,CAACwB,YAAY,GAAG;EAAEV,KAAK,EAAE;IAAEW,OAAO,EAAE,OAAO;IAAEC,QAAQ,EAAE,UAAU;IAAEC,MAAM,EAAE,GAAG;IAAEC,OAAO,EAAE;EAAU;AAAE,CAAC;AACvG,IAAIC,CAAC,GAAG7B,CAAC;AACT,SACE6B,CAAC,IAAIC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}