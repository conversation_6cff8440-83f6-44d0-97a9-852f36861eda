{"ast": null, "code": "import { firstDecadeOfCentury } from './first-decade-of-century';\n/**\n * A function that calculates duration in centuries between two `Date` objects.\n *\n * @param start - The start date value.\n * @param end - The end date value.\n * @returns - The duration in months.\n *\n * @example\n * ```ts-no-run\n * durationInCenturies(new Date(2016, 0, 1), new Date(3216, 0, 1)); // 12\n * durationInCenturies(new Date(2016, 6, 1), new Date(2617, 0, 1)); // 6\n * durationInCenturies(new Date(2016, 0, 1), new Date(2016, 0, 1)); // 0\n * ```\n */\nexport var durationInCenturies = function (start, end) {\n  return (firstDecadeOfCentury(end).getFullYear() - firstDecadeOfCentury(start).getFullYear()) / 100;\n};", "map": {"version": 3, "names": ["firstDecadeOfCentury", "durationInCenturies", "start", "end", "getFullYear"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/duration-in-centuries.js"], "sourcesContent": ["import { firstDecadeOfCentury } from './first-decade-of-century';\n/**\n * A function that calculates duration in centuries between two `Date` objects.\n *\n * @param start - The start date value.\n * @param end - The end date value.\n * @returns - The duration in months.\n *\n * @example\n * ```ts-no-run\n * durationInCenturies(new Date(2016, 0, 1), new Date(3216, 0, 1)); // 12\n * durationInCenturies(new Date(2016, 6, 1), new Date(2617, 0, 1)); // 6\n * durationInCenturies(new Date(2016, 0, 1), new Date(2016, 0, 1)); // 0\n * ```\n */\nexport var durationInCenturies = function (start, end) { return ((firstDecadeOfCentury(end).getFullYear() - firstDecadeOfCentury(start).getFullYear()) / 100); };\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,2BAA2B;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,mBAAmB,GAAG,SAAAA,CAAUC,KAAK,EAAEC,GAAG,EAAE;EAAE,OAAQ,CAACH,oBAAoB,CAACG,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGJ,oBAAoB,CAACE,KAAK,CAAC,CAACE,WAAW,CAAC,CAAC,IAAI,GAAG;AAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}