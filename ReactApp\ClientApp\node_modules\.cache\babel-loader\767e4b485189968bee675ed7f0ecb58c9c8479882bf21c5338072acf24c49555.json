{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as n from \"react\";\nimport r from \"prop-types\";\nconst s = class s extends n.Component {\n  /**\n   * @hidden\n   */\n  render() {\n    return null;\n  }\n};\ns.propTypes = {\n  text: r.string,\n  url: r.string,\n  icon: r.string,\n  disabled: r.bool,\n  cssClass: r.string,\n  cssStyle: r.object,\n  render: r.any,\n  linkRender: r.any,\n  contentRender: r.any,\n  data: r.any,\n  separator: r.bool\n};\nlet t = s;\nexport { t as MenuItem };", "map": {"version": 3, "names": ["n", "r", "s", "Component", "render", "propTypes", "text", "string", "url", "icon", "disabled", "bool", "cssClass", "cssStyle", "object", "any", "linkRender", "contentRender", "data", "separator", "t", "MenuItem"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/menu/components/MenuItem.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as n from \"react\";\nimport r from \"prop-types\";\nconst s = class s extends n.Component {\n  /**\n   * @hidden\n   */\n  render() {\n    return null;\n  }\n};\ns.propTypes = {\n  text: r.string,\n  url: r.string,\n  icon: r.string,\n  disabled: r.bool,\n  cssClass: r.string,\n  cssStyle: r.object,\n  render: r.any,\n  linkRender: r.any,\n  contentRender: r.any,\n  data: r.any,\n  separator: r.bool\n};\nlet t = s;\nexport {\n  t as MenuItem\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,MAAMC,CAAC,GAAG,MAAMA,CAAC,SAASF,CAAC,CAACG,SAAS,CAAC;EACpC;AACF;AACA;EACEC,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI;EACb;AACF,CAAC;AACDF,CAAC,CAACG,SAAS,GAAG;EACZC,IAAI,EAAEL,CAAC,CAACM,MAAM;EACdC,GAAG,EAAEP,CAAC,CAACM,MAAM;EACbE,IAAI,EAAER,CAAC,CAACM,MAAM;EACdG,QAAQ,EAAET,CAAC,CAACU,IAAI;EAChBC,QAAQ,EAAEX,CAAC,CAACM,MAAM;EAClBM,QAAQ,EAAEZ,CAAC,CAACa,MAAM;EAClBV,MAAM,EAAEH,CAAC,CAACc,GAAG;EACbC,UAAU,EAAEf,CAAC,CAACc,GAAG;EACjBE,aAAa,EAAEhB,CAAC,CAACc,GAAG;EACpBG,IAAI,EAAEjB,CAAC,CAACc,GAAG;EACXI,SAAS,EAAElB,CAAC,CAACU;AACf,CAAC;AACD,IAAIS,CAAC,GAAGlB,CAAC;AACT,SACEkB,CAAC,IAAIC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}