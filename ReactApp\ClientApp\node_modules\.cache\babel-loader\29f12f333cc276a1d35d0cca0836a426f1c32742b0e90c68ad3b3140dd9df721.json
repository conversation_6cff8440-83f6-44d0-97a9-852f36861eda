{"ast": null, "code": "import defined from './defined';\nexport default function isTransparent(color) {\n  return color === \"\" || color === null || color === \"none\" || color === \"transparent\" || !defined(color);\n}", "map": {"version": 3, "names": ["defined", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "color"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/util/is-transparent.js"], "sourcesContent": ["import defined from './defined';\n\nexport default function isTransparent(color) {\n    return color === \"\" || color === null || color === \"none\" || color === \"transparent\" || !defined(color);\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,WAAW;AAE/B,eAAe,SAASC,aAAaA,CAACC,KAAK,EAAE;EACzC,OAAOA,KAAK,KAAK,EAAE,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,aAAa,IAAI,CAACF,OAAO,CAACE,KAAK,CAAC;AAC3G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}