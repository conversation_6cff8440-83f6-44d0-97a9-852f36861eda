{"ast": null, "code": "import GeometryArc from '../geometry/arc';\nimport Element from './element';\nimport { Path } from './path';\nimport paintable from '../mixins/paintable';\nimport measurable from '../mixins/measurable';\nimport withGeometry from '../mixins/with-geometry';\nimport { defined } from '../util';\nvar DEFAULT_STROKE = \"#000\";\nvar Arc = function (superclass) {\n  function Arc(geometry, options) {\n    if (geometry === void 0) geometry = new GeometryArc();\n    if (options === void 0) options = {};\n    superclass.call(this, options);\n    this.geometry(geometry);\n    if (!defined(this.options.stroke)) {\n      this.stroke(DEFAULT_STROKE);\n    }\n  }\n  if (superclass) Arc.__proto__ = superclass;\n  Arc.prototype = Object.create(superclass && superclass.prototype);\n  Arc.prototype.constructor = Arc;\n  var prototypeAccessors = {\n    nodeType: {\n      configurable: true\n    }\n  };\n  prototypeAccessors.nodeType.get = function () {\n    return \"Arc\";\n  };\n  Arc.prototype._bbox = function _bbox(matrix) {\n    return this._geometry.bbox(matrix);\n  };\n  Arc.prototype.rawBBox = function rawBBox() {\n    return this.geometry().bbox();\n  };\n  Arc.prototype.toPath = function toPath() {\n    var path = new Path();\n    var curvePoints = this.geometry().curvePoints();\n    if (curvePoints.length > 0) {\n      path.moveTo(curvePoints[0].x, curvePoints[0].y);\n      for (var i = 1; i < curvePoints.length; i += 3) {\n        path.curveTo(curvePoints[i], curvePoints[i + 1], curvePoints[i + 2]);\n      }\n    }\n    return path;\n  };\n  Arc.prototype._containsPoint = function _containsPoint(point) {\n    return this.geometry().containsPoint(point);\n  };\n  Arc.prototype._isOnPath = function _isOnPath(point) {\n    return this.geometry()._isOnPath(point, this.options.stroke.width / 2);\n  };\n  Object.defineProperties(Arc.prototype, prototypeAccessors);\n  return Arc;\n}(paintable(measurable(withGeometry(Element))));\nexport default Arc;", "map": {"version": 3, "names": ["GeometryArc", "Element", "Path", "paintable", "measurable", "withGeometry", "defined", "DEFAULT_STROKE", "Arc", "superclass", "geometry", "options", "call", "stroke", "__proto__", "prototype", "Object", "create", "constructor", "prototypeAccessors", "nodeType", "configurable", "get", "_bbox", "matrix", "_geometry", "bbox", "rawBBox", "to<PERSON><PERSON>", "path", "curvePoints", "length", "moveTo", "x", "y", "i", "curveTo", "_containsPoint", "point", "containsPoint", "_isOnPath", "width", "defineProperties"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/shapes/arc.js"], "sourcesContent": ["import GeometryArc from '../geometry/arc';\nimport Element from './element';\nimport { Path } from './path';\nimport paintable from '../mixins/paintable';\nimport measurable from '../mixins/measurable';\nimport withGeometry from '../mixins/with-geometry';\nimport { defined } from '../util';\n\n\nvar DEFAULT_STROKE = \"#000\";\n\nvar Arc = (function (superclass) {\n    function Arc(geometry, options) {\n        if ( geometry === void 0 ) geometry = new GeometryArc();\n        if ( options === void 0 ) options = {};\n\n        superclass.call(this, options);\n\n        this.geometry(geometry);\n\n        if (!defined(this.options.stroke)) {\n            this.stroke(DEFAULT_STROKE);\n        }\n    }\n\n    if ( superclass ) Arc.__proto__ = superclass;\n    Arc.prototype = Object.create( superclass && superclass.prototype );\n    Arc.prototype.constructor = Arc;\n\n    var prototypeAccessors = { nodeType: { configurable: true } };\n\n    prototypeAccessors.nodeType.get = function () {\n        return \"Arc\";\n    };\n\n    Arc.prototype._bbox = function _bbox (matrix) {\n        return this._geometry.bbox(matrix);\n    };\n\n    Arc.prototype.rawBBox = function rawBBox () {\n        return this.geometry().bbox();\n    };\n\n    Arc.prototype.toPath = function toPath () {\n        var path = new Path();\n        var curvePoints = this.geometry().curvePoints();\n\n        if (curvePoints.length > 0) {\n            path.moveTo(curvePoints[0].x, curvePoints[0].y);\n\n            for (var i = 1; i < curvePoints.length; i += 3) {\n                path.curveTo(curvePoints[i], curvePoints[i + 1], curvePoints[i + 2]);\n            }\n        }\n\n        return path;\n    };\n\n    Arc.prototype._containsPoint = function _containsPoint (point) {\n        return this.geometry().containsPoint(point);\n    };\n\n    Arc.prototype._isOnPath = function _isOnPath (point) {\n        return this.geometry()._isOnPath(point, this.options.stroke.width / 2);\n    };\n\n    Object.defineProperties( Arc.prototype, prototypeAccessors );\n\n    return Arc;\n}(paintable(measurable(withGeometry(Element)))));\n\nexport default Arc;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,iBAAiB;AACzC,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASC,IAAI,QAAQ,QAAQ;AAC7B,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,UAAU,MAAM,sBAAsB;AAC7C,OAAOC,YAAY,MAAM,yBAAyB;AAClD,SAASC,OAAO,QAAQ,SAAS;AAGjC,IAAIC,cAAc,GAAG,MAAM;AAE3B,IAAIC,GAAG,GAAI,UAAUC,UAAU,EAAE;EAC7B,SAASD,GAAGA,CAACE,QAAQ,EAAEC,OAAO,EAAE;IAC5B,IAAKD,QAAQ,KAAK,KAAK,CAAC,EAAGA,QAAQ,GAAG,IAAIV,WAAW,CAAC,CAAC;IACvD,IAAKW,OAAO,KAAK,KAAK,CAAC,EAAGA,OAAO,GAAG,CAAC,CAAC;IAEtCF,UAAU,CAACG,IAAI,CAAC,IAAI,EAAED,OAAO,CAAC;IAE9B,IAAI,CAACD,QAAQ,CAACA,QAAQ,CAAC;IAEvB,IAAI,CAACJ,OAAO,CAAC,IAAI,CAACK,OAAO,CAACE,MAAM,CAAC,EAAE;MAC/B,IAAI,CAACA,MAAM,CAACN,cAAc,CAAC;IAC/B;EACJ;EAEA,IAAKE,UAAU,EAAGD,GAAG,CAACM,SAAS,GAAGL,UAAU;EAC5CD,GAAG,CAACO,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAER,UAAU,IAAIA,UAAU,CAACM,SAAU,CAAC;EACnEP,GAAG,CAACO,SAAS,CAACG,WAAW,GAAGV,GAAG;EAE/B,IAAIW,kBAAkB,GAAG;IAAEC,QAAQ,EAAE;MAAEC,YAAY,EAAE;IAAK;EAAE,CAAC;EAE7DF,kBAAkB,CAACC,QAAQ,CAACE,GAAG,GAAG,YAAY;IAC1C,OAAO,KAAK;EAChB,CAAC;EAEDd,GAAG,CAACO,SAAS,CAACQ,KAAK,GAAG,SAASA,KAAKA,CAAEC,MAAM,EAAE;IAC1C,OAAO,IAAI,CAACC,SAAS,CAACC,IAAI,CAACF,MAAM,CAAC;EACtC,CAAC;EAEDhB,GAAG,CAACO,SAAS,CAACY,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;IACxC,OAAO,IAAI,CAACjB,QAAQ,CAAC,CAAC,CAACgB,IAAI,CAAC,CAAC;EACjC,CAAC;EAEDlB,GAAG,CAACO,SAAS,CAACa,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAI;IACtC,IAAIC,IAAI,GAAG,IAAI3B,IAAI,CAAC,CAAC;IACrB,IAAI4B,WAAW,GAAG,IAAI,CAACpB,QAAQ,CAAC,CAAC,CAACoB,WAAW,CAAC,CAAC;IAE/C,IAAIA,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;MACxBF,IAAI,CAACG,MAAM,CAACF,WAAW,CAAC,CAAC,CAAC,CAACG,CAAC,EAAEH,WAAW,CAAC,CAAC,CAAC,CAACI,CAAC,CAAC;MAE/C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,WAAW,CAACC,MAAM,EAAEI,CAAC,IAAI,CAAC,EAAE;QAC5CN,IAAI,CAACO,OAAO,CAACN,WAAW,CAACK,CAAC,CAAC,EAAEL,WAAW,CAACK,CAAC,GAAG,CAAC,CAAC,EAAEL,WAAW,CAACK,CAAC,GAAG,CAAC,CAAC,CAAC;MACxE;IACJ;IAEA,OAAON,IAAI;EACf,CAAC;EAEDrB,GAAG,CAACO,SAAS,CAACsB,cAAc,GAAG,SAASA,cAAcA,CAAEC,KAAK,EAAE;IAC3D,OAAO,IAAI,CAAC5B,QAAQ,CAAC,CAAC,CAAC6B,aAAa,CAACD,KAAK,CAAC;EAC/C,CAAC;EAED9B,GAAG,CAACO,SAAS,CAACyB,SAAS,GAAG,SAASA,SAASA,CAAEF,KAAK,EAAE;IACjD,OAAO,IAAI,CAAC5B,QAAQ,CAAC,CAAC,CAAC8B,SAAS,CAACF,KAAK,EAAE,IAAI,CAAC3B,OAAO,CAACE,MAAM,CAAC4B,KAAK,GAAG,CAAC,CAAC;EAC1E,CAAC;EAEDzB,MAAM,CAAC0B,gBAAgB,CAAElC,GAAG,CAACO,SAAS,EAAEI,kBAAmB,CAAC;EAE5D,OAAOX,GAAG;AACd,CAAC,CAACL,SAAS,CAACC,UAAU,CAACC,YAAY,CAACJ,OAAO,CAAC,CAAC,CAAC,CAAE;AAEhD,eAAeO,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}