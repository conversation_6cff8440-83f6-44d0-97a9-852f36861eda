{"ast": null, "code": "export default function elementStyles(element, styles) {\n  var result = {};\n  var style = window.getComputedStyle(element) || {};\n  var stylesArray = Array.isArray(styles) ? styles : [styles];\n  for (var idx = 0; idx < stylesArray.length; idx++) {\n    var field = stylesArray[idx];\n    result[field] = style[field];\n  }\n  return result;\n}", "map": {"version": 3, "names": ["elementStyles", "element", "styles", "result", "style", "window", "getComputedStyle", "stylesArray", "Array", "isArray", "idx", "length", "field"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/util/element-styles.js"], "sourcesContent": ["export default function elementStyles(element, styles) {\n    var result = {};\n    var style = window.getComputedStyle(element) || {};\n    var stylesArray = Array.isArray(styles) ? styles : [ styles ];\n\n    for (var idx = 0; idx < stylesArray.length; idx++) {\n        var field = stylesArray[idx];\n        result[field] = style[field];\n    }\n\n    return result;\n}"], "mappings": "AAAA,eAAe,SAASA,aAAaA,CAACC,OAAO,EAAEC,MAAM,EAAE;EACnD,IAAIC,MAAM,GAAG,CAAC,CAAC;EACf,IAAIC,KAAK,GAAGC,MAAM,CAACC,gBAAgB,CAACL,OAAO,CAAC,IAAI,CAAC,CAAC;EAClD,IAAIM,WAAW,GAAGC,KAAK,CAACC,OAAO,CAACP,MAAM,CAAC,GAAGA,MAAM,GAAG,CAAEA,MAAM,CAAE;EAE7D,KAAK,IAAIQ,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGH,WAAW,CAACI,MAAM,EAAED,GAAG,EAAE,EAAE;IAC/C,IAAIE,KAAK,GAAGL,WAAW,CAACG,GAAG,CAAC;IAC5BP,MAAM,CAACS,KAAK,CAAC,GAAGR,KAAK,CAACQ,KAAK,CAAC;EAChC;EAEA,OAAOT,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}