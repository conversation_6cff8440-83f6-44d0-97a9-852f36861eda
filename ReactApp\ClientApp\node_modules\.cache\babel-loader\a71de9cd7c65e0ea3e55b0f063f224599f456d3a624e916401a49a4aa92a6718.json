{"ast": null, "code": "import * as easingFunctions from './easing-functions';\nimport { limitValue, now } from '../util';\nimport { animationFrame, Class } from '../common';\nimport AnimationFactory from './animation-factory';\nvar Animation = function (Class) {\n  function Animation(element, options) {\n    Class.call(this);\n    this.options = Object.assign({}, this.options, options);\n    this.element = element;\n  }\n  if (Class) Animation.__proto__ = Class;\n  Animation.prototype = Object.create(Class && Class.prototype);\n  Animation.prototype.constructor = Animation;\n  var prototypeAccessors = {\n    options: {\n      configurable: true\n    }\n  };\n  Animation.create = function create(type, element, options) {\n    return AnimationFactory.current.create(type, element, options);\n  };\n  prototypeAccessors.options.get = function () {\n    return this._options || {\n      duration: 500,\n      easing: \"swing\"\n    };\n  };\n  prototypeAccessors.options.set = function (value) {\n    this._options = value;\n  };\n  Animation.prototype.setup = function setup() {};\n  Animation.prototype.step = function step() {};\n  Animation.prototype.play = function play() {\n    var this$1 = this;\n    var options = this.options;\n    var duration = options.duration;\n    var delay = options.delay;\n    if (delay === void 0) delay = 0;\n    var easing = easingFunctions[options.easing];\n    var start = now() + delay;\n    var finish = start + duration;\n    if (duration === 0) {\n      this.step(1);\n      this.abort();\n    } else {\n      setTimeout(function () {\n        var loop = function () {\n          if (this$1._stopped) {\n            return;\n          }\n          var wallTime = now();\n          var time = limitValue(wallTime - start, 0, duration);\n          var position = time / duration;\n          var easingPosition = easing(position, time, 0, 1, duration);\n          this$1.step(easingPosition);\n          if (wallTime < finish) {\n            animationFrame(loop);\n          } else {\n            this$1.abort();\n          }\n        };\n        loop();\n      }, delay);\n    }\n  };\n  Animation.prototype.abort = function abort() {\n    this._stopped = true;\n  };\n  Animation.prototype.destroy = function destroy() {\n    this.abort();\n  };\n  Object.defineProperties(Animation.prototype, prototypeAccessors);\n  return Animation;\n}(Class);\nexport default Animation;", "map": {"version": 3, "names": ["easingFunctions", "limitValue", "now", "animationFrame", "Class", "AnimationFactory", "Animation", "element", "options", "call", "Object", "assign", "__proto__", "prototype", "create", "constructor", "prototypeAccessors", "configurable", "type", "current", "get", "_options", "duration", "easing", "set", "value", "setup", "step", "play", "this$1", "delay", "start", "finish", "abort", "setTimeout", "loop", "_stopped", "wallTime", "time", "position", "easingPosition", "destroy", "defineProperties"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/animations/animation.js"], "sourcesContent": ["import * as easingFunctions from './easing-functions';\nimport { limitValue, now } from '../util';\nimport { animationFrame, Class } from '../common';\nimport AnimationFactory from './animation-factory';\n\nvar Animation = (function (Class) {\n    function Animation(element, options) {\n        Class.call(this);\n\n        this.options = Object.assign({}, this.options, options);\n        this.element = element;\n    }\n\n    if ( Class ) Animation.__proto__ = Class;\n    Animation.prototype = Object.create( Class && Class.prototype );\n    Animation.prototype.constructor = Animation;\n\n    var prototypeAccessors = { options: { configurable: true } };\n\n    Animation.create = function create (type, element, options) {\n        return AnimationFactory.current.create(type, element, options);\n    };\n\n    prototypeAccessors.options.get = function () {\n        return this._options || {\n            duration: 500,\n            easing: \"swing\"\n        };\n    };\n\n    prototypeAccessors.options.set = function (value) {\n        this._options = value;\n    };\n\n    Animation.prototype.setup = function setup () {};\n    Animation.prototype.step = function step () {};\n\n    Animation.prototype.play = function play () {\n        var this$1 = this;\n\n        var options = this.options;\n        var duration = options.duration;\n        var delay = options.delay; if ( delay === void 0 ) delay = 0;\n        var easing = easingFunctions[options.easing];\n        var start = now() + delay;\n        var finish = start + duration;\n\n        if (duration === 0) {\n            this.step(1);\n            this.abort();\n        } else {\n            setTimeout(function () {\n                var loop = function () {\n                    if (this$1._stopped) {\n                        return;\n                    }\n\n                    var wallTime = now();\n\n                    var time = limitValue(wallTime - start, 0, duration);\n                    var position = time / duration;\n                    var easingPosition = easing(position, time, 0, 1, duration);\n\n                    this$1.step(easingPosition);\n\n                    if (wallTime < finish) {\n                        animationFrame(loop);\n                    } else {\n                        this$1.abort();\n                    }\n                };\n\n                loop();\n            }, delay);\n        }\n    };\n\n    Animation.prototype.abort = function abort () {\n        this._stopped = true;\n    };\n\n    Animation.prototype.destroy = function destroy () {\n        this.abort();\n    };\n\n    Object.defineProperties( Animation.prototype, prototypeAccessors );\n\n    return Animation;\n}(Class));\n\nexport default Animation;\n"], "mappings": "AAAA,OAAO,KAAKA,eAAe,MAAM,oBAAoB;AACrD,SAASC,UAAU,EAAEC,GAAG,QAAQ,SAAS;AACzC,SAASC,cAAc,EAAEC,KAAK,QAAQ,WAAW;AACjD,OAAOC,gBAAgB,MAAM,qBAAqB;AAElD,IAAIC,SAAS,GAAI,UAAUF,KAAK,EAAE;EAC9B,SAASE,SAASA,CAACC,OAAO,EAAEC,OAAO,EAAE;IACjCJ,KAAK,CAACK,IAAI,CAAC,IAAI,CAAC;IAEhB,IAAI,CAACD,OAAO,GAAGE,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACH,OAAO,EAAEA,OAAO,CAAC;IACvD,IAAI,CAACD,OAAO,GAAGA,OAAO;EAC1B;EAEA,IAAKH,KAAK,EAAGE,SAAS,CAACM,SAAS,GAAGR,KAAK;EACxCE,SAAS,CAACO,SAAS,GAAGH,MAAM,CAACI,MAAM,CAAEV,KAAK,IAAIA,KAAK,CAACS,SAAU,CAAC;EAC/DP,SAAS,CAACO,SAAS,CAACE,WAAW,GAAGT,SAAS;EAE3C,IAAIU,kBAAkB,GAAG;IAAER,OAAO,EAAE;MAAES,YAAY,EAAE;IAAK;EAAE,CAAC;EAE5DX,SAAS,CAACQ,MAAM,GAAG,SAASA,MAAMA,CAAEI,IAAI,EAAEX,OAAO,EAAEC,OAAO,EAAE;IACxD,OAAOH,gBAAgB,CAACc,OAAO,CAACL,MAAM,CAACI,IAAI,EAAEX,OAAO,EAAEC,OAAO,CAAC;EAClE,CAAC;EAEDQ,kBAAkB,CAACR,OAAO,CAACY,GAAG,GAAG,YAAY;IACzC,OAAO,IAAI,CAACC,QAAQ,IAAI;MACpBC,QAAQ,EAAE,GAAG;MACbC,MAAM,EAAE;IACZ,CAAC;EACL,CAAC;EAEDP,kBAAkB,CAACR,OAAO,CAACgB,GAAG,GAAG,UAAUC,KAAK,EAAE;IAC9C,IAAI,CAACJ,QAAQ,GAAGI,KAAK;EACzB,CAAC;EAEDnB,SAAS,CAACO,SAAS,CAACa,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAI,CAAC,CAAC;EAChDpB,SAAS,CAACO,SAAS,CAACc,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAI,CAAC,CAAC;EAE9CrB,SAAS,CAACO,SAAS,CAACe,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAI;IACxC,IAAIC,MAAM,GAAG,IAAI;IAEjB,IAAIrB,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,IAAIc,QAAQ,GAAGd,OAAO,CAACc,QAAQ;IAC/B,IAAIQ,KAAK,GAAGtB,OAAO,CAACsB,KAAK;IAAE,IAAKA,KAAK,KAAK,KAAK,CAAC,EAAGA,KAAK,GAAG,CAAC;IAC5D,IAAIP,MAAM,GAAGvB,eAAe,CAACQ,OAAO,CAACe,MAAM,CAAC;IAC5C,IAAIQ,KAAK,GAAG7B,GAAG,CAAC,CAAC,GAAG4B,KAAK;IACzB,IAAIE,MAAM,GAAGD,KAAK,GAAGT,QAAQ;IAE7B,IAAIA,QAAQ,KAAK,CAAC,EAAE;MAChB,IAAI,CAACK,IAAI,CAAC,CAAC,CAAC;MACZ,IAAI,CAACM,KAAK,CAAC,CAAC;IAChB,CAAC,MAAM;MACHC,UAAU,CAAC,YAAY;QACnB,IAAIC,IAAI,GAAG,SAAAA,CAAA,EAAY;UACnB,IAAIN,MAAM,CAACO,QAAQ,EAAE;YACjB;UACJ;UAEA,IAAIC,QAAQ,GAAGnC,GAAG,CAAC,CAAC;UAEpB,IAAIoC,IAAI,GAAGrC,UAAU,CAACoC,QAAQ,GAAGN,KAAK,EAAE,CAAC,EAAET,QAAQ,CAAC;UACpD,IAAIiB,QAAQ,GAAGD,IAAI,GAAGhB,QAAQ;UAC9B,IAAIkB,cAAc,GAAGjB,MAAM,CAACgB,QAAQ,EAAED,IAAI,EAAE,CAAC,EAAE,CAAC,EAAEhB,QAAQ,CAAC;UAE3DO,MAAM,CAACF,IAAI,CAACa,cAAc,CAAC;UAE3B,IAAIH,QAAQ,GAAGL,MAAM,EAAE;YACnB7B,cAAc,CAACgC,IAAI,CAAC;UACxB,CAAC,MAAM;YACHN,MAAM,CAACI,KAAK,CAAC,CAAC;UAClB;QACJ,CAAC;QAEDE,IAAI,CAAC,CAAC;MACV,CAAC,EAAEL,KAAK,CAAC;IACb;EACJ,CAAC;EAEDxB,SAAS,CAACO,SAAS,CAACoB,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAI;IAC1C,IAAI,CAACG,QAAQ,GAAG,IAAI;EACxB,CAAC;EAED9B,SAAS,CAACO,SAAS,CAAC4B,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;IAC9C,IAAI,CAACR,KAAK,CAAC,CAAC;EAChB,CAAC;EAEDvB,MAAM,CAACgC,gBAAgB,CAAEpC,SAAS,CAACO,SAAS,EAAEG,kBAAmB,CAAC;EAElE,OAAOV,SAAS;AACpB,CAAC,CAACF,KAAK,CAAE;AAET,eAAeE,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}