{"ast": null, "code": "/* eslint-disable no-multi-spaces, key-spacing, indent, camelcase, space-before-blocks, eqeqeq, brace-style */\n/* eslint-disable space-infix-ops, space-before-function-paren, array-bracket-spacing, object-curly-spacing */\n/* eslint-disable no-nested-ternary, max-params, default-case, no-else-return, no-empty */\n/* eslint-disable no-param-reassign, no-var, block-scoped-var */\n\n/*****************************************************************************\\\n *\n * The code in this file, although written from scratch, is influenced by the\n * TrueType parser/encoder in PDFKit -- http://pdfkit.org/ (a CoffeeScript\n * library for producing PDF files).\n *\n * PDFKit is (c) Devon Govett 2014 and released under the MIT License.\n *\n\\*****************************************************************************/\n\nimport { BinaryStream, ucs2decode } from \"./utils\";\nfunction hasOwnProperty(obj, key) {\n  return Object.prototype.hasOwnProperty.call(obj, key);\n}\nfunction sortedKeys(obj) {\n  return Object.keys(obj).sort(function (a, b) {\n    return a - b;\n  }).map(parseFloat);\n}\n\n///\nvar Directory = function Directory(data) {\n  this.raw = data;\n  this.scalerType = data.readLong();\n  this.tableCount = data.readShort();\n  this.searchRange = data.readShort();\n  this.entrySelector = data.readShort();\n  this.rangeShift = data.readShort();\n  var tables = this.tables = {};\n  for (var i = 0; i < this.tableCount; ++i) {\n    var entry = {\n      tag: data.readString(4),\n      checksum: data.readLong(),\n      offset: data.readLong(),\n      length: data.readLong()\n    };\n    tables[entry.tag] = entry;\n  }\n};\nDirectory.prototype.readTable = function readTable(name, Ctor) {\n  var def = this.tables[name];\n  if (!def) {\n    throw new Error(\"Table \" + name + \" not found in directory\");\n  }\n  return this[name] = def.table = new Ctor(this, def);\n};\nDirectory.prototype.render = function render(tables) {\n  var this$1 = this;\n  var tableCount = Object.keys(tables).length;\n  var maxpow2 = Math.pow(2, Math.floor(Math.log(tableCount) / Math.LN2));\n  var searchRange = maxpow2 * 16;\n  var entrySelector = Math.floor(Math.log(maxpow2) / Math.LN2);\n  var rangeShift = tableCount * 16 - searchRange;\n  var out = BinaryStream();\n  out.writeLong(this.scalerType);\n  out.writeShort(tableCount);\n  out.writeShort(searchRange);\n  out.writeShort(entrySelector);\n  out.writeShort(rangeShift);\n  var directoryLength = tableCount * 16;\n  var offset = out.offset() + directoryLength;\n  var headOffset = null;\n  var tableData = BinaryStream();\n  for (var tag in tables) {\n    if (hasOwnProperty(tables, tag)) {\n      var table = tables[tag];\n      out.writeString(tag);\n      out.writeLong(this$1.checksum(table));\n      out.writeLong(offset);\n      out.writeLong(table.length);\n      tableData.write(table);\n      if (tag == \"head\") {\n        headOffset = offset;\n      }\n      offset += table.length;\n      while (offset % 4) {\n        tableData.writeByte(0);\n        offset++;\n      }\n    }\n  }\n  out.write(tableData.get());\n  var sum = this.checksum(out.get());\n  var adjustment = 0xB1B0AFBA - sum;\n  out.offset(headOffset + 8);\n  out.writeLong(adjustment);\n  return out.get();\n};\nDirectory.prototype.checksum = function checksum(data) {\n  data = BinaryStream(data);\n  var sum = 0;\n  while (!data.eof()) {\n    sum += data.readLong();\n  }\n  return sum & 0xFFFFFFFF;\n};\nvar Table = function Table(file, def) {\n  this.definition = def;\n  this.length = def.length;\n  this.offset = def.offset;\n  this.file = file;\n  this.rawData = file.raw;\n  this.parse(file.raw);\n};\nTable.prototype.raw = function raw() {\n  return this.rawData.slice(this.offset, this.length);\n};\nTable.prototype.parse = function parse() {};\nvar HeadTable = function (Table) {\n  function HeadTable() {\n    Table.apply(this, arguments);\n  }\n  if (Table) HeadTable.__proto__ = Table;\n  HeadTable.prototype = Object.create(Table && Table.prototype);\n  HeadTable.prototype.constructor = HeadTable;\n  HeadTable.prototype.parse = function parse(data) {\n    data.offset(this.offset);\n    this.version = data.readLong();\n    this.revision = data.readLong();\n    this.checkSumAdjustment = data.readLong();\n    this.magicNumber = data.readLong();\n    this.flags = data.readShort();\n    this.unitsPerEm = data.readShort();\n    this.created = data.read(8);\n    this.modified = data.read(8);\n    this.xMin = data.readShort_();\n    this.yMin = data.readShort_();\n    this.xMax = data.readShort_();\n    this.yMax = data.readShort_();\n    this.macStyle = data.readShort();\n    this.lowestRecPPEM = data.readShort();\n    this.fontDirectionHint = data.readShort_();\n    this.indexToLocFormat = data.readShort_();\n    this.glyphDataFormat = data.readShort_();\n  };\n  HeadTable.prototype.render = function render(indexToLocFormat) {\n    var out = BinaryStream();\n    out.writeLong(this.version);\n    out.writeLong(this.revision);\n    out.writeLong(0); // checksum adjustment; shall be computed later\n    out.writeLong(this.magicNumber);\n    out.writeShort(this.flags);\n    out.writeShort(this.unitsPerEm);\n    out.write(this.created);\n    out.write(this.modified);\n    out.writeShort_(this.xMin);\n    out.writeShort_(this.yMin);\n    out.writeShort_(this.xMax);\n    out.writeShort_(this.yMax);\n    out.writeShort(this.macStyle);\n    out.writeShort(this.lowestRecPPEM);\n    out.writeShort_(this.fontDirectionHint);\n    out.writeShort_(indexToLocFormat); // this will depend on the `loca` table\n    out.writeShort_(this.glyphDataFormat);\n    return out.get();\n  };\n  return HeadTable;\n}(Table);\nvar LocaTable = function (Table) {\n  function LocaTable() {\n    Table.apply(this, arguments);\n  }\n  if (Table) LocaTable.__proto__ = Table;\n  LocaTable.prototype = Object.create(Table && Table.prototype);\n  LocaTable.prototype.constructor = LocaTable;\n  LocaTable.prototype.parse = function parse(data) {\n    data.offset(this.offset);\n    var format = this.file.head.indexToLocFormat;\n    if (format === 0) {\n      this.offsets = data.times(this.length / 2, function () {\n        return 2 * data.readShort();\n      });\n    } else {\n      this.offsets = data.times(this.length / 4, data.readLong);\n    }\n  };\n  LocaTable.prototype.offsetOf = function offsetOf(id) {\n    return this.offsets[id];\n  };\n  LocaTable.prototype.lengthOf = function lengthOf(id) {\n    return this.offsets[id + 1] - this.offsets[id];\n  };\n  LocaTable.prototype.render = function render(offsets) {\n    var out = BinaryStream();\n    var needsLongFormat = offsets[offsets.length - 1] > 0xFFFF;\n    for (var i = 0; i < offsets.length; ++i) {\n      if (needsLongFormat) {\n        out.writeLong(offsets[i]);\n      } else {\n        out.writeShort(offsets[i] / 2);\n      }\n    }\n    return {\n      format: needsLongFormat ? 1 : 0,\n      table: out.get()\n    };\n  };\n  return LocaTable;\n}(Table);\nvar HheaTable = function (Table) {\n  function HheaTable() {\n    Table.apply(this, arguments);\n  }\n  if (Table) HheaTable.__proto__ = Table;\n  HheaTable.prototype = Object.create(Table && Table.prototype);\n  HheaTable.prototype.constructor = HheaTable;\n  HheaTable.prototype.parse = function parse(data) {\n    data.offset(this.offset);\n    this.version = data.readLong();\n    this.ascent = data.readShort_();\n    this.descent = data.readShort_();\n    this.lineGap = data.readShort_();\n    this.advanceWidthMax = data.readShort();\n    this.minLeftSideBearing = data.readShort_();\n    this.minRightSideBearing = data.readShort_();\n    this.xMaxExtent = data.readShort_();\n    this.caretSlopeRise = data.readShort_();\n    this.caretSlopeRun = data.readShort_();\n    this.caretOffset = data.readShort_();\n    data.skip(4 * 2); // reserved\n\n    this.metricDataFormat = data.readShort_();\n    this.numOfLongHorMetrics = data.readShort();\n  };\n  HheaTable.prototype.render = function render(ids) {\n    var out = BinaryStream();\n    out.writeLong(this.version);\n    out.writeShort_(this.ascent);\n    out.writeShort_(this.descent);\n    out.writeShort_(this.lineGap);\n    out.writeShort(this.advanceWidthMax);\n    out.writeShort_(this.minLeftSideBearing);\n    out.writeShort_(this.minRightSideBearing);\n    out.writeShort_(this.xMaxExtent);\n    out.writeShort_(this.caretSlopeRise);\n    out.writeShort_(this.caretSlopeRun);\n    out.writeShort_(this.caretOffset);\n    out.write([0, 0, 0, 0, 0, 0, 0, 0]); // reserved bytes\n\n    out.writeShort_(this.metricDataFormat);\n    out.writeShort(ids.length);\n    return out.get();\n  };\n  return HheaTable;\n}(Table);\nvar MaxpTable = function (Table) {\n  function MaxpTable() {\n    Table.apply(this, arguments);\n  }\n  if (Table) MaxpTable.__proto__ = Table;\n  MaxpTable.prototype = Object.create(Table && Table.prototype);\n  MaxpTable.prototype.constructor = MaxpTable;\n  MaxpTable.prototype.parse = function parse(data) {\n    data.offset(this.offset);\n    this.version = data.readLong();\n    this.numGlyphs = data.readShort();\n    this.maxPoints = data.readShort();\n    this.maxContours = data.readShort();\n    this.maxComponentPoints = data.readShort();\n    this.maxComponentContours = data.readShort();\n    this.maxZones = data.readShort();\n    this.maxTwilightPoints = data.readShort();\n    this.maxStorage = data.readShort();\n    this.maxFunctionDefs = data.readShort();\n    this.maxInstructionDefs = data.readShort();\n    this.maxStackElements = data.readShort();\n    this.maxSizeOfInstructions = data.readShort();\n    this.maxComponentElements = data.readShort();\n    this.maxComponentDepth = data.readShort();\n  };\n  MaxpTable.prototype.render = function render(glyphIds) {\n    var out = BinaryStream();\n    out.writeLong(this.version);\n    out.writeShort(glyphIds.length);\n    out.writeShort(this.maxPoints);\n    out.writeShort(this.maxContours);\n    out.writeShort(this.maxComponentPoints);\n    out.writeShort(this.maxComponentContours);\n    out.writeShort(this.maxZones);\n    out.writeShort(this.maxTwilightPoints);\n    out.writeShort(this.maxStorage);\n    out.writeShort(this.maxFunctionDefs);\n    out.writeShort(this.maxInstructionDefs);\n    out.writeShort(this.maxStackElements);\n    out.writeShort(this.maxSizeOfInstructions);\n    out.writeShort(this.maxComponentElements);\n    out.writeShort(this.maxComponentDepth);\n    return out.get();\n  };\n  return MaxpTable;\n}(Table);\nvar HmtxTable = function (Table) {\n  function HmtxTable() {\n    Table.apply(this, arguments);\n  }\n  if (Table) HmtxTable.__proto__ = Table;\n  HmtxTable.prototype = Object.create(Table && Table.prototype);\n  HmtxTable.prototype.constructor = HmtxTable;\n  HmtxTable.prototype.parse = function parse(data) {\n    data.offset(this.offset);\n    var dir = this.file,\n      hhea = dir.hhea;\n    this.metrics = data.times(hhea.numOfLongHorMetrics, function () {\n      return {\n        advance: data.readShort(),\n        lsb: data.readShort_()\n      };\n    });\n    var lsbCount = dir.maxp.numGlyphs - dir.hhea.numOfLongHorMetrics;\n    this.leftSideBearings = data.times(lsbCount, data.readShort_);\n  };\n  HmtxTable.prototype.forGlyph = function forGlyph(id) {\n    var metrics = this.metrics;\n    var n = metrics.length;\n    if (id < n) {\n      return metrics[id];\n    }\n    return {\n      advance: metrics[n - 1].advance,\n      lsb: this.leftSideBearings[id - n]\n    };\n  };\n  HmtxTable.prototype.render = function render(glyphIds) {\n    var this$1 = this;\n    var out = BinaryStream();\n    for (var i = 0; i < glyphIds.length; ++i) {\n      var m = this$1.forGlyph(glyphIds[i]);\n      out.writeShort(m.advance);\n      out.writeShort_(m.lsb);\n    }\n    return out.get();\n  };\n  return HmtxTable;\n}(Table);\nvar GlyfTable = function () {\n  var SimpleGlyph = function SimpleGlyph(raw) {\n    this.raw = raw;\n  };\n  var prototypeAccessors = {\n    compound: {\n      configurable: true\n    }\n  };\n  prototypeAccessors.compound.get = function () {\n    return false;\n  };\n  SimpleGlyph.prototype.render = function render() {\n    return this.raw.get();\n  };\n  Object.defineProperties(SimpleGlyph.prototype, prototypeAccessors);\n  var ARG_1_AND_2_ARE_WORDS = 0x0001;\n  var WE_HAVE_A_SCALE = 0x0008;\n  var MORE_COMPONENTS = 0x0020;\n  var WE_HAVE_AN_X_AND_Y_SCALE = 0x0040;\n  var WE_HAVE_A_TWO_BY_TWO = 0x0080;\n  //var WE_HAVE_INSTRUCTIONS      = 0x0100;\n\n  var CompoundGlyph = function CompoundGlyph(data) {\n    this.raw = data;\n    var ids = this.glyphIds = [];\n    var offsets = this.idOffsets = [];\n    while (true) {\n      // eslint-disable-line no-constant-condition\n      var flags = data.readShort();\n      offsets.push(data.offset());\n      ids.push(data.readShort());\n      if (!(flags & MORE_COMPONENTS)) {\n        break;\n      }\n      data.skip(flags & ARG_1_AND_2_ARE_WORDS ? 4 : 2);\n      if (flags & WE_HAVE_A_TWO_BY_TWO) {\n        data.skip(8);\n      } else if (flags & WE_HAVE_AN_X_AND_Y_SCALE) {\n        data.skip(4);\n      } else if (flags & WE_HAVE_A_SCALE) {\n        data.skip(2);\n      }\n    }\n  };\n  var prototypeAccessors$1 = {\n    compound: {\n      configurable: true\n    }\n  };\n  prototypeAccessors$1.compound.get = function () {\n    return true;\n  };\n  CompoundGlyph.prototype.render = function render(old2new) {\n    var this$1 = this;\n    var out = BinaryStream(this.raw.get());\n    for (var i = 0; i < this.glyphIds.length; ++i) {\n      var id = this$1.glyphIds[i];\n      out.offset(this$1.idOffsets[i]);\n      out.writeShort(old2new[id]);\n    }\n    return out.get();\n  };\n  Object.defineProperties(CompoundGlyph.prototype, prototypeAccessors$1);\n  return function (Table) {\n    function anonymous() {\n      Table.apply(this, arguments);\n    }\n    if (Table) anonymous.__proto__ = Table;\n    anonymous.prototype = Object.create(Table && Table.prototype);\n    anonymous.prototype.constructor = anonymous;\n    anonymous.prototype.parse = function parse() {\n      this.cache = {};\n    };\n    anonymous.prototype.glyphFor = function glyphFor(id) {\n      var cache = this.cache;\n      if (hasOwnProperty(cache, id)) {\n        return cache[id];\n      }\n      var loca = this.file.loca;\n      var length = loca.lengthOf(id);\n      if (length === 0) {\n        return cache[id] = null;\n      }\n      var data = this.rawData;\n      var offset = this.offset + loca.offsetOf(id);\n      var raw = BinaryStream(data.slice(offset, length));\n      var numberOfContours = raw.readShort_();\n      var xMin = raw.readShort_();\n      var yMin = raw.readShort_();\n      var xMax = raw.readShort_();\n      var yMax = raw.readShort_();\n      var glyph = cache[id] = numberOfContours < 0 ? new CompoundGlyph(raw) : new SimpleGlyph(raw);\n      glyph.numberOfContours = numberOfContours;\n      glyph.xMin = xMin;\n      glyph.yMin = yMin;\n      glyph.xMax = xMax;\n      glyph.yMax = yMax;\n      return glyph;\n    };\n    anonymous.prototype.render = function render(glyphs, oldIds, old2new) {\n      var out = BinaryStream(),\n        offsets = [];\n      for (var i = 0; i < oldIds.length; ++i) {\n        var id = oldIds[i];\n        var glyph = glyphs[id];\n        if (out.offset() % 2) {\n          out.writeByte(0);\n        }\n        offsets.push(out.offset());\n        if (glyph) {\n          out.write(glyph.render(old2new));\n        }\n      }\n      if (out.offset() % 2) {\n        out.writeByte(0);\n      }\n      offsets.push(out.offset());\n      return {\n        table: out.get(),\n        offsets: offsets\n      };\n    };\n    return anonymous;\n  }(Table);\n}();\nvar NameTable = function () {\n  var NameEntry = function NameEntry(text, entry) {\n    this.text = text;\n    this.length = text.length;\n    this.platformID = entry.platformID;\n    this.platformSpecificID = entry.platformSpecificID;\n    this.languageID = entry.languageID;\n    this.nameID = entry.nameID;\n  };\n  return function (Table) {\n    function anonymous() {\n      Table.apply(this, arguments);\n    }\n    if (Table) anonymous.__proto__ = Table;\n    anonymous.prototype = Object.create(Table && Table.prototype);\n    anonymous.prototype.constructor = anonymous;\n    anonymous.prototype.parse = function parse(data) {\n      data.offset(this.offset);\n      data.readShort(); // format\n      var count = data.readShort();\n      var stringOffset = this.offset + data.readShort();\n      var nameRecords = data.times(count, function () {\n        return {\n          platformID: data.readShort(),\n          platformSpecificID: data.readShort(),\n          languageID: data.readShort(),\n          nameID: data.readShort(),\n          length: data.readShort(),\n          offset: data.readShort() + stringOffset\n        };\n      });\n      var strings = this.strings = {};\n      for (var i = 0; i < nameRecords.length; ++i) {\n        var rec = nameRecords[i];\n        data.offset(rec.offset);\n        var text = data.readString(rec.length);\n        if (!strings[rec.nameID]) {\n          strings[rec.nameID] = [];\n        }\n        strings[rec.nameID].push(new NameEntry(text, rec));\n      }\n      this.postscriptEntry = strings[6][0];\n      this.postscriptName = this.postscriptEntry.text.replace(/[^\\x20-\\x7F]/g, \"\");\n    };\n    anonymous.prototype.render = function render(psName) {\n      var this$1 = this;\n      var strings = this.strings;\n      var strCount = 0;\n      for (var i in strings) {\n        if (hasOwnProperty(strings, i)) {\n          strCount += strings[i].length;\n        }\n      }\n      var out = BinaryStream();\n      var strTable = BinaryStream();\n      out.writeShort(0); // format\n      out.writeShort(strCount);\n      out.writeShort(6 + 12 * strCount); // stringOffset\n\n      for (i in strings) {\n        if (hasOwnProperty(strings, i)) {\n          var list = i == 6 ? [new NameEntry(psName, this$1.postscriptEntry)] : strings[i];\n          for (var j = 0; j < list.length; ++j) {\n            var str = list[j];\n            out.writeShort(str.platformID);\n            out.writeShort(str.platformSpecificID);\n            out.writeShort(str.languageID);\n            out.writeShort(str.nameID);\n            out.writeShort(str.length);\n            out.writeShort(strTable.offset());\n            strTable.writeString(str.text);\n          }\n        }\n      }\n      out.write(strTable.get());\n      return out.get();\n    };\n    return anonymous;\n  }(Table);\n}();\nvar PostTable = function () {\n  var POSTSCRIPT_GLYPHS = \".notdef .null nonmarkingreturn space exclam quotedbl numbersign dollar percent ampersand quotesingle parenleft parenright asterisk plus comma hyphen period slash zero one two three four five six seven eight nine colon semicolon less equal greater question at A B C D E F G H I J K L M N O P Q R S T U V W X Y Z bracketleft backslash bracketright asciicircum underscore grave a b c d e f g h i j k l m n o p q r s t u v w x y z braceleft bar braceright asciitilde Adieresis Aring Ccedilla Eacute Ntilde Odieresis Udieresis aacute agrave acircumflex adieresis atilde aring ccedilla eacute egrave ecircumflex edieresis iacute igrave icircumflex idieresis ntilde oacute ograve ocircumflex odieresis otilde uacute ugrave ucircumflex udieresis dagger degree cent sterling section bullet paragraph germandbls registered copyright trademark acute dieresis notequal AE Oslash infinity plusminus lessequal greaterequal yen mu partialdiff summation product pi integral ordfeminine ordmasculine Omega ae oslash questiondown exclamdown logicalnot radical florin approxequal Delta guillemotleft guillemotright ellipsis nonbreakingspace Agrave Atilde Otilde OE oe endash emdash quotedblleft quotedblright quoteleft quoteright divide lozenge ydieresis Ydieresis fraction currency guilsinglleft guilsinglright fi fl daggerdbl periodcentered quotesinglbase quotedblbase perthousand Acircumflex Ecircumflex Aacute Edieresis Egrave Iacute Icircumflex Idieresis Igrave Oacute Ocircumflex apple Ograve Uacute Ucircumflex Ugrave dotlessi circumflex tilde macron breve dotaccent ring cedilla hungarumlaut ogonek caron Lslash lslash Scaron scaron Zcaron zcaron brokenbar Eth eth Yacute yacute Thorn thorn minus multiply onesuperior twosuperior threesuperior onehalf onequarter threequarters franc Gbreve gbreve Idotaccent Scedilla scedilla Cacute cacute Ccaron ccaron dcroat\".split(/\\s+/g);\n  return function (Table) {\n    function anonymous() {\n      Table.apply(this, arguments);\n    }\n    if (Table) anonymous.__proto__ = Table;\n    anonymous.prototype = Object.create(Table && Table.prototype);\n    anonymous.prototype.constructor = anonymous;\n    anonymous.prototype.parse = function parse(data) {\n      var this$1 = this;\n      data.offset(this.offset);\n      this.format = data.readLong();\n      this.italicAngle = data.readFixed_();\n      this.underlinePosition = data.readShort_();\n      this.underlineThickness = data.readShort_();\n      this.isFixedPitch = data.readLong();\n      this.minMemType42 = data.readLong();\n      this.maxMemType42 = data.readLong();\n      this.minMemType1 = data.readLong();\n      this.maxMemType1 = data.readLong();\n      var numberOfGlyphs;\n      switch (this.format) {\n        case 0x00010000:\n        case 0x00030000:\n          break;\n        case 0x00020000:\n          numberOfGlyphs = data.readShort();\n          this.glyphNameIndex = data.times(numberOfGlyphs, data.readShort);\n          this.names = [];\n          var limit = this.offset + this.length;\n          while (data.offset() < limit) {\n            this$1.names.push(data.readString(data.readByte()));\n          }\n          break;\n        case 0x00025000:\n          numberOfGlyphs = data.readShort();\n          this.offsets = data.read(numberOfGlyphs);\n          break;\n        case 0x00040000:\n          this.map = data.times(this.file.maxp.numGlyphs, data.readShort);\n          break;\n      }\n    };\n    anonymous.prototype.glyphFor = function glyphFor(code) {\n      switch (this.format) {\n        case 0x00010000:\n          return POSTSCRIPT_GLYPHS[code] || \".notdef\";\n        case 0x00020000:\n          var index = this.glyphNameIndex[code];\n          if (index < POSTSCRIPT_GLYPHS.length) {\n            return POSTSCRIPT_GLYPHS[index];\n          }\n          return this.names[index - POSTSCRIPT_GLYPHS.length] || \".notdef\";\n        case 0x00025000:\n        case 0x00030000:\n          return \".notdef\";\n        case 0x00040000:\n          return this.map[code] || 0xFFFF;\n      }\n    };\n    anonymous.prototype.render = function render(mapping) {\n      var this$1 = this;\n      if (this.format == 0x00030000) {\n        return this.raw();\n      }\n\n      // keep original header, but set format to 2.0\n      var out = BinaryStream(this.rawData.slice(this.offset, 32));\n      out.writeLong(0x00020000);\n      out.offset(32);\n      var indexes = [];\n      var strings = [];\n      for (var i = 0; i < mapping.length; ++i) {\n        var id = mapping[i];\n        var post = this$1.glyphFor(id);\n        var index = POSTSCRIPT_GLYPHS.indexOf(post);\n        if (index >= 0) {\n          indexes.push(index);\n        } else {\n          indexes.push(POSTSCRIPT_GLYPHS.length + strings.length);\n          strings.push(post);\n        }\n      }\n      out.writeShort(mapping.length);\n      for (i = 0; i < indexes.length; ++i) {\n        out.writeShort(indexes[i]);\n      }\n      for (i = 0; i < strings.length; ++i) {\n        out.writeByte(strings[i].length);\n        out.writeString(strings[i]);\n      }\n      return out.get();\n    };\n    return anonymous;\n  }(Table);\n}();\nvar CmapTable = function () {\n  var CmapEntry = function CmapEntry(data, offset, codeMap) {\n    var self = this;\n    self.platformID = data.readShort();\n    self.platformSpecificID = data.readShort();\n    self.offset = offset + data.readLong();\n    data.saveExcursion(function () {\n      var code;\n      data.offset(self.offset);\n      self.format = data.readShort();\n      switch (self.format) {\n        case 0:\n          self.length = data.readShort();\n          self.language = data.readShort();\n          for (var i = 0; i < 256; ++i) {\n            codeMap[i] = data.readByte();\n          }\n          break;\n        case 4:\n          self.length = data.readShort();\n          self.language = data.readShort();\n          var segCount = data.readShort() / 2;\n          data.skip(6); // searchRange, entrySelector, rangeShift\n          var endCode = data.times(segCount, data.readShort);\n          data.skip(2); // reserved pad\n          var startCode = data.times(segCount, data.readShort);\n          var idDelta = data.times(segCount, data.readShort_);\n          var idRangeOffset = data.times(segCount, data.readShort);\n          var count = (self.length + self.offset - data.offset()) / 2;\n          var glyphIds = data.times(count, data.readShort);\n          for (i = 0; i < segCount; ++i) {\n            var start = startCode[i],\n              end = endCode[i];\n            for (code = start; code <= end; ++code) {\n              var glyphId;\n              if (idRangeOffset[i] === 0) {\n                glyphId = code + idDelta[i];\n              } else {\n                ///\n                // When non-zero, idRangeOffset contains for each segment the byte offset of the Glyph ID\n                // into the glyphIds table, from the *current* `i` cell of idRangeOffset.  In other words,\n                // this offset spans from the first into the second array.  This works, because the arrays\n                // are consecutive in the TTF file:\n                //\n                // [ ...idRangeOffset... ][ ...glyphIds... ]\n                //   ...... 48 ......   .... ID ....\n                //          ^----- 48 bytes -----^\n                //\n                // (but I can't stop wondering why is it not just a plain index, possibly incremented by 1\n                // so that we can have that special `zero` value.)\n                //\n                // The elements of idRangeOffset are even numbers, because both arrays contain 16-bit words,\n                // yet the offset is in bytes.  That is why we divide it by 2.  Then we subtract the\n                // remaining segments (segCount-i), and add the code-start offset, to which we need to add\n                // the corresponding delta to get the actual glyph ID.\n                ///\n                var index = idRangeOffset[i] / 2 - (segCount - i) + (code - start);\n                glyphId = glyphIds[index] || 0;\n                if (glyphId !== 0) {\n                  glyphId += idDelta[i];\n                }\n              }\n              codeMap[code] = glyphId & 0xFFFF;\n            }\n          }\n          break;\n        case 6:\n          self.length = data.readShort();\n          self.language = data.readShort();\n          code = data.readShort();\n          var length = data.readShort();\n          while (length-- > 0) {\n            codeMap[code++] = data.readShort();\n          }\n          break;\n        case 12:\n          data.readShort(); // reserved\n          self.length = data.readLong();\n          self.language = data.readLong();\n          var ngroups = data.readLong();\n          while (ngroups-- > 0) {\n            code = data.readLong();\n            var endCharCode = data.readLong();\n            var glyphCode = data.readLong();\n            while (code <= endCharCode) {\n              codeMap[code++] = glyphCode++;\n            }\n          }\n          break;\n        default:\n          if (window.console) {\n            window.console.error(\"Unhandled CMAP format: \" + self.format);\n          }\n      }\n    });\n  };\n  function renderCharmap(ncid2ogid, ogid2ngid) {\n    var codes = sortedKeys(ncid2ogid);\n    var startCodes = [];\n    var endCodes = [];\n    var last = null;\n    var diff = null;\n    function new_gid(charcode) {\n      return ogid2ngid[ncid2ogid[charcode]];\n    }\n    for (var i = 0; i < codes.length; ++i) {\n      var code = codes[i];\n      var gid = new_gid(code);\n      var delta = gid - code;\n      if (last == null || delta !== diff) {\n        if (last) {\n          endCodes.push(last);\n        }\n        startCodes.push(code);\n        diff = delta;\n      }\n      last = code;\n    }\n    if (last) {\n      endCodes.push(last);\n    }\n    endCodes.push(0xFFFF);\n    startCodes.push(0xFFFF);\n    var segCount = startCodes.length;\n    var segCountX2 = segCount * 2;\n    var searchRange = 2 * Math.pow(2, Math.floor(Math.log(segCount) / Math.LN2));\n    var entrySelector = Math.log(searchRange / 2) / Math.LN2;\n    var rangeShift = segCountX2 - searchRange;\n    var deltas = [];\n    var rangeOffsets = [];\n    var glyphIds = [];\n    for (i = 0; i < segCount; ++i) {\n      var startCode = startCodes[i];\n      var endCode = endCodes[i];\n      if (startCode == 0xFFFF) {\n        deltas.push(0);\n        rangeOffsets.push(0);\n        break;\n      }\n      var startGlyph = new_gid(startCode);\n      if (startCode - startGlyph >= 0x8000) {\n        deltas.push(0);\n        rangeOffsets.push(2 * (glyphIds.length + segCount - i));\n        for (var j = startCode; j <= endCode; ++j) {\n          glyphIds.push(new_gid(j));\n        }\n      } else {\n        deltas.push(startGlyph - startCode);\n        rangeOffsets.push(0);\n      }\n    }\n    var out = BinaryStream();\n    out.writeShort(3); // platformID\n    out.writeShort(1); // platformSpecificID\n    out.writeLong(12); // offset\n    out.writeShort(4); // format\n    out.writeShort(16 + segCount * 8 + glyphIds.length * 2); // length\n    out.writeShort(0); // language\n    out.writeShort(segCountX2);\n    out.writeShort(searchRange);\n    out.writeShort(entrySelector);\n    out.writeShort(rangeShift);\n    endCodes.forEach(out.writeShort);\n    out.writeShort(0); // reserved pad\n    startCodes.forEach(out.writeShort);\n    deltas.forEach(out.writeShort_);\n    rangeOffsets.forEach(out.writeShort);\n    glyphIds.forEach(out.writeShort);\n    return out.get();\n  }\n  return function (Table) {\n    function anonymous() {\n      Table.apply(this, arguments);\n    }\n    if (Table) anonymous.__proto__ = Table;\n    anonymous.prototype = Object.create(Table && Table.prototype);\n    anonymous.prototype.constructor = anonymous;\n    anonymous.prototype.parse = function parse(data) {\n      var self = this;\n      var offset = self.offset;\n      data.offset(offset);\n      self.codeMap = {};\n      self.version = data.readShort();\n      var tableCount = data.readShort();\n      self.tables = data.times(tableCount, function () {\n        return new CmapEntry(data, offset, self.codeMap);\n      });\n    };\n    anonymous.render = function render(ncid2ogid, ogid2ngid) {\n      var out = BinaryStream();\n      out.writeShort(0); // version\n      out.writeShort(1); // tableCount\n      out.write(renderCharmap(ncid2ogid, ogid2ngid));\n      return out.get();\n    };\n    return anonymous;\n  }(Table);\n}();\nvar OS2Table = function (Table) {\n  function OS2Table() {\n    Table.apply(this, arguments);\n  }\n  if (Table) OS2Table.__proto__ = Table;\n  OS2Table.prototype = Object.create(Table && Table.prototype);\n  OS2Table.prototype.constructor = OS2Table;\n  OS2Table.prototype.parse = function parse(data) {\n    data.offset(this.offset);\n    this.version = data.readShort();\n    this.averageCharWidth = data.readShort_();\n    this.weightClass = data.readShort();\n    this.widthClass = data.readShort();\n    this.type = data.readShort();\n    this.ySubscriptXSize = data.readShort_();\n    this.ySubscriptYSize = data.readShort_();\n    this.ySubscriptXOffset = data.readShort_();\n    this.ySubscriptYOffset = data.readShort_();\n    this.ySuperscriptXSize = data.readShort_();\n    this.ySuperscriptYSize = data.readShort_();\n    this.ySuperscriptXOffset = data.readShort_();\n    this.ySuperscriptYOffset = data.readShort_();\n    this.yStrikeoutSize = data.readShort_();\n    this.yStrikeoutPosition = data.readShort_();\n    this.familyClass = data.readShort_();\n    this.panose = data.times(10, data.readByte);\n    this.charRange = data.times(4, data.readLong);\n    this.vendorID = data.readString(4);\n    this.selection = data.readShort();\n    this.firstCharIndex = data.readShort();\n    this.lastCharIndex = data.readShort();\n    if (this.version > 0) {\n      this.ascent = data.readShort_();\n      this.descent = data.readShort_();\n      this.lineGap = data.readShort_();\n      this.winAscent = data.readShort();\n      this.winDescent = data.readShort();\n      this.codePageRange = data.times(2, data.readLong);\n      if (this.version > 1) {\n        this.xHeight = data.readShort();\n        this.capHeight = data.readShort();\n        this.defaultChar = data.readShort();\n        this.breakChar = data.readShort();\n        this.maxContext = data.readShort();\n      }\n    }\n  };\n  OS2Table.prototype.render = function render() {\n    return this.raw();\n  };\n  return OS2Table;\n}(Table);\nvar subsetTag = 100000;\nfunction nextSubsetTag() {\n  var ret = \"\",\n    n = String(subsetTag);\n  for (var i = 0; i < n.length; ++i) {\n    ret += String.fromCharCode(n.charCodeAt(i) - 48 + 65);\n  }\n  ++subsetTag;\n  return ret;\n}\nvar Subfont = function Subfont(font) {\n  this.font = font;\n  this.subset = {};\n  this.unicodes = {};\n  this.ogid2ngid = {\n    0: 0\n  };\n  this.ngid2ogid = {\n    0: 0\n  };\n  this.ncid2ogid = {};\n  this.next = this.firstChar = 1;\n  this.nextGid = 1;\n  this.psName = nextSubsetTag() + \"+\" + this.font.psName;\n};\nSubfont.prototype.use = function use(ch) {\n  var self = this;\n  if (typeof ch == \"string\") {\n    return ucs2decode(ch).reduce(function (ret, code) {\n      return ret + String.fromCharCode(self.use(code));\n    }, \"\");\n  }\n  var code = self.unicodes[ch];\n  if (!code) {\n    code = self.next++;\n    self.subset[code] = ch;\n    self.unicodes[ch] = code;\n\n    // generate new GID (glyph ID) and maintain newGID ->\n    // oldGID and back mappings\n    var old_gid = self.font.cmap.codeMap[ch];\n    if (old_gid) {\n      self.ncid2ogid[code] = old_gid;\n      if (self.ogid2ngid[old_gid] == null) {\n        var new_gid = self.nextGid++;\n        self.ogid2ngid[old_gid] = new_gid;\n        self.ngid2ogid[new_gid] = old_gid;\n      }\n    }\n  }\n  return code;\n};\nSubfont.prototype.encodeText = function encodeText(text) {\n  return this.use(text);\n};\nSubfont.prototype.glyphIds = function glyphIds() {\n  return sortedKeys(this.ogid2ngid);\n};\nSubfont.prototype.glyphsFor = function glyphsFor(glyphIds, result) {\n  var this$1 = this;\n  if (!result) {\n    result = {};\n  }\n  for (var i = 0; i < glyphIds.length; ++i) {\n    var id = glyphIds[i];\n    if (!result[id]) {\n      var glyph = result[id] = this$1.font.glyf.glyphFor(id);\n      if (glyph && glyph.compound) {\n        this$1.glyphsFor(glyph.glyphIds, result);\n      }\n    }\n  }\n  return result;\n};\nSubfont.prototype.render = function render() {\n  var this$1 = this;\n  var glyphs = this.glyphsFor(this.glyphIds());\n\n  // add missing sub-glyphs\n  for (var old_gid in glyphs) {\n    if (hasOwnProperty(glyphs, old_gid)) {\n      old_gid = parseInt(old_gid, 10);\n      if (this$1.ogid2ngid[old_gid] == null) {\n        var new_gid = this$1.nextGid++;\n        this$1.ogid2ngid[old_gid] = new_gid;\n        this$1.ngid2ogid[new_gid] = old_gid;\n      }\n    }\n  }\n\n  // must obtain old_gid_ids in an order matching sorted\n  // new_gid_ids\n  var new_gid_ids = sortedKeys(this.ngid2ogid);\n  var old_gid_ids = new_gid_ids.map(function (id) {\n    return this.ngid2ogid[id];\n  }, this);\n  var font = this.font;\n  var glyf = font.glyf.render(glyphs, old_gid_ids, this.ogid2ngid);\n  var loca = font.loca.render(glyf.offsets);\n  this.lastChar = this.next - 1;\n  var tables = {\n    \"cmap\": CmapTable.render(this.ncid2ogid, this.ogid2ngid),\n    \"glyf\": glyf.table,\n    \"loca\": loca.table,\n    \"hmtx\": font.hmtx.render(old_gid_ids),\n    \"hhea\": font.hhea.render(old_gid_ids),\n    \"maxp\": font.maxp.render(old_gid_ids),\n    \"post\": font.post.render(old_gid_ids),\n    \"name\": font.name.render(this.psName),\n    \"head\": font.head.render(loca.format),\n    \"OS/2\": font.os2.render()\n  };\n  return this.font.directory.render(tables);\n};\nSubfont.prototype.cidToGidMap = function cidToGidMap() {\n  var this$1 = this;\n  var out = BinaryStream(),\n    len = 0;\n  for (var cid = this.firstChar; cid < this.next; ++cid) {\n    while (len < cid) {\n      out.writeShort(0);\n      len++;\n    }\n    var old_gid = this$1.ncid2ogid[cid];\n    if (old_gid) {\n      var new_gid = this$1.ogid2ngid[old_gid];\n      out.writeShort(new_gid);\n    } else {\n      out.writeShort(0);\n    }\n    len++;\n  }\n  return out.get();\n};\nvar TTFFont = function TTFFont(rawData, name) {\n  var self = this;\n  var data = self.contents = BinaryStream(rawData);\n  if (data.readString(4) == \"ttcf\") {\n    var offset;\n    var parse = function () {\n      data.offset(offset);\n      self.parse();\n    };\n    if (!name) {\n      throw new Error(\"Must specify a name for TTC files\");\n    }\n    data.readLong(); // version\n    var numFonts = data.readLong();\n    for (var i = 0; i < numFonts; ++i) {\n      offset = data.readLong();\n      data.saveExcursion(parse);\n      if (self.psName == name) {\n        return;\n      }\n    }\n    throw new Error(\"Font \" + name + \" not found in collection\");\n  } else {\n    data.offset(0);\n    self.parse();\n  }\n};\nTTFFont.prototype.parse = function parse() {\n  var dir = this.directory = new Directory(this.contents);\n  this.head = dir.readTable(\"head\", HeadTable);\n  this.loca = dir.readTable(\"loca\", LocaTable);\n  this.hhea = dir.readTable(\"hhea\", HheaTable);\n  this.maxp = dir.readTable(\"maxp\", MaxpTable);\n  this.hmtx = dir.readTable(\"hmtx\", HmtxTable);\n  this.glyf = dir.readTable(\"glyf\", GlyfTable);\n  this.name = dir.readTable(\"name\", NameTable);\n  this.post = dir.readTable(\"post\", PostTable);\n  this.cmap = dir.readTable(\"cmap\", CmapTable);\n  this.os2 = dir.readTable(\"OS/2\", OS2Table);\n  this.psName = this.name.postscriptName;\n  this.ascent = this.os2.ascent || this.hhea.ascent;\n  this.descent = this.os2.descent || this.hhea.descent;\n  this.lineGap = this.os2.lineGap || this.hhea.lineGap;\n  this.scale = 1000 / this.head.unitsPerEm;\n};\nTTFFont.prototype.widthOfGlyph = function widthOfGlyph(glyph) {\n  return this.hmtx.forGlyph(glyph).advance * this.scale;\n};\nTTFFont.prototype.makeSubset = function makeSubset() {\n  return new Subfont(this);\n};\nexport { TTFFont };", "map": {"version": 3, "names": ["BinaryStream", "ucs2decode", "hasOwnProperty", "obj", "key", "Object", "prototype", "call", "sortedKeys", "keys", "sort", "a", "b", "map", "parseFloat", "Directory", "data", "raw", "scalerType", "readLong", "tableCount", "readShort", "searchRange", "entrySelector", "rangeShift", "tables", "i", "entry", "tag", "readString", "checksum", "offset", "length", "readTable", "name", "Ctor", "def", "Error", "table", "render", "this$1", "maxpow2", "Math", "pow", "floor", "log", "LN2", "out", "writeLong", "writeShort", "directoryLength", "headOffset", "tableData", "writeString", "write", "writeByte", "get", "sum", "adjustment", "eof", "Table", "file", "definition", "rawData", "parse", "slice", "HeadTable", "apply", "arguments", "__proto__", "create", "constructor", "version", "revision", "checkSumAdjustment", "magicNumber", "flags", "unitsPerEm", "created", "read", "modified", "xMin", "readShort_", "yMin", "xMax", "yMax", "macStyle", "lowestRecPPEM", "fontDirectionHint", "indexToLocFormat", "glyphDataFormat", "writeShort_", "LocaTable", "format", "head", "offsets", "times", "offsetOf", "id", "lengthOf", "needsLongFormat", "HheaTable", "ascent", "descent", "lineGap", "advanceWidthMax", "minLeftSideBearing", "minRightSideBearing", "xMaxExtent", "caretSlopeRise", "caretSlopeRun", "caretOffset", "skip", "metricDataFormat", "numOfLongHorMetrics", "ids", "MaxpTable", "numGlyphs", "maxPoints", "maxContours", "maxComponentPoints", "maxComponentContours", "maxZones", "maxTwilightPoints", "maxStorage", "maxFunctionDefs", "maxInstructionDefs", "maxStackElements", "maxSizeOfInstructions", "maxComponentElements", "max<PERSON>ompo<PERSON><PERSON><PERSON><PERSON>", "glyphIds", "HmtxTable", "dir", "hhea", "metrics", "advance", "lsb", "lsbCount", "maxp", "leftSideBearings", "forGlyph", "n", "m", "GlyfTable", "SimpleGlyph", "prototypeAccessors", "compound", "configurable", "defineProperties", "ARG_1_AND_2_ARE_WORDS", "WE_HAVE_A_SCALE", "MORE_COMPONENTS", "WE_HAVE_AN_X_AND_Y_SCALE", "WE_HAVE_A_TWO_BY_TWO", "CompoundGlyph", "idOffsets", "push", "prototypeAccessors$1", "old2new", "anonymous", "cache", "glyphFor", "loca", "numberOfContours", "glyph", "glyphs", "oldIds", "NameTable", "NameEntry", "text", "platformID", "platformSpecificID", "languageID", "nameID", "count", "stringOffset", "nameRecords", "strings", "rec", "postscriptEntry", "postscriptName", "replace", "psName", "strCount", "strTable", "list", "j", "str", "PostTable", "POSTSCRIPT_GLYPHS", "split", "italicAngle", "readFixed_", "underlinePosition", "underlineThickness", "isFixedPitch", "minMemType42", "maxMemType42", "minMemType1", "maxMemType1", "numberOfGlyphs", "glyphNameIndex", "names", "limit", "readByte", "code", "index", "mapping", "indexes", "post", "indexOf", "CmapTable", "CmapEntry", "codeMap", "self", "saveExcursion", "language", "segCount", "endCode", "startCode", "id<PERSON><PERSON><PERSON>", "idRangeOffset", "start", "end", "glyphId", "ngroups", "endCharCode", "glyphCode", "window", "console", "error", "renderCharmap", "ncid2ogid", "ogid2ngid", "codes", "startCodes", "endCodes", "last", "diff", "new_gid", "charcode", "gid", "delta", "segCountX2", "deltas", "rangeOffsets", "startGlyph", "for<PERSON>ach", "OS2Table", "averageCharWidth", "weightClass", "widthClass", "type", "ySubscriptXSize", "ySubscriptYSize", "ySubscriptXOffset", "ySubscriptYOffset", "ySuperscriptXSize", "ySuperscriptYSize", "ySuperscriptXOffset", "ySuperscriptYOffset", "yStrikeoutSize", "yStrikeoutPosition", "familyClass", "panose", "char<PERSON><PERSON><PERSON>", "vendorID", "selection", "firstCharIndex", "lastCharIndex", "winAscent", "winDescent", "codePageRange", "xHeight", "capHeight", "defaultChar", "breakChar", "max<PERSON><PERSON><PERSON>t", "subsetTag", "nextSubsetTag", "ret", "String", "fromCharCode", "charCodeAt", "Subfont", "font", "subset", "unicodes", "ngid2ogid", "next", "firstChar", "nextGid", "use", "ch", "reduce", "old_gid", "cmap", "encodeText", "glyphsFor", "result", "glyf", "parseInt", "new_gid_ids", "old_gid_ids", "lastChar", "hmtx", "os2", "directory", "cidToGidMap", "len", "cid", "TTFFont", "contents", "numFonts", "scale", "widthOfGlyph", "makeSubset"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/pdf/ttf.js"], "sourcesContent": ["/* eslint-disable no-multi-spaces, key-spacing, indent, camelcase, space-before-blocks, eqeqeq, brace-style */\n/* eslint-disable space-infix-ops, space-before-function-paren, array-bracket-spacing, object-curly-spacing */\n/* eslint-disable no-nested-ternary, max-params, default-case, no-else-return, no-empty */\n/* eslint-disable no-param-reassign, no-var, block-scoped-var */\n\n/*****************************************************************************\\\n *\n * The code in this file, although written from scratch, is influenced by the\n * TrueType parser/encoder in PDFKit -- http://pdfkit.org/ (a CoffeeScript\n * library for producing PDF files).\n *\n * PDFKit is (c) Devon Govett 2014 and released under the MIT License.\n *\n\\*****************************************************************************/\n\nimport { BinaryStream, ucs2decode } from \"./utils\";\n\nfunction hasOwnProperty(obj, key) {\n    return Object.prototype.hasOwnProperty.call(obj, key);\n}\n\nfunction sortedKeys(obj) {\n    return Object.keys(obj).sort(function(a, b){ return a - b; }).map(parseFloat);\n}\n\n///\nvar Directory = function Directory(data) {\n    this.raw = data;\n    this.scalerType = data.readLong();\n    this.tableCount = data.readShort();\n    this.searchRange = data.readShort();\n    this.entrySelector = data.readShort();\n    this.rangeShift = data.readShort();\n\n    var tables = this.tables = {};\n    for (var i = 0; i < this.tableCount; ++i) {\n        var entry = {\n            tag  : data.readString(4),\n            checksum : data.readLong(),\n            offset   : data.readLong(),\n            length   : data.readLong()\n        };\n        tables[entry.tag] = entry;\n    }\n};\n\nDirectory.prototype.readTable = function readTable (name, Ctor) {\n    var def = this.tables[name];\n    if (!def) {\n        throw new Error(\"Table \" + name + \" not found in directory\");\n    }\n    return (this[name] = def.table = new Ctor(this, def));\n};\n\nDirectory.prototype.render = function render (tables) {\n        var this$1 = this;\n\n    var tableCount = Object.keys(tables).length;\n\n    var maxpow2 = Math.pow(2, Math.floor(Math.log(tableCount) / Math.LN2));\n    var searchRange = maxpow2 * 16;\n    var entrySelector = Math.floor(Math.log(maxpow2) / Math.LN2);\n    var rangeShift = tableCount * 16 - searchRange;\n\n    var out = BinaryStream();\n    out.writeLong(this.scalerType);\n    out.writeShort(tableCount);\n    out.writeShort(searchRange);\n    out.writeShort(entrySelector);\n    out.writeShort(rangeShift);\n\n    var directoryLength = tableCount * 16;\n    var offset = out.offset() + directoryLength;\n    var headOffset = null;\n    var tableData = BinaryStream();\n\n    for (var tag in tables) {\n        if (hasOwnProperty(tables, tag)) {\n            var table = tables[tag];\n\n            out.writeString(tag);\n            out.writeLong(this$1.checksum(table));\n            out.writeLong(offset);\n            out.writeLong(table.length);\n\n            tableData.write(table);\n            if (tag == \"head\") {\n                headOffset = offset;\n            }\n            offset += table.length;\n\n            while (offset % 4) {\n                tableData.writeByte(0);\n                offset++;\n            }\n        }\n    }\n\n    out.write(tableData.get());\n    var sum = this.checksum(out.get());\n    var adjustment = 0xB1B0AFBA - sum;\n\n    out.offset(headOffset + 8);\n    out.writeLong(adjustment);\n    return out.get();\n};\n\nDirectory.prototype.checksum = function checksum (data) {\n    data = BinaryStream(data);\n    var sum = 0;\n    while (!data.eof()) {\n        sum += data.readLong();\n    }\n    return sum & 0xFFFFFFFF;\n};\n\nvar Table = function Table(file, def) {\n    this.definition = def;\n    this.length = def.length;\n    this.offset = def.offset;\n    this.file = file;\n    this.rawData = file.raw;\n    this.parse(file.raw);\n};\n\nTable.prototype.raw = function raw () {\n    return this.rawData.slice(this.offset, this.length);\n};\n\nTable.prototype.parse = function parse () {};\n\nvar HeadTable = (function (Table) {\n    function HeadTable () {\n        Table.apply(this, arguments);\n    }\n\n    if ( Table ) HeadTable.__proto__ = Table;\n    HeadTable.prototype = Object.create( Table && Table.prototype );\n    HeadTable.prototype.constructor = HeadTable;\n\n    HeadTable.prototype.parse = function parse (data) {\n        data.offset(this.offset);\n        this.version             = data.readLong();\n        this.revision            = data.readLong();\n        this.checkSumAdjustment  = data.readLong();\n        this.magicNumber         = data.readLong();\n        this.flags               = data.readShort();\n        this.unitsPerEm          = data.readShort();\n        this.created             = data.read(8);\n        this.modified            = data.read(8);\n\n        this.xMin = data.readShort_();\n        this.yMin = data.readShort_();\n        this.xMax = data.readShort_();\n        this.yMax = data.readShort_();\n\n        this.macStyle           = data.readShort();\n        this.lowestRecPPEM      = data.readShort();\n        this.fontDirectionHint  = data.readShort_();\n        this.indexToLocFormat   = data.readShort_();\n        this.glyphDataFormat    = data.readShort_();\n    };\n\n    HeadTable.prototype.render = function render (indexToLocFormat) {\n        var out = BinaryStream();\n        out.writeLong(this.version);\n        out.writeLong(this.revision);\n        out.writeLong(0);       // checksum adjustment; shall be computed later\n        out.writeLong(this.magicNumber);\n        out.writeShort(this.flags);\n        out.writeShort(this.unitsPerEm);\n        out.write(this.created);\n        out.write(this.modified);\n        out.writeShort_(this.xMin);\n        out.writeShort_(this.yMin);\n        out.writeShort_(this.xMax);\n        out.writeShort_(this.yMax);\n        out.writeShort(this.macStyle);\n        out.writeShort(this.lowestRecPPEM);\n        out.writeShort_(this.fontDirectionHint);\n        out.writeShort_(indexToLocFormat); // this will depend on the `loca` table\n        out.writeShort_(this.glyphDataFormat);\n        return out.get();\n    };\n\n    return HeadTable;\n}(Table));\n\nvar LocaTable = (function (Table) {\n    function LocaTable () {\n        Table.apply(this, arguments);\n    }\n\n    if ( Table ) LocaTable.__proto__ = Table;\n    LocaTable.prototype = Object.create( Table && Table.prototype );\n    LocaTable.prototype.constructor = LocaTable;\n\n    LocaTable.prototype.parse = function parse (data) {\n        data.offset(this.offset);\n        var format = this.file.head.indexToLocFormat;\n        if (format === 0) {\n            this.offsets = data.times(this.length / 2, function(){\n                return 2 * data.readShort();\n            });\n        } else {\n            this.offsets = data.times(this.length / 4, data.readLong);\n        }\n    };\n\n    LocaTable.prototype.offsetOf = function offsetOf (id) {\n        return this.offsets[id];\n    };\n\n    LocaTable.prototype.lengthOf = function lengthOf (id) {\n        return this.offsets[id + 1] - this.offsets[id];\n    };\n\n    LocaTable.prototype.render = function render (offsets) {\n        var out = BinaryStream();\n        var needsLongFormat = offsets[offsets.length - 1] > 0xFFFF;\n        for (var i = 0; i < offsets.length; ++i) {\n            if (needsLongFormat) {\n                out.writeLong(offsets[i]);\n            } else {\n                out.writeShort(offsets[i] / 2);\n            }\n        }\n        return {\n            format: needsLongFormat ? 1 : 0,\n            table: out.get()\n        };\n    };\n\n    return LocaTable;\n}(Table));\n\nvar HheaTable = (function (Table) {\n    function HheaTable () {\n        Table.apply(this, arguments);\n    }\n\n    if ( Table ) HheaTable.__proto__ = Table;\n    HheaTable.prototype = Object.create( Table && Table.prototype );\n    HheaTable.prototype.constructor = HheaTable;\n\n    HheaTable.prototype.parse = function parse (data) {\n        data.offset(this.offset);\n\n        this.version              = data.readLong();\n        this.ascent               = data.readShort_();\n        this.descent              = data.readShort_();\n        this.lineGap              = data.readShort_();\n        this.advanceWidthMax      = data.readShort();\n        this.minLeftSideBearing   = data.readShort_();\n        this.minRightSideBearing  = data.readShort_();\n        this.xMaxExtent           = data.readShort_();\n        this.caretSlopeRise       = data.readShort_();\n        this.caretSlopeRun        = data.readShort_();\n        this.caretOffset          = data.readShort_();\n\n        data.skip(4 * 2);       // reserved\n\n        this.metricDataFormat     = data.readShort_();\n        this.numOfLongHorMetrics  = data.readShort();\n    };\n\n    HheaTable.prototype.render = function render (ids) {\n        var out = BinaryStream();\n        out.writeLong(this.version);\n        out.writeShort_(this.ascent);\n        out.writeShort_(this.descent);\n        out.writeShort_(this.lineGap);\n        out.writeShort(this.advanceWidthMax);\n        out.writeShort_(this.minLeftSideBearing);\n        out.writeShort_(this.minRightSideBearing);\n        out.writeShort_(this.xMaxExtent);\n        out.writeShort_(this.caretSlopeRise);\n        out.writeShort_(this.caretSlopeRun);\n        out.writeShort_(this.caretOffset);\n\n        out.write([ 0, 0, 0, 0, 0, 0, 0, 0 ]); // reserved bytes\n\n        out.writeShort_(this.metricDataFormat);\n        out.writeShort(ids.length);\n        return out.get();\n    };\n\n    return HheaTable;\n}(Table));\n\nvar MaxpTable = (function (Table) {\n    function MaxpTable () {\n        Table.apply(this, arguments);\n    }\n\n    if ( Table ) MaxpTable.__proto__ = Table;\n    MaxpTable.prototype = Object.create( Table && Table.prototype );\n    MaxpTable.prototype.constructor = MaxpTable;\n\n    MaxpTable.prototype.parse = function parse (data) {\n        data.offset(this.offset);\n        this.version = data.readLong();\n        this.numGlyphs = data.readShort();\n        this.maxPoints = data.readShort();\n        this.maxContours = data.readShort();\n        this.maxComponentPoints = data.readShort();\n        this.maxComponentContours = data.readShort();\n        this.maxZones = data.readShort();\n        this.maxTwilightPoints = data.readShort();\n        this.maxStorage = data.readShort();\n        this.maxFunctionDefs = data.readShort();\n        this.maxInstructionDefs = data.readShort();\n        this.maxStackElements = data.readShort();\n        this.maxSizeOfInstructions = data.readShort();\n        this.maxComponentElements = data.readShort();\n        this.maxComponentDepth = data.readShort();\n    };\n\n    MaxpTable.prototype.render = function render (glyphIds) {\n        var out = BinaryStream();\n        out.writeLong(this.version);\n        out.writeShort(glyphIds.length);\n        out.writeShort(this.maxPoints);\n        out.writeShort(this.maxContours);\n        out.writeShort(this.maxComponentPoints);\n        out.writeShort(this.maxComponentContours);\n        out.writeShort(this.maxZones);\n        out.writeShort(this.maxTwilightPoints);\n        out.writeShort(this.maxStorage);\n        out.writeShort(this.maxFunctionDefs);\n        out.writeShort(this.maxInstructionDefs);\n        out.writeShort(this.maxStackElements);\n        out.writeShort(this.maxSizeOfInstructions);\n        out.writeShort(this.maxComponentElements);\n        out.writeShort(this.maxComponentDepth);\n        return out.get();\n    };\n\n    return MaxpTable;\n}(Table));\n\nvar HmtxTable = (function (Table) {\n    function HmtxTable () {\n        Table.apply(this, arguments);\n    }\n\n    if ( Table ) HmtxTable.__proto__ = Table;\n    HmtxTable.prototype = Object.create( Table && Table.prototype );\n    HmtxTable.prototype.constructor = HmtxTable;\n\n    HmtxTable.prototype.parse = function parse (data) {\n        data.offset(this.offset);\n        var dir = this.file, hhea = dir.hhea;\n        this.metrics = data.times(hhea.numOfLongHorMetrics, function(){\n            return {\n                advance: data.readShort(),\n                lsb: data.readShort_()\n            };\n        });\n        var lsbCount = dir.maxp.numGlyphs - dir.hhea.numOfLongHorMetrics;\n        this.leftSideBearings = data.times(lsbCount, data.readShort_);\n    };\n\n    HmtxTable.prototype.forGlyph = function forGlyph (id) {\n        var metrics = this.metrics;\n        var n = metrics.length;\n        if (id < n) {\n            return metrics[id];\n        }\n        return {\n            advance: metrics[n - 1].advance,\n            lsb: this.leftSideBearings[id - n]\n        };\n    };\n\n    HmtxTable.prototype.render = function render (glyphIds) {\n        var this$1 = this;\n\n        var out = BinaryStream();\n        for (var i = 0; i < glyphIds.length; ++i) {\n            var m = this$1.forGlyph(glyphIds[i]);\n            out.writeShort(m.advance);\n            out.writeShort_(m.lsb);\n        }\n        return out.get();\n    };\n\n    return HmtxTable;\n}(Table));\n\nvar GlyfTable = (function(){\n    var SimpleGlyph = function SimpleGlyph(raw) {\n        this.raw = raw;\n    };\n\n    var prototypeAccessors = { compound: { configurable: true } };\n\n    prototypeAccessors.compound.get = function () {\n        return false;\n    };\n\n    SimpleGlyph.prototype.render = function render () {\n        return this.raw.get();\n    };\n\n    Object.defineProperties( SimpleGlyph.prototype, prototypeAccessors );\n\n    var ARG_1_AND_2_ARE_WORDS     = 0x0001;\n    var WE_HAVE_A_SCALE           = 0x0008;\n    var MORE_COMPONENTS           = 0x0020;\n    var WE_HAVE_AN_X_AND_Y_SCALE  = 0x0040;\n    var WE_HAVE_A_TWO_BY_TWO      = 0x0080;\n    //var WE_HAVE_INSTRUCTIONS      = 0x0100;\n\n    var CompoundGlyph = function CompoundGlyph(data) {\n        this.raw = data;\n        var ids = this.glyphIds = [];\n        var offsets = this.idOffsets = [];\n        while (true) {      // eslint-disable-line no-constant-condition\n            var flags = data.readShort();\n            offsets.push(data.offset());\n            ids.push(data.readShort());\n\n            if (!(flags & MORE_COMPONENTS)) {\n                break;\n            }\n\n            data.skip(flags & ARG_1_AND_2_ARE_WORDS ? 4 : 2);\n\n            if (flags & WE_HAVE_A_TWO_BY_TWO) {\n                data.skip(8);\n            } else if (flags & WE_HAVE_AN_X_AND_Y_SCALE) {\n                data.skip(4);\n            } else if (flags & WE_HAVE_A_SCALE) {\n                data.skip(2);\n            }\n        }\n    };\n\n    var prototypeAccessors$1 = { compound: { configurable: true } };\n\n    prototypeAccessors$1.compound.get = function () {\n        return true;\n    };\n\n    CompoundGlyph.prototype.render = function render (old2new) {\n            var this$1 = this;\n\n        var out = BinaryStream(this.raw.get());\n        for (var i = 0; i < this.glyphIds.length; ++i) {\n            var id = this$1.glyphIds[i];\n            out.offset(this$1.idOffsets[i]);\n            out.writeShort(old2new[id]);\n        }\n        return out.get();\n    };\n\n    Object.defineProperties( CompoundGlyph.prototype, prototypeAccessors$1 );\n\n    return (function (Table) {\n        function anonymous () {\n            Table.apply(this, arguments);\n        }\n\n        if ( Table ) anonymous.__proto__ = Table;\n        anonymous.prototype = Object.create( Table && Table.prototype );\n        anonymous.prototype.constructor = anonymous;\n\n        anonymous.prototype.parse = function parse () {\n            this.cache = {};\n        };\n\n        anonymous.prototype.glyphFor = function glyphFor (id) {\n            var cache = this.cache;\n            if (hasOwnProperty(cache, id)) {\n                return cache[id];\n            }\n\n            var loca = this.file.loca;\n            var length = loca.lengthOf(id);\n\n            if (length === 0) {\n                return (cache[id] = null);\n            }\n\n            var data = this.rawData;\n            var offset = this.offset + loca.offsetOf(id);\n            var raw = BinaryStream(data.slice(offset, length));\n\n            var numberOfContours = raw.readShort_();\n            var xMin = raw.readShort_();\n            var yMin = raw.readShort_();\n            var xMax = raw.readShort_();\n            var yMax = raw.readShort_();\n\n            var glyph = cache[id] = numberOfContours < 0 ? new CompoundGlyph(raw) : new SimpleGlyph(raw);\n\n            glyph.numberOfContours = numberOfContours;\n            glyph.xMin = xMin;\n            glyph.yMin = yMin;\n            glyph.xMax = xMax;\n            glyph.yMax = yMax;\n\n            return glyph;\n        };\n\n        anonymous.prototype.render = function render (glyphs, oldIds, old2new) {\n            var out = BinaryStream(), offsets = [];\n            for (var i = 0; i < oldIds.length; ++i) {\n                var id = oldIds[i];\n                var glyph = glyphs[id];\n                if (out.offset() % 2) {\n                    out.writeByte(0);\n                }\n                offsets.push(out.offset());\n                if (glyph) {\n                    out.write(glyph.render(old2new));\n                }\n            }\n            if (out.offset() % 2) {\n                out.writeByte(0);\n            }\n            offsets.push(out.offset());\n            return {\n                table: out.get(),\n                offsets: offsets\n            };\n        };\n\n        return anonymous;\n    }(Table));\n}());\n\nvar NameTable = (function(){\n    var NameEntry = function NameEntry(text, entry) {\n        this.text = text;\n        this.length = text.length;\n        this.platformID = entry.platformID;\n        this.platformSpecificID = entry.platformSpecificID;\n        this.languageID = entry.languageID;\n        this.nameID = entry.nameID;\n    };\n\n    return (function (Table) {\n        function anonymous () {\n            Table.apply(this, arguments);\n        }\n\n        if ( Table ) anonymous.__proto__ = Table;\n        anonymous.prototype = Object.create( Table && Table.prototype );\n        anonymous.prototype.constructor = anonymous;\n\n        anonymous.prototype.parse = function parse (data) {\n            data.offset(this.offset);\n            data.readShort();   // format\n            var count = data.readShort();\n            var stringOffset = this.offset + data.readShort();\n            var nameRecords = data.times(count, function(){\n                return {\n                    platformID         : data.readShort(),\n                    platformSpecificID : data.readShort(),\n                    languageID         : data.readShort(),\n                    nameID             : data.readShort(),\n                    length             : data.readShort(),\n                    offset             : data.readShort() + stringOffset\n                };\n            });\n            var strings = this.strings = {};\n            for (var i = 0; i < nameRecords.length; ++i) {\n                var rec = nameRecords[i];\n                data.offset(rec.offset);\n                var text = data.readString(rec.length);\n                if (!strings[rec.nameID]) {\n                    strings[rec.nameID] = [];\n                }\n                strings[rec.nameID].push(new NameEntry(text, rec));\n            }\n            this.postscriptEntry = strings[6][0];\n            this.postscriptName = this.postscriptEntry.text.replace(/[^\\x20-\\x7F]/g, \"\");\n        };\n\n        anonymous.prototype.render = function render (psName) {\n            var this$1 = this;\n\n            var strings = this.strings;\n            var strCount = 0;\n            for (var i in strings) {\n                if (hasOwnProperty(strings, i)) {\n                    strCount += strings[i].length;\n                }\n            }\n            var out = BinaryStream();\n            var strTable = BinaryStream();\n\n            out.writeShort(0);  // format\n            out.writeShort(strCount);\n            out.writeShort(6 + 12 * strCount); // stringOffset\n\n            for (i in strings) {\n                if (hasOwnProperty(strings, i)) {\n                    var list = i == 6 ? [\n                        new NameEntry(psName, this$1.postscriptEntry)\n                    ] : strings[i];\n                    for (var j = 0; j < list.length; ++j) {\n                        var str = list[j];\n                        out.writeShort(str.platformID);\n                        out.writeShort(str.platformSpecificID);\n                        out.writeShort(str.languageID);\n                        out.writeShort(str.nameID);\n                        out.writeShort(str.length);\n                        out.writeShort(strTable.offset());\n\n                        strTable.writeString(str.text);\n                    }\n                }\n            }\n\n            out.write(strTable.get());\n\n            return out.get();\n        };\n\n        return anonymous;\n    }(Table));\n})();\n\nvar PostTable = (function(){\n    var POSTSCRIPT_GLYPHS = \".notdef .null nonmarkingreturn space exclam quotedbl numbersign dollar percent ampersand quotesingle parenleft parenright asterisk plus comma hyphen period slash zero one two three four five six seven eight nine colon semicolon less equal greater question at A B C D E F G H I J K L M N O P Q R S T U V W X Y Z bracketleft backslash bracketright asciicircum underscore grave a b c d e f g h i j k l m n o p q r s t u v w x y z braceleft bar braceright asciitilde Adieresis Aring Ccedilla Eacute Ntilde Odieresis Udieresis aacute agrave acircumflex adieresis atilde aring ccedilla eacute egrave ecircumflex edieresis iacute igrave icircumflex idieresis ntilde oacute ograve ocircumflex odieresis otilde uacute ugrave ucircumflex udieresis dagger degree cent sterling section bullet paragraph germandbls registered copyright trademark acute dieresis notequal AE Oslash infinity plusminus lessequal greaterequal yen mu partialdiff summation product pi integral ordfeminine ordmasculine Omega ae oslash questiondown exclamdown logicalnot radical florin approxequal Delta guillemotleft guillemotright ellipsis nonbreakingspace Agrave Atilde Otilde OE oe endash emdash quotedblleft quotedblright quoteleft quoteright divide lozenge ydieresis Ydieresis fraction currency guilsinglleft guilsinglright fi fl daggerdbl periodcentered quotesinglbase quotedblbase perthousand Acircumflex Ecircumflex Aacute Edieresis Egrave Iacute Icircumflex Idieresis Igrave Oacute Ocircumflex apple Ograve Uacute Ucircumflex Ugrave dotlessi circumflex tilde macron breve dotaccent ring cedilla hungarumlaut ogonek caron Lslash lslash Scaron scaron Zcaron zcaron brokenbar Eth eth Yacute yacute Thorn thorn minus multiply onesuperior twosuperior threesuperior onehalf onequarter threequarters franc Gbreve gbreve Idotaccent Scedilla scedilla Cacute cacute Ccaron ccaron dcroat\".split(/\\s+/g);\n\n    return (function (Table) {\n        function anonymous () {\n            Table.apply(this, arguments);\n        }\n\n        if ( Table ) anonymous.__proto__ = Table;\n        anonymous.prototype = Object.create( Table && Table.prototype );\n        anonymous.prototype.constructor = anonymous;\n\n        anonymous.prototype.parse = function parse (data) {\n            var this$1 = this;\n\n            data.offset(this.offset);\n\n            this.format = data.readLong();\n            this.italicAngle = data.readFixed_();\n            this.underlinePosition = data.readShort_();\n            this.underlineThickness = data.readShort_();\n            this.isFixedPitch = data.readLong();\n            this.minMemType42 = data.readLong();\n            this.maxMemType42 = data.readLong();\n            this.minMemType1 = data.readLong();\n            this.maxMemType1 = data.readLong();\n\n            var numberOfGlyphs;\n\n            switch (this.format) {\n              case 0x00010000:\n              case 0x00030000:\n                break;\n\n              case 0x00020000:\n                numberOfGlyphs = data.readShort();\n                this.glyphNameIndex = data.times(numberOfGlyphs, data.readShort);\n                this.names = [];\n                var limit = this.offset + this.length;\n                while (data.offset() < limit) {\n                    this$1.names.push(data.readString(data.readByte()));\n                }\n                break;\n\n              case 0x00025000:\n                numberOfGlyphs = data.readShort();\n                this.offsets = data.read(numberOfGlyphs);\n                break;\n\n              case 0x00040000:\n                this.map = data.times(this.file.maxp.numGlyphs, data.readShort);\n                break;\n            }\n        };\n\n        anonymous.prototype.glyphFor = function glyphFor (code) {\n            switch (this.format) {\n              case 0x00010000:\n                return POSTSCRIPT_GLYPHS[code] || \".notdef\";\n\n              case 0x00020000:\n                var index = this.glyphNameIndex[code];\n                if (index < POSTSCRIPT_GLYPHS.length) {\n                    return POSTSCRIPT_GLYPHS[index];\n                }\n                return this.names[index - POSTSCRIPT_GLYPHS.length] || \".notdef\";\n\n              case 0x00025000:\n\n              case 0x00030000:\n                return \".notdef\";\n\n              case 0x00040000:\n                return this.map[code] || 0xFFFF;\n            }\n        };\n\n        anonymous.prototype.render = function render (mapping) {\n            var this$1 = this;\n\n            if (this.format == 0x00030000) {\n                return this.raw();\n            }\n\n            // keep original header, but set format to 2.0\n            var out = BinaryStream(this.rawData.slice(this.offset, 32));\n            out.writeLong(0x00020000);\n            out.offset(32);\n\n            var indexes = [];\n            var strings = [];\n\n            for (var i = 0; i < mapping.length; ++i) {\n                var id = mapping[i];\n                var post = this$1.glyphFor(id);\n                var index = POSTSCRIPT_GLYPHS.indexOf(post);\n                if (index >= 0) {\n                    indexes.push(index);\n                } else {\n                    indexes.push(POSTSCRIPT_GLYPHS.length + strings.length);\n                    strings.push(post);\n                }\n            }\n\n            out.writeShort(mapping.length);\n\n            for (i = 0; i < indexes.length; ++i) {\n                out.writeShort(indexes[i]);\n            }\n\n            for (i = 0; i < strings.length; ++i) {\n                out.writeByte(strings[i].length);\n                out.writeString(strings[i]);\n            }\n\n            return out.get();\n        };\n\n        return anonymous;\n    }(Table));\n})();\n\nvar CmapTable = (function(){\n    var CmapEntry = function CmapEntry(data, offset, codeMap) {\n        var self = this;\n        self.platformID = data.readShort();\n        self.platformSpecificID = data.readShort();\n        self.offset = offset + data.readLong();\n\n        data.saveExcursion(function(){\n            var code;\n            data.offset(self.offset);\n            self.format = data.readShort();\n\n            switch (self.format) {\n            case 0:\n                self.length = data.readShort();\n                self.language = data.readShort();\n                for (var i = 0; i < 256; ++i) {\n                    codeMap[i] = data.readByte();\n                }\n                break;\n\n            case 4:\n                self.length = data.readShort();\n                self.language = data.readShort();\n                var segCount = data.readShort() / 2;\n\n                data.skip(6);   // searchRange, entrySelector, rangeShift\n                var endCode = data.times(segCount, data.readShort);\n                data.skip(2);   // reserved pad\n                var startCode = data.times(segCount, data.readShort);\n                var idDelta = data.times(segCount, data.readShort_);\n                var idRangeOffset = data.times(segCount, data.readShort);\n\n                var count = (self.length + self.offset - data.offset()) / 2;\n                var glyphIds = data.times(count, data.readShort);\n\n                for (i = 0; i < segCount; ++i) {\n                    var start = startCode[i], end = endCode[i];\n                    for (code = start; code <= end; ++code) {\n                        var glyphId;\n                        if (idRangeOffset[i] === 0) {\n                            glyphId = code + idDelta[i];\n                        } else {\n                            ///\n                            // When non-zero, idRangeOffset contains for each segment the byte offset of the Glyph ID\n                            // into the glyphIds table, from the *current* `i` cell of idRangeOffset.  In other words,\n                            // this offset spans from the first into the second array.  This works, because the arrays\n                            // are consecutive in the TTF file:\n                            //\n                            // [ ...idRangeOffset... ][ ...glyphIds... ]\n                            //   ...... 48 ......   .... ID ....\n                            //          ^----- 48 bytes -----^\n                            //\n                            // (but I can't stop wondering why is it not just a plain index, possibly incremented by 1\n                            // so that we can have that special `zero` value.)\n                            //\n                            // The elements of idRangeOffset are even numbers, because both arrays contain 16-bit words,\n                            // yet the offset is in bytes.  That is why we divide it by 2.  Then we subtract the\n                            // remaining segments (segCount-i), and add the code-start offset, to which we need to add\n                            // the corresponding delta to get the actual glyph ID.\n                            ///\n                            var index = idRangeOffset[i] / 2 - (segCount - i) + (code - start);\n                            glyphId = glyphIds[index] || 0;\n                            if (glyphId !== 0) {\n                                glyphId += idDelta[i];\n                            }\n                        }\n                        codeMap[code] = glyphId & 0xFFFF;\n                    }\n                }\n                break;\n\n            case 6:\n                self.length = data.readShort();\n                self.language = data.readShort();\n                code = data.readShort();\n                var length = data.readShort();\n                while (length-- > 0) {\n                    codeMap[code++] = data.readShort();\n                }\n                break;\n\n            case 12:\n                data.readShort(); // reserved\n                self.length = data.readLong();\n                self.language = data.readLong();\n                var ngroups = data.readLong();\n                while (ngroups-- > 0) {\n                    code = data.readLong();\n                    var endCharCode = data.readLong();\n                    var glyphCode = data.readLong();\n                    while (code <= endCharCode) {\n                        codeMap[code++] = glyphCode++;\n                    }\n                }\n                break;\n\n            default:\n                if (window.console) {\n                    window.console.error(\"Unhandled CMAP format: \" + self.format);\n                }\n            }\n        });\n    };\n\n    function renderCharmap(ncid2ogid, ogid2ngid) {\n        var codes = sortedKeys(ncid2ogid);\n        var startCodes = [];\n        var endCodes = [];\n        var last = null;\n        var diff = null;\n\n        function new_gid(charcode) {\n            return ogid2ngid[ncid2ogid[charcode]];\n        }\n\n        for (var i = 0; i < codes.length; ++i) {\n            var code = codes[i];\n            var gid = new_gid(code);\n            var delta = gid - code;\n            if (last == null || delta !== diff) {\n                if (last) {\n                    endCodes.push(last);\n                }\n                startCodes.push(code);\n                diff = delta;\n            }\n            last = code;\n        }\n\n        if (last) {\n            endCodes.push(last);\n        }\n        endCodes.push(0xFFFF);\n        startCodes.push(0xFFFF);\n\n        var segCount = startCodes.length;\n        var segCountX2 = segCount * 2;\n        var searchRange = 2 * Math.pow(2, Math.floor(Math.log(segCount) / Math.LN2));\n        var entrySelector = Math.log(searchRange / 2) / Math.LN2;\n        var rangeShift = segCountX2 - searchRange;\n\n        var deltas = [];\n        var rangeOffsets = [];\n        var glyphIds = [];\n\n        for (i = 0; i < segCount; ++i) {\n            var startCode = startCodes[i];\n            var endCode = endCodes[i];\n            if (startCode == 0xFFFF) {\n                deltas.push(0);\n                rangeOffsets.push(0);\n                break;\n            }\n            var startGlyph = new_gid(startCode);\n            if (startCode - startGlyph >= 0x8000) {\n                deltas.push(0);\n                rangeOffsets.push(2 * (glyphIds.length + segCount - i));\n                for (var j = startCode; j <= endCode; ++j) {\n                    glyphIds.push(new_gid(j));\n                }\n            } else {\n                deltas.push(startGlyph - startCode);\n                rangeOffsets.push(0);\n            }\n        }\n\n        var out = BinaryStream();\n\n        out.writeShort(3);      // platformID\n        out.writeShort(1);      // platformSpecificID\n        out.writeLong(12);      // offset\n        out.writeShort(4);      // format\n        out.writeShort(16 + segCount * 8 + glyphIds.length * 2); // length\n        out.writeShort(0);      // language\n        out.writeShort(segCountX2);\n        out.writeShort(searchRange);\n        out.writeShort(entrySelector);\n        out.writeShort(rangeShift);\n\n        endCodes.forEach(out.writeShort);\n        out.writeShort(0);      // reserved pad\n        startCodes.forEach(out.writeShort);\n        deltas.forEach(out.writeShort_);\n        rangeOffsets.forEach(out.writeShort);\n        glyphIds.forEach(out.writeShort);\n\n        return out.get();\n    }\n\n    return (function (Table) {\n        function anonymous () {\n            Table.apply(this, arguments);\n        }\n\n        if ( Table ) anonymous.__proto__ = Table;\n        anonymous.prototype = Object.create( Table && Table.prototype );\n        anonymous.prototype.constructor = anonymous;\n\n        anonymous.prototype.parse = function parse (data) {\n            var self = this;\n            var offset = self.offset;\n            data.offset(offset);\n            self.codeMap = {};\n            self.version = data.readShort();\n            var tableCount = data.readShort();\n            self.tables = data.times(tableCount, function(){\n                return new CmapEntry(data, offset, self.codeMap);\n            });\n        };\n\n        anonymous.render = function render (ncid2ogid, ogid2ngid) {\n            var out = BinaryStream();\n            out.writeShort(0);  // version\n            out.writeShort(1);  // tableCount\n            out.write(renderCharmap(ncid2ogid, ogid2ngid));\n            return out.get();\n        };\n\n        return anonymous;\n    }(Table));\n\n})();\n\nvar OS2Table = (function (Table) {\n    function OS2Table () {\n        Table.apply(this, arguments);\n    }\n\n    if ( Table ) OS2Table.__proto__ = Table;\n    OS2Table.prototype = Object.create( Table && Table.prototype );\n    OS2Table.prototype.constructor = OS2Table;\n\n    OS2Table.prototype.parse = function parse (data) {\n        data.offset(this.offset);\n        this.version = data.readShort();\n        this.averageCharWidth = data.readShort_();\n        this.weightClass = data.readShort();\n        this.widthClass = data.readShort();\n        this.type = data.readShort();\n        this.ySubscriptXSize = data.readShort_();\n        this.ySubscriptYSize = data.readShort_();\n        this.ySubscriptXOffset = data.readShort_();\n        this.ySubscriptYOffset = data.readShort_();\n        this.ySuperscriptXSize = data.readShort_();\n        this.ySuperscriptYSize = data.readShort_();\n        this.ySuperscriptXOffset = data.readShort_();\n        this.ySuperscriptYOffset = data.readShort_();\n        this.yStrikeoutSize = data.readShort_();\n        this.yStrikeoutPosition = data.readShort_();\n        this.familyClass = data.readShort_();\n\n        this.panose = data.times(10, data.readByte);\n        this.charRange = data.times(4, data.readLong);\n\n        this.vendorID = data.readString(4);\n        this.selection = data.readShort();\n        this.firstCharIndex = data.readShort();\n        this.lastCharIndex = data.readShort();\n\n        if (this.version > 0) {\n            this.ascent = data.readShort_();\n            this.descent = data.readShort_();\n            this.lineGap = data.readShort_();\n            this.winAscent = data.readShort();\n            this.winDescent = data.readShort();\n            this.codePageRange = data.times(2, data.readLong);\n\n            if (this.version > 1) {\n                this.xHeight = data.readShort();\n                this.capHeight = data.readShort();\n                this.defaultChar = data.readShort();\n                this.breakChar = data.readShort();\n                this.maxContext = data.readShort();\n            }\n        }\n    };\n\n    OS2Table.prototype.render = function render () {\n        return this.raw();\n    };\n\n    return OS2Table;\n}(Table));\n\nvar subsetTag = 100000;\n\nfunction nextSubsetTag() {\n    var ret = \"\", n = String(subsetTag);\n    for (var i = 0; i < n.length; ++i) {\n        ret += String.fromCharCode(n.charCodeAt(i) - 48 + 65);\n    }\n    ++subsetTag;\n    return ret;\n}\n\nvar Subfont = function Subfont(font) {\n    this.font = font;\n    this.subset = {};\n    this.unicodes = {};\n    this.ogid2ngid = { 0: 0 };\n    this.ngid2ogid = { 0: 0 };\n    this.ncid2ogid = {};\n    this.next = this.firstChar = 1;\n    this.nextGid = 1;\n    this.psName = nextSubsetTag() + \"+\" + this.font.psName;\n};\n\nSubfont.prototype.use = function use (ch) {\n    var self = this;\n    if (typeof ch == \"string\") {\n        return ucs2decode(ch).reduce(function(ret, code){\n            return ret + String.fromCharCode(self.use(code));\n        }, \"\");\n    }\n    var code = self.unicodes[ch];\n    if (!code) {\n        code = self.next++;\n        self.subset[code] = ch;\n        self.unicodes[ch] = code;\n\n        // generate new GID (glyph ID) and maintain newGID ->\n        // oldGID and back mappings\n        var old_gid = self.font.cmap.codeMap[ch];\n        if (old_gid) {\n            self.ncid2ogid[code] = old_gid;\n            if (self.ogid2ngid[old_gid] == null) {\n                var new_gid = self.nextGid++;\n                self.ogid2ngid[old_gid] = new_gid;\n                self.ngid2ogid[new_gid] = old_gid;\n            }\n        }\n    }\n    return code;\n};\n\nSubfont.prototype.encodeText = function encodeText (text) {\n    return this.use(text);\n};\n\nSubfont.prototype.glyphIds = function glyphIds () {\n    return sortedKeys(this.ogid2ngid);\n};\n\nSubfont.prototype.glyphsFor = function glyphsFor (glyphIds, result) {\n        var this$1 = this;\n\n    if (!result) {\n        result = {};\n    }\n    for (var i = 0; i < glyphIds.length; ++i) {\n        var id = glyphIds[i];\n        if (!result[id]) {\n            var glyph = result[id] = this$1.font.glyf.glyphFor(id);\n            if (glyph && glyph.compound) {\n                this$1.glyphsFor(glyph.glyphIds, result);\n            }\n        }\n    }\n    return result;\n};\n\nSubfont.prototype.render = function render () {\n        var this$1 = this;\n\n    var glyphs = this.glyphsFor(this.glyphIds());\n\n    // add missing sub-glyphs\n    for (var old_gid in glyphs) {\n        if (hasOwnProperty(glyphs, old_gid)) {\n            old_gid = parseInt(old_gid, 10);\n            if (this$1.ogid2ngid[old_gid] == null) {\n                var new_gid = this$1.nextGid++;\n                this$1.ogid2ngid[old_gid] = new_gid;\n                this$1.ngid2ogid[new_gid] = old_gid;\n            }\n        }\n    }\n\n    // must obtain old_gid_ids in an order matching sorted\n    // new_gid_ids\n    var new_gid_ids = sortedKeys(this.ngid2ogid);\n    var old_gid_ids = new_gid_ids.map(function(id){\n        return this.ngid2ogid[id];\n    }, this);\n\n    var font = this.font;\n    var glyf = font.glyf.render(glyphs, old_gid_ids, this.ogid2ngid);\n    var loca = font.loca.render(glyf.offsets);\n\n    this.lastChar = this.next - 1;\n\n    var tables = {\n        \"cmap\" : CmapTable.render(this.ncid2ogid, this.ogid2ngid),\n        \"glyf\" : glyf.table,\n        \"loca\" : loca.table,\n        \"hmtx\" : font.hmtx.render(old_gid_ids),\n        \"hhea\" : font.hhea.render(old_gid_ids),\n        \"maxp\" : font.maxp.render(old_gid_ids),\n        \"post\" : font.post.render(old_gid_ids),\n        \"name\" : font.name.render(this.psName),\n        \"head\" : font.head.render(loca.format),\n        \"OS/2\" : font.os2.render()\n    };\n\n    return this.font.directory.render(tables);\n};\n\nSubfont.prototype.cidToGidMap = function cidToGidMap () {\n        var this$1 = this;\n\n    var out = BinaryStream(), len = 0;\n    for (var cid = this.firstChar; cid < this.next; ++cid) {\n        while (len < cid) {\n            out.writeShort(0);\n            len++;\n        }\n        var old_gid = this$1.ncid2ogid[cid];\n        if (old_gid) {\n            var new_gid = this$1.ogid2ngid[old_gid];\n            out.writeShort(new_gid);\n        } else {\n            out.writeShort(0);\n        }\n        len++;\n    }\n    return out.get();\n};\n\nvar TTFFont = function TTFFont(rawData, name) {\n    var self = this;\n    var data = self.contents = BinaryStream(rawData);\n    if (data.readString(4) == \"ttcf\") {\n        var offset;\n        var parse = function() {\n            data.offset(offset);\n            self.parse();\n        };\n        if (!name) {\n            throw new Error(\"Must specify a name for TTC files\");\n        }\n        data.readLong();    // version\n        var numFonts = data.readLong();\n        for (var i = 0; i < numFonts; ++i) {\n            offset = data.readLong();\n            data.saveExcursion(parse);\n            if (self.psName == name) {\n                return;\n            }\n        }\n        throw new Error(\"Font \" + name + \" not found in collection\");\n    } else {\n        data.offset(0);\n        self.parse();\n    }\n};\n\nTTFFont.prototype.parse = function parse () {\n    var dir = this.directory = new Directory(this.contents);\n\n    this.head = dir.readTable(\"head\", HeadTable);\n    this.loca = dir.readTable(\"loca\", LocaTable);\n    this.hhea = dir.readTable(\"hhea\", HheaTable);\n    this.maxp = dir.readTable(\"maxp\", MaxpTable);\n    this.hmtx = dir.readTable(\"hmtx\", HmtxTable);\n    this.glyf = dir.readTable(\"glyf\", GlyfTable);\n    this.name = dir.readTable(\"name\", NameTable);\n    this.post = dir.readTable(\"post\", PostTable);\n    this.cmap = dir.readTable(\"cmap\", CmapTable);\n    this.os2  = dir.readTable(\"OS/2\", OS2Table);\n\n    this.psName = this.name.postscriptName;\n    this.ascent = this.os2.ascent || this.hhea.ascent;\n    this.descent = this.os2.descent || this.hhea.descent;\n    this.lineGap = this.os2.lineGap || this.hhea.lineGap;\n    this.scale = 1000 / this.head.unitsPerEm;\n};\n\nTTFFont.prototype.widthOfGlyph = function widthOfGlyph (glyph) {\n    return this.hmtx.forGlyph(glyph).advance * this.scale;\n};\n\nTTFFont.prototype.makeSubset = function makeSubset () {\n    return new Subfont(this);\n};\n\nexport { TTFFont };\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,YAAY,EAAEC,UAAU,QAAQ,SAAS;AAElD,SAASC,cAAcA,CAACC,GAAG,EAAEC,GAAG,EAAE;EAC9B,OAAOC,MAAM,CAACC,SAAS,CAACJ,cAAc,CAACK,IAAI,CAACJ,GAAG,EAAEC,GAAG,CAAC;AACzD;AAEA,SAASI,UAAUA,CAACL,GAAG,EAAE;EACrB,OAAOE,MAAM,CAACI,IAAI,CAACN,GAAG,CAAC,CAACO,IAAI,CAAC,UAASC,CAAC,EAAEC,CAAC,EAAC;IAAE,OAAOD,CAAC,GAAGC,CAAC;EAAE,CAAC,CAAC,CAACC,GAAG,CAACC,UAAU,CAAC;AACjF;;AAEA;AACA,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,IAAI,EAAE;EACrC,IAAI,CAACC,GAAG,GAAGD,IAAI;EACf,IAAI,CAACE,UAAU,GAAGF,IAAI,CAACG,QAAQ,CAAC,CAAC;EACjC,IAAI,CAACC,UAAU,GAAGJ,IAAI,CAACK,SAAS,CAAC,CAAC;EAClC,IAAI,CAACC,WAAW,GAAGN,IAAI,CAACK,SAAS,CAAC,CAAC;EACnC,IAAI,CAACE,aAAa,GAAGP,IAAI,CAACK,SAAS,CAAC,CAAC;EACrC,IAAI,CAACG,UAAU,GAAGR,IAAI,CAACK,SAAS,CAAC,CAAC;EAElC,IAAII,MAAM,GAAG,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC;EAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACN,UAAU,EAAE,EAAEM,CAAC,EAAE;IACtC,IAAIC,KAAK,GAAG;MACRC,GAAG,EAAIZ,IAAI,CAACa,UAAU,CAAC,CAAC,CAAC;MACzBC,QAAQ,EAAGd,IAAI,CAACG,QAAQ,CAAC,CAAC;MAC1BY,MAAM,EAAKf,IAAI,CAACG,QAAQ,CAAC,CAAC;MAC1Ba,MAAM,EAAKhB,IAAI,CAACG,QAAQ,CAAC;IAC7B,CAAC;IACDM,MAAM,CAACE,KAAK,CAACC,GAAG,CAAC,GAAGD,KAAK;EAC7B;AACJ,CAAC;AAEDZ,SAAS,CAACT,SAAS,CAAC2B,SAAS,GAAG,SAASA,SAASA,CAAEC,IAAI,EAAEC,IAAI,EAAE;EAC5D,IAAIC,GAAG,GAAG,IAAI,CAACX,MAAM,CAACS,IAAI,CAAC;EAC3B,IAAI,CAACE,GAAG,EAAE;IACN,MAAM,IAAIC,KAAK,CAAC,QAAQ,GAAGH,IAAI,GAAG,yBAAyB,CAAC;EAChE;EACA,OAAQ,IAAI,CAACA,IAAI,CAAC,GAAGE,GAAG,CAACE,KAAK,GAAG,IAAIH,IAAI,CAAC,IAAI,EAAEC,GAAG,CAAC;AACxD,CAAC;AAEDrB,SAAS,CAACT,SAAS,CAACiC,MAAM,GAAG,SAASA,MAAMA,CAAEd,MAAM,EAAE;EAC9C,IAAIe,MAAM,GAAG,IAAI;EAErB,IAAIpB,UAAU,GAAGf,MAAM,CAACI,IAAI,CAACgB,MAAM,CAAC,CAACO,MAAM;EAE3C,IAAIS,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,KAAK,CAACF,IAAI,CAACG,GAAG,CAACzB,UAAU,CAAC,GAAGsB,IAAI,CAACI,GAAG,CAAC,CAAC;EACtE,IAAIxB,WAAW,GAAGmB,OAAO,GAAG,EAAE;EAC9B,IAAIlB,aAAa,GAAGmB,IAAI,CAACE,KAAK,CAACF,IAAI,CAACG,GAAG,CAACJ,OAAO,CAAC,GAAGC,IAAI,CAACI,GAAG,CAAC;EAC5D,IAAItB,UAAU,GAAGJ,UAAU,GAAG,EAAE,GAAGE,WAAW;EAE9C,IAAIyB,GAAG,GAAG/C,YAAY,CAAC,CAAC;EACxB+C,GAAG,CAACC,SAAS,CAAC,IAAI,CAAC9B,UAAU,CAAC;EAC9B6B,GAAG,CAACE,UAAU,CAAC7B,UAAU,CAAC;EAC1B2B,GAAG,CAACE,UAAU,CAAC3B,WAAW,CAAC;EAC3ByB,GAAG,CAACE,UAAU,CAAC1B,aAAa,CAAC;EAC7BwB,GAAG,CAACE,UAAU,CAACzB,UAAU,CAAC;EAE1B,IAAI0B,eAAe,GAAG9B,UAAU,GAAG,EAAE;EACrC,IAAIW,MAAM,GAAGgB,GAAG,CAAChB,MAAM,CAAC,CAAC,GAAGmB,eAAe;EAC3C,IAAIC,UAAU,GAAG,IAAI;EACrB,IAAIC,SAAS,GAAGpD,YAAY,CAAC,CAAC;EAE9B,KAAK,IAAI4B,GAAG,IAAIH,MAAM,EAAE;IACpB,IAAIvB,cAAc,CAACuB,MAAM,EAAEG,GAAG,CAAC,EAAE;MAC7B,IAAIU,KAAK,GAAGb,MAAM,CAACG,GAAG,CAAC;MAEvBmB,GAAG,CAACM,WAAW,CAACzB,GAAG,CAAC;MACpBmB,GAAG,CAACC,SAAS,CAACR,MAAM,CAACV,QAAQ,CAACQ,KAAK,CAAC,CAAC;MACrCS,GAAG,CAACC,SAAS,CAACjB,MAAM,CAAC;MACrBgB,GAAG,CAACC,SAAS,CAACV,KAAK,CAACN,MAAM,CAAC;MAE3BoB,SAAS,CAACE,KAAK,CAAChB,KAAK,CAAC;MACtB,IAAIV,GAAG,IAAI,MAAM,EAAE;QACfuB,UAAU,GAAGpB,MAAM;MACvB;MACAA,MAAM,IAAIO,KAAK,CAACN,MAAM;MAEtB,OAAOD,MAAM,GAAG,CAAC,EAAE;QACfqB,SAAS,CAACG,SAAS,CAAC,CAAC,CAAC;QACtBxB,MAAM,EAAE;MACZ;IACJ;EACJ;EAEAgB,GAAG,CAACO,KAAK,CAACF,SAAS,CAACI,GAAG,CAAC,CAAC,CAAC;EAC1B,IAAIC,GAAG,GAAG,IAAI,CAAC3B,QAAQ,CAACiB,GAAG,CAACS,GAAG,CAAC,CAAC,CAAC;EAClC,IAAIE,UAAU,GAAG,UAAU,GAAGD,GAAG;EAEjCV,GAAG,CAAChB,MAAM,CAACoB,UAAU,GAAG,CAAC,CAAC;EAC1BJ,GAAG,CAACC,SAAS,CAACU,UAAU,CAAC;EACzB,OAAOX,GAAG,CAACS,GAAG,CAAC,CAAC;AACpB,CAAC;AAEDzC,SAAS,CAACT,SAAS,CAACwB,QAAQ,GAAG,SAASA,QAAQA,CAAEd,IAAI,EAAE;EACpDA,IAAI,GAAGhB,YAAY,CAACgB,IAAI,CAAC;EACzB,IAAIyC,GAAG,GAAG,CAAC;EACX,OAAO,CAACzC,IAAI,CAAC2C,GAAG,CAAC,CAAC,EAAE;IAChBF,GAAG,IAAIzC,IAAI,CAACG,QAAQ,CAAC,CAAC;EAC1B;EACA,OAAOsC,GAAG,GAAG,UAAU;AAC3B,CAAC;AAED,IAAIG,KAAK,GAAG,SAASA,KAAKA,CAACC,IAAI,EAAEzB,GAAG,EAAE;EAClC,IAAI,CAAC0B,UAAU,GAAG1B,GAAG;EACrB,IAAI,CAACJ,MAAM,GAAGI,GAAG,CAACJ,MAAM;EACxB,IAAI,CAACD,MAAM,GAAGK,GAAG,CAACL,MAAM;EACxB,IAAI,CAAC8B,IAAI,GAAGA,IAAI;EAChB,IAAI,CAACE,OAAO,GAAGF,IAAI,CAAC5C,GAAG;EACvB,IAAI,CAAC+C,KAAK,CAACH,IAAI,CAAC5C,GAAG,CAAC;AACxB,CAAC;AAED2C,KAAK,CAACtD,SAAS,CAACW,GAAG,GAAG,SAASA,GAAGA,CAAA,EAAI;EAClC,OAAO,IAAI,CAAC8C,OAAO,CAACE,KAAK,CAAC,IAAI,CAAClC,MAAM,EAAE,IAAI,CAACC,MAAM,CAAC;AACvD,CAAC;AAED4B,KAAK,CAACtD,SAAS,CAAC0D,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAI,CAAC,CAAC;AAE5C,IAAIE,SAAS,GAAI,UAAUN,KAAK,EAAE;EAC9B,SAASM,SAASA,CAAA,EAAI;IAClBN,KAAK,CAACO,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EAChC;EAEA,IAAKR,KAAK,EAAGM,SAAS,CAACG,SAAS,GAAGT,KAAK;EACxCM,SAAS,CAAC5D,SAAS,GAAGD,MAAM,CAACiE,MAAM,CAAEV,KAAK,IAAIA,KAAK,CAACtD,SAAU,CAAC;EAC/D4D,SAAS,CAAC5D,SAAS,CAACiE,WAAW,GAAGL,SAAS;EAE3CA,SAAS,CAAC5D,SAAS,CAAC0D,KAAK,GAAG,SAASA,KAAKA,CAAEhD,IAAI,EAAE;IAC9CA,IAAI,CAACe,MAAM,CAAC,IAAI,CAACA,MAAM,CAAC;IACxB,IAAI,CAACyC,OAAO,GAAexD,IAAI,CAACG,QAAQ,CAAC,CAAC;IAC1C,IAAI,CAACsD,QAAQ,GAAczD,IAAI,CAACG,QAAQ,CAAC,CAAC;IAC1C,IAAI,CAACuD,kBAAkB,GAAI1D,IAAI,CAACG,QAAQ,CAAC,CAAC;IAC1C,IAAI,CAACwD,WAAW,GAAW3D,IAAI,CAACG,QAAQ,CAAC,CAAC;IAC1C,IAAI,CAACyD,KAAK,GAAiB5D,IAAI,CAACK,SAAS,CAAC,CAAC;IAC3C,IAAI,CAACwD,UAAU,GAAY7D,IAAI,CAACK,SAAS,CAAC,CAAC;IAC3C,IAAI,CAACyD,OAAO,GAAe9D,IAAI,CAAC+D,IAAI,CAAC,CAAC,CAAC;IACvC,IAAI,CAACC,QAAQ,GAAchE,IAAI,CAAC+D,IAAI,CAAC,CAAC,CAAC;IAEvC,IAAI,CAACE,IAAI,GAAGjE,IAAI,CAACkE,UAAU,CAAC,CAAC;IAC7B,IAAI,CAACC,IAAI,GAAGnE,IAAI,CAACkE,UAAU,CAAC,CAAC;IAC7B,IAAI,CAACE,IAAI,GAAGpE,IAAI,CAACkE,UAAU,CAAC,CAAC;IAC7B,IAAI,CAACG,IAAI,GAAGrE,IAAI,CAACkE,UAAU,CAAC,CAAC;IAE7B,IAAI,CAACI,QAAQ,GAAatE,IAAI,CAACK,SAAS,CAAC,CAAC;IAC1C,IAAI,CAACkE,aAAa,GAAQvE,IAAI,CAACK,SAAS,CAAC,CAAC;IAC1C,IAAI,CAACmE,iBAAiB,GAAIxE,IAAI,CAACkE,UAAU,CAAC,CAAC;IAC3C,IAAI,CAACO,gBAAgB,GAAKzE,IAAI,CAACkE,UAAU,CAAC,CAAC;IAC3C,IAAI,CAACQ,eAAe,GAAM1E,IAAI,CAACkE,UAAU,CAAC,CAAC;EAC/C,CAAC;EAEDhB,SAAS,CAAC5D,SAAS,CAACiC,MAAM,GAAG,SAASA,MAAMA,CAAEkD,gBAAgB,EAAE;IAC5D,IAAI1C,GAAG,GAAG/C,YAAY,CAAC,CAAC;IACxB+C,GAAG,CAACC,SAAS,CAAC,IAAI,CAACwB,OAAO,CAAC;IAC3BzB,GAAG,CAACC,SAAS,CAAC,IAAI,CAACyB,QAAQ,CAAC;IAC5B1B,GAAG,CAACC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAO;IACxBD,GAAG,CAACC,SAAS,CAAC,IAAI,CAAC2B,WAAW,CAAC;IAC/B5B,GAAG,CAACE,UAAU,CAAC,IAAI,CAAC2B,KAAK,CAAC;IAC1B7B,GAAG,CAACE,UAAU,CAAC,IAAI,CAAC4B,UAAU,CAAC;IAC/B9B,GAAG,CAACO,KAAK,CAAC,IAAI,CAACwB,OAAO,CAAC;IACvB/B,GAAG,CAACO,KAAK,CAAC,IAAI,CAAC0B,QAAQ,CAAC;IACxBjC,GAAG,CAAC4C,WAAW,CAAC,IAAI,CAACV,IAAI,CAAC;IAC1BlC,GAAG,CAAC4C,WAAW,CAAC,IAAI,CAACR,IAAI,CAAC;IAC1BpC,GAAG,CAAC4C,WAAW,CAAC,IAAI,CAACP,IAAI,CAAC;IAC1BrC,GAAG,CAAC4C,WAAW,CAAC,IAAI,CAACN,IAAI,CAAC;IAC1BtC,GAAG,CAACE,UAAU,CAAC,IAAI,CAACqC,QAAQ,CAAC;IAC7BvC,GAAG,CAACE,UAAU,CAAC,IAAI,CAACsC,aAAa,CAAC;IAClCxC,GAAG,CAAC4C,WAAW,CAAC,IAAI,CAACH,iBAAiB,CAAC;IACvCzC,GAAG,CAAC4C,WAAW,CAACF,gBAAgB,CAAC,CAAC,CAAC;IACnC1C,GAAG,CAAC4C,WAAW,CAAC,IAAI,CAACD,eAAe,CAAC;IACrC,OAAO3C,GAAG,CAACS,GAAG,CAAC,CAAC;EACpB,CAAC;EAED,OAAOU,SAAS;AACpB,CAAC,CAACN,KAAK,CAAE;AAET,IAAIgC,SAAS,GAAI,UAAUhC,KAAK,EAAE;EAC9B,SAASgC,SAASA,CAAA,EAAI;IAClBhC,KAAK,CAACO,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EAChC;EAEA,IAAKR,KAAK,EAAGgC,SAAS,CAACvB,SAAS,GAAGT,KAAK;EACxCgC,SAAS,CAACtF,SAAS,GAAGD,MAAM,CAACiE,MAAM,CAAEV,KAAK,IAAIA,KAAK,CAACtD,SAAU,CAAC;EAC/DsF,SAAS,CAACtF,SAAS,CAACiE,WAAW,GAAGqB,SAAS;EAE3CA,SAAS,CAACtF,SAAS,CAAC0D,KAAK,GAAG,SAASA,KAAKA,CAAEhD,IAAI,EAAE;IAC9CA,IAAI,CAACe,MAAM,CAAC,IAAI,CAACA,MAAM,CAAC;IACxB,IAAI8D,MAAM,GAAG,IAAI,CAAChC,IAAI,CAACiC,IAAI,CAACL,gBAAgB;IAC5C,IAAII,MAAM,KAAK,CAAC,EAAE;MACd,IAAI,CAACE,OAAO,GAAG/E,IAAI,CAACgF,KAAK,CAAC,IAAI,CAAChE,MAAM,GAAG,CAAC,EAAE,YAAU;QACjD,OAAO,CAAC,GAAGhB,IAAI,CAACK,SAAS,CAAC,CAAC;MAC/B,CAAC,CAAC;IACN,CAAC,MAAM;MACH,IAAI,CAAC0E,OAAO,GAAG/E,IAAI,CAACgF,KAAK,CAAC,IAAI,CAAChE,MAAM,GAAG,CAAC,EAAEhB,IAAI,CAACG,QAAQ,CAAC;IAC7D;EACJ,CAAC;EAEDyE,SAAS,CAACtF,SAAS,CAAC2F,QAAQ,GAAG,SAASA,QAAQA,CAAEC,EAAE,EAAE;IAClD,OAAO,IAAI,CAACH,OAAO,CAACG,EAAE,CAAC;EAC3B,CAAC;EAEDN,SAAS,CAACtF,SAAS,CAAC6F,QAAQ,GAAG,SAASA,QAAQA,CAAED,EAAE,EAAE;IAClD,OAAO,IAAI,CAACH,OAAO,CAACG,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAACH,OAAO,CAACG,EAAE,CAAC;EAClD,CAAC;EAEDN,SAAS,CAACtF,SAAS,CAACiC,MAAM,GAAG,SAASA,MAAMA,CAAEwD,OAAO,EAAE;IACnD,IAAIhD,GAAG,GAAG/C,YAAY,CAAC,CAAC;IACxB,IAAIoG,eAAe,GAAGL,OAAO,CAACA,OAAO,CAAC/D,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM;IAC1D,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqE,OAAO,CAAC/D,MAAM,EAAE,EAAEN,CAAC,EAAE;MACrC,IAAI0E,eAAe,EAAE;QACjBrD,GAAG,CAACC,SAAS,CAAC+C,OAAO,CAACrE,CAAC,CAAC,CAAC;MAC7B,CAAC,MAAM;QACHqB,GAAG,CAACE,UAAU,CAAC8C,OAAO,CAACrE,CAAC,CAAC,GAAG,CAAC,CAAC;MAClC;IACJ;IACA,OAAO;MACHmE,MAAM,EAAEO,eAAe,GAAG,CAAC,GAAG,CAAC;MAC/B9D,KAAK,EAAES,GAAG,CAACS,GAAG,CAAC;IACnB,CAAC;EACL,CAAC;EAED,OAAOoC,SAAS;AACpB,CAAC,CAAChC,KAAK,CAAE;AAET,IAAIyC,SAAS,GAAI,UAAUzC,KAAK,EAAE;EAC9B,SAASyC,SAASA,CAAA,EAAI;IAClBzC,KAAK,CAACO,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EAChC;EAEA,IAAKR,KAAK,EAAGyC,SAAS,CAAChC,SAAS,GAAGT,KAAK;EACxCyC,SAAS,CAAC/F,SAAS,GAAGD,MAAM,CAACiE,MAAM,CAAEV,KAAK,IAAIA,KAAK,CAACtD,SAAU,CAAC;EAC/D+F,SAAS,CAAC/F,SAAS,CAACiE,WAAW,GAAG8B,SAAS;EAE3CA,SAAS,CAAC/F,SAAS,CAAC0D,KAAK,GAAG,SAASA,KAAKA,CAAEhD,IAAI,EAAE;IAC9CA,IAAI,CAACe,MAAM,CAAC,IAAI,CAACA,MAAM,CAAC;IAExB,IAAI,CAACyC,OAAO,GAAgBxD,IAAI,CAACG,QAAQ,CAAC,CAAC;IAC3C,IAAI,CAACmF,MAAM,GAAiBtF,IAAI,CAACkE,UAAU,CAAC,CAAC;IAC7C,IAAI,CAACqB,OAAO,GAAgBvF,IAAI,CAACkE,UAAU,CAAC,CAAC;IAC7C,IAAI,CAACsB,OAAO,GAAgBxF,IAAI,CAACkE,UAAU,CAAC,CAAC;IAC7C,IAAI,CAACuB,eAAe,GAAQzF,IAAI,CAACK,SAAS,CAAC,CAAC;IAC5C,IAAI,CAACqF,kBAAkB,GAAK1F,IAAI,CAACkE,UAAU,CAAC,CAAC;IAC7C,IAAI,CAACyB,mBAAmB,GAAI3F,IAAI,CAACkE,UAAU,CAAC,CAAC;IAC7C,IAAI,CAAC0B,UAAU,GAAa5F,IAAI,CAACkE,UAAU,CAAC,CAAC;IAC7C,IAAI,CAAC2B,cAAc,GAAS7F,IAAI,CAACkE,UAAU,CAAC,CAAC;IAC7C,IAAI,CAAC4B,aAAa,GAAU9F,IAAI,CAACkE,UAAU,CAAC,CAAC;IAC7C,IAAI,CAAC6B,WAAW,GAAY/F,IAAI,CAACkE,UAAU,CAAC,CAAC;IAE7ClE,IAAI,CAACgG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAO;;IAExB,IAAI,CAACC,gBAAgB,GAAOjG,IAAI,CAACkE,UAAU,CAAC,CAAC;IAC7C,IAAI,CAACgC,mBAAmB,GAAIlG,IAAI,CAACK,SAAS,CAAC,CAAC;EAChD,CAAC;EAEDgF,SAAS,CAAC/F,SAAS,CAACiC,MAAM,GAAG,SAASA,MAAMA,CAAE4E,GAAG,EAAE;IAC/C,IAAIpE,GAAG,GAAG/C,YAAY,CAAC,CAAC;IACxB+C,GAAG,CAACC,SAAS,CAAC,IAAI,CAACwB,OAAO,CAAC;IAC3BzB,GAAG,CAAC4C,WAAW,CAAC,IAAI,CAACW,MAAM,CAAC;IAC5BvD,GAAG,CAAC4C,WAAW,CAAC,IAAI,CAACY,OAAO,CAAC;IAC7BxD,GAAG,CAAC4C,WAAW,CAAC,IAAI,CAACa,OAAO,CAAC;IAC7BzD,GAAG,CAACE,UAAU,CAAC,IAAI,CAACwD,eAAe,CAAC;IACpC1D,GAAG,CAAC4C,WAAW,CAAC,IAAI,CAACe,kBAAkB,CAAC;IACxC3D,GAAG,CAAC4C,WAAW,CAAC,IAAI,CAACgB,mBAAmB,CAAC;IACzC5D,GAAG,CAAC4C,WAAW,CAAC,IAAI,CAACiB,UAAU,CAAC;IAChC7D,GAAG,CAAC4C,WAAW,CAAC,IAAI,CAACkB,cAAc,CAAC;IACpC9D,GAAG,CAAC4C,WAAW,CAAC,IAAI,CAACmB,aAAa,CAAC;IACnC/D,GAAG,CAAC4C,WAAW,CAAC,IAAI,CAACoB,WAAW,CAAC;IAEjChE,GAAG,CAACO,KAAK,CAAC,CAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAE,CAAC,CAAC,CAAC;;IAEvCP,GAAG,CAAC4C,WAAW,CAAC,IAAI,CAACsB,gBAAgB,CAAC;IACtClE,GAAG,CAACE,UAAU,CAACkE,GAAG,CAACnF,MAAM,CAAC;IAC1B,OAAOe,GAAG,CAACS,GAAG,CAAC,CAAC;EACpB,CAAC;EAED,OAAO6C,SAAS;AACpB,CAAC,CAACzC,KAAK,CAAE;AAET,IAAIwD,SAAS,GAAI,UAAUxD,KAAK,EAAE;EAC9B,SAASwD,SAASA,CAAA,EAAI;IAClBxD,KAAK,CAACO,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EAChC;EAEA,IAAKR,KAAK,EAAGwD,SAAS,CAAC/C,SAAS,GAAGT,KAAK;EACxCwD,SAAS,CAAC9G,SAAS,GAAGD,MAAM,CAACiE,MAAM,CAAEV,KAAK,IAAIA,KAAK,CAACtD,SAAU,CAAC;EAC/D8G,SAAS,CAAC9G,SAAS,CAACiE,WAAW,GAAG6C,SAAS;EAE3CA,SAAS,CAAC9G,SAAS,CAAC0D,KAAK,GAAG,SAASA,KAAKA,CAAEhD,IAAI,EAAE;IAC9CA,IAAI,CAACe,MAAM,CAAC,IAAI,CAACA,MAAM,CAAC;IACxB,IAAI,CAACyC,OAAO,GAAGxD,IAAI,CAACG,QAAQ,CAAC,CAAC;IAC9B,IAAI,CAACkG,SAAS,GAAGrG,IAAI,CAACK,SAAS,CAAC,CAAC;IACjC,IAAI,CAACiG,SAAS,GAAGtG,IAAI,CAACK,SAAS,CAAC,CAAC;IACjC,IAAI,CAACkG,WAAW,GAAGvG,IAAI,CAACK,SAAS,CAAC,CAAC;IACnC,IAAI,CAACmG,kBAAkB,GAAGxG,IAAI,CAACK,SAAS,CAAC,CAAC;IAC1C,IAAI,CAACoG,oBAAoB,GAAGzG,IAAI,CAACK,SAAS,CAAC,CAAC;IAC5C,IAAI,CAACqG,QAAQ,GAAG1G,IAAI,CAACK,SAAS,CAAC,CAAC;IAChC,IAAI,CAACsG,iBAAiB,GAAG3G,IAAI,CAACK,SAAS,CAAC,CAAC;IACzC,IAAI,CAACuG,UAAU,GAAG5G,IAAI,CAACK,SAAS,CAAC,CAAC;IAClC,IAAI,CAACwG,eAAe,GAAG7G,IAAI,CAACK,SAAS,CAAC,CAAC;IACvC,IAAI,CAACyG,kBAAkB,GAAG9G,IAAI,CAACK,SAAS,CAAC,CAAC;IAC1C,IAAI,CAAC0G,gBAAgB,GAAG/G,IAAI,CAACK,SAAS,CAAC,CAAC;IACxC,IAAI,CAAC2G,qBAAqB,GAAGhH,IAAI,CAACK,SAAS,CAAC,CAAC;IAC7C,IAAI,CAAC4G,oBAAoB,GAAGjH,IAAI,CAACK,SAAS,CAAC,CAAC;IAC5C,IAAI,CAAC6G,iBAAiB,GAAGlH,IAAI,CAACK,SAAS,CAAC,CAAC;EAC7C,CAAC;EAED+F,SAAS,CAAC9G,SAAS,CAACiC,MAAM,GAAG,SAASA,MAAMA,CAAE4F,QAAQ,EAAE;IACpD,IAAIpF,GAAG,GAAG/C,YAAY,CAAC,CAAC;IACxB+C,GAAG,CAACC,SAAS,CAAC,IAAI,CAACwB,OAAO,CAAC;IAC3BzB,GAAG,CAACE,UAAU,CAACkF,QAAQ,CAACnG,MAAM,CAAC;IAC/Be,GAAG,CAACE,UAAU,CAAC,IAAI,CAACqE,SAAS,CAAC;IAC9BvE,GAAG,CAACE,UAAU,CAAC,IAAI,CAACsE,WAAW,CAAC;IAChCxE,GAAG,CAACE,UAAU,CAAC,IAAI,CAACuE,kBAAkB,CAAC;IACvCzE,GAAG,CAACE,UAAU,CAAC,IAAI,CAACwE,oBAAoB,CAAC;IACzC1E,GAAG,CAACE,UAAU,CAAC,IAAI,CAACyE,QAAQ,CAAC;IAC7B3E,GAAG,CAACE,UAAU,CAAC,IAAI,CAAC0E,iBAAiB,CAAC;IACtC5E,GAAG,CAACE,UAAU,CAAC,IAAI,CAAC2E,UAAU,CAAC;IAC/B7E,GAAG,CAACE,UAAU,CAAC,IAAI,CAAC4E,eAAe,CAAC;IACpC9E,GAAG,CAACE,UAAU,CAAC,IAAI,CAAC6E,kBAAkB,CAAC;IACvC/E,GAAG,CAACE,UAAU,CAAC,IAAI,CAAC8E,gBAAgB,CAAC;IACrChF,GAAG,CAACE,UAAU,CAAC,IAAI,CAAC+E,qBAAqB,CAAC;IAC1CjF,GAAG,CAACE,UAAU,CAAC,IAAI,CAACgF,oBAAoB,CAAC;IACzClF,GAAG,CAACE,UAAU,CAAC,IAAI,CAACiF,iBAAiB,CAAC;IACtC,OAAOnF,GAAG,CAACS,GAAG,CAAC,CAAC;EACpB,CAAC;EAED,OAAO4D,SAAS;AACpB,CAAC,CAACxD,KAAK,CAAE;AAET,IAAIwE,SAAS,GAAI,UAAUxE,KAAK,EAAE;EAC9B,SAASwE,SAASA,CAAA,EAAI;IAClBxE,KAAK,CAACO,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EAChC;EAEA,IAAKR,KAAK,EAAGwE,SAAS,CAAC/D,SAAS,GAAGT,KAAK;EACxCwE,SAAS,CAAC9H,SAAS,GAAGD,MAAM,CAACiE,MAAM,CAAEV,KAAK,IAAIA,KAAK,CAACtD,SAAU,CAAC;EAC/D8H,SAAS,CAAC9H,SAAS,CAACiE,WAAW,GAAG6D,SAAS;EAE3CA,SAAS,CAAC9H,SAAS,CAAC0D,KAAK,GAAG,SAASA,KAAKA,CAAEhD,IAAI,EAAE;IAC9CA,IAAI,CAACe,MAAM,CAAC,IAAI,CAACA,MAAM,CAAC;IACxB,IAAIsG,GAAG,GAAG,IAAI,CAACxE,IAAI;MAAEyE,IAAI,GAAGD,GAAG,CAACC,IAAI;IACpC,IAAI,CAACC,OAAO,GAAGvH,IAAI,CAACgF,KAAK,CAACsC,IAAI,CAACpB,mBAAmB,EAAE,YAAU;MAC1D,OAAO;QACHsB,OAAO,EAAExH,IAAI,CAACK,SAAS,CAAC,CAAC;QACzBoH,GAAG,EAAEzH,IAAI,CAACkE,UAAU,CAAC;MACzB,CAAC;IACL,CAAC,CAAC;IACF,IAAIwD,QAAQ,GAAGL,GAAG,CAACM,IAAI,CAACtB,SAAS,GAAGgB,GAAG,CAACC,IAAI,CAACpB,mBAAmB;IAChE,IAAI,CAAC0B,gBAAgB,GAAG5H,IAAI,CAACgF,KAAK,CAAC0C,QAAQ,EAAE1H,IAAI,CAACkE,UAAU,CAAC;EACjE,CAAC;EAEDkD,SAAS,CAAC9H,SAAS,CAACuI,QAAQ,GAAG,SAASA,QAAQA,CAAE3C,EAAE,EAAE;IAClD,IAAIqC,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,IAAIO,CAAC,GAAGP,OAAO,CAACvG,MAAM;IACtB,IAAIkE,EAAE,GAAG4C,CAAC,EAAE;MACR,OAAOP,OAAO,CAACrC,EAAE,CAAC;IACtB;IACA,OAAO;MACHsC,OAAO,EAAED,OAAO,CAACO,CAAC,GAAG,CAAC,CAAC,CAACN,OAAO;MAC/BC,GAAG,EAAE,IAAI,CAACG,gBAAgB,CAAC1C,EAAE,GAAG4C,CAAC;IACrC,CAAC;EACL,CAAC;EAEDV,SAAS,CAAC9H,SAAS,CAACiC,MAAM,GAAG,SAASA,MAAMA,CAAE4F,QAAQ,EAAE;IACpD,IAAI3F,MAAM,GAAG,IAAI;IAEjB,IAAIO,GAAG,GAAG/C,YAAY,CAAC,CAAC;IACxB,KAAK,IAAI0B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyG,QAAQ,CAACnG,MAAM,EAAE,EAAEN,CAAC,EAAE;MACtC,IAAIqH,CAAC,GAAGvG,MAAM,CAACqG,QAAQ,CAACV,QAAQ,CAACzG,CAAC,CAAC,CAAC;MACpCqB,GAAG,CAACE,UAAU,CAAC8F,CAAC,CAACP,OAAO,CAAC;MACzBzF,GAAG,CAAC4C,WAAW,CAACoD,CAAC,CAACN,GAAG,CAAC;IAC1B;IACA,OAAO1F,GAAG,CAACS,GAAG,CAAC,CAAC;EACpB,CAAC;EAED,OAAO4E,SAAS;AACpB,CAAC,CAACxE,KAAK,CAAE;AAET,IAAIoF,SAAS,GAAI,YAAU;EACvB,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAAChI,GAAG,EAAE;IACxC,IAAI,CAACA,GAAG,GAAGA,GAAG;EAClB,CAAC;EAED,IAAIiI,kBAAkB,GAAG;IAAEC,QAAQ,EAAE;MAAEC,YAAY,EAAE;IAAK;EAAE,CAAC;EAE7DF,kBAAkB,CAACC,QAAQ,CAAC3F,GAAG,GAAG,YAAY;IAC1C,OAAO,KAAK;EAChB,CAAC;EAEDyF,WAAW,CAAC3I,SAAS,CAACiC,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAI;IAC9C,OAAO,IAAI,CAACtB,GAAG,CAACuC,GAAG,CAAC,CAAC;EACzB,CAAC;EAEDnD,MAAM,CAACgJ,gBAAgB,CAAEJ,WAAW,CAAC3I,SAAS,EAAE4I,kBAAmB,CAAC;EAEpE,IAAII,qBAAqB,GAAO,MAAM;EACtC,IAAIC,eAAe,GAAa,MAAM;EACtC,IAAIC,eAAe,GAAa,MAAM;EACtC,IAAIC,wBAAwB,GAAI,MAAM;EACtC,IAAIC,oBAAoB,GAAQ,MAAM;EACtC;;EAEA,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAAC3I,IAAI,EAAE;IAC7C,IAAI,CAACC,GAAG,GAAGD,IAAI;IACf,IAAImG,GAAG,GAAG,IAAI,CAACgB,QAAQ,GAAG,EAAE;IAC5B,IAAIpC,OAAO,GAAG,IAAI,CAAC6D,SAAS,GAAG,EAAE;IACjC,OAAO,IAAI,EAAE;MAAO;MAChB,IAAIhF,KAAK,GAAG5D,IAAI,CAACK,SAAS,CAAC,CAAC;MAC5B0E,OAAO,CAAC8D,IAAI,CAAC7I,IAAI,CAACe,MAAM,CAAC,CAAC,CAAC;MAC3BoF,GAAG,CAAC0C,IAAI,CAAC7I,IAAI,CAACK,SAAS,CAAC,CAAC,CAAC;MAE1B,IAAI,EAAEuD,KAAK,GAAG4E,eAAe,CAAC,EAAE;QAC5B;MACJ;MAEAxI,IAAI,CAACgG,IAAI,CAACpC,KAAK,GAAG0E,qBAAqB,GAAG,CAAC,GAAG,CAAC,CAAC;MAEhD,IAAI1E,KAAK,GAAG8E,oBAAoB,EAAE;QAC9B1I,IAAI,CAACgG,IAAI,CAAC,CAAC,CAAC;MAChB,CAAC,MAAM,IAAIpC,KAAK,GAAG6E,wBAAwB,EAAE;QACzCzI,IAAI,CAACgG,IAAI,CAAC,CAAC,CAAC;MAChB,CAAC,MAAM,IAAIpC,KAAK,GAAG2E,eAAe,EAAE;QAChCvI,IAAI,CAACgG,IAAI,CAAC,CAAC,CAAC;MAChB;IACJ;EACJ,CAAC;EAED,IAAI8C,oBAAoB,GAAG;IAAEX,QAAQ,EAAE;MAAEC,YAAY,EAAE;IAAK;EAAE,CAAC;EAE/DU,oBAAoB,CAACX,QAAQ,CAAC3F,GAAG,GAAG,YAAY;IAC5C,OAAO,IAAI;EACf,CAAC;EAEDmG,aAAa,CAACrJ,SAAS,CAACiC,MAAM,GAAG,SAASA,MAAMA,CAAEwH,OAAO,EAAE;IACnD,IAAIvH,MAAM,GAAG,IAAI;IAErB,IAAIO,GAAG,GAAG/C,YAAY,CAAC,IAAI,CAACiB,GAAG,CAACuC,GAAG,CAAC,CAAC,CAAC;IACtC,KAAK,IAAI9B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACyG,QAAQ,CAACnG,MAAM,EAAE,EAAEN,CAAC,EAAE;MAC3C,IAAIwE,EAAE,GAAG1D,MAAM,CAAC2F,QAAQ,CAACzG,CAAC,CAAC;MAC3BqB,GAAG,CAAChB,MAAM,CAACS,MAAM,CAACoH,SAAS,CAAClI,CAAC,CAAC,CAAC;MAC/BqB,GAAG,CAACE,UAAU,CAAC8G,OAAO,CAAC7D,EAAE,CAAC,CAAC;IAC/B;IACA,OAAOnD,GAAG,CAACS,GAAG,CAAC,CAAC;EACpB,CAAC;EAEDnD,MAAM,CAACgJ,gBAAgB,CAAEM,aAAa,CAACrJ,SAAS,EAAEwJ,oBAAqB,CAAC;EAExE,OAAQ,UAAUlG,KAAK,EAAE;IACrB,SAASoG,SAASA,CAAA,EAAI;MAClBpG,KAAK,CAACO,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAChC;IAEA,IAAKR,KAAK,EAAGoG,SAAS,CAAC3F,SAAS,GAAGT,KAAK;IACxCoG,SAAS,CAAC1J,SAAS,GAAGD,MAAM,CAACiE,MAAM,CAAEV,KAAK,IAAIA,KAAK,CAACtD,SAAU,CAAC;IAC/D0J,SAAS,CAAC1J,SAAS,CAACiE,WAAW,GAAGyF,SAAS;IAE3CA,SAAS,CAAC1J,SAAS,CAAC0D,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAI;MAC1C,IAAI,CAACiG,KAAK,GAAG,CAAC,CAAC;IACnB,CAAC;IAEDD,SAAS,CAAC1J,SAAS,CAAC4J,QAAQ,GAAG,SAASA,QAAQA,CAAEhE,EAAE,EAAE;MAClD,IAAI+D,KAAK,GAAG,IAAI,CAACA,KAAK;MACtB,IAAI/J,cAAc,CAAC+J,KAAK,EAAE/D,EAAE,CAAC,EAAE;QAC3B,OAAO+D,KAAK,CAAC/D,EAAE,CAAC;MACpB;MAEA,IAAIiE,IAAI,GAAG,IAAI,CAACtG,IAAI,CAACsG,IAAI;MACzB,IAAInI,MAAM,GAAGmI,IAAI,CAAChE,QAAQ,CAACD,EAAE,CAAC;MAE9B,IAAIlE,MAAM,KAAK,CAAC,EAAE;QACd,OAAQiI,KAAK,CAAC/D,EAAE,CAAC,GAAG,IAAI;MAC5B;MAEA,IAAIlF,IAAI,GAAG,IAAI,CAAC+C,OAAO;MACvB,IAAIhC,MAAM,GAAG,IAAI,CAACA,MAAM,GAAGoI,IAAI,CAAClE,QAAQ,CAACC,EAAE,CAAC;MAC5C,IAAIjF,GAAG,GAAGjB,YAAY,CAACgB,IAAI,CAACiD,KAAK,CAAClC,MAAM,EAAEC,MAAM,CAAC,CAAC;MAElD,IAAIoI,gBAAgB,GAAGnJ,GAAG,CAACiE,UAAU,CAAC,CAAC;MACvC,IAAID,IAAI,GAAGhE,GAAG,CAACiE,UAAU,CAAC,CAAC;MAC3B,IAAIC,IAAI,GAAGlE,GAAG,CAACiE,UAAU,CAAC,CAAC;MAC3B,IAAIE,IAAI,GAAGnE,GAAG,CAACiE,UAAU,CAAC,CAAC;MAC3B,IAAIG,IAAI,GAAGpE,GAAG,CAACiE,UAAU,CAAC,CAAC;MAE3B,IAAImF,KAAK,GAAGJ,KAAK,CAAC/D,EAAE,CAAC,GAAGkE,gBAAgB,GAAG,CAAC,GAAG,IAAIT,aAAa,CAAC1I,GAAG,CAAC,GAAG,IAAIgI,WAAW,CAAChI,GAAG,CAAC;MAE5FoJ,KAAK,CAACD,gBAAgB,GAAGA,gBAAgB;MACzCC,KAAK,CAACpF,IAAI,GAAGA,IAAI;MACjBoF,KAAK,CAAClF,IAAI,GAAGA,IAAI;MACjBkF,KAAK,CAACjF,IAAI,GAAGA,IAAI;MACjBiF,KAAK,CAAChF,IAAI,GAAGA,IAAI;MAEjB,OAAOgF,KAAK;IAChB,CAAC;IAEDL,SAAS,CAAC1J,SAAS,CAACiC,MAAM,GAAG,SAASA,MAAMA,CAAE+H,MAAM,EAAEC,MAAM,EAAER,OAAO,EAAE;MACnE,IAAIhH,GAAG,GAAG/C,YAAY,CAAC,CAAC;QAAE+F,OAAO,GAAG,EAAE;MACtC,KAAK,IAAIrE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6I,MAAM,CAACvI,MAAM,EAAE,EAAEN,CAAC,EAAE;QACpC,IAAIwE,EAAE,GAAGqE,MAAM,CAAC7I,CAAC,CAAC;QAClB,IAAI2I,KAAK,GAAGC,MAAM,CAACpE,EAAE,CAAC;QACtB,IAAInD,GAAG,CAAChB,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE;UAClBgB,GAAG,CAACQ,SAAS,CAAC,CAAC,CAAC;QACpB;QACAwC,OAAO,CAAC8D,IAAI,CAAC9G,GAAG,CAAChB,MAAM,CAAC,CAAC,CAAC;QAC1B,IAAIsI,KAAK,EAAE;UACPtH,GAAG,CAACO,KAAK,CAAC+G,KAAK,CAAC9H,MAAM,CAACwH,OAAO,CAAC,CAAC;QACpC;MACJ;MACA,IAAIhH,GAAG,CAAChB,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE;QAClBgB,GAAG,CAACQ,SAAS,CAAC,CAAC,CAAC;MACpB;MACAwC,OAAO,CAAC8D,IAAI,CAAC9G,GAAG,CAAChB,MAAM,CAAC,CAAC,CAAC;MAC1B,OAAO;QACHO,KAAK,EAAES,GAAG,CAACS,GAAG,CAAC,CAAC;QAChBuC,OAAO,EAAEA;MACb,CAAC;IACL,CAAC;IAED,OAAOiE,SAAS;EACpB,CAAC,CAACpG,KAAK,CAAC;AACZ,CAAC,CAAC,CAAE;AAEJ,IAAI4G,SAAS,GAAI,YAAU;EACvB,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,IAAI,EAAE/I,KAAK,EAAE;IAC5C,IAAI,CAAC+I,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC1I,MAAM,GAAG0I,IAAI,CAAC1I,MAAM;IACzB,IAAI,CAAC2I,UAAU,GAAGhJ,KAAK,CAACgJ,UAAU;IAClC,IAAI,CAACC,kBAAkB,GAAGjJ,KAAK,CAACiJ,kBAAkB;IAClD,IAAI,CAACC,UAAU,GAAGlJ,KAAK,CAACkJ,UAAU;IAClC,IAAI,CAACC,MAAM,GAAGnJ,KAAK,CAACmJ,MAAM;EAC9B,CAAC;EAED,OAAQ,UAAUlH,KAAK,EAAE;IACrB,SAASoG,SAASA,CAAA,EAAI;MAClBpG,KAAK,CAACO,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAChC;IAEA,IAAKR,KAAK,EAAGoG,SAAS,CAAC3F,SAAS,GAAGT,KAAK;IACxCoG,SAAS,CAAC1J,SAAS,GAAGD,MAAM,CAACiE,MAAM,CAAEV,KAAK,IAAIA,KAAK,CAACtD,SAAU,CAAC;IAC/D0J,SAAS,CAAC1J,SAAS,CAACiE,WAAW,GAAGyF,SAAS;IAE3CA,SAAS,CAAC1J,SAAS,CAAC0D,KAAK,GAAG,SAASA,KAAKA,CAAEhD,IAAI,EAAE;MAC9CA,IAAI,CAACe,MAAM,CAAC,IAAI,CAACA,MAAM,CAAC;MACxBf,IAAI,CAACK,SAAS,CAAC,CAAC,CAAC,CAAG;MACpB,IAAI0J,KAAK,GAAG/J,IAAI,CAACK,SAAS,CAAC,CAAC;MAC5B,IAAI2J,YAAY,GAAG,IAAI,CAACjJ,MAAM,GAAGf,IAAI,CAACK,SAAS,CAAC,CAAC;MACjD,IAAI4J,WAAW,GAAGjK,IAAI,CAACgF,KAAK,CAAC+E,KAAK,EAAE,YAAU;QAC1C,OAAO;UACHJ,UAAU,EAAW3J,IAAI,CAACK,SAAS,CAAC,CAAC;UACrCuJ,kBAAkB,EAAG5J,IAAI,CAACK,SAAS,CAAC,CAAC;UACrCwJ,UAAU,EAAW7J,IAAI,CAACK,SAAS,CAAC,CAAC;UACrCyJ,MAAM,EAAe9J,IAAI,CAACK,SAAS,CAAC,CAAC;UACrCW,MAAM,EAAehB,IAAI,CAACK,SAAS,CAAC,CAAC;UACrCU,MAAM,EAAef,IAAI,CAACK,SAAS,CAAC,CAAC,GAAG2J;QAC5C,CAAC;MACL,CAAC,CAAC;MACF,IAAIE,OAAO,GAAG,IAAI,CAACA,OAAO,GAAG,CAAC,CAAC;MAC/B,KAAK,IAAIxJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuJ,WAAW,CAACjJ,MAAM,EAAE,EAAEN,CAAC,EAAE;QACzC,IAAIyJ,GAAG,GAAGF,WAAW,CAACvJ,CAAC,CAAC;QACxBV,IAAI,CAACe,MAAM,CAACoJ,GAAG,CAACpJ,MAAM,CAAC;QACvB,IAAI2I,IAAI,GAAG1J,IAAI,CAACa,UAAU,CAACsJ,GAAG,CAACnJ,MAAM,CAAC;QACtC,IAAI,CAACkJ,OAAO,CAACC,GAAG,CAACL,MAAM,CAAC,EAAE;UACtBI,OAAO,CAACC,GAAG,CAACL,MAAM,CAAC,GAAG,EAAE;QAC5B;QACAI,OAAO,CAACC,GAAG,CAACL,MAAM,CAAC,CAACjB,IAAI,CAAC,IAAIY,SAAS,CAACC,IAAI,EAAES,GAAG,CAAC,CAAC;MACtD;MACA,IAAI,CAACC,eAAe,GAAGF,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,IAAI,CAACG,cAAc,GAAG,IAAI,CAACD,eAAe,CAACV,IAAI,CAACY,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;IAChF,CAAC;IAEDtB,SAAS,CAAC1J,SAAS,CAACiC,MAAM,GAAG,SAASA,MAAMA,CAAEgJ,MAAM,EAAE;MAClD,IAAI/I,MAAM,GAAG,IAAI;MAEjB,IAAI0I,OAAO,GAAG,IAAI,CAACA,OAAO;MAC1B,IAAIM,QAAQ,GAAG,CAAC;MAChB,KAAK,IAAI9J,CAAC,IAAIwJ,OAAO,EAAE;QACnB,IAAIhL,cAAc,CAACgL,OAAO,EAAExJ,CAAC,CAAC,EAAE;UAC5B8J,QAAQ,IAAIN,OAAO,CAACxJ,CAAC,CAAC,CAACM,MAAM;QACjC;MACJ;MACA,IAAIe,GAAG,GAAG/C,YAAY,CAAC,CAAC;MACxB,IAAIyL,QAAQ,GAAGzL,YAAY,CAAC,CAAC;MAE7B+C,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAE;MACpBF,GAAG,CAACE,UAAU,CAACuI,QAAQ,CAAC;MACxBzI,GAAG,CAACE,UAAU,CAAC,CAAC,GAAG,EAAE,GAAGuI,QAAQ,CAAC,CAAC,CAAC;;MAEnC,KAAK9J,CAAC,IAAIwJ,OAAO,EAAE;QACf,IAAIhL,cAAc,CAACgL,OAAO,EAAExJ,CAAC,CAAC,EAAE;UAC5B,IAAIgK,IAAI,GAAGhK,CAAC,IAAI,CAAC,GAAG,CAChB,IAAI+I,SAAS,CAACc,MAAM,EAAE/I,MAAM,CAAC4I,eAAe,CAAC,CAChD,GAAGF,OAAO,CAACxJ,CAAC,CAAC;UACd,KAAK,IAAIiK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,IAAI,CAAC1J,MAAM,EAAE,EAAE2J,CAAC,EAAE;YAClC,IAAIC,GAAG,GAAGF,IAAI,CAACC,CAAC,CAAC;YACjB5I,GAAG,CAACE,UAAU,CAAC2I,GAAG,CAACjB,UAAU,CAAC;YAC9B5H,GAAG,CAACE,UAAU,CAAC2I,GAAG,CAAChB,kBAAkB,CAAC;YACtC7H,GAAG,CAACE,UAAU,CAAC2I,GAAG,CAACf,UAAU,CAAC;YAC9B9H,GAAG,CAACE,UAAU,CAAC2I,GAAG,CAACd,MAAM,CAAC;YAC1B/H,GAAG,CAACE,UAAU,CAAC2I,GAAG,CAAC5J,MAAM,CAAC;YAC1Be,GAAG,CAACE,UAAU,CAACwI,QAAQ,CAAC1J,MAAM,CAAC,CAAC,CAAC;YAEjC0J,QAAQ,CAACpI,WAAW,CAACuI,GAAG,CAAClB,IAAI,CAAC;UAClC;QACJ;MACJ;MAEA3H,GAAG,CAACO,KAAK,CAACmI,QAAQ,CAACjI,GAAG,CAAC,CAAC,CAAC;MAEzB,OAAOT,GAAG,CAACS,GAAG,CAAC,CAAC;IACpB,CAAC;IAED,OAAOwG,SAAS;EACpB,CAAC,CAACpG,KAAK,CAAC;AACZ,CAAC,CAAE,CAAC;AAEJ,IAAIiI,SAAS,GAAI,YAAU;EACvB,IAAIC,iBAAiB,GAAG,2zDAA2zD,CAACC,KAAK,CAAC,MAAM,CAAC;EAEj2D,OAAQ,UAAUnI,KAAK,EAAE;IACrB,SAASoG,SAASA,CAAA,EAAI;MAClBpG,KAAK,CAACO,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAChC;IAEA,IAAKR,KAAK,EAAGoG,SAAS,CAAC3F,SAAS,GAAGT,KAAK;IACxCoG,SAAS,CAAC1J,SAAS,GAAGD,MAAM,CAACiE,MAAM,CAAEV,KAAK,IAAIA,KAAK,CAACtD,SAAU,CAAC;IAC/D0J,SAAS,CAAC1J,SAAS,CAACiE,WAAW,GAAGyF,SAAS;IAE3CA,SAAS,CAAC1J,SAAS,CAAC0D,KAAK,GAAG,SAASA,KAAKA,CAAEhD,IAAI,EAAE;MAC9C,IAAIwB,MAAM,GAAG,IAAI;MAEjBxB,IAAI,CAACe,MAAM,CAAC,IAAI,CAACA,MAAM,CAAC;MAExB,IAAI,CAAC8D,MAAM,GAAG7E,IAAI,CAACG,QAAQ,CAAC,CAAC;MAC7B,IAAI,CAAC6K,WAAW,GAAGhL,IAAI,CAACiL,UAAU,CAAC,CAAC;MACpC,IAAI,CAACC,iBAAiB,GAAGlL,IAAI,CAACkE,UAAU,CAAC,CAAC;MAC1C,IAAI,CAACiH,kBAAkB,GAAGnL,IAAI,CAACkE,UAAU,CAAC,CAAC;MAC3C,IAAI,CAACkH,YAAY,GAAGpL,IAAI,CAACG,QAAQ,CAAC,CAAC;MACnC,IAAI,CAACkL,YAAY,GAAGrL,IAAI,CAACG,QAAQ,CAAC,CAAC;MACnC,IAAI,CAACmL,YAAY,GAAGtL,IAAI,CAACG,QAAQ,CAAC,CAAC;MACnC,IAAI,CAACoL,WAAW,GAAGvL,IAAI,CAACG,QAAQ,CAAC,CAAC;MAClC,IAAI,CAACqL,WAAW,GAAGxL,IAAI,CAACG,QAAQ,CAAC,CAAC;MAElC,IAAIsL,cAAc;MAElB,QAAQ,IAAI,CAAC5G,MAAM;QACjB,KAAK,UAAU;QACf,KAAK,UAAU;UACb;QAEF,KAAK,UAAU;UACb4G,cAAc,GAAGzL,IAAI,CAACK,SAAS,CAAC,CAAC;UACjC,IAAI,CAACqL,cAAc,GAAG1L,IAAI,CAACgF,KAAK,CAACyG,cAAc,EAAEzL,IAAI,CAACK,SAAS,CAAC;UAChE,IAAI,CAACsL,KAAK,GAAG,EAAE;UACf,IAAIC,KAAK,GAAG,IAAI,CAAC7K,MAAM,GAAG,IAAI,CAACC,MAAM;UACrC,OAAOhB,IAAI,CAACe,MAAM,CAAC,CAAC,GAAG6K,KAAK,EAAE;YAC1BpK,MAAM,CAACmK,KAAK,CAAC9C,IAAI,CAAC7I,IAAI,CAACa,UAAU,CAACb,IAAI,CAAC6L,QAAQ,CAAC,CAAC,CAAC,CAAC;UACvD;UACA;QAEF,KAAK,UAAU;UACbJ,cAAc,GAAGzL,IAAI,CAACK,SAAS,CAAC,CAAC;UACjC,IAAI,CAAC0E,OAAO,GAAG/E,IAAI,CAAC+D,IAAI,CAAC0H,cAAc,CAAC;UACxC;QAEF,KAAK,UAAU;UACb,IAAI,CAAC5L,GAAG,GAAGG,IAAI,CAACgF,KAAK,CAAC,IAAI,CAACnC,IAAI,CAAC8E,IAAI,CAACtB,SAAS,EAAErG,IAAI,CAACK,SAAS,CAAC;UAC/D;MACJ;IACJ,CAAC;IAED2I,SAAS,CAAC1J,SAAS,CAAC4J,QAAQ,GAAG,SAASA,QAAQA,CAAE4C,IAAI,EAAE;MACpD,QAAQ,IAAI,CAACjH,MAAM;QACjB,KAAK,UAAU;UACb,OAAOiG,iBAAiB,CAACgB,IAAI,CAAC,IAAI,SAAS;QAE7C,KAAK,UAAU;UACb,IAAIC,KAAK,GAAG,IAAI,CAACL,cAAc,CAACI,IAAI,CAAC;UACrC,IAAIC,KAAK,GAAGjB,iBAAiB,CAAC9J,MAAM,EAAE;YAClC,OAAO8J,iBAAiB,CAACiB,KAAK,CAAC;UACnC;UACA,OAAO,IAAI,CAACJ,KAAK,CAACI,KAAK,GAAGjB,iBAAiB,CAAC9J,MAAM,CAAC,IAAI,SAAS;QAElE,KAAK,UAAU;QAEf,KAAK,UAAU;UACb,OAAO,SAAS;QAElB,KAAK,UAAU;UACb,OAAO,IAAI,CAACnB,GAAG,CAACiM,IAAI,CAAC,IAAI,MAAM;MACnC;IACJ,CAAC;IAED9C,SAAS,CAAC1J,SAAS,CAACiC,MAAM,GAAG,SAASA,MAAMA,CAAEyK,OAAO,EAAE;MACnD,IAAIxK,MAAM,GAAG,IAAI;MAEjB,IAAI,IAAI,CAACqD,MAAM,IAAI,UAAU,EAAE;QAC3B,OAAO,IAAI,CAAC5E,GAAG,CAAC,CAAC;MACrB;;MAEA;MACA,IAAI8B,GAAG,GAAG/C,YAAY,CAAC,IAAI,CAAC+D,OAAO,CAACE,KAAK,CAAC,IAAI,CAAClC,MAAM,EAAE,EAAE,CAAC,CAAC;MAC3DgB,GAAG,CAACC,SAAS,CAAC,UAAU,CAAC;MACzBD,GAAG,CAAChB,MAAM,CAAC,EAAE,CAAC;MAEd,IAAIkL,OAAO,GAAG,EAAE;MAChB,IAAI/B,OAAO,GAAG,EAAE;MAEhB,KAAK,IAAIxJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsL,OAAO,CAAChL,MAAM,EAAE,EAAEN,CAAC,EAAE;QACrC,IAAIwE,EAAE,GAAG8G,OAAO,CAACtL,CAAC,CAAC;QACnB,IAAIwL,IAAI,GAAG1K,MAAM,CAAC0H,QAAQ,CAAChE,EAAE,CAAC;QAC9B,IAAI6G,KAAK,GAAGjB,iBAAiB,CAACqB,OAAO,CAACD,IAAI,CAAC;QAC3C,IAAIH,KAAK,IAAI,CAAC,EAAE;UACZE,OAAO,CAACpD,IAAI,CAACkD,KAAK,CAAC;QACvB,CAAC,MAAM;UACHE,OAAO,CAACpD,IAAI,CAACiC,iBAAiB,CAAC9J,MAAM,GAAGkJ,OAAO,CAAClJ,MAAM,CAAC;UACvDkJ,OAAO,CAACrB,IAAI,CAACqD,IAAI,CAAC;QACtB;MACJ;MAEAnK,GAAG,CAACE,UAAU,CAAC+J,OAAO,CAAChL,MAAM,CAAC;MAE9B,KAAKN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuL,OAAO,CAACjL,MAAM,EAAE,EAAEN,CAAC,EAAE;QACjCqB,GAAG,CAACE,UAAU,CAACgK,OAAO,CAACvL,CAAC,CAAC,CAAC;MAC9B;MAEA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwJ,OAAO,CAAClJ,MAAM,EAAE,EAAEN,CAAC,EAAE;QACjCqB,GAAG,CAACQ,SAAS,CAAC2H,OAAO,CAACxJ,CAAC,CAAC,CAACM,MAAM,CAAC;QAChCe,GAAG,CAACM,WAAW,CAAC6H,OAAO,CAACxJ,CAAC,CAAC,CAAC;MAC/B;MAEA,OAAOqB,GAAG,CAACS,GAAG,CAAC,CAAC;IACpB,CAAC;IAED,OAAOwG,SAAS;EACpB,CAAC,CAACpG,KAAK,CAAC;AACZ,CAAC,CAAE,CAAC;AAEJ,IAAIwJ,SAAS,GAAI,YAAU;EACvB,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACrM,IAAI,EAAEe,MAAM,EAAEuL,OAAO,EAAE;IACtD,IAAIC,IAAI,GAAG,IAAI;IACfA,IAAI,CAAC5C,UAAU,GAAG3J,IAAI,CAACK,SAAS,CAAC,CAAC;IAClCkM,IAAI,CAAC3C,kBAAkB,GAAG5J,IAAI,CAACK,SAAS,CAAC,CAAC;IAC1CkM,IAAI,CAACxL,MAAM,GAAGA,MAAM,GAAGf,IAAI,CAACG,QAAQ,CAAC,CAAC;IAEtCH,IAAI,CAACwM,aAAa,CAAC,YAAU;MACzB,IAAIV,IAAI;MACR9L,IAAI,CAACe,MAAM,CAACwL,IAAI,CAACxL,MAAM,CAAC;MACxBwL,IAAI,CAAC1H,MAAM,GAAG7E,IAAI,CAACK,SAAS,CAAC,CAAC;MAE9B,QAAQkM,IAAI,CAAC1H,MAAM;QACnB,KAAK,CAAC;UACF0H,IAAI,CAACvL,MAAM,GAAGhB,IAAI,CAACK,SAAS,CAAC,CAAC;UAC9BkM,IAAI,CAACE,QAAQ,GAAGzM,IAAI,CAACK,SAAS,CAAC,CAAC;UAChC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAE,EAAEA,CAAC,EAAE;YAC1B4L,OAAO,CAAC5L,CAAC,CAAC,GAAGV,IAAI,CAAC6L,QAAQ,CAAC,CAAC;UAChC;UACA;QAEJ,KAAK,CAAC;UACFU,IAAI,CAACvL,MAAM,GAAGhB,IAAI,CAACK,SAAS,CAAC,CAAC;UAC9BkM,IAAI,CAACE,QAAQ,GAAGzM,IAAI,CAACK,SAAS,CAAC,CAAC;UAChC,IAAIqM,QAAQ,GAAG1M,IAAI,CAACK,SAAS,CAAC,CAAC,GAAG,CAAC;UAEnCL,IAAI,CAACgG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAG;UAChB,IAAI2G,OAAO,GAAG3M,IAAI,CAACgF,KAAK,CAAC0H,QAAQ,EAAE1M,IAAI,CAACK,SAAS,CAAC;UAClDL,IAAI,CAACgG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAG;UAChB,IAAI4G,SAAS,GAAG5M,IAAI,CAACgF,KAAK,CAAC0H,QAAQ,EAAE1M,IAAI,CAACK,SAAS,CAAC;UACpD,IAAIwM,OAAO,GAAG7M,IAAI,CAACgF,KAAK,CAAC0H,QAAQ,EAAE1M,IAAI,CAACkE,UAAU,CAAC;UACnD,IAAI4I,aAAa,GAAG9M,IAAI,CAACgF,KAAK,CAAC0H,QAAQ,EAAE1M,IAAI,CAACK,SAAS,CAAC;UAExD,IAAI0J,KAAK,GAAG,CAACwC,IAAI,CAACvL,MAAM,GAAGuL,IAAI,CAACxL,MAAM,GAAGf,IAAI,CAACe,MAAM,CAAC,CAAC,IAAI,CAAC;UAC3D,IAAIoG,QAAQ,GAAGnH,IAAI,CAACgF,KAAK,CAAC+E,KAAK,EAAE/J,IAAI,CAACK,SAAS,CAAC;UAEhD,KAAKK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgM,QAAQ,EAAE,EAAEhM,CAAC,EAAE;YAC3B,IAAIqM,KAAK,GAAGH,SAAS,CAAClM,CAAC,CAAC;cAAEsM,GAAG,GAAGL,OAAO,CAACjM,CAAC,CAAC;YAC1C,KAAKoL,IAAI,GAAGiB,KAAK,EAAEjB,IAAI,IAAIkB,GAAG,EAAE,EAAElB,IAAI,EAAE;cACpC,IAAImB,OAAO;cACX,IAAIH,aAAa,CAACpM,CAAC,CAAC,KAAK,CAAC,EAAE;gBACxBuM,OAAO,GAAGnB,IAAI,GAAGe,OAAO,CAACnM,CAAC,CAAC;cAC/B,CAAC,MAAM;gBACH;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA,IAAIqL,KAAK,GAAGe,aAAa,CAACpM,CAAC,CAAC,GAAG,CAAC,IAAIgM,QAAQ,GAAGhM,CAAC,CAAC,IAAIoL,IAAI,GAAGiB,KAAK,CAAC;gBAClEE,OAAO,GAAG9F,QAAQ,CAAC4E,KAAK,CAAC,IAAI,CAAC;gBAC9B,IAAIkB,OAAO,KAAK,CAAC,EAAE;kBACfA,OAAO,IAAIJ,OAAO,CAACnM,CAAC,CAAC;gBACzB;cACJ;cACA4L,OAAO,CAACR,IAAI,CAAC,GAAGmB,OAAO,GAAG,MAAM;YACpC;UACJ;UACA;QAEJ,KAAK,CAAC;UACFV,IAAI,CAACvL,MAAM,GAAGhB,IAAI,CAACK,SAAS,CAAC,CAAC;UAC9BkM,IAAI,CAACE,QAAQ,GAAGzM,IAAI,CAACK,SAAS,CAAC,CAAC;UAChCyL,IAAI,GAAG9L,IAAI,CAACK,SAAS,CAAC,CAAC;UACvB,IAAIW,MAAM,GAAGhB,IAAI,CAACK,SAAS,CAAC,CAAC;UAC7B,OAAOW,MAAM,EAAE,GAAG,CAAC,EAAE;YACjBsL,OAAO,CAACR,IAAI,EAAE,CAAC,GAAG9L,IAAI,CAACK,SAAS,CAAC,CAAC;UACtC;UACA;QAEJ,KAAK,EAAE;UACHL,IAAI,CAACK,SAAS,CAAC,CAAC,CAAC,CAAC;UAClBkM,IAAI,CAACvL,MAAM,GAAGhB,IAAI,CAACG,QAAQ,CAAC,CAAC;UAC7BoM,IAAI,CAACE,QAAQ,GAAGzM,IAAI,CAACG,QAAQ,CAAC,CAAC;UAC/B,IAAI+M,OAAO,GAAGlN,IAAI,CAACG,QAAQ,CAAC,CAAC;UAC7B,OAAO+M,OAAO,EAAE,GAAG,CAAC,EAAE;YAClBpB,IAAI,GAAG9L,IAAI,CAACG,QAAQ,CAAC,CAAC;YACtB,IAAIgN,WAAW,GAAGnN,IAAI,CAACG,QAAQ,CAAC,CAAC;YACjC,IAAIiN,SAAS,GAAGpN,IAAI,CAACG,QAAQ,CAAC,CAAC;YAC/B,OAAO2L,IAAI,IAAIqB,WAAW,EAAE;cACxBb,OAAO,CAACR,IAAI,EAAE,CAAC,GAAGsB,SAAS,EAAE;YACjC;UACJ;UACA;QAEJ;UACI,IAAIC,MAAM,CAACC,OAAO,EAAE;YAChBD,MAAM,CAACC,OAAO,CAACC,KAAK,CAAC,yBAAyB,GAAGhB,IAAI,CAAC1H,MAAM,CAAC;UACjE;MACJ;IACJ,CAAC,CAAC;EACN,CAAC;EAED,SAAS2I,aAAaA,CAACC,SAAS,EAAEC,SAAS,EAAE;IACzC,IAAIC,KAAK,GAAGnO,UAAU,CAACiO,SAAS,CAAC;IACjC,IAAIG,UAAU,GAAG,EAAE;IACnB,IAAIC,QAAQ,GAAG,EAAE;IACjB,IAAIC,IAAI,GAAG,IAAI;IACf,IAAIC,IAAI,GAAG,IAAI;IAEf,SAASC,OAAOA,CAACC,QAAQ,EAAE;MACvB,OAAOP,SAAS,CAACD,SAAS,CAACQ,QAAQ,CAAC,CAAC;IACzC;IAEA,KAAK,IAAIvN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiN,KAAK,CAAC3M,MAAM,EAAE,EAAEN,CAAC,EAAE;MACnC,IAAIoL,IAAI,GAAG6B,KAAK,CAACjN,CAAC,CAAC;MACnB,IAAIwN,GAAG,GAAGF,OAAO,CAAClC,IAAI,CAAC;MACvB,IAAIqC,KAAK,GAAGD,GAAG,GAAGpC,IAAI;MACtB,IAAIgC,IAAI,IAAI,IAAI,IAAIK,KAAK,KAAKJ,IAAI,EAAE;QAChC,IAAID,IAAI,EAAE;UACND,QAAQ,CAAChF,IAAI,CAACiF,IAAI,CAAC;QACvB;QACAF,UAAU,CAAC/E,IAAI,CAACiD,IAAI,CAAC;QACrBiC,IAAI,GAAGI,KAAK;MAChB;MACAL,IAAI,GAAGhC,IAAI;IACf;IAEA,IAAIgC,IAAI,EAAE;MACND,QAAQ,CAAChF,IAAI,CAACiF,IAAI,CAAC;IACvB;IACAD,QAAQ,CAAChF,IAAI,CAAC,MAAM,CAAC;IACrB+E,UAAU,CAAC/E,IAAI,CAAC,MAAM,CAAC;IAEvB,IAAI6D,QAAQ,GAAGkB,UAAU,CAAC5M,MAAM;IAChC,IAAIoN,UAAU,GAAG1B,QAAQ,GAAG,CAAC;IAC7B,IAAIpM,WAAW,GAAG,CAAC,GAAGoB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,KAAK,CAACF,IAAI,CAACG,GAAG,CAAC6K,QAAQ,CAAC,GAAGhL,IAAI,CAACI,GAAG,CAAC,CAAC;IAC5E,IAAIvB,aAAa,GAAGmB,IAAI,CAACG,GAAG,CAACvB,WAAW,GAAG,CAAC,CAAC,GAAGoB,IAAI,CAACI,GAAG;IACxD,IAAItB,UAAU,GAAG4N,UAAU,GAAG9N,WAAW;IAEzC,IAAI+N,MAAM,GAAG,EAAE;IACf,IAAIC,YAAY,GAAG,EAAE;IACrB,IAAInH,QAAQ,GAAG,EAAE;IAEjB,KAAKzG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgM,QAAQ,EAAE,EAAEhM,CAAC,EAAE;MAC3B,IAAIkM,SAAS,GAAGgB,UAAU,CAAClN,CAAC,CAAC;MAC7B,IAAIiM,OAAO,GAAGkB,QAAQ,CAACnN,CAAC,CAAC;MACzB,IAAIkM,SAAS,IAAI,MAAM,EAAE;QACrByB,MAAM,CAACxF,IAAI,CAAC,CAAC,CAAC;QACdyF,YAAY,CAACzF,IAAI,CAAC,CAAC,CAAC;QACpB;MACJ;MACA,IAAI0F,UAAU,GAAGP,OAAO,CAACpB,SAAS,CAAC;MACnC,IAAIA,SAAS,GAAG2B,UAAU,IAAI,MAAM,EAAE;QAClCF,MAAM,CAACxF,IAAI,CAAC,CAAC,CAAC;QACdyF,YAAY,CAACzF,IAAI,CAAC,CAAC,IAAI1B,QAAQ,CAACnG,MAAM,GAAG0L,QAAQ,GAAGhM,CAAC,CAAC,CAAC;QACvD,KAAK,IAAIiK,CAAC,GAAGiC,SAAS,EAAEjC,CAAC,IAAIgC,OAAO,EAAE,EAAEhC,CAAC,EAAE;UACvCxD,QAAQ,CAAC0B,IAAI,CAACmF,OAAO,CAACrD,CAAC,CAAC,CAAC;QAC7B;MACJ,CAAC,MAAM;QACH0D,MAAM,CAACxF,IAAI,CAAC0F,UAAU,GAAG3B,SAAS,CAAC;QACnC0B,YAAY,CAACzF,IAAI,CAAC,CAAC,CAAC;MACxB;IACJ;IAEA,IAAI9G,GAAG,GAAG/C,YAAY,CAAC,CAAC;IAExB+C,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAM;IACxBF,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAM;IACxBF,GAAG,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAM;IACxBD,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAM;IACxBF,GAAG,CAACE,UAAU,CAAC,EAAE,GAAGyK,QAAQ,GAAG,CAAC,GAAGvF,QAAQ,CAACnG,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;IACzDe,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAM;IACxBF,GAAG,CAACE,UAAU,CAACmM,UAAU,CAAC;IAC1BrM,GAAG,CAACE,UAAU,CAAC3B,WAAW,CAAC;IAC3ByB,GAAG,CAACE,UAAU,CAAC1B,aAAa,CAAC;IAC7BwB,GAAG,CAACE,UAAU,CAACzB,UAAU,CAAC;IAE1BqN,QAAQ,CAACW,OAAO,CAACzM,GAAG,CAACE,UAAU,CAAC;IAChCF,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAM;IACxB2L,UAAU,CAACY,OAAO,CAACzM,GAAG,CAACE,UAAU,CAAC;IAClCoM,MAAM,CAACG,OAAO,CAACzM,GAAG,CAAC4C,WAAW,CAAC;IAC/B2J,YAAY,CAACE,OAAO,CAACzM,GAAG,CAACE,UAAU,CAAC;IACpCkF,QAAQ,CAACqH,OAAO,CAACzM,GAAG,CAACE,UAAU,CAAC;IAEhC,OAAOF,GAAG,CAACS,GAAG,CAAC,CAAC;EACpB;EAEA,OAAQ,UAAUI,KAAK,EAAE;IACrB,SAASoG,SAASA,CAAA,EAAI;MAClBpG,KAAK,CAACO,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAChC;IAEA,IAAKR,KAAK,EAAGoG,SAAS,CAAC3F,SAAS,GAAGT,KAAK;IACxCoG,SAAS,CAAC1J,SAAS,GAAGD,MAAM,CAACiE,MAAM,CAAEV,KAAK,IAAIA,KAAK,CAACtD,SAAU,CAAC;IAC/D0J,SAAS,CAAC1J,SAAS,CAACiE,WAAW,GAAGyF,SAAS;IAE3CA,SAAS,CAAC1J,SAAS,CAAC0D,KAAK,GAAG,SAASA,KAAKA,CAAEhD,IAAI,EAAE;MAC9C,IAAIuM,IAAI,GAAG,IAAI;MACf,IAAIxL,MAAM,GAAGwL,IAAI,CAACxL,MAAM;MACxBf,IAAI,CAACe,MAAM,CAACA,MAAM,CAAC;MACnBwL,IAAI,CAACD,OAAO,GAAG,CAAC,CAAC;MACjBC,IAAI,CAAC/I,OAAO,GAAGxD,IAAI,CAACK,SAAS,CAAC,CAAC;MAC/B,IAAID,UAAU,GAAGJ,IAAI,CAACK,SAAS,CAAC,CAAC;MACjCkM,IAAI,CAAC9L,MAAM,GAAGT,IAAI,CAACgF,KAAK,CAAC5E,UAAU,EAAE,YAAU;QAC3C,OAAO,IAAIiM,SAAS,CAACrM,IAAI,EAAEe,MAAM,EAAEwL,IAAI,CAACD,OAAO,CAAC;MACpD,CAAC,CAAC;IACN,CAAC;IAEDtD,SAAS,CAACzH,MAAM,GAAG,SAASA,MAAMA,CAAEkM,SAAS,EAAEC,SAAS,EAAE;MACtD,IAAI3L,GAAG,GAAG/C,YAAY,CAAC,CAAC;MACxB+C,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAE;MACpBF,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAE;MACpBF,GAAG,CAACO,KAAK,CAACkL,aAAa,CAACC,SAAS,EAAEC,SAAS,CAAC,CAAC;MAC9C,OAAO3L,GAAG,CAACS,GAAG,CAAC,CAAC;IACpB,CAAC;IAED,OAAOwG,SAAS;EACpB,CAAC,CAACpG,KAAK,CAAC;AAEZ,CAAC,CAAE,CAAC;AAEJ,IAAI6L,QAAQ,GAAI,UAAU7L,KAAK,EAAE;EAC7B,SAAS6L,QAAQA,CAAA,EAAI;IACjB7L,KAAK,CAACO,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EAChC;EAEA,IAAKR,KAAK,EAAG6L,QAAQ,CAACpL,SAAS,GAAGT,KAAK;EACvC6L,QAAQ,CAACnP,SAAS,GAAGD,MAAM,CAACiE,MAAM,CAAEV,KAAK,IAAIA,KAAK,CAACtD,SAAU,CAAC;EAC9DmP,QAAQ,CAACnP,SAAS,CAACiE,WAAW,GAAGkL,QAAQ;EAEzCA,QAAQ,CAACnP,SAAS,CAAC0D,KAAK,GAAG,SAASA,KAAKA,CAAEhD,IAAI,EAAE;IAC7CA,IAAI,CAACe,MAAM,CAAC,IAAI,CAACA,MAAM,CAAC;IACxB,IAAI,CAACyC,OAAO,GAAGxD,IAAI,CAACK,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACqO,gBAAgB,GAAG1O,IAAI,CAACkE,UAAU,CAAC,CAAC;IACzC,IAAI,CAACyK,WAAW,GAAG3O,IAAI,CAACK,SAAS,CAAC,CAAC;IACnC,IAAI,CAACuO,UAAU,GAAG5O,IAAI,CAACK,SAAS,CAAC,CAAC;IAClC,IAAI,CAACwO,IAAI,GAAG7O,IAAI,CAACK,SAAS,CAAC,CAAC;IAC5B,IAAI,CAACyO,eAAe,GAAG9O,IAAI,CAACkE,UAAU,CAAC,CAAC;IACxC,IAAI,CAAC6K,eAAe,GAAG/O,IAAI,CAACkE,UAAU,CAAC,CAAC;IACxC,IAAI,CAAC8K,iBAAiB,GAAGhP,IAAI,CAACkE,UAAU,CAAC,CAAC;IAC1C,IAAI,CAAC+K,iBAAiB,GAAGjP,IAAI,CAACkE,UAAU,CAAC,CAAC;IAC1C,IAAI,CAACgL,iBAAiB,GAAGlP,IAAI,CAACkE,UAAU,CAAC,CAAC;IAC1C,IAAI,CAACiL,iBAAiB,GAAGnP,IAAI,CAACkE,UAAU,CAAC,CAAC;IAC1C,IAAI,CAACkL,mBAAmB,GAAGpP,IAAI,CAACkE,UAAU,CAAC,CAAC;IAC5C,IAAI,CAACmL,mBAAmB,GAAGrP,IAAI,CAACkE,UAAU,CAAC,CAAC;IAC5C,IAAI,CAACoL,cAAc,GAAGtP,IAAI,CAACkE,UAAU,CAAC,CAAC;IACvC,IAAI,CAACqL,kBAAkB,GAAGvP,IAAI,CAACkE,UAAU,CAAC,CAAC;IAC3C,IAAI,CAACsL,WAAW,GAAGxP,IAAI,CAACkE,UAAU,CAAC,CAAC;IAEpC,IAAI,CAACuL,MAAM,GAAGzP,IAAI,CAACgF,KAAK,CAAC,EAAE,EAAEhF,IAAI,CAAC6L,QAAQ,CAAC;IAC3C,IAAI,CAAC6D,SAAS,GAAG1P,IAAI,CAACgF,KAAK,CAAC,CAAC,EAAEhF,IAAI,CAACG,QAAQ,CAAC;IAE7C,IAAI,CAACwP,QAAQ,GAAG3P,IAAI,CAACa,UAAU,CAAC,CAAC,CAAC;IAClC,IAAI,CAAC+O,SAAS,GAAG5P,IAAI,CAACK,SAAS,CAAC,CAAC;IACjC,IAAI,CAACwP,cAAc,GAAG7P,IAAI,CAACK,SAAS,CAAC,CAAC;IACtC,IAAI,CAACyP,aAAa,GAAG9P,IAAI,CAACK,SAAS,CAAC,CAAC;IAErC,IAAI,IAAI,CAACmD,OAAO,GAAG,CAAC,EAAE;MAClB,IAAI,CAAC8B,MAAM,GAAGtF,IAAI,CAACkE,UAAU,CAAC,CAAC;MAC/B,IAAI,CAACqB,OAAO,GAAGvF,IAAI,CAACkE,UAAU,CAAC,CAAC;MAChC,IAAI,CAACsB,OAAO,GAAGxF,IAAI,CAACkE,UAAU,CAAC,CAAC;MAChC,IAAI,CAAC6L,SAAS,GAAG/P,IAAI,CAACK,SAAS,CAAC,CAAC;MACjC,IAAI,CAAC2P,UAAU,GAAGhQ,IAAI,CAACK,SAAS,CAAC,CAAC;MAClC,IAAI,CAAC4P,aAAa,GAAGjQ,IAAI,CAACgF,KAAK,CAAC,CAAC,EAAEhF,IAAI,CAACG,QAAQ,CAAC;MAEjD,IAAI,IAAI,CAACqD,OAAO,GAAG,CAAC,EAAE;QAClB,IAAI,CAAC0M,OAAO,GAAGlQ,IAAI,CAACK,SAAS,CAAC,CAAC;QAC/B,IAAI,CAAC8P,SAAS,GAAGnQ,IAAI,CAACK,SAAS,CAAC,CAAC;QACjC,IAAI,CAAC+P,WAAW,GAAGpQ,IAAI,CAACK,SAAS,CAAC,CAAC;QACnC,IAAI,CAACgQ,SAAS,GAAGrQ,IAAI,CAACK,SAAS,CAAC,CAAC;QACjC,IAAI,CAACiQ,UAAU,GAAGtQ,IAAI,CAACK,SAAS,CAAC,CAAC;MACtC;IACJ;EACJ,CAAC;EAEDoO,QAAQ,CAACnP,SAAS,CAACiC,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAI;IAC3C,OAAO,IAAI,CAACtB,GAAG,CAAC,CAAC;EACrB,CAAC;EAED,OAAOwO,QAAQ;AACnB,CAAC,CAAC7L,KAAK,CAAE;AAET,IAAI2N,SAAS,GAAG,MAAM;AAEtB,SAASC,aAAaA,CAAA,EAAG;EACrB,IAAIC,GAAG,GAAG,EAAE;IAAE3I,CAAC,GAAG4I,MAAM,CAACH,SAAS,CAAC;EACnC,KAAK,IAAI7P,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoH,CAAC,CAAC9G,MAAM,EAAE,EAAEN,CAAC,EAAE;IAC/B+P,GAAG,IAAIC,MAAM,CAACC,YAAY,CAAC7I,CAAC,CAAC8I,UAAU,CAAClQ,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;EACzD;EACA,EAAE6P,SAAS;EACX,OAAOE,GAAG;AACd;AAEA,IAAII,OAAO,GAAG,SAASA,OAAOA,CAACC,IAAI,EAAE;EACjC,IAAI,CAACA,IAAI,GAAGA,IAAI;EAChB,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;EAChB,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;EAClB,IAAI,CAACtD,SAAS,GAAG;IAAE,CAAC,EAAE;EAAE,CAAC;EACzB,IAAI,CAACuD,SAAS,GAAG;IAAE,CAAC,EAAE;EAAE,CAAC;EACzB,IAAI,CAACxD,SAAS,GAAG,CAAC,CAAC;EACnB,IAAI,CAACyD,IAAI,GAAG,IAAI,CAACC,SAAS,GAAG,CAAC;EAC9B,IAAI,CAACC,OAAO,GAAG,CAAC;EAChB,IAAI,CAAC7G,MAAM,GAAGiG,aAAa,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAACM,IAAI,CAACvG,MAAM;AAC1D,CAAC;AAEDsG,OAAO,CAACvR,SAAS,CAAC+R,GAAG,GAAG,SAASA,GAAGA,CAAEC,EAAE,EAAE;EACtC,IAAI/E,IAAI,GAAG,IAAI;EACf,IAAI,OAAO+E,EAAE,IAAI,QAAQ,EAAE;IACvB,OAAOrS,UAAU,CAACqS,EAAE,CAAC,CAACC,MAAM,CAAC,UAASd,GAAG,EAAE3E,IAAI,EAAC;MAC5C,OAAO2E,GAAG,GAAGC,MAAM,CAACC,YAAY,CAACpE,IAAI,CAAC8E,GAAG,CAACvF,IAAI,CAAC,CAAC;IACpD,CAAC,EAAE,EAAE,CAAC;EACV;EACA,IAAIA,IAAI,GAAGS,IAAI,CAACyE,QAAQ,CAACM,EAAE,CAAC;EAC5B,IAAI,CAACxF,IAAI,EAAE;IACPA,IAAI,GAAGS,IAAI,CAAC2E,IAAI,EAAE;IAClB3E,IAAI,CAACwE,MAAM,CAACjF,IAAI,CAAC,GAAGwF,EAAE;IACtB/E,IAAI,CAACyE,QAAQ,CAACM,EAAE,CAAC,GAAGxF,IAAI;;IAExB;IACA;IACA,IAAI0F,OAAO,GAAGjF,IAAI,CAACuE,IAAI,CAACW,IAAI,CAACnF,OAAO,CAACgF,EAAE,CAAC;IACxC,IAAIE,OAAO,EAAE;MACTjF,IAAI,CAACkB,SAAS,CAAC3B,IAAI,CAAC,GAAG0F,OAAO;MAC9B,IAAIjF,IAAI,CAACmB,SAAS,CAAC8D,OAAO,CAAC,IAAI,IAAI,EAAE;QACjC,IAAIxD,OAAO,GAAGzB,IAAI,CAAC6E,OAAO,EAAE;QAC5B7E,IAAI,CAACmB,SAAS,CAAC8D,OAAO,CAAC,GAAGxD,OAAO;QACjCzB,IAAI,CAAC0E,SAAS,CAACjD,OAAO,CAAC,GAAGwD,OAAO;MACrC;IACJ;EACJ;EACA,OAAO1F,IAAI;AACf,CAAC;AAED+E,OAAO,CAACvR,SAAS,CAACoS,UAAU,GAAG,SAASA,UAAUA,CAAEhI,IAAI,EAAE;EACtD,OAAO,IAAI,CAAC2H,GAAG,CAAC3H,IAAI,CAAC;AACzB,CAAC;AAEDmH,OAAO,CAACvR,SAAS,CAAC6H,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;EAC9C,OAAO3H,UAAU,CAAC,IAAI,CAACkO,SAAS,CAAC;AACrC,CAAC;AAEDmD,OAAO,CAACvR,SAAS,CAACqS,SAAS,GAAG,SAASA,SAASA,CAAExK,QAAQ,EAAEyK,MAAM,EAAE;EAC5D,IAAIpQ,MAAM,GAAG,IAAI;EAErB,IAAI,CAACoQ,MAAM,EAAE;IACTA,MAAM,GAAG,CAAC,CAAC;EACf;EACA,KAAK,IAAIlR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyG,QAAQ,CAACnG,MAAM,EAAE,EAAEN,CAAC,EAAE;IACtC,IAAIwE,EAAE,GAAGiC,QAAQ,CAACzG,CAAC,CAAC;IACpB,IAAI,CAACkR,MAAM,CAAC1M,EAAE,CAAC,EAAE;MACb,IAAImE,KAAK,GAAGuI,MAAM,CAAC1M,EAAE,CAAC,GAAG1D,MAAM,CAACsP,IAAI,CAACe,IAAI,CAAC3I,QAAQ,CAAChE,EAAE,CAAC;MACtD,IAAImE,KAAK,IAAIA,KAAK,CAAClB,QAAQ,EAAE;QACzB3G,MAAM,CAACmQ,SAAS,CAACtI,KAAK,CAAClC,QAAQ,EAAEyK,MAAM,CAAC;MAC5C;IACJ;EACJ;EACA,OAAOA,MAAM;AACjB,CAAC;AAEDf,OAAO,CAACvR,SAAS,CAACiC,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAI;EACtC,IAAIC,MAAM,GAAG,IAAI;EAErB,IAAI8H,MAAM,GAAG,IAAI,CAACqI,SAAS,CAAC,IAAI,CAACxK,QAAQ,CAAC,CAAC,CAAC;;EAE5C;EACA,KAAK,IAAIqK,OAAO,IAAIlI,MAAM,EAAE;IACxB,IAAIpK,cAAc,CAACoK,MAAM,EAAEkI,OAAO,CAAC,EAAE;MACjCA,OAAO,GAAGM,QAAQ,CAACN,OAAO,EAAE,EAAE,CAAC;MAC/B,IAAIhQ,MAAM,CAACkM,SAAS,CAAC8D,OAAO,CAAC,IAAI,IAAI,EAAE;QACnC,IAAIxD,OAAO,GAAGxM,MAAM,CAAC4P,OAAO,EAAE;QAC9B5P,MAAM,CAACkM,SAAS,CAAC8D,OAAO,CAAC,GAAGxD,OAAO;QACnCxM,MAAM,CAACyP,SAAS,CAACjD,OAAO,CAAC,GAAGwD,OAAO;MACvC;IACJ;EACJ;;EAEA;EACA;EACA,IAAIO,WAAW,GAAGvS,UAAU,CAAC,IAAI,CAACyR,SAAS,CAAC;EAC5C,IAAIe,WAAW,GAAGD,WAAW,CAAClS,GAAG,CAAC,UAASqF,EAAE,EAAC;IAC1C,OAAO,IAAI,CAAC+L,SAAS,CAAC/L,EAAE,CAAC;EAC7B,CAAC,EAAE,IAAI,CAAC;EAER,IAAI4L,IAAI,GAAG,IAAI,CAACA,IAAI;EACpB,IAAIe,IAAI,GAAGf,IAAI,CAACe,IAAI,CAACtQ,MAAM,CAAC+H,MAAM,EAAE0I,WAAW,EAAE,IAAI,CAACtE,SAAS,CAAC;EAChE,IAAIvE,IAAI,GAAG2H,IAAI,CAAC3H,IAAI,CAAC5H,MAAM,CAACsQ,IAAI,CAAC9M,OAAO,CAAC;EAEzC,IAAI,CAACkN,QAAQ,GAAG,IAAI,CAACf,IAAI,GAAG,CAAC;EAE7B,IAAIzQ,MAAM,GAAG;IACT,MAAM,EAAG2L,SAAS,CAAC7K,MAAM,CAAC,IAAI,CAACkM,SAAS,EAAE,IAAI,CAACC,SAAS,CAAC;IACzD,MAAM,EAAGmE,IAAI,CAACvQ,KAAK;IACnB,MAAM,EAAG6H,IAAI,CAAC7H,KAAK;IACnB,MAAM,EAAGwP,IAAI,CAACoB,IAAI,CAAC3Q,MAAM,CAACyQ,WAAW,CAAC;IACtC,MAAM,EAAGlB,IAAI,CAACxJ,IAAI,CAAC/F,MAAM,CAACyQ,WAAW,CAAC;IACtC,MAAM,EAAGlB,IAAI,CAACnJ,IAAI,CAACpG,MAAM,CAACyQ,WAAW,CAAC;IACtC,MAAM,EAAGlB,IAAI,CAAC5E,IAAI,CAAC3K,MAAM,CAACyQ,WAAW,CAAC;IACtC,MAAM,EAAGlB,IAAI,CAAC5P,IAAI,CAACK,MAAM,CAAC,IAAI,CAACgJ,MAAM,CAAC;IACtC,MAAM,EAAGuG,IAAI,CAAChM,IAAI,CAACvD,MAAM,CAAC4H,IAAI,CAACtE,MAAM,CAAC;IACtC,MAAM,EAAGiM,IAAI,CAACqB,GAAG,CAAC5Q,MAAM,CAAC;EAC7B,CAAC;EAED,OAAO,IAAI,CAACuP,IAAI,CAACsB,SAAS,CAAC7Q,MAAM,CAACd,MAAM,CAAC;AAC7C,CAAC;AAEDoQ,OAAO,CAACvR,SAAS,CAAC+S,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAI;EAChD,IAAI7Q,MAAM,GAAG,IAAI;EAErB,IAAIO,GAAG,GAAG/C,YAAY,CAAC,CAAC;IAAEsT,GAAG,GAAG,CAAC;EACjC,KAAK,IAAIC,GAAG,GAAG,IAAI,CAACpB,SAAS,EAAEoB,GAAG,GAAG,IAAI,CAACrB,IAAI,EAAE,EAAEqB,GAAG,EAAE;IACnD,OAAOD,GAAG,GAAGC,GAAG,EAAE;MACdxQ,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC;MACjBqQ,GAAG,EAAE;IACT;IACA,IAAId,OAAO,GAAGhQ,MAAM,CAACiM,SAAS,CAAC8E,GAAG,CAAC;IACnC,IAAIf,OAAO,EAAE;MACT,IAAIxD,OAAO,GAAGxM,MAAM,CAACkM,SAAS,CAAC8D,OAAO,CAAC;MACvCzP,GAAG,CAACE,UAAU,CAAC+L,OAAO,CAAC;IAC3B,CAAC,MAAM;MACHjM,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC;IACrB;IACAqQ,GAAG,EAAE;EACT;EACA,OAAOvQ,GAAG,CAACS,GAAG,CAAC,CAAC;AACpB,CAAC;AAED,IAAIgQ,OAAO,GAAG,SAASA,OAAOA,CAACzP,OAAO,EAAE7B,IAAI,EAAE;EAC1C,IAAIqL,IAAI,GAAG,IAAI;EACf,IAAIvM,IAAI,GAAGuM,IAAI,CAACkG,QAAQ,GAAGzT,YAAY,CAAC+D,OAAO,CAAC;EAChD,IAAI/C,IAAI,CAACa,UAAU,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE;IAC9B,IAAIE,MAAM;IACV,IAAIiC,KAAK,GAAG,SAAAA,CAAA,EAAW;MACnBhD,IAAI,CAACe,MAAM,CAACA,MAAM,CAAC;MACnBwL,IAAI,CAACvJ,KAAK,CAAC,CAAC;IAChB,CAAC;IACD,IAAI,CAAC9B,IAAI,EAAE;MACP,MAAM,IAAIG,KAAK,CAAC,mCAAmC,CAAC;IACxD;IACArB,IAAI,CAACG,QAAQ,CAAC,CAAC,CAAC,CAAI;IACpB,IAAIuS,QAAQ,GAAG1S,IAAI,CAACG,QAAQ,CAAC,CAAC;IAC9B,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgS,QAAQ,EAAE,EAAEhS,CAAC,EAAE;MAC/BK,MAAM,GAAGf,IAAI,CAACG,QAAQ,CAAC,CAAC;MACxBH,IAAI,CAACwM,aAAa,CAACxJ,KAAK,CAAC;MACzB,IAAIuJ,IAAI,CAAChC,MAAM,IAAIrJ,IAAI,EAAE;QACrB;MACJ;IACJ;IACA,MAAM,IAAIG,KAAK,CAAC,OAAO,GAAGH,IAAI,GAAG,0BAA0B,CAAC;EAChE,CAAC,MAAM;IACHlB,IAAI,CAACe,MAAM,CAAC,CAAC,CAAC;IACdwL,IAAI,CAACvJ,KAAK,CAAC,CAAC;EAChB;AACJ,CAAC;AAEDwP,OAAO,CAAClT,SAAS,CAAC0D,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAI;EACxC,IAAIqE,GAAG,GAAG,IAAI,CAAC+K,SAAS,GAAG,IAAIrS,SAAS,CAAC,IAAI,CAAC0S,QAAQ,CAAC;EAEvD,IAAI,CAAC3N,IAAI,GAAGuC,GAAG,CAACpG,SAAS,CAAC,MAAM,EAAEiC,SAAS,CAAC;EAC5C,IAAI,CAACiG,IAAI,GAAG9B,GAAG,CAACpG,SAAS,CAAC,MAAM,EAAE2D,SAAS,CAAC;EAC5C,IAAI,CAAC0C,IAAI,GAAGD,GAAG,CAACpG,SAAS,CAAC,MAAM,EAAEoE,SAAS,CAAC;EAC5C,IAAI,CAACsC,IAAI,GAAGN,GAAG,CAACpG,SAAS,CAAC,MAAM,EAAEmF,SAAS,CAAC;EAC5C,IAAI,CAAC8L,IAAI,GAAG7K,GAAG,CAACpG,SAAS,CAAC,MAAM,EAAEmG,SAAS,CAAC;EAC5C,IAAI,CAACyK,IAAI,GAAGxK,GAAG,CAACpG,SAAS,CAAC,MAAM,EAAE+G,SAAS,CAAC;EAC5C,IAAI,CAAC9G,IAAI,GAAGmG,GAAG,CAACpG,SAAS,CAAC,MAAM,EAAEuI,SAAS,CAAC;EAC5C,IAAI,CAAC0C,IAAI,GAAG7E,GAAG,CAACpG,SAAS,CAAC,MAAM,EAAE4J,SAAS,CAAC;EAC5C,IAAI,CAAC4G,IAAI,GAAGpK,GAAG,CAACpG,SAAS,CAAC,MAAM,EAAEmL,SAAS,CAAC;EAC5C,IAAI,CAAC+F,GAAG,GAAI9K,GAAG,CAACpG,SAAS,CAAC,MAAM,EAAEwN,QAAQ,CAAC;EAE3C,IAAI,CAAClE,MAAM,GAAG,IAAI,CAACrJ,IAAI,CAACmJ,cAAc;EACtC,IAAI,CAAC/E,MAAM,GAAG,IAAI,CAAC6M,GAAG,CAAC7M,MAAM,IAAI,IAAI,CAACgC,IAAI,CAAChC,MAAM;EACjD,IAAI,CAACC,OAAO,GAAG,IAAI,CAAC4M,GAAG,CAAC5M,OAAO,IAAI,IAAI,CAAC+B,IAAI,CAAC/B,OAAO;EACpD,IAAI,CAACC,OAAO,GAAG,IAAI,CAAC2M,GAAG,CAAC3M,OAAO,IAAI,IAAI,CAAC8B,IAAI,CAAC9B,OAAO;EACpD,IAAI,CAACmN,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC7N,IAAI,CAACjB,UAAU;AAC5C,CAAC;AAED2O,OAAO,CAAClT,SAAS,CAACsT,YAAY,GAAG,SAASA,YAAYA,CAAEvJ,KAAK,EAAE;EAC3D,OAAO,IAAI,CAAC6I,IAAI,CAACrK,QAAQ,CAACwB,KAAK,CAAC,CAAC7B,OAAO,GAAG,IAAI,CAACmL,KAAK;AACzD,CAAC;AAEDH,OAAO,CAAClT,SAAS,CAACuT,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAI;EAClD,OAAO,IAAIhC,OAAO,CAAC,IAAI,CAAC;AAC5B,CAAC;AAED,SAAS2B,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}