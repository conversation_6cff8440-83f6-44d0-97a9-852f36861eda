{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { Result as i, ResultType as u } from \"./result.mjs\";\nimport { Stream as p } from \"./stream.mjs\";\nconst h = t => (t || \"\").split(\"\"),\n  a = \"\\\\\";\nclass o {\n  constructor(e) {\n    this.parse = e;\n  }\n  run(e, n = \"\") {\n    return e instanceof p ? this.parse(e) : this.parse(new p(h(e), h(n)));\n  }\n  // map :: Functor f => f a ~> (a -> b) -> f b\n  map(e) {\n    return new o(n => this.parse(n).map(e));\n  }\n  // chain :: Chain m => m a ~> (a -> m b) -> m b\n  chain(e) {\n    return new o(n => this.parse(n).chain((r, c) => e(r).run(c)));\n  }\n  isLiteral(e) {\n    return this.run(e).type === u.Literal;\n  }\n}\nconst k = t => e => new o(n => {\n    const {\n      prompt: r,\n      promptPlaceholder: c\n    } = t;\n    for (; !n.eof();) {\n      const {\n        char: w,\n        control: l\n      } = n.peek();\n      if (w === l && l === r) return n.eat(), new i(r, n, u.Mask);\n      if (e.test(w)) return n.eat(), new i(w, n, u.Mask);\n      if (w === c) return n.eat(), new i(r, n, u.Mask);\n      n.eat_input();\n    }\n    return n.eat(), new i(r, n, u.Mask);\n  }),\n  x = t => new o(e => e.peek().char === t ? (e.eat(), new i(t, e, u.Literal)) : new i(t, e, u.Literal)),\n  L = t => e => new o(n => {\n    for (; !n.eof();) {\n      const {\n        char: r,\n        control: c\n      } = n.peek();\n      if (r === t && c === t) return n.eat(), new i(r, n);\n      if (e.test(r)) return n.eat(), new i(r, n);\n      n.eat_input();\n    }\n    return n.eat(), new i(\"\", n);\n  }),\n  M = t => new o(e => {\n    if (e.eof()) return new i(\"\", e);\n    const {\n      char: n\n    } = e.peek();\n    return n === t && e.eat(), new i(t, e);\n  }),\n  A = (t, e) => new o(n => {\n    let {\n      char: r\n    } = n.next();\n    const c = t[r];\n    return r === a ? (r = n.next().char, new i(e.literal(r), n)) : c ? new i(e.mask(c), n) : new i(e.literal(r), n);\n  }),\n  R = t => new o(e => {\n    const {\n        prompt: n,\n        promptPlaceholder: r\n      } = t,\n      {\n        char: c\n      } = e.next();\n    return c === n ? new i(r, e) : new i(c, e);\n  }),\n  y = t => new o(e => {\n    const {\n      char: n\n    } = e.next();\n    return t ? new i(n, e) : new i(\"\", e);\n  });\nexport { o as Parser, x as literal, k as mask, y as rawLiteral, R as rawMask, A as token, M as unliteral, L as unmask };", "map": {"version": 3, "names": ["Result", "i", "ResultType", "u", "Stream", "p", "h", "t", "split", "a", "o", "constructor", "e", "parse", "run", "n", "map", "chain", "r", "c", "isLiteral", "type", "Literal", "k", "prompt", "promptPlaceholder", "eof", "char", "w", "control", "l", "peek", "eat", "Mask", "test", "eat_input", "x", "L", "M", "A", "next", "literal", "mask", "R", "y", "<PERSON><PERSON><PERSON>", "rawLiteral", "rawMask", "token", "unliteral", "unmask"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-inputs/maskedtextbox/parsing/parsers.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { Result as i, ResultType as u } from \"./result.mjs\";\nimport { Stream as p } from \"./stream.mjs\";\nconst h = (t) => (t || \"\").split(\"\"), a = \"\\\\\";\nclass o {\n  constructor(e) {\n    this.parse = e;\n  }\n  run(e, n = \"\") {\n    return e instanceof p ? this.parse(e) : this.parse(new p(h(e), h(n)));\n  }\n  // map :: Functor f => f a ~> (a -> b) -> f b\n  map(e) {\n    return new o((n) => this.parse(n).map(e));\n  }\n  // chain :: Chain m => m a ~> (a -> m b) -> m b\n  chain(e) {\n    return new o((n) => this.parse(n).chain((r, c) => e(r).run(c)));\n  }\n  isLiteral(e) {\n    return this.run(e).type === u.Literal;\n  }\n}\nconst k = (t) => (e) => new o((n) => {\n  const { prompt: r, promptPlaceholder: c } = t;\n  for (; !n.eof(); ) {\n    const { char: w, control: l } = n.peek();\n    if (w === l && l === r)\n      return n.eat(), new i(r, n, u.Mask);\n    if (e.test(w))\n      return n.eat(), new i(w, n, u.Mask);\n    if (w === c)\n      return n.eat(), new i(r, n, u.Mask);\n    n.eat_input();\n  }\n  return n.eat(), new i(r, n, u.Mask);\n}), x = (t) => new o((e) => e.peek().char === t ? (e.eat(), new i(t, e, u.Literal)) : new i(t, e, u.Literal)), L = (t) => (e) => new o((n) => {\n  for (; !n.eof(); ) {\n    const { char: r, control: c } = n.peek();\n    if (r === t && c === t)\n      return n.eat(), new i(r, n);\n    if (e.test(r))\n      return n.eat(), new i(r, n);\n    n.eat_input();\n  }\n  return n.eat(), new i(\"\", n);\n}), M = (t) => new o((e) => {\n  if (e.eof())\n    return new i(\"\", e);\n  const { char: n } = e.peek();\n  return n === t && e.eat(), new i(t, e);\n}), A = (t, e) => new o((n) => {\n  let { char: r } = n.next();\n  const c = t[r];\n  return r === a ? (r = n.next().char, new i(e.literal(r), n)) : c ? new i(e.mask(c), n) : new i(e.literal(r), n);\n}), R = (t) => new o((e) => {\n  const { prompt: n, promptPlaceholder: r } = t, { char: c } = e.next();\n  return c === n ? new i(r, e) : new i(c, e);\n}), y = (t) => new o((e) => {\n  const { char: n } = e.next();\n  return t ? new i(n, e) : new i(\"\", e);\n});\nexport {\n  o as Parser,\n  x as literal,\n  k as mask,\n  y as rawLiteral,\n  R as rawMask,\n  A as token,\n  M as unliteral,\n  L as unmask\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAM,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,QAAQ,cAAc;AAC3D,SAASC,MAAM,IAAIC,CAAC,QAAQ,cAAc;AAC1C,MAAMC,CAAC,GAAIC,CAAC,IAAK,CAACA,CAAC,IAAI,EAAE,EAAEC,KAAK,CAAC,EAAE,CAAC;EAAEC,CAAC,GAAG,IAAI;AAC9C,MAAMC,CAAC,CAAC;EACNC,WAAWA,CAACC,CAAC,EAAE;IACb,IAAI,CAACC,KAAK,GAAGD,CAAC;EAChB;EACAE,GAAGA,CAACF,CAAC,EAAEG,CAAC,GAAG,EAAE,EAAE;IACb,OAAOH,CAAC,YAAYP,CAAC,GAAG,IAAI,CAACQ,KAAK,CAACD,CAAC,CAAC,GAAG,IAAI,CAACC,KAAK,CAAC,IAAIR,CAAC,CAACC,CAAC,CAACM,CAAC,CAAC,EAAEN,CAAC,CAACS,CAAC,CAAC,CAAC,CAAC;EACvE;EACA;EACAC,GAAGA,CAACJ,CAAC,EAAE;IACL,OAAO,IAAIF,CAAC,CAAEK,CAAC,IAAK,IAAI,CAACF,KAAK,CAACE,CAAC,CAAC,CAACC,GAAG,CAACJ,CAAC,CAAC,CAAC;EAC3C;EACA;EACAK,KAAKA,CAACL,CAAC,EAAE;IACP,OAAO,IAAIF,CAAC,CAAEK,CAAC,IAAK,IAAI,CAACF,KAAK,CAACE,CAAC,CAAC,CAACE,KAAK,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKP,CAAC,CAACM,CAAC,CAAC,CAACJ,GAAG,CAACK,CAAC,CAAC,CAAC,CAAC;EACjE;EACAC,SAASA,CAACR,CAAC,EAAE;IACX,OAAO,IAAI,CAACE,GAAG,CAACF,CAAC,CAAC,CAACS,IAAI,KAAKlB,CAAC,CAACmB,OAAO;EACvC;AACF;AACA,MAAMC,CAAC,GAAIhB,CAAC,IAAMK,CAAC,IAAK,IAAIF,CAAC,CAAEK,CAAC,IAAK;IACnC,MAAM;MAAES,MAAM,EAAEN,CAAC;MAAEO,iBAAiB,EAAEN;IAAE,CAAC,GAAGZ,CAAC;IAC7C,OAAO,CAACQ,CAAC,CAACW,GAAG,CAAC,CAAC,GAAI;MACjB,MAAM;QAAEC,IAAI,EAAEC,CAAC;QAAEC,OAAO,EAAEC;MAAE,CAAC,GAAGf,CAAC,CAACgB,IAAI,CAAC,CAAC;MACxC,IAAIH,CAAC,KAAKE,CAAC,IAAIA,CAAC,KAAKZ,CAAC,EACpB,OAAOH,CAAC,CAACiB,GAAG,CAAC,CAAC,EAAE,IAAI/B,CAAC,CAACiB,CAAC,EAAEH,CAAC,EAAEZ,CAAC,CAAC8B,IAAI,CAAC;MACrC,IAAIrB,CAAC,CAACsB,IAAI,CAACN,CAAC,CAAC,EACX,OAAOb,CAAC,CAACiB,GAAG,CAAC,CAAC,EAAE,IAAI/B,CAAC,CAAC2B,CAAC,EAAEb,CAAC,EAAEZ,CAAC,CAAC8B,IAAI,CAAC;MACrC,IAAIL,CAAC,KAAKT,CAAC,EACT,OAAOJ,CAAC,CAACiB,GAAG,CAAC,CAAC,EAAE,IAAI/B,CAAC,CAACiB,CAAC,EAAEH,CAAC,EAAEZ,CAAC,CAAC8B,IAAI,CAAC;MACrClB,CAAC,CAACoB,SAAS,CAAC,CAAC;IACf;IACA,OAAOpB,CAAC,CAACiB,GAAG,CAAC,CAAC,EAAE,IAAI/B,CAAC,CAACiB,CAAC,EAAEH,CAAC,EAAEZ,CAAC,CAAC8B,IAAI,CAAC;EACrC,CAAC,CAAC;EAAEG,CAAC,GAAI7B,CAAC,IAAK,IAAIG,CAAC,CAAEE,CAAC,IAAKA,CAAC,CAACmB,IAAI,CAAC,CAAC,CAACJ,IAAI,KAAKpB,CAAC,IAAIK,CAAC,CAACoB,GAAG,CAAC,CAAC,EAAE,IAAI/B,CAAC,CAACM,CAAC,EAAEK,CAAC,EAAET,CAAC,CAACmB,OAAO,CAAC,IAAI,IAAIrB,CAAC,CAACM,CAAC,EAAEK,CAAC,EAAET,CAAC,CAACmB,OAAO,CAAC,CAAC;EAAEe,CAAC,GAAI9B,CAAC,IAAMK,CAAC,IAAK,IAAIF,CAAC,CAAEK,CAAC,IAAK;IAC5I,OAAO,CAACA,CAAC,CAACW,GAAG,CAAC,CAAC,GAAI;MACjB,MAAM;QAAEC,IAAI,EAAET,CAAC;QAAEW,OAAO,EAAEV;MAAE,CAAC,GAAGJ,CAAC,CAACgB,IAAI,CAAC,CAAC;MACxC,IAAIb,CAAC,KAAKX,CAAC,IAAIY,CAAC,KAAKZ,CAAC,EACpB,OAAOQ,CAAC,CAACiB,GAAG,CAAC,CAAC,EAAE,IAAI/B,CAAC,CAACiB,CAAC,EAAEH,CAAC,CAAC;MAC7B,IAAIH,CAAC,CAACsB,IAAI,CAAChB,CAAC,CAAC,EACX,OAAOH,CAAC,CAACiB,GAAG,CAAC,CAAC,EAAE,IAAI/B,CAAC,CAACiB,CAAC,EAAEH,CAAC,CAAC;MAC7BA,CAAC,CAACoB,SAAS,CAAC,CAAC;IACf;IACA,OAAOpB,CAAC,CAACiB,GAAG,CAAC,CAAC,EAAE,IAAI/B,CAAC,CAAC,EAAE,EAAEc,CAAC,CAAC;EAC9B,CAAC,CAAC;EAAEuB,CAAC,GAAI/B,CAAC,IAAK,IAAIG,CAAC,CAAEE,CAAC,IAAK;IAC1B,IAAIA,CAAC,CAACc,GAAG,CAAC,CAAC,EACT,OAAO,IAAIzB,CAAC,CAAC,EAAE,EAAEW,CAAC,CAAC;IACrB,MAAM;MAAEe,IAAI,EAAEZ;IAAE,CAAC,GAAGH,CAAC,CAACmB,IAAI,CAAC,CAAC;IAC5B,OAAOhB,CAAC,KAAKR,CAAC,IAAIK,CAAC,CAACoB,GAAG,CAAC,CAAC,EAAE,IAAI/B,CAAC,CAACM,CAAC,EAAEK,CAAC,CAAC;EACxC,CAAC,CAAC;EAAE2B,CAAC,GAAGA,CAAChC,CAAC,EAAEK,CAAC,KAAK,IAAIF,CAAC,CAAEK,CAAC,IAAK;IAC7B,IAAI;MAAEY,IAAI,EAAET;IAAE,CAAC,GAAGH,CAAC,CAACyB,IAAI,CAAC,CAAC;IAC1B,MAAMrB,CAAC,GAAGZ,CAAC,CAACW,CAAC,CAAC;IACd,OAAOA,CAAC,KAAKT,CAAC,IAAIS,CAAC,GAAGH,CAAC,CAACyB,IAAI,CAAC,CAAC,CAACb,IAAI,EAAE,IAAI1B,CAAC,CAACW,CAAC,CAAC6B,OAAO,CAACvB,CAAC,CAAC,EAAEH,CAAC,CAAC,IAAII,CAAC,GAAG,IAAIlB,CAAC,CAACW,CAAC,CAAC8B,IAAI,CAACvB,CAAC,CAAC,EAAEJ,CAAC,CAAC,GAAG,IAAId,CAAC,CAACW,CAAC,CAAC6B,OAAO,CAACvB,CAAC,CAAC,EAAEH,CAAC,CAAC;EACjH,CAAC,CAAC;EAAE4B,CAAC,GAAIpC,CAAC,IAAK,IAAIG,CAAC,CAAEE,CAAC,IAAK;IAC1B,MAAM;QAAEY,MAAM,EAAET,CAAC;QAAEU,iBAAiB,EAAEP;MAAE,CAAC,GAAGX,CAAC;MAAE;QAAEoB,IAAI,EAAER;MAAE,CAAC,GAAGP,CAAC,CAAC4B,IAAI,CAAC,CAAC;IACrE,OAAOrB,CAAC,KAAKJ,CAAC,GAAG,IAAId,CAAC,CAACiB,CAAC,EAAEN,CAAC,CAAC,GAAG,IAAIX,CAAC,CAACkB,CAAC,EAAEP,CAAC,CAAC;EAC5C,CAAC,CAAC;EAAEgC,CAAC,GAAIrC,CAAC,IAAK,IAAIG,CAAC,CAAEE,CAAC,IAAK;IAC1B,MAAM;MAAEe,IAAI,EAAEZ;IAAE,CAAC,GAAGH,CAAC,CAAC4B,IAAI,CAAC,CAAC;IAC5B,OAAOjC,CAAC,GAAG,IAAIN,CAAC,CAACc,CAAC,EAAEH,CAAC,CAAC,GAAG,IAAIX,CAAC,CAAC,EAAE,EAAEW,CAAC,CAAC;EACvC,CAAC,CAAC;AACF,SACEF,CAAC,IAAImC,MAAM,EACXT,CAAC,IAAIK,OAAO,EACZlB,CAAC,IAAImB,IAAI,EACTE,CAAC,IAAIE,UAAU,EACfH,CAAC,IAAII,OAAO,EACZR,CAAC,IAAIS,KAAK,EACVV,CAAC,IAAIW,SAAS,EACdZ,CAAC,IAAIa,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}