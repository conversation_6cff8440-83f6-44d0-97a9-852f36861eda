{"ast": null, "code": "/* Copyright 2022 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar _IdManager_id;\nimport { __classPrivateFieldGet, __classPrivateFieldSet } from \"tslib\";\nimport { AnnotationEditorPrefix } from \"../shared/utils\";\nexport class IdManager {\n  constructor(idPrefix = AnnotationEditorPrefix) {\n    _IdManager_id.set(this, 0);\n    this.idPrefix = AnnotationEditorPrefix;\n    // if (typeof PDFJSDev !== \"undefined\" && PDFJSDev.test(\"TESTING\")) {\n    Object.defineProperty(this, \"reset\", {\n      value: () => __classPrivateFieldSet(this, _IdManager_id, 0, \"f\")\n    });\n    // }\n    this.idPrefix = idPrefix;\n  }\n  get id() {\n    var _a, _b;\n    return `${this.idPrefix}${__classPrivateFieldSet(this, _IdManager_id, (_b = __classPrivateFieldGet(this, _IdManager_id, \"f\"), _a = _b++, _b), \"f\"), _a}`;\n  }\n}\n_IdManager_id = new WeakMap();", "map": {"version": 3, "names": ["_IdManager_id", "__classPrivateFieldGet", "__classPrivateFieldSet", "AnnotationEditorPrefix", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "idPrefix", "set", "Object", "defineProperty", "value", "id", "_a", "_b", "WeakMap"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-pdfviewer-common/dist/es/annotations/helpers/id-manager.js"], "sourcesContent": ["/* Copyright 2022 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar _IdManager_id;\nimport { __classPrivateFieldGet, __classPrivateFieldSet } from \"tslib\";\nimport { AnnotationEditorPrefix } from \"../shared/utils\";\nexport class IdManager {\n    constructor(idPrefix = AnnotationEditorPrefix) {\n        _IdManager_id.set(this, 0);\n        this.idPrefix = AnnotationEditorPrefix;\n        // if (typeof PDFJSDev !== \"undefined\" && PDFJSDev.test(\"TESTING\")) {\n        Object.defineProperty(this, \"reset\", {\n            value: () => (__classPrivateFieldSet(this, _IdManager_id, 0, \"f\"))\n        });\n        // }\n        this.idPrefix = idPrefix;\n    }\n    get id() {\n        var _a, _b;\n        return `${this.idPrefix}${__classPrivateFieldSet(this, _IdManager_id, (_b = __classPrivateFieldGet(this, _IdManager_id, \"f\"), _a = _b++, _b), \"f\"), _a}`;\n    }\n}\n_IdManager_id = new WeakMap();\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,aAAa;AACjB,SAASC,sBAAsB,EAAEC,sBAAsB,QAAQ,OAAO;AACtE,SAASC,sBAAsB,QAAQ,iBAAiB;AACxD,OAAO,MAAMC,SAAS,CAAC;EACnBC,WAAWA,CAACC,QAAQ,GAAGH,sBAAsB,EAAE;IAC3CH,aAAa,CAACO,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;IAC1B,IAAI,CAACD,QAAQ,GAAGH,sBAAsB;IACtC;IACAK,MAAM,CAACC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE;MACjCC,KAAK,EAAEA,CAAA,KAAOR,sBAAsB,CAAC,IAAI,EAAEF,aAAa,EAAE,CAAC,EAAE,GAAG;IACpE,CAAC,CAAC;IACF;IACA,IAAI,CAACM,QAAQ,GAAGA,QAAQ;EAC5B;EACA,IAAIK,EAAEA,CAAA,EAAG;IACL,IAAIC,EAAE,EAAEC,EAAE;IACV,OAAO,GAAG,IAAI,CAACP,QAAQ,GAAGJ,sBAAsB,CAAC,IAAI,EAAEF,aAAa,GAAGa,EAAE,GAAGZ,sBAAsB,CAAC,IAAI,EAAED,aAAa,EAAE,GAAG,CAAC,EAAEY,EAAE,GAAGC,EAAE,EAAE,EAAEA,EAAE,GAAG,GAAG,CAAC,EAAED,EAAE,EAAE;EAC5J;AACJ;AACAZ,aAAa,GAAG,IAAIc,OAAO,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}