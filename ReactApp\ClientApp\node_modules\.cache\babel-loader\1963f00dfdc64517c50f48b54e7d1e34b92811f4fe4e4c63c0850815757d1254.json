{"ast": null, "code": "export default function getEntity(keyEntities, key) {\n  return keyEntities[key];\n}", "map": {"version": 3, "names": ["getEntity", "keyEntities", "key"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/rc-tree/es/utils/keyUtil.js"], "sourcesContent": ["export default function getEntity(keyEntities, key) {\n  return keyEntities[key];\n}"], "mappings": "AAAA,eAAe,SAASA,SAASA,CAACC,WAAW,EAAEC,GAAG,EAAE;EAClD,OAAOD,WAAW,CAACC,GAAG,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}