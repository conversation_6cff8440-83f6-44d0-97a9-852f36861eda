{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as i from \"react\";\nimport t from \"prop-types\";\nimport { classNames as b } from \"@progress/kendo-react-common\";\nconst o = class o extends i.Component {\n  constructor() {\n    super(...arguments), this.itemRef = null, this.focus = () => {\n      this.itemRef && this.itemRef.focus();\n    }, this.blur = () => {\n      this.itemRef && this.itemRef.blur();\n    }, this.onClick = () => {\n      this.props.onSelect && this.props.onSelect(this.props.index), this.itemRef && (this.itemRef.tabIndex = 0, this.itemRef.focus());\n    };\n  }\n  /**\n   * @hidden\n   */\n  render() {\n    const {\n        id: a,\n        active: e,\n        disabled: s,\n        title: c = \"Untitled\",\n        index: n,\n        renderAllContent: p,\n        first: f,\n        last: m\n      } = this.props,\n      r = p ? `${this.props.contentPanelId}-${n.toString()}` : this.props.contentPanelId,\n      d = {\n        id: `${a}-${n.toString()}`,\n        \"aria-selected\": e,\n        \"aria-controls\": e ? r : void 0,\n        \"aria-disabled\": s,\n        role: \"tab\",\n        onClick: s ? void 0 : this.onClick\n      },\n      h = b(\"k-item\", \"k-tabstrip-item\", {\n        \"k-disabled\": s,\n        \"k-active\": e,\n        \"k-first\": f,\n        \"k-last\": m\n      });\n    return /* @__PURE__ */i.createElement(\"li\", {\n      ...d,\n      className: h,\n      ref: u => {\n        this.itemRef = u;\n      },\n      onBlur: this.blur,\n      \"aria-controls\": r\n    }, /* @__PURE__ */i.createElement(\"span\", {\n      className: \"k-link\"\n    }, c));\n  }\n};\no.propTypes = {\n  active: t.bool,\n  disabled: t.bool,\n  index: t.number,\n  onSelect: t.func,\n  title: t.oneOfType([t.string, t.element]),\n  first: t.bool,\n  last: t.bool\n};\nlet l = o;\nexport { l as TabStripNavigationItem };", "map": {"version": 3, "names": ["i", "t", "classNames", "b", "o", "Component", "constructor", "arguments", "itemRef", "focus", "blur", "onClick", "props", "onSelect", "index", "tabIndex", "render", "id", "a", "active", "e", "disabled", "s", "title", "c", "n", "renderAllContent", "p", "first", "f", "last", "m", "r", "contentPanelId", "toString", "d", "role", "h", "createElement", "className", "ref", "u", "onBlur", "propTypes", "bool", "number", "func", "oneOfType", "string", "element", "l", "TabStripNavigationItem"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/tabstrip/TabStripNavigationItem.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as i from \"react\";\nimport t from \"prop-types\";\nimport { classNames as b } from \"@progress/kendo-react-common\";\nconst o = class o extends i.Component {\n  constructor() {\n    super(...arguments), this.itemRef = null, this.focus = () => {\n      this.itemRef && this.itemRef.focus();\n    }, this.blur = () => {\n      this.itemRef && this.itemRef.blur();\n    }, this.onClick = () => {\n      this.props.onSelect && this.props.onSelect(this.props.index), this.itemRef && (this.itemRef.tabIndex = 0, this.itemRef.focus());\n    };\n  }\n  /**\n   * @hidden\n   */\n  render() {\n    const { id: a, active: e, disabled: s, title: c = \"Untitled\", index: n, renderAllContent: p, first: f, last: m } = this.props, r = p ? `${this.props.contentPanelId}-${n.toString()}` : this.props.contentPanelId, d = {\n      id: `${a}-${n.toString()}`,\n      \"aria-selected\": e,\n      \"aria-controls\": e ? r : void 0,\n      \"aria-disabled\": s,\n      role: \"tab\",\n      onClick: s ? void 0 : this.onClick\n    }, h = b(\"k-item\", \"k-tabstrip-item\", {\n      \"k-disabled\": s,\n      \"k-active\": e,\n      \"k-first\": f,\n      \"k-last\": m\n    });\n    return /* @__PURE__ */ i.createElement(\n      \"li\",\n      {\n        ...d,\n        className: h,\n        ref: (u) => {\n          this.itemRef = u;\n        },\n        onBlur: this.blur,\n        \"aria-controls\": r\n      },\n      /* @__PURE__ */ i.createElement(\"span\", { className: \"k-link\" }, c)\n    );\n  }\n};\no.propTypes = {\n  active: t.bool,\n  disabled: t.bool,\n  index: t.number,\n  onSelect: t.func,\n  title: t.oneOfType([t.string, t.element]),\n  first: t.bool,\n  last: t.bool\n};\nlet l = o;\nexport {\n  l as TabStripNavigationItem\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AAC9D,MAAMC,CAAC,GAAG,MAAMA,CAAC,SAASJ,CAAC,CAACK,SAAS,CAAC;EACpCC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,GAAGC,SAAS,CAAC,EAAE,IAAI,CAACC,OAAO,GAAG,IAAI,EAAE,IAAI,CAACC,KAAK,GAAG,MAAM;MAC3D,IAAI,CAACD,OAAO,IAAI,IAAI,CAACA,OAAO,CAACC,KAAK,CAAC,CAAC;IACtC,CAAC,EAAE,IAAI,CAACC,IAAI,GAAG,MAAM;MACnB,IAAI,CAACF,OAAO,IAAI,IAAI,CAACA,OAAO,CAACE,IAAI,CAAC,CAAC;IACrC,CAAC,EAAE,IAAI,CAACC,OAAO,GAAG,MAAM;MACtB,IAAI,CAACC,KAAK,CAACC,QAAQ,IAAI,IAAI,CAACD,KAAK,CAACC,QAAQ,CAAC,IAAI,CAACD,KAAK,CAACE,KAAK,CAAC,EAAE,IAAI,CAACN,OAAO,KAAK,IAAI,CAACA,OAAO,CAACO,QAAQ,GAAG,CAAC,EAAE,IAAI,CAACP,OAAO,CAACC,KAAK,CAAC,CAAC,CAAC;IACjI,CAAC;EACH;EACA;AACF;AACA;EACEO,MAAMA,CAAA,EAAG;IACP,MAAM;QAAEC,EAAE,EAAEC,CAAC;QAAEC,MAAM,EAAEC,CAAC;QAAEC,QAAQ,EAAEC,CAAC;QAAEC,KAAK,EAAEC,CAAC,GAAG,UAAU;QAAEV,KAAK,EAAEW,CAAC;QAAEC,gBAAgB,EAAEC,CAAC;QAAEC,KAAK,EAAEC,CAAC;QAAEC,IAAI,EAAEC;MAAE,CAAC,GAAG,IAAI,CAACnB,KAAK;MAAEoB,CAAC,GAAGL,CAAC,GAAG,GAAG,IAAI,CAACf,KAAK,CAACqB,cAAc,IAAIR,CAAC,CAACS,QAAQ,CAAC,CAAC,EAAE,GAAG,IAAI,CAACtB,KAAK,CAACqB,cAAc;MAAEE,CAAC,GAAG;QACrNlB,EAAE,EAAE,GAAGC,CAAC,IAAIO,CAAC,CAACS,QAAQ,CAAC,CAAC,EAAE;QAC1B,eAAe,EAAEd,CAAC;QAClB,eAAe,EAAEA,CAAC,GAAGY,CAAC,GAAG,KAAK,CAAC;QAC/B,eAAe,EAAEV,CAAC;QAClBc,IAAI,EAAE,KAAK;QACXzB,OAAO,EAAEW,CAAC,GAAG,KAAK,CAAC,GAAG,IAAI,CAACX;MAC7B,CAAC;MAAE0B,CAAC,GAAGlC,CAAC,CAAC,QAAQ,EAAE,iBAAiB,EAAE;QACpC,YAAY,EAAEmB,CAAC;QACf,UAAU,EAAEF,CAAC;QACb,SAAS,EAAES,CAAC;QACZ,QAAQ,EAAEE;MACZ,CAAC,CAAC;IACF,OAAO,eAAgB/B,CAAC,CAACsC,aAAa,CACpC,IAAI,EACJ;MACE,GAAGH,CAAC;MACJI,SAAS,EAAEF,CAAC;MACZG,GAAG,EAAGC,CAAC,IAAK;QACV,IAAI,CAACjC,OAAO,GAAGiC,CAAC;MAClB,CAAC;MACDC,MAAM,EAAE,IAAI,CAAChC,IAAI;MACjB,eAAe,EAAEsB;IACnB,CAAC,EACD,eAAgBhC,CAAC,CAACsC,aAAa,CAAC,MAAM,EAAE;MAAEC,SAAS,EAAE;IAAS,CAAC,EAAEf,CAAC,CACpE,CAAC;EACH;AACF,CAAC;AACDpB,CAAC,CAACuC,SAAS,GAAG;EACZxB,MAAM,EAAElB,CAAC,CAAC2C,IAAI;EACdvB,QAAQ,EAAEpB,CAAC,CAAC2C,IAAI;EAChB9B,KAAK,EAAEb,CAAC,CAAC4C,MAAM;EACfhC,QAAQ,EAAEZ,CAAC,CAAC6C,IAAI;EAChBvB,KAAK,EAAEtB,CAAC,CAAC8C,SAAS,CAAC,CAAC9C,CAAC,CAAC+C,MAAM,EAAE/C,CAAC,CAACgD,OAAO,CAAC,CAAC;EACzCrB,KAAK,EAAE3B,CAAC,CAAC2C,IAAI;EACbd,IAAI,EAAE7B,CAAC,CAAC2C;AACV,CAAC;AACD,IAAIM,CAAC,GAAG9C,CAAC;AACT,SACE8C,CAAC,IAAIC,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}