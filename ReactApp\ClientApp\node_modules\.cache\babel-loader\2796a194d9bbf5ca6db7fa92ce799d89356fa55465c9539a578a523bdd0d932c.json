{"ast": null, "code": "/* Copyright 2020 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * PLEASE NOTE: This file is currently imported in both the `../display/` and\n *              `../scripting_api/` folders, hence be EXTREMELY careful about\n *              introducing any dependencies here since that can lead to an\n *              unexpected/unnecessary size increase of the *built* files.\n */\nfunction makeColorComp(n) {\n  return Math.floor(Math.max(0, Math.min(1, n)) * 255).toString(16).padStart(2, \"0\");\n}\nfunction scaleAndClamp(x) {\n  return Math.max(0, Math.min(255, 255 * x));\n}\n// PDF specifications section 10.3\nclass ColorConverters {\n  static CMYK_G([c, y, m, k]) {\n    return [\"G\", 1 - Math.min(1, 0.3 * c + 0.59 * m + 0.11 * y + k)];\n  }\n  static G_CMYK([g]) {\n    return [\"CMYK\", 0, 0, 0, 1 - g];\n  }\n  static G_RGB([g]) {\n    return [\"RGB\", g, g, g];\n  }\n  static G_rgb([g]) {\n    g = scaleAndClamp(g);\n    return [g, g, g];\n  }\n  static G_HTML([g]) {\n    const G = makeColorComp(g);\n    return `#${G}${G}${G}`;\n  }\n  static RGB_G([r, g, b]) {\n    return [\"G\", 0.3 * r + 0.59 * g + 0.11 * b];\n  }\n  static RGB_rgb(color) {\n    return color.map(scaleAndClamp);\n  }\n  static RGB_HTML(color) {\n    return `#${color.map(makeColorComp).join(\"\")}`;\n  }\n  static T_HTML() {\n    return \"#00000000\";\n  }\n  static T_rgb() {\n    return [null];\n  }\n  static CMYK_RGB([c, y, m, k]) {\n    return [\"RGB\", 1 - Math.min(1, c + k), 1 - Math.min(1, m + k), 1 - Math.min(1, y + k)];\n  }\n  static CMYK_rgb([c, y, m, k]) {\n    return [scaleAndClamp(1 - Math.min(1, c + k)), scaleAndClamp(1 - Math.min(1, m + k)), scaleAndClamp(1 - Math.min(1, y + k))];\n  }\n  static CMYK_HTML(components) {\n    const rgb = this.CMYK_RGB(components).slice(1);\n    return this.RGB_HTML(rgb);\n  }\n  static RGB_CMYK([r, g, b]) {\n    const c = 1 - r;\n    const m = 1 - g;\n    const y = 1 - b;\n    const k = Math.min(c, m, y);\n    return [\"CMYK\", c, m, y, k];\n  }\n}\nexport { ColorConverters };", "map": {"version": 3, "names": ["makeColorComp", "n", "Math", "floor", "max", "min", "toString", "padStart", "scaleAndClamp", "x", "ColorConverters", "CMYK_G", "c", "y", "m", "k", "G_CMYK", "g", "G_RGB", "G_rgb", "G_HTML", "G", "RGB_G", "r", "b", "RGB_rgb", "color", "map", "RGB_HTML", "join", "T_HTML", "T_rgb", "CMYK_RGB", "CMYK_rgb", "CMYK_HTML", "components", "rgb", "slice", "RGB_CMYK"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-pdfviewer-common/dist/es/annotations/shared/scripting_utils.js"], "sourcesContent": ["/* Copyright 2020 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * PLEASE NOTE: This file is currently imported in both the `../display/` and\n *              `../scripting_api/` folders, hence be EXTREMELY careful about\n *              introducing any dependencies here since that can lead to an\n *              unexpected/unnecessary size increase of the *built* files.\n */\nfunction makeColorComp(n) {\n    return Math.floor(Math.max(0, Math.min(1, n)) * 255)\n        .toString(16)\n        .padStart(2, \"0\");\n}\nfunction scaleAndClamp(x) {\n    return Math.max(0, Math.min(255, 255 * x));\n}\n// PDF specifications section 10.3\nclass ColorConverters {\n    static CMYK_G([c, y, m, k]) {\n        return [\"G\", 1 - Math.min(1, 0.3 * c + 0.59 * m + 0.11 * y + k)];\n    }\n    static G_CMYK([g]) {\n        return [\"CMYK\", 0, 0, 0, 1 - g];\n    }\n    static G_RGB([g]) {\n        return [\"RGB\", g, g, g];\n    }\n    static G_rgb([g]) {\n        g = scaleAndClamp(g);\n        return [g, g, g];\n    }\n    static G_HTML([g]) {\n        const G = makeColorComp(g);\n        return `#${G}${G}${G}`;\n    }\n    static RGB_G([r, g, b]) {\n        return [\"G\", 0.3 * r + 0.59 * g + 0.11 * b];\n    }\n    static RGB_rgb(color) {\n        return color.map(scaleAndClamp);\n    }\n    static RGB_HTML(color) {\n        return `#${color.map(makeColorComp).join(\"\")}`;\n    }\n    static T_HTML() {\n        return \"#00000000\";\n    }\n    static T_rgb() {\n        return [null];\n    }\n    static CMYK_RGB([c, y, m, k]) {\n        return [\n            \"RGB\",\n            1 - Math.min(1, c + k),\n            1 - Math.min(1, m + k),\n            1 - Math.min(1, y + k)\n        ];\n    }\n    static CMYK_rgb([c, y, m, k]) {\n        return [\n            scaleAndClamp(1 - Math.min(1, c + k)),\n            scaleAndClamp(1 - Math.min(1, m + k)),\n            scaleAndClamp(1 - Math.min(1, y + k))\n        ];\n    }\n    static CMYK_HTML(components) {\n        const rgb = this.CMYK_RGB(components).slice(1);\n        return this.RGB_HTML(rgb);\n    }\n    static RGB_CMYK([r, g, b]) {\n        const c = 1 - r;\n        const m = 1 - g;\n        const y = 1 - b;\n        const k = Math.min(c, m, y);\n        return [\"CMYK\", c, m, y, k];\n    }\n}\nexport { ColorConverters };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,aAAaA,CAACC,CAAC,EAAE;EACtB,OAAOC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEF,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEJ,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAC/CK,QAAQ,CAAC,EAAE,CAAC,CACZC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;AACzB;AACA,SAASC,aAAaA,CAACC,CAAC,EAAE;EACtB,OAAOP,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEF,IAAI,CAACG,GAAG,CAAC,GAAG,EAAE,GAAG,GAAGI,CAAC,CAAC,CAAC;AAC9C;AACA;AACA,MAAMC,eAAe,CAAC;EAClB,OAAOC,MAAMA,CAAC,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,EAAE;IACxB,OAAO,CAAC,GAAG,EAAE,CAAC,GAAGb,IAAI,CAACG,GAAG,CAAC,CAAC,EAAE,GAAG,GAAGO,CAAC,GAAG,IAAI,GAAGE,CAAC,GAAG,IAAI,GAAGD,CAAC,GAAGE,CAAC,CAAC,CAAC;EACpE;EACA,OAAOC,MAAMA,CAAC,CAACC,CAAC,CAAC,EAAE;IACf,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGA,CAAC,CAAC;EACnC;EACA,OAAOC,KAAKA,CAAC,CAACD,CAAC,CAAC,EAAE;IACd,OAAO,CAAC,KAAK,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC;EAC3B;EACA,OAAOE,KAAKA,CAAC,CAACF,CAAC,CAAC,EAAE;IACdA,CAAC,GAAGT,aAAa,CAACS,CAAC,CAAC;IACpB,OAAO,CAACA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC;EACpB;EACA,OAAOG,MAAMA,CAAC,CAACH,CAAC,CAAC,EAAE;IACf,MAAMI,CAAC,GAAGrB,aAAa,CAACiB,CAAC,CAAC;IAC1B,OAAO,IAAII,CAAC,GAAGA,CAAC,GAAGA,CAAC,EAAE;EAC1B;EACA,OAAOC,KAAKA,CAAC,CAACC,CAAC,EAAEN,CAAC,EAAEO,CAAC,CAAC,EAAE;IACpB,OAAO,CAAC,GAAG,EAAE,GAAG,GAAGD,CAAC,GAAG,IAAI,GAAGN,CAAC,GAAG,IAAI,GAAGO,CAAC,CAAC;EAC/C;EACA,OAAOC,OAAOA,CAACC,KAAK,EAAE;IAClB,OAAOA,KAAK,CAACC,GAAG,CAACnB,aAAa,CAAC;EACnC;EACA,OAAOoB,QAAQA,CAACF,KAAK,EAAE;IACnB,OAAO,IAAIA,KAAK,CAACC,GAAG,CAAC3B,aAAa,CAAC,CAAC6B,IAAI,CAAC,EAAE,CAAC,EAAE;EAClD;EACA,OAAOC,MAAMA,CAAA,EAAG;IACZ,OAAO,WAAW;EACtB;EACA,OAAOC,KAAKA,CAAA,EAAG;IACX,OAAO,CAAC,IAAI,CAAC;EACjB;EACA,OAAOC,QAAQA,CAAC,CAACpB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,EAAE;IAC1B,OAAO,CACH,KAAK,EACL,CAAC,GAAGb,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEO,CAAC,GAAGG,CAAC,CAAC,EACtB,CAAC,GAAGb,IAAI,CAACG,GAAG,CAAC,CAAC,EAAES,CAAC,GAAGC,CAAC,CAAC,EACtB,CAAC,GAAGb,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEQ,CAAC,GAAGE,CAAC,CAAC,CACzB;EACL;EACA,OAAOkB,QAAQA,CAAC,CAACrB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,EAAE;IAC1B,OAAO,CACHP,aAAa,CAAC,CAAC,GAAGN,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEO,CAAC,GAAGG,CAAC,CAAC,CAAC,EACrCP,aAAa,CAAC,CAAC,GAAGN,IAAI,CAACG,GAAG,CAAC,CAAC,EAAES,CAAC,GAAGC,CAAC,CAAC,CAAC,EACrCP,aAAa,CAAC,CAAC,GAAGN,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEQ,CAAC,GAAGE,CAAC,CAAC,CAAC,CACxC;EACL;EACA,OAAOmB,SAASA,CAACC,UAAU,EAAE;IACzB,MAAMC,GAAG,GAAG,IAAI,CAACJ,QAAQ,CAACG,UAAU,CAAC,CAACE,KAAK,CAAC,CAAC,CAAC;IAC9C,OAAO,IAAI,CAACT,QAAQ,CAACQ,GAAG,CAAC;EAC7B;EACA,OAAOE,QAAQA,CAAC,CAACf,CAAC,EAAEN,CAAC,EAAEO,CAAC,CAAC,EAAE;IACvB,MAAMZ,CAAC,GAAG,CAAC,GAAGW,CAAC;IACf,MAAMT,CAAC,GAAG,CAAC,GAAGG,CAAC;IACf,MAAMJ,CAAC,GAAG,CAAC,GAAGW,CAAC;IACf,MAAMT,CAAC,GAAGb,IAAI,CAACG,GAAG,CAACO,CAAC,EAAEE,CAAC,EAAED,CAAC,CAAC;IAC3B,OAAO,CAAC,MAAM,EAAED,CAAC,EAAEE,CAAC,EAAED,CAAC,EAAEE,CAAC,CAAC;EAC/B;AACJ;AACA,SAASL,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}