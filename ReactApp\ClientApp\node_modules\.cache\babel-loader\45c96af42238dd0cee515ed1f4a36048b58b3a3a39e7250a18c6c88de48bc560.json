{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nconst a = t => /* @__PURE__ */e.createElement(\"div\", {\n  className: \"k-nodata\"\n}, /* @__PURE__ */e.createElement(\"div\", null, t.children));\nexport { a as ListNoData };", "map": {"version": 3, "names": ["e", "a", "t", "createElement", "className", "children", "ListNoData"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dropdowns/DropDownTree/ListNoData.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nconst a = (t) => /* @__PURE__ */ e.createElement(\"div\", { className: \"k-nodata\" }, /* @__PURE__ */ e.createElement(\"div\", null, t.children));\nexport {\n  a as ListNoData\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,MAAMC,CAAC,GAAIC,CAAC,IAAK,eAAgBF,CAAC,CAACG,aAAa,CAAC,KAAK,EAAE;EAAEC,SAAS,EAAE;AAAW,CAAC,EAAE,eAAgBJ,CAAC,CAACG,aAAa,CAAC,KAAK,EAAE,IAAI,EAAED,CAAC,CAACG,QAAQ,CAAC,CAAC;AAC5I,SACEJ,CAAC,IAAIK,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}