{"ast": null, "code": "/* eslint-disable no-loop-func */\n\nimport parents from './parents';\nimport siblings from './siblings';\nexport default function (anchor, container) {\n  var parentElements = parents(anchor);\n  var containerElement = container;\n  var siblingElements;\n  var result;\n  while (containerElement) {\n    siblingElements = siblings(containerElement);\n    result = parentElements.reduce(function (list, p) {\n      return list.concat(siblingElements.filter(function (s) {\n        return s === p;\n      }));\n    }, [])[0];\n    if (result) {\n      break;\n    }\n    containerElement = containerElement.parentElement;\n  }\n  return result;\n}\n;", "map": {"version": 3, "names": ["parents", "siblings", "anchor", "container", "parentElements", "containerElement", "siblingElements", "result", "reduce", "list", "p", "concat", "filter", "s", "parentElement"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-popup-common/dist/es/sibling-container.js"], "sourcesContent": ["/* eslint-disable no-loop-func */\n\nimport parents from './parents';\nimport siblings from './siblings';\n\nexport default function (anchor, container) {\n    var parentElements = parents(anchor);\n    var containerElement = container;\n    var siblingElements;\n    var result;\n\n    while (containerElement) {\n        siblingElements = siblings(containerElement);\n\n        result = parentElements.reduce(\n            function (list, p) { return list.concat(siblingElements.filter(function (s) { return s === p; })); },\n            []\n        )[0];\n\n        if (result) { break; }\n\n        containerElement = containerElement.parentElement;\n    }\n\n    return result;\n};\n\n"], "mappings": "AAAA;;AAEA,OAAOA,OAAO,MAAM,WAAW;AAC/B,OAAOC,QAAQ,MAAM,YAAY;AAEjC,eAAe,UAAUC,MAAM,EAAEC,SAAS,EAAE;EACxC,IAAIC,cAAc,GAAGJ,OAAO,CAACE,MAAM,CAAC;EACpC,IAAIG,gBAAgB,GAAGF,SAAS;EAChC,IAAIG,eAAe;EACnB,IAAIC,MAAM;EAEV,OAAOF,gBAAgB,EAAE;IACrBC,eAAe,GAAGL,QAAQ,CAACI,gBAAgB,CAAC;IAE5CE,MAAM,GAAGH,cAAc,CAACI,MAAM,CAC1B,UAAUC,IAAI,EAAEC,CAAC,EAAE;MAAE,OAAOD,IAAI,CAACE,MAAM,CAACL,eAAe,CAACM,MAAM,CAAC,UAAUC,CAAC,EAAE;QAAE,OAAOA,CAAC,KAAKH,CAAC;MAAE,CAAC,CAAC,CAAC;IAAE,CAAC,EACpG,EACJ,CAAC,CAAC,CAAC,CAAC;IAEJ,IAAIH,MAAM,EAAE;MAAE;IAAO;IAErBF,gBAAgB,GAAGA,gBAAgB,CAACS,aAAa;EACrD;EAEA,OAAOP,MAAM;AACjB;AAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}