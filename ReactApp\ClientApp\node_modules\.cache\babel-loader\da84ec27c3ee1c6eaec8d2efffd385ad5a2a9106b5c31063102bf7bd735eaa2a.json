{"ast": null, "code": "/* eslint-disable no-multi-spaces, key-spacing, indent, camelcase, space-before-blocks, eqeqeq, brace-style */\n/* eslint-disable space-infix-ops, space-before-function-paren, array-bracket-spacing, object-curly-spacing */\n/* eslint-disable no-nested-ternary, max-params, default-case, no-else-return, no-empty, yoda */\n/* eslint-disable no-param-reassign, no-var, block-scoped-var */\n\nimport * as geo from \"../geometry\";\nimport * as PDF from \"../pdf\";\nimport { arabicToRoman, createPromise, measureText, mergeSort } from '../util';\nimport { parseColor as utils_parseColor, support, template as compileTemplate } from \"../common\";\nimport { Path, Text, Group, Image, Circle, Rect, LinearGradient } from \"../drawing\";\nimport { encodeBase64 } from \"../util\";\nimport { setInnerHTML, setStyle } from \"../util/element-set-styles-safe\";\nvar browser = support.browser || {};\n/*\n\n  XXX: to test:\n\n  - cloneNodes function:\n    - drawing document containing canvas with page breaking\n    - drawing document with named radio <input>-s (should not clear selection)\n    - IE9/IE10 don't support el.dataset; do they copy user data?\n\n  - repeating table headers/footers on page breaking\n\n  - forceBreak, keepTogether\n\n  - avoidLinks\n\n */\n\n/* -----[ local vars ]----- */\n\nfunction slice(thing) {\n  return Array.prototype.slice.call(thing);\n}\nvar KENDO_PSEUDO_ELEMENT = \"KENDO-PSEUDO-ELEMENT\";\nvar KENDO_BULLET_TYPE = 'data-kendo-bullet-type';\nvar IMAGE_CACHE = {};\nvar nodeInfo = {};\nnodeInfo._root = nodeInfo;\n\n/* -----[ Custom Text node to speed up rendering in PDF ]----- */\n\nvar inBrowser = typeof window !== 'undefined';\nvar microsoft = inBrowser ? browser.msie || browser.edge : false;\nvar TextRect = function (Text) {\n  function TextRect(str, rect, options) {\n    Text.call(this, str, rect.getOrigin(), options);\n    this._pdfRect = rect;\n  }\n  if (Text) TextRect.__proto__ = Text;\n  TextRect.prototype = Object.create(Text && Text.prototype);\n  TextRect.prototype.constructor = TextRect;\n  TextRect.prototype.rect = function rect() {\n    // this is the crux of it: we can avoid a call to\n    // measure(), which is what the base class does, since we\n    // already know the rect.  measure() is s-l-o-w.\n    return this._pdfRect;\n  };\n  TextRect.prototype.rawBBox = function rawBBox() {\n    // also let's avoid creating a new rectangle.\n    return this._pdfRect;\n  };\n  return TextRect;\n}(Text);\nfunction addClass(el, cls) {\n  if (el.classList) {\n    el.classList.add(cls);\n  } else {\n    el.className += \" \" + cls;\n  }\n}\nfunction removeClass(el, cls) {\n  if (el.classList) {\n    el.classList.remove(cls);\n  } else {\n    el.className = el.className.split(/\\s+/).reduce(function (a, word) {\n      if (word != cls) {\n        a.push(word);\n      }\n      return a;\n    }, []).join(\" \");\n  }\n}\nfunction setCSS(el, styles) {\n  Object.keys(styles).forEach(function (key) {\n    el.style[key] = styles[key];\n  });\n}\nvar matches = typeof Element !== \"undefined\" && Element.prototype && function (p) {\n  if (p.matches) {\n    return function (el, selector) {\n      return el.matches(selector);\n    };\n  }\n  if (p.webkitMatchesSelector) {\n    return function (el, selector) {\n      return el.webkitMatchesSelector(selector);\n    };\n  }\n  if (p.mozMatchesSelector) {\n    return function (el, selector) {\n      return el.mozMatchesSelector(selector);\n    };\n  }\n  if (p.msMatchesSelector) {\n    return function (el, selector) {\n      return el.msMatchesSelector(selector);\n    };\n  }\n  return function (s) {\n    return [].indexOf.call(document.querySelectorAll(s), this) !== -1;\n  };\n}(Element.prototype);\nfunction closest(el, selector) {\n  if (el.closest) {\n    return el.closest(selector);\n  }\n  // IE: stringifying rather than simply comparing with `document`,\n  // which is not iframe-proof and fails in editor export —\n  // https://github.com/telerik/kendo/issues/6721\n  while (el && !/^\\[object (?:HTML)?Document\\]$/.test(String(el))) {\n    if (el.nodeType == 1 /* Element */ && matches(el, selector)) {\n      return el;\n    }\n    el = el.parentNode;\n  }\n}\n\n// clone nodes ourselves, so that we redraw <canvas> (DOM or\n// jQuery clone will not)\nvar cloneNodes = function ($) {\n  if ($) {\n    // if we have Kendo and jQuery, use this version as it will\n    // maintain proper links between cloned element and Kendo\n    // widgets (i.e. it clones jQuery data(), which isn't the same\n    // as element's data attributes).\n    // https://github.com/telerik/kendo-ui-core/issues/2750\n    return function cloneNodes(el) {\n      var clone = el.cloneNode(false);\n      if (el.nodeType == 1 /* Element */) {\n        var $el = $(el),\n          $clone = $(clone),\n          i;\n        var data = $el.data();\n        for (i in data) {\n          $clone.data(i, data[i]);\n        }\n        if (/^canvas$/i.test(el.tagName)) {\n          clone.getContext(\"2d\").drawImage(el, 0, 0);\n        } else if (/^(?:input|select|textarea|option)$/i.test(el.tagName)) {\n          // drop the name attributes so that we don't affect the selection of the\n          // original nodes (i.e. checked status of radio buttons) when we insert our copy\n          // into the DOM.  https://github.com/telerik/kendo/issues/5409\n          clone.removeAttribute(\"id\");\n          clone.removeAttribute(\"name\");\n          if (!/^textarea$/i.test(el.tagName)) {\n            clone.value = el.value;\n          }\n          clone.checked = el.checked;\n          clone.selected = el.selected;\n        }\n        if (el._kendoExportVisual) {\n          clone._kendoExportVisual = el._kendoExportVisual;\n        }\n        for (i = el.firstChild; i; i = i.nextSibling) {\n          clone.appendChild(cloneNodes(i));\n        }\n      }\n      return clone;\n    };\n  } else {\n    // the no-jQuery version\n    return function cloneNodes(el) {\n      var clone = function dive(node) {\n        var clone = node.cloneNode(false);\n        if (node._kendoExportVisual) {\n          clone._kendoExportVisual = node._kendoExportVisual;\n        }\n        for (var i = node.firstChild; i; i = i.nextSibling) {\n          clone.appendChild(dive(i));\n        }\n        return clone;\n      }(el);\n\n      // re-draw canvases - https://github.com/telerik/kendo/issues/4872\n      var canvases = el.querySelectorAll(\"canvas\");\n      if (canvases.length) {\n        slice(clone.querySelectorAll(\"canvas\")).forEach(function (canvas, i) {\n          canvas.getContext(\"2d\").drawImage(canvases[i], 0, 0);\n        });\n      }\n\n      // remove \"name\" attributes from <input> elements -\n      // https://github.com/telerik/kendo/issues/5409\n      var orig = el.querySelectorAll(\"input, select, textarea, option\");\n      slice(clone.querySelectorAll(\"input, select, textarea, option\")).forEach(function (el, i) {\n        el.removeAttribute(\"id\");\n        el.removeAttribute(\"name\");\n        if (!/^textarea$/i.test(el.tagName)) {\n          el.value = orig[i].value;\n        }\n        el.checked = orig[i].checked;\n        el.selected = orig[i].selected;\n      });\n      return clone;\n    };\n  }\n}(typeof window !== \"undefined\" && window.kendo && window.kendo.jQuery);\nfunction getXY(thing) {\n  if (typeof thing == \"number\") {\n    return {\n      x: thing,\n      y: thing\n    };\n  }\n  if (Array.isArray(thing)) {\n    return {\n      x: thing[0],\n      y: thing[1]\n    };\n  }\n  return {\n    x: thing.x,\n    y: thing.y\n  };\n}\nfunction drawDOM(element, options) {\n  if (!options) {\n    options = {};\n  }\n  var promise = createPromise();\n  if (!element) {\n    return promise.reject(\"No element to export\");\n  }\n  if (typeof window.getComputedStyle != \"function\") {\n    throw new Error(\"window.getComputedStyle is missing.  You are using an unsupported browser, or running in IE8 compatibility mode.  Drawing HTML is supported in Chrome, Firefox, Safari and IE9+.\");\n  }\n  PDF.defineFont(getFontFaces(element.ownerDocument));\n  var scale = getXY(options.scale || 1);\n  function doOne(element) {\n    var group = new Group();\n\n    // translate to start of page\n    var pos = element.getBoundingClientRect();\n    setTransform(group, [scale.x, 0, 0, scale.y, -pos.left * scale.x, -pos.top * scale.y]);\n    nodeInfo._clipbox = false;\n    nodeInfo._matrix = geo.Matrix.unit();\n    nodeInfo._stackingContext = {\n      element: element,\n      group: group\n    };\n    if (options.avoidLinks === true) {\n      nodeInfo._avoidLinks = \"a\";\n    } else {\n      nodeInfo._avoidLinks = options.avoidLinks;\n    }\n    addClass(element, \"k-pdf-export\");\n    renderElement(element, group);\n    removeClass(element, \"k-pdf-export\");\n    return group;\n  }\n  cacheImages([element], function () {\n    var forceBreak = options && options.forcePageBreak;\n    var hasPaperSize = options && options.paperSize && options.paperSize != \"auto\";\n    var paperOptions = PDF.getPaperOptions(function (key, def) {\n      if (key == \"paperSize\") {\n        // PDF.getPaperOptions croaks on \"auto\", just pass dummy A4 as we might\n        // still be interested in margins.\n        return hasPaperSize ? options[key] : \"A4\";\n      }\n      return key in options ? options[key] : def;\n    });\n    var pageWidth = hasPaperSize && paperOptions.paperSize[0];\n    var pageHeight = hasPaperSize && paperOptions.paperSize[1];\n    var margin = options.margin && paperOptions.margin;\n    var hasMargin = Boolean(margin);\n    if (forceBreak || pageHeight) {\n      if (!margin) {\n        margin = {\n          left: 0,\n          top: 0,\n          right: 0,\n          bottom: 0\n        };\n      }\n\n      // we want paper size and margin to be unaffected by\n      // scaling in the output, so we have to reverse-scale\n      // before our calculations begin.\n      if (pageWidth) {\n        pageWidth /= scale.x;\n      }\n      if (pageHeight) {\n        pageHeight /= scale.y;\n      }\n      margin.left /= scale.x;\n      margin.right /= scale.x;\n      margin.top /= scale.y;\n      margin.bottom /= scale.y;\n      var group = new Group({\n        pdf: {\n          multiPage: true,\n          paperSize: hasPaperSize ? paperOptions.paperSize : \"auto\",\n          _ignoreMargin: hasMargin // HACK!  see exportPDF in pdf/drawing.js\n        }\n      });\n      handlePageBreaks(function (x) {\n        if (options.progress) {\n          var canceled = false,\n            pageNum = 0;\n          (function next() {\n            if (pageNum < x.pages.length) {\n              var page = doOne(x.pages[pageNum]);\n              group.append(page);\n              options.progress({\n                page: page,\n                pageNum: ++pageNum,\n                totalPages: x.pages.length,\n                cancel: function () {\n                  canceled = true;\n                }\n              });\n              if (!canceled) {\n                setTimeout(next);\n              } else {\n                // XXX: should we also fail() the deferred object?\n                x.container.parentNode.removeChild(x.container);\n              }\n            } else {\n              x.container.parentNode.removeChild(x.container);\n              promise.resolve(group);\n            }\n          })();\n        } else {\n          x.pages.forEach(function (page) {\n            group.append(doOne(page));\n          });\n          x.container.parentNode.removeChild(x.container);\n          promise.resolve(group);\n        }\n      }, element, forceBreak, pageWidth ? pageWidth - margin.left - margin.right : null, pageHeight ? pageHeight - margin.top - margin.bottom : null, margin, options);\n    } else {\n      promise.resolve(doOne(element));\n    }\n  });\n  function makeTemplate(template) {\n    if (template != null) {\n      if (typeof template == \"string\") {\n        template = compileTemplate(template.replace(/^\\s+|\\s+$/g, \"\"));\n      }\n      if (typeof template == \"function\") {\n        return function (data) {\n          var el = template(data);\n          if (el && typeof el == \"string\") {\n            var div = document.createElement(\"div\");\n            setInnerHTML(div, el);\n            el = div.firstElementChild;\n          }\n          return el;\n        };\n      }\n      // assumed DOM element\n      return function () {\n        return template.cloneNode(true);\n      };\n    }\n  }\n  function handlePageBreaks(callback, element, forceBreak, pageWidth, pageHeight, margin, options) {\n    var template = makeTemplate(options.template);\n    var doc = element.ownerDocument;\n    var pages = [];\n    var copy = options._destructive ? element : cloneNodes(element);\n    var container = doc.createElement(\"KENDO-PDF-DOCUMENT\");\n    var adjust = 0;\n\n    // make sure <tfoot> elements are at the end (Grid widget\n    // places TFOOT before TBODY, tricking our algorithm to\n    // insert a page break right after the header).\n    // https://github.com/telerik/kendo/issues/4699\n    slice(copy.querySelectorAll(\"tfoot\")).forEach(function (tfoot) {\n      tfoot.parentNode.appendChild(tfoot);\n    });\n\n    // remember the index of each LI from an ordered list.\n    // we'll use it to reconstruct the proper numbering.\n    slice(copy.querySelectorAll(\"ol\")).forEach(function (ol) {\n      slice(ol.children).forEach(function (li, index) {\n        li.setAttribute(\"kendo-split-index\", index);\n      });\n    });\n    setCSS(container, {\n      display: \"block\",\n      position: \"absolute\",\n      boxSizing: \"content-box\",\n      left: \"-10000px\",\n      top: \"-10000px\"\n    });\n    if (pageWidth) {\n      // subtle: if we don't set the width *and* margins here, the layout in this\n      // container will be different from the one in our final page elements, and we'll\n      // split at the wrong places.\n      setCSS(container, {\n        width: pageWidth + \"px\",\n        paddingLeft: margin.left + \"px\",\n        paddingRight: margin.right + \"px\"\n      });\n\n      // when the first element has a margin-top (i.e. a <h1>) the page will be\n      // inadvertently enlarged by that number (the browser will report the container's\n      // bounding box top to start at the element's top, rather than including its\n      // margin).  Adding overflow: hidden seems to fix it.\n      //\n      // to understand the difference, try the following snippets in your browser:\n      //\n      // 1. <div style=\"background: yellow\">\n      //      <h1 style=\"margin: 3em\">Foo</h1>\n      //    </div>\n      //\n      // 2. <div style=\"background: yellow; overflow: hidden\">\n      //      <h1 style=\"margin: 3em\">Foo</h1>\n      //    </div>\n      //\n      // this detail is not important when automatic page breaking is not requested, hence\n      // doing it only if pageWidth is defined.\n      setCSS(copy, {\n        overflow: \"hidden\"\n      });\n    }\n    element.parentNode.insertBefore(container, element);\n    container.appendChild(copy);\n\n    // With cache disabled, images will still have height zero until their `complete` attribute\n    // is true.  `whenImagesAreActuallyLoaded` will wait for it.\n    if (options.beforePageBreak) {\n      whenImagesAreActuallyLoaded([container], function () {\n        options.beforePageBreak(container, doPageBreak);\n      });\n    } else {\n      whenImagesAreActuallyLoaded([container], doPageBreak);\n    }\n    function doPageBreak() {\n      if (forceBreak != \"-\" || pageHeight) {\n        splitElement(copy);\n      }\n      {\n        var page = makePage();\n        copy.parentNode.insertBefore(page, copy);\n        page.appendChild(copy);\n      }\n      if (template) {\n        pages.forEach(function (page, i) {\n          var el = template({\n            element: page,\n            pageNum: i + 1,\n            totalPages: pages.length\n          });\n          if (el) {\n            page.appendChild(el);\n          }\n        });\n      }\n      cacheImages(pages, callback.bind(null, {\n        pages: pages,\n        container: container\n      }));\n    }\n    function keepTogether(el) {\n      if (options.keepTogether && matches(el, options.keepTogether) && el.offsetHeight <= pageHeight - adjust) {\n        return true;\n      }\n      var tag = el.tagName;\n      if (/^h[1-6]$/i.test(tag) && el.offsetHeight >= pageHeight - adjust) {\n        return false;\n      }\n      return el.getAttribute(\"data-kendo-chart\") || /^(?:img|tr|thead|th|tfoot|iframe|svg|object|canvas|input|textarea|select|video|h[1-6])/i.test(el.tagName);\n    }\n    function splitElement(element) {\n      if (element.tagName == \"TABLE\") {\n        setCSS(element, {\n          tableLayout: \"fixed\"\n        });\n      }\n      if (keepTogether(element)) {\n        return;\n      }\n      var style = getComputedStyle(element);\n      var bottomPadding = parseFloat(getPropertyValue(style, \"padding-bottom\"));\n      var bottomBorder = parseFloat(getPropertyValue(style, \"border-bottom-width\"));\n      var saveAdjust = adjust;\n      adjust += bottomPadding + bottomBorder;\n      var isFirst = true;\n      for (var el = element.firstChild; el; el = el.nextSibling) {\n        if (el.nodeType == 1 /* Element */) {\n          isFirst = false;\n          if (matches(el, forceBreak)) {\n            breakAtElement(el);\n            continue;\n          }\n          if (!pageHeight) {\n            // we're in \"manual breaks mode\"\n            splitElement(el);\n            continue;\n          }\n          if (!/^(?:static|relative)$/.test(getPropertyValue(getComputedStyle(el), \"position\"))) {\n            continue;\n          }\n          var fall = fallsOnMargin(el);\n          if (fall == 1) {\n            // element starts on next page, break before anyway.\n            breakAtElement(el);\n          } else if (fall) {\n            // elements ends up on next page, or possibly doesn't fit on a page at\n            // all.  break before it anyway if it's an <img> or <tr>, otherwise\n            // attempt to split.\n            if (keepTogether(el)) {\n              breakAtElement(el);\n            } else {\n              splitElement(el);\n            }\n          } else {\n            splitElement(el);\n          }\n        } else if (el.nodeType == 3 /* Text */ && pageHeight) {\n          splitText(el, isFirst);\n          isFirst = false;\n        }\n      }\n      adjust = saveAdjust;\n    }\n    function firstInParent(el) {\n      var p = el.parentNode,\n        first = p.firstChild;\n      if (el === first) {\n        return true;\n      }\n      if (el === p.children[0]) {\n        if (first.nodeType == 7 /* comment */ || first.nodeType == 8 /* processing instruction */) {\n          return true;\n        }\n        if (first.nodeType == 3 /* text */) {\n          // if whitespace only we can probably consider it's first\n          return !/\\S/.test(first.data);\n        }\n      }\n      return false;\n    }\n    function breakAtElement(el) {\n      if (el.nodeType == 1 && el !== copy && firstInParent(el)) {\n        return breakAtElement(el.parentNode);\n      }\n      var table, colgroup, thead, grid, gridHead;\n      table = closest(el, \"table\");\n      colgroup = table && table.querySelector(\"colgroup\");\n      if (options.repeatHeaders) {\n        thead = table && table.querySelector(\"thead\");\n\n        // If we break page in a Kendo Grid, repeat its header.  This ugly hack is\n        // necessary because a scrollable grid will keep the header in a separate\n        // <table> element from its content.\n        //\n        // XXX: This is likely to break as soon as the widget HTML is modified.\n        grid = closest(el, \".k-grid\");\n        if (grid && grid.querySelector(\".k-auto-scrollable\")) {\n          gridHead = grid.querySelector(\".k-grid-header\");\n        }\n      }\n      var page = makePage();\n      var range = doc.createRange();\n      range.setStartBefore(copy);\n      range.setEndBefore(el);\n      page.appendChild(range.extractContents());\n      copy.parentNode.insertBefore(page, copy);\n      preventBulletOnListItem(el.parentNode);\n      if (table) {\n        table = closest(el, \"table\"); // that's the <table> on next page!\n        if (options.repeatHeaders && thead) {\n          table.insertBefore(thead.cloneNode(true), table.firstChild);\n        }\n        if (colgroup) {\n          table.insertBefore(colgroup.cloneNode(true), table.firstChild);\n        }\n      }\n      if (options.repeatHeaders && gridHead) {\n        grid = closest(el, \".k-grid\");\n        grid.insertBefore(gridHead.cloneNode(true), grid.firstChild);\n      }\n    }\n    function makePage() {\n      var page = doc.createElement(\"KENDO-PDF-PAGE\");\n      setCSS(page, {\n        display: \"block\",\n        boxSizing: \"content-box\",\n        width: pageWidth ? pageWidth + \"px\" : \"auto\",\n        padding: margin.top + \"px \" + margin.right + \"px \" + margin.bottom + \"px \" + margin.left + \"px\",\n        // allow absolutely positioned elements to be relative to current page\n        position: \"relative\",\n        // without the following we might affect layout of subsequent pages\n        height: pageHeight ? pageHeight + \"px\" : \"auto\",\n        overflow: pageHeight || pageWidth ? \"hidden\" : \"visible\",\n        clear: \"both\"\n      });\n\n      // debug\n      // $(\"<div>\").css({\n      //     position  : \"absolute\",\n      //     left      : margin.left,\n      //     top       : margin.top,\n      //     width     : pageWidth,\n      //     height    : pageHeight,\n      //     boxSizing : \"border-box\",\n      //     background: \"rgba(255, 255, 0, 0.5)\"\n      //     //border    : \"1px solid red\"\n      // }).appendTo(page);\n\n      if (options && options.pageClassName) {\n        page.className = options.pageClassName;\n      }\n      pages.push(page);\n      return page;\n    }\n    function fallsOnMargin(thing) {\n      var box = thing.getBoundingClientRect();\n      if (box.width === 0 || box.height === 0) {\n        // I'd say an element with dimensions zero fits on current page.\n        return 0;\n      }\n      var top = copy.getBoundingClientRect().top;\n      var available = pageHeight - adjust;\n      return box.height > available ? 3 : box.top - top > available ? 1 : box.bottom - top > available ? 2 : 0;\n    }\n    function splitText(node, isFirst) {\n      if (!/\\S/.test(node.data)) {\n        return;\n      }\n      var len = node.data.length;\n      var range = doc.createRange();\n      range.selectNodeContents(node);\n      var fall = fallsOnMargin(range);\n      if (!fall) {\n        return; // the whole text fits on current page\n      }\n      var nextnode = node;\n      if (fall == 1) {\n        // starts on next page, break before anyway.\n        if (isFirst) {\n          // avoid leaving an empty <p>, <li>, etc. on previous page.\n          breakAtElement(node.parentNode);\n        } else {\n          breakAtElement(node);\n        }\n      } else {\n        (function findEOP(min, pos, max) {\n          range.setEnd(node, pos);\n          if (min == pos || pos == max) {\n            return pos;\n          }\n          if (fallsOnMargin(range)) {\n            return findEOP(min, min + pos >> 1, pos);\n          } else {\n            return findEOP(pos, pos + max >> 1, max);\n          }\n        })(0, len >> 1, len);\n        if (!/\\S/.test(range.toString()) && isFirst) {\n          // avoid leaving an empty <p>, <li>, etc. on previous page.\n          breakAtElement(node.parentNode);\n        } else {\n          // This is only needed for IE, but it feels cleaner to do it anyway.  Without\n          // it, IE will truncate a very long text (playground/pdf-long-text-2.html).\n          nextnode = node.splitText(range.endOffset);\n          var page = makePage();\n          range.setStartBefore(copy);\n          page.appendChild(range.extractContents());\n          copy.parentNode.insertBefore(page, copy);\n          preventBulletOnListItem(nextnode.parentNode);\n        }\n      }\n      splitText(nextnode);\n    }\n    function preventBulletOnListItem(el) {\n      // set a hint on continued LI elements, to tell the\n      // renderer not to draw the bullet again.\n      // https://github.com/telerik/kendo-ui-core/issues/2732\n      var li = closest(el, \"li\");\n      if (li) {\n        li.setAttribute(\"kendo-no-bullet\", \"1\");\n        preventBulletOnListItem(li.parentNode);\n      }\n    }\n  }\n  return promise;\n}\n\n// This is needed for the Spreadsheet print functionality.  Since\n// there we only need to draw text, this cuts through the ceremony\n// of drawDOM/renderElement and renders the text node directly.\nfunction drawText(element) {\n  var group = new Group();\n  nodeInfo._clipbox = false;\n  nodeInfo._matrix = geo.Matrix.unit();\n  nodeInfo._stackingContext = {\n    element: element,\n    group: group\n  };\n  pushNodeInfo(element, getComputedStyle(element), group);\n  if (element.firstChild.nodeType == 3 /* Text */) {\n    // avoid the penalty of renderElement\n    renderText(element, element.firstChild, group);\n  } else {\n    _renderElement(element, group);\n  }\n  popNodeInfo();\n  return group;\n}\nvar parseBackgroundImage = function () {\n  var tok_linear_gradient = /^((-webkit-|-moz-|-o-|-ms-)?linear-gradient\\s*)\\(/;\n  //var tok_radial_gradient  = /^((-webkit-|-moz-|-o-|-ms-)?radial-gradient\\s*)\\(/;\n  var tok_percent = /^([-0-9.]+%)/;\n  var tok_length = /^([-0-9.]+px)/;\n  var tok_keyword = /^(left|right|top|bottom|to|center)\\W/;\n  var tok_angle = /^([-0-9.]+(deg|grad|rad|turn)|0)/;\n  var tok_whitespace = /^(\\s+)/;\n  var tok_popen = /^(\\()/;\n  var tok_pclose = /^(\\))/;\n  var tok_comma = /^(,)/;\n  var tok_url = /^(url)\\(/;\n  var tok_content = /^(.*?)\\)/;\n  var cache1 = {},\n    cache2 = {};\n  function parse(input) {\n    var orig = input;\n    if (hasOwnProperty(cache1, orig)) {\n      return cache1[orig];\n    }\n    function skip_ws() {\n      var m = tok_whitespace.exec(input);\n      if (m) {\n        input = input.substr(m[1].length);\n      }\n    }\n    function read(token) {\n      skip_ws();\n      var m = token.exec(input);\n      if (m) {\n        input = input.substr(m[1].length);\n        return m[1];\n      }\n    }\n    function read_stop() {\n      var color = utils_parseColor(input, true);\n      var length, percent;\n      if (color) {\n        var match = /^#[0-9a-f]+/i.exec(input) || /^rgba?\\(.*?\\)/i.exec(input) || /^..*?\\b/.exec(input); // maybe named color\n        input = input.substr(match[0].length);\n        color = color.toRGB();\n        if (!(length = read(tok_length))) {\n          percent = read(tok_percent);\n        }\n        return {\n          color: color,\n          length: length,\n          percent: percent\n        };\n      }\n    }\n    function read_linear_gradient(propName) {\n      var angle;\n      var to1, to2;\n      var stops = [];\n      var reverse = false;\n      if (read(tok_popen)) {\n        // 1. [ <angle> || to <side-or-corner>, ]?\n        angle = read(tok_angle);\n        if (angle == \"0\") {\n          angle = \"0deg\"; // Edge\n        }\n        if (angle) {\n          angle = parseAngle(angle);\n          read(tok_comma);\n        } else {\n          to1 = read(tok_keyword);\n          if (to1 == \"to\") {\n            to1 = read(tok_keyword);\n          } else if (to1 && /^-/.test(propName)) {\n            reverse = true;\n          }\n          to2 = read(tok_keyword);\n          read(tok_comma);\n        }\n        if (/-moz-/.test(propName) && angle == null && to1 == null) {\n          var x = read(tok_percent),\n            y = read(tok_percent);\n          reverse = true;\n          if (x == \"0%\") {\n            to1 = \"left\";\n          } else if (x == \"100%\") {\n            to1 = \"right\";\n          }\n          if (y == \"0%\") {\n            to2 = \"top\";\n          } else if (y == \"100%\") {\n            to2 = \"bottom\";\n          }\n          read(tok_comma);\n        }\n\n        // 2. color stops\n        while (input && !read(tok_pclose)) {\n          var stop = read_stop();\n          if (!stop) {\n            break;\n          }\n          stops.push(stop);\n          read(tok_comma);\n        }\n        return {\n          type: \"linear\",\n          angle: angle,\n          to: to1 && to2 ? to1 + \" \" + to2 : to1 ? to1 : to2 ? to2 : null,\n          stops: stops,\n          reverse: reverse\n        };\n      }\n    }\n    function read_url() {\n      if (read(tok_popen)) {\n        var url = read(tok_content);\n        url = url.replace(/^['\"]+|[\"']+$/g, \"\");\n        read(tok_pclose);\n        return {\n          type: \"url\",\n          url: url\n        };\n      }\n    }\n    var tok;\n    if (tok = read(tok_linear_gradient)) {\n      tok = read_linear_gradient(tok);\n    } else if (tok = read(tok_url)) {\n      tok = read_url();\n    }\n    return cache1[orig] = tok || {\n      type: \"none\"\n    };\n  }\n  return function (input) {\n    if (hasOwnProperty(cache2, input)) {\n      return cache2[input];\n    }\n    return cache2[input] = splitProperty(input).map(parse);\n  };\n}();\nvar splitProperty = function () {\n  var cache = {};\n  return function (input, separator) {\n    if (!separator) {\n      separator = /^\\s*,\\s*/;\n    }\n    var cacheKey = input + separator;\n    if (hasOwnProperty(cache, cacheKey)) {\n      return cache[cacheKey];\n    }\n    var ret = [];\n    var last = 0,\n      pos = 0;\n    var in_paren = 0;\n    var in_string = false;\n    var m;\n    function looking_at(rx) {\n      return m = rx.exec(input.substr(pos));\n    }\n    function trim(str) {\n      return str.replace(/^\\s+|\\s+$/g, \"\");\n    }\n    while (pos < input.length) {\n      if (!in_string && looking_at(/^[\\(\\[\\{]/)) {\n        in_paren++;\n        pos++;\n      } else if (!in_string && looking_at(/^[\\)\\]\\}]/)) {\n        in_paren--;\n        pos++;\n      } else if (!in_string && looking_at(/^[\\\"\\']/)) {\n        in_string = m[0];\n        pos++;\n      } else if (in_string == \"'\" && looking_at(/^\\\\\\'/)) {\n        pos += 2;\n      } else if (in_string == '\"' && looking_at(/^\\\\\\\"/)) {\n        pos += 2;\n      } else if (in_string == \"'\" && looking_at(/^\\'/)) {\n        in_string = false;\n        pos++;\n      } else if (in_string == '\"' && looking_at(/^\\\"/)) {\n        in_string = false;\n        pos++;\n      } else if (looking_at(separator)) {\n        if (!in_string && !in_paren && pos > last) {\n          ret.push(trim(input.substring(last, pos)));\n          last = pos + m[0].length;\n        }\n        pos += m[0].length;\n      } else {\n        pos++;\n      }\n    }\n    if (last < pos) {\n      ret.push(trim(input.substring(last, pos)));\n    }\n    return cache[cacheKey] = ret;\n  };\n}();\nvar getFontURL = function (cache) {\n  return function (el) {\n    // XXX: for IE we get here the whole cssText of the rule,\n    // because the computedStyle.src is empty.  Next time we need\n    // to fix these regexps we better write a CSS parser. :-\\\n    var url = cache[el];\n    if (!url) {\n      var m;\n      if (m = /url\\((['\"]?)([^'\")]*?)\\1\\)\\s+format\\((['\"]?)truetype\\3\\)/.exec(el)) {\n        url = cache[el] = m[2];\n      } else if (m = /url\\((['\"]?)([^'\")]*?\\.ttf)\\1\\)/.exec(el)) {\n        url = cache[el] = m[2];\n      }\n    }\n    return url;\n  };\n}(Object.create(null));\nvar getFontHeight = function (cache) {\n  return function (font) {\n    var height = cache[font];\n    if (height == null) {\n      height = cache[font] = measureText(\"Mapq\", {\n        font: font\n      }).height;\n    }\n    return height;\n  };\n}(Object.create(null));\nfunction getFontFaces(doc) {\n  if (doc == null) {\n    doc = document;\n  }\n  var result = {};\n  for (var i = 0; i < doc.styleSheets.length; ++i) {\n    doStylesheet(doc.styleSheets[i]);\n  }\n  return result;\n  function doStylesheet(ss) {\n    if (ss) {\n      var rules = null;\n      try {\n        rules = ss.cssRules;\n      } catch (ex) {}\n      if (rules) {\n        addRules(ss, rules);\n      }\n    }\n  }\n  function findFonts(rule) {\n    var src = getPropertyValue(rule.style, \"src\");\n    if (src) {\n      return splitProperty(src).reduce(function (a, el) {\n        var font = getFontURL(el);\n        if (font) {\n          a.push(font);\n        }\n        return a;\n      }, []);\n    } else {\n      // Internet Explorer\n      // XXX: this is gross.  should work though for valid CSS.\n      var font = getFontURL(rule.cssText);\n      return font ? [font] : [];\n    }\n  }\n  function addRules(styleSheet, rules) {\n    for (var i = 0; i < rules.length; ++i) {\n      var r = rules[i];\n      switch (r.type) {\n        case 3:\n          // CSSImportRule\n          doStylesheet(r.styleSheet);\n          break;\n        case 5:\n          // CSSFontFaceRule\n          var style = r.style;\n          var family = splitProperty(getPropertyValue(style, \"font-family\"));\n          var bold = /^([56789]00|bold)$/i.test(getPropertyValue(style, \"font-weight\"));\n          var italic = \"italic\" == getPropertyValue(style, \"font-style\");\n          var src = findFonts(r);\n          if (src.length > 0) {\n            addRule(styleSheet, family, bold, italic, src[0]);\n          }\n      }\n    }\n  }\n  function addRule(styleSheet, names, bold, italic, url) {\n    // We get full resolved absolute URLs in Chrome, but sadly\n    // not in Firefox.\n    if (!/^data:/i.test(url)) {\n      if (!(/^[^\\/:]+:\\/\\//.test(url) || /^\\//.test(url))) {\n        url = String(styleSheet.href).replace(/[^\\/]*$/, \"\") + url;\n      }\n    }\n    names.forEach(function (name) {\n      name = name.replace(/^(['\"]?)(.*?)\\1$/, \"$2\"); // it's quoted\n      if (bold) {\n        name += \"|bold\";\n      }\n      if (italic) {\n        name += \"|italic\";\n      }\n      result[name] = url;\n    });\n  }\n}\nfunction hasOwnProperty(obj, key) {\n  return Object.prototype.hasOwnProperty.call(obj, key);\n}\nfunction getCounter(name) {\n  name = \"_counter_\" + name;\n  return nodeInfo[name];\n}\nfunction getAllCounters(name) {\n  var values = [],\n    p = nodeInfo;\n  name = \"_counter_\" + name;\n  while (p) {\n    if (hasOwnProperty(p, name)) {\n      values.push(p[name]);\n    }\n    p = Object.getPrototypeOf(p);\n  }\n  return values.reverse();\n}\nfunction incCounter(name, inc) {\n  var p = nodeInfo;\n  name = \"_counter_\" + name;\n  while (p && !hasOwnProperty(p, name)) {\n    p = Object.getPrototypeOf(p);\n  }\n  if (!p) {\n    p = nodeInfo._root;\n  }\n  p[name] = (p[name] || 0) + (inc == null ? 1 : inc);\n}\nfunction resetCounter(name, val) {\n  name = \"_counter_\" + name;\n  nodeInfo[name] = val == null ? 0 : val;\n}\nfunction doCounters(a, f, def) {\n  for (var i = 0; i < a.length;) {\n    var name = a[i++];\n    var val = parseFloat(a[i]);\n    if (isNaN(val)) {\n      f(name, def);\n    } else {\n      f(name, val);\n      ++i;\n    }\n  }\n}\nfunction updateCounters(style) {\n  var counterReset = getPropertyValue(style, \"counter-reset\");\n  if (counterReset) {\n    doCounters(splitProperty(counterReset, /^\\s+/), resetCounter, 0);\n  }\n  var counterIncrement = getPropertyValue(style, \"counter-increment\");\n  if (counterIncrement) {\n    doCounters(splitProperty(counterIncrement, /^\\s+/), incCounter, 1);\n  }\n}\nfunction parseColor(str, css) {\n  var color = utils_parseColor(str, true);\n  if (color) {\n    color = color.toRGB();\n    if (css) {\n      color = color.toCssRgba();\n    } else if (color.a === 0) {\n      color = null;\n    }\n  }\n  return color;\n}\nfunction whenImagesAreActuallyLoaded(elements, callback) {\n  var pending = 0;\n  var done = false;\n  elements.forEach(function (el) {\n    var images = el.querySelectorAll(\"img\");\n    for (var i = 0; i < images.length; ++i) {\n      var img = images[i];\n      if (!img.complete) {\n        pending++;\n        img.onload = img.onerror = next;\n      }\n    }\n  });\n  if (!pending) {\n    next();\n  }\n  function next() {\n    if (!done && --pending <= 0) {\n      callback();\n      done = true;\n    }\n  }\n}\nfunction cacheImages(elements, callback) {\n  var urls = [];\n  function add(url) {\n    if (!IMAGE_CACHE[url]) {\n      IMAGE_CACHE[url] = true;\n      urls.push(url);\n    }\n  }\n  elements.forEach(function dive(element) {\n    if (/^img$/i.test(element.tagName)) {\n      add(element.src);\n    }\n    parseBackgroundImage(getPropertyValue(getComputedStyle(element), \"background-image\")).forEach(function (bg) {\n      if (bg.type == \"url\") {\n        add(bg.url);\n      }\n    });\n    if (element.children) {\n      slice(element.children).forEach(dive);\n    }\n  });\n  var count = urls.length;\n  function next() {\n    if (--count <= 0) {\n      // Even though we cached them, they simply won't be available immediately in the newly\n      // created DOM.  Previously we'd allow a 10ms timeout, but that's arbitrary and clearly\n      // not working in all cases (https://github.com/telerik/kendo/issues/5399), so this\n      // function will wait for their .complete attribute.\n      whenImagesAreActuallyLoaded(elements, callback);\n    }\n  }\n  if (count === 0) {\n    next();\n  }\n  urls.forEach(function (url) {\n    var img = IMAGE_CACHE[url] = new window.Image();\n    if (!/^data:/i.test(url)) {\n      img.crossOrigin = \"Anonymous\";\n    }\n    img.src = url;\n    if (img.complete) {\n      next();\n    } else {\n      img.onload = next;\n      img.onerror = function () {\n        IMAGE_CACHE[url] = null;\n        next();\n      };\n    }\n  });\n}\nfunction alphaNumeral(n) {\n  var result = \"\";\n  do {\n    var r = n % 26;\n    result = String.fromCharCode(97 + r) + result;\n    n = Math.floor(n / 26);\n  } while (n > 0);\n  return result;\n}\nfunction pushNodeInfo(element, style, group) {\n  nodeInfo = Object.create(nodeInfo);\n  nodeInfo[element.tagName.toLowerCase()] = {\n    element: element,\n    style: style\n  };\n  var decoration = getPropertyValue(style, \"text-decoration\");\n  if (decoration && decoration != \"none\") {\n    var color = getPropertyValue(style, \"text-decoration-color\");\n    decoration.split(/\\s+/g).forEach(function (name) {\n      if (!nodeInfo[name]) {\n        nodeInfo[name] = color;\n        if (name == \"underline\") {\n          var offset = getPropertyValue(style, \"text-underline-offset\");\n          if (offset != \"auto\") {\n            nodeInfo[\"underline-offset\"] = parseFloat(offset);\n          }\n        }\n      }\n    });\n  }\n  if (createsStackingContext(style)) {\n    nodeInfo._stackingContext = {\n      element: element,\n      group: group\n    };\n  }\n}\nfunction popNodeInfo() {\n  nodeInfo = Object.getPrototypeOf(nodeInfo);\n}\nfunction updateClipbox(path) {\n  if (nodeInfo._clipbox != null) {\n    var box = path.bbox(nodeInfo._matrix);\n    if (nodeInfo._clipbox) {\n      nodeInfo._clipbox = geo.Rect.intersect(nodeInfo._clipbox, box);\n    } else {\n      nodeInfo._clipbox = box;\n    }\n  }\n}\nfunction emptyClipbox() {\n  var cb = nodeInfo._clipbox;\n  if (cb == null) {\n    return true;\n  }\n  if (cb) {\n    return cb.width() === 0 || cb.height() === 0;\n  }\n}\nfunction createsStackingContext(style) {\n  function prop(name) {\n    return getPropertyValue(style, name);\n  }\n  if (prop(\"transform\") != \"none\" || prop(\"position\") != \"static\" || prop(\"z-index\") != \"auto\" || prop(\"opacity\") < 1) {\n    return true;\n  }\n}\nfunction getComputedStyle(element, pseudoElt) {\n  return window.getComputedStyle(element, pseudoElt || null);\n}\nfunction getPropertyValue(style, prop, defa) {\n  var val = style.getPropertyValue(prop);\n  if (val == null || val === \"\") {\n    if (browser.webkit) {\n      val = style.getPropertyValue(\"-webkit-\" + prop);\n    } else if (browser.mozilla) {\n      val = style.getPropertyValue(\"-moz-\" + prop);\n    } else if (browser.opera) {\n      val = style.getPropertyValue(\"-o-\" + prop);\n    } else if (microsoft) {\n      val = style.getPropertyValue(\"-ms-\" + prop);\n    }\n  }\n  if (arguments.length > 2 && (val == null || val === \"\")) {\n    return defa;\n  } else {\n    return val;\n  }\n}\nfunction pleaseSetPropertyValue(style, prop, value, important) {\n  style.setProperty(prop, value, important);\n  if (browser.webkit) {\n    style.setProperty(\"-webkit-\" + prop, value, important);\n  } else if (browser.mozilla) {\n    style.setProperty(\"-moz-\" + prop, value, important);\n  } else if (browser.opera) {\n    style.setProperty(\"-o-\" + prop, value, important);\n  } else if (microsoft) {\n    style.setProperty(\"-ms-\" + prop, value, important);\n    prop = \"ms\" + prop.replace(/(^|-)([a-z])/g, function (s, p1, p2) {\n      return p1 + p2.toUpperCase();\n    });\n    style[prop] = value;\n  }\n}\nfunction getBorder(style, side) {\n  side = \"border-\" + side;\n  return {\n    width: parseFloat(getPropertyValue(style, side + \"-width\")),\n    style: getPropertyValue(style, side + \"-style\"),\n    color: parseColor(getPropertyValue(style, side + \"-color\"), true)\n  };\n}\nfunction saveStyle(element, func) {\n  var prev = element.style.cssText;\n  var result = func();\n  setStyle(element, prev);\n  return result;\n}\nfunction getBorderRadius(style, side) {\n  var r = getPropertyValue(style, \"border-\" + side + \"-radius\").split(/\\s+/g).map(parseFloat);\n  if (r.length == 1) {\n    r.push(r[0]);\n  }\n  return sanitizeRadius({\n    x: r[0],\n    y: r[1]\n  });\n}\nfunction getContentBox(element) {\n  var box = element.getBoundingClientRect();\n  box = innerBox(box, \"border-*-width\", element);\n  box = innerBox(box, \"padding-*\", element);\n  return box;\n}\nfunction innerBox(box, prop, element) {\n  var style, wt, wr, wb, wl;\n  if (typeof prop == \"string\") {\n    style = getComputedStyle(element);\n    wt = parseFloat(getPropertyValue(style, prop.replace(\"*\", \"top\")));\n    wr = parseFloat(getPropertyValue(style, prop.replace(\"*\", \"right\")));\n    wb = parseFloat(getPropertyValue(style, prop.replace(\"*\", \"bottom\")));\n    wl = parseFloat(getPropertyValue(style, prop.replace(\"*\", \"left\")));\n  } else if (typeof prop == \"number\") {\n    wt = wr = wb = wl = prop;\n  }\n  return {\n    top: box.top + wt,\n    right: box.right - wr,\n    bottom: box.bottom - wb,\n    left: box.left + wl,\n    width: box.right - box.left - wr - wl,\n    height: box.bottom - box.top - wb - wt\n  };\n}\nfunction getTransform(style) {\n  var transform = getPropertyValue(style, \"transform\");\n  if (transform == \"none\") {\n    return null;\n  }\n  var matrix = /^\\s*matrix\\(\\s*(.*?)\\s*\\)\\s*$/.exec(transform);\n  if (matrix) {\n    var origin = getPropertyValue(style, \"transform-origin\");\n    matrix = matrix[1].split(/\\s*,\\s*/g).map(parseFloat);\n    origin = origin.split(/\\s+/g).map(parseFloat);\n    return {\n      matrix: matrix,\n      origin: origin\n    };\n  }\n}\nfunction radiansToDegrees(radians) {\n  return 180 * radians / Math.PI % 360;\n}\nfunction parseAngle(angle) {\n  var num = parseFloat(angle);\n  if (/grad$/.test(angle)) {\n    return Math.PI * num / 200;\n  } else if (/rad$/.test(angle)) {\n    return num;\n  } else if (/turn$/.test(angle)) {\n    return Math.PI * num * 2;\n  } else if (/deg$/.test(angle)) {\n    return Math.PI * num / 180;\n  }\n}\nfunction setTransform(shape, m) {\n  m = new geo.Matrix(m[0], m[1], m[2], m[3], m[4], m[5]);\n  shape.transform(m);\n  return m;\n}\nfunction setClipping(shape, clipPath) {\n  shape.clip(clipPath);\n}\nfunction addArcToPath(path, x, y, options) {\n  var points = new geo.Arc([x, y], options).curvePoints(),\n    i = 1;\n  while (i < points.length) {\n    path.curveTo(points[i++], points[i++], points[i++]);\n  }\n}\nfunction sanitizeRadius(r) {\n  if (r.x <= 0 || r.y <= 0) {\n    r.x = r.y = 0;\n  }\n  return r;\n}\nfunction adjustBorderRadiusForBox(box, rTL, rTR, rBR, rBL) {\n  // adjust border radiuses such that the sum of adjacent\n  // radiuses is not bigger than the length of the side.\n  // seems the correct algorithm is variant (3) from here:\n  // http://www.w3.org/Style/CSS/Tracker/issues/29?changelog\n  var tl_x = Math.max(0, rTL.x),\n    tl_y = Math.max(0, rTL.y);\n  var tr_x = Math.max(0, rTR.x),\n    tr_y = Math.max(0, rTR.y);\n  var br_x = Math.max(0, rBR.x),\n    br_y = Math.max(0, rBR.y);\n  var bl_x = Math.max(0, rBL.x),\n    bl_y = Math.max(0, rBL.y);\n  var f = Math.min(box.width / (tl_x + tr_x), box.height / (tr_y + br_y), box.width / (br_x + bl_x), box.height / (bl_y + tl_y));\n  if (f < 1) {\n    tl_x *= f;\n    tl_y *= f;\n    tr_x *= f;\n    tr_y *= f;\n    br_x *= f;\n    br_y *= f;\n    bl_x *= f;\n    bl_y *= f;\n  }\n  return {\n    tl: {\n      x: tl_x,\n      y: tl_y\n    },\n    tr: {\n      x: tr_x,\n      y: tr_y\n    },\n    br: {\n      x: br_x,\n      y: br_y\n    },\n    bl: {\n      x: bl_x,\n      y: bl_y\n    }\n  };\n}\nfunction elementRoundBox(element, box, type) {\n  var style = getComputedStyle(element);\n  var rTL = getBorderRadius(style, \"top-left\");\n  var rTR = getBorderRadius(style, \"top-right\");\n  var rBL = getBorderRadius(style, \"bottom-left\");\n  var rBR = getBorderRadius(style, \"bottom-right\");\n  if (type == \"padding\" || type == \"content\") {\n    var bt = getBorder(style, \"top\");\n    var br = getBorder(style, \"right\");\n    var bb = getBorder(style, \"bottom\");\n    var bl = getBorder(style, \"left\");\n    rTL.x -= bl.width;\n    rTL.y -= bt.width;\n    rTR.x -= br.width;\n    rTR.y -= bt.width;\n    rBR.x -= br.width;\n    rBR.y -= bb.width;\n    rBL.x -= bl.width;\n    rBL.y -= bb.width;\n    if (type == \"content\") {\n      var pt = parseFloat(getPropertyValue(style, \"padding-top\"));\n      var pr = parseFloat(getPropertyValue(style, \"padding-right\"));\n      var pb = parseFloat(getPropertyValue(style, \"padding-bottom\"));\n      var pl = parseFloat(getPropertyValue(style, \"padding-left\"));\n      rTL.x -= pl;\n      rTL.y -= pt;\n      rTR.x -= pr;\n      rTR.y -= pt;\n      rBR.x -= pr;\n      rBR.y -= pb;\n      rBL.x -= pl;\n      rBL.y -= pb;\n    }\n  }\n  if (typeof type == \"number\") {\n    rTL.x -= type;\n    rTL.y -= type;\n    rTR.x -= type;\n    rTR.y -= type;\n    rBR.x -= type;\n    rBR.y -= type;\n    rBL.x -= type;\n    rBL.y -= type;\n  }\n  return roundBox(box, rTL, rTR, rBR, rBL);\n}\n\n// Create a drawing.Path for a rounded rectangle.  Receives the\n// bounding box and the border-radiuses in CSS order (top-left,\n// top-right, bottom-right, bottom-left).  The radiuses must be\n// objects containing x (horiz. radius) and y (vertical radius).\nfunction roundBox(box, rTL0, rTR0, rBR0, rBL0) {\n  var tmp = adjustBorderRadiusForBox(box, rTL0, rTR0, rBR0, rBL0);\n  var rTL = tmp.tl;\n  var rTR = tmp.tr;\n  var rBR = tmp.br;\n  var rBL = tmp.bl;\n  var path = new Path({\n    fill: null,\n    stroke: null\n  });\n  path.moveTo(box.left, box.top + rTL.y);\n  if (rTL.x) {\n    addArcToPath(path, box.left + rTL.x, box.top + rTL.y, {\n      startAngle: -180,\n      endAngle: -90,\n      radiusX: rTL.x,\n      radiusY: rTL.y\n    });\n  }\n  path.lineTo(box.right - rTR.x, box.top);\n  if (rTR.x) {\n    addArcToPath(path, box.right - rTR.x, box.top + rTR.y, {\n      startAngle: -90,\n      endAngle: 0,\n      radiusX: rTR.x,\n      radiusY: rTR.y\n    });\n  }\n  path.lineTo(box.right, box.bottom - rBR.y);\n  if (rBR.x) {\n    addArcToPath(path, box.right - rBR.x, box.bottom - rBR.y, {\n      startAngle: 0,\n      endAngle: 90,\n      radiusX: rBR.x,\n      radiusY: rBR.y\n    });\n  }\n  path.lineTo(box.left + rBL.x, box.bottom);\n  if (rBL.x) {\n    addArcToPath(path, box.left + rBL.x, box.bottom - rBL.y, {\n      startAngle: 90,\n      endAngle: 180,\n      radiusX: rBL.x,\n      radiusY: rBL.y\n    });\n  }\n  return path.close();\n}\nfunction formatCounter(val, style) {\n  var str = String(parseFloat(val));\n  switch (style) {\n    case \"decimal-leading-zero\":\n      if (str.length < 2) {\n        str = \"0\" + str;\n      }\n      return str;\n    case \"lower-roman\":\n      return arabicToRoman(val).toLowerCase();\n    case \"upper-roman\":\n      return arabicToRoman(val).toUpperCase();\n    case \"lower-latin\":\n    case \"lower-alpha\":\n      return alphaNumeral(val - 1);\n    case \"upper-latin\":\n    case \"upper-alpha\":\n      return alphaNumeral(val - 1).toUpperCase();\n    default:\n      return str;\n  }\n}\nfunction evalPseudoElementContent(element, content) {\n  function displayCounter(name, style, separator) {\n    if (!separator) {\n      return formatCounter(getCounter(name) || 0, style);\n    }\n    separator = separator.replace(/^\\s*([\"'])(.*)\\1\\s*$/, \"$2\");\n    return getAllCounters(name).map(function (val) {\n      return formatCounter(val, style);\n    }).join(separator);\n  }\n  var a = splitProperty(content, /^\\s+/);\n  var result = [],\n    m;\n  a.forEach(function (el) {\n    var tmp;\n    if (m = /^\\s*([\"'])(.*)\\1\\s*$/.exec(el)) {\n      result.push(m[2].replace(/\\\\([0-9a-f]{4})/gi, function (s, p) {\n        return String.fromCharCode(parseInt(p, 16));\n      }));\n    } else if (m = /^\\s*counter\\((.*?)\\)\\s*$/.exec(el)) {\n      tmp = splitProperty(m[1]);\n      result.push(displayCounter(tmp[0], tmp[1]));\n    } else if (m = /^\\s*counters\\((.*?)\\)\\s*$/.exec(el)) {\n      tmp = splitProperty(m[1]);\n      result.push(displayCounter(tmp[0], tmp[2], tmp[1]));\n    } else if (m = /^\\s*attr\\((.*?)\\)\\s*$/.exec(el)) {\n      result.push(element.getAttribute(m[1]) || \"\");\n    } else {\n      result.push(el);\n    }\n  });\n  return result.join(\"\");\n}\nfunction getCssText(style) {\n  if (style.cssText) {\n    return style.cssText;\n  }\n  // Status: NEW.  Report year: 2002.  Current year: 2014.\n  // Nice played, Mozillians.\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=137687\n  var result = [];\n  for (var i = 0; i < style.length; ++i) {\n    result.push(style[i] + \": \" + getPropertyValue(style, style[i]));\n  }\n  return result.join(\";\\n\");\n}\nfunction _renderWithPseudoElements(element, group) {\n  if (element.tagName == KENDO_PSEUDO_ELEMENT) {\n    _renderElement(element, group);\n    return;\n  }\n  var fake = [];\n  function pseudo(kind, place) {\n    var style = getComputedStyle(element, kind),\n      content = style.content;\n    updateCounters(style);\n    if (content && content != \"normal\" && content != \"none\" && style.width != \"0px\") {\n      var psel = element.ownerDocument.createElement(KENDO_PSEUDO_ELEMENT);\n      setStyle(psel, getCssText(style));\n      psel.textContent = evalPseudoElementContent(element, content);\n      element.insertBefore(psel, place);\n      fake.push(psel);\n    }\n  }\n  pseudo(\":before\", element.firstChild);\n  pseudo(\":after\", null);\n  if (fake.length > 0) {\n    var saveClass = element.className;\n    element.className += \" kendo-pdf-hide-pseudo-elements\";\n    _renderElement(element, group);\n    element.className = saveClass;\n    fake.forEach(function (el) {\n      element.removeChild(el);\n    });\n  } else {\n    _renderElement(element, group);\n  }\n}\nfunction _renderElement(element, group) {\n  var style = getComputedStyle(element);\n  var top = getBorder(style, \"top\");\n  var right = getBorder(style, \"right\");\n  var bottom = getBorder(style, \"bottom\");\n  var left = getBorder(style, \"left\");\n  var rTL0 = getBorderRadius(style, \"top-left\");\n  var rTR0 = getBorderRadius(style, \"top-right\");\n  var rBL0 = getBorderRadius(style, \"bottom-left\");\n  var rBR0 = getBorderRadius(style, \"bottom-right\");\n  var dir = getPropertyValue(style, \"direction\");\n  var backgroundColor = getPropertyValue(style, \"background-color\");\n  backgroundColor = parseColor(backgroundColor);\n  var backgroundImage = parseBackgroundImage(getPropertyValue(style, \"background-image\"));\n  var backgroundRepeat = splitProperty(getPropertyValue(style, \"background-repeat\"));\n  var backgroundPosition = splitProperty(getPropertyValue(style, \"background-position\"));\n  var backgroundOrigin = splitProperty(getPropertyValue(style, \"background-origin\"));\n  var backgroundSize = splitProperty(getPropertyValue(style, \"background-size\"));\n\n  // IE shrinks the text with text-overflow: ellipsis,\n  // apparently because the returned bounding box for the range\n  // is limited to the visible area minus space for the dots,\n  // instead of being the full width of the text.\n  //\n  // https://github.com/telerik/kendo/issues/5232\n  // https://github.com/telerik/kendo-ui-core/issues/1868\n  //\n  // We have to test it here rather than in renderText because\n  // text-overflow: ellipsis could be set on a parent element (not\n  // necessarily the one containing the text); in this case,\n  // getComputedStyle(elementWithTheText) will return \"clip\", not\n  // \"ellipsis\" (which is probably a bug, but oh well...)\n  var textOverflow, saveTextOverflow;\n  if (microsoft) {\n    textOverflow = style.textOverflow; // computed style\n    if (textOverflow == \"ellipsis\") {\n      saveTextOverflow = element.style.textOverflow; // own style.\n      element.style.textOverflow = \"clip\";\n    }\n  }\n  if (browser.msie && browser.version < 10) {\n    // IE9 hacks.  getPropertyValue won't return the correct\n    // value.  Sucks that we have to do it here, I'd prefer to\n    // move it in getPropertyValue, but we don't have the\n    // element.\n    backgroundPosition = splitProperty(element.currentStyle.backgroundPosition);\n  }\n  var innerbox = innerBox(element.getBoundingClientRect(), \"border-*-width\", element);\n\n  // CSS \"clip\" property - if present, replace the group with a\n  // new one which is clipped.  This must happen before drawing\n  // the borders and background.\n  (function () {\n    var clip = getPropertyValue(style, \"clip\");\n    var m = /^\\s*rect\\((.*)\\)\\s*$/.exec(clip);\n    if (m) {\n      var a = m[1].split(/[ ,]+/g);\n      var top = a[0] == \"auto\" ? innerbox.top : parseFloat(a[0]) + innerbox.top;\n      var right = a[1] == \"auto\" ? innerbox.right : parseFloat(a[1]) + innerbox.left;\n      var bottom = a[2] == \"auto\" ? innerbox.bottom : parseFloat(a[2]) + innerbox.top;\n      var left = a[3] == \"auto\" ? innerbox.left : parseFloat(a[3]) + innerbox.left;\n      var tmp = new Group();\n      var clipPath = new Path().moveTo(left, top).lineTo(right, top).lineTo(right, bottom).lineTo(left, bottom).close();\n      setClipping(tmp, clipPath);\n      group.append(tmp);\n      group = tmp;\n      updateClipbox(clipPath);\n    }\n  })();\n  var boxes, i, cells;\n  var display = getPropertyValue(style, \"display\");\n  if (display == \"table-row\") {\n    // because of rowspan/colspan, we shouldn't draw background of table row elements on the\n    // box given by its getBoundingClientRect, because if we do we risk overwritting a\n    // previously rendered cell.  https://github.com/telerik/kendo/issues/4881\n    boxes = [];\n    for (i = 0, cells = element.children; i < cells.length; ++i) {\n      boxes.push(cells[i].getBoundingClientRect());\n    }\n  } else {\n    boxes = element.getClientRects();\n    if (boxes.length == 1) {\n      // Workaround the missing borders in Chrome!  getClientRects() boxes contains values\n      // rounded to integer.  getBoundingClientRect() appears to work fine.  We still need\n      // getClientRects() to support cases where there are more boxes (continued inline\n      // elements that might have border/background).\n      boxes = [element.getBoundingClientRect()];\n    }\n  }\n\n  // This function workarounds another Chrome bug, where boxes returned for a table with\n  // border-collapse: collapse will overlap the table border.  Our rendering is not perfect in\n  // such case anyway, but with this is better than without it.\n  boxes = adjustBoxes(boxes);\n  for (i = 0; i < boxes.length; ++i) {\n    drawOneBox(boxes[i], i === 0, i == boxes.length - 1);\n  }\n\n  // Render links as separate groups.  We can't use boxes returned by element's getClientRects\n  // because if display type is \"inline\" (default for <a>), boxes will not include the height of\n  // images inside.  https://github.com/telerik/kendo-ui-core/issues/3359\n  if (element.tagName == \"A\" && element.href && !/^#?$/.test(element.getAttribute(\"href\"))) {\n    if (!nodeInfo._avoidLinks || !matches(element, nodeInfo._avoidLinks)) {\n      var r = document.createRange();\n      r.selectNodeContents(element);\n      slice(r.getClientRects()).forEach(function (box) {\n        var g = new Group();\n        g._pdfLink = {\n          url: element.href,\n          top: box.top,\n          right: box.right,\n          bottom: box.bottom,\n          left: box.left\n        };\n        group.append(g);\n      });\n    }\n  }\n  if (boxes.length > 0 && display == \"list-item\" && !element.getAttribute(\"kendo-no-bullet\")) {\n    drawBullet(boxes[0]);\n  }\n\n  // overflow: hidden/auto - if present, replace the group with\n  // a new one clipped by the inner box.\n  (function () {\n    function clipit() {\n      var clipPath = elementRoundBox(element, innerbox, \"padding\");\n      var tmp = new Group();\n      setClipping(tmp, clipPath);\n      group.append(tmp);\n      group = tmp;\n      updateClipbox(clipPath);\n    }\n    if (isFormField(element)) {\n      clipit();\n    } else if (/^(hidden|auto|scroll)/.test(getPropertyValue(style, \"overflow\"))) {\n      clipit();\n    } else if (/^(hidden|auto|scroll)/.test(getPropertyValue(style, \"overflow-x\"))) {\n      clipit();\n    } else if (/^(hidden|auto|scroll)/.test(getPropertyValue(style, \"overflow-y\"))) {\n      clipit();\n    }\n  })();\n  if (!maybeRenderWidget(element, group) && !maybeRenderBullet(element, group)) {\n    renderContents(element, group);\n  }\n  if (microsoft && textOverflow == \"ellipsis\") {\n    element.style.textOverflow = saveTextOverflow;\n  }\n  return group; // only utility functions after this line.\n\n  function adjustBoxes(boxes) {\n    if (/^td$/i.test(element.tagName)) {\n      var table = nodeInfo.table;\n      if (table && getPropertyValue(table.style, \"border-collapse\") == \"collapse\") {\n        var tableBorderLeft = getBorder(table.style, \"left\").width;\n        var tableBorderTop = getBorder(table.style, \"top\").width;\n        // check if we need to adjust\n        if (tableBorderLeft === 0 && tableBorderTop === 0) {\n          return boxes; // nope\n        }\n        var tableBox = table.element.getBoundingClientRect();\n        var firstCell = table.element.rows[0].cells[0];\n        var firstCellBox = firstCell.getBoundingClientRect();\n        if (firstCellBox.top == tableBox.top || firstCellBox.left == tableBox.left) {\n          return slice(boxes).map(function (box) {\n            return {\n              left: box.left + tableBorderLeft,\n              top: box.top + tableBorderTop,\n              right: box.right + tableBorderLeft,\n              bottom: box.bottom + tableBorderTop,\n              height: box.height,\n              width: box.width\n            };\n          });\n        }\n      }\n    }\n    return boxes;\n  }\n\n  // this function will be called to draw each border.  it\n  // draws starting at origin and the resulted path must be\n  // translated/rotated to be placed in the proper position.\n  //\n  // arguments are named as if it draws the top border:\n  //\n  //    - `len` the length of the edge\n  //    - `Wtop` the width of the edge (i.e. border-top-width)\n  //    - `Wleft` the width of the left edge (border-left-width)\n  //    - `Wright` the width of the right edge\n  //    - `rl` and `rl` -- the border radius on the left and right\n  //      (objects containing x and y, for horiz/vertical radius)\n  //    - `transform` -- transformation to apply\n  //\n  function drawEdge(color, len, Wtop, Wleft, Wright, rl, rr, transform) {\n    if (Wtop <= 0) {\n      return;\n    }\n    var path,\n      edge = new Group();\n    setTransform(edge, transform);\n    group.append(edge);\n    sanitizeRadius(rl);\n    sanitizeRadius(rr);\n\n    // draw main border.  this is the area without the rounded corners\n    path = new Path({\n      fill: {\n        color: color\n      },\n      stroke: null\n    });\n    edge.append(path);\n    path.moveTo(rl.x ? Math.max(rl.x, Wleft) : 0, 0).lineTo(len - (rr.x ? Math.max(rr.x, Wright) : 0), 0).lineTo(len - Math.max(rr.x, Wright), Wtop).lineTo(Math.max(rl.x, Wleft), Wtop).close();\n    if (rl.x) {\n      drawRoundCorner(Wleft, rl, [-1, 0, 0, 1, rl.x, 0]);\n    }\n    if (rr.x) {\n      drawRoundCorner(Wright, rr, [1, 0, 0, 1, len - rr.x, 0]);\n    }\n\n    // draws one round corner, starting at origin (needs to be\n    // translated/rotated to be placed properly).\n    function drawRoundCorner(Wright, r, transform) {\n      var angle = Math.PI / 2 * Wright / (Wright + Wtop);\n\n      // not sanitizing this one, because negative values\n      // are useful to fill the box correctly.\n      var ri = {\n        x: r.x - Wright,\n        y: r.y - Wtop\n      };\n      var path = new Path({\n        fill: {\n          color: color\n        },\n        stroke: null\n      }).moveTo(0, 0);\n      setTransform(path, transform);\n      addArcToPath(path, 0, r.y, {\n        startAngle: -90,\n        endAngle: -radiansToDegrees(angle),\n        radiusX: r.x,\n        radiusY: r.y\n      });\n      if (ri.x > 0 && ri.y > 0) {\n        path.lineTo(ri.x * Math.cos(angle), r.y - ri.y * Math.sin(angle));\n        addArcToPath(path, 0, r.y, {\n          startAngle: -radiansToDegrees(angle),\n          endAngle: -90,\n          radiusX: ri.x,\n          radiusY: ri.y,\n          anticlockwise: true\n        });\n      } else if (ri.x > 0) {\n        path.lineTo(ri.x, Wtop).lineTo(0, Wtop);\n      } else {\n        path.lineTo(ri.x, Wtop).lineTo(ri.x, 0);\n      }\n      edge.append(path.close());\n    }\n  }\n  function drawBackground(box) {\n    var background = new Group();\n    setClipping(background, roundBox(box, rTL0, rTR0, rBR0, rBL0));\n    group.append(background);\n    if (backgroundColor) {\n      var path = new Path({\n        fill: {\n          color: backgroundColor.toCssRgba()\n        },\n        stroke: null\n      });\n      path.moveTo(box.left, box.top).lineTo(box.right, box.top).lineTo(box.right, box.bottom).lineTo(box.left, box.bottom).close();\n      background.append(path);\n    }\n    for (var i = backgroundImage.length; --i >= 0;) {\n      drawOneBackground(background, box, backgroundImage[i], backgroundRepeat[i % backgroundRepeat.length], backgroundPosition[i % backgroundPosition.length], backgroundOrigin[i % backgroundOrigin.length], backgroundSize[i % backgroundSize.length]);\n    }\n  }\n  function drawOneBackground(group, box, background, backgroundRepeat, backgroundPosition, backgroundOrigin, backgroundSize) {\n    if (!background || background == \"none\") {\n      return;\n    }\n    if (background.type == \"url\") {\n      var img = IMAGE_CACHE[background.url];\n      if (img && img.width > 0 && img.height > 0) {\n        drawBackgroundImage(group, box, img.width, img.height, function (group, rect) {\n          group.append(new Image(background.url, rect));\n        });\n      }\n    } else if (background.type == \"linear\") {\n      drawBackgroundImage(group, box, box.width, box.height, gradientRenderer(background));\n    } else {\n      return;\n    }\n    function drawBackgroundImage(group, box, img_width, img_height, renderBG) {\n      var aspect_ratio = img_width / img_height,\n        f;\n\n      // for background-origin: border-box the box is already appropriate\n      var orgBox = box;\n      if (backgroundOrigin == \"content-box\") {\n        orgBox = innerBox(orgBox, \"border-*-width\", element);\n        orgBox = innerBox(orgBox, \"padding-*\", element);\n      } else if (backgroundOrigin == \"padding-box\") {\n        orgBox = innerBox(orgBox, \"border-*-width\", element);\n      }\n      if (!/^\\s*auto(\\s+auto)?\\s*$/.test(backgroundSize)) {\n        if (backgroundSize == \"contain\") {\n          f = Math.min(orgBox.width / img_width, orgBox.height / img_height);\n          img_width *= f;\n          img_height *= f;\n        } else if (backgroundSize == \"cover\") {\n          f = Math.max(orgBox.width / img_width, orgBox.height / img_height);\n          img_width *= f;\n          img_height *= f;\n        } else {\n          var size = backgroundSize.split(/\\s+/g);\n          // compute width\n          if (/%$/.test(size[0])) {\n            img_width = orgBox.width * parseFloat(size[0]) / 100;\n          } else {\n            img_width = parseFloat(size[0]);\n          }\n          // compute height\n          if (size.length == 1 || size[1] == \"auto\") {\n            img_height = img_width / aspect_ratio;\n          } else if (/%$/.test(size[1])) {\n            img_height = orgBox.height * parseFloat(size[1]) / 100;\n          } else {\n            img_height = parseFloat(size[1]);\n          }\n        }\n      }\n      var pos = String(backgroundPosition);\n\n      // IE sometimes reports single-word positions\n      // https://github.com/telerik/kendo-ui-core/issues/2786\n      //\n      // it seems to switch to percentages when the horizontal\n      // position is not \"center\", therefore we don't handle\n      // multi-word cases here.  All other browsers return\n      // percentages or pixels instead of keywords.  At least\n      // for now...\n      switch (pos) {\n        case \"bottom\":\n          pos = \"50% 100%\";\n          break;\n        case \"top\":\n          pos = \"50% 0\";\n          break;\n        case \"left\":\n          pos = \"0 50%\";\n          break;\n        case \"right\":\n          pos = \"100% 50%\";\n          break;\n        case \"center\":\n          pos = \"50% 50%\";\n          break;\n      }\n      pos = pos.split(/\\s+/);\n      if (pos.length == 1) {\n        pos[1] = \"50%\";\n      }\n      if (/%$/.test(pos[0])) {\n        pos[0] = parseFloat(pos[0]) / 100 * (orgBox.width - img_width);\n      } else {\n        pos[0] = parseFloat(pos[0]);\n      }\n      if (/%$/.test(pos[1])) {\n        pos[1] = parseFloat(pos[1]) / 100 * (orgBox.height - img_height);\n      } else {\n        pos[1] = parseFloat(pos[1]);\n      }\n      var rect = new geo.Rect([orgBox.left + pos[0], orgBox.top + pos[1]], [img_width, img_height]);\n\n      // XXX: background-repeat could be implemented more\n      //      efficiently as a fill pattern (at least for PDF\n      //      output, probably SVG too).\n\n      function rewX() {\n        while (rect.origin.x > box.left) {\n          rect.origin.x -= img_width;\n        }\n      }\n      function rewY() {\n        while (rect.origin.y > box.top) {\n          rect.origin.y -= img_height;\n        }\n      }\n      function repeatX() {\n        while (rect.origin.x < box.right) {\n          renderBG(group, rect.clone());\n          rect.origin.x += img_width;\n        }\n      }\n      if (backgroundRepeat == \"no-repeat\") {\n        renderBG(group, rect);\n      } else if (backgroundRepeat == \"repeat-x\") {\n        rewX();\n        repeatX();\n      } else if (backgroundRepeat == \"repeat-y\") {\n        rewY();\n        while (rect.origin.y < box.bottom) {\n          renderBG(group, rect.clone());\n          rect.origin.y += img_height;\n        }\n      } else if (backgroundRepeat == \"repeat\") {\n        rewX();\n        rewY();\n        var origin = rect.origin.clone();\n        while (rect.origin.y < box.bottom) {\n          rect.origin.x = origin.x;\n          repeatX();\n          rect.origin.y += img_height;\n        }\n      }\n    }\n  }\n  function drawBullet() {\n    var listStyleType = getPropertyValue(style, \"list-style-type\");\n    if (listStyleType == \"none\") {\n      return;\n    }\n    var listStylePosition = getPropertyValue(style, \"list-style-position\");\n    function _drawBullet(f) {\n      saveStyle(element, function () {\n        element.style.position = \"relative\";\n        var bullet = element.ownerDocument.createElement(KENDO_PSEUDO_ELEMENT);\n        bullet.style.position = \"absolute\";\n        bullet.style.boxSizing = \"border-box\";\n        if (listStylePosition == \"outside\") {\n          bullet.style.width = \"6em\";\n          bullet.style.left = \"-6.8em\";\n          bullet.style.textAlign = \"right\";\n        } else {\n          bullet.style.left = \"0px\";\n        }\n        f(bullet);\n        element.insertBefore(bullet, element.firstChild);\n        renderElement(bullet, group);\n        element.removeChild(bullet);\n      });\n    }\n    function elementIndex(f) {\n      var a = element.parentNode.children;\n      var k = element.getAttribute(\"kendo-split-index\");\n      if (k != null) {\n        return f(k | 0, a.length);\n      }\n      for (var i = 0; i < a.length; ++i) {\n        if (a[i] === element) {\n          return f(i, a.length);\n        }\n      }\n    }\n    switch (listStyleType) {\n      case \"circle\":\n      case \"disc\":\n      case \"square\":\n        _drawBullet(function (bullet) {\n          bullet.innerHTML = '&nbsp;';\n          bullet.setAttribute(KENDO_BULLET_TYPE, listStyleType);\n        });\n        break;\n      case \"decimal\":\n      case \"decimal-leading-zero\":\n        _drawBullet(function (bullet) {\n          elementIndex(function (idx) {\n            ++idx;\n            if (listStyleType == \"decimal-leading-zero\" && idx < 10) {\n              idx = \"0\" + idx;\n            }\n            bullet.innerHTML = idx + \".\";\n          });\n        });\n        break;\n      case \"lower-roman\":\n      case \"upper-roman\":\n        _drawBullet(function (bullet) {\n          elementIndex(function (idx) {\n            idx = arabicToRoman(idx + 1);\n            if (listStyleType == \"upper-roman\") {\n              idx = idx.toUpperCase();\n            }\n            bullet.innerHTML = idx + \".\";\n          });\n        });\n        break;\n      case \"lower-latin\":\n      case \"lower-alpha\":\n      case \"upper-latin\":\n      case \"upper-alpha\":\n        _drawBullet(function (bullet) {\n          elementIndex(function (idx) {\n            idx = alphaNumeral(idx);\n            if (/^upper/i.test(listStyleType)) {\n              idx = idx.toUpperCase();\n            }\n            bullet.innerHTML = idx + \".\";\n          });\n        });\n        break;\n    }\n  }\n\n  // draws a single border box\n  function drawOneBox(box, isFirst, isLast) {\n    if (box.width === 0 || box.height === 0) {\n      return;\n    }\n    drawBackground(box);\n    var shouldDrawLeft = left.width > 0 && (isFirst && dir == \"ltr\" || isLast && dir == \"rtl\");\n    var shouldDrawRight = right.width > 0 && (isLast && dir == \"ltr\" || isFirst && dir == \"rtl\");\n\n    // The most general case is that the 4 borders have different widths and border\n    // radiuses.  The way that is handled is by drawing 3 Paths for each border: the\n    // straight line, and two round corners which represent half of the entire rounded\n    // corner.  To simplify code those shapes are drawed at origin (by the drawEdge\n    // function), then translated/rotated into the right position.\n    //\n    // However, this leads to poor results due to rounding in the simpler cases where\n    // borders are straight lines.  Therefore we handle a few such cases separately with\n    // straight lines. C^wC^wC^w -- nope, scratch that.  poor rendering was because of a bug\n    // in Chrome (getClientRects() returns rounded integer values rather than exact floats.\n    // web dev is still a ghetto.)\n\n    // first, just in case there is no border...\n    if (top.width === 0 && left.width === 0 && right.width === 0 && bottom.width === 0) {\n      return;\n    }\n\n    // START paint borders\n    // if all borders have equal colors...\n    if (top.color == right.color && top.color == bottom.color && top.color == left.color) {\n      // if same widths too, we can draw the whole border by stroking a single path.\n      if (top.width == right.width && top.width == bottom.width && top.width == left.width) {\n        if (shouldDrawLeft && shouldDrawRight) {\n          // reduce box by half the border width, so we can draw it by stroking.\n          box = innerBox(box, top.width / 2);\n\n          // adjust the border radiuses, again by top.width/2, and make the path element.\n          var path = elementRoundBox(element, box, top.width / 2);\n          path.options.stroke = {\n            color: top.color,\n            width: top.width\n          };\n          group.append(path);\n          return;\n        }\n      }\n    }\n\n    // if border radiuses are zero and widths are at most one pixel, we can again use simple\n    // paths.\n    if (rTL0.x === 0 && rTR0.x === 0 && rBR0.x === 0 && rBL0.x === 0) {\n      // alright, 1.9px will do as well.  the difference in color blending should not be\n      // noticeable.\n      if (top.width < 2 && left.width < 2 && right.width < 2 && bottom.width < 2) {\n        // top border\n        if (top.width > 0) {\n          group.append(new Path({\n            stroke: {\n              width: top.width,\n              color: top.color\n            }\n          }).moveTo(box.left, box.top + top.width / 2).lineTo(box.right, box.top + top.width / 2));\n        }\n\n        // bottom border\n        if (bottom.width > 0) {\n          group.append(new Path({\n            stroke: {\n              width: bottom.width,\n              color: bottom.color\n            }\n          }).moveTo(box.left, box.bottom - bottom.width / 2).lineTo(box.right, box.bottom - bottom.width / 2));\n        }\n\n        // left border\n        if (shouldDrawLeft) {\n          group.append(new Path({\n            stroke: {\n              width: left.width,\n              color: left.color\n            }\n          }).moveTo(box.left + left.width / 2, box.top).lineTo(box.left + left.width / 2, box.bottom));\n        }\n\n        // right border\n        if (shouldDrawRight) {\n          group.append(new Path({\n            stroke: {\n              width: right.width,\n              color: right.color\n            }\n          }).moveTo(box.right - right.width / 2, box.top).lineTo(box.right - right.width / 2, box.bottom));\n        }\n        return;\n      }\n    }\n    // END paint borders\n\n    var tmp = adjustBorderRadiusForBox(box, rTL0, rTR0, rBR0, rBL0);\n    var rTL = tmp.tl;\n    var rTR = tmp.tr;\n    var rBR = tmp.br;\n    var rBL = tmp.bl;\n\n    // top border\n    drawEdge(top.color, box.width, top.width, left.width, right.width, rTL, rTR, [1, 0, 0, 1, box.left, box.top]);\n\n    // bottom border\n    drawEdge(bottom.color, box.width, bottom.width, right.width, left.width, rBR, rBL, [-1, 0, 0, -1, box.right, box.bottom]);\n\n    // for left/right borders we need to invert the border-radiuses\n    function inv(p) {\n      return {\n        x: p.y,\n        y: p.x\n      };\n    }\n\n    // left border\n    drawEdge(left.color, box.height, left.width, bottom.width, top.width, inv(rBL), inv(rTL), [0, -1, 1, 0, box.left, box.bottom]);\n\n    // right border\n    drawEdge(right.color, box.height, right.width, top.width, bottom.width, inv(rTR), inv(rBR), [0, 1, -1, 0, box.right, box.top]);\n  }\n}\nfunction gradientRenderer(gradient) {\n  return function (group, rect) {\n    var width = rect.width(),\n      height = rect.height();\n    switch (gradient.type) {\n      case \"linear\":\n        // figure out the angle.\n        var angle = gradient.angle != null ? gradient.angle : Math.PI;\n        switch (gradient.to) {\n          case \"top\":\n            angle = 0;\n            break;\n          case \"left\":\n            angle = -Math.PI / 2;\n            break;\n          case \"bottom\":\n            angle = Math.PI;\n            break;\n          case \"right\":\n            angle = Math.PI / 2;\n            break;\n          case \"top left\":\n          case \"left top\":\n            angle = -Math.atan2(height, width);\n            break;\n          case \"top right\":\n          case \"right top\":\n            angle = Math.atan2(height, width);\n            break;\n          case \"bottom left\":\n          case \"left bottom\":\n            angle = Math.PI + Math.atan2(height, width);\n            break;\n          case \"bottom right\":\n          case \"right bottom\":\n            angle = Math.PI - Math.atan2(height, width);\n            break;\n        }\n        if (gradient.reverse) {\n          angle -= Math.PI;\n        }\n\n        // limit the angle between 0..2PI\n        angle %= 2 * Math.PI;\n        if (angle < 0) {\n          angle += 2 * Math.PI;\n        }\n\n        // compute gradient's start/end points.  here len is the length of the gradient line\n        // and x,y is the end point relative to the center of the rectangle in conventional\n        // (math) axis direction.\n\n        // this is the original (unscaled) length of the gradient line.  needed to deal with\n        // absolutely positioned color stops.  formula from the CSS spec:\n        // http://dev.w3.org/csswg/css-images-3/#linear-gradient-syntax\n        var pxlen = Math.abs(width * Math.sin(angle)) + Math.abs(height * Math.cos(angle));\n\n        // The math below is pretty simple, but it took a while to figure out.  We compute x\n        // and y, the *end* of the gradient line.  However, we want to transform them into\n        // element-based coordinates (SVG's gradientUnits=\"objectBoundingBox\").  That means,\n        // x=0 is the left edge, x=1 is the right edge, y=0 is the top edge and y=1 is the\n        // bottom edge.\n        //\n        // A naive approach would use the original angle for these calculations.  Say we'd\n        // like to draw a gradient angled at 45deg in a 100x400 box.  When we use\n        // objectBoundingBox, the renderer will draw it in a 1x1 *square* box, and then\n        // scale that to the desired dimensions.  The 45deg angle will look more like 70deg\n        // after scaling.  SVG (http://www.w3.org/TR/SVG/pservers.html#LinearGradients) says\n        // the following:\n        //\n        //     When gradientUnits=\"objectBoundingBox\" and 'gradientTransform' is the\n        //     identity matrix, the normal of the linear gradient is perpendicular to the\n        //     gradient vector in object bounding box space (i.e., the abstract coordinate\n        //     system where (0,0) is at the top/left of the object bounding box and (1,1) is\n        //     at the bottom/right of the object bounding box). When the object's bounding\n        //     box is not square, the gradient normal which is initially perpendicular to\n        //     the gradient vector within object bounding box space may render\n        //     non-perpendicular relative to the gradient vector in user space. If the\n        //     gradient vector is parallel to one of the axes of the bounding box, the\n        //     gradient normal will remain perpendicular. This transformation is due to\n        //     application of the non-uniform scaling transformation from bounding box space\n        //     to user space.\n        //\n        // which is an extremely long and confusing way to tell what I just said above.\n        //\n        // For this reason we need to apply the reverse scaling to the original angle, so\n        // that when it'll finally be rendered it'll actually be at the desired slope.  Now\n        // I'll let you figure out the math yourself.\n\n        var scaledAngle = Math.atan(width * Math.tan(angle) / height);\n        var sin = Math.sin(scaledAngle),\n          cos = Math.cos(scaledAngle);\n        var len = Math.abs(sin) + Math.abs(cos);\n        var x = len / 2 * sin;\n        var y = len / 2 * cos;\n\n        // Because of the arctangent, our scaledAngle ends up between -PI/2..PI/2, possibly\n        // losing the intended direction of the gradient.  The following fixes it.\n        if (angle > Math.PI / 2 && angle <= 3 * Math.PI / 2) {\n          x = -x;\n          y = -y;\n        }\n\n        // compute the color stops.\n        var implicit = [],\n          right = 0;\n        var stops = gradient.stops.map(function (s, i) {\n          var offset = s.percent;\n          if (offset) {\n            offset = parseFloat(offset) / 100;\n          } else if (s.length) {\n            offset = parseFloat(s.length) / pxlen;\n          } else if (i === 0) {\n            offset = 0;\n          } else if (i == gradient.stops.length - 1) {\n            offset = 1;\n          }\n          var stop = {\n            color: s.color.toCssRgba(),\n            offset: offset\n          };\n          if (offset != null) {\n            right = offset;\n            // fix implicit offsets\n            implicit.forEach(function (s, i) {\n              var stop = s.stop;\n              stop.offset = s.left + (right - s.left) * (i + 1) / (implicit.length + 1);\n            });\n            implicit = [];\n          } else {\n            implicit.push({\n              left: right,\n              stop: stop\n            });\n          }\n          return stop;\n        });\n        var start = [0.5 - x, 0.5 + y];\n        var end = [0.5 + x, 0.5 - y];\n\n        // finally, draw it.\n        group.append(Path.fromRect(rect).stroke(null).fill(new LinearGradient({\n          start: start,\n          end: end,\n          stops: stops,\n          userSpace: false\n        })));\n        break;\n      case \"radial\":\n        // XXX:\n        if (window.console && window.console.log) {\n          window.console.log(\"Radial gradients are not yet supported in HTML renderer\");\n        }\n        break;\n    }\n  };\n}\nfunction maybeRenderWidget(element, group) {\n  var visual;\n  if (element._kendoExportVisual) {\n    var rect = element.getBoundingClientRect();\n    var size = {\n      width: rect.width,\n      height: rect.height\n    };\n    visual = element._kendoExportVisual(size);\n  } else if (window.kendo && window.kendo.jQuery && element.getAttribute(window.kendo.attr(\"role\"))) {\n    var widget = window.kendo.widgetInstance(window.kendo.jQuery(element));\n    if (widget && (widget.exportDOMVisual || widget.exportVisual)) {\n      if (widget.exportDOMVisual) {\n        visual = widget.exportDOMVisual();\n      } else {\n        visual = widget.exportVisual();\n      }\n    }\n  }\n  if (!visual) {\n    return false;\n  }\n  var wrap = new Group();\n  wrap.children.push(visual);\n  var bbox = element.getBoundingClientRect();\n  wrap.transform(geo.transform().translate(bbox.left, bbox.top));\n  group.append(wrap);\n  return true;\n}\nfunction maybeRenderBullet(element, group) {\n  var bulletType = element.getAttribute(KENDO_BULLET_TYPE);\n  if (!bulletType) {\n    return false;\n  }\n  var box = element.getBoundingClientRect();\n  var color = getComputedStyle(element).color;\n  if (bulletType === 'square') {\n    var rectSize = box.height / 5;\n    group.append(new Rect(new geo.Rect([box.right - rectSize, box.top + box.height / 2.1], [rectSize, rectSize])).fill(color).stroke(color));\n  } else {\n    var radius = box.height / 7;\n    var center = [box.right - radius, box.top + (box.height + radius) / 2];\n    var circle = new Circle(new geo.Circle(center, radius));\n    if (bulletType === 'circle') {\n      circle.stroke(color, 0.5);\n    } else {\n      circle.fill(color).stroke(null);\n    }\n    group.append(circle);\n  }\n  return true;\n}\nfunction renderImage(element, url, group) {\n  var box = getContentBox(element);\n  var rect = new geo.Rect([box.left, box.top], [box.width, box.height]);\n  var image = new Image(url, rect);\n  setClipping(image, elementRoundBox(element, box, \"content\"));\n  group.append(image);\n}\nfunction zIndexSort(a, b) {\n  var sa = getComputedStyle(a);\n  var sb = getComputedStyle(b);\n  var za = parseFloat(getPropertyValue(sa, \"z-index\"));\n  var zb = parseFloat(getPropertyValue(sb, \"z-index\"));\n  var pa = getPropertyValue(sa, \"position\");\n  var pb = getPropertyValue(sb, \"position\");\n  if (isNaN(za) && isNaN(zb)) {\n    if (/static|absolute/.test(pa) && /static|absolute/.test(pb)) {\n      return 0;\n    }\n    if (pa == \"static\") {\n      return -1;\n    }\n    if (pb == \"static\") {\n      return 1;\n    }\n    return 0;\n  }\n  if (isNaN(za)) {\n    return zb === 0 ? 0 : zb > 0 ? -1 : 1;\n  }\n  if (isNaN(zb)) {\n    return za === 0 ? 0 : za > 0 ? 1 : -1;\n  }\n  return parseFloat(za) - parseFloat(zb);\n}\nfunction isFormField(element) {\n  return /^(?:textarea|select|input)$/i.test(element.tagName);\n}\nfunction getSelectedOption(element) {\n  if (element.selectedOptions && element.selectedOptions.length > 0) {\n    return element.selectedOptions[0];\n  }\n  return element.options[element.selectedIndex];\n}\nfunction renderCheckbox(element, group) {\n  var style = getComputedStyle(element);\n  var color = getPropertyValue(style, \"color\");\n  var box = element.getBoundingClientRect();\n  if (element.type == \"checkbox\") {\n    group.append(Path.fromRect(new geo.Rect([box.left + 1, box.top + 1], [box.width - 2, box.height - 2])).stroke(color, 1));\n    if (element.checked) {\n      // fill a rectangle inside?  looks kinda ugly.\n      // group.append(\n      //     Path.fromRect(\n      //         new geo.Rect([ box.left+4, box.top+4 ],\n      //                      [ box.width-8, box.height-8])\n      //     ).fill(color).stroke(null)\n      // );\n\n      // let's draw a checkmark instead.  artistic, eh?\n      group.append(new Path().stroke(color, 1.2).moveTo(box.left + 0.22 * box.width, box.top + 0.55 * box.height).lineTo(box.left + 0.45 * box.width, box.top + 0.75 * box.height).lineTo(box.left + 0.78 * box.width, box.top + 0.22 * box.width));\n    }\n  } else {\n    group.append(new Circle(new geo.Circle([(box.left + box.right) / 2, (box.top + box.bottom) / 2], Math.min(box.width - 2, box.height - 2) / 2)).stroke(color, 1));\n    if (element.checked) {\n      group.append(new Circle(new geo.Circle([(box.left + box.right) / 2, (box.top + box.bottom) / 2], Math.min(box.width - 8, box.height - 8) / 2)).fill(color).stroke(null));\n    }\n  }\n}\nfunction renderFormField(element, group) {\n  var tag = element.tagName.toLowerCase();\n  if (tag == \"input\" && (element.type == \"checkbox\" || element.type == \"radio\")) {\n    return renderCheckbox(element, group);\n  }\n  var p = element.parentNode;\n  var doc = element.ownerDocument;\n  var el = doc.createElement(KENDO_PSEUDO_ELEMENT);\n  var option;\n  setStyle(el, getCssText(getComputedStyle(element)));\n  if (tag == \"input\") {\n    el.style.whiteSpace = \"pre\";\n  }\n  if (tag == \"select\" || tag == \"textarea\") {\n    el.style.overflow = \"auto\";\n  }\n  if (tag == \"select\") {\n    if (element.multiple) {\n      for (var i = 0; i < element.options.length; ++i) {\n        option = doc.createElement(KENDO_PSEUDO_ELEMENT);\n        setStyle(option, getCssText(getComputedStyle(element.options[i])));\n        option.style.display = \"block\"; // IE9 messes up without this\n        option.textContent = element.options[i].textContent;\n        el.appendChild(option);\n      }\n    } else {\n      option = getSelectedOption(element);\n      if (option) {\n        el.textContent = option.textContent;\n      }\n    }\n  } else {\n    el.textContent = element.value;\n  }\n  p.insertBefore(el, element);\n  el.scrollLeft = element.scrollLeft;\n  el.scrollTop = element.scrollTop;\n\n  // must temporarily hide the original element, otherwise it\n  // may affect layout of the fake element we want to render.\n  element.style.display = \"none\";\n  renderContents(el, group);\n  element.style.display = \"\";\n  p.removeChild(el);\n}\nfunction serializeSVG(element) {\n  var serializer = new window.XMLSerializer();\n  var xml = serializer.serializeToString(element);\n  if (browser.mozilla && !(element.getAttribute(\"width\") && element.getAttribute(\"height\"))) {\n    var doc = new window.DOMParser().parseFromString(xml, \"image/svg+xml\");\n    var svg = doc.documentElement;\n    var box = getContentBox(element);\n    svg.setAttribute(\"width\", box.width);\n    svg.setAttribute(\"height\", box.height);\n    xml = serializer.serializeToString(svg);\n  }\n  return xml;\n}\nfunction renderContents(element, group) {\n  if (nodeInfo._stackingContext.element === element) {\n    // the group that was set in pushNodeInfo might have\n    // changed due to clipping/transforms, update it here.\n    nodeInfo._stackingContext.group = group;\n  }\n  switch (element.tagName.toLowerCase()) {\n    case \"img\":\n      renderImage(element, element.src, group);\n      break;\n    case \"svg\":\n      var xml = serializeSVG(element);\n      var dataURL = \"data:image/svg+xml;base64,\" + encodeBase64(xml);\n      renderImage(element, dataURL, group);\n      break;\n    case \"canvas\":\n      try {\n        renderImage(element, element.toDataURL(\"image/png\"), group);\n      } catch (ex) {\n        // tainted; can't draw it, ignore.\n      }\n      break;\n    case \"textarea\":\n    case \"input\":\n    case \"select\":\n      renderFormField(element, group);\n      break;\n    default:\n      var children = [],\n        floats = [],\n        positioned = [];\n      for (var i = element.firstChild; i; i = i.nextSibling) {\n        switch (i.nodeType) {\n          case 3:\n            // Text\n            if (/\\S/.test(i.data)) {\n              renderText(element, i, group);\n            }\n            break;\n          case 1:\n            // Element\n            var style = getComputedStyle(i);\n            var floating = getPropertyValue(style, \"float\");\n            var position = getPropertyValue(style, \"position\");\n            if (position != \"static\") {\n              positioned.push(i);\n            } else if (floating != \"none\") {\n              floats.push(i);\n            } else {\n              children.push(i);\n            }\n            break;\n        }\n      }\n      mergeSort(children, zIndexSort).forEach(function (el) {\n        renderElement(el, group);\n      });\n      mergeSort(floats, zIndexSort).forEach(function (el) {\n        renderElement(el, group);\n      });\n      mergeSort(positioned, zIndexSort).forEach(function (el) {\n        renderElement(el, group);\n      });\n  }\n}\nfunction renderText(element, node, group) {\n  if (emptyClipbox()) {\n    return;\n  }\n  var style = getComputedStyle(element);\n  if (parseFloat(getPropertyValue(style, \"text-indent\")) < -500) {\n    // assume it should not be displayed.  the slider's\n    // draggable handle displays a Drag text for some reason,\n    // having text-indent: -3333px.\n    return;\n  }\n  var text = node.data;\n  var start = 0;\n  var end = text.search(/\\S\\s*$/) + 1;\n  if (!end) {\n    return; // whitespace-only node\n  }\n  var fontSize = getPropertyValue(style, \"font-size\");\n  var lineHeight = getPropertyValue(style, \"line-height\");\n\n  // simply getPropertyValue(\"font\") doesn't work in Firefox :-\\\n  var font = [getPropertyValue(style, \"font-style\"), getPropertyValue(style, \"font-variant\"), getPropertyValue(style, \"font-weight\"), fontSize,\n  // no need for line height here; it breaks layout in FF\n  getPropertyValue(style, \"font-family\")].join(\" \");\n  fontSize = parseFloat(fontSize);\n  lineHeight = parseFloat(lineHeight);\n  if (fontSize === 0 || isNaN(fontSize)) {\n    return;\n  }\n  var color = getPropertyValue(style, \"color\");\n  var range = element.ownerDocument.createRange();\n  var align = getPropertyValue(style, \"text-align\");\n  var isJustified = align == \"justify\";\n  var columnCount = getPropertyValue(style, \"column-count\", 1);\n  var whiteSpace = getPropertyValue(style, \"white-space\");\n  var textTransform = getPropertyValue(style, \"text-transform\");\n\n  // A line of 500px, with a font of 12px, contains an average of 80 characters, but since we\n  // err, we'd like to guess a bigger number rather than a smaller one.  Multiplying by 5\n  // seems to be a good option.\n  var estimateLineLength = element.getBoundingClientRect().width / fontSize * 5;\n  if (estimateLineLength === 0) {\n    estimateLineLength = 500;\n  }\n\n  // we'll maintain this so we can workaround bugs in Chrome's Range.getClientRects\n  // https://github.com/telerik/kendo/issues/5740\n  var prevLineBottom = null;\n  var underline = nodeInfo[\"underline\"];\n  var lineThrough = nodeInfo[\"line-through\"];\n  var overline = nodeInfo[\"overline\"];\n  var underlineOffset = nodeInfo[\"underline-offset\"];\n  if (underline) {\n    forEachRect(decorateUnder);\n  }\n\n  // doChunk returns true when all text has been rendered\n  while (!doChunk()) {}\n  if (lineThrough || overline) {\n    forEachRect(decorateOver);\n  }\n  return; // only function declarations after this line\n\n  function forEachRect(callback) {\n    range.selectNode(node);\n    var clientRects = slice(range.getClientRects());\n    forEachRect = function (cb) {\n      return clientRects.forEach(cb);\n    };\n    forEachRect(callback);\n  }\n  function actuallyGetRangeBoundingRect(range) {\n    // XXX: to be revised when this Chrome bug is fixed:\n    // https://bugs.chromium.org/p/chromium/issues/detail?id=612459\n    if (microsoft || browser.chrome || browser.safari) {\n      // Workaround browser bugs: IE and Chrome would sometimes\n      // return 0 or 1-width rectangles before or after the main\n      // one.  https://github.com/telerik/kendo/issues/4674\n\n      // Actually Chrome 50 got worse, since the rectangles can now have the width of a\n      // full character, making it hard to tell whether it's a bogus rectangle or valid\n      // selection location.  The workaround is to ignore rectangles that fall on the\n      // previous line.  https://github.com/telerik/kendo/issues/5740\n      var rectangles = range.getClientRects(),\n        box = {\n          top: Infinity,\n          right: -Infinity,\n          bottom: -Infinity,\n          left: Infinity\n        },\n        done = false;\n      for (var i = 0; i < rectangles.length; ++i) {\n        var b = rectangles[i];\n        if (b.width <= 1 || b.bottom === prevLineBottom) {\n          continue; // bogus rectangle\n        }\n        box.left = Math.min(b.left, box.left);\n        box.top = Math.min(b.top, box.top);\n        box.right = Math.max(b.right, box.right);\n        box.bottom = Math.max(b.bottom, box.bottom);\n        done = true;\n      }\n      if (!done) {\n        return range.getBoundingClientRect();\n      }\n      box.width = box.right - box.left;\n      box.height = box.bottom - box.top;\n      return box;\n    }\n    return range.getBoundingClientRect();\n  }\n\n  // Render a chunk of text, typically one line (but for justified text we render each word as\n  // a separate Text object, because spacing is variable).  Returns true when it finished the\n  // current node.  After each chunk it updates `start` to just after the last rendered\n  // character.\n  function doChunk() {\n    var origStart = start;\n    var box,\n      pos = text.substr(start).search(/\\S/);\n    start += pos;\n    if (pos < 0 || start >= end) {\n      return true;\n    }\n\n    // Select a single character to determine the height of a line of text.  The box.bottom\n    // will be essential for us to figure out where the next line begins.\n    range.setStart(node, start);\n    range.setEnd(node, start + 1);\n    box = actuallyGetRangeBoundingRect(range);\n\n    // for justified text we must split at each space, because space has variable width.\n    var found = false;\n    if (isJustified || columnCount > 1) {\n      pos = text.substr(start).search(/\\s/);\n      if (pos >= 0) {\n        // we can only split there if it's on the same line, otherwise we'll fall back\n        // to the default mechanism (see findEOL below).\n        range.setEnd(node, start + pos);\n        var r = actuallyGetRangeBoundingRect(range);\n        if (r.bottom == box.bottom) {\n          box = r;\n          found = true;\n          start += pos;\n        }\n      }\n    }\n    if (!found) {\n      // This code does three things: (1) it selects one line of text in `range`, (2) it\n      // leaves the bounding rect of that line in `box` and (3) it returns the position\n      // just after the EOL.  We know where the line starts (`start`) but we don't know\n      // where it ends.  To figure this out, we select a piece of text and look at the\n      // bottom of the bounding box.  If it changes, we have more than one line selected\n      // and should retry with a smaller selection.\n      //\n      // To speed things up, we first try to select all text in the node (`start` ->\n      // `end`).  If there's more than one line there, then select only half of it.  And\n      // so on.  When we find a value for `end` that fits in one line, we try increasing\n      // it (also in halves) until we get to the next line.  The algorithm stops when the\n      // right side of the bounding box does not change.\n      //\n      // One more thing to note is that everything happens in a single Text DOM node.\n      // There's no other tags inside it, therefore the left/top coordinates of the\n      // bounding box will not change.\n      pos = function findEOL(min, eol, max) {\n        range.setEnd(node, eol);\n        var r = actuallyGetRangeBoundingRect(range);\n        if (r.bottom != box.bottom && min < eol) {\n          return findEOL(min, min + eol >> 1, eol);\n        } else if (r.right != box.right) {\n          box = r;\n          if (eol < max) {\n            return findEOL(eol, eol + max >> 1, max);\n          } else {\n            return eol;\n          }\n        } else {\n          return eol;\n        }\n      }(start, Math.min(end, start + estimateLineLength), end);\n      if (pos == start) {\n        // if EOL is at the start, then no more text fits on this line.  Skip the\n        // remainder of this node entirely to avoid a stack overflow.\n        return true;\n      }\n      start = pos;\n      pos = range.toString().search(/\\s+$/);\n      if (pos === 0) {\n        return false; // whitespace only; we should not get here.\n      }\n      if (pos > 0) {\n        // eliminate trailing whitespace\n        range.setEnd(node, range.startOffset + pos);\n        box = actuallyGetRangeBoundingRect(range);\n      }\n    }\n\n    // another workaround for IE: if we rely on getBoundingClientRect() we'll overlap with the bullet for LI\n    // elements.  Calling getClientRects() and using the *first* rect appears to give us the correct location.\n    // Note: not to be used in Chrome as it randomly returns a zero-width rectangle from the previous line.\n    if (microsoft) {\n      box = range.getClientRects()[0];\n    }\n    var str = range.toString();\n    if (!/^(?:pre|pre-wrap)$/i.test(whiteSpace)) {\n      // node with non-significant space -- collapse whitespace.\n      str = str.replace(/\\s+/g, \" \");\n    } else if (/\\t/.test(str)) {\n      // with significant whitespace we need to do something about literal TAB characters.\n      // There's no TAB glyph in a font so they would be rendered in PDF as an empty box,\n      // and the whole text will stretch to fill the original width.  The core PDF lib\n      // does not have sufficient context to deal with it.\n\n      // calculate the starting column here, since we initially discarded any whitespace.\n      var cc = 0;\n      for (pos = origStart; pos < range.startOffset; ++pos) {\n        var code = text.charCodeAt(pos);\n        if (code == 9) {\n          // when we meet a TAB we must round up to the next tab stop.\n          // in all browsers TABs seem to be 8 characters.\n          cc += 8 - cc % 8;\n        } else if (code == 10 || code == 13) {\n          // just in case we meet a newline we must restart.\n          cc = 0;\n        } else {\n          // ordinary character --> advance one column\n          cc++;\n        }\n      }\n\n      // based on starting column, replace any TAB characters in the string we actually\n      // have to display with spaces so that they align to columns multiple of 8.\n      while ((pos = str.search(\"\\t\")) >= 0) {\n        var indent = \"        \".substr(0, 8 - (cc + pos) % 8);\n        str = str.substr(0, pos) + indent + str.substr(pos + 1);\n      }\n    }\n    if (!found) {\n      prevLineBottom = box.bottom;\n    }\n    drawText(str, box);\n  }\n  function drawText(str, box) {\n    // In IE the box height will be approximately lineHeight, while in\n    // other browsers it'll (correctly) be the height of the bounding\n    // box for the current text/font.  Which is to say, IE sucks again.\n    // The only good solution I can think of is to measure the text\n    // ourselves and center the bounding box.\n    if (microsoft && !isNaN(lineHeight)) {\n      var height = getFontHeight(font);\n      var top = (box.top + box.bottom - height) / 2;\n      box = {\n        top: top,\n        right: box.right,\n        bottom: top + height,\n        left: box.left,\n        height: height,\n        width: box.right - box.left\n      };\n    }\n\n    // var path = new Path({ stroke: { color: \"red\" }});\n    // path.moveTo(box.left, box.top)\n    //     .lineTo(box.right, box.top)\n    //     .lineTo(box.right, box.bottom)\n    //     .lineTo(box.left, box.bottom)\n    //     .close();\n    // group.append(path);\n\n    switch (textTransform) {\n      case \"uppercase\":\n        str = str.toUpperCase();\n        break;\n      case \"lowercase\":\n        str = str.toLowerCase();\n        break;\n      case \"capitalize\":\n        str = str.replace(/(?:^|\\s)\\S/g, function (l) {\n          return l.toUpperCase();\n        });\n        break;\n    }\n    var text = new TextRect(str, new geo.Rect([box.left, box.top], [box.width, box.height]), {\n      font: font,\n      fill: {\n        color: color\n      }\n    });\n    group.append(text);\n  }\n  function drawTextLine(lineWidth, textBox, color, ypos) {\n    if (color) {\n      var path = new Path({\n        stroke: {\n          width: lineWidth,\n          color: color\n        }\n      });\n      ypos -= lineWidth;\n      path.moveTo(textBox.left, ypos).lineTo(textBox.right, ypos);\n      group.append(path);\n    }\n  }\n  function decorateOver(box) {\n    var width = fontSize / 12;\n    drawTextLine(width, box, lineThrough, box.bottom - box.height / 2.7);\n    drawTextLine(width, box, overline, box.top);\n  }\n  function decorateUnder(box) {\n    var width = fontSize / 12;\n    var underlinePos = box.bottom;\n    if (underlineOffset != null) {\n      underlinePos += underlineOffset;\n    } else {\n      underlinePos += width; // for \"auto\" it seems better to add line width\n    }\n    drawTextLine(width, box, underline, underlinePos);\n  }\n}\nfunction groupInStackingContext(element, group, zIndex) {\n  var main;\n  if (zIndex != \"auto\") {\n    // use the current stacking context\n    main = nodeInfo._stackingContext.group;\n    zIndex = parseFloat(zIndex);\n  } else {\n    // normal flow — use given container.  we still have to\n    // figure out where should we insert this element with the\n    // assumption that its z-index is zero, as the group might\n    // already contain elements with higher z-index.\n    main = group;\n    zIndex = 0;\n  }\n  var a = main.children;\n  for (var i = 0; i < a.length; ++i) {\n    if (a[i]._dom_zIndex != null && a[i]._dom_zIndex > zIndex) {\n      break;\n    }\n  }\n  var tmp = new Group();\n  main.insert(i, tmp);\n  tmp._dom_zIndex = zIndex;\n  if (main !== group) {\n    // console.log(\"Placing\", element, \"in\", nodeInfo._stackingContext.element, \"at position\", i, \" / \", a.length);\n    // console.log(a.slice(i+1));\n\n    // if (nodeInfo._matrix) {\n    //     tmp.transform(nodeInfo._matrix);\n    // }\n    if (nodeInfo._clipbox) {\n      var m = nodeInfo._matrix.invert();\n      var r = nodeInfo._clipbox.transformCopy(m);\n      setClipping(tmp, Path.fromRect(r));\n      // console.log(r);\n      // tmp.append(Path.fromRect(r));\n      // tmp.append(new Text(element.className || element.id, r.topLeft()));\n    }\n  }\n  return tmp;\n}\nfunction renderElement(element, container) {\n  var style = getComputedStyle(element);\n  updateCounters(style);\n  if (/^(style|script|link|meta|iframe|col|colgroup)$/i.test(element.tagName)) {\n    return;\n  }\n  if (nodeInfo._clipbox == null) {\n    return;\n  }\n  var opacity = parseFloat(getPropertyValue(style, \"opacity\"));\n  var visibility = getPropertyValue(style, \"visibility\");\n  var display = getPropertyValue(style, \"display\");\n  if (opacity === 0 || visibility == \"hidden\" || display == \"none\") {\n    return;\n  }\n  var tr = getTransform(style);\n  var group;\n  var zIndex = getPropertyValue(style, \"z-index\");\n  if ((tr || opacity < 1) && zIndex == \"auto\") {\n    zIndex = 0;\n  }\n  group = groupInStackingContext(element, container, zIndex);\n\n  // XXX: remove at some point\n  // group._pdfElement = element;\n  // group.options._pdfDebug = \"\";\n  // if (element.id) {\n  //     group.options._pdfDebug = \"#\" + element.id;\n  // }\n  // if (element.className) {\n  //     group.options._pdfDebug += \".\" + element.className.split(\" \").join(\".\");\n  // }\n\n  if (opacity < 1) {\n    group.opacity(opacity * group.opacity());\n  }\n  pushNodeInfo(element, style, group);\n  if (!tr) {\n    _renderWithPseudoElements(element, group);\n  } else {\n    saveStyle(element, function () {\n      // must clear transform, so getBoundingClientRect returns correct values.\n      pleaseSetPropertyValue(element.style, \"transform\", \"none\", \"important\");\n\n      // must also clear transitions, so correct values are returned *immediately*\n      pleaseSetPropertyValue(element.style, \"transition\", \"none\", \"important\");\n\n      // the presence of any transform makes it behave like it had position: relative,\n      // because why not.\n      // http://meyerweb.com/eric/thoughts/2011/09/12/un-fixing-fixed-elements-with-css-transforms/\n      if (getPropertyValue(style, \"position\") == \"static\") {\n        // but only if it's not already positioned. :-/\n        pleaseSetPropertyValue(element.style, \"position\", \"relative\", \"important\");\n      }\n\n      // must translate to origin before applying the CSS\n      // transformation, then translate back.\n      var bbox = element.getBoundingClientRect();\n      var x = bbox.left + tr.origin[0];\n      var y = bbox.top + tr.origin[1];\n      var m = [1, 0, 0, 1, -x, -y];\n      m = mmul(m, tr.matrix);\n      m = mmul(m, [1, 0, 0, 1, x, y]);\n      m = setTransform(group, m);\n      nodeInfo._matrix = nodeInfo._matrix.multiplyCopy(m);\n      _renderWithPseudoElements(element, group);\n    });\n  }\n  popNodeInfo();\n\n  //drawDebugBox(element.getBoundingClientRect(), container);\n}\n\n// function drawDebugBox(box, group, color) {\n//     var path = Path.fromRect(new geo.Rect([ box.left, box.top ], [ box.width, box.height ]));\n//     if (color) {\n//         path.stroke(color);\n//     }\n//     group.append(path);\n// }\n\n// function dumpTextNode(node) {\n//     var txt = node.data.replace(/^\\s+/, \"\");\n//     if (txt.length < 100) {\n//         console.log(node.data.length + \": |\" + txt);\n//     } else {\n//         console.log(node.data.length + \": |\" + txt.substr(0, 50) + \"|...|\" + txt.substr(-50));\n//     }\n// }\n\nfunction mmul(a, b) {\n  var a1 = a[0],\n    b1 = a[1],\n    c1 = a[2],\n    d1 = a[3],\n    e1 = a[4],\n    f1 = a[5];\n  var a2 = b[0],\n    b2 = b[1],\n    c2 = b[2],\n    d2 = b[3],\n    e2 = b[4],\n    f2 = b[5];\n  return [a1 * a2 + b1 * c2, a1 * b2 + b1 * d2, c1 * a2 + d1 * c2, c1 * b2 + d1 * d2, e1 * a2 + f1 * c2 + e2, e1 * b2 + f1 * d2 + f2];\n}\nexport { drawDOM, drawText, getFontFaces };", "map": {"version": 3, "names": ["geo", "PDF", "arabicToRoman", "createPromise", "measureText", "mergeSort", "parseColor", "utils_parseColor", "support", "template", "compileTemplate", "Path", "Text", "Group", "Image", "Circle", "Rect", "LinearGradient", "encodeBase64", "setInnerHTML", "setStyle", "browser", "slice", "thing", "Array", "prototype", "call", "KENDO_PSEUDO_ELEMENT", "KENDO_BULLET_TYPE", "IMAGE_CACHE", "nodeInfo", "_root", "inBrowser", "window", "microsoft", "msie", "edge", "TextRect", "str", "rect", "options", "<PERSON><PERSON><PERSON><PERSON>", "_pdfRect", "__proto__", "Object", "create", "constructor", "rawBBox", "addClass", "el", "cls", "classList", "add", "className", "removeClass", "remove", "split", "reduce", "a", "word", "push", "join", "setCSS", "styles", "keys", "for<PERSON>ach", "key", "style", "matches", "Element", "p", "selector", "webkitMatchesSelector", "mozMatchesSelector", "msMatchesSelector", "s", "indexOf", "document", "querySelectorAll", "closest", "test", "String", "nodeType", "parentNode", "cloneNodes", "$", "clone", "cloneNode", "$el", "$clone", "i", "data", "tagName", "getContext", "drawImage", "removeAttribute", "value", "checked", "selected", "_kendoExportVisual", "<PERSON><PERSON><PERSON><PERSON>", "nextS<PERSON>ling", "append<PERSON><PERSON><PERSON>", "dive", "node", "canvases", "length", "canvas", "orig", "kendo", "j<PERSON><PERSON><PERSON>", "getXY", "x", "y", "isArray", "drawDOM", "element", "promise", "reject", "getComputedStyle", "Error", "defineFont", "getFontFaces", "ownerDocument", "scale", "doOne", "group", "pos", "getBoundingClientRect", "setTransform", "left", "top", "_clipbox", "_matrix", "Matrix", "unit", "_stackingContext", "avoidLinks", "_avoidLinks", "renderElement", "cacheImages", "forceBreak", "forcePageBreak", "hasPaperSize", "paperSize", "paperOptions", "getPaperOptions", "def", "pageWidth", "pageHeight", "margin", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "right", "bottom", "pdf", "multiPage", "_ignore<PERSON><PERSON><PERSON>", "handlePageBreaks", "progress", "canceled", "pageNum", "next", "pages", "page", "append", "totalPages", "cancel", "setTimeout", "container", "<PERSON><PERSON><PERSON><PERSON>", "resolve", "makeTemplate", "replace", "div", "createElement", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "callback", "doc", "copy", "_destructive", "adjust", "tfoot", "ol", "children", "li", "index", "setAttribute", "display", "position", "boxSizing", "width", "paddingLeft", "paddingRight", "overflow", "insertBefore", "beforePageBreak", "whenImagesAreActuallyLoaded", "doPageBreak", "splitElement", "makePage", "bind", "keepTogether", "offsetHeight", "tag", "getAttribute", "tableLayout", "bottomPadding", "parseFloat", "getPropertyValue", "bottomBorder", "saveAdjust", "<PERSON><PERSON><PERSON><PERSON>", "breakAtElement", "fall", "falls<PERSON>n<PERSON>argin", "splitText", "firstInParent", "first", "table", "colgroup", "thead", "grid", "gridHead", "querySelector", "repeatHeaders", "range", "createRange", "setStartBefore", "setEndBefore", "extractContents", "preventBulletOnListItem", "padding", "height", "clear", "pageClassName", "box", "available", "len", "selectNodeContents", "nextnode", "findEOP", "min", "max", "setEnd", "toString", "endOffset", "drawText", "pushNodeInfo", "renderText", "_renderElement", "popNodeInfo", "parseBackgroundImage", "tok_linear_gradient", "tok_percent", "tok_length", "tok_keyword", "tok_angle", "tok_whitespace", "tok_popen", "tok_pclose", "tok_comma", "tok_url", "tok_content", "cache1", "cache2", "parse", "input", "hasOwnProperty", "skip_ws", "m", "exec", "substr", "read", "token", "read_stop", "color", "percent", "match", "toRGB", "read_linear_gradient", "propName", "angle", "to1", "to2", "stops", "reverse", "parseAngle", "stop", "type", "to", "read_url", "url", "tok", "splitProperty", "map", "cache", "separator", "cache<PERSON>ey", "ret", "last", "in_paren", "in_string", "looking_at", "rx", "trim", "substring", "getFontURL", "getFontHeight", "font", "result", "styleSheets", "do<PERSON><PERSON>lesh<PERSON>t", "ss", "rules", "cssRules", "ex", "addRules", "findFonts", "rule", "src", "cssText", "styleSheet", "r", "family", "bold", "italic", "addRule", "names", "href", "name", "obj", "get<PERSON>ounter", "getAllCounters", "values", "getPrototypeOf", "incCounter", "inc", "resetCounter", "val", "do<PERSON>ou<PERSON>s", "f", "isNaN", "updateCounters", "counterReset", "counterIncrement", "css", "toCssRgba", "elements", "pending", "done", "images", "img", "complete", "onload", "onerror", "urls", "bg", "count", "crossOrigin", "alphaNumeral", "n", "fromCharCode", "Math", "floor", "toLowerCase", "decoration", "offset", "createsStackingContext", "updateClipbox", "path", "bbox", "intersect", "emptyClipbox", "cb", "prop", "pseudoElt", "defa", "webkit", "mozilla", "opera", "arguments", "pleaseSetPropertyValue", "important", "setProperty", "p1", "p2", "toUpperCase", "getBorder", "side", "saveStyle", "func", "prev", "getBorderRadius", "sanitizeRadius", "getContentBox", "innerBox", "wt", "wr", "wb", "wl", "getTransform", "transform", "matrix", "origin", "radiansToDegrees", "radians", "PI", "num", "shape", "setClipping", "clipPath", "clip", "addArcToPath", "points", "Arc", "curvePoints", "curveTo", "adjustBorderRadiusForBox", "rTL", "rTR", "rBR", "rBL", "tl_x", "tl_y", "tr_x", "tr_y", "br_x", "br_y", "bl_x", "bl_y", "tl", "tr", "br", "bl", "elementRoundBox", "bt", "bb", "pt", "pr", "pb", "pl", "roundBox", "rTL0", "rTR0", "rBR0", "rBL0", "tmp", "fill", "stroke", "moveTo", "startAngle", "endAngle", "radiusX", "radiusY", "lineTo", "close", "formatCounter", "evalPseudo<PERSON><PERSON><PERSON><PERSON>nt", "content", "displayCounter", "parseInt", "getCssText", "_renderWithPseudoElements", "fake", "pseudo", "kind", "place", "psel", "textContent", "saveClass", "dir", "backgroundColor", "backgroundImage", "backgroundRepeat", "backgroundPosition", "<PERSON><PERSON><PERSON><PERSON>", "backgroundSize", "textOverflow", "saveTextOverflow", "version", "currentStyle", "innerbox", "boxes", "cells", "getClientRects", "adjustBoxes", "drawOneBox", "g", "_pdfLink", "drawBullet", "clipit", "isFormField", "maybeRenderWidget", "maybeRenderBullet", "renderContents", "tableBorderLeft", "tableBorderTop", "tableBox", "firstCell", "rows", "firstCellBox", "drawEdge", "Wtop", "Wleft", "<PERSON>", "rl", "rr", "draw<PERSON>ound<PERSON><PERSON><PERSON>", "ri", "cos", "sin", "anticlockwise", "drawBackground", "background", "drawOneBackground", "drawBackgroundImage", "<PERSON><PERSON><PERSON><PERSON>", "img_width", "img_height", "renderBG", "aspect_ratio", "orgBox", "size", "rewX", "rewY", "repeatX", "listStyleType", "listStylePosition", "_drawBullet", "bullet", "textAlign", "elementIndex", "k", "innerHTML", "idx", "isLast", "shouldDrawLeft", "shouldDrawRight", "inv", "gradient", "atan2", "pxlen", "abs", "scaledAngle", "atan", "tan", "implicit", "start", "end", "fromRect", "userSpace", "console", "log", "visual", "attr", "widget", "widgetInstance", "exportDOMVisual", "exportVisual", "wrap", "translate", "bulletType", "rectSize", "radius", "center", "circle", "renderImage", "image", "zIndexSort", "b", "sa", "sb", "za", "zb", "pa", "getSelectedOption", "selectedOptions", "selectedIndex", "renderCheckbox", "renderFormField", "option", "whiteSpace", "multiple", "scrollLeft", "scrollTop", "serializeSVG", "serializer", "XMLSerializer", "xml", "serializeToString", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "svg", "documentElement", "dataURL", "toDataURL", "floats", "positioned", "floating", "text", "search", "fontSize", "lineHeight", "align", "isJustified", "columnCount", "textTransform", "estimateLineLength", "prevLineBottom", "underline", "lineThrough", "overline", "underlineOffset", "forEachRect", "decorateUnder", "doChunk", "decorateOver", "selectNode", "clientRects", "actuallyGetRangeBoundingRect", "chrome", "safari", "rectangles", "Infinity", "origStart", "setStart", "found", "findEOL", "eol", "startOffset", "cc", "code", "charCodeAt", "indent", "l", "drawTextLine", "lineWidth", "textBox", "ypos", "underlinePos", "groupInStackingContext", "zIndex", "main", "_dom_zIndex", "insert", "invert", "transformCopy", "opacity", "visibility", "mmul", "multiplyCopy", "a1", "b1", "c1", "d1", "e1", "f1", "a2", "b2", "c2", "d2", "e2", "f2"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/html/core.js"], "sourcesContent": ["/* eslint-disable no-multi-spaces, key-spacing, indent, camelcase, space-before-blocks, eqeqeq, brace-style */\n/* eslint-disable space-infix-ops, space-before-function-paren, array-bracket-spacing, object-curly-spacing */\n/* eslint-disable no-nested-ternary, max-params, default-case, no-else-return, no-empty, yoda */\n/* eslint-disable no-param-reassign, no-var, block-scoped-var */\n\nimport * as geo from \"../geometry\";\nimport * as PDF from \"../pdf\";\nimport { arabicToRoman, createPromise, measureText, mergeSort } from '../util';\nimport { parseColor as utils_parseColor, support, template as compileTemplate } from \"../common\";\nimport { Path, Text, Group, Image, Circle, Rect, LinearGradient } from \"../drawing\";\nimport { encodeBase64 } from \"../util\";\nimport { setInnerHTML, setStyle } from \"../util/element-set-styles-safe\";\n\nvar browser = support.browser || {};\n/*\n\n  XXX: to test:\n\n  - cloneNodes function:\n    - drawing document containing canvas with page breaking\n    - drawing document with named radio <input>-s (should not clear selection)\n    - IE9/IE10 don't support el.dataset; do they copy user data?\n\n  - repeating table headers/footers on page breaking\n\n  - forceBreak, keepTogether\n\n  - avoidLinks\n\n */\n\n/* -----[ local vars ]----- */\n\nfunction slice(thing) {\n    return Array.prototype.slice.call(thing);\n}\n\nvar KENDO_PSEUDO_ELEMENT = \"KENDO-PSEUDO-ELEMENT\";\nvar KENDO_BULLET_TYPE = 'data-kendo-bullet-type';\n\nvar IMAGE_CACHE = {};\n\nvar nodeInfo = {};\nnodeInfo._root = nodeInfo;\n\n/* -----[ Custom Text node to speed up rendering in PDF ]----- */\n\nvar inBrowser = typeof window !== 'undefined';\nvar microsoft = inBrowser ? browser.msie || browser.edge : false;\n\nvar TextRect = (function (Text) {\n  function TextRect(str, rect, options) {\n        Text.call(this, str, rect.getOrigin(), options);\n        this._pdfRect = rect;\n    }\n\n  if ( Text ) TextRect.__proto__ = Text;\n  TextRect.prototype = Object.create( Text && Text.prototype );\n  TextRect.prototype.constructor = TextRect;\n    TextRect.prototype.rect = function rect () {\n        // this is the crux of it: we can avoid a call to\n        // measure(), which is what the base class does, since we\n        // already know the rect.  measure() is s-l-o-w.\n        return this._pdfRect;\n    };\n    TextRect.prototype.rawBBox = function rawBBox () {\n        // also let's avoid creating a new rectangle.\n        return this._pdfRect;\n    };\n\n  return TextRect;\n}(Text));\n\nfunction addClass(el, cls) {\n    if (el.classList) {\n        el.classList.add(cls);\n    } else {\n        el.className += \" \" + cls;\n    }\n}\n\nfunction removeClass(el, cls) {\n    if (el.classList) {\n        el.classList.remove(cls);\n    } else {\n        el.className = el.className.split(/\\s+/).reduce(function(a, word){\n            if (word != cls) {\n                a.push(word);\n            }\n            return a;\n        }, []).join(\" \");\n    }\n}\n\nfunction setCSS(el, styles) {\n    Object.keys(styles).forEach(function(key){\n        el.style[key] = styles[key];\n    });\n}\n\nvar matches = typeof Element !== \"undefined\" && Element.prototype && (function(p){\n    if (p.matches) {\n        return function(el, selector) { return el.matches(selector); };\n    }\n    if (p.webkitMatchesSelector) {\n        return function(el, selector) { return el.webkitMatchesSelector(selector); };\n    }\n    if (p.mozMatchesSelector) {\n        return function(el, selector) { return el.mozMatchesSelector(selector); };\n    }\n    if (p.msMatchesSelector) {\n        return function(el, selector) { return el.msMatchesSelector(selector); };\n    }\n    return function(s) {\n\treturn [].indexOf.call(document.querySelectorAll(s), this) !== -1;\n    };\n})(Element.prototype);\n\nfunction closest(el, selector) {\n    if (el.closest) {\n        return el.closest(selector);\n    }\n    // IE: stringifying rather than simply comparing with `document`,\n    // which is not iframe-proof and fails in editor export —\n    // https://github.com/telerik/kendo/issues/6721\n    while (el && !/^\\[object (?:HTML)?Document\\]$/.test(String(el))) {\n        if (el.nodeType == 1 /* Element */ && matches(el, selector)) {\n            return el;\n        }\n        el = el.parentNode;\n    }\n}\n\n// clone nodes ourselves, so that we redraw <canvas> (DOM or\n// jQuery clone will not)\nvar cloneNodes = (function($){\n    if ($) {\n        // if we have Kendo and jQuery, use this version as it will\n        // maintain proper links between cloned element and Kendo\n        // widgets (i.e. it clones jQuery data(), which isn't the same\n        // as element's data attributes).\n        // https://github.com/telerik/kendo-ui-core/issues/2750\n        return function cloneNodes(el) {\n            var clone = el.cloneNode(false);\n            if (el.nodeType == 1 /* Element */) {\n                var $el = $(el), $clone = $(clone), i;\n                var data = $el.data();\n                for (i in data) {\n                    $clone.data(i, data[i]);\n                }\n                if (/^canvas$/i.test(el.tagName)) {\n                    clone.getContext(\"2d\").drawImage(el, 0, 0);\n                } else if (/^(?:input|select|textarea|option)$/i.test(el.tagName)) {\n                    // drop the name attributes so that we don't affect the selection of the\n                    // original nodes (i.e. checked status of radio buttons) when we insert our copy\n                    // into the DOM.  https://github.com/telerik/kendo/issues/5409\n                    clone.removeAttribute(\"id\");\n                    clone.removeAttribute(\"name\");\n                    if (!/^textarea$/i.test(el.tagName)) {\n                        clone.value = el.value;\n                    }\n                    clone.checked = el.checked;\n                    clone.selected = el.selected;\n                }\n\n                if (el._kendoExportVisual) {\n                    clone._kendoExportVisual = el._kendoExportVisual;\n                }\n\n                for (i = el.firstChild; i; i = i.nextSibling) {\n                    clone.appendChild(cloneNodes(i));\n                }\n            }\n            return clone;\n        };\n    } else {\n        // the no-jQuery version\n        return function cloneNodes(el) {\n            var clone = (function dive(node){\n                var clone = node.cloneNode(false);\n                if (node._kendoExportVisual) {\n                    clone._kendoExportVisual = node._kendoExportVisual;\n                }\n                for (var i = node.firstChild; i; i = i.nextSibling) {\n                    clone.appendChild(dive(i));\n                }\n                return clone;\n            })(el);\n\n            // re-draw canvases - https://github.com/telerik/kendo/issues/4872\n            var canvases = el.querySelectorAll(\"canvas\");\n            if (canvases.length) {\n                slice(clone.querySelectorAll(\"canvas\")).forEach(function (canvas, i) {\n                    canvas.getContext(\"2d\").drawImage(canvases[i], 0, 0);\n                });\n            }\n\n            // remove \"name\" attributes from <input> elements -\n            // https://github.com/telerik/kendo/issues/5409\n            var orig = el.querySelectorAll(\"input, select, textarea, option\");\n            slice(clone.querySelectorAll(\"input, select, textarea, option\")).forEach(function (el, i) {\n                el.removeAttribute(\"id\");\n                el.removeAttribute(\"name\");\n                if (!/^textarea$/i.test(el.tagName)) {\n                    el.value = orig[i].value;\n                }\n                el.checked = orig[i].checked;\n                el.selected = orig[i].selected;\n            });\n\n            return clone;\n        };\n    }\n})(typeof window !== \"undefined\" && window.kendo && window.kendo.jQuery);\n\nfunction getXY(thing) {\n    if (typeof thing == \"number\") {\n        return { x: thing, y: thing };\n    }\n    if (Array.isArray(thing)) {\n        return { x: thing[0], y: thing[1] };\n    }\n    return { x: thing.x, y: thing.y };\n}\n\nfunction drawDOM(element, options) {\n    if (!options) {\n        options = {};\n    }\n    var promise = createPromise();\n\n    if (!element) {\n        return promise.reject(\"No element to export\");\n    }\n\n    if (typeof window.getComputedStyle != \"function\") {\n        throw new Error(\"window.getComputedStyle is missing.  You are using an unsupported browser, or running in IE8 compatibility mode.  Drawing HTML is supported in Chrome, Firefox, Safari and IE9+.\");\n    }\n\n    PDF.defineFont(getFontFaces(element.ownerDocument));\n\n    var scale = getXY(options.scale || 1);\n\n    function doOne(element) {\n        var group = new Group();\n\n        // translate to start of page\n        var pos = element.getBoundingClientRect();\n        setTransform(group, [\n            scale.x,\n            0,\n            0,\n            scale.y,\n            (-pos.left * scale.x),\n            (-pos.top * scale.y)\n        ]);\n\n        nodeInfo._clipbox = false;\n        nodeInfo._matrix = geo.Matrix.unit();\n        nodeInfo._stackingContext = {\n            element: element,\n            group: group\n        };\n\n        if (options.avoidLinks === true) {\n            nodeInfo._avoidLinks = \"a\";\n        } else {\n            nodeInfo._avoidLinks = options.avoidLinks;\n        }\n\n        addClass(element, \"k-pdf-export\");\n        renderElement(element, group);\n        removeClass(element, \"k-pdf-export\");\n\n        return group;\n    }\n\n    cacheImages([ element ], function(){\n        var forceBreak = options && options.forcePageBreak;\n        var hasPaperSize = options && options.paperSize && options.paperSize != \"auto\";\n        var paperOptions = PDF.getPaperOptions(function(key, def){\n            if (key == \"paperSize\") {\n                // PDF.getPaperOptions croaks on \"auto\", just pass dummy A4 as we might\n                // still be interested in margins.\n                return hasPaperSize ? options[key] : \"A4\";\n            }\n            return key in options ? options[key] : def;\n        });\n        var pageWidth = hasPaperSize && paperOptions.paperSize[0];\n        var pageHeight = hasPaperSize && paperOptions.paperSize[1];\n        var margin = options.margin && paperOptions.margin;\n        var hasMargin = Boolean(margin);\n        if (forceBreak || pageHeight) {\n            if (!margin) {\n                margin = { left: 0, top: 0, right: 0, bottom: 0 };\n            }\n\n            // we want paper size and margin to be unaffected by\n            // scaling in the output, so we have to reverse-scale\n            // before our calculations begin.\n            if (pageWidth)  { pageWidth  /= scale.x; }\n            if (pageHeight) { pageHeight /= scale.y; }\n            margin.left   /= scale.x;\n            margin.right  /= scale.x;\n            margin.top    /= scale.y;\n            margin.bottom /= scale.y;\n\n            var group = new Group({\n                pdf: {\n                    multiPage     : true,\n                    paperSize     : hasPaperSize ? paperOptions.paperSize : \"auto\",\n                    _ignoreMargin : hasMargin // HACK!  see exportPDF in pdf/drawing.js\n                }\n            });\n            handlePageBreaks(\n                function(x) {\n                    if (options.progress) {\n                        var canceled = false, pageNum = 0;\n                        (function next(){\n                            if (pageNum < x.pages.length) {\n                                var page = doOne(x.pages[pageNum]);\n                                group.append(page);\n                                options.progress({\n                                    page: page,\n                                    pageNum: ++pageNum,\n                                    totalPages: x.pages.length,\n                                    cancel: function() {\n                                        canceled = true;\n                                    }\n                                });\n                                if (!canceled) {\n                                    setTimeout(next);\n                                } else {\n                                    // XXX: should we also fail() the deferred object?\n                                    x.container.parentNode.removeChild(x.container);\n                                }\n                            } else {\n                                x.container.parentNode.removeChild(x.container);\n                                promise.resolve(group);\n                            }\n                        })();\n                    } else {\n                        x.pages.forEach(function(page){\n                            group.append(doOne(page));\n                        });\n                        x.container.parentNode.removeChild(x.container);\n                        promise.resolve(group);\n                    }\n                },\n                element,\n                forceBreak,\n                pageWidth ? pageWidth - margin.left - margin.right : null,\n                pageHeight ? pageHeight - margin.top - margin.bottom : null,\n                margin,\n                options\n            );\n        } else {\n            promise.resolve(doOne(element));\n        }\n    });\n\n    function makeTemplate(template) {\n        if (template != null) {\n            if (typeof template == \"string\") {\n                template = compileTemplate(template.replace(/^\\s+|\\s+$/g, \"\"));\n            }\n            if (typeof template == \"function\") {\n                return function(data) {\n                    var el = template(data);\n                    if (el && typeof el == \"string\") {\n                        var div = document.createElement(\"div\");\n                        setInnerHTML(div, el);\n                        el = div.firstElementChild;\n                    }\n                    return el;\n                };\n            }\n            // assumed DOM element\n            return function() {\n                return template.cloneNode(true);\n            };\n        }\n    }\n\n    function handlePageBreaks(callback, element, forceBreak, pageWidth, pageHeight, margin, options) {\n        var template = makeTemplate(options.template);\n        var doc = element.ownerDocument;\n        var pages = [];\n        var copy = options._destructive ? element : cloneNodes(element);\n        var container = doc.createElement(\"KENDO-PDF-DOCUMENT\");\n        var adjust = 0;\n\n        // make sure <tfoot> elements are at the end (Grid widget\n        // places TFOOT before TBODY, tricking our algorithm to\n        // insert a page break right after the header).\n        // https://github.com/telerik/kendo/issues/4699\n        slice(copy.querySelectorAll(\"tfoot\")).forEach(function(tfoot){\n            tfoot.parentNode.appendChild(tfoot);\n        });\n\n        // remember the index of each LI from an ordered list.\n        // we'll use it to reconstruct the proper numbering.\n        slice(copy.querySelectorAll(\"ol\")).forEach(function(ol){\n            slice(ol.children).forEach(function(li, index){\n                li.setAttribute(\"kendo-split-index\", index);\n            });\n        });\n\n        setCSS(container, {\n            display   : \"block\",\n            position  : \"absolute\",\n            boxSizing : \"content-box\",\n            left      : \"-10000px\",\n            top       : \"-10000px\"\n        });\n\n        if (pageWidth) {\n            // subtle: if we don't set the width *and* margins here, the layout in this\n            // container will be different from the one in our final page elements, and we'll\n            // split at the wrong places.\n            setCSS(container, {\n                width        : pageWidth + \"px\",\n                paddingLeft  : margin.left + \"px\",\n                paddingRight : margin.right + \"px\"\n            });\n\n            // when the first element has a margin-top (i.e. a <h1>) the page will be\n            // inadvertently enlarged by that number (the browser will report the container's\n            // bounding box top to start at the element's top, rather than including its\n            // margin).  Adding overflow: hidden seems to fix it.\n            //\n            // to understand the difference, try the following snippets in your browser:\n            //\n            // 1. <div style=\"background: yellow\">\n            //      <h1 style=\"margin: 3em\">Foo</h1>\n            //    </div>\n            //\n            // 2. <div style=\"background: yellow; overflow: hidden\">\n            //      <h1 style=\"margin: 3em\">Foo</h1>\n            //    </div>\n            //\n            // this detail is not important when automatic page breaking is not requested, hence\n            // doing it only if pageWidth is defined.\n            setCSS(copy, { overflow: \"hidden\" });\n        }\n\n        element.parentNode.insertBefore(container, element);\n        container.appendChild(copy);\n\n        // With cache disabled, images will still have height zero until their `complete` attribute\n        // is true.  `whenImagesAreActuallyLoaded` will wait for it.\n        if (options.beforePageBreak) {\n            whenImagesAreActuallyLoaded([ container ], function() {\n                options.beforePageBreak(container, doPageBreak);\n            });\n        } else {\n            whenImagesAreActuallyLoaded([ container ], doPageBreak);\n        }\n\n        function doPageBreak() {\n            if (forceBreak != \"-\" || pageHeight) {\n                splitElement(copy);\n            }\n\n            {\n                var page = makePage();\n                copy.parentNode.insertBefore(page, copy);\n                page.appendChild(copy);\n            }\n\n            if (template) {\n                pages.forEach(function(page, i){\n                    var el = template({\n                        element    : page,\n                        pageNum    : i + 1,\n                        totalPages : pages.length\n                    });\n                    if (el) {\n                        page.appendChild(el);\n                    }\n                });\n            }\n\n            cacheImages(pages, callback.bind(null, { pages: pages, container: container }));\n        }\n\n        function keepTogether(el) {\n            if (options.keepTogether && matches(el, options.keepTogether) && el.offsetHeight <= pageHeight - adjust) {\n                return true;\n            }\n\n            var tag = el.tagName;\n            if (/^h[1-6]$/i.test(tag) && el.offsetHeight >= pageHeight - adjust) {\n                return false;\n            }\n\n            return (el.getAttribute(\"data-kendo-chart\") ||\n                    /^(?:img|tr|thead|th|tfoot|iframe|svg|object|canvas|input|textarea|select|video|h[1-6])/i.test(el.tagName));\n        }\n\n        function splitElement(element) {\n            if (element.tagName == \"TABLE\") {\n                setCSS(element, { tableLayout: \"fixed\" });\n            }\n            if (keepTogether(element)) {\n                return;\n            }\n            var style = getComputedStyle(element);\n            var bottomPadding = parseFloat(getPropertyValue(style, \"padding-bottom\"));\n            var bottomBorder = parseFloat(getPropertyValue(style, \"border-bottom-width\"));\n            var saveAdjust = adjust;\n            adjust += bottomPadding + bottomBorder;\n            var isFirst = true;\n            for (var el = element.firstChild; el; el = el.nextSibling) {\n                if (el.nodeType == 1 /* Element */) {\n                    isFirst = false;\n                    if (matches(el, forceBreak)) {\n                        breakAtElement(el);\n                        continue;\n                    }\n                    if (!pageHeight) {\n                        // we're in \"manual breaks mode\"\n                        splitElement(el);\n                        continue;\n                    }\n                    if (!/^(?:static|relative)$/.test(getPropertyValue(getComputedStyle(el), \"position\"))) {\n                        continue;\n                    }\n                    var fall = fallsOnMargin(el);\n                    if (fall == 1) {\n                        // element starts on next page, break before anyway.\n                        breakAtElement(el);\n                    }\n                    else if (fall) {\n                        // elements ends up on next page, or possibly doesn't fit on a page at\n                        // all.  break before it anyway if it's an <img> or <tr>, otherwise\n                        // attempt to split.\n                        if (keepTogether(el)) {\n                            breakAtElement(el);\n                        } else {\n                            splitElement(el);\n                        }\n                    }\n                    else {\n                        splitElement(el);\n                    }\n                }\n                else if (el.nodeType == 3 /* Text */ && pageHeight) {\n                    splitText(el, isFirst);\n                    isFirst = false;\n                }\n            }\n            adjust = saveAdjust;\n        }\n\n        function firstInParent(el) {\n            var p = el.parentNode, first = p.firstChild;\n            if (el === first) {\n                return true;\n            }\n            if (el === p.children[0]) {\n                if (first.nodeType == 7 /* comment */ ||\n                    first.nodeType == 8 /* processing instruction */) {\n                    return true;\n                }\n                if (first.nodeType == 3 /* text */) {\n                    // if whitespace only we can probably consider it's first\n                    return !/\\S/.test(first.data);\n                }\n            }\n            return false;\n        }\n\n        function breakAtElement(el) {\n            if (el.nodeType == 1 && el !== copy && firstInParent(el)) {\n                return breakAtElement(el.parentNode);\n            }\n            var table, colgroup, thead, grid, gridHead;\n            table = closest(el, \"table\");\n            colgroup = table && table.querySelector(\"colgroup\");\n            if (options.repeatHeaders) {\n                thead = table && table.querySelector(\"thead\");\n\n                // If we break page in a Kendo Grid, repeat its header.  This ugly hack is\n                // necessary because a scrollable grid will keep the header in a separate\n                // <table> element from its content.\n                //\n                // XXX: This is likely to break as soon as the widget HTML is modified.\n                grid = closest(el, \".k-grid\");\n                if (grid && grid.querySelector(\".k-auto-scrollable\")) {\n                    gridHead = grid.querySelector(\".k-grid-header\");\n                }\n            }\n            var page = makePage();\n            var range = doc.createRange();\n            range.setStartBefore(copy);\n            range.setEndBefore(el);\n            page.appendChild(range.extractContents());\n            copy.parentNode.insertBefore(page, copy);\n            preventBulletOnListItem(el.parentNode);\n            if (table) {\n                table = closest(el, \"table\"); // that's the <table> on next page!\n                if (options.repeatHeaders && thead) {\n                    table.insertBefore(thead.cloneNode(true), table.firstChild);\n                }\n                if (colgroup) {\n                    table.insertBefore(colgroup.cloneNode(true), table.firstChild);\n                }\n            }\n            if (options.repeatHeaders && gridHead) {\n                grid = closest(el, \".k-grid\");\n                grid.insertBefore(gridHead.cloneNode(true), grid.firstChild);\n            }\n        }\n\n        function makePage() {\n            var page = doc.createElement(\"KENDO-PDF-PAGE\");\n            setCSS(page, {\n                display  : \"block\",\n                boxSizing: \"content-box\",\n                width    : pageWidth ? (pageWidth + \"px\") : \"auto\",\n                padding  : (margin.top + \"px \" +\n                            margin.right + \"px \" +\n                            margin.bottom + \"px \" +\n                            margin.left + \"px\"),\n\n                // allow absolutely positioned elements to be relative to current page\n                position : \"relative\",\n\n                // without the following we might affect layout of subsequent pages\n                height   : pageHeight ? (pageHeight + \"px\") : \"auto\",\n                overflow : pageHeight || pageWidth ? \"hidden\" : \"visible\",\n                clear    : \"both\"\n            });\n\n            // debug\n            // $(\"<div>\").css({\n            //     position  : \"absolute\",\n            //     left      : margin.left,\n            //     top       : margin.top,\n            //     width     : pageWidth,\n            //     height    : pageHeight,\n            //     boxSizing : \"border-box\",\n            //     background: \"rgba(255, 255, 0, 0.5)\"\n            //     //border    : \"1px solid red\"\n            // }).appendTo(page);\n\n            if (options && options.pageClassName) {\n                page.className = options.pageClassName;\n            }\n            pages.push(page);\n            return page;\n        }\n\n        function fallsOnMargin(thing) {\n            var box = thing.getBoundingClientRect();\n            if (box.width === 0 || box.height === 0) {\n                // I'd say an element with dimensions zero fits on current page.\n                return 0;\n            }\n            var top = copy.getBoundingClientRect().top;\n            var available = pageHeight - adjust;\n            return (box.height > available) ? 3\n                : (box.top - top > available) ? 1\n                : (box.bottom - top > available) ? 2\n                : 0;\n        }\n\n        function splitText(node, isFirst) {\n            if (!/\\S/.test(node.data)) {\n                return;\n            }\n\n            var len = node.data.length;\n            var range = doc.createRange();\n            range.selectNodeContents(node);\n            var fall = fallsOnMargin(range);\n            if (!fall) {\n                return;     // the whole text fits on current page\n            }\n\n            var nextnode = node;\n            if (fall == 1) {\n                // starts on next page, break before anyway.\n                if (isFirst) {\n                    // avoid leaving an empty <p>, <li>, etc. on previous page.\n                    breakAtElement(node.parentNode);\n                } else {\n                    breakAtElement(node);\n                }\n            }\n            else {\n                (function findEOP(min, pos, max) {\n                    range.setEnd(node, pos);\n                    if (min == pos || pos == max) {\n                        return pos;\n                    }\n                    if (fallsOnMargin(range)) {\n                        return findEOP(min, (min + pos) >> 1, pos);\n                    } else {\n                        return findEOP(pos, (pos + max) >> 1, max);\n                    }\n                })(0, len >> 1, len);\n\n                if (!/\\S/.test(range.toString()) && isFirst) {\n                    // avoid leaving an empty <p>, <li>, etc. on previous page.\n                    breakAtElement(node.parentNode);\n                } else {\n                    // This is only needed for IE, but it feels cleaner to do it anyway.  Without\n                    // it, IE will truncate a very long text (playground/pdf-long-text-2.html).\n                    nextnode = node.splitText(range.endOffset);\n\n                    var page = makePage();\n                    range.setStartBefore(copy);\n                    page.appendChild(range.extractContents());\n                    copy.parentNode.insertBefore(page, copy);\n                    preventBulletOnListItem(nextnode.parentNode);\n                }\n            }\n\n            splitText(nextnode);\n        }\n\n        function preventBulletOnListItem(el) {\n            // set a hint on continued LI elements, to tell the\n            // renderer not to draw the bullet again.\n            // https://github.com/telerik/kendo-ui-core/issues/2732\n            var li = closest(el, \"li\");\n            if (li) {\n                li.setAttribute(\"kendo-no-bullet\", \"1\");\n                preventBulletOnListItem(li.parentNode);\n            }\n        }\n    }\n\n    return promise;\n}\n\n// This is needed for the Spreadsheet print functionality.  Since\n// there we only need to draw text, this cuts through the ceremony\n// of drawDOM/renderElement and renders the text node directly.\nfunction drawText(element) {\n    var group = new Group();\n    nodeInfo._clipbox = false;\n    nodeInfo._matrix = geo.Matrix.unit();\n    nodeInfo._stackingContext = {\n        element: element,\n        group: group\n    };\n    pushNodeInfo(element, getComputedStyle(element), group);\n    if (element.firstChild.nodeType == 3 /* Text */) {\n        // avoid the penalty of renderElement\n        renderText(element, element.firstChild, group);\n    } else {\n        _renderElement(element, group);\n    }\n    popNodeInfo();\n    return group;\n}\n\nvar parseBackgroundImage = (function(){\n    var tok_linear_gradient  = /^((-webkit-|-moz-|-o-|-ms-)?linear-gradient\\s*)\\(/;\n    //var tok_radial_gradient  = /^((-webkit-|-moz-|-o-|-ms-)?radial-gradient\\s*)\\(/;\n    var tok_percent          = /^([-0-9.]+%)/;\n    var tok_length           = /^([-0-9.]+px)/;\n    var tok_keyword          = /^(left|right|top|bottom|to|center)\\W/;\n    var tok_angle            = /^([-0-9.]+(deg|grad|rad|turn)|0)/;\n    var tok_whitespace       = /^(\\s+)/;\n    var tok_popen            = /^(\\()/;\n    var tok_pclose           = /^(\\))/;\n    var tok_comma            = /^(,)/;\n    var tok_url              = /^(url)\\(/;\n    var tok_content          = /^(.*?)\\)/;\n\n    var cache1 = {}, cache2 = {};\n\n    function parse(input) {\n        var orig = input;\n        if (hasOwnProperty(cache1, orig)) {\n            return cache1[orig];\n        }\n        function skip_ws() {\n            var m = tok_whitespace.exec(input);\n            if (m) {\n                input = input.substr(m[1].length);\n            }\n        }\n        function read(token) {\n            skip_ws();\n            var m = token.exec(input);\n            if (m) {\n                input = input.substr(m[1].length);\n                return m[1];\n            }\n        }\n\n        function read_stop() {\n            var color = utils_parseColor(input, true);\n            var length, percent;\n            if (color) {\n                var match =\n                    /^#[0-9a-f]+/i.exec(input) ||\n                    /^rgba?\\(.*?\\)/i.exec(input) ||\n                    /^..*?\\b/.exec(input); // maybe named color\n                input = input.substr(match[0].length);\n                color = color.toRGB();\n                if (!(length = read(tok_length))) {\n                    percent = read(tok_percent);\n                }\n                return { color: color, length: length, percent: percent };\n            }\n        }\n\n        function read_linear_gradient(propName) {\n            var angle;\n            var to1, to2;\n            var stops = [];\n            var reverse = false;\n\n            if (read(tok_popen)) {\n                // 1. [ <angle> || to <side-or-corner>, ]?\n                angle = read(tok_angle);\n                if (angle == \"0\") {\n                    angle = \"0deg\"; // Edge\n                }\n                if (angle) {\n                    angle = parseAngle(angle);\n                    read(tok_comma);\n                }\n                else {\n                    to1 = read(tok_keyword);\n                    if (to1 == \"to\") {\n                        to1 = read(tok_keyword);\n                    } else if (to1 && /^-/.test(propName)) {\n                        reverse = true;\n                    }\n                    to2 = read(tok_keyword);\n                    read(tok_comma);\n                }\n\n                if (/-moz-/.test(propName) && angle == null && to1 == null) {\n                    var x = read(tok_percent), y = read(tok_percent);\n                    reverse = true;\n                    if (x == \"0%\") {\n                        to1 = \"left\";\n                    } else if (x == \"100%\") {\n                        to1 = \"right\";\n                    }\n                    if (y == \"0%\") {\n                        to2 = \"top\";\n                    } else if (y == \"100%\") {\n                        to2 = \"bottom\";\n                    }\n                    read(tok_comma);\n                }\n\n                // 2. color stops\n                while (input && !read(tok_pclose)) {\n                    var stop = read_stop();\n                    if (!stop) {\n                        break;\n                    }\n                    stops.push(stop);\n                    read(tok_comma);\n                }\n\n                return {\n                    type    : \"linear\",\n                    angle   : angle,\n                    to      : to1 && to2 ? to1 + \" \" + to2 : to1 ? to1 : to2 ? to2 : null,\n                    stops   : stops,\n                    reverse : reverse\n                };\n            }\n        }\n\n        function read_url() {\n            if (read(tok_popen)) {\n                var url = read(tok_content);\n                url = url.replace(/^['\"]+|[\"']+$/g, \"\");\n                read(tok_pclose);\n                return { type: \"url\", url: url };\n            }\n        }\n\n        var tok;\n\n        if ((tok = read(tok_linear_gradient))) {\n            tok = read_linear_gradient(tok);\n        }\n        else if ((tok = read(tok_url))) {\n            tok = read_url();\n        }\n\n        return (cache1[orig] = tok || { type: \"none\" });\n    }\n\n    return function(input) {\n        if (hasOwnProperty(cache2, input)) {\n            return cache2[input];\n        }\n        return (cache2[input] = splitProperty(input).map(parse));\n    };\n})();\n\nvar splitProperty = (function(){\n    var cache = {};\n    return function(input, separator) {\n        if (!separator) {\n            separator = /^\\s*,\\s*/;\n        }\n\n        var cacheKey = input + separator;\n\n        if (hasOwnProperty(cache, cacheKey)) {\n            return cache[cacheKey];\n        }\n\n        var ret = [];\n        var last = 0, pos = 0;\n        var in_paren = 0;\n        var in_string = false;\n        var m;\n\n        function looking_at(rx) {\n            return (m = rx.exec(input.substr(pos)));\n        }\n\n        function trim(str) {\n            return str.replace(/^\\s+|\\s+$/g, \"\");\n        }\n\n        while (pos < input.length) {\n            if (!in_string && looking_at(/^[\\(\\[\\{]/)) {\n                in_paren++;\n                pos++;\n            }\n            else if (!in_string && looking_at(/^[\\)\\]\\}]/)) {\n                in_paren--;\n                pos++;\n            }\n            else if (!in_string && looking_at(/^[\\\"\\']/)) {\n                in_string = m[0];\n                pos++;\n            }\n            else if (in_string == \"'\" && looking_at(/^\\\\\\'/)) {\n                pos += 2;\n            }\n            else if (in_string == '\"' && looking_at(/^\\\\\\\"/)) {\n                pos += 2;\n            }\n            else if (in_string == \"'\" && looking_at(/^\\'/)) {\n                in_string = false;\n                pos++;\n            }\n            else if (in_string == '\"' && looking_at(/^\\\"/)) {\n                in_string = false;\n                pos++;\n            }\n            else if (looking_at(separator)) {\n                if (!in_string && !in_paren && pos > last) {\n                    ret.push(trim(input.substring(last, pos)));\n                    last = pos + m[0].length;\n                }\n                pos += m[0].length;\n            }\n            else {\n                pos++;\n            }\n        }\n        if (last < pos) {\n            ret.push(trim(input.substring(last, pos)));\n        }\n        return (cache[cacheKey] = ret);\n    };\n})();\n\nvar getFontURL = (function(cache){\n    return function(el){\n        // XXX: for IE we get here the whole cssText of the rule,\n        // because the computedStyle.src is empty.  Next time we need\n        // to fix these regexps we better write a CSS parser. :-\\\n        var url = cache[el];\n        if (!url) {\n            var m;\n            if ((m = /url\\((['\"]?)([^'\")]*?)\\1\\)\\s+format\\((['\"]?)truetype\\3\\)/.exec(el))) {\n                url = cache[el] = m[2];\n            } else if ((m = /url\\((['\"]?)([^'\")]*?\\.ttf)\\1\\)/.exec(el))) {\n                url = cache[el] = m[2];\n            }\n        }\n        return url;\n    };\n})(Object.create(null));\n\nvar getFontHeight = (function(cache){\n    return function(font) {\n        var height = cache[font];\n        if (height == null) {\n            height = cache[font] = measureText(\"Mapq\", { font: font }).height;\n        }\n        return height;\n    };\n})(Object.create(null));\n\nfunction getFontFaces(doc) {\n    if (doc == null) {\n        doc = document;\n    }\n    var result = {};\n    for (var i = 0; i < doc.styleSheets.length; ++i) {\n        doStylesheet(doc.styleSheets[i]);\n    }\n    return result;\n    function doStylesheet(ss) {\n        if (ss) {\n            var rules = null;\n            try {\n                rules = ss.cssRules;\n            } catch (ex) {}\n            if (rules) {\n                addRules(ss, rules);\n            }\n        }\n    }\n    function findFonts(rule) {\n        var src = getPropertyValue(rule.style, \"src\");\n        if (src) {\n            return splitProperty(src).reduce(function(a, el){\n                var font = getFontURL(el);\n                if (font) {\n                    a.push(font);\n                }\n                return a;\n            }, []);\n        } else {\n            // Internet Explorer\n            // XXX: this is gross.  should work though for valid CSS.\n            var font = getFontURL(rule.cssText);\n            return font ? [ font ] : [];\n        }\n    }\n    function addRules(styleSheet, rules) {\n        for (var i = 0; i < rules.length; ++i) {\n            var r = rules[i];\n            switch (r.type) {\n              case 3:       // CSSImportRule\n                doStylesheet(r.styleSheet);\n                break;\n              case 5:       // CSSFontFaceRule\n                var style  = r.style;\n                var family = splitProperty(getPropertyValue(style, \"font-family\"));\n                var bold   = /^([56789]00|bold)$/i.test(getPropertyValue(style, \"font-weight\"));\n                var italic = \"italic\" == getPropertyValue(style, \"font-style\");\n                var src    = findFonts(r);\n                if (src.length > 0) {\n                    addRule(styleSheet, family, bold, italic, src[0]);\n                }\n            }\n        }\n    }\n    function addRule(styleSheet, names, bold, italic, url) {\n        // We get full resolved absolute URLs in Chrome, but sadly\n        // not in Firefox.\n        if (!(/^data:/i.test(url))) {\n            if (!(/^[^\\/:]+:\\/\\//.test(url) || /^\\//.test(url))) {\n                url = String(styleSheet.href).replace(/[^\\/]*$/, \"\") + url;\n            }\n        }\n        names.forEach(function(name){\n            name = name.replace(/^(['\"]?)(.*?)\\1$/, \"$2\"); // it's quoted\n            if (bold) {\n                name += \"|bold\";\n            }\n            if (italic) {\n                name += \"|italic\";\n            }\n            result[name] = url;\n        });\n    }\n}\n\nfunction hasOwnProperty(obj, key) {\n    return Object.prototype.hasOwnProperty.call(obj, key);\n}\n\nfunction getCounter(name) {\n    name = \"_counter_\" + name;\n    return nodeInfo[name];\n}\n\nfunction getAllCounters(name) {\n    var values = [], p = nodeInfo;\n    name = \"_counter_\" + name;\n    while (p) {\n        if (hasOwnProperty(p, name)) {\n            values.push(p[name]);\n        }\n        p = Object.getPrototypeOf(p);\n    }\n    return values.reverse();\n}\n\nfunction incCounter(name, inc) {\n    var p = nodeInfo;\n    name = \"_counter_\" + name;\n    while (p && !hasOwnProperty(p, name)) {\n        p = Object.getPrototypeOf(p);\n    }\n    if (!p) {\n        p = nodeInfo._root;\n    }\n    p[name] = (p[name] || 0) + (inc == null ? 1 : inc);\n}\n\nfunction resetCounter(name, val) {\n    name = \"_counter_\" + name;\n    nodeInfo[name] = val == null ? 0 : val;\n}\n\nfunction doCounters(a, f, def) {\n    for (var i = 0; i < a.length;) {\n        var name = a[i++];\n        var val = parseFloat(a[i]);\n        if (isNaN(val)) {\n            f(name, def);\n        } else {\n            f(name, val);\n            ++i;\n        }\n    }\n}\n\nfunction updateCounters(style) {\n    var counterReset = getPropertyValue(style, \"counter-reset\");\n    if (counterReset) {\n        doCounters(splitProperty(counterReset, /^\\s+/), resetCounter, 0);\n    }\n    var counterIncrement = getPropertyValue(style, \"counter-increment\");\n    if (counterIncrement) {\n        doCounters(splitProperty(counterIncrement, /^\\s+/), incCounter, 1);\n    }\n}\n\nfunction parseColor(str, css) {\n    var color = utils_parseColor(str, true);\n    if (color) {\n        color = color.toRGB();\n        if (css) {\n            color = color.toCssRgba();\n        } else if (color.a === 0) {\n            color = null;\n        }\n    }\n    return color;\n}\n\nfunction whenImagesAreActuallyLoaded(elements, callback) {\n    var pending = 0;\n    var done = false;\n    elements.forEach(function(el){\n        var images = el.querySelectorAll(\"img\");\n        for (var i = 0; i < images.length; ++i) {\n            var img = images[i];\n            if (!img.complete) {\n                pending++;\n                img.onload = img.onerror = next;\n            }\n        }\n    });\n\n    if (!pending) {\n        next();\n    }\n\n    function next() {\n        if (!done && --pending <= 0) {\n            callback();\n            done = true;\n        }\n    }\n}\n\nfunction cacheImages(elements, callback) {\n    var urls = [];\n    function add(url) {\n        if (!IMAGE_CACHE[url]) {\n            IMAGE_CACHE[url] = true;\n            urls.push(url);\n        }\n    }\n\n    elements.forEach(function dive(element){\n        if (/^img$/i.test(element.tagName)) {\n            add(element.src);\n        }\n        parseBackgroundImage(\n            getPropertyValue(\n                getComputedStyle(element), \"background-image\"\n            )\n        ).forEach(function(bg){\n            if (bg.type == \"url\") {\n                add(bg.url);\n            }\n        });\n\n        if (element.children) {\n            slice(element.children).forEach(dive);\n        }\n    });\n\n    var count = urls.length;\n    function next() {\n        if (--count <= 0) {\n            // Even though we cached them, they simply won't be available immediately in the newly\n            // created DOM.  Previously we'd allow a 10ms timeout, but that's arbitrary and clearly\n            // not working in all cases (https://github.com/telerik/kendo/issues/5399), so this\n            // function will wait for their .complete attribute.\n            whenImagesAreActuallyLoaded(elements, callback);\n        }\n    }\n    if (count === 0) {\n        next();\n    }\n    urls.forEach(function(url){\n        var img = IMAGE_CACHE[url] = new window.Image();\n        if (!(/^data:/i.test(url))) {\n            img.crossOrigin = \"Anonymous\";\n        }\n        img.src = url;\n        if (img.complete) {\n            next();\n        } else {\n            img.onload = next;\n            img.onerror = function() {\n                IMAGE_CACHE[url] = null;\n                next();\n            };\n        }\n    });\n}\n\nfunction alphaNumeral(n) {\n    var result = \"\";\n    do {\n        var r = n % 26;\n        result = String.fromCharCode(97 + r) + result;\n        n = Math.floor(n / 26);\n    } while (n > 0);\n    return result;\n}\n\nfunction pushNodeInfo(element, style, group) {\n    nodeInfo = Object.create(nodeInfo);\n    nodeInfo[element.tagName.toLowerCase()] = {\n        element: element,\n        style: style\n    };\n    var decoration = getPropertyValue(style, \"text-decoration\");\n    if (decoration && decoration != \"none\") {\n        var color = getPropertyValue(style, \"text-decoration-color\");\n        decoration.split(/\\s+/g).forEach(function(name){\n            if (!nodeInfo[name]) {\n                nodeInfo[name] = color;\n                if (name == \"underline\") {\n                    var offset = getPropertyValue(style, \"text-underline-offset\");\n                    if (offset != \"auto\") {\n                        nodeInfo[\"underline-offset\"] = parseFloat(offset);\n                    }\n                }\n            }\n        });\n    }\n\n    if (createsStackingContext(style)) {\n        nodeInfo._stackingContext = {\n            element: element,\n            group: group\n        };\n    }\n}\n\nfunction popNodeInfo() {\n    nodeInfo = Object.getPrototypeOf(nodeInfo);\n}\n\nfunction updateClipbox(path) {\n    if (nodeInfo._clipbox != null) {\n        var box = path.bbox(nodeInfo._matrix);\n        if (nodeInfo._clipbox) {\n            nodeInfo._clipbox = geo.Rect.intersect(nodeInfo._clipbox, box);\n        } else {\n            nodeInfo._clipbox = box;\n        }\n    }\n}\n\nfunction emptyClipbox() {\n    var cb = nodeInfo._clipbox;\n    if (cb == null) {\n        return true;\n    }\n    if (cb) {\n        return cb.width() === 0 || cb.height() === 0;\n    }\n}\n\nfunction createsStackingContext(style) {\n    function prop(name) { return getPropertyValue(style, name); }\n    if (prop(\"transform\") != \"none\" ||\n        prop(\"position\") != \"static\" ||\n        prop(\"z-index\") != \"auto\" ||\n        prop(\"opacity\") < 1) {\n        return true;\n    }\n}\n\nfunction getComputedStyle(element, pseudoElt) {\n    return window.getComputedStyle(element, pseudoElt || null);\n}\n\nfunction getPropertyValue(style, prop, defa) {\n    var val = style.getPropertyValue(prop);\n    if (val == null || val === \"\") {\n        if (browser.webkit) {\n            val = style.getPropertyValue(\"-webkit-\" + prop );\n        } else if (browser.mozilla) {\n            val = style.getPropertyValue(\"-moz-\" + prop );\n        } else if (browser.opera) {\n            val = style.getPropertyValue(\"-o-\" + prop);\n        } else if (microsoft) {\n            val = style.getPropertyValue(\"-ms-\" + prop);\n        }\n    }\n    if (arguments.length > 2 && (val == null || val === \"\")) {\n        return defa;\n    } else {\n        return val;\n    }\n}\n\nfunction pleaseSetPropertyValue(style, prop, value, important) {\n    style.setProperty(prop, value, important);\n    if (browser.webkit) {\n        style.setProperty(\"-webkit-\" + prop, value, important);\n    } else if (browser.mozilla) {\n        style.setProperty(\"-moz-\" + prop, value, important);\n    } else if (browser.opera) {\n        style.setProperty(\"-o-\" + prop, value, important);\n    } else if (microsoft) {\n        style.setProperty(\"-ms-\" + prop, value, important);\n        prop = \"ms\" + prop.replace(/(^|-)([a-z])/g, function(s, p1, p2){\n            return p1 + p2.toUpperCase();\n        });\n        style[prop] = value;\n    }\n}\n\nfunction getBorder(style, side) {\n    side = \"border-\" + side;\n    return {\n        width: parseFloat(getPropertyValue(style, side + \"-width\")),\n        style: getPropertyValue(style, side + \"-style\"),\n        color: parseColor(getPropertyValue(style, side + \"-color\"), true)\n    };\n}\n\nfunction saveStyle(element, func) {\n    var prev = element.style.cssText;\n    var result = func();\n    setStyle(element, prev);\n    return result;\n}\n\nfunction getBorderRadius(style, side) {\n    var r = getPropertyValue(style, \"border-\" + side + \"-radius\").split(/\\s+/g).map(parseFloat);\n    if (r.length == 1) {\n        r.push(r[0]);\n    }\n    return sanitizeRadius({ x: r[0], y: r[1] });\n}\n\nfunction getContentBox(element) {\n    var box = element.getBoundingClientRect();\n    box = innerBox(box, \"border-*-width\", element);\n    box = innerBox(box, \"padding-*\", element);\n    return box;\n}\n\nfunction innerBox(box, prop, element) {\n    var style, wt, wr, wb, wl;\n    if (typeof prop == \"string\") {\n        style = getComputedStyle(element);\n        wt = parseFloat(getPropertyValue(style, prop.replace(\"*\", \"top\")));\n        wr = parseFloat(getPropertyValue(style, prop.replace(\"*\", \"right\")));\n        wb = parseFloat(getPropertyValue(style, prop.replace(\"*\", \"bottom\")));\n        wl = parseFloat(getPropertyValue(style, prop.replace(\"*\", \"left\")));\n    }\n    else if (typeof prop == \"number\") {\n        wt = wr = wb = wl = prop;\n    }\n    return {\n        top    : box.top + wt,\n        right  : box.right - wr,\n        bottom : box.bottom - wb,\n        left   : box.left + wl,\n        width  : box.right - box.left - wr - wl,\n        height : box.bottom - box.top - wb - wt\n    };\n}\n\nfunction getTransform(style) {\n    var transform = getPropertyValue(style, \"transform\");\n    if (transform == \"none\") {\n        return null;\n    }\n    var matrix = /^\\s*matrix\\(\\s*(.*?)\\s*\\)\\s*$/.exec(transform);\n    if (matrix) {\n        var origin = getPropertyValue(style, \"transform-origin\");\n        matrix = matrix[1].split(/\\s*,\\s*/g).map(parseFloat);\n        origin = origin.split(/\\s+/g).map(parseFloat);\n        return {\n            matrix: matrix,\n            origin: origin\n        };\n    }\n}\n\nfunction radiansToDegrees(radians) {\n    return ((180 * radians) / Math.PI) % 360;\n}\n\nfunction parseAngle(angle) {\n    var num = parseFloat(angle);\n    if (/grad$/.test(angle)) {\n        return Math.PI * num / 200;\n    }\n    else if (/rad$/.test(angle)) {\n        return num;\n    }\n    else if (/turn$/.test(angle)) {\n        return Math.PI * num * 2;\n    }\n    else if (/deg$/.test(angle)) {\n        return Math.PI * num / 180;\n    }\n}\n\nfunction setTransform(shape, m) {\n    m = new geo.Matrix(m[0], m[1], m[2], m[3], m[4], m[5]);\n    shape.transform(m);\n    return m;\n}\n\nfunction setClipping(shape, clipPath) {\n    shape.clip(clipPath);\n}\n\nfunction addArcToPath(path, x, y, options) {\n    var points = new geo.Arc([ x, y ], options).curvePoints(), i = 1;\n    while (i < points.length) {\n        path.curveTo(points[i++], points[i++], points[i++]);\n    }\n}\n\nfunction sanitizeRadius(r) {\n    if (r.x <= 0 || r.y <= 0) {\n        r.x = r.y = 0;\n    }\n    return r;\n}\n\nfunction adjustBorderRadiusForBox(box, rTL, rTR, rBR, rBL) {\n    // adjust border radiuses such that the sum of adjacent\n    // radiuses is not bigger than the length of the side.\n    // seems the correct algorithm is variant (3) from here:\n    // http://www.w3.org/Style/CSS/Tracker/issues/29?changelog\n    var tl_x = Math.max(0, rTL.x), tl_y = Math.max(0, rTL.y);\n    var tr_x = Math.max(0, rTR.x), tr_y = Math.max(0, rTR.y);\n    var br_x = Math.max(0, rBR.x), br_y = Math.max(0, rBR.y);\n    var bl_x = Math.max(0, rBL.x), bl_y = Math.max(0, rBL.y);\n\n    var f = Math.min(\n        box.width / (tl_x + tr_x),\n        box.height / (tr_y + br_y),\n        box.width / (br_x + bl_x),\n        box.height / (bl_y + tl_y)\n    );\n\n    if (f < 1) {\n        tl_x *= f; tl_y *= f;\n        tr_x *= f; tr_y *= f;\n        br_x *= f; br_y *= f;\n        bl_x *= f; bl_y *= f;\n    }\n\n    return {\n        tl: { x: tl_x, y: tl_y },\n        tr: { x: tr_x, y: tr_y },\n        br: { x: br_x, y: br_y },\n        bl: { x: bl_x, y: bl_y }\n    };\n}\n\nfunction elementRoundBox(element, box, type) {\n    var style = getComputedStyle(element);\n\n    var rTL = getBorderRadius(style, \"top-left\");\n    var rTR = getBorderRadius(style, \"top-right\");\n    var rBL = getBorderRadius(style, \"bottom-left\");\n    var rBR = getBorderRadius(style, \"bottom-right\");\n\n    if (type == \"padding\" || type == \"content\") {\n        var bt = getBorder(style, \"top\");\n        var br = getBorder(style, \"right\");\n        var bb = getBorder(style, \"bottom\");\n        var bl = getBorder(style, \"left\");\n        rTL.x -= bl.width; rTL.y -= bt.width;\n        rTR.x -= br.width; rTR.y -= bt.width;\n        rBR.x -= br.width; rBR.y -= bb.width;\n        rBL.x -= bl.width; rBL.y -= bb.width;\n        if (type == \"content\") {\n            var pt = parseFloat(getPropertyValue(style, \"padding-top\"));\n            var pr = parseFloat(getPropertyValue(style, \"padding-right\"));\n            var pb = parseFloat(getPropertyValue(style, \"padding-bottom\"));\n            var pl = parseFloat(getPropertyValue(style, \"padding-left\"));\n            rTL.x -= pl; rTL.y -= pt;\n            rTR.x -= pr; rTR.y -= pt;\n            rBR.x -= pr; rBR.y -= pb;\n            rBL.x -= pl; rBL.y -= pb;\n        }\n    }\n\n    if (typeof type == \"number\") {\n        rTL.x -= type; rTL.y -= type;\n        rTR.x -= type; rTR.y -= type;\n        rBR.x -= type; rBR.y -= type;\n        rBL.x -= type; rBL.y -= type;\n    }\n\n    return roundBox(box, rTL, rTR, rBR, rBL);\n}\n\n// Create a drawing.Path for a rounded rectangle.  Receives the\n// bounding box and the border-radiuses in CSS order (top-left,\n// top-right, bottom-right, bottom-left).  The radiuses must be\n// objects containing x (horiz. radius) and y (vertical radius).\nfunction roundBox(box, rTL0, rTR0, rBR0, rBL0) {\n    var tmp = adjustBorderRadiusForBox(box, rTL0, rTR0, rBR0, rBL0);\n    var rTL = tmp.tl;\n    var rTR = tmp.tr;\n    var rBR = tmp.br;\n    var rBL = tmp.bl;\n    var path = new Path({ fill: null, stroke: null });\n    path.moveTo(box.left, box.top + rTL.y);\n    if (rTL.x) {\n        addArcToPath(path, box.left + rTL.x, box.top + rTL.y, {\n            startAngle: -180,\n            endAngle: -90,\n            radiusX: rTL.x,\n            radiusY: rTL.y\n        });\n    }\n    path.lineTo(box.right - rTR.x, box.top);\n    if (rTR.x) {\n        addArcToPath(path, box.right - rTR.x, box.top + rTR.y, {\n            startAngle: -90,\n            endAngle: 0,\n            radiusX: rTR.x,\n            radiusY: rTR.y\n        });\n    }\n    path.lineTo(box.right, box.bottom - rBR.y);\n    if (rBR.x) {\n        addArcToPath(path, box.right - rBR.x, box.bottom - rBR.y, {\n            startAngle: 0,\n            endAngle: 90,\n            radiusX: rBR.x,\n            radiusY: rBR.y\n        });\n    }\n    path.lineTo(box.left + rBL.x, box.bottom);\n    if (rBL.x) {\n        addArcToPath(path, box.left + rBL.x, box.bottom - rBL.y, {\n            startAngle: 90,\n            endAngle: 180,\n            radiusX: rBL.x,\n            radiusY: rBL.y\n        });\n    }\n    return path.close();\n}\n\nfunction formatCounter(val, style) {\n    var str = String(parseFloat(val));\n    switch (style) {\n      case \"decimal-leading-zero\":\n        if (str.length < 2) {\n            str = \"0\" + str;\n        }\n        return str;\n      case \"lower-roman\":\n        return arabicToRoman(val).toLowerCase();\n      case \"upper-roman\":\n        return arabicToRoman(val).toUpperCase();\n      case \"lower-latin\":\n      case \"lower-alpha\":\n        return alphaNumeral(val - 1);\n      case \"upper-latin\":\n      case \"upper-alpha\":\n        return alphaNumeral(val - 1).toUpperCase();\n      default:\n        return str;\n    }\n}\n\nfunction evalPseudoElementContent(element, content) {\n    function displayCounter(name, style, separator) {\n        if (!separator) {\n            return formatCounter(getCounter(name) || 0, style);\n        }\n        separator = separator.replace(/^\\s*([\"'])(.*)\\1\\s*$/, \"$2\");\n        return getAllCounters(name).map(function(val){\n            return formatCounter(val, style);\n        }).join(separator);\n    }\n    var a = splitProperty(content, /^\\s+/);\n    var result = [], m;\n    a.forEach(function(el){\n        var tmp;\n        if ((m = /^\\s*([\"'])(.*)\\1\\s*$/.exec(el))) {\n            result.push(m[2].replace(/\\\\([0-9a-f]{4})/gi, function(s, p){\n                return String.fromCharCode(parseInt(p, 16));\n            }));\n        }\n        else if ((m = /^\\s*counter\\((.*?)\\)\\s*$/.exec(el))) {\n            tmp = splitProperty(m[1]);\n            result.push(displayCounter(tmp[0], tmp[1]));\n        }\n        else if ((m = /^\\s*counters\\((.*?)\\)\\s*$/.exec(el))) {\n            tmp = splitProperty(m[1]);\n            result.push(displayCounter(tmp[0], tmp[2], tmp[1]));\n        }\n        else if ((m = /^\\s*attr\\((.*?)\\)\\s*$/.exec(el))) {\n            result.push(element.getAttribute(m[1]) || \"\");\n        }\n        else {\n            result.push(el);\n        }\n    });\n    return result.join(\"\");\n}\n\nfunction getCssText(style) {\n    if (style.cssText) {\n        return style.cssText;\n    }\n    // Status: NEW.  Report year: 2002.  Current year: 2014.\n    // Nice played, Mozillians.\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=137687\n    var result = [];\n    for (var i = 0; i < style.length; ++i) {\n        result.push(style[i] + \": \" + getPropertyValue(style, style[i]));\n    }\n    return result.join(\";\\n\");\n}\n\nfunction _renderWithPseudoElements(element, group) {\n    if (element.tagName == KENDO_PSEUDO_ELEMENT) {\n        _renderElement(element, group);\n        return;\n    }\n    var fake = [];\n    function pseudo(kind, place) {\n        var style = getComputedStyle(element, kind), content = style.content;\n        updateCounters(style);\n        if (content && content != \"normal\" && content != \"none\" && style.width != \"0px\") {\n            var psel = element.ownerDocument.createElement(KENDO_PSEUDO_ELEMENT);\n            setStyle(psel, getCssText(style));\n            psel.textContent = evalPseudoElementContent(element, content);\n            element.insertBefore(psel, place);\n            fake.push(psel);\n        }\n    }\n    pseudo(\":before\", element.firstChild);\n    pseudo(\":after\", null);\n    if (fake.length > 0) {\n        var saveClass = element.className;\n        element.className += \" kendo-pdf-hide-pseudo-elements\";\n        _renderElement(element, group);\n        element.className = saveClass;\n        fake.forEach(function(el){ element.removeChild(el); });\n    } else {\n        _renderElement(element, group);\n    }\n}\n\nfunction _renderElement(element, group) {\n    var style = getComputedStyle(element);\n\n    var top = getBorder(style, \"top\");\n    var right = getBorder(style, \"right\");\n    var bottom = getBorder(style, \"bottom\");\n    var left = getBorder(style, \"left\");\n\n    var rTL0 = getBorderRadius(style, \"top-left\");\n    var rTR0 = getBorderRadius(style, \"top-right\");\n    var rBL0 = getBorderRadius(style, \"bottom-left\");\n    var rBR0 = getBorderRadius(style, \"bottom-right\");\n\n    var dir = getPropertyValue(style, \"direction\");\n\n    var backgroundColor = getPropertyValue(style, \"background-color\");\n    backgroundColor = parseColor(backgroundColor);\n\n    var backgroundImage = parseBackgroundImage( getPropertyValue(style, \"background-image\") );\n    var backgroundRepeat = splitProperty( getPropertyValue(style, \"background-repeat\") );\n    var backgroundPosition = splitProperty( getPropertyValue(style, \"background-position\") );\n    var backgroundOrigin = splitProperty( getPropertyValue(style, \"background-origin\") );\n    var backgroundSize = splitProperty( getPropertyValue(style, \"background-size\") );\n\n    // IE shrinks the text with text-overflow: ellipsis,\n    // apparently because the returned bounding box for the range\n    // is limited to the visible area minus space for the dots,\n    // instead of being the full width of the text.\n    //\n    // https://github.com/telerik/kendo/issues/5232\n    // https://github.com/telerik/kendo-ui-core/issues/1868\n    //\n    // We have to test it here rather than in renderText because\n    // text-overflow: ellipsis could be set on a parent element (not\n    // necessarily the one containing the text); in this case,\n    // getComputedStyle(elementWithTheText) will return \"clip\", not\n    // \"ellipsis\" (which is probably a bug, but oh well...)\n    var textOverflow, saveTextOverflow;\n    if (microsoft) {\n        textOverflow = style.textOverflow;             // computed style\n        if (textOverflow == \"ellipsis\") {\n            saveTextOverflow = element.style.textOverflow; // own style.\n            element.style.textOverflow = \"clip\";\n        }\n    }\n\n    if (browser.msie && browser.version < 10) {\n        // IE9 hacks.  getPropertyValue won't return the correct\n        // value.  Sucks that we have to do it here, I'd prefer to\n        // move it in getPropertyValue, but we don't have the\n        // element.\n        backgroundPosition = splitProperty(element.currentStyle.backgroundPosition);\n    }\n\n    var innerbox = innerBox(element.getBoundingClientRect(), \"border-*-width\", element);\n\n    // CSS \"clip\" property - if present, replace the group with a\n    // new one which is clipped.  This must happen before drawing\n    // the borders and background.\n    (function(){\n        var clip = getPropertyValue(style, \"clip\");\n        var m = /^\\s*rect\\((.*)\\)\\s*$/.exec(clip);\n        if (m) {\n            var a = m[1].split(/[ ,]+/g);\n            var top = a[0] == \"auto\" ? innerbox.top : parseFloat(a[0]) + innerbox.top;\n            var right = a[1] == \"auto\" ? innerbox.right : parseFloat(a[1]) + innerbox.left;\n            var bottom = a[2] == \"auto\" ? innerbox.bottom : parseFloat(a[2]) + innerbox.top;\n            var left = a[3] == \"auto\" ? innerbox.left : parseFloat(a[3]) + innerbox.left;\n            var tmp = new Group();\n            var clipPath = new Path()\n                .moveTo(left, top)\n                .lineTo(right, top)\n                .lineTo(right, bottom)\n                .lineTo(left, bottom)\n                .close();\n            setClipping(tmp, clipPath);\n            group.append(tmp);\n            group = tmp;\n            updateClipbox(clipPath);\n        }\n    })();\n\n    var boxes, i, cells;\n    var display = getPropertyValue(style, \"display\");\n\n    if (display == \"table-row\") {\n        // because of rowspan/colspan, we shouldn't draw background of table row elements on the\n        // box given by its getBoundingClientRect, because if we do we risk overwritting a\n        // previously rendered cell.  https://github.com/telerik/kendo/issues/4881\n        boxes = [];\n        for (i = 0, cells = element.children; i < cells.length; ++i) {\n            boxes.push(cells[i].getBoundingClientRect());\n        }\n    } else {\n        boxes = element.getClientRects();\n        if (boxes.length == 1) {\n            // Workaround the missing borders in Chrome!  getClientRects() boxes contains values\n            // rounded to integer.  getBoundingClientRect() appears to work fine.  We still need\n            // getClientRects() to support cases where there are more boxes (continued inline\n            // elements that might have border/background).\n            boxes = [ element.getBoundingClientRect() ];\n        }\n    }\n\n    // This function workarounds another Chrome bug, where boxes returned for a table with\n    // border-collapse: collapse will overlap the table border.  Our rendering is not perfect in\n    // such case anyway, but with this is better than without it.\n    boxes = adjustBoxes(boxes);\n\n    for (i = 0; i < boxes.length; ++i) {\n        drawOneBox(boxes[i], i === 0, i == boxes.length - 1);\n    }\n\n    // Render links as separate groups.  We can't use boxes returned by element's getClientRects\n    // because if display type is \"inline\" (default for <a>), boxes will not include the height of\n    // images inside.  https://github.com/telerik/kendo-ui-core/issues/3359\n    if (element.tagName == \"A\" && element.href && !/^#?$/.test(element.getAttribute(\"href\"))) {\n        if (!nodeInfo._avoidLinks || !matches(element, nodeInfo._avoidLinks)) {\n            var r = document.createRange();\n            r.selectNodeContents(element);\n            slice(r.getClientRects()).forEach(function(box){\n                var g = new Group();\n                g._pdfLink = {\n                    url    : element.href,\n                    top    : box.top,\n                    right  : box.right,\n                    bottom : box.bottom,\n                    left   : box.left\n                };\n                group.append(g);\n            });\n        }\n    }\n\n    if (boxes.length > 0 && display == \"list-item\" && !element.getAttribute(\"kendo-no-bullet\")) {\n        drawBullet(boxes[0]);\n    }\n\n    // overflow: hidden/auto - if present, replace the group with\n    // a new one clipped by the inner box.\n    (function(){\n        function clipit() {\n            var clipPath = elementRoundBox(element, innerbox, \"padding\");\n            var tmp = new Group();\n            setClipping(tmp, clipPath);\n            group.append(tmp);\n            group = tmp;\n            updateClipbox(clipPath);\n        }\n        if (isFormField(element)) {\n            clipit();\n        } else if (/^(hidden|auto|scroll)/.test(getPropertyValue(style, \"overflow\"))) {\n            clipit();\n        } else if (/^(hidden|auto|scroll)/.test(getPropertyValue(style, \"overflow-x\"))) {\n            clipit();\n        } else if (/^(hidden|auto|scroll)/.test(getPropertyValue(style, \"overflow-y\"))) {\n            clipit();\n        }\n    })();\n\n    if (!maybeRenderWidget(element, group) && !maybeRenderBullet(element, group)) {\n        renderContents(element, group);\n    }\n\n    if (microsoft && textOverflow == \"ellipsis\") {\n        element.style.textOverflow = saveTextOverflow;\n    }\n\n    return group; // only utility functions after this line.\n\n    function adjustBoxes(boxes) {\n        if (/^td$/i.test(element.tagName)) {\n            var table = nodeInfo.table;\n            if (table && getPropertyValue(table.style, \"border-collapse\") == \"collapse\") {\n                var tableBorderLeft = getBorder(table.style, \"left\").width;\n                var tableBorderTop = getBorder(table.style, \"top\").width;\n                // check if we need to adjust\n                if (tableBorderLeft === 0 && tableBorderTop === 0) {\n                    return boxes; // nope\n                }\n                var tableBox = table.element.getBoundingClientRect();\n                var firstCell = table.element.rows[0].cells[0];\n                var firstCellBox = firstCell.getBoundingClientRect();\n                if (firstCellBox.top == tableBox.top || firstCellBox.left == tableBox.left) {\n                    return slice(boxes).map(function(box){\n                        return {\n                            left   : box.left + tableBorderLeft,\n                            top    : box.top + tableBorderTop,\n                            right  : box.right + tableBorderLeft,\n                            bottom : box.bottom + tableBorderTop,\n                            height : box.height,\n                            width  : box.width\n                        };\n                    });\n                }\n            }\n        }\n        return boxes;\n    }\n\n    // this function will be called to draw each border.  it\n    // draws starting at origin and the resulted path must be\n    // translated/rotated to be placed in the proper position.\n    //\n    // arguments are named as if it draws the top border:\n    //\n    //    - `len` the length of the edge\n    //    - `Wtop` the width of the edge (i.e. border-top-width)\n    //    - `Wleft` the width of the left edge (border-left-width)\n    //    - `Wright` the width of the right edge\n    //    - `rl` and `rl` -- the border radius on the left and right\n    //      (objects containing x and y, for horiz/vertical radius)\n    //    - `transform` -- transformation to apply\n    //\n    function drawEdge(color, len, Wtop, Wleft, Wright, rl, rr, transform) {\n        if (Wtop <= 0) {\n            return;\n        }\n\n        var path, edge = new Group();\n        setTransform(edge, transform);\n        group.append(edge);\n\n        sanitizeRadius(rl);\n        sanitizeRadius(rr);\n\n        // draw main border.  this is the area without the rounded corners\n        path = new Path({\n            fill: { color: color },\n            stroke: null\n        });\n        edge.append(path);\n        path.moveTo(rl.x ? Math.max(rl.x, Wleft) : 0, 0)\n            .lineTo(len - (rr.x ? Math.max(rr.x, Wright) : 0), 0)\n            .lineTo(len - Math.max(rr.x, Wright), Wtop)\n            .lineTo(Math.max(rl.x, Wleft), Wtop)\n            .close();\n\n        if (rl.x) {\n            drawRoundCorner(Wleft, rl, [ -1, 0, 0, 1, rl.x, 0 ]);\n        }\n\n        if (rr.x) {\n            drawRoundCorner(Wright, rr, [ 1, 0, 0, 1, len - rr.x, 0 ]);\n        }\n\n        // draws one round corner, starting at origin (needs to be\n        // translated/rotated to be placed properly).\n        function drawRoundCorner(Wright, r, transform) {\n            var angle = Math.PI/2 * Wright / (Wright + Wtop);\n\n            // not sanitizing this one, because negative values\n            // are useful to fill the box correctly.\n            var ri = {\n                x: r.x - Wright,\n                y: r.y - Wtop\n            };\n\n            var path = new Path({\n                fill: { color: color },\n                stroke: null\n            }).moveTo(0, 0);\n\n            setTransform(path, transform);\n\n            addArcToPath(path, 0, r.y, {\n                startAngle: -90,\n                endAngle: -radiansToDegrees(angle),\n                radiusX: r.x,\n                radiusY: r.y\n            });\n\n            if (ri.x > 0 && ri.y > 0) {\n                path.lineTo(ri.x * Math.cos(angle), r.y - ri.y * Math.sin(angle));\n                addArcToPath(path, 0, r.y, {\n                    startAngle: -radiansToDegrees(angle),\n                    endAngle: -90,\n                    radiusX: ri.x,\n                    radiusY: ri.y,\n                    anticlockwise: true\n                });\n            }\n            else if (ri.x > 0) {\n                path.lineTo(ri.x, Wtop)\n                    .lineTo(0, Wtop);\n            }\n            else {\n                path.lineTo(ri.x, Wtop)\n                    .lineTo(ri.x, 0);\n            }\n\n            edge.append(path.close());\n        }\n    }\n\n    function drawBackground(box) {\n        var background = new Group();\n        setClipping(background, roundBox(box, rTL0, rTR0, rBR0, rBL0));\n        group.append(background);\n\n        if (backgroundColor) {\n            var path = new Path({\n                fill: { color: backgroundColor.toCssRgba() },\n                stroke: null\n            });\n            path.moveTo(box.left, box.top)\n                .lineTo(box.right, box.top)\n                .lineTo(box.right, box.bottom)\n                .lineTo(box.left, box.bottom)\n                .close();\n            background.append(path);\n        }\n\n        for (var i = backgroundImage.length; --i >= 0;) {\n            drawOneBackground(\n                background, box,\n                backgroundImage[i],\n                backgroundRepeat[i % backgroundRepeat.length],\n                backgroundPosition[i % backgroundPosition.length],\n                backgroundOrigin[i % backgroundOrigin.length],\n                backgroundSize[i % backgroundSize.length]\n            );\n        }\n    }\n\n    function drawOneBackground(group, box, background, backgroundRepeat, backgroundPosition, backgroundOrigin, backgroundSize) {\n        if (!background || (background == \"none\")) {\n            return;\n        }\n\n        if (background.type == \"url\") {\n            var img = IMAGE_CACHE[background.url];\n            if (img && img.width > 0 && img.height > 0) {\n                drawBackgroundImage(group, box, img.width, img.height, function(group, rect){\n                    group.append(new Image(background.url, rect));\n                });\n            }\n        } else if (background.type == \"linear\") {\n            drawBackgroundImage(group, box, box.width, box.height, gradientRenderer(background));\n        } else {\n            return;\n        }\n\n        function drawBackgroundImage(group, box, img_width, img_height, renderBG) {\n            var aspect_ratio = img_width / img_height, f;\n\n            // for background-origin: border-box the box is already appropriate\n            var orgBox = box;\n            if (backgroundOrigin == \"content-box\") {\n                orgBox = innerBox(orgBox, \"border-*-width\", element);\n                orgBox = innerBox(orgBox, \"padding-*\", element);\n            } else if (backgroundOrigin == \"padding-box\") {\n                orgBox = innerBox(orgBox, \"border-*-width\", element);\n            }\n\n            if (!/^\\s*auto(\\s+auto)?\\s*$/.test(backgroundSize)) {\n                if (backgroundSize == \"contain\") {\n                    f = Math.min(orgBox.width / img_width,\n                                 orgBox.height / img_height);\n                    img_width *= f;\n                    img_height *= f;\n                }\n                else if (backgroundSize == \"cover\") {\n                    f = Math.max(orgBox.width / img_width,\n                                 orgBox.height / img_height);\n                    img_width *= f;\n                    img_height *= f;\n                }\n                else {\n                    var size = backgroundSize.split(/\\s+/g);\n                    // compute width\n                    if (/%$/.test(size[0])) {\n                        img_width = orgBox.width * parseFloat(size[0]) / 100;\n                    } else {\n                        img_width = parseFloat(size[0]);\n                    }\n                    // compute height\n                    if (size.length == 1 || size[1] == \"auto\") {\n                        img_height = img_width / aspect_ratio;\n                    } else if (/%$/.test(size[1])) {\n                        img_height = orgBox.height * parseFloat(size[1]) / 100;\n                    } else {\n                        img_height = parseFloat(size[1]);\n                    }\n                }\n            }\n\n            var pos = String(backgroundPosition);\n\n            // IE sometimes reports single-word positions\n            // https://github.com/telerik/kendo-ui-core/issues/2786\n            //\n            // it seems to switch to percentages when the horizontal\n            // position is not \"center\", therefore we don't handle\n            // multi-word cases here.  All other browsers return\n            // percentages or pixels instead of keywords.  At least\n            // for now...\n            switch (pos) {\n              case \"bottom\" : pos = \"50% 100%\"; break;\n              case \"top\"    : pos = \"50% 0\"; break;\n              case \"left\"   : pos = \"0 50%\"; break;\n              case \"right\"  : pos = \"100% 50%\"; break;\n              case \"center\" : pos = \"50% 50%\"; break;\n            }\n\n            pos = pos.split(/\\s+/);\n            if (pos.length == 1) {\n                pos[1] = \"50%\";\n            }\n\n            if (/%$/.test(pos[0])) {\n                pos[0] = parseFloat(pos[0]) / 100 * (orgBox.width - img_width);\n            } else {\n                pos[0] = parseFloat(pos[0]);\n            }\n            if (/%$/.test(pos[1])) {\n                pos[1] = parseFloat(pos[1]) / 100 * (orgBox.height - img_height);\n            } else {\n                pos[1] = parseFloat(pos[1]);\n            }\n\n            var rect = new geo.Rect([ orgBox.left + pos[0], orgBox.top + pos[1] ], [ img_width, img_height ]);\n\n            // XXX: background-repeat could be implemented more\n            //      efficiently as a fill pattern (at least for PDF\n            //      output, probably SVG too).\n\n            function rewX() {\n                while (rect.origin.x > box.left) {\n                    rect.origin.x -= img_width;\n                }\n            }\n\n            function rewY() {\n                while (rect.origin.y > box.top) {\n                    rect.origin.y -= img_height;\n                }\n            }\n\n            function repeatX() {\n                while (rect.origin.x < box.right) {\n                    renderBG(group, rect.clone());\n                    rect.origin.x += img_width;\n                }\n            }\n\n            if (backgroundRepeat == \"no-repeat\") {\n                renderBG(group, rect);\n            }\n            else if (backgroundRepeat == \"repeat-x\") {\n                rewX();\n                repeatX();\n            }\n            else if (backgroundRepeat == \"repeat-y\") {\n                rewY();\n                while (rect.origin.y < box.bottom) {\n                    renderBG(group, rect.clone());\n                    rect.origin.y += img_height;\n                }\n            }\n            else if (backgroundRepeat == \"repeat\") {\n                rewX();\n                rewY();\n                var origin = rect.origin.clone();\n                while (rect.origin.y < box.bottom) {\n                    rect.origin.x = origin.x;\n                    repeatX();\n                    rect.origin.y += img_height;\n                }\n            }\n        }\n    }\n\n    function drawBullet() {\n        var listStyleType = getPropertyValue(style, \"list-style-type\");\n        if (listStyleType == \"none\") {\n            return;\n        }\n        var listStylePosition = getPropertyValue(style, \"list-style-position\");\n\n        function _drawBullet(f) {\n            saveStyle(element, function(){\n                element.style.position = \"relative\";\n                var bullet = element.ownerDocument.createElement(KENDO_PSEUDO_ELEMENT);\n                bullet.style.position = \"absolute\";\n                bullet.style.boxSizing = \"border-box\";\n                if (listStylePosition == \"outside\") {\n                    bullet.style.width = \"6em\";\n                    bullet.style.left = \"-6.8em\";\n                    bullet.style.textAlign = \"right\";\n                } else {\n                    bullet.style.left = \"0px\";\n                }\n                f(bullet);\n                element.insertBefore(bullet, element.firstChild);\n                renderElement(bullet, group);\n                element.removeChild(bullet);\n            });\n        }\n\n        function elementIndex(f) {\n            var a = element.parentNode.children;\n            var k = element.getAttribute(\"kendo-split-index\");\n            if (k != null) {\n                return f(k|0, a.length);\n            }\n            for (var i = 0; i < a.length; ++i) {\n                if (a[i] === element) {\n                    return f(i, a.length);\n                }\n            }\n        }\n\n        switch (listStyleType) {\n          case \"circle\":\n          case \"disc\":\n          case \"square\":\n            _drawBullet(function(bullet){\n                bullet.innerHTML = '&nbsp;';\n                bullet.setAttribute(KENDO_BULLET_TYPE, listStyleType);\n            });\n            break;\n\n          case \"decimal\":\n          case \"decimal-leading-zero\":\n            _drawBullet(function(bullet){\n                elementIndex(function(idx){\n                    ++idx;\n                    if (listStyleType == \"decimal-leading-zero\" && idx < 10) {\n                        idx = \"0\" + idx;\n                    }\n                    bullet.innerHTML = idx + \".\";\n                });\n            });\n            break;\n\n          case \"lower-roman\":\n          case \"upper-roman\":\n            _drawBullet(function(bullet){\n                elementIndex(function(idx){\n                    idx = arabicToRoman(idx + 1);\n                    if (listStyleType == \"upper-roman\") {\n                        idx = idx.toUpperCase();\n                    }\n                    bullet.innerHTML = idx + \".\";\n                });\n            });\n            break;\n\n          case \"lower-latin\":\n          case \"lower-alpha\":\n          case \"upper-latin\":\n          case \"upper-alpha\":\n            _drawBullet(function(bullet){\n                elementIndex(function(idx){\n                    idx = alphaNumeral(idx);\n                    if (/^upper/i.test(listStyleType)) {\n                        idx = idx.toUpperCase();\n                    }\n                    bullet.innerHTML = idx + \".\";\n                });\n            });\n            break;\n        }\n    }\n\n    // draws a single border box\n    function drawOneBox(box, isFirst, isLast) {\n        if (box.width === 0 || box.height === 0) {\n            return;\n        }\n\n        drawBackground(box);\n\n        var shouldDrawLeft = (left.width > 0 && ((isFirst && dir == \"ltr\") || (isLast && dir == \"rtl\")));\n        var shouldDrawRight = (right.width > 0 && ((isLast && dir == \"ltr\") || (isFirst && dir == \"rtl\")));\n\n        // The most general case is that the 4 borders have different widths and border\n        // radiuses.  The way that is handled is by drawing 3 Paths for each border: the\n        // straight line, and two round corners which represent half of the entire rounded\n        // corner.  To simplify code those shapes are drawed at origin (by the drawEdge\n        // function), then translated/rotated into the right position.\n        //\n        // However, this leads to poor results due to rounding in the simpler cases where\n        // borders are straight lines.  Therefore we handle a few such cases separately with\n        // straight lines. C^wC^wC^w -- nope, scratch that.  poor rendering was because of a bug\n        // in Chrome (getClientRects() returns rounded integer values rather than exact floats.\n        // web dev is still a ghetto.)\n\n        // first, just in case there is no border...\n        if (top.width === 0 && left.width === 0 && right.width === 0 && bottom.width === 0) {\n            return;\n        }\n\n        // START paint borders\n        // if all borders have equal colors...\n        if (top.color == right.color && top.color == bottom.color && top.color == left.color) {\n\n            // if same widths too, we can draw the whole border by stroking a single path.\n            if (top.width == right.width && top.width == bottom.width && top.width == left.width)\n            {\n                if (shouldDrawLeft && shouldDrawRight) {\n                    // reduce box by half the border width, so we can draw it by stroking.\n                    box = innerBox(box, top.width/2);\n\n                    // adjust the border radiuses, again by top.width/2, and make the path element.\n                    var path = elementRoundBox(element, box, top.width/2);\n                    path.options.stroke = {\n                        color: top.color,\n                        width: top.width\n                    };\n                    group.append(path);\n                    return;\n                }\n            }\n        }\n\n        // if border radiuses are zero and widths are at most one pixel, we can again use simple\n        // paths.\n        if (rTL0.x === 0 && rTR0.x === 0 && rBR0.x === 0 && rBL0.x === 0) {\n            // alright, 1.9px will do as well.  the difference in color blending should not be\n            // noticeable.\n            if (top.width < 2 && left.width < 2 && right.width < 2 && bottom.width < 2) {\n                // top border\n                if (top.width > 0) {\n                    group.append(\n                        new Path({\n                            stroke: { width: top.width, color: top.color }\n                        })\n                            .moveTo(box.left, box.top + top.width/2)\n                            .lineTo(box.right, box.top + top.width/2)\n                    );\n                }\n\n                // bottom border\n                if (bottom.width > 0) {\n                    group.append(\n                        new Path({\n                            stroke: { width: bottom.width, color: bottom.color }\n                        })\n                            .moveTo(box.left, box.bottom - bottom.width/2)\n                            .lineTo(box.right, box.bottom - bottom.width/2)\n                    );\n                }\n\n                // left border\n                if (shouldDrawLeft) {\n                    group.append(\n                        new Path({\n                            stroke: { width: left.width, color: left.color }\n                        })\n                            .moveTo(box.left + left.width/2, box.top)\n                            .lineTo(box.left + left.width/2, box.bottom)\n                    );\n                }\n\n                // right border\n                if (shouldDrawRight) {\n                    group.append(\n                        new Path({\n                            stroke: { width: right.width, color: right.color }\n                        })\n                            .moveTo(box.right - right.width/2, box.top)\n                            .lineTo(box.right - right.width/2, box.bottom)\n                    );\n                }\n\n                return;\n            }\n        }\n        // END paint borders\n\n        var tmp = adjustBorderRadiusForBox(box, rTL0, rTR0, rBR0, rBL0);\n        var rTL = tmp.tl;\n        var rTR = tmp.tr;\n        var rBR = tmp.br;\n        var rBL = tmp.bl;\n\n        // top border\n        drawEdge(top.color,\n                 box.width, top.width, left.width, right.width,\n                 rTL, rTR,\n                 [ 1, 0, 0, 1, box.left, box.top ]);\n\n        // bottom border\n        drawEdge(bottom.color,\n                 box.width, bottom.width, right.width, left.width,\n                 rBR, rBL,\n                 [ -1, 0, 0, -1, box.right, box.bottom ]);\n\n        // for left/right borders we need to invert the border-radiuses\n        function inv(p) {\n            return { x: p.y, y: p.x };\n        }\n\n        // left border\n        drawEdge(left.color,\n                 box.height, left.width, bottom.width, top.width,\n                 inv(rBL), inv(rTL),\n                 [ 0, -1, 1, 0, box.left, box.bottom ]);\n\n        // right border\n        drawEdge(right.color,\n                 box.height, right.width, top.width, bottom.width,\n                 inv(rTR), inv(rBR),\n                 [ 0, 1, -1, 0, box.right, box.top ]);\n    }\n}\n\nfunction gradientRenderer(gradient) {\n    return function(group, rect) {\n        var width = rect.width(), height = rect.height();\n\n        switch (gradient.type) {\n          case \"linear\":\n\n            // figure out the angle.\n            var angle = gradient.angle != null ? gradient.angle : Math.PI;\n            switch (gradient.to) {\n              case \"top\":\n                angle = 0;\n                break;\n              case \"left\":\n                angle = -Math.PI / 2;\n                break;\n              case \"bottom\":\n                angle = Math.PI;\n                break;\n              case \"right\":\n                angle = Math.PI / 2;\n                break;\n              case \"top left\": case \"left top\":\n                angle = -Math.atan2(height, width);\n                break;\n              case \"top right\": case \"right top\":\n                angle = Math.atan2(height, width);\n                break;\n              case \"bottom left\": case \"left bottom\":\n                angle = Math.PI + Math.atan2(height, width);\n                break;\n              case \"bottom right\": case \"right bottom\":\n                angle = Math.PI - Math.atan2(height, width);\n                break;\n            }\n\n            if (gradient.reverse) {\n                angle -= Math.PI;\n            }\n\n            // limit the angle between 0..2PI\n            angle %= 2 * Math.PI;\n            if (angle < 0) {\n                angle += 2 * Math.PI;\n            }\n\n            // compute gradient's start/end points.  here len is the length of the gradient line\n            // and x,y is the end point relative to the center of the rectangle in conventional\n            // (math) axis direction.\n\n            // this is the original (unscaled) length of the gradient line.  needed to deal with\n            // absolutely positioned color stops.  formula from the CSS spec:\n            // http://dev.w3.org/csswg/css-images-3/#linear-gradient-syntax\n            var pxlen = Math.abs(width * Math.sin(angle)) + Math.abs(height * Math.cos(angle));\n\n            // The math below is pretty simple, but it took a while to figure out.  We compute x\n            // and y, the *end* of the gradient line.  However, we want to transform them into\n            // element-based coordinates (SVG's gradientUnits=\"objectBoundingBox\").  That means,\n            // x=0 is the left edge, x=1 is the right edge, y=0 is the top edge and y=1 is the\n            // bottom edge.\n            //\n            // A naive approach would use the original angle for these calculations.  Say we'd\n            // like to draw a gradient angled at 45deg in a 100x400 box.  When we use\n            // objectBoundingBox, the renderer will draw it in a 1x1 *square* box, and then\n            // scale that to the desired dimensions.  The 45deg angle will look more like 70deg\n            // after scaling.  SVG (http://www.w3.org/TR/SVG/pservers.html#LinearGradients) says\n            // the following:\n            //\n            //     When gradientUnits=\"objectBoundingBox\" and 'gradientTransform' is the\n            //     identity matrix, the normal of the linear gradient is perpendicular to the\n            //     gradient vector in object bounding box space (i.e., the abstract coordinate\n            //     system where (0,0) is at the top/left of the object bounding box and (1,1) is\n            //     at the bottom/right of the object bounding box). When the object's bounding\n            //     box is not square, the gradient normal which is initially perpendicular to\n            //     the gradient vector within object bounding box space may render\n            //     non-perpendicular relative to the gradient vector in user space. If the\n            //     gradient vector is parallel to one of the axes of the bounding box, the\n            //     gradient normal will remain perpendicular. This transformation is due to\n            //     application of the non-uniform scaling transformation from bounding box space\n            //     to user space.\n            //\n            // which is an extremely long and confusing way to tell what I just said above.\n            //\n            // For this reason we need to apply the reverse scaling to the original angle, so\n            // that when it'll finally be rendered it'll actually be at the desired slope.  Now\n            // I'll let you figure out the math yourself.\n\n            var scaledAngle = Math.atan(width * Math.tan(angle) / height);\n            var sin = Math.sin(scaledAngle), cos = Math.cos(scaledAngle);\n            var len = Math.abs(sin) + Math.abs(cos);\n            var x = len/2 * sin;\n            var y = len/2 * cos;\n\n            // Because of the arctangent, our scaledAngle ends up between -PI/2..PI/2, possibly\n            // losing the intended direction of the gradient.  The following fixes it.\n            if (angle > Math.PI/2 && angle <= 3*Math.PI/2) {\n                x = -x;\n                y = -y;\n            }\n\n            // compute the color stops.\n            var implicit = [], right = 0;\n            var stops = gradient.stops.map(function(s, i){\n                var offset = s.percent;\n                if (offset) {\n                    offset = parseFloat(offset) / 100;\n                } else if (s.length) {\n                    offset = parseFloat(s.length) / pxlen;\n                } else if (i === 0) {\n                    offset = 0;\n                } else if (i == gradient.stops.length - 1) {\n                    offset = 1;\n                }\n                var stop = {\n                    color: s.color.toCssRgba(),\n                    offset: offset\n                };\n                if (offset != null) {\n                    right = offset;\n                    // fix implicit offsets\n                    implicit.forEach(function(s, i){\n                        var stop = s.stop;\n                        stop.offset = s.left + (right - s.left) * (i + 1) / (implicit.length + 1);\n                    });\n                    implicit = [];\n                } else {\n                    implicit.push({ left: right, stop: stop });\n                }\n                return stop;\n            });\n\n            var start = [ 0.5 - x, 0.5 + y ];\n            var end = [ 0.5 + x, 0.5 - y ];\n\n            // finally, draw it.\n            group.append(\n                Path.fromRect(rect)\n                    .stroke(null)\n                    .fill(new LinearGradient({\n                        start     : start,\n                        end       : end,\n                        stops     : stops,\n                        userSpace : false\n                    }))\n            );\n            break;\n          case \"radial\":\n            // XXX:\n            if (window.console && window.console.log) {\n                window.console.log(\"Radial gradients are not yet supported in HTML renderer\");\n            }\n            break;\n        }\n    };\n}\n\nfunction maybeRenderWidget(element, group) {\n    var visual;\n    if (element._kendoExportVisual) {\n        var rect = element.getBoundingClientRect();\n        var size = {\n            width: rect.width,\n            height: rect.height\n        };\n        visual = element._kendoExportVisual(size);\n    } else if (window.kendo && window.kendo.jQuery && element.getAttribute(window.kendo.attr(\"role\"))) {\n        var widget = window.kendo.widgetInstance(window.kendo.jQuery(element));\n        if (widget && (widget.exportDOMVisual || widget.exportVisual)) {\n            if (widget.exportDOMVisual) {\n                visual = widget.exportDOMVisual();\n            } else {\n                visual = widget.exportVisual();\n            }\n        }\n    }\n\n    if (!visual) {\n        return false;\n    }\n\n    var wrap = new Group();\n    wrap.children.push(visual);\n\n    var bbox = element.getBoundingClientRect();\n    wrap.transform(geo.transform().translate(bbox.left, bbox.top));\n\n    group.append(wrap);\n\n    return true;\n}\n\nfunction maybeRenderBullet(element, group) {\n    var bulletType = element.getAttribute(KENDO_BULLET_TYPE);\n\n    if (!bulletType) {\n        return false;\n    }\n\n    var box = element.getBoundingClientRect();\n    var color = getComputedStyle(element).color;\n\n    if (bulletType === 'square') {\n        var rectSize = box.height / 5;\n        group.append(new Rect(new geo.Rect([\n            box.right - rectSize,\n            box.top + box.height / 2.1\n        ], [rectSize, rectSize])).fill(color).stroke(color));\n    } else {\n        var radius = box.height / 7;\n        var center = [\n            box.right - radius,\n            box.top + (box.height + radius) / 2\n        ];\n        var circle = new Circle(new geo.Circle(center, radius));\n        if (bulletType === 'circle') {\n            circle.stroke(color, 0.5);\n        } else {\n            circle.fill(color).stroke(null);\n        }\n        group.append(circle);\n    }\n\n    return true;\n}\n\nfunction renderImage(element, url, group) {\n    var box = getContentBox(element);\n    var rect = new geo.Rect([ box.left, box.top ], [ box.width, box.height ]);\n    var image = new Image(url, rect);\n    setClipping(image, elementRoundBox(element, box, \"content\"));\n    group.append(image);\n}\n\nfunction zIndexSort(a, b) {\n    var sa = getComputedStyle(a);\n    var sb = getComputedStyle(b);\n    var za = parseFloat(getPropertyValue(sa, \"z-index\"));\n    var zb = parseFloat(getPropertyValue(sb, \"z-index\"));\n    var pa = getPropertyValue(sa, \"position\");\n    var pb = getPropertyValue(sb, \"position\");\n    if (isNaN(za) && isNaN(zb)) {\n        if ((/static|absolute/.test(pa)) && (/static|absolute/.test(pb))) {\n            return 0;\n        }\n        if (pa == \"static\") {\n            return -1;\n        }\n        if (pb == \"static\") {\n            return 1;\n        }\n        return 0;\n    }\n    if (isNaN(za)) {\n        return zb === 0 ? 0 : zb > 0 ? -1 : 1;\n    }\n    if (isNaN(zb)) {\n        return za === 0 ? 0 : za > 0 ? 1 : -1;\n    }\n    return parseFloat(za) - parseFloat(zb);\n}\n\nfunction isFormField(element) {\n    return /^(?:textarea|select|input)$/i.test(element.tagName);\n}\n\nfunction getSelectedOption(element) {\n    if (element.selectedOptions && element.selectedOptions.length > 0) {\n        return element.selectedOptions[0];\n    }\n    return element.options[element.selectedIndex];\n}\n\nfunction renderCheckbox(element, group) {\n    var style = getComputedStyle(element);\n    var color = getPropertyValue(style, \"color\");\n    var box = element.getBoundingClientRect();\n    if (element.type == \"checkbox\") {\n        group.append(\n            Path.fromRect(\n                new geo.Rect([ box.left+1, box.top+1 ],\n                             [ box.width-2, box.height-2 ])\n            ).stroke(color, 1)\n        );\n        if (element.checked) {\n            // fill a rectangle inside?  looks kinda ugly.\n            // group.append(\n            //     Path.fromRect(\n            //         new geo.Rect([ box.left+4, box.top+4 ],\n            //                      [ box.width-8, box.height-8])\n            //     ).fill(color).stroke(null)\n            // );\n\n            // let's draw a checkmark instead.  artistic, eh?\n            group.append(\n                new Path()\n                    .stroke(color, 1.2)\n                    .moveTo(box.left + 0.22 * box.width,\n                            box.top + 0.55 * box.height)\n                    .lineTo(box.left + 0.45 * box.width,\n                            box.top + 0.75 * box.height)\n                    .lineTo(box.left + 0.78 * box.width,\n                            box.top + 0.22 * box.width)\n            );\n        }\n    } else {\n        group.append(\n            new Circle(\n                new geo.Circle([\n                    (box.left + box.right) / 2,\n                    (box.top + box.bottom) / 2\n                ], Math.min(box.width-2, box.height-2) / 2)\n            ).stroke(color, 1)\n        );\n        if (element.checked) {\n            group.append(\n                new Circle(\n                    new geo.Circle([\n                        (box.left + box.right) / 2,\n                        (box.top + box.bottom) / 2\n                    ], Math.min(box.width-8, box.height-8) / 2)\n                ).fill(color).stroke(null)\n            );\n        }\n    }\n}\n\nfunction renderFormField(element, group) {\n    var tag = element.tagName.toLowerCase();\n    if (tag == \"input\" && (element.type == \"checkbox\" || element.type == \"radio\")) {\n        return renderCheckbox(element, group);\n    }\n    var p = element.parentNode;\n    var doc = element.ownerDocument;\n    var el = doc.createElement(KENDO_PSEUDO_ELEMENT);\n    var option;\n    setStyle(el, getCssText(getComputedStyle(element)));\n    if (tag == \"input\") {\n        el.style.whiteSpace = \"pre\";\n    }\n    if (tag == \"select\" || tag == \"textarea\") {\n        el.style.overflow = \"auto\";\n    }\n    if (tag == \"select\") {\n        if (element.multiple) {\n            for (var i = 0; i < element.options.length; ++i) {\n                option = doc.createElement(KENDO_PSEUDO_ELEMENT);\n                setStyle(option, getCssText(getComputedStyle(element.options[i])));\n                option.style.display = \"block\"; // IE9 messes up without this\n                option.textContent = element.options[i].textContent;\n                el.appendChild(option);\n            }\n        } else {\n            option = getSelectedOption(element);\n            if (option) {\n                el.textContent = option.textContent;\n            }\n        }\n    } else {\n        el.textContent = element.value;\n    }\n    p.insertBefore(el, element);\n    el.scrollLeft = element.scrollLeft;\n    el.scrollTop = element.scrollTop;\n\n    // must temporarily hide the original element, otherwise it\n    // may affect layout of the fake element we want to render.\n    element.style.display = \"none\";\n\n    renderContents(el, group);\n    element.style.display = \"\";\n    p.removeChild(el);\n}\n\nfunction serializeSVG(element) {\n    var serializer = new window.XMLSerializer();\n    var xml = serializer.serializeToString(element);\n\n    if (browser.mozilla && !(element.getAttribute(\"width\") && element.getAttribute(\"height\"))) {\n        var doc = new window.DOMParser().parseFromString(xml, \"image/svg+xml\");\n        var svg = doc.documentElement;\n        var box = getContentBox(element);\n        svg.setAttribute(\"width\", box.width);\n        svg.setAttribute(\"height\", box.height);\n        xml = serializer.serializeToString(svg);\n    }\n\n    return xml;\n}\n\nfunction renderContents(element, group) {\n    if (nodeInfo._stackingContext.element === element) {\n        // the group that was set in pushNodeInfo might have\n        // changed due to clipping/transforms, update it here.\n        nodeInfo._stackingContext.group = group;\n    }\n    switch (element.tagName.toLowerCase()) {\n      case \"img\":\n        renderImage(element, element.src, group);\n        break;\n\n      case \"svg\":\n        var xml = serializeSVG(element);\n        var dataURL = \"data:image/svg+xml;base64,\" + (encodeBase64(xml));\n        renderImage(element, dataURL, group);\n        break;\n\n      case \"canvas\":\n        try {\n            renderImage(element, element.toDataURL(\"image/png\"), group);\n        } catch (ex) {\n            // tainted; can't draw it, ignore.\n        }\n        break;\n\n      case \"textarea\":\n      case \"input\":\n      case \"select\":\n        renderFormField(element, group);\n        break;\n\n      default:\n        var children = [], floats = [], positioned = [];\n        for (var i = element.firstChild; i; i = i.nextSibling) {\n            switch (i.nodeType) {\n              case 3:         // Text\n                if (/\\S/.test(i.data)) {\n                    renderText(element, i, group);\n                }\n                break;\n              case 1:         // Element\n                var style = getComputedStyle(i);\n                var floating = getPropertyValue(style, \"float\");\n                var position = getPropertyValue(style, \"position\");\n                if (position != \"static\") {\n                    positioned.push(i);\n                }\n                else if (floating != \"none\") {\n                    floats.push(i);\n                } else {\n                    children.push(i);\n                }\n                break;\n            }\n        }\n\n        mergeSort(children, zIndexSort).forEach(function(el){ renderElement(el, group); });\n        mergeSort(floats, zIndexSort).forEach(function(el){ renderElement(el, group); });\n        mergeSort(positioned, zIndexSort).forEach(function(el){ renderElement(el, group); });\n    }\n}\n\nfunction renderText(element, node, group) {\n    if (emptyClipbox()) {\n        return;\n    }\n    var style = getComputedStyle(element);\n\n    if (parseFloat(getPropertyValue(style, \"text-indent\")) < -500) {\n        // assume it should not be displayed.  the slider's\n        // draggable handle displays a Drag text for some reason,\n        // having text-indent: -3333px.\n        return;\n    }\n\n    var text = node.data;\n    var start = 0;\n    var end = text.search(/\\S\\s*$/) + 1;\n\n    if (!end) {\n        return; // whitespace-only node\n    }\n\n    var fontSize = getPropertyValue(style, \"font-size\");\n    var lineHeight = getPropertyValue(style, \"line-height\");\n\n    // simply getPropertyValue(\"font\") doesn't work in Firefox :-\\\n    var font = [\n        getPropertyValue(style, \"font-style\"),\n        getPropertyValue(style, \"font-variant\"),\n        getPropertyValue(style, \"font-weight\"),\n        fontSize, // no need for line height here; it breaks layout in FF\n        getPropertyValue(style, \"font-family\")\n    ].join(\" \");\n\n    fontSize = parseFloat(fontSize);\n    lineHeight = parseFloat(lineHeight);\n\n    if (fontSize === 0 || isNaN(fontSize)) {\n        return;\n    }\n\n    var color = getPropertyValue(style, \"color\");\n    var range = element.ownerDocument.createRange();\n    var align = getPropertyValue(style, \"text-align\");\n    var isJustified = align == \"justify\";\n    var columnCount = getPropertyValue(style, \"column-count\", 1);\n    var whiteSpace = getPropertyValue(style, \"white-space\");\n    var textTransform = getPropertyValue(style, \"text-transform\");\n\n    // A line of 500px, with a font of 12px, contains an average of 80 characters, but since we\n    // err, we'd like to guess a bigger number rather than a smaller one.  Multiplying by 5\n    // seems to be a good option.\n    var estimateLineLength = element.getBoundingClientRect().width / fontSize * 5;\n    if (estimateLineLength === 0) {\n        estimateLineLength = 500;\n    }\n\n    // we'll maintain this so we can workaround bugs in Chrome's Range.getClientRects\n    // https://github.com/telerik/kendo/issues/5740\n    var prevLineBottom = null;\n\n    var underline = nodeInfo[\"underline\"];\n    var lineThrough = nodeInfo[\"line-through\"];\n    var overline = nodeInfo[\"overline\"];\n    var underlineOffset = nodeInfo[\"underline-offset\"];\n\n    if (underline) {\n        forEachRect(decorateUnder);\n    }\n\n    // doChunk returns true when all text has been rendered\n    while (!doChunk()) {}\n\n    if (lineThrough || overline) {\n        forEachRect(decorateOver);\n    }\n\n    return;                 // only function declarations after this line\n\n    function forEachRect(callback) {\n        range.selectNode(node);\n        var clientRects = slice(range.getClientRects());\n\n        forEachRect = function (cb) { return clientRects.forEach(cb); };\n        forEachRect(callback);\n    }\n\n    function actuallyGetRangeBoundingRect(range) {\n        // XXX: to be revised when this Chrome bug is fixed:\n        // https://bugs.chromium.org/p/chromium/issues/detail?id=612459\n        if (microsoft || browser.chrome || browser.safari) {\n            // Workaround browser bugs: IE and Chrome would sometimes\n            // return 0 or 1-width rectangles before or after the main\n            // one.  https://github.com/telerik/kendo/issues/4674\n\n            // Actually Chrome 50 got worse, since the rectangles can now have the width of a\n            // full character, making it hard to tell whether it's a bogus rectangle or valid\n            // selection location.  The workaround is to ignore rectangles that fall on the\n            // previous line.  https://github.com/telerik/kendo/issues/5740\n            var rectangles = range.getClientRects(), box = {\n                top    :  Infinity,\n                right  : -Infinity,\n                bottom : -Infinity,\n                left   :  Infinity\n            }, done = false;\n            for (var i = 0; i < rectangles.length; ++i) {\n                var b = rectangles[i];\n                if (b.width <= 1 || b.bottom === prevLineBottom) {\n                    continue;   // bogus rectangle\n                }\n                box.left   = Math.min(b.left   , box.left);\n                box.top    = Math.min(b.top    , box.top);\n                box.right  = Math.max(b.right  , box.right);\n                box.bottom = Math.max(b.bottom , box.bottom);\n                done = true;\n            }\n            if (!done) {\n                return range.getBoundingClientRect();\n            }\n            box.width = box.right - box.left;\n            box.height = box.bottom - box.top;\n            return box;\n        }\n        return range.getBoundingClientRect();\n    }\n\n    // Render a chunk of text, typically one line (but for justified text we render each word as\n    // a separate Text object, because spacing is variable).  Returns true when it finished the\n    // current node.  After each chunk it updates `start` to just after the last rendered\n    // character.\n    function doChunk() {\n        var origStart = start;\n        var box, pos = text.substr(start).search(/\\S/);\n        start += pos;\n        if (pos < 0 || start >= end) {\n            return true;\n        }\n\n        // Select a single character to determine the height of a line of text.  The box.bottom\n        // will be essential for us to figure out where the next line begins.\n        range.setStart(node, start);\n        range.setEnd(node, start + 1);\n        box = actuallyGetRangeBoundingRect(range);\n\n        // for justified text we must split at each space, because space has variable width.\n        var found = false;\n        if (isJustified || columnCount > 1) {\n            pos = text.substr(start).search(/\\s/);\n            if (pos >= 0) {\n                // we can only split there if it's on the same line, otherwise we'll fall back\n                // to the default mechanism (see findEOL below).\n                range.setEnd(node, start + pos);\n                var r = actuallyGetRangeBoundingRect(range);\n                if (r.bottom == box.bottom) {\n                    box = r;\n                    found = true;\n                    start += pos;\n                }\n            }\n        }\n\n        if (!found) {\n            // This code does three things: (1) it selects one line of text in `range`, (2) it\n            // leaves the bounding rect of that line in `box` and (3) it returns the position\n            // just after the EOL.  We know where the line starts (`start`) but we don't know\n            // where it ends.  To figure this out, we select a piece of text and look at the\n            // bottom of the bounding box.  If it changes, we have more than one line selected\n            // and should retry with a smaller selection.\n            //\n            // To speed things up, we first try to select all text in the node (`start` ->\n            // `end`).  If there's more than one line there, then select only half of it.  And\n            // so on.  When we find a value for `end` that fits in one line, we try increasing\n            // it (also in halves) until we get to the next line.  The algorithm stops when the\n            // right side of the bounding box does not change.\n            //\n            // One more thing to note is that everything happens in a single Text DOM node.\n            // There's no other tags inside it, therefore the left/top coordinates of the\n            // bounding box will not change.\n            pos = (function findEOL(min, eol, max){\n                range.setEnd(node, eol);\n                var r = actuallyGetRangeBoundingRect(range);\n                if (r.bottom != box.bottom && min < eol) {\n                    return findEOL(min, (min + eol) >> 1, eol);\n                } else if (r.right != box.right) {\n                    box = r;\n                    if (eol < max) {\n                        return findEOL(eol, (eol + max) >> 1, max);\n                    } else {\n                        return eol;\n                    }\n                } else {\n                    return eol;\n                }\n            })(start, Math.min(end, start + estimateLineLength), end);\n\n            if (pos == start) {\n                // if EOL is at the start, then no more text fits on this line.  Skip the\n                // remainder of this node entirely to avoid a stack overflow.\n                return true;\n            }\n            start = pos;\n\n            pos = range.toString().search(/\\s+$/);\n            if (pos === 0) {\n                return false; // whitespace only; we should not get here.\n            }\n            if (pos > 0) {\n                // eliminate trailing whitespace\n                range.setEnd(node, range.startOffset + pos);\n                box = actuallyGetRangeBoundingRect(range);\n            }\n        }\n\n        // another workaround for IE: if we rely on getBoundingClientRect() we'll overlap with the bullet for LI\n        // elements.  Calling getClientRects() and using the *first* rect appears to give us the correct location.\n        // Note: not to be used in Chrome as it randomly returns a zero-width rectangle from the previous line.\n        if (microsoft) {\n            box = range.getClientRects()[0];\n        }\n\n        var str = range.toString();\n        if (!/^(?:pre|pre-wrap)$/i.test(whiteSpace)) {\n            // node with non-significant space -- collapse whitespace.\n            str = str.replace(/\\s+/g, \" \");\n        }\n        else if (/\\t/.test(str)) {\n            // with significant whitespace we need to do something about literal TAB characters.\n            // There's no TAB glyph in a font so they would be rendered in PDF as an empty box,\n            // and the whole text will stretch to fill the original width.  The core PDF lib\n            // does not have sufficient context to deal with it.\n\n            // calculate the starting column here, since we initially discarded any whitespace.\n            var cc = 0;\n            for (pos = origStart; pos < range.startOffset; ++pos) {\n                var code = text.charCodeAt(pos);\n                if (code == 9) {\n                    // when we meet a TAB we must round up to the next tab stop.\n                    // in all browsers TABs seem to be 8 characters.\n                    cc += 8 - cc % 8;\n                } else if (code == 10 || code == 13) {\n                    // just in case we meet a newline we must restart.\n                    cc = 0;\n                } else {\n                    // ordinary character --> advance one column\n                    cc++;\n                }\n            }\n\n            // based on starting column, replace any TAB characters in the string we actually\n            // have to display with spaces so that they align to columns multiple of 8.\n            while ((pos = str.search(\"\\t\")) >= 0) {\n                var indent = \"        \".substr(0, 8 - (cc + pos) % 8);\n                str = str.substr(0, pos) + indent + str.substr(pos + 1);\n            }\n        }\n\n        if (!found) {\n            prevLineBottom = box.bottom;\n        }\n        drawText(str, box);\n    }\n\n    function drawText(str, box) {\n        // In IE the box height will be approximately lineHeight, while in\n        // other browsers it'll (correctly) be the height of the bounding\n        // box for the current text/font.  Which is to say, IE sucks again.\n        // The only good solution I can think of is to measure the text\n        // ourselves and center the bounding box.\n        if (microsoft && !isNaN(lineHeight)) {\n            var height = getFontHeight(font);\n            var top = (box.top + box.bottom - height) / 2;\n            box = {\n                top    : top,\n                right  : box.right,\n                bottom : top + height,\n                left   : box.left,\n                height : height,\n                width  : box.right - box.left\n            };\n        }\n\n        // var path = new Path({ stroke: { color: \"red\" }});\n        // path.moveTo(box.left, box.top)\n        //     .lineTo(box.right, box.top)\n        //     .lineTo(box.right, box.bottom)\n        //     .lineTo(box.left, box.bottom)\n        //     .close();\n        // group.append(path);\n\n        switch (textTransform) {\n          case \"uppercase\":\n            str = str.toUpperCase();\n            break;\n          case \"lowercase\":\n            str = str.toLowerCase();\n            break;\n          case \"capitalize\":\n            str = str.replace(/(?:^|\\s)\\S/g, function (l) { return l.toUpperCase(); });\n            break;\n        }\n\n        var text = new TextRect(\n            str, new geo.Rect([ box.left, box.top ],\n                              [ box.width, box.height ]),\n            {\n                font: font,\n                fill: { color: color }\n            }\n        );\n        group.append(text);\n    }\n\n    function drawTextLine(lineWidth, textBox, color, ypos) {\n        if (color) {\n            var path = new Path({ stroke: {\n                width: lineWidth,\n                color: color\n            }});\n\n            ypos -= lineWidth;\n            path.moveTo(textBox.left, ypos)\n                .lineTo(textBox.right, ypos);\n            group.append(path);\n        }\n    }\n\n    function decorateOver(box) {\n        var width = fontSize / 12;\n        drawTextLine(width, box, lineThrough, box.bottom - box.height / 2.7);\n        drawTextLine(width, box, overline, box.top);\n    }\n\n    function decorateUnder(box) {\n        var width = fontSize / 12;\n        var underlinePos = box.bottom;\n        if (underlineOffset != null) {\n            underlinePos += underlineOffset;\n        } else {\n            underlinePos += width; // for \"auto\" it seems better to add line width\n        }\n        drawTextLine(width, box, underline, underlinePos);\n    }\n}\n\nfunction groupInStackingContext(element, group, zIndex) {\n    var main;\n    if (zIndex != \"auto\") {\n        // use the current stacking context\n        main = nodeInfo._stackingContext.group;\n        zIndex = parseFloat(zIndex);\n    } else {\n        // normal flow — use given container.  we still have to\n        // figure out where should we insert this element with the\n        // assumption that its z-index is zero, as the group might\n        // already contain elements with higher z-index.\n        main = group;\n        zIndex = 0;\n    }\n    var a = main.children;\n    for (var i = 0; i < a.length; ++i) {\n        if (a[i]._dom_zIndex != null && a[i]._dom_zIndex > zIndex) {\n            break;\n        }\n    }\n\n    var tmp = new Group();\n    main.insert(i, tmp);\n    tmp._dom_zIndex = zIndex;\n\n    if (main !== group) {\n        // console.log(\"Placing\", element, \"in\", nodeInfo._stackingContext.element, \"at position\", i, \" / \", a.length);\n        // console.log(a.slice(i+1));\n\n        // if (nodeInfo._matrix) {\n        //     tmp.transform(nodeInfo._matrix);\n        // }\n        if (nodeInfo._clipbox) {\n            var m = nodeInfo._matrix.invert();\n            var r = nodeInfo._clipbox.transformCopy(m);\n            setClipping(tmp, Path.fromRect(r));\n            // console.log(r);\n            // tmp.append(Path.fromRect(r));\n            // tmp.append(new Text(element.className || element.id, r.topLeft()));\n        }\n    }\n\n    return tmp;\n}\n\nfunction renderElement(element, container) {\n    var style = getComputedStyle(element);\n\n    updateCounters(style);\n\n    if (/^(style|script|link|meta|iframe|col|colgroup)$/i.test(element.tagName)) {\n        return;\n    }\n\n    if (nodeInfo._clipbox == null) {\n        return;\n    }\n\n    var opacity = parseFloat(getPropertyValue(style, \"opacity\"));\n    var visibility = getPropertyValue(style, \"visibility\");\n    var display = getPropertyValue(style, \"display\");\n\n    if (opacity === 0 || visibility == \"hidden\" || display == \"none\") {\n        return;\n    }\n\n    var tr = getTransform(style);\n    var group;\n\n    var zIndex = getPropertyValue(style, \"z-index\");\n    if ((tr || opacity < 1) && zIndex == \"auto\") {\n        zIndex = 0;\n    }\n    group = groupInStackingContext(element, container, zIndex);\n\n    // XXX: remove at some point\n    // group._pdfElement = element;\n    // group.options._pdfDebug = \"\";\n    // if (element.id) {\n    //     group.options._pdfDebug = \"#\" + element.id;\n    // }\n    // if (element.className) {\n    //     group.options._pdfDebug += \".\" + element.className.split(\" \").join(\".\");\n    // }\n\n    if (opacity < 1) {\n        group.opacity(opacity * group.opacity());\n    }\n\n    pushNodeInfo(element, style, group);\n\n    if (!tr) {\n        _renderWithPseudoElements(element, group);\n    }\n    else {\n        saveStyle(element, function(){\n            // must clear transform, so getBoundingClientRect returns correct values.\n            pleaseSetPropertyValue(element.style, \"transform\", \"none\", \"important\");\n\n            // must also clear transitions, so correct values are returned *immediately*\n            pleaseSetPropertyValue(element.style, \"transition\", \"none\", \"important\");\n\n            // the presence of any transform makes it behave like it had position: relative,\n            // because why not.\n            // http://meyerweb.com/eric/thoughts/2011/09/12/un-fixing-fixed-elements-with-css-transforms/\n            if (getPropertyValue(style, \"position\") == \"static\") {\n                // but only if it's not already positioned. :-/\n                pleaseSetPropertyValue(element.style, \"position\", \"relative\", \"important\");\n            }\n\n            // must translate to origin before applying the CSS\n            // transformation, then translate back.\n            var bbox = element.getBoundingClientRect();\n            var x = bbox.left + tr.origin[0];\n            var y = bbox.top + tr.origin[1];\n            var m = [ 1, 0, 0, 1, -x, -y ];\n            m = mmul(m, tr.matrix);\n            m = mmul(m, [ 1, 0, 0, 1, x, y ]);\n            m = setTransform(group, m);\n\n            nodeInfo._matrix = nodeInfo._matrix.multiplyCopy(m);\n\n            _renderWithPseudoElements(element, group);\n        });\n    }\n\n    popNodeInfo();\n\n    //drawDebugBox(element.getBoundingClientRect(), container);\n}\n\n// function drawDebugBox(box, group, color) {\n//     var path = Path.fromRect(new geo.Rect([ box.left, box.top ], [ box.width, box.height ]));\n//     if (color) {\n//         path.stroke(color);\n//     }\n//     group.append(path);\n// }\n\n// function dumpTextNode(node) {\n//     var txt = node.data.replace(/^\\s+/, \"\");\n//     if (txt.length < 100) {\n//         console.log(node.data.length + \": |\" + txt);\n//     } else {\n//         console.log(node.data.length + \": |\" + txt.substr(0, 50) + \"|...|\" + txt.substr(-50));\n//     }\n// }\n\nfunction mmul(a, b) {\n    var a1 = a[0], b1 = a[1], c1 = a[2], d1 = a[3], e1 = a[4], f1 = a[5];\n    var a2 = b[0], b2 = b[1], c2 = b[2], d2 = b[3], e2 = b[4], f2 = b[5];\n    return [\n        a1*a2 + b1*c2,          a1*b2 + b1*d2,\n        c1*a2 + d1*c2,          c1*b2 + d1*d2,\n        e1*a2 + f1*c2 + e2,     e1*b2 + f1*d2 + f2\n    ];\n}\n\nexport { drawDOM, drawText, getFontFaces };\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,GAAG,MAAM,aAAa;AAClC,OAAO,KAAKC,GAAG,MAAM,QAAQ;AAC7B,SAASC,aAAa,EAAEC,aAAa,EAAEC,WAAW,EAAEC,SAAS,QAAQ,SAAS;AAC9E,SAASC,UAAU,IAAIC,gBAAgB,EAAEC,OAAO,EAAEC,QAAQ,IAAIC,eAAe,QAAQ,WAAW;AAChG,SAASC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,cAAc,QAAQ,YAAY;AACnF,SAASC,YAAY,QAAQ,SAAS;AACtC,SAASC,YAAY,EAAEC,QAAQ,QAAQ,iCAAiC;AAExE,IAAIC,OAAO,GAAGb,OAAO,CAACa,OAAO,IAAI,CAAC,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA,SAASC,KAAKA,CAACC,KAAK,EAAE;EAClB,OAAOC,KAAK,CAACC,SAAS,CAACH,KAAK,CAACI,IAAI,CAACH,KAAK,CAAC;AAC5C;AAEA,IAAII,oBAAoB,GAAG,sBAAsB;AACjD,IAAIC,iBAAiB,GAAG,wBAAwB;AAEhD,IAAIC,WAAW,GAAG,CAAC,CAAC;AAEpB,IAAIC,QAAQ,GAAG,CAAC,CAAC;AACjBA,QAAQ,CAACC,KAAK,GAAGD,QAAQ;;AAEzB;;AAEA,IAAIE,SAAS,GAAG,OAAOC,MAAM,KAAK,WAAW;AAC7C,IAAIC,SAAS,GAAGF,SAAS,GAAGX,OAAO,CAACc,IAAI,IAAId,OAAO,CAACe,IAAI,GAAG,KAAK;AAEhE,IAAIC,QAAQ,GAAI,UAAUzB,IAAI,EAAE;EAC9B,SAASyB,QAAQA,CAACC,GAAG,EAAEC,IAAI,EAAEC,OAAO,EAAE;IAChC5B,IAAI,CAACc,IAAI,CAAC,IAAI,EAAEY,GAAG,EAAEC,IAAI,CAACE,SAAS,CAAC,CAAC,EAAED,OAAO,CAAC;IAC/C,IAAI,CAACE,QAAQ,GAAGH,IAAI;EACxB;EAEF,IAAK3B,IAAI,EAAGyB,QAAQ,CAACM,SAAS,GAAG/B,IAAI;EACrCyB,QAAQ,CAACZ,SAAS,GAAGmB,MAAM,CAACC,MAAM,CAAEjC,IAAI,IAAIA,IAAI,CAACa,SAAU,CAAC;EAC5DY,QAAQ,CAACZ,SAAS,CAACqB,WAAW,GAAGT,QAAQ;EACvCA,QAAQ,CAACZ,SAAS,CAACc,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAI;IACvC;IACA;IACA;IACA,OAAO,IAAI,CAACG,QAAQ;EACxB,CAAC;EACDL,QAAQ,CAACZ,SAAS,CAACsB,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;IAC7C;IACA,OAAO,IAAI,CAACL,QAAQ;EACxB,CAAC;EAEH,OAAOL,QAAQ;AACjB,CAAC,CAACzB,IAAI,CAAE;AAER,SAASoC,QAAQA,CAACC,EAAE,EAAEC,GAAG,EAAE;EACvB,IAAID,EAAE,CAACE,SAAS,EAAE;IACdF,EAAE,CAACE,SAAS,CAACC,GAAG,CAACF,GAAG,CAAC;EACzB,CAAC,MAAM;IACHD,EAAE,CAACI,SAAS,IAAI,GAAG,GAAGH,GAAG;EAC7B;AACJ;AAEA,SAASI,WAAWA,CAACL,EAAE,EAAEC,GAAG,EAAE;EAC1B,IAAID,EAAE,CAACE,SAAS,EAAE;IACdF,EAAE,CAACE,SAAS,CAACI,MAAM,CAACL,GAAG,CAAC;EAC5B,CAAC,MAAM;IACHD,EAAE,CAACI,SAAS,GAAGJ,EAAE,CAACI,SAAS,CAACG,KAAK,CAAC,KAAK,CAAC,CAACC,MAAM,CAAC,UAASC,CAAC,EAAEC,IAAI,EAAC;MAC7D,IAAIA,IAAI,IAAIT,GAAG,EAAE;QACbQ,CAAC,CAACE,IAAI,CAACD,IAAI,CAAC;MAChB;MACA,OAAOD,CAAC;IACZ,CAAC,EAAE,EAAE,CAAC,CAACG,IAAI,CAAC,GAAG,CAAC;EACpB;AACJ;AAEA,SAASC,MAAMA,CAACb,EAAE,EAAEc,MAAM,EAAE;EACxBnB,MAAM,CAACoB,IAAI,CAACD,MAAM,CAAC,CAACE,OAAO,CAAC,UAASC,GAAG,EAAC;IACrCjB,EAAE,CAACkB,KAAK,CAACD,GAAG,CAAC,GAAGH,MAAM,CAACG,GAAG,CAAC;EAC/B,CAAC,CAAC;AACN;AAEA,IAAIE,OAAO,GAAG,OAAOC,OAAO,KAAK,WAAW,IAAIA,OAAO,CAAC5C,SAAS,IAAK,UAAS6C,CAAC,EAAC;EAC7E,IAAIA,CAAC,CAACF,OAAO,EAAE;IACX,OAAO,UAASnB,EAAE,EAAEsB,QAAQ,EAAE;MAAE,OAAOtB,EAAE,CAACmB,OAAO,CAACG,QAAQ,CAAC;IAAE,CAAC;EAClE;EACA,IAAID,CAAC,CAACE,qBAAqB,EAAE;IACzB,OAAO,UAASvB,EAAE,EAAEsB,QAAQ,EAAE;MAAE,OAAOtB,EAAE,CAACuB,qBAAqB,CAACD,QAAQ,CAAC;IAAE,CAAC;EAChF;EACA,IAAID,CAAC,CAACG,kBAAkB,EAAE;IACtB,OAAO,UAASxB,EAAE,EAAEsB,QAAQ,EAAE;MAAE,OAAOtB,EAAE,CAACwB,kBAAkB,CAACF,QAAQ,CAAC;IAAE,CAAC;EAC7E;EACA,IAAID,CAAC,CAACI,iBAAiB,EAAE;IACrB,OAAO,UAASzB,EAAE,EAAEsB,QAAQ,EAAE;MAAE,OAAOtB,EAAE,CAACyB,iBAAiB,CAACH,QAAQ,CAAC;IAAE,CAAC;EAC5E;EACA,OAAO,UAASI,CAAC,EAAE;IACtB,OAAO,EAAE,CAACC,OAAO,CAAClD,IAAI,CAACmD,QAAQ,CAACC,gBAAgB,CAACH,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;EAC9D,CAAC;AACL,CAAC,CAAEN,OAAO,CAAC5C,SAAS,CAAC;AAErB,SAASsD,OAAOA,CAAC9B,EAAE,EAAEsB,QAAQ,EAAE;EAC3B,IAAItB,EAAE,CAAC8B,OAAO,EAAE;IACZ,OAAO9B,EAAE,CAAC8B,OAAO,CAACR,QAAQ,CAAC;EAC/B;EACA;EACA;EACA;EACA,OAAOtB,EAAE,IAAI,CAAC,gCAAgC,CAAC+B,IAAI,CAACC,MAAM,CAAChC,EAAE,CAAC,CAAC,EAAE;IAC7D,IAAIA,EAAE,CAACiC,QAAQ,IAAI,CAAC,CAAC,iBAAiBd,OAAO,CAACnB,EAAE,EAAEsB,QAAQ,CAAC,EAAE;MACzD,OAAOtB,EAAE;IACb;IACAA,EAAE,GAAGA,EAAE,CAACkC,UAAU;EACtB;AACJ;;AAEA;AACA;AACA,IAAIC,UAAU,GAAI,UAASC,CAAC,EAAC;EACzB,IAAIA,CAAC,EAAE;IACH;IACA;IACA;IACA;IACA;IACA,OAAO,SAASD,UAAUA,CAACnC,EAAE,EAAE;MAC3B,IAAIqC,KAAK,GAAGrC,EAAE,CAACsC,SAAS,CAAC,KAAK,CAAC;MAC/B,IAAItC,EAAE,CAACiC,QAAQ,IAAI,CAAC,CAAC,eAAe;QAChC,IAAIM,GAAG,GAAGH,CAAC,CAACpC,EAAE,CAAC;UAAEwC,MAAM,GAAGJ,CAAC,CAACC,KAAK,CAAC;UAAEI,CAAC;QACrC,IAAIC,IAAI,GAAGH,GAAG,CAACG,IAAI,CAAC,CAAC;QACrB,KAAKD,CAAC,IAAIC,IAAI,EAAE;UACZF,MAAM,CAACE,IAAI,CAACD,CAAC,EAAEC,IAAI,CAACD,CAAC,CAAC,CAAC;QAC3B;QACA,IAAI,WAAW,CAACV,IAAI,CAAC/B,EAAE,CAAC2C,OAAO,CAAC,EAAE;UAC9BN,KAAK,CAACO,UAAU,CAAC,IAAI,CAAC,CAACC,SAAS,CAAC7C,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;QAC9C,CAAC,MAAM,IAAI,qCAAqC,CAAC+B,IAAI,CAAC/B,EAAE,CAAC2C,OAAO,CAAC,EAAE;UAC/D;UACA;UACA;UACAN,KAAK,CAACS,eAAe,CAAC,IAAI,CAAC;UAC3BT,KAAK,CAACS,eAAe,CAAC,MAAM,CAAC;UAC7B,IAAI,CAAC,aAAa,CAACf,IAAI,CAAC/B,EAAE,CAAC2C,OAAO,CAAC,EAAE;YACjCN,KAAK,CAACU,KAAK,GAAG/C,EAAE,CAAC+C,KAAK;UAC1B;UACAV,KAAK,CAACW,OAAO,GAAGhD,EAAE,CAACgD,OAAO;UAC1BX,KAAK,CAACY,QAAQ,GAAGjD,EAAE,CAACiD,QAAQ;QAChC;QAEA,IAAIjD,EAAE,CAACkD,kBAAkB,EAAE;UACvBb,KAAK,CAACa,kBAAkB,GAAGlD,EAAE,CAACkD,kBAAkB;QACpD;QAEA,KAAKT,CAAC,GAAGzC,EAAE,CAACmD,UAAU,EAAEV,CAAC,EAAEA,CAAC,GAAGA,CAAC,CAACW,WAAW,EAAE;UAC1Cf,KAAK,CAACgB,WAAW,CAAClB,UAAU,CAACM,CAAC,CAAC,CAAC;QACpC;MACJ;MACA,OAAOJ,KAAK;IAChB,CAAC;EACL,CAAC,MAAM;IACH;IACA,OAAO,SAASF,UAAUA,CAACnC,EAAE,EAAE;MAC3B,IAAIqC,KAAK,GAAI,SAASiB,IAAIA,CAACC,IAAI,EAAC;QAC5B,IAAIlB,KAAK,GAAGkB,IAAI,CAACjB,SAAS,CAAC,KAAK,CAAC;QACjC,IAAIiB,IAAI,CAACL,kBAAkB,EAAE;UACzBb,KAAK,CAACa,kBAAkB,GAAGK,IAAI,CAACL,kBAAkB;QACtD;QACA,KAAK,IAAIT,CAAC,GAAGc,IAAI,CAACJ,UAAU,EAAEV,CAAC,EAAEA,CAAC,GAAGA,CAAC,CAACW,WAAW,EAAE;UAChDf,KAAK,CAACgB,WAAW,CAACC,IAAI,CAACb,CAAC,CAAC,CAAC;QAC9B;QACA,OAAOJ,KAAK;MAChB,CAAC,CAAErC,EAAE,CAAC;;MAEN;MACA,IAAIwD,QAAQ,GAAGxD,EAAE,CAAC6B,gBAAgB,CAAC,QAAQ,CAAC;MAC5C,IAAI2B,QAAQ,CAACC,MAAM,EAAE;QACjBpF,KAAK,CAACgE,KAAK,CAACR,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAACb,OAAO,CAAC,UAAU0C,MAAM,EAAEjB,CAAC,EAAE;UACjEiB,MAAM,CAACd,UAAU,CAAC,IAAI,CAAC,CAACC,SAAS,CAACW,QAAQ,CAACf,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxD,CAAC,CAAC;MACN;;MAEA;MACA;MACA,IAAIkB,IAAI,GAAG3D,EAAE,CAAC6B,gBAAgB,CAAC,iCAAiC,CAAC;MACjExD,KAAK,CAACgE,KAAK,CAACR,gBAAgB,CAAC,iCAAiC,CAAC,CAAC,CAACb,OAAO,CAAC,UAAUhB,EAAE,EAAEyC,CAAC,EAAE;QACtFzC,EAAE,CAAC8C,eAAe,CAAC,IAAI,CAAC;QACxB9C,EAAE,CAAC8C,eAAe,CAAC,MAAM,CAAC;QAC1B,IAAI,CAAC,aAAa,CAACf,IAAI,CAAC/B,EAAE,CAAC2C,OAAO,CAAC,EAAE;UACjC3C,EAAE,CAAC+C,KAAK,GAAGY,IAAI,CAAClB,CAAC,CAAC,CAACM,KAAK;QAC5B;QACA/C,EAAE,CAACgD,OAAO,GAAGW,IAAI,CAAClB,CAAC,CAAC,CAACO,OAAO;QAC5BhD,EAAE,CAACiD,QAAQ,GAAGU,IAAI,CAAClB,CAAC,CAAC,CAACQ,QAAQ;MAClC,CAAC,CAAC;MAEF,OAAOZ,KAAK;IAChB,CAAC;EACL;AACJ,CAAC,CAAE,OAAOrD,MAAM,KAAK,WAAW,IAAIA,MAAM,CAAC4E,KAAK,IAAI5E,MAAM,CAAC4E,KAAK,CAACC,MAAM,CAAC;AAExE,SAASC,KAAKA,CAACxF,KAAK,EAAE;EAClB,IAAI,OAAOA,KAAK,IAAI,QAAQ,EAAE;IAC1B,OAAO;MAAEyF,CAAC,EAAEzF,KAAK;MAAE0F,CAAC,EAAE1F;IAAM,CAAC;EACjC;EACA,IAAIC,KAAK,CAAC0F,OAAO,CAAC3F,KAAK,CAAC,EAAE;IACtB,OAAO;MAAEyF,CAAC,EAAEzF,KAAK,CAAC,CAAC,CAAC;MAAE0F,CAAC,EAAE1F,KAAK,CAAC,CAAC;IAAE,CAAC;EACvC;EACA,OAAO;IAAEyF,CAAC,EAAEzF,KAAK,CAACyF,CAAC;IAAEC,CAAC,EAAE1F,KAAK,CAAC0F;EAAE,CAAC;AACrC;AAEA,SAASE,OAAOA,CAACC,OAAO,EAAE5E,OAAO,EAAE;EAC/B,IAAI,CAACA,OAAO,EAAE;IACVA,OAAO,GAAG,CAAC,CAAC;EAChB;EACA,IAAI6E,OAAO,GAAGlH,aAAa,CAAC,CAAC;EAE7B,IAAI,CAACiH,OAAO,EAAE;IACV,OAAOC,OAAO,CAACC,MAAM,CAAC,sBAAsB,CAAC;EACjD;EAEA,IAAI,OAAOrF,MAAM,CAACsF,gBAAgB,IAAI,UAAU,EAAE;IAC9C,MAAM,IAAIC,KAAK,CAAC,kLAAkL,CAAC;EACvM;EAEAvH,GAAG,CAACwH,UAAU,CAACC,YAAY,CAACN,OAAO,CAACO,aAAa,CAAC,CAAC;EAEnD,IAAIC,KAAK,GAAGb,KAAK,CAACvE,OAAO,CAACoF,KAAK,IAAI,CAAC,CAAC;EAErC,SAASC,KAAKA,CAACT,OAAO,EAAE;IACpB,IAAIU,KAAK,GAAG,IAAIjH,KAAK,CAAC,CAAC;;IAEvB;IACA,IAAIkH,GAAG,GAAGX,OAAO,CAACY,qBAAqB,CAAC,CAAC;IACzCC,YAAY,CAACH,KAAK,EAAE,CAChBF,KAAK,CAACZ,CAAC,EACP,CAAC,EACD,CAAC,EACDY,KAAK,CAACX,CAAC,EACN,CAACc,GAAG,CAACG,IAAI,GAAGN,KAAK,CAACZ,CAAC,EACnB,CAACe,GAAG,CAACI,GAAG,GAAGP,KAAK,CAACX,CAAC,CACtB,CAAC;IAEFnF,QAAQ,CAACsG,QAAQ,GAAG,KAAK;IACzBtG,QAAQ,CAACuG,OAAO,GAAGrI,GAAG,CAACsI,MAAM,CAACC,IAAI,CAAC,CAAC;IACpCzG,QAAQ,CAAC0G,gBAAgB,GAAG;MACxBpB,OAAO,EAAEA,OAAO;MAChBU,KAAK,EAAEA;IACX,CAAC;IAED,IAAItF,OAAO,CAACiG,UAAU,KAAK,IAAI,EAAE;MAC7B3G,QAAQ,CAAC4G,WAAW,GAAG,GAAG;IAC9B,CAAC,MAAM;MACH5G,QAAQ,CAAC4G,WAAW,GAAGlG,OAAO,CAACiG,UAAU;IAC7C;IAEAzF,QAAQ,CAACoE,OAAO,EAAE,cAAc,CAAC;IACjCuB,aAAa,CAACvB,OAAO,EAAEU,KAAK,CAAC;IAC7BxE,WAAW,CAAC8D,OAAO,EAAE,cAAc,CAAC;IAEpC,OAAOU,KAAK;EAChB;EAEAc,WAAW,CAAC,CAAExB,OAAO,CAAE,EAAE,YAAU;IAC/B,IAAIyB,UAAU,GAAGrG,OAAO,IAAIA,OAAO,CAACsG,cAAc;IAClD,IAAIC,YAAY,GAAGvG,OAAO,IAAIA,OAAO,CAACwG,SAAS,IAAIxG,OAAO,CAACwG,SAAS,IAAI,MAAM;IAC9E,IAAIC,YAAY,GAAGhJ,GAAG,CAACiJ,eAAe,CAAC,UAAShF,GAAG,EAAEiF,GAAG,EAAC;MACrD,IAAIjF,GAAG,IAAI,WAAW,EAAE;QACpB;QACA;QACA,OAAO6E,YAAY,GAAGvG,OAAO,CAAC0B,GAAG,CAAC,GAAG,IAAI;MAC7C;MACA,OAAOA,GAAG,IAAI1B,OAAO,GAAGA,OAAO,CAAC0B,GAAG,CAAC,GAAGiF,GAAG;IAC9C,CAAC,CAAC;IACF,IAAIC,SAAS,GAAGL,YAAY,IAAIE,YAAY,CAACD,SAAS,CAAC,CAAC,CAAC;IACzD,IAAIK,UAAU,GAAGN,YAAY,IAAIE,YAAY,CAACD,SAAS,CAAC,CAAC,CAAC;IAC1D,IAAIM,MAAM,GAAG9G,OAAO,CAAC8G,MAAM,IAAIL,YAAY,CAACK,MAAM;IAClD,IAAIC,SAAS,GAAGC,OAAO,CAACF,MAAM,CAAC;IAC/B,IAAIT,UAAU,IAAIQ,UAAU,EAAE;MAC1B,IAAI,CAACC,MAAM,EAAE;QACTA,MAAM,GAAG;UAAEpB,IAAI,EAAE,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEsB,KAAK,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAE,CAAC;MACrD;;MAEA;MACA;MACA;MACA,IAAIN,SAAS,EAAG;QAAEA,SAAS,IAAKxB,KAAK,CAACZ,CAAC;MAAE;MACzC,IAAIqC,UAAU,EAAE;QAAEA,UAAU,IAAIzB,KAAK,CAACX,CAAC;MAAE;MACzCqC,MAAM,CAACpB,IAAI,IAAMN,KAAK,CAACZ,CAAC;MACxBsC,MAAM,CAACG,KAAK,IAAK7B,KAAK,CAACZ,CAAC;MACxBsC,MAAM,CAACnB,GAAG,IAAOP,KAAK,CAACX,CAAC;MACxBqC,MAAM,CAACI,MAAM,IAAI9B,KAAK,CAACX,CAAC;MAExB,IAAIa,KAAK,GAAG,IAAIjH,KAAK,CAAC;QAClB8I,GAAG,EAAE;UACDC,SAAS,EAAO,IAAI;UACpBZ,SAAS,EAAOD,YAAY,GAAGE,YAAY,CAACD,SAAS,GAAG,MAAM;UAC9Da,aAAa,EAAGN,SAAS,CAAC;QAC9B;MACJ,CAAC,CAAC;MACFO,gBAAgB,CACZ,UAAS9C,CAAC,EAAE;QACR,IAAIxE,OAAO,CAACuH,QAAQ,EAAE;UAClB,IAAIC,QAAQ,GAAG,KAAK;YAAEC,OAAO,GAAG,CAAC;UACjC,CAAC,SAASC,IAAIA,CAAA,EAAE;YACZ,IAAID,OAAO,GAAGjD,CAAC,CAACmD,KAAK,CAACzD,MAAM,EAAE;cAC1B,IAAI0D,IAAI,GAAGvC,KAAK,CAACb,CAAC,CAACmD,KAAK,CAACF,OAAO,CAAC,CAAC;cAClCnC,KAAK,CAACuC,MAAM,CAACD,IAAI,CAAC;cAClB5H,OAAO,CAACuH,QAAQ,CAAC;gBACbK,IAAI,EAAEA,IAAI;gBACVH,OAAO,EAAE,EAAEA,OAAO;gBAClBK,UAAU,EAAEtD,CAAC,CAACmD,KAAK,CAACzD,MAAM;gBAC1B6D,MAAM,EAAE,SAAAA,CAAA,EAAW;kBACfP,QAAQ,GAAG,IAAI;gBACnB;cACJ,CAAC,CAAC;cACF,IAAI,CAACA,QAAQ,EAAE;gBACXQ,UAAU,CAACN,IAAI,CAAC;cACpB,CAAC,MAAM;gBACH;gBACAlD,CAAC,CAACyD,SAAS,CAACtF,UAAU,CAACuF,WAAW,CAAC1D,CAAC,CAACyD,SAAS,CAAC;cACnD;YACJ,CAAC,MAAM;cACHzD,CAAC,CAACyD,SAAS,CAACtF,UAAU,CAACuF,WAAW,CAAC1D,CAAC,CAACyD,SAAS,CAAC;cAC/CpD,OAAO,CAACsD,OAAO,CAAC7C,KAAK,CAAC;YAC1B;UACJ,CAAC,EAAE,CAAC;QACR,CAAC,MAAM;UACHd,CAAC,CAACmD,KAAK,CAAClG,OAAO,CAAC,UAASmG,IAAI,EAAC;YAC1BtC,KAAK,CAACuC,MAAM,CAACxC,KAAK,CAACuC,IAAI,CAAC,CAAC;UAC7B,CAAC,CAAC;UACFpD,CAAC,CAACyD,SAAS,CAACtF,UAAU,CAACuF,WAAW,CAAC1D,CAAC,CAACyD,SAAS,CAAC;UAC/CpD,OAAO,CAACsD,OAAO,CAAC7C,KAAK,CAAC;QAC1B;MACJ,CAAC,EACDV,OAAO,EACPyB,UAAU,EACVO,SAAS,GAAGA,SAAS,GAAGE,MAAM,CAACpB,IAAI,GAAGoB,MAAM,CAACG,KAAK,GAAG,IAAI,EACzDJ,UAAU,GAAGA,UAAU,GAAGC,MAAM,CAACnB,GAAG,GAAGmB,MAAM,CAACI,MAAM,GAAG,IAAI,EAC3DJ,MAAM,EACN9G,OACJ,CAAC;IACL,CAAC,MAAM;MACH6E,OAAO,CAACsD,OAAO,CAAC9C,KAAK,CAACT,OAAO,CAAC,CAAC;IACnC;EACJ,CAAC,CAAC;EAEF,SAASwD,YAAYA,CAACnK,QAAQ,EAAE;IAC5B,IAAIA,QAAQ,IAAI,IAAI,EAAE;MAClB,IAAI,OAAOA,QAAQ,IAAI,QAAQ,EAAE;QAC7BA,QAAQ,GAAGC,eAAe,CAACD,QAAQ,CAACoK,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;MAClE;MACA,IAAI,OAAOpK,QAAQ,IAAI,UAAU,EAAE;QAC/B,OAAO,UAASkF,IAAI,EAAE;UAClB,IAAI1C,EAAE,GAAGxC,QAAQ,CAACkF,IAAI,CAAC;UACvB,IAAI1C,EAAE,IAAI,OAAOA,EAAE,IAAI,QAAQ,EAAE;YAC7B,IAAI6H,GAAG,GAAGjG,QAAQ,CAACkG,aAAa,CAAC,KAAK,CAAC;YACvC5J,YAAY,CAAC2J,GAAG,EAAE7H,EAAE,CAAC;YACrBA,EAAE,GAAG6H,GAAG,CAACE,iBAAiB;UAC9B;UACA,OAAO/H,EAAE;QACb,CAAC;MACL;MACA;MACA,OAAO,YAAW;QACd,OAAOxC,QAAQ,CAAC8E,SAAS,CAAC,IAAI,CAAC;MACnC,CAAC;IACL;EACJ;EAEA,SAASuE,gBAAgBA,CAACmB,QAAQ,EAAE7D,OAAO,EAAEyB,UAAU,EAAEO,SAAS,EAAEC,UAAU,EAAEC,MAAM,EAAE9G,OAAO,EAAE;IAC7F,IAAI/B,QAAQ,GAAGmK,YAAY,CAACpI,OAAO,CAAC/B,QAAQ,CAAC;IAC7C,IAAIyK,GAAG,GAAG9D,OAAO,CAACO,aAAa;IAC/B,IAAIwC,KAAK,GAAG,EAAE;IACd,IAAIgB,IAAI,GAAG3I,OAAO,CAAC4I,YAAY,GAAGhE,OAAO,GAAGhC,UAAU,CAACgC,OAAO,CAAC;IAC/D,IAAIqD,SAAS,GAAGS,GAAG,CAACH,aAAa,CAAC,oBAAoB,CAAC;IACvD,IAAIM,MAAM,GAAG,CAAC;;IAEd;IACA;IACA;IACA;IACA/J,KAAK,CAAC6J,IAAI,CAACrG,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAACb,OAAO,CAAC,UAASqH,KAAK,EAAC;MACzDA,KAAK,CAACnG,UAAU,CAACmB,WAAW,CAACgF,KAAK,CAAC;IACvC,CAAC,CAAC;;IAEF;IACA;IACAhK,KAAK,CAAC6J,IAAI,CAACrG,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAACb,OAAO,CAAC,UAASsH,EAAE,EAAC;MACnDjK,KAAK,CAACiK,EAAE,CAACC,QAAQ,CAAC,CAACvH,OAAO,CAAC,UAASwH,EAAE,EAAEC,KAAK,EAAC;QAC1CD,EAAE,CAACE,YAAY,CAAC,mBAAmB,EAAED,KAAK,CAAC;MAC/C,CAAC,CAAC;IACN,CAAC,CAAC;IAEF5H,MAAM,CAAC2G,SAAS,EAAE;MACdmB,OAAO,EAAK,OAAO;MACnBC,QAAQ,EAAI,UAAU;MACtBC,SAAS,EAAG,aAAa;MACzB5D,IAAI,EAAQ,UAAU;MACtBC,GAAG,EAAS;IAChB,CAAC,CAAC;IAEF,IAAIiB,SAAS,EAAE;MACX;MACA;MACA;MACAtF,MAAM,CAAC2G,SAAS,EAAE;QACdsB,KAAK,EAAU3C,SAAS,GAAG,IAAI;QAC/B4C,WAAW,EAAI1C,MAAM,CAACpB,IAAI,GAAG,IAAI;QACjC+D,YAAY,EAAG3C,MAAM,CAACG,KAAK,GAAG;MAClC,CAAC,CAAC;;MAEF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA3F,MAAM,CAACqH,IAAI,EAAE;QAAEe,QAAQ,EAAE;MAAS,CAAC,CAAC;IACxC;IAEA9E,OAAO,CAACjC,UAAU,CAACgH,YAAY,CAAC1B,SAAS,EAAErD,OAAO,CAAC;IACnDqD,SAAS,CAACnE,WAAW,CAAC6E,IAAI,CAAC;;IAE3B;IACA;IACA,IAAI3I,OAAO,CAAC4J,eAAe,EAAE;MACzBC,2BAA2B,CAAC,CAAE5B,SAAS,CAAE,EAAE,YAAW;QAClDjI,OAAO,CAAC4J,eAAe,CAAC3B,SAAS,EAAE6B,WAAW,CAAC;MACnD,CAAC,CAAC;IACN,CAAC,MAAM;MACHD,2BAA2B,CAAC,CAAE5B,SAAS,CAAE,EAAE6B,WAAW,CAAC;IAC3D;IAEA,SAASA,WAAWA,CAAA,EAAG;MACnB,IAAIzD,UAAU,IAAI,GAAG,IAAIQ,UAAU,EAAE;QACjCkD,YAAY,CAACpB,IAAI,CAAC;MACtB;MAEA;QACI,IAAIf,IAAI,GAAGoC,QAAQ,CAAC,CAAC;QACrBrB,IAAI,CAAChG,UAAU,CAACgH,YAAY,CAAC/B,IAAI,EAAEe,IAAI,CAAC;QACxCf,IAAI,CAAC9D,WAAW,CAAC6E,IAAI,CAAC;MAC1B;MAEA,IAAI1K,QAAQ,EAAE;QACV0J,KAAK,CAAClG,OAAO,CAAC,UAASmG,IAAI,EAAE1E,CAAC,EAAC;UAC3B,IAAIzC,EAAE,GAAGxC,QAAQ,CAAC;YACd2G,OAAO,EAAMgD,IAAI;YACjBH,OAAO,EAAMvE,CAAC,GAAG,CAAC;YAClB4E,UAAU,EAAGH,KAAK,CAACzD;UACvB,CAAC,CAAC;UACF,IAAIzD,EAAE,EAAE;YACJmH,IAAI,CAAC9D,WAAW,CAACrD,EAAE,CAAC;UACxB;QACJ,CAAC,CAAC;MACN;MAEA2F,WAAW,CAACuB,KAAK,EAAEc,QAAQ,CAACwB,IAAI,CAAC,IAAI,EAAE;QAAEtC,KAAK,EAAEA,KAAK;QAAEM,SAAS,EAAEA;MAAU,CAAC,CAAC,CAAC;IACnF;IAEA,SAASiC,YAAYA,CAACzJ,EAAE,EAAE;MACtB,IAAIT,OAAO,CAACkK,YAAY,IAAItI,OAAO,CAACnB,EAAE,EAAET,OAAO,CAACkK,YAAY,CAAC,IAAIzJ,EAAE,CAAC0J,YAAY,IAAItD,UAAU,GAAGgC,MAAM,EAAE;QACrG,OAAO,IAAI;MACf;MAEA,IAAIuB,GAAG,GAAG3J,EAAE,CAAC2C,OAAO;MACpB,IAAI,WAAW,CAACZ,IAAI,CAAC4H,GAAG,CAAC,IAAI3J,EAAE,CAAC0J,YAAY,IAAItD,UAAU,GAAGgC,MAAM,EAAE;QACjE,OAAO,KAAK;MAChB;MAEA,OAAQpI,EAAE,CAAC4J,YAAY,CAAC,kBAAkB,CAAC,IACnC,yFAAyF,CAAC7H,IAAI,CAAC/B,EAAE,CAAC2C,OAAO,CAAC;IACtH;IAEA,SAAS2G,YAAYA,CAACnF,OAAO,EAAE;MAC3B,IAAIA,OAAO,CAACxB,OAAO,IAAI,OAAO,EAAE;QAC5B9B,MAAM,CAACsD,OAAO,EAAE;UAAE0F,WAAW,EAAE;QAAQ,CAAC,CAAC;MAC7C;MACA,IAAIJ,YAAY,CAACtF,OAAO,CAAC,EAAE;QACvB;MACJ;MACA,IAAIjD,KAAK,GAAGoD,gBAAgB,CAACH,OAAO,CAAC;MACrC,IAAI2F,aAAa,GAAGC,UAAU,CAACC,gBAAgB,CAAC9I,KAAK,EAAE,gBAAgB,CAAC,CAAC;MACzE,IAAI+I,YAAY,GAAGF,UAAU,CAACC,gBAAgB,CAAC9I,KAAK,EAAE,qBAAqB,CAAC,CAAC;MAC7E,IAAIgJ,UAAU,GAAG9B,MAAM;MACvBA,MAAM,IAAI0B,aAAa,GAAGG,YAAY;MACtC,IAAIE,OAAO,GAAG,IAAI;MAClB,KAAK,IAAInK,EAAE,GAAGmE,OAAO,CAAChB,UAAU,EAAEnD,EAAE,EAAEA,EAAE,GAAGA,EAAE,CAACoD,WAAW,EAAE;QACvD,IAAIpD,EAAE,CAACiC,QAAQ,IAAI,CAAC,CAAC,eAAe;UAChCkI,OAAO,GAAG,KAAK;UACf,IAAIhJ,OAAO,CAACnB,EAAE,EAAE4F,UAAU,CAAC,EAAE;YACzBwE,cAAc,CAACpK,EAAE,CAAC;YAClB;UACJ;UACA,IAAI,CAACoG,UAAU,EAAE;YACb;YACAkD,YAAY,CAACtJ,EAAE,CAAC;YAChB;UACJ;UACA,IAAI,CAAC,uBAAuB,CAAC+B,IAAI,CAACiI,gBAAgB,CAAC1F,gBAAgB,CAACtE,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE;YACnF;UACJ;UACA,IAAIqK,IAAI,GAAGC,aAAa,CAACtK,EAAE,CAAC;UAC5B,IAAIqK,IAAI,IAAI,CAAC,EAAE;YACX;YACAD,cAAc,CAACpK,EAAE,CAAC;UACtB,CAAC,MACI,IAAIqK,IAAI,EAAE;YACX;YACA;YACA;YACA,IAAIZ,YAAY,CAACzJ,EAAE,CAAC,EAAE;cAClBoK,cAAc,CAACpK,EAAE,CAAC;YACtB,CAAC,MAAM;cACHsJ,YAAY,CAACtJ,EAAE,CAAC;YACpB;UACJ,CAAC,MACI;YACDsJ,YAAY,CAACtJ,EAAE,CAAC;UACpB;QACJ,CAAC,MACI,IAAIA,EAAE,CAACiC,QAAQ,IAAI,CAAC,CAAC,cAAcmE,UAAU,EAAE;UAChDmE,SAAS,CAACvK,EAAE,EAAEmK,OAAO,CAAC;UACtBA,OAAO,GAAG,KAAK;QACnB;MACJ;MACA/B,MAAM,GAAG8B,UAAU;IACvB;IAEA,SAASM,aAAaA,CAACxK,EAAE,EAAE;MACvB,IAAIqB,CAAC,GAAGrB,EAAE,CAACkC,UAAU;QAAEuI,KAAK,GAAGpJ,CAAC,CAAC8B,UAAU;MAC3C,IAAInD,EAAE,KAAKyK,KAAK,EAAE;QACd,OAAO,IAAI;MACf;MACA,IAAIzK,EAAE,KAAKqB,CAAC,CAACkH,QAAQ,CAAC,CAAC,CAAC,EAAE;QACtB,IAAIkC,KAAK,CAACxI,QAAQ,IAAI,CAAC,CAAC,iBACpBwI,KAAK,CAACxI,QAAQ,IAAI,CAAC,CAAC,8BAA8B;UAClD,OAAO,IAAI;QACf;QACA,IAAIwI,KAAK,CAACxI,QAAQ,IAAI,CAAC,CAAC,YAAY;UAChC;UACA,OAAO,CAAC,IAAI,CAACF,IAAI,CAAC0I,KAAK,CAAC/H,IAAI,CAAC;QACjC;MACJ;MACA,OAAO,KAAK;IAChB;IAEA,SAAS0H,cAAcA,CAACpK,EAAE,EAAE;MACxB,IAAIA,EAAE,CAACiC,QAAQ,IAAI,CAAC,IAAIjC,EAAE,KAAKkI,IAAI,IAAIsC,aAAa,CAACxK,EAAE,CAAC,EAAE;QACtD,OAAOoK,cAAc,CAACpK,EAAE,CAACkC,UAAU,CAAC;MACxC;MACA,IAAIwI,KAAK,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAEC,QAAQ;MAC1CJ,KAAK,GAAG5I,OAAO,CAAC9B,EAAE,EAAE,OAAO,CAAC;MAC5B2K,QAAQ,GAAGD,KAAK,IAAIA,KAAK,CAACK,aAAa,CAAC,UAAU,CAAC;MACnD,IAAIxL,OAAO,CAACyL,aAAa,EAAE;QACvBJ,KAAK,GAAGF,KAAK,IAAIA,KAAK,CAACK,aAAa,CAAC,OAAO,CAAC;;QAE7C;QACA;QACA;QACA;QACA;QACAF,IAAI,GAAG/I,OAAO,CAAC9B,EAAE,EAAE,SAAS,CAAC;QAC7B,IAAI6K,IAAI,IAAIA,IAAI,CAACE,aAAa,CAAC,oBAAoB,CAAC,EAAE;UAClDD,QAAQ,GAAGD,IAAI,CAACE,aAAa,CAAC,gBAAgB,CAAC;QACnD;MACJ;MACA,IAAI5D,IAAI,GAAGoC,QAAQ,CAAC,CAAC;MACrB,IAAI0B,KAAK,GAAGhD,GAAG,CAACiD,WAAW,CAAC,CAAC;MAC7BD,KAAK,CAACE,cAAc,CAACjD,IAAI,CAAC;MAC1B+C,KAAK,CAACG,YAAY,CAACpL,EAAE,CAAC;MACtBmH,IAAI,CAAC9D,WAAW,CAAC4H,KAAK,CAACI,eAAe,CAAC,CAAC,CAAC;MACzCnD,IAAI,CAAChG,UAAU,CAACgH,YAAY,CAAC/B,IAAI,EAAEe,IAAI,CAAC;MACxCoD,uBAAuB,CAACtL,EAAE,CAACkC,UAAU,CAAC;MACtC,IAAIwI,KAAK,EAAE;QACPA,KAAK,GAAG5I,OAAO,CAAC9B,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC;QAC9B,IAAIT,OAAO,CAACyL,aAAa,IAAIJ,KAAK,EAAE;UAChCF,KAAK,CAACxB,YAAY,CAAC0B,KAAK,CAACtI,SAAS,CAAC,IAAI,CAAC,EAAEoI,KAAK,CAACvH,UAAU,CAAC;QAC/D;QACA,IAAIwH,QAAQ,EAAE;UACVD,KAAK,CAACxB,YAAY,CAACyB,QAAQ,CAACrI,SAAS,CAAC,IAAI,CAAC,EAAEoI,KAAK,CAACvH,UAAU,CAAC;QAClE;MACJ;MACA,IAAI5D,OAAO,CAACyL,aAAa,IAAIF,QAAQ,EAAE;QACnCD,IAAI,GAAG/I,OAAO,CAAC9B,EAAE,EAAE,SAAS,CAAC;QAC7B6K,IAAI,CAAC3B,YAAY,CAAC4B,QAAQ,CAACxI,SAAS,CAAC,IAAI,CAAC,EAAEuI,IAAI,CAAC1H,UAAU,CAAC;MAChE;IACJ;IAEA,SAASoG,QAAQA,CAAA,EAAG;MAChB,IAAIpC,IAAI,GAAGc,GAAG,CAACH,aAAa,CAAC,gBAAgB,CAAC;MAC9CjH,MAAM,CAACsG,IAAI,EAAE;QACTwB,OAAO,EAAI,OAAO;QAClBE,SAAS,EAAE,aAAa;QACxBC,KAAK,EAAM3C,SAAS,GAAIA,SAAS,GAAG,IAAI,GAAI,MAAM;QAClDoF,OAAO,EAAKlF,MAAM,CAACnB,GAAG,GAAG,KAAK,GAClBmB,MAAM,CAACG,KAAK,GAAG,KAAK,GACpBH,MAAM,CAACI,MAAM,GAAG,KAAK,GACrBJ,MAAM,CAACpB,IAAI,GAAG,IAAK;QAE/B;QACA2D,QAAQ,EAAG,UAAU;QAErB;QACA4C,MAAM,EAAKpF,UAAU,GAAIA,UAAU,GAAG,IAAI,GAAI,MAAM;QACpD6C,QAAQ,EAAG7C,UAAU,IAAID,SAAS,GAAG,QAAQ,GAAG,SAAS;QACzDsF,KAAK,EAAM;MACf,CAAC,CAAC;;MAEF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA,IAAIlM,OAAO,IAAIA,OAAO,CAACmM,aAAa,EAAE;QAClCvE,IAAI,CAAC/G,SAAS,GAAGb,OAAO,CAACmM,aAAa;MAC1C;MACAxE,KAAK,CAACvG,IAAI,CAACwG,IAAI,CAAC;MAChB,OAAOA,IAAI;IACf;IAEA,SAASmD,aAAaA,CAAChM,KAAK,EAAE;MAC1B,IAAIqN,GAAG,GAAGrN,KAAK,CAACyG,qBAAqB,CAAC,CAAC;MACvC,IAAI4G,GAAG,CAAC7C,KAAK,KAAK,CAAC,IAAI6C,GAAG,CAACH,MAAM,KAAK,CAAC,EAAE;QACrC;QACA,OAAO,CAAC;MACZ;MACA,IAAItG,GAAG,GAAGgD,IAAI,CAACnD,qBAAqB,CAAC,CAAC,CAACG,GAAG;MAC1C,IAAI0G,SAAS,GAAGxF,UAAU,GAAGgC,MAAM;MACnC,OAAQuD,GAAG,CAACH,MAAM,GAAGI,SAAS,GAAI,CAAC,GAC5BD,GAAG,CAACzG,GAAG,GAAGA,GAAG,GAAG0G,SAAS,GAAI,CAAC,GAC9BD,GAAG,CAAClF,MAAM,GAAGvB,GAAG,GAAG0G,SAAS,GAAI,CAAC,GAClC,CAAC;IACX;IAEA,SAASrB,SAASA,CAAChH,IAAI,EAAE4G,OAAO,EAAE;MAC9B,IAAI,CAAC,IAAI,CAACpI,IAAI,CAACwB,IAAI,CAACb,IAAI,CAAC,EAAE;QACvB;MACJ;MAEA,IAAImJ,GAAG,GAAGtI,IAAI,CAACb,IAAI,CAACe,MAAM;MAC1B,IAAIwH,KAAK,GAAGhD,GAAG,CAACiD,WAAW,CAAC,CAAC;MAC7BD,KAAK,CAACa,kBAAkB,CAACvI,IAAI,CAAC;MAC9B,IAAI8G,IAAI,GAAGC,aAAa,CAACW,KAAK,CAAC;MAC/B,IAAI,CAACZ,IAAI,EAAE;QACP,OAAO,CAAK;MAChB;MAEA,IAAI0B,QAAQ,GAAGxI,IAAI;MACnB,IAAI8G,IAAI,IAAI,CAAC,EAAE;QACX;QACA,IAAIF,OAAO,EAAE;UACT;UACAC,cAAc,CAAC7G,IAAI,CAACrB,UAAU,CAAC;QACnC,CAAC,MAAM;UACHkI,cAAc,CAAC7G,IAAI,CAAC;QACxB;MACJ,CAAC,MACI;QACD,CAAC,SAASyI,OAAOA,CAACC,GAAG,EAAEnH,GAAG,EAAEoH,GAAG,EAAE;UAC7BjB,KAAK,CAACkB,MAAM,CAAC5I,IAAI,EAAEuB,GAAG,CAAC;UACvB,IAAImH,GAAG,IAAInH,GAAG,IAAIA,GAAG,IAAIoH,GAAG,EAAE;YAC1B,OAAOpH,GAAG;UACd;UACA,IAAIwF,aAAa,CAACW,KAAK,CAAC,EAAE;YACtB,OAAOe,OAAO,CAACC,GAAG,EAAGA,GAAG,GAAGnH,GAAG,IAAK,CAAC,EAAEA,GAAG,CAAC;UAC9C,CAAC,MAAM;YACH,OAAOkH,OAAO,CAAClH,GAAG,EAAGA,GAAG,GAAGoH,GAAG,IAAK,CAAC,EAAEA,GAAG,CAAC;UAC9C;QACJ,CAAC,EAAE,CAAC,EAAEL,GAAG,IAAI,CAAC,EAAEA,GAAG,CAAC;QAEpB,IAAI,CAAC,IAAI,CAAC9J,IAAI,CAACkJ,KAAK,CAACmB,QAAQ,CAAC,CAAC,CAAC,IAAIjC,OAAO,EAAE;UACzC;UACAC,cAAc,CAAC7G,IAAI,CAACrB,UAAU,CAAC;QACnC,CAAC,MAAM;UACH;UACA;UACA6J,QAAQ,GAAGxI,IAAI,CAACgH,SAAS,CAACU,KAAK,CAACoB,SAAS,CAAC;UAE1C,IAAIlF,IAAI,GAAGoC,QAAQ,CAAC,CAAC;UACrB0B,KAAK,CAACE,cAAc,CAACjD,IAAI,CAAC;UAC1Bf,IAAI,CAAC9D,WAAW,CAAC4H,KAAK,CAACI,eAAe,CAAC,CAAC,CAAC;UACzCnD,IAAI,CAAChG,UAAU,CAACgH,YAAY,CAAC/B,IAAI,EAAEe,IAAI,CAAC;UACxCoD,uBAAuB,CAACS,QAAQ,CAAC7J,UAAU,CAAC;QAChD;MACJ;MAEAqI,SAAS,CAACwB,QAAQ,CAAC;IACvB;IAEA,SAAST,uBAAuBA,CAACtL,EAAE,EAAE;MACjC;MACA;MACA;MACA,IAAIwI,EAAE,GAAG1G,OAAO,CAAC9B,EAAE,EAAE,IAAI,CAAC;MAC1B,IAAIwI,EAAE,EAAE;QACJA,EAAE,CAACE,YAAY,CAAC,iBAAiB,EAAE,GAAG,CAAC;QACvC4C,uBAAuB,CAAC9C,EAAE,CAACtG,UAAU,CAAC;MAC1C;IACJ;EACJ;EAEA,OAAOkC,OAAO;AAClB;;AAEA;AACA;AACA;AACA,SAASkI,QAAQA,CAACnI,OAAO,EAAE;EACvB,IAAIU,KAAK,GAAG,IAAIjH,KAAK,CAAC,CAAC;EACvBiB,QAAQ,CAACsG,QAAQ,GAAG,KAAK;EACzBtG,QAAQ,CAACuG,OAAO,GAAGrI,GAAG,CAACsI,MAAM,CAACC,IAAI,CAAC,CAAC;EACpCzG,QAAQ,CAAC0G,gBAAgB,GAAG;IACxBpB,OAAO,EAAEA,OAAO;IAChBU,KAAK,EAAEA;EACX,CAAC;EACD0H,YAAY,CAACpI,OAAO,EAAEG,gBAAgB,CAACH,OAAO,CAAC,EAAEU,KAAK,CAAC;EACvD,IAAIV,OAAO,CAAChB,UAAU,CAAClB,QAAQ,IAAI,CAAC,CAAC,YAAY;IAC7C;IACAuK,UAAU,CAACrI,OAAO,EAAEA,OAAO,CAAChB,UAAU,EAAE0B,KAAK,CAAC;EAClD,CAAC,MAAM;IACH4H,cAAc,CAACtI,OAAO,EAAEU,KAAK,CAAC;EAClC;EACA6H,WAAW,CAAC,CAAC;EACb,OAAO7H,KAAK;AAChB;AAEA,IAAI8H,oBAAoB,GAAI,YAAU;EAClC,IAAIC,mBAAmB,GAAI,mDAAmD;EAC9E;EACA,IAAIC,WAAW,GAAY,cAAc;EACzC,IAAIC,UAAU,GAAa,eAAe;EAC1C,IAAIC,WAAW,GAAY,sCAAsC;EACjE,IAAIC,SAAS,GAAc,kCAAkC;EAC7D,IAAIC,cAAc,GAAS,QAAQ;EACnC,IAAIC,SAAS,GAAc,OAAO;EAClC,IAAIC,UAAU,GAAa,OAAO;EAClC,IAAIC,SAAS,GAAc,MAAM;EACjC,IAAIC,OAAO,GAAgB,UAAU;EACrC,IAAIC,WAAW,GAAY,UAAU;EAErC,IAAIC,MAAM,GAAG,CAAC,CAAC;IAAEC,MAAM,GAAG,CAAC,CAAC;EAE5B,SAASC,KAAKA,CAACC,KAAK,EAAE;IAClB,IAAI/J,IAAI,GAAG+J,KAAK;IAChB,IAAIC,cAAc,CAACJ,MAAM,EAAE5J,IAAI,CAAC,EAAE;MAC9B,OAAO4J,MAAM,CAAC5J,IAAI,CAAC;IACvB;IACA,SAASiK,OAAOA,CAAA,EAAG;MACf,IAAIC,CAAC,GAAGZ,cAAc,CAACa,IAAI,CAACJ,KAAK,CAAC;MAClC,IAAIG,CAAC,EAAE;QACHH,KAAK,GAAGA,KAAK,CAACK,MAAM,CAACF,CAAC,CAAC,CAAC,CAAC,CAACpK,MAAM,CAAC;MACrC;IACJ;IACA,SAASuK,IAAIA,CAACC,KAAK,EAAE;MACjBL,OAAO,CAAC,CAAC;MACT,IAAIC,CAAC,GAAGI,KAAK,CAACH,IAAI,CAACJ,KAAK,CAAC;MACzB,IAAIG,CAAC,EAAE;QACHH,KAAK,GAAGA,KAAK,CAACK,MAAM,CAACF,CAAC,CAAC,CAAC,CAAC,CAACpK,MAAM,CAAC;QACjC,OAAOoK,CAAC,CAAC,CAAC,CAAC;MACf;IACJ;IAEA,SAASK,SAASA,CAAA,EAAG;MACjB,IAAIC,KAAK,GAAG7Q,gBAAgB,CAACoQ,KAAK,EAAE,IAAI,CAAC;MACzC,IAAIjK,MAAM,EAAE2K,OAAO;MACnB,IAAID,KAAK,EAAE;QACP,IAAIE,KAAK,GACL,cAAc,CAACP,IAAI,CAACJ,KAAK,CAAC,IAC1B,gBAAgB,CAACI,IAAI,CAACJ,KAAK,CAAC,IAC5B,SAAS,CAACI,IAAI,CAACJ,KAAK,CAAC,CAAC,CAAC;QAC3BA,KAAK,GAAGA,KAAK,CAACK,MAAM,CAACM,KAAK,CAAC,CAAC,CAAC,CAAC5K,MAAM,CAAC;QACrC0K,KAAK,GAAGA,KAAK,CAACG,KAAK,CAAC,CAAC;QACrB,IAAI,EAAE7K,MAAM,GAAGuK,IAAI,CAAClB,UAAU,CAAC,CAAC,EAAE;UAC9BsB,OAAO,GAAGJ,IAAI,CAACnB,WAAW,CAAC;QAC/B;QACA,OAAO;UAAEsB,KAAK,EAAEA,KAAK;UAAE1K,MAAM,EAAEA,MAAM;UAAE2K,OAAO,EAAEA;QAAQ,CAAC;MAC7D;IACJ;IAEA,SAASG,oBAAoBA,CAACC,QAAQ,EAAE;MACpC,IAAIC,KAAK;MACT,IAAIC,GAAG,EAAEC,GAAG;MACZ,IAAIC,KAAK,GAAG,EAAE;MACd,IAAIC,OAAO,GAAG,KAAK;MAEnB,IAAIb,IAAI,CAACd,SAAS,CAAC,EAAE;QACjB;QACAuB,KAAK,GAAGT,IAAI,CAAChB,SAAS,CAAC;QACvB,IAAIyB,KAAK,IAAI,GAAG,EAAE;UACdA,KAAK,GAAG,MAAM,CAAC,CAAC;QACpB;QACA,IAAIA,KAAK,EAAE;UACPA,KAAK,GAAGK,UAAU,CAACL,KAAK,CAAC;UACzBT,IAAI,CAACZ,SAAS,CAAC;QACnB,CAAC,MACI;UACDsB,GAAG,GAAGV,IAAI,CAACjB,WAAW,CAAC;UACvB,IAAI2B,GAAG,IAAI,IAAI,EAAE;YACbA,GAAG,GAAGV,IAAI,CAACjB,WAAW,CAAC;UAC3B,CAAC,MAAM,IAAI2B,GAAG,IAAI,IAAI,CAAC3M,IAAI,CAACyM,QAAQ,CAAC,EAAE;YACnCK,OAAO,GAAG,IAAI;UAClB;UACAF,GAAG,GAAGX,IAAI,CAACjB,WAAW,CAAC;UACvBiB,IAAI,CAACZ,SAAS,CAAC;QACnB;QAEA,IAAI,OAAO,CAACrL,IAAI,CAACyM,QAAQ,CAAC,IAAIC,KAAK,IAAI,IAAI,IAAIC,GAAG,IAAI,IAAI,EAAE;UACxD,IAAI3K,CAAC,GAAGiK,IAAI,CAACnB,WAAW,CAAC;YAAE7I,CAAC,GAAGgK,IAAI,CAACnB,WAAW,CAAC;UAChDgC,OAAO,GAAG,IAAI;UACd,IAAI9K,CAAC,IAAI,IAAI,EAAE;YACX2K,GAAG,GAAG,MAAM;UAChB,CAAC,MAAM,IAAI3K,CAAC,IAAI,MAAM,EAAE;YACpB2K,GAAG,GAAG,OAAO;UACjB;UACA,IAAI1K,CAAC,IAAI,IAAI,EAAE;YACX2K,GAAG,GAAG,KAAK;UACf,CAAC,MAAM,IAAI3K,CAAC,IAAI,MAAM,EAAE;YACpB2K,GAAG,GAAG,QAAQ;UAClB;UACAX,IAAI,CAACZ,SAAS,CAAC;QACnB;;QAEA;QACA,OAAOM,KAAK,IAAI,CAACM,IAAI,CAACb,UAAU,CAAC,EAAE;UAC/B,IAAI4B,IAAI,GAAGb,SAAS,CAAC,CAAC;UACtB,IAAI,CAACa,IAAI,EAAE;YACP;UACJ;UACAH,KAAK,CAACjO,IAAI,CAACoO,IAAI,CAAC;UAChBf,IAAI,CAACZ,SAAS,CAAC;QACnB;QAEA,OAAO;UACH4B,IAAI,EAAM,QAAQ;UAClBP,KAAK,EAAKA,KAAK;UACfQ,EAAE,EAAQP,GAAG,IAAIC,GAAG,GAAGD,GAAG,GAAG,GAAG,GAAGC,GAAG,GAAGD,GAAG,GAAGA,GAAG,GAAGC,GAAG,GAAGA,GAAG,GAAG,IAAI;UACrEC,KAAK,EAAKA,KAAK;UACfC,OAAO,EAAGA;QACd,CAAC;MACL;IACJ;IAEA,SAASK,QAAQA,CAAA,EAAG;MAChB,IAAIlB,IAAI,CAACd,SAAS,CAAC,EAAE;QACjB,IAAIiC,GAAG,GAAGnB,IAAI,CAACV,WAAW,CAAC;QAC3B6B,GAAG,GAAGA,GAAG,CAACvH,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC;QACvCoG,IAAI,CAACb,UAAU,CAAC;QAChB,OAAO;UAAE6B,IAAI,EAAE,KAAK;UAAEG,GAAG,EAAEA;QAAI,CAAC;MACpC;IACJ;IAEA,IAAIC,GAAG;IAEP,IAAKA,GAAG,GAAGpB,IAAI,CAACpB,mBAAmB,CAAC,EAAG;MACnCwC,GAAG,GAAGb,oBAAoB,CAACa,GAAG,CAAC;IACnC,CAAC,MACI,IAAKA,GAAG,GAAGpB,IAAI,CAACX,OAAO,CAAC,EAAG;MAC5B+B,GAAG,GAAGF,QAAQ,CAAC,CAAC;IACpB;IAEA,OAAQ3B,MAAM,CAAC5J,IAAI,CAAC,GAAGyL,GAAG,IAAI;MAAEJ,IAAI,EAAE;IAAO,CAAC;EAClD;EAEA,OAAO,UAAStB,KAAK,EAAE;IACnB,IAAIC,cAAc,CAACH,MAAM,EAAEE,KAAK,CAAC,EAAE;MAC/B,OAAOF,MAAM,CAACE,KAAK,CAAC;IACxB;IACA,OAAQF,MAAM,CAACE,KAAK,CAAC,GAAG2B,aAAa,CAAC3B,KAAK,CAAC,CAAC4B,GAAG,CAAC7B,KAAK,CAAC;EAC3D,CAAC;AACL,CAAC,CAAE,CAAC;AAEJ,IAAI4B,aAAa,GAAI,YAAU;EAC3B,IAAIE,KAAK,GAAG,CAAC,CAAC;EACd,OAAO,UAAS7B,KAAK,EAAE8B,SAAS,EAAE;IAC9B,IAAI,CAACA,SAAS,EAAE;MACZA,SAAS,GAAG,UAAU;IAC1B;IAEA,IAAIC,QAAQ,GAAG/B,KAAK,GAAG8B,SAAS;IAEhC,IAAI7B,cAAc,CAAC4B,KAAK,EAAEE,QAAQ,CAAC,EAAE;MACjC,OAAOF,KAAK,CAACE,QAAQ,CAAC;IAC1B;IAEA,IAAIC,GAAG,GAAG,EAAE;IACZ,IAAIC,IAAI,GAAG,CAAC;MAAE7K,GAAG,GAAG,CAAC;IACrB,IAAI8K,QAAQ,GAAG,CAAC;IAChB,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIhC,CAAC;IAEL,SAASiC,UAAUA,CAACC,EAAE,EAAE;MACpB,OAAQlC,CAAC,GAAGkC,EAAE,CAACjC,IAAI,CAACJ,KAAK,CAACK,MAAM,CAACjJ,GAAG,CAAC,CAAC;IAC1C;IAEA,SAASkL,IAAIA,CAAC3Q,GAAG,EAAE;MACf,OAAOA,GAAG,CAACuI,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;IACxC;IAEA,OAAO9C,GAAG,GAAG4I,KAAK,CAACjK,MAAM,EAAE;MACvB,IAAI,CAACoM,SAAS,IAAIC,UAAU,CAAC,WAAW,CAAC,EAAE;QACvCF,QAAQ,EAAE;QACV9K,GAAG,EAAE;MACT,CAAC,MACI,IAAI,CAAC+K,SAAS,IAAIC,UAAU,CAAC,WAAW,CAAC,EAAE;QAC5CF,QAAQ,EAAE;QACV9K,GAAG,EAAE;MACT,CAAC,MACI,IAAI,CAAC+K,SAAS,IAAIC,UAAU,CAAC,SAAS,CAAC,EAAE;QAC1CD,SAAS,GAAGhC,CAAC,CAAC,CAAC,CAAC;QAChB/I,GAAG,EAAE;MACT,CAAC,MACI,IAAI+K,SAAS,IAAI,GAAG,IAAIC,UAAU,CAAC,OAAO,CAAC,EAAE;QAC9ChL,GAAG,IAAI,CAAC;MACZ,CAAC,MACI,IAAI+K,SAAS,IAAI,GAAG,IAAIC,UAAU,CAAC,OAAO,CAAC,EAAE;QAC9ChL,GAAG,IAAI,CAAC;MACZ,CAAC,MACI,IAAI+K,SAAS,IAAI,GAAG,IAAIC,UAAU,CAAC,KAAK,CAAC,EAAE;QAC5CD,SAAS,GAAG,KAAK;QACjB/K,GAAG,EAAE;MACT,CAAC,MACI,IAAI+K,SAAS,IAAI,GAAG,IAAIC,UAAU,CAAC,KAAK,CAAC,EAAE;QAC5CD,SAAS,GAAG,KAAK;QACjB/K,GAAG,EAAE;MACT,CAAC,MACI,IAAIgL,UAAU,CAACN,SAAS,CAAC,EAAE;QAC5B,IAAI,CAACK,SAAS,IAAI,CAACD,QAAQ,IAAI9K,GAAG,GAAG6K,IAAI,EAAE;UACvCD,GAAG,CAAC/O,IAAI,CAACqP,IAAI,CAACtC,KAAK,CAACuC,SAAS,CAACN,IAAI,EAAE7K,GAAG,CAAC,CAAC,CAAC;UAC1C6K,IAAI,GAAG7K,GAAG,GAAG+I,CAAC,CAAC,CAAC,CAAC,CAACpK,MAAM;QAC5B;QACAqB,GAAG,IAAI+I,CAAC,CAAC,CAAC,CAAC,CAACpK,MAAM;MACtB,CAAC,MACI;QACDqB,GAAG,EAAE;MACT;IACJ;IACA,IAAI6K,IAAI,GAAG7K,GAAG,EAAE;MACZ4K,GAAG,CAAC/O,IAAI,CAACqP,IAAI,CAACtC,KAAK,CAACuC,SAAS,CAACN,IAAI,EAAE7K,GAAG,CAAC,CAAC,CAAC;IAC9C;IACA,OAAQyK,KAAK,CAACE,QAAQ,CAAC,GAAGC,GAAG;EACjC,CAAC;AACL,CAAC,CAAE,CAAC;AAEJ,IAAIQ,UAAU,GAAI,UAASX,KAAK,EAAC;EAC7B,OAAO,UAASvP,EAAE,EAAC;IACf;IACA;IACA;IACA,IAAImP,GAAG,GAAGI,KAAK,CAACvP,EAAE,CAAC;IACnB,IAAI,CAACmP,GAAG,EAAE;MACN,IAAItB,CAAC;MACL,IAAKA,CAAC,GAAG,0DAA0D,CAACC,IAAI,CAAC9N,EAAE,CAAC,EAAG;QAC3EmP,GAAG,GAAGI,KAAK,CAACvP,EAAE,CAAC,GAAG6N,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,MAAM,IAAKA,CAAC,GAAG,iCAAiC,CAACC,IAAI,CAAC9N,EAAE,CAAC,EAAG;QACzDmP,GAAG,GAAGI,KAAK,CAACvP,EAAE,CAAC,GAAG6N,CAAC,CAAC,CAAC,CAAC;MAC1B;IACJ;IACA,OAAOsB,GAAG;EACd,CAAC;AACL,CAAC,CAAExP,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC;AAEvB,IAAIuQ,aAAa,GAAI,UAASZ,KAAK,EAAC;EAChC,OAAO,UAASa,IAAI,EAAE;IAClB,IAAI5E,MAAM,GAAG+D,KAAK,CAACa,IAAI,CAAC;IACxB,IAAI5E,MAAM,IAAI,IAAI,EAAE;MAChBA,MAAM,GAAG+D,KAAK,CAACa,IAAI,CAAC,GAAGjT,WAAW,CAAC,MAAM,EAAE;QAAEiT,IAAI,EAAEA;MAAK,CAAC,CAAC,CAAC5E,MAAM;IACrE;IACA,OAAOA,MAAM;EACjB,CAAC;AACL,CAAC,CAAE7L,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC;AAEvB,SAAS6E,YAAYA,CAACwD,GAAG,EAAE;EACvB,IAAIA,GAAG,IAAI,IAAI,EAAE;IACbA,GAAG,GAAGrG,QAAQ;EAClB;EACA,IAAIyO,MAAM,GAAG,CAAC,CAAC;EACf,KAAK,IAAI5N,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwF,GAAG,CAACqI,WAAW,CAAC7M,MAAM,EAAE,EAAEhB,CAAC,EAAE;IAC7C8N,YAAY,CAACtI,GAAG,CAACqI,WAAW,CAAC7N,CAAC,CAAC,CAAC;EACpC;EACA,OAAO4N,MAAM;EACb,SAASE,YAAYA,CAACC,EAAE,EAAE;IACtB,IAAIA,EAAE,EAAE;MACJ,IAAIC,KAAK,GAAG,IAAI;MAChB,IAAI;QACAA,KAAK,GAAGD,EAAE,CAACE,QAAQ;MACvB,CAAC,CAAC,OAAOC,EAAE,EAAE,CAAC;MACd,IAAIF,KAAK,EAAE;QACPG,QAAQ,CAACJ,EAAE,EAAEC,KAAK,CAAC;MACvB;IACJ;EACJ;EACA,SAASI,SAASA,CAACC,IAAI,EAAE;IACrB,IAAIC,GAAG,GAAG/G,gBAAgB,CAAC8G,IAAI,CAAC5P,KAAK,EAAE,KAAK,CAAC;IAC7C,IAAI6P,GAAG,EAAE;MACL,OAAO1B,aAAa,CAAC0B,GAAG,CAAC,CAACvQ,MAAM,CAAC,UAASC,CAAC,EAAET,EAAE,EAAC;QAC5C,IAAIoQ,IAAI,GAAGF,UAAU,CAAClQ,EAAE,CAAC;QACzB,IAAIoQ,IAAI,EAAE;UACN3P,CAAC,CAACE,IAAI,CAACyP,IAAI,CAAC;QAChB;QACA,OAAO3P,CAAC;MACZ,CAAC,EAAE,EAAE,CAAC;IACV,CAAC,MAAM;MACH;MACA;MACA,IAAI2P,IAAI,GAAGF,UAAU,CAACY,IAAI,CAACE,OAAO,CAAC;MACnC,OAAOZ,IAAI,GAAG,CAAEA,IAAI,CAAE,GAAG,EAAE;IAC/B;EACJ;EACA,SAASQ,QAAQA,CAACK,UAAU,EAAER,KAAK,EAAE;IACjC,KAAK,IAAIhO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgO,KAAK,CAAChN,MAAM,EAAE,EAAEhB,CAAC,EAAE;MACnC,IAAIyO,CAAC,GAAGT,KAAK,CAAChO,CAAC,CAAC;MAChB,QAAQyO,CAAC,CAAClC,IAAI;QACZ,KAAK,CAAC;UAAQ;UACZuB,YAAY,CAACW,CAAC,CAACD,UAAU,CAAC;UAC1B;QACF,KAAK,CAAC;UAAQ;UACZ,IAAI/P,KAAK,GAAIgQ,CAAC,CAAChQ,KAAK;UACpB,IAAIiQ,MAAM,GAAG9B,aAAa,CAACrF,gBAAgB,CAAC9I,KAAK,EAAE,aAAa,CAAC,CAAC;UAClE,IAAIkQ,IAAI,GAAK,qBAAqB,CAACrP,IAAI,CAACiI,gBAAgB,CAAC9I,KAAK,EAAE,aAAa,CAAC,CAAC;UAC/E,IAAImQ,MAAM,GAAG,QAAQ,IAAIrH,gBAAgB,CAAC9I,KAAK,EAAE,YAAY,CAAC;UAC9D,IAAI6P,GAAG,GAAMF,SAAS,CAACK,CAAC,CAAC;UACzB,IAAIH,GAAG,CAACtN,MAAM,GAAG,CAAC,EAAE;YAChB6N,OAAO,CAACL,UAAU,EAAEE,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEN,GAAG,CAAC,CAAC,CAAC,CAAC;UACrD;MACJ;IACJ;EACJ;EACA,SAASO,OAAOA,CAACL,UAAU,EAAEM,KAAK,EAAEH,IAAI,EAAEC,MAAM,EAAElC,GAAG,EAAE;IACnD;IACA;IACA,IAAI,CAAE,SAAS,CAACpN,IAAI,CAACoN,GAAG,CAAE,EAAE;MACxB,IAAI,EAAE,eAAe,CAACpN,IAAI,CAACoN,GAAG,CAAC,IAAI,KAAK,CAACpN,IAAI,CAACoN,GAAG,CAAC,CAAC,EAAE;QACjDA,GAAG,GAAGnN,MAAM,CAACiP,UAAU,CAACO,IAAI,CAAC,CAAC5J,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,GAAGuH,GAAG;MAC9D;IACJ;IACAoC,KAAK,CAACvQ,OAAO,CAAC,UAASyQ,IAAI,EAAC;MACxBA,IAAI,GAAGA,IAAI,CAAC7J,OAAO,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC,CAAC;MAC/C,IAAIwJ,IAAI,EAAE;QACNK,IAAI,IAAI,OAAO;MACnB;MACA,IAAIJ,MAAM,EAAE;QACRI,IAAI,IAAI,SAAS;MACrB;MACApB,MAAM,CAACoB,IAAI,CAAC,GAAGtC,GAAG;IACtB,CAAC,CAAC;EACN;AACJ;AAEA,SAASxB,cAAcA,CAAC+D,GAAG,EAAEzQ,GAAG,EAAE;EAC9B,OAAOtB,MAAM,CAACnB,SAAS,CAACmP,cAAc,CAAClP,IAAI,CAACiT,GAAG,EAAEzQ,GAAG,CAAC;AACzD;AAEA,SAAS0Q,UAAUA,CAACF,IAAI,EAAE;EACtBA,IAAI,GAAG,WAAW,GAAGA,IAAI;EACzB,OAAO5S,QAAQ,CAAC4S,IAAI,CAAC;AACzB;AAEA,SAASG,cAAcA,CAACH,IAAI,EAAE;EAC1B,IAAII,MAAM,GAAG,EAAE;IAAExQ,CAAC,GAAGxC,QAAQ;EAC7B4S,IAAI,GAAG,WAAW,GAAGA,IAAI;EACzB,OAAOpQ,CAAC,EAAE;IACN,IAAIsM,cAAc,CAACtM,CAAC,EAAEoQ,IAAI,CAAC,EAAE;MACzBI,MAAM,CAAClR,IAAI,CAACU,CAAC,CAACoQ,IAAI,CAAC,CAAC;IACxB;IACApQ,CAAC,GAAG1B,MAAM,CAACmS,cAAc,CAACzQ,CAAC,CAAC;EAChC;EACA,OAAOwQ,MAAM,CAAChD,OAAO,CAAC,CAAC;AAC3B;AAEA,SAASkD,UAAUA,CAACN,IAAI,EAAEO,GAAG,EAAE;EAC3B,IAAI3Q,CAAC,GAAGxC,QAAQ;EAChB4S,IAAI,GAAG,WAAW,GAAGA,IAAI;EACzB,OAAOpQ,CAAC,IAAI,CAACsM,cAAc,CAACtM,CAAC,EAAEoQ,IAAI,CAAC,EAAE;IAClCpQ,CAAC,GAAG1B,MAAM,CAACmS,cAAc,CAACzQ,CAAC,CAAC;EAChC;EACA,IAAI,CAACA,CAAC,EAAE;IACJA,CAAC,GAAGxC,QAAQ,CAACC,KAAK;EACtB;EACAuC,CAAC,CAACoQ,IAAI,CAAC,GAAG,CAACpQ,CAAC,CAACoQ,IAAI,CAAC,IAAI,CAAC,KAAKO,GAAG,IAAI,IAAI,GAAG,CAAC,GAAGA,GAAG,CAAC;AACtD;AAEA,SAASC,YAAYA,CAACR,IAAI,EAAES,GAAG,EAAE;EAC7BT,IAAI,GAAG,WAAW,GAAGA,IAAI;EACzB5S,QAAQ,CAAC4S,IAAI,CAAC,GAAGS,GAAG,IAAI,IAAI,GAAG,CAAC,GAAGA,GAAG;AAC1C;AAEA,SAASC,UAAUA,CAAC1R,CAAC,EAAE2R,CAAC,EAAElM,GAAG,EAAE;EAC3B,KAAK,IAAIzD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhC,CAAC,CAACgD,MAAM,GAAG;IAC3B,IAAIgO,IAAI,GAAGhR,CAAC,CAACgC,CAAC,EAAE,CAAC;IACjB,IAAIyP,GAAG,GAAGnI,UAAU,CAACtJ,CAAC,CAACgC,CAAC,CAAC,CAAC;IAC1B,IAAI4P,KAAK,CAACH,GAAG,CAAC,EAAE;MACZE,CAAC,CAACX,IAAI,EAAEvL,GAAG,CAAC;IAChB,CAAC,MAAM;MACHkM,CAAC,CAACX,IAAI,EAAES,GAAG,CAAC;MACZ,EAAEzP,CAAC;IACP;EACJ;AACJ;AAEA,SAAS6P,cAAcA,CAACpR,KAAK,EAAE;EAC3B,IAAIqR,YAAY,GAAGvI,gBAAgB,CAAC9I,KAAK,EAAE,eAAe,CAAC;EAC3D,IAAIqR,YAAY,EAAE;IACdJ,UAAU,CAAC9C,aAAa,CAACkD,YAAY,EAAE,MAAM,CAAC,EAAEN,YAAY,EAAE,CAAC,CAAC;EACpE;EACA,IAAIO,gBAAgB,GAAGxI,gBAAgB,CAAC9I,KAAK,EAAE,mBAAmB,CAAC;EACnE,IAAIsR,gBAAgB,EAAE;IAClBL,UAAU,CAAC9C,aAAa,CAACmD,gBAAgB,EAAE,MAAM,CAAC,EAAET,UAAU,EAAE,CAAC,CAAC;EACtE;AACJ;AAEA,SAAS1U,UAAUA,CAACgC,GAAG,EAAEoT,GAAG,EAAE;EAC1B,IAAItE,KAAK,GAAG7Q,gBAAgB,CAAC+B,GAAG,EAAE,IAAI,CAAC;EACvC,IAAI8O,KAAK,EAAE;IACPA,KAAK,GAAGA,KAAK,CAACG,KAAK,CAAC,CAAC;IACrB,IAAImE,GAAG,EAAE;MACLtE,KAAK,GAAGA,KAAK,CAACuE,SAAS,CAAC,CAAC;IAC7B,CAAC,MAAM,IAAIvE,KAAK,CAAC1N,CAAC,KAAK,CAAC,EAAE;MACtB0N,KAAK,GAAG,IAAI;IAChB;EACJ;EACA,OAAOA,KAAK;AAChB;AAEA,SAAS/E,2BAA2BA,CAACuJ,QAAQ,EAAE3K,QAAQ,EAAE;EACrD,IAAI4K,OAAO,GAAG,CAAC;EACf,IAAIC,IAAI,GAAG,KAAK;EAChBF,QAAQ,CAAC3R,OAAO,CAAC,UAAShB,EAAE,EAAC;IACzB,IAAI8S,MAAM,GAAG9S,EAAE,CAAC6B,gBAAgB,CAAC,KAAK,CAAC;IACvC,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqQ,MAAM,CAACrP,MAAM,EAAE,EAAEhB,CAAC,EAAE;MACpC,IAAIsQ,GAAG,GAAGD,MAAM,CAACrQ,CAAC,CAAC;MACnB,IAAI,CAACsQ,GAAG,CAACC,QAAQ,EAAE;QACfJ,OAAO,EAAE;QACTG,GAAG,CAACE,MAAM,GAAGF,GAAG,CAACG,OAAO,GAAGjM,IAAI;MACnC;IACJ;EACJ,CAAC,CAAC;EAEF,IAAI,CAAC2L,OAAO,EAAE;IACV3L,IAAI,CAAC,CAAC;EACV;EAEA,SAASA,IAAIA,CAAA,EAAG;IACZ,IAAI,CAAC4L,IAAI,IAAI,EAAED,OAAO,IAAI,CAAC,EAAE;MACzB5K,QAAQ,CAAC,CAAC;MACV6K,IAAI,GAAG,IAAI;IACf;EACJ;AACJ;AAEA,SAASlN,WAAWA,CAACgN,QAAQ,EAAE3K,QAAQ,EAAE;EACrC,IAAImL,IAAI,GAAG,EAAE;EACb,SAAShT,GAAGA,CAACgP,GAAG,EAAE;IACd,IAAI,CAACvQ,WAAW,CAACuQ,GAAG,CAAC,EAAE;MACnBvQ,WAAW,CAACuQ,GAAG,CAAC,GAAG,IAAI;MACvBgE,IAAI,CAACxS,IAAI,CAACwO,GAAG,CAAC;IAClB;EACJ;EAEAwD,QAAQ,CAAC3R,OAAO,CAAC,SAASsC,IAAIA,CAACa,OAAO,EAAC;IACnC,IAAI,QAAQ,CAACpC,IAAI,CAACoC,OAAO,CAACxB,OAAO,CAAC,EAAE;MAChCxC,GAAG,CAACgE,OAAO,CAAC4M,GAAG,CAAC;IACpB;IACApE,oBAAoB,CAChB3C,gBAAgB,CACZ1F,gBAAgB,CAACH,OAAO,CAAC,EAAE,kBAC/B,CACJ,CAAC,CAACnD,OAAO,CAAC,UAASoS,EAAE,EAAC;MAClB,IAAIA,EAAE,CAACpE,IAAI,IAAI,KAAK,EAAE;QAClB7O,GAAG,CAACiT,EAAE,CAACjE,GAAG,CAAC;MACf;IACJ,CAAC,CAAC;IAEF,IAAIhL,OAAO,CAACoE,QAAQ,EAAE;MAClBlK,KAAK,CAAC8F,OAAO,CAACoE,QAAQ,CAAC,CAACvH,OAAO,CAACsC,IAAI,CAAC;IACzC;EACJ,CAAC,CAAC;EAEF,IAAI+P,KAAK,GAAGF,IAAI,CAAC1P,MAAM;EACvB,SAASwD,IAAIA,CAAA,EAAG;IACZ,IAAI,EAAEoM,KAAK,IAAI,CAAC,EAAE;MACd;MACA;MACA;MACA;MACAjK,2BAA2B,CAACuJ,QAAQ,EAAE3K,QAAQ,CAAC;IACnD;EACJ;EACA,IAAIqL,KAAK,KAAK,CAAC,EAAE;IACbpM,IAAI,CAAC,CAAC;EACV;EACAkM,IAAI,CAACnS,OAAO,CAAC,UAASmO,GAAG,EAAC;IACtB,IAAI4D,GAAG,GAAGnU,WAAW,CAACuQ,GAAG,CAAC,GAAG,IAAInQ,MAAM,CAACnB,KAAK,CAAC,CAAC;IAC/C,IAAI,CAAE,SAAS,CAACkE,IAAI,CAACoN,GAAG,CAAE,EAAE;MACxB4D,GAAG,CAACO,WAAW,GAAG,WAAW;IACjC;IACAP,GAAG,CAAChC,GAAG,GAAG5B,GAAG;IACb,IAAI4D,GAAG,CAACC,QAAQ,EAAE;MACd/L,IAAI,CAAC,CAAC;IACV,CAAC,MAAM;MACH8L,GAAG,CAACE,MAAM,GAAGhM,IAAI;MACjB8L,GAAG,CAACG,OAAO,GAAG,YAAW;QACrBtU,WAAW,CAACuQ,GAAG,CAAC,GAAG,IAAI;QACvBlI,IAAI,CAAC,CAAC;MACV,CAAC;IACL;EACJ,CAAC,CAAC;AACN;AAEA,SAASsM,YAAYA,CAACC,CAAC,EAAE;EACrB,IAAInD,MAAM,GAAG,EAAE;EACf,GAAG;IACC,IAAIa,CAAC,GAAGsC,CAAC,GAAG,EAAE;IACdnD,MAAM,GAAGrO,MAAM,CAACyR,YAAY,CAAC,EAAE,GAAGvC,CAAC,CAAC,GAAGb,MAAM;IAC7CmD,CAAC,GAAGE,IAAI,CAACC,KAAK,CAACH,CAAC,GAAG,EAAE,CAAC;EAC1B,CAAC,QAAQA,CAAC,GAAG,CAAC;EACd,OAAOnD,MAAM;AACjB;AAEA,SAAS9D,YAAYA,CAACpI,OAAO,EAAEjD,KAAK,EAAE2D,KAAK,EAAE;EACzChG,QAAQ,GAAGc,MAAM,CAACC,MAAM,CAACf,QAAQ,CAAC;EAClCA,QAAQ,CAACsF,OAAO,CAACxB,OAAO,CAACiR,WAAW,CAAC,CAAC,CAAC,GAAG;IACtCzP,OAAO,EAAEA,OAAO;IAChBjD,KAAK,EAAEA;EACX,CAAC;EACD,IAAI2S,UAAU,GAAG7J,gBAAgB,CAAC9I,KAAK,EAAE,iBAAiB,CAAC;EAC3D,IAAI2S,UAAU,IAAIA,UAAU,IAAI,MAAM,EAAE;IACpC,IAAI1F,KAAK,GAAGnE,gBAAgB,CAAC9I,KAAK,EAAE,uBAAuB,CAAC;IAC5D2S,UAAU,CAACtT,KAAK,CAAC,MAAM,CAAC,CAACS,OAAO,CAAC,UAASyQ,IAAI,EAAC;MAC3C,IAAI,CAAC5S,QAAQ,CAAC4S,IAAI,CAAC,EAAE;QACjB5S,QAAQ,CAAC4S,IAAI,CAAC,GAAGtD,KAAK;QACtB,IAAIsD,IAAI,IAAI,WAAW,EAAE;UACrB,IAAIqC,MAAM,GAAG9J,gBAAgB,CAAC9I,KAAK,EAAE,uBAAuB,CAAC;UAC7D,IAAI4S,MAAM,IAAI,MAAM,EAAE;YAClBjV,QAAQ,CAAC,kBAAkB,CAAC,GAAGkL,UAAU,CAAC+J,MAAM,CAAC;UACrD;QACJ;MACJ;IACJ,CAAC,CAAC;EACN;EAEA,IAAIC,sBAAsB,CAAC7S,KAAK,CAAC,EAAE;IAC/BrC,QAAQ,CAAC0G,gBAAgB,GAAG;MACxBpB,OAAO,EAAEA,OAAO;MAChBU,KAAK,EAAEA;IACX,CAAC;EACL;AACJ;AAEA,SAAS6H,WAAWA,CAAA,EAAG;EACnB7N,QAAQ,GAAGc,MAAM,CAACmS,cAAc,CAACjT,QAAQ,CAAC;AAC9C;AAEA,SAASmV,aAAaA,CAACC,IAAI,EAAE;EACzB,IAAIpV,QAAQ,CAACsG,QAAQ,IAAI,IAAI,EAAE;IAC3B,IAAIwG,GAAG,GAAGsI,IAAI,CAACC,IAAI,CAACrV,QAAQ,CAACuG,OAAO,CAAC;IACrC,IAAIvG,QAAQ,CAACsG,QAAQ,EAAE;MACnBtG,QAAQ,CAACsG,QAAQ,GAAGpI,GAAG,CAACgB,IAAI,CAACoW,SAAS,CAACtV,QAAQ,CAACsG,QAAQ,EAAEwG,GAAG,CAAC;IAClE,CAAC,MAAM;MACH9M,QAAQ,CAACsG,QAAQ,GAAGwG,GAAG;IAC3B;EACJ;AACJ;AAEA,SAASyI,YAAYA,CAAA,EAAG;EACpB,IAAIC,EAAE,GAAGxV,QAAQ,CAACsG,QAAQ;EAC1B,IAAIkP,EAAE,IAAI,IAAI,EAAE;IACZ,OAAO,IAAI;EACf;EACA,IAAIA,EAAE,EAAE;IACJ,OAAOA,EAAE,CAACvL,KAAK,CAAC,CAAC,KAAK,CAAC,IAAIuL,EAAE,CAAC7I,MAAM,CAAC,CAAC,KAAK,CAAC;EAChD;AACJ;AAEA,SAASuI,sBAAsBA,CAAC7S,KAAK,EAAE;EACnC,SAASoT,IAAIA,CAAC7C,IAAI,EAAE;IAAE,OAAOzH,gBAAgB,CAAC9I,KAAK,EAAEuQ,IAAI,CAAC;EAAE;EAC5D,IAAI6C,IAAI,CAAC,WAAW,CAAC,IAAI,MAAM,IAC3BA,IAAI,CAAC,UAAU,CAAC,IAAI,QAAQ,IAC5BA,IAAI,CAAC,SAAS,CAAC,IAAI,MAAM,IACzBA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;IACrB,OAAO,IAAI;EACf;AACJ;AAEA,SAAShQ,gBAAgBA,CAACH,OAAO,EAAEoQ,SAAS,EAAE;EAC1C,OAAOvV,MAAM,CAACsF,gBAAgB,CAACH,OAAO,EAAEoQ,SAAS,IAAI,IAAI,CAAC;AAC9D;AAEA,SAASvK,gBAAgBA,CAAC9I,KAAK,EAAEoT,IAAI,EAAEE,IAAI,EAAE;EACzC,IAAItC,GAAG,GAAGhR,KAAK,CAAC8I,gBAAgB,CAACsK,IAAI,CAAC;EACtC,IAAIpC,GAAG,IAAI,IAAI,IAAIA,GAAG,KAAK,EAAE,EAAE;IAC3B,IAAI9T,OAAO,CAACqW,MAAM,EAAE;MAChBvC,GAAG,GAAGhR,KAAK,CAAC8I,gBAAgB,CAAC,UAAU,GAAGsK,IAAK,CAAC;IACpD,CAAC,MAAM,IAAIlW,OAAO,CAACsW,OAAO,EAAE;MACxBxC,GAAG,GAAGhR,KAAK,CAAC8I,gBAAgB,CAAC,OAAO,GAAGsK,IAAK,CAAC;IACjD,CAAC,MAAM,IAAIlW,OAAO,CAACuW,KAAK,EAAE;MACtBzC,GAAG,GAAGhR,KAAK,CAAC8I,gBAAgB,CAAC,KAAK,GAAGsK,IAAI,CAAC;IAC9C,CAAC,MAAM,IAAIrV,SAAS,EAAE;MAClBiT,GAAG,GAAGhR,KAAK,CAAC8I,gBAAgB,CAAC,MAAM,GAAGsK,IAAI,CAAC;IAC/C;EACJ;EACA,IAAIM,SAAS,CAACnR,MAAM,GAAG,CAAC,KAAKyO,GAAG,IAAI,IAAI,IAAIA,GAAG,KAAK,EAAE,CAAC,EAAE;IACrD,OAAOsC,IAAI;EACf,CAAC,MAAM;IACH,OAAOtC,GAAG;EACd;AACJ;AAEA,SAAS2C,sBAAsBA,CAAC3T,KAAK,EAAEoT,IAAI,EAAEvR,KAAK,EAAE+R,SAAS,EAAE;EAC3D5T,KAAK,CAAC6T,WAAW,CAACT,IAAI,EAAEvR,KAAK,EAAE+R,SAAS,CAAC;EACzC,IAAI1W,OAAO,CAACqW,MAAM,EAAE;IAChBvT,KAAK,CAAC6T,WAAW,CAAC,UAAU,GAAGT,IAAI,EAAEvR,KAAK,EAAE+R,SAAS,CAAC;EAC1D,CAAC,MAAM,IAAI1W,OAAO,CAACsW,OAAO,EAAE;IACxBxT,KAAK,CAAC6T,WAAW,CAAC,OAAO,GAAGT,IAAI,EAAEvR,KAAK,EAAE+R,SAAS,CAAC;EACvD,CAAC,MAAM,IAAI1W,OAAO,CAACuW,KAAK,EAAE;IACtBzT,KAAK,CAAC6T,WAAW,CAAC,KAAK,GAAGT,IAAI,EAAEvR,KAAK,EAAE+R,SAAS,CAAC;EACrD,CAAC,MAAM,IAAI7V,SAAS,EAAE;IAClBiC,KAAK,CAAC6T,WAAW,CAAC,MAAM,GAAGT,IAAI,EAAEvR,KAAK,EAAE+R,SAAS,CAAC;IAClDR,IAAI,GAAG,IAAI,GAAGA,IAAI,CAAC1M,OAAO,CAAC,eAAe,EAAE,UAASlG,CAAC,EAAEsT,EAAE,EAAEC,EAAE,EAAC;MAC3D,OAAOD,EAAE,GAAGC,EAAE,CAACC,WAAW,CAAC,CAAC;IAChC,CAAC,CAAC;IACFhU,KAAK,CAACoT,IAAI,CAAC,GAAGvR,KAAK;EACvB;AACJ;AAEA,SAASoS,SAASA,CAACjU,KAAK,EAAEkU,IAAI,EAAE;EAC5BA,IAAI,GAAG,SAAS,GAAGA,IAAI;EACvB,OAAO;IACHtM,KAAK,EAAEiB,UAAU,CAACC,gBAAgB,CAAC9I,KAAK,EAAEkU,IAAI,GAAG,QAAQ,CAAC,CAAC;IAC3DlU,KAAK,EAAE8I,gBAAgB,CAAC9I,KAAK,EAAEkU,IAAI,GAAG,QAAQ,CAAC;IAC/CjH,KAAK,EAAE9Q,UAAU,CAAC2M,gBAAgB,CAAC9I,KAAK,EAAEkU,IAAI,GAAG,QAAQ,CAAC,EAAE,IAAI;EACpE,CAAC;AACL;AAEA,SAASC,SAASA,CAAClR,OAAO,EAAEmR,IAAI,EAAE;EAC9B,IAAIC,IAAI,GAAGpR,OAAO,CAACjD,KAAK,CAAC8P,OAAO;EAChC,IAAIX,MAAM,GAAGiF,IAAI,CAAC,CAAC;EACnBnX,QAAQ,CAACgG,OAAO,EAAEoR,IAAI,CAAC;EACvB,OAAOlF,MAAM;AACjB;AAEA,SAASmF,eAAeA,CAACtU,KAAK,EAAEkU,IAAI,EAAE;EAClC,IAAIlE,CAAC,GAAGlH,gBAAgB,CAAC9I,KAAK,EAAE,SAAS,GAAGkU,IAAI,GAAG,SAAS,CAAC,CAAC7U,KAAK,CAAC,MAAM,CAAC,CAAC+O,GAAG,CAACvF,UAAU,CAAC;EAC3F,IAAImH,CAAC,CAACzN,MAAM,IAAI,CAAC,EAAE;IACfyN,CAAC,CAACvQ,IAAI,CAACuQ,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;EACA,OAAOuE,cAAc,CAAC;IAAE1R,CAAC,EAAEmN,CAAC,CAAC,CAAC,CAAC;IAAElN,CAAC,EAAEkN,CAAC,CAAC,CAAC;EAAE,CAAC,CAAC;AAC/C;AAEA,SAASwE,aAAaA,CAACvR,OAAO,EAAE;EAC5B,IAAIwH,GAAG,GAAGxH,OAAO,CAACY,qBAAqB,CAAC,CAAC;EACzC4G,GAAG,GAAGgK,QAAQ,CAAChK,GAAG,EAAE,gBAAgB,EAAExH,OAAO,CAAC;EAC9CwH,GAAG,GAAGgK,QAAQ,CAAChK,GAAG,EAAE,WAAW,EAAExH,OAAO,CAAC;EACzC,OAAOwH,GAAG;AACd;AAEA,SAASgK,QAAQA,CAAChK,GAAG,EAAE2I,IAAI,EAAEnQ,OAAO,EAAE;EAClC,IAAIjD,KAAK,EAAE0U,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EACzB,IAAI,OAAOzB,IAAI,IAAI,QAAQ,EAAE;IACzBpT,KAAK,GAAGoD,gBAAgB,CAACH,OAAO,CAAC;IACjCyR,EAAE,GAAG7L,UAAU,CAACC,gBAAgB,CAAC9I,KAAK,EAAEoT,IAAI,CAAC1M,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;IAClEiO,EAAE,GAAG9L,UAAU,CAACC,gBAAgB,CAAC9I,KAAK,EAAEoT,IAAI,CAAC1M,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;IACpEkO,EAAE,GAAG/L,UAAU,CAACC,gBAAgB,CAAC9I,KAAK,EAAEoT,IAAI,CAAC1M,OAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;IACrEmO,EAAE,GAAGhM,UAAU,CAACC,gBAAgB,CAAC9I,KAAK,EAAEoT,IAAI,CAAC1M,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC;EACvE,CAAC,MACI,IAAI,OAAO0M,IAAI,IAAI,QAAQ,EAAE;IAC9BsB,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGzB,IAAI;EAC5B;EACA,OAAO;IACHpP,GAAG,EAAMyG,GAAG,CAACzG,GAAG,GAAG0Q,EAAE;IACrBpP,KAAK,EAAImF,GAAG,CAACnF,KAAK,GAAGqP,EAAE;IACvBpP,MAAM,EAAGkF,GAAG,CAAClF,MAAM,GAAGqP,EAAE;IACxB7Q,IAAI,EAAK0G,GAAG,CAAC1G,IAAI,GAAG8Q,EAAE;IACtBjN,KAAK,EAAI6C,GAAG,CAACnF,KAAK,GAAGmF,GAAG,CAAC1G,IAAI,GAAG4Q,EAAE,GAAGE,EAAE;IACvCvK,MAAM,EAAGG,GAAG,CAAClF,MAAM,GAAGkF,GAAG,CAACzG,GAAG,GAAG4Q,EAAE,GAAGF;EACzC,CAAC;AACL;AAEA,SAASI,YAAYA,CAAC9U,KAAK,EAAE;EACzB,IAAI+U,SAAS,GAAGjM,gBAAgB,CAAC9I,KAAK,EAAE,WAAW,CAAC;EACpD,IAAI+U,SAAS,IAAI,MAAM,EAAE;IACrB,OAAO,IAAI;EACf;EACA,IAAIC,MAAM,GAAG,+BAA+B,CAACpI,IAAI,CAACmI,SAAS,CAAC;EAC5D,IAAIC,MAAM,EAAE;IACR,IAAIC,MAAM,GAAGnM,gBAAgB,CAAC9I,KAAK,EAAE,kBAAkB,CAAC;IACxDgV,MAAM,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC3V,KAAK,CAAC,UAAU,CAAC,CAAC+O,GAAG,CAACvF,UAAU,CAAC;IACpDoM,MAAM,GAAGA,MAAM,CAAC5V,KAAK,CAAC,MAAM,CAAC,CAAC+O,GAAG,CAACvF,UAAU,CAAC;IAC7C,OAAO;MACHmM,MAAM,EAAEA,MAAM;MACdC,MAAM,EAAEA;IACZ,CAAC;EACL;AACJ;AAEA,SAASC,gBAAgBA,CAACC,OAAO,EAAE;EAC/B,OAAS,GAAG,GAAGA,OAAO,GAAI3C,IAAI,CAAC4C,EAAE,GAAI,GAAG;AAC5C;AAEA,SAASxH,UAAUA,CAACL,KAAK,EAAE;EACvB,IAAI8H,GAAG,GAAGxM,UAAU,CAAC0E,KAAK,CAAC;EAC3B,IAAI,OAAO,CAAC1M,IAAI,CAAC0M,KAAK,CAAC,EAAE;IACrB,OAAOiF,IAAI,CAAC4C,EAAE,GAAGC,GAAG,GAAG,GAAG;EAC9B,CAAC,MACI,IAAI,MAAM,CAACxU,IAAI,CAAC0M,KAAK,CAAC,EAAE;IACzB,OAAO8H,GAAG;EACd,CAAC,MACI,IAAI,OAAO,CAACxU,IAAI,CAAC0M,KAAK,CAAC,EAAE;IAC1B,OAAOiF,IAAI,CAAC4C,EAAE,GAAGC,GAAG,GAAG,CAAC;EAC5B,CAAC,MACI,IAAI,MAAM,CAACxU,IAAI,CAAC0M,KAAK,CAAC,EAAE;IACzB,OAAOiF,IAAI,CAAC4C,EAAE,GAAGC,GAAG,GAAG,GAAG;EAC9B;AACJ;AAEA,SAASvR,YAAYA,CAACwR,KAAK,EAAE3I,CAAC,EAAE;EAC5BA,CAAC,GAAG,IAAI9Q,GAAG,CAACsI,MAAM,CAACwI,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD2I,KAAK,CAACP,SAAS,CAACpI,CAAC,CAAC;EAClB,OAAOA,CAAC;AACZ;AAEA,SAAS4I,WAAWA,CAACD,KAAK,EAAEE,QAAQ,EAAE;EAClCF,KAAK,CAACG,IAAI,CAACD,QAAQ,CAAC;AACxB;AAEA,SAASE,YAAYA,CAAC3C,IAAI,EAAElQ,CAAC,EAAEC,CAAC,EAAEzE,OAAO,EAAE;EACvC,IAAIsX,MAAM,GAAG,IAAI9Z,GAAG,CAAC+Z,GAAG,CAAC,CAAE/S,CAAC,EAAEC,CAAC,CAAE,EAAEzE,OAAO,CAAC,CAACwX,WAAW,CAAC,CAAC;IAAEtU,CAAC,GAAG,CAAC;EAChE,OAAOA,CAAC,GAAGoU,MAAM,CAACpT,MAAM,EAAE;IACtBwQ,IAAI,CAAC+C,OAAO,CAACH,MAAM,CAACpU,CAAC,EAAE,CAAC,EAAEoU,MAAM,CAACpU,CAAC,EAAE,CAAC,EAAEoU,MAAM,CAACpU,CAAC,EAAE,CAAC,CAAC;EACvD;AACJ;AAEA,SAASgT,cAAcA,CAACvE,CAAC,EAAE;EACvB,IAAIA,CAAC,CAACnN,CAAC,IAAI,CAAC,IAAImN,CAAC,CAAClN,CAAC,IAAI,CAAC,EAAE;IACtBkN,CAAC,CAACnN,CAAC,GAAGmN,CAAC,CAAClN,CAAC,GAAG,CAAC;EACjB;EACA,OAAOkN,CAAC;AACZ;AAEA,SAAS+F,wBAAwBA,CAACtL,GAAG,EAAEuL,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAE;EACvD;EACA;EACA;EACA;EACA,IAAIC,IAAI,GAAG5D,IAAI,CAACxH,GAAG,CAAC,CAAC,EAAEgL,GAAG,CAACnT,CAAC,CAAC;IAAEwT,IAAI,GAAG7D,IAAI,CAACxH,GAAG,CAAC,CAAC,EAAEgL,GAAG,CAAClT,CAAC,CAAC;EACxD,IAAIwT,IAAI,GAAG9D,IAAI,CAACxH,GAAG,CAAC,CAAC,EAAEiL,GAAG,CAACpT,CAAC,CAAC;IAAE0T,IAAI,GAAG/D,IAAI,CAACxH,GAAG,CAAC,CAAC,EAAEiL,GAAG,CAACnT,CAAC,CAAC;EACxD,IAAI0T,IAAI,GAAGhE,IAAI,CAACxH,GAAG,CAAC,CAAC,EAAEkL,GAAG,CAACrT,CAAC,CAAC;IAAE4T,IAAI,GAAGjE,IAAI,CAACxH,GAAG,CAAC,CAAC,EAAEkL,GAAG,CAACpT,CAAC,CAAC;EACxD,IAAI4T,IAAI,GAAGlE,IAAI,CAACxH,GAAG,CAAC,CAAC,EAAEmL,GAAG,CAACtT,CAAC,CAAC;IAAE8T,IAAI,GAAGnE,IAAI,CAACxH,GAAG,CAAC,CAAC,EAAEmL,GAAG,CAACrT,CAAC,CAAC;EAExD,IAAIoO,CAAC,GAAGsB,IAAI,CAACzH,GAAG,CACZN,GAAG,CAAC7C,KAAK,IAAIwO,IAAI,GAAGE,IAAI,CAAC,EACzB7L,GAAG,CAACH,MAAM,IAAIiM,IAAI,GAAGE,IAAI,CAAC,EAC1BhM,GAAG,CAAC7C,KAAK,IAAI4O,IAAI,GAAGE,IAAI,CAAC,EACzBjM,GAAG,CAACH,MAAM,IAAIqM,IAAI,GAAGN,IAAI,CAC7B,CAAC;EAED,IAAInF,CAAC,GAAG,CAAC,EAAE;IACPkF,IAAI,IAAIlF,CAAC;IAAEmF,IAAI,IAAInF,CAAC;IACpBoF,IAAI,IAAIpF,CAAC;IAAEqF,IAAI,IAAIrF,CAAC;IACpBsF,IAAI,IAAItF,CAAC;IAAEuF,IAAI,IAAIvF,CAAC;IACpBwF,IAAI,IAAIxF,CAAC;IAAEyF,IAAI,IAAIzF,CAAC;EACxB;EAEA,OAAO;IACH0F,EAAE,EAAE;MAAE/T,CAAC,EAAEuT,IAAI;MAAEtT,CAAC,EAAEuT;IAAK,CAAC;IACxBQ,EAAE,EAAE;MAAEhU,CAAC,EAAEyT,IAAI;MAAExT,CAAC,EAAEyT;IAAK,CAAC;IACxBO,EAAE,EAAE;MAAEjU,CAAC,EAAE2T,IAAI;MAAE1T,CAAC,EAAE2T;IAAK,CAAC;IACxBM,EAAE,EAAE;MAAElU,CAAC,EAAE6T,IAAI;MAAE5T,CAAC,EAAE6T;IAAK;EAC3B,CAAC;AACL;AAEA,SAASK,eAAeA,CAAC/T,OAAO,EAAEwH,GAAG,EAAEqD,IAAI,EAAE;EACzC,IAAI9N,KAAK,GAAGoD,gBAAgB,CAACH,OAAO,CAAC;EAErC,IAAI+S,GAAG,GAAG1B,eAAe,CAACtU,KAAK,EAAE,UAAU,CAAC;EAC5C,IAAIiW,GAAG,GAAG3B,eAAe,CAACtU,KAAK,EAAE,WAAW,CAAC;EAC7C,IAAImW,GAAG,GAAG7B,eAAe,CAACtU,KAAK,EAAE,aAAa,CAAC;EAC/C,IAAIkW,GAAG,GAAG5B,eAAe,CAACtU,KAAK,EAAE,cAAc,CAAC;EAEhD,IAAI8N,IAAI,IAAI,SAAS,IAAIA,IAAI,IAAI,SAAS,EAAE;IACxC,IAAImJ,EAAE,GAAGhD,SAAS,CAACjU,KAAK,EAAE,KAAK,CAAC;IAChC,IAAI8W,EAAE,GAAG7C,SAAS,CAACjU,KAAK,EAAE,OAAO,CAAC;IAClC,IAAIkX,EAAE,GAAGjD,SAAS,CAACjU,KAAK,EAAE,QAAQ,CAAC;IACnC,IAAI+W,EAAE,GAAG9C,SAAS,CAACjU,KAAK,EAAE,MAAM,CAAC;IACjCgW,GAAG,CAACnT,CAAC,IAAIkU,EAAE,CAACnP,KAAK;IAAEoO,GAAG,CAAClT,CAAC,IAAImU,EAAE,CAACrP,KAAK;IACpCqO,GAAG,CAACpT,CAAC,IAAIiU,EAAE,CAAClP,KAAK;IAAEqO,GAAG,CAACnT,CAAC,IAAImU,EAAE,CAACrP,KAAK;IACpCsO,GAAG,CAACrT,CAAC,IAAIiU,EAAE,CAAClP,KAAK;IAAEsO,GAAG,CAACpT,CAAC,IAAIoU,EAAE,CAACtP,KAAK;IACpCuO,GAAG,CAACtT,CAAC,IAAIkU,EAAE,CAACnP,KAAK;IAAEuO,GAAG,CAACrT,CAAC,IAAIoU,EAAE,CAACtP,KAAK;IACpC,IAAIkG,IAAI,IAAI,SAAS,EAAE;MACnB,IAAIqJ,EAAE,GAAGtO,UAAU,CAACC,gBAAgB,CAAC9I,KAAK,EAAE,aAAa,CAAC,CAAC;MAC3D,IAAIoX,EAAE,GAAGvO,UAAU,CAACC,gBAAgB,CAAC9I,KAAK,EAAE,eAAe,CAAC,CAAC;MAC7D,IAAIqX,EAAE,GAAGxO,UAAU,CAACC,gBAAgB,CAAC9I,KAAK,EAAE,gBAAgB,CAAC,CAAC;MAC9D,IAAIsX,EAAE,GAAGzO,UAAU,CAACC,gBAAgB,CAAC9I,KAAK,EAAE,cAAc,CAAC,CAAC;MAC5DgW,GAAG,CAACnT,CAAC,IAAIyU,EAAE;MAAEtB,GAAG,CAAClT,CAAC,IAAIqU,EAAE;MACxBlB,GAAG,CAACpT,CAAC,IAAIuU,EAAE;MAAEnB,GAAG,CAACnT,CAAC,IAAIqU,EAAE;MACxBjB,GAAG,CAACrT,CAAC,IAAIuU,EAAE;MAAElB,GAAG,CAACpT,CAAC,IAAIuU,EAAE;MACxBlB,GAAG,CAACtT,CAAC,IAAIyU,EAAE;MAAEnB,GAAG,CAACrT,CAAC,IAAIuU,EAAE;IAC5B;EACJ;EAEA,IAAI,OAAOvJ,IAAI,IAAI,QAAQ,EAAE;IACzBkI,GAAG,CAACnT,CAAC,IAAIiL,IAAI;IAAEkI,GAAG,CAAClT,CAAC,IAAIgL,IAAI;IAC5BmI,GAAG,CAACpT,CAAC,IAAIiL,IAAI;IAAEmI,GAAG,CAACnT,CAAC,IAAIgL,IAAI;IAC5BoI,GAAG,CAACrT,CAAC,IAAIiL,IAAI;IAAEoI,GAAG,CAACpT,CAAC,IAAIgL,IAAI;IAC5BqI,GAAG,CAACtT,CAAC,IAAIiL,IAAI;IAAEqI,GAAG,CAACrT,CAAC,IAAIgL,IAAI;EAChC;EAEA,OAAOyJ,QAAQ,CAAC9M,GAAG,EAAEuL,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,CAAC;AAC5C;;AAEA;AACA;AACA;AACA;AACA,SAASoB,QAAQA,CAAC9M,GAAG,EAAE+M,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAE;EAC3C,IAAIC,GAAG,GAAG7B,wBAAwB,CAACtL,GAAG,EAAE+M,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,CAAC;EAC/D,IAAI3B,GAAG,GAAG4B,GAAG,CAAChB,EAAE;EAChB,IAAIX,GAAG,GAAG2B,GAAG,CAACf,EAAE;EAChB,IAAIX,GAAG,GAAG0B,GAAG,CAACd,EAAE;EAChB,IAAIX,GAAG,GAAGyB,GAAG,CAACb,EAAE;EAChB,IAAIhE,IAAI,GAAG,IAAIvW,IAAI,CAAC;IAAEqb,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAK,CAAC,CAAC;EACjD/E,IAAI,CAACgF,MAAM,CAACtN,GAAG,CAAC1G,IAAI,EAAE0G,GAAG,CAACzG,GAAG,GAAGgS,GAAG,CAAClT,CAAC,CAAC;EACtC,IAAIkT,GAAG,CAACnT,CAAC,EAAE;IACP6S,YAAY,CAAC3C,IAAI,EAAEtI,GAAG,CAAC1G,IAAI,GAAGiS,GAAG,CAACnT,CAAC,EAAE4H,GAAG,CAACzG,GAAG,GAAGgS,GAAG,CAAClT,CAAC,EAAE;MAClDkV,UAAU,EAAE,CAAC,GAAG;MAChBC,QAAQ,EAAE,CAAC,EAAE;MACbC,OAAO,EAAElC,GAAG,CAACnT,CAAC;MACdsV,OAAO,EAAEnC,GAAG,CAAClT;IACjB,CAAC,CAAC;EACN;EACAiQ,IAAI,CAACqF,MAAM,CAAC3N,GAAG,CAACnF,KAAK,GAAG2Q,GAAG,CAACpT,CAAC,EAAE4H,GAAG,CAACzG,GAAG,CAAC;EACvC,IAAIiS,GAAG,CAACpT,CAAC,EAAE;IACP6S,YAAY,CAAC3C,IAAI,EAAEtI,GAAG,CAACnF,KAAK,GAAG2Q,GAAG,CAACpT,CAAC,EAAE4H,GAAG,CAACzG,GAAG,GAAGiS,GAAG,CAACnT,CAAC,EAAE;MACnDkV,UAAU,EAAE,CAAC,EAAE;MACfC,QAAQ,EAAE,CAAC;MACXC,OAAO,EAAEjC,GAAG,CAACpT,CAAC;MACdsV,OAAO,EAAElC,GAAG,CAACnT;IACjB,CAAC,CAAC;EACN;EACAiQ,IAAI,CAACqF,MAAM,CAAC3N,GAAG,CAACnF,KAAK,EAAEmF,GAAG,CAAClF,MAAM,GAAG2Q,GAAG,CAACpT,CAAC,CAAC;EAC1C,IAAIoT,GAAG,CAACrT,CAAC,EAAE;IACP6S,YAAY,CAAC3C,IAAI,EAAEtI,GAAG,CAACnF,KAAK,GAAG4Q,GAAG,CAACrT,CAAC,EAAE4H,GAAG,CAAClF,MAAM,GAAG2Q,GAAG,CAACpT,CAAC,EAAE;MACtDkV,UAAU,EAAE,CAAC;MACbC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAEhC,GAAG,CAACrT,CAAC;MACdsV,OAAO,EAAEjC,GAAG,CAACpT;IACjB,CAAC,CAAC;EACN;EACAiQ,IAAI,CAACqF,MAAM,CAAC3N,GAAG,CAAC1G,IAAI,GAAGoS,GAAG,CAACtT,CAAC,EAAE4H,GAAG,CAAClF,MAAM,CAAC;EACzC,IAAI4Q,GAAG,CAACtT,CAAC,EAAE;IACP6S,YAAY,CAAC3C,IAAI,EAAEtI,GAAG,CAAC1G,IAAI,GAAGoS,GAAG,CAACtT,CAAC,EAAE4H,GAAG,CAAClF,MAAM,GAAG4Q,GAAG,CAACrT,CAAC,EAAE;MACrDkV,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE,GAAG;MACbC,OAAO,EAAE/B,GAAG,CAACtT,CAAC;MACdsV,OAAO,EAAEhC,GAAG,CAACrT;IACjB,CAAC,CAAC;EACN;EACA,OAAOiQ,IAAI,CAACsF,KAAK,CAAC,CAAC;AACvB;AAEA,SAASC,aAAaA,CAACtH,GAAG,EAAEhR,KAAK,EAAE;EAC/B,IAAI7B,GAAG,GAAG2C,MAAM,CAAC+H,UAAU,CAACmI,GAAG,CAAC,CAAC;EACjC,QAAQhR,KAAK;IACX,KAAK,sBAAsB;MACzB,IAAI7B,GAAG,CAACoE,MAAM,GAAG,CAAC,EAAE;QAChBpE,GAAG,GAAG,GAAG,GAAGA,GAAG;MACnB;MACA,OAAOA,GAAG;IACZ,KAAK,aAAa;MAChB,OAAOpC,aAAa,CAACiV,GAAG,CAAC,CAAC0B,WAAW,CAAC,CAAC;IACzC,KAAK,aAAa;MAChB,OAAO3W,aAAa,CAACiV,GAAG,CAAC,CAACgD,WAAW,CAAC,CAAC;IACzC,KAAK,aAAa;IAClB,KAAK,aAAa;MAChB,OAAO3B,YAAY,CAACrB,GAAG,GAAG,CAAC,CAAC;IAC9B,KAAK,aAAa;IAClB,KAAK,aAAa;MAChB,OAAOqB,YAAY,CAACrB,GAAG,GAAG,CAAC,CAAC,CAACgD,WAAW,CAAC,CAAC;IAC5C;MACE,OAAO7V,GAAG;EACd;AACJ;AAEA,SAASoa,wBAAwBA,CAACtV,OAAO,EAAEuV,OAAO,EAAE;EAChD,SAASC,cAAcA,CAAClI,IAAI,EAAEvQ,KAAK,EAAEsO,SAAS,EAAE;IAC5C,IAAI,CAACA,SAAS,EAAE;MACZ,OAAOgK,aAAa,CAAC7H,UAAU,CAACF,IAAI,CAAC,IAAI,CAAC,EAAEvQ,KAAK,CAAC;IACtD;IACAsO,SAAS,GAAGA,SAAS,CAAC5H,OAAO,CAAC,sBAAsB,EAAE,IAAI,CAAC;IAC3D,OAAOgK,cAAc,CAACH,IAAI,CAAC,CAACnC,GAAG,CAAC,UAAS4C,GAAG,EAAC;MACzC,OAAOsH,aAAa,CAACtH,GAAG,EAAEhR,KAAK,CAAC;IACpC,CAAC,CAAC,CAACN,IAAI,CAAC4O,SAAS,CAAC;EACtB;EACA,IAAI/O,CAAC,GAAG4O,aAAa,CAACqK,OAAO,EAAE,MAAM,CAAC;EACtC,IAAIrJ,MAAM,GAAG,EAAE;IAAExC,CAAC;EAClBpN,CAAC,CAACO,OAAO,CAAC,UAAShB,EAAE,EAAC;IAClB,IAAI8Y,GAAG;IACP,IAAKjL,CAAC,GAAG,sBAAsB,CAACC,IAAI,CAAC9N,EAAE,CAAC,EAAG;MACvCqQ,MAAM,CAAC1P,IAAI,CAACkN,CAAC,CAAC,CAAC,CAAC,CAACjG,OAAO,CAAC,mBAAmB,EAAE,UAASlG,CAAC,EAAEL,CAAC,EAAC;QACxD,OAAOW,MAAM,CAACyR,YAAY,CAACmG,QAAQ,CAACvY,CAAC,EAAE,EAAE,CAAC,CAAC;MAC/C,CAAC,CAAC,CAAC;IACP,CAAC,MACI,IAAKwM,CAAC,GAAG,0BAA0B,CAACC,IAAI,CAAC9N,EAAE,CAAC,EAAG;MAChD8Y,GAAG,GAAGzJ,aAAa,CAACxB,CAAC,CAAC,CAAC,CAAC,CAAC;MACzBwC,MAAM,CAAC1P,IAAI,CAACgZ,cAAc,CAACb,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC,MACI,IAAKjL,CAAC,GAAG,2BAA2B,CAACC,IAAI,CAAC9N,EAAE,CAAC,EAAG;MACjD8Y,GAAG,GAAGzJ,aAAa,CAACxB,CAAC,CAAC,CAAC,CAAC,CAAC;MACzBwC,MAAM,CAAC1P,IAAI,CAACgZ,cAAc,CAACb,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACvD,CAAC,MACI,IAAKjL,CAAC,GAAG,uBAAuB,CAACC,IAAI,CAAC9N,EAAE,CAAC,EAAG;MAC7CqQ,MAAM,CAAC1P,IAAI,CAACwD,OAAO,CAACyF,YAAY,CAACiE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IACjD,CAAC,MACI;MACDwC,MAAM,CAAC1P,IAAI,CAACX,EAAE,CAAC;IACnB;EACJ,CAAC,CAAC;EACF,OAAOqQ,MAAM,CAACzP,IAAI,CAAC,EAAE,CAAC;AAC1B;AAEA,SAASiZ,UAAUA,CAAC3Y,KAAK,EAAE;EACvB,IAAIA,KAAK,CAAC8P,OAAO,EAAE;IACf,OAAO9P,KAAK,CAAC8P,OAAO;EACxB;EACA;EACA;EACA;EACA,IAAIX,MAAM,GAAG,EAAE;EACf,KAAK,IAAI5N,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,KAAK,CAACuC,MAAM,EAAE,EAAEhB,CAAC,EAAE;IACnC4N,MAAM,CAAC1P,IAAI,CAACO,KAAK,CAACuB,CAAC,CAAC,GAAG,IAAI,GAAGuH,gBAAgB,CAAC9I,KAAK,EAAEA,KAAK,CAACuB,CAAC,CAAC,CAAC,CAAC;EACpE;EACA,OAAO4N,MAAM,CAACzP,IAAI,CAAC,KAAK,CAAC;AAC7B;AAEA,SAASkZ,yBAAyBA,CAAC3V,OAAO,EAAEU,KAAK,EAAE;EAC/C,IAAIV,OAAO,CAACxB,OAAO,IAAIjE,oBAAoB,EAAE;IACzC+N,cAAc,CAACtI,OAAO,EAAEU,KAAK,CAAC;IAC9B;EACJ;EACA,IAAIkV,IAAI,GAAG,EAAE;EACb,SAASC,MAAMA,CAACC,IAAI,EAAEC,KAAK,EAAE;IACzB,IAAIhZ,KAAK,GAAGoD,gBAAgB,CAACH,OAAO,EAAE8V,IAAI,CAAC;MAAEP,OAAO,GAAGxY,KAAK,CAACwY,OAAO;IACpEpH,cAAc,CAACpR,KAAK,CAAC;IACrB,IAAIwY,OAAO,IAAIA,OAAO,IAAI,QAAQ,IAAIA,OAAO,IAAI,MAAM,IAAIxY,KAAK,CAAC4H,KAAK,IAAI,KAAK,EAAE;MAC7E,IAAIqR,IAAI,GAAGhW,OAAO,CAACO,aAAa,CAACoD,aAAa,CAACpJ,oBAAoB,CAAC;MACpEP,QAAQ,CAACgc,IAAI,EAAEN,UAAU,CAAC3Y,KAAK,CAAC,CAAC;MACjCiZ,IAAI,CAACC,WAAW,GAAGX,wBAAwB,CAACtV,OAAO,EAAEuV,OAAO,CAAC;MAC7DvV,OAAO,CAAC+E,YAAY,CAACiR,IAAI,EAAED,KAAK,CAAC;MACjCH,IAAI,CAACpZ,IAAI,CAACwZ,IAAI,CAAC;IACnB;EACJ;EACAH,MAAM,CAAC,SAAS,EAAE7V,OAAO,CAAChB,UAAU,CAAC;EACrC6W,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC;EACtB,IAAID,IAAI,CAACtW,MAAM,GAAG,CAAC,EAAE;IACjB,IAAI4W,SAAS,GAAGlW,OAAO,CAAC/D,SAAS;IACjC+D,OAAO,CAAC/D,SAAS,IAAI,iCAAiC;IACtDqM,cAAc,CAACtI,OAAO,EAAEU,KAAK,CAAC;IAC9BV,OAAO,CAAC/D,SAAS,GAAGia,SAAS;IAC7BN,IAAI,CAAC/Y,OAAO,CAAC,UAAShB,EAAE,EAAC;MAAEmE,OAAO,CAACsD,WAAW,CAACzH,EAAE,CAAC;IAAE,CAAC,CAAC;EAC1D,CAAC,MAAM;IACHyM,cAAc,CAACtI,OAAO,EAAEU,KAAK,CAAC;EAClC;AACJ;AAEA,SAAS4H,cAAcA,CAACtI,OAAO,EAAEU,KAAK,EAAE;EACpC,IAAI3D,KAAK,GAAGoD,gBAAgB,CAACH,OAAO,CAAC;EAErC,IAAIe,GAAG,GAAGiQ,SAAS,CAACjU,KAAK,EAAE,KAAK,CAAC;EACjC,IAAIsF,KAAK,GAAG2O,SAAS,CAACjU,KAAK,EAAE,OAAO,CAAC;EACrC,IAAIuF,MAAM,GAAG0O,SAAS,CAACjU,KAAK,EAAE,QAAQ,CAAC;EACvC,IAAI+D,IAAI,GAAGkQ,SAAS,CAACjU,KAAK,EAAE,MAAM,CAAC;EAEnC,IAAIwX,IAAI,GAAGlD,eAAe,CAACtU,KAAK,EAAE,UAAU,CAAC;EAC7C,IAAIyX,IAAI,GAAGnD,eAAe,CAACtU,KAAK,EAAE,WAAW,CAAC;EAC9C,IAAI2X,IAAI,GAAGrD,eAAe,CAACtU,KAAK,EAAE,aAAa,CAAC;EAChD,IAAI0X,IAAI,GAAGpD,eAAe,CAACtU,KAAK,EAAE,cAAc,CAAC;EAEjD,IAAIoZ,GAAG,GAAGtQ,gBAAgB,CAAC9I,KAAK,EAAE,WAAW,CAAC;EAE9C,IAAIqZ,eAAe,GAAGvQ,gBAAgB,CAAC9I,KAAK,EAAE,kBAAkB,CAAC;EACjEqZ,eAAe,GAAGld,UAAU,CAACkd,eAAe,CAAC;EAE7C,IAAIC,eAAe,GAAG7N,oBAAoB,CAAE3C,gBAAgB,CAAC9I,KAAK,EAAE,kBAAkB,CAAE,CAAC;EACzF,IAAIuZ,gBAAgB,GAAGpL,aAAa,CAAErF,gBAAgB,CAAC9I,KAAK,EAAE,mBAAmB,CAAE,CAAC;EACpF,IAAIwZ,kBAAkB,GAAGrL,aAAa,CAAErF,gBAAgB,CAAC9I,KAAK,EAAE,qBAAqB,CAAE,CAAC;EACxF,IAAIyZ,gBAAgB,GAAGtL,aAAa,CAAErF,gBAAgB,CAAC9I,KAAK,EAAE,mBAAmB,CAAE,CAAC;EACpF,IAAI0Z,cAAc,GAAGvL,aAAa,CAAErF,gBAAgB,CAAC9I,KAAK,EAAE,iBAAiB,CAAE,CAAC;;EAEhF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAI2Z,YAAY,EAAEC,gBAAgB;EAClC,IAAI7b,SAAS,EAAE;IACX4b,YAAY,GAAG3Z,KAAK,CAAC2Z,YAAY,CAAC,CAAa;IAC/C,IAAIA,YAAY,IAAI,UAAU,EAAE;MAC5BC,gBAAgB,GAAG3W,OAAO,CAACjD,KAAK,CAAC2Z,YAAY,CAAC,CAAC;MAC/C1W,OAAO,CAACjD,KAAK,CAAC2Z,YAAY,GAAG,MAAM;IACvC;EACJ;EAEA,IAAIzc,OAAO,CAACc,IAAI,IAAId,OAAO,CAAC2c,OAAO,GAAG,EAAE,EAAE;IACtC;IACA;IACA;IACA;IACAL,kBAAkB,GAAGrL,aAAa,CAAClL,OAAO,CAAC6W,YAAY,CAACN,kBAAkB,CAAC;EAC/E;EAEA,IAAIO,QAAQ,GAAGtF,QAAQ,CAACxR,OAAO,CAACY,qBAAqB,CAAC,CAAC,EAAE,gBAAgB,EAAEZ,OAAO,CAAC;;EAEnF;EACA;EACA;EACA,CAAC,YAAU;IACP,IAAIwS,IAAI,GAAG3M,gBAAgB,CAAC9I,KAAK,EAAE,MAAM,CAAC;IAC1C,IAAI2M,CAAC,GAAG,sBAAsB,CAACC,IAAI,CAAC6I,IAAI,CAAC;IACzC,IAAI9I,CAAC,EAAE;MACH,IAAIpN,CAAC,GAAGoN,CAAC,CAAC,CAAC,CAAC,CAACtN,KAAK,CAAC,QAAQ,CAAC;MAC5B,IAAI2E,GAAG,GAAGzE,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,GAAGwa,QAAQ,CAAC/V,GAAG,GAAG6E,UAAU,CAACtJ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGwa,QAAQ,CAAC/V,GAAG;MACzE,IAAIsB,KAAK,GAAG/F,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,GAAGwa,QAAQ,CAACzU,KAAK,GAAGuD,UAAU,CAACtJ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGwa,QAAQ,CAAChW,IAAI;MAC9E,IAAIwB,MAAM,GAAGhG,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,GAAGwa,QAAQ,CAACxU,MAAM,GAAGsD,UAAU,CAACtJ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGwa,QAAQ,CAAC/V,GAAG;MAC/E,IAAID,IAAI,GAAGxE,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,GAAGwa,QAAQ,CAAChW,IAAI,GAAG8E,UAAU,CAACtJ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGwa,QAAQ,CAAChW,IAAI;MAC5E,IAAI6T,GAAG,GAAG,IAAIlb,KAAK,CAAC,CAAC;MACrB,IAAI8Y,QAAQ,GAAG,IAAIhZ,IAAI,CAAC,CAAC,CACpBub,MAAM,CAAChU,IAAI,EAAEC,GAAG,CAAC,CACjBoU,MAAM,CAAC9S,KAAK,EAAEtB,GAAG,CAAC,CAClBoU,MAAM,CAAC9S,KAAK,EAAEC,MAAM,CAAC,CACrB6S,MAAM,CAACrU,IAAI,EAAEwB,MAAM,CAAC,CACpB8S,KAAK,CAAC,CAAC;MACZ9C,WAAW,CAACqC,GAAG,EAAEpC,QAAQ,CAAC;MAC1B7R,KAAK,CAACuC,MAAM,CAAC0R,GAAG,CAAC;MACjBjU,KAAK,GAAGiU,GAAG;MACX9E,aAAa,CAAC0C,QAAQ,CAAC;IAC3B;EACJ,CAAC,EAAE,CAAC;EAEJ,IAAIwE,KAAK,EAAEzY,CAAC,EAAE0Y,KAAK;EACnB,IAAIxS,OAAO,GAAGqB,gBAAgB,CAAC9I,KAAK,EAAE,SAAS,CAAC;EAEhD,IAAIyH,OAAO,IAAI,WAAW,EAAE;IACxB;IACA;IACA;IACAuS,KAAK,GAAG,EAAE;IACV,KAAKzY,CAAC,GAAG,CAAC,EAAE0Y,KAAK,GAAGhX,OAAO,CAACoE,QAAQ,EAAE9F,CAAC,GAAG0Y,KAAK,CAAC1X,MAAM,EAAE,EAAEhB,CAAC,EAAE;MACzDyY,KAAK,CAACva,IAAI,CAACwa,KAAK,CAAC1Y,CAAC,CAAC,CAACsC,qBAAqB,CAAC,CAAC,CAAC;IAChD;EACJ,CAAC,MAAM;IACHmW,KAAK,GAAG/W,OAAO,CAACiX,cAAc,CAAC,CAAC;IAChC,IAAIF,KAAK,CAACzX,MAAM,IAAI,CAAC,EAAE;MACnB;MACA;MACA;MACA;MACAyX,KAAK,GAAG,CAAE/W,OAAO,CAACY,qBAAqB,CAAC,CAAC,CAAE;IAC/C;EACJ;;EAEA;EACA;EACA;EACAmW,KAAK,GAAGG,WAAW,CAACH,KAAK,CAAC;EAE1B,KAAKzY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyY,KAAK,CAACzX,MAAM,EAAE,EAAEhB,CAAC,EAAE;IAC/B6Y,UAAU,CAACJ,KAAK,CAACzY,CAAC,CAAC,EAAEA,CAAC,KAAK,CAAC,EAAEA,CAAC,IAAIyY,KAAK,CAACzX,MAAM,GAAG,CAAC,CAAC;EACxD;;EAEA;EACA;EACA;EACA,IAAIU,OAAO,CAACxB,OAAO,IAAI,GAAG,IAAIwB,OAAO,CAACqN,IAAI,IAAI,CAAC,MAAM,CAACzP,IAAI,CAACoC,OAAO,CAACyF,YAAY,CAAC,MAAM,CAAC,CAAC,EAAE;IACtF,IAAI,CAAC/K,QAAQ,CAAC4G,WAAW,IAAI,CAACtE,OAAO,CAACgD,OAAO,EAAEtF,QAAQ,CAAC4G,WAAW,CAAC,EAAE;MAClE,IAAIyL,CAAC,GAAGtP,QAAQ,CAACsJ,WAAW,CAAC,CAAC;MAC9BgG,CAAC,CAACpF,kBAAkB,CAAC3H,OAAO,CAAC;MAC7B9F,KAAK,CAAC6S,CAAC,CAACkK,cAAc,CAAC,CAAC,CAAC,CAACpa,OAAO,CAAC,UAAS2K,GAAG,EAAC;QAC3C,IAAI4P,CAAC,GAAG,IAAI3d,KAAK,CAAC,CAAC;QACnB2d,CAAC,CAACC,QAAQ,GAAG;UACTrM,GAAG,EAAMhL,OAAO,CAACqN,IAAI;UACrBtM,GAAG,EAAMyG,GAAG,CAACzG,GAAG;UAChBsB,KAAK,EAAImF,GAAG,CAACnF,KAAK;UAClBC,MAAM,EAAGkF,GAAG,CAAClF,MAAM;UACnBxB,IAAI,EAAK0G,GAAG,CAAC1G;QACjB,CAAC;QACDJ,KAAK,CAACuC,MAAM,CAACmU,CAAC,CAAC;MACnB,CAAC,CAAC;IACN;EACJ;EAEA,IAAIL,KAAK,CAACzX,MAAM,GAAG,CAAC,IAAIkF,OAAO,IAAI,WAAW,IAAI,CAACxE,OAAO,CAACyF,YAAY,CAAC,iBAAiB,CAAC,EAAE;IACxF6R,UAAU,CAACP,KAAK,CAAC,CAAC,CAAC,CAAC;EACxB;;EAEA;EACA;EACA,CAAC,YAAU;IACP,SAASQ,MAAMA,CAAA,EAAG;MACd,IAAIhF,QAAQ,GAAGwB,eAAe,CAAC/T,OAAO,EAAE8W,QAAQ,EAAE,SAAS,CAAC;MAC5D,IAAInC,GAAG,GAAG,IAAIlb,KAAK,CAAC,CAAC;MACrB6Y,WAAW,CAACqC,GAAG,EAAEpC,QAAQ,CAAC;MAC1B7R,KAAK,CAACuC,MAAM,CAAC0R,GAAG,CAAC;MACjBjU,KAAK,GAAGiU,GAAG;MACX9E,aAAa,CAAC0C,QAAQ,CAAC;IAC3B;IACA,IAAIiF,WAAW,CAACxX,OAAO,CAAC,EAAE;MACtBuX,MAAM,CAAC,CAAC;IACZ,CAAC,MAAM,IAAI,uBAAuB,CAAC3Z,IAAI,CAACiI,gBAAgB,CAAC9I,KAAK,EAAE,UAAU,CAAC,CAAC,EAAE;MAC1Ewa,MAAM,CAAC,CAAC;IACZ,CAAC,MAAM,IAAI,uBAAuB,CAAC3Z,IAAI,CAACiI,gBAAgB,CAAC9I,KAAK,EAAE,YAAY,CAAC,CAAC,EAAE;MAC5Ewa,MAAM,CAAC,CAAC;IACZ,CAAC,MAAM,IAAI,uBAAuB,CAAC3Z,IAAI,CAACiI,gBAAgB,CAAC9I,KAAK,EAAE,YAAY,CAAC,CAAC,EAAE;MAC5Ewa,MAAM,CAAC,CAAC;IACZ;EACJ,CAAC,EAAE,CAAC;EAEJ,IAAI,CAACE,iBAAiB,CAACzX,OAAO,EAAEU,KAAK,CAAC,IAAI,CAACgX,iBAAiB,CAAC1X,OAAO,EAAEU,KAAK,CAAC,EAAE;IAC1EiX,cAAc,CAAC3X,OAAO,EAAEU,KAAK,CAAC;EAClC;EAEA,IAAI5F,SAAS,IAAI4b,YAAY,IAAI,UAAU,EAAE;IACzC1W,OAAO,CAACjD,KAAK,CAAC2Z,YAAY,GAAGC,gBAAgB;EACjD;EAEA,OAAOjW,KAAK,CAAC,CAAC;;EAEd,SAASwW,WAAWA,CAACH,KAAK,EAAE;IACxB,IAAI,OAAO,CAACnZ,IAAI,CAACoC,OAAO,CAACxB,OAAO,CAAC,EAAE;MAC/B,IAAI+H,KAAK,GAAG7L,QAAQ,CAAC6L,KAAK;MAC1B,IAAIA,KAAK,IAAIV,gBAAgB,CAACU,KAAK,CAACxJ,KAAK,EAAE,iBAAiB,CAAC,IAAI,UAAU,EAAE;QACzE,IAAI6a,eAAe,GAAG5G,SAAS,CAACzK,KAAK,CAACxJ,KAAK,EAAE,MAAM,CAAC,CAAC4H,KAAK;QAC1D,IAAIkT,cAAc,GAAG7G,SAAS,CAACzK,KAAK,CAACxJ,KAAK,EAAE,KAAK,CAAC,CAAC4H,KAAK;QACxD;QACA,IAAIiT,eAAe,KAAK,CAAC,IAAIC,cAAc,KAAK,CAAC,EAAE;UAC/C,OAAOd,KAAK,CAAC,CAAC;QAClB;QACA,IAAIe,QAAQ,GAAGvR,KAAK,CAACvG,OAAO,CAACY,qBAAqB,CAAC,CAAC;QACpD,IAAImX,SAAS,GAAGxR,KAAK,CAACvG,OAAO,CAACgY,IAAI,CAAC,CAAC,CAAC,CAAChB,KAAK,CAAC,CAAC,CAAC;QAC9C,IAAIiB,YAAY,GAAGF,SAAS,CAACnX,qBAAqB,CAAC,CAAC;QACpD,IAAIqX,YAAY,CAAClX,GAAG,IAAI+W,QAAQ,CAAC/W,GAAG,IAAIkX,YAAY,CAACnX,IAAI,IAAIgX,QAAQ,CAAChX,IAAI,EAAE;UACxE,OAAO5G,KAAK,CAAC6c,KAAK,CAAC,CAAC5L,GAAG,CAAC,UAAS3D,GAAG,EAAC;YACjC,OAAO;cACH1G,IAAI,EAAK0G,GAAG,CAAC1G,IAAI,GAAG8W,eAAe;cACnC7W,GAAG,EAAMyG,GAAG,CAACzG,GAAG,GAAG8W,cAAc;cACjCxV,KAAK,EAAImF,GAAG,CAACnF,KAAK,GAAGuV,eAAe;cACpCtV,MAAM,EAAGkF,GAAG,CAAClF,MAAM,GAAGuV,cAAc;cACpCxQ,MAAM,EAAGG,GAAG,CAACH,MAAM;cACnB1C,KAAK,EAAI6C,GAAG,CAAC7C;YACjB,CAAC;UACL,CAAC,CAAC;QACN;MACJ;IACJ;IACA,OAAOoS,KAAK;EAChB;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAASmB,QAAQA,CAAClO,KAAK,EAAEtC,GAAG,EAAEyQ,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,EAAE,EAAEC,EAAE,EAAEzG,SAAS,EAAE;IAClE,IAAIqG,IAAI,IAAI,CAAC,EAAE;MACX;IACJ;IAEA,IAAIrI,IAAI;MAAE9U,IAAI,GAAG,IAAIvB,KAAK,CAAC,CAAC;IAC5BoH,YAAY,CAAC7F,IAAI,EAAE8W,SAAS,CAAC;IAC7BpR,KAAK,CAACuC,MAAM,CAACjI,IAAI,CAAC;IAElBsW,cAAc,CAACgH,EAAE,CAAC;IAClBhH,cAAc,CAACiH,EAAE,CAAC;;IAElB;IACAzI,IAAI,GAAG,IAAIvW,IAAI,CAAC;MACZqb,IAAI,EAAE;QAAE5K,KAAK,EAAEA;MAAM,CAAC;MACtB6K,MAAM,EAAE;IACZ,CAAC,CAAC;IACF7Z,IAAI,CAACiI,MAAM,CAAC6M,IAAI,CAAC;IACjBA,IAAI,CAACgF,MAAM,CAACwD,EAAE,CAAC1Y,CAAC,GAAG2P,IAAI,CAACxH,GAAG,CAACuQ,EAAE,CAAC1Y,CAAC,EAAEwY,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAC3CjD,MAAM,CAACzN,GAAG,IAAI6Q,EAAE,CAAC3Y,CAAC,GAAG2P,IAAI,CAACxH,GAAG,CAACwQ,EAAE,CAAC3Y,CAAC,EAAEyY,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CACpDlD,MAAM,CAACzN,GAAG,GAAG6H,IAAI,CAACxH,GAAG,CAACwQ,EAAE,CAAC3Y,CAAC,EAAEyY,MAAM,CAAC,EAAEF,IAAI,CAAC,CAC1ChD,MAAM,CAAC5F,IAAI,CAACxH,GAAG,CAACuQ,EAAE,CAAC1Y,CAAC,EAAEwY,KAAK,CAAC,EAAED,IAAI,CAAC,CACnC/C,KAAK,CAAC,CAAC;IAEZ,IAAIkD,EAAE,CAAC1Y,CAAC,EAAE;MACN4Y,eAAe,CAACJ,KAAK,EAAEE,EAAE,EAAE,CAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEA,EAAE,CAAC1Y,CAAC,EAAE,CAAC,CAAE,CAAC;IACxD;IAEA,IAAI2Y,EAAE,CAAC3Y,CAAC,EAAE;MACN4Y,eAAe,CAACH,MAAM,EAAEE,EAAE,EAAE,CAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE7Q,GAAG,GAAG6Q,EAAE,CAAC3Y,CAAC,EAAE,CAAC,CAAE,CAAC;IAC9D;;IAEA;IACA;IACA,SAAS4Y,eAAeA,CAACH,MAAM,EAAEtL,CAAC,EAAE+E,SAAS,EAAE;MAC3C,IAAIxH,KAAK,GAAGiF,IAAI,CAAC4C,EAAE,GAAC,CAAC,GAAGkG,MAAM,IAAIA,MAAM,GAAGF,IAAI,CAAC;;MAEhD;MACA;MACA,IAAIM,EAAE,GAAG;QACL7Y,CAAC,EAAEmN,CAAC,CAACnN,CAAC,GAAGyY,MAAM;QACfxY,CAAC,EAAEkN,CAAC,CAAClN,CAAC,GAAGsY;MACb,CAAC;MAED,IAAIrI,IAAI,GAAG,IAAIvW,IAAI,CAAC;QAChBqb,IAAI,EAAE;UAAE5K,KAAK,EAAEA;QAAM,CAAC;QACtB6K,MAAM,EAAE;MACZ,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;MAEfjU,YAAY,CAACiP,IAAI,EAAEgC,SAAS,CAAC;MAE7BW,YAAY,CAAC3C,IAAI,EAAE,CAAC,EAAE/C,CAAC,CAAClN,CAAC,EAAE;QACvBkV,UAAU,EAAE,CAAC,EAAE;QACfC,QAAQ,EAAE,CAAC/C,gBAAgB,CAAC3H,KAAK,CAAC;QAClC2K,OAAO,EAAElI,CAAC,CAACnN,CAAC;QACZsV,OAAO,EAAEnI,CAAC,CAAClN;MACf,CAAC,CAAC;MAEF,IAAI4Y,EAAE,CAAC7Y,CAAC,GAAG,CAAC,IAAI6Y,EAAE,CAAC5Y,CAAC,GAAG,CAAC,EAAE;QACtBiQ,IAAI,CAACqF,MAAM,CAACsD,EAAE,CAAC7Y,CAAC,GAAG2P,IAAI,CAACmJ,GAAG,CAACpO,KAAK,CAAC,EAAEyC,CAAC,CAAClN,CAAC,GAAG4Y,EAAE,CAAC5Y,CAAC,GAAG0P,IAAI,CAACoJ,GAAG,CAACrO,KAAK,CAAC,CAAC;QACjEmI,YAAY,CAAC3C,IAAI,EAAE,CAAC,EAAE/C,CAAC,CAAClN,CAAC,EAAE;UACvBkV,UAAU,EAAE,CAAC9C,gBAAgB,CAAC3H,KAAK,CAAC;UACpC0K,QAAQ,EAAE,CAAC,EAAE;UACbC,OAAO,EAAEwD,EAAE,CAAC7Y,CAAC;UACbsV,OAAO,EAAEuD,EAAE,CAAC5Y,CAAC;UACb+Y,aAAa,EAAE;QACnB,CAAC,CAAC;MACN,CAAC,MACI,IAAIH,EAAE,CAAC7Y,CAAC,GAAG,CAAC,EAAE;QACfkQ,IAAI,CAACqF,MAAM,CAACsD,EAAE,CAAC7Y,CAAC,EAAEuY,IAAI,CAAC,CAClBhD,MAAM,CAAC,CAAC,EAAEgD,IAAI,CAAC;MACxB,CAAC,MACI;QACDrI,IAAI,CAACqF,MAAM,CAACsD,EAAE,CAAC7Y,CAAC,EAAEuY,IAAI,CAAC,CAClBhD,MAAM,CAACsD,EAAE,CAAC7Y,CAAC,EAAE,CAAC,CAAC;MACxB;MAEA5E,IAAI,CAACiI,MAAM,CAAC6M,IAAI,CAACsF,KAAK,CAAC,CAAC,CAAC;IAC7B;EACJ;EAEA,SAASyD,cAAcA,CAACrR,GAAG,EAAE;IACzB,IAAIsR,UAAU,GAAG,IAAIrf,KAAK,CAAC,CAAC;IAC5B6Y,WAAW,CAACwG,UAAU,EAAExE,QAAQ,CAAC9M,GAAG,EAAE+M,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,CAAC,CAAC;IAC9DhU,KAAK,CAACuC,MAAM,CAAC6V,UAAU,CAAC;IAExB,IAAI1C,eAAe,EAAE;MACjB,IAAItG,IAAI,GAAG,IAAIvW,IAAI,CAAC;QAChBqb,IAAI,EAAE;UAAE5K,KAAK,EAAEoM,eAAe,CAAC7H,SAAS,CAAC;QAAE,CAAC;QAC5CsG,MAAM,EAAE;MACZ,CAAC,CAAC;MACF/E,IAAI,CAACgF,MAAM,CAACtN,GAAG,CAAC1G,IAAI,EAAE0G,GAAG,CAACzG,GAAG,CAAC,CACzBoU,MAAM,CAAC3N,GAAG,CAACnF,KAAK,EAAEmF,GAAG,CAACzG,GAAG,CAAC,CAC1BoU,MAAM,CAAC3N,GAAG,CAACnF,KAAK,EAAEmF,GAAG,CAAClF,MAAM,CAAC,CAC7B6S,MAAM,CAAC3N,GAAG,CAAC1G,IAAI,EAAE0G,GAAG,CAAClF,MAAM,CAAC,CAC5B8S,KAAK,CAAC,CAAC;MACZ0D,UAAU,CAAC7V,MAAM,CAAC6M,IAAI,CAAC;IAC3B;IAEA,KAAK,IAAIxR,CAAC,GAAG+X,eAAe,CAAC/W,MAAM,EAAE,EAAEhB,CAAC,IAAI,CAAC,GAAG;MAC5Cya,iBAAiB,CACbD,UAAU,EAAEtR,GAAG,EACf6O,eAAe,CAAC/X,CAAC,CAAC,EAClBgY,gBAAgB,CAAChY,CAAC,GAAGgY,gBAAgB,CAAChX,MAAM,CAAC,EAC7CiX,kBAAkB,CAACjY,CAAC,GAAGiY,kBAAkB,CAACjX,MAAM,CAAC,EACjDkX,gBAAgB,CAAClY,CAAC,GAAGkY,gBAAgB,CAAClX,MAAM,CAAC,EAC7CmX,cAAc,CAACnY,CAAC,GAAGmY,cAAc,CAACnX,MAAM,CAC5C,CAAC;IACL;EACJ;EAEA,SAASyZ,iBAAiBA,CAACrY,KAAK,EAAE8G,GAAG,EAAEsR,UAAU,EAAExC,gBAAgB,EAAEC,kBAAkB,EAAEC,gBAAgB,EAAEC,cAAc,EAAE;IACvH,IAAI,CAACqC,UAAU,IAAKA,UAAU,IAAI,MAAO,EAAE;MACvC;IACJ;IAEA,IAAIA,UAAU,CAACjO,IAAI,IAAI,KAAK,EAAE;MAC1B,IAAI+D,GAAG,GAAGnU,WAAW,CAACqe,UAAU,CAAC9N,GAAG,CAAC;MACrC,IAAI4D,GAAG,IAAIA,GAAG,CAACjK,KAAK,GAAG,CAAC,IAAIiK,GAAG,CAACvH,MAAM,GAAG,CAAC,EAAE;QACxC2R,mBAAmB,CAACtY,KAAK,EAAE8G,GAAG,EAAEoH,GAAG,CAACjK,KAAK,EAAEiK,GAAG,CAACvH,MAAM,EAAE,UAAS3G,KAAK,EAAEvF,IAAI,EAAC;UACxEuF,KAAK,CAACuC,MAAM,CAAC,IAAIvJ,KAAK,CAACof,UAAU,CAAC9N,GAAG,EAAE7P,IAAI,CAAC,CAAC;QACjD,CAAC,CAAC;MACN;IACJ,CAAC,MAAM,IAAI2d,UAAU,CAACjO,IAAI,IAAI,QAAQ,EAAE;MACpCmO,mBAAmB,CAACtY,KAAK,EAAE8G,GAAG,EAAEA,GAAG,CAAC7C,KAAK,EAAE6C,GAAG,CAACH,MAAM,EAAE4R,gBAAgB,CAACH,UAAU,CAAC,CAAC;IACxF,CAAC,MAAM;MACH;IACJ;IAEA,SAASE,mBAAmBA,CAACtY,KAAK,EAAE8G,GAAG,EAAE0R,SAAS,EAAEC,UAAU,EAAEC,QAAQ,EAAE;MACtE,IAAIC,YAAY,GAAGH,SAAS,GAAGC,UAAU;QAAElL,CAAC;;MAE5C;MACA,IAAIqL,MAAM,GAAG9R,GAAG;MAChB,IAAIgP,gBAAgB,IAAI,aAAa,EAAE;QACnC8C,MAAM,GAAG9H,QAAQ,CAAC8H,MAAM,EAAE,gBAAgB,EAAEtZ,OAAO,CAAC;QACpDsZ,MAAM,GAAG9H,QAAQ,CAAC8H,MAAM,EAAE,WAAW,EAAEtZ,OAAO,CAAC;MACnD,CAAC,MAAM,IAAIwW,gBAAgB,IAAI,aAAa,EAAE;QAC1C8C,MAAM,GAAG9H,QAAQ,CAAC8H,MAAM,EAAE,gBAAgB,EAAEtZ,OAAO,CAAC;MACxD;MAEA,IAAI,CAAC,wBAAwB,CAACpC,IAAI,CAAC6Y,cAAc,CAAC,EAAE;QAChD,IAAIA,cAAc,IAAI,SAAS,EAAE;UAC7BxI,CAAC,GAAGsB,IAAI,CAACzH,GAAG,CAACwR,MAAM,CAAC3U,KAAK,GAAGuU,SAAS,EACxBI,MAAM,CAACjS,MAAM,GAAG8R,UAAU,CAAC;UACxCD,SAAS,IAAIjL,CAAC;UACdkL,UAAU,IAAIlL,CAAC;QACnB,CAAC,MACI,IAAIwI,cAAc,IAAI,OAAO,EAAE;UAChCxI,CAAC,GAAGsB,IAAI,CAACxH,GAAG,CAACuR,MAAM,CAAC3U,KAAK,GAAGuU,SAAS,EACxBI,MAAM,CAACjS,MAAM,GAAG8R,UAAU,CAAC;UACxCD,SAAS,IAAIjL,CAAC;UACdkL,UAAU,IAAIlL,CAAC;QACnB,CAAC,MACI;UACD,IAAIsL,IAAI,GAAG9C,cAAc,CAACra,KAAK,CAAC,MAAM,CAAC;UACvC;UACA,IAAI,IAAI,CAACwB,IAAI,CAAC2b,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;YACpBL,SAAS,GAAGI,MAAM,CAAC3U,KAAK,GAAGiB,UAAU,CAAC2T,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;UACxD,CAAC,MAAM;YACHL,SAAS,GAAGtT,UAAU,CAAC2T,IAAI,CAAC,CAAC,CAAC,CAAC;UACnC;UACA;UACA,IAAIA,IAAI,CAACja,MAAM,IAAI,CAAC,IAAIia,IAAI,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE;YACvCJ,UAAU,GAAGD,SAAS,GAAGG,YAAY;UACzC,CAAC,MAAM,IAAI,IAAI,CAACzb,IAAI,CAAC2b,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;YAC3BJ,UAAU,GAAGG,MAAM,CAACjS,MAAM,GAAGzB,UAAU,CAAC2T,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;UAC1D,CAAC,MAAM;YACHJ,UAAU,GAAGvT,UAAU,CAAC2T,IAAI,CAAC,CAAC,CAAC,CAAC;UACpC;QACJ;MACJ;MAEA,IAAI5Y,GAAG,GAAG9C,MAAM,CAAC0Y,kBAAkB,CAAC;;MAEpC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,QAAQ5V,GAAG;QACT,KAAK,QAAQ;UAAGA,GAAG,GAAG,UAAU;UAAE;QAClC,KAAK,KAAK;UAAMA,GAAG,GAAG,OAAO;UAAE;QAC/B,KAAK,MAAM;UAAKA,GAAG,GAAG,OAAO;UAAE;QAC/B,KAAK,OAAO;UAAIA,GAAG,GAAG,UAAU;UAAE;QAClC,KAAK,QAAQ;UAAGA,GAAG,GAAG,SAAS;UAAE;MACnC;MAEAA,GAAG,GAAGA,GAAG,CAACvE,KAAK,CAAC,KAAK,CAAC;MACtB,IAAIuE,GAAG,CAACrB,MAAM,IAAI,CAAC,EAAE;QACjBqB,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK;MAClB;MAEA,IAAI,IAAI,CAAC/C,IAAI,CAAC+C,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;QACnBA,GAAG,CAAC,CAAC,CAAC,GAAGiF,UAAU,CAACjF,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI2Y,MAAM,CAAC3U,KAAK,GAAGuU,SAAS,CAAC;MAClE,CAAC,MAAM;QACHvY,GAAG,CAAC,CAAC,CAAC,GAAGiF,UAAU,CAACjF,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/B;MACA,IAAI,IAAI,CAAC/C,IAAI,CAAC+C,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;QACnBA,GAAG,CAAC,CAAC,CAAC,GAAGiF,UAAU,CAACjF,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI2Y,MAAM,CAACjS,MAAM,GAAG8R,UAAU,CAAC;MACpE,CAAC,MAAM;QACHxY,GAAG,CAAC,CAAC,CAAC,GAAGiF,UAAU,CAACjF,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/B;MAEA,IAAIxF,IAAI,GAAG,IAAIvC,GAAG,CAACgB,IAAI,CAAC,CAAE0f,MAAM,CAACxY,IAAI,GAAGH,GAAG,CAAC,CAAC,CAAC,EAAE2Y,MAAM,CAACvY,GAAG,GAAGJ,GAAG,CAAC,CAAC,CAAC,CAAE,EAAE,CAAEuY,SAAS,EAAEC,UAAU,CAAE,CAAC;;MAEjG;MACA;MACA;;MAEA,SAASK,IAAIA,CAAA,EAAG;QACZ,OAAOre,IAAI,CAAC6W,MAAM,CAACpS,CAAC,GAAG4H,GAAG,CAAC1G,IAAI,EAAE;UAC7B3F,IAAI,CAAC6W,MAAM,CAACpS,CAAC,IAAIsZ,SAAS;QAC9B;MACJ;MAEA,SAASO,IAAIA,CAAA,EAAG;QACZ,OAAOte,IAAI,CAAC6W,MAAM,CAACnS,CAAC,GAAG2H,GAAG,CAACzG,GAAG,EAAE;UAC5B5F,IAAI,CAAC6W,MAAM,CAACnS,CAAC,IAAIsZ,UAAU;QAC/B;MACJ;MAEA,SAASO,OAAOA,CAAA,EAAG;QACf,OAAOve,IAAI,CAAC6W,MAAM,CAACpS,CAAC,GAAG4H,GAAG,CAACnF,KAAK,EAAE;UAC9B+W,QAAQ,CAAC1Y,KAAK,EAAEvF,IAAI,CAAC+C,KAAK,CAAC,CAAC,CAAC;UAC7B/C,IAAI,CAAC6W,MAAM,CAACpS,CAAC,IAAIsZ,SAAS;QAC9B;MACJ;MAEA,IAAI5C,gBAAgB,IAAI,WAAW,EAAE;QACjC8C,QAAQ,CAAC1Y,KAAK,EAAEvF,IAAI,CAAC;MACzB,CAAC,MACI,IAAImb,gBAAgB,IAAI,UAAU,EAAE;QACrCkD,IAAI,CAAC,CAAC;QACNE,OAAO,CAAC,CAAC;MACb,CAAC,MACI,IAAIpD,gBAAgB,IAAI,UAAU,EAAE;QACrCmD,IAAI,CAAC,CAAC;QACN,OAAOte,IAAI,CAAC6W,MAAM,CAACnS,CAAC,GAAG2H,GAAG,CAAClF,MAAM,EAAE;UAC/B8W,QAAQ,CAAC1Y,KAAK,EAAEvF,IAAI,CAAC+C,KAAK,CAAC,CAAC,CAAC;UAC7B/C,IAAI,CAAC6W,MAAM,CAACnS,CAAC,IAAIsZ,UAAU;QAC/B;MACJ,CAAC,MACI,IAAI7C,gBAAgB,IAAI,QAAQ,EAAE;QACnCkD,IAAI,CAAC,CAAC;QACNC,IAAI,CAAC,CAAC;QACN,IAAIzH,MAAM,GAAG7W,IAAI,CAAC6W,MAAM,CAAC9T,KAAK,CAAC,CAAC;QAChC,OAAO/C,IAAI,CAAC6W,MAAM,CAACnS,CAAC,GAAG2H,GAAG,CAAClF,MAAM,EAAE;UAC/BnH,IAAI,CAAC6W,MAAM,CAACpS,CAAC,GAAGoS,MAAM,CAACpS,CAAC;UACxB8Z,OAAO,CAAC,CAAC;UACTve,IAAI,CAAC6W,MAAM,CAACnS,CAAC,IAAIsZ,UAAU;QAC/B;MACJ;IACJ;EACJ;EAEA,SAAS7B,UAAUA,CAAA,EAAG;IAClB,IAAIqC,aAAa,GAAG9T,gBAAgB,CAAC9I,KAAK,EAAE,iBAAiB,CAAC;IAC9D,IAAI4c,aAAa,IAAI,MAAM,EAAE;MACzB;IACJ;IACA,IAAIC,iBAAiB,GAAG/T,gBAAgB,CAAC9I,KAAK,EAAE,qBAAqB,CAAC;IAEtE,SAAS8c,WAAWA,CAAC5L,CAAC,EAAE;MACpBiD,SAAS,CAAClR,OAAO,EAAE,YAAU;QACzBA,OAAO,CAACjD,KAAK,CAAC0H,QAAQ,GAAG,UAAU;QACnC,IAAIqV,MAAM,GAAG9Z,OAAO,CAACO,aAAa,CAACoD,aAAa,CAACpJ,oBAAoB,CAAC;QACtEuf,MAAM,CAAC/c,KAAK,CAAC0H,QAAQ,GAAG,UAAU;QAClCqV,MAAM,CAAC/c,KAAK,CAAC2H,SAAS,GAAG,YAAY;QACrC,IAAIkV,iBAAiB,IAAI,SAAS,EAAE;UAChCE,MAAM,CAAC/c,KAAK,CAAC4H,KAAK,GAAG,KAAK;UAC1BmV,MAAM,CAAC/c,KAAK,CAAC+D,IAAI,GAAG,QAAQ;UAC5BgZ,MAAM,CAAC/c,KAAK,CAACgd,SAAS,GAAG,OAAO;QACpC,CAAC,MAAM;UACHD,MAAM,CAAC/c,KAAK,CAAC+D,IAAI,GAAG,KAAK;QAC7B;QACAmN,CAAC,CAAC6L,MAAM,CAAC;QACT9Z,OAAO,CAAC+E,YAAY,CAAC+U,MAAM,EAAE9Z,OAAO,CAAChB,UAAU,CAAC;QAChDuC,aAAa,CAACuY,MAAM,EAAEpZ,KAAK,CAAC;QAC5BV,OAAO,CAACsD,WAAW,CAACwW,MAAM,CAAC;MAC/B,CAAC,CAAC;IACN;IAEA,SAASE,YAAYA,CAAC/L,CAAC,EAAE;MACrB,IAAI3R,CAAC,GAAG0D,OAAO,CAACjC,UAAU,CAACqG,QAAQ;MACnC,IAAI6V,CAAC,GAAGja,OAAO,CAACyF,YAAY,CAAC,mBAAmB,CAAC;MACjD,IAAIwU,CAAC,IAAI,IAAI,EAAE;QACX,OAAOhM,CAAC,CAACgM,CAAC,GAAC,CAAC,EAAE3d,CAAC,CAACgD,MAAM,CAAC;MAC3B;MACA,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhC,CAAC,CAACgD,MAAM,EAAE,EAAEhB,CAAC,EAAE;QAC/B,IAAIhC,CAAC,CAACgC,CAAC,CAAC,KAAK0B,OAAO,EAAE;UAClB,OAAOiO,CAAC,CAAC3P,CAAC,EAAEhC,CAAC,CAACgD,MAAM,CAAC;QACzB;MACJ;IACJ;IAEA,QAAQqa,aAAa;MACnB,KAAK,QAAQ;MACb,KAAK,MAAM;MACX,KAAK,QAAQ;QACXE,WAAW,CAAC,UAASC,MAAM,EAAC;UACxBA,MAAM,CAACI,SAAS,GAAG,QAAQ;UAC3BJ,MAAM,CAACvV,YAAY,CAAC/J,iBAAiB,EAAEmf,aAAa,CAAC;QACzD,CAAC,CAAC;QACF;MAEF,KAAK,SAAS;MACd,KAAK,sBAAsB;QACzBE,WAAW,CAAC,UAASC,MAAM,EAAC;UACxBE,YAAY,CAAC,UAASG,GAAG,EAAC;YACtB,EAAEA,GAAG;YACL,IAAIR,aAAa,IAAI,sBAAsB,IAAIQ,GAAG,GAAG,EAAE,EAAE;cACrDA,GAAG,GAAG,GAAG,GAAGA,GAAG;YACnB;YACAL,MAAM,CAACI,SAAS,GAAGC,GAAG,GAAG,GAAG;UAChC,CAAC,CAAC;QACN,CAAC,CAAC;QACF;MAEF,KAAK,aAAa;MAClB,KAAK,aAAa;QAChBN,WAAW,CAAC,UAASC,MAAM,EAAC;UACxBE,YAAY,CAAC,UAASG,GAAG,EAAC;YACtBA,GAAG,GAAGrhB,aAAa,CAACqhB,GAAG,GAAG,CAAC,CAAC;YAC5B,IAAIR,aAAa,IAAI,aAAa,EAAE;cAChCQ,GAAG,GAAGA,GAAG,CAACpJ,WAAW,CAAC,CAAC;YAC3B;YACA+I,MAAM,CAACI,SAAS,GAAGC,GAAG,GAAG,GAAG;UAChC,CAAC,CAAC;QACN,CAAC,CAAC;QACF;MAEF,KAAK,aAAa;MAClB,KAAK,aAAa;MAClB,KAAK,aAAa;MAClB,KAAK,aAAa;QAChBN,WAAW,CAAC,UAASC,MAAM,EAAC;UACxBE,YAAY,CAAC,UAASG,GAAG,EAAC;YACtBA,GAAG,GAAG/K,YAAY,CAAC+K,GAAG,CAAC;YACvB,IAAI,SAAS,CAACvc,IAAI,CAAC+b,aAAa,CAAC,EAAE;cAC/BQ,GAAG,GAAGA,GAAG,CAACpJ,WAAW,CAAC,CAAC;YAC3B;YACA+I,MAAM,CAACI,SAAS,GAAGC,GAAG,GAAG,GAAG;UAChC,CAAC,CAAC;QACN,CAAC,CAAC;QACF;IACJ;EACJ;;EAEA;EACA,SAAShD,UAAUA,CAAC3P,GAAG,EAAExB,OAAO,EAAEoU,MAAM,EAAE;IACtC,IAAI5S,GAAG,CAAC7C,KAAK,KAAK,CAAC,IAAI6C,GAAG,CAACH,MAAM,KAAK,CAAC,EAAE;MACrC;IACJ;IAEAwR,cAAc,CAACrR,GAAG,CAAC;IAEnB,IAAI6S,cAAc,GAAIvZ,IAAI,CAAC6D,KAAK,GAAG,CAAC,KAAMqB,OAAO,IAAImQ,GAAG,IAAI,KAAK,IAAMiE,MAAM,IAAIjE,GAAG,IAAI,KAAM,CAAE;IAChG,IAAImE,eAAe,GAAIjY,KAAK,CAACsC,KAAK,GAAG,CAAC,KAAMyV,MAAM,IAAIjE,GAAG,IAAI,KAAK,IAAMnQ,OAAO,IAAImQ,GAAG,IAAI,KAAM,CAAE;;IAElG;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA,IAAIpV,GAAG,CAAC4D,KAAK,KAAK,CAAC,IAAI7D,IAAI,CAAC6D,KAAK,KAAK,CAAC,IAAItC,KAAK,CAACsC,KAAK,KAAK,CAAC,IAAIrC,MAAM,CAACqC,KAAK,KAAK,CAAC,EAAE;MAChF;IACJ;;IAEA;IACA;IACA,IAAI5D,GAAG,CAACiJ,KAAK,IAAI3H,KAAK,CAAC2H,KAAK,IAAIjJ,GAAG,CAACiJ,KAAK,IAAI1H,MAAM,CAAC0H,KAAK,IAAIjJ,GAAG,CAACiJ,KAAK,IAAIlJ,IAAI,CAACkJ,KAAK,EAAE;MAElF;MACA,IAAIjJ,GAAG,CAAC4D,KAAK,IAAItC,KAAK,CAACsC,KAAK,IAAI5D,GAAG,CAAC4D,KAAK,IAAIrC,MAAM,CAACqC,KAAK,IAAI5D,GAAG,CAAC4D,KAAK,IAAI7D,IAAI,CAAC6D,KAAK,EACpF;QACI,IAAI0V,cAAc,IAAIC,eAAe,EAAE;UACnC;UACA9S,GAAG,GAAGgK,QAAQ,CAAChK,GAAG,EAAEzG,GAAG,CAAC4D,KAAK,GAAC,CAAC,CAAC;;UAEhC;UACA,IAAImL,IAAI,GAAGiE,eAAe,CAAC/T,OAAO,EAAEwH,GAAG,EAAEzG,GAAG,CAAC4D,KAAK,GAAC,CAAC,CAAC;UACrDmL,IAAI,CAAC1U,OAAO,CAACyZ,MAAM,GAAG;YAClB7K,KAAK,EAAEjJ,GAAG,CAACiJ,KAAK;YAChBrF,KAAK,EAAE5D,GAAG,CAAC4D;UACf,CAAC;UACDjE,KAAK,CAACuC,MAAM,CAAC6M,IAAI,CAAC;UAClB;QACJ;MACJ;IACJ;;IAEA;IACA;IACA,IAAIyE,IAAI,CAAC3U,CAAC,KAAK,CAAC,IAAI4U,IAAI,CAAC5U,CAAC,KAAK,CAAC,IAAI6U,IAAI,CAAC7U,CAAC,KAAK,CAAC,IAAI8U,IAAI,CAAC9U,CAAC,KAAK,CAAC,EAAE;MAC9D;MACA;MACA,IAAImB,GAAG,CAAC4D,KAAK,GAAG,CAAC,IAAI7D,IAAI,CAAC6D,KAAK,GAAG,CAAC,IAAItC,KAAK,CAACsC,KAAK,GAAG,CAAC,IAAIrC,MAAM,CAACqC,KAAK,GAAG,CAAC,EAAE;QACxE;QACA,IAAI5D,GAAG,CAAC4D,KAAK,GAAG,CAAC,EAAE;UACfjE,KAAK,CAACuC,MAAM,CACR,IAAI1J,IAAI,CAAC;YACLsb,MAAM,EAAE;cAAElQ,KAAK,EAAE5D,GAAG,CAAC4D,KAAK;cAAEqF,KAAK,EAAEjJ,GAAG,CAACiJ;YAAM;UACjD,CAAC,CAAC,CACG8K,MAAM,CAACtN,GAAG,CAAC1G,IAAI,EAAE0G,GAAG,CAACzG,GAAG,GAAGA,GAAG,CAAC4D,KAAK,GAAC,CAAC,CAAC,CACvCwQ,MAAM,CAAC3N,GAAG,CAACnF,KAAK,EAAEmF,GAAG,CAACzG,GAAG,GAAGA,GAAG,CAAC4D,KAAK,GAAC,CAAC,CAChD,CAAC;QACL;;QAEA;QACA,IAAIrC,MAAM,CAACqC,KAAK,GAAG,CAAC,EAAE;UAClBjE,KAAK,CAACuC,MAAM,CACR,IAAI1J,IAAI,CAAC;YACLsb,MAAM,EAAE;cAAElQ,KAAK,EAAErC,MAAM,CAACqC,KAAK;cAAEqF,KAAK,EAAE1H,MAAM,CAAC0H;YAAM;UACvD,CAAC,CAAC,CACG8K,MAAM,CAACtN,GAAG,CAAC1G,IAAI,EAAE0G,GAAG,CAAClF,MAAM,GAAGA,MAAM,CAACqC,KAAK,GAAC,CAAC,CAAC,CAC7CwQ,MAAM,CAAC3N,GAAG,CAACnF,KAAK,EAAEmF,GAAG,CAAClF,MAAM,GAAGA,MAAM,CAACqC,KAAK,GAAC,CAAC,CACtD,CAAC;QACL;;QAEA;QACA,IAAI0V,cAAc,EAAE;UAChB3Z,KAAK,CAACuC,MAAM,CACR,IAAI1J,IAAI,CAAC;YACLsb,MAAM,EAAE;cAAElQ,KAAK,EAAE7D,IAAI,CAAC6D,KAAK;cAAEqF,KAAK,EAAElJ,IAAI,CAACkJ;YAAM;UACnD,CAAC,CAAC,CACG8K,MAAM,CAACtN,GAAG,CAAC1G,IAAI,GAAGA,IAAI,CAAC6D,KAAK,GAAC,CAAC,EAAE6C,GAAG,CAACzG,GAAG,CAAC,CACxCoU,MAAM,CAAC3N,GAAG,CAAC1G,IAAI,GAAGA,IAAI,CAAC6D,KAAK,GAAC,CAAC,EAAE6C,GAAG,CAAClF,MAAM,CACnD,CAAC;QACL;;QAEA;QACA,IAAIgY,eAAe,EAAE;UACjB5Z,KAAK,CAACuC,MAAM,CACR,IAAI1J,IAAI,CAAC;YACLsb,MAAM,EAAE;cAAElQ,KAAK,EAAEtC,KAAK,CAACsC,KAAK;cAAEqF,KAAK,EAAE3H,KAAK,CAAC2H;YAAM;UACrD,CAAC,CAAC,CACG8K,MAAM,CAACtN,GAAG,CAACnF,KAAK,GAAGA,KAAK,CAACsC,KAAK,GAAC,CAAC,EAAE6C,GAAG,CAACzG,GAAG,CAAC,CAC1CoU,MAAM,CAAC3N,GAAG,CAACnF,KAAK,GAAGA,KAAK,CAACsC,KAAK,GAAC,CAAC,EAAE6C,GAAG,CAAClF,MAAM,CACrD,CAAC;QACL;QAEA;MACJ;IACJ;IACA;;IAEA,IAAIqS,GAAG,GAAG7B,wBAAwB,CAACtL,GAAG,EAAE+M,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,CAAC;IAC/D,IAAI3B,GAAG,GAAG4B,GAAG,CAAChB,EAAE;IAChB,IAAIX,GAAG,GAAG2B,GAAG,CAACf,EAAE;IAChB,IAAIX,GAAG,GAAG0B,GAAG,CAACd,EAAE;IAChB,IAAIX,GAAG,GAAGyB,GAAG,CAACb,EAAE;;IAEhB;IACAoE,QAAQ,CAACnX,GAAG,CAACiJ,KAAK,EACTxC,GAAG,CAAC7C,KAAK,EAAE5D,GAAG,CAAC4D,KAAK,EAAE7D,IAAI,CAAC6D,KAAK,EAAEtC,KAAK,CAACsC,KAAK,EAC7CoO,GAAG,EAAEC,GAAG,EACR,CAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAExL,GAAG,CAAC1G,IAAI,EAAE0G,GAAG,CAACzG,GAAG,CAAE,CAAC;;IAE3C;IACAmX,QAAQ,CAAC5V,MAAM,CAAC0H,KAAK,EACZxC,GAAG,CAAC7C,KAAK,EAAErC,MAAM,CAACqC,KAAK,EAAEtC,KAAK,CAACsC,KAAK,EAAE7D,IAAI,CAAC6D,KAAK,EAChDsO,GAAG,EAAEC,GAAG,EACR,CAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE1L,GAAG,CAACnF,KAAK,EAAEmF,GAAG,CAAClF,MAAM,CAAE,CAAC;;IAEjD;IACA,SAASiY,GAAGA,CAACrd,CAAC,EAAE;MACZ,OAAO;QAAE0C,CAAC,EAAE1C,CAAC,CAAC2C,CAAC;QAAEA,CAAC,EAAE3C,CAAC,CAAC0C;MAAE,CAAC;IAC7B;;IAEA;IACAsY,QAAQ,CAACpX,IAAI,CAACkJ,KAAK,EACVxC,GAAG,CAACH,MAAM,EAAEvG,IAAI,CAAC6D,KAAK,EAAErC,MAAM,CAACqC,KAAK,EAAE5D,GAAG,CAAC4D,KAAK,EAC/C4V,GAAG,CAACrH,GAAG,CAAC,EAAEqH,GAAG,CAACxH,GAAG,CAAC,EAClB,CAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEvL,GAAG,CAAC1G,IAAI,EAAE0G,GAAG,CAAClF,MAAM,CAAE,CAAC;;IAE/C;IACA4V,QAAQ,CAAC7V,KAAK,CAAC2H,KAAK,EACXxC,GAAG,CAACH,MAAM,EAAEhF,KAAK,CAACsC,KAAK,EAAE5D,GAAG,CAAC4D,KAAK,EAAErC,MAAM,CAACqC,KAAK,EAChD4V,GAAG,CAACvH,GAAG,CAAC,EAAEuH,GAAG,CAACtH,GAAG,CAAC,EAClB,CAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAEzL,GAAG,CAACnF,KAAK,EAAEmF,GAAG,CAACzG,GAAG,CAAE,CAAC;EACjD;AACJ;AAEA,SAASkY,gBAAgBA,CAACuB,QAAQ,EAAE;EAChC,OAAO,UAAS9Z,KAAK,EAAEvF,IAAI,EAAE;IACzB,IAAIwJ,KAAK,GAAGxJ,IAAI,CAACwJ,KAAK,CAAC,CAAC;MAAE0C,MAAM,GAAGlM,IAAI,CAACkM,MAAM,CAAC,CAAC;IAEhD,QAAQmT,QAAQ,CAAC3P,IAAI;MACnB,KAAK,QAAQ;QAEX;QACA,IAAIP,KAAK,GAAGkQ,QAAQ,CAAClQ,KAAK,IAAI,IAAI,GAAGkQ,QAAQ,CAAClQ,KAAK,GAAGiF,IAAI,CAAC4C,EAAE;QAC7D,QAAQqI,QAAQ,CAAC1P,EAAE;UACjB,KAAK,KAAK;YACRR,KAAK,GAAG,CAAC;YACT;UACF,KAAK,MAAM;YACTA,KAAK,GAAG,CAACiF,IAAI,CAAC4C,EAAE,GAAG,CAAC;YACpB;UACF,KAAK,QAAQ;YACX7H,KAAK,GAAGiF,IAAI,CAAC4C,EAAE;YACf;UACF,KAAK,OAAO;YACV7H,KAAK,GAAGiF,IAAI,CAAC4C,EAAE,GAAG,CAAC;YACnB;UACF,KAAK,UAAU;UAAE,KAAK,UAAU;YAC9B7H,KAAK,GAAG,CAACiF,IAAI,CAACkL,KAAK,CAACpT,MAAM,EAAE1C,KAAK,CAAC;YAClC;UACF,KAAK,WAAW;UAAE,KAAK,WAAW;YAChC2F,KAAK,GAAGiF,IAAI,CAACkL,KAAK,CAACpT,MAAM,EAAE1C,KAAK,CAAC;YACjC;UACF,KAAK,aAAa;UAAE,KAAK,aAAa;YACpC2F,KAAK,GAAGiF,IAAI,CAAC4C,EAAE,GAAG5C,IAAI,CAACkL,KAAK,CAACpT,MAAM,EAAE1C,KAAK,CAAC;YAC3C;UACF,KAAK,cAAc;UAAE,KAAK,cAAc;YACtC2F,KAAK,GAAGiF,IAAI,CAAC4C,EAAE,GAAG5C,IAAI,CAACkL,KAAK,CAACpT,MAAM,EAAE1C,KAAK,CAAC;YAC3C;QACJ;QAEA,IAAI6V,QAAQ,CAAC9P,OAAO,EAAE;UAClBJ,KAAK,IAAIiF,IAAI,CAAC4C,EAAE;QACpB;;QAEA;QACA7H,KAAK,IAAI,CAAC,GAAGiF,IAAI,CAAC4C,EAAE;QACpB,IAAI7H,KAAK,GAAG,CAAC,EAAE;UACXA,KAAK,IAAI,CAAC,GAAGiF,IAAI,CAAC4C,EAAE;QACxB;;QAEA;QACA;QACA;;QAEA;QACA;QACA;QACA,IAAIuI,KAAK,GAAGnL,IAAI,CAACoL,GAAG,CAAChW,KAAK,GAAG4K,IAAI,CAACoJ,GAAG,CAACrO,KAAK,CAAC,CAAC,GAAGiF,IAAI,CAACoL,GAAG,CAACtT,MAAM,GAAGkI,IAAI,CAACmJ,GAAG,CAACpO,KAAK,CAAC,CAAC;;QAElF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA,IAAIsQ,WAAW,GAAGrL,IAAI,CAACsL,IAAI,CAAClW,KAAK,GAAG4K,IAAI,CAACuL,GAAG,CAACxQ,KAAK,CAAC,GAAGjD,MAAM,CAAC;QAC7D,IAAIsR,GAAG,GAAGpJ,IAAI,CAACoJ,GAAG,CAACiC,WAAW,CAAC;UAAElC,GAAG,GAAGnJ,IAAI,CAACmJ,GAAG,CAACkC,WAAW,CAAC;QAC5D,IAAIlT,GAAG,GAAG6H,IAAI,CAACoL,GAAG,CAAChC,GAAG,CAAC,GAAGpJ,IAAI,CAACoL,GAAG,CAACjC,GAAG,CAAC;QACvC,IAAI9Y,CAAC,GAAG8H,GAAG,GAAC,CAAC,GAAGiR,GAAG;QACnB,IAAI9Y,CAAC,GAAG6H,GAAG,GAAC,CAAC,GAAGgR,GAAG;;QAEnB;QACA;QACA,IAAIpO,KAAK,GAAGiF,IAAI,CAAC4C,EAAE,GAAC,CAAC,IAAI7H,KAAK,IAAI,CAAC,GAACiF,IAAI,CAAC4C,EAAE,GAAC,CAAC,EAAE;UAC3CvS,CAAC,GAAG,CAACA,CAAC;UACNC,CAAC,GAAG,CAACA,CAAC;QACV;;QAEA;QACA,IAAIkb,QAAQ,GAAG,EAAE;UAAE1Y,KAAK,GAAG,CAAC;QAC5B,IAAIoI,KAAK,GAAG+P,QAAQ,CAAC/P,KAAK,CAACU,GAAG,CAAC,UAAS5N,CAAC,EAAEe,CAAC,EAAC;UACzC,IAAIqR,MAAM,GAAGpS,CAAC,CAAC0M,OAAO;UACtB,IAAI0F,MAAM,EAAE;YACRA,MAAM,GAAG/J,UAAU,CAAC+J,MAAM,CAAC,GAAG,GAAG;UACrC,CAAC,MAAM,IAAIpS,CAAC,CAAC+B,MAAM,EAAE;YACjBqQ,MAAM,GAAG/J,UAAU,CAACrI,CAAC,CAAC+B,MAAM,CAAC,GAAGob,KAAK;UACzC,CAAC,MAAM,IAAIpc,CAAC,KAAK,CAAC,EAAE;YAChBqR,MAAM,GAAG,CAAC;UACd,CAAC,MAAM,IAAIrR,CAAC,IAAIkc,QAAQ,CAAC/P,KAAK,CAACnL,MAAM,GAAG,CAAC,EAAE;YACvCqQ,MAAM,GAAG,CAAC;UACd;UACA,IAAI/E,IAAI,GAAG;YACPZ,KAAK,EAAEzM,CAAC,CAACyM,KAAK,CAACuE,SAAS,CAAC,CAAC;YAC1BoB,MAAM,EAAEA;UACZ,CAAC;UACD,IAAIA,MAAM,IAAI,IAAI,EAAE;YAChBtN,KAAK,GAAGsN,MAAM;YACd;YACAoL,QAAQ,CAACle,OAAO,CAAC,UAASU,CAAC,EAAEe,CAAC,EAAC;cAC3B,IAAIsM,IAAI,GAAGrN,CAAC,CAACqN,IAAI;cACjBA,IAAI,CAAC+E,MAAM,GAAGpS,CAAC,CAACuD,IAAI,GAAG,CAACuB,KAAK,GAAG9E,CAAC,CAACuD,IAAI,KAAKxC,CAAC,GAAG,CAAC,CAAC,IAAIyc,QAAQ,CAACzb,MAAM,GAAG,CAAC,CAAC;YAC7E,CAAC,CAAC;YACFyb,QAAQ,GAAG,EAAE;UACjB,CAAC,MAAM;YACHA,QAAQ,CAACve,IAAI,CAAC;cAAEsE,IAAI,EAAEuB,KAAK;cAAEuI,IAAI,EAAEA;YAAK,CAAC,CAAC;UAC9C;UACA,OAAOA,IAAI;QACf,CAAC,CAAC;QAEF,IAAIoQ,KAAK,GAAG,CAAE,GAAG,GAAGpb,CAAC,EAAE,GAAG,GAAGC,CAAC,CAAE;QAChC,IAAIob,GAAG,GAAG,CAAE,GAAG,GAAGrb,CAAC,EAAE,GAAG,GAAGC,CAAC,CAAE;;QAE9B;QACAa,KAAK,CAACuC,MAAM,CACR1J,IAAI,CAAC2hB,QAAQ,CAAC/f,IAAI,CAAC,CACd0Z,MAAM,CAAC,IAAI,CAAC,CACZD,IAAI,CAAC,IAAI/a,cAAc,CAAC;UACrBmhB,KAAK,EAAOA,KAAK;UACjBC,GAAG,EAASA,GAAG;UACfxQ,KAAK,EAAOA,KAAK;UACjB0Q,SAAS,EAAG;QAChB,CAAC,CAAC,CACV,CAAC;QACD;MACF,KAAK,QAAQ;QACX;QACA,IAAItgB,MAAM,CAACugB,OAAO,IAAIvgB,MAAM,CAACugB,OAAO,CAACC,GAAG,EAAE;UACtCxgB,MAAM,CAACugB,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;QACjF;QACA;IACJ;EACJ,CAAC;AACL;AAEA,SAAS5D,iBAAiBA,CAACzX,OAAO,EAAEU,KAAK,EAAE;EACvC,IAAI4a,MAAM;EACV,IAAItb,OAAO,CAACjB,kBAAkB,EAAE;IAC5B,IAAI5D,IAAI,GAAG6E,OAAO,CAACY,qBAAqB,CAAC,CAAC;IAC1C,IAAI2Y,IAAI,GAAG;MACP5U,KAAK,EAAExJ,IAAI,CAACwJ,KAAK;MACjB0C,MAAM,EAAElM,IAAI,CAACkM;IACjB,CAAC;IACDiU,MAAM,GAAGtb,OAAO,CAACjB,kBAAkB,CAACwa,IAAI,CAAC;EAC7C,CAAC,MAAM,IAAI1e,MAAM,CAAC4E,KAAK,IAAI5E,MAAM,CAAC4E,KAAK,CAACC,MAAM,IAAIM,OAAO,CAACyF,YAAY,CAAC5K,MAAM,CAAC4E,KAAK,CAAC8b,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE;IAC/F,IAAIC,MAAM,GAAG3gB,MAAM,CAAC4E,KAAK,CAACgc,cAAc,CAAC5gB,MAAM,CAAC4E,KAAK,CAACC,MAAM,CAACM,OAAO,CAAC,CAAC;IACtE,IAAIwb,MAAM,KAAKA,MAAM,CAACE,eAAe,IAAIF,MAAM,CAACG,YAAY,CAAC,EAAE;MAC3D,IAAIH,MAAM,CAACE,eAAe,EAAE;QACxBJ,MAAM,GAAGE,MAAM,CAACE,eAAe,CAAC,CAAC;MACrC,CAAC,MAAM;QACHJ,MAAM,GAAGE,MAAM,CAACG,YAAY,CAAC,CAAC;MAClC;IACJ;EACJ;EAEA,IAAI,CAACL,MAAM,EAAE;IACT,OAAO,KAAK;EAChB;EAEA,IAAIM,IAAI,GAAG,IAAIniB,KAAK,CAAC,CAAC;EACtBmiB,IAAI,CAACxX,QAAQ,CAAC5H,IAAI,CAAC8e,MAAM,CAAC;EAE1B,IAAIvL,IAAI,GAAG/P,OAAO,CAACY,qBAAqB,CAAC,CAAC;EAC1Cgb,IAAI,CAAC9J,SAAS,CAAClZ,GAAG,CAACkZ,SAAS,CAAC,CAAC,CAAC+J,SAAS,CAAC9L,IAAI,CAACjP,IAAI,EAAEiP,IAAI,CAAChP,GAAG,CAAC,CAAC;EAE9DL,KAAK,CAACuC,MAAM,CAAC2Y,IAAI,CAAC;EAElB,OAAO,IAAI;AACf;AAEA,SAASlE,iBAAiBA,CAAC1X,OAAO,EAAEU,KAAK,EAAE;EACvC,IAAIob,UAAU,GAAG9b,OAAO,CAACyF,YAAY,CAACjL,iBAAiB,CAAC;EAExD,IAAI,CAACshB,UAAU,EAAE;IACb,OAAO,KAAK;EAChB;EAEA,IAAItU,GAAG,GAAGxH,OAAO,CAACY,qBAAqB,CAAC,CAAC;EACzC,IAAIoJ,KAAK,GAAG7J,gBAAgB,CAACH,OAAO,CAAC,CAACgK,KAAK;EAE3C,IAAI8R,UAAU,KAAK,QAAQ,EAAE;IACzB,IAAIC,QAAQ,GAAGvU,GAAG,CAACH,MAAM,GAAG,CAAC;IAC7B3G,KAAK,CAACuC,MAAM,CAAC,IAAIrJ,IAAI,CAAC,IAAIhB,GAAG,CAACgB,IAAI,CAAC,CAC/B4N,GAAG,CAACnF,KAAK,GAAG0Z,QAAQ,EACpBvU,GAAG,CAACzG,GAAG,GAAGyG,GAAG,CAACH,MAAM,GAAG,GAAG,CAC7B,EAAE,CAAC0U,QAAQ,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAACnH,IAAI,CAAC5K,KAAK,CAAC,CAAC6K,MAAM,CAAC7K,KAAK,CAAC,CAAC;EACxD,CAAC,MAAM;IACH,IAAIgS,MAAM,GAAGxU,GAAG,CAACH,MAAM,GAAG,CAAC;IAC3B,IAAI4U,MAAM,GAAG,CACTzU,GAAG,CAACnF,KAAK,GAAG2Z,MAAM,EAClBxU,GAAG,CAACzG,GAAG,GAAG,CAACyG,GAAG,CAACH,MAAM,GAAG2U,MAAM,IAAI,CAAC,CACtC;IACD,IAAIE,MAAM,GAAG,IAAIviB,MAAM,CAAC,IAAIf,GAAG,CAACe,MAAM,CAACsiB,MAAM,EAAED,MAAM,CAAC,CAAC;IACvD,IAAIF,UAAU,KAAK,QAAQ,EAAE;MACzBI,MAAM,CAACrH,MAAM,CAAC7K,KAAK,EAAE,GAAG,CAAC;IAC7B,CAAC,MAAM;MACHkS,MAAM,CAACtH,IAAI,CAAC5K,KAAK,CAAC,CAAC6K,MAAM,CAAC,IAAI,CAAC;IACnC;IACAnU,KAAK,CAACuC,MAAM,CAACiZ,MAAM,CAAC;EACxB;EAEA,OAAO,IAAI;AACf;AAEA,SAASC,WAAWA,CAACnc,OAAO,EAAEgL,GAAG,EAAEtK,KAAK,EAAE;EACtC,IAAI8G,GAAG,GAAG+J,aAAa,CAACvR,OAAO,CAAC;EAChC,IAAI7E,IAAI,GAAG,IAAIvC,GAAG,CAACgB,IAAI,CAAC,CAAE4N,GAAG,CAAC1G,IAAI,EAAE0G,GAAG,CAACzG,GAAG,CAAE,EAAE,CAAEyG,GAAG,CAAC7C,KAAK,EAAE6C,GAAG,CAACH,MAAM,CAAE,CAAC;EACzE,IAAI+U,KAAK,GAAG,IAAI1iB,KAAK,CAACsR,GAAG,EAAE7P,IAAI,CAAC;EAChCmX,WAAW,CAAC8J,KAAK,EAAErI,eAAe,CAAC/T,OAAO,EAAEwH,GAAG,EAAE,SAAS,CAAC,CAAC;EAC5D9G,KAAK,CAACuC,MAAM,CAACmZ,KAAK,CAAC;AACvB;AAEA,SAASC,UAAUA,CAAC/f,CAAC,EAAEggB,CAAC,EAAE;EACtB,IAAIC,EAAE,GAAGpc,gBAAgB,CAAC7D,CAAC,CAAC;EAC5B,IAAIkgB,EAAE,GAAGrc,gBAAgB,CAACmc,CAAC,CAAC;EAC5B,IAAIG,EAAE,GAAG7W,UAAU,CAACC,gBAAgB,CAAC0W,EAAE,EAAE,SAAS,CAAC,CAAC;EACpD,IAAIG,EAAE,GAAG9W,UAAU,CAACC,gBAAgB,CAAC2W,EAAE,EAAE,SAAS,CAAC,CAAC;EACpD,IAAIG,EAAE,GAAG9W,gBAAgB,CAAC0W,EAAE,EAAE,UAAU,CAAC;EACzC,IAAInI,EAAE,GAAGvO,gBAAgB,CAAC2W,EAAE,EAAE,UAAU,CAAC;EACzC,IAAItO,KAAK,CAACuO,EAAE,CAAC,IAAIvO,KAAK,CAACwO,EAAE,CAAC,EAAE;IACxB,IAAK,iBAAiB,CAAC9e,IAAI,CAAC+e,EAAE,CAAC,IAAM,iBAAiB,CAAC/e,IAAI,CAACwW,EAAE,CAAE,EAAE;MAC9D,OAAO,CAAC;IACZ;IACA,IAAIuI,EAAE,IAAI,QAAQ,EAAE;MAChB,OAAO,CAAC,CAAC;IACb;IACA,IAAIvI,EAAE,IAAI,QAAQ,EAAE;MAChB,OAAO,CAAC;IACZ;IACA,OAAO,CAAC;EACZ;EACA,IAAIlG,KAAK,CAACuO,EAAE,CAAC,EAAE;IACX,OAAOC,EAAE,KAAK,CAAC,GAAG,CAAC,GAAGA,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EACzC;EACA,IAAIxO,KAAK,CAACwO,EAAE,CAAC,EAAE;IACX,OAAOD,EAAE,KAAK,CAAC,GAAG,CAAC,GAAGA,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACzC;EACA,OAAO7W,UAAU,CAAC6W,EAAE,CAAC,GAAG7W,UAAU,CAAC8W,EAAE,CAAC;AAC1C;AAEA,SAASlF,WAAWA,CAACxX,OAAO,EAAE;EAC1B,OAAO,8BAA8B,CAACpC,IAAI,CAACoC,OAAO,CAACxB,OAAO,CAAC;AAC/D;AAEA,SAASoe,iBAAiBA,CAAC5c,OAAO,EAAE;EAChC,IAAIA,OAAO,CAAC6c,eAAe,IAAI7c,OAAO,CAAC6c,eAAe,CAACvd,MAAM,GAAG,CAAC,EAAE;IAC/D,OAAOU,OAAO,CAAC6c,eAAe,CAAC,CAAC,CAAC;EACrC;EACA,OAAO7c,OAAO,CAAC5E,OAAO,CAAC4E,OAAO,CAAC8c,aAAa,CAAC;AACjD;AAEA,SAASC,cAAcA,CAAC/c,OAAO,EAAEU,KAAK,EAAE;EACpC,IAAI3D,KAAK,GAAGoD,gBAAgB,CAACH,OAAO,CAAC;EACrC,IAAIgK,KAAK,GAAGnE,gBAAgB,CAAC9I,KAAK,EAAE,OAAO,CAAC;EAC5C,IAAIyK,GAAG,GAAGxH,OAAO,CAACY,qBAAqB,CAAC,CAAC;EACzC,IAAIZ,OAAO,CAAC6K,IAAI,IAAI,UAAU,EAAE;IAC5BnK,KAAK,CAACuC,MAAM,CACR1J,IAAI,CAAC2hB,QAAQ,CACT,IAAItiB,GAAG,CAACgB,IAAI,CAAC,CAAE4N,GAAG,CAAC1G,IAAI,GAAC,CAAC,EAAE0G,GAAG,CAACzG,GAAG,GAAC,CAAC,CAAE,EACzB,CAAEyG,GAAG,CAAC7C,KAAK,GAAC,CAAC,EAAE6C,GAAG,CAACH,MAAM,GAAC,CAAC,CAAE,CAC9C,CAAC,CAACwN,MAAM,CAAC7K,KAAK,EAAE,CAAC,CACrB,CAAC;IACD,IAAIhK,OAAO,CAACnB,OAAO,EAAE;MACjB;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA6B,KAAK,CAACuC,MAAM,CACR,IAAI1J,IAAI,CAAC,CAAC,CACLsb,MAAM,CAAC7K,KAAK,EAAE,GAAG,CAAC,CAClB8K,MAAM,CAACtN,GAAG,CAAC1G,IAAI,GAAG,IAAI,GAAG0G,GAAG,CAAC7C,KAAK,EAC3B6C,GAAG,CAACzG,GAAG,GAAG,IAAI,GAAGyG,GAAG,CAACH,MAAM,CAAC,CACnC8N,MAAM,CAAC3N,GAAG,CAAC1G,IAAI,GAAG,IAAI,GAAG0G,GAAG,CAAC7C,KAAK,EAC3B6C,GAAG,CAACzG,GAAG,GAAG,IAAI,GAAGyG,GAAG,CAACH,MAAM,CAAC,CACnC8N,MAAM,CAAC3N,GAAG,CAAC1G,IAAI,GAAG,IAAI,GAAG0G,GAAG,CAAC7C,KAAK,EAC3B6C,GAAG,CAACzG,GAAG,GAAG,IAAI,GAAGyG,GAAG,CAAC7C,KAAK,CAC1C,CAAC;IACL;EACJ,CAAC,MAAM;IACHjE,KAAK,CAACuC,MAAM,CACR,IAAItJ,MAAM,CACN,IAAIf,GAAG,CAACe,MAAM,CAAC,CACX,CAAC6N,GAAG,CAAC1G,IAAI,GAAG0G,GAAG,CAACnF,KAAK,IAAI,CAAC,EAC1B,CAACmF,GAAG,CAACzG,GAAG,GAAGyG,GAAG,CAAClF,MAAM,IAAI,CAAC,CAC7B,EAAEiN,IAAI,CAACzH,GAAG,CAACN,GAAG,CAAC7C,KAAK,GAAC,CAAC,EAAE6C,GAAG,CAACH,MAAM,GAAC,CAAC,CAAC,GAAG,CAAC,CAC9C,CAAC,CAACwN,MAAM,CAAC7K,KAAK,EAAE,CAAC,CACrB,CAAC;IACD,IAAIhK,OAAO,CAACnB,OAAO,EAAE;MACjB6B,KAAK,CAACuC,MAAM,CACR,IAAItJ,MAAM,CACN,IAAIf,GAAG,CAACe,MAAM,CAAC,CACX,CAAC6N,GAAG,CAAC1G,IAAI,GAAG0G,GAAG,CAACnF,KAAK,IAAI,CAAC,EAC1B,CAACmF,GAAG,CAACzG,GAAG,GAAGyG,GAAG,CAAClF,MAAM,IAAI,CAAC,CAC7B,EAAEiN,IAAI,CAACzH,GAAG,CAACN,GAAG,CAAC7C,KAAK,GAAC,CAAC,EAAE6C,GAAG,CAACH,MAAM,GAAC,CAAC,CAAC,GAAG,CAAC,CAC9C,CAAC,CAACuN,IAAI,CAAC5K,KAAK,CAAC,CAAC6K,MAAM,CAAC,IAAI,CAC7B,CAAC;IACL;EACJ;AACJ;AAEA,SAASmI,eAAeA,CAAChd,OAAO,EAAEU,KAAK,EAAE;EACrC,IAAI8E,GAAG,GAAGxF,OAAO,CAACxB,OAAO,CAACiR,WAAW,CAAC,CAAC;EACvC,IAAIjK,GAAG,IAAI,OAAO,KAAKxF,OAAO,CAAC6K,IAAI,IAAI,UAAU,IAAI7K,OAAO,CAAC6K,IAAI,IAAI,OAAO,CAAC,EAAE;IAC3E,OAAOkS,cAAc,CAAC/c,OAAO,EAAEU,KAAK,CAAC;EACzC;EACA,IAAIxD,CAAC,GAAG8C,OAAO,CAACjC,UAAU;EAC1B,IAAI+F,GAAG,GAAG9D,OAAO,CAACO,aAAa;EAC/B,IAAI1E,EAAE,GAAGiI,GAAG,CAACH,aAAa,CAACpJ,oBAAoB,CAAC;EAChD,IAAI0iB,MAAM;EACVjjB,QAAQ,CAAC6B,EAAE,EAAE6Z,UAAU,CAACvV,gBAAgB,CAACH,OAAO,CAAC,CAAC,CAAC;EACnD,IAAIwF,GAAG,IAAI,OAAO,EAAE;IAChB3J,EAAE,CAACkB,KAAK,CAACmgB,UAAU,GAAG,KAAK;EAC/B;EACA,IAAI1X,GAAG,IAAI,QAAQ,IAAIA,GAAG,IAAI,UAAU,EAAE;IACtC3J,EAAE,CAACkB,KAAK,CAAC+H,QAAQ,GAAG,MAAM;EAC9B;EACA,IAAIU,GAAG,IAAI,QAAQ,EAAE;IACjB,IAAIxF,OAAO,CAACmd,QAAQ,EAAE;MAClB,KAAK,IAAI7e,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0B,OAAO,CAAC5E,OAAO,CAACkE,MAAM,EAAE,EAAEhB,CAAC,EAAE;QAC7C2e,MAAM,GAAGnZ,GAAG,CAACH,aAAa,CAACpJ,oBAAoB,CAAC;QAChDP,QAAQ,CAACijB,MAAM,EAAEvH,UAAU,CAACvV,gBAAgB,CAACH,OAAO,CAAC5E,OAAO,CAACkD,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE2e,MAAM,CAAClgB,KAAK,CAACyH,OAAO,GAAG,OAAO,CAAC,CAAC;QAChCyY,MAAM,CAAChH,WAAW,GAAGjW,OAAO,CAAC5E,OAAO,CAACkD,CAAC,CAAC,CAAC2X,WAAW;QACnDpa,EAAE,CAACqD,WAAW,CAAC+d,MAAM,CAAC;MAC1B;IACJ,CAAC,MAAM;MACHA,MAAM,GAAGL,iBAAiB,CAAC5c,OAAO,CAAC;MACnC,IAAIid,MAAM,EAAE;QACRphB,EAAE,CAACoa,WAAW,GAAGgH,MAAM,CAAChH,WAAW;MACvC;IACJ;EACJ,CAAC,MAAM;IACHpa,EAAE,CAACoa,WAAW,GAAGjW,OAAO,CAACpB,KAAK;EAClC;EACA1B,CAAC,CAAC6H,YAAY,CAAClJ,EAAE,EAAEmE,OAAO,CAAC;EAC3BnE,EAAE,CAACuhB,UAAU,GAAGpd,OAAO,CAACod,UAAU;EAClCvhB,EAAE,CAACwhB,SAAS,GAAGrd,OAAO,CAACqd,SAAS;;EAEhC;EACA;EACArd,OAAO,CAACjD,KAAK,CAACyH,OAAO,GAAG,MAAM;EAE9BmT,cAAc,CAAC9b,EAAE,EAAE6E,KAAK,CAAC;EACzBV,OAAO,CAACjD,KAAK,CAACyH,OAAO,GAAG,EAAE;EAC1BtH,CAAC,CAACoG,WAAW,CAACzH,EAAE,CAAC;AACrB;AAEA,SAASyhB,YAAYA,CAACtd,OAAO,EAAE;EAC3B,IAAIud,UAAU,GAAG,IAAI1iB,MAAM,CAAC2iB,aAAa,CAAC,CAAC;EAC3C,IAAIC,GAAG,GAAGF,UAAU,CAACG,iBAAiB,CAAC1d,OAAO,CAAC;EAE/C,IAAI/F,OAAO,CAACsW,OAAO,IAAI,EAAEvQ,OAAO,CAACyF,YAAY,CAAC,OAAO,CAAC,IAAIzF,OAAO,CAACyF,YAAY,CAAC,QAAQ,CAAC,CAAC,EAAE;IACvF,IAAI3B,GAAG,GAAG,IAAIjJ,MAAM,CAAC8iB,SAAS,CAAC,CAAC,CAACC,eAAe,CAACH,GAAG,EAAE,eAAe,CAAC;IACtE,IAAII,GAAG,GAAG/Z,GAAG,CAACga,eAAe;IAC7B,IAAItW,GAAG,GAAG+J,aAAa,CAACvR,OAAO,CAAC;IAChC6d,GAAG,CAACtZ,YAAY,CAAC,OAAO,EAAEiD,GAAG,CAAC7C,KAAK,CAAC;IACpCkZ,GAAG,CAACtZ,YAAY,CAAC,QAAQ,EAAEiD,GAAG,CAACH,MAAM,CAAC;IACtCoW,GAAG,GAAGF,UAAU,CAACG,iBAAiB,CAACG,GAAG,CAAC;EAC3C;EAEA,OAAOJ,GAAG;AACd;AAEA,SAAS9F,cAAcA,CAAC3X,OAAO,EAAEU,KAAK,EAAE;EACpC,IAAIhG,QAAQ,CAAC0G,gBAAgB,CAACpB,OAAO,KAAKA,OAAO,EAAE;IAC/C;IACA;IACAtF,QAAQ,CAAC0G,gBAAgB,CAACV,KAAK,GAAGA,KAAK;EAC3C;EACA,QAAQV,OAAO,CAACxB,OAAO,CAACiR,WAAW,CAAC,CAAC;IACnC,KAAK,KAAK;MACR0M,WAAW,CAACnc,OAAO,EAAEA,OAAO,CAAC4M,GAAG,EAAElM,KAAK,CAAC;MACxC;IAEF,KAAK,KAAK;MACR,IAAI+c,GAAG,GAAGH,YAAY,CAACtd,OAAO,CAAC;MAC/B,IAAI+d,OAAO,GAAG,4BAA4B,GAAIjkB,YAAY,CAAC2jB,GAAG,CAAE;MAChEtB,WAAW,CAACnc,OAAO,EAAE+d,OAAO,EAAErd,KAAK,CAAC;MACpC;IAEF,KAAK,QAAQ;MACX,IAAI;QACAyb,WAAW,CAACnc,OAAO,EAAEA,OAAO,CAACge,SAAS,CAAC,WAAW,CAAC,EAAEtd,KAAK,CAAC;MAC/D,CAAC,CAAC,OAAO8L,EAAE,EAAE;QACT;MAAA;MAEJ;IAEF,KAAK,UAAU;IACf,KAAK,OAAO;IACZ,KAAK,QAAQ;MACXwQ,eAAe,CAAChd,OAAO,EAAEU,KAAK,CAAC;MAC/B;IAEF;MACE,IAAI0D,QAAQ,GAAG,EAAE;QAAE6Z,MAAM,GAAG,EAAE;QAAEC,UAAU,GAAG,EAAE;MAC/C,KAAK,IAAI5f,CAAC,GAAG0B,OAAO,CAAChB,UAAU,EAAEV,CAAC,EAAEA,CAAC,GAAGA,CAAC,CAACW,WAAW,EAAE;QACnD,QAAQX,CAAC,CAACR,QAAQ;UAChB,KAAK,CAAC;YAAU;YACd,IAAI,IAAI,CAACF,IAAI,CAACU,CAAC,CAACC,IAAI,CAAC,EAAE;cACnB8J,UAAU,CAACrI,OAAO,EAAE1B,CAAC,EAAEoC,KAAK,CAAC;YACjC;YACA;UACF,KAAK,CAAC;YAAU;YACd,IAAI3D,KAAK,GAAGoD,gBAAgB,CAAC7B,CAAC,CAAC;YAC/B,IAAI6f,QAAQ,GAAGtY,gBAAgB,CAAC9I,KAAK,EAAE,OAAO,CAAC;YAC/C,IAAI0H,QAAQ,GAAGoB,gBAAgB,CAAC9I,KAAK,EAAE,UAAU,CAAC;YAClD,IAAI0H,QAAQ,IAAI,QAAQ,EAAE;cACtByZ,UAAU,CAAC1hB,IAAI,CAAC8B,CAAC,CAAC;YACtB,CAAC,MACI,IAAI6f,QAAQ,IAAI,MAAM,EAAE;cACzBF,MAAM,CAACzhB,IAAI,CAAC8B,CAAC,CAAC;YAClB,CAAC,MAAM;cACH8F,QAAQ,CAAC5H,IAAI,CAAC8B,CAAC,CAAC;YACpB;YACA;QACJ;MACJ;MAEArF,SAAS,CAACmL,QAAQ,EAAEiY,UAAU,CAAC,CAACxf,OAAO,CAAC,UAAShB,EAAE,EAAC;QAAE0F,aAAa,CAAC1F,EAAE,EAAE6E,KAAK,CAAC;MAAE,CAAC,CAAC;MAClFzH,SAAS,CAACglB,MAAM,EAAE5B,UAAU,CAAC,CAACxf,OAAO,CAAC,UAAShB,EAAE,EAAC;QAAE0F,aAAa,CAAC1F,EAAE,EAAE6E,KAAK,CAAC;MAAE,CAAC,CAAC;MAChFzH,SAAS,CAACilB,UAAU,EAAE7B,UAAU,CAAC,CAACxf,OAAO,CAAC,UAAShB,EAAE,EAAC;QAAE0F,aAAa,CAAC1F,EAAE,EAAE6E,KAAK,CAAC;MAAE,CAAC,CAAC;EACxF;AACJ;AAEA,SAAS2H,UAAUA,CAACrI,OAAO,EAAEZ,IAAI,EAAEsB,KAAK,EAAE;EACtC,IAAIuP,YAAY,CAAC,CAAC,EAAE;IAChB;EACJ;EACA,IAAIlT,KAAK,GAAGoD,gBAAgB,CAACH,OAAO,CAAC;EAErC,IAAI4F,UAAU,CAACC,gBAAgB,CAAC9I,KAAK,EAAE,aAAa,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE;IAC3D;IACA;IACA;IACA;EACJ;EAEA,IAAIqhB,IAAI,GAAGhf,IAAI,CAACb,IAAI;EACpB,IAAIyc,KAAK,GAAG,CAAC;EACb,IAAIC,GAAG,GAAGmD,IAAI,CAACC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC;EAEnC,IAAI,CAACpD,GAAG,EAAE;IACN,OAAO,CAAC;EACZ;EAEA,IAAIqD,QAAQ,GAAGzY,gBAAgB,CAAC9I,KAAK,EAAE,WAAW,CAAC;EACnD,IAAIwhB,UAAU,GAAG1Y,gBAAgB,CAAC9I,KAAK,EAAE,aAAa,CAAC;;EAEvD;EACA,IAAIkP,IAAI,GAAG,CACPpG,gBAAgB,CAAC9I,KAAK,EAAE,YAAY,CAAC,EACrC8I,gBAAgB,CAAC9I,KAAK,EAAE,cAAc,CAAC,EACvC8I,gBAAgB,CAAC9I,KAAK,EAAE,aAAa,CAAC,EACtCuhB,QAAQ;EAAE;EACVzY,gBAAgB,CAAC9I,KAAK,EAAE,aAAa,CAAC,CACzC,CAACN,IAAI,CAAC,GAAG,CAAC;EAEX6hB,QAAQ,GAAG1Y,UAAU,CAAC0Y,QAAQ,CAAC;EAC/BC,UAAU,GAAG3Y,UAAU,CAAC2Y,UAAU,CAAC;EAEnC,IAAID,QAAQ,KAAK,CAAC,IAAIpQ,KAAK,CAACoQ,QAAQ,CAAC,EAAE;IACnC;EACJ;EAEA,IAAItU,KAAK,GAAGnE,gBAAgB,CAAC9I,KAAK,EAAE,OAAO,CAAC;EAC5C,IAAI+J,KAAK,GAAG9G,OAAO,CAACO,aAAa,CAACwG,WAAW,CAAC,CAAC;EAC/C,IAAIyX,KAAK,GAAG3Y,gBAAgB,CAAC9I,KAAK,EAAE,YAAY,CAAC;EACjD,IAAI0hB,WAAW,GAAGD,KAAK,IAAI,SAAS;EACpC,IAAIE,WAAW,GAAG7Y,gBAAgB,CAAC9I,KAAK,EAAE,cAAc,EAAE,CAAC,CAAC;EAC5D,IAAImgB,UAAU,GAAGrX,gBAAgB,CAAC9I,KAAK,EAAE,aAAa,CAAC;EACvD,IAAI4hB,aAAa,GAAG9Y,gBAAgB,CAAC9I,KAAK,EAAE,gBAAgB,CAAC;;EAE7D;EACA;EACA;EACA,IAAI6hB,kBAAkB,GAAG5e,OAAO,CAACY,qBAAqB,CAAC,CAAC,CAAC+D,KAAK,GAAG2Z,QAAQ,GAAG,CAAC;EAC7E,IAAIM,kBAAkB,KAAK,CAAC,EAAE;IAC1BA,kBAAkB,GAAG,GAAG;EAC5B;;EAEA;EACA;EACA,IAAIC,cAAc,GAAG,IAAI;EAEzB,IAAIC,SAAS,GAAGpkB,QAAQ,CAAC,WAAW,CAAC;EACrC,IAAIqkB,WAAW,GAAGrkB,QAAQ,CAAC,cAAc,CAAC;EAC1C,IAAIskB,QAAQ,GAAGtkB,QAAQ,CAAC,UAAU,CAAC;EACnC,IAAIukB,eAAe,GAAGvkB,QAAQ,CAAC,kBAAkB,CAAC;EAElD,IAAIokB,SAAS,EAAE;IACXI,WAAW,CAACC,aAAa,CAAC;EAC9B;;EAEA;EACA,OAAO,CAACC,OAAO,CAAC,CAAC,EAAE,CAAC;EAEpB,IAAIL,WAAW,IAAIC,QAAQ,EAAE;IACzBE,WAAW,CAACG,YAAY,CAAC;EAC7B;EAEA,OAAO,CAAiB;;EAExB,SAASH,WAAWA,CAACrb,QAAQ,EAAE;IAC3BiD,KAAK,CAACwY,UAAU,CAAClgB,IAAI,CAAC;IACtB,IAAImgB,WAAW,GAAGrlB,KAAK,CAAC4M,KAAK,CAACmQ,cAAc,CAAC,CAAC,CAAC;IAE/CiI,WAAW,GAAG,SAAAA,CAAUhP,EAAE,EAAE;MAAE,OAAOqP,WAAW,CAAC1iB,OAAO,CAACqT,EAAE,CAAC;IAAE,CAAC;IAC/DgP,WAAW,CAACrb,QAAQ,CAAC;EACzB;EAEA,SAAS2b,4BAA4BA,CAAC1Y,KAAK,EAAE;IACzC;IACA;IACA,IAAIhM,SAAS,IAAIb,OAAO,CAACwlB,MAAM,IAAIxlB,OAAO,CAACylB,MAAM,EAAE;MAC/C;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA,IAAIC,UAAU,GAAG7Y,KAAK,CAACmQ,cAAc,CAAC,CAAC;QAAEzP,GAAG,GAAG;UAC3CzG,GAAG,EAAO6e,QAAQ;UAClBvd,KAAK,EAAI,CAACud,QAAQ;UAClBtd,MAAM,EAAG,CAACsd,QAAQ;UAClB9e,IAAI,EAAM8e;QACd,CAAC;QAAElR,IAAI,GAAG,KAAK;MACf,KAAK,IAAIpQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqhB,UAAU,CAACrgB,MAAM,EAAE,EAAEhB,CAAC,EAAE;QACxC,IAAIge,CAAC,GAAGqD,UAAU,CAACrhB,CAAC,CAAC;QACrB,IAAIge,CAAC,CAAC3X,KAAK,IAAI,CAAC,IAAI2X,CAAC,CAACha,MAAM,KAAKuc,cAAc,EAAE;UAC7C,SAAS,CAAG;QAChB;QACArX,GAAG,CAAC1G,IAAI,GAAKyO,IAAI,CAACzH,GAAG,CAACwU,CAAC,CAACxb,IAAI,EAAK0G,GAAG,CAAC1G,IAAI,CAAC;QAC1C0G,GAAG,CAACzG,GAAG,GAAMwO,IAAI,CAACzH,GAAG,CAACwU,CAAC,CAACvb,GAAG,EAAMyG,GAAG,CAACzG,GAAG,CAAC;QACzCyG,GAAG,CAACnF,KAAK,GAAIkN,IAAI,CAACxH,GAAG,CAACuU,CAAC,CAACja,KAAK,EAAImF,GAAG,CAACnF,KAAK,CAAC;QAC3CmF,GAAG,CAAClF,MAAM,GAAGiN,IAAI,CAACxH,GAAG,CAACuU,CAAC,CAACha,MAAM,EAAGkF,GAAG,CAAClF,MAAM,CAAC;QAC5CoM,IAAI,GAAG,IAAI;MACf;MACA,IAAI,CAACA,IAAI,EAAE;QACP,OAAO5H,KAAK,CAAClG,qBAAqB,CAAC,CAAC;MACxC;MACA4G,GAAG,CAAC7C,KAAK,GAAG6C,GAAG,CAACnF,KAAK,GAAGmF,GAAG,CAAC1G,IAAI;MAChC0G,GAAG,CAACH,MAAM,GAAGG,GAAG,CAAClF,MAAM,GAAGkF,GAAG,CAACzG,GAAG;MACjC,OAAOyG,GAAG;IACd;IACA,OAAOV,KAAK,CAAClG,qBAAqB,CAAC,CAAC;EACxC;;EAEA;EACA;EACA;EACA;EACA,SAASwe,OAAOA,CAAA,EAAG;IACf,IAAIS,SAAS,GAAG7E,KAAK;IACrB,IAAIxT,GAAG;MAAE7G,GAAG,GAAGyd,IAAI,CAACxU,MAAM,CAACoR,KAAK,CAAC,CAACqD,MAAM,CAAC,IAAI,CAAC;IAC9CrD,KAAK,IAAIra,GAAG;IACZ,IAAIA,GAAG,GAAG,CAAC,IAAIqa,KAAK,IAAIC,GAAG,EAAE;MACzB,OAAO,IAAI;IACf;;IAEA;IACA;IACAnU,KAAK,CAACgZ,QAAQ,CAAC1gB,IAAI,EAAE4b,KAAK,CAAC;IAC3BlU,KAAK,CAACkB,MAAM,CAAC5I,IAAI,EAAE4b,KAAK,GAAG,CAAC,CAAC;IAC7BxT,GAAG,GAAGgY,4BAA4B,CAAC1Y,KAAK,CAAC;;IAEzC;IACA,IAAIiZ,KAAK,GAAG,KAAK;IACjB,IAAItB,WAAW,IAAIC,WAAW,GAAG,CAAC,EAAE;MAChC/d,GAAG,GAAGyd,IAAI,CAACxU,MAAM,CAACoR,KAAK,CAAC,CAACqD,MAAM,CAAC,IAAI,CAAC;MACrC,IAAI1d,GAAG,IAAI,CAAC,EAAE;QACV;QACA;QACAmG,KAAK,CAACkB,MAAM,CAAC5I,IAAI,EAAE4b,KAAK,GAAGra,GAAG,CAAC;QAC/B,IAAIoM,CAAC,GAAGyS,4BAA4B,CAAC1Y,KAAK,CAAC;QAC3C,IAAIiG,CAAC,CAACzK,MAAM,IAAIkF,GAAG,CAAClF,MAAM,EAAE;UACxBkF,GAAG,GAAGuF,CAAC;UACPgT,KAAK,GAAG,IAAI;UACZ/E,KAAK,IAAIra,GAAG;QAChB;MACJ;IACJ;IAEA,IAAI,CAACof,KAAK,EAAE;MACR;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACApf,GAAG,GAAI,SAASqf,OAAOA,CAAClY,GAAG,EAAEmY,GAAG,EAAElY,GAAG,EAAC;QAClCjB,KAAK,CAACkB,MAAM,CAAC5I,IAAI,EAAE6gB,GAAG,CAAC;QACvB,IAAIlT,CAAC,GAAGyS,4BAA4B,CAAC1Y,KAAK,CAAC;QAC3C,IAAIiG,CAAC,CAACzK,MAAM,IAAIkF,GAAG,CAAClF,MAAM,IAAIwF,GAAG,GAAGmY,GAAG,EAAE;UACrC,OAAOD,OAAO,CAAClY,GAAG,EAAGA,GAAG,GAAGmY,GAAG,IAAK,CAAC,EAAEA,GAAG,CAAC;QAC9C,CAAC,MAAM,IAAIlT,CAAC,CAAC1K,KAAK,IAAImF,GAAG,CAACnF,KAAK,EAAE;UAC7BmF,GAAG,GAAGuF,CAAC;UACP,IAAIkT,GAAG,GAAGlY,GAAG,EAAE;YACX,OAAOiY,OAAO,CAACC,GAAG,EAAGA,GAAG,GAAGlY,GAAG,IAAK,CAAC,EAAEA,GAAG,CAAC;UAC9C,CAAC,MAAM;YACH,OAAOkY,GAAG;UACd;QACJ,CAAC,MAAM;UACH,OAAOA,GAAG;QACd;MACJ,CAAC,CAAEjF,KAAK,EAAEzL,IAAI,CAACzH,GAAG,CAACmT,GAAG,EAAED,KAAK,GAAG4D,kBAAkB,CAAC,EAAE3D,GAAG,CAAC;MAEzD,IAAIta,GAAG,IAAIqa,KAAK,EAAE;QACd;QACA;QACA,OAAO,IAAI;MACf;MACAA,KAAK,GAAGra,GAAG;MAEXA,GAAG,GAAGmG,KAAK,CAACmB,QAAQ,CAAC,CAAC,CAACoW,MAAM,CAAC,MAAM,CAAC;MACrC,IAAI1d,GAAG,KAAK,CAAC,EAAE;QACX,OAAO,KAAK,CAAC,CAAC;MAClB;MACA,IAAIA,GAAG,GAAG,CAAC,EAAE;QACT;QACAmG,KAAK,CAACkB,MAAM,CAAC5I,IAAI,EAAE0H,KAAK,CAACoZ,WAAW,GAAGvf,GAAG,CAAC;QAC3C6G,GAAG,GAAGgY,4BAA4B,CAAC1Y,KAAK,CAAC;MAC7C;IACJ;;IAEA;IACA;IACA;IACA,IAAIhM,SAAS,EAAE;MACX0M,GAAG,GAAGV,KAAK,CAACmQ,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC;IAEA,IAAI/b,GAAG,GAAG4L,KAAK,CAACmB,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAAC,qBAAqB,CAACrK,IAAI,CAACsf,UAAU,CAAC,EAAE;MACzC;MACAhiB,GAAG,GAAGA,GAAG,CAACuI,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;IAClC,CAAC,MACI,IAAI,IAAI,CAAC7F,IAAI,CAAC1C,GAAG,CAAC,EAAE;MACrB;MACA;MACA;MACA;;MAEA;MACA,IAAIilB,EAAE,GAAG,CAAC;MACV,KAAKxf,GAAG,GAAGkf,SAAS,EAAElf,GAAG,GAAGmG,KAAK,CAACoZ,WAAW,EAAE,EAAEvf,GAAG,EAAE;QAClD,IAAIyf,IAAI,GAAGhC,IAAI,CAACiC,UAAU,CAAC1f,GAAG,CAAC;QAC/B,IAAIyf,IAAI,IAAI,CAAC,EAAE;UACX;UACA;UACAD,EAAE,IAAI,CAAC,GAAGA,EAAE,GAAG,CAAC;QACpB,CAAC,MAAM,IAAIC,IAAI,IAAI,EAAE,IAAIA,IAAI,IAAI,EAAE,EAAE;UACjC;UACAD,EAAE,GAAG,CAAC;QACV,CAAC,MAAM;UACH;UACAA,EAAE,EAAE;QACR;MACJ;;MAEA;MACA;MACA,OAAO,CAACxf,GAAG,GAAGzF,GAAG,CAACmjB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QAClC,IAAIiC,MAAM,GAAG,UAAU,CAAC1W,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,CAACuW,EAAE,GAAGxf,GAAG,IAAI,CAAC,CAAC;QACrDzF,GAAG,GAAGA,GAAG,CAAC0O,MAAM,CAAC,CAAC,EAAEjJ,GAAG,CAAC,GAAG2f,MAAM,GAAGplB,GAAG,CAAC0O,MAAM,CAACjJ,GAAG,GAAG,CAAC,CAAC;MAC3D;IACJ;IAEA,IAAI,CAACof,KAAK,EAAE;MACRlB,cAAc,GAAGrX,GAAG,CAAClF,MAAM;IAC/B;IACA6F,QAAQ,CAACjN,GAAG,EAAEsM,GAAG,CAAC;EACtB;EAEA,SAASW,QAAQA,CAACjN,GAAG,EAAEsM,GAAG,EAAE;IACxB;IACA;IACA;IACA;IACA;IACA,IAAI1M,SAAS,IAAI,CAACoT,KAAK,CAACqQ,UAAU,CAAC,EAAE;MACjC,IAAIlX,MAAM,GAAG2E,aAAa,CAACC,IAAI,CAAC;MAChC,IAAIlL,GAAG,GAAG,CAACyG,GAAG,CAACzG,GAAG,GAAGyG,GAAG,CAAClF,MAAM,GAAG+E,MAAM,IAAI,CAAC;MAC7CG,GAAG,GAAG;QACFzG,GAAG,EAAMA,GAAG;QACZsB,KAAK,EAAImF,GAAG,CAACnF,KAAK;QAClBC,MAAM,EAAGvB,GAAG,GAAGsG,MAAM;QACrBvG,IAAI,EAAK0G,GAAG,CAAC1G,IAAI;QACjBuG,MAAM,EAAGA,MAAM;QACf1C,KAAK,EAAI6C,GAAG,CAACnF,KAAK,GAAGmF,GAAG,CAAC1G;MAC7B,CAAC;IACL;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA,QAAQ6d,aAAa;MACnB,KAAK,WAAW;QACdzjB,GAAG,GAAGA,GAAG,CAAC6V,WAAW,CAAC,CAAC;QACvB;MACF,KAAK,WAAW;QACd7V,GAAG,GAAGA,GAAG,CAACuU,WAAW,CAAC,CAAC;QACvB;MACF,KAAK,YAAY;QACfvU,GAAG,GAAGA,GAAG,CAACuI,OAAO,CAAC,aAAa,EAAE,UAAU8c,CAAC,EAAE;UAAE,OAAOA,CAAC,CAACxP,WAAW,CAAC,CAAC;QAAE,CAAC,CAAC;QAC1E;IACJ;IAEA,IAAIqN,IAAI,GAAG,IAAInjB,QAAQ,CACnBC,GAAG,EAAE,IAAItC,GAAG,CAACgB,IAAI,CAAC,CAAE4N,GAAG,CAAC1G,IAAI,EAAE0G,GAAG,CAACzG,GAAG,CAAE,EACrB,CAAEyG,GAAG,CAAC7C,KAAK,EAAE6C,GAAG,CAACH,MAAM,CAAE,CAAC,EAC5C;MACI4E,IAAI,EAAEA,IAAI;MACV2I,IAAI,EAAE;QAAE5K,KAAK,EAAEA;MAAM;IACzB,CACJ,CAAC;IACDtJ,KAAK,CAACuC,MAAM,CAACmb,IAAI,CAAC;EACtB;EAEA,SAASoC,YAAYA,CAACC,SAAS,EAAEC,OAAO,EAAE1W,KAAK,EAAE2W,IAAI,EAAE;IACnD,IAAI3W,KAAK,EAAE;MACP,IAAI8F,IAAI,GAAG,IAAIvW,IAAI,CAAC;QAAEsb,MAAM,EAAE;UAC1BlQ,KAAK,EAAE8b,SAAS;UAChBzW,KAAK,EAAEA;QACX;MAAC,CAAC,CAAC;MAEH2W,IAAI,IAAIF,SAAS;MACjB3Q,IAAI,CAACgF,MAAM,CAAC4L,OAAO,CAAC5f,IAAI,EAAE6f,IAAI,CAAC,CAC1BxL,MAAM,CAACuL,OAAO,CAACre,KAAK,EAAEse,IAAI,CAAC;MAChCjgB,KAAK,CAACuC,MAAM,CAAC6M,IAAI,CAAC;IACtB;EACJ;EAEA,SAASuP,YAAYA,CAAC7X,GAAG,EAAE;IACvB,IAAI7C,KAAK,GAAG2Z,QAAQ,GAAG,EAAE;IACzBkC,YAAY,CAAC7b,KAAK,EAAE6C,GAAG,EAAEuX,WAAW,EAAEvX,GAAG,CAAClF,MAAM,GAAGkF,GAAG,CAACH,MAAM,GAAG,GAAG,CAAC;IACpEmZ,YAAY,CAAC7b,KAAK,EAAE6C,GAAG,EAAEwX,QAAQ,EAAExX,GAAG,CAACzG,GAAG,CAAC;EAC/C;EAEA,SAASoe,aAAaA,CAAC3X,GAAG,EAAE;IACxB,IAAI7C,KAAK,GAAG2Z,QAAQ,GAAG,EAAE;IACzB,IAAIsC,YAAY,GAAGpZ,GAAG,CAAClF,MAAM;IAC7B,IAAI2c,eAAe,IAAI,IAAI,EAAE;MACzB2B,YAAY,IAAI3B,eAAe;IACnC,CAAC,MAAM;MACH2B,YAAY,IAAIjc,KAAK,CAAC,CAAC;IAC3B;IACA6b,YAAY,CAAC7b,KAAK,EAAE6C,GAAG,EAAEsX,SAAS,EAAE8B,YAAY,CAAC;EACrD;AACJ;AAEA,SAASC,sBAAsBA,CAAC7gB,OAAO,EAAEU,KAAK,EAAEogB,MAAM,EAAE;EACpD,IAAIC,IAAI;EACR,IAAID,MAAM,IAAI,MAAM,EAAE;IAClB;IACAC,IAAI,GAAGrmB,QAAQ,CAAC0G,gBAAgB,CAACV,KAAK;IACtCogB,MAAM,GAAGlb,UAAU,CAACkb,MAAM,CAAC;EAC/B,CAAC,MAAM;IACH;IACA;IACA;IACA;IACAC,IAAI,GAAGrgB,KAAK;IACZogB,MAAM,GAAG,CAAC;EACd;EACA,IAAIxkB,CAAC,GAAGykB,IAAI,CAAC3c,QAAQ;EACrB,KAAK,IAAI9F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhC,CAAC,CAACgD,MAAM,EAAE,EAAEhB,CAAC,EAAE;IAC/B,IAAIhC,CAAC,CAACgC,CAAC,CAAC,CAAC0iB,WAAW,IAAI,IAAI,IAAI1kB,CAAC,CAACgC,CAAC,CAAC,CAAC0iB,WAAW,GAAGF,MAAM,EAAE;MACvD;IACJ;EACJ;EAEA,IAAInM,GAAG,GAAG,IAAIlb,KAAK,CAAC,CAAC;EACrBsnB,IAAI,CAACE,MAAM,CAAC3iB,CAAC,EAAEqW,GAAG,CAAC;EACnBA,GAAG,CAACqM,WAAW,GAAGF,MAAM;EAExB,IAAIC,IAAI,KAAKrgB,KAAK,EAAE;IAChB;IACA;;IAEA;IACA;IACA;IACA,IAAIhG,QAAQ,CAACsG,QAAQ,EAAE;MACnB,IAAI0I,CAAC,GAAGhP,QAAQ,CAACuG,OAAO,CAACigB,MAAM,CAAC,CAAC;MACjC,IAAInU,CAAC,GAAGrS,QAAQ,CAACsG,QAAQ,CAACmgB,aAAa,CAACzX,CAAC,CAAC;MAC1C4I,WAAW,CAACqC,GAAG,EAAEpb,IAAI,CAAC2hB,QAAQ,CAACnO,CAAC,CAAC,CAAC;MAClC;MACA;MACA;IACJ;EACJ;EAEA,OAAO4H,GAAG;AACd;AAEA,SAASpT,aAAaA,CAACvB,OAAO,EAAEqD,SAAS,EAAE;EACvC,IAAItG,KAAK,GAAGoD,gBAAgB,CAACH,OAAO,CAAC;EAErCmO,cAAc,CAACpR,KAAK,CAAC;EAErB,IAAI,iDAAiD,CAACa,IAAI,CAACoC,OAAO,CAACxB,OAAO,CAAC,EAAE;IACzE;EACJ;EAEA,IAAI9D,QAAQ,CAACsG,QAAQ,IAAI,IAAI,EAAE;IAC3B;EACJ;EAEA,IAAIogB,OAAO,GAAGxb,UAAU,CAACC,gBAAgB,CAAC9I,KAAK,EAAE,SAAS,CAAC,CAAC;EAC5D,IAAIskB,UAAU,GAAGxb,gBAAgB,CAAC9I,KAAK,EAAE,YAAY,CAAC;EACtD,IAAIyH,OAAO,GAAGqB,gBAAgB,CAAC9I,KAAK,EAAE,SAAS,CAAC;EAEhD,IAAIqkB,OAAO,KAAK,CAAC,IAAIC,UAAU,IAAI,QAAQ,IAAI7c,OAAO,IAAI,MAAM,EAAE;IAC9D;EACJ;EAEA,IAAIoP,EAAE,GAAG/B,YAAY,CAAC9U,KAAK,CAAC;EAC5B,IAAI2D,KAAK;EAET,IAAIogB,MAAM,GAAGjb,gBAAgB,CAAC9I,KAAK,EAAE,SAAS,CAAC;EAC/C,IAAI,CAAC6W,EAAE,IAAIwN,OAAO,GAAG,CAAC,KAAKN,MAAM,IAAI,MAAM,EAAE;IACzCA,MAAM,GAAG,CAAC;EACd;EACApgB,KAAK,GAAGmgB,sBAAsB,CAAC7gB,OAAO,EAAEqD,SAAS,EAAEyd,MAAM,CAAC;;EAE1D;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,IAAIM,OAAO,GAAG,CAAC,EAAE;IACb1gB,KAAK,CAAC0gB,OAAO,CAACA,OAAO,GAAG1gB,KAAK,CAAC0gB,OAAO,CAAC,CAAC,CAAC;EAC5C;EAEAhZ,YAAY,CAACpI,OAAO,EAAEjD,KAAK,EAAE2D,KAAK,CAAC;EAEnC,IAAI,CAACkT,EAAE,EAAE;IACL+B,yBAAyB,CAAC3V,OAAO,EAAEU,KAAK,CAAC;EAC7C,CAAC,MACI;IACDwQ,SAAS,CAAClR,OAAO,EAAE,YAAU;MACzB;MACA0Q,sBAAsB,CAAC1Q,OAAO,CAACjD,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,CAAC;;MAEvE;MACA2T,sBAAsB,CAAC1Q,OAAO,CAACjD,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,WAAW,CAAC;;MAExE;MACA;MACA;MACA,IAAI8I,gBAAgB,CAAC9I,KAAK,EAAE,UAAU,CAAC,IAAI,QAAQ,EAAE;QACjD;QACA2T,sBAAsB,CAAC1Q,OAAO,CAACjD,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,CAAC;MAC9E;;MAEA;MACA;MACA,IAAIgT,IAAI,GAAG/P,OAAO,CAACY,qBAAqB,CAAC,CAAC;MAC1C,IAAIhB,CAAC,GAAGmQ,IAAI,CAACjP,IAAI,GAAG8S,EAAE,CAAC5B,MAAM,CAAC,CAAC,CAAC;MAChC,IAAInS,CAAC,GAAGkQ,IAAI,CAAChP,GAAG,GAAG6S,EAAE,CAAC5B,MAAM,CAAC,CAAC,CAAC;MAC/B,IAAItI,CAAC,GAAG,CAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC9J,CAAC,EAAE,CAACC,CAAC,CAAE;MAC9B6J,CAAC,GAAG4X,IAAI,CAAC5X,CAAC,EAAEkK,EAAE,CAAC7B,MAAM,CAAC;MACtBrI,CAAC,GAAG4X,IAAI,CAAC5X,CAAC,EAAE,CAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE9J,CAAC,EAAEC,CAAC,CAAE,CAAC;MACjC6J,CAAC,GAAG7I,YAAY,CAACH,KAAK,EAAEgJ,CAAC,CAAC;MAE1BhP,QAAQ,CAACuG,OAAO,GAAGvG,QAAQ,CAACuG,OAAO,CAACsgB,YAAY,CAAC7X,CAAC,CAAC;MAEnDiM,yBAAyB,CAAC3V,OAAO,EAAEU,KAAK,CAAC;IAC7C,CAAC,CAAC;EACN;EAEA6H,WAAW,CAAC,CAAC;;EAEb;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAS+Y,IAAIA,CAAChlB,CAAC,EAAEggB,CAAC,EAAE;EAChB,IAAIkF,EAAE,GAAGllB,CAAC,CAAC,CAAC,CAAC;IAAEmlB,EAAE,GAAGnlB,CAAC,CAAC,CAAC,CAAC;IAAEolB,EAAE,GAAGplB,CAAC,CAAC,CAAC,CAAC;IAAEqlB,EAAE,GAAGrlB,CAAC,CAAC,CAAC,CAAC;IAAEslB,EAAE,GAAGtlB,CAAC,CAAC,CAAC,CAAC;IAAEulB,EAAE,GAAGvlB,CAAC,CAAC,CAAC,CAAC;EACpE,IAAIwlB,EAAE,GAAGxF,CAAC,CAAC,CAAC,CAAC;IAAEyF,EAAE,GAAGzF,CAAC,CAAC,CAAC,CAAC;IAAE0F,EAAE,GAAG1F,CAAC,CAAC,CAAC,CAAC;IAAE2F,EAAE,GAAG3F,CAAC,CAAC,CAAC,CAAC;IAAE4F,EAAE,GAAG5F,CAAC,CAAC,CAAC,CAAC;IAAE6F,EAAE,GAAG7F,CAAC,CAAC,CAAC,CAAC;EACpE,OAAO,CACHkF,EAAE,GAACM,EAAE,GAAGL,EAAE,GAACO,EAAE,EAAWR,EAAE,GAACO,EAAE,GAAGN,EAAE,GAACQ,EAAE,EACrCP,EAAE,GAACI,EAAE,GAAGH,EAAE,GAACK,EAAE,EAAWN,EAAE,GAACK,EAAE,GAAGJ,EAAE,GAACM,EAAE,EACrCL,EAAE,GAACE,EAAE,GAAGD,EAAE,GAACG,EAAE,GAAGE,EAAE,EAAMN,EAAE,GAACG,EAAE,GAAGF,EAAE,GAACI,EAAE,GAAGE,EAAE,CAC7C;AACL;AAEA,SAASpiB,OAAO,EAAEoI,QAAQ,EAAE7H,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}