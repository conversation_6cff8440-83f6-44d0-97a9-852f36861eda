{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as l from \"react\";\nimport e from \"./List.mjs\";\nimport { classNames as a } from \"@progress/kendo-react-common\";\nconst i = t => /* @__PURE__ */l.createElement(e, {\n  ...t,\n  wrapperCssClass: \"k-table-body k-table-scroller\",\n  listClassName: a(\"k-table k-table-list\", {\n    \"k-virtual-table\": t.virtual !== void 0\n  }),\n  listStyle: {\n    ...t.listStyle\n  }\n});\nexport { i as MultiColumnList };", "map": {"version": 3, "names": ["l", "e", "classNames", "a", "i", "t", "createElement", "wrapperCssClass", "listClassName", "virtual", "listStyle", "MultiColumnList"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dropdowns/common/MultiColumnList.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as l from \"react\";\nimport e from \"./List.mjs\";\nimport { classNames as a } from \"@progress/kendo-react-common\";\nconst i = (t) => /* @__PURE__ */ l.createElement(\n  e,\n  {\n    ...t,\n    wrapperCssClass: \"k-table-body k-table-scroller\",\n    listClassName: a(\"k-table k-table-list\", {\n      \"k-virtual-table\": t.virtual !== void 0\n    }),\n    listStyle: { ...t.listStyle }\n  }\n);\nexport {\n  i as MultiColumnList\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AAC9D,MAAMC,CAAC,GAAIC,CAAC,IAAK,eAAgBL,CAAC,CAACM,aAAa,CAC9CL,CAAC,EACD;EACE,GAAGI,CAAC;EACJE,eAAe,EAAE,+BAA+B;EAChDC,aAAa,EAAEL,CAAC,CAAC,sBAAsB,EAAE;IACvC,iBAAiB,EAAEE,CAAC,CAACI,OAAO,KAAK,KAAK;EACxC,CAAC,CAAC;EACFC,SAAS,EAAE;IAAE,GAAGL,CAAC,CAACK;EAAU;AAC9B,CACF,CAAC;AACD,SACEN,CAAC,IAAIO,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}