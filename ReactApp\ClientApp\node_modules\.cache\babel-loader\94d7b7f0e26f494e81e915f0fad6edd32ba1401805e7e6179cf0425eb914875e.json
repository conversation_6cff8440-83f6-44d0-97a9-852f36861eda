{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { getNestedValue as I, isArray as x, getAllDirectIndirectChildrenIds as m, areAllDirectChildrenChecked as p, getAllParents as g, treeIdUtils as u } from \"@progress/kendo-react-common\";\nimport { CHILDREN_FIELD as O } from \"./utils/consts.mjs\";\nfunction b(i, n, t, s = {}, d) {\n  if (!t || !t.length) return [];\n  const {\n      ids: l,\n      idField: r\n    } = k(n),\n    c = r ? I(r, i.item) : i.itemHierarchicalIndex,\n    h = l.indexOf(c),\n    o = h === -1,\n    a = d || O;\n  let e;\n  return s.singleMode ? e = o ? [c] : [] : (e = l.slice(), o ? e.push(c) : e.splice(h, 1), s.checkChildren && C(i.item, i.itemHierarchicalIndex, o, r, a, e), s.checkParents && D(i.itemHierarchicalIndex, o, r, a, e, t)), x(n) ? e : Object.assign({}, n, {\n    ids: e\n  });\n}\nfunction k(i) {\n  let n, t;\n  return x(i) ? n = i : (n = i.ids || [], t = i.idField), {\n    ids: n,\n    idField: t\n  };\n}\nfunction C(i, n, t, s, d, l) {\n  m(i, n, d, s).forEach(r => {\n    t && l.indexOf(r) === -1 ? l.push(r) : !t && l.indexOf(r) > -1 && l.splice(l.indexOf(r), 1);\n  });\n}\nfunction D(i, n, t, s, d, l) {\n  const r = a();\n  let c = r.next();\n  n ? h() : o();\n  function h() {\n    for (; !c.done;) {\n      const {\n        id: e,\n        item: f\n      } = c.value;\n      if (d.indexOf(e) === -1 && p(f, e, t, s, d)) d.push(e), c = r.next();else break;\n    }\n  }\n  function o() {\n    for (; !c.done;) {\n      const {\n          id: e\n        } = c.value,\n        f = d.indexOf(e);\n      if (f > -1) d.splice(f, 1), c = r.next();else break;\n    }\n  }\n  function* a() {\n    if (t) {\n      const e = g(i, s, l);\n      for (let f = e.length - 1; f > -1; f--) yield {\n        id: I(t, e[f]),\n        item: n ? e[f] : void 0\n      };\n    } else {\n      let e = u.getDirectParentId(i);\n      for (; e;) yield {\n        id: e,\n        item: n ? u.getItemById(e, l, s) : void 0\n      }, e = u.getDirectParentId(e);\n    }\n  }\n}\nexport { b as handleTreeViewCheckChange };", "map": {"version": 3, "names": ["getNestedValue", "I", "isArray", "x", "getAllDirectIndirectChildrenIds", "m", "areAllDirectChildrenChecked", "p", "getAllParents", "g", "treeIdUtils", "u", "CHILDREN_FIELD", "O", "b", "i", "n", "t", "s", "d", "length", "ids", "l", "idField", "r", "k", "c", "item", "itemHierarchicalIndex", "h", "indexOf", "o", "a", "e", "singleMode", "slice", "push", "splice", "check<PERSON><PERSON><PERSON><PERSON>", "C", "checkParents", "D", "Object", "assign", "for<PERSON>ach", "next", "done", "id", "f", "value", "getDirectParentId", "getItemById", "handleTreeViewCheckChange"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-treeview/handleTreeViewCheckChange.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { getNestedValue as I, isArray as x, getAllDirectIndirectChildrenIds as m, areAllDirectChildrenChecked as p, getAllParents as g, treeIdUtils as u } from \"@progress/kendo-react-common\";\nimport { CHILDREN_FIELD as O } from \"./utils/consts.mjs\";\nfunction b(i, n, t, s = {}, d) {\n  if (!t || !t.length)\n    return [];\n  const { ids: l, idField: r } = k(n), c = r ? I(r, i.item) : i.itemHierarchicalIndex, h = l.indexOf(c), o = h === -1, a = d || O;\n  let e;\n  return s.singleMode ? e = o ? [c] : [] : (e = l.slice(), o ? e.push(c) : e.splice(h, 1), s.checkChildren && C(i.item, i.itemHierarchicalIndex, o, r, a, e), s.checkParents && D(i.itemHierarchicalIndex, o, r, a, e, t)), x(n) ? e : Object.assign({}, n, { ids: e });\n}\nfunction k(i) {\n  let n, t;\n  return x(i) ? n = i : (n = i.ids || [], t = i.idField), { ids: n, idField: t };\n}\nfunction C(i, n, t, s, d, l) {\n  m(i, n, d, s).forEach((r) => {\n    t && l.indexOf(r) === -1 ? l.push(r) : !t && l.indexOf(r) > -1 && l.splice(l.indexOf(r), 1);\n  });\n}\nfunction D(i, n, t, s, d, l) {\n  const r = a();\n  let c = r.next();\n  n ? h() : o();\n  function h() {\n    for (; !c.done; ) {\n      const { id: e, item: f } = c.value;\n      if (d.indexOf(e) === -1 && p(f, e, t, s, d))\n        d.push(e), c = r.next();\n      else\n        break;\n    }\n  }\n  function o() {\n    for (; !c.done; ) {\n      const { id: e } = c.value, f = d.indexOf(e);\n      if (f > -1)\n        d.splice(f, 1), c = r.next();\n      else\n        break;\n    }\n  }\n  function* a() {\n    if (t) {\n      const e = g(i, s, l);\n      for (let f = e.length - 1; f > -1; f--)\n        yield { id: I(t, e[f]), item: n ? e[f] : void 0 };\n    } else {\n      let e = u.getDirectParentId(i);\n      for (; e; )\n        yield {\n          id: e,\n          item: n ? u.getItemById(e, l, s) : void 0\n        }, e = u.getDirectParentId(e);\n    }\n  }\n}\nexport {\n  b as handleTreeViewCheckChange\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,cAAc,IAAIC,CAAC,EAAEC,OAAO,IAAIC,CAAC,EAAEC,+BAA+B,IAAIC,CAAC,EAAEC,2BAA2B,IAAIC,CAAC,EAAEC,aAAa,IAAIC,CAAC,EAAEC,WAAW,IAAIC,CAAC,QAAQ,8BAA8B;AAC9L,SAASC,cAAc,IAAIC,CAAC,QAAQ,oBAAoB;AACxD,SAASC,CAACA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC,EAAEC,CAAC,EAAE;EAC7B,IAAI,CAACF,CAAC,IAAI,CAACA,CAAC,CAACG,MAAM,EACjB,OAAO,EAAE;EACX,MAAM;MAAEC,GAAG,EAAEC,CAAC;MAAEC,OAAO,EAAEC;IAAE,CAAC,GAAGC,CAAC,CAACT,CAAC,CAAC;IAAEU,CAAC,GAAGF,CAAC,GAAGvB,CAAC,CAACuB,CAAC,EAAET,CAAC,CAACY,IAAI,CAAC,GAAGZ,CAAC,CAACa,qBAAqB;IAAEC,CAAC,GAAGP,CAAC,CAACQ,OAAO,CAACJ,CAAC,CAAC;IAAEK,CAAC,GAAGF,CAAC,KAAK,CAAC,CAAC;IAAEG,CAAC,GAAGb,CAAC,IAAIN,CAAC;EAC/H,IAAIoB,CAAC;EACL,OAAOf,CAAC,CAACgB,UAAU,GAAGD,CAAC,GAAGF,CAAC,GAAG,CAACL,CAAC,CAAC,GAAG,EAAE,IAAIO,CAAC,GAAGX,CAAC,CAACa,KAAK,CAAC,CAAC,EAAEJ,CAAC,GAAGE,CAAC,CAACG,IAAI,CAACV,CAAC,CAAC,GAAGO,CAAC,CAACI,MAAM,CAACR,CAAC,EAAE,CAAC,CAAC,EAAEX,CAAC,CAACoB,aAAa,IAAIC,CAAC,CAACxB,CAAC,CAACY,IAAI,EAAEZ,CAAC,CAACa,qBAAqB,EAAEG,CAAC,EAAEP,CAAC,EAAEQ,CAAC,EAAEC,CAAC,CAAC,EAAEf,CAAC,CAACsB,YAAY,IAAIC,CAAC,CAAC1B,CAAC,CAACa,qBAAqB,EAAEG,CAAC,EAAEP,CAAC,EAAEQ,CAAC,EAAEC,CAAC,EAAEhB,CAAC,CAAC,CAAC,EAAEd,CAAC,CAACa,CAAC,CAAC,GAAGiB,CAAC,GAAGS,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE3B,CAAC,EAAE;IAAEK,GAAG,EAAEY;EAAE,CAAC,CAAC;AACvQ;AACA,SAASR,CAACA,CAACV,CAAC,EAAE;EACZ,IAAIC,CAAC,EAAEC,CAAC;EACR,OAAOd,CAAC,CAACY,CAAC,CAAC,GAAGC,CAAC,GAAGD,CAAC,IAAIC,CAAC,GAAGD,CAAC,CAACM,GAAG,IAAI,EAAE,EAAEJ,CAAC,GAAGF,CAAC,CAACQ,OAAO,CAAC,EAAE;IAAEF,GAAG,EAAEL,CAAC;IAAEO,OAAO,EAAEN;EAAE,CAAC;AAChF;AACA,SAASsB,CAACA,CAACxB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEG,CAAC,EAAE;EAC3BjB,CAAC,CAACU,CAAC,EAAEC,CAAC,EAAEG,CAAC,EAAED,CAAC,CAAC,CAAC0B,OAAO,CAAEpB,CAAC,IAAK;IAC3BP,CAAC,IAAIK,CAAC,CAACQ,OAAO,CAACN,CAAC,CAAC,KAAK,CAAC,CAAC,GAAGF,CAAC,CAACc,IAAI,CAACZ,CAAC,CAAC,GAAG,CAACP,CAAC,IAAIK,CAAC,CAACQ,OAAO,CAACN,CAAC,CAAC,GAAG,CAAC,CAAC,IAAIF,CAAC,CAACe,MAAM,CAACf,CAAC,CAACQ,OAAO,CAACN,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7F,CAAC,CAAC;AACJ;AACA,SAASiB,CAACA,CAAC1B,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEG,CAAC,EAAE;EAC3B,MAAME,CAAC,GAAGQ,CAAC,CAAC,CAAC;EACb,IAAIN,CAAC,GAAGF,CAAC,CAACqB,IAAI,CAAC,CAAC;EAChB7B,CAAC,GAAGa,CAAC,CAAC,CAAC,GAAGE,CAAC,CAAC,CAAC;EACb,SAASF,CAACA,CAAA,EAAG;IACX,OAAO,CAACH,CAAC,CAACoB,IAAI,GAAI;MAChB,MAAM;QAAEC,EAAE,EAAEd,CAAC;QAAEN,IAAI,EAAEqB;MAAE,CAAC,GAAGtB,CAAC,CAACuB,KAAK;MAClC,IAAI9B,CAAC,CAACW,OAAO,CAACG,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI1B,CAAC,CAACyC,CAAC,EAAEf,CAAC,EAAEhB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,EACzCA,CAAC,CAACiB,IAAI,CAACH,CAAC,CAAC,EAAEP,CAAC,GAAGF,CAAC,CAACqB,IAAI,CAAC,CAAC,CAAC,KAExB;IACJ;EACF;EACA,SAASd,CAACA,CAAA,EAAG;IACX,OAAO,CAACL,CAAC,CAACoB,IAAI,GAAI;MAChB,MAAM;UAAEC,EAAE,EAAEd;QAAE,CAAC,GAAGP,CAAC,CAACuB,KAAK;QAAED,CAAC,GAAG7B,CAAC,CAACW,OAAO,CAACG,CAAC,CAAC;MAC3C,IAAIe,CAAC,GAAG,CAAC,CAAC,EACR7B,CAAC,CAACkB,MAAM,CAACW,CAAC,EAAE,CAAC,CAAC,EAAEtB,CAAC,GAAGF,CAAC,CAACqB,IAAI,CAAC,CAAC,CAAC,KAE7B;IACJ;EACF;EACA,UAAUb,CAACA,CAAA,EAAG;IACZ,IAAIf,CAAC,EAAE;MACL,MAAMgB,CAAC,GAAGxB,CAAC,CAACM,CAAC,EAAEG,CAAC,EAAEI,CAAC,CAAC;MACpB,KAAK,IAAI0B,CAAC,GAAGf,CAAC,CAACb,MAAM,GAAG,CAAC,EAAE4B,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EACpC,MAAM;QAAED,EAAE,EAAE9C,CAAC,CAACgB,CAAC,EAAEgB,CAAC,CAACe,CAAC,CAAC,CAAC;QAAErB,IAAI,EAAEX,CAAC,GAAGiB,CAAC,CAACe,CAAC,CAAC,GAAG,KAAK;MAAE,CAAC;IACrD,CAAC,MAAM;MACL,IAAIf,CAAC,GAAGtB,CAAC,CAACuC,iBAAiB,CAACnC,CAAC,CAAC;MAC9B,OAAOkB,CAAC,GACN,MAAM;QACJc,EAAE,EAAEd,CAAC;QACLN,IAAI,EAAEX,CAAC,GAAGL,CAAC,CAACwC,WAAW,CAAClB,CAAC,EAAEX,CAAC,EAAEJ,CAAC,CAAC,GAAG,KAAK;MAC1C,CAAC,EAAEe,CAAC,GAAGtB,CAAC,CAACuC,iBAAiB,CAACjB,CAAC,CAAC;IACjC;EACF;AACF;AACA,SACEnB,CAAC,IAAIsC,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}