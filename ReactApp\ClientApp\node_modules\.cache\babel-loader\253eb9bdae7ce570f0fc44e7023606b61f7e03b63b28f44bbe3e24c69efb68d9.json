{"ast": null, "code": "import { extend, isFunction } from \"./utils\";\nvar defaultOptions = {\n  events: {}\n};\nvar Observable = /** @class */function () {\n  function Observable(options) {\n    this.options = extend({}, defaultOptions, options);\n  }\n  Observable.prototype.destroy = function () {\n    this.options = {};\n  };\n  /**\n   * @hidden\n   */\n  Observable.prototype.trigger = function (eventName, args) {\n    if (args === void 0) {\n      args = {};\n    }\n    var eventData = {\n      defaultPrevented: false,\n      preventDefault: function () {\n        eventData.defaultPrevented = true;\n      }\n    };\n    if (isFunction(this.options.events[eventName])) {\n      this.options.events[eventName](extend(eventData, args, {\n        sender: this\n      }));\n      return eventData.defaultPrevented;\n    }\n    return false;\n  };\n  return Observable;\n}();\nexport { Observable };", "map": {"version": 3, "names": ["extend", "isFunction", "defaultOptions", "events", "Observable", "options", "prototype", "destroy", "trigger", "eventName", "args", "eventData", "defaultPrevented", "preventDefault", "sender"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-dateinputs-common/dist/es/common/observable.js"], "sourcesContent": ["import { extend, isFunction } from \"./utils\";\nvar defaultOptions = {\n    events: {}\n};\nvar Observable = /** @class */ (function () {\n    function Observable(options) {\n        this.options = extend({}, defaultOptions, options);\n    }\n    Observable.prototype.destroy = function () {\n        this.options = {};\n    };\n    /**\n     * @hidden\n     */\n    Observable.prototype.trigger = function (eventName, args) {\n        if (args === void 0) { args = {}; }\n        var eventData = {\n            defaultPrevented: false,\n            preventDefault: function () {\n                eventData.defaultPrevented = true;\n            }\n        };\n        if (isFunction(this.options.events[eventName])) {\n            this.options.events[eventName](extend(eventData, args, {\n                sender: this\n            }));\n            return eventData.defaultPrevented;\n        }\n        return false;\n    };\n    return Observable;\n}());\nexport { Observable };\n"], "mappings": "AAAA,SAASA,MAAM,EAAEC,UAAU,QAAQ,SAAS;AAC5C,IAAIC,cAAc,GAAG;EACjBC,MAAM,EAAE,CAAC;AACb,CAAC;AACD,IAAIC,UAAU,GAAG,aAAe,YAAY;EACxC,SAASA,UAAUA,CAACC,OAAO,EAAE;IACzB,IAAI,CAACA,OAAO,GAAGL,MAAM,CAAC,CAAC,CAAC,EAAEE,cAAc,EAAEG,OAAO,CAAC;EACtD;EACAD,UAAU,CAACE,SAAS,CAACC,OAAO,GAAG,YAAY;IACvC,IAAI,CAACF,OAAO,GAAG,CAAC,CAAC;EACrB,CAAC;EACD;AACJ;AACA;EACID,UAAU,CAACE,SAAS,CAACE,OAAO,GAAG,UAAUC,SAAS,EAAEC,IAAI,EAAE;IACtD,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;MAAEA,IAAI,GAAG,CAAC,CAAC;IAAE;IAClC,IAAIC,SAAS,GAAG;MACZC,gBAAgB,EAAE,KAAK;MACvBC,cAAc,EAAE,SAAAA,CAAA,EAAY;QACxBF,SAAS,CAACC,gBAAgB,GAAG,IAAI;MACrC;IACJ,CAAC;IACD,IAAIX,UAAU,CAAC,IAAI,CAACI,OAAO,CAACF,MAAM,CAACM,SAAS,CAAC,CAAC,EAAE;MAC5C,IAAI,CAACJ,OAAO,CAACF,MAAM,CAACM,SAAS,CAAC,CAACT,MAAM,CAACW,SAAS,EAAED,IAAI,EAAE;QACnDI,MAAM,EAAE;MACZ,CAAC,CAAC,CAAC;MACH,OAAOH,SAAS,CAACC,gBAAgB;IACrC;IACA,OAAO,KAAK;EAChB,CAAC;EACD,OAAOR,UAAU;AACrB,CAAC,CAAC,CAAE;AACJ,SAASA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}