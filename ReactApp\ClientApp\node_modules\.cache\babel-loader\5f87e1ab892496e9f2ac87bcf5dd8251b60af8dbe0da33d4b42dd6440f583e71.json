{"ast": null, "code": "export default function createPromise() {\n  var resolveFn, rejectFn;\n  var promise = new Promise(function (resolve, reject) {\n    resolveFn = function (data) {\n      promise._state = \"resolved\";\n      resolve(data);\n      return promise;\n    };\n    rejectFn = function (data) {\n      promise._state = \"rejected\";\n      reject(data);\n      return promise;\n    };\n  });\n  promise._state = \"pending\";\n  promise.resolve = resolveFn;\n  promise.reject = rejectFn;\n  promise.state = function () {\n    return promise._state;\n  };\n  return promise;\n}", "map": {"version": 3, "names": ["createPromise", "resolveFn", "rejectFn", "promise", "Promise", "resolve", "reject", "data", "_state", "state"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/util/create-promise.js"], "sourcesContent": ["export default function createPromise() {\n    var resolveFn, rejectFn;\n    var promise = new Promise(function (resolve, reject) {\n        resolveFn = function (data) {\n            promise._state = \"resolved\";\n            resolve(data);\n            return promise;\n        };\n        rejectFn = function (data) {\n            promise._state = \"rejected\";\n            reject(data);\n\n            return promise;\n        };\n    });\n    promise._state = \"pending\";\n    promise.resolve = resolveFn;\n    promise.reject = rejectFn;\n    promise.state = function () { return promise._state; };\n\n    return promise;\n}\n"], "mappings": "AAAA,eAAe,SAASA,aAAaA,CAAA,EAAG;EACpC,IAAIC,SAAS,EAAEC,QAAQ;EACvB,IAAIC,OAAO,GAAG,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;IACjDL,SAAS,GAAG,SAAAA,CAAUM,IAAI,EAAE;MACxBJ,OAAO,CAACK,MAAM,GAAG,UAAU;MAC3BH,OAAO,CAACE,IAAI,CAAC;MACb,OAAOJ,OAAO;IAClB,CAAC;IACDD,QAAQ,GAAG,SAAAA,CAAUK,IAAI,EAAE;MACvBJ,OAAO,CAACK,MAAM,GAAG,UAAU;MAC3BF,MAAM,CAACC,IAAI,CAAC;MAEZ,OAAOJ,OAAO;IAClB,CAAC;EACL,CAAC,CAAC;EACFA,OAAO,CAACK,MAAM,GAAG,SAAS;EAC1BL,OAAO,CAACE,OAAO,GAAGJ,SAAS;EAC3BE,OAAO,CAACG,MAAM,GAAGJ,QAAQ;EACzBC,OAAO,CAACM,KAAK,GAAG,YAAY;IAAE,OAAON,OAAO,CAACK,MAAM;EAAE,CAAC;EAEtD,OAAOL,OAAO;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}