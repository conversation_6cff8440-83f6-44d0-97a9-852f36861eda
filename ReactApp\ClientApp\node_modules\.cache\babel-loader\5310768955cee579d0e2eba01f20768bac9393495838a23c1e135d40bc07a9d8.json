{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as t from \"react\";\nconst e = {\n    animation: !0,\n    expanded: !1,\n    mode: \"overlay\",\n    position: \"start\",\n    mini: !1,\n    dir: \"ltr\",\n    width: 240,\n    miniWidth: 50\n  },\n  o = t.createContext(e);\nexport { o as DrawerContext };", "map": {"version": 3, "names": ["t", "e", "animation", "expanded", "mode", "position", "mini", "dir", "width", "miniWidth", "o", "createContext", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/drawer/context/DrawerContext.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as t from \"react\";\nconst e = {\n  animation: !0,\n  expanded: !1,\n  mode: \"overlay\",\n  position: \"start\",\n  mini: !1,\n  dir: \"ltr\",\n  width: 240,\n  miniWidth: 50\n}, o = t.createContext(e);\nexport {\n  o as DrawerContext\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,MAAMC,CAAC,GAAG;IACRC,SAAS,EAAE,CAAC,CAAC;IACbC,QAAQ,EAAE,CAAC,CAAC;IACZC,IAAI,EAAE,SAAS;IACfC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,CAAC,CAAC;IACRC,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,GAAG;IACVC,SAAS,EAAE;EACb,CAAC;EAAEC,CAAC,GAAGV,CAAC,CAACW,aAAa,CAACV,CAAC,CAAC;AACzB,SACES,CAAC,IAAIE,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}