{"ast": null, "code": "import { exec, map, aggregatesCombinator, expandAggregates } from '../transducers';\nvar identity = map(function (x) {\n  return x;\n});\n// tslint:disable:max-line-length\n/**\n * Applies the specified [`AggregateDescriptors`]({% slug api_kendo-data-query_aggregatedescriptor %}) to the data. Returns an [`AggregateResult`]({% slug api_kendo-data-query_aggregateresult %}) instance.\n *\n * @example\n * ```ts\n * const data = [\n *    { unitPrice: 23, unitsInStock: 21 },\n *    { unitPrice: 10, unitsInStock: 12 },\n *    { unitPrice: 20, unitsInStock: 33 }\n * ];\n *\n * const result = aggregateBy(data, [\n *   { aggregate: \"sum\", field: \"unitPrice\" },\n *   { aggregate: \"sum\", field: \"unitsInStock\" }\n * ]);\n *\n * //output:\n * // {\n * //     \"unitPrice\": { \"sum\": 53 },\n * //     \"unitsInStock\": { \"sum\": 66 }\n * // }\n * ```\n * @param {T[]} data - The data on which the calculation will be executed.\n * @param {AggregateDescriptor[]} descriptors - The aggregate operations that will be executed.\n * @param {any} transformers - For internal use.\n * @returns {AggregateResult} - The aggregated result.\n * For more information, refer to the [aggregateresult](slug:api_kendo-data-query_aggregateresult) configuration.\n */\n// tslint:enable:max-line-length\nexport var aggregateBy = function (data, descriptors, transformers) {\n  if (descriptors === void 0) {\n    descriptors = [];\n  }\n  if (transformers === void 0) {\n    transformers = identity;\n  }\n  var initialValue = {};\n  if (!descriptors.length) {\n    return initialValue;\n  }\n  var result = exec(transformers(aggregatesCombinator(descriptors)), initialValue, data);\n  return expandAggregates(result);\n};", "map": {"version": 3, "names": ["exec", "map", "aggregatesCombinator", "expandAggregates", "identity", "x", "aggregateBy", "data", "descriptors", "transformers", "initialValue", "length", "result"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-data-query/dist/es/grouping/aggregate.operators.js"], "sourcesContent": ["import { exec, map, aggregatesCombinator, expandAggregates } from '../transducers';\nvar identity = map(function (x) { return x; });\n// tslint:disable:max-line-length\n/**\n * Applies the specified [`AggregateDescriptors`]({% slug api_kendo-data-query_aggregatedescriptor %}) to the data. Returns an [`AggregateResult`]({% slug api_kendo-data-query_aggregateresult %}) instance.\n *\n * @example\n * ```ts\n * const data = [\n *    { unitPrice: 23, unitsInStock: 21 },\n *    { unitPrice: 10, unitsInStock: 12 },\n *    { unitPrice: 20, unitsInStock: 33 }\n * ];\n *\n * const result = aggregateBy(data, [\n *   { aggregate: \"sum\", field: \"unitPrice\" },\n *   { aggregate: \"sum\", field: \"unitsInStock\" }\n * ]);\n *\n * //output:\n * // {\n * //     \"unitPrice\": { \"sum\": 53 },\n * //     \"unitsInStock\": { \"sum\": 66 }\n * // }\n * ```\n * @param {T[]} data - The data on which the calculation will be executed.\n * @param {AggregateDescriptor[]} descriptors - The aggregate operations that will be executed.\n * @param {any} transformers - For internal use.\n * @returns {AggregateResult} - The aggregated result.\n * For more information, refer to the [aggregateresult](slug:api_kendo-data-query_aggregateresult) configuration.\n */\n// tslint:enable:max-line-length\nexport var aggregateBy = function (data, descriptors, transformers) {\n    if (descriptors === void 0) { descriptors = []; }\n    if (transformers === void 0) { transformers = identity; }\n    var initialValue = {};\n    if (!descriptors.length) {\n        return initialValue;\n    }\n    var result = exec(transformers(aggregatesCombinator(descriptors)), initialValue, data);\n    return expandAggregates(result);\n};\n"], "mappings": "AAAA,SAASA,IAAI,EAAEC,GAAG,EAAEC,oBAAoB,EAAEC,gBAAgB,QAAQ,gBAAgB;AAClF,IAAIC,QAAQ,GAAGH,GAAG,CAAC,UAAUI,CAAC,EAAE;EAAE,OAAOA,CAAC;AAAE,CAAC,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,WAAW,GAAG,SAAAA,CAAUC,IAAI,EAAEC,WAAW,EAAEC,YAAY,EAAE;EAChE,IAAID,WAAW,KAAK,KAAK,CAAC,EAAE;IAAEA,WAAW,GAAG,EAAE;EAAE;EAChD,IAAIC,YAAY,KAAK,KAAK,CAAC,EAAE;IAAEA,YAAY,GAAGL,QAAQ;EAAE;EACxD,IAAIM,YAAY,GAAG,CAAC,CAAC;EACrB,IAAI,CAACF,WAAW,CAACG,MAAM,EAAE;IACrB,OAAOD,YAAY;EACvB;EACA,IAAIE,MAAM,GAAGZ,IAAI,CAACS,YAAY,CAACP,oBAAoB,CAACM,WAAW,CAAC,CAAC,EAAEE,YAAY,EAAEH,IAAI,CAAC;EACtF,OAAOJ,gBAAgB,CAACS,MAAM,CAAC;AACnC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}