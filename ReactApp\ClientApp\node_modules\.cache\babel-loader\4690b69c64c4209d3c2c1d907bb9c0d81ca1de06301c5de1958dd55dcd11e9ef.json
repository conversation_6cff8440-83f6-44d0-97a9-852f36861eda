{"ast": null, "code": "import withGeometry from '../mixins/with-geometry';\nimport Element from './element';\nimport paintable from '../mixins/paintable';\nimport measurable from '../mixins/measurable';\nimport GeometryRect from '../geometry/rect';\nimport { defined } from '../util';\nvar Rect = function (superclass) {\n  function Rect(geometry, options) {\n    if (geometry === void 0) geometry = new GeometryRect();\n    if (options === void 0) options = {};\n    superclass.call(this, options);\n    this.geometry(geometry);\n    if (!defined(this.options.stroke)) {\n      this.stroke(\"#000\");\n    }\n  }\n  if (superclass) Rect.__proto__ = superclass;\n  Rect.prototype = Object.create(superclass && superclass.prototype);\n  Rect.prototype.constructor = Rect;\n  var prototypeAccessors = {\n    nodeType: {\n      configurable: true\n    }\n  };\n  prototypeAccessors.nodeType.get = function () {\n    return \"Rect\";\n  };\n  Rect.prototype._bbox = function _bbox(matrix) {\n    return this._geometry.bbox(matrix);\n  };\n  Rect.prototype.rawBBox = function rawBBox() {\n    return this._geometry.bbox();\n  };\n  Rect.prototype._containsPoint = function _containsPoint(point) {\n    return this._geometry.containsPoint(point);\n  };\n  Rect.prototype._isOnPath = function _isOnPath(point) {\n    return this.geometry()._isOnPath(point, this.options.stroke.width / 2);\n  };\n  Object.defineProperties(Rect.prototype, prototypeAccessors);\n  return Rect;\n}(paintable(measurable(withGeometry(Element))));\nexport default Rect;", "map": {"version": 3, "names": ["withGeometry", "Element", "paintable", "measurable", "GeometryRect", "defined", "Rect", "superclass", "geometry", "options", "call", "stroke", "__proto__", "prototype", "Object", "create", "constructor", "prototypeAccessors", "nodeType", "configurable", "get", "_bbox", "matrix", "_geometry", "bbox", "rawBBox", "_containsPoint", "point", "containsPoint", "_isOnPath", "width", "defineProperties"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/shapes/rect.js"], "sourcesContent": ["import withGeometry from '../mixins/with-geometry';\nimport Element from './element';\nimport paintable from '../mixins/paintable';\nimport measurable from '../mixins/measurable';\nimport GeometryRect from '../geometry/rect';\nimport { defined } from '../util';\n\n\nvar Rect = (function (superclass) {\n    function Rect(geometry, options) {\n        if ( geometry === void 0 ) geometry = new GeometryRect();\n        if ( options === void 0 ) options = {};\n\n        superclass.call(this, options);\n\n        this.geometry(geometry);\n\n        if (!defined(this.options.stroke)) {\n            this.stroke(\"#000\");\n        }\n    }\n\n    if ( superclass ) Rect.__proto__ = superclass;\n    Rect.prototype = Object.create( superclass && superclass.prototype );\n    Rect.prototype.constructor = Rect;\n\n    var prototypeAccessors = { nodeType: { configurable: true } };\n\n    prototypeAccessors.nodeType.get = function () {\n        return \"Rect\";\n    };\n\n    Rect.prototype._bbox = function _bbox (matrix) {\n        return this._geometry.bbox(matrix);\n    };\n\n    Rect.prototype.rawBBox = function rawBBox () {\n        return this._geometry.bbox();\n    };\n\n    Rect.prototype._containsPoint = function _containsPoint (point) {\n        return this._geometry.containsPoint(point);\n    };\n\n    Rect.prototype._isOnPath = function _isOnPath (point) {\n        return this.geometry()._isOnPath(point, this.options.stroke.width / 2);\n    };\n\n    Object.defineProperties( Rect.prototype, prototypeAccessors );\n\n    return Rect;\n}(paintable(measurable(withGeometry(Element)))));\n\nexport default Rect;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,yBAAyB;AAClD,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,UAAU,MAAM,sBAAsB;AAC7C,OAAOC,YAAY,MAAM,kBAAkB;AAC3C,SAASC,OAAO,QAAQ,SAAS;AAGjC,IAAIC,IAAI,GAAI,UAAUC,UAAU,EAAE;EAC9B,SAASD,IAAIA,CAACE,QAAQ,EAAEC,OAAO,EAAE;IAC7B,IAAKD,QAAQ,KAAK,KAAK,CAAC,EAAGA,QAAQ,GAAG,IAAIJ,YAAY,CAAC,CAAC;IACxD,IAAKK,OAAO,KAAK,KAAK,CAAC,EAAGA,OAAO,GAAG,CAAC,CAAC;IAEtCF,UAAU,CAACG,IAAI,CAAC,IAAI,EAAED,OAAO,CAAC;IAE9B,IAAI,CAACD,QAAQ,CAACA,QAAQ,CAAC;IAEvB,IAAI,CAACH,OAAO,CAAC,IAAI,CAACI,OAAO,CAACE,MAAM,CAAC,EAAE;MAC/B,IAAI,CAACA,MAAM,CAAC,MAAM,CAAC;IACvB;EACJ;EAEA,IAAKJ,UAAU,EAAGD,IAAI,CAACM,SAAS,GAAGL,UAAU;EAC7CD,IAAI,CAACO,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAER,UAAU,IAAIA,UAAU,CAACM,SAAU,CAAC;EACpEP,IAAI,CAACO,SAAS,CAACG,WAAW,GAAGV,IAAI;EAEjC,IAAIW,kBAAkB,GAAG;IAAEC,QAAQ,EAAE;MAAEC,YAAY,EAAE;IAAK;EAAE,CAAC;EAE7DF,kBAAkB,CAACC,QAAQ,CAACE,GAAG,GAAG,YAAY;IAC1C,OAAO,MAAM;EACjB,CAAC;EAEDd,IAAI,CAACO,SAAS,CAACQ,KAAK,GAAG,SAASA,KAAKA,CAAEC,MAAM,EAAE;IAC3C,OAAO,IAAI,CAACC,SAAS,CAACC,IAAI,CAACF,MAAM,CAAC;EACtC,CAAC;EAEDhB,IAAI,CAACO,SAAS,CAACY,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;IACzC,OAAO,IAAI,CAACF,SAAS,CAACC,IAAI,CAAC,CAAC;EAChC,CAAC;EAEDlB,IAAI,CAACO,SAAS,CAACa,cAAc,GAAG,SAASA,cAAcA,CAAEC,KAAK,EAAE;IAC5D,OAAO,IAAI,CAACJ,SAAS,CAACK,aAAa,CAACD,KAAK,CAAC;EAC9C,CAAC;EAEDrB,IAAI,CAACO,SAAS,CAACgB,SAAS,GAAG,SAASA,SAASA,CAAEF,KAAK,EAAE;IAClD,OAAO,IAAI,CAACnB,QAAQ,CAAC,CAAC,CAACqB,SAAS,CAACF,KAAK,EAAE,IAAI,CAAClB,OAAO,CAACE,MAAM,CAACmB,KAAK,GAAG,CAAC,CAAC;EAC1E,CAAC;EAEDhB,MAAM,CAACiB,gBAAgB,CAAEzB,IAAI,CAACO,SAAS,EAAEI,kBAAmB,CAAC;EAE7D,OAAOX,IAAI;AACf,CAAC,CAACJ,SAAS,CAACC,UAAU,CAACH,YAAY,CAACC,OAAO,CAAC,CAAC,CAAC,CAAE;AAEhD,eAAeK,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}