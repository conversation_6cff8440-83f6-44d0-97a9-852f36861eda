{"ast": null, "code": "import { setMonth } from './set-month';\n/**\n * A function that returns a `Date` object of the first month in a year.\n *\n * @param date - The start date value.\n * @returns - The first month in a year.\n *\n * @example\n * ```ts-no-run\n * firstMonthOfYear(new Date(2017, 11, 1)); // 2017-1-1\n * firstMonthOfYear(new Date(2017, 0, 1)); // 2017-1-1\n * ```\n */\nexport var firstMonthOfYear = function (value) {\n  return setMonth(value, 0);\n};", "map": {"version": 3, "names": ["setMonth", "firstMonthOfYear", "value"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/first-month-of-year.js"], "sourcesContent": ["import { setMonth } from './set-month';\n/**\n * A function that returns a `Date` object of the first month in a year.\n *\n * @param date - The start date value.\n * @returns - The first month in a year.\n *\n * @example\n * ```ts-no-run\n * firstMonthOfYear(new Date(2017, 11, 1)); // 2017-1-1\n * firstMonthOfYear(new Date(2017, 0, 1)); // 2017-1-1\n * ```\n */\nexport var firstMonthOfYear = function (value) { return setMonth(value, 0); };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,aAAa;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,gBAAgB,GAAG,SAAAA,CAAUC,KAAK,EAAE;EAAE,OAAOF,QAAQ,CAACE,KAAK,EAAE,CAAC,CAAC;AAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}