{"ast": null, "code": "/* eslint-disable */\nimport { Parser } from './parsers';\nimport { Result } from './result';\n/**\n * @hidden\n */\nvar always = function (value) {\n  return new Parser(function (stream) {\n    return new Result(value, stream);\n  });\n};\n/**\n * @hidden\n */\nvar append = function (p1, p2) {\n  return p1.chain(function (vs) {\n    return p2.map(function (v) {\n      return vs.concat([v]);\n    });\n  });\n};\n/**\n * @hidden\n */\nexport var sequence = function (list) {\n  return list.reduce(function (acc, parser) {\n    return append(acc, parser);\n  }, always([]));\n};\n/**\n * @hidden\n */\nexport var greedy = function (parser) {\n  return new Parser(function (stream) {\n    var result = new Result([], stream);\n    while (!stream.eof()) {\n      result = result.concat(parser.run(stream));\n    }\n    return result;\n  });\n};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "Result", "always", "value", "stream", "append", "p1", "p2", "chain", "vs", "map", "v", "concat", "sequence", "list", "reduce", "acc", "parser", "greedy", "result", "eof", "run"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-inputs-common/dist/es/maskedtextbox/parsing/combinators.js"], "sourcesContent": ["/* eslint-disable */\nimport { Parser } from './parsers';\nimport { Result } from './result';\n/**\n * @hidden\n */\nvar always = function (value) { return new Parser(function (stream) { return new Result(value, stream); }); };\n/**\n * @hidden\n */\nvar append = function (p1, p2) { return p1.chain(function (vs) { return p2.map(function (v) { return vs.concat([v]); }); }); };\n/**\n * @hidden\n */\nexport var sequence = function (list) { return list.reduce(function (acc, parser) { return append(acc, parser); }, always([])); };\n/**\n * @hidden\n */\nexport var greedy = function (parser) { return new Parser(function (stream) {\n    var result = new Result([], stream);\n    while (!stream.eof()) {\n        result = result.concat(parser.run(stream));\n    }\n    return result;\n}); };\n"], "mappings": "AAAA;AACA,SAASA,MAAM,QAAQ,WAAW;AAClC,SAASC,MAAM,QAAQ,UAAU;AACjC;AACA;AACA;AACA,IAAIC,MAAM,GAAG,SAAAA,CAAUC,KAAK,EAAE;EAAE,OAAO,IAAIH,MAAM,CAAC,UAAUI,MAAM,EAAE;IAAE,OAAO,IAAIH,MAAM,CAACE,KAAK,EAAEC,MAAM,CAAC;EAAE,CAAC,CAAC;AAAE,CAAC;AAC7G;AACA;AACA;AACA,IAAIC,MAAM,GAAG,SAAAA,CAAUC,EAAE,EAAEC,EAAE,EAAE;EAAE,OAAOD,EAAE,CAACE,KAAK,CAAC,UAAUC,EAAE,EAAE;IAAE,OAAOF,EAAE,CAACG,GAAG,CAAC,UAAUC,CAAC,EAAE;MAAE,OAAOF,EAAE,CAACG,MAAM,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,CAAC;AAAE,CAAC;AAC9H;AACA;AACA;AACA,OAAO,IAAIE,QAAQ,GAAG,SAAAA,CAAUC,IAAI,EAAE;EAAE,OAAOA,IAAI,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAEC,MAAM,EAAE;IAAE,OAAOZ,MAAM,CAACW,GAAG,EAAEC,MAAM,CAAC;EAAE,CAAC,EAAEf,MAAM,CAAC,EAAE,CAAC,CAAC;AAAE,CAAC;AACjI;AACA;AACA;AACA,OAAO,IAAIgB,MAAM,GAAG,SAAAA,CAAUD,MAAM,EAAE;EAAE,OAAO,IAAIjB,MAAM,CAAC,UAAUI,MAAM,EAAE;IACxE,IAAIe,MAAM,GAAG,IAAIlB,MAAM,CAAC,EAAE,EAAEG,MAAM,CAAC;IACnC,OAAO,CAACA,MAAM,CAACgB,GAAG,CAAC,CAAC,EAAE;MAClBD,MAAM,GAAGA,MAAM,CAACP,MAAM,CAACK,MAAM,CAACI,GAAG,CAACjB,MAAM,CAAC,CAAC;IAC9C;IACA,OAAOe,MAAM;EACjB,CAAC,CAAC;AAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}