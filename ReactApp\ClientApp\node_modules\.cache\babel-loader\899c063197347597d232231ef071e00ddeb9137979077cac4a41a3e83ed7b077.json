{"ast": null, "code": "import { cldr, getLocaleInfo } from './info';\nimport localeTerritory from './territory';\nimport { DAYS_OF_WEEK, DEFAULT_TERRITORY } from './constants';\nimport { errors } from '../errors';\nvar NoWeekData = errors.NoWeekData;\nexport default function weekendRange(locale) {\n  var info = getLocaleInfo(locale);\n  if (info.weekendRange) {\n    return info.weekendRange;\n  }\n  var weekData = cldr.supplemental.weekData;\n  if (!weekData) {\n    throw NoWeekData.error();\n  }\n  var territory = localeTerritory(info);\n  var start = weekData.weekendStart[territory] || weekData.weekendStart[DEFAULT_TERRITORY];\n  var end = weekData.weekendEnd[territory] || weekData.weekendEnd[DEFAULT_TERRITORY];\n  info.weekendRange = {\n    start: DAYS_OF_WEEK.indexOf(start),\n    end: DAYS_OF_WEEK.indexOf(end)\n  };\n  return info.weekendRange;\n}", "map": {"version": 3, "names": ["cldr", "getLocaleInfo", "localeTerritory", "DAYS_OF_WEEK", "DEFAULT_TERRITORY", "errors", "NoWeekData", "weekendRange", "locale", "info", "weekData", "supplemental", "error", "territory", "start", "weekendStart", "end", "weekendEnd", "indexOf"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-intl/dist/es/cldr/weekend-range.js"], "sourcesContent": ["import { cldr, getLocaleInfo } from './info';\nimport localeTerritory from './territory';\n\nimport { DAYS_OF_WEEK, DEFAULT_TERRITORY } from './constants';\nimport { errors } from '../errors';\n\nvar NoWeekData = errors.NoWeekData;\n\nexport default function weekendRange(locale) {\n    var info = getLocaleInfo(locale);\n\n    if (info.weekendRange) {\n        return info.weekendRange;\n    }\n\n    var weekData = cldr.supplemental.weekData;\n    if (!weekData) {\n        throw NoWeekData.error();\n    }\n\n    var territory = localeTerritory(info);\n    var start = weekData.weekendStart[territory] || weekData.weekendStart[DEFAULT_TERRITORY];\n    var end = weekData.weekendEnd[territory] || weekData.weekendEnd[DEFAULT_TERRITORY];\n\n    info.weekendRange = {\n        start: DAYS_OF_WEEK.indexOf(start),\n        end: DAYS_OF_WEEK.indexOf(end)\n    };\n\n    return info.weekendRange;\n}\n"], "mappings": "AAAA,SAASA,IAAI,EAAEC,aAAa,QAAQ,QAAQ;AAC5C,OAAOC,eAAe,MAAM,aAAa;AAEzC,SAASC,YAAY,EAAEC,iBAAiB,QAAQ,aAAa;AAC7D,SAASC,MAAM,QAAQ,WAAW;AAElC,IAAIC,UAAU,GAAGD,MAAM,CAACC,UAAU;AAElC,eAAe,SAASC,YAAYA,CAACC,MAAM,EAAE;EACzC,IAAIC,IAAI,GAAGR,aAAa,CAACO,MAAM,CAAC;EAEhC,IAAIC,IAAI,CAACF,YAAY,EAAE;IACnB,OAAOE,IAAI,CAACF,YAAY;EAC5B;EAEA,IAAIG,QAAQ,GAAGV,IAAI,CAACW,YAAY,CAACD,QAAQ;EACzC,IAAI,CAACA,QAAQ,EAAE;IACX,MAAMJ,UAAU,CAACM,KAAK,CAAC,CAAC;EAC5B;EAEA,IAAIC,SAAS,GAAGX,eAAe,CAACO,IAAI,CAAC;EACrC,IAAIK,KAAK,GAAGJ,QAAQ,CAACK,YAAY,CAACF,SAAS,CAAC,IAAIH,QAAQ,CAACK,YAAY,CAACX,iBAAiB,CAAC;EACxF,IAAIY,GAAG,GAAGN,QAAQ,CAACO,UAAU,CAACJ,SAAS,CAAC,IAAIH,QAAQ,CAACO,UAAU,CAACb,iBAAiB,CAAC;EAElFK,IAAI,CAACF,YAAY,GAAG;IAChBO,KAAK,EAAEX,YAAY,CAACe,OAAO,CAACJ,KAAK,CAAC;IAClCE,GAAG,EAAEb,YAAY,CAACe,OAAO,CAACF,GAAG;EACjC,CAAC;EAED,OAAOP,IAAI,CAACF,YAAY;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}