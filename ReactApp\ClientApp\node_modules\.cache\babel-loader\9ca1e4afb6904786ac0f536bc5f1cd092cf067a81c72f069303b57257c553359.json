{"ast": null, "code": "var Stack = require('./_Stack'),\n  equalArrays = require('./_equalArrays'),\n  equalByTag = require('./_equalByTag'),\n  equalObjects = require('./_equalObjects'),\n  getTag = require('./_getTag'),\n  isArray = require('./isArray'),\n  isBuffer = require('./isBuffer'),\n  isTypedArray = require('./isTypedArray');\n\n/** Used to compose bitmasks for value comparisons. */\nvar COMPARE_PARTIAL_FLAG = 1;\n\n/** `Object#toString` result references. */\nvar argsTag = '[object Arguments]',\n  arrayTag = '[object Array]',\n  objectTag = '[object Object]';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * A specialized version of `baseIsEqual` for arrays and objects which performs\n * deep comparisons and tracks traversed objects enabling objects with circular\n * references to be compared.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} [stack] Tracks traversed `object` and `other` objects.\n * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.\n */\nfunction baseIsEqualDeep(object, other, bitmask, customizer, equalFunc, stack) {\n  var objIsArr = isArray(object),\n    othIsArr = isArray(other),\n    objTag = objIsArr ? arrayTag : getTag(object),\n    othTag = othIsArr ? arrayTag : getTag(other);\n  objTag = objTag == argsTag ? objectTag : objTag;\n  othTag = othTag == argsTag ? objectTag : othTag;\n  var objIsObj = objTag == objectTag,\n    othIsObj = othTag == objectTag,\n    isSameTag = objTag == othTag;\n  if (isSameTag && isBuffer(object)) {\n    if (!isBuffer(other)) {\n      return false;\n    }\n    objIsArr = true;\n    objIsObj = false;\n  }\n  if (isSameTag && !objIsObj) {\n    stack || (stack = new Stack());\n    return objIsArr || isTypedArray(object) ? equalArrays(object, other, bitmask, customizer, equalFunc, stack) : equalByTag(object, other, objTag, bitmask, customizer, equalFunc, stack);\n  }\n  if (!(bitmask & COMPARE_PARTIAL_FLAG)) {\n    var objIsWrapped = objIsObj && hasOwnProperty.call(object, '__wrapped__'),\n      othIsWrapped = othIsObj && hasOwnProperty.call(other, '__wrapped__');\n    if (objIsWrapped || othIsWrapped) {\n      var objUnwrapped = objIsWrapped ? object.value() : object,\n        othUnwrapped = othIsWrapped ? other.value() : other;\n      stack || (stack = new Stack());\n      return equalFunc(objUnwrapped, othUnwrapped, bitmask, customizer, stack);\n    }\n  }\n  if (!isSameTag) {\n    return false;\n  }\n  stack || (stack = new Stack());\n  return equalObjects(object, other, bitmask, customizer, equalFunc, stack);\n}\nmodule.exports = baseIsEqualDeep;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "require", "equalArrays", "equalByTag", "equalObjects", "getTag", "isArray", "<PERSON><PERSON><PERSON><PERSON>", "isTypedArray", "COMPARE_PARTIAL_FLAG", "argsTag", "arrayTag", "objectTag", "objectProto", "Object", "prototype", "hasOwnProperty", "baseIsEqualDeep", "object", "other", "bitmask", "customizer", "equalFunc", "stack", "objIsArr", "othIsArr", "objTag", "othTag", "objIsObj", "othIsObj", "isSameTag", "objIsWrapped", "call", "othIsWrapped", "objUnwrapped", "value", "othUnwrapped", "module", "exports"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/lodash/_baseIsEqualDeep.js"], "sourcesContent": ["var Stack = require('./_Stack'),\n    equalArrays = require('./_equalArrays'),\n    equalByTag = require('./_equalByTag'),\n    equalObjects = require('./_equalObjects'),\n    getTag = require('./_getTag'),\n    isArray = require('./isArray'),\n    isBuffer = require('./isBuffer'),\n    isTypedArray = require('./isTypedArray');\n\n/** Used to compose bitmasks for value comparisons. */\nvar COMPARE_PARTIAL_FLAG = 1;\n\n/** `Object#toString` result references. */\nvar argsTag = '[object Arguments]',\n    arrayTag = '[object Array]',\n    objectTag = '[object Object]';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * A specialized version of `baseIsEqual` for arrays and objects which performs\n * deep comparisons and tracks traversed objects enabling objects with circular\n * references to be compared.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} [stack] Tracks traversed `object` and `other` objects.\n * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.\n */\nfunction baseIsEqualDeep(object, other, bitmask, customizer, equalFunc, stack) {\n  var objIsArr = isArray(object),\n      othIsArr = isArray(other),\n      objTag = objIsArr ? arrayTag : getTag(object),\n      othTag = othIsArr ? arrayTag : getTag(other);\n\n  objTag = objTag == argsTag ? objectTag : objTag;\n  othTag = othTag == argsTag ? objectTag : othTag;\n\n  var objIsObj = objTag == objectTag,\n      othIsObj = othTag == objectTag,\n      isSameTag = objTag == othTag;\n\n  if (isSameTag && isBuffer(object)) {\n    if (!isBuffer(other)) {\n      return false;\n    }\n    objIsArr = true;\n    objIsObj = false;\n  }\n  if (isSameTag && !objIsObj) {\n    stack || (stack = new Stack);\n    return (objIsArr || isTypedArray(object))\n      ? equalArrays(object, other, bitmask, customizer, equalFunc, stack)\n      : equalByTag(object, other, objTag, bitmask, customizer, equalFunc, stack);\n  }\n  if (!(bitmask & COMPARE_PARTIAL_FLAG)) {\n    var objIsWrapped = objIsObj && hasOwnProperty.call(object, '__wrapped__'),\n        othIsWrapped = othIsObj && hasOwnProperty.call(other, '__wrapped__');\n\n    if (objIsWrapped || othIsWrapped) {\n      var objUnwrapped = objIsWrapped ? object.value() : object,\n          othUnwrapped = othIsWrapped ? other.value() : other;\n\n      stack || (stack = new Stack);\n      return equalFunc(objUnwrapped, othUnwrapped, bitmask, customizer, stack);\n    }\n  }\n  if (!isSameTag) {\n    return false;\n  }\n  stack || (stack = new Stack);\n  return equalObjects(object, other, bitmask, customizer, equalFunc, stack);\n}\n\nmodule.exports = baseIsEqualDeep;\n"], "mappings": "AAAA,IAAIA,KAAK,GAAGC,OAAO,CAAC,UAAU,CAAC;EAC3BC,WAAW,GAAGD,OAAO,CAAC,gBAAgB,CAAC;EACvCE,UAAU,GAAGF,OAAO,CAAC,eAAe,CAAC;EACrCG,YAAY,GAAGH,OAAO,CAAC,iBAAiB,CAAC;EACzCI,MAAM,GAAGJ,OAAO,CAAC,WAAW,CAAC;EAC7BK,OAAO,GAAGL,OAAO,CAAC,WAAW,CAAC;EAC9BM,QAAQ,GAAGN,OAAO,CAAC,YAAY,CAAC;EAChCO,YAAY,GAAGP,OAAO,CAAC,gBAAgB,CAAC;;AAE5C;AACA,IAAIQ,oBAAoB,GAAG,CAAC;;AAE5B;AACA,IAAIC,OAAO,GAAG,oBAAoB;EAC9BC,QAAQ,GAAG,gBAAgB;EAC3BC,SAAS,GAAG,iBAAiB;;AAEjC;AACA,IAAIC,WAAW,GAAGC,MAAM,CAACC,SAAS;;AAElC;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACG,cAAc;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAE;EAC7E,IAAIC,QAAQ,GAAGlB,OAAO,CAACY,MAAM,CAAC;IAC1BO,QAAQ,GAAGnB,OAAO,CAACa,KAAK,CAAC;IACzBO,MAAM,GAAGF,QAAQ,GAAGb,QAAQ,GAAGN,MAAM,CAACa,MAAM,CAAC;IAC7CS,MAAM,GAAGF,QAAQ,GAAGd,QAAQ,GAAGN,MAAM,CAACc,KAAK,CAAC;EAEhDO,MAAM,GAAGA,MAAM,IAAIhB,OAAO,GAAGE,SAAS,GAAGc,MAAM;EAC/CC,MAAM,GAAGA,MAAM,IAAIjB,OAAO,GAAGE,SAAS,GAAGe,MAAM;EAE/C,IAAIC,QAAQ,GAAGF,MAAM,IAAId,SAAS;IAC9BiB,QAAQ,GAAGF,MAAM,IAAIf,SAAS;IAC9BkB,SAAS,GAAGJ,MAAM,IAAIC,MAAM;EAEhC,IAAIG,SAAS,IAAIvB,QAAQ,CAACW,MAAM,CAAC,EAAE;IACjC,IAAI,CAACX,QAAQ,CAACY,KAAK,CAAC,EAAE;MACpB,OAAO,KAAK;IACd;IACAK,QAAQ,GAAG,IAAI;IACfI,QAAQ,GAAG,KAAK;EAClB;EACA,IAAIE,SAAS,IAAI,CAACF,QAAQ,EAAE;IAC1BL,KAAK,KAAKA,KAAK,GAAG,IAAIvB,KAAK,CAAD,CAAC,CAAC;IAC5B,OAAQwB,QAAQ,IAAIhB,YAAY,CAACU,MAAM,CAAC,GACpChB,WAAW,CAACgB,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,CAAC,GACjEpB,UAAU,CAACe,MAAM,EAAEC,KAAK,EAAEO,MAAM,EAAEN,OAAO,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,CAAC;EAC9E;EACA,IAAI,EAAEH,OAAO,GAAGX,oBAAoB,CAAC,EAAE;IACrC,IAAIsB,YAAY,GAAGH,QAAQ,IAAIZ,cAAc,CAACgB,IAAI,CAACd,MAAM,EAAE,aAAa,CAAC;MACrEe,YAAY,GAAGJ,QAAQ,IAAIb,cAAc,CAACgB,IAAI,CAACb,KAAK,EAAE,aAAa,CAAC;IAExE,IAAIY,YAAY,IAAIE,YAAY,EAAE;MAChC,IAAIC,YAAY,GAAGH,YAAY,GAAGb,MAAM,CAACiB,KAAK,CAAC,CAAC,GAAGjB,MAAM;QACrDkB,YAAY,GAAGH,YAAY,GAAGd,KAAK,CAACgB,KAAK,CAAC,CAAC,GAAGhB,KAAK;MAEvDI,KAAK,KAAKA,KAAK,GAAG,IAAIvB,KAAK,CAAD,CAAC,CAAC;MAC5B,OAAOsB,SAAS,CAACY,YAAY,EAAEE,YAAY,EAAEhB,OAAO,EAAEC,UAAU,EAAEE,KAAK,CAAC;IAC1E;EACF;EACA,IAAI,CAACO,SAAS,EAAE;IACd,OAAO,KAAK;EACd;EACAP,KAAK,KAAKA,KAAK,GAAG,IAAIvB,KAAK,CAAD,CAAC,CAAC;EAC5B,OAAOI,YAAY,CAACc,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,CAAC;AAC3E;AAEAc,MAAM,CAACC,OAAO,GAAGrB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}