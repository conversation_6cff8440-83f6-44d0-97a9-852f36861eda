{"ast": null, "code": "import { localeInfo, localeCurrency, currencyDisplays } from '../cldr';\nimport { PERCENT, NUMBER_PLACEHOLDER, CURRENCY_PLACEHOLDER, DEFAULT_LOCALE, EMPTY, POINT } from '../common/constants';\nimport { setStyleOptions, setFormatLiterals } from './utils';\nimport isNumber from '../common/is-number';\nimport isCurrencyStyle from './is-currency-style';\nimport formatOptions from './format-options';\nimport isString from '../common/is-string';\nvar exponentRegExp = /[eE][-+]?[0-9]+/;\nvar nonBreakingSpaceRegExp = /\\u00A0/g;\nfunction cleanNegativePattern(number, patterns) {\n  if (patterns.length > 1) {\n    var parts = (patterns[1] || EMPTY).replace(CURRENCY_PLACEHOLDER, EMPTY).split(NUMBER_PLACEHOLDER);\n    if (number.indexOf(parts[0]) > -1 && number.indexOf(parts[1]) > -1) {\n      return number.replace(parts[0], EMPTY).replace(parts[1], EMPTY);\n    }\n  }\n}\nfunction cleanCurrencyNumber(value, info, format) {\n  var options = formatOptions(format) || {};\n  var isCurrency = isCurrencyStyle(options.style);\n  var number = value;\n  var negative;\n  var currency = options.currency || localeCurrency(info, isCurrency);\n  if (currency) {\n    var displays = currencyDisplays(info, currency, isCurrency);\n    if (displays) {\n      for (var idx = 0; idx < displays.length; idx++) {\n        var display = displays[idx];\n        if (number.includes(display)) {\n          number = number.replace(display, EMPTY);\n          isCurrency = true;\n          break;\n        }\n      }\n    }\n    if (isCurrency) {\n      var cleanNumber = cleanNegativePattern(number, info.numbers.currency.patterns) || cleanNegativePattern(number, info.numbers.accounting.patterns);\n      if (cleanNumber) {\n        negative = true;\n        number = cleanNumber;\n      }\n    }\n  }\n  return {\n    number: number,\n    negative: negative\n  };\n}\nfunction cleanLiterals(number, formatOptions) {\n  var literals = formatOptions.literals;\n  var result = number;\n  if (literals) {\n    for (var idx = 0; idx < literals.length; idx++) {\n      result = result.replace(literals[idx], EMPTY);\n    }\n  }\n  return result;\n}\nfunction divideBy100(number) {\n  var strNumber = String(number);\n  var pointIndex = strNumber.indexOf(POINT);\n  var zeroesCount = 2;\n  var result = number / Math.pow(10, zeroesCount);\n  if (pointIndex === -1 || String(result).length <= strNumber.length + zeroesCount) {\n    return result;\n  }\n  var fractionDigits = strNumber.length - pointIndex + 1 + zeroesCount;\n  return parseFloat(result.toFixed(fractionDigits));\n}\nexport default function parseNumber(value, locale, format) {\n  if (locale === void 0) locale = DEFAULT_LOCALE;\n  if (format === void 0) format = {};\n  if (!value && value !== 0) {\n    return null;\n  }\n  if (isNumber(value)) {\n    return value;\n  }\n  var info = localeInfo(locale);\n  var symbols = info.numbers.symbols;\n  var number = value.toString();\n  var formatOptions = format || {};\n  var isPercent;\n  if (isString(format)) {\n    formatOptions = {\n      format: format\n    };\n    setFormatLiterals(formatOptions);\n    number = cleanLiterals(number, formatOptions);\n    setStyleOptions(formatOptions, info);\n  }\n  if (formatOptions.style === PERCENT || number.indexOf(symbols.percentSign) > -1) {\n    number = number.replace(symbols.percentSign, EMPTY);\n    isPercent = true;\n  }\n  if (exponentRegExp.test(number)) {\n    number = parseFloat(number.replace(symbols.decimal, POINT));\n    return isNaN(number) ? null : number;\n  }\n  var ref = cleanCurrencyNumber(number, info, formatOptions);\n  var negativeCurrency = ref.negative;\n  var currencyNumber = ref.number;\n  number = String(currencyNumber).trim();\n  var negativeSignIndex = number.indexOf(\"-\");\n  if (negativeSignIndex > 0) {\n    return null;\n  }\n  var isNegative = negativeSignIndex > -1;\n  isNegative = negativeCurrency !== undefined ? negativeCurrency : isNegative;\n  number = number.replace(\"-\", EMPTY).replace(nonBreakingSpaceRegExp, \" \").split(symbols.group.replace(nonBreakingSpaceRegExp, \" \")).join(EMPTY).replace(symbols.decimal, POINT);\n  number = parseFloat(number);\n  if (isNaN(number)) {\n    number = null;\n  } else if (isNegative) {\n    number *= -1;\n  }\n  if (number && isPercent) {\n    number = divideBy100(number);\n  }\n  return number;\n}", "map": {"version": 3, "names": ["localeInfo", "localeCurrency", "currencyDisplays", "PERCENT", "NUMBER_PLACEHOLDER", "CURRENCY_PLACEHOLDER", "DEFAULT_LOCALE", "EMPTY", "POINT", "setStyleOptions", "setFormatLiterals", "isNumber", "isCurrencyStyle", "formatOptions", "isString", "exponentRegExp", "nonBreakingSpaceRegExp", "cleanNegativePattern", "number", "patterns", "length", "parts", "replace", "split", "indexOf", "cleanCurrencyNumber", "value", "info", "format", "options", "isCurrency", "style", "negative", "currency", "displays", "idx", "display", "includes", "cleanNumber", "numbers", "accounting", "cleanLiterals", "literals", "result", "divideBy100", "strNumber", "String", "pointIndex", "zeroesCount", "Math", "pow", "fractionDigits", "parseFloat", "toFixed", "parseNumber", "locale", "symbols", "toString", "isPercent", "percentSign", "test", "decimal", "isNaN", "ref", "negativeCurrency", "currencyNumber", "trim", "negativeSignIndex", "isNegative", "undefined", "group", "join"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-intl/dist/es/numbers/parse-number.js"], "sourcesContent": ["import { localeInfo, localeCurrency, currencyDisplays } from '../cldr';\nimport { PERCENT, NUMBER_PLACEHOLDER, CURRENCY_PLACEHOLDER, DEFAULT_LOCALE, EMPTY, POINT } from '../common/constants';\nimport { setStyleOptions, setFormatLiterals } from './utils';\nimport isNumber from '../common/is-number';\nimport isCurrencyStyle from './is-currency-style';\nimport formatOptions from './format-options';\nimport isString from '../common/is-string';\n\nvar exponentRegExp = /[eE][-+]?[0-9]+/;\nvar nonBreakingSpaceRegExp = /\\u00A0/g;\n\nfunction cleanNegativePattern(number, patterns) {\n    if (patterns.length > 1) {\n        var parts = (patterns[1] || EMPTY).replace(CURRENCY_PLACEHOLDER, EMPTY).split(NUMBER_PLACEHOLDER);\n        if (number.indexOf(parts[0]) > -1 && number.indexOf(parts[1]) > -1) {\n            return number.replace(parts[0], EMPTY).replace(parts[1], EMPTY);\n        }\n    }\n}\n\nfunction cleanCurrencyNumber(value, info, format) {\n    var options = formatOptions(format) || {};\n    var isCurrency = isCurrencyStyle(options.style);\n    var number = value;\n    var negative;\n\n    var currency = options.currency || localeCurrency(info, isCurrency);\n\n    if (currency) {\n        var displays = currencyDisplays(info, currency, isCurrency);\n        if (displays) {\n            for (var idx = 0; idx < displays.length; idx++) {\n                var display = displays[idx];\n                if (number.includes(display)) {\n                    number = number.replace(display, EMPTY);\n                    isCurrency = true;\n                    break;\n                }\n            }\n        }\n\n        if (isCurrency) {\n            var cleanNumber = cleanNegativePattern(number, info.numbers.currency.patterns) ||\n                cleanNegativePattern(number, info.numbers.accounting.patterns);\n\n            if (cleanNumber) {\n                negative = true;\n                number = cleanNumber;\n            }\n\n        }\n    }\n\n    return {\n        number: number,\n        negative: negative\n    };\n}\n\nfunction cleanLiterals(number, formatOptions) {\n    var literals = formatOptions.literals;\n    var result = number;\n\n    if (literals) {\n        for (var idx = 0; idx < literals.length; idx++) {\n            result = result.replace(literals[idx], EMPTY);\n        }\n    }\n\n    return result;\n}\n\nfunction divideBy100(number) {\n    var strNumber = String(number);\n    var pointIndex = strNumber.indexOf(POINT);\n    var zeroesCount = 2;\n    var result = number / Math.pow(10, zeroesCount);\n\n    if (pointIndex === -1 || String(result).length <= strNumber.length + zeroesCount) {\n        return result;\n    }\n\n    var fractionDigits = strNumber.length - pointIndex + 1 + zeroesCount;\n    return parseFloat(result.toFixed(fractionDigits));\n}\n\nexport default function parseNumber(value, locale, format) {\n    if ( locale === void 0 ) locale = DEFAULT_LOCALE;\n    if ( format === void 0 ) format = {};\n\n    if (!value && value !== 0) {\n        return null;\n    }\n\n    if (isNumber(value)) {\n        return value;\n    }\n\n    var info = localeInfo(locale);\n    var symbols = info.numbers.symbols;\n\n    var number = value.toString();\n    var formatOptions = format || {};\n    var isPercent;\n\n    if (isString(format)) {\n        formatOptions = { format: format };\n        setFormatLiterals(formatOptions);\n        number = cleanLiterals(number, formatOptions);\n\n        setStyleOptions(formatOptions, info);\n    }\n\n    if (formatOptions.style === PERCENT || number.indexOf(symbols.percentSign) > -1) {\n        number = number.replace(symbols.percentSign, EMPTY);\n        isPercent = true;\n    }\n\n    if (exponentRegExp.test(number)) {\n        number = parseFloat(number.replace(symbols.decimal, POINT));\n        return isNaN(number) ? null : number;\n    }\n\n    var ref = cleanCurrencyNumber(number, info, formatOptions);\n    var negativeCurrency = ref.negative;\n    var currencyNumber = ref.number;\n    number = String(currencyNumber).trim();\n\n    var negativeSignIndex = number.indexOf(\"-\");\n    if (negativeSignIndex > 0) {\n        return null;\n    }\n\n    var isNegative = negativeSignIndex > -1;\n\n    isNegative = negativeCurrency !== undefined ? negativeCurrency : isNegative;\n\n    number = number.replace(\"-\", EMPTY)\n          .replace(nonBreakingSpaceRegExp, \" \")\n          .split(symbols.group.replace(nonBreakingSpaceRegExp, \" \")).join(EMPTY)\n          .replace(symbols.decimal, POINT);\n\n    number = parseFloat(number);\n\n    if (isNaN(number)) {\n        number = null;\n    } else if (isNegative) {\n        number *= -1;\n    }\n\n    if (number && isPercent) {\n        number = divideBy100(number);\n    }\n\n    return number;\n}\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,cAAc,EAAEC,gBAAgB,QAAQ,SAAS;AACtE,SAASC,OAAO,EAAEC,kBAAkB,EAAEC,oBAAoB,EAAEC,cAAc,EAAEC,KAAK,EAAEC,KAAK,QAAQ,qBAAqB;AACrH,SAASC,eAAe,EAAEC,iBAAiB,QAAQ,SAAS;AAC5D,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C,OAAOC,eAAe,MAAM,qBAAqB;AACjD,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,QAAQ,MAAM,qBAAqB;AAE1C,IAAIC,cAAc,GAAG,iBAAiB;AACtC,IAAIC,sBAAsB,GAAG,SAAS;AAEtC,SAASC,oBAAoBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAC5C,IAAIA,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;IACrB,IAAIC,KAAK,GAAG,CAACF,QAAQ,CAAC,CAAC,CAAC,IAAIZ,KAAK,EAAEe,OAAO,CAACjB,oBAAoB,EAAEE,KAAK,CAAC,CAACgB,KAAK,CAACnB,kBAAkB,CAAC;IACjG,IAAIc,MAAM,CAACM,OAAO,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAIH,MAAM,CAACM,OAAO,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;MAChE,OAAOH,MAAM,CAACI,OAAO,CAACD,KAAK,CAAC,CAAC,CAAC,EAAEd,KAAK,CAAC,CAACe,OAAO,CAACD,KAAK,CAAC,CAAC,CAAC,EAAEd,KAAK,CAAC;IACnE;EACJ;AACJ;AAEA,SAASkB,mBAAmBA,CAACC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAE;EAC9C,IAAIC,OAAO,GAAGhB,aAAa,CAACe,MAAM,CAAC,IAAI,CAAC,CAAC;EACzC,IAAIE,UAAU,GAAGlB,eAAe,CAACiB,OAAO,CAACE,KAAK,CAAC;EAC/C,IAAIb,MAAM,GAAGQ,KAAK;EAClB,IAAIM,QAAQ;EAEZ,IAAIC,QAAQ,GAAGJ,OAAO,CAACI,QAAQ,IAAIhC,cAAc,CAAC0B,IAAI,EAAEG,UAAU,CAAC;EAEnE,IAAIG,QAAQ,EAAE;IACV,IAAIC,QAAQ,GAAGhC,gBAAgB,CAACyB,IAAI,EAAEM,QAAQ,EAAEH,UAAU,CAAC;IAC3D,IAAII,QAAQ,EAAE;MACV,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGD,QAAQ,CAACd,MAAM,EAAEe,GAAG,EAAE,EAAE;QAC5C,IAAIC,OAAO,GAAGF,QAAQ,CAACC,GAAG,CAAC;QAC3B,IAAIjB,MAAM,CAACmB,QAAQ,CAACD,OAAO,CAAC,EAAE;UAC1BlB,MAAM,GAAGA,MAAM,CAACI,OAAO,CAACc,OAAO,EAAE7B,KAAK,CAAC;UACvCuB,UAAU,GAAG,IAAI;UACjB;QACJ;MACJ;IACJ;IAEA,IAAIA,UAAU,EAAE;MACZ,IAAIQ,WAAW,GAAGrB,oBAAoB,CAACC,MAAM,EAAES,IAAI,CAACY,OAAO,CAACN,QAAQ,CAACd,QAAQ,CAAC,IAC1EF,oBAAoB,CAACC,MAAM,EAAES,IAAI,CAACY,OAAO,CAACC,UAAU,CAACrB,QAAQ,CAAC;MAElE,IAAImB,WAAW,EAAE;QACbN,QAAQ,GAAG,IAAI;QACfd,MAAM,GAAGoB,WAAW;MACxB;IAEJ;EACJ;EAEA,OAAO;IACHpB,MAAM,EAAEA,MAAM;IACdc,QAAQ,EAAEA;EACd,CAAC;AACL;AAEA,SAASS,aAAaA,CAACvB,MAAM,EAAEL,aAAa,EAAE;EAC1C,IAAI6B,QAAQ,GAAG7B,aAAa,CAAC6B,QAAQ;EACrC,IAAIC,MAAM,GAAGzB,MAAM;EAEnB,IAAIwB,QAAQ,EAAE;IACV,KAAK,IAAIP,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGO,QAAQ,CAACtB,MAAM,EAAEe,GAAG,EAAE,EAAE;MAC5CQ,MAAM,GAAGA,MAAM,CAACrB,OAAO,CAACoB,QAAQ,CAACP,GAAG,CAAC,EAAE5B,KAAK,CAAC;IACjD;EACJ;EAEA,OAAOoC,MAAM;AACjB;AAEA,SAASC,WAAWA,CAAC1B,MAAM,EAAE;EACzB,IAAI2B,SAAS,GAAGC,MAAM,CAAC5B,MAAM,CAAC;EAC9B,IAAI6B,UAAU,GAAGF,SAAS,CAACrB,OAAO,CAAChB,KAAK,CAAC;EACzC,IAAIwC,WAAW,GAAG,CAAC;EACnB,IAAIL,MAAM,GAAGzB,MAAM,GAAG+B,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEF,WAAW,CAAC;EAE/C,IAAID,UAAU,KAAK,CAAC,CAAC,IAAID,MAAM,CAACH,MAAM,CAAC,CAACvB,MAAM,IAAIyB,SAAS,CAACzB,MAAM,GAAG4B,WAAW,EAAE;IAC9E,OAAOL,MAAM;EACjB;EAEA,IAAIQ,cAAc,GAAGN,SAAS,CAACzB,MAAM,GAAG2B,UAAU,GAAG,CAAC,GAAGC,WAAW;EACpE,OAAOI,UAAU,CAACT,MAAM,CAACU,OAAO,CAACF,cAAc,CAAC,CAAC;AACrD;AAEA,eAAe,SAASG,WAAWA,CAAC5B,KAAK,EAAE6B,MAAM,EAAE3B,MAAM,EAAE;EACvD,IAAK2B,MAAM,KAAK,KAAK,CAAC,EAAGA,MAAM,GAAGjD,cAAc;EAChD,IAAKsB,MAAM,KAAK,KAAK,CAAC,EAAGA,MAAM,GAAG,CAAC,CAAC;EAEpC,IAAI,CAACF,KAAK,IAAIA,KAAK,KAAK,CAAC,EAAE;IACvB,OAAO,IAAI;EACf;EAEA,IAAIf,QAAQ,CAACe,KAAK,CAAC,EAAE;IACjB,OAAOA,KAAK;EAChB;EAEA,IAAIC,IAAI,GAAG3B,UAAU,CAACuD,MAAM,CAAC;EAC7B,IAAIC,OAAO,GAAG7B,IAAI,CAACY,OAAO,CAACiB,OAAO;EAElC,IAAItC,MAAM,GAAGQ,KAAK,CAAC+B,QAAQ,CAAC,CAAC;EAC7B,IAAI5C,aAAa,GAAGe,MAAM,IAAI,CAAC,CAAC;EAChC,IAAI8B,SAAS;EAEb,IAAI5C,QAAQ,CAACc,MAAM,CAAC,EAAE;IAClBf,aAAa,GAAG;MAAEe,MAAM,EAAEA;IAAO,CAAC;IAClClB,iBAAiB,CAACG,aAAa,CAAC;IAChCK,MAAM,GAAGuB,aAAa,CAACvB,MAAM,EAAEL,aAAa,CAAC;IAE7CJ,eAAe,CAACI,aAAa,EAAEc,IAAI,CAAC;EACxC;EAEA,IAAId,aAAa,CAACkB,KAAK,KAAK5B,OAAO,IAAIe,MAAM,CAACM,OAAO,CAACgC,OAAO,CAACG,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE;IAC7EzC,MAAM,GAAGA,MAAM,CAACI,OAAO,CAACkC,OAAO,CAACG,WAAW,EAAEpD,KAAK,CAAC;IACnDmD,SAAS,GAAG,IAAI;EACpB;EAEA,IAAI3C,cAAc,CAAC6C,IAAI,CAAC1C,MAAM,CAAC,EAAE;IAC7BA,MAAM,GAAGkC,UAAU,CAAClC,MAAM,CAACI,OAAO,CAACkC,OAAO,CAACK,OAAO,EAAErD,KAAK,CAAC,CAAC;IAC3D,OAAOsD,KAAK,CAAC5C,MAAM,CAAC,GAAG,IAAI,GAAGA,MAAM;EACxC;EAEA,IAAI6C,GAAG,GAAGtC,mBAAmB,CAACP,MAAM,EAAES,IAAI,EAAEd,aAAa,CAAC;EAC1D,IAAImD,gBAAgB,GAAGD,GAAG,CAAC/B,QAAQ;EACnC,IAAIiC,cAAc,GAAGF,GAAG,CAAC7C,MAAM;EAC/BA,MAAM,GAAG4B,MAAM,CAACmB,cAAc,CAAC,CAACC,IAAI,CAAC,CAAC;EAEtC,IAAIC,iBAAiB,GAAGjD,MAAM,CAACM,OAAO,CAAC,GAAG,CAAC;EAC3C,IAAI2C,iBAAiB,GAAG,CAAC,EAAE;IACvB,OAAO,IAAI;EACf;EAEA,IAAIC,UAAU,GAAGD,iBAAiB,GAAG,CAAC,CAAC;EAEvCC,UAAU,GAAGJ,gBAAgB,KAAKK,SAAS,GAAGL,gBAAgB,GAAGI,UAAU;EAE3ElD,MAAM,GAAGA,MAAM,CAACI,OAAO,CAAC,GAAG,EAAEf,KAAK,CAAC,CAC5Be,OAAO,CAACN,sBAAsB,EAAE,GAAG,CAAC,CACpCO,KAAK,CAACiC,OAAO,CAACc,KAAK,CAAChD,OAAO,CAACN,sBAAsB,EAAE,GAAG,CAAC,CAAC,CAACuD,IAAI,CAAChE,KAAK,CAAC,CACrEe,OAAO,CAACkC,OAAO,CAACK,OAAO,EAAErD,KAAK,CAAC;EAEtCU,MAAM,GAAGkC,UAAU,CAAClC,MAAM,CAAC;EAE3B,IAAI4C,KAAK,CAAC5C,MAAM,CAAC,EAAE;IACfA,MAAM,GAAG,IAAI;EACjB,CAAC,MAAM,IAAIkD,UAAU,EAAE;IACnBlD,MAAM,IAAI,CAAC,CAAC;EAChB;EAEA,IAAIA,MAAM,IAAIwC,SAAS,EAAE;IACrBxC,MAAM,GAAG0B,WAAW,CAAC1B,MAAM,CAAC;EAChC;EAEA,OAAOA,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}