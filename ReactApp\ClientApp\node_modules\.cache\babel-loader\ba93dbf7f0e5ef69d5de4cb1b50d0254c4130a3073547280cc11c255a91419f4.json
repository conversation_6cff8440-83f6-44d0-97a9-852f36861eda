{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { animationStyles as s } from \"@progress/kendo-react-common\";\nconst e = t => {\n    if (!t || !t.ownerDocument.defaultView) return 0;\n    const o = t.ownerDocument.defaultView.getComputedStyle(t),\n      n = parseFloat(o.marginTop),\n      r = parseFloat(o.marginBottom);\n    return t.offsetHeight + n + r;\n  },\n  i = t => {\n    if (!t || !t.ownerDocument.defaultView) return 0;\n    const o = t.ownerDocument.defaultView.getComputedStyle(t),\n      n = parseFloat(o.marginLeft),\n      r = parseFloat(o.marginRight);\n    return t.offsetWidth + n + r;\n  },\n  c = {\n    outerHeight: e,\n    outerWidth: i,\n    styles: s\n  };\nexport { c as default };", "map": {"version": 3, "names": ["animationStyles", "s", "e", "t", "ownerDocument", "defaultView", "o", "getComputedStyle", "n", "parseFloat", "marginTop", "r", "marginBottom", "offsetHeight", "i", "marginLeft", "marginRight", "offsetWidth", "c", "outerHeight", "outerWidth", "styles", "default"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-animation/util.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { animationStyles as s } from \"@progress/kendo-react-common\";\nconst e = (t) => {\n  if (!t || !t.ownerDocument.defaultView)\n    return 0;\n  const o = t.ownerDocument.defaultView.getComputedStyle(t), n = parseFloat(o.marginTop), r = parseFloat(o.marginBottom);\n  return t.offsetHeight + n + r;\n}, i = (t) => {\n  if (!t || !t.ownerDocument.defaultView)\n    return 0;\n  const o = t.ownerDocument.defaultView.getComputedStyle(t), n = parseFloat(o.marginLeft), r = parseFloat(o.marginRight);\n  return t.offsetWidth + n + r;\n}, c = {\n  outerHeight: e,\n  outerWidth: i,\n  styles: s\n};\nexport {\n  c as default\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,eAAe,IAAIC,CAAC,QAAQ,8BAA8B;AACnE,MAAMC,CAAC,GAAIC,CAAC,IAAK;IACf,IAAI,CAACA,CAAC,IAAI,CAACA,CAAC,CAACC,aAAa,CAACC,WAAW,EACpC,OAAO,CAAC;IACV,MAAMC,CAAC,GAAGH,CAAC,CAACC,aAAa,CAACC,WAAW,CAACE,gBAAgB,CAACJ,CAAC,CAAC;MAAEK,CAAC,GAAGC,UAAU,CAACH,CAAC,CAACI,SAAS,CAAC;MAAEC,CAAC,GAAGF,UAAU,CAACH,CAAC,CAACM,YAAY,CAAC;IACtH,OAAOT,CAAC,CAACU,YAAY,GAAGL,CAAC,GAAGG,CAAC;EAC/B,CAAC;EAAEG,CAAC,GAAIX,CAAC,IAAK;IACZ,IAAI,CAACA,CAAC,IAAI,CAACA,CAAC,CAACC,aAAa,CAACC,WAAW,EACpC,OAAO,CAAC;IACV,MAAMC,CAAC,GAAGH,CAAC,CAACC,aAAa,CAACC,WAAW,CAACE,gBAAgB,CAACJ,CAAC,CAAC;MAAEK,CAAC,GAAGC,UAAU,CAACH,CAAC,CAACS,UAAU,CAAC;MAAEJ,CAAC,GAAGF,UAAU,CAACH,CAAC,CAACU,WAAW,CAAC;IACtH,OAAOb,CAAC,CAACc,WAAW,GAAGT,CAAC,GAAGG,CAAC;EAC9B,CAAC;EAAEO,CAAC,GAAG;IACLC,WAAW,EAAEjB,CAAC;IACdkB,UAAU,EAAEN,CAAC;IACbO,MAAM,EAAEpB;EACV,CAAC;AACD,SACEiB,CAAC,IAAII,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}