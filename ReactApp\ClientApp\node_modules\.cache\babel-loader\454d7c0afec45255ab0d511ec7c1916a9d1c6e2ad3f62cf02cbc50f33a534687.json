{"ast": null, "code": "import { defined } from '../util';\nfunction optionsAccessor(name) {\n  return function (value) {\n    if (defined(value)) {\n      this.options.set(name, value);\n      return this;\n    }\n    return this.options.get(name);\n  };\n}\nfunction defineOptionsAccessors(fn, names) {\n  for (var i = 0; i < names.length; i++) {\n    fn[names[i]] = optionsAccessor(names[i]);\n  }\n}\nvar withOptions = function (TBase, names) {\n  var result = function (TBase) {\n    function result() {\n      TBase.apply(this, arguments);\n    }\n    if (TBase) result.__proto__ = TBase;\n    result.prototype = Object.create(TBase && TBase.prototype);\n    result.prototype.constructor = result;\n    return result;\n  }(TBase);\n  defineOptionsAccessors(result.prototype, names);\n  return result;\n};\nexport default withOptions;", "map": {"version": 3, "names": ["defined", "optionsAccessor", "name", "value", "options", "set", "get", "defineOptionsAccessors", "fn", "names", "i", "length", "withOptions", "TBase", "result", "apply", "arguments", "__proto__", "prototype", "Object", "create", "constructor"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/mixins/with-options.js"], "sourcesContent": ["import { defined } from '../util';\n\nfunction optionsAccessor(name) {\n    return function(value) {\n        if (defined(value)) {\n            this.options.set(name, value);\n            return this;\n        }\n\n        return this.options.get(name);\n    };\n}\n\nfunction defineOptionsAccessors(fn, names) {\n    for (var i = 0; i < names.length; i++) {\n        fn[names[i]] = optionsAccessor(names[i]);\n    }\n}\n\nvar withOptions = function (TBase, names) {\n    var result = (function (TBase) {\n        function result () {\n            TBase.apply(this, arguments);\n        }if ( TBase ) result.__proto__ = TBase;\n        result.prototype = Object.create( TBase && TBase.prototype );\n        result.prototype.constructor = result;\n\n        \n\n        return result;\n    }(TBase));\n    defineOptionsAccessors(result.prototype, names);\n\n    return result;\n};\n\nexport default withOptions;\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,SAAS;AAEjC,SAASC,eAAeA,CAACC,IAAI,EAAE;EAC3B,OAAO,UAASC,KAAK,EAAE;IACnB,IAAIH,OAAO,CAACG,KAAK,CAAC,EAAE;MAChB,IAAI,CAACC,OAAO,CAACC,GAAG,CAACH,IAAI,EAAEC,KAAK,CAAC;MAC7B,OAAO,IAAI;IACf;IAEA,OAAO,IAAI,CAACC,OAAO,CAACE,GAAG,CAACJ,IAAI,CAAC;EACjC,CAAC;AACL;AAEA,SAASK,sBAAsBA,CAACC,EAAE,EAAEC,KAAK,EAAE;EACvC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IACnCF,EAAE,CAACC,KAAK,CAACC,CAAC,CAAC,CAAC,GAAGT,eAAe,CAACQ,KAAK,CAACC,CAAC,CAAC,CAAC;EAC5C;AACJ;AAEA,IAAIE,WAAW,GAAG,SAAAA,CAAUC,KAAK,EAAEJ,KAAK,EAAE;EACtC,IAAIK,MAAM,GAAI,UAAUD,KAAK,EAAE;IAC3B,SAASC,MAAMA,CAAA,EAAI;MACfD,KAAK,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAChC;IAAC,IAAKH,KAAK,EAAGC,MAAM,CAACG,SAAS,GAAGJ,KAAK;IACtCC,MAAM,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAEP,KAAK,IAAIA,KAAK,CAACK,SAAU,CAAC;IAC5DJ,MAAM,CAACI,SAAS,CAACG,WAAW,GAAGP,MAAM;IAIrC,OAAOA,MAAM;EACjB,CAAC,CAACD,KAAK,CAAE;EACTN,sBAAsB,CAACO,MAAM,CAACI,SAAS,EAAET,KAAK,CAAC;EAE/C,OAAOK,MAAM;AACjB,CAAC;AAED,eAAeF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}