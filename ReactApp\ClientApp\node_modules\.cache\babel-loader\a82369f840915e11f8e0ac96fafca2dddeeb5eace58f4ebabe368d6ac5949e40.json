{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as r from \"react\";\nimport s from \"prop-types\";\nimport { classNames as a } from \"@progress/kendo-react-common\";\nconst t = e => /* @__PURE__ */r.createElement(\"div\", {\n  style: e.style,\n  className: a(\"k-card-body\", e.className)\n}, e.children);\nt.propTypes = {\n  className: s.string\n};\nexport { t as CardBody };", "map": {"version": 3, "names": ["r", "s", "classNames", "a", "t", "e", "createElement", "style", "className", "children", "propTypes", "string", "CardBody"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/card/CardBody.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as r from \"react\";\nimport s from \"prop-types\";\nimport { classNames as a } from \"@progress/kendo-react-common\";\nconst t = (e) => /* @__PURE__ */ r.createElement(\"div\", { style: e.style, className: a(\"k-card-body\", e.className) }, e.children);\nt.propTypes = {\n  className: s.string\n};\nexport {\n  t as CardBody\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AAC9D,MAAMC,CAAC,GAAIC,CAAC,IAAK,eAAgBL,CAAC,CAACM,aAAa,CAAC,KAAK,EAAE;EAAEC,KAAK,EAAEF,CAAC,CAACE,KAAK;EAAEC,SAAS,EAAEL,CAAC,CAAC,aAAa,EAAEE,CAAC,CAACG,SAAS;AAAE,CAAC,EAAEH,CAAC,CAACI,QAAQ,CAAC;AACjIL,CAAC,CAACM,SAAS,GAAG;EACZF,SAAS,EAAEP,CAAC,CAACU;AACf,CAAC;AACD,SACEP,CAAC,IAAIQ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}