{"ast": null, "code": "export default function isNegativeZero(value) {\n  return 1 / value === -Infinity;\n}", "map": {"version": 3, "names": ["isNegativeZero", "value", "Infinity"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-intl/dist/es/common/is-negative-zero.js"], "sourcesContent": ["export default function isNegativeZero(value) {\n    return (1 / value === -Infinity);\n}\n"], "mappings": "AAAA,eAAe,SAASA,cAAcA,CAACC,KAAK,EAAE;EAC1C,OAAQ,CAAC,GAAGA,KAAK,KAAK,CAACC,QAAQ;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}