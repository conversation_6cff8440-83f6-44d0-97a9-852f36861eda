{"ast": null, "code": "/* eslint-disable no-nested-ternary */\nvar PIXEL_PATTERN = /margin|padding|width|height|max|min|offset/;\nvar removePixel = {\n  left: true,\n  top: true\n};\nvar floatMap = {\n  cssFloat: 1,\n  styleFloat: 1,\n  float: 1\n};\nfunction getComputedStyle(node) {\n  return node.nodeType === 1 ? node.ownerDocument.defaultView.getComputedStyle(node, null) : {};\n}\nfunction getStyleValue(node, type, value) {\n  type = type.toLowerCase();\n  if (value === 'auto') {\n    if (type === 'height') {\n      return node.offsetHeight;\n    }\n    if (type === 'width') {\n      return node.offsetWidth;\n    }\n  }\n  if (!(type in removePixel)) {\n    removePixel[type] = PIXEL_PATTERN.test(type);\n  }\n  return removePixel[type] ? parseFloat(value) || 0 : value;\n}\nexport function get(node, name) {\n  var length = arguments.length;\n  var style = getComputedStyle(node);\n  name = floatMap[name] ? 'cssFloat' in node.style ? 'cssFloat' : 'styleFloat' : name;\n  return length === 1 ? style : getStyleValue(node, name, style[name] || node.style[name]);\n}\nexport function set(node, name, value) {\n  var length = arguments.length;\n  name = floatMap[name] ? 'cssFloat' in node.style ? 'cssFloat' : 'styleFloat' : name;\n  if (length === 3) {\n    if (typeof value === 'number' && PIXEL_PATTERN.test(name)) {\n      value = \"\".concat(value, \"px\");\n    }\n    node.style[name] = value; // Number\n    return value;\n  }\n  for (var x in name) {\n    if (name.hasOwnProperty(x)) {\n      set(node, x, name[x]);\n    }\n  }\n  return getComputedStyle(node);\n}\nexport function getOuterWidth(el) {\n  if (el === document.body) {\n    return document.documentElement.clientWidth;\n  }\n  return el.offsetWidth;\n}\nexport function getOuterHeight(el) {\n  if (el === document.body) {\n    return window.innerHeight || document.documentElement.clientHeight;\n  }\n  return el.offsetHeight;\n}\nexport function getDocSize() {\n  var width = Math.max(document.documentElement.scrollWidth, document.body.scrollWidth);\n  var height = Math.max(document.documentElement.scrollHeight, document.body.scrollHeight);\n  return {\n    width: width,\n    height: height\n  };\n}\nexport function getClientSize() {\n  var width = document.documentElement.clientWidth;\n  var height = window.innerHeight || document.documentElement.clientHeight;\n  return {\n    width: width,\n    height: height\n  };\n}\nexport function getScroll() {\n  return {\n    scrollLeft: Math.max(document.documentElement.scrollLeft, document.body.scrollLeft),\n    scrollTop: Math.max(document.documentElement.scrollTop, document.body.scrollTop)\n  };\n}\nexport function getOffset(node) {\n  var box = node.getBoundingClientRect();\n  var docElem = document.documentElement;\n\n  // < ie8 不支持 win.pageXOffset, 则使用 docElem.scrollLeft\n  return {\n    left: box.left + (window.pageXOffset || docElem.scrollLeft) - (docElem.clientLeft || document.body.clientLeft || 0),\n    top: box.top + (window.pageYOffset || docElem.scrollTop) - (docElem.clientTop || document.body.clientTop || 0)\n  };\n}", "map": {"version": 3, "names": ["PIXEL_PATTERN", "removePixel", "left", "top", "floatMap", "cssFloat", "styleFloat", "float", "getComputedStyle", "node", "nodeType", "ownerDocument", "defaultView", "getStyleValue", "type", "value", "toLowerCase", "offsetHeight", "offsetWidth", "test", "parseFloat", "get", "name", "length", "arguments", "style", "set", "concat", "x", "hasOwnProperty", "getOuterWidth", "el", "document", "body", "documentElement", "clientWidth", "getOuterHeight", "window", "innerHeight", "clientHeight", "getDocSize", "width", "Math", "max", "scrollWidth", "height", "scrollHeight", "getClientSize", "getScroll", "scrollLeft", "scrollTop", "getOffset", "box", "getBoundingClientRect", "doc<PERSON><PERSON>", "pageXOffset", "clientLeft", "pageYOffset", "clientTop"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/rc-util/es/Dom/css.js"], "sourcesContent": ["/* eslint-disable no-nested-ternary */\nvar PIXEL_PATTERN = /margin|padding|width|height|max|min|offset/;\nvar removePixel = {\n  left: true,\n  top: true\n};\nvar floatMap = {\n  cssFloat: 1,\n  styleFloat: 1,\n  float: 1\n};\nfunction getComputedStyle(node) {\n  return node.nodeType === 1 ? node.ownerDocument.defaultView.getComputedStyle(node, null) : {};\n}\nfunction getStyleValue(node, type, value) {\n  type = type.toLowerCase();\n  if (value === 'auto') {\n    if (type === 'height') {\n      return node.offsetHeight;\n    }\n    if (type === 'width') {\n      return node.offsetWidth;\n    }\n  }\n  if (!(type in removePixel)) {\n    removePixel[type] = PIXEL_PATTERN.test(type);\n  }\n  return removePixel[type] ? parseFloat(value) || 0 : value;\n}\nexport function get(node, name) {\n  var length = arguments.length;\n  var style = getComputedStyle(node);\n  name = floatMap[name] ? 'cssFloat' in node.style ? 'cssFloat' : 'styleFloat' : name;\n  return length === 1 ? style : getStyleValue(node, name, style[name] || node.style[name]);\n}\nexport function set(node, name, value) {\n  var length = arguments.length;\n  name = floatMap[name] ? 'cssFloat' in node.style ? 'cssFloat' : 'styleFloat' : name;\n  if (length === 3) {\n    if (typeof value === 'number' && PIXEL_PATTERN.test(name)) {\n      value = \"\".concat(value, \"px\");\n    }\n    node.style[name] = value; // Number\n    return value;\n  }\n  for (var x in name) {\n    if (name.hasOwnProperty(x)) {\n      set(node, x, name[x]);\n    }\n  }\n  return getComputedStyle(node);\n}\nexport function getOuterWidth(el) {\n  if (el === document.body) {\n    return document.documentElement.clientWidth;\n  }\n  return el.offsetWidth;\n}\nexport function getOuterHeight(el) {\n  if (el === document.body) {\n    return window.innerHeight || document.documentElement.clientHeight;\n  }\n  return el.offsetHeight;\n}\nexport function getDocSize() {\n  var width = Math.max(document.documentElement.scrollWidth, document.body.scrollWidth);\n  var height = Math.max(document.documentElement.scrollHeight, document.body.scrollHeight);\n  return {\n    width: width,\n    height: height\n  };\n}\nexport function getClientSize() {\n  var width = document.documentElement.clientWidth;\n  var height = window.innerHeight || document.documentElement.clientHeight;\n  return {\n    width: width,\n    height: height\n  };\n}\nexport function getScroll() {\n  return {\n    scrollLeft: Math.max(document.documentElement.scrollLeft, document.body.scrollLeft),\n    scrollTop: Math.max(document.documentElement.scrollTop, document.body.scrollTop)\n  };\n}\nexport function getOffset(node) {\n  var box = node.getBoundingClientRect();\n  var docElem = document.documentElement;\n\n  // < ie8 不支持 win.pageXOffset, 则使用 docElem.scrollLeft\n  return {\n    left: box.left + (window.pageXOffset || docElem.scrollLeft) - (docElem.clientLeft || document.body.clientLeft || 0),\n    top: box.top + (window.pageYOffset || docElem.scrollTop) - (docElem.clientTop || document.body.clientTop || 0)\n  };\n}"], "mappings": "AAAA;AACA,IAAIA,aAAa,GAAG,4CAA4C;AAChE,IAAIC,WAAW,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVC,GAAG,EAAE;AACP,CAAC;AACD,IAAIC,QAAQ,GAAG;EACbC,QAAQ,EAAE,CAAC;EACXC,UAAU,EAAE,CAAC;EACbC,KAAK,EAAE;AACT,CAAC;AACD,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,OAAOA,IAAI,CAACC,QAAQ,KAAK,CAAC,GAAGD,IAAI,CAACE,aAAa,CAACC,WAAW,CAACJ,gBAAgB,CAACC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AAC/F;AACA,SAASI,aAAaA,CAACJ,IAAI,EAAEK,IAAI,EAAEC,KAAK,EAAE;EACxCD,IAAI,GAAGA,IAAI,CAACE,WAAW,CAAC,CAAC;EACzB,IAAID,KAAK,KAAK,MAAM,EAAE;IACpB,IAAID,IAAI,KAAK,QAAQ,EAAE;MACrB,OAAOL,IAAI,CAACQ,YAAY;IAC1B;IACA,IAAIH,IAAI,KAAK,OAAO,EAAE;MACpB,OAAOL,IAAI,CAACS,WAAW;IACzB;EACF;EACA,IAAI,EAAEJ,IAAI,IAAIb,WAAW,CAAC,EAAE;IAC1BA,WAAW,CAACa,IAAI,CAAC,GAAGd,aAAa,CAACmB,IAAI,CAACL,IAAI,CAAC;EAC9C;EACA,OAAOb,WAAW,CAACa,IAAI,CAAC,GAAGM,UAAU,CAACL,KAAK,CAAC,IAAI,CAAC,GAAGA,KAAK;AAC3D;AACA,OAAO,SAASM,GAAGA,CAACZ,IAAI,EAAEa,IAAI,EAAE;EAC9B,IAAIC,MAAM,GAAGC,SAAS,CAACD,MAAM;EAC7B,IAAIE,KAAK,GAAGjB,gBAAgB,CAACC,IAAI,CAAC;EAClCa,IAAI,GAAGlB,QAAQ,CAACkB,IAAI,CAAC,GAAG,UAAU,IAAIb,IAAI,CAACgB,KAAK,GAAG,UAAU,GAAG,YAAY,GAAGH,IAAI;EACnF,OAAOC,MAAM,KAAK,CAAC,GAAGE,KAAK,GAAGZ,aAAa,CAACJ,IAAI,EAAEa,IAAI,EAAEG,KAAK,CAACH,IAAI,CAAC,IAAIb,IAAI,CAACgB,KAAK,CAACH,IAAI,CAAC,CAAC;AAC1F;AACA,OAAO,SAASI,GAAGA,CAACjB,IAAI,EAAEa,IAAI,EAAEP,KAAK,EAAE;EACrC,IAAIQ,MAAM,GAAGC,SAAS,CAACD,MAAM;EAC7BD,IAAI,GAAGlB,QAAQ,CAACkB,IAAI,CAAC,GAAG,UAAU,IAAIb,IAAI,CAACgB,KAAK,GAAG,UAAU,GAAG,YAAY,GAAGH,IAAI;EACnF,IAAIC,MAAM,KAAK,CAAC,EAAE;IAChB,IAAI,OAAOR,KAAK,KAAK,QAAQ,IAAIf,aAAa,CAACmB,IAAI,CAACG,IAAI,CAAC,EAAE;MACzDP,KAAK,GAAG,EAAE,CAACY,MAAM,CAACZ,KAAK,EAAE,IAAI,CAAC;IAChC;IACAN,IAAI,CAACgB,KAAK,CAACH,IAAI,CAAC,GAAGP,KAAK,CAAC,CAAC;IAC1B,OAAOA,KAAK;EACd;EACA,KAAK,IAAIa,CAAC,IAAIN,IAAI,EAAE;IAClB,IAAIA,IAAI,CAACO,cAAc,CAACD,CAAC,CAAC,EAAE;MAC1BF,GAAG,CAACjB,IAAI,EAAEmB,CAAC,EAAEN,IAAI,CAACM,CAAC,CAAC,CAAC;IACvB;EACF;EACA,OAAOpB,gBAAgB,CAACC,IAAI,CAAC;AAC/B;AACA,OAAO,SAASqB,aAAaA,CAACC,EAAE,EAAE;EAChC,IAAIA,EAAE,KAAKC,QAAQ,CAACC,IAAI,EAAE;IACxB,OAAOD,QAAQ,CAACE,eAAe,CAACC,WAAW;EAC7C;EACA,OAAOJ,EAAE,CAACb,WAAW;AACvB;AACA,OAAO,SAASkB,cAAcA,CAACL,EAAE,EAAE;EACjC,IAAIA,EAAE,KAAKC,QAAQ,CAACC,IAAI,EAAE;IACxB,OAAOI,MAAM,CAACC,WAAW,IAAIN,QAAQ,CAACE,eAAe,CAACK,YAAY;EACpE;EACA,OAAOR,EAAE,CAACd,YAAY;AACxB;AACA,OAAO,SAASuB,UAAUA,CAAA,EAAG;EAC3B,IAAIC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACX,QAAQ,CAACE,eAAe,CAACU,WAAW,EAAEZ,QAAQ,CAACC,IAAI,CAACW,WAAW,CAAC;EACrF,IAAIC,MAAM,GAAGH,IAAI,CAACC,GAAG,CAACX,QAAQ,CAACE,eAAe,CAACY,YAAY,EAAEd,QAAQ,CAACC,IAAI,CAACa,YAAY,CAAC;EACxF,OAAO;IACLL,KAAK,EAAEA,KAAK;IACZI,MAAM,EAAEA;EACV,CAAC;AACH;AACA,OAAO,SAASE,aAAaA,CAAA,EAAG;EAC9B,IAAIN,KAAK,GAAGT,QAAQ,CAACE,eAAe,CAACC,WAAW;EAChD,IAAIU,MAAM,GAAGR,MAAM,CAACC,WAAW,IAAIN,QAAQ,CAACE,eAAe,CAACK,YAAY;EACxE,OAAO;IACLE,KAAK,EAAEA,KAAK;IACZI,MAAM,EAAEA;EACV,CAAC;AACH;AACA,OAAO,SAASG,SAASA,CAAA,EAAG;EAC1B,OAAO;IACLC,UAAU,EAAEP,IAAI,CAACC,GAAG,CAACX,QAAQ,CAACE,eAAe,CAACe,UAAU,EAAEjB,QAAQ,CAACC,IAAI,CAACgB,UAAU,CAAC;IACnFC,SAAS,EAAER,IAAI,CAACC,GAAG,CAACX,QAAQ,CAACE,eAAe,CAACgB,SAAS,EAAElB,QAAQ,CAACC,IAAI,CAACiB,SAAS;EACjF,CAAC;AACH;AACA,OAAO,SAASC,SAASA,CAAC1C,IAAI,EAAE;EAC9B,IAAI2C,GAAG,GAAG3C,IAAI,CAAC4C,qBAAqB,CAAC,CAAC;EACtC,IAAIC,OAAO,GAAGtB,QAAQ,CAACE,eAAe;;EAEtC;EACA,OAAO;IACLhC,IAAI,EAAEkD,GAAG,CAAClD,IAAI,IAAImC,MAAM,CAACkB,WAAW,IAAID,OAAO,CAACL,UAAU,CAAC,IAAIK,OAAO,CAACE,UAAU,IAAIxB,QAAQ,CAACC,IAAI,CAACuB,UAAU,IAAI,CAAC,CAAC;IACnHrD,GAAG,EAAEiD,GAAG,CAACjD,GAAG,IAAIkC,MAAM,CAACoB,WAAW,IAAIH,OAAO,CAACJ,SAAS,CAAC,IAAII,OAAO,CAACI,SAAS,IAAI1B,QAAQ,CAACC,IAAI,CAACyB,SAAS,IAAI,CAAC;EAC/G,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}