{"ast": null, "code": "import close from './close';\nexport default function closeOrLess(a, b, tolerance) {\n  return a < b || close(a, b, tolerance);\n}", "map": {"version": 3, "names": ["close", "closeOrLess", "a", "b", "tolerance"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/geometry/math/close-or-less.js"], "sourcesContent": ["import close from './close';\n\nexport default function closeOrLess(a, b, tolerance) {\n    return a < b || close(a, b, tolerance);\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,SAAS;AAE3B,eAAe,SAASC,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACjD,OAAOF,CAAC,GAAGC,CAAC,IAAIH,KAAK,CAACE,CAAC,EAAEC,CAAC,EAAEC,SAAS,CAAC;AAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}