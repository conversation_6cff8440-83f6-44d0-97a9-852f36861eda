{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as n from \"react\";\nimport e from \"prop-types\";\nimport { DrawerContext as P } from \"./context/DrawerContext.mjs\";\nimport { DrawerNavigation as O } from \"./DrawerNavigation.mjs\";\nimport { useDir as T, classNames as j } from \"@progress/kendo-react-common\";\nconst u = n.forwardRef((d, p) => {\n  const {\n      expanded: a = t.expanded,\n      mode: o = t.mode,\n      position: f = t.position,\n      className: v,\n      drawerClassName: w,\n      children: h,\n      style: y,\n      animation: b = t.animation,\n      mini: l = t.mini,\n      width: k = t.width,\n      miniWidth: x = t.miniWidth,\n      items: i,\n      item: N,\n      tabIndex: C,\n      onOverlayClick: E,\n      onSelect: s\n    } = d,\n    r = n.useRef(null),\n    g = n.useCallback(() => {\n      r.current && r.current.focus();\n    }, []);\n  n.useImperativeHandle(p, () => ({\n    element: r.current,\n    focus: g\n  }));\n  const D = n.useCallback((S, W, m) => {\n      if (i && s) {\n        const I = {\n          itemTarget: S,\n          itemIndex: W,\n          syntheticEvent: m,\n          nativeEvent: m && m.nativeEvent,\n          target: void 0\n        };\n        s.call(void 0, I);\n      }\n    }, [i, s]),\n    c = T(r, d.dir),\n    R = j({\n      \"k-drawer-container\": !0,\n      \"k-drawer-expanded\": a,\n      \"k-drawer-overlay\": o === \"overlay\",\n      \"k-drawer-push\": o === \"push\",\n      \"k-drawer-mini\": l && !a\n    }, v);\n  return /* @__PURE__ */n.createElement(P.Provider, {\n    value: {\n      animation: b,\n      expanded: a,\n      mode: o,\n      position: f,\n      mini: l,\n      dir: c,\n      items: i,\n      item: N,\n      width: k,\n      miniWidth: x,\n      onOverlayClick: E,\n      onSelect: D\n    }\n  }, /* @__PURE__ */n.createElement(\"div\", {\n    className: R,\n    ref: r,\n    dir: c,\n    style: y,\n    tabIndex: C\n  }, i && /* @__PURE__ */n.createElement(O, {\n    className: w\n  }), h));\n});\nu.propTypes = {\n  animation: e.any,\n  expanded: e.bool,\n  children: e.any,\n  className: e.string,\n  dir: e.string,\n  mode: e.string,\n  position: e.string,\n  mini: e.bool,\n  style: e.object,\n  tabIndex: e.number,\n  width: e.number,\n  miniWidth: e.number,\n  selected: e.number,\n  onSelect: e.func,\n  onOverlayClick: e.func\n};\nconst t = {\n  animation: !0,\n  expanded: !1,\n  mode: \"overlay\",\n  position: \"start\",\n  mini: !1,\n  width: 240,\n  miniWidth: 48\n};\nu.displayName = \"KendoDrawer\";\nexport { u as Drawer };", "map": {"version": 3, "names": ["n", "e", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "P", "DrawerNavigation", "O", "useDir", "T", "classNames", "j", "u", "forwardRef", "d", "p", "expanded", "a", "t", "mode", "o", "position", "f", "className", "v", "drawerClassName", "w", "children", "h", "style", "y", "animation", "b", "mini", "l", "width", "k", "miniWidth", "x", "items", "i", "item", "N", "tabIndex", "C", "onOverlayClick", "E", "onSelect", "s", "r", "useRef", "g", "useCallback", "current", "focus", "useImperativeHandle", "element", "D", "S", "W", "m", "I", "itemTarget", "itemIndex", "syntheticEvent", "nativeEvent", "target", "call", "c", "dir", "R", "createElement", "Provider", "value", "ref", "propTypes", "any", "bool", "string", "object", "number", "selected", "func", "displayName", "Drawer"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/drawer/Drawer.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as n from \"react\";\nimport e from \"prop-types\";\nimport { DrawerContext as P } from \"./context/DrawerContext.mjs\";\nimport { DrawerNavigation as O } from \"./DrawerNavigation.mjs\";\nimport { useDir as T, classNames as j } from \"@progress/kendo-react-common\";\nconst u = n.forwardRef((d, p) => {\n  const {\n    expanded: a = t.expanded,\n    mode: o = t.mode,\n    position: f = t.position,\n    className: v,\n    drawerClassName: w,\n    children: h,\n    style: y,\n    animation: b = t.animation,\n    mini: l = t.mini,\n    width: k = t.width,\n    miniWidth: x = t.miniWidth,\n    items: i,\n    item: N,\n    tabIndex: C,\n    onOverlayClick: E,\n    onSelect: s\n  } = d, r = n.useRef(null), g = n.useCallback(() => {\n    r.current && r.current.focus();\n  }, []);\n  n.useImperativeHandle(\n    p,\n    () => ({\n      element: r.current,\n      focus: g\n    })\n  );\n  const D = n.useCallback(\n    (S, W, m) => {\n      if (i && s) {\n        const I = {\n          itemTarget: S,\n          itemIndex: W,\n          syntheticEvent: m,\n          nativeEvent: m && m.nativeEvent,\n          target: void 0\n        };\n        s.call(void 0, I);\n      }\n    },\n    [i, s]\n  ), c = T(r, d.dir), R = j(\n    {\n      \"k-drawer-container\": !0,\n      \"k-drawer-expanded\": a,\n      \"k-drawer-overlay\": o === \"overlay\",\n      \"k-drawer-push\": o === \"push\",\n      \"k-drawer-mini\": l && !a\n    },\n    v\n  );\n  return /* @__PURE__ */ n.createElement(\n    P.Provider,\n    {\n      value: {\n        animation: b,\n        expanded: a,\n        mode: o,\n        position: f,\n        mini: l,\n        dir: c,\n        items: i,\n        item: N,\n        width: k,\n        miniWidth: x,\n        onOverlayClick: E,\n        onSelect: D\n      }\n    },\n    /* @__PURE__ */ n.createElement(\"div\", { className: R, ref: r, dir: c, style: y, tabIndex: C }, i && /* @__PURE__ */ n.createElement(O, { className: w }), h)\n  );\n});\nu.propTypes = {\n  animation: e.any,\n  expanded: e.bool,\n  children: e.any,\n  className: e.string,\n  dir: e.string,\n  mode: e.string,\n  position: e.string,\n  mini: e.bool,\n  style: e.object,\n  tabIndex: e.number,\n  width: e.number,\n  miniWidth: e.number,\n  selected: e.number,\n  onSelect: e.func,\n  onOverlayClick: e.func\n};\nconst t = {\n  animation: !0,\n  expanded: !1,\n  mode: \"overlay\",\n  position: \"start\",\n  mini: !1,\n  width: 240,\n  miniWidth: 48\n};\nu.displayName = \"KendoDrawer\";\nexport {\n  u as Drawer\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,aAAa,IAAIC,CAAC,QAAQ,6BAA6B;AAChE,SAASC,gBAAgB,IAAIC,CAAC,QAAQ,wBAAwB;AAC9D,SAASC,MAAM,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AAC3E,MAAMC,CAAC,GAAGV,CAAC,CAACW,UAAU,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;EAC/B,MAAM;MACJC,QAAQ,EAAEC,CAAC,GAAGC,CAAC,CAACF,QAAQ;MACxBG,IAAI,EAAEC,CAAC,GAAGF,CAAC,CAACC,IAAI;MAChBE,QAAQ,EAAEC,CAAC,GAAGJ,CAAC,CAACG,QAAQ;MACxBE,SAAS,EAAEC,CAAC;MACZC,eAAe,EAAEC,CAAC;MAClBC,QAAQ,EAAEC,CAAC;MACXC,KAAK,EAAEC,CAAC;MACRC,SAAS,EAAEC,CAAC,GAAGd,CAAC,CAACa,SAAS;MAC1BE,IAAI,EAAEC,CAAC,GAAGhB,CAAC,CAACe,IAAI;MAChBE,KAAK,EAAEC,CAAC,GAAGlB,CAAC,CAACiB,KAAK;MAClBE,SAAS,EAAEC,CAAC,GAAGpB,CAAC,CAACmB,SAAS;MAC1BE,KAAK,EAAEC,CAAC;MACRC,IAAI,EAAEC,CAAC;MACPC,QAAQ,EAAEC,CAAC;MACXC,cAAc,EAAEC,CAAC;MACjBC,QAAQ,EAAEC;IACZ,CAAC,GAAGlC,CAAC;IAAEmC,CAAC,GAAG/C,CAAC,CAACgD,MAAM,CAAC,IAAI,CAAC;IAAEC,CAAC,GAAGjD,CAAC,CAACkD,WAAW,CAAC,MAAM;MACjDH,CAAC,CAACI,OAAO,IAAIJ,CAAC,CAACI,OAAO,CAACC,KAAK,CAAC,CAAC;IAChC,CAAC,EAAE,EAAE,CAAC;EACNpD,CAAC,CAACqD,mBAAmB,CACnBxC,CAAC,EACD,OAAO;IACLyC,OAAO,EAAEP,CAAC,CAACI,OAAO;IAClBC,KAAK,EAAEH;EACT,CAAC,CACH,CAAC;EACD,MAAMM,CAAC,GAAGvD,CAAC,CAACkD,WAAW,CACrB,CAACM,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;MACX,IAAIpB,CAAC,IAAIQ,CAAC,EAAE;QACV,MAAMa,CAAC,GAAG;UACRC,UAAU,EAAEJ,CAAC;UACbK,SAAS,EAAEJ,CAAC;UACZK,cAAc,EAAEJ,CAAC;UACjBK,WAAW,EAAEL,CAAC,IAAIA,CAAC,CAACK,WAAW;UAC/BC,MAAM,EAAE,KAAK;QACf,CAAC;QACDlB,CAAC,CAACmB,IAAI,CAAC,KAAK,CAAC,EAAEN,CAAC,CAAC;MACnB;IACF,CAAC,EACD,CAACrB,CAAC,EAAEQ,CAAC,CACP,CAAC;IAAEoB,CAAC,GAAG3D,CAAC,CAACwC,CAAC,EAAEnC,CAAC,CAACuD,GAAG,CAAC;IAAEC,CAAC,GAAG3D,CAAC,CACvB;MACE,oBAAoB,EAAE,CAAC,CAAC;MACxB,mBAAmB,EAAEM,CAAC;MACtB,kBAAkB,EAAEG,CAAC,KAAK,SAAS;MACnC,eAAe,EAAEA,CAAC,KAAK,MAAM;MAC7B,eAAe,EAAEc,CAAC,IAAI,CAACjB;IACzB,CAAC,EACDO,CACF,CAAC;EACD,OAAO,eAAgBtB,CAAC,CAACqE,aAAa,CACpClE,CAAC,CAACmE,QAAQ,EACV;IACEC,KAAK,EAAE;MACL1C,SAAS,EAAEC,CAAC;MACZhB,QAAQ,EAAEC,CAAC;MACXE,IAAI,EAAEC,CAAC;MACPC,QAAQ,EAAEC,CAAC;MACXW,IAAI,EAAEC,CAAC;MACPmC,GAAG,EAAED,CAAC;MACN7B,KAAK,EAAEC,CAAC;MACRC,IAAI,EAAEC,CAAC;MACPP,KAAK,EAAEC,CAAC;MACRC,SAAS,EAAEC,CAAC;MACZO,cAAc,EAAEC,CAAC;MACjBC,QAAQ,EAAEU;IACZ;EACF,CAAC,EACD,eAAgBvD,CAAC,CAACqE,aAAa,CAAC,KAAK,EAAE;IAAEhD,SAAS,EAAE+C,CAAC;IAAEI,GAAG,EAAEzB,CAAC;IAAEoB,GAAG,EAAED,CAAC;IAAEvC,KAAK,EAAEC,CAAC;IAAEa,QAAQ,EAAEC;EAAE,CAAC,EAAEJ,CAAC,IAAI,eAAgBtC,CAAC,CAACqE,aAAa,CAAChE,CAAC,EAAE;IAAEgB,SAAS,EAAEG;EAAE,CAAC,CAAC,EAAEE,CAAC,CAC9J,CAAC;AACH,CAAC,CAAC;AACFhB,CAAC,CAAC+D,SAAS,GAAG;EACZ5C,SAAS,EAAE5B,CAAC,CAACyE,GAAG;EAChB5D,QAAQ,EAAEb,CAAC,CAAC0E,IAAI;EAChBlD,QAAQ,EAAExB,CAAC,CAACyE,GAAG;EACfrD,SAAS,EAAEpB,CAAC,CAAC2E,MAAM;EACnBT,GAAG,EAAElE,CAAC,CAAC2E,MAAM;EACb3D,IAAI,EAAEhB,CAAC,CAAC2E,MAAM;EACdzD,QAAQ,EAAElB,CAAC,CAAC2E,MAAM;EAClB7C,IAAI,EAAE9B,CAAC,CAAC0E,IAAI;EACZhD,KAAK,EAAE1B,CAAC,CAAC4E,MAAM;EACfpC,QAAQ,EAAExC,CAAC,CAAC6E,MAAM;EAClB7C,KAAK,EAAEhC,CAAC,CAAC6E,MAAM;EACf3C,SAAS,EAAElC,CAAC,CAAC6E,MAAM;EACnBC,QAAQ,EAAE9E,CAAC,CAAC6E,MAAM;EAClBjC,QAAQ,EAAE5C,CAAC,CAAC+E,IAAI;EAChBrC,cAAc,EAAE1C,CAAC,CAAC+E;AACpB,CAAC;AACD,MAAMhE,CAAC,GAAG;EACRa,SAAS,EAAE,CAAC,CAAC;EACbf,QAAQ,EAAE,CAAC,CAAC;EACZG,IAAI,EAAE,SAAS;EACfE,QAAQ,EAAE,OAAO;EACjBY,IAAI,EAAE,CAAC,CAAC;EACRE,KAAK,EAAE,GAAG;EACVE,SAAS,EAAE;AACb,CAAC;AACDzB,CAAC,CAACuE,WAAW,GAAG,aAAa;AAC7B,SACEvE,CAAC,IAAIwE,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}