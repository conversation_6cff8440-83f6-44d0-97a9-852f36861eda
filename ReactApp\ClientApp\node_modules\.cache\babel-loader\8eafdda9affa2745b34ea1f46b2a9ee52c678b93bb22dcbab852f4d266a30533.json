{"ast": null, "code": "export default function ellipseExtremeAngles(center, rx, ry, matrix) {\n  var extremeX = 0;\n  var extremeY = 0;\n  if (matrix) {\n    extremeX = Math.atan2(matrix.c * ry, matrix.a * rx);\n    if (matrix.b !== 0) {\n      extremeY = Math.atan2(matrix.d * ry, matrix.b * rx);\n    }\n  }\n  return {\n    x: extremeX,\n    y: extremeY\n  };\n}", "map": {"version": 3, "names": ["ellipseExtremeAngles", "center", "rx", "ry", "matrix", "extremeX", "extremeY", "Math", "atan2", "c", "a", "b", "d", "x", "y"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/geometry/math/ellipse-extreme-angles.js"], "sourcesContent": ["export default function ellipseExtremeAngles(center, rx, ry, matrix) {\n    var extremeX = 0;\n    var extremeY = 0;\n\n    if (matrix) {\n        extremeX = Math.atan2(matrix.c * ry, matrix.a * rx);\n        if (matrix.b !== 0) {\n            extremeY = Math.atan2(matrix.d * ry, matrix.b * rx);\n        }\n    }\n\n    return {\n        x: extremeX,\n        y: extremeY\n    };\n}"], "mappings": "AAAA,eAAe,SAASA,oBAAoBA,CAACC,MAAM,EAAEC,EAAE,EAAEC,EAAE,EAAEC,MAAM,EAAE;EACjE,IAAIC,QAAQ,GAAG,CAAC;EAChB,IAAIC,QAAQ,GAAG,CAAC;EAEhB,IAAIF,MAAM,EAAE;IACRC,QAAQ,GAAGE,IAAI,CAACC,KAAK,CAACJ,MAAM,CAACK,CAAC,GAAGN,EAAE,EAAEC,MAAM,CAACM,CAAC,GAAGR,EAAE,CAAC;IACnD,IAAIE,MAAM,CAACO,CAAC,KAAK,CAAC,EAAE;MAChBL,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACJ,MAAM,CAACQ,CAAC,GAAGT,EAAE,EAAEC,MAAM,CAACO,CAAC,GAAGT,EAAE,CAAC;IACvD;EACJ;EAEA,OAAO;IACHW,CAAC,EAAER,QAAQ;IACXS,CAAC,EAAER;EACP,CA<PERSON>;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}