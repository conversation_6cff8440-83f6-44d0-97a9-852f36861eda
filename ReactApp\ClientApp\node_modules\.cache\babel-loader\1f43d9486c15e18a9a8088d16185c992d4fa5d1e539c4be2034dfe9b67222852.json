{"ast": null, "code": "var cachedWidth = 0;\nexport default function scrollbarWidth() {\n  if (!cachedWidth && typeof document !== 'undefined') {\n    var div = document.createElement(\"div\");\n    div.style.cssText = \"overflow:scroll;overflow-x:hidden;zoom:1;clear:both;display:block\";\n    div.innerHTML = \"&nbsp;\";\n    document.body.appendChild(div);\n    cachedWidth = div.offsetWidth - div.scrollWidth;\n    document.body.removeChild(div);\n  }\n  return cachedWidth;\n}", "map": {"version": 3, "names": ["cachedWidth", "scrollbarWidth", "document", "div", "createElement", "style", "cssText", "innerHTML", "body", "append<PERSON><PERSON><PERSON>", "offsetWidth", "scrollWidth", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-popup-common/dist/es/scrollbar-width.js"], "sourcesContent": ["var cachedWidth = 0;\n\nexport default function scrollbarWidth() {\n    if (!cachedWidth && typeof document !== 'undefined') {\n        var div = document.createElement(\"div\");\n\n        div.style.cssText = \"overflow:scroll;overflow-x:hidden;zoom:1;clear:both;display:block\";\n        div.innerHTML = \"&nbsp;\";\n        document.body.appendChild(div);\n\n        cachedWidth = div.offsetWidth - div.scrollWidth;\n\n        document.body.removeChild(div);\n    }\n\n    return cachedWidth;\n}\n"], "mappings": "AAAA,IAAIA,WAAW,GAAG,CAAC;AAEnB,eAAe,SAASC,cAAcA,CAAA,EAAG;EACrC,IAAI,CAACD,WAAW,IAAI,OAAOE,QAAQ,KAAK,WAAW,EAAE;IACjD,IAAIC,GAAG,GAAGD,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;IAEvCD,GAAG,CAACE,KAAK,CAACC,OAAO,GAAG,mEAAmE;IACvFH,GAAG,CAACI,SAAS,GAAG,QAAQ;IACxBL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACN,GAAG,CAAC;IAE9BH,WAAW,GAAGG,GAAG,CAACO,WAAW,GAAGP,GAAG,CAACQ,WAAW;IAE/CT,QAAQ,CAACM,IAAI,CAACI,WAAW,CAACT,GAAG,CAAC;EAClC;EAEA,OAAOH,WAAW;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}