{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as t from \"react\";\nimport { ActionSheet as v, ActionSheetFooter as x } from \"@progress/kendo-react-layout\";\nimport { checkIcon as C } from \"@progress/kendo-svg-icons\";\nimport { Button as o } from \"@progress/kendo-react-buttons\";\nimport { useAdaptiveModeContext as g } from \"@progress/kendo-react-common\";\nconst E = i => {\n  const {\n      windowWidth: l = 0,\n      children: r,\n      navigatable: c,\n      navigatableElements: s,\n      expand: m,\n      animation: p,\n      title: d,\n      subTitle: f,\n      footer: e,\n      onClose: n\n    } = i,\n    h = () => /* @__PURE__ */t.createElement(o, {\n      tabIndex: 0,\n      \"aria-label\": \"Cancel\",\n      \"aria-disabled\": \"false\",\n      type: \"button\",\n      fillMode: \"flat\",\n      size: \"large\",\n      themeColor: \"primary\",\n      svgIcon: C,\n      onClick: n\n    }),\n    a = g(),\n    b = {\n      navigatable: c || !1,\n      navigatableElements: s || [],\n      expand: m,\n      animation: p,\n      suffixActions: h(),\n      onClose: n,\n      animationStyles: a && l <= a.small ? {\n        top: 0,\n        width: \"100%\",\n        height: \"100%\"\n      } : void 0,\n      title: d,\n      subTitle: f,\n      className: \"k-adaptive-actionsheet\",\n      position: a && l <= a.small ? \"fullscreen\" : void 0\n    };\n  return /* @__PURE__ */t.createElement(v, {\n    ...b\n  }, r, /* @__PURE__ */t.createElement(x, {\n    className: \"k-actions k-actions-stretched\"\n  }, /* @__PURE__ */t.createElement(o, {\n    size: \"large\",\n    className: \"k-coloreditor-cancel\",\n    \"aria-label\": e.cancelText,\n    onClick: e.onCancel\n  }, e.cancelText), /* @__PURE__ */t.createElement(o, {\n    themeColor: \"primary\",\n    size: \"large\",\n    className: \"k-coloreditor-apply\",\n    \"aria-label\": e.applyText,\n    onClick: e.onApply\n  }, e.applyText)));\n};\nexport { E as AdaptiveMode };", "map": {"version": 3, "names": ["t", "ActionSheet", "v", "ActionSheetFooter", "x", "checkIcon", "C", "<PERSON><PERSON>", "o", "useAdaptiveModeContext", "g", "E", "i", "windowWidth", "l", "children", "r", "navigatable", "c", "navigatableElements", "s", "expand", "m", "animation", "p", "title", "d", "subTitle", "f", "footer", "e", "onClose", "n", "h", "createElement", "tabIndex", "type", "fillMode", "size", "themeColor", "svgIcon", "onClick", "a", "b", "suffixActions", "animationStyles", "small", "top", "width", "height", "className", "position", "cancelText", "onCancel", "applyText", "onApply", "AdaptiveMode"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-inputs/common/AdaptiveMode.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as t from \"react\";\nimport { ActionSheet as v, ActionSheetFooter as x } from \"@progress/kendo-react-layout\";\nimport { checkIcon as C } from \"@progress/kendo-svg-icons\";\nimport { Button as o } from \"@progress/kendo-react-buttons\";\nimport { useAdaptiveModeContext as g } from \"@progress/kendo-react-common\";\nconst E = (i) => {\n  const {\n    windowWidth: l = 0,\n    children: r,\n    navigatable: c,\n    navigatableElements: s,\n    expand: m,\n    animation: p,\n    title: d,\n    subTitle: f,\n    footer: e,\n    onClose: n\n  } = i, h = () => /* @__PURE__ */ t.createElement(\n    o,\n    {\n      tabIndex: 0,\n      \"aria-label\": \"Cancel\",\n      \"aria-disabled\": \"false\",\n      type: \"button\",\n      fillMode: \"flat\",\n      size: \"large\",\n      themeColor: \"primary\",\n      svgIcon: C,\n      onClick: n\n    }\n  ), a = g(), b = {\n    navigatable: c || !1,\n    navigatableElements: s || [],\n    expand: m,\n    animation: p,\n    suffixActions: h(),\n    onClose: n,\n    animationStyles: a && l <= a.small ? { top: 0, width: \"100%\", height: \"100%\" } : void 0,\n    title: d,\n    subTitle: f,\n    className: \"k-adaptive-actionsheet\",\n    position: a && l <= a.small ? \"fullscreen\" : void 0\n  };\n  return /* @__PURE__ */ t.createElement(v, { ...b }, r, /* @__PURE__ */ t.createElement(x, { className: \"k-actions k-actions-stretched\" }, /* @__PURE__ */ t.createElement(\n    o,\n    {\n      size: \"large\",\n      className: \"k-coloreditor-cancel\",\n      \"aria-label\": e.cancelText,\n      onClick: e.onCancel\n    },\n    e.cancelText\n  ), /* @__PURE__ */ t.createElement(\n    o,\n    {\n      themeColor: \"primary\",\n      size: \"large\",\n      className: \"k-coloreditor-apply\",\n      \"aria-label\": e.applyText,\n      onClick: e.onApply\n    },\n    e.applyText\n  )));\n};\nexport {\n  E as AdaptiveMode\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,SAASC,WAAW,IAAIC,CAAC,EAAEC,iBAAiB,IAAIC,CAAC,QAAQ,8BAA8B;AACvF,SAASC,SAAS,IAAIC,CAAC,QAAQ,2BAA2B;AAC1D,SAASC,MAAM,IAAIC,CAAC,QAAQ,+BAA+B;AAC3D,SAASC,sBAAsB,IAAIC,CAAC,QAAQ,8BAA8B;AAC1E,MAAMC,CAAC,GAAIC,CAAC,IAAK;EACf,MAAM;MACJC,WAAW,EAAEC,CAAC,GAAG,CAAC;MAClBC,QAAQ,EAAEC,CAAC;MACXC,WAAW,EAAEC,CAAC;MACdC,mBAAmB,EAAEC,CAAC;MACtBC,MAAM,EAAEC,CAAC;MACTC,SAAS,EAAEC,CAAC;MACZC,KAAK,EAAEC,CAAC;MACRC,QAAQ,EAAEC,CAAC;MACXC,MAAM,EAAEC,CAAC;MACTC,OAAO,EAAEC;IACX,CAAC,GAAGpB,CAAC;IAAEqB,CAAC,GAAGA,CAAA,KAAM,eAAgBjC,CAAC,CAACkC,aAAa,CAC9C1B,CAAC,EACD;MACE2B,QAAQ,EAAE,CAAC;MACX,YAAY,EAAE,QAAQ;MACtB,eAAe,EAAE,OAAO;MACxBC,IAAI,EAAE,QAAQ;MACdC,QAAQ,EAAE,MAAM;MAChBC,IAAI,EAAE,OAAO;MACbC,UAAU,EAAE,SAAS;MACrBC,OAAO,EAAElC,CAAC;MACVmC,OAAO,EAAET;IACX,CACF,CAAC;IAAEU,CAAC,GAAGhC,CAAC,CAAC,CAAC;IAAEiC,CAAC,GAAG;MACd1B,WAAW,EAAEC,CAAC,IAAI,CAAC,CAAC;MACpBC,mBAAmB,EAAEC,CAAC,IAAI,EAAE;MAC5BC,MAAM,EAAEC,CAAC;MACTC,SAAS,EAAEC,CAAC;MACZoB,aAAa,EAAEX,CAAC,CAAC,CAAC;MAClBF,OAAO,EAAEC,CAAC;MACVa,eAAe,EAAEH,CAAC,IAAI5B,CAAC,IAAI4B,CAAC,CAACI,KAAK,GAAG;QAAEC,GAAG,EAAE,CAAC;QAAEC,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAO,CAAC,GAAG,KAAK,CAAC;MACvFxB,KAAK,EAAEC,CAAC;MACRC,QAAQ,EAAEC,CAAC;MACXsB,SAAS,EAAE,wBAAwB;MACnCC,QAAQ,EAAET,CAAC,IAAI5B,CAAC,IAAI4B,CAAC,CAACI,KAAK,GAAG,YAAY,GAAG,KAAK;IACpD,CAAC;EACD,OAAO,eAAgB9C,CAAC,CAACkC,aAAa,CAAChC,CAAC,EAAE;IAAE,GAAGyC;EAAE,CAAC,EAAE3B,CAAC,EAAE,eAAgBhB,CAAC,CAACkC,aAAa,CAAC9B,CAAC,EAAE;IAAE8C,SAAS,EAAE;EAAgC,CAAC,EAAE,eAAgBlD,CAAC,CAACkC,aAAa,CACvK1B,CAAC,EACD;IACE8B,IAAI,EAAE,OAAO;IACbY,SAAS,EAAE,sBAAsB;IACjC,YAAY,EAAEpB,CAAC,CAACsB,UAAU;IAC1BX,OAAO,EAAEX,CAAC,CAACuB;EACb,CAAC,EACDvB,CAAC,CAACsB,UACJ,CAAC,EAAE,eAAgBpD,CAAC,CAACkC,aAAa,CAChC1B,CAAC,EACD;IACE+B,UAAU,EAAE,SAAS;IACrBD,IAAI,EAAE,OAAO;IACbY,SAAS,EAAE,qBAAqB;IAChC,YAAY,EAAEpB,CAAC,CAACwB,SAAS;IACzBb,OAAO,EAAEX,CAAC,CAACyB;EACb,CAAC,EACDzB,CAAC,CAACwB,SACJ,CAAC,CAAC,CAAC;AACL,CAAC;AACD,SACE3C,CAAC,IAAI6C,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}