{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst a = {};\nfunction r(c, o, e) {\n  a[c] = {\n    value: o,\n    hsva: e\n  };\n}\nfunction t(c, o, e) {\n  a[c] = {\n    value: o,\n    rgba: e\n  };\n}\nfunction n(c, o, e) {\n  a[c] = {\n    value: o,\n    hex: e\n  };\n}\nfunction h(c) {\n  delete a[c];\n}\nfunction i(c, o) {\n  if (c) {\n    const e = a[c];\n    return e && e.value === o ? e.hex : void 0;\n  }\n}\nfunction u(c, o) {\n  if (c) {\n    const e = a[c];\n    return e && e.value === o ? e.hsva : void 0;\n  }\n}\nfunction v(c, o) {\n  if (c) {\n    const e = a[c];\n    return e && e.value === o ? e.rgba : void 0;\n  }\n}\nexport { n as cacheHex, r as cacheHsva, t as cacheRgba, i as getCachedHex, u as getCachedHsva, v as getCachedRgba, h as removeCachedColor };", "map": {"version": 3, "names": ["a", "r", "c", "o", "e", "value", "hsva", "t", "rgba", "n", "hex", "h", "i", "u", "v", "cacheHex", "cacheHsva", "cacheRgba", "getCachedHex", "getCachedHsva", "getCachedRgba", "removeCachedColor"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-inputs/colors/utils/color-cache.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst a = {};\nfunction r(c, o, e) {\n  a[c] = { value: o, hsva: e };\n}\nfunction t(c, o, e) {\n  a[c] = { value: o, rgba: e };\n}\nfunction n(c, o, e) {\n  a[c] = { value: o, hex: e };\n}\nfunction h(c) {\n  delete a[c];\n}\nfunction i(c, o) {\n  if (c) {\n    const e = a[c];\n    return e && e.value === o ? e.hex : void 0;\n  }\n}\nfunction u(c, o) {\n  if (c) {\n    const e = a[c];\n    return e && e.value === o ? e.hsva : void 0;\n  }\n}\nfunction v(c, o) {\n  if (c) {\n    const e = a[c];\n    return e && e.value === o ? e.rgba : void 0;\n  }\n}\nexport {\n  n as cacheHex,\n  r as cacheHsva,\n  t as cacheRgba,\n  i as getCachedHex,\n  u as getCachedHsva,\n  v as getCachedRgba,\n  h as removeCachedColor\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAG,CAAC,CAAC;AACZ,SAASC,CAACA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAClBJ,CAAC,CAACE,CAAC,CAAC,GAAG;IAAEG,KAAK,EAAEF,CAAC;IAAEG,IAAI,EAAEF;EAAE,CAAC;AAC9B;AACA,SAASG,CAACA,CAACL,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAClBJ,CAAC,CAACE,CAAC,CAAC,GAAG;IAAEG,KAAK,EAAEF,CAAC;IAAEK,IAAI,EAAEJ;EAAE,CAAC;AAC9B;AACA,SAASK,CAACA,CAACP,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAClBJ,CAAC,CAACE,CAAC,CAAC,GAAG;IAAEG,KAAK,EAAEF,CAAC;IAAEO,GAAG,EAAEN;EAAE,CAAC;AAC7B;AACA,SAASO,CAACA,CAACT,CAAC,EAAE;EACZ,OAAOF,CAAC,CAACE,CAAC,CAAC;AACb;AACA,SAASU,CAACA,CAACV,CAAC,EAAEC,CAAC,EAAE;EACf,IAAID,CAAC,EAAE;IACL,MAAME,CAAC,GAAGJ,CAAC,CAACE,CAAC,CAAC;IACd,OAAOE,CAAC,IAAIA,CAAC,CAACC,KAAK,KAAKF,CAAC,GAAGC,CAAC,CAACM,GAAG,GAAG,KAAK,CAAC;EAC5C;AACF;AACA,SAASG,CAACA,CAACX,CAAC,EAAEC,CAAC,EAAE;EACf,IAAID,CAAC,EAAE;IACL,MAAME,CAAC,GAAGJ,CAAC,CAACE,CAAC,CAAC;IACd,OAAOE,CAAC,IAAIA,CAAC,CAACC,KAAK,KAAKF,CAAC,GAAGC,CAAC,CAACE,IAAI,GAAG,KAAK,CAAC;EAC7C;AACF;AACA,SAASQ,CAACA,CAACZ,CAAC,EAAEC,CAAC,EAAE;EACf,IAAID,CAAC,EAAE;IACL,MAAME,CAAC,GAAGJ,CAAC,CAACE,CAAC,CAAC;IACd,OAAOE,CAAC,IAAIA,CAAC,CAACC,KAAK,KAAKF,CAAC,GAAGC,CAAC,CAACI,IAAI,GAAG,KAAK,CAAC;EAC7C;AACF;AACA,SACEC,CAAC,IAAIM,QAAQ,EACbd,CAAC,IAAIe,SAAS,EACdT,CAAC,IAAIU,SAAS,EACdL,CAAC,IAAIM,YAAY,EACjBL,CAAC,IAAIM,aAAa,EAClBL,CAAC,IAAIM,aAAa,EAClBT,CAAC,IAAIU,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}