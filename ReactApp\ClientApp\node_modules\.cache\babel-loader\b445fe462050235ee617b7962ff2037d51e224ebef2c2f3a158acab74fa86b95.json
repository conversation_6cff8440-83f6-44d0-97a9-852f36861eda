{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\nimport constants from './utils/Constants';\nimport { apiSlice as tncApiSlice } from '../app/api/tncApiSlice';\nconst defaultSettings = {\n  urlFileUpload: false,\n  internalFileStatusUpdate: false,\n  internalFilesEmail: false,\n  internalFileSetAssignee: false,\n  internalFileSetProject: false,\n  internalFilesDownload: false,\n  internalFilesCheckinCheckout: false,\n  internalFilesRecategorize: false,\n  fileAreaFolderStructure: false,\n  fileAreaManageEmailSubscription: false,\n  fileAreaManageTags: false,\n  internalFilesAssign: false,\n  internalFilesCopy: false,\n  internalFilesDelete: false,\n  internalFilesHistory: false,\n  internalFilesMove: false,\n  internalFilesRename: false,\n  internalFilesUpdate: false,\n  internalFilesUpload: false,\n  internalFilesView: false,\n  internalFilesViewAssignmentHistory: false,\n  internalFilesViewCheckoutHistory: false,\n  internalFilesViewProperties: false,\n  internalFilesViewVersionHistory: false,\n  portalFilesView: false,\n  internalFilesPublishUnPublish: false\n};\nexport const initialState = {\n  endPoints: null,\n  tenant: localStorage.getItem(constants.tenatKey),\n  user: null,\n  menuItems: [],\n  selectedSite: 'N/A',\n  publishedFileCount: undefined,\n  dynamicBreadcrumbValues: [],\n  fileAreaSettings: defaultSettings,\n  apiError: undefined,\n  menuApiError: undefined,\n  authUrl: undefined,\n  tenantContext: null,\n  tnc: {\n    isValidated: false,\n    document: null\n  }\n};\nexport const appSlice = createSlice({\n  name: 'app',\n  initialState,\n  reducers: {\n    setTenant: (state, action) => {\n      if (action.payload) localStorage.setItem(constants.tenatKey, action.payload);else localStorage.removeItem(constants.tenatKey);\n      state.endPoints = null;\n      state.tenant = action.payload;\n    },\n    setEndpoints: (state, action) => {\n      state.endPoints = action.payload;\n    },\n    setUser: (state, action) => {\n      state.user = action.payload;\n    },\n    setSelectedSite: (state, action) => {\n      state.selectedSite = action.payload;\n    },\n    setPublishedFileCount: (state, action) => {\n      state.publishedFileCount = action.payload;\n    },\n    setMenu: (state, action) => {\n      state.menuItems = action.payload;\n    },\n    setMenuApiError: (state, action) => {\n      state.menuApiError = action.payload;\n    },\n    setApiError: (state, action) => {\n      state.apiError = action.payload;\n    },\n    setTenantContext: (state, action) => {\n      state.tenantContext = action.payload;\n    }\n  },\n  extraReducers: builder => {\n    builder.addMatcher(tncApiSlice.endpoints.evaluateTnc.matchFulfilled, handleEvaluateTncSuccess).addMatcher(tncApiSlice.endpoints.acceptTnc.matchFulfilled, handleAcceptTncSuccess);\n  }\n});\nconst handleEvaluateTncSuccess = (state, action) => {\n  if (!action.payload.show) state.tnc.isValidated = true;\n  const payload = {\n    termsAndConditionsId: action.payload.termsAndConditionsId,\n    statementOfAgreement: action.payload.statementOfAgreement,\n    ...action.payload.file\n  };\n  state.tnc.document = action.payload.show ? payload : null;\n};\nconst handleAcceptTncSuccess = state => {\n  state.tnc.isValidated = true;\n  state.tnc.document = null;\n};\nexport const {\n  setEndpoints,\n  setTenant,\n  setUser,\n  setMenu,\n  setSelectedSite,\n  setPublishedFileCount,\n  setApiError,\n  setMenuApiError,\n  setTenantContext\n} = appSlice.actions;\nexport const selectAppState = state => state.app;\nexport const selectAppInitializing = state => {\n  return {\n    ...state.app,\n    isInitializing: !(state.app.tenant && state.app.tenantContext && state.app.endPoints && state.app.menuItems.length && state.app.selectedSite && !state.app.apiError)\n  };\n};\nexport default appSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "constants", "apiSlice", "tncApiSlice", "defaultSettings", "urlFileUpload", "internalFileStatusUpdate", "internalFilesEmail", "internalFileSetAssignee", "internalFileSetProject", "internalFilesDownload", "internalFilesCheckinCheckout", "internalFilesRecategorize", "fileAreaFolderStructure", "fileAreaManageEmailSubscription", "fileAreaManageTags", "internalFilesAssign", "internalFilesCopy", "internalFilesDelete", "internalFilesHistory", "internalFilesMove", "internalFilesRename", "internalFilesUpdate", "internalFilesUpload", "internalFilesView", "internalFilesViewAssignmentHistory", "internalFilesViewCheckoutHistory", "internalFilesViewProperties", "internalFilesViewVersionHistory", "portalFilesView", "internalFilesPublishUnPublish", "initialState", "endPoints", "tenant", "localStorage", "getItem", "tenatKey", "user", "menuItems", "selectedSite", "publishedFileCount", "undefined", "dynamicBreadcrumbValues", "fileAreaSettings", "apiError", "menuApiError", "authUrl", "tenantContext", "tnc", "isValidated", "document", "appSlice", "name", "reducers", "<PERSON><PERSON><PERSON><PERSON>", "state", "action", "payload", "setItem", "removeItem", "setEndpoints", "setUser", "setSelectedSite", "setPublishedFileCount", "setMenu", "setMenuApiError", "setApiError", "setTenantContext", "extraReducers", "builder", "addMatcher", "endpoints", "evaluateTnc", "matchFulfilled", "handleEvaluateTncSuccess", "acceptTnc", "handleAcceptTncSuccess", "show", "termsAndConditionsId", "statementOfAgreement", "file", "actions", "selectAppState", "app", "selectAppInitializing", "isInitializing", "length", "reducer"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/src/app/appSlice.ts"], "sourcesContent": ["import { FileAreaSettings } from '@app/types/fileAreaTypes';\r\nimport MenuItem from './types/MenuItemType';\r\nimport { WEBEndpointType } from '@iris/discovery.fe.client';\r\nimport { CustomUserClaims, UserClaims } from '@okta/okta-auth-js';\r\nimport { createSlice, PayloadAction } from '@reduxjs/toolkit';\r\nimport constants from './utils/Constants';\r\nimport { RootState } from './store/store';\r\nimport { EvaluateTncResponse, TncDocument, TncDocumentState } from './types/tncTypes';\r\nimport { apiSlice as tncApiSlice } from '../app/api/tncApiSlice';\r\n\r\nexport interface IAppState {\r\n  endPoints: WEBEndpointType[] | null;\r\n  tenant: string | null;\r\n  user: null | UserClaims<CustomUserClaims>;\r\n  selectedSite: string;\r\n  publishedFileCount: number | undefined;\r\n  menuItems: MenuItem[] | [];\r\n  dynamicBreadcrumbValues: [];\r\n  fileAreaSettings: FileAreaSettings;\r\n  apiError: string | undefined;\r\n  menuApiError: string | undefined;\r\n  authUrl: string | undefined;\r\n  tenantContext: any | null;\r\n  tnc: TncDocumentState;\r\n}\r\n\r\nconst defaultSettings: FileAreaSettings = {\r\n  urlFileUpload: false,\r\n  internalFileStatusUpdate: false,\r\n  internalFilesEmail: false,\r\n  internalFileSetAssignee: false,\r\n  internalFileSetProject: false,\r\n  internalFilesDownload: false,\r\n  internalFilesCheckinCheckout: false,\r\n  internalFilesRecategorize: false,\r\n  fileAreaFolderStructure: false,\r\n  fileAreaManageEmailSubscription: false,\r\n  fileAreaManageTags: false,\r\n  internalFilesAssign: false,\r\n  internalFilesCopy: false,\r\n  internalFilesDelete: false,\r\n  internalFilesHistory: false,\r\n  internalFilesMove: false,\r\n  internalFilesRename: false,\r\n  internalFilesUpdate: false,\r\n  internalFilesUpload: false,\r\n  internalFilesView: false,\r\n  internalFilesViewAssignmentHistory: false,\r\n  internalFilesViewCheckoutHistory: false,\r\n  internalFilesViewProperties: false,\r\n  internalFilesViewVersionHistory: false,\r\n  portalFilesView: false,\r\n  internalFilesPublishUnPublish: false,\r\n};\r\n\r\nexport const initialState: IAppState = {\r\n  endPoints: null,\r\n  tenant: localStorage.getItem(constants.tenatKey),\r\n  user: null,\r\n  menuItems: [],\r\n  selectedSite: 'N/A',\r\n  publishedFileCount: undefined,\r\n  dynamicBreadcrumbValues: [],\r\n  fileAreaSettings: defaultSettings,\r\n  apiError: undefined,\r\n  menuApiError: undefined,\r\n  authUrl: undefined,\r\n  tenantContext: null,\r\n  tnc: {\r\n    isValidated: false,\r\n    document: null,\r\n  }\r\n};\r\n\r\nexport type IAppInitializing = IAppState & { isInitializing: boolean };\r\n\r\nexport const appSlice = createSlice({\r\n  name: 'app',\r\n  initialState,\r\n  reducers: {\r\n    setTenant: (state: IAppState, action: PayloadAction<string | null>) => {\r\n      if (action.payload) localStorage.setItem(constants.tenatKey, action.payload);\r\n      else localStorage.removeItem(constants.tenatKey);\r\n      state.endPoints = null;\r\n      state.tenant = action.payload;\r\n    },\r\n    setEndpoints: (state: IAppState, action: PayloadAction<WEBEndpointType[]>) => {\r\n      state.endPoints = action.payload;\r\n    },\r\n    setUser: (state: IAppState, action: PayloadAction<UserClaims<CustomUserClaims>>) => {\r\n      state.user = action.payload;\r\n    },\r\n    setSelectedSite: (state: IAppState, action: PayloadAction<string>) => {\r\n      state.selectedSite = action.payload;\r\n    },\r\n    setPublishedFileCount: (state: IAppState, action: PayloadAction<number | undefined>) => {\r\n      state.publishedFileCount = action.payload;\r\n    },\r\n    setMenu: (state: IAppState, action: PayloadAction<MenuItem[]>) => {\r\n      state.menuItems = action.payload;\r\n    },\r\n    setMenuApiError: (state: IAppState, action: PayloadAction<string | undefined>) => {\r\n      state.menuApiError = action.payload;\r\n    },\r\n    setApiError: (state: IAppState, action: PayloadAction<string | undefined>) => {\r\n      state.apiError = action.payload;\r\n    },\r\n    setTenantContext: (state: any, action: PayloadAction<string>) => {\r\n      state.tenantContext = action.payload;\r\n    },\r\n  },\r\n  extraReducers: (builder) => {\r\n    builder\r\n      .addMatcher(tncApiSlice.endpoints.evaluateTnc.matchFulfilled, handleEvaluateTncSuccess)\r\n      .addMatcher(tncApiSlice.endpoints.acceptTnc.matchFulfilled, handleAcceptTncSuccess);\r\n  },\r\n});\r\n\r\nconst handleEvaluateTncSuccess = (state: IAppState, action: PayloadAction<EvaluateTncResponse>) => {\r\n  if (!action.payload.show) state.tnc.isValidated = true;\r\n  const payload = {\r\n    termsAndConditionsId: action.payload.termsAndConditionsId,\r\n    statementOfAgreement: action.payload.statementOfAgreement,\r\n    ...action.payload.file! \r\n  }\r\n  state.tnc.document = action.payload.show ? payload : null;\r\n};\r\n\r\nconst handleAcceptTncSuccess = (state: IAppState) => {\r\n  state.tnc.isValidated = true;\r\n  state.tnc.document = null;\r\n};\r\n\r\nexport const { setEndpoints, setTenant, setUser, setMenu, setSelectedSite, setPublishedFileCount, setApiError, setMenuApiError, setTenantContext } = appSlice.actions;\r\nexport const selectAppState = (state: RootState): IAppState => state.app;\r\nexport const selectAppInitializing = (state: RootState): IAppInitializing => {\r\n  return {\r\n    ...state.app,\r\n    isInitializing: !(state.app.tenant && state.app.tenantContext && state.app.endPoints && state.app.menuItems.length && state.app.selectedSite && !state.app.apiError),\r\n  };\r\n};\r\n\r\nexport default appSlice.reducer;\r\n"], "mappings": "AAIA,SAASA,WAAW,QAAuB,kBAAkB;AAC7D,OAAOC,SAAS,MAAM,mBAAmB;AAGzC,SAASC,QAAQ,IAAIC,WAAW,QAAQ,wBAAwB;AAkBhE,MAAMC,eAAiC,GAAG;EACxCC,aAAa,EAAE,KAAK;EACpBC,wBAAwB,EAAE,KAAK;EAC/BC,kBAAkB,EAAE,KAAK;EACzBC,uBAAuB,EAAE,KAAK;EAC9BC,sBAAsB,EAAE,KAAK;EAC7BC,qBAAqB,EAAE,KAAK;EAC5BC,4BAA4B,EAAE,KAAK;EACnCC,yBAAyB,EAAE,KAAK;EAChCC,uBAAuB,EAAE,KAAK;EAC9BC,+BAA+B,EAAE,KAAK;EACtCC,kBAAkB,EAAE,KAAK;EACzBC,mBAAmB,EAAE,KAAK;EAC1BC,iBAAiB,EAAE,KAAK;EACxBC,mBAAmB,EAAE,KAAK;EAC1BC,oBAAoB,EAAE,KAAK;EAC3BC,iBAAiB,EAAE,KAAK;EACxBC,mBAAmB,EAAE,KAAK;EAC1BC,mBAAmB,EAAE,KAAK;EAC1BC,mBAAmB,EAAE,KAAK;EAC1BC,iBAAiB,EAAE,KAAK;EACxBC,kCAAkC,EAAE,KAAK;EACzCC,gCAAgC,EAAE,KAAK;EACvCC,2BAA2B,EAAE,KAAK;EAClCC,+BAA+B,EAAE,KAAK;EACtCC,eAAe,EAAE,KAAK;EACtBC,6BAA6B,EAAE;AACjC,CAAC;AAED,OAAO,MAAMC,YAAuB,GAAG;EACrCC,SAAS,EAAE,IAAI;EACfC,MAAM,EAAEC,YAAY,CAACC,OAAO,CAAClC,SAAS,CAACmC,QAAQ,CAAC;EAChDC,IAAI,EAAE,IAAI;EACVC,SAAS,EAAE,EAAE;EACbC,YAAY,EAAE,KAAK;EACnBC,kBAAkB,EAAEC,SAAS;EAC7BC,uBAAuB,EAAE,EAAE;EAC3BC,gBAAgB,EAAEvC,eAAe;EACjCwC,QAAQ,EAAEH,SAAS;EACnBI,YAAY,EAAEJ,SAAS;EACvBK,OAAO,EAAEL,SAAS;EAClBM,aAAa,EAAE,IAAI;EACnBC,GAAG,EAAE;IACHC,WAAW,EAAE,KAAK;IAClBC,QAAQ,EAAE;EACZ;AACF,CAAC;AAID,OAAO,MAAMC,QAAQ,GAAGnD,WAAW,CAAC;EAClCoD,IAAI,EAAE,KAAK;EACXrB,YAAY;EACZsB,QAAQ,EAAE;IACRC,SAAS,EAAEA,CAACC,KAAgB,EAAEC,MAAoC,KAAK;MACrE,IAAIA,MAAM,CAACC,OAAO,EAAEvB,YAAY,CAACwB,OAAO,CAACzD,SAAS,CAACmC,QAAQ,EAAEoB,MAAM,CAACC,OAAO,CAAC,CAAC,KACxEvB,YAAY,CAACyB,UAAU,CAAC1D,SAAS,CAACmC,QAAQ,CAAC;MAChDmB,KAAK,CAACvB,SAAS,GAAG,IAAI;MACtBuB,KAAK,CAACtB,MAAM,GAAGuB,MAAM,CAACC,OAAO;IAC/B,CAAC;IACDG,YAAY,EAAEA,CAACL,KAAgB,EAAEC,MAAwC,KAAK;MAC5ED,KAAK,CAACvB,SAAS,GAAGwB,MAAM,CAACC,OAAO;IAClC,CAAC;IACDI,OAAO,EAAEA,CAACN,KAAgB,EAAEC,MAAmD,KAAK;MAClFD,KAAK,CAAClB,IAAI,GAAGmB,MAAM,CAACC,OAAO;IAC7B,CAAC;IACDK,eAAe,EAAEA,CAACP,KAAgB,EAAEC,MAA6B,KAAK;MACpED,KAAK,CAAChB,YAAY,GAAGiB,MAAM,CAACC,OAAO;IACrC,CAAC;IACDM,qBAAqB,EAAEA,CAACR,KAAgB,EAAEC,MAAyC,KAAK;MACtFD,KAAK,CAACf,kBAAkB,GAAGgB,MAAM,CAACC,OAAO;IAC3C,CAAC;IACDO,OAAO,EAAEA,CAACT,KAAgB,EAAEC,MAAiC,KAAK;MAChED,KAAK,CAACjB,SAAS,GAAGkB,MAAM,CAACC,OAAO;IAClC,CAAC;IACDQ,eAAe,EAAEA,CAACV,KAAgB,EAAEC,MAAyC,KAAK;MAChFD,KAAK,CAACV,YAAY,GAAGW,MAAM,CAACC,OAAO;IACrC,CAAC;IACDS,WAAW,EAAEA,CAACX,KAAgB,EAAEC,MAAyC,KAAK;MAC5ED,KAAK,CAACX,QAAQ,GAAGY,MAAM,CAACC,OAAO;IACjC,CAAC;IACDU,gBAAgB,EAAEA,CAACZ,KAAU,EAAEC,MAA6B,KAAK;MAC/DD,KAAK,CAACR,aAAa,GAAGS,MAAM,CAACC,OAAO;IACtC;EACF,CAAC;EACDW,aAAa,EAAGC,OAAO,IAAK;IAC1BA,OAAO,CACJC,UAAU,CAACnE,WAAW,CAACoE,SAAS,CAACC,WAAW,CAACC,cAAc,EAAEC,wBAAwB,CAAC,CACtFJ,UAAU,CAACnE,WAAW,CAACoE,SAAS,CAACI,SAAS,CAACF,cAAc,EAAEG,sBAAsB,CAAC;EACvF;AACF,CAAC,CAAC;AAEF,MAAMF,wBAAwB,GAAGA,CAACnB,KAAgB,EAAEC,MAA0C,KAAK;EACjG,IAAI,CAACA,MAAM,CAACC,OAAO,CAACoB,IAAI,EAAEtB,KAAK,CAACP,GAAG,CAACC,WAAW,GAAG,IAAI;EACtD,MAAMQ,OAAO,GAAG;IACdqB,oBAAoB,EAAEtB,MAAM,CAACC,OAAO,CAACqB,oBAAoB;IACzDC,oBAAoB,EAAEvB,MAAM,CAACC,OAAO,CAACsB,oBAAoB;IACzD,GAAGvB,MAAM,CAACC,OAAO,CAACuB;EACpB,CAAC;EACDzB,KAAK,CAACP,GAAG,CAACE,QAAQ,GAAGM,MAAM,CAACC,OAAO,CAACoB,IAAI,GAAGpB,OAAO,GAAG,IAAI;AAC3D,CAAC;AAED,MAAMmB,sBAAsB,GAAIrB,KAAgB,IAAK;EACnDA,KAAK,CAACP,GAAG,CAACC,WAAW,GAAG,IAAI;EAC5BM,KAAK,CAACP,GAAG,CAACE,QAAQ,GAAG,IAAI;AAC3B,CAAC;AAED,OAAO,MAAM;EAAEU,YAAY;EAAEN,SAAS;EAAEO,OAAO;EAAEG,OAAO;EAAEF,eAAe;EAAEC,qBAAqB;EAAEG,WAAW;EAAED,eAAe;EAAEE;AAAiB,CAAC,GAAGhB,QAAQ,CAAC8B,OAAO;AACrK,OAAO,MAAMC,cAAc,GAAI3B,KAAgB,IAAgBA,KAAK,CAAC4B,GAAG;AACxE,OAAO,MAAMC,qBAAqB,GAAI7B,KAAgB,IAAuB;EAC3E,OAAO;IACL,GAAGA,KAAK,CAAC4B,GAAG;IACZE,cAAc,EAAE,EAAE9B,KAAK,CAAC4B,GAAG,CAAClD,MAAM,IAAIsB,KAAK,CAAC4B,GAAG,CAACpC,aAAa,IAAIQ,KAAK,CAAC4B,GAAG,CAACnD,SAAS,IAAIuB,KAAK,CAAC4B,GAAG,CAAC7C,SAAS,CAACgD,MAAM,IAAI/B,KAAK,CAAC4B,GAAG,CAAC5C,YAAY,IAAI,CAACgB,KAAK,CAAC4B,GAAG,CAACvC,QAAQ;EACrK,CAAC;AACH,CAAC;AAED,eAAeO,QAAQ,CAACoC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}