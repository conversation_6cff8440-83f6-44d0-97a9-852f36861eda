{"ast": null, "code": "import { isCompositeFilterDescriptor } from \"./filter-descriptor.interface\";\nimport { getter } from \"../accessor\";\nimport { isFunction, isPresent, isDate, isString, isBlank } from \"../utils\";\nvar logic = {\n  \"or\": {\n    concat: function (acc, fn) {\n      return function (a) {\n        return acc(a) || fn(a);\n      };\n    },\n    identity: function () {\n      return false;\n    }\n  },\n  \"and\": {\n    concat: function (acc, fn) {\n      return function (a) {\n        return acc(a) && fn(a);\n      };\n    },\n    identity: function () {\n      return true;\n    }\n  }\n};\nvar operatorsMap = {\n  contains: function (a, b) {\n    return (a || \"\").indexOf(b) >= 0;\n  },\n  doesnotcontain: function (a, b) {\n    return (a || \"\").indexOf(b) === -1;\n  },\n  doesnotendwith: function (a, b) {\n    return (a || \"\").indexOf(b, (a || \"\").length - (b || \"\").length) < 0;\n  },\n  doesnotstartwith: function (a, b) {\n    return (a || \"\").lastIndexOf(b, 0) === -1;\n  },\n  endswith: function (a, b) {\n    return (a || \"\").indexOf(b, (a || \"\").length - (b || \"\").length) >= 0;\n  },\n  eq: function (a, b) {\n    return a === b;\n  },\n  gt: function (a, b) {\n    return a > b;\n  },\n  gte: function (a, b) {\n    return a >= b;\n  },\n  isempty: function (a) {\n    return a === '';\n  },\n  isnotempty: function (a) {\n    return a !== '';\n  },\n  isnotnull: function (a) {\n    return isPresent(a);\n  },\n  isnull: function (a) {\n    return isBlank(a);\n  },\n  lt: function (a, b) {\n    return a < b;\n  },\n  lte: function (a, b) {\n    return a <= b;\n  },\n  neq: function (a, b) {\n    return a != b;\n  },\n  startswith: function (a, b) {\n    return (a || \"\").lastIndexOf(b, 0) === 0;\n  }\n};\nvar dateRegExp = /^\\/Date\\((.*?)\\)\\/$/;\nvar convertValue = function (value, ignoreCase) {\n  if (value != null && isString(value)) {\n    var date = dateRegExp.exec(value);\n    if (date) {\n      return new Date(+date[1]).getTime();\n    } else if (ignoreCase) {\n      return value.toLowerCase();\n    }\n  } else if (value != null && isDate(value)) {\n    return value.getTime();\n  }\n  return value;\n};\nvar typedGetter = function (prop, value, ignoreCase) {\n  if (!isPresent(value)) {\n    return prop;\n  }\n  var acc = prop;\n  if (isString(value)) {\n    var date = dateRegExp.exec(value);\n    if (date) {\n      value = new Date(+date[1]);\n    } else {\n      acc = function (a) {\n        var x = prop(a);\n        if (x === null) {\n          return x;\n        }\n        var stringValue = typeof x === 'string' ? x : x + \"\";\n        return ignoreCase ? stringValue.toLowerCase() : stringValue;\n      };\n    }\n  }\n  if (isDate(value)) {\n    return function (a) {\n      var x = acc(a);\n      return isDate(x) ? x.getTime() : x;\n    };\n  }\n  return acc;\n};\nvar transformFilter = function (_a) {\n  var field = _a.field,\n    ignoreCase = _a.ignoreCase,\n    value = _a.value,\n    operator = _a.operator;\n  field = !isPresent(field) ? function (a) {\n    return a;\n  } : field;\n  ignoreCase = isPresent(ignoreCase) ? ignoreCase : true;\n  var itemProp = typedGetter(isFunction(field) ? field : getter(field, true), value, ignoreCase);\n  value = convertValue(value, ignoreCase);\n  var op = isFunction(operator) ? operator : operatorsMap[operator];\n  return function (a) {\n    return op(itemProp(a), value, ignoreCase);\n  };\n};\n/**\n * @hidden\n */\nexport var transformCompositeFilter = function (filter) {\n  var combiner = logic[filter.logic];\n  return filter.filters.filter(isPresent).map(function (x) {\n    return isCompositeFilterDescriptor(x) ? transformCompositeFilter(x) : transformFilter(x);\n  }).reduce(combiner.concat, combiner.identity);\n};", "map": {"version": 3, "names": ["isCompositeFilterDescriptor", "getter", "isFunction", "isPresent", "isDate", "isString", "isBlank", "logic", "concat", "acc", "fn", "a", "identity", "operatorsMap", "contains", "b", "indexOf", "doesnotcontain", "doesnotendwith", "length", "doesnotstartwith", "lastIndexOf", "endswith", "eq", "gt", "gte", "isempty", "isnotempty", "isnotnull", "isnull", "lt", "lte", "neq", "startswith", "dateRegExp", "convertValue", "value", "ignoreCase", "date", "exec", "Date", "getTime", "toLowerCase", "typedGetter", "prop", "x", "stringValue", "transformFilter", "_a", "field", "operator", "itemProp", "op", "transformCompositeFilter", "filter", "combiner", "filters", "map", "reduce"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-data-query/dist/es/filtering/filter-no-eval.js"], "sourcesContent": ["import { isCompositeFilterDescriptor } from \"./filter-descriptor.interface\";\nimport { getter } from \"../accessor\";\nimport { isFunction, isPresent, isDate, isString, isBlank } from \"../utils\";\nvar logic = {\n    \"or\": {\n        concat: function (acc, fn) { return function (a) { return acc(a) || fn(a); }; },\n        identity: function () { return false; }\n    },\n    \"and\": {\n        concat: function (acc, fn) { return function (a) { return acc(a) && fn(a); }; },\n        identity: function () { return true; }\n    }\n};\nvar operatorsMap = {\n    contains: function (a, b) { return (a || \"\").indexOf(b) >= 0; },\n    doesnotcontain: function (a, b) { return (a || \"\").indexOf(b) === -1; },\n    doesnotendwith: function (a, b) { return (a || \"\").indexOf(b, (a || \"\").length - (b || \"\").length) < 0; },\n    doesnotstartwith: function (a, b) { return (a || \"\").lastIndexOf(b, 0) === -1; },\n    endswith: function (a, b) { return (a || \"\").indexOf(b, (a || \"\").length - (b || \"\").length) >= 0; },\n    eq: function (a, b) { return a === b; },\n    gt: function (a, b) { return a > b; },\n    gte: function (a, b) { return a >= b; },\n    isempty: function (a) { return a === ''; },\n    isnotempty: function (a) { return a !== ''; },\n    isnotnull: function (a) { return isPresent(a); },\n    isnull: function (a) { return isBlank(a); },\n    lt: function (a, b) { return a < b; },\n    lte: function (a, b) { return a <= b; },\n    neq: function (a, b) { return a != b; },\n    startswith: function (a, b) { return (a || \"\").lastIndexOf(b, 0) === 0; }\n};\nvar dateRegExp = /^\\/Date\\((.*?)\\)\\/$/;\nvar convertValue = function (value, ignoreCase) {\n    if (value != null && isString(value)) {\n        var date = dateRegExp.exec(value);\n        if (date) {\n            return new Date(+date[1]).getTime();\n        }\n        else if (ignoreCase) {\n            return value.toLowerCase();\n        }\n    }\n    else if (value != null && isDate(value)) {\n        return value.getTime();\n    }\n    return value;\n};\nvar typedGetter = function (prop, value, ignoreCase) {\n    if (!isPresent(value)) {\n        return prop;\n    }\n    var acc = prop;\n    if (isString(value)) {\n        var date = dateRegExp.exec(value);\n        if (date) {\n            value = new Date(+date[1]);\n        }\n        else {\n            acc = function (a) {\n                var x = prop(a);\n                if (x === null) {\n                    return x;\n                }\n                var stringValue = typeof x === 'string' ? x : x + \"\";\n                return ignoreCase ? stringValue.toLowerCase() : stringValue;\n            };\n        }\n    }\n    if (isDate(value)) {\n        return function (a) {\n            var x = acc(a);\n            return isDate(x) ? x.getTime() : x;\n        };\n    }\n    return acc;\n};\nvar transformFilter = function (_a) {\n    var field = _a.field, ignoreCase = _a.ignoreCase, value = _a.value, operator = _a.operator;\n    field = !isPresent(field) ? function (a) { return a; } : field;\n    ignoreCase = isPresent(ignoreCase) ? ignoreCase : true;\n    var itemProp = typedGetter(isFunction(field) ? field : getter(field, true), value, ignoreCase);\n    value = convertValue(value, ignoreCase);\n    var op = isFunction(operator) ? operator : operatorsMap[operator];\n    return function (a) { return op(itemProp(a), value, ignoreCase); };\n};\n/**\n * @hidden\n */\nexport var transformCompositeFilter = function (filter) {\n    var combiner = logic[filter.logic];\n    return filter.filters\n        .filter(isPresent)\n        .map(function (x) { return isCompositeFilterDescriptor(x) ? transformCompositeFilter(x) : transformFilter(x); })\n        .reduce(combiner.concat, combiner.identity);\n};\n"], "mappings": "AAAA,SAASA,2BAA2B,QAAQ,+BAA+B;AAC3E,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,QAAQ,UAAU;AAC3E,IAAIC,KAAK,GAAG;EACR,IAAI,EAAE;IACFC,MAAM,EAAE,SAAAA,CAAUC,GAAG,EAAEC,EAAE,EAAE;MAAE,OAAO,UAAUC,CAAC,EAAE;QAAE,OAAOF,GAAG,CAACE,CAAC,CAAC,IAAID,EAAE,CAACC,CAAC,CAAC;MAAE,CAAC;IAAE,CAAC;IAC/EC,QAAQ,EAAE,SAAAA,CAAA,EAAY;MAAE,OAAO,KAAK;IAAE;EAC1C,CAAC;EACD,KAAK,EAAE;IACHJ,MAAM,EAAE,SAAAA,CAAUC,GAAG,EAAEC,EAAE,EAAE;MAAE,OAAO,UAAUC,CAAC,EAAE;QAAE,OAAOF,GAAG,CAACE,CAAC,CAAC,IAAID,EAAE,CAACC,CAAC,CAAC;MAAE,CAAC;IAAE,CAAC;IAC/EC,QAAQ,EAAE,SAAAA,CAAA,EAAY;MAAE,OAAO,IAAI;IAAE;EACzC;AACJ,CAAC;AACD,IAAIC,YAAY,GAAG;EACfC,QAAQ,EAAE,SAAAA,CAAUH,CAAC,EAAEI,CAAC,EAAE;IAAE,OAAO,CAACJ,CAAC,IAAI,EAAE,EAAEK,OAAO,CAACD,CAAC,CAAC,IAAI,CAAC;EAAE,CAAC;EAC/DE,cAAc,EAAE,SAAAA,CAAUN,CAAC,EAAEI,CAAC,EAAE;IAAE,OAAO,CAACJ,CAAC,IAAI,EAAE,EAAEK,OAAO,CAACD,CAAC,CAAC,KAAK,CAAC,CAAC;EAAE,CAAC;EACvEG,cAAc,EAAE,SAAAA,CAAUP,CAAC,EAAEI,CAAC,EAAE;IAAE,OAAO,CAACJ,CAAC,IAAI,EAAE,EAAEK,OAAO,CAACD,CAAC,EAAE,CAACJ,CAAC,IAAI,EAAE,EAAEQ,MAAM,GAAG,CAACJ,CAAC,IAAI,EAAE,EAAEI,MAAM,CAAC,GAAG,CAAC;EAAE,CAAC;EACzGC,gBAAgB,EAAE,SAAAA,CAAUT,CAAC,EAAEI,CAAC,EAAE;IAAE,OAAO,CAACJ,CAAC,IAAI,EAAE,EAAEU,WAAW,CAACN,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;EAAE,CAAC;EAChFO,QAAQ,EAAE,SAAAA,CAAUX,CAAC,EAAEI,CAAC,EAAE;IAAE,OAAO,CAACJ,CAAC,IAAI,EAAE,EAAEK,OAAO,CAACD,CAAC,EAAE,CAACJ,CAAC,IAAI,EAAE,EAAEQ,MAAM,GAAG,CAACJ,CAAC,IAAI,EAAE,EAAEI,MAAM,CAAC,IAAI,CAAC;EAAE,CAAC;EACpGI,EAAE,EAAE,SAAAA,CAAUZ,CAAC,EAAEI,CAAC,EAAE;IAAE,OAAOJ,CAAC,KAAKI,CAAC;EAAE,CAAC;EACvCS,EAAE,EAAE,SAAAA,CAAUb,CAAC,EAAEI,CAAC,EAAE;IAAE,OAAOJ,CAAC,GAAGI,CAAC;EAAE,CAAC;EACrCU,GAAG,EAAE,SAAAA,CAAUd,CAAC,EAAEI,CAAC,EAAE;IAAE,OAAOJ,CAAC,IAAII,CAAC;EAAE,CAAC;EACvCW,OAAO,EAAE,SAAAA,CAAUf,CAAC,EAAE;IAAE,OAAOA,CAAC,KAAK,EAAE;EAAE,CAAC;EAC1CgB,UAAU,EAAE,SAAAA,CAAUhB,CAAC,EAAE;IAAE,OAAOA,CAAC,KAAK,EAAE;EAAE,CAAC;EAC7CiB,SAAS,EAAE,SAAAA,CAAUjB,CAAC,EAAE;IAAE,OAAOR,SAAS,CAACQ,CAAC,CAAC;EAAE,CAAC;EAChDkB,MAAM,EAAE,SAAAA,CAAUlB,CAAC,EAAE;IAAE,OAAOL,OAAO,CAACK,CAAC,CAAC;EAAE,CAAC;EAC3CmB,EAAE,EAAE,SAAAA,CAAUnB,CAAC,EAAEI,CAAC,EAAE;IAAE,OAAOJ,CAAC,GAAGI,CAAC;EAAE,CAAC;EACrCgB,GAAG,EAAE,SAAAA,CAAUpB,CAAC,EAAEI,CAAC,EAAE;IAAE,OAAOJ,CAAC,IAAII,CAAC;EAAE,CAAC;EACvCiB,GAAG,EAAE,SAAAA,CAAUrB,CAAC,EAAEI,CAAC,EAAE;IAAE,OAAOJ,CAAC,IAAII,CAAC;EAAE,CAAC;EACvCkB,UAAU,EAAE,SAAAA,CAAUtB,CAAC,EAAEI,CAAC,EAAE;IAAE,OAAO,CAACJ,CAAC,IAAI,EAAE,EAAEU,WAAW,CAACN,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC;EAAE;AAC5E,CAAC;AACD,IAAImB,UAAU,GAAG,qBAAqB;AACtC,IAAIC,YAAY,GAAG,SAAAA,CAAUC,KAAK,EAAEC,UAAU,EAAE;EAC5C,IAAID,KAAK,IAAI,IAAI,IAAI/B,QAAQ,CAAC+B,KAAK,CAAC,EAAE;IAClC,IAAIE,IAAI,GAAGJ,UAAU,CAACK,IAAI,CAACH,KAAK,CAAC;IACjC,IAAIE,IAAI,EAAE;MACN,OAAO,IAAIE,IAAI,CAAC,CAACF,IAAI,CAAC,CAAC,CAAC,CAAC,CAACG,OAAO,CAAC,CAAC;IACvC,CAAC,MACI,IAAIJ,UAAU,EAAE;MACjB,OAAOD,KAAK,CAACM,WAAW,CAAC,CAAC;IAC9B;EACJ,CAAC,MACI,IAAIN,KAAK,IAAI,IAAI,IAAIhC,MAAM,CAACgC,KAAK,CAAC,EAAE;IACrC,OAAOA,KAAK,CAACK,OAAO,CAAC,CAAC;EAC1B;EACA,OAAOL,KAAK;AAChB,CAAC;AACD,IAAIO,WAAW,GAAG,SAAAA,CAAUC,IAAI,EAAER,KAAK,EAAEC,UAAU,EAAE;EACjD,IAAI,CAAClC,SAAS,CAACiC,KAAK,CAAC,EAAE;IACnB,OAAOQ,IAAI;EACf;EACA,IAAInC,GAAG,GAAGmC,IAAI;EACd,IAAIvC,QAAQ,CAAC+B,KAAK,CAAC,EAAE;IACjB,IAAIE,IAAI,GAAGJ,UAAU,CAACK,IAAI,CAACH,KAAK,CAAC;IACjC,IAAIE,IAAI,EAAE;MACNF,KAAK,GAAG,IAAII,IAAI,CAAC,CAACF,IAAI,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,MACI;MACD7B,GAAG,GAAG,SAAAA,CAAUE,CAAC,EAAE;QACf,IAAIkC,CAAC,GAAGD,IAAI,CAACjC,CAAC,CAAC;QACf,IAAIkC,CAAC,KAAK,IAAI,EAAE;UACZ,OAAOA,CAAC;QACZ;QACA,IAAIC,WAAW,GAAG,OAAOD,CAAC,KAAK,QAAQ,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;QACpD,OAAOR,UAAU,GAAGS,WAAW,CAACJ,WAAW,CAAC,CAAC,GAAGI,WAAW;MAC/D,CAAC;IACL;EACJ;EACA,IAAI1C,MAAM,CAACgC,KAAK,CAAC,EAAE;IACf,OAAO,UAAUzB,CAAC,EAAE;MAChB,IAAIkC,CAAC,GAAGpC,GAAG,CAACE,CAAC,CAAC;MACd,OAAOP,MAAM,CAACyC,CAAC,CAAC,GAAGA,CAAC,CAACJ,OAAO,CAAC,CAAC,GAAGI,CAAC;IACtC,CAAC;EACL;EACA,OAAOpC,GAAG;AACd,CAAC;AACD,IAAIsC,eAAe,GAAG,SAAAA,CAAUC,EAAE,EAAE;EAChC,IAAIC,KAAK,GAAGD,EAAE,CAACC,KAAK;IAAEZ,UAAU,GAAGW,EAAE,CAACX,UAAU;IAAED,KAAK,GAAGY,EAAE,CAACZ,KAAK;IAAEc,QAAQ,GAAGF,EAAE,CAACE,QAAQ;EAC1FD,KAAK,GAAG,CAAC9C,SAAS,CAAC8C,KAAK,CAAC,GAAG,UAAUtC,CAAC,EAAE;IAAE,OAAOA,CAAC;EAAE,CAAC,GAAGsC,KAAK;EAC9DZ,UAAU,GAAGlC,SAAS,CAACkC,UAAU,CAAC,GAAGA,UAAU,GAAG,IAAI;EACtD,IAAIc,QAAQ,GAAGR,WAAW,CAACzC,UAAU,CAAC+C,KAAK,CAAC,GAAGA,KAAK,GAAGhD,MAAM,CAACgD,KAAK,EAAE,IAAI,CAAC,EAAEb,KAAK,EAAEC,UAAU,CAAC;EAC9FD,KAAK,GAAGD,YAAY,CAACC,KAAK,EAAEC,UAAU,CAAC;EACvC,IAAIe,EAAE,GAAGlD,UAAU,CAACgD,QAAQ,CAAC,GAAGA,QAAQ,GAAGrC,YAAY,CAACqC,QAAQ,CAAC;EACjE,OAAO,UAAUvC,CAAC,EAAE;IAAE,OAAOyC,EAAE,CAACD,QAAQ,CAACxC,CAAC,CAAC,EAAEyB,KAAK,EAAEC,UAAU,CAAC;EAAE,CAAC;AACtE,CAAC;AACD;AACA;AACA;AACA,OAAO,IAAIgB,wBAAwB,GAAG,SAAAA,CAAUC,MAAM,EAAE;EACpD,IAAIC,QAAQ,GAAGhD,KAAK,CAAC+C,MAAM,CAAC/C,KAAK,CAAC;EAClC,OAAO+C,MAAM,CAACE,OAAO,CAChBF,MAAM,CAACnD,SAAS,CAAC,CACjBsD,GAAG,CAAC,UAAUZ,CAAC,EAAE;IAAE,OAAO7C,2BAA2B,CAAC6C,CAAC,CAAC,GAAGQ,wBAAwB,CAACR,CAAC,CAAC,GAAGE,eAAe,CAACF,CAAC,CAAC;EAAE,CAAC,CAAC,CAC/Ga,MAAM,CAACH,QAAQ,CAAC/C,MAAM,EAAE+C,QAAQ,CAAC3C,QAAQ,CAAC;AACnD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}