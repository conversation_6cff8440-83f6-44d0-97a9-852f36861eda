{"ast": null, "code": "import wrapElements from './wrap-elements';\nexport default function wrap(elements, rect) {\n  return wrapElements(elements, rect, \"x\", \"y\", \"width\");\n}", "map": {"version": 3, "names": ["wrapElements", "wrap", "elements", "rect"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/alignment/wrap.js"], "sourcesContent": ["import wrapElements from './wrap-elements';\n\nexport default function wrap(elements, rect) {\n    return wrapElements(elements, rect, \"x\", \"y\", \"width\");\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,iBAAiB;AAE1C,eAAe,SAASC,IAAIA,CAACC,QAAQ,EAAEC,IAAI,EAAE;EACzC,OAAOH,YAAY,CAACE,QAAQ,EAAEC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC;AAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}