{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as n from \"react\";\nimport t from \"prop-types\";\nimport { classNames as c } from \"@progress/kendo-react-common\";\nconst i = s => {\n  const e = {\n      layout: \"stretched\",\n      ...s\n    },\n    {\n      layout: o,\n      children: a\n    } = e,\n    r = c(\"k-actions\", \"k-actions-horizontal\", \"k-window-actions k-dialog-actions\", {\n      [`k-actions-${o}`]: o\n    });\n  return /* @__PURE__ */n.createElement(\"div\", {\n    className: r\n  }, a);\n};\ni.propTypes = {\n  children: t.any,\n  layout: t.oneOf([\"start\", \"center\", \"end\", \"stretched\"])\n};\nexport { i as DialogActionsBar };", "map": {"version": 3, "names": ["n", "t", "classNames", "c", "i", "s", "e", "layout", "o", "children", "a", "r", "createElement", "className", "propTypes", "any", "oneOf", "DialogActionsBar"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dialogs/DialogActionsBar.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as n from \"react\";\nimport t from \"prop-types\";\nimport { classNames as c } from \"@progress/kendo-react-common\";\nconst i = (s) => {\n  const e = {\n    layout: \"stretched\",\n    ...s\n  }, { layout: o, children: a } = e, r = c(\"k-actions\", \"k-actions-horizontal\", \"k-window-actions k-dialog-actions\", {\n    [`k-actions-${o}`]: o\n  });\n  return /* @__PURE__ */ n.createElement(\"div\", { className: r }, a);\n};\ni.propTypes = {\n  children: t.any,\n  layout: t.oneOf([\"start\", \"center\", \"end\", \"stretched\"])\n};\nexport {\n  i as DialogActionsBar\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AAC9D,MAAMC,CAAC,GAAIC,CAAC,IAAK;EACf,MAAMC,CAAC,GAAG;MACRC,MAAM,EAAE,WAAW;MACnB,GAAGF;IACL,CAAC;IAAE;MAAEE,MAAM,EAAEC,CAAC;MAAEC,QAAQ,EAAEC;IAAE,CAAC,GAAGJ,CAAC;IAAEK,CAAC,GAAGR,CAAC,CAAC,WAAW,EAAE,sBAAsB,EAAE,mCAAmC,EAAE;MACjH,CAAC,aAAaK,CAAC,EAAE,GAAGA;IACtB,CAAC,CAAC;EACF,OAAO,eAAgBR,CAAC,CAACY,aAAa,CAAC,KAAK,EAAE;IAAEC,SAAS,EAAEF;EAAE,CAAC,EAAED,CAAC,CAAC;AACpE,CAAC;AACDN,CAAC,CAACU,SAAS,GAAG;EACZL,QAAQ,EAAER,CAAC,CAACc,GAAG;EACfR,MAAM,EAAEN,CAAC,CAACe,KAAK,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,CAAC;AACzD,CAAC;AACD,SACEZ,CAAC,IAAIa,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}