{"ast": null, "code": "import { cldr } from './info';\nexport default function setData(data) {\n  var locale = data.name;\n  var info = cldr[locale] = cldr[locale] || {};\n  var supplemental = cldr.supplemental = cldr.supplemental || {};\n  if (data.likelySubtags) {\n    supplemental.likelySubtags = Object.assign(supplemental.likelySubtags || {}, data.likelySubtags);\n  }\n  if (data.currencyData) {\n    supplemental.currencyData = supplemental.currencyData || {};\n    supplemental.currencyData.fractions = Object.assign(supplemental.currencyData.fractions || {}, data.currencyData);\n  }\n  var numbers = info.numbers;\n  Object.assign(info, data);\n  if (numbers && data.numbers) {\n    info.numbers = Object.assign({}, numbers, data.numbers);\n  }\n}", "map": {"version": 3, "names": ["cldr", "setData", "data", "locale", "name", "info", "supplemental", "likelySubtags", "Object", "assign", "currencyData", "fractions", "numbers"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-intl/dist/es/cldr/set-data.js"], "sourcesContent": ["import { cldr } from './info';\n\nexport default function setData(data) {\n    var locale = data.name;\n    var info = cldr[locale] = cldr[locale] || {};\n    var supplemental = cldr.supplemental = cldr.supplemental || {};\n\n    if (data.likelySubtags) {\n        supplemental.likelySubtags = Object.assign(supplemental.likelySubtags || {}, data.likelySubtags);\n    }\n\n    if (data.currencyData) {\n        supplemental.currencyData = supplemental.currencyData || {};\n        supplemental.currencyData.fractions = Object.assign(supplemental.currencyData.fractions || {}, data.currencyData);\n    }\n\n    var numbers = info.numbers;\n\n    Object.assign(info, data);\n\n    if (numbers && data.numbers) {\n        info.numbers = Object.assign({}, numbers, data.numbers);\n    }\n}"], "mappings": "AAAA,SAASA,IAAI,QAAQ,QAAQ;AAE7B,eAAe,SAASC,OAAOA,CAACC,IAAI,EAAE;EAClC,IAAIC,MAAM,GAAGD,IAAI,CAACE,IAAI;EACtB,IAAIC,IAAI,GAAGL,IAAI,CAACG,MAAM,CAAC,GAAGH,IAAI,CAACG,MAAM,CAAC,IAAI,CAAC,CAAC;EAC5C,IAAIG,YAAY,GAAGN,IAAI,CAACM,YAAY,GAAGN,IAAI,CAACM,YAAY,IAAI,CAAC,CAAC;EAE9D,IAAIJ,IAAI,CAACK,aAAa,EAAE;IACpBD,YAAY,CAACC,aAAa,GAAGC,MAAM,CAACC,MAAM,CAACH,YAAY,CAACC,aAAa,IAAI,CAAC,CAAC,EAAEL,IAAI,CAACK,aAAa,CAAC;EACpG;EAEA,IAAIL,IAAI,CAACQ,YAAY,EAAE;IACnBJ,YAAY,CAACI,YAAY,GAAGJ,YAAY,CAACI,YAAY,IAAI,CAAC,CAAC;IAC3DJ,YAAY,CAACI,YAAY,CAACC,SAAS,GAAGH,MAAM,CAACC,MAAM,CAACH,YAAY,CAACI,YAAY,CAACC,SAAS,IAAI,CAAC,CAAC,EAAET,IAAI,CAACQ,YAAY,CAAC;EACrH;EAEA,IAAIE,OAAO,GAAGP,IAAI,CAACO,OAAO;EAE1BJ,MAAM,CAACC,MAAM,CAACJ,IAAI,EAAEH,IAAI,CAAC;EAEzB,IAAIU,OAAO,IAAIV,IAAI,CAACU,OAAO,EAAE;IACzBP,IAAI,CAACO,OAAO,GAAGJ,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEG,OAAO,EAAEV,IAAI,CAACU,OAAO,CAAC;EAC3D;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}