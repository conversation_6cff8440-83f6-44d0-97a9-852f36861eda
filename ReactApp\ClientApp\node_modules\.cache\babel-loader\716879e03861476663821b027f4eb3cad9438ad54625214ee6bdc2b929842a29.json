{"ast": null, "code": "import BaseSurface from '../core/surface';\nimport RootNode from './root-node';\nimport Group from '../shapes/group';\nimport transform from '../geometry/transform';\nimport renderSVG from './utils/render-svg';\nimport { SVG_NS } from './constants';\nimport { bindEvents, unbindEvents, elementStyles } from '../util';\nimport ArcNode from './arc-node';\nimport CircleNode from './circle-node';\nimport GroupNode from './group-node';\nimport ImageNode from './image-node';\nimport MultiPathNode from './multi-path-node';\nimport PathNode from './path-node';\nimport RectNode from './rect-node';\nimport TextNode from './text-node';\nimport NODE_MAP from './node-map';\nNODE_MAP.Arc = ArcNode;\nNODE_MAP.Circle = CircleNode;\nNODE_MAP.Group = GroupNode;\nNODE_MAP.Image = ImageNode;\nNODE_MAP.MultiPath = MultiPathNode;\nNODE_MAP.Path = PathNode;\nNODE_MAP.Rect = RectNode;\nNODE_MAP.Text = TextNode;\nvar RTL = 'rtl';\nfunction alignToScreen(element) {\n  var ctm;\n  try {\n    ctm = element.getScreenCTM ? element.getScreenCTM() : null;\n  } catch (e) {} // eslint-disable-line no-empty\n\n  if (ctm) {\n    var left = -ctm.e % 1;\n    var top = -ctm.f % 1;\n    var style = element.style;\n    if (left !== 0 || top !== 0) {\n      style.left = left + \"px\";\n      style.top = top + \"px\";\n    }\n  }\n}\nvar Surface = function (BaseSurface) {\n  function Surface(element, options) {\n    BaseSurface.call(this, element, options);\n    this._root = new RootNode(Object.assign({\n      rtl: elementStyles(element, 'direction').direction === RTL\n    }, this.options));\n    renderSVG(this.element, this._template(''));\n    this._rootElement = this.element.firstElementChild;\n    this._rootElement.style.width = '100%';\n    this._rootElement.style.height = '100%';\n    this._rootElement.style.overflow = 'hidden';\n    alignToScreen(this._rootElement);\n    this._root.attachTo(this._rootElement);\n    bindEvents(this.element, {\n      click: this._click,\n      mouseover: this._mouseenter,\n      mouseout: this._mouseleave,\n      mousemove: this._mousemove\n    });\n    this.resize();\n  }\n  if (BaseSurface) Surface.__proto__ = BaseSurface;\n  Surface.prototype = Object.create(BaseSurface && BaseSurface.prototype);\n  Surface.prototype.constructor = Surface;\n  var prototypeAccessors = {\n    type: {\n      configurable: true\n    }\n  };\n  prototypeAccessors.type.get = function () {\n    return \"svg\";\n  };\n  Surface.prototype.destroy = function destroy() {\n    if (this._root) {\n      this._root.destroy();\n      this._root = null;\n      this._rootElement = null;\n      unbindEvents(this.element, {\n        click: this._click,\n        mouseover: this._mouseenter,\n        mouseout: this._mouseleave,\n        mousemove: this._mousemove\n      });\n    }\n    BaseSurface.prototype.destroy.call(this);\n  };\n  Surface.prototype.translate = function translate(offset) {\n    var viewBox = Math.round(offset.x) + \" \" + Math.round(offset.y) + \" \" + this._size.width + \" \" + this._size.height;\n    this._offset = offset;\n    this._rootElement.setAttribute(\"viewBox\", viewBox);\n  };\n  Surface.prototype.draw = function draw(element) {\n    BaseSurface.prototype.draw.call(this, element);\n    this._root.load([element]);\n  };\n  Surface.prototype.clear = function clear() {\n    BaseSurface.prototype.clear.call(this);\n    this._root.clear();\n  };\n  Surface.prototype.svg = function svg() {\n    return \"<?xml version='1.0' ?>\" + this._template();\n  };\n  Surface.prototype.exportVisual = function exportVisual() {\n    var ref = this;\n    var visual = ref._visual;\n    var offset = ref._offset;\n    if (offset) {\n      var wrap = new Group();\n      wrap.children.push(visual);\n      wrap.transform(transform().translate(-offset.x, -offset.y));\n      visual = wrap;\n    }\n    return visual;\n  };\n  Surface.prototype._resize = function _resize() {\n    if (this._offset) {\n      this.translate(this._offset);\n    }\n  };\n  Surface.prototype._template = function _template(svgStyles) {\n    var styles = typeof svgStyles === 'string' ? svgStyles : \"style='width: 100%; height: 100%; overflow: hidden;' \";\n    return \"<svg \" + styles + \"xmlns='\" + SVG_NS + \"' xmlns:xlink='http://www.w3.org/1999/xlink' version='1.1'>\" + this._root.render() + \"</svg>\";\n  };\n  Object.defineProperties(Surface.prototype, prototypeAccessors);\n  return Surface;\n}(BaseSurface);\nexport default Surface;", "map": {"version": 3, "names": ["BaseSurface", "RootNode", "Group", "transform", "renderSVG", "SVG_NS", "bindEvents", "unbindEvents", "elementStyles", "ArcNode", "CircleNode", "GroupNode", "ImageNode", "MultiPathNode", "PathNode", "RectNode", "TextNode", "NODE_MAP", "Arc", "Circle", "Image", "MultiPath", "Path", "Rect", "Text", "RTL", "alignToScreen", "element", "ctm", "getScreenCTM", "e", "left", "top", "f", "style", "Surface", "options", "call", "_root", "Object", "assign", "rtl", "direction", "_template", "_rootElement", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "width", "height", "overflow", "attachTo", "click", "_click", "mouseover", "_mouseenter", "mouseout", "_mouseleave", "mousemove", "_mousemove", "resize", "__proto__", "prototype", "create", "constructor", "prototypeAccessors", "type", "configurable", "get", "destroy", "translate", "offset", "viewBox", "Math", "round", "x", "y", "_size", "_offset", "setAttribute", "draw", "load", "clear", "svg", "exportVisual", "ref", "visual", "_visual", "wrap", "children", "push", "_resize", "svgStyles", "styles", "render", "defineProperties"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/svg/surface.js"], "sourcesContent": ["import BaseSurface from '../core/surface';\nimport RootNode from './root-node';\nimport Group from '../shapes/group';\nimport transform from '../geometry/transform';\nimport renderSVG from './utils/render-svg';\nimport { SVG_NS } from './constants';\nimport { bindEvents, unbindEvents, elementStyles } from '../util';\n\nimport ArcNode from './arc-node';\nimport CircleNode from './circle-node';\nimport GroupNode from './group-node';\nimport ImageNode from './image-node';\nimport MultiPathNode from './multi-path-node';\nimport PathNode from './path-node';\nimport RectNode from './rect-node';\nimport TextNode from './text-node';\nimport NODE_MAP from './node-map';\n\nNODE_MAP.Arc = ArcNode;\nNODE_MAP.Circle = CircleNode;\nNODE_MAP.Group = GroupNode;\nNODE_MAP.Image = ImageNode;\nNODE_MAP.MultiPath = MultiPathNode;\nNODE_MAP.Path = PathNode;\nNODE_MAP.Rect = RectNode;\nNODE_MAP.Text = TextNode;\n\nvar RTL = 'rtl';\n\nfunction alignToScreen(element) {\n    var ctm;\n\n    try {\n        ctm = element.getScreenCTM ? element.getScreenCTM() : null;\n    } catch (e) { } // eslint-disable-line no-empty\n\n    if (ctm) {\n        var left = - ctm.e % 1;\n        var top = - ctm.f % 1;\n        var style = element.style;\n\n        if (left !== 0 || top !== 0) {\n            style.left = left + \"px\";\n            style.top = top + \"px\";\n        }\n    }\n}\n\nvar Surface = (function (BaseSurface) {\n    function Surface(element, options) {\n        BaseSurface.call(this, element, options);\n\n        this._root = new RootNode(Object.assign({\n            rtl: elementStyles(element, 'direction').direction === RTL\n        }, this.options));\n\n        renderSVG(this.element, this._template(''));\n\n        this._rootElement = this.element.firstElementChild;\n        this._rootElement.style.width = '100%';\n        this._rootElement.style.height = '100%';\n        this._rootElement.style.overflow = 'hidden';\n\n        alignToScreen(this._rootElement);\n\n        this._root.attachTo(this._rootElement);\n\n        bindEvents(this.element, {\n            click: this._click,\n            mouseover: this._mouseenter,\n            mouseout: this._mouseleave,\n            mousemove: this._mousemove\n        });\n\n        this.resize();\n    }\n\n    if ( BaseSurface ) Surface.__proto__ = BaseSurface;\n    Surface.prototype = Object.create( BaseSurface && BaseSurface.prototype );\n    Surface.prototype.constructor = Surface;\n\n    var prototypeAccessors = { type: { configurable: true } };\n\n    prototypeAccessors.type.get = function () {\n        return \"svg\";\n    };\n\n    Surface.prototype.destroy = function destroy () {\n        if (this._root) {\n            this._root.destroy();\n            this._root = null;\n            this._rootElement = null;\n            unbindEvents(this.element, {\n                click: this._click,\n                mouseover: this._mouseenter,\n                mouseout: this._mouseleave,\n                mousemove: this._mousemove\n            });\n        }\n\n        BaseSurface.prototype.destroy.call(this);\n    };\n\n    Surface.prototype.translate = function translate (offset) {\n        var viewBox = (Math.round(offset.x)) + \" \" + (Math.round(offset.y)) + \" \" + (this._size.width) + \" \" + (this._size.height);\n\n        this._offset = offset;\n        this._rootElement.setAttribute(\"viewBox\", viewBox);\n    };\n\n    Surface.prototype.draw = function draw (element) {\n        BaseSurface.prototype.draw.call(this, element);\n        this._root.load([ element ]);\n    };\n\n    Surface.prototype.clear = function clear () {\n        BaseSurface.prototype.clear.call(this);\n        this._root.clear();\n    };\n\n    Surface.prototype.svg = function svg () {\n        return \"<?xml version='1.0' ?>\" + this._template();\n    };\n\n    Surface.prototype.exportVisual = function exportVisual () {\n        var ref = this;\n        var visual = ref._visual;\n        var offset = ref._offset;\n\n        if (offset) {\n            var wrap = new Group();\n            wrap.children.push(visual);\n\n            wrap.transform(\n                transform().translate(-offset.x, -offset.y)\n            );\n\n            visual = wrap;\n        }\n\n        return visual;\n    };\n\n    Surface.prototype._resize = function _resize () {\n        if (this._offset) {\n            this.translate(this._offset);\n        }\n    };\n\n    Surface.prototype._template = function _template (svgStyles) {\n        var styles = typeof svgStyles === 'string' ? svgStyles :\n            \"style='width: 100%; height: 100%; overflow: hidden;' \";\n        return (\"<svg \" + styles + \"xmlns='\" + SVG_NS + \"' xmlns:xlink='http://www.w3.org/1999/xlink' version='1.1'>\" + (this._root.render()) + \"</svg>\");\n    };\n\n    Object.defineProperties( Surface.prototype, prototypeAccessors );\n\n    return Surface;\n}(BaseSurface));\n\nexport default Surface;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,iBAAiB;AACzC,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,KAAK,MAAM,iBAAiB;AACnC,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,UAAU,EAAEC,YAAY,EAAEC,aAAa,QAAQ,SAAS;AAEjE,OAAOC,OAAO,MAAM,YAAY;AAChC,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAOC,SAAS,MAAM,cAAc;AACpC,OAAOC,SAAS,MAAM,cAAc;AACpC,OAAOC,aAAa,MAAM,mBAAmB;AAC7C,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,QAAQ,MAAM,YAAY;AAEjCA,QAAQ,CAACC,GAAG,GAAGT,OAAO;AACtBQ,QAAQ,CAACE,MAAM,GAAGT,UAAU;AAC5BO,QAAQ,CAACf,KAAK,GAAGS,SAAS;AAC1BM,QAAQ,CAACG,KAAK,GAAGR,SAAS;AAC1BK,QAAQ,CAACI,SAAS,GAAGR,aAAa;AAClCI,QAAQ,CAACK,IAAI,GAAGR,QAAQ;AACxBG,QAAQ,CAACM,IAAI,GAAGR,QAAQ;AACxBE,QAAQ,CAACO,IAAI,GAAGR,QAAQ;AAExB,IAAIS,GAAG,GAAG,KAAK;AAEf,SAASC,aAAaA,CAACC,OAAO,EAAE;EAC5B,IAAIC,GAAG;EAEP,IAAI;IACAA,GAAG,GAAGD,OAAO,CAACE,YAAY,GAAGF,OAAO,CAACE,YAAY,CAAC,CAAC,GAAG,IAAI;EAC9D,CAAC,CAAC,OAAOC,CAAC,EAAE,CAAE,CAAC,CAAC;;EAEhB,IAAIF,GAAG,EAAE;IACL,IAAIG,IAAI,GAAG,CAAEH,GAAG,CAACE,CAAC,GAAG,CAAC;IACtB,IAAIE,GAAG,GAAG,CAAEJ,GAAG,CAACK,CAAC,GAAG,CAAC;IACrB,IAAIC,KAAK,GAAGP,OAAO,CAACO,KAAK;IAEzB,IAAIH,IAAI,KAAK,CAAC,IAAIC,GAAG,KAAK,CAAC,EAAE;MACzBE,KAAK,CAACH,IAAI,GAAGA,IAAI,GAAG,IAAI;MACxBG,KAAK,CAACF,GAAG,GAAGA,GAAG,GAAG,IAAI;IAC1B;EACJ;AACJ;AAEA,IAAIG,OAAO,GAAI,UAAUnC,WAAW,EAAE;EAClC,SAASmC,OAAOA,CAACR,OAAO,EAAES,OAAO,EAAE;IAC/BpC,WAAW,CAACqC,IAAI,CAAC,IAAI,EAAEV,OAAO,EAAES,OAAO,CAAC;IAExC,IAAI,CAACE,KAAK,GAAG,IAAIrC,QAAQ,CAACsC,MAAM,CAACC,MAAM,CAAC;MACpCC,GAAG,EAAEjC,aAAa,CAACmB,OAAO,EAAE,WAAW,CAAC,CAACe,SAAS,KAAKjB;IAC3D,CAAC,EAAE,IAAI,CAACW,OAAO,CAAC,CAAC;IAEjBhC,SAAS,CAAC,IAAI,CAACuB,OAAO,EAAE,IAAI,CAACgB,SAAS,CAAC,EAAE,CAAC,CAAC;IAE3C,IAAI,CAACC,YAAY,GAAG,IAAI,CAACjB,OAAO,CAACkB,iBAAiB;IAClD,IAAI,CAACD,YAAY,CAACV,KAAK,CAACY,KAAK,GAAG,MAAM;IACtC,IAAI,CAACF,YAAY,CAACV,KAAK,CAACa,MAAM,GAAG,MAAM;IACvC,IAAI,CAACH,YAAY,CAACV,KAAK,CAACc,QAAQ,GAAG,QAAQ;IAE3CtB,aAAa,CAAC,IAAI,CAACkB,YAAY,CAAC;IAEhC,IAAI,CAACN,KAAK,CAACW,QAAQ,CAAC,IAAI,CAACL,YAAY,CAAC;IAEtCtC,UAAU,CAAC,IAAI,CAACqB,OAAO,EAAE;MACrBuB,KAAK,EAAE,IAAI,CAACC,MAAM;MAClBC,SAAS,EAAE,IAAI,CAACC,WAAW;MAC3BC,QAAQ,EAAE,IAAI,CAACC,WAAW;MAC1BC,SAAS,EAAE,IAAI,CAACC;IACpB,CAAC,CAAC;IAEF,IAAI,CAACC,MAAM,CAAC,CAAC;EACjB;EAEA,IAAK1D,WAAW,EAAGmC,OAAO,CAACwB,SAAS,GAAG3D,WAAW;EAClDmC,OAAO,CAACyB,SAAS,GAAGrB,MAAM,CAACsB,MAAM,CAAE7D,WAAW,IAAIA,WAAW,CAAC4D,SAAU,CAAC;EACzEzB,OAAO,CAACyB,SAAS,CAACE,WAAW,GAAG3B,OAAO;EAEvC,IAAI4B,kBAAkB,GAAG;IAAEC,IAAI,EAAE;MAAEC,YAAY,EAAE;IAAK;EAAE,CAAC;EAEzDF,kBAAkB,CAACC,IAAI,CAACE,GAAG,GAAG,YAAY;IACtC,OAAO,KAAK;EAChB,CAAC;EAED/B,OAAO,CAACyB,SAAS,CAACO,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;IAC5C,IAAI,IAAI,CAAC7B,KAAK,EAAE;MACZ,IAAI,CAACA,KAAK,CAAC6B,OAAO,CAAC,CAAC;MACpB,IAAI,CAAC7B,KAAK,GAAG,IAAI;MACjB,IAAI,CAACM,YAAY,GAAG,IAAI;MACxBrC,YAAY,CAAC,IAAI,CAACoB,OAAO,EAAE;QACvBuB,KAAK,EAAE,IAAI,CAACC,MAAM;QAClBC,SAAS,EAAE,IAAI,CAACC,WAAW;QAC3BC,QAAQ,EAAE,IAAI,CAACC,WAAW;QAC1BC,SAAS,EAAE,IAAI,CAACC;MACpB,CAAC,CAAC;IACN;IAEAzD,WAAW,CAAC4D,SAAS,CAACO,OAAO,CAAC9B,IAAI,CAAC,IAAI,CAAC;EAC5C,CAAC;EAEDF,OAAO,CAACyB,SAAS,CAACQ,SAAS,GAAG,SAASA,SAASA,CAAEC,MAAM,EAAE;IACtD,IAAIC,OAAO,GAAIC,IAAI,CAACC,KAAK,CAACH,MAAM,CAACI,CAAC,CAAC,GAAI,GAAG,GAAIF,IAAI,CAACC,KAAK,CAACH,MAAM,CAACK,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAACC,KAAK,CAAC7B,KAAM,GAAG,GAAG,GAAI,IAAI,CAAC6B,KAAK,CAAC5B,MAAO;IAE1H,IAAI,CAAC6B,OAAO,GAAGP,MAAM;IACrB,IAAI,CAACzB,YAAY,CAACiC,YAAY,CAAC,SAAS,EAAEP,OAAO,CAAC;EACtD,CAAC;EAEDnC,OAAO,CAACyB,SAAS,CAACkB,IAAI,GAAG,SAASA,IAAIA,CAAEnD,OAAO,EAAE;IAC7C3B,WAAW,CAAC4D,SAAS,CAACkB,IAAI,CAACzC,IAAI,CAAC,IAAI,EAAEV,OAAO,CAAC;IAC9C,IAAI,CAACW,KAAK,CAACyC,IAAI,CAAC,CAAEpD,OAAO,CAAE,CAAC;EAChC,CAAC;EAEDQ,OAAO,CAACyB,SAAS,CAACoB,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAI;IACxChF,WAAW,CAAC4D,SAAS,CAACoB,KAAK,CAAC3C,IAAI,CAAC,IAAI,CAAC;IACtC,IAAI,CAACC,KAAK,CAAC0C,KAAK,CAAC,CAAC;EACtB,CAAC;EAED7C,OAAO,CAACyB,SAAS,CAACqB,GAAG,GAAG,SAASA,GAAGA,CAAA,EAAI;IACpC,OAAO,wBAAwB,GAAG,IAAI,CAACtC,SAAS,CAAC,CAAC;EACtD,CAAC;EAEDR,OAAO,CAACyB,SAAS,CAACsB,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAI;IACtD,IAAIC,GAAG,GAAG,IAAI;IACd,IAAIC,MAAM,GAAGD,GAAG,CAACE,OAAO;IACxB,IAAIhB,MAAM,GAAGc,GAAG,CAACP,OAAO;IAExB,IAAIP,MAAM,EAAE;MACR,IAAIiB,IAAI,GAAG,IAAIpF,KAAK,CAAC,CAAC;MACtBoF,IAAI,CAACC,QAAQ,CAACC,IAAI,CAACJ,MAAM,CAAC;MAE1BE,IAAI,CAACnF,SAAS,CACVA,SAAS,CAAC,CAAC,CAACiE,SAAS,CAAC,CAACC,MAAM,CAACI,CAAC,EAAE,CAACJ,MAAM,CAACK,CAAC,CAC9C,CAAC;MAEDU,MAAM,GAAGE,IAAI;IACjB;IAEA,OAAOF,MAAM;EACjB,CAAC;EAEDjD,OAAO,CAACyB,SAAS,CAAC6B,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;IAC5C,IAAI,IAAI,CAACb,OAAO,EAAE;MACd,IAAI,CAACR,SAAS,CAAC,IAAI,CAACQ,OAAO,CAAC;IAChC;EACJ,CAAC;EAEDzC,OAAO,CAACyB,SAAS,CAACjB,SAAS,GAAG,SAASA,SAASA,CAAE+C,SAAS,EAAE;IACzD,IAAIC,MAAM,GAAG,OAAOD,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAClD,uDAAuD;IAC3D,OAAQ,OAAO,GAAGC,MAAM,GAAG,SAAS,GAAGtF,MAAM,GAAG,6DAA6D,GAAI,IAAI,CAACiC,KAAK,CAACsD,MAAM,CAAC,CAAE,GAAG,QAAQ;EACpJ,CAAC;EAEDrD,MAAM,CAACsD,gBAAgB,CAAE1D,OAAO,CAACyB,SAAS,EAAEG,kBAAmB,CAAC;EAEhE,OAAO5B,OAAO;AAClB,CAAC,CAACnC,WAAW,CAAE;AAEf,eAAemC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}