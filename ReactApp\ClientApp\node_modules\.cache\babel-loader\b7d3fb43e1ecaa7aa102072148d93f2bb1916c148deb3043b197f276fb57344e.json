{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { presetPrimaryColors } from '@ant-design/colors';\nimport classNames from 'classnames';\nimport { Circle as RCCircle } from 'rc-progress';\nimport * as React from 'react';\nimport { getSuccessPercent, validProgress } from './utils';\nfunction getPercentage(_ref) {\n  var percent = _ref.percent,\n    success = _ref.success,\n    successPercent = _ref.successPercent;\n  var realSuccessPercent = validProgress(getSuccessPercent({\n    success: success,\n    successPercent: successPercent\n  }));\n  return [realSuccessPercent, validProgress(validProgress(percent) - realSuccessPercent)];\n}\nfunction getStrokeColor(_ref2) {\n  var _ref2$success = _ref2.success,\n    success = _ref2$success === void 0 ? {} : _ref2$success,\n    strokeColor = _ref2.strokeColor;\n  var successColor = success.strokeColor;\n  return [successColor || presetPrimaryColors.green, strokeColor || null];\n}\nvar Circle = function Circle(props) {\n  var prefixCls = props.prefixCls,\n    width = props.width,\n    strokeWidth = props.strokeWidth,\n    _props$trailColor = props.trailColor,\n    trailColor = _props$trailColor === void 0 ? null : _props$trailColor,\n    _props$strokeLinecap = props.strokeLinecap,\n    strokeLinecap = _props$strokeLinecap === void 0 ? 'round' : _props$strokeLinecap,\n    gapPosition = props.gapPosition,\n    gapDegree = props.gapDegree,\n    type = props.type,\n    children = props.children,\n    success = props.success;\n  var circleSize = width || 120;\n  var circleStyle = {\n    width: circleSize,\n    height: circleSize,\n    fontSize: circleSize * 0.15 + 6\n  };\n  var circleWidth = strokeWidth || 6;\n  var gapPos = gapPosition || type === 'dashboard' && 'bottom' || undefined;\n  var getGapDegree = function getGapDegree() {\n    // Support gapDeg = 0 when type = 'dashboard'\n    if (gapDegree || gapDegree === 0) {\n      return gapDegree;\n    }\n    if (type === 'dashboard') {\n      return 75;\n    }\n    return undefined;\n  };\n  // using className to style stroke color\n  var isGradient = Object.prototype.toString.call(props.strokeColor) === '[object Object]';\n  var strokeColor = getStrokeColor({\n    success: success,\n    strokeColor: props.strokeColor\n  });\n  var wrapperClassName = classNames(\"\".concat(prefixCls, \"-inner\"), _defineProperty({}, \"\".concat(prefixCls, \"-circle-gradient\"), isGradient));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: wrapperClassName,\n    style: circleStyle\n  }, /*#__PURE__*/React.createElement(RCCircle, {\n    percent: getPercentage(props),\n    strokeWidth: circleWidth,\n    trailWidth: circleWidth,\n    strokeColor: strokeColor,\n    strokeLinecap: strokeLinecap,\n    trailColor: trailColor,\n    prefixCls: prefixCls,\n    gapDegree: getGapDegree(),\n    gapPosition: gapPos\n  }), children);\n};\nexport default Circle;", "map": {"version": 3, "names": ["_defineProperty", "presetPrimaryColors", "classNames", "Circle", "RCCircle", "React", "getSuccessPercent", "validProgress", "getPercentage", "_ref", "percent", "success", "successPercent", "realSuccessPercent", "getStrokeColor", "_ref2", "_ref2$success", "strokeColor", "successColor", "green", "props", "prefixCls", "width", "strokeWidth", "_props$trailColor", "trailColor", "_props$strokeLinecap", "strokeLinecap", "gapPosition", "gapDegree", "type", "children", "circleSize", "circleStyle", "height", "fontSize", "circleWidth", "gapPos", "undefined", "getGapDegree", "isGradient", "Object", "prototype", "toString", "call", "wrapperClassName", "concat", "createElement", "className", "style", "trailWidth"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/antd/es/progress/Circle.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { presetPrimaryColors } from '@ant-design/colors';\nimport classNames from 'classnames';\nimport { Circle as RCCircle } from 'rc-progress';\nimport * as React from 'react';\nimport { getSuccessPercent, validProgress } from './utils';\nfunction getPercentage(_ref) {\n  var percent = _ref.percent,\n    success = _ref.success,\n    successPercent = _ref.successPercent;\n  var realSuccessPercent = validProgress(getSuccessPercent({\n    success: success,\n    successPercent: successPercent\n  }));\n  return [realSuccessPercent, validProgress(validProgress(percent) - realSuccessPercent)];\n}\nfunction getStrokeColor(_ref2) {\n  var _ref2$success = _ref2.success,\n    success = _ref2$success === void 0 ? {} : _ref2$success,\n    strokeColor = _ref2.strokeColor;\n  var successColor = success.strokeColor;\n  return [successColor || presetPrimaryColors.green, strokeColor || null];\n}\nvar Circle = function Circle(props) {\n  var prefixCls = props.prefixCls,\n    width = props.width,\n    strokeWidth = props.strokeWidth,\n    _props$trailColor = props.trailColor,\n    trailColor = _props$trailColor === void 0 ? null : _props$trailColor,\n    _props$strokeLinecap = props.strokeLinecap,\n    strokeLinecap = _props$strokeLinecap === void 0 ? 'round' : _props$strokeLinecap,\n    gapPosition = props.gapPosition,\n    gapDegree = props.gapDegree,\n    type = props.type,\n    children = props.children,\n    success = props.success;\n  var circleSize = width || 120;\n  var circleStyle = {\n    width: circleSize,\n    height: circleSize,\n    fontSize: circleSize * 0.15 + 6\n  };\n  var circleWidth = strokeWidth || 6;\n  var gapPos = gapPosition || type === 'dashboard' && 'bottom' || undefined;\n  var getGapDegree = function getGapDegree() {\n    // Support gapDeg = 0 when type = 'dashboard'\n    if (gapDegree || gapDegree === 0) {\n      return gapDegree;\n    }\n    if (type === 'dashboard') {\n      return 75;\n    }\n    return undefined;\n  };\n  // using className to style stroke color\n  var isGradient = Object.prototype.toString.call(props.strokeColor) === '[object Object]';\n  var strokeColor = getStrokeColor({\n    success: success,\n    strokeColor: props.strokeColor\n  });\n  var wrapperClassName = classNames(\"\".concat(prefixCls, \"-inner\"), _defineProperty({}, \"\".concat(prefixCls, \"-circle-gradient\"), isGradient));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: wrapperClassName,\n    style: circleStyle\n  }, /*#__PURE__*/React.createElement(RCCircle, {\n    percent: getPercentage(props),\n    strokeWidth: circleWidth,\n    trailWidth: circleWidth,\n    strokeColor: strokeColor,\n    strokeLinecap: strokeLinecap,\n    trailColor: trailColor,\n    prefixCls: prefixCls,\n    gapDegree: getGapDegree(),\n    gapPosition: gapPos\n  }), children);\n};\nexport default Circle;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,SAASC,mBAAmB,QAAQ,oBAAoB;AACxD,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,MAAM,IAAIC,QAAQ,QAAQ,aAAa;AAChD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,EAAEC,aAAa,QAAQ,SAAS;AAC1D,SAASC,aAAaA,CAACC,IAAI,EAAE;EAC3B,IAAIC,OAAO,GAAGD,IAAI,CAACC,OAAO;IACxBC,OAAO,GAAGF,IAAI,CAACE,OAAO;IACtBC,cAAc,GAAGH,IAAI,CAACG,cAAc;EACtC,IAAIC,kBAAkB,GAAGN,aAAa,CAACD,iBAAiB,CAAC;IACvDK,OAAO,EAAEA,OAAO;IAChBC,cAAc,EAAEA;EAClB,CAAC,CAAC,CAAC;EACH,OAAO,CAACC,kBAAkB,EAAEN,aAAa,CAACA,aAAa,CAACG,OAAO,CAAC,GAAGG,kBAAkB,CAAC,CAAC;AACzF;AACA,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC7B,IAAIC,aAAa,GAAGD,KAAK,CAACJ,OAAO;IAC/BA,OAAO,GAAGK,aAAa,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,aAAa;IACvDC,WAAW,GAAGF,KAAK,CAACE,WAAW;EACjC,IAAIC,YAAY,GAAGP,OAAO,CAACM,WAAW;EACtC,OAAO,CAACC,YAAY,IAAIjB,mBAAmB,CAACkB,KAAK,EAAEF,WAAW,IAAI,IAAI,CAAC;AACzE;AACA,IAAId,MAAM,GAAG,SAASA,MAAMA,CAACiB,KAAK,EAAE;EAClC,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACnBC,WAAW,GAAGH,KAAK,CAACG,WAAW;IAC/BC,iBAAiB,GAAGJ,KAAK,CAACK,UAAU;IACpCA,UAAU,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,iBAAiB;IACpEE,oBAAoB,GAAGN,KAAK,CAACO,aAAa;IAC1CA,aAAa,GAAGD,oBAAoB,KAAK,KAAK,CAAC,GAAG,OAAO,GAAGA,oBAAoB;IAChFE,WAAW,GAAGR,KAAK,CAACQ,WAAW;IAC/BC,SAAS,GAAGT,KAAK,CAACS,SAAS;IAC3BC,IAAI,GAAGV,KAAK,CAACU,IAAI;IACjBC,QAAQ,GAAGX,KAAK,CAACW,QAAQ;IACzBpB,OAAO,GAAGS,KAAK,CAACT,OAAO;EACzB,IAAIqB,UAAU,GAAGV,KAAK,IAAI,GAAG;EAC7B,IAAIW,WAAW,GAAG;IAChBX,KAAK,EAAEU,UAAU;IACjBE,MAAM,EAAEF,UAAU;IAClBG,QAAQ,EAAEH,UAAU,GAAG,IAAI,GAAG;EAChC,CAAC;EACD,IAAII,WAAW,GAAGb,WAAW,IAAI,CAAC;EAClC,IAAIc,MAAM,GAAGT,WAAW,IAAIE,IAAI,KAAK,WAAW,IAAI,QAAQ,IAAIQ,SAAS;EACzE,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzC;IACA,IAAIV,SAAS,IAAIA,SAAS,KAAK,CAAC,EAAE;MAChC,OAAOA,SAAS;IAClB;IACA,IAAIC,IAAI,KAAK,WAAW,EAAE;MACxB,OAAO,EAAE;IACX;IACA,OAAOQ,SAAS;EAClB,CAAC;EACD;EACA,IAAIE,UAAU,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACxB,KAAK,CAACH,WAAW,CAAC,KAAK,iBAAiB;EACxF,IAAIA,WAAW,GAAGH,cAAc,CAAC;IAC/BH,OAAO,EAAEA,OAAO;IAChBM,WAAW,EAAEG,KAAK,CAACH;EACrB,CAAC,CAAC;EACF,IAAI4B,gBAAgB,GAAG3C,UAAU,CAAC,EAAE,CAAC4C,MAAM,CAACzB,SAAS,EAAE,QAAQ,CAAC,EAAErB,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC8C,MAAM,CAACzB,SAAS,EAAE,kBAAkB,CAAC,EAAEmB,UAAU,CAAC,CAAC;EAC5I,OAAO,aAAanC,KAAK,CAAC0C,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAEH,gBAAgB;IAC3BI,KAAK,EAAEhB;EACT,CAAC,EAAE,aAAa5B,KAAK,CAAC0C,aAAa,CAAC3C,QAAQ,EAAE;IAC5CM,OAAO,EAAEF,aAAa,CAACY,KAAK,CAAC;IAC7BG,WAAW,EAAEa,WAAW;IACxBc,UAAU,EAAEd,WAAW;IACvBnB,WAAW,EAAEA,WAAW;IACxBU,aAAa,EAAEA,aAAa;IAC5BF,UAAU,EAAEA,UAAU;IACtBJ,SAAS,EAAEA,SAAS;IACpBQ,SAAS,EAAEU,YAAY,CAAC,CAAC;IACzBX,WAAW,EAAES;EACf,CAAC,CAAC,EAAEN,QAAQ,CAAC;AACf,CAAC;AACD,eAAe5B,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}