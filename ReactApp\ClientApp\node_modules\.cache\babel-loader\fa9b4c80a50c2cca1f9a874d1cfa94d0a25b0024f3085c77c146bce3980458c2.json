{"ast": null, "code": "import Transformation from './transformation';\nexport default function transform(matrix) {\n  if (matrix === null) {\n    return null;\n  }\n  if (matrix instanceof Transformation) {\n    return matrix;\n  }\n  return new Transformation(matrix);\n}", "map": {"version": 3, "names": ["Transformation", "transform", "matrix"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/geometry/transform.js"], "sourcesContent": ["import Transformation from './transformation';\n\nexport default function transform(matrix) {\n    if (matrix === null) {\n        return null;\n    }\n\n    if (matrix instanceof Transformation) {\n        return matrix;\n    }\n\n    return new Transformation(matrix);\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,kBAAkB;AAE7C,eAAe,SAASC,SAASA,CAACC,MAAM,EAAE;EACtC,IAAIA,MAAM,KAAK,IAAI,EAAE;IACjB,OAAO,IAAI;EACf;EAEA,IAAIA,MAAM,YAAYF,cAAc,EAAE;IAClC,OAAOE,MAAM;EACjB;EAEA,OAAO,IAAIF,cAAc,CAACE,MAAM,CAAC;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}