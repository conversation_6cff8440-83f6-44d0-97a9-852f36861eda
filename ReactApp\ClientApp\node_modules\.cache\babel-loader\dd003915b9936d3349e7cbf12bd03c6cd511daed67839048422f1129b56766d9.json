{"ast": null, "code": "import HasObservers from '../core/has-observers';\nimport withAccessors from '../mixins/with-accessors';\nimport { defined, round } from '../util';\nvar Size = function (superclass) {\n  function Size(width, height) {\n    superclass.call(this);\n    this.width = width || 0;\n    this.height = height || 0;\n  }\n  if (superclass) Size.__proto__ = superclass;\n  Size.prototype = Object.create(superclass && superclass.prototype);\n  Size.prototype.constructor = Size;\n  var staticAccessors = {\n    ZERO: {\n      configurable: true\n    }\n  };\n  Size.prototype.equals = function equals(other) {\n    return other && other.width === this.width && other.height === this.height;\n  };\n  Size.prototype.clone = function clone() {\n    return new Size(this.width, this.height);\n  };\n  Size.prototype.toArray = function toArray(digits) {\n    var doRound = defined(digits);\n    var width = doRound ? round(this.width, digits) : this.width;\n    var height = doRound ? round(this.height, digits) : this.height;\n    return [width, height];\n  };\n  Size.create = function create(arg0, arg1) {\n    if (defined(arg0)) {\n      if (arg0 instanceof Size) {\n        return arg0;\n      } else if (arguments.length === 1 && arg0.length === 2) {\n        return new Size(arg0[0], arg0[1]);\n      }\n      return new Size(arg0, arg1);\n    }\n  };\n  staticAccessors.ZERO.get = function () {\n    return new Size(0, 0);\n  };\n  Object.defineProperties(Size, staticAccessors);\n  return Size;\n}(withAccessors(HasObservers, [\"width\", \"height\"]));\nexport default Size;", "map": {"version": 3, "names": ["HasObservers", "withAccessors", "defined", "round", "Size", "superclass", "width", "height", "call", "__proto__", "prototype", "Object", "create", "constructor", "staticAccessors", "ZERO", "configurable", "equals", "other", "clone", "toArray", "digits", "doRound", "arg0", "arg1", "arguments", "length", "get", "defineProperties"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/geometry/size.js"], "sourcesContent": ["import HasObservers from '../core/has-observers';\nimport withAccessors from '../mixins/with-accessors';\nimport { defined, round } from '../util';\n\n\nvar Size = (function (superclass) {\n    function Size(width, height) {\n        superclass.call(this);\n\n        this.width = width || 0;\n        this.height = height || 0;\n    }\n\n    if ( superclass ) Size.__proto__ = superclass;\n    Size.prototype = Object.create( superclass && superclass.prototype );\n    Size.prototype.constructor = Size;\n\n    var staticAccessors = { ZERO: { configurable: true } };\n\n    Size.prototype.equals = function equals (other) {\n        return other && other.width === this.width && other.height === this.height;\n    };\n\n    Size.prototype.clone = function clone () {\n        return new Size(this.width, this.height);\n    };\n\n    Size.prototype.toArray = function toArray (digits) {\n        var doRound = defined(digits);\n        var width = doRound ? round(this.width, digits) : this.width;\n        var height = doRound ? round(this.height, digits) : this.height;\n\n        return [ width, height ];\n    };\n\n    Size.create = function create (arg0, arg1) {\n        if (defined(arg0)) {\n            if (arg0 instanceof Size) {\n                return arg0;\n            } else if (arguments.length === 1 && arg0.length === 2) {\n                return new Size(arg0[0], arg0[1]);\n            }\n\n            return new Size(arg0, arg1);\n        }\n    };\n\n    staticAccessors.ZERO.get = function () {\n        return new Size(0, 0);\n    };\n\n    Object.defineProperties( Size, staticAccessors );\n\n    return Size;\n}(withAccessors(HasObservers, [ \"width\", \"height\" ])));\n\nexport default Size;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,uBAAuB;AAChD,OAAOC,aAAa,MAAM,0BAA0B;AACpD,SAASC,OAAO,EAAEC,KAAK,QAAQ,SAAS;AAGxC,IAAIC,IAAI,GAAI,UAAUC,UAAU,EAAE;EAC9B,SAASD,IAAIA,CAACE,KAAK,EAAEC,MAAM,EAAE;IACzBF,UAAU,CAACG,IAAI,CAAC,IAAI,CAAC;IAErB,IAAI,CAACF,KAAK,GAAGA,KAAK,IAAI,CAAC;IACvB,IAAI,CAACC,MAAM,GAAGA,MAAM,IAAI,CAAC;EAC7B;EAEA,IAAKF,UAAU,EAAGD,IAAI,CAACK,SAAS,GAAGJ,UAAU;EAC7CD,IAAI,CAACM,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAEP,UAAU,IAAIA,UAAU,CAACK,SAAU,CAAC;EACpEN,IAAI,CAACM,SAAS,CAACG,WAAW,GAAGT,IAAI;EAEjC,IAAIU,eAAe,GAAG;IAAEC,IAAI,EAAE;MAAEC,YAAY,EAAE;IAAK;EAAE,CAAC;EAEtDZ,IAAI,CAACM,SAAS,CAACO,MAAM,GAAG,SAASA,MAAMA,CAAEC,KAAK,EAAE;IAC5C,OAAOA,KAAK,IAAIA,KAAK,CAACZ,KAAK,KAAK,IAAI,CAACA,KAAK,IAAIY,KAAK,CAACX,MAAM,KAAK,IAAI,CAACA,MAAM;EAC9E,CAAC;EAEDH,IAAI,CAACM,SAAS,CAACS,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAI;IACrC,OAAO,IAAIf,IAAI,CAAC,IAAI,CAACE,KAAK,EAAE,IAAI,CAACC,MAAM,CAAC;EAC5C,CAAC;EAEDH,IAAI,CAACM,SAAS,CAACU,OAAO,GAAG,SAASA,OAAOA,CAAEC,MAAM,EAAE;IAC/C,IAAIC,OAAO,GAAGpB,OAAO,CAACmB,MAAM,CAAC;IAC7B,IAAIf,KAAK,GAAGgB,OAAO,GAAGnB,KAAK,CAAC,IAAI,CAACG,KAAK,EAAEe,MAAM,CAAC,GAAG,IAAI,CAACf,KAAK;IAC5D,IAAIC,MAAM,GAAGe,OAAO,GAAGnB,KAAK,CAAC,IAAI,CAACI,MAAM,EAAEc,MAAM,CAAC,GAAG,IAAI,CAACd,MAAM;IAE/D,OAAO,CAAED,KAAK,EAAEC,MAAM,CAAE;EAC5B,CAAC;EAEDH,IAAI,CAACQ,MAAM,GAAG,SAASA,MAAMA,CAAEW,IAAI,EAAEC,IAAI,EAAE;IACvC,IAAItB,OAAO,CAACqB,IAAI,CAAC,EAAE;MACf,IAAIA,IAAI,YAAYnB,IAAI,EAAE;QACtB,OAAOmB,IAAI;MACf,CAAC,MAAM,IAAIE,SAAS,CAACC,MAAM,KAAK,CAAC,IAAIH,IAAI,CAACG,MAAM,KAAK,CAAC,EAAE;QACpD,OAAO,IAAItB,IAAI,CAACmB,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;MACrC;MAEA,OAAO,IAAInB,IAAI,CAACmB,IAAI,EAAEC,IAAI,CAAC;IAC/B;EACJ,CAAC;EAEDV,eAAe,CAACC,IAAI,CAACY,GAAG,GAAG,YAAY;IACnC,OAAO,IAAIvB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACzB,CAAC;EAEDO,MAAM,CAACiB,gBAAgB,CAAExB,IAAI,EAAEU,eAAgB,CAAC;EAEhD,OAAOV,IAAI;AACf,CAAC,CAACH,aAAa,CAACD,YAAY,EAAE,CAAE,OAAO,EAAE,QAAQ,CAAE,CAAC,CAAE;AAEtD,eAAeI,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}