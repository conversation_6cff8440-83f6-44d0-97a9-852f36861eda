{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport getScrollBarSize from \"rc-util/es/getScrollBarSize\";\nimport classNames from 'classnames';\nimport { getOffset } from \"rc-util/es/Dom/css\";\nimport TableContext from './context/TableContext';\nimport { useLayoutState } from './hooks/useFrame';\nvar StickyScrollBar = function StickyScrollBar(_ref, ref) {\n  var _scrollBodyRef$curren, _scrollBodyRef$curren2;\n  var scrollBodyRef = _ref.scrollBodyRef,\n    onScroll = _ref.onScroll,\n    offsetScroll = _ref.offsetScroll,\n    container = _ref.container;\n  var _React$useContext = React.useContext(TableContext),\n    prefixCls = _React$useContext.prefixCls;\n  var bodyScrollWidth = ((_scrollBodyRef$curren = scrollBodyRef.current) === null || _scrollBodyRef$curren === void 0 ? void 0 : _scrollBodyRef$curren.scrollWidth) || 0;\n  var bodyWidth = ((_scrollBodyRef$curren2 = scrollBodyRef.current) === null || _scrollBodyRef$curren2 === void 0 ? void 0 : _scrollBodyRef$curren2.clientWidth) || 0;\n  var scrollBarWidth = bodyScrollWidth && bodyWidth * (bodyWidth / bodyScrollWidth);\n  var scrollBarRef = React.useRef();\n  var _useLayoutState = useLayoutState({\n      scrollLeft: 0,\n      isHiddenScrollBar: false\n    }),\n    _useLayoutState2 = _slicedToArray(_useLayoutState, 2),\n    scrollState = _useLayoutState2[0],\n    setScrollState = _useLayoutState2[1];\n  var refState = React.useRef({\n    delta: 0,\n    x: 0\n  });\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    isActive = _React$useState2[0],\n    setActive = _React$useState2[1];\n  var onMouseUp = function onMouseUp() {\n    setActive(false);\n  };\n  var onMouseDown = function onMouseDown(event) {\n    event.persist();\n    refState.current.delta = event.pageX - scrollState.scrollLeft;\n    refState.current.x = 0;\n    setActive(true);\n    event.preventDefault();\n  };\n  var onMouseMove = function onMouseMove(event) {\n    var _window;\n\n    // https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/buttons\n    var _ref2 = event || ((_window = window) === null || _window === void 0 ? void 0 : _window.event),\n      buttons = _ref2.buttons;\n    if (!isActive || buttons === 0) {\n      // If out body mouse up, we can set isActive false when mouse move\n      if (isActive) {\n        setActive(false);\n      }\n      return;\n    }\n    var left = refState.current.x + event.pageX - refState.current.x - refState.current.delta;\n    if (left <= 0) {\n      left = 0;\n    }\n    if (left + scrollBarWidth >= bodyWidth) {\n      left = bodyWidth - scrollBarWidth;\n    }\n    onScroll({\n      scrollLeft: left / bodyWidth * (bodyScrollWidth + 2)\n    });\n    refState.current.x = event.pageX;\n  };\n  var onContainerScroll = function onContainerScroll() {\n    if (!scrollBodyRef.current) {\n      return;\n    }\n    var tableOffsetTop = getOffset(scrollBodyRef.current).top;\n    var tableBottomOffset = tableOffsetTop + scrollBodyRef.current.offsetHeight;\n    var currentClientOffset = container === window ? document.documentElement.scrollTop + window.innerHeight : getOffset(container).top + container.clientHeight;\n    if (tableBottomOffset - getScrollBarSize() <= currentClientOffset || tableOffsetTop >= currentClientOffset - offsetScroll) {\n      setScrollState(function (state) {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          isHiddenScrollBar: true\n        });\n      });\n    } else {\n      setScrollState(function (state) {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          isHiddenScrollBar: false\n        });\n      });\n    }\n  };\n  var setScrollLeft = function setScrollLeft(left) {\n    setScrollState(function (state) {\n      return _objectSpread(_objectSpread({}, state), {}, {\n        scrollLeft: left / bodyScrollWidth * bodyWidth || 0\n      });\n    });\n  };\n  React.useImperativeHandle(ref, function () {\n    return {\n      setScrollLeft: setScrollLeft\n    };\n  });\n  React.useEffect(function () {\n    var onMouseUpListener = addEventListener(document.body, 'mouseup', onMouseUp, false);\n    var onMouseMoveListener = addEventListener(document.body, 'mousemove', onMouseMove, false);\n    onContainerScroll();\n    return function () {\n      onMouseUpListener.remove();\n      onMouseMoveListener.remove();\n    };\n  }, [scrollBarWidth, isActive]);\n  React.useEffect(function () {\n    var onScrollListener = addEventListener(container, 'scroll', onContainerScroll, false);\n    var onResizeListener = addEventListener(window, 'resize', onContainerScroll, false);\n    return function () {\n      onScrollListener.remove();\n      onResizeListener.remove();\n    };\n  }, [container]);\n  React.useEffect(function () {\n    if (!scrollState.isHiddenScrollBar) {\n      setScrollState(function (state) {\n        var bodyNode = scrollBodyRef.current;\n        if (!bodyNode) {\n          return state;\n        }\n        return _objectSpread(_objectSpread({}, state), {}, {\n          scrollLeft: bodyNode.scrollLeft / bodyNode.scrollWidth * bodyNode.clientWidth\n        });\n      });\n    }\n  }, [scrollState.isHiddenScrollBar]);\n  if (bodyScrollWidth <= bodyWidth || !scrollBarWidth || scrollState.isHiddenScrollBar) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      height: getScrollBarSize(),\n      width: bodyWidth,\n      bottom: offsetScroll\n    },\n    className: \"\".concat(prefixCls, \"-sticky-scroll\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    onMouseDown: onMouseDown,\n    ref: scrollBarRef,\n    className: classNames(\"\".concat(prefixCls, \"-sticky-scroll-bar\"), _defineProperty({}, \"\".concat(prefixCls, \"-sticky-scroll-bar-active\"), isActive)),\n    style: {\n      width: \"\".concat(scrollBarWidth, \"px\"),\n      transform: \"translate3d(\".concat(scrollState.scrollLeft, \"px, 0, 0)\")\n    }\n  }));\n};\nexport default /*#__PURE__*/React.forwardRef(StickyScrollBar);", "map": {"version": 3, "names": ["_defineProperty", "_objectSpread", "_slicedToArray", "React", "addEventListener", "getScrollBarSize", "classNames", "getOffset", "TableContext", "useLayoutState", "StickyScrollBar", "_ref", "ref", "_scrollBodyRef$curren", "_scrollBodyRef$curren2", "scrollBodyRef", "onScroll", "offsetScroll", "container", "_React$useContext", "useContext", "prefixCls", "bodyScrollWidth", "current", "scrollWidth", "bodyWidth", "clientWidth", "scrollBarWidth", "scrollBarRef", "useRef", "_useLayoutState", "scrollLeft", "isHiddenScrollBar", "_useLayoutState2", "scrollState", "setScrollState", "refState", "delta", "x", "_React$useState", "useState", "_React$useState2", "isActive", "setActive", "onMouseUp", "onMouseDown", "event", "persist", "pageX", "preventDefault", "onMouseMove", "_window", "_ref2", "window", "buttons", "left", "onContainerScroll", "tableOffsetTop", "top", "tableBottomOffset", "offsetHeight", "currentClientOffset", "document", "documentElement", "scrollTop", "innerHeight", "clientHeight", "state", "setScrollLeft", "useImperativeHandle", "useEffect", "onMouseUpListener", "body", "onMouseMoveListener", "remove", "onScrollListener", "onResizeListener", "bodyNode", "createElement", "style", "height", "width", "bottom", "className", "concat", "transform", "forwardRef"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/rc-table/es/stickyScrollBar.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport getScrollBarSize from \"rc-util/es/getScrollBarSize\";\nimport classNames from 'classnames';\nimport { getOffset } from \"rc-util/es/Dom/css\";\nimport TableContext from './context/TableContext';\nimport { useLayoutState } from './hooks/useFrame';\n\nvar StickyScrollBar = function StickyScrollBar(_ref, ref) {\n  var _scrollBodyRef$curren, _scrollBodyRef$curren2;\n\n  var scrollBodyRef = _ref.scrollBodyRef,\n      onScroll = _ref.onScroll,\n      offsetScroll = _ref.offsetScroll,\n      container = _ref.container;\n\n  var _React$useContext = React.useContext(TableContext),\n      prefixCls = _React$useContext.prefixCls;\n\n  var bodyScrollWidth = ((_scrollBodyRef$curren = scrollBodyRef.current) === null || _scrollBodyRef$curren === void 0 ? void 0 : _scrollBodyRef$curren.scrollWidth) || 0;\n  var bodyWidth = ((_scrollBodyRef$curren2 = scrollBodyRef.current) === null || _scrollBodyRef$curren2 === void 0 ? void 0 : _scrollBodyRef$curren2.clientWidth) || 0;\n  var scrollBarWidth = bodyScrollWidth && bodyWidth * (bodyWidth / bodyScrollWidth);\n  var scrollBarRef = React.useRef();\n\n  var _useLayoutState = useLayoutState({\n    scrollLeft: 0,\n    isHiddenScrollBar: false\n  }),\n      _useLayoutState2 = _slicedToArray(_useLayoutState, 2),\n      scrollState = _useLayoutState2[0],\n      setScrollState = _useLayoutState2[1];\n\n  var refState = React.useRef({\n    delta: 0,\n    x: 0\n  });\n\n  var _React$useState = React.useState(false),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      isActive = _React$useState2[0],\n      setActive = _React$useState2[1];\n\n  var onMouseUp = function onMouseUp() {\n    setActive(false);\n  };\n\n  var onMouseDown = function onMouseDown(event) {\n    event.persist();\n    refState.current.delta = event.pageX - scrollState.scrollLeft;\n    refState.current.x = 0;\n    setActive(true);\n    event.preventDefault();\n  };\n\n  var onMouseMove = function onMouseMove(event) {\n    var _window;\n\n    // https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/buttons\n    var _ref2 = event || ((_window = window) === null || _window === void 0 ? void 0 : _window.event),\n        buttons = _ref2.buttons;\n\n    if (!isActive || buttons === 0) {\n      // If out body mouse up, we can set isActive false when mouse move\n      if (isActive) {\n        setActive(false);\n      }\n\n      return;\n    }\n\n    var left = refState.current.x + event.pageX - refState.current.x - refState.current.delta;\n\n    if (left <= 0) {\n      left = 0;\n    }\n\n    if (left + scrollBarWidth >= bodyWidth) {\n      left = bodyWidth - scrollBarWidth;\n    }\n\n    onScroll({\n      scrollLeft: left / bodyWidth * (bodyScrollWidth + 2)\n    });\n    refState.current.x = event.pageX;\n  };\n\n  var onContainerScroll = function onContainerScroll() {\n    if (!scrollBodyRef.current) {\n      return;\n    }\n\n    var tableOffsetTop = getOffset(scrollBodyRef.current).top;\n    var tableBottomOffset = tableOffsetTop + scrollBodyRef.current.offsetHeight;\n    var currentClientOffset = container === window ? document.documentElement.scrollTop + window.innerHeight : getOffset(container).top + container.clientHeight;\n\n    if (tableBottomOffset - getScrollBarSize() <= currentClientOffset || tableOffsetTop >= currentClientOffset - offsetScroll) {\n      setScrollState(function (state) {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          isHiddenScrollBar: true\n        });\n      });\n    } else {\n      setScrollState(function (state) {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          isHiddenScrollBar: false\n        });\n      });\n    }\n  };\n\n  var setScrollLeft = function setScrollLeft(left) {\n    setScrollState(function (state) {\n      return _objectSpread(_objectSpread({}, state), {}, {\n        scrollLeft: left / bodyScrollWidth * bodyWidth || 0\n      });\n    });\n  };\n\n  React.useImperativeHandle(ref, function () {\n    return {\n      setScrollLeft: setScrollLeft\n    };\n  });\n  React.useEffect(function () {\n    var onMouseUpListener = addEventListener(document.body, 'mouseup', onMouseUp, false);\n    var onMouseMoveListener = addEventListener(document.body, 'mousemove', onMouseMove, false);\n    onContainerScroll();\n    return function () {\n      onMouseUpListener.remove();\n      onMouseMoveListener.remove();\n    };\n  }, [scrollBarWidth, isActive]);\n  React.useEffect(function () {\n    var onScrollListener = addEventListener(container, 'scroll', onContainerScroll, false);\n    var onResizeListener = addEventListener(window, 'resize', onContainerScroll, false);\n    return function () {\n      onScrollListener.remove();\n      onResizeListener.remove();\n    };\n  }, [container]);\n  React.useEffect(function () {\n    if (!scrollState.isHiddenScrollBar) {\n      setScrollState(function (state) {\n        var bodyNode = scrollBodyRef.current;\n\n        if (!bodyNode) {\n          return state;\n        }\n\n        return _objectSpread(_objectSpread({}, state), {}, {\n          scrollLeft: bodyNode.scrollLeft / bodyNode.scrollWidth * bodyNode.clientWidth\n        });\n      });\n    }\n  }, [scrollState.isHiddenScrollBar]);\n\n  if (bodyScrollWidth <= bodyWidth || !scrollBarWidth || scrollState.isHiddenScrollBar) {\n    return null;\n  }\n\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      height: getScrollBarSize(),\n      width: bodyWidth,\n      bottom: offsetScroll\n    },\n    className: \"\".concat(prefixCls, \"-sticky-scroll\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    onMouseDown: onMouseDown,\n    ref: scrollBarRef,\n    className: classNames(\"\".concat(prefixCls, \"-sticky-scroll-bar\"), _defineProperty({}, \"\".concat(prefixCls, \"-sticky-scroll-bar-active\"), isActive)),\n    style: {\n      width: \"\".concat(scrollBarWidth, \"px\"),\n      transform: \"translate3d(\".concat(scrollState.scrollLeft, \"px, 0, 0)\")\n    }\n  }));\n};\n\nexport default /*#__PURE__*/React.forwardRef(StickyScrollBar);"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,OAAOC,YAAY,MAAM,wBAAwB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AAEjD,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,IAAI,EAAEC,GAAG,EAAE;EACxD,IAAIC,qBAAqB,EAAEC,sBAAsB;EAEjD,IAAIC,aAAa,GAAGJ,IAAI,CAACI,aAAa;IAClCC,QAAQ,GAAGL,IAAI,CAACK,QAAQ;IACxBC,YAAY,GAAGN,IAAI,CAACM,YAAY;IAChCC,SAAS,GAAGP,IAAI,CAACO,SAAS;EAE9B,IAAIC,iBAAiB,GAAGhB,KAAK,CAACiB,UAAU,CAACZ,YAAY,CAAC;IAClDa,SAAS,GAAGF,iBAAiB,CAACE,SAAS;EAE3C,IAAIC,eAAe,GAAG,CAAC,CAACT,qBAAqB,GAAGE,aAAa,CAACQ,OAAO,MAAM,IAAI,IAAIV,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACW,WAAW,KAAK,CAAC;EACtK,IAAIC,SAAS,GAAG,CAAC,CAACX,sBAAsB,GAAGC,aAAa,CAACQ,OAAO,MAAM,IAAI,IAAIT,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACY,WAAW,KAAK,CAAC;EACnK,IAAIC,cAAc,GAAGL,eAAe,IAAIG,SAAS,IAAIA,SAAS,GAAGH,eAAe,CAAC;EACjF,IAAIM,YAAY,GAAGzB,KAAK,CAAC0B,MAAM,CAAC,CAAC;EAEjC,IAAIC,eAAe,GAAGrB,cAAc,CAAC;MACnCsB,UAAU,EAAE,CAAC;MACbC,iBAAiB,EAAE;IACrB,CAAC,CAAC;IACEC,gBAAgB,GAAG/B,cAAc,CAAC4B,eAAe,EAAE,CAAC,CAAC;IACrDI,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAExC,IAAIG,QAAQ,GAAGjC,KAAK,CAAC0B,MAAM,CAAC;IAC1BQ,KAAK,EAAE,CAAC;IACRC,CAAC,EAAE;EACL,CAAC,CAAC;EAEF,IAAIC,eAAe,GAAGpC,KAAK,CAACqC,QAAQ,CAAC,KAAK,CAAC;IACvCC,gBAAgB,GAAGvC,cAAc,CAACqC,eAAe,EAAE,CAAC,CAAC;IACrDG,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC9BE,SAAS,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEnC,IAAIG,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnCD,SAAS,CAAC,KAAK,CAAC;EAClB,CAAC;EAED,IAAIE,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAE;IAC5CA,KAAK,CAACC,OAAO,CAAC,CAAC;IACfX,QAAQ,CAACb,OAAO,CAACc,KAAK,GAAGS,KAAK,CAACE,KAAK,GAAGd,WAAW,CAACH,UAAU;IAC7DK,QAAQ,CAACb,OAAO,CAACe,CAAC,GAAG,CAAC;IACtBK,SAAS,CAAC,IAAI,CAAC;IACfG,KAAK,CAACG,cAAc,CAAC,CAAC;EACxB,CAAC;EAED,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACJ,KAAK,EAAE;IAC5C,IAAIK,OAAO;;IAEX;IACA,IAAIC,KAAK,GAAGN,KAAK,KAAK,CAACK,OAAO,GAAGE,MAAM,MAAM,IAAI,IAAIF,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACL,KAAK,CAAC;MAC7FQ,OAAO,GAAGF,KAAK,CAACE,OAAO;IAE3B,IAAI,CAACZ,QAAQ,IAAIY,OAAO,KAAK,CAAC,EAAE;MAC9B;MACA,IAAIZ,QAAQ,EAAE;QACZC,SAAS,CAAC,KAAK,CAAC;MAClB;MAEA;IACF;IAEA,IAAIY,IAAI,GAAGnB,QAAQ,CAACb,OAAO,CAACe,CAAC,GAAGQ,KAAK,CAACE,KAAK,GAAGZ,QAAQ,CAACb,OAAO,CAACe,CAAC,GAAGF,QAAQ,CAACb,OAAO,CAACc,KAAK;IAEzF,IAAIkB,IAAI,IAAI,CAAC,EAAE;MACbA,IAAI,GAAG,CAAC;IACV;IAEA,IAAIA,IAAI,GAAG5B,cAAc,IAAIF,SAAS,EAAE;MACtC8B,IAAI,GAAG9B,SAAS,GAAGE,cAAc;IACnC;IAEAX,QAAQ,CAAC;MACPe,UAAU,EAAEwB,IAAI,GAAG9B,SAAS,IAAIH,eAAe,GAAG,CAAC;IACrD,CAAC,CAAC;IACFc,QAAQ,CAACb,OAAO,CAACe,CAAC,GAAGQ,KAAK,CAACE,KAAK;EAClC,CAAC;EAED,IAAIQ,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;IACnD,IAAI,CAACzC,aAAa,CAACQ,OAAO,EAAE;MAC1B;IACF;IAEA,IAAIkC,cAAc,GAAGlD,SAAS,CAACQ,aAAa,CAACQ,OAAO,CAAC,CAACmC,GAAG;IACzD,IAAIC,iBAAiB,GAAGF,cAAc,GAAG1C,aAAa,CAACQ,OAAO,CAACqC,YAAY;IAC3E,IAAIC,mBAAmB,GAAG3C,SAAS,KAAKmC,MAAM,GAAGS,QAAQ,CAACC,eAAe,CAACC,SAAS,GAAGX,MAAM,CAACY,WAAW,GAAG1D,SAAS,CAACW,SAAS,CAAC,CAACwC,GAAG,GAAGxC,SAAS,CAACgD,YAAY;IAE5J,IAAIP,iBAAiB,GAAGtD,gBAAgB,CAAC,CAAC,IAAIwD,mBAAmB,IAAIJ,cAAc,IAAII,mBAAmB,GAAG5C,YAAY,EAAE;MACzHkB,cAAc,CAAC,UAAUgC,KAAK,EAAE;QAC9B,OAAOlE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACjDnC,iBAAiB,EAAE;QACrB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,MAAM;MACLG,cAAc,CAAC,UAAUgC,KAAK,EAAE;QAC9B,OAAOlE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACjDnC,iBAAiB,EAAE;QACrB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC;EAED,IAAIoC,aAAa,GAAG,SAASA,aAAaA,CAACb,IAAI,EAAE;IAC/CpB,cAAc,CAAC,UAAUgC,KAAK,EAAE;MAC9B,OAAOlE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACjDpC,UAAU,EAAEwB,IAAI,GAAGjC,eAAe,GAAGG,SAAS,IAAI;MACpD,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EAEDtB,KAAK,CAACkE,mBAAmB,CAACzD,GAAG,EAAE,YAAY;IACzC,OAAO;MACLwD,aAAa,EAAEA;IACjB,CAAC;EACH,CAAC,CAAC;EACFjE,KAAK,CAACmE,SAAS,CAAC,YAAY;IAC1B,IAAIC,iBAAiB,GAAGnE,gBAAgB,CAAC0D,QAAQ,CAACU,IAAI,EAAE,SAAS,EAAE5B,SAAS,EAAE,KAAK,CAAC;IACpF,IAAI6B,mBAAmB,GAAGrE,gBAAgB,CAAC0D,QAAQ,CAACU,IAAI,EAAE,WAAW,EAAEtB,WAAW,EAAE,KAAK,CAAC;IAC1FM,iBAAiB,CAAC,CAAC;IACnB,OAAO,YAAY;MACjBe,iBAAiB,CAACG,MAAM,CAAC,CAAC;MAC1BD,mBAAmB,CAACC,MAAM,CAAC,CAAC;IAC9B,CAAC;EACH,CAAC,EAAE,CAAC/C,cAAc,EAAEe,QAAQ,CAAC,CAAC;EAC9BvC,KAAK,CAACmE,SAAS,CAAC,YAAY;IAC1B,IAAIK,gBAAgB,GAAGvE,gBAAgB,CAACc,SAAS,EAAE,QAAQ,EAAEsC,iBAAiB,EAAE,KAAK,CAAC;IACtF,IAAIoB,gBAAgB,GAAGxE,gBAAgB,CAACiD,MAAM,EAAE,QAAQ,EAAEG,iBAAiB,EAAE,KAAK,CAAC;IACnF,OAAO,YAAY;MACjBmB,gBAAgB,CAACD,MAAM,CAAC,CAAC;MACzBE,gBAAgB,CAACF,MAAM,CAAC,CAAC;IAC3B,CAAC;EACH,CAAC,EAAE,CAACxD,SAAS,CAAC,CAAC;EACff,KAAK,CAACmE,SAAS,CAAC,YAAY;IAC1B,IAAI,CAACpC,WAAW,CAACF,iBAAiB,EAAE;MAClCG,cAAc,CAAC,UAAUgC,KAAK,EAAE;QAC9B,IAAIU,QAAQ,GAAG9D,aAAa,CAACQ,OAAO;QAEpC,IAAI,CAACsD,QAAQ,EAAE;UACb,OAAOV,KAAK;QACd;QAEA,OAAOlE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACjDpC,UAAU,EAAE8C,QAAQ,CAAC9C,UAAU,GAAG8C,QAAQ,CAACrD,WAAW,GAAGqD,QAAQ,CAACnD;QACpE,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACQ,WAAW,CAACF,iBAAiB,CAAC,CAAC;EAEnC,IAAIV,eAAe,IAAIG,SAAS,IAAI,CAACE,cAAc,IAAIO,WAAW,CAACF,iBAAiB,EAAE;IACpF,OAAO,IAAI;EACb;EAEA,OAAO,aAAa7B,KAAK,CAAC2E,aAAa,CAAC,KAAK,EAAE;IAC7CC,KAAK,EAAE;MACLC,MAAM,EAAE3E,gBAAgB,CAAC,CAAC;MAC1B4E,KAAK,EAAExD,SAAS;MAChByD,MAAM,EAAEjE;IACV,CAAC;IACDkE,SAAS,EAAE,EAAE,CAACC,MAAM,CAAC/D,SAAS,EAAE,gBAAgB;EAClD,CAAC,EAAE,aAAalB,KAAK,CAAC2E,aAAa,CAAC,KAAK,EAAE;IACzCjC,WAAW,EAAEA,WAAW;IACxBjC,GAAG,EAAEgB,YAAY;IACjBuD,SAAS,EAAE7E,UAAU,CAAC,EAAE,CAAC8E,MAAM,CAAC/D,SAAS,EAAE,oBAAoB,CAAC,EAAErB,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACoF,MAAM,CAAC/D,SAAS,EAAE,2BAA2B,CAAC,EAAEqB,QAAQ,CAAC,CAAC;IACnJqC,KAAK,EAAE;MACLE,KAAK,EAAE,EAAE,CAACG,MAAM,CAACzD,cAAc,EAAE,IAAI,CAAC;MACtC0D,SAAS,EAAE,cAAc,CAACD,MAAM,CAAClD,WAAW,CAACH,UAAU,EAAE,WAAW;IACtE;EACF,CAAC,CAAC,CAAC;AACL,CAAC;AAED,eAAe,aAAa5B,KAAK,CAACmF,UAAU,CAAC5E,eAAe,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}