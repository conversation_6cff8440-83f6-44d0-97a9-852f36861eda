{"ast": null, "code": "/* eslint-disable key-spacing,no-multi-spaces,no-param-reassign */\n\nvar literals = {\n  1: \"i\",\n  10: \"x\",\n  100: \"c\",\n  2: \"ii\",\n  20: \"xx\",\n  200: \"cc\",\n  3: \"iii\",\n  30: \"xxx\",\n  300: \"ccc\",\n  4: \"iv\",\n  40: \"xl\",\n  400: \"cd\",\n  5: \"v\",\n  50: \"l\",\n  500: \"d\",\n  6: \"vi\",\n  60: \"lx\",\n  600: \"dc\",\n  7: \"vii\",\n  70: \"lxx\",\n  700: \"dcc\",\n  8: \"viii\",\n  80: \"lxxx\",\n  800: \"dccc\",\n  9: \"ix\",\n  90: \"xc\",\n  900: \"cm\",\n  1000: \"m\"\n};\nexport default function arabicToRoman(n) {\n  var values = [1000, 900, 800, 700, 600, 500, 400, 300, 200, 100, 90, 80, 70, 60, 50, 40, 30, 20, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1];\n  var roman = \"\";\n  while (n > 0) {\n    if (n < values[0]) {\n      values.shift();\n    } else {\n      roman += literals[values[0]];\n      n -= values[0];\n    }\n  }\n  return roman;\n}", "map": {"version": 3, "names": ["literals", "arabicToRoman", "n", "values", "roman", "shift"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/util/arabic-to-roman.js"], "sourcesContent": ["/* eslint-disable key-spacing,no-multi-spaces,no-param-reassign */\n\nvar literals = {\n    1    : \"i\",       10   : \"x\",       100  : \"c\",\n    2    : \"ii\",      20   : \"xx\",      200  : \"cc\",\n    3    : \"iii\",     30   : \"xxx\",     300  : \"ccc\",\n    4    : \"iv\",      40   : \"xl\",      400  : \"cd\",\n    5    : \"v\",       50   : \"l\",       500  : \"d\",\n    6    : \"vi\",      60   : \"lx\",      600  : \"dc\",\n    7    : \"vii\",     70   : \"lxx\",     700  : \"dcc\",\n    8    : \"viii\",    80   : \"lxxx\",    800  : \"dccc\",\n    9    : \"ix\",      90   : \"xc\",      900  : \"cm\",\n    1000 : \"m\"\n};\n\nexport default function arabicToRoman(n) {\n    var values = [ 1000,\n        900 , 800, 700, 600, 500, 400, 300, 200, 100,\n        90  , 80 , 70 , 60 , 50 , 40 , 30 , 20 , 10 ,\n        9   , 8  , 7  , 6  , 5  , 4  , 3  , 2  , 1 ];\n\n    var roman = \"\";\n    while (n > 0) {\n        if (n < values[0]) {\n            values.shift();\n        } else {\n            roman += literals[values[0]];\n            n -= values[0];\n        }\n    }\n    return roman;\n}\n"], "mappings": "AAAA;;AAEA,IAAIA,QAAQ,GAAG;EACX,CAAC,EAAM,GAAG;EAAQ,EAAE,EAAK,GAAG;EAAQ,GAAG,EAAI,GAAG;EAC9C,CAAC,EAAM,IAAI;EAAO,EAAE,EAAK,IAAI;EAAO,GAAG,EAAI,IAAI;EAC/C,CAAC,EAAM,KAAK;EAAM,EAAE,EAAK,KAAK;EAAM,GAAG,EAAI,KAAK;EAChD,CAAC,EAAM,IAAI;EAAO,EAAE,EAAK,IAAI;EAAO,GAAG,EAAI,IAAI;EAC/C,CAAC,EAAM,GAAG;EAAQ,EAAE,EAAK,GAAG;EAAQ,GAAG,EAAI,GAAG;EAC9C,CAAC,EAAM,IAAI;EAAO,EAAE,EAAK,IAAI;EAAO,GAAG,EAAI,IAAI;EAC/C,CAAC,EAAM,KAAK;EAAM,EAAE,EAAK,KAAK;EAAM,GAAG,EAAI,KAAK;EAChD,CAAC,EAAM,MAAM;EAAK,EAAE,EAAK,MAAM;EAAK,GAAG,EAAI,MAAM;EACjD,CAAC,EAAM,IAAI;EAAO,EAAE,EAAK,IAAI;EAAO,GAAG,EAAI,IAAI;EAC/C,IAAI,EAAG;AACX,CAAC;AAED,eAAe,SAASC,aAAaA,CAACC,CAAC,EAAE;EACrC,IAAIC,MAAM,GAAG,CAAE,IAAI,EACf,GAAG,EAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5C,EAAE,EAAI,EAAE,EAAG,EAAE,EAAG,EAAE,EAAG,EAAE,EAAG,EAAE,EAAG,EAAE,EAAG,EAAE,EAAG,EAAE,EAC3C,CAAC,EAAK,CAAC,EAAI,CAAC,EAAI,CAAC,EAAI,CAAC,EAAI,CAAC,EAAI,CAAC,EAAI,CAAC,EAAI,CAAC,CAAE;EAEhD,IAAIC,KAAK,GAAG,EAAE;EACd,OAAOF,CAAC,GAAG,CAAC,EAAE;IACV,IAAIA,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC,EAAE;MACfA,MAAM,CAACE,KAAK,CAAC,CAAC;IAClB,CAAC,MAAM;MACHD,KAAK,IAAIJ,QAAQ,CAACG,MAAM,CAAC,CAAC,CAAC,CAAC;MAC5BD,CAAC,IAAIC,MAAM,CAAC,CAAC,CAAC;IAClB;EACJ;EACA,OAAOC,KAAK;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}