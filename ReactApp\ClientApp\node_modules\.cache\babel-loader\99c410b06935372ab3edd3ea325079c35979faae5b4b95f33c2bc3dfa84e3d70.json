{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { memoizeOne as f, RowHeightService as d } from \"@progress/kendo-react-common\";\nimport * as p from \"react\";\nconst S = 17895697,\n  r = typeof window != \"undefined\" && /Firefox/.test(window.navigator.userAgent);\nclass u {\n  constructor() {\n    this.table = null, this.total = 0, this.enabled = !1, this.skip = 0, this.pageSize = 0, this.PageChange = null, this.scrollElement = null, this.listTransform = \"\", this.itemHeight = 0, this.containerHeight = 0, this.reactVersion = Number.parseFloat(p.version), this.scrollSyncing = !1, this.lastLoaded = 0, this.firstLoaded = 0, this.lastScrollTop = 0, this.listTranslate = 0, this.list = null, this.container = null, this.calcScrollElementHeight = () => {\n      var h;\n      this.scrollSyncing = !0;\n      let t = !1;\n      this.itemHeight = this.list ? this.list.children[0].offsetHeight : this.itemHeight;\n      const i = this.itemHeight * this.total - (((h = this.list) == null ? void 0 : h.offsetHeight) || 0);\n      this.containerHeight = r ? Math.min(S, i) : i;\n      const s = this.containerHeight;\n      return this.scrollElement && (t = this.scrollElement.style.height !== s + \"px\", t && (this.scrollElement.style.height = s + \"px\")), this.scrollSyncing = !1, t;\n    }, this.scrollerRef = t => {\n      this.container = t, t && (t.setAttribute(\"unselectable\", \"on\"), window.setTimeout(this.calcScrollElementHeight.bind(this), 0));\n    }, this.getRowHeightService = f((t, i) => new d(i, t)), this.firstLoaded = this.pageSize, this.lastLoaded = this.skip + this.pageSize, this.scrollHandler = this.scrollHandler.bind(this);\n  }\n  get translate() {\n    return this.listTranslate;\n  }\n  translateTo(t, i) {\n    this.listTranslate = t, this.enabled && this.list && (r || this.reactVersion <= 17 || i ? this.list.style.transform = \"translateY(\" + t + \"px)\" : this.listTransform = \"translateY(\" + t + \"px)\");\n  }\n  changePage(t, i) {\n    const s = Math.min(Math.max(0, t), this.total - this.pageSize);\n    s !== this.skip && this.PageChange && this.PageChange({\n      skip: s,\n      take: this.pageSize\n    }, i);\n  }\n  reset() {\n    this.container && (this.calcScrollElementHeight(), this.container.scrollTop = 0, this.translateTo(0, !0));\n  }\n  scrollToEnd() {\n    if (this.container && this.list) {\n      this.calcScrollElementHeight();\n      const {\n        scrollHeight: t,\n        offsetHeight: i\n      } = this.container;\n      this.container.scrollTop = t, this.translateTo(t - i, !0);\n    }\n  }\n  scrollHandler(t) {\n    if (!this.enabled || !this.container || !this.list) return;\n    if (this.scrollSyncing) {\n      this.scrollSyncing = !1;\n      return;\n    }\n    this.rowHeightService = this.getRowHeightService(this.itemHeight, this.total);\n    const i = this.container.scrollTop,\n      s = this.lastScrollTop >= i,\n      h = !s,\n      n = Math.abs(i - this.lastScrollTop);\n    this.lastScrollTop = i;\n    let e = this.rowHeightService.index(i),\n      a = this.rowHeightService.offset(e);\n    const {\n        offsetHeight: c\n      } = this.container,\n      g = this.rowHeightService.index(i + c);\n    if (h && g >= this.lastLoaded && this.lastLoaded < this.total) {\n      const l = e + this.pageSize - this.total;\n      l > 0 && (e = e - l, a = this.rowHeightService.offset(e)), this.firstLoaded = e, this.translateTo(a);\n      const o = this.firstLoaded + this.pageSize;\n      this.lastLoaded = Math.min(o, this.total), this.changePage(this.firstLoaded, t);\n    } else if (s && e - 1 <= this.firstLoaded) {\n      const l = n === this.itemHeight;\n      if (this.skip === 0 && l) this.lastScrollTop = 0, this.firstLoaded = 0, this.lastLoaded = this.pageSize, this.container.scrollTop = 0, this.translateTo(0, !0);else {\n        const o = Math.floor(this.pageSize * 0.3);\n        this.firstLoaded = Math.max(e - o, 0), this.translateTo(this.rowHeightService.offset(this.firstLoaded)), this.lastLoaded = Math.min(this.firstLoaded + this.pageSize, this.total), this.changePage(this.firstLoaded, t);\n      }\n    }\n  }\n}\nexport { u as VirtualScroll };", "map": {"version": 3, "names": ["memoizeOne", "f", "RowHeightService", "d", "p", "S", "r", "window", "test", "navigator", "userAgent", "u", "constructor", "table", "total", "enabled", "skip", "pageSize", "PageChange", "scrollElement", "listTransform", "itemHeight", "containerHeight", "reactVersion", "Number", "parseFloat", "version", "scrollSyncing", "lastLoaded", "firstLoaded", "lastScrollTop", "listTranslate", "list", "container", "calcScrollElementHeight", "h", "t", "children", "offsetHeight", "i", "Math", "min", "s", "style", "height", "scrollerRef", "setAttribute", "setTimeout", "bind", "getRowHeightService", "<PERSON><PERSON><PERSON><PERSON>", "translate", "translateTo", "transform", "changePage", "max", "take", "reset", "scrollTop", "scrollToEnd", "scrollHeight", "rowHeightService", "n", "abs", "e", "index", "a", "offset", "c", "g", "l", "o", "floor", "VirtualScroll"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dropdowns/common/VirtualScrollStatic.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { memoizeOne as f, RowHeightService as d } from \"@progress/kendo-react-common\";\nimport * as p from \"react\";\nconst S = 17895697, r = typeof window != \"undefined\" && /Firefox/.test(window.navigator.userAgent);\nclass u {\n  constructor() {\n    this.table = null, this.total = 0, this.enabled = !1, this.skip = 0, this.pageSize = 0, this.PageChange = null, this.scrollElement = null, this.listTransform = \"\", this.itemHeight = 0, this.containerHeight = 0, this.reactVersion = Number.parseFloat(p.version), this.scrollSyncing = !1, this.lastLoaded = 0, this.firstLoaded = 0, this.lastScrollTop = 0, this.listTranslate = 0, this.list = null, this.container = null, this.calcScrollElementHeight = () => {\n      var h;\n      this.scrollSyncing = !0;\n      let t = !1;\n      this.itemHeight = this.list ? this.list.children[0].offsetHeight : this.itemHeight;\n      const i = this.itemHeight * this.total - (((h = this.list) == null ? void 0 : h.offsetHeight) || 0);\n      this.containerHeight = r ? Math.min(S, i) : i;\n      const s = this.containerHeight;\n      return this.scrollElement && (t = this.scrollElement.style.height !== s + \"px\", t && (this.scrollElement.style.height = s + \"px\")), this.scrollSyncing = !1, t;\n    }, this.scrollerRef = (t) => {\n      this.container = t, t && (t.setAttribute(\"unselectable\", \"on\"), window.setTimeout(this.calcScrollElementHeight.bind(this), 0));\n    }, this.getRowHeightService = f((t, i) => new d(i, t)), this.firstLoaded = this.pageSize, this.lastLoaded = this.skip + this.pageSize, this.scrollHandler = this.scrollHandler.bind(this);\n  }\n  get translate() {\n    return this.listTranslate;\n  }\n  translateTo(t, i) {\n    this.listTranslate = t, this.enabled && this.list && (r || this.reactVersion <= 17 || i ? this.list.style.transform = \"translateY(\" + t + \"px)\" : this.listTransform = \"translateY(\" + t + \"px)\");\n  }\n  changePage(t, i) {\n    const s = Math.min(Math.max(0, t), this.total - this.pageSize);\n    s !== this.skip && this.PageChange && this.PageChange({ skip: s, take: this.pageSize }, i);\n  }\n  reset() {\n    this.container && (this.calcScrollElementHeight(), this.container.scrollTop = 0, this.translateTo(0, !0));\n  }\n  scrollToEnd() {\n    if (this.container && this.list) {\n      this.calcScrollElementHeight();\n      const { scrollHeight: t, offsetHeight: i } = this.container;\n      this.container.scrollTop = t, this.translateTo(t - i, !0);\n    }\n  }\n  scrollHandler(t) {\n    if (!this.enabled || !this.container || !this.list)\n      return;\n    if (this.scrollSyncing) {\n      this.scrollSyncing = !1;\n      return;\n    }\n    this.rowHeightService = this.getRowHeightService(this.itemHeight, this.total);\n    const i = this.container.scrollTop, s = this.lastScrollTop >= i, h = !s, n = Math.abs(i - this.lastScrollTop);\n    this.lastScrollTop = i;\n    let e = this.rowHeightService.index(i), a = this.rowHeightService.offset(e);\n    const { offsetHeight: c } = this.container, g = this.rowHeightService.index(i + c);\n    if (h && g >= this.lastLoaded && this.lastLoaded < this.total) {\n      const l = e + this.pageSize - this.total;\n      l > 0 && (e = e - l, a = this.rowHeightService.offset(e)), this.firstLoaded = e, this.translateTo(a);\n      const o = this.firstLoaded + this.pageSize;\n      this.lastLoaded = Math.min(o, this.total), this.changePage(this.firstLoaded, t);\n    } else if (s && e - 1 <= this.firstLoaded) {\n      const l = n === this.itemHeight;\n      if (this.skip === 0 && l)\n        this.lastScrollTop = 0, this.firstLoaded = 0, this.lastLoaded = this.pageSize, this.container.scrollTop = 0, this.translateTo(0, !0);\n      else {\n        const o = Math.floor(this.pageSize * 0.3);\n        this.firstLoaded = Math.max(e - o, 0), this.translateTo(this.rowHeightService.offset(this.firstLoaded)), this.lastLoaded = Math.min(this.firstLoaded + this.pageSize, this.total), this.changePage(this.firstLoaded, t);\n      }\n    }\n  }\n}\nexport {\n  u as VirtualScroll\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAU,IAAIC,CAAC,EAAEC,gBAAgB,IAAIC,CAAC,QAAQ,8BAA8B;AACrF,OAAO,KAAKC,CAAC,MAAM,OAAO;AAC1B,MAAMC,CAAC,GAAG,QAAQ;EAAEC,CAAC,GAAG,OAAOC,MAAM,IAAI,WAAW,IAAI,SAAS,CAACC,IAAI,CAACD,MAAM,CAACE,SAAS,CAACC,SAAS,CAAC;AAClG,MAAMC,CAAC,CAAC;EACNC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,KAAK,GAAG,IAAI,EAAE,IAAI,CAACC,KAAK,GAAG,CAAC,EAAE,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE,IAAI,CAACC,QAAQ,GAAG,CAAC,EAAE,IAAI,CAACC,UAAU,GAAG,IAAI,EAAE,IAAI,CAACC,aAAa,GAAG,IAAI,EAAE,IAAI,CAACC,aAAa,GAAG,EAAE,EAAE,IAAI,CAACC,UAAU,GAAG,CAAC,EAAE,IAAI,CAACC,eAAe,GAAG,CAAC,EAAE,IAAI,CAACC,YAAY,GAAGC,MAAM,CAACC,UAAU,CAACrB,CAAC,CAACsB,OAAO,CAAC,EAAE,IAAI,CAACC,aAAa,GAAG,CAAC,CAAC,EAAE,IAAI,CAACC,UAAU,GAAG,CAAC,EAAE,IAAI,CAACC,WAAW,GAAG,CAAC,EAAE,IAAI,CAACC,aAAa,GAAG,CAAC,EAAE,IAAI,CAACC,aAAa,GAAG,CAAC,EAAE,IAAI,CAACC,IAAI,GAAG,IAAI,EAAE,IAAI,CAACC,SAAS,GAAG,IAAI,EAAE,IAAI,CAACC,uBAAuB,GAAG,MAAM;MACrc,IAAIC,CAAC;MACL,IAAI,CAACR,aAAa,GAAG,CAAC,CAAC;MACvB,IAAIS,CAAC,GAAG,CAAC,CAAC;MACV,IAAI,CAACf,UAAU,GAAG,IAAI,CAACW,IAAI,GAAG,IAAI,CAACA,IAAI,CAACK,QAAQ,CAAC,CAAC,CAAC,CAACC,YAAY,GAAG,IAAI,CAACjB,UAAU;MAClF,MAAMkB,CAAC,GAAG,IAAI,CAAClB,UAAU,GAAG,IAAI,CAACP,KAAK,IAAI,CAAC,CAACqB,CAAC,GAAG,IAAI,CAACH,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGG,CAAC,CAACG,YAAY,KAAK,CAAC,CAAC;MACnG,IAAI,CAAChB,eAAe,GAAGhB,CAAC,GAAGkC,IAAI,CAACC,GAAG,CAACpC,CAAC,EAAEkC,CAAC,CAAC,GAAGA,CAAC;MAC7C,MAAMG,CAAC,GAAG,IAAI,CAACpB,eAAe;MAC9B,OAAO,IAAI,CAACH,aAAa,KAAKiB,CAAC,GAAG,IAAI,CAACjB,aAAa,CAACwB,KAAK,CAACC,MAAM,KAAKF,CAAC,GAAG,IAAI,EAAEN,CAAC,KAAK,IAAI,CAACjB,aAAa,CAACwB,KAAK,CAACC,MAAM,GAAGF,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,IAAI,CAACf,aAAa,GAAG,CAAC,CAAC,EAAES,CAAC;IAChK,CAAC,EAAE,IAAI,CAACS,WAAW,GAAIT,CAAC,IAAK;MAC3B,IAAI,CAACH,SAAS,GAAGG,CAAC,EAAEA,CAAC,KAAKA,CAAC,CAACU,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC,EAAEvC,MAAM,CAACwC,UAAU,CAAC,IAAI,CAACb,uBAAuB,CAACc,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;IAChI,CAAC,EAAE,IAAI,CAACC,mBAAmB,GAAGhD,CAAC,CAAC,CAACmC,CAAC,EAAEG,CAAC,KAAK,IAAIpC,CAAC,CAACoC,CAAC,EAAEH,CAAC,CAAC,CAAC,EAAE,IAAI,CAACP,WAAW,GAAG,IAAI,CAACZ,QAAQ,EAAE,IAAI,CAACW,UAAU,GAAG,IAAI,CAACZ,IAAI,GAAG,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACiC,aAAa,GAAG,IAAI,CAACA,aAAa,CAACF,IAAI,CAAC,IAAI,CAAC;EAC3L;EACA,IAAIG,SAASA,CAAA,EAAG;IACd,OAAO,IAAI,CAACpB,aAAa;EAC3B;EACAqB,WAAWA,CAAChB,CAAC,EAAEG,CAAC,EAAE;IAChB,IAAI,CAACR,aAAa,GAAGK,CAAC,EAAE,IAAI,CAACrB,OAAO,IAAI,IAAI,CAACiB,IAAI,KAAK1B,CAAC,IAAI,IAAI,CAACiB,YAAY,IAAI,EAAE,IAAIgB,CAAC,GAAG,IAAI,CAACP,IAAI,CAACW,KAAK,CAACU,SAAS,GAAG,aAAa,GAAGjB,CAAC,GAAG,KAAK,GAAG,IAAI,CAAChB,aAAa,GAAG,aAAa,GAAGgB,CAAC,GAAG,KAAK,CAAC;EACnM;EACAkB,UAAUA,CAAClB,CAAC,EAAEG,CAAC,EAAE;IACf,MAAMG,CAAC,GAAGF,IAAI,CAACC,GAAG,CAACD,IAAI,CAACe,GAAG,CAAC,CAAC,EAAEnB,CAAC,CAAC,EAAE,IAAI,CAACtB,KAAK,GAAG,IAAI,CAACG,QAAQ,CAAC;IAC9DyB,CAAC,KAAK,IAAI,CAAC1B,IAAI,IAAI,IAAI,CAACE,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC;MAAEF,IAAI,EAAE0B,CAAC;MAAEc,IAAI,EAAE,IAAI,CAACvC;IAAS,CAAC,EAAEsB,CAAC,CAAC;EAC5F;EACAkB,KAAKA,CAAA,EAAG;IACN,IAAI,CAACxB,SAAS,KAAK,IAAI,CAACC,uBAAuB,CAAC,CAAC,EAAE,IAAI,CAACD,SAAS,CAACyB,SAAS,GAAG,CAAC,EAAE,IAAI,CAACN,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC3G;EACAO,WAAWA,CAAA,EAAG;IACZ,IAAI,IAAI,CAAC1B,SAAS,IAAI,IAAI,CAACD,IAAI,EAAE;MAC/B,IAAI,CAACE,uBAAuB,CAAC,CAAC;MAC9B,MAAM;QAAE0B,YAAY,EAAExB,CAAC;QAAEE,YAAY,EAAEC;MAAE,CAAC,GAAG,IAAI,CAACN,SAAS;MAC3D,IAAI,CAACA,SAAS,CAACyB,SAAS,GAAGtB,CAAC,EAAE,IAAI,CAACgB,WAAW,CAAChB,CAAC,GAAGG,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3D;EACF;EACAW,aAAaA,CAACd,CAAC,EAAE;IACf,IAAI,CAAC,IAAI,CAACrB,OAAO,IAAI,CAAC,IAAI,CAACkB,SAAS,IAAI,CAAC,IAAI,CAACD,IAAI,EAChD;IACF,IAAI,IAAI,CAACL,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,GAAG,CAAC,CAAC;MACvB;IACF;IACA,IAAI,CAACkC,gBAAgB,GAAG,IAAI,CAACZ,mBAAmB,CAAC,IAAI,CAAC5B,UAAU,EAAE,IAAI,CAACP,KAAK,CAAC;IAC7E,MAAMyB,CAAC,GAAG,IAAI,CAACN,SAAS,CAACyB,SAAS;MAAEhB,CAAC,GAAG,IAAI,CAACZ,aAAa,IAAIS,CAAC;MAAEJ,CAAC,GAAG,CAACO,CAAC;MAAEoB,CAAC,GAAGtB,IAAI,CAACuB,GAAG,CAACxB,CAAC,GAAG,IAAI,CAACT,aAAa,CAAC;IAC7G,IAAI,CAACA,aAAa,GAAGS,CAAC;IACtB,IAAIyB,CAAC,GAAG,IAAI,CAACH,gBAAgB,CAACI,KAAK,CAAC1B,CAAC,CAAC;MAAE2B,CAAC,GAAG,IAAI,CAACL,gBAAgB,CAACM,MAAM,CAACH,CAAC,CAAC;IAC3E,MAAM;QAAE1B,YAAY,EAAE8B;MAAE,CAAC,GAAG,IAAI,CAACnC,SAAS;MAAEoC,CAAC,GAAG,IAAI,CAACR,gBAAgB,CAACI,KAAK,CAAC1B,CAAC,GAAG6B,CAAC,CAAC;IAClF,IAAIjC,CAAC,IAAIkC,CAAC,IAAI,IAAI,CAACzC,UAAU,IAAI,IAAI,CAACA,UAAU,GAAG,IAAI,CAACd,KAAK,EAAE;MAC7D,MAAMwD,CAAC,GAAGN,CAAC,GAAG,IAAI,CAAC/C,QAAQ,GAAG,IAAI,CAACH,KAAK;MACxCwD,CAAC,GAAG,CAAC,KAAKN,CAAC,GAAGA,CAAC,GAAGM,CAAC,EAAEJ,CAAC,GAAG,IAAI,CAACL,gBAAgB,CAACM,MAAM,CAACH,CAAC,CAAC,CAAC,EAAE,IAAI,CAACnC,WAAW,GAAGmC,CAAC,EAAE,IAAI,CAACZ,WAAW,CAACc,CAAC,CAAC;MACpG,MAAMK,CAAC,GAAG,IAAI,CAAC1C,WAAW,GAAG,IAAI,CAACZ,QAAQ;MAC1C,IAAI,CAACW,UAAU,GAAGY,IAAI,CAACC,GAAG,CAAC8B,CAAC,EAAE,IAAI,CAACzD,KAAK,CAAC,EAAE,IAAI,CAACwC,UAAU,CAAC,IAAI,CAACzB,WAAW,EAAEO,CAAC,CAAC;IACjF,CAAC,MAAM,IAAIM,CAAC,IAAIsB,CAAC,GAAG,CAAC,IAAI,IAAI,CAACnC,WAAW,EAAE;MACzC,MAAMyC,CAAC,GAAGR,CAAC,KAAK,IAAI,CAACzC,UAAU;MAC/B,IAAI,IAAI,CAACL,IAAI,KAAK,CAAC,IAAIsD,CAAC,EACtB,IAAI,CAACxC,aAAa,GAAG,CAAC,EAAE,IAAI,CAACD,WAAW,GAAG,CAAC,EAAE,IAAI,CAACD,UAAU,GAAG,IAAI,CAACX,QAAQ,EAAE,IAAI,CAACgB,SAAS,CAACyB,SAAS,GAAG,CAAC,EAAE,IAAI,CAACN,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAClI;QACH,MAAMmB,CAAC,GAAG/B,IAAI,CAACgC,KAAK,CAAC,IAAI,CAACvD,QAAQ,GAAG,GAAG,CAAC;QACzC,IAAI,CAACY,WAAW,GAAGW,IAAI,CAACe,GAAG,CAACS,CAAC,GAAGO,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAACnB,WAAW,CAAC,IAAI,CAACS,gBAAgB,CAACM,MAAM,CAAC,IAAI,CAACtC,WAAW,CAAC,CAAC,EAAE,IAAI,CAACD,UAAU,GAAGY,IAAI,CAACC,GAAG,CAAC,IAAI,CAACZ,WAAW,GAAG,IAAI,CAACZ,QAAQ,EAAE,IAAI,CAACH,KAAK,CAAC,EAAE,IAAI,CAACwC,UAAU,CAAC,IAAI,CAACzB,WAAW,EAAEO,CAAC,CAAC;MACzN;IACF;EACF;AACF;AACA,SACEzB,CAAC,IAAI8D,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}