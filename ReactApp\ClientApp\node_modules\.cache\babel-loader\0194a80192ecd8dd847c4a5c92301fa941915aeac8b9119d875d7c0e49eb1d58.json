{"ast": null, "code": "import GradientStopNode from './gradient-stop-node';\nimport BaseNode from '../core/base-node';\nimport Node from './node';\nimport renderAllAttr from './utils/render-all-attributes';\nvar GradientNode = function (Node) {\n  function GradientNode(srcElement) {\n    Node.call(this, srcElement);\n    this.id = srcElement.id;\n    this.loadStops();\n  }\n  if (Node) GradientNode.__proto__ = Node;\n  GradientNode.prototype = Object.create(Node && Node.prototype);\n  GradientNode.prototype.constructor = GradientNode;\n  GradientNode.prototype.loadStops = function loadStops() {\n    var this$1 = this;\n    var stops = this.srcElement.stops;\n    var element = this.element;\n    for (var idx = 0; idx < stops.length; idx++) {\n      var stopNode = new GradientStopNode(stops[idx]);\n      this$1.append(stopNode);\n      if (element) {\n        stopNode.attachTo(element);\n      }\n    }\n  };\n  GradientNode.prototype.optionsChange = function optionsChange(e) {\n    if (e.field === \"gradient.stops\") {\n      BaseNode.prototype.clear.call(this);\n      this.loadStops();\n    } else if (e.field === \"gradient\") {\n      this.allAttr(this.mapCoordinates());\n    }\n  };\n  GradientNode.prototype.renderCoordinates = function renderCoordinates() {\n    return renderAllAttr(this.mapCoordinates());\n  };\n  GradientNode.prototype.mapSpace = function mapSpace() {\n    return [\"gradientUnits\", this.srcElement.userSpace() ? \"userSpaceOnUse\" : \"objectBoundingBox\"];\n  };\n  return GradientNode;\n}(Node);\nexport default GradientNode;", "map": {"version": 3, "names": ["GradientStopNode", "BaseNode", "Node", "renderAllAttr", "GradientNode", "srcElement", "call", "id", "loadStops", "__proto__", "prototype", "Object", "create", "constructor", "this$1", "stops", "element", "idx", "length", "stopNode", "append", "attachTo", "optionsChange", "e", "field", "clear", "allAttr", "mapCoordinates", "renderCoordinates", "mapSpace", "userSpace"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/svg/gradient-node.js"], "sourcesContent": ["import GradientStopNode from './gradient-stop-node';\nimport BaseNode from '../core/base-node';\nimport Node from './node';\nimport renderAllAttr from './utils/render-all-attributes';\n\nvar GradientNode = (function (Node) {\n    function GradientNode(srcElement) {\n        Node.call(this, srcElement);\n\n        this.id = srcElement.id;\n\n        this.loadStops();\n    }\n\n    if ( Node ) GradientNode.__proto__ = Node;\n    GradientNode.prototype = Object.create( Node && Node.prototype );\n    GradientNode.prototype.constructor = GradientNode;\n\n    GradientNode.prototype.loadStops = function loadStops () {\n        var this$1 = this;\n\n        var stops = this.srcElement.stops;\n        var element = this.element;\n\n        for (var idx = 0; idx < stops.length; idx++) {\n            var stopNode = new GradientStopNode(stops[idx]);\n            this$1.append(stopNode);\n            if (element) {\n                stopNode.attachTo(element);\n            }\n        }\n    };\n\n    GradientNode.prototype.optionsChange = function optionsChange (e) {\n        if (e.field === \"gradient.stops\") {\n            BaseNode.prototype.clear.call(this);\n            this.loadStops();\n        } else if (e.field === \"gradient\") {\n            this.allAttr(this.mapCoordinates());\n        }\n    };\n\n    GradientNode.prototype.renderCoordinates = function renderCoordinates () {\n        return renderAllAttr(this.mapCoordinates());\n    };\n\n    GradientNode.prototype.mapSpace = function mapSpace () {\n        return [ \"gradientUnits\", this.srcElement.userSpace() ? \"userSpaceOnUse\" : \"objectBoundingBox\" ];\n    };\n\n    return GradientNode;\n}(Node));\n\nexport default GradientNode;"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,sBAAsB;AACnD,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,aAAa,MAAM,+BAA+B;AAEzD,IAAIC,YAAY,GAAI,UAAUF,IAAI,EAAE;EAChC,SAASE,YAAYA,CAACC,UAAU,EAAE;IAC9BH,IAAI,CAACI,IAAI,CAAC,IAAI,EAAED,UAAU,CAAC;IAE3B,IAAI,CAACE,EAAE,GAAGF,UAAU,CAACE,EAAE;IAEvB,IAAI,CAACC,SAAS,CAAC,CAAC;EACpB;EAEA,IAAKN,IAAI,EAAGE,YAAY,CAACK,SAAS,GAAGP,IAAI;EACzCE,YAAY,CAACM,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAEV,IAAI,IAAIA,IAAI,CAACQ,SAAU,CAAC;EAChEN,YAAY,CAACM,SAAS,CAACG,WAAW,GAAGT,YAAY;EAEjDA,YAAY,CAACM,SAAS,CAACF,SAAS,GAAG,SAASA,SAASA,CAAA,EAAI;IACrD,IAAIM,MAAM,GAAG,IAAI;IAEjB,IAAIC,KAAK,GAAG,IAAI,CAACV,UAAU,CAACU,KAAK;IACjC,IAAIC,OAAO,GAAG,IAAI,CAACA,OAAO;IAE1B,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGF,KAAK,CAACG,MAAM,EAAED,GAAG,EAAE,EAAE;MACzC,IAAIE,QAAQ,GAAG,IAAInB,gBAAgB,CAACe,KAAK,CAACE,GAAG,CAAC,CAAC;MAC/CH,MAAM,CAACM,MAAM,CAACD,QAAQ,CAAC;MACvB,IAAIH,OAAO,EAAE;QACTG,QAAQ,CAACE,QAAQ,CAACL,OAAO,CAAC;MAC9B;IACJ;EACJ,CAAC;EAEDZ,YAAY,CAACM,SAAS,CAACY,aAAa,GAAG,SAASA,aAAaA,CAAEC,CAAC,EAAE;IAC9D,IAAIA,CAAC,CAACC,KAAK,KAAK,gBAAgB,EAAE;MAC9BvB,QAAQ,CAACS,SAAS,CAACe,KAAK,CAACnB,IAAI,CAAC,IAAI,CAAC;MACnC,IAAI,CAACE,SAAS,CAAC,CAAC;IACpB,CAAC,MAAM,IAAIe,CAAC,CAACC,KAAK,KAAK,UAAU,EAAE;MAC/B,IAAI,CAACE,OAAO,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC;IACvC;EACJ,CAAC;EAEDvB,YAAY,CAACM,SAAS,CAACkB,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAI;IACrE,OAAOzB,aAAa,CAAC,IAAI,CAACwB,cAAc,CAAC,CAAC,CAAC;EAC/C,CAAC;EAEDvB,YAAY,CAACM,SAAS,CAACmB,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;IACnD,OAAO,CAAE,eAAe,EAAE,IAAI,CAACxB,UAAU,CAACyB,SAAS,CAAC,CAAC,GAAG,gBAAgB,GAAG,mBAAmB,CAAE;EACpG,CAAC;EAED,OAAO1B,YAAY;AACvB,CAAC,CAACF,IAAI,CAAE;AAER,eAAeE,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}