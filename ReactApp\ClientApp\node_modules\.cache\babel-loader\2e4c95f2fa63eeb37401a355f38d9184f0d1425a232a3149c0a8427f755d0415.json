{"ast": null, "code": "export var Key = {\n  DELETE: \"Delete\",\n  BACKSPACE: \"Backspace\",\n  TAB: \"Tab\",\n  ENTER: \"Enter\",\n  ESCAPE: \"Escape\",\n  ARROW_LEFT: \"ArrowLeft\",\n  ARROW_UP: \"ArrowUp\",\n  ARROW_RIGHT: \"<PERSON>R<PERSON>\",\n  ARROW_DOWN: \"ArrowDown\",\n  SPACE: \" \",\n  END: \"End\",\n  HOME: \"Home\",\n  PAGE_UP: \"PageUp\",\n  PAGE_DOWN: \"PageDown\"\n};", "map": {"version": 3, "names": ["Key", "DELETE", "BACKSPACE", "TAB", "ENTER", "ESCAPE", "ARROW_LEFT", "ARROW_UP", "ARROW_RIGHT", "ARROW_DOWN", "SPACE", "END", "HOME", "PAGE_UP", "PAGE_DOWN"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-dateinputs-common/dist/es/common/key.js"], "sourcesContent": ["export var Key = {\n    DELETE: \"Delete\",\n    BACKSPACE: \"Backspace\",\n    TAB: \"Tab\",\n    ENTER: \"Enter\",\n    ESCAPE: \"Escape\",\n    ARROW_LEFT: \"ArrowLeft\",\n    ARROW_UP: \"ArrowUp\",\n    ARROW_RIGHT: \"<PERSON>R<PERSON>\",\n    ARROW_DOWN: \"ArrowDown\",\n    SPACE: \" \",\n    END: \"End\",\n    HOME: \"Home\",\n    PAGE_UP: \"PageUp\",\n    PAGE_DOWN: \"PageDown\"\n};\n"], "mappings": "AAAA,OAAO,IAAIA,GAAG,GAAG;EACbC,MAAM,EAAE,QAAQ;EAChBC,SAAS,EAAE,WAAW;EACtBC,GAAG,EAAE,KAAK;EACVC,KAAK,EAAE,OAAO;EACdC,MAAM,EAAE,QAAQ;EAChBC,UAAU,EAAE,WAAW;EACvBC,QAAQ,EAAE,SAAS;EACnBC,WAAW,EAAE,YAAY;EACzBC,UAAU,EAAE,WAAW;EACvBC,KAAK,EAAE,GAAG;EACVC,GAAG,EAAE,KAAK;EACVC,IAAI,EAAE,MAAM;EACZC,OAAO,EAAE,QAAQ;EACjBC,SAAS,EAAE;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}