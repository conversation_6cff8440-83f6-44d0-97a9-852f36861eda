{"ast": null, "code": "import { PRECISION } from '../constants';\nimport { round } from '../../util';\nexport default function close(a, b, tolerance) {\n  if (tolerance === void 0) tolerance = PRECISION;\n  return round(Math.abs(a - b), tolerance) === 0;\n}", "map": {"version": 3, "names": ["PRECISION", "round", "close", "a", "b", "tolerance", "Math", "abs"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/geometry/math/close.js"], "sourcesContent": ["import { PRECISION } from '../constants';\n\nimport { round } from '../../util';\n\nexport default function close(a, b, tolerance) {\n    if ( tolerance === void 0 ) tolerance = PRECISION;\n\n    return round(Math.abs(a - b), tolerance) === 0;\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,cAAc;AAExC,SAASC,KAAK,QAAQ,YAAY;AAElC,eAAe,SAASC,KAAKA,CAACC,CAAC,EAAEC,CAAC,EAAEC,SAAS,EAAE;EAC3C,IAAKA,SAAS,KAAK,KAAK,CAAC,EAAGA,SAAS,GAAGL,SAAS;EAEjD,OAAOC,KAAK,CAACK,IAAI,CAACC,GAAG,CAACJ,CAAC,GAAGC,CAAC,CAAC,EAAEC,SAAS,CAAC,KAAK,CAAC;AAClD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}