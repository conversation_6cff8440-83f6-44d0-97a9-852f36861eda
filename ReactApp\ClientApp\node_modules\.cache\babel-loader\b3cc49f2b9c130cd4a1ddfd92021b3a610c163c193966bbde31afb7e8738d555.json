{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as h from \"react\";\nimport * as B from \"react-dom\";\nimport n from \"prop-types\";\nimport { WindowTitleBar as R } from \"./WindowTitlebar.mjs\";\nimport { keepFocusInContainer as x, Keys as a, dispatchEvent as l, canUseDOM as u, ZIndexContext as M, classNames as N, Draggable as U, createPropsContext as H, withIdHOC as z, withPropsContext as k } from \"@progress/kendo-react-common\";\nimport { ResizeHandlers as _ } from \"./WindowResizeHandlers.mjs\";\nimport { MiddleLayerOptimization as Z } from \"./MiddleLayerOptimization.mjs\";\nimport { windowStage as i } from \"./StageEnum.mjs\";\nimport { WindowActionsBar as v } from \"./WindowActionsBar.mjs\";\nimport { DEFAULT_DIALOGS_ZINDEX as F, ZINDEX_DIALOGS_STEP as y, DATA_DIALOGS_ID as O } from \"./constants.mjs\";\nimport { getMaxZIndex as A } from \"./utils.mjs\";\nconst w = 300,\n  m = 300,\n  S = 120,\n  E = 100,\n  d = 5,\n  f = class f extends h.Component {\n    constructor(s) {\n      super(s), this.context = 0, this.draggable = null, this.offSetCoordinates = {\n        x: 0,\n        y: 0\n      }, this.titleId = this.generateTitleId(), this.mounted = !1, this.activeElement = null, this.onKeyDown = t => {\n        if (this.props.modal && x(t, this.element), t.target !== t.currentTarget) return;\n        const e = this.props.minWidth || S,\n          o = this.props.minHeight || E;\n        if ((t.metaKey || t.ctrlKey) && this.props.resizable) {\n          switch (t.keyCode) {\n            case a.up:\n              t.preventDefault(), o <= this.height - d && this.setState({\n                height: this.height - d\n              });\n              break;\n            case a.down:\n              t.preventDefault(), this.setState({\n                height: this.height + d\n              });\n              break;\n            case a.left:\n              e <= this.width - d && this.setState({\n                width: this.width - d\n              });\n              break;\n            case a.right:\n              this.setState({\n                width: this.width + d\n              });\n              break;\n            default:\n              return;\n          }\n          this.dispatchMoveEvent(this.props.onResize, t, !1, void 0);\n          return;\n        }\n        if (t.altKey) {\n          switch (t.keyCode) {\n            case a.up:\n              this.windowStage === i.MINIMIZED ? (this.handleRestore(t), l(this.props.onStageChange, t, this, {\n                state: i.DEFAULT\n              })) : this.windowStage === i.DEFAULT && (this.handleFullscreen(t), l(this.props.onStageChange, t, this, {\n                state: i.FULLSCREEN\n              }));\n              break;\n            case a.down:\n              this.windowStage === i.FULLSCREEN ? (this.handleRestore(t), l(this.props.onStageChange, t, this, {\n                state: i.DEFAULT\n              })) : this.windowStage === i.DEFAULT && (this.handleMinimize(t), l(this.props.onStageChange, t, this, {\n                state: i.MINIMIZED\n              }));\n              break;\n          }\n          return;\n        }\n        if (!t.ctrlKey) switch (t.keyCode) {\n          case a.esc:\n            this.props.onClose && this.handleCloseWindow(t);\n            return;\n          case a.up:\n            this.setState(r => ({\n              top: r.top - d\n            }));\n            break;\n          case a.down:\n            this.setState(r => ({\n              top: r.top + d\n            }));\n            break;\n          case a.left:\n            this.setState(r => ({\n              left: r.left - d\n            }));\n            break;\n          case a.right:\n            this.setState(r => ({\n              left: r.left + d\n            }));\n            break;\n          default:\n            return;\n        }\n        this.dispatchMoveEvent(this.props.onMove, t, !1, void 0);\n      }, this.onPress = t => {\n        const e = t.event;\n        this.windowCoordinatesState.differenceLeft = e.pageX - this.left, this.windowCoordinatesState.differenceTop = e.pageY - this.top;\n      }, this.onDrag = t => {\n        const e = t.event;\n        e.originalEvent.preventDefault(), this.windowStage !== i.FULLSCREEN && this.props.draggable && (this.setState({\n          top: Math.max(e.pageY - this.windowCoordinatesState.differenceTop, 0),\n          left: e.pageX - this.windowCoordinatesState.differenceLeft,\n          isDragging: !0\n        }), this.props.onMove && this.dispatchMoveEvent(this.props.onMove, e, !0, !1));\n      }, this.onRelease = t => {\n        const e = t.event;\n        this.windowStage !== i.FULLSCREEN && this.props.draggable && this.props.onMove && this.dispatchMoveEvent(this.props.onMove, e, !0, !0), this.setState({\n          isDragging: !1\n        });\n      }, this.onFocus = () => {\n        this._blurTimeout ? (clearTimeout(this._blurTimeout), this._blurTimeout = void 0) : this.setState({\n          focused: !0,\n          zIndex: A(this.getCurrentZIndex(), this.getDocument(), this._id)\n        });\n      }, this.onBlur = () => {\n        clearTimeout(this._blurTimeout);\n        const t = this.getWindow();\n        t && (this._blurTimeout = t.setTimeout(() => {\n          this.mounted && this.setState({\n            focused: !1\n          }), this._blurTimeout = void 0;\n        }));\n      }, this.getInitialTop = () => {\n        if (this.props.top !== void 0) return this.props.top;\n        if (this.props.initialTop !== void 0) return this.props.initialTop;\n        let t = m;\n        if (this.props.height !== void 0 ? t = this.props.height : this.props.initialHeight !== void 0 && (t = this.props.initialHeight), this.props.appendTo) return this.props.appendTo.offsetHeight / 2 - t / 2;\n        const e = this.getWindow();\n        return e ? e.innerHeight / 2 - t / 2 : 0;\n      }, this.getInitialLeft = () => {\n        if (this.props.left !== void 0) return this.props.left;\n        if (this.props.initialLeft !== void 0) return this.props.initialLeft;\n        let t = w;\n        if (this.props.width !== void 0 ? t = this.props.width : this.props.initialWidth !== void 0 && (t = this.props.initialWidth), this.props.appendTo) return this.props.appendTo.offsetWidth / 2 - t / 2;\n        const e = this.getWindow();\n        return e ? e.innerWidth / 2 - t / 2 : 0;\n      }, this.getInitialWidth = () => {\n        let t = w;\n        return this.props.width !== void 0 ? t = this.props.width : this.props.initialWidth !== void 0 && (t = this.props.initialWidth), t;\n      }, this.getInitialHeight = () => {\n        let t = m;\n        return this.props.height !== void 0 ? t = this.props.height : this.props.initialHeight !== void 0 && (t = this.props.initialHeight), t;\n      }, this.handleMinimize = t => {\n        t.preventDefault(), this.windowCoordinatesState.leftBeforeAction = this.left, this.windowCoordinatesState.topBeforeAction = this.top, this.windowCoordinatesState.widthBeforeAction = this.width, this.windowCoordinatesState.heightBeforeAction = this.height, this.setState({\n          stage: i.MINIMIZED,\n          height: 0\n        }), l(this.props.onStageChange, t, this, {\n          state: i.MINIMIZED\n        });\n      }, this.handleFullscreen = t => {\n        t.preventDefault(), this.windowCoordinatesState.leftBeforeAction = this.left, this.windowCoordinatesState.topBeforeAction = this.top, this.windowCoordinatesState.widthBeforeAction = this.width, this.windowCoordinatesState.heightBeforeAction = this.height;\n        const e = this.getWindow(),\n          o = e ? e.innerWidth : 0,\n          p = e ? e.innerHeight : 0;\n        this.setState({\n          left: 0,\n          top: 0,\n          width: this.props.appendTo ? this.props.appendTo.offsetWidth : o,\n          height: this.props.appendTo ? this.props.appendTo.offsetHeight : p,\n          stage: i.FULLSCREEN\n        }), l(this.props.onStageChange, t, this, {\n          state: i.FULLSCREEN\n        });\n      }, this.handleRestore = t => {\n        t.preventDefault(), this.windowStage === i.FULLSCREEN ? this.setState({\n          stage: i.DEFAULT,\n          left: this.windowCoordinatesState.leftBeforeAction,\n          top: this.windowCoordinatesState.topBeforeAction,\n          width: this.windowCoordinatesState.widthBeforeAction,\n          height: this.windowCoordinatesState.heightBeforeAction\n        }) : this.windowStage === i.MINIMIZED && this.setState({\n          stage: i.DEFAULT,\n          height: this.windowCoordinatesState.heightBeforeAction\n        }), l(this.props.onStageChange, t, this, {\n          state: i.DEFAULT\n        });\n      }, this.handleCloseWindow = t => {\n        t.preventDefault(), l(this.props.onClose, t, this, {\n          state: void 0\n        });\n      }, this.handleDoubleClick = t => {\n        this.windowStage === i.FULLSCREEN || this.windowStage === i.MINIMIZED ? this.handleRestore(t) : this.handleFullscreen(t);\n      }, this.handleResize = (t, e) => {\n        const o = this.props.appendTo ? t.pageX - this.offSetCoordinates.x : t.pageX,\n          p = this.props.appendTo ? t.pageY - this.offSetCoordinates.y : t.pageY,\n          r = this.width,\n          c = this.height,\n          D = this.props.minWidth || S,\n          I = this.props.minHeight || E,\n          T = this.top - p,\n          L = this.left - o,\n          b = o - this.left,\n          W = p - this.top,\n          g = Object.assign({}, this.state, {\n            isDragging: !e.end\n          });\n        e.direction.indexOf(\"n\") >= 0 && I - (c + T) < 0 && (this.top > 0 && (g.height = c + T), g.top = p), e.direction.indexOf(\"s\") >= 0 && I - W < 0 && (g.height = W), e.direction.indexOf(\"w\") >= 0 && D - (r + L) < 0 && (this.left > 0 && (g.width = r + L), g.left = o), e.direction.indexOf(\"e\") >= 0 && D - b < 0 && (g.width = b), this.setState(g), this.dispatchMoveEvent(this.props.onResize, t, !0, e.end);\n      }, this.dispatchMoveEvent = (t, e, o, p) => {\n        t && t.call(void 0, {\n          nativeEvent: e.nativeEvent ? e.nativeEvent : e.originalEvent,\n          drag: o,\n          end: p,\n          target: this,\n          left: this.state.left,\n          top: this.state.top,\n          width: this.state.width,\n          hight: this.state.height,\n          height: this.state.height\n        });\n      }, this.handleBrowserWindowResize = () => {\n        if (this.windowStage === i.FULLSCREEN) {\n          const t = this.getWindow(),\n            e = t ? t.innerWidth : 0,\n            o = t ? t.innerHeight : 0;\n          this.setState({\n            width: this.props.appendTo ? this.props.appendTo.offsetWidth : e,\n            height: this.props.appendTo ? this.props.appendTo.offsetHeight : o\n          });\n        }\n      }, this.getCurrentZIndex = () => !this.state || this.context === void 0 ? this.context ? this.context : F : this.state.zIndex > (this.context ? this.context + y : 0) ? this.state.zIndex : this.context + y, this.getDocument = () => {\n        const t = u ? document : null;\n        return this.props.appendTo ? this.props.appendTo.ownerDocument : t;\n      }, this.getWindow = () => {\n        const t = this.getDocument();\n        return t && t.defaultView;\n      }, this.state = {\n        stage: this.props.stage || i.DEFAULT,\n        isDragging: !1,\n        top: 0,\n        left: 0,\n        width: w,\n        height: m,\n        focused: !0,\n        zIndex: F\n      }, u && (this.activeElement = document.activeElement);\n    }\n    get _id() {\n      return this.props.id + \"-accessibility-id\";\n    }\n    /**\n     * @hidden\n     */\n    componentDidMount() {\n      this.element && this.props.autoFocus && this.element.focus({\n        preventScroll: !0\n      });\n      const s = this.getWindow();\n      s && s.addEventListener(\"resize\", this.handleBrowserWindowResize), this.setState({\n        stage: this.props.stage || i.DEFAULT,\n        isDragging: !1,\n        top: this.getInitialTop(),\n        left: this.getInitialLeft(),\n        width: this.getInitialWidth(),\n        height: this.getInitialHeight(),\n        focused: !0,\n        zIndex: A(this.getCurrentZIndex(), this.getDocument(), this._id)\n      }), this.windowCoordinatesState = {\n        leftBeforeAction: this.getInitialLeft(),\n        topBeforeAction: this.getInitialTop(),\n        widthBeforeAction: this.getInitialWidth(),\n        heightBeforeAction: this.getInitialHeight()\n      };\n      const t = this.getDocument();\n      if (this.props.appendTo && t) {\n        const e = this.props.appendTo.getBoundingClientRect(),\n          o = t.body.getBoundingClientRect();\n        this.offSetCoordinates.x = e.left - o.left, this.offSetCoordinates.y = e.top - o.top;\n      }\n      this.mounted = !0;\n    }\n    /**\n     * @hidden\n     */\n    componentWillUnmount() {\n      const s = this.getWindow();\n      s && s.removeEventListener(\"resize\", this.handleBrowserWindowResize), this.mounted = !1, setTimeout(() => {\n        var t;\n        !this.element && this.activeElement && u && (document.contains(this.activeElement) ? this.activeElement.focus({\n          preventScroll: !0\n        }) : this.activeElement.id && ((t = document.getElementById(this.activeElement.id)) == null || t.focus({\n          preventScroll: !0\n        })));\n      });\n    }\n    /**\n     * @hidden\n     */\n    componentDidUpdate(s) {\n      this.props.left && s.left !== this.props.left && this.setState({\n        left: this.props.left\n      }), this.props.top && s.top !== this.props.top && this.setState({\n        top: this.props.top\n      });\n      const t = this.getDocument();\n      if (this.props.appendTo && t) {\n        const e = this.props.appendTo.getBoundingClientRect(),\n          o = t.body.getBoundingClientRect();\n        this.offSetCoordinates.x = e.left - o.left, this.offSetCoordinates.y = e.top - o.top;\n      }\n      this.mounted = !0;\n    }\n    /**\n     * @hidden\n     */\n    render() {\n      const s = h.Children.toArray(this.props.children),\n        t = this.getContent(s),\n        e = this.getActionBar(s),\n        o = this.getCurrentZIndex(),\n        p = N(\"k-window\", this.props.className, {\n          [`k-window-${this.props.themeColor}`]: this.props.themeColor,\n          \"k-window-minimized\": this.state.stage === \"MINIMIZED\",\n          \"k-focus\": this.state.focused\n        }),\n        r = /* @__PURE__ */h.createElement(M.Provider, {\n          value: o\n        }, /* @__PURE__ */h.createElement(h.Fragment, null, this.props.modal && /* @__PURE__ */h.createElement(\"div\", {\n          className: \"k-overlay\",\n          style: {\n            zIndex: o,\n            ...this.props.overlayStyle\n          }\n        }), /* @__PURE__ */h.createElement(\"div\", {\n          id: this.props.id,\n          [O]: this._id,\n          tabIndex: 0,\n          role: \"dialog\",\n          \"aria-labelledby\": this.titleId,\n          onFocus: this.onFocus,\n          onBlur: this.onBlur,\n          onKeyDown: this.onKeyDown,\n          ref: c => {\n            this.windowElement = c, this.element = c;\n          },\n          className: p,\n          style: {\n            top: this.top,\n            left: this.left,\n            width: this.width,\n            height: this.height || \"\",\n            zIndex: o,\n            ...this.props.style\n          }\n        }, /* @__PURE__ */h.createElement(Z, {\n          shouldUpdateOnDrag: this.props.shouldUpdateOnDrag || !1,\n          isDragging: this.state.isDragging\n        }, /* @__PURE__ */h.createElement(U, {\n          onPress: this.onPress,\n          onDrag: this.onDrag,\n          onRelease: this.onRelease,\n          autoScroll: !1,\n          ref: c => {\n            this.draggable = c;\n          }\n        }, /* @__PURE__ */h.createElement(R, {\n          stage: this.windowStage,\n          onDoubleClick: this.props.doubleClickStageChange ? this.handleDoubleClick : void 0,\n          onMinimizeButtonClick: this.handleMinimize,\n          onFullScreenButtonClick: this.handleFullscreen,\n          onRestoreButtonClick: this.handleRestore,\n          onCloseButtonClick: this.handleCloseWindow,\n          closeButton: this.props.closeButton,\n          minimizeButton: this.props.minimizeButton,\n          maximizeButton: this.props.maximizeButton,\n          restoreButton: this.props.restoreButton,\n          id: this.titleId\n        }, this.props.title)), this.windowStage !== i.MINIMIZED ? /* @__PURE__ */h.createElement(h.Fragment, null, /* @__PURE__ */h.createElement(\"div\", {\n          className: \"k-window-content\"\n        }, t), e) : null, this.windowStage === i.DEFAULT && this.props.resizable ? /* @__PURE__ */h.createElement(_, {\n          onResize: this.handleResize\n        }) : null))));\n      return u ? this.props.appendTo !== null ? B.createPortal(r, this.props.appendTo || document.body) : r : null;\n    }\n    // Getters\n    get top() {\n      return this.windowStage !== i.FULLSCREEN ? Math.max(this.props.top || this.state.top, 0) : 0;\n    }\n    get left() {\n      return this.windowStage !== i.FULLSCREEN ? Math.max(this.props.left || this.state.left, 0) : 0;\n    }\n    get width() {\n      let s = this.props.width || this.state.width;\n      if (this.windowStage === i.FULLSCREEN) {\n        if (this.props.appendTo) return s = this.props.appendTo.offsetWidth, s;\n        const t = this.getWindow();\n        s = t ? t.innerWidth : 0;\n      }\n      return s;\n    }\n    get height() {\n      let s = this.props.height || this.state.height;\n      if (this.windowStage === i.FULLSCREEN) {\n        if (this.props.appendTo) return s = this.props.appendTo.offsetHeight, s;\n        const t = this.getWindow();\n        s = t ? t.innerHeight : 0;\n      } else this.windowStage === i.MINIMIZED && (s = 0);\n      return s;\n    }\n    get windowStage() {\n      return this.props.stage || this.state.stage;\n    }\n    getActionBar(s) {\n      return s.filter(t => t && t.type === v);\n    }\n    getContent(s) {\n      return s.filter(t => t && t.type !== v);\n    }\n    generateTitleId() {\n      return \"window-title-\" + this._id;\n    }\n  };\nf.displayName = \"Window\", f.propTypes = {\n  width: n.number,\n  height: n.number,\n  left: n.number,\n  top: n.number,\n  initialWidth: n.number,\n  initialHeight: n.number,\n  initialLeft: n.number,\n  initialTop: n.number,\n  minWidth: n.number,\n  minHeight: n.number,\n  resizable: n.bool,\n  draggable: n.bool,\n  title: n.any,\n  shouldUpdateOnDrag: n.bool,\n  stage: n.oneOf([\"DEFAULT\", \"MINIMIZED\", \"FULLSCREEN\"]),\n  className: n.string,\n  id: n.string,\n  style: n.object,\n  overlayStyle: n.object,\n  autoFocus: n.bool\n}, f.defaultProps = {\n  minWidth: S,\n  minHeight: E,\n  resizable: !0,\n  draggable: !0,\n  modal: !1,\n  doubleClickStageChange: !0,\n  autoFocus: !0\n}, f.contextType = M;\nlet C = f;\nconst P = H(),\n  K = z(k(P, C));\nK.displayName = \"KendoReactWindow\";\nexport { K as Window, P as WindowPropsContext, C as WindowWithoutContext };", "map": {"version": 3, "names": ["h", "B", "n", "WindowTitleBar", "R", "keepFocusInContainer", "x", "Keys", "a", "dispatchEvent", "l", "canUseDOM", "u", "ZIndexContext", "M", "classNames", "N", "Draggable", "U", "createPropsContext", "H", "withIdHOC", "z", "withPropsContext", "k", "ResizeHandlers", "_", "MiddleLayerOptimization", "Z", "windowStage", "i", "WindowActionsBar", "v", "DEFAULT_DIALOGS_ZINDEX", "F", "ZINDEX_DIALOGS_STEP", "y", "DATA_DIALOGS_ID", "O", "getMaxZIndex", "A", "w", "m", "S", "E", "d", "f", "Component", "constructor", "s", "context", "draggable", "offSetCoordinates", "titleId", "generateTitleId", "mounted", "activeElement", "onKeyDown", "t", "props", "modal", "element", "target", "currentTarget", "e", "min<PERSON><PERSON><PERSON>", "o", "minHeight", "metaKey", "ctrl<PERSON>ey", "resizable", "keyCode", "up", "preventDefault", "height", "setState", "down", "left", "width", "right", "dispatchMoveEvent", "onResize", "altKey", "MINIMIZED", "handleRestore", "onStageChange", "state", "DEFAULT", "handleFullscreen", "FULLSCREEN", "handleMinimize", "esc", "onClose", "handleCloseWindow", "r", "top", "onMove", "onPress", "event", "windowCoordinatesState", "differenceLeft", "pageX", "differenceTop", "pageY", "onDrag", "originalEvent", "Math", "max", "isDragging", "onRelease", "onFocus", "_blurTimeout", "clearTimeout", "focused", "zIndex", "getCurrentZIndex", "getDocument", "_id", "onBlur", "getWindow", "setTimeout", "getInitialTop", "initialTop", "initialHeight", "appendTo", "offsetHeight", "innerHeight", "getInitialLeft", "initialLeft", "initialWidth", "offsetWidth", "innerWidth", "getInitialWidth", "getInitialHeight", "leftBeforeAction", "topBeforeAction", "widthBeforeAction", "heightBeforeAction", "stage", "p", "handleDoubleClick", "handleResize", "c", "D", "I", "T", "L", "b", "W", "g", "Object", "assign", "end", "direction", "indexOf", "call", "nativeEvent", "drag", "hight", "handleBrowserWindowResize", "document", "ownerDocument", "defaultView", "id", "componentDidMount", "autoFocus", "focus", "preventScroll", "addEventListener", "getBoundingClientRect", "body", "componentWillUnmount", "removeEventListener", "contains", "getElementById", "componentDidUpdate", "render", "Children", "toArray", "children", "get<PERSON>ontent", "getActionBar", "className", "themeColor", "createElement", "Provider", "value", "Fragment", "style", "overlayStyle", "tabIndex", "role", "ref", "windowElement", "shouldUpdateOnDrag", "autoScroll", "onDoubleClick", "doubleClickStageChange", "onMinimizeButtonClick", "onFullScreenButtonClick", "onRestoreButtonClick", "onCloseButtonClick", "closeButton", "minimizeButton", "maximizeButton", "restoreButton", "title", "createPortal", "filter", "type", "displayName", "propTypes", "number", "bool", "any", "oneOf", "string", "object", "defaultProps", "contextType", "C", "P", "K", "Window", "WindowPropsContext", "WindowWithoutContext"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dialogs/Window.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as h from \"react\";\nimport * as B from \"react-dom\";\nimport n from \"prop-types\";\nimport { WindowTitleBar as R } from \"./WindowTitlebar.mjs\";\nimport { keepFocusInContainer as x, Keys as a, dispatchEvent as l, canUseDOM as u, ZIndexContext as M, classNames as N, Draggable as U, createPropsContext as H, withIdHOC as z, withPropsContext as k } from \"@progress/kendo-react-common\";\nimport { ResizeHandlers as _ } from \"./WindowResizeHandlers.mjs\";\nimport { MiddleLayerOptimization as Z } from \"./MiddleLayerOptimization.mjs\";\nimport { windowStage as i } from \"./StageEnum.mjs\";\nimport { WindowActionsBar as v } from \"./WindowActionsBar.mjs\";\nimport { DEFAULT_DIALOGS_ZINDEX as F, ZINDEX_DIALOGS_STEP as y, DATA_DIALOGS_ID as O } from \"./constants.mjs\";\nimport { getMaxZIndex as A } from \"./utils.mjs\";\nconst w = 300, m = 300, S = 120, E = 100, d = 5, f = class f extends h.Component {\n  constructor(s) {\n    super(s), this.context = 0, this.draggable = null, this.offSetCoordinates = { x: 0, y: 0 }, this.titleId = this.generateTitleId(), this.mounted = !1, this.activeElement = null, this.onKeyDown = (t) => {\n      if (this.props.modal && x(t, this.element), t.target !== t.currentTarget)\n        return;\n      const e = this.props.minWidth || S, o = this.props.minHeight || E;\n      if ((t.metaKey || t.ctrlKey) && this.props.resizable) {\n        switch (t.keyCode) {\n          case a.up:\n            t.preventDefault(), o <= this.height - d && this.setState({ height: this.height - d });\n            break;\n          case a.down:\n            t.preventDefault(), this.setState({ height: this.height + d });\n            break;\n          case a.left:\n            e <= this.width - d && this.setState({ width: this.width - d });\n            break;\n          case a.right:\n            this.setState({ width: this.width + d });\n            break;\n          default:\n            return;\n        }\n        this.dispatchMoveEvent(this.props.onResize, t, !1, void 0);\n        return;\n      }\n      if (t.altKey) {\n        switch (t.keyCode) {\n          case a.up:\n            this.windowStage === i.MINIMIZED ? (this.handleRestore(t), l(this.props.onStageChange, t, this, { state: i.DEFAULT })) : this.windowStage === i.DEFAULT && (this.handleFullscreen(t), l(this.props.onStageChange, t, this, { state: i.FULLSCREEN }));\n            break;\n          case a.down:\n            this.windowStage === i.FULLSCREEN ? (this.handleRestore(t), l(this.props.onStageChange, t, this, { state: i.DEFAULT })) : this.windowStage === i.DEFAULT && (this.handleMinimize(t), l(this.props.onStageChange, t, this, { state: i.MINIMIZED }));\n            break;\n        }\n        return;\n      }\n      if (!t.ctrlKey)\n        switch (t.keyCode) {\n          case a.esc:\n            this.props.onClose && this.handleCloseWindow(t);\n            return;\n          case a.up:\n            this.setState((r) => ({ top: r.top - d }));\n            break;\n          case a.down:\n            this.setState((r) => ({ top: r.top + d }));\n            break;\n          case a.left:\n            this.setState((r) => ({ left: r.left - d }));\n            break;\n          case a.right:\n            this.setState((r) => ({ left: r.left + d }));\n            break;\n          default:\n            return;\n        }\n      this.dispatchMoveEvent(this.props.onMove, t, !1, void 0);\n    }, this.onPress = (t) => {\n      const e = t.event;\n      this.windowCoordinatesState.differenceLeft = e.pageX - this.left, this.windowCoordinatesState.differenceTop = e.pageY - this.top;\n    }, this.onDrag = (t) => {\n      const e = t.event;\n      e.originalEvent.preventDefault(), this.windowStage !== i.FULLSCREEN && this.props.draggable && (this.setState({\n        top: Math.max(e.pageY - this.windowCoordinatesState.differenceTop, 0),\n        left: e.pageX - this.windowCoordinatesState.differenceLeft,\n        isDragging: !0\n      }), this.props.onMove && this.dispatchMoveEvent(this.props.onMove, e, !0, !1));\n    }, this.onRelease = (t) => {\n      const e = t.event;\n      this.windowStage !== i.FULLSCREEN && this.props.draggable && this.props.onMove && this.dispatchMoveEvent(this.props.onMove, e, !0, !0), this.setState({\n        isDragging: !1\n      });\n    }, this.onFocus = () => {\n      this._blurTimeout ? (clearTimeout(this._blurTimeout), this._blurTimeout = void 0) : this.setState({\n        focused: !0,\n        zIndex: A(this.getCurrentZIndex(), this.getDocument(), this._id)\n      });\n    }, this.onBlur = () => {\n      clearTimeout(this._blurTimeout);\n      const t = this.getWindow();\n      t && (this._blurTimeout = t.setTimeout(() => {\n        this.mounted && this.setState({ focused: !1 }), this._blurTimeout = void 0;\n      }));\n    }, this.getInitialTop = () => {\n      if (this.props.top !== void 0)\n        return this.props.top;\n      if (this.props.initialTop !== void 0)\n        return this.props.initialTop;\n      let t = m;\n      if (this.props.height !== void 0 ? t = this.props.height : this.props.initialHeight !== void 0 && (t = this.props.initialHeight), this.props.appendTo)\n        return this.props.appendTo.offsetHeight / 2 - t / 2;\n      const e = this.getWindow();\n      return e ? e.innerHeight / 2 - t / 2 : 0;\n    }, this.getInitialLeft = () => {\n      if (this.props.left !== void 0)\n        return this.props.left;\n      if (this.props.initialLeft !== void 0)\n        return this.props.initialLeft;\n      let t = w;\n      if (this.props.width !== void 0 ? t = this.props.width : this.props.initialWidth !== void 0 && (t = this.props.initialWidth), this.props.appendTo)\n        return this.props.appendTo.offsetWidth / 2 - t / 2;\n      const e = this.getWindow();\n      return e ? e.innerWidth / 2 - t / 2 : 0;\n    }, this.getInitialWidth = () => {\n      let t = w;\n      return this.props.width !== void 0 ? t = this.props.width : this.props.initialWidth !== void 0 && (t = this.props.initialWidth), t;\n    }, this.getInitialHeight = () => {\n      let t = m;\n      return this.props.height !== void 0 ? t = this.props.height : this.props.initialHeight !== void 0 && (t = this.props.initialHeight), t;\n    }, this.handleMinimize = (t) => {\n      t.preventDefault(), this.windowCoordinatesState.leftBeforeAction = this.left, this.windowCoordinatesState.topBeforeAction = this.top, this.windowCoordinatesState.widthBeforeAction = this.width, this.windowCoordinatesState.heightBeforeAction = this.height, this.setState({\n        stage: i.MINIMIZED,\n        height: 0\n      }), l(this.props.onStageChange, t, this, { state: i.MINIMIZED });\n    }, this.handleFullscreen = (t) => {\n      t.preventDefault(), this.windowCoordinatesState.leftBeforeAction = this.left, this.windowCoordinatesState.topBeforeAction = this.top, this.windowCoordinatesState.widthBeforeAction = this.width, this.windowCoordinatesState.heightBeforeAction = this.height;\n      const e = this.getWindow(), o = e ? e.innerWidth : 0, p = e ? e.innerHeight : 0;\n      this.setState({\n        left: 0,\n        top: 0,\n        width: this.props.appendTo ? this.props.appendTo.offsetWidth : o,\n        height: this.props.appendTo ? this.props.appendTo.offsetHeight : p,\n        stage: i.FULLSCREEN\n      }), l(this.props.onStageChange, t, this, { state: i.FULLSCREEN });\n    }, this.handleRestore = (t) => {\n      t.preventDefault(), this.windowStage === i.FULLSCREEN ? this.setState({\n        stage: i.DEFAULT,\n        left: this.windowCoordinatesState.leftBeforeAction,\n        top: this.windowCoordinatesState.topBeforeAction,\n        width: this.windowCoordinatesState.widthBeforeAction,\n        height: this.windowCoordinatesState.heightBeforeAction\n      }) : this.windowStage === i.MINIMIZED && this.setState({\n        stage: i.DEFAULT,\n        height: this.windowCoordinatesState.heightBeforeAction\n      }), l(this.props.onStageChange, t, this, { state: i.DEFAULT });\n    }, this.handleCloseWindow = (t) => {\n      t.preventDefault(), l(this.props.onClose, t, this, { state: void 0 });\n    }, this.handleDoubleClick = (t) => {\n      this.windowStage === i.FULLSCREEN || this.windowStage === i.MINIMIZED ? this.handleRestore(t) : this.handleFullscreen(t);\n    }, this.handleResize = (t, e) => {\n      const o = this.props.appendTo ? t.pageX - this.offSetCoordinates.x : t.pageX, p = this.props.appendTo ? t.pageY - this.offSetCoordinates.y : t.pageY, r = this.width, c = this.height, D = this.props.minWidth || S, I = this.props.minHeight || E, T = this.top - p, L = this.left - o, b = o - this.left, W = p - this.top, g = Object.assign({}, this.state, { isDragging: !e.end });\n      e.direction.indexOf(\"n\") >= 0 && I - (c + T) < 0 && (this.top > 0 && (g.height = c + T), g.top = p), e.direction.indexOf(\"s\") >= 0 && I - W < 0 && (g.height = W), e.direction.indexOf(\"w\") >= 0 && D - (r + L) < 0 && (this.left > 0 && (g.width = r + L), g.left = o), e.direction.indexOf(\"e\") >= 0 && D - b < 0 && (g.width = b), this.setState(g), this.dispatchMoveEvent(this.props.onResize, t, !0, e.end);\n    }, this.dispatchMoveEvent = (t, e, o, p) => {\n      t && t.call(void 0, {\n        nativeEvent: e.nativeEvent ? e.nativeEvent : e.originalEvent,\n        drag: o,\n        end: p,\n        target: this,\n        left: this.state.left,\n        top: this.state.top,\n        width: this.state.width,\n        hight: this.state.height,\n        height: this.state.height\n      });\n    }, this.handleBrowserWindowResize = () => {\n      if (this.windowStage === i.FULLSCREEN) {\n        const t = this.getWindow(), e = t ? t.innerWidth : 0, o = t ? t.innerHeight : 0;\n        this.setState({\n          width: this.props.appendTo ? this.props.appendTo.offsetWidth : e,\n          height: this.props.appendTo ? this.props.appendTo.offsetHeight : o\n        });\n      }\n    }, this.getCurrentZIndex = () => !this.state || this.context === void 0 ? this.context ? this.context : F : this.state.zIndex > (this.context ? this.context + y : 0) ? this.state.zIndex : this.context + y, this.getDocument = () => {\n      const t = u ? document : null;\n      return this.props.appendTo ? this.props.appendTo.ownerDocument : t;\n    }, this.getWindow = () => {\n      const t = this.getDocument();\n      return t && t.defaultView;\n    }, this.state = {\n      stage: this.props.stage || i.DEFAULT,\n      isDragging: !1,\n      top: 0,\n      left: 0,\n      width: w,\n      height: m,\n      focused: !0,\n      zIndex: F\n    }, u && (this.activeElement = document.activeElement);\n  }\n  get _id() {\n    return this.props.id + \"-accessibility-id\";\n  }\n  /**\n   * @hidden\n   */\n  componentDidMount() {\n    this.element && this.props.autoFocus && this.element.focus({ preventScroll: !0 });\n    const s = this.getWindow();\n    s && s.addEventListener(\"resize\", this.handleBrowserWindowResize), this.setState({\n      stage: this.props.stage || i.DEFAULT,\n      isDragging: !1,\n      top: this.getInitialTop(),\n      left: this.getInitialLeft(),\n      width: this.getInitialWidth(),\n      height: this.getInitialHeight(),\n      focused: !0,\n      zIndex: A(this.getCurrentZIndex(), this.getDocument(), this._id)\n    }), this.windowCoordinatesState = {\n      leftBeforeAction: this.getInitialLeft(),\n      topBeforeAction: this.getInitialTop(),\n      widthBeforeAction: this.getInitialWidth(),\n      heightBeforeAction: this.getInitialHeight()\n    };\n    const t = this.getDocument();\n    if (this.props.appendTo && t) {\n      const e = this.props.appendTo.getBoundingClientRect(), o = t.body.getBoundingClientRect();\n      this.offSetCoordinates.x = e.left - o.left, this.offSetCoordinates.y = e.top - o.top;\n    }\n    this.mounted = !0;\n  }\n  /**\n   * @hidden\n   */\n  componentWillUnmount() {\n    const s = this.getWindow();\n    s && s.removeEventListener(\"resize\", this.handleBrowserWindowResize), this.mounted = !1, setTimeout(() => {\n      var t;\n      !this.element && this.activeElement && u && (document.contains(this.activeElement) ? this.activeElement.focus({ preventScroll: !0 }) : this.activeElement.id && ((t = document.getElementById(this.activeElement.id)) == null || t.focus({ preventScroll: !0 })));\n    });\n  }\n  /**\n   * @hidden\n   */\n  componentDidUpdate(s) {\n    this.props.left && s.left !== this.props.left && this.setState({ left: this.props.left }), this.props.top && s.top !== this.props.top && this.setState({ top: this.props.top });\n    const t = this.getDocument();\n    if (this.props.appendTo && t) {\n      const e = this.props.appendTo.getBoundingClientRect(), o = t.body.getBoundingClientRect();\n      this.offSetCoordinates.x = e.left - o.left, this.offSetCoordinates.y = e.top - o.top;\n    }\n    this.mounted = !0;\n  }\n  /**\n   * @hidden\n   */\n  render() {\n    const s = h.Children.toArray(this.props.children), t = this.getContent(s), e = this.getActionBar(s), o = this.getCurrentZIndex(), p = N(\"k-window\", this.props.className, {\n      [`k-window-${this.props.themeColor}`]: this.props.themeColor,\n      \"k-window-minimized\": this.state.stage === \"MINIMIZED\",\n      \"k-focus\": this.state.focused\n    }), r = /* @__PURE__ */ h.createElement(M.Provider, { value: o }, /* @__PURE__ */ h.createElement(h.Fragment, null, this.props.modal && /* @__PURE__ */ h.createElement(\n      \"div\",\n      {\n        className: \"k-overlay\",\n        style: {\n          zIndex: o,\n          ...this.props.overlayStyle\n        }\n      }\n    ), /* @__PURE__ */ h.createElement(\n      \"div\",\n      {\n        id: this.props.id,\n        [O]: this._id,\n        tabIndex: 0,\n        role: \"dialog\",\n        \"aria-labelledby\": this.titleId,\n        onFocus: this.onFocus,\n        onBlur: this.onBlur,\n        onKeyDown: this.onKeyDown,\n        ref: (c) => {\n          this.windowElement = c, this.element = c;\n        },\n        className: p,\n        style: {\n          top: this.top,\n          left: this.left,\n          width: this.width,\n          height: this.height || \"\",\n          zIndex: o,\n          ...this.props.style\n        }\n      },\n      /* @__PURE__ */ h.createElement(\n        Z,\n        {\n          shouldUpdateOnDrag: this.props.shouldUpdateOnDrag || !1,\n          isDragging: this.state.isDragging\n        },\n        /* @__PURE__ */ h.createElement(\n          U,\n          {\n            onPress: this.onPress,\n            onDrag: this.onDrag,\n            onRelease: this.onRelease,\n            autoScroll: !1,\n            ref: (c) => {\n              this.draggable = c;\n            }\n          },\n          /* @__PURE__ */ h.createElement(\n            R,\n            {\n              stage: this.windowStage,\n              onDoubleClick: this.props.doubleClickStageChange ? this.handleDoubleClick : void 0,\n              onMinimizeButtonClick: this.handleMinimize,\n              onFullScreenButtonClick: this.handleFullscreen,\n              onRestoreButtonClick: this.handleRestore,\n              onCloseButtonClick: this.handleCloseWindow,\n              closeButton: this.props.closeButton,\n              minimizeButton: this.props.minimizeButton,\n              maximizeButton: this.props.maximizeButton,\n              restoreButton: this.props.restoreButton,\n              id: this.titleId\n            },\n            this.props.title\n          )\n        ),\n        this.windowStage !== i.MINIMIZED ? /* @__PURE__ */ h.createElement(h.Fragment, null, /* @__PURE__ */ h.createElement(\"div\", { className: \"k-window-content\" }, t), e) : null,\n        this.windowStage === i.DEFAULT && this.props.resizable ? /* @__PURE__ */ h.createElement(_, { onResize: this.handleResize }) : null\n      )\n    )));\n    return u ? this.props.appendTo !== null ? B.createPortal(r, this.props.appendTo || document.body) : r : null;\n  }\n  // Getters\n  get top() {\n    return this.windowStage !== i.FULLSCREEN ? Math.max(this.props.top || this.state.top, 0) : 0;\n  }\n  get left() {\n    return this.windowStage !== i.FULLSCREEN ? Math.max(this.props.left || this.state.left, 0) : 0;\n  }\n  get width() {\n    let s = this.props.width || this.state.width;\n    if (this.windowStage === i.FULLSCREEN) {\n      if (this.props.appendTo)\n        return s = this.props.appendTo.offsetWidth, s;\n      const t = this.getWindow();\n      s = t ? t.innerWidth : 0;\n    }\n    return s;\n  }\n  get height() {\n    let s = this.props.height || this.state.height;\n    if (this.windowStage === i.FULLSCREEN) {\n      if (this.props.appendTo)\n        return s = this.props.appendTo.offsetHeight, s;\n      const t = this.getWindow();\n      s = t ? t.innerHeight : 0;\n    } else this.windowStage === i.MINIMIZED && (s = 0);\n    return s;\n  }\n  get windowStage() {\n    return this.props.stage || this.state.stage;\n  }\n  getActionBar(s) {\n    return s.filter((t) => t && t.type === v);\n  }\n  getContent(s) {\n    return s.filter((t) => t && t.type !== v);\n  }\n  generateTitleId() {\n    return \"window-title-\" + this._id;\n  }\n};\nf.displayName = \"Window\", f.propTypes = {\n  width: n.number,\n  height: n.number,\n  left: n.number,\n  top: n.number,\n  initialWidth: n.number,\n  initialHeight: n.number,\n  initialLeft: n.number,\n  initialTop: n.number,\n  minWidth: n.number,\n  minHeight: n.number,\n  resizable: n.bool,\n  draggable: n.bool,\n  title: n.any,\n  shouldUpdateOnDrag: n.bool,\n  stage: n.oneOf([\"DEFAULT\", \"MINIMIZED\", \"FULLSCREEN\"]),\n  className: n.string,\n  id: n.string,\n  style: n.object,\n  overlayStyle: n.object,\n  autoFocus: n.bool\n}, f.defaultProps = {\n  minWidth: S,\n  minHeight: E,\n  resizable: !0,\n  draggable: !0,\n  modal: !1,\n  doubleClickStageChange: !0,\n  autoFocus: !0\n}, f.contextType = M;\nlet C = f;\nconst P = H(), K = z(\n  k(\n    P,\n    C\n  )\n);\nK.displayName = \"KendoReactWindow\";\nexport {\n  K as Window,\n  P as WindowPropsContext,\n  C as WindowWithoutContext\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAO,KAAKC,CAAC,MAAM,WAAW;AAC9B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,cAAc,IAAIC,CAAC,QAAQ,sBAAsB;AAC1D,SAASC,oBAAoB,IAAIC,CAAC,EAAEC,IAAI,IAAIC,CAAC,EAAEC,aAAa,IAAIC,CAAC,EAAEC,SAAS,IAAIC,CAAC,EAAEC,aAAa,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,EAAEC,SAAS,IAAIC,CAAC,EAAEC,kBAAkB,IAAIC,CAAC,EAAEC,SAAS,IAAIC,CAAC,EAAEC,gBAAgB,IAAIC,CAAC,QAAQ,8BAA8B;AAC5O,SAASC,cAAc,IAAIC,CAAC,QAAQ,4BAA4B;AAChE,SAASC,uBAAuB,IAAIC,CAAC,QAAQ,+BAA+B;AAC5E,SAASC,WAAW,IAAIC,CAAC,QAAQ,iBAAiB;AAClD,SAASC,gBAAgB,IAAIC,CAAC,QAAQ,wBAAwB;AAC9D,SAASC,sBAAsB,IAAIC,CAAC,EAAEC,mBAAmB,IAAIC,CAAC,EAAEC,eAAe,IAAIC,CAAC,QAAQ,iBAAiB;AAC7G,SAASC,YAAY,IAAIC,CAAC,QAAQ,aAAa;AAC/C,MAAMC,CAAC,GAAG,GAAG;EAAEC,CAAC,GAAG,GAAG;EAAEC,CAAC,GAAG,GAAG;EAAEC,CAAC,GAAG,GAAG;EAAEC,CAAC,GAAG,CAAC;EAAEC,CAAC,GAAG,MAAMA,CAAC,SAAS9C,CAAC,CAAC+C,SAAS,CAAC;IAC/EC,WAAWA,CAACC,CAAC,EAAE;MACb,KAAK,CAACA,CAAC,CAAC,EAAE,IAAI,CAACC,OAAO,GAAG,CAAC,EAAE,IAAI,CAACC,SAAS,GAAG,IAAI,EAAE,IAAI,CAACC,iBAAiB,GAAG;QAAE9C,CAAC,EAAE,CAAC;QAAE8B,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAACiB,OAAO,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC,EAAE,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE,IAAI,CAACC,aAAa,GAAG,IAAI,EAAE,IAAI,CAACC,SAAS,GAAIC,CAAC,IAAK;QACvM,IAAI,IAAI,CAACC,KAAK,CAACC,KAAK,IAAItD,CAAC,CAACoD,CAAC,EAAE,IAAI,CAACG,OAAO,CAAC,EAAEH,CAAC,CAACI,MAAM,KAAKJ,CAAC,CAACK,aAAa,EACtE;QACF,MAAMC,CAAC,GAAG,IAAI,CAACL,KAAK,CAACM,QAAQ,IAAItB,CAAC;UAAEuB,CAAC,GAAG,IAAI,CAACP,KAAK,CAACQ,SAAS,IAAIvB,CAAC;QACjE,IAAI,CAACc,CAAC,CAACU,OAAO,IAAIV,CAAC,CAACW,OAAO,KAAK,IAAI,CAACV,KAAK,CAACW,SAAS,EAAE;UACpD,QAAQZ,CAAC,CAACa,OAAO;YACf,KAAK/D,CAAC,CAACgE,EAAE;cACPd,CAAC,CAACe,cAAc,CAAC,CAAC,EAAEP,CAAC,IAAI,IAAI,CAACQ,MAAM,GAAG7B,CAAC,IAAI,IAAI,CAAC8B,QAAQ,CAAC;gBAAED,MAAM,EAAE,IAAI,CAACA,MAAM,GAAG7B;cAAE,CAAC,CAAC;cACtF;YACF,KAAKrC,CAAC,CAACoE,IAAI;cACTlB,CAAC,CAACe,cAAc,CAAC,CAAC,EAAE,IAAI,CAACE,QAAQ,CAAC;gBAAED,MAAM,EAAE,IAAI,CAACA,MAAM,GAAG7B;cAAE,CAAC,CAAC;cAC9D;YACF,KAAKrC,CAAC,CAACqE,IAAI;cACTb,CAAC,IAAI,IAAI,CAACc,KAAK,GAAGjC,CAAC,IAAI,IAAI,CAAC8B,QAAQ,CAAC;gBAAEG,KAAK,EAAE,IAAI,CAACA,KAAK,GAAGjC;cAAE,CAAC,CAAC;cAC/D;YACF,KAAKrC,CAAC,CAACuE,KAAK;cACV,IAAI,CAACJ,QAAQ,CAAC;gBAAEG,KAAK,EAAE,IAAI,CAACA,KAAK,GAAGjC;cAAE,CAAC,CAAC;cACxC;YACF;cACE;UACJ;UACA,IAAI,CAACmC,iBAAiB,CAAC,IAAI,CAACrB,KAAK,CAACsB,QAAQ,EAAEvB,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;UAC1D;QACF;QACA,IAAIA,CAAC,CAACwB,MAAM,EAAE;UACZ,QAAQxB,CAAC,CAACa,OAAO;YACf,KAAK/D,CAAC,CAACgE,EAAE;cACP,IAAI,CAAC3C,WAAW,KAAKC,CAAC,CAACqD,SAAS,IAAI,IAAI,CAACC,aAAa,CAAC1B,CAAC,CAAC,EAAEhD,CAAC,CAAC,IAAI,CAACiD,KAAK,CAAC0B,aAAa,EAAE3B,CAAC,EAAE,IAAI,EAAE;gBAAE4B,KAAK,EAAExD,CAAC,CAACyD;cAAQ,CAAC,CAAC,IAAI,IAAI,CAAC1D,WAAW,KAAKC,CAAC,CAACyD,OAAO,KAAK,IAAI,CAACC,gBAAgB,CAAC9B,CAAC,CAAC,EAAEhD,CAAC,CAAC,IAAI,CAACiD,KAAK,CAAC0B,aAAa,EAAE3B,CAAC,EAAE,IAAI,EAAE;gBAAE4B,KAAK,EAAExD,CAAC,CAAC2D;cAAW,CAAC,CAAC,CAAC;cACpP;YACF,KAAKjF,CAAC,CAACoE,IAAI;cACT,IAAI,CAAC/C,WAAW,KAAKC,CAAC,CAAC2D,UAAU,IAAI,IAAI,CAACL,aAAa,CAAC1B,CAAC,CAAC,EAAEhD,CAAC,CAAC,IAAI,CAACiD,KAAK,CAAC0B,aAAa,EAAE3B,CAAC,EAAE,IAAI,EAAE;gBAAE4B,KAAK,EAAExD,CAAC,CAACyD;cAAQ,CAAC,CAAC,IAAI,IAAI,CAAC1D,WAAW,KAAKC,CAAC,CAACyD,OAAO,KAAK,IAAI,CAACG,cAAc,CAAChC,CAAC,CAAC,EAAEhD,CAAC,CAAC,IAAI,CAACiD,KAAK,CAAC0B,aAAa,EAAE3B,CAAC,EAAE,IAAI,EAAE;gBAAE4B,KAAK,EAAExD,CAAC,CAACqD;cAAU,CAAC,CAAC,CAAC;cAClP;UACJ;UACA;QACF;QACA,IAAI,CAACzB,CAAC,CAACW,OAAO,EACZ,QAAQX,CAAC,CAACa,OAAO;UACf,KAAK/D,CAAC,CAACmF,GAAG;YACR,IAAI,CAAChC,KAAK,CAACiC,OAAO,IAAI,IAAI,CAACC,iBAAiB,CAACnC,CAAC,CAAC;YAC/C;UACF,KAAKlD,CAAC,CAACgE,EAAE;YACP,IAAI,CAACG,QAAQ,CAAEmB,CAAC,KAAM;cAAEC,GAAG,EAAED,CAAC,CAACC,GAAG,GAAGlD;YAAE,CAAC,CAAC,CAAC;YAC1C;UACF,KAAKrC,CAAC,CAACoE,IAAI;YACT,IAAI,CAACD,QAAQ,CAAEmB,CAAC,KAAM;cAAEC,GAAG,EAAED,CAAC,CAACC,GAAG,GAAGlD;YAAE,CAAC,CAAC,CAAC;YAC1C;UACF,KAAKrC,CAAC,CAACqE,IAAI;YACT,IAAI,CAACF,QAAQ,CAAEmB,CAAC,KAAM;cAAEjB,IAAI,EAAEiB,CAAC,CAACjB,IAAI,GAAGhC;YAAE,CAAC,CAAC,CAAC;YAC5C;UACF,KAAKrC,CAAC,CAACuE,KAAK;YACV,IAAI,CAACJ,QAAQ,CAAEmB,CAAC,KAAM;cAAEjB,IAAI,EAAEiB,CAAC,CAACjB,IAAI,GAAGhC;YAAE,CAAC,CAAC,CAAC;YAC5C;UACF;YACE;QACJ;QACF,IAAI,CAACmC,iBAAiB,CAAC,IAAI,CAACrB,KAAK,CAACqC,MAAM,EAAEtC,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;MAC1D,CAAC,EAAE,IAAI,CAACuC,OAAO,GAAIvC,CAAC,IAAK;QACvB,MAAMM,CAAC,GAAGN,CAAC,CAACwC,KAAK;QACjB,IAAI,CAACC,sBAAsB,CAACC,cAAc,GAAGpC,CAAC,CAACqC,KAAK,GAAG,IAAI,CAACxB,IAAI,EAAE,IAAI,CAACsB,sBAAsB,CAACG,aAAa,GAAGtC,CAAC,CAACuC,KAAK,GAAG,IAAI,CAACR,GAAG;MAClI,CAAC,EAAE,IAAI,CAACS,MAAM,GAAI9C,CAAC,IAAK;QACtB,MAAMM,CAAC,GAAGN,CAAC,CAACwC,KAAK;QACjBlC,CAAC,CAACyC,aAAa,CAAChC,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC5C,WAAW,KAAKC,CAAC,CAAC2D,UAAU,IAAI,IAAI,CAAC9B,KAAK,CAACR,SAAS,KAAK,IAAI,CAACwB,QAAQ,CAAC;UAC5GoB,GAAG,EAAEW,IAAI,CAACC,GAAG,CAAC3C,CAAC,CAACuC,KAAK,GAAG,IAAI,CAACJ,sBAAsB,CAACG,aAAa,EAAE,CAAC,CAAC;UACrEzB,IAAI,EAAEb,CAAC,CAACqC,KAAK,GAAG,IAAI,CAACF,sBAAsB,CAACC,cAAc;UAC1DQ,UAAU,EAAE,CAAC;QACf,CAAC,CAAC,EAAE,IAAI,CAACjD,KAAK,CAACqC,MAAM,IAAI,IAAI,CAAChB,iBAAiB,CAAC,IAAI,CAACrB,KAAK,CAACqC,MAAM,EAAEhC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChF,CAAC,EAAE,IAAI,CAAC6C,SAAS,GAAInD,CAAC,IAAK;QACzB,MAAMM,CAAC,GAAGN,CAAC,CAACwC,KAAK;QACjB,IAAI,CAACrE,WAAW,KAAKC,CAAC,CAAC2D,UAAU,IAAI,IAAI,CAAC9B,KAAK,CAACR,SAAS,IAAI,IAAI,CAACQ,KAAK,CAACqC,MAAM,IAAI,IAAI,CAAChB,iBAAiB,CAAC,IAAI,CAACrB,KAAK,CAACqC,MAAM,EAAEhC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAACW,QAAQ,CAAC;UACpJiC,UAAU,EAAE,CAAC;QACf,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,CAACE,OAAO,GAAG,MAAM;QACtB,IAAI,CAACC,YAAY,IAAIC,YAAY,CAAC,IAAI,CAACD,YAAY,CAAC,EAAE,IAAI,CAACA,YAAY,GAAG,KAAK,CAAC,IAAI,IAAI,CAACpC,QAAQ,CAAC;UAChGsC,OAAO,EAAE,CAAC,CAAC;UACXC,MAAM,EAAE1E,CAAC,CAAC,IAAI,CAAC2E,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAACC,WAAW,CAAC,CAAC,EAAE,IAAI,CAACC,GAAG;QACjE,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,CAACC,MAAM,GAAG,MAAM;QACrBN,YAAY,CAAC,IAAI,CAACD,YAAY,CAAC;QAC/B,MAAMrD,CAAC,GAAG,IAAI,CAAC6D,SAAS,CAAC,CAAC;QAC1B7D,CAAC,KAAK,IAAI,CAACqD,YAAY,GAAGrD,CAAC,CAAC8D,UAAU,CAAC,MAAM;UAC3C,IAAI,CAACjE,OAAO,IAAI,IAAI,CAACoB,QAAQ,CAAC;YAAEsC,OAAO,EAAE,CAAC;UAAE,CAAC,CAAC,EAAE,IAAI,CAACF,YAAY,GAAG,KAAK,CAAC;QAC5E,CAAC,CAAC,CAAC;MACL,CAAC,EAAE,IAAI,CAACU,aAAa,GAAG,MAAM;QAC5B,IAAI,IAAI,CAAC9D,KAAK,CAACoC,GAAG,KAAK,KAAK,CAAC,EAC3B,OAAO,IAAI,CAACpC,KAAK,CAACoC,GAAG;QACvB,IAAI,IAAI,CAACpC,KAAK,CAAC+D,UAAU,KAAK,KAAK,CAAC,EAClC,OAAO,IAAI,CAAC/D,KAAK,CAAC+D,UAAU;QAC9B,IAAIhE,CAAC,GAAGhB,CAAC;QACT,IAAI,IAAI,CAACiB,KAAK,CAACe,MAAM,KAAK,KAAK,CAAC,GAAGhB,CAAC,GAAG,IAAI,CAACC,KAAK,CAACe,MAAM,GAAG,IAAI,CAACf,KAAK,CAACgE,aAAa,KAAK,KAAK,CAAC,KAAKjE,CAAC,GAAG,IAAI,CAACC,KAAK,CAACgE,aAAa,CAAC,EAAE,IAAI,CAAChE,KAAK,CAACiE,QAAQ,EACnJ,OAAO,IAAI,CAACjE,KAAK,CAACiE,QAAQ,CAACC,YAAY,GAAG,CAAC,GAAGnE,CAAC,GAAG,CAAC;QACrD,MAAMM,CAAC,GAAG,IAAI,CAACuD,SAAS,CAAC,CAAC;QAC1B,OAAOvD,CAAC,GAAGA,CAAC,CAAC8D,WAAW,GAAG,CAAC,GAAGpE,CAAC,GAAG,CAAC,GAAG,CAAC;MAC1C,CAAC,EAAE,IAAI,CAACqE,cAAc,GAAG,MAAM;QAC7B,IAAI,IAAI,CAACpE,KAAK,CAACkB,IAAI,KAAK,KAAK,CAAC,EAC5B,OAAO,IAAI,CAAClB,KAAK,CAACkB,IAAI;QACxB,IAAI,IAAI,CAAClB,KAAK,CAACqE,WAAW,KAAK,KAAK,CAAC,EACnC,OAAO,IAAI,CAACrE,KAAK,CAACqE,WAAW;QAC/B,IAAItE,CAAC,GAAGjB,CAAC;QACT,IAAI,IAAI,CAACkB,KAAK,CAACmB,KAAK,KAAK,KAAK,CAAC,GAAGpB,CAAC,GAAG,IAAI,CAACC,KAAK,CAACmB,KAAK,GAAG,IAAI,CAACnB,KAAK,CAACsE,YAAY,KAAK,KAAK,CAAC,KAAKvE,CAAC,GAAG,IAAI,CAACC,KAAK,CAACsE,YAAY,CAAC,EAAE,IAAI,CAACtE,KAAK,CAACiE,QAAQ,EAC/I,OAAO,IAAI,CAACjE,KAAK,CAACiE,QAAQ,CAACM,WAAW,GAAG,CAAC,GAAGxE,CAAC,GAAG,CAAC;QACpD,MAAMM,CAAC,GAAG,IAAI,CAACuD,SAAS,CAAC,CAAC;QAC1B,OAAOvD,CAAC,GAAGA,CAAC,CAACmE,UAAU,GAAG,CAAC,GAAGzE,CAAC,GAAG,CAAC,GAAG,CAAC;MACzC,CAAC,EAAE,IAAI,CAAC0E,eAAe,GAAG,MAAM;QAC9B,IAAI1E,CAAC,GAAGjB,CAAC;QACT,OAAO,IAAI,CAACkB,KAAK,CAACmB,KAAK,KAAK,KAAK,CAAC,GAAGpB,CAAC,GAAG,IAAI,CAACC,KAAK,CAACmB,KAAK,GAAG,IAAI,CAACnB,KAAK,CAACsE,YAAY,KAAK,KAAK,CAAC,KAAKvE,CAAC,GAAG,IAAI,CAACC,KAAK,CAACsE,YAAY,CAAC,EAAEvE,CAAC;MACpI,CAAC,EAAE,IAAI,CAAC2E,gBAAgB,GAAG,MAAM;QAC/B,IAAI3E,CAAC,GAAGhB,CAAC;QACT,OAAO,IAAI,CAACiB,KAAK,CAACe,MAAM,KAAK,KAAK,CAAC,GAAGhB,CAAC,GAAG,IAAI,CAACC,KAAK,CAACe,MAAM,GAAG,IAAI,CAACf,KAAK,CAACgE,aAAa,KAAK,KAAK,CAAC,KAAKjE,CAAC,GAAG,IAAI,CAACC,KAAK,CAACgE,aAAa,CAAC,EAAEjE,CAAC;MACxI,CAAC,EAAE,IAAI,CAACgC,cAAc,GAAIhC,CAAC,IAAK;QAC9BA,CAAC,CAACe,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC0B,sBAAsB,CAACmC,gBAAgB,GAAG,IAAI,CAACzD,IAAI,EAAE,IAAI,CAACsB,sBAAsB,CAACoC,eAAe,GAAG,IAAI,CAACxC,GAAG,EAAE,IAAI,CAACI,sBAAsB,CAACqC,iBAAiB,GAAG,IAAI,CAAC1D,KAAK,EAAE,IAAI,CAACqB,sBAAsB,CAACsC,kBAAkB,GAAG,IAAI,CAAC/D,MAAM,EAAE,IAAI,CAACC,QAAQ,CAAC;UAC5Q+D,KAAK,EAAE5G,CAAC,CAACqD,SAAS;UAClBT,MAAM,EAAE;QACV,CAAC,CAAC,EAAEhE,CAAC,CAAC,IAAI,CAACiD,KAAK,CAAC0B,aAAa,EAAE3B,CAAC,EAAE,IAAI,EAAE;UAAE4B,KAAK,EAAExD,CAAC,CAACqD;QAAU,CAAC,CAAC;MAClE,CAAC,EAAE,IAAI,CAACK,gBAAgB,GAAI9B,CAAC,IAAK;QAChCA,CAAC,CAACe,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC0B,sBAAsB,CAACmC,gBAAgB,GAAG,IAAI,CAACzD,IAAI,EAAE,IAAI,CAACsB,sBAAsB,CAACoC,eAAe,GAAG,IAAI,CAACxC,GAAG,EAAE,IAAI,CAACI,sBAAsB,CAACqC,iBAAiB,GAAG,IAAI,CAAC1D,KAAK,EAAE,IAAI,CAACqB,sBAAsB,CAACsC,kBAAkB,GAAG,IAAI,CAAC/D,MAAM;QAC9P,MAAMV,CAAC,GAAG,IAAI,CAACuD,SAAS,CAAC,CAAC;UAAErD,CAAC,GAAGF,CAAC,GAAGA,CAAC,CAACmE,UAAU,GAAG,CAAC;UAAEQ,CAAC,GAAG3E,CAAC,GAAGA,CAAC,CAAC8D,WAAW,GAAG,CAAC;QAC/E,IAAI,CAACnD,QAAQ,CAAC;UACZE,IAAI,EAAE,CAAC;UACPkB,GAAG,EAAE,CAAC;UACNjB,KAAK,EAAE,IAAI,CAACnB,KAAK,CAACiE,QAAQ,GAAG,IAAI,CAACjE,KAAK,CAACiE,QAAQ,CAACM,WAAW,GAAGhE,CAAC;UAChEQ,MAAM,EAAE,IAAI,CAACf,KAAK,CAACiE,QAAQ,GAAG,IAAI,CAACjE,KAAK,CAACiE,QAAQ,CAACC,YAAY,GAAGc,CAAC;UAClED,KAAK,EAAE5G,CAAC,CAAC2D;QACX,CAAC,CAAC,EAAE/E,CAAC,CAAC,IAAI,CAACiD,KAAK,CAAC0B,aAAa,EAAE3B,CAAC,EAAE,IAAI,EAAE;UAAE4B,KAAK,EAAExD,CAAC,CAAC2D;QAAW,CAAC,CAAC;MACnE,CAAC,EAAE,IAAI,CAACL,aAAa,GAAI1B,CAAC,IAAK;QAC7BA,CAAC,CAACe,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC5C,WAAW,KAAKC,CAAC,CAAC2D,UAAU,GAAG,IAAI,CAACd,QAAQ,CAAC;UACpE+D,KAAK,EAAE5G,CAAC,CAACyD,OAAO;UAChBV,IAAI,EAAE,IAAI,CAACsB,sBAAsB,CAACmC,gBAAgB;UAClDvC,GAAG,EAAE,IAAI,CAACI,sBAAsB,CAACoC,eAAe;UAChDzD,KAAK,EAAE,IAAI,CAACqB,sBAAsB,CAACqC,iBAAiB;UACpD9D,MAAM,EAAE,IAAI,CAACyB,sBAAsB,CAACsC;QACtC,CAAC,CAAC,GAAG,IAAI,CAAC5G,WAAW,KAAKC,CAAC,CAACqD,SAAS,IAAI,IAAI,CAACR,QAAQ,CAAC;UACrD+D,KAAK,EAAE5G,CAAC,CAACyD,OAAO;UAChBb,MAAM,EAAE,IAAI,CAACyB,sBAAsB,CAACsC;QACtC,CAAC,CAAC,EAAE/H,CAAC,CAAC,IAAI,CAACiD,KAAK,CAAC0B,aAAa,EAAE3B,CAAC,EAAE,IAAI,EAAE;UAAE4B,KAAK,EAAExD,CAAC,CAACyD;QAAQ,CAAC,CAAC;MAChE,CAAC,EAAE,IAAI,CAACM,iBAAiB,GAAInC,CAAC,IAAK;QACjCA,CAAC,CAACe,cAAc,CAAC,CAAC,EAAE/D,CAAC,CAAC,IAAI,CAACiD,KAAK,CAACiC,OAAO,EAAElC,CAAC,EAAE,IAAI,EAAE;UAAE4B,KAAK,EAAE,KAAK;QAAE,CAAC,CAAC;MACvE,CAAC,EAAE,IAAI,CAACsD,iBAAiB,GAAIlF,CAAC,IAAK;QACjC,IAAI,CAAC7B,WAAW,KAAKC,CAAC,CAAC2D,UAAU,IAAI,IAAI,CAAC5D,WAAW,KAAKC,CAAC,CAACqD,SAAS,GAAG,IAAI,CAACC,aAAa,CAAC1B,CAAC,CAAC,GAAG,IAAI,CAAC8B,gBAAgB,CAAC9B,CAAC,CAAC;MAC1H,CAAC,EAAE,IAAI,CAACmF,YAAY,GAAG,CAACnF,CAAC,EAAEM,CAAC,KAAK;QAC/B,MAAME,CAAC,GAAG,IAAI,CAACP,KAAK,CAACiE,QAAQ,GAAGlE,CAAC,CAAC2C,KAAK,GAAG,IAAI,CAACjD,iBAAiB,CAAC9C,CAAC,GAAGoD,CAAC,CAAC2C,KAAK;UAAEsC,CAAC,GAAG,IAAI,CAAChF,KAAK,CAACiE,QAAQ,GAAGlE,CAAC,CAAC6C,KAAK,GAAG,IAAI,CAACnD,iBAAiB,CAAChB,CAAC,GAAGsB,CAAC,CAAC6C,KAAK;UAAET,CAAC,GAAG,IAAI,CAAChB,KAAK;UAAEgE,CAAC,GAAG,IAAI,CAACpE,MAAM;UAAEqE,CAAC,GAAG,IAAI,CAACpF,KAAK,CAACM,QAAQ,IAAItB,CAAC;UAAEqG,CAAC,GAAG,IAAI,CAACrF,KAAK,CAACQ,SAAS,IAAIvB,CAAC;UAAEqG,CAAC,GAAG,IAAI,CAAClD,GAAG,GAAG4C,CAAC;UAAEO,CAAC,GAAG,IAAI,CAACrE,IAAI,GAAGX,CAAC;UAAEiF,CAAC,GAAGjF,CAAC,GAAG,IAAI,CAACW,IAAI;UAAEuE,CAAC,GAAGT,CAAC,GAAG,IAAI,CAAC5C,GAAG;UAAEsD,CAAC,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACjE,KAAK,EAAE;YAAEsB,UAAU,EAAE,CAAC5C,CAAC,CAACwF;UAAI,CAAC,CAAC;QACvXxF,CAAC,CAACyF,SAAS,CAACC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAIV,CAAC,IAAIF,CAAC,GAAGG,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,CAAClD,GAAG,GAAG,CAAC,KAAKsD,CAAC,CAAC3E,MAAM,GAAGoE,CAAC,GAAGG,CAAC,CAAC,EAAEI,CAAC,CAACtD,GAAG,GAAG4C,CAAC,CAAC,EAAE3E,CAAC,CAACyF,SAAS,CAACC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAIV,CAAC,GAAGI,CAAC,GAAG,CAAC,KAAKC,CAAC,CAAC3E,MAAM,GAAG0E,CAAC,CAAC,EAAEpF,CAAC,CAACyF,SAAS,CAACC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAIX,CAAC,IAAIjD,CAAC,GAAGoD,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,CAACrE,IAAI,GAAG,CAAC,KAAKwE,CAAC,CAACvE,KAAK,GAAGgB,CAAC,GAAGoD,CAAC,CAAC,EAAEG,CAAC,CAACxE,IAAI,GAAGX,CAAC,CAAC,EAAEF,CAAC,CAACyF,SAAS,CAACC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAIX,CAAC,GAAGI,CAAC,GAAG,CAAC,KAAKE,CAAC,CAACvE,KAAK,GAAGqE,CAAC,CAAC,EAAE,IAAI,CAACxE,QAAQ,CAAC0E,CAAC,CAAC,EAAE,IAAI,CAACrE,iBAAiB,CAAC,IAAI,CAACrB,KAAK,CAACsB,QAAQ,EAAEvB,CAAC,EAAE,CAAC,CAAC,EAAEM,CAAC,CAACwF,GAAG,CAAC;MACnZ,CAAC,EAAE,IAAI,CAACxE,iBAAiB,GAAG,CAACtB,CAAC,EAAEM,CAAC,EAAEE,CAAC,EAAEyE,CAAC,KAAK;QAC1CjF,CAAC,IAAIA,CAAC,CAACiG,IAAI,CAAC,KAAK,CAAC,EAAE;UAClBC,WAAW,EAAE5F,CAAC,CAAC4F,WAAW,GAAG5F,CAAC,CAAC4F,WAAW,GAAG5F,CAAC,CAACyC,aAAa;UAC5DoD,IAAI,EAAE3F,CAAC;UACPsF,GAAG,EAAEb,CAAC;UACN7E,MAAM,EAAE,IAAI;UACZe,IAAI,EAAE,IAAI,CAACS,KAAK,CAACT,IAAI;UACrBkB,GAAG,EAAE,IAAI,CAACT,KAAK,CAACS,GAAG;UACnBjB,KAAK,EAAE,IAAI,CAACQ,KAAK,CAACR,KAAK;UACvBgF,KAAK,EAAE,IAAI,CAACxE,KAAK,CAACZ,MAAM;UACxBA,MAAM,EAAE,IAAI,CAACY,KAAK,CAACZ;QACrB,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,CAACqF,yBAAyB,GAAG,MAAM;QACxC,IAAI,IAAI,CAAClI,WAAW,KAAKC,CAAC,CAAC2D,UAAU,EAAE;UACrC,MAAM/B,CAAC,GAAG,IAAI,CAAC6D,SAAS,CAAC,CAAC;YAAEvD,CAAC,GAAGN,CAAC,GAAGA,CAAC,CAACyE,UAAU,GAAG,CAAC;YAAEjE,CAAC,GAAGR,CAAC,GAAGA,CAAC,CAACoE,WAAW,GAAG,CAAC;UAC/E,IAAI,CAACnD,QAAQ,CAAC;YACZG,KAAK,EAAE,IAAI,CAACnB,KAAK,CAACiE,QAAQ,GAAG,IAAI,CAACjE,KAAK,CAACiE,QAAQ,CAACM,WAAW,GAAGlE,CAAC;YAChEU,MAAM,EAAE,IAAI,CAACf,KAAK,CAACiE,QAAQ,GAAG,IAAI,CAACjE,KAAK,CAACiE,QAAQ,CAACC,YAAY,GAAG3D;UACnE,CAAC,CAAC;QACJ;MACF,CAAC,EAAE,IAAI,CAACiD,gBAAgB,GAAG,MAAM,CAAC,IAAI,CAAC7B,KAAK,IAAI,IAAI,CAACpC,OAAO,KAAK,KAAK,CAAC,GAAG,IAAI,CAACA,OAAO,GAAG,IAAI,CAACA,OAAO,GAAGhB,CAAC,GAAG,IAAI,CAACoD,KAAK,CAAC4B,MAAM,IAAI,IAAI,CAAChE,OAAO,GAAG,IAAI,CAACA,OAAO,GAAGd,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACkD,KAAK,CAAC4B,MAAM,GAAG,IAAI,CAAChE,OAAO,GAAGd,CAAC,EAAE,IAAI,CAACgF,WAAW,GAAG,MAAM;QACrO,MAAM1D,CAAC,GAAG9C,CAAC,GAAGoJ,QAAQ,GAAG,IAAI;QAC7B,OAAO,IAAI,CAACrG,KAAK,CAACiE,QAAQ,GAAG,IAAI,CAACjE,KAAK,CAACiE,QAAQ,CAACqC,aAAa,GAAGvG,CAAC;MACpE,CAAC,EAAE,IAAI,CAAC6D,SAAS,GAAG,MAAM;QACxB,MAAM7D,CAAC,GAAG,IAAI,CAAC0D,WAAW,CAAC,CAAC;QAC5B,OAAO1D,CAAC,IAAIA,CAAC,CAACwG,WAAW;MAC3B,CAAC,EAAE,IAAI,CAAC5E,KAAK,GAAG;QACdoD,KAAK,EAAE,IAAI,CAAC/E,KAAK,CAAC+E,KAAK,IAAI5G,CAAC,CAACyD,OAAO;QACpCqB,UAAU,EAAE,CAAC,CAAC;QACdb,GAAG,EAAE,CAAC;QACNlB,IAAI,EAAE,CAAC;QACPC,KAAK,EAAErC,CAAC;QACRiC,MAAM,EAAEhC,CAAC;QACTuE,OAAO,EAAE,CAAC,CAAC;QACXC,MAAM,EAAEhF;MACV,CAAC,EAAEtB,CAAC,KAAK,IAAI,CAAC4C,aAAa,GAAGwG,QAAQ,CAACxG,aAAa,CAAC;IACvD;IACA,IAAI6D,GAAGA,CAAA,EAAG;MACR,OAAO,IAAI,CAAC1D,KAAK,CAACwG,EAAE,GAAG,mBAAmB;IAC5C;IACA;AACF;AACA;IACEC,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAACvG,OAAO,IAAI,IAAI,CAACF,KAAK,CAAC0G,SAAS,IAAI,IAAI,CAACxG,OAAO,CAACyG,KAAK,CAAC;QAAEC,aAAa,EAAE,CAAC;MAAE,CAAC,CAAC;MACjF,MAAMtH,CAAC,GAAG,IAAI,CAACsE,SAAS,CAAC,CAAC;MAC1BtE,CAAC,IAAIA,CAAC,CAACuH,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACT,yBAAyB,CAAC,EAAE,IAAI,CAACpF,QAAQ,CAAC;QAC/E+D,KAAK,EAAE,IAAI,CAAC/E,KAAK,CAAC+E,KAAK,IAAI5G,CAAC,CAACyD,OAAO;QACpCqB,UAAU,EAAE,CAAC,CAAC;QACdb,GAAG,EAAE,IAAI,CAAC0B,aAAa,CAAC,CAAC;QACzB5C,IAAI,EAAE,IAAI,CAACkD,cAAc,CAAC,CAAC;QAC3BjD,KAAK,EAAE,IAAI,CAACsD,eAAe,CAAC,CAAC;QAC7B1D,MAAM,EAAE,IAAI,CAAC2D,gBAAgB,CAAC,CAAC;QAC/BpB,OAAO,EAAE,CAAC,CAAC;QACXC,MAAM,EAAE1E,CAAC,CAAC,IAAI,CAAC2E,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAACC,WAAW,CAAC,CAAC,EAAE,IAAI,CAACC,GAAG;MACjE,CAAC,CAAC,EAAE,IAAI,CAAClB,sBAAsB,GAAG;QAChCmC,gBAAgB,EAAE,IAAI,CAACP,cAAc,CAAC,CAAC;QACvCQ,eAAe,EAAE,IAAI,CAACd,aAAa,CAAC,CAAC;QACrCe,iBAAiB,EAAE,IAAI,CAACJ,eAAe,CAAC,CAAC;QACzCK,kBAAkB,EAAE,IAAI,CAACJ,gBAAgB,CAAC;MAC5C,CAAC;MACD,MAAM3E,CAAC,GAAG,IAAI,CAAC0D,WAAW,CAAC,CAAC;MAC5B,IAAI,IAAI,CAACzD,KAAK,CAACiE,QAAQ,IAAIlE,CAAC,EAAE;QAC5B,MAAMM,CAAC,GAAG,IAAI,CAACL,KAAK,CAACiE,QAAQ,CAAC6C,qBAAqB,CAAC,CAAC;UAAEvG,CAAC,GAAGR,CAAC,CAACgH,IAAI,CAACD,qBAAqB,CAAC,CAAC;QACzF,IAAI,CAACrH,iBAAiB,CAAC9C,CAAC,GAAG0D,CAAC,CAACa,IAAI,GAAGX,CAAC,CAACW,IAAI,EAAE,IAAI,CAACzB,iBAAiB,CAAChB,CAAC,GAAG4B,CAAC,CAAC+B,GAAG,GAAG7B,CAAC,CAAC6B,GAAG;MACtF;MACA,IAAI,CAACxC,OAAO,GAAG,CAAC,CAAC;IACnB;IACA;AACF;AACA;IACEoH,oBAAoBA,CAAA,EAAG;MACrB,MAAM1H,CAAC,GAAG,IAAI,CAACsE,SAAS,CAAC,CAAC;MAC1BtE,CAAC,IAAIA,CAAC,CAAC2H,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACb,yBAAyB,CAAC,EAAE,IAAI,CAACxG,OAAO,GAAG,CAAC,CAAC,EAAEiE,UAAU,CAAC,MAAM;QACxG,IAAI9D,CAAC;QACL,CAAC,IAAI,CAACG,OAAO,IAAI,IAAI,CAACL,aAAa,IAAI5C,CAAC,KAAKoJ,QAAQ,CAACa,QAAQ,CAAC,IAAI,CAACrH,aAAa,CAAC,GAAG,IAAI,CAACA,aAAa,CAAC8G,KAAK,CAAC;UAAEC,aAAa,EAAE,CAAC;QAAE,CAAC,CAAC,GAAG,IAAI,CAAC/G,aAAa,CAAC2G,EAAE,KAAK,CAACzG,CAAC,GAAGsG,QAAQ,CAACc,cAAc,CAAC,IAAI,CAACtH,aAAa,CAAC2G,EAAE,CAAC,KAAK,IAAI,IAAIzG,CAAC,CAAC4G,KAAK,CAAC;UAAEC,aAAa,EAAE,CAAC;QAAE,CAAC,CAAC,CAAC,CAAC;MACnQ,CAAC,CAAC;IACJ;IACA;AACF;AACA;IACEQ,kBAAkBA,CAAC9H,CAAC,EAAE;MACpB,IAAI,CAACU,KAAK,CAACkB,IAAI,IAAI5B,CAAC,CAAC4B,IAAI,KAAK,IAAI,CAAClB,KAAK,CAACkB,IAAI,IAAI,IAAI,CAACF,QAAQ,CAAC;QAAEE,IAAI,EAAE,IAAI,CAAClB,KAAK,CAACkB;MAAK,CAAC,CAAC,EAAE,IAAI,CAAClB,KAAK,CAACoC,GAAG,IAAI9C,CAAC,CAAC8C,GAAG,KAAK,IAAI,CAACpC,KAAK,CAACoC,GAAG,IAAI,IAAI,CAACpB,QAAQ,CAAC;QAAEoB,GAAG,EAAE,IAAI,CAACpC,KAAK,CAACoC;MAAI,CAAC,CAAC;MAC/K,MAAMrC,CAAC,GAAG,IAAI,CAAC0D,WAAW,CAAC,CAAC;MAC5B,IAAI,IAAI,CAACzD,KAAK,CAACiE,QAAQ,IAAIlE,CAAC,EAAE;QAC5B,MAAMM,CAAC,GAAG,IAAI,CAACL,KAAK,CAACiE,QAAQ,CAAC6C,qBAAqB,CAAC,CAAC;UAAEvG,CAAC,GAAGR,CAAC,CAACgH,IAAI,CAACD,qBAAqB,CAAC,CAAC;QACzF,IAAI,CAACrH,iBAAiB,CAAC9C,CAAC,GAAG0D,CAAC,CAACa,IAAI,GAAGX,CAAC,CAACW,IAAI,EAAE,IAAI,CAACzB,iBAAiB,CAAChB,CAAC,GAAG4B,CAAC,CAAC+B,GAAG,GAAG7B,CAAC,CAAC6B,GAAG;MACtF;MACA,IAAI,CAACxC,OAAO,GAAG,CAAC,CAAC;IACnB;IACA;AACF;AACA;IACEyH,MAAMA,CAAA,EAAG;MACP,MAAM/H,CAAC,GAAGjD,CAAC,CAACiL,QAAQ,CAACC,OAAO,CAAC,IAAI,CAACvH,KAAK,CAACwH,QAAQ,CAAC;QAAEzH,CAAC,GAAG,IAAI,CAAC0H,UAAU,CAACnI,CAAC,CAAC;QAAEe,CAAC,GAAG,IAAI,CAACqH,YAAY,CAACpI,CAAC,CAAC;QAAEiB,CAAC,GAAG,IAAI,CAACiD,gBAAgB,CAAC,CAAC;QAAEwB,CAAC,GAAG3H,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC2C,KAAK,CAAC2H,SAAS,EAAE;UACxK,CAAC,YAAY,IAAI,CAAC3H,KAAK,CAAC4H,UAAU,EAAE,GAAG,IAAI,CAAC5H,KAAK,CAAC4H,UAAU;UAC5D,oBAAoB,EAAE,IAAI,CAACjG,KAAK,CAACoD,KAAK,KAAK,WAAW;UACtD,SAAS,EAAE,IAAI,CAACpD,KAAK,CAAC2B;QACxB,CAAC,CAAC;QAAEnB,CAAC,GAAG,eAAgB9F,CAAC,CAACwL,aAAa,CAAC1K,CAAC,CAAC2K,QAAQ,EAAE;UAAEC,KAAK,EAAExH;QAAE,CAAC,EAAE,eAAgBlE,CAAC,CAACwL,aAAa,CAACxL,CAAC,CAAC2L,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAChI,KAAK,CAACC,KAAK,IAAI,eAAgB5D,CAAC,CAACwL,aAAa,CACrK,KAAK,EACL;UACEF,SAAS,EAAE,WAAW;UACtBM,KAAK,EAAE;YACL1E,MAAM,EAAEhD,CAAC;YACT,GAAG,IAAI,CAACP,KAAK,CAACkI;UAChB;QACF,CACF,CAAC,EAAE,eAAgB7L,CAAC,CAACwL,aAAa,CAChC,KAAK,EACL;UACErB,EAAE,EAAE,IAAI,CAACxG,KAAK,CAACwG,EAAE;UACjB,CAAC7H,CAAC,GAAG,IAAI,CAAC+E,GAAG;UACbyE,QAAQ,EAAE,CAAC;UACXC,IAAI,EAAE,QAAQ;UACd,iBAAiB,EAAE,IAAI,CAAC1I,OAAO;UAC/ByD,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBQ,MAAM,EAAE,IAAI,CAACA,MAAM;UACnB7D,SAAS,EAAE,IAAI,CAACA,SAAS;UACzBuI,GAAG,EAAGlD,CAAC,IAAK;YACV,IAAI,CAACmD,aAAa,GAAGnD,CAAC,EAAE,IAAI,CAACjF,OAAO,GAAGiF,CAAC;UAC1C,CAAC;UACDwC,SAAS,EAAE3C,CAAC;UACZiD,KAAK,EAAE;YACL7F,GAAG,EAAE,IAAI,CAACA,GAAG;YACblB,IAAI,EAAE,IAAI,CAACA,IAAI;YACfC,KAAK,EAAE,IAAI,CAACA,KAAK;YACjBJ,MAAM,EAAE,IAAI,CAACA,MAAM,IAAI,EAAE;YACzBwC,MAAM,EAAEhD,CAAC;YACT,GAAG,IAAI,CAACP,KAAK,CAACiI;UAChB;QACF,CAAC,EACD,eAAgB5L,CAAC,CAACwL,aAAa,CAC7B5J,CAAC,EACD;UACEsK,kBAAkB,EAAE,IAAI,CAACvI,KAAK,CAACuI,kBAAkB,IAAI,CAAC,CAAC;UACvDtF,UAAU,EAAE,IAAI,CAACtB,KAAK,CAACsB;QACzB,CAAC,EACD,eAAgB5G,CAAC,CAACwL,aAAa,CAC7BtK,CAAC,EACD;UACE+E,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBO,MAAM,EAAE,IAAI,CAACA,MAAM;UACnBK,SAAS,EAAE,IAAI,CAACA,SAAS;UACzBsF,UAAU,EAAE,CAAC,CAAC;UACdH,GAAG,EAAGlD,CAAC,IAAK;YACV,IAAI,CAAC3F,SAAS,GAAG2F,CAAC;UACpB;QACF,CAAC,EACD,eAAgB9I,CAAC,CAACwL,aAAa,CAC7BpL,CAAC,EACD;UACEsI,KAAK,EAAE,IAAI,CAAC7G,WAAW;UACvBuK,aAAa,EAAE,IAAI,CAACzI,KAAK,CAAC0I,sBAAsB,GAAG,IAAI,CAACzD,iBAAiB,GAAG,KAAK,CAAC;UAClF0D,qBAAqB,EAAE,IAAI,CAAC5G,cAAc;UAC1C6G,uBAAuB,EAAE,IAAI,CAAC/G,gBAAgB;UAC9CgH,oBAAoB,EAAE,IAAI,CAACpH,aAAa;UACxCqH,kBAAkB,EAAE,IAAI,CAAC5G,iBAAiB;UAC1C6G,WAAW,EAAE,IAAI,CAAC/I,KAAK,CAAC+I,WAAW;UACnCC,cAAc,EAAE,IAAI,CAAChJ,KAAK,CAACgJ,cAAc;UACzCC,cAAc,EAAE,IAAI,CAACjJ,KAAK,CAACiJ,cAAc;UACzCC,aAAa,EAAE,IAAI,CAAClJ,KAAK,CAACkJ,aAAa;UACvC1C,EAAE,EAAE,IAAI,CAAC9G;QACX,CAAC,EACD,IAAI,CAACM,KAAK,CAACmJ,KACb,CACF,CAAC,EACD,IAAI,CAACjL,WAAW,KAAKC,CAAC,CAACqD,SAAS,GAAG,eAAgBnF,CAAC,CAACwL,aAAa,CAACxL,CAAC,CAAC2L,QAAQ,EAAE,IAAI,EAAE,eAAgB3L,CAAC,CAACwL,aAAa,CAAC,KAAK,EAAE;UAAEF,SAAS,EAAE;QAAmB,CAAC,EAAE5H,CAAC,CAAC,EAAEM,CAAC,CAAC,GAAG,IAAI,EAC5K,IAAI,CAACnC,WAAW,KAAKC,CAAC,CAACyD,OAAO,IAAI,IAAI,CAAC5B,KAAK,CAACW,SAAS,GAAG,eAAgBtE,CAAC,CAACwL,aAAa,CAAC9J,CAAC,EAAE;UAAEuD,QAAQ,EAAE,IAAI,CAAC4D;QAAa,CAAC,CAAC,GAAG,IACjI,CACF,CAAC,CAAC,CAAC;MACH,OAAOjI,CAAC,GAAG,IAAI,CAAC+C,KAAK,CAACiE,QAAQ,KAAK,IAAI,GAAG3H,CAAC,CAAC8M,YAAY,CAACjH,CAAC,EAAE,IAAI,CAACnC,KAAK,CAACiE,QAAQ,IAAIoC,QAAQ,CAACU,IAAI,CAAC,GAAG5E,CAAC,GAAG,IAAI;IAC9G;IACA;IACA,IAAIC,GAAGA,CAAA,EAAG;MACR,OAAO,IAAI,CAAClE,WAAW,KAAKC,CAAC,CAAC2D,UAAU,GAAGiB,IAAI,CAACC,GAAG,CAAC,IAAI,CAAChD,KAAK,CAACoC,GAAG,IAAI,IAAI,CAACT,KAAK,CAACS,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC;IAC9F;IACA,IAAIlB,IAAIA,CAAA,EAAG;MACT,OAAO,IAAI,CAAChD,WAAW,KAAKC,CAAC,CAAC2D,UAAU,GAAGiB,IAAI,CAACC,GAAG,CAAC,IAAI,CAAChD,KAAK,CAACkB,IAAI,IAAI,IAAI,CAACS,KAAK,CAACT,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC;IAChG;IACA,IAAIC,KAAKA,CAAA,EAAG;MACV,IAAI7B,CAAC,GAAG,IAAI,CAACU,KAAK,CAACmB,KAAK,IAAI,IAAI,CAACQ,KAAK,CAACR,KAAK;MAC5C,IAAI,IAAI,CAACjD,WAAW,KAAKC,CAAC,CAAC2D,UAAU,EAAE;QACrC,IAAI,IAAI,CAAC9B,KAAK,CAACiE,QAAQ,EACrB,OAAO3E,CAAC,GAAG,IAAI,CAACU,KAAK,CAACiE,QAAQ,CAACM,WAAW,EAAEjF,CAAC;QAC/C,MAAMS,CAAC,GAAG,IAAI,CAAC6D,SAAS,CAAC,CAAC;QAC1BtE,CAAC,GAAGS,CAAC,GAAGA,CAAC,CAACyE,UAAU,GAAG,CAAC;MAC1B;MACA,OAAOlF,CAAC;IACV;IACA,IAAIyB,MAAMA,CAAA,EAAG;MACX,IAAIzB,CAAC,GAAG,IAAI,CAACU,KAAK,CAACe,MAAM,IAAI,IAAI,CAACY,KAAK,CAACZ,MAAM;MAC9C,IAAI,IAAI,CAAC7C,WAAW,KAAKC,CAAC,CAAC2D,UAAU,EAAE;QACrC,IAAI,IAAI,CAAC9B,KAAK,CAACiE,QAAQ,EACrB,OAAO3E,CAAC,GAAG,IAAI,CAACU,KAAK,CAACiE,QAAQ,CAACC,YAAY,EAAE5E,CAAC;QAChD,MAAMS,CAAC,GAAG,IAAI,CAAC6D,SAAS,CAAC,CAAC;QAC1BtE,CAAC,GAAGS,CAAC,GAAGA,CAAC,CAACoE,WAAW,GAAG,CAAC;MAC3B,CAAC,MAAM,IAAI,CAACjG,WAAW,KAAKC,CAAC,CAACqD,SAAS,KAAKlC,CAAC,GAAG,CAAC,CAAC;MAClD,OAAOA,CAAC;IACV;IACA,IAAIpB,WAAWA,CAAA,EAAG;MAChB,OAAO,IAAI,CAAC8B,KAAK,CAAC+E,KAAK,IAAI,IAAI,CAACpD,KAAK,CAACoD,KAAK;IAC7C;IACA2C,YAAYA,CAACpI,CAAC,EAAE;MACd,OAAOA,CAAC,CAAC+J,MAAM,CAAEtJ,CAAC,IAAKA,CAAC,IAAIA,CAAC,CAACuJ,IAAI,KAAKjL,CAAC,CAAC;IAC3C;IACAoJ,UAAUA,CAACnI,CAAC,EAAE;MACZ,OAAOA,CAAC,CAAC+J,MAAM,CAAEtJ,CAAC,IAAKA,CAAC,IAAIA,CAAC,CAACuJ,IAAI,KAAKjL,CAAC,CAAC;IAC3C;IACAsB,eAAeA,CAAA,EAAG;MAChB,OAAO,eAAe,GAAG,IAAI,CAAC+D,GAAG;IACnC;EACF,CAAC;AACDvE,CAAC,CAACoK,WAAW,GAAG,QAAQ,EAAEpK,CAAC,CAACqK,SAAS,GAAG;EACtCrI,KAAK,EAAE5E,CAAC,CAACkN,MAAM;EACf1I,MAAM,EAAExE,CAAC,CAACkN,MAAM;EAChBvI,IAAI,EAAE3E,CAAC,CAACkN,MAAM;EACdrH,GAAG,EAAE7F,CAAC,CAACkN,MAAM;EACbnF,YAAY,EAAE/H,CAAC,CAACkN,MAAM;EACtBzF,aAAa,EAAEzH,CAAC,CAACkN,MAAM;EACvBpF,WAAW,EAAE9H,CAAC,CAACkN,MAAM;EACrB1F,UAAU,EAAExH,CAAC,CAACkN,MAAM;EACpBnJ,QAAQ,EAAE/D,CAAC,CAACkN,MAAM;EAClBjJ,SAAS,EAAEjE,CAAC,CAACkN,MAAM;EACnB9I,SAAS,EAAEpE,CAAC,CAACmN,IAAI;EACjBlK,SAAS,EAAEjD,CAAC,CAACmN,IAAI;EACjBP,KAAK,EAAE5M,CAAC,CAACoN,GAAG;EACZpB,kBAAkB,EAAEhM,CAAC,CAACmN,IAAI;EAC1B3E,KAAK,EAAExI,CAAC,CAACqN,KAAK,CAAC,CAAC,SAAS,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;EACtDjC,SAAS,EAAEpL,CAAC,CAACsN,MAAM;EACnBrD,EAAE,EAAEjK,CAAC,CAACsN,MAAM;EACZ5B,KAAK,EAAE1L,CAAC,CAACuN,MAAM;EACf5B,YAAY,EAAE3L,CAAC,CAACuN,MAAM;EACtBpD,SAAS,EAAEnK,CAAC,CAACmN;AACf,CAAC,EAAEvK,CAAC,CAAC4K,YAAY,GAAG;EAClBzJ,QAAQ,EAAEtB,CAAC;EACXwB,SAAS,EAAEvB,CAAC;EACZ0B,SAAS,EAAE,CAAC,CAAC;EACbnB,SAAS,EAAE,CAAC,CAAC;EACbS,KAAK,EAAE,CAAC,CAAC;EACTyI,sBAAsB,EAAE,CAAC,CAAC;EAC1BhC,SAAS,EAAE,CAAC;AACd,CAAC,EAAEvH,CAAC,CAAC6K,WAAW,GAAG7M,CAAC;AACpB,IAAI8M,CAAC,GAAG9K,CAAC;AACT,MAAM+K,CAAC,GAAGzM,CAAC,CAAC,CAAC;EAAE0M,CAAC,GAAGxM,CAAC,CAClBE,CAAC,CACCqM,CAAC,EACDD,CACF,CACF,CAAC;AACDE,CAAC,CAACZ,WAAW,GAAG,kBAAkB;AAClC,SACEY,CAAC,IAAIC,MAAM,EACXF,CAAC,IAAIG,kBAAkB,EACvBJ,CAAC,IAAIK,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}