{"ast": null, "code": "/**\n * @hidden\n */\nexport var ifElse = function (predicate, right, left) {\n  return function (value) {\n    return predicate(value) ? right(value) : left(value);\n  };\n};\n/**\n * @hidden\n * Performs the right-to-left function composition. Functions should have a unary.\n */\nexport var compose = function () {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  return function (data) {\n    return args.reduceRight(function (acc, curr) {\n      return curr(acc);\n    }, data);\n  };\n};\n/**\n * @hidden\n */\nexport var constant = function (x) {\n  return function () {\n    return x;\n  };\n};\n/**\n * @hidden\n */\nexport var identity = function (x) {\n  return x;\n};", "map": {"version": 3, "names": ["ifElse", "predicate", "right", "left", "value", "compose", "args", "_i", "arguments", "length", "data", "reduceRight", "acc", "curr", "constant", "x", "identity"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-data-query/dist/es/funcs.js"], "sourcesContent": ["/**\n * @hidden\n */\nexport var ifElse = function (predicate, right, left) { return function (value) { return predicate(value) ? right(value) : left(value); }; };\n/**\n * @hidden\n * Performs the right-to-left function composition. Functions should have a unary.\n */\nexport var compose = function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    return function (data) { return args.reduceRight(function (acc, curr) { return curr(acc); }, data); };\n};\n/**\n * @hidden\n */\nexport var constant = function (x) { return function () { return x; }; };\n/**\n * @hidden\n */\nexport var identity = function (x) { return x; };\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,IAAIA,MAAM,GAAG,SAAAA,CAAUC,SAAS,EAAEC,KAAK,EAAEC,IAAI,EAAE;EAAE,OAAO,UAAUC,KAAK,EAAE;IAAE,OAAOH,SAAS,CAACG,KAAK,CAAC,GAAGF,KAAK,CAACE,KAAK,CAAC,GAAGD,IAAI,CAACC,KAAK,CAAC;EAAE,CAAC;AAAE,CAAC;AAC5I;AACA;AACA;AACA;AACA,OAAO,IAAIC,OAAO,GAAG,SAAAA,CAAA,EAAY;EAC7B,IAAIC,IAAI,GAAG,EAAE;EACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EAC5B;EACA,OAAO,UAAUG,IAAI,EAAE;IAAE,OAAOJ,IAAI,CAACK,WAAW,CAAC,UAAUC,GAAG,EAAEC,IAAI,EAAE;MAAE,OAAOA,IAAI,CAACD,GAAG,CAAC;IAAE,CAAC,EAAEF,IAAI,CAAC;EAAE,CAAC;AACzG,CAAC;AACD;AACA;AACA;AACA,OAAO,IAAII,QAAQ,GAAG,SAAAA,CAAUC,CAAC,EAAE;EAAE,OAAO,YAAY;IAAE,OAAOA,CAAC;EAAE,CAAC;AAAE,CAAC;AACxE;AACA;AACA;AACA,OAAO,IAAIC,QAAQ,GAAG,SAAAA,CAAUD,CAAC,EAAE;EAAE,OAAOA,CAAC;AAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}