{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nvar _typeof = require(\"@babel/runtime/helpers/typeof\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _createSuper2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createSuper\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _reactDom = _interopRequireDefault(require(\"react-dom\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nfunction _getRequireWildcardCache(nodeInterop) {\n  if (typeof WeakMap !== \"function\") return null;\n  var cacheBabelInterop = new WeakMap();\n  var cacheNodeInterop = new WeakMap();\n  return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) {\n    return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n  })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n  if (!nodeInterop && obj && obj.__esModule) {\n    return obj;\n  }\n  if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") {\n    return {\n      default: obj\n    };\n  }\n  var cache = _getRequireWildcardCache(nodeInterop);\n  if (cache && cache.has(obj)) {\n    return cache.get(obj);\n  }\n  var newObj = {};\n  var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var key in obj) {\n    if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n      var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n      if (desc && (desc.get || desc.set)) {\n        Object.defineProperty(newObj, key, desc);\n      } else {\n        newObj[key] = obj[key];\n      }\n    }\n  }\n  newObj.default = obj;\n  if (cache) {\n    cache.set(obj, newObj);\n  }\n  return newObj;\n}\nvar Notice = /*#__PURE__*/function (_Component) {\n  (0, _inherits2.default)(Notice, _Component);\n  var _super = (0, _createSuper2.default)(Notice);\n  function Notice() {\n    var _this;\n    (0, _classCallCheck2.default)(this, Notice);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _this.closeTimer = null;\n    _this.close = function (e) {\n      if (e) {\n        e.stopPropagation();\n      }\n      _this.clearCloseTimer();\n      var _this$props = _this.props,\n        onClose = _this$props.onClose,\n        noticeKey = _this$props.noticeKey;\n      if (onClose) {\n        onClose(noticeKey);\n      }\n    };\n    _this.startCloseTimer = function () {\n      if (_this.props.duration) {\n        _this.closeTimer = window.setTimeout(function () {\n          _this.close();\n        }, _this.props.duration * 1000);\n      }\n    };\n    _this.clearCloseTimer = function () {\n      if (_this.closeTimer) {\n        clearTimeout(_this.closeTimer);\n        _this.closeTimer = null;\n      }\n    };\n    return _this;\n  }\n  (0, _createClass2.default)(Notice, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.startCloseTimer();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      if (this.props.duration !== prevProps.duration || this.props.updateMark !== prevProps.updateMark ||\n      // Visible again need reset timer\n      this.props.visible !== prevProps.visible && this.props.visible) {\n        this.restartCloseTimer();\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.clearCloseTimer();\n    }\n  }, {\n    key: \"restartCloseTimer\",\n    value: function restartCloseTimer() {\n      this.clearCloseTimer();\n      this.startCloseTimer();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n      var _this$props2 = this.props,\n        prefixCls = _this$props2.prefixCls,\n        className = _this$props2.className,\n        closable = _this$props2.closable,\n        closeIcon = _this$props2.closeIcon,\n        style = _this$props2.style,\n        onClick = _this$props2.onClick,\n        children = _this$props2.children,\n        holder = _this$props2.holder;\n      var componentClass = \"\".concat(prefixCls, \"-notice\");\n      var dataOrAriaAttributeProps = Object.keys(this.props).reduce(function (acc, key) {\n        if (key.substr(0, 5) === 'data-' || key.substr(0, 5) === 'aria-' || key === 'role') {\n          acc[key] = _this2.props[key];\n        }\n        return acc;\n      }, {});\n      var node = /*#__PURE__*/React.createElement(\"div\", (0, _extends2.default)({\n        className: (0, _classnames.default)(componentClass, className, (0, _defineProperty2.default)({}, \"\".concat(componentClass, \"-closable\"), closable)),\n        style: style,\n        onMouseEnter: this.clearCloseTimer,\n        onMouseLeave: this.startCloseTimer,\n        onClick: onClick\n      }, dataOrAriaAttributeProps), /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(componentClass, \"-content\")\n      }, children), closable ? /*#__PURE__*/React.createElement(\"a\", {\n        tabIndex: 0,\n        onClick: this.close,\n        className: \"\".concat(componentClass, \"-close\")\n      }, closeIcon || /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(componentClass, \"-close-x\")\n      })) : null);\n      if (holder) {\n        return /*#__PURE__*/_reactDom.default.createPortal(node, holder);\n      }\n      return node;\n    }\n  }]);\n  return Notice;\n}(React.Component);\nexports.default = Notice;\nNotice.defaultProps = {\n  onClose: function onClose() {},\n  duration: 1.5\n};", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "_typeof", "Object", "defineProperty", "exports", "value", "default", "_extends2", "_defineProperty2", "_classCallCheck2", "_createClass2", "_inherits2", "_createSuper2", "React", "_interopRequireWildcard", "_reactDom", "_classnames", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "Notice", "_Component", "_super", "_this", "_len", "arguments", "length", "args", "Array", "_key", "apply", "concat", "closeTimer", "close", "e", "stopPropagation", "clearCloseTimer", "_this$props", "props", "onClose", "<PERSON><PERSON><PERSON>", "startCloseTimer", "duration", "window", "setTimeout", "clearTimeout", "componentDidMount", "componentDidUpdate", "prevProps", "updateMark", "visible", "restartCloseTimer", "componentWillUnmount", "render", "_this2", "_this$props2", "prefixCls", "className", "closable", "closeIcon", "style", "onClick", "children", "holder", "componentClass", "dataOrAriaAttributeProps", "keys", "reduce", "acc", "substr", "node", "createElement", "onMouseEnter", "onMouseLeave", "tabIndex", "createPortal", "Component", "defaultProps"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/rc-notification/lib/Notice.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nvar _typeof = require(\"@babel/runtime/helpers/typeof\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _createSuper2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createSuper\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _reactDom = _interopRequireDefault(require(\"react-dom\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\nvar Notice = /*#__PURE__*/function (_Component) {\n  (0, _inherits2.default)(Notice, _Component);\n  var _super = (0, _createSuper2.default)(Notice);\n  function Notice() {\n    var _this;\n    (0, _classCallCheck2.default)(this, Notice);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _this.closeTimer = null;\n    _this.close = function (e) {\n      if (e) {\n        e.stopPropagation();\n      }\n      _this.clearCloseTimer();\n      var _this$props = _this.props,\n        onClose = _this$props.onClose,\n        noticeKey = _this$props.noticeKey;\n      if (onClose) {\n        onClose(noticeKey);\n      }\n    };\n    _this.startCloseTimer = function () {\n      if (_this.props.duration) {\n        _this.closeTimer = window.setTimeout(function () {\n          _this.close();\n        }, _this.props.duration * 1000);\n      }\n    };\n    _this.clearCloseTimer = function () {\n      if (_this.closeTimer) {\n        clearTimeout(_this.closeTimer);\n        _this.closeTimer = null;\n      }\n    };\n    return _this;\n  }\n  (0, _createClass2.default)(Notice, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.startCloseTimer();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      if (this.props.duration !== prevProps.duration || this.props.updateMark !== prevProps.updateMark ||\n      // Visible again need reset timer\n      this.props.visible !== prevProps.visible && this.props.visible) {\n        this.restartCloseTimer();\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.clearCloseTimer();\n    }\n  }, {\n    key: \"restartCloseTimer\",\n    value: function restartCloseTimer() {\n      this.clearCloseTimer();\n      this.startCloseTimer();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n      var _this$props2 = this.props,\n        prefixCls = _this$props2.prefixCls,\n        className = _this$props2.className,\n        closable = _this$props2.closable,\n        closeIcon = _this$props2.closeIcon,\n        style = _this$props2.style,\n        onClick = _this$props2.onClick,\n        children = _this$props2.children,\n        holder = _this$props2.holder;\n      var componentClass = \"\".concat(prefixCls, \"-notice\");\n      var dataOrAriaAttributeProps = Object.keys(this.props).reduce(function (acc, key) {\n        if (key.substr(0, 5) === 'data-' || key.substr(0, 5) === 'aria-' || key === 'role') {\n          acc[key] = _this2.props[key];\n        }\n        return acc;\n      }, {});\n      var node = /*#__PURE__*/React.createElement(\"div\", (0, _extends2.default)({\n        className: (0, _classnames.default)(componentClass, className, (0, _defineProperty2.default)({}, \"\".concat(componentClass, \"-closable\"), closable)),\n        style: style,\n        onMouseEnter: this.clearCloseTimer,\n        onMouseLeave: this.startCloseTimer,\n        onClick: onClick\n      }, dataOrAriaAttributeProps), /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(componentClass, \"-content\")\n      }, children), closable ? /*#__PURE__*/React.createElement(\"a\", {\n        tabIndex: 0,\n        onClick: this.close,\n        className: \"\".concat(componentClass, \"-close\")\n      }, closeIcon || /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(componentClass, \"-close-x\")\n      })) : null);\n      if (holder) {\n        return /*#__PURE__*/_reactDom.default.createPortal(node, holder);\n      }\n      return node;\n    }\n  }]);\n  return Notice;\n}(React.Component);\nexports.default = Notice;\nNotice.defaultProps = {\n  onClose: function onClose() {},\n  duration: 1.5\n};"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AACpF,IAAIC,OAAO,GAAGD,OAAO,CAAC,+BAA+B,CAAC;AACtDE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIC,SAAS,GAAGR,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIQ,gBAAgB,GAAGT,sBAAsB,CAACC,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAC/F,IAAIS,gBAAgB,GAAGV,sBAAsB,CAACC,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAC/F,IAAIU,aAAa,GAAGX,sBAAsB,CAACC,OAAO,CAAC,oCAAoC,CAAC,CAAC;AACzF,IAAIW,UAAU,GAAGZ,sBAAsB,CAACC,OAAO,CAAC,iCAAiC,CAAC,CAAC;AACnF,IAAIY,aAAa,GAAGb,sBAAsB,CAACC,OAAO,CAAC,oCAAoC,CAAC,CAAC;AACzF,IAAIa,KAAK,GAAGC,uBAAuB,CAACd,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIe,SAAS,GAAGhB,sBAAsB,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC;AAC5D,IAAIgB,WAAW,GAAGjB,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAC/D,SAASiB,wBAAwBA,CAACC,WAAW,EAAE;EAAE,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAIC,iBAAiB,GAAG,IAAID,OAAO,CAAC,CAAC;EAAE,IAAIE,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;EAAE,OAAO,CAACF,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,WAAW,EAAE;IAAE,OAAOA,WAAW,GAAGG,gBAAgB,GAAGD,iBAAiB;EAAE,CAAC,EAAEF,WAAW,CAAC;AAAE;AAC9U,SAASJ,uBAAuBA,CAACQ,GAAG,EAAEJ,WAAW,EAAE;EAAE,IAAI,CAACA,WAAW,IAAII,GAAG,IAAIA,GAAG,CAACC,UAAU,EAAE;IAAE,OAAOD,GAAG;EAAE;EAAE,IAAIA,GAAG,KAAK,IAAI,IAAIrB,OAAO,CAACqB,GAAG,CAAC,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;IAAE,OAAO;MAAEhB,OAAO,EAAEgB;IAAI,CAAC;EAAE;EAAE,IAAIE,KAAK,GAAGP,wBAAwB,CAACC,WAAW,CAAC;EAAE,IAAIM,KAAK,IAAIA,KAAK,CAACC,GAAG,CAACH,GAAG,CAAC,EAAE;IAAE,OAAOE,KAAK,CAACE,GAAG,CAACJ,GAAG,CAAC;EAAE;EAAE,IAAIK,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIC,qBAAqB,GAAG1B,MAAM,CAACC,cAAc,IAAID,MAAM,CAAC2B,wBAAwB;EAAE,KAAK,IAAIC,GAAG,IAAIR,GAAG,EAAE;IAAE,IAAIQ,GAAG,KAAK,SAAS,IAAI5B,MAAM,CAAC6B,SAAS,CAACC,cAAc,CAACC,IAAI,CAACX,GAAG,EAAEQ,GAAG,CAAC,EAAE;MAAE,IAAII,IAAI,GAAGN,qBAAqB,GAAG1B,MAAM,CAAC2B,wBAAwB,CAACP,GAAG,EAAEQ,GAAG,CAAC,GAAG,IAAI;MAAE,IAAII,IAAI,KAAKA,IAAI,CAACR,GAAG,IAAIQ,IAAI,CAACC,GAAG,CAAC,EAAE;QAAEjC,MAAM,CAACC,cAAc,CAACwB,MAAM,EAAEG,GAAG,EAAEI,IAAI,CAAC;MAAE,CAAC,MAAM;QAAEP,MAAM,CAACG,GAAG,CAAC,GAAGR,GAAG,CAACQ,GAAG,CAAC;MAAE;IAAE;EAAE;EAAEH,MAAM,CAACrB,OAAO,GAAGgB,GAAG;EAAE,IAAIE,KAAK,EAAE;IAAEA,KAAK,CAACW,GAAG,CAACb,GAAG,EAAEK,MAAM,CAAC;EAAE;EAAE,OAAOA,MAAM;AAAE;AACryB,IAAIS,MAAM,GAAG,aAAa,UAAUC,UAAU,EAAE;EAC9C,CAAC,CAAC,EAAE1B,UAAU,CAACL,OAAO,EAAE8B,MAAM,EAAEC,UAAU,CAAC;EAC3C,IAAIC,MAAM,GAAG,CAAC,CAAC,EAAE1B,aAAa,CAACN,OAAO,EAAE8B,MAAM,CAAC;EAC/C,SAASA,MAAMA,CAAA,EAAG;IAChB,IAAIG,KAAK;IACT,CAAC,CAAC,EAAE9B,gBAAgB,CAACH,OAAO,EAAE,IAAI,EAAE8B,MAAM,CAAC;IAC3C,KAAK,IAAII,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IACAN,KAAK,GAAGD,MAAM,CAACL,IAAI,CAACa,KAAK,CAACR,MAAM,EAAE,CAAC,IAAI,CAAC,CAACS,MAAM,CAACJ,IAAI,CAAC,CAAC;IACtDJ,KAAK,CAACS,UAAU,GAAG,IAAI;IACvBT,KAAK,CAACU,KAAK,GAAG,UAAUC,CAAC,EAAE;MACzB,IAAIA,CAAC,EAAE;QACLA,CAAC,CAACC,eAAe,CAAC,CAAC;MACrB;MACAZ,KAAK,CAACa,eAAe,CAAC,CAAC;MACvB,IAAIC,WAAW,GAAGd,KAAK,CAACe,KAAK;QAC3BC,OAAO,GAAGF,WAAW,CAACE,OAAO;QAC7BC,SAAS,GAAGH,WAAW,CAACG,SAAS;MACnC,IAAID,OAAO,EAAE;QACXA,OAAO,CAACC,SAAS,CAAC;MACpB;IACF,CAAC;IACDjB,KAAK,CAACkB,eAAe,GAAG,YAAY;MAClC,IAAIlB,KAAK,CAACe,KAAK,CAACI,QAAQ,EAAE;QACxBnB,KAAK,CAACS,UAAU,GAAGW,MAAM,CAACC,UAAU,CAAC,YAAY;UAC/CrB,KAAK,CAACU,KAAK,CAAC,CAAC;QACf,CAAC,EAAEV,KAAK,CAACe,KAAK,CAACI,QAAQ,GAAG,IAAI,CAAC;MACjC;IACF,CAAC;IACDnB,KAAK,CAACa,eAAe,GAAG,YAAY;MAClC,IAAIb,KAAK,CAACS,UAAU,EAAE;QACpBa,YAAY,CAACtB,KAAK,CAACS,UAAU,CAAC;QAC9BT,KAAK,CAACS,UAAU,GAAG,IAAI;MACzB;IACF,CAAC;IACD,OAAOT,KAAK;EACd;EACA,CAAC,CAAC,EAAE7B,aAAa,CAACJ,OAAO,EAAE8B,MAAM,EAAE,CAAC;IAClCN,GAAG,EAAE,mBAAmB;IACxBzB,KAAK,EAAE,SAASyD,iBAAiBA,CAAA,EAAG;MAClC,IAAI,CAACL,eAAe,CAAC,CAAC;IACxB;EACF,CAAC,EAAE;IACD3B,GAAG,EAAE,oBAAoB;IACzBzB,KAAK,EAAE,SAAS0D,kBAAkBA,CAACC,SAAS,EAAE;MAC5C,IAAI,IAAI,CAACV,KAAK,CAACI,QAAQ,KAAKM,SAAS,CAACN,QAAQ,IAAI,IAAI,CAACJ,KAAK,CAACW,UAAU,KAAKD,SAAS,CAACC,UAAU;MAChG;MACA,IAAI,CAACX,KAAK,CAACY,OAAO,KAAKF,SAAS,CAACE,OAAO,IAAI,IAAI,CAACZ,KAAK,CAACY,OAAO,EAAE;QAC9D,IAAI,CAACC,iBAAiB,CAAC,CAAC;MAC1B;IACF;EACF,CAAC,EAAE;IACDrC,GAAG,EAAE,sBAAsB;IAC3BzB,KAAK,EAAE,SAAS+D,oBAAoBA,CAAA,EAAG;MACrC,IAAI,CAAChB,eAAe,CAAC,CAAC;IACxB;EACF,CAAC,EAAE;IACDtB,GAAG,EAAE,mBAAmB;IACxBzB,KAAK,EAAE,SAAS8D,iBAAiBA,CAAA,EAAG;MAClC,IAAI,CAACf,eAAe,CAAC,CAAC;MACtB,IAAI,CAACK,eAAe,CAAC,CAAC;IACxB;EACF,CAAC,EAAE;IACD3B,GAAG,EAAE,QAAQ;IACbzB,KAAK,EAAE,SAASgE,MAAMA,CAAA,EAAG;MACvB,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAACjB,KAAK;QAC3BkB,SAAS,GAAGD,YAAY,CAACC,SAAS;QAClCC,SAAS,GAAGF,YAAY,CAACE,SAAS;QAClCC,QAAQ,GAAGH,YAAY,CAACG,QAAQ;QAChCC,SAAS,GAAGJ,YAAY,CAACI,SAAS;QAClCC,KAAK,GAAGL,YAAY,CAACK,KAAK;QAC1BC,OAAO,GAAGN,YAAY,CAACM,OAAO;QAC9BC,QAAQ,GAAGP,YAAY,CAACO,QAAQ;QAChCC,MAAM,GAAGR,YAAY,CAACQ,MAAM;MAC9B,IAAIC,cAAc,GAAG,EAAE,CAACjC,MAAM,CAACyB,SAAS,EAAE,SAAS,CAAC;MACpD,IAAIS,wBAAwB,GAAG/E,MAAM,CAACgF,IAAI,CAAC,IAAI,CAAC5B,KAAK,CAAC,CAAC6B,MAAM,CAAC,UAAUC,GAAG,EAAEtD,GAAG,EAAE;QAChF,IAAIA,GAAG,CAACuD,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,IAAIvD,GAAG,CAACuD,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,IAAIvD,GAAG,KAAK,MAAM,EAAE;UAClFsD,GAAG,CAACtD,GAAG,CAAC,GAAGwC,MAAM,CAAChB,KAAK,CAACxB,GAAG,CAAC;QAC9B;QACA,OAAOsD,GAAG;MACZ,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,IAAIE,IAAI,GAAG,aAAazE,KAAK,CAAC0E,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC,EAAEhF,SAAS,CAACD,OAAO,EAAE;QACxEmE,SAAS,EAAE,CAAC,CAAC,EAAEzD,WAAW,CAACV,OAAO,EAAE0E,cAAc,EAAEP,SAAS,EAAE,CAAC,CAAC,EAAEjE,gBAAgB,CAACF,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAACyC,MAAM,CAACiC,cAAc,EAAE,WAAW,CAAC,EAAEN,QAAQ,CAAC,CAAC;QACnJE,KAAK,EAAEA,KAAK;QACZY,YAAY,EAAE,IAAI,CAACpC,eAAe;QAClCqC,YAAY,EAAE,IAAI,CAAChC,eAAe;QAClCoB,OAAO,EAAEA;MACX,CAAC,EAAEI,wBAAwB,CAAC,EAAE,aAAapE,KAAK,CAAC0E,aAAa,CAAC,KAAK,EAAE;QACpEd,SAAS,EAAE,EAAE,CAAC1B,MAAM,CAACiC,cAAc,EAAE,UAAU;MACjD,CAAC,EAAEF,QAAQ,CAAC,EAAEJ,QAAQ,GAAG,aAAa7D,KAAK,CAAC0E,aAAa,CAAC,GAAG,EAAE;QAC7DG,QAAQ,EAAE,CAAC;QACXb,OAAO,EAAE,IAAI,CAAC5B,KAAK;QACnBwB,SAAS,EAAE,EAAE,CAAC1B,MAAM,CAACiC,cAAc,EAAE,QAAQ;MAC/C,CAAC,EAAEL,SAAS,IAAI,aAAa9D,KAAK,CAAC0E,aAAa,CAAC,MAAM,EAAE;QACvDd,SAAS,EAAE,EAAE,CAAC1B,MAAM,CAACiC,cAAc,EAAE,UAAU;MACjD,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;MACX,IAAID,MAAM,EAAE;QACV,OAAO,aAAahE,SAAS,CAACT,OAAO,CAACqF,YAAY,CAACL,IAAI,EAAEP,MAAM,CAAC;MAClE;MACA,OAAOO,IAAI;IACb;EACF,CAAC,CAAC,CAAC;EACH,OAAOlD,MAAM;AACf,CAAC,CAACvB,KAAK,CAAC+E,SAAS,CAAC;AAClBxF,OAAO,CAACE,OAAO,GAAG8B,MAAM;AACxBA,MAAM,CAACyD,YAAY,GAAG;EACpBtC,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG,CAAC,CAAC;EAC9BG,QAAQ,EAAE;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}