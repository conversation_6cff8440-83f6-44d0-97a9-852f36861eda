{"ast": null, "code": "import alignElements from './align-elements';\nexport default function align(elements, rect, alignment) {\n  alignElements(elements, rect, alignment, \"x\", \"width\");\n}", "map": {"version": 3, "names": ["alignElements", "align", "elements", "rect", "alignment"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/alignment/align.js"], "sourcesContent": ["\nimport alignElements from './align-elements';\n\nexport default function align(elements, rect, alignment) {\n    alignElements(elements, rect, alignment, \"x\", \"width\");\n}"], "mappings": "AACA,OAAOA,aAAa,MAAM,kBAAkB;AAE5C,eAAe,SAASC,KAAKA,CAACC,QAAQ,EAAEC,IAAI,EAAEC,SAAS,EAAE;EACrDJ,aAAa,CAACE,QAAQ,EAAEC,IAAI,EAAEC,SAAS,EAAE,GAAG,EAAE,OAAO,CAAC;AAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}