{"ast": null, "code": "import LRUCache from './lru-cache';\nimport { Class } from '../common';\nimport { objectKey, hashKey, normalizeText } from './util';\nfunction zeroSize() {\n  return {\n    width: 0,\n    height: 0,\n    baseline: 0\n  };\n}\nvar DEFAULT_OPTIONS = {\n  baselineMarkerSize: 1\n};\nvar defaultMeasureBox;\nif (typeof document !== \"undefined\") {\n  defaultMeasureBox = document.createElement(\"div\");\n  defaultMeasureBox.style.setProperty(\"position\", \"absolute\", \"important\");\n  defaultMeasureBox.style.setProperty(\"top\", \"-4000px\", \"important\");\n  defaultMeasureBox.style.setProperty(\"width\", \"auto\", \"important\");\n  defaultMeasureBox.style.setProperty(\"height\", \"auto\", \"important\");\n  defaultMeasureBox.style.setProperty(\"padding\", \"0\", \"important\");\n  defaultMeasureBox.style.setProperty(\"margin\", \"0\", \"important\");\n  defaultMeasureBox.style.setProperty(\"border\", \"0\", \"important\");\n  defaultMeasureBox.style.setProperty(\"line-height\", \"normal\", \"important\");\n  defaultMeasureBox.style.setProperty(\"visibility\", \"hidden\", \"important\");\n  defaultMeasureBox.style.setProperty(\"white-space\", \"pre\", \"important\");\n}\nvar TextMetrics = function (Class) {\n  function TextMetrics(options) {\n    Class.call(this);\n    this._cache = new LRUCache(1000);\n    this.options = Object.assign({}, DEFAULT_OPTIONS, options);\n  }\n  if (Class) TextMetrics.__proto__ = Class;\n  TextMetrics.prototype = Object.create(Class && Class.prototype);\n  TextMetrics.prototype.constructor = TextMetrics;\n  TextMetrics.prototype.measure = function measure(text, style, options) {\n    if (options === void 0) options = {};\n    if (typeof text === 'undefined' || text === null) {\n      return zeroSize();\n    }\n    var styleKey = objectKey(style);\n    var cacheKey = hashKey(text + styleKey);\n    var cachedResult = this._cache.get(cacheKey);\n    if (cachedResult) {\n      return cachedResult;\n    }\n    var size = zeroSize();\n    var measureBox = options.box || defaultMeasureBox;\n    var baselineMarker = this._baselineMarker().cloneNode(false);\n    for (var key in style) {\n      var value = style[key];\n      if (typeof value !== \"undefined\") {\n        measureBox.style[key] = value;\n      }\n    }\n    var textStr = options.normalizeText !== false ? normalizeText(text) : String(text);\n    measureBox.textContent = textStr;\n    measureBox.appendChild(baselineMarker);\n    document.body.appendChild(measureBox);\n    if (textStr.length) {\n      size.width = measureBox.offsetWidth - this.options.baselineMarkerSize;\n      size.height = measureBox.offsetHeight;\n      size.baseline = baselineMarker.offsetTop + this.options.baselineMarkerSize;\n    }\n    if (size.width > 0 && size.height > 0) {\n      this._cache.put(cacheKey, size);\n    }\n    measureBox.parentNode.removeChild(measureBox);\n    return size;\n  };\n  TextMetrics.prototype._baselineMarker = function _baselineMarker() {\n    var marker = document.createElement(\"div\");\n    marker.style.display = \"inline-block\";\n    marker.style.verticalAlign = \"baseline\";\n    marker.style.width = this.options.baselineMarkerSize + \"px\";\n    marker.style.height = this.options.baselineMarkerSize + \"px\";\n    marker.style.overflow = \"hidden\";\n    return marker;\n  };\n  return TextMetrics;\n}(Class);\nTextMetrics.current = new TextMetrics();\nexport default TextMetrics;", "map": {"version": 3, "names": ["L<PERSON><PERSON><PERSON>", "Class", "object<PERSON>ey", "hash<PERSON><PERSON>", "normalizeText", "zeroSize", "width", "height", "baseline", "DEFAULT_OPTIONS", "baselineMarkerSize", "defaultMeasureBox", "document", "createElement", "style", "setProperty", "TextMetrics", "options", "call", "_cache", "Object", "assign", "__proto__", "prototype", "create", "constructor", "measure", "text", "styleKey", "cache<PERSON>ey", "cachedResult", "get", "size", "measureBox", "box", "baseline<PERSON>arker", "_baselineMarker", "cloneNode", "key", "value", "textStr", "String", "textContent", "append<PERSON><PERSON><PERSON>", "body", "length", "offsetWidth", "offsetHeight", "offsetTop", "put", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "marker", "display", "verticalAlign", "overflow", "current"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/text-metrics/text-metrics.js"], "sourcesContent": ["import LRUCache from './lru-cache';\nimport { Class } from '../common';\nimport { objectKey, hashKey, normalizeText } from './util';\n\nfunction zeroSize() {\n    return { width: 0, height: 0, baseline: 0 };\n}\n\nvar DEFAULT_OPTIONS = {\n    baselineMarkerSize: 1\n};\n\nvar defaultMeasureBox;\n\nif (typeof document !== \"undefined\") {\n    defaultMeasureBox = document.createElement(\"div\");\n    defaultMeasureBox.style.setProperty(\"position\", \"absolute\", \"important\");\n    defaultMeasureBox.style.setProperty(\"top\", \"-4000px\", \"important\");\n    defaultMeasureBox.style.setProperty(\"width\", \"auto\", \"important\");\n    defaultMeasureBox.style.setProperty(\"height\", \"auto\", \"important\");\n    defaultMeasureBox.style.setProperty(\"padding\", \"0\", \"important\");\n    defaultMeasureBox.style.setProperty(\"margin\", \"0\", \"important\");\n    defaultMeasureBox.style.setProperty(\"border\", \"0\", \"important\");\n    defaultMeasureBox.style.setProperty(\"line-height\", \"normal\", \"important\");\n    defaultMeasureBox.style.setProperty(\"visibility\", \"hidden\", \"important\");\n    defaultMeasureBox.style.setProperty(\"white-space\", \"pre\", \"important\");\n}\n\nvar TextMetrics = (function (Class) {\n    function TextMetrics(options) {\n        Class.call(this);\n\n        this._cache = new LRUCache(1000);\n        this.options = Object.assign({}, DEFAULT_OPTIONS, options);\n    }\n\n    if ( Class ) TextMetrics.__proto__ = Class;\n    TextMetrics.prototype = Object.create( Class && Class.prototype );\n    TextMetrics.prototype.constructor = TextMetrics;\n\n    TextMetrics.prototype.measure = function measure (text, style, options) {\n        if ( options === void 0 ) options = {};\n\n        if (typeof text === 'undefined' || text === null) {\n            return zeroSize();\n        }\n\n        var styleKey = objectKey(style);\n        var cacheKey = hashKey(text + styleKey);\n        var cachedResult = this._cache.get(cacheKey);\n\n        if (cachedResult) {\n            return cachedResult;\n        }\n\n        var size = zeroSize();\n        var measureBox = options.box || defaultMeasureBox;\n        var baselineMarker = this._baselineMarker().cloneNode(false);\n\n        for (var key in style) {\n            var value = style[key];\n            if (typeof value !== \"undefined\") {\n                measureBox.style[key] = value;\n            }\n        }\n\n        var textStr = options.normalizeText !== false ? normalizeText(text) : String(text);\n\n        measureBox.textContent = textStr;\n        measureBox.appendChild(baselineMarker);\n        document.body.appendChild(measureBox);\n\n        if (textStr.length) {\n            size.width = measureBox.offsetWidth - this.options.baselineMarkerSize;\n            size.height = measureBox.offsetHeight;\n            size.baseline = baselineMarker.offsetTop + this.options.baselineMarkerSize;\n        }\n\n        if (size.width > 0 && size.height > 0) {\n            this._cache.put(cacheKey, size);\n        }\n\n        measureBox.parentNode.removeChild(measureBox);\n\n        return size;\n    };\n\n    TextMetrics.prototype._baselineMarker = function _baselineMarker () {\n        var marker = document.createElement(\"div\");\n        marker.style.display = \"inline-block\";\n        marker.style.verticalAlign = \"baseline\";\n        marker.style.width = this.options.baselineMarkerSize + \"px\";\n        marker.style.height = this.options.baselineMarkerSize + \"px\";\n        marker.style.overflow = \"hidden\";\n\n        return marker;\n    };\n\n    return TextMetrics;\n}(Class));\n\nTextMetrics.current = new TextMetrics();\n\nexport default TextMetrics;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,aAAa;AAClC,SAASC,KAAK,QAAQ,WAAW;AACjC,SAASC,SAAS,EAAEC,OAAO,EAAEC,aAAa,QAAQ,QAAQ;AAE1D,SAASC,QAAQA,CAAA,EAAG;EAChB,OAAO;IAAEC,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE,CAAC;IAAEC,QAAQ,EAAE;EAAE,CAAC;AAC/C;AAEA,IAAIC,eAAe,GAAG;EAClBC,kBAAkB,EAAE;AACxB,CAAC;AAED,IAAIC,iBAAiB;AAErB,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;EACjCD,iBAAiB,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EACjDF,iBAAiB,CAACG,KAAK,CAACC,WAAW,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,CAAC;EACxEJ,iBAAiB,CAACG,KAAK,CAACC,WAAW,CAAC,KAAK,EAAE,SAAS,EAAE,WAAW,CAAC;EAClEJ,iBAAiB,CAACG,KAAK,CAACC,WAAW,CAAC,OAAO,EAAE,MAAM,EAAE,WAAW,CAAC;EACjEJ,iBAAiB,CAACG,KAAK,CAACC,WAAW,CAAC,QAAQ,EAAE,MAAM,EAAE,WAAW,CAAC;EAClEJ,iBAAiB,CAACG,KAAK,CAACC,WAAW,CAAC,SAAS,EAAE,GAAG,EAAE,WAAW,CAAC;EAChEJ,iBAAiB,CAACG,KAAK,CAACC,WAAW,CAAC,QAAQ,EAAE,GAAG,EAAE,WAAW,CAAC;EAC/DJ,iBAAiB,CAACG,KAAK,CAACC,WAAW,CAAC,QAAQ,EAAE,GAAG,EAAE,WAAW,CAAC;EAC/DJ,iBAAiB,CAACG,KAAK,CAACC,WAAW,CAAC,aAAa,EAAE,QAAQ,EAAE,WAAW,CAAC;EACzEJ,iBAAiB,CAACG,KAAK,CAACC,WAAW,CAAC,YAAY,EAAE,QAAQ,EAAE,WAAW,CAAC;EACxEJ,iBAAiB,CAACG,KAAK,CAACC,WAAW,CAAC,aAAa,EAAE,KAAK,EAAE,WAAW,CAAC;AAC1E;AAEA,IAAIC,WAAW,GAAI,UAAUf,KAAK,EAAE;EAChC,SAASe,WAAWA,CAACC,OAAO,EAAE;IAC1BhB,KAAK,CAACiB,IAAI,CAAC,IAAI,CAAC;IAEhB,IAAI,CAACC,MAAM,GAAG,IAAInB,QAAQ,CAAC,IAAI,CAAC;IAChC,IAAI,CAACiB,OAAO,GAAGG,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEZ,eAAe,EAAEQ,OAAO,CAAC;EAC9D;EAEA,IAAKhB,KAAK,EAAGe,WAAW,CAACM,SAAS,GAAGrB,KAAK;EAC1Ce,WAAW,CAACO,SAAS,GAAGH,MAAM,CAACI,MAAM,CAAEvB,KAAK,IAAIA,KAAK,CAACsB,SAAU,CAAC;EACjEP,WAAW,CAACO,SAAS,CAACE,WAAW,GAAGT,WAAW;EAE/CA,WAAW,CAACO,SAAS,CAACG,OAAO,GAAG,SAASA,OAAOA,CAAEC,IAAI,EAAEb,KAAK,EAAEG,OAAO,EAAE;IACpE,IAAKA,OAAO,KAAK,KAAK,CAAC,EAAGA,OAAO,GAAG,CAAC,CAAC;IAEtC,IAAI,OAAOU,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,IAAI,EAAE;MAC9C,OAAOtB,QAAQ,CAAC,CAAC;IACrB;IAEA,IAAIuB,QAAQ,GAAG1B,SAAS,CAACY,KAAK,CAAC;IAC/B,IAAIe,QAAQ,GAAG1B,OAAO,CAACwB,IAAI,GAAGC,QAAQ,CAAC;IACvC,IAAIE,YAAY,GAAG,IAAI,CAACX,MAAM,CAACY,GAAG,CAACF,QAAQ,CAAC;IAE5C,IAAIC,YAAY,EAAE;MACd,OAAOA,YAAY;IACvB;IAEA,IAAIE,IAAI,GAAG3B,QAAQ,CAAC,CAAC;IACrB,IAAI4B,UAAU,GAAGhB,OAAO,CAACiB,GAAG,IAAIvB,iBAAiB;IACjD,IAAIwB,cAAc,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC,CAACC,SAAS,CAAC,KAAK,CAAC;IAE5D,KAAK,IAAIC,GAAG,IAAIxB,KAAK,EAAE;MACnB,IAAIyB,KAAK,GAAGzB,KAAK,CAACwB,GAAG,CAAC;MACtB,IAAI,OAAOC,KAAK,KAAK,WAAW,EAAE;QAC9BN,UAAU,CAACnB,KAAK,CAACwB,GAAG,CAAC,GAAGC,KAAK;MACjC;IACJ;IAEA,IAAIC,OAAO,GAAGvB,OAAO,CAACb,aAAa,KAAK,KAAK,GAAGA,aAAa,CAACuB,IAAI,CAAC,GAAGc,MAAM,CAACd,IAAI,CAAC;IAElFM,UAAU,CAACS,WAAW,GAAGF,OAAO;IAChCP,UAAU,CAACU,WAAW,CAACR,cAAc,CAAC;IACtCvB,QAAQ,CAACgC,IAAI,CAACD,WAAW,CAACV,UAAU,CAAC;IAErC,IAAIO,OAAO,CAACK,MAAM,EAAE;MAChBb,IAAI,CAAC1B,KAAK,GAAG2B,UAAU,CAACa,WAAW,GAAG,IAAI,CAAC7B,OAAO,CAACP,kBAAkB;MACrEsB,IAAI,CAACzB,MAAM,GAAG0B,UAAU,CAACc,YAAY;MACrCf,IAAI,CAACxB,QAAQ,GAAG2B,cAAc,CAACa,SAAS,GAAG,IAAI,CAAC/B,OAAO,CAACP,kBAAkB;IAC9E;IAEA,IAAIsB,IAAI,CAAC1B,KAAK,GAAG,CAAC,IAAI0B,IAAI,CAACzB,MAAM,GAAG,CAAC,EAAE;MACnC,IAAI,CAACY,MAAM,CAAC8B,GAAG,CAACpB,QAAQ,EAAEG,IAAI,CAAC;IACnC;IAEAC,UAAU,CAACiB,UAAU,CAACC,WAAW,CAAClB,UAAU,CAAC;IAE7C,OAAOD,IAAI;EACf,CAAC;EAEDhB,WAAW,CAACO,SAAS,CAACa,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAI;IAChE,IAAIgB,MAAM,GAAGxC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC1CuC,MAAM,CAACtC,KAAK,CAACuC,OAAO,GAAG,cAAc;IACrCD,MAAM,CAACtC,KAAK,CAACwC,aAAa,GAAG,UAAU;IACvCF,MAAM,CAACtC,KAAK,CAACR,KAAK,GAAG,IAAI,CAACW,OAAO,CAACP,kBAAkB,GAAG,IAAI;IAC3D0C,MAAM,CAACtC,KAAK,CAACP,MAAM,GAAG,IAAI,CAACU,OAAO,CAACP,kBAAkB,GAAG,IAAI;IAC5D0C,MAAM,CAACtC,KAAK,CAACyC,QAAQ,GAAG,QAAQ;IAEhC,OAAOH,MAAM;EACjB,CAAC;EAED,OAAOpC,WAAW;AACtB,CAAC,CAACf,KAAK,CAAE;AAETe,WAAW,CAACwC,OAAO,GAAG,IAAIxC,WAAW,CAAC,CAAC;AAEvC,eAAeA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}