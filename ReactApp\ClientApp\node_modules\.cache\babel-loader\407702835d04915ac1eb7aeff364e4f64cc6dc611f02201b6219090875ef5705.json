{"ast": null, "code": "import Point from '../geometry/point';\nimport translateToPoint from './translate-to-point';\nexport default function stackElements(elements, stackAxis, otherAxis, sizeField) {\n  if (elements.length > 1) {\n    var origin = new Point();\n    var previousBBox = elements[0].bbox;\n    for (var idx = 1; idx < elements.length; idx++) {\n      var element = elements[idx].element;\n      var bbox = elements[idx].bbox;\n      origin[stackAxis] = previousBBox.origin[stackAxis] + previousBBox.size[sizeField];\n      origin[otherAxis] = bbox.origin[otherAxis];\n      translateToPoint(origin, bbox, element);\n      bbox.origin[stackAxis] = origin[stackAxis];\n      previousBBox = bbox;\n    }\n  }\n}", "map": {"version": 3, "names": ["Point", "translateToPoint", "stackElements", "elements", "stackAxis", "otherAxis", "sizeField", "length", "origin", "previousBBox", "bbox", "idx", "element", "size"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/alignment/stack-elements.js"], "sourcesContent": ["import Point from '../geometry/point';\nimport translateToPoint from './translate-to-point';\n\nexport default function stackElements(elements, stackAxis, otherAxis, sizeField) {\n    if (elements.length > 1) {\n        var origin = new Point();\n        var previousBBox = elements[0].bbox;\n\n        for (var idx = 1; idx < elements.length; idx++) {\n            var element = elements[idx].element;\n            var bbox = elements[idx].bbox;\n            origin[stackAxis] = previousBBox.origin[stackAxis] + previousBBox.size[sizeField];\n            origin[otherAxis] = bbox.origin[otherAxis];\n            translateToPoint(origin, bbox, element);\n            bbox.origin[stackAxis] = origin[stackAxis];\n            previousBBox = bbox;\n        }\n    }\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,mBAAmB;AACrC,OAAOC,gBAAgB,MAAM,sBAAsB;AAEnD,eAAe,SAASC,aAAaA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,SAAS,EAAEC,SAAS,EAAE;EAC7E,IAAIH,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;IACrB,IAAIC,MAAM,GAAG,IAAIR,KAAK,CAAC,CAAC;IACxB,IAAIS,YAAY,GAAGN,QAAQ,CAAC,CAAC,CAAC,CAACO,IAAI;IAEnC,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGR,QAAQ,CAACI,MAAM,EAAEI,GAAG,EAAE,EAAE;MAC5C,IAAIC,OAAO,GAAGT,QAAQ,CAACQ,GAAG,CAAC,CAACC,OAAO;MACnC,IAAIF,IAAI,GAAGP,QAAQ,CAACQ,GAAG,CAAC,CAACD,IAAI;MAC7BF,MAAM,CAACJ,SAAS,CAAC,GAAGK,YAAY,CAACD,MAAM,CAACJ,SAAS,CAAC,GAAGK,YAAY,CAACI,IAAI,CAACP,SAAS,CAAC;MACjFE,MAAM,CAACH,SAAS,CAAC,GAAGK,IAAI,CAACF,MAAM,CAACH,SAAS,CAAC;MAC1CJ,gBAAgB,CAACO,MAAM,EAAEE,IAAI,EAAEE,OAAO,CAAC;MACvCF,IAAI,CAACF,MAAM,CAACJ,SAAS,CAAC,GAAGI,MAAM,CAACJ,SAAS,CAAC;MAC1CK,YAAY,GAAGC,IAAI;IACvB;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}