{"ast": null, "code": "import * as React from 'react';\nexport default function DropIndicator(_ref) {\n  var dropPosition = _ref.dropPosition,\n    dropLevelOffset = _ref.dropLevelOffset,\n    indent = _ref.indent;\n  var style = {\n    pointerEvents: 'none',\n    position: 'absolute',\n    right: 0,\n    backgroundColor: 'red',\n    height: 2\n  };\n  switch (dropPosition) {\n    case -1:\n      style.top = 0;\n      style.left = -dropLevelOffset * indent;\n      break;\n    case 1:\n      style.bottom = 0;\n      style.left = -dropLevelOffset * indent;\n      break;\n    case 0:\n      style.bottom = 0;\n      style.left = indent;\n      break;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: style\n  });\n}", "map": {"version": 3, "names": ["React", "DropIndicator", "_ref", "dropPosition", "dropLevelOffset", "indent", "style", "pointerEvents", "position", "right", "backgroundColor", "height", "top", "left", "bottom", "createElement"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/rc-tree/es/DropIndicator.js"], "sourcesContent": ["import * as React from 'react';\nexport default function DropIndicator(_ref) {\n  var dropPosition = _ref.dropPosition,\n    dropLevelOffset = _ref.dropLevelOffset,\n    indent = _ref.indent;\n  var style = {\n    pointerEvents: 'none',\n    position: 'absolute',\n    right: 0,\n    backgroundColor: 'red',\n    height: 2\n  };\n  switch (dropPosition) {\n    case -1:\n      style.top = 0;\n      style.left = -dropLevelOffset * indent;\n      break;\n    case 1:\n      style.bottom = 0;\n      style.left = -dropLevelOffset * indent;\n      break;\n    case 0:\n      style.bottom = 0;\n      style.left = indent;\n      break;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: style\n  });\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,aAAaA,CAACC,IAAI,EAAE;EAC1C,IAAIC,YAAY,GAAGD,IAAI,CAACC,YAAY;IAClCC,eAAe,GAAGF,IAAI,CAACE,eAAe;IACtCC,MAAM,GAAGH,IAAI,CAACG,MAAM;EACtB,IAAIC,KAAK,GAAG;IACVC,aAAa,EAAE,MAAM;IACrBC,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,CAAC;IACRC,eAAe,EAAE,KAAK;IACtBC,MAAM,EAAE;EACV,CAAC;EACD,QAAQR,YAAY;IAClB,KAAK,CAAC,CAAC;MACLG,KAAK,CAACM,GAAG,GAAG,CAAC;MACbN,KAAK,CAACO,IAAI,GAAG,CAACT,eAAe,GAAGC,MAAM;MACtC;IACF,KAAK,CAAC;MACJC,KAAK,CAACQ,MAAM,GAAG,CAAC;MAChBR,KAAK,CAACO,IAAI,GAAG,CAACT,eAAe,GAAGC,MAAM;MACtC;IACF,KAAK,CAAC;MACJC,KAAK,CAACQ,MAAM,GAAG,CAAC;MAChBR,KAAK,CAACO,IAAI,GAAGR,MAAM;MACnB;EACJ;EACA,OAAO,aAAaL,KAAK,CAACe,aAAa,CAAC,KAAK,EAAE;IAC7CT,KAAK,EAAEA;EACT,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}