{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\n\"use client\";\n\nimport { load as e } from \"./load.mjs\";\nconst n = o => {\n  const {\n    locale: r,\n    data: t,\n    children: a\n  } = o;\n  return r && e(t), a;\n};\nexport { n as IntlDataProvider };", "map": {"version": 3, "names": ["load", "e", "n", "o", "locale", "r", "data", "t", "children", "a", "IntlDataProvider"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-intl/Intl/IntlDataProvider.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\n\"use client\";\nimport { load as e } from \"./load.mjs\";\nconst n = (o) => {\n  const { locale: r, data: t, children: a } = o;\n  return r && e(t), a;\n};\nexport {\n  n as IntlDataProvider\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;;AACZ,SAASA,IAAI,IAAIC,CAAC,QAAQ,YAAY;AACtC,MAAMC,CAAC,GAAIC,CAAC,IAAK;EACf,MAAM;IAAEC,MAAM,EAAEC,CAAC;IAAEC,IAAI,EAAEC,CAAC;IAAEC,QAAQ,EAAEC;EAAE,CAAC,GAAGN,CAAC;EAC7C,OAAOE,CAAC,IAAIJ,CAAC,CAACM,CAAC,CAAC,EAAEE,CAAC;AACrB,CAAC;AACD,SACEP,CAAC,IAAIQ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}