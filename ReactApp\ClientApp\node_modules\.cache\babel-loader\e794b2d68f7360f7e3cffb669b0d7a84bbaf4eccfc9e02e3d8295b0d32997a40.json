{"ast": null, "code": "import { localeInfo } from '../cldr';\nimport { DECIMAL, DEFAULT_LOCALE, NUMBER_PLACEHOLDER, EMPTY } from '../common/constants';\nimport standardNumberFormat from './standard-number-format';\nimport customNumberFormat from './custom-number-format';\nimport formatOptions from './format-options';\nexport default function formatNumber(number, format, locale) {\n  if (format === void 0) format = NUMBER_PLACEHOLDER;\n  if (locale === void 0) locale = DEFAULT_LOCALE;\n  if (number === undefined || number === null) {\n    return EMPTY;\n  }\n  if (!isFinite(number)) {\n    return String(number);\n  }\n  var info = localeInfo(locale);\n  var options = formatOptions(format);\n  var result;\n  if (options) {\n    var style = options.style || DECIMAL;\n    result = standardNumberFormat(number, Object.assign({}, info.numbers[style], options), info);\n  } else {\n    result = customNumberFormat(number, format, info);\n  }\n  return result;\n}", "map": {"version": 3, "names": ["localeInfo", "DECIMAL", "DEFAULT_LOCALE", "NUMBER_PLACEHOLDER", "EMPTY", "standardNumberFormat", "customNumberFormat", "formatOptions", "formatNumber", "number", "format", "locale", "undefined", "isFinite", "String", "info", "options", "result", "style", "Object", "assign", "numbers"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-intl/dist/es/numbers/format-number.js"], "sourcesContent": ["import { localeInfo } from '../cldr';\nimport { DECIMAL, DEFAULT_LOCALE, NUMBER_PLACEHOLDER, EMPTY } from '../common/constants';\nimport standardNumberFormat from './standard-number-format';\nimport customNumberFormat from './custom-number-format';\nimport formatOptions from './format-options';\n\nexport default function formatNumber(number, format, locale) {\n    if ( format === void 0 ) format = NUMBER_PLACEHOLDER;\n    if ( locale === void 0 ) locale = DEFAULT_LOCALE;\n\n    if (number === undefined || number === null) {\n        return EMPTY;\n    }\n\n    if (!isFinite(number)) {\n        return String(number);\n    }\n\n    var info = localeInfo(locale);\n    var options = formatOptions(format);\n\n    var result;\n    if (options) {\n        var style = options.style || DECIMAL;\n        result = standardNumberFormat(number, Object.assign({}, info.numbers[style], options), info);\n    } else {\n        result = customNumberFormat(number, format, info);\n    }\n\n    return result;\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,SAAS;AACpC,SAASC,OAAO,EAAEC,cAAc,EAAEC,kBAAkB,EAAEC,KAAK,QAAQ,qBAAqB;AACxF,OAAOC,oBAAoB,MAAM,0BAA0B;AAC3D,OAAOC,kBAAkB,MAAM,wBAAwB;AACvD,OAAOC,aAAa,MAAM,kBAAkB;AAE5C,eAAe,SAASC,YAAYA,CAACC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE;EACzD,IAAKD,MAAM,KAAK,KAAK,CAAC,EAAGA,MAAM,GAAGP,kBAAkB;EACpD,IAAKQ,MAAM,KAAK,KAAK,CAAC,EAAGA,MAAM,GAAGT,cAAc;EAEhD,IAAIO,MAAM,KAAKG,SAAS,IAAIH,MAAM,KAAK,IAAI,EAAE;IACzC,OAAOL,KAAK;EAChB;EAEA,IAAI,CAACS,QAAQ,CAACJ,MAAM,CAAC,EAAE;IACnB,OAAOK,MAAM,CAACL,MAAM,CAAC;EACzB;EAEA,IAAIM,IAAI,GAAGf,UAAU,CAACW,MAAM,CAAC;EAC7B,IAAIK,OAAO,GAAGT,aAAa,CAACG,MAAM,CAAC;EAEnC,IAAIO,MAAM;EACV,IAAID,OAAO,EAAE;IACT,IAAIE,KAAK,GAAGF,OAAO,CAACE,KAAK,IAAIjB,OAAO;IACpCgB,MAAM,GAAGZ,oBAAoB,CAACI,MAAM,EAAEU,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEL,IAAI,CAACM,OAAO,CAACH,KAAK,CAAC,EAAEF,OAAO,CAAC,EAAED,IAAI,CAAC;EAChG,CAAC,MAAM;IACHE,MAAM,GAAGX,kBAAkB,CAACG,MAAM,EAAEC,MAAM,EAAEK,IAAI,CAAC;EACrD;EAEA,OAAOE,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}