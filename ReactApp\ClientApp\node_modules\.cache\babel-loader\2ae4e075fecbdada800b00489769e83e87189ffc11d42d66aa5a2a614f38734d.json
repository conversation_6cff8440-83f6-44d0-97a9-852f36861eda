{"ast": null, "code": "import Rect from '../../geometry/rect';\nexport default function elementsClippedBoundingBox(elements, transformation) {\n  var boundingBox;\n  for (var i = 0; i < elements.length; i++) {\n    var element = elements[i];\n    if (element.visible()) {\n      var elementBoundingBox = element.clippedBBox(transformation);\n      if (elementBoundingBox) {\n        if (boundingBox) {\n          boundingBox = Rect.union(boundingBox, elementBoundingBox);\n        } else {\n          boundingBox = elementBoundingBox;\n        }\n      }\n    }\n  }\n  return boundingBox;\n}", "map": {"version": 3, "names": ["Rect", "elementsClippedBoundingBox", "elements", "transformation", "boundingBox", "i", "length", "element", "visible", "elementBoundingBox", "clippedBBox", "union"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/shapes/utils/elements-clippend-bounding-box.js"], "sourcesContent": ["import Rect from '../../geometry/rect';\n\nexport default function elementsClippedBoundingBox(elements, transformation) {\n    var boundingBox;\n\n    for (var i = 0; i < elements.length; i++) {\n        var element = elements[i];\n        if (element.visible()) {\n            var elementBoundingBox = element.clippedBBox(transformation);\n            if (elementBoundingBox) {\n                if (boundingBox) {\n                    boundingBox = Rect.union(boundingBox, elementBoundingBox);\n                } else {\n                    boundingBox = elementBoundingBox;\n                }\n            }\n        }\n    }\n\n    return boundingBox;\n}"], "mappings": "AAAA,OAAOA,IAAI,MAAM,qBAAqB;AAEtC,eAAe,SAASC,0BAA0BA,CAACC,QAAQ,EAAEC,cAAc,EAAE;EACzE,IAAIC,WAAW;EAEf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,QAAQ,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACtC,IAAIE,OAAO,GAAGL,QAAQ,CAACG,CAAC,CAAC;IACzB,IAAIE,OAAO,CAACC,OAAO,CAAC,CAAC,EAAE;MACnB,IAAIC,kBAAkB,GAAGF,OAAO,CAACG,WAAW,CAACP,cAAc,CAAC;MAC5D,IAAIM,kBAAkB,EAAE;QACpB,IAAIL,WAAW,EAAE;UACbA,WAAW,GAAGJ,IAAI,CAACW,KAAK,CAACP,WAAW,EAAEK,kBAAkB,CAAC;QAC7D,CAAC,MAAM;UACHL,WAAW,GAAGK,kBAAkB;QACpC;MACJ;IACJ;EACJ;EAEA,OAAOL,WAAW;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}