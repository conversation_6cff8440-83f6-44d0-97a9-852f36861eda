{"ast": null, "code": "/**\n * @hidden\n *\n * An object which contains the information about the cities within the timezone.\n */\nexport var timezones = {\n  rules: {},\n  titles: {},\n  zones: {}\n};", "map": {"version": 3, "names": ["timezones", "rules", "titles", "zones"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/tz/timezones.js"], "sourcesContent": ["/**\n * @hidden\n *\n * An object which contains the information about the cities within the timezone.\n */\nexport var timezones = {\n    rules: {},\n    titles: {},\n    zones: {}\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIA,SAAS,GAAG;EACnBC,KAAK,EAAE,CAAC,CAAC;EACTC,MAAM,EAAE,CAAC,CAAC;EACVC,KAAK,EAAE,CAAC;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}