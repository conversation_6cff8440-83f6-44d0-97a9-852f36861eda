{"ast": null, "code": "/* Copyright 2022 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar _HighlightEditor_instances, _a, _HighlightEditor_anchorNode, _HighlightEditor_anchorOffset, _HighlightEditor_boxes, _HighlightEditor_clipPathId, _HighlightEditor_focusOutlines, _HighlightEditor_focusNode, _HighlightEditor_focusOffset, _HighlightEditor_highlightDiv, _HighlightEditor_highlightOutlines, _HighlightEditor_id, _HighlightEditor_isFreeHighlight, _HighlightEditor_lastPoint, _HighlightEditor_opacity, _HighlightEditor_outlineId, _HighlightEditor_text, _HighlightEditor_thickness, _HighlightEditor_methodOfCreation, _HighlightEditor_createOutlines, _HighlightEditor_createFreeOutlines, _HighlightEditor_updateColor, _HighlightEditor_updateThickness, _HighlightEditor_cleanDrawLayer, _HighlightEditor_addToDrawLayer, _HighlightEditor_rotateBbox, _HighlightEditor_keydown, _HighlightEditor_setCaret, _HighlightEditor_getRotation, _HighlightEditor_serializeBoxes, _HighlightEditor_serializeOutlines, _HighlightEditor_highlightMove, _HighlightEditor_endHighlight;\nimport { __classPrivateFieldGet, __classPrivateFieldSet } from \"tslib\";\nimport { AnnotationEditorParamsType, AnnotationEditorType,\n// DrawLayer,\n// shadow,\nUtil, noContextMenu } from \"pdfjs-dist/legacy/build/pdf.mjs\";\nimport { bindEvents } from \"../helpers/tools\";\n// import { bindEvents, KeyboardManager } from \"./tools.js\";\n// import { FreeOutliner, Outliner } from \"./outliner.js\";\nimport { AnnotationEditor } from \"./annotation-editor\";\nimport { FreeOutliner, Outliner } from \"./outliner\";\n// import { ColorPicker } from \"./color_picker.js\";\n// import { noContextMenu } from \"../../shared/display_utils\";\n/**\n * Basic draw editor in order to generate an Highlight annotation.\n */\nclass HighlightEditor extends AnnotationEditor {\n  // static get _keyboardManager() {\n  //     const proto = HighlightEditor.prototype;\n  //     return shadow(\n  //         this,\n  //         \"_keyboardManager\",\n  //         new KeyboardManager([\n  //             [[\"ArrowLeft\", \"mac+ArrowLeft\"], proto._moveCaret, { args: [0] }],\n  //             [[\"ArrowRight\", \"mac+ArrowRight\"], proto._moveCaret, { args: [1] }],\n  //             [[\"ArrowUp\", \"mac+ArrowUp\"], proto._moveCaret, { args: [2] }],\n  //             [[\"ArrowDown\", \"mac+ArrowDown\"], proto._moveCaret, { args: [3] }],\n  //         ])\n  //     );\n  // }\n  constructor(params) {\n    super(Object.assign(Object.assign({}, params), {\n      name: \"k-highlight-editor\"\n    }));\n    _HighlightEditor_instances.add(this);\n    // todo: props\n    this.color = \"\";\n    // parent = null;\n    // width = null;\n    // height = null;\n    // x = null;\n    // y = null;\n    // todo: props\n    _HighlightEditor_anchorNode.set(this, null);\n    _HighlightEditor_anchorOffset.set(this, 0);\n    _HighlightEditor_boxes.set(this, void 0);\n    _HighlightEditor_clipPathId.set(this, null);\n    // #colorPicker = null;\n    _HighlightEditor_focusOutlines.set(this, null);\n    _HighlightEditor_focusNode.set(this, null);\n    _HighlightEditor_focusOffset.set(this, 0);\n    _HighlightEditor_highlightDiv.set(this, null);\n    _HighlightEditor_highlightOutlines.set(this, null);\n    _HighlightEditor_id.set(this, null);\n    _HighlightEditor_isFreeHighlight.set(this, false);\n    _HighlightEditor_lastPoint.set(this, null);\n    _HighlightEditor_opacity.set(this, void 0);\n    _HighlightEditor_outlineId.set(this, null);\n    _HighlightEditor_text.set(this, \"\");\n    _HighlightEditor_thickness.set(this, void 0);\n    _HighlightEditor_methodOfCreation.set(this, \"\");\n    this.color = params.color || _a._defaultColor;\n    __classPrivateFieldSet(this, _HighlightEditor_thickness, params.thickness || _a._defaultThickness, \"f\");\n    __classPrivateFieldSet(this, _HighlightEditor_opacity, params.opacity || _a._defaultOpacity, \"f\");\n    __classPrivateFieldSet(this, _HighlightEditor_boxes, params.boxes || null, \"f\");\n    __classPrivateFieldSet(this, _HighlightEditor_methodOfCreation, params.methodOfCreation || \"\", \"f\");\n    __classPrivateFieldSet(this, _HighlightEditor_text, params.text || \"\", \"f\");\n    this._isDraggable = false;\n    if (params.highlightId > -1) {\n      __classPrivateFieldSet(this, _HighlightEditor_isFreeHighlight, true, \"f\");\n      __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_createFreeOutlines).call(this, params);\n      __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_addToDrawLayer).call(this);\n    } else {\n      __classPrivateFieldSet(this, _HighlightEditor_anchorNode, params.anchorNode, \"f\");\n      __classPrivateFieldSet(this, _HighlightEditor_anchorOffset, params.anchorOffset, \"f\");\n      __classPrivateFieldSet(this, _HighlightEditor_focusNode, params.focusNode, \"f\");\n      __classPrivateFieldSet(this, _HighlightEditor_focusOffset, params.focusOffset, \"f\");\n      __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_createOutlines).call(this);\n      __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_addToDrawLayer).call(this);\n      this.rotate(this.rotation);\n    }\n  }\n  /** @inheritdoc */\n  get telemetryInitialData() {\n    return {\n      action: \"added\",\n      type: __classPrivateFieldGet(this, _HighlightEditor_isFreeHighlight, \"f\") ? \"free_highlight\" : \"highlight\",\n      color: this._uiManager.highlightColorNames.get(this.color),\n      thickness: __classPrivateFieldGet(this, _HighlightEditor_thickness, \"f\"),\n      methodOfCreation: __classPrivateFieldGet(this, _HighlightEditor_methodOfCreation, \"f\")\n    };\n  }\n  /** @inheritdoc */\n  get telemetryFinalData() {\n    return {\n      type: \"highlight\",\n      color: this._uiManager.highlightColorNames.get(this.color)\n    };\n  }\n  static computeTelemetryFinalData(data) {\n    // We want to know how many colors have been used.\n    return {\n      numberOfColors: data.get(\"color\").size\n    };\n  }\n  /** @inheritdoc */\n  static initialize(l10n, uiManager) {\n    var _b, _c, _d, _e;\n    AnnotationEditor.initialize(l10n, uiManager, {});\n    _a._defaultColor || (_a._defaultColor =\n    // uiManager.highlightColors?.values().next().value || \"#fff066\";\n    // uiManager.highlightColors?.values().next().value || \"#ffff00\";\n    ((_e = (_d = (_c = (_b = uiManager.viewer) === null || _b === void 0 ? void 0 : _b.options) === null || _c === void 0 ? void 0 : _c.annotations) === null || _d === void 0 ? void 0 : _d.highlight) === null || _e === void 0 ? void 0 : _e.color) || \"#ffff00\");\n  }\n  /** @inheritdoc */\n  static updateDefaultParams(type, value) {\n    switch (type) {\n      case AnnotationEditorParamsType.HIGHLIGHT_DEFAULT_COLOR:\n        _a._defaultColor = value;\n        break;\n      case AnnotationEditorParamsType.HIGHLIGHT_THICKNESS:\n        _a._defaultThickness = value;\n        break;\n      default:\n        break;\n    }\n  }\n  /** @inheritdoc */\n  // translateInPage(x, y) { }\n  translateInPage() {}\n  /** @inheritdoc */\n  get toolbarPosition() {\n    return __classPrivateFieldGet(this, _HighlightEditor_lastPoint, \"f\");\n  }\n  /** @inheritdoc */\n  updateParams(type, value) {\n    switch (type) {\n      case AnnotationEditorParamsType.HIGHLIGHT_COLOR:\n        __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_updateColor).call(this, value);\n        break;\n      case AnnotationEditorParamsType.HIGHLIGHT_THICKNESS:\n        __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_updateThickness).call(this, value);\n        break;\n      default:\n        break;\n    }\n  }\n  static get defaultPropertiesToUpdate() {\n    return [[AnnotationEditorParamsType.HIGHLIGHT_DEFAULT_COLOR, _a._defaultColor], [AnnotationEditorParamsType.HIGHLIGHT_THICKNESS, _a._defaultThickness]];\n  }\n  /** @inheritdoc */\n  get propertiesToUpdate() {\n    return [[AnnotationEditorParamsType.HIGHLIGHT_COLOR, this.color || _a._defaultColor], [AnnotationEditorParamsType.HIGHLIGHT_THICKNESS, __classPrivateFieldGet(this, _HighlightEditor_thickness, \"f\") || _a._defaultThickness], [AnnotationEditorParamsType.HIGHLIGHT_FREE, __classPrivateFieldGet(this, _HighlightEditor_isFreeHighlight, \"f\")]];\n  }\n  /** @inheritdoc */\n  // async addEditToolbar() {\n  //     // const toolbar = await super.addEditToolbar();\n  //     // if (!toolbar) {\n  //     //     return null;\n  //     // }\n  //     // if (this._uiManager.highlightColors) {\n  //     //     // this.#colorPicker = new ColorPicker({ editor: this });\n  //     //     // toolbar.addColorPicker(this.#colorPicker);\n  //     // }\n  //     // return toolbar;\n  // }\n  /** @inheritdoc */\n  disableEditing() {\n    super.disableEditing();\n    // this.div.classList.toggle(\"disabled\", true);\n    this.div.classList.toggle(\"k-highlight-editor-disabled\", true);\n  }\n  /** @inheritdoc */\n  enableEditing() {\n    super.enableEditing();\n    // this.div.classList.toggle(\"disabled\", false);\n    this.div.classList.toggle(\"k-highlight-editor-disabled\", false);\n  }\n  /** @inheritdoc */\n  fixAndSetPosition() {\n    return super.fixAndSetPosition(__classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_getRotation).call(this));\n  }\n  /** @inheritdoc */\n  getBaseTranslation() {\n    // The editor itself doesn't have any CSS border (we're drawing one\n    // ourselves in using SVG).\n    return [0, 0];\n  }\n  /** @inheritdoc */\n  getRect(tx, ty) {\n    return super.getRect(tx, ty, __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_getRotation).call(this));\n  }\n  /** @inheritdoc */\n  onceAdded() {\n    this.parent.addUndoableEditor(this);\n    this.div.focus();\n  }\n  /** @inheritdoc */\n  remove() {\n    __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_cleanDrawLayer).call(this);\n    // this._reportTelemetry({\n    //     action: \"deleted\",\n    // });\n    super.remove();\n  }\n  /** @inheritdoc */\n  rebuild() {\n    if (!this.parent) {\n      return;\n    }\n    super.rebuild();\n    if (this.div === null) {\n      return;\n    }\n    __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_addToDrawLayer).call(this);\n    if (!this.isAttachedToDOM) {\n      // At some point this editor was removed and we're rebuilding it,\n      // hence we must add it to its parent.\n      this.parent.add(this);\n    }\n  }\n  setParent(parent) {\n    var _b, _c;\n    let mustBeSelected = false;\n    if (this.parent && !parent) {\n      __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_cleanDrawLayer).call(this);\n    } else if (parent) {\n      __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_addToDrawLayer).call(this, parent);\n      // If mustBeSelected is true it means that this editor was selected\n      // when its parent has been destroyed, hence we must select it again.\n      mustBeSelected = !this.parent && (((_b = this.div) === null || _b === void 0 ? void 0 : _b.classList.contains(\"selectedEditor\")) || ((_c = this.div) === null || _c === void 0 ? void 0 : _c.classList.contains(\"k-selected\")));\n    }\n    super.setParent(parent);\n    this.show(this._isVisible);\n    if (mustBeSelected) {\n      // We select it after the parent has been set.\n      this.select();\n    }\n  }\n  /** @inheritdoc */\n  rotate(angle) {\n    // We need to rotate the svgs because of the coordinates system.\n    const {\n      drawLayer\n    } = this.parent;\n    let box;\n    if (__classPrivateFieldGet(this, _HighlightEditor_isFreeHighlight, \"f\")) {\n      angle = (angle - this.rotation + 360) % 360;\n      box = __classPrivateFieldGet(_a, _a, \"m\", _HighlightEditor_rotateBbox).call(_a, __classPrivateFieldGet(this, _HighlightEditor_highlightOutlines, \"f\").box, angle);\n    } else {\n      // An highlight annotation is always drawn horizontally.\n      box = __classPrivateFieldGet(_a, _a, \"m\", _HighlightEditor_rotateBbox).call(_a, this, angle);\n    }\n    drawLayer.rotate(__classPrivateFieldGet(this, _HighlightEditor_id, \"f\"), angle);\n    drawLayer.rotate(__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\"), angle);\n    drawLayer.updateBox(__classPrivateFieldGet(this, _HighlightEditor_id, \"f\"), box);\n    drawLayer.updateBox(__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\"), __classPrivateFieldGet(_a, _a, \"m\", _HighlightEditor_rotateBbox).call(_a, __classPrivateFieldGet(this, _HighlightEditor_focusOutlines, \"f\").box, angle));\n  }\n  /** @inheritdoc */\n  render() {\n    if (this.div) {\n      return this.div;\n    }\n    const div = super.render();\n    if (__classPrivateFieldGet(this, _HighlightEditor_text, \"f\")) {\n      div.setAttribute(\"aria-label\", __classPrivateFieldGet(this, _HighlightEditor_text, \"f\"));\n      div.setAttribute(\"role\", \"mark\");\n    }\n    if (__classPrivateFieldGet(this, _HighlightEditor_isFreeHighlight, \"f\")) {\n      div.classList.add(\"free\");\n    } else {\n      this.div.addEventListener(\"keydown\", __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_keydown).bind(this), {\n        signal: this._uiManager._signal\n      });\n    }\n    const highlightDiv = __classPrivateFieldSet(this, _HighlightEditor_highlightDiv, document.createElement(\"div\"), \"f\");\n    div.append(highlightDiv);\n    highlightDiv.setAttribute(\"aria-hidden\", \"true\");\n    highlightDiv.className = \"k-internal internal\";\n    highlightDiv.style.clipPath = __classPrivateFieldGet(this, _HighlightEditor_clipPathId, \"f\");\n    const [parentWidth, parentHeight] = this.parentDimensions;\n    this.setDims(this.width * parentWidth, this.height * parentHeight);\n    bindEvents(this, __classPrivateFieldGet(this, _HighlightEditor_highlightDiv, \"f\"), [\"pointerover\", \"pointerleave\"]);\n    this.enableEditing();\n    return div;\n  }\n  pointerover() {\n    this.parent.drawLayer.addClass(__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\"), \"hovered\");\n  }\n  pointerleave() {\n    this.parent.drawLayer.removeClass(__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\"), \"hovered\");\n  }\n  _moveCaret(direction) {\n    this.parent.unselect(this);\n    switch (direction) {\n      case 0 /* left */:\n      case 2 /* up */:\n        __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_setCaret).call(this, /* start = */true);\n        break;\n      case 1 /* right */:\n      case 3 /* down */:\n        __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_setCaret).call(this, /* start = */false);\n        break;\n      default:\n        break;\n    }\n  }\n  /** @inheritdoc */\n  select() {\n    var _b, _c, _d;\n    super.select();\n    if (!__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\")) {\n      return;\n    }\n    (_b = this.parent) === null || _b === void 0 ? void 0 : _b.drawLayer.removeClass(__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\"), \"hovered\");\n    (_c = this.parent) === null || _c === void 0 ? void 0 : _c.drawLayer.addClass(__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\"), \"selected\");\n    (_d = this.parent) === null || _d === void 0 ? void 0 : _d.drawLayer.addClass(__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\"), \"k-selected\");\n  }\n  /** @inheritdoc */\n  unselect() {\n    var _b, _c;\n    super.unselect();\n    if (!__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\")) {\n      return;\n    }\n    (_b = this.parent) === null || _b === void 0 ? void 0 : _b.drawLayer.removeClass(__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\"), \"selected\");\n    (_c = this.parent) === null || _c === void 0 ? void 0 : _c.drawLayer.removeClass(__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\"), \"k-selected\");\n    if (!__classPrivateFieldGet(this, _HighlightEditor_isFreeHighlight, \"f\")) {\n      __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_setCaret).call(this, /* start = */false);\n    }\n  }\n  /** @inheritdoc */\n  get _mustFixPosition() {\n    return !__classPrivateFieldGet(this, _HighlightEditor_isFreeHighlight, \"f\");\n  }\n  /** @inheritdoc */\n  show(visible = this._isVisible) {\n    super.show(visible);\n    if (this.parent) {\n      this.parent.drawLayer.show(__classPrivateFieldGet(this, _HighlightEditor_id, \"f\"), visible);\n      this.parent.drawLayer.show(__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\"), visible);\n    }\n  }\n  static startHighlighting(parent, isLTR, {\n    target: textLayer,\n    x,\n    y\n  }) {\n    const {\n      x: layerX,\n      y: layerY,\n      width: parentWidth,\n      height: parentHeight\n    } = textLayer.getBoundingClientRect();\n    const ac = new AbortController();\n    const signal = parent.combinedSignal(ac);\n    const pointerDown = e => {\n      // Avoid to have undesired clicks during the drawing.\n      e.preventDefault();\n      e.stopPropagation();\n    };\n    const pointerUpCallback = e => {\n      ac.abort();\n      __classPrivateFieldGet(this, _a, \"m\", _HighlightEditor_endHighlight).call(this, parent, e);\n    };\n    window.addEventListener(\"blur\", pointerUpCallback, {\n      signal\n    });\n    window.addEventListener(\"pointerup\", pointerUpCallback, {\n      signal\n    });\n    window.addEventListener(\"pointerdown\", pointerDown, {\n      capture: true,\n      passive: false,\n      signal\n    });\n    window.addEventListener(\"contextmenu\", noContextMenu, {\n      signal\n    });\n    textLayer.addEventListener(\"pointermove\", __classPrivateFieldGet(this, _a, \"m\", _HighlightEditor_highlightMove).bind(this, parent), {\n      signal\n    });\n    this._freeHighlight = new FreeOutliner({\n      x,\n      y\n    }, [layerX, layerY, parentWidth, parentHeight], parent.scale, this._defaultThickness / 2, isLTR, /* innerMargin = */0.001);\n    ({\n      id: this._freeHighlightId,\n      clipPathId: this._freeHighlightClipId\n    } = parent.drawLayer.highlight(this._freeHighlight, this._defaultColor, this._defaultOpacity, /* isPathUpdatable = */true));\n  }\n  /** @inheritdoc */\n  static deserialize(data, parent, uiManager) {\n    const editor = super.deserialize(data, parent, uiManager);\n    const {\n      rect: [blX, blY, trX, trY],\n      color,\n      quadPoints\n    } = data;\n    // @ts-expect-error TS()\n    editor.color = Util.makeHexColor(...color);\n    __classPrivateFieldSet(editor, _HighlightEditor_opacity, data.opacity, \"f\");\n    const [pageWidth, pageHeight] = editor.pageDimensions;\n    editor.width = (trX - blX) / pageWidth;\n    editor.height = (trY - blY) / pageHeight;\n    const boxes = __classPrivateFieldSet(editor, _HighlightEditor_boxes, [], \"f\");\n    for (let i = 0; i < quadPoints.length; i += 8) {\n      boxes.push({\n        x: (quadPoints[4] - trX) / pageWidth,\n        y: (trY - (1 - quadPoints[i + 5])) / pageHeight,\n        width: (quadPoints[i + 2] - quadPoints[i]) / pageWidth,\n        height: (quadPoints[i + 5] - quadPoints[i + 1]) / pageHeight\n      });\n    }\n    __classPrivateFieldGet(editor, _HighlightEditor_instances, \"m\", _HighlightEditor_createOutlines).call(editor);\n    return editor;\n  }\n  /** @inheritdoc */\n  serialize(isForCopying = false) {\n    // It doesn't make sense to copy/paste a highlight annotation.\n    if (this.isEmpty() || isForCopying) {\n      return null;\n    }\n    const rect = this.getRect(0, 0);\n    const color = AnnotationEditor._colorManager.convert(this.color);\n    return {\n      annotationType: AnnotationEditorType.HIGHLIGHT,\n      color,\n      opacity: __classPrivateFieldGet(this, _HighlightEditor_opacity, \"f\"),\n      thickness: __classPrivateFieldGet(this, _HighlightEditor_thickness, \"f\"),\n      quadPoints: __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_serializeBoxes).call(this),\n      outlines: __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_serializeOutlines).call(this, rect),\n      pageIndex: this.pageIndex,\n      rect,\n      rotation: __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_getRotation).call(this),\n      structTreeParentId: this._structTreeParentId\n    };\n  }\n  static canCreateNewEmptyEditor() {\n    return false;\n  }\n  // todo: this is necessary\n  // saveDocument() has checks that test \"editor instanceof AnnotationEditor\", but they fail\n  // because AnnotationEditor from \"pdfjs-dist/legacy/build/pdf.mjs\" is not exported\n  // thus replace instances of editors with their serialized version\n  toJSON() {\n    const data = this.serialize();\n    return data;\n  }\n}\n_a = HighlightEditor, _HighlightEditor_anchorNode = new WeakMap(), _HighlightEditor_anchorOffset = new WeakMap(), _HighlightEditor_boxes = new WeakMap(), _HighlightEditor_clipPathId = new WeakMap(), _HighlightEditor_focusOutlines = new WeakMap(), _HighlightEditor_focusNode = new WeakMap(), _HighlightEditor_focusOffset = new WeakMap(), _HighlightEditor_highlightDiv = new WeakMap(), _HighlightEditor_highlightOutlines = new WeakMap(), _HighlightEditor_id = new WeakMap(), _HighlightEditor_isFreeHighlight = new WeakMap(), _HighlightEditor_lastPoint = new WeakMap(), _HighlightEditor_opacity = new WeakMap(), _HighlightEditor_outlineId = new WeakMap(), _HighlightEditor_text = new WeakMap(), _HighlightEditor_thickness = new WeakMap(), _HighlightEditor_methodOfCreation = new WeakMap(), _HighlightEditor_instances = new WeakSet(), _HighlightEditor_createOutlines = function _HighlightEditor_createOutlines() {\n  const outliner = new Outliner(__classPrivateFieldGet(this, _HighlightEditor_boxes, \"f\"), /* borderWidth = */0.001);\n  __classPrivateFieldSet(this, _HighlightEditor_highlightOutlines, outliner.getOutlines(), \"f\");\n  ({\n    x: this.x,\n    y: this.y,\n    width: this.width,\n    height: this.height\n  } = __classPrivateFieldGet(this, _HighlightEditor_highlightOutlines, \"f\").box);\n  const outlinerForOutline = new Outliner(__classPrivateFieldGet(this, _HighlightEditor_boxes, \"f\"), /* borderWidth = */0.0025, /* innerMargin = */0.001, this._uiManager.direction === \"ltr\");\n  __classPrivateFieldSet(this, _HighlightEditor_focusOutlines, outlinerForOutline.getOutlines(), \"f\");\n  // The last point is in the pages coordinate system.\n  const {\n    lastPoint\n  } = __classPrivateFieldGet(this, _HighlightEditor_focusOutlines, \"f\").box;\n  __classPrivateFieldSet(this, _HighlightEditor_lastPoint, [(lastPoint[0] - this.x) / this.width, (lastPoint[1] - this.y) / this.height], \"f\");\n}, _HighlightEditor_createFreeOutlines = function _HighlightEditor_createFreeOutlines({\n  highlightOutlines,\n  highlightId,\n  clipPathId\n}) {\n  __classPrivateFieldSet(this, _HighlightEditor_highlightOutlines, highlightOutlines, \"f\");\n  const extraThickness = 1.5;\n  __classPrivateFieldSet(this, _HighlightEditor_focusOutlines, highlightOutlines.getNewOutline(\n  /* Slightly bigger than the highlight in order to have a little\n     space between the highlight and the outline. */\n  __classPrivateFieldGet(this, _HighlightEditor_thickness, \"f\") / 2 + extraThickness, /* innerMargin = */0.0025), \"f\");\n  if (highlightId >= 0) {\n    __classPrivateFieldSet(this, _HighlightEditor_id, highlightId, \"f\");\n    __classPrivateFieldSet(this, _HighlightEditor_clipPathId, clipPathId, \"f\");\n    // We need to redraw the highlight because we change the coordinates to be\n    // in the box coordinate system.\n    this.parent.drawLayer.finalizeLine(highlightId, highlightOutlines);\n    __classPrivateFieldSet(this, _HighlightEditor_outlineId, this.parent.drawLayer.highlightOutline(__classPrivateFieldGet(this, _HighlightEditor_focusOutlines, \"f\")), \"f\");\n  } else if (this.parent) {\n    const angle = this.parent.viewport.rotation;\n    this.parent.drawLayer.updateLine(__classPrivateFieldGet(this, _HighlightEditor_id, \"f\"), highlightOutlines);\n    this.parent.drawLayer.updateBox(__classPrivateFieldGet(this, _HighlightEditor_id, \"f\"), __classPrivateFieldGet(_a, _a, \"m\", _HighlightEditor_rotateBbox).call(_a, __classPrivateFieldGet(this, _HighlightEditor_highlightOutlines, \"f\").box, (angle - this.rotation + 360) % 360));\n    this.parent.drawLayer.updateLine(__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\"), __classPrivateFieldGet(this, _HighlightEditor_focusOutlines, \"f\"));\n    this.parent.drawLayer.updateBox(__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\"), __classPrivateFieldGet(_a, _a, \"m\", _HighlightEditor_rotateBbox).call(_a, __classPrivateFieldGet(this, _HighlightEditor_focusOutlines, \"f\").box, angle));\n  }\n  const {\n    x,\n    y,\n    width,\n    height\n  } = highlightOutlines.box;\n  switch (this.rotation) {\n    case 0:\n      this.x = x;\n      this.y = y;\n      this.width = width;\n      this.height = height;\n      break;\n    case 90:\n      {\n        const [pageWidth, pageHeight] = this.parentDimensions;\n        this.x = y;\n        this.y = 1 - x;\n        this.width = width * pageHeight / pageWidth;\n        this.height = height * pageWidth / pageHeight;\n        break;\n      }\n    case 180:\n      this.x = 1 - x;\n      this.y = 1 - y;\n      this.width = width;\n      this.height = height;\n      break;\n    case 270:\n      {\n        const [pageWidth, pageHeight] = this.parentDimensions;\n        this.x = 1 - y;\n        this.y = x;\n        this.width = width * pageHeight / pageWidth;\n        this.height = height * pageWidth / pageHeight;\n        break;\n      }\n    default:\n      break;\n  }\n  const {\n    lastPoint\n  } = __classPrivateFieldGet(this, _HighlightEditor_focusOutlines, \"f\").box;\n  __classPrivateFieldSet(this, _HighlightEditor_lastPoint, [(lastPoint[0] - x) / width, (lastPoint[1] - y) / height], \"f\");\n}, _HighlightEditor_updateColor = function _HighlightEditor_updateColor(color) {\n  if (!color) {\n    return;\n  }\n  const setColor = col => {\n    var _b;\n    this.color = col;\n    (_b = this.parent) === null || _b === void 0 ? void 0 : _b.drawLayer.changeColor(__classPrivateFieldGet(this, _HighlightEditor_id, \"f\"), col);\n    // this.#colorPicker?.updateColor(col);\n  };\n  const savedColor = this.color;\n  this.addCommands({\n    cmd: setColor.bind(this, color),\n    undo: setColor.bind(this, savedColor),\n    post: this._uiManager.updateUI.bind(this._uiManager, this),\n    mustExec: true,\n    type: AnnotationEditorParamsType.HIGHLIGHT_COLOR,\n    overwriteIfSameType: true,\n    keepUndo: true\n  });\n  // this._reportTelemetry(\n  //     {\n  //         action: \"color_changed\",\n  //         color: this._uiManager.highlightColorNames.get(color),\n  //     },\n  //     /* mustWait = */ true\n  // );\n}, _HighlightEditor_updateThickness = function _HighlightEditor_updateThickness(thickness) {\n  if (!thickness) {\n    /* no-empty */\n  }\n  // const savedThickness = this.#thickness;\n  // const setThickness = th => {\n  //     this.#thickness = th;\n  //     this.#changeThickness(th);\n  // };\n  // this.addCommands({\n  //     cmd: setThickness.bind(this, thickness),\n  //     undo: setThickness.bind(this, savedThickness),\n  //     post: this._uiManager.updateUI.bind(this._uiManager, this),\n  //     mustExec: true,\n  //     type: AnnotationEditorParamsType.INK_THICKNESS,\n  //     overwriteIfSameType: true,\n  //     keepUndo: true,\n  // });\n  // this._reportTelemetry(\n  //     { action: \"thickness_changed\", thickness },\n  // /* mustWait = */ true\n  // );\n}, _HighlightEditor_cleanDrawLayer = function _HighlightEditor_cleanDrawLayer() {\n  if (__classPrivateFieldGet(this, _HighlightEditor_id, \"f\") === null || !this.parent) {\n    return;\n  }\n  this.parent.drawLayer.remove(__classPrivateFieldGet(this, _HighlightEditor_id, \"f\"));\n  __classPrivateFieldSet(this, _HighlightEditor_id, null, \"f\");\n  this.parent.drawLayer.remove(__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\"));\n  __classPrivateFieldSet(this, _HighlightEditor_outlineId, null, \"f\");\n}, _HighlightEditor_addToDrawLayer = function _HighlightEditor_addToDrawLayer(parent = this.parent) {\n  var _b, _c;\n  if (__classPrivateFieldGet(this, _HighlightEditor_id, \"f\") !== null) {\n    return;\n  }\n  _b = this, _c = this, {\n    id: {\n      set value(_d) {\n        __classPrivateFieldSet(_b, _HighlightEditor_id, _d, \"f\");\n      }\n    }.value,\n    clipPathId: {\n      set value(_d) {\n        __classPrivateFieldSet(_c, _HighlightEditor_clipPathId, _d, \"f\");\n      }\n    }.value\n  } = parent.drawLayer.highlight(__classPrivateFieldGet(this, _HighlightEditor_highlightOutlines, \"f\"), this.color, __classPrivateFieldGet(this, _HighlightEditor_opacity, \"f\"));\n  __classPrivateFieldSet(this, _HighlightEditor_outlineId, parent.drawLayer.highlightOutline(__classPrivateFieldGet(this, _HighlightEditor_focusOutlines, \"f\")), \"f\");\n  // // todo: manually set styles, so that SVG elements are properly displayed\n  // if (parent.drawLayer.parent) {\n  //     Array.from(parent.drawLayer.parent.querySelectorAll(\".highlight\")).forEach(x => {\n  //         const element = x as any;\n  //         element.style[\"--blend-mode\"] = \"multiply\";\n  //         element.style.position = \"absolute\";\n  //         element.style[\"mix-blend-mode\"] = \"var(--blend-mode)\";\n  //         element.style.transform = \"none\";\n  //     });\n  // }\n  if (__classPrivateFieldGet(this, _HighlightEditor_highlightDiv, \"f\")) {\n    __classPrivateFieldGet(this, _HighlightEditor_highlightDiv, \"f\").style.clipPath = __classPrivateFieldGet(this, _HighlightEditor_clipPathId, \"f\");\n  }\n}, _HighlightEditor_rotateBbox = function _HighlightEditor_rotateBbox({\n  x,\n  y,\n  width,\n  height\n}, angle) {\n  switch (angle) {\n    case 90:\n      return {\n        x: 1 - y - height,\n        y: x,\n        width: height,\n        height: width\n      };\n    case 180:\n      return {\n        x: 1 - x - width,\n        y: 1 - y - height,\n        width,\n        height\n      };\n    case 270:\n      return {\n        x: y,\n        y: 1 - x - width,\n        width: height,\n        height: width\n      };\n    default:\n      break;\n  }\n  return {\n    x,\n    y,\n    width,\n    height\n  };\n}, _HighlightEditor_keydown = function _HighlightEditor_keydown(event) {\n  if (!event) {\n    /* no-empty */\n  }\n  // HighlightEditor._keyboardManager.exec(this, event);\n}, _HighlightEditor_setCaret = function _HighlightEditor_setCaret(start) {\n  if (!__classPrivateFieldGet(this, _HighlightEditor_anchorNode, \"f\")) {\n    return;\n  }\n  const selection = window.getSelection();\n  if (start) {\n    selection.setPosition(__classPrivateFieldGet(this, _HighlightEditor_anchorNode, \"f\"), __classPrivateFieldGet(this, _HighlightEditor_anchorOffset, \"f\"));\n  } else {\n    selection.setPosition(__classPrivateFieldGet(this, _HighlightEditor_focusNode, \"f\"), __classPrivateFieldGet(this, _HighlightEditor_focusOffset, \"f\"));\n  }\n}, _HighlightEditor_getRotation = function _HighlightEditor_getRotation() {\n  // Highlight annotations are always drawn horizontally but if\n  // a free highlight annotation can be rotated.\n  return __classPrivateFieldGet(this, _HighlightEditor_isFreeHighlight, \"f\") ? this.rotation : 0;\n}, _HighlightEditor_serializeBoxes = function _HighlightEditor_serializeBoxes() {\n  if (__classPrivateFieldGet(this, _HighlightEditor_isFreeHighlight, \"f\")) {\n    return null;\n  }\n  const [pageWidth, pageHeight] = this.pageDimensions;\n  const [pageX, pageY] = this.pageTranslation;\n  const boxes = __classPrivateFieldGet(this, _HighlightEditor_boxes, \"f\");\n  const quadPoints = new Float32Array(boxes.length * 8);\n  let i = 0;\n  for (const {\n    x,\n    y,\n    width,\n    height\n  } of boxes) {\n    const sx = x * pageWidth + pageX;\n    const sy = (1 - y - height) * pageHeight + pageY;\n    // The specifications say that the rectangle should start from the bottom\n    // left corner and go counter-clockwise.\n    // But when opening the file in Adobe Acrobat it appears that this isn't\n    // correct hence the 4th and 6th numbers are just swapped.\n    quadPoints[i] = quadPoints[i + 4] = sx;\n    quadPoints[i + 1] = quadPoints[i + 3] = sy;\n    quadPoints[i + 2] = quadPoints[i + 6] = sx + width * pageWidth;\n    quadPoints[i + 5] = quadPoints[i + 7] = sy + height * pageHeight;\n    i += 8;\n  }\n  return quadPoints;\n}, _HighlightEditor_serializeOutlines = function _HighlightEditor_serializeOutlines(rect) {\n  return __classPrivateFieldGet(this, _HighlightEditor_highlightOutlines, \"f\").serialize(rect, __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_getRotation).call(this));\n}, _HighlightEditor_highlightMove = function _HighlightEditor_highlightMove(parent, event) {\n  if (this._freeHighlight.add(event)) {\n    // Redraw only if the point has been added.\n    parent.drawLayer.updatePath(this._freeHighlightId, this._freeHighlight);\n  }\n}, _HighlightEditor_endHighlight = function _HighlightEditor_endHighlight(parent, event) {\n  if (!this._freeHighlight.isEmpty()) {\n    parent.createAndAddNewEditor(event, false, {\n      highlightId: this._freeHighlightId,\n      highlightOutlines: this._freeHighlight.getOutlines(),\n      clipPathId: this._freeHighlightClipId,\n      methodOfCreation: \"main_toolbar\"\n    });\n  } else {\n    parent.drawLayer.removeFreeHighlight(this._freeHighlightId);\n  }\n  this._freeHighlightId = -1;\n  this._freeHighlight = null;\n  this._freeHighlightClipId = \"\";\n};\nHighlightEditor._defaultColor = null;\nHighlightEditor._defaultOpacity = 1;\nHighlightEditor._defaultThickness = 12;\nHighlightEditor._type = \"highlight\";\nHighlightEditor._editorType = AnnotationEditorType.HIGHLIGHT;\nHighlightEditor._freeHighlightId = -1;\nHighlightEditor._freeHighlight = null;\nHighlightEditor._freeHighlightClipId = \"\";\nexport { HighlightEditor };", "map": {"version": 3, "names": ["_HighlightEditor_instances", "_a", "_HighlightEditor_anchorNode", "_HighlightEditor_anchorOffset", "_HighlightEditor_boxes", "_HighlightEditor_clipPathId", "_HighlightEditor_focusOutlines", "_HighlightEditor_focusNode", "_HighlightEditor_focusOffset", "_HighlightEditor_highlightDiv", "_HighlightEditor_highlightOutlines", "_HighlightEditor_id", "_HighlightEditor_isFreeHighlight", "_HighlightEditor_lastPoint", "_HighlightEditor_opacity", "_HighlightEditor_outlineId", "_HighlightEditor_text", "_HighlightEditor_thickness", "_HighlightEditor_methodOfCreation", "_HighlightEditor_createOutlines", "_HighlightEditor_createFreeOutlines", "_HighlightEditor_updateColor", "_HighlightEditor_updateThickness", "_HighlightEditor_cleanDrawLayer", "_HighlightEditor_addToDrawLayer", "_HighlightEditor_rotateBbox", "_HighlightEditor_keydown", "_HighlightEditor_setCaret", "_HighlightEditor_getRotation", "_HighlightEditor_serializeBoxes", "_HighlightEditor_serializeOutlines", "_HighlightEditor_highlightMove", "_HighlightEditor_endHighlight", "__classPrivateFieldGet", "__classPrivateFieldSet", "AnnotationEditorParamsType", "AnnotationEditorType", "<PERSON><PERSON>", "noContextMenu", "bindEvents", "AnnotationEditor", "FreeOutliner", "Outliner", "HighlightEditor", "constructor", "params", "Object", "assign", "name", "add", "color", "set", "_defaultColor", "thickness", "_defaultThickness", "opacity", "_defaultOpacity", "boxes", "methodOfCreation", "text", "_isDraggable", "highlightId", "call", "anchorNode", "anchorOffset", "focusNode", "focusOffset", "rotate", "rotation", "telemetryInitialData", "action", "type", "_uiManager", "highlightColorNames", "get", "telemetryFinalData", "computeTelemetryFinalData", "data", "numberOfColors", "size", "initialize", "l10n", "uiManager", "_b", "_c", "_d", "_e", "viewer", "options", "annotations", "highlight", "updateDefaultParams", "value", "HIGHLIGHT_DEFAULT_COLOR", "HIGHLIGHT_THICKNESS", "translateInPage", "toolbarPosition", "updateParams", "HIGHLIGHT_COLOR", "defaultPropertiesToUpdate", "propertiesToUpdate", "HIGHLIGHT_FREE", "disableEditing", "div", "classList", "toggle", "enableEditing", "fixAndSetPosition", "getBaseTranslation", "getRect", "tx", "ty", "onceAdded", "parent", "addUndoableEditor", "focus", "remove", "rebuild", "isAttachedToDOM", "setParent", "mustBeSelected", "contains", "show", "_isVisible", "select", "angle", "draw<PERSON>ayer", "box", "updateBox", "render", "setAttribute", "addEventListener", "bind", "signal", "_signal", "highlightDiv", "document", "createElement", "append", "className", "style", "clipPath", "parentWidth", "parentHeight", "parentDimensions", "setDims", "width", "height", "pointerover", "addClass", "pointerleave", "removeClass", "_moveCaret", "direction", "unselect", "_mustFixPosition", "visible", "startHighlighting", "isLTR", "target", "textLayer", "x", "y", "layerX", "layerY", "getBoundingClientRect", "ac", "AbortController", "combinedSignal", "pointerDown", "e", "preventDefault", "stopPropagation", "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>", "abort", "window", "capture", "passive", "_freeHighlight", "scale", "id", "_freeHighlightId", "clipPathId", "_freeHighlightClipId", "deserialize", "editor", "rect", "blX", "blY", "trX", "trY", "quadPoints", "makeHexColor", "pageWidth", "pageHeight", "pageDimensions", "i", "length", "push", "serialize", "isForCopying", "isEmpty", "_colorManager", "convert", "annotationType", "HIGHLIGHT", "outlines", "pageIndex", "structTreeParentId", "_structTreeParentId", "canCreateNewEmptyEditor", "toJSON", "WeakMap", "WeakSet", "outliner", "getOutlines", "outlinerForOutline", "lastPoint", "highlightOutlines", "extraThickness", "getNewOutline", "finalizeLine", "highlightOutline", "viewport", "updateLine", "setColor", "col", "changeColor", "savedColor", "addCommands", "cmd", "undo", "post", "updateUI", "mustExec", "overwriteIfSameType", "keepUndo", "event", "start", "selection", "getSelection", "setPosition", "pageX", "pageY", "pageTranslation", "Float32Array", "sx", "sy", "updatePath", "createAndAddNewEditor", "removeFreeHighlight", "_type", "_editorType"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-pdfviewer-common/dist/es/annotations/editors/highlight-editor.js"], "sourcesContent": ["/* Copyright 2022 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar _HighlightEditor_instances, _a, _HighlightEditor_anchorNode, _HighlightEditor_anchorOffset, _HighlightEditor_boxes, _HighlightEditor_clipPathId, _HighlightEditor_focusOutlines, _HighlightEditor_focusNode, _HighlightEditor_focusOffset, _HighlightEditor_highlightDiv, _HighlightEditor_highlightOutlines, _HighlightEditor_id, _HighlightEditor_isFreeHighlight, _HighlightEditor_lastPoint, _HighlightEditor_opacity, _HighlightEditor_outlineId, _HighlightEditor_text, _HighlightEditor_thickness, _HighlightEditor_methodOfCreation, _HighlightEditor_createOutlines, _HighlightEditor_createFreeOutlines, _HighlightEditor_updateColor, _HighlightEditor_updateThickness, _HighlightEditor_cleanDrawLayer, _HighlightEditor_addToDrawLayer, _HighlightEditor_rotateBbox, _HighlightEditor_keydown, _HighlightEditor_setCaret, _HighlightEditor_getRotation, _HighlightEditor_serializeBoxes, _HighlightEditor_serializeOutlines, _HighlightEditor_highlightMove, _HighlightEditor_endHighlight;\nimport { __classPrivateFieldGet, __classPrivateFieldSet } from \"tslib\";\nimport { AnnotationEditorParamsType, AnnotationEditorType, \n// DrawLayer,\n// shadow,\nUtil, noContextMenu } from \"pdfjs-dist/legacy/build/pdf.mjs\";\nimport { bindEvents } from \"../helpers/tools\";\n// import { bindEvents, KeyboardManager } from \"./tools.js\";\n// import { FreeOutliner, Outliner } from \"./outliner.js\";\nimport { AnnotationEditor } from \"./annotation-editor\";\nimport { FreeOutliner, Outliner } from \"./outliner\";\n// import { ColorPicker } from \"./color_picker.js\";\n// import { noContextMenu } from \"../../shared/display_utils\";\n/**\n * Basic draw editor in order to generate an Highlight annotation.\n */\nclass HighlightEditor extends AnnotationEditor {\n    // static get _keyboardManager() {\n    //     const proto = HighlightEditor.prototype;\n    //     return shadow(\n    //         this,\n    //         \"_keyboardManager\",\n    //         new KeyboardManager([\n    //             [[\"ArrowLeft\", \"mac+ArrowLeft\"], proto._moveCaret, { args: [0] }],\n    //             [[\"ArrowRight\", \"mac+ArrowRight\"], proto._moveCaret, { args: [1] }],\n    //             [[\"ArrowUp\", \"mac+ArrowUp\"], proto._moveCaret, { args: [2] }],\n    //             [[\"ArrowDown\", \"mac+ArrowDown\"], proto._moveCaret, { args: [3] }],\n    //         ])\n    //     );\n    // }\n    constructor(params) {\n        super(Object.assign(Object.assign({}, params), { name: \"k-highlight-editor\" }));\n        _HighlightEditor_instances.add(this);\n        // todo: props\n        this.color = \"\";\n        // parent = null;\n        // width = null;\n        // height = null;\n        // x = null;\n        // y = null;\n        // todo: props\n        _HighlightEditor_anchorNode.set(this, null);\n        _HighlightEditor_anchorOffset.set(this, 0);\n        _HighlightEditor_boxes.set(this, void 0);\n        _HighlightEditor_clipPathId.set(this, null);\n        // #colorPicker = null;\n        _HighlightEditor_focusOutlines.set(this, null);\n        _HighlightEditor_focusNode.set(this, null);\n        _HighlightEditor_focusOffset.set(this, 0);\n        _HighlightEditor_highlightDiv.set(this, null);\n        _HighlightEditor_highlightOutlines.set(this, null);\n        _HighlightEditor_id.set(this, null);\n        _HighlightEditor_isFreeHighlight.set(this, false);\n        _HighlightEditor_lastPoint.set(this, null);\n        _HighlightEditor_opacity.set(this, void 0);\n        _HighlightEditor_outlineId.set(this, null);\n        _HighlightEditor_text.set(this, \"\");\n        _HighlightEditor_thickness.set(this, void 0);\n        _HighlightEditor_methodOfCreation.set(this, \"\");\n        this.color = params.color || _a._defaultColor;\n        __classPrivateFieldSet(this, _HighlightEditor_thickness, params.thickness || _a._defaultThickness, \"f\");\n        __classPrivateFieldSet(this, _HighlightEditor_opacity, params.opacity || _a._defaultOpacity, \"f\");\n        __classPrivateFieldSet(this, _HighlightEditor_boxes, params.boxes || null, \"f\");\n        __classPrivateFieldSet(this, _HighlightEditor_methodOfCreation, params.methodOfCreation || \"\", \"f\");\n        __classPrivateFieldSet(this, _HighlightEditor_text, params.text || \"\", \"f\");\n        this._isDraggable = false;\n        if (params.highlightId > -1) {\n            __classPrivateFieldSet(this, _HighlightEditor_isFreeHighlight, true, \"f\");\n            __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_createFreeOutlines).call(this, params);\n            __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_addToDrawLayer).call(this);\n        }\n        else {\n            __classPrivateFieldSet(this, _HighlightEditor_anchorNode, params.anchorNode, \"f\");\n            __classPrivateFieldSet(this, _HighlightEditor_anchorOffset, params.anchorOffset, \"f\");\n            __classPrivateFieldSet(this, _HighlightEditor_focusNode, params.focusNode, \"f\");\n            __classPrivateFieldSet(this, _HighlightEditor_focusOffset, params.focusOffset, \"f\");\n            __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_createOutlines).call(this);\n            __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_addToDrawLayer).call(this);\n            this.rotate(this.rotation);\n        }\n    }\n    /** @inheritdoc */\n    get telemetryInitialData() {\n        return {\n            action: \"added\",\n            type: __classPrivateFieldGet(this, _HighlightEditor_isFreeHighlight, \"f\") ? \"free_highlight\" : \"highlight\",\n            color: this._uiManager.highlightColorNames.get(this.color),\n            thickness: __classPrivateFieldGet(this, _HighlightEditor_thickness, \"f\"),\n            methodOfCreation: __classPrivateFieldGet(this, _HighlightEditor_methodOfCreation, \"f\")\n        };\n    }\n    /** @inheritdoc */\n    get telemetryFinalData() {\n        return {\n            type: \"highlight\",\n            color: this._uiManager.highlightColorNames.get(this.color)\n        };\n    }\n    static computeTelemetryFinalData(data) {\n        // We want to know how many colors have been used.\n        return { numberOfColors: data.get(\"color\").size };\n    }\n    /** @inheritdoc */\n    static initialize(l10n, uiManager) {\n        var _b, _c, _d, _e;\n        AnnotationEditor.initialize(l10n, uiManager, {});\n        _a._defaultColor || (_a._defaultColor = \n        // uiManager.highlightColors?.values().next().value || \"#fff066\";\n        // uiManager.highlightColors?.values().next().value || \"#ffff00\";\n        ((_e = (_d = (_c = (_b = uiManager.viewer) === null || _b === void 0 ? void 0 : _b.options) === null || _c === void 0 ? void 0 : _c.annotations) === null || _d === void 0 ? void 0 : _d.highlight) === null || _e === void 0 ? void 0 : _e.color) || \"#ffff00\");\n    }\n    /** @inheritdoc */\n    static updateDefaultParams(type, value) {\n        switch (type) {\n            case AnnotationEditorParamsType.HIGHLIGHT_DEFAULT_COLOR:\n                _a._defaultColor = value;\n                break;\n            case AnnotationEditorParamsType.HIGHLIGHT_THICKNESS:\n                _a._defaultThickness = value;\n                break;\n            default: break;\n        }\n    }\n    /** @inheritdoc */\n    // translateInPage(x, y) { }\n    translateInPage() { }\n    /** @inheritdoc */\n    get toolbarPosition() {\n        return __classPrivateFieldGet(this, _HighlightEditor_lastPoint, \"f\");\n    }\n    /** @inheritdoc */\n    updateParams(type, value) {\n        switch (type) {\n            case AnnotationEditorParamsType.HIGHLIGHT_COLOR:\n                __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_updateColor).call(this, value);\n                break;\n            case AnnotationEditorParamsType.HIGHLIGHT_THICKNESS:\n                __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_updateThickness).call(this, value);\n                break;\n            default: break;\n        }\n    }\n    static get defaultPropertiesToUpdate() {\n        return [\n            [\n                AnnotationEditorParamsType.HIGHLIGHT_DEFAULT_COLOR,\n                _a._defaultColor\n            ],\n            [\n                AnnotationEditorParamsType.HIGHLIGHT_THICKNESS,\n                _a._defaultThickness\n            ]\n        ];\n    }\n    /** @inheritdoc */\n    get propertiesToUpdate() {\n        return [\n            [\n                AnnotationEditorParamsType.HIGHLIGHT_COLOR,\n                this.color || _a._defaultColor\n            ],\n            [\n                AnnotationEditorParamsType.HIGHLIGHT_THICKNESS,\n                __classPrivateFieldGet(this, _HighlightEditor_thickness, \"f\") || _a._defaultThickness\n            ],\n            [AnnotationEditorParamsType.HIGHLIGHT_FREE, __classPrivateFieldGet(this, _HighlightEditor_isFreeHighlight, \"f\")]\n        ];\n    }\n    /** @inheritdoc */\n    // async addEditToolbar() {\n    //     // const toolbar = await super.addEditToolbar();\n    //     // if (!toolbar) {\n    //     //     return null;\n    //     // }\n    //     // if (this._uiManager.highlightColors) {\n    //     //     // this.#colorPicker = new ColorPicker({ editor: this });\n    //     //     // toolbar.addColorPicker(this.#colorPicker);\n    //     // }\n    //     // return toolbar;\n    // }\n    /** @inheritdoc */\n    disableEditing() {\n        super.disableEditing();\n        // this.div.classList.toggle(\"disabled\", true);\n        this.div.classList.toggle(\"k-highlight-editor-disabled\", true);\n    }\n    /** @inheritdoc */\n    enableEditing() {\n        super.enableEditing();\n        // this.div.classList.toggle(\"disabled\", false);\n        this.div.classList.toggle(\"k-highlight-editor-disabled\", false);\n    }\n    /** @inheritdoc */\n    fixAndSetPosition() {\n        return super.fixAndSetPosition(__classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_getRotation).call(this));\n    }\n    /** @inheritdoc */\n    getBaseTranslation() {\n        // The editor itself doesn't have any CSS border (we're drawing one\n        // ourselves in using SVG).\n        return [0, 0];\n    }\n    /** @inheritdoc */\n    getRect(tx, ty) {\n        return super.getRect(tx, ty, __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_getRotation).call(this));\n    }\n    /** @inheritdoc */\n    onceAdded() {\n        this.parent.addUndoableEditor(this);\n        this.div.focus();\n    }\n    /** @inheritdoc */\n    remove() {\n        __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_cleanDrawLayer).call(this);\n        // this._reportTelemetry({\n        //     action: \"deleted\",\n        // });\n        super.remove();\n    }\n    /** @inheritdoc */\n    rebuild() {\n        if (!this.parent) {\n            return;\n        }\n        super.rebuild();\n        if (this.div === null) {\n            return;\n        }\n        __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_addToDrawLayer).call(this);\n        if (!this.isAttachedToDOM) {\n            // At some point this editor was removed and we're rebuilding it,\n            // hence we must add it to its parent.\n            this.parent.add(this);\n        }\n    }\n    setParent(parent) {\n        var _b, _c;\n        let mustBeSelected = false;\n        if (this.parent && !parent) {\n            __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_cleanDrawLayer).call(this);\n        }\n        else if (parent) {\n            __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_addToDrawLayer).call(this, parent);\n            // If mustBeSelected is true it means that this editor was selected\n            // when its parent has been destroyed, hence we must select it again.\n            mustBeSelected =\n                !this.parent &&\n                    (((_b = this.div) === null || _b === void 0 ? void 0 : _b.classList.contains(\"selectedEditor\")) ||\n                        ((_c = this.div) === null || _c === void 0 ? void 0 : _c.classList.contains(\"k-selected\")));\n        }\n        super.setParent(parent);\n        this.show(this._isVisible);\n        if (mustBeSelected) {\n            // We select it after the parent has been set.\n            this.select();\n        }\n    }\n    /** @inheritdoc */\n    rotate(angle) {\n        // We need to rotate the svgs because of the coordinates system.\n        const { drawLayer } = this.parent;\n        let box;\n        if (__classPrivateFieldGet(this, _HighlightEditor_isFreeHighlight, \"f\")) {\n            angle = (angle - this.rotation + 360) % 360;\n            box = __classPrivateFieldGet(_a, _a, \"m\", _HighlightEditor_rotateBbox).call(_a, __classPrivateFieldGet(this, _HighlightEditor_highlightOutlines, \"f\").box, angle);\n        }\n        else {\n            // An highlight annotation is always drawn horizontally.\n            box = __classPrivateFieldGet(_a, _a, \"m\", _HighlightEditor_rotateBbox).call(_a, this, angle);\n        }\n        drawLayer.rotate(__classPrivateFieldGet(this, _HighlightEditor_id, \"f\"), angle);\n        drawLayer.rotate(__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\"), angle);\n        drawLayer.updateBox(__classPrivateFieldGet(this, _HighlightEditor_id, \"f\"), box);\n        drawLayer.updateBox(__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\"), __classPrivateFieldGet(_a, _a, \"m\", _HighlightEditor_rotateBbox).call(_a, __classPrivateFieldGet(this, _HighlightEditor_focusOutlines, \"f\").box, angle));\n    }\n    /** @inheritdoc */\n    render() {\n        if (this.div) {\n            return this.div;\n        }\n        const div = super.render();\n        if (__classPrivateFieldGet(this, _HighlightEditor_text, \"f\")) {\n            div.setAttribute(\"aria-label\", __classPrivateFieldGet(this, _HighlightEditor_text, \"f\"));\n            div.setAttribute(\"role\", \"mark\");\n        }\n        if (__classPrivateFieldGet(this, _HighlightEditor_isFreeHighlight, \"f\")) {\n            div.classList.add(\"free\");\n        }\n        else {\n            this.div.addEventListener(\"keydown\", __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_keydown).bind(this), {\n                signal: this._uiManager._signal\n            });\n        }\n        const highlightDiv = (__classPrivateFieldSet(this, _HighlightEditor_highlightDiv, document.createElement(\"div\"), \"f\"));\n        div.append(highlightDiv);\n        highlightDiv.setAttribute(\"aria-hidden\", \"true\");\n        highlightDiv.className = \"k-internal internal\";\n        highlightDiv.style.clipPath = __classPrivateFieldGet(this, _HighlightEditor_clipPathId, \"f\");\n        const [parentWidth, parentHeight] = this.parentDimensions;\n        this.setDims(this.width * parentWidth, this.height * parentHeight);\n        bindEvents(this, __classPrivateFieldGet(this, _HighlightEditor_highlightDiv, \"f\"), [\"pointerover\", \"pointerleave\"]);\n        this.enableEditing();\n        return div;\n    }\n    pointerover() {\n        this.parent.drawLayer.addClass(__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\"), \"hovered\");\n    }\n    pointerleave() {\n        this.parent.drawLayer.removeClass(__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\"), \"hovered\");\n    }\n    _moveCaret(direction) {\n        this.parent.unselect(this);\n        switch (direction) {\n            case 0 /* left */:\n            case 2 /* up */:\n                __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_setCaret).call(this, /* start = */ true);\n                break;\n            case 1 /* right */:\n            case 3 /* down */:\n                __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_setCaret).call(this, /* start = */ false);\n                break;\n            default: break;\n        }\n    }\n    /** @inheritdoc */\n    select() {\n        var _b, _c, _d;\n        super.select();\n        if (!__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\")) {\n            return;\n        }\n        (_b = this.parent) === null || _b === void 0 ? void 0 : _b.drawLayer.removeClass(__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\"), \"hovered\");\n        (_c = this.parent) === null || _c === void 0 ? void 0 : _c.drawLayer.addClass(__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\"), \"selected\");\n        (_d = this.parent) === null || _d === void 0 ? void 0 : _d.drawLayer.addClass(__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\"), \"k-selected\");\n    }\n    /** @inheritdoc */\n    unselect() {\n        var _b, _c;\n        super.unselect();\n        if (!__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\")) {\n            return;\n        }\n        (_b = this.parent) === null || _b === void 0 ? void 0 : _b.drawLayer.removeClass(__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\"), \"selected\");\n        (_c = this.parent) === null || _c === void 0 ? void 0 : _c.drawLayer.removeClass(__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\"), \"k-selected\");\n        if (!__classPrivateFieldGet(this, _HighlightEditor_isFreeHighlight, \"f\")) {\n            __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_setCaret).call(this, /* start = */ false);\n        }\n    }\n    /** @inheritdoc */\n    get _mustFixPosition() {\n        return !__classPrivateFieldGet(this, _HighlightEditor_isFreeHighlight, \"f\");\n    }\n    /** @inheritdoc */\n    show(visible = this._isVisible) {\n        super.show(visible);\n        if (this.parent) {\n            this.parent.drawLayer.show(__classPrivateFieldGet(this, _HighlightEditor_id, \"f\"), visible);\n            this.parent.drawLayer.show(__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\"), visible);\n        }\n    }\n    static startHighlighting(parent, isLTR, { target: textLayer, x, y }) {\n        const { x: layerX, y: layerY, width: parentWidth, height: parentHeight } = textLayer.getBoundingClientRect();\n        const ac = new AbortController();\n        const signal = parent.combinedSignal(ac);\n        const pointerDown = e => {\n            // Avoid to have undesired clicks during the drawing.\n            e.preventDefault();\n            e.stopPropagation();\n        };\n        const pointerUpCallback = e => {\n            ac.abort();\n            __classPrivateFieldGet(this, _a, \"m\", _HighlightEditor_endHighlight).call(this, parent, e);\n        };\n        window.addEventListener(\"blur\", pointerUpCallback, { signal });\n        window.addEventListener(\"pointerup\", pointerUpCallback, { signal });\n        window.addEventListener(\"pointerdown\", pointerDown, {\n            capture: true,\n            passive: false,\n            signal\n        });\n        window.addEventListener(\"contextmenu\", noContextMenu, { signal });\n        textLayer.addEventListener(\"pointermove\", __classPrivateFieldGet(this, _a, \"m\", _HighlightEditor_highlightMove).bind(this, parent), { signal });\n        this._freeHighlight = new FreeOutliner({ x, y }, [layerX, layerY, parentWidth, parentHeight], parent.scale, this._defaultThickness / 2, isLTR, \n        /* innerMargin = */ 0.001);\n        ({ id: this._freeHighlightId, clipPathId: this._freeHighlightClipId } =\n            parent.drawLayer.highlight(this._freeHighlight, this._defaultColor, this._defaultOpacity, \n            /* isPathUpdatable = */ true));\n    }\n    /** @inheritdoc */\n    static deserialize(data, parent, uiManager) {\n        const editor = super.deserialize(data, parent, uiManager);\n        const { rect: [blX, blY, trX, trY], color, quadPoints } = data;\n        // @ts-expect-error TS()\n        editor.color = Util.makeHexColor(...color);\n        __classPrivateFieldSet(editor, _HighlightEditor_opacity, data.opacity, \"f\");\n        const [pageWidth, pageHeight] = editor.pageDimensions;\n        editor.width = (trX - blX) / pageWidth;\n        editor.height = (trY - blY) / pageHeight;\n        const boxes = (__classPrivateFieldSet(editor, _HighlightEditor_boxes, [], \"f\"));\n        for (let i = 0; i < quadPoints.length; i += 8) {\n            boxes.push({\n                x: (quadPoints[4] - trX) / pageWidth,\n                y: (trY - (1 - quadPoints[i + 5])) / pageHeight,\n                width: (quadPoints[i + 2] - quadPoints[i]) / pageWidth,\n                height: (quadPoints[i + 5] - quadPoints[i + 1]) / pageHeight\n            });\n        }\n        __classPrivateFieldGet(editor, _HighlightEditor_instances, \"m\", _HighlightEditor_createOutlines).call(editor);\n        return editor;\n    }\n    /** @inheritdoc */\n    serialize(isForCopying = false) {\n        // It doesn't make sense to copy/paste a highlight annotation.\n        if (this.isEmpty() || isForCopying) {\n            return null;\n        }\n        const rect = this.getRect(0, 0);\n        const color = AnnotationEditor._colorManager.convert(this.color);\n        return {\n            annotationType: AnnotationEditorType.HIGHLIGHT,\n            color,\n            opacity: __classPrivateFieldGet(this, _HighlightEditor_opacity, \"f\"),\n            thickness: __classPrivateFieldGet(this, _HighlightEditor_thickness, \"f\"),\n            quadPoints: __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_serializeBoxes).call(this),\n            outlines: __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_serializeOutlines).call(this, rect),\n            pageIndex: this.pageIndex,\n            rect,\n            rotation: __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_getRotation).call(this),\n            structTreeParentId: this._structTreeParentId\n        };\n    }\n    static canCreateNewEmptyEditor() {\n        return false;\n    }\n    // todo: this is necessary\n    // saveDocument() has checks that test \"editor instanceof AnnotationEditor\", but they fail\n    // because AnnotationEditor from \"pdfjs-dist/legacy/build/pdf.mjs\" is not exported\n    // thus replace instances of editors with their serialized version\n    toJSON() {\n        const data = this.serialize();\n        return data;\n    }\n}\n_a = HighlightEditor, _HighlightEditor_anchorNode = new WeakMap(), _HighlightEditor_anchorOffset = new WeakMap(), _HighlightEditor_boxes = new WeakMap(), _HighlightEditor_clipPathId = new WeakMap(), _HighlightEditor_focusOutlines = new WeakMap(), _HighlightEditor_focusNode = new WeakMap(), _HighlightEditor_focusOffset = new WeakMap(), _HighlightEditor_highlightDiv = new WeakMap(), _HighlightEditor_highlightOutlines = new WeakMap(), _HighlightEditor_id = new WeakMap(), _HighlightEditor_isFreeHighlight = new WeakMap(), _HighlightEditor_lastPoint = new WeakMap(), _HighlightEditor_opacity = new WeakMap(), _HighlightEditor_outlineId = new WeakMap(), _HighlightEditor_text = new WeakMap(), _HighlightEditor_thickness = new WeakMap(), _HighlightEditor_methodOfCreation = new WeakMap(), _HighlightEditor_instances = new WeakSet(), _HighlightEditor_createOutlines = function _HighlightEditor_createOutlines() {\n    const outliner = new Outliner(__classPrivateFieldGet(this, _HighlightEditor_boxes, \"f\"), /* borderWidth = */ 0.001);\n    __classPrivateFieldSet(this, _HighlightEditor_highlightOutlines, outliner.getOutlines(), \"f\");\n    ({\n        x: this.x,\n        y: this.y,\n        width: this.width,\n        height: this.height\n    } = __classPrivateFieldGet(this, _HighlightEditor_highlightOutlines, \"f\").box);\n    const outlinerForOutline = new Outliner(__classPrivateFieldGet(this, _HighlightEditor_boxes, \"f\"), \n    /* borderWidth = */ 0.0025, \n    /* innerMargin = */ 0.001, this._uiManager.direction === \"ltr\");\n    __classPrivateFieldSet(this, _HighlightEditor_focusOutlines, outlinerForOutline.getOutlines(), \"f\");\n    // The last point is in the pages coordinate system.\n    const { lastPoint } = __classPrivateFieldGet(this, _HighlightEditor_focusOutlines, \"f\").box;\n    __classPrivateFieldSet(this, _HighlightEditor_lastPoint, [\n        (lastPoint[0] - this.x) / this.width,\n        (lastPoint[1] - this.y) / this.height\n    ], \"f\");\n}, _HighlightEditor_createFreeOutlines = function _HighlightEditor_createFreeOutlines({ highlightOutlines, highlightId, clipPathId }) {\n    __classPrivateFieldSet(this, _HighlightEditor_highlightOutlines, highlightOutlines, \"f\");\n    const extraThickness = 1.5;\n    __classPrivateFieldSet(this, _HighlightEditor_focusOutlines, highlightOutlines.getNewOutline(\n    /* Slightly bigger than the highlight in order to have a little\n       space between the highlight and the outline. */\n    __classPrivateFieldGet(this, _HighlightEditor_thickness, \"f\") / 2 + extraThickness, \n    /* innerMargin = */ 0.0025), \"f\");\n    if (highlightId >= 0) {\n        __classPrivateFieldSet(this, _HighlightEditor_id, highlightId, \"f\");\n        __classPrivateFieldSet(this, _HighlightEditor_clipPathId, clipPathId, \"f\");\n        // We need to redraw the highlight because we change the coordinates to be\n        // in the box coordinate system.\n        this.parent.drawLayer.finalizeLine(highlightId, highlightOutlines);\n        __classPrivateFieldSet(this, _HighlightEditor_outlineId, this.parent.drawLayer.highlightOutline(__classPrivateFieldGet(this, _HighlightEditor_focusOutlines, \"f\")), \"f\");\n    }\n    else if (this.parent) {\n        const angle = this.parent.viewport.rotation;\n        this.parent.drawLayer.updateLine(__classPrivateFieldGet(this, _HighlightEditor_id, \"f\"), highlightOutlines);\n        this.parent.drawLayer.updateBox(__classPrivateFieldGet(this, _HighlightEditor_id, \"f\"), __classPrivateFieldGet(_a, _a, \"m\", _HighlightEditor_rotateBbox).call(_a, __classPrivateFieldGet(this, _HighlightEditor_highlightOutlines, \"f\").box, (angle - this.rotation + 360) % 360));\n        this.parent.drawLayer.updateLine(__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\"), __classPrivateFieldGet(this, _HighlightEditor_focusOutlines, \"f\"));\n        this.parent.drawLayer.updateBox(__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\"), __classPrivateFieldGet(_a, _a, \"m\", _HighlightEditor_rotateBbox).call(_a, __classPrivateFieldGet(this, _HighlightEditor_focusOutlines, \"f\").box, angle));\n    }\n    const { x, y, width, height } = highlightOutlines.box;\n    switch (this.rotation) {\n        case 0:\n            this.x = x;\n            this.y = y;\n            this.width = width;\n            this.height = height;\n            break;\n        case 90: {\n            const [pageWidth, pageHeight] = this.parentDimensions;\n            this.x = y;\n            this.y = 1 - x;\n            this.width = (width * pageHeight) / pageWidth;\n            this.height = (height * pageWidth) / pageHeight;\n            break;\n        }\n        case 180:\n            this.x = 1 - x;\n            this.y = 1 - y;\n            this.width = width;\n            this.height = height;\n            break;\n        case 270: {\n            const [pageWidth, pageHeight] = this.parentDimensions;\n            this.x = 1 - y;\n            this.y = x;\n            this.width = (width * pageHeight) / pageWidth;\n            this.height = (height * pageWidth) / pageHeight;\n            break;\n        }\n        default: break;\n    }\n    const { lastPoint } = __classPrivateFieldGet(this, _HighlightEditor_focusOutlines, \"f\").box;\n    __classPrivateFieldSet(this, _HighlightEditor_lastPoint, [(lastPoint[0] - x) / width, (lastPoint[1] - y) / height], \"f\");\n}, _HighlightEditor_updateColor = function _HighlightEditor_updateColor(color) {\n    if (!color) {\n        return;\n    }\n    const setColor = col => {\n        var _b;\n        this.color = col;\n        (_b = this.parent) === null || _b === void 0 ? void 0 : _b.drawLayer.changeColor(__classPrivateFieldGet(this, _HighlightEditor_id, \"f\"), col);\n        // this.#colorPicker?.updateColor(col);\n    };\n    const savedColor = this.color;\n    this.addCommands({\n        cmd: setColor.bind(this, color),\n        undo: setColor.bind(this, savedColor),\n        post: this._uiManager.updateUI.bind(this._uiManager, this),\n        mustExec: true,\n        type: AnnotationEditorParamsType.HIGHLIGHT_COLOR,\n        overwriteIfSameType: true,\n        keepUndo: true\n    });\n    // this._reportTelemetry(\n    //     {\n    //         action: \"color_changed\",\n    //         color: this._uiManager.highlightColorNames.get(color),\n    //     },\n    //     /* mustWait = */ true\n    // );\n}, _HighlightEditor_updateThickness = function _HighlightEditor_updateThickness(thickness) {\n    if (!thickness) {\n        /* no-empty */\n    }\n    // const savedThickness = this.#thickness;\n    // const setThickness = th => {\n    //     this.#thickness = th;\n    //     this.#changeThickness(th);\n    // };\n    // this.addCommands({\n    //     cmd: setThickness.bind(this, thickness),\n    //     undo: setThickness.bind(this, savedThickness),\n    //     post: this._uiManager.updateUI.bind(this._uiManager, this),\n    //     mustExec: true,\n    //     type: AnnotationEditorParamsType.INK_THICKNESS,\n    //     overwriteIfSameType: true,\n    //     keepUndo: true,\n    // });\n    // this._reportTelemetry(\n    //     { action: \"thickness_changed\", thickness },\n    // /* mustWait = */ true\n    // );\n}, _HighlightEditor_cleanDrawLayer = function _HighlightEditor_cleanDrawLayer() {\n    if (__classPrivateFieldGet(this, _HighlightEditor_id, \"f\") === null || !this.parent) {\n        return;\n    }\n    this.parent.drawLayer.remove(__classPrivateFieldGet(this, _HighlightEditor_id, \"f\"));\n    __classPrivateFieldSet(this, _HighlightEditor_id, null, \"f\");\n    this.parent.drawLayer.remove(__classPrivateFieldGet(this, _HighlightEditor_outlineId, \"f\"));\n    __classPrivateFieldSet(this, _HighlightEditor_outlineId, null, \"f\");\n}, _HighlightEditor_addToDrawLayer = function _HighlightEditor_addToDrawLayer(parent = this.parent) {\n    var _b, _c;\n    if (__classPrivateFieldGet(this, _HighlightEditor_id, \"f\") !== null) {\n        return;\n    }\n    (_b = this, _c = this, { id: ({ set value(_d) { __classPrivateFieldSet(_b, _HighlightEditor_id, _d, \"f\"); } }).value, clipPathId: ({ set value(_d) { __classPrivateFieldSet(_c, _HighlightEditor_clipPathId, _d, \"f\"); } }).value } =\n        parent.drawLayer.highlight(__classPrivateFieldGet(this, _HighlightEditor_highlightOutlines, \"f\"), this.color, __classPrivateFieldGet(this, _HighlightEditor_opacity, \"f\")));\n    __classPrivateFieldSet(this, _HighlightEditor_outlineId, parent.drawLayer.highlightOutline(__classPrivateFieldGet(this, _HighlightEditor_focusOutlines, \"f\")), \"f\");\n    // // todo: manually set styles, so that SVG elements are properly displayed\n    // if (parent.drawLayer.parent) {\n    //     Array.from(parent.drawLayer.parent.querySelectorAll(\".highlight\")).forEach(x => {\n    //         const element = x as any;\n    //         element.style[\"--blend-mode\"] = \"multiply\";\n    //         element.style.position = \"absolute\";\n    //         element.style[\"mix-blend-mode\"] = \"var(--blend-mode)\";\n    //         element.style.transform = \"none\";\n    //     });\n    // }\n    if (__classPrivateFieldGet(this, _HighlightEditor_highlightDiv, \"f\")) {\n        __classPrivateFieldGet(this, _HighlightEditor_highlightDiv, \"f\").style.clipPath = __classPrivateFieldGet(this, _HighlightEditor_clipPathId, \"f\");\n    }\n}, _HighlightEditor_rotateBbox = function _HighlightEditor_rotateBbox({ x, y, width, height }, angle) {\n    switch (angle) {\n        case 90:\n            return {\n                x: 1 - y - height,\n                y: x,\n                width: height,\n                height: width\n            };\n        case 180:\n            return {\n                x: 1 - x - width,\n                y: 1 - y - height,\n                width,\n                height\n            };\n        case 270:\n            return {\n                x: y,\n                y: 1 - x - width,\n                width: height,\n                height: width\n            };\n        default: break;\n    }\n    return {\n        x,\n        y,\n        width,\n        height\n    };\n}, _HighlightEditor_keydown = function _HighlightEditor_keydown(event) {\n    if (!event) {\n        /* no-empty */\n    }\n    // HighlightEditor._keyboardManager.exec(this, event);\n}, _HighlightEditor_setCaret = function _HighlightEditor_setCaret(start) {\n    if (!__classPrivateFieldGet(this, _HighlightEditor_anchorNode, \"f\")) {\n        return;\n    }\n    const selection = window.getSelection();\n    if (start) {\n        selection.setPosition(__classPrivateFieldGet(this, _HighlightEditor_anchorNode, \"f\"), __classPrivateFieldGet(this, _HighlightEditor_anchorOffset, \"f\"));\n    }\n    else {\n        selection.setPosition(__classPrivateFieldGet(this, _HighlightEditor_focusNode, \"f\"), __classPrivateFieldGet(this, _HighlightEditor_focusOffset, \"f\"));\n    }\n}, _HighlightEditor_getRotation = function _HighlightEditor_getRotation() {\n    // Highlight annotations are always drawn horizontally but if\n    // a free highlight annotation can be rotated.\n    return __classPrivateFieldGet(this, _HighlightEditor_isFreeHighlight, \"f\") ? this.rotation : 0;\n}, _HighlightEditor_serializeBoxes = function _HighlightEditor_serializeBoxes() {\n    if (__classPrivateFieldGet(this, _HighlightEditor_isFreeHighlight, \"f\")) {\n        return null;\n    }\n    const [pageWidth, pageHeight] = this.pageDimensions;\n    const [pageX, pageY] = this.pageTranslation;\n    const boxes = __classPrivateFieldGet(this, _HighlightEditor_boxes, \"f\");\n    const quadPoints = new Float32Array(boxes.length * 8);\n    let i = 0;\n    for (const { x, y, width, height } of boxes) {\n        const sx = x * pageWidth + pageX;\n        const sy = (1 - y - height) * pageHeight + pageY;\n        // The specifications say that the rectangle should start from the bottom\n        // left corner and go counter-clockwise.\n        // But when opening the file in Adobe Acrobat it appears that this isn't\n        // correct hence the 4th and 6th numbers are just swapped.\n        quadPoints[i] = quadPoints[i + 4] = sx;\n        quadPoints[i + 1] = quadPoints[i + 3] = sy;\n        quadPoints[i + 2] = quadPoints[i + 6] = sx + width * pageWidth;\n        quadPoints[i + 5] = quadPoints[i + 7] = sy + height * pageHeight;\n        i += 8;\n    }\n    return quadPoints;\n}, _HighlightEditor_serializeOutlines = function _HighlightEditor_serializeOutlines(rect) {\n    return __classPrivateFieldGet(this, _HighlightEditor_highlightOutlines, \"f\").serialize(rect, __classPrivateFieldGet(this, _HighlightEditor_instances, \"m\", _HighlightEditor_getRotation).call(this));\n}, _HighlightEditor_highlightMove = function _HighlightEditor_highlightMove(parent, event) {\n    if (this._freeHighlight.add(event)) {\n        // Redraw only if the point has been added.\n        parent.drawLayer.updatePath(this._freeHighlightId, this._freeHighlight);\n    }\n}, _HighlightEditor_endHighlight = function _HighlightEditor_endHighlight(parent, event) {\n    if (!this._freeHighlight.isEmpty()) {\n        parent.createAndAddNewEditor(event, false, {\n            highlightId: this._freeHighlightId,\n            highlightOutlines: this._freeHighlight.getOutlines(),\n            clipPathId: this._freeHighlightClipId,\n            methodOfCreation: \"main_toolbar\"\n        });\n    }\n    else {\n        parent.drawLayer.removeFreeHighlight(this._freeHighlightId);\n    }\n    this._freeHighlightId = -1;\n    this._freeHighlight = null;\n    this._freeHighlightClipId = \"\";\n};\nHighlightEditor._defaultColor = null;\nHighlightEditor._defaultOpacity = 1;\nHighlightEditor._defaultThickness = 12;\nHighlightEditor._type = \"highlight\";\nHighlightEditor._editorType = AnnotationEditorType.HIGHLIGHT;\nHighlightEditor._freeHighlightId = -1;\nHighlightEditor._freeHighlight = null;\nHighlightEditor._freeHighlightClipId = \"\";\nexport { HighlightEditor };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,0BAA0B,EAAEC,EAAE,EAAEC,2BAA2B,EAAEC,6BAA6B,EAAEC,sBAAsB,EAAEC,2BAA2B,EAAEC,8BAA8B,EAAEC,0BAA0B,EAAEC,4BAA4B,EAAEC,6BAA6B,EAAEC,kCAAkC,EAAEC,mBAAmB,EAAEC,gCAAgC,EAAEC,0BAA0B,EAAEC,wBAAwB,EAAEC,0BAA0B,EAAEC,qBAAqB,EAAEC,0BAA0B,EAAEC,iCAAiC,EAAEC,+BAA+B,EAAEC,mCAAmC,EAAEC,4BAA4B,EAAEC,gCAAgC,EAAEC,+BAA+B,EAAEC,+BAA+B,EAAEC,2BAA2B,EAAEC,wBAAwB,EAAEC,yBAAyB,EAAEC,4BAA4B,EAAEC,+BAA+B,EAAEC,kCAAkC,EAAEC,8BAA8B,EAAEC,6BAA6B;AAC38B,SAASC,sBAAsB,EAAEC,sBAAsB,QAAQ,OAAO;AACtE,SAASC,0BAA0B,EAAEC,oBAAoB;AACzD;AACA;AACAC,IAAI,EAAEC,aAAa,QAAQ,iCAAiC;AAC5D,SAASC,UAAU,QAAQ,kBAAkB;AAC7C;AACA;AACA,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,YAAY,EAAEC,QAAQ,QAAQ,YAAY;AACnD;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,SAASH,gBAAgB,CAAC;EAC3C;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAI,WAAWA,CAACC,MAAM,EAAE;IAChB,KAAK,CAACC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,MAAM,CAAC,EAAE;MAAEG,IAAI,EAAE;IAAqB,CAAC,CAAC,CAAC;IAC/EhD,0BAA0B,CAACiD,GAAG,CAAC,IAAI,CAAC;IACpC;IACA,IAAI,CAACC,KAAK,GAAG,EAAE;IACf;IACA;IACA;IACA;IACA;IACA;IACAhD,2BAA2B,CAACiD,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAC3ChD,6BAA6B,CAACgD,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;IAC1C/C,sBAAsB,CAAC+C,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACxC9C,2BAA2B,CAAC8C,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAC3C;IACA7C,8BAA8B,CAAC6C,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAC9C5C,0BAA0B,CAAC4C,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAC1C3C,4BAA4B,CAAC2C,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;IACzC1C,6BAA6B,CAAC0C,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAC7CzC,kCAAkC,CAACyC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAClDxC,mBAAmB,CAACwC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IACnCvC,gCAAgC,CAACuC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC;IACjDtC,0BAA0B,CAACsC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAC1CrC,wBAAwB,CAACqC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC1CpC,0BAA0B,CAACoC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAC1CnC,qBAAqB,CAACmC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;IACnClC,0BAA0B,CAACkC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC5CjC,iCAAiC,CAACiC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;IAC/C,IAAI,CAACD,KAAK,GAAGL,MAAM,CAACK,KAAK,IAAIjD,EAAE,CAACmD,aAAa;IAC7ClB,sBAAsB,CAAC,IAAI,EAAEjB,0BAA0B,EAAE4B,MAAM,CAACQ,SAAS,IAAIpD,EAAE,CAACqD,iBAAiB,EAAE,GAAG,CAAC;IACvGpB,sBAAsB,CAAC,IAAI,EAAEpB,wBAAwB,EAAE+B,MAAM,CAACU,OAAO,IAAItD,EAAE,CAACuD,eAAe,EAAE,GAAG,CAAC;IACjGtB,sBAAsB,CAAC,IAAI,EAAE9B,sBAAsB,EAAEyC,MAAM,CAACY,KAAK,IAAI,IAAI,EAAE,GAAG,CAAC;IAC/EvB,sBAAsB,CAAC,IAAI,EAAEhB,iCAAiC,EAAE2B,MAAM,CAACa,gBAAgB,IAAI,EAAE,EAAE,GAAG,CAAC;IACnGxB,sBAAsB,CAAC,IAAI,EAAElB,qBAAqB,EAAE6B,MAAM,CAACc,IAAI,IAAI,EAAE,EAAE,GAAG,CAAC;IAC3E,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAIf,MAAM,CAACgB,WAAW,GAAG,CAAC,CAAC,EAAE;MACzB3B,sBAAsB,CAAC,IAAI,EAAEtB,gCAAgC,EAAE,IAAI,EAAE,GAAG,CAAC;MACzEqB,sBAAsB,CAAC,IAAI,EAAEjC,0BAA0B,EAAE,GAAG,EAAEoB,mCAAmC,CAAC,CAAC0C,IAAI,CAAC,IAAI,EAAEjB,MAAM,CAAC;MACrHZ,sBAAsB,CAAC,IAAI,EAAEjC,0BAA0B,EAAE,GAAG,EAAEwB,+BAA+B,CAAC,CAACsC,IAAI,CAAC,IAAI,CAAC;IAC7G,CAAC,MACI;MACD5B,sBAAsB,CAAC,IAAI,EAAEhC,2BAA2B,EAAE2C,MAAM,CAACkB,UAAU,EAAE,GAAG,CAAC;MACjF7B,sBAAsB,CAAC,IAAI,EAAE/B,6BAA6B,EAAE0C,MAAM,CAACmB,YAAY,EAAE,GAAG,CAAC;MACrF9B,sBAAsB,CAAC,IAAI,EAAE3B,0BAA0B,EAAEsC,MAAM,CAACoB,SAAS,EAAE,GAAG,CAAC;MAC/E/B,sBAAsB,CAAC,IAAI,EAAE1B,4BAA4B,EAAEqC,MAAM,CAACqB,WAAW,EAAE,GAAG,CAAC;MACnFjC,sBAAsB,CAAC,IAAI,EAAEjC,0BAA0B,EAAE,GAAG,EAAEmB,+BAA+B,CAAC,CAAC2C,IAAI,CAAC,IAAI,CAAC;MACzG7B,sBAAsB,CAAC,IAAI,EAAEjC,0BAA0B,EAAE,GAAG,EAAEwB,+BAA+B,CAAC,CAACsC,IAAI,CAAC,IAAI,CAAC;MACzG,IAAI,CAACK,MAAM,CAAC,IAAI,CAACC,QAAQ,CAAC;IAC9B;EACJ;EACA;EACA,IAAIC,oBAAoBA,CAAA,EAAG;IACvB,OAAO;MACHC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAEtC,sBAAsB,CAAC,IAAI,EAAErB,gCAAgC,EAAE,GAAG,CAAC,GAAG,gBAAgB,GAAG,WAAW;MAC1GsC,KAAK,EAAE,IAAI,CAACsB,UAAU,CAACC,mBAAmB,CAACC,GAAG,CAAC,IAAI,CAACxB,KAAK,CAAC;MAC1DG,SAAS,EAAEpB,sBAAsB,CAAC,IAAI,EAAEhB,0BAA0B,EAAE,GAAG,CAAC;MACxEyC,gBAAgB,EAAEzB,sBAAsB,CAAC,IAAI,EAAEf,iCAAiC,EAAE,GAAG;IACzF,CAAC;EACL;EACA;EACA,IAAIyD,kBAAkBA,CAAA,EAAG;IACrB,OAAO;MACHJ,IAAI,EAAE,WAAW;MACjBrB,KAAK,EAAE,IAAI,CAACsB,UAAU,CAACC,mBAAmB,CAACC,GAAG,CAAC,IAAI,CAACxB,KAAK;IAC7D,CAAC;EACL;EACA,OAAO0B,yBAAyBA,CAACC,IAAI,EAAE;IACnC;IACA,OAAO;MAAEC,cAAc,EAAED,IAAI,CAACH,GAAG,CAAC,OAAO,CAAC,CAACK;IAAK,CAAC;EACrD;EACA;EACA,OAAOC,UAAUA,CAACC,IAAI,EAAEC,SAAS,EAAE;IAC/B,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAClB9C,gBAAgB,CAACwC,UAAU,CAACC,IAAI,EAAEC,SAAS,EAAE,CAAC,CAAC,CAAC;IAChDjF,EAAE,CAACmD,aAAa,KAAKnD,EAAE,CAACmD,aAAa;IACrC;IACA;IACA,CAAC,CAACkC,EAAE,GAAG,CAACD,EAAE,GAAG,CAACD,EAAE,GAAG,CAACD,EAAE,GAAGD,SAAS,CAACK,MAAM,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,OAAO,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,WAAW,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,SAAS,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACpC,KAAK,KAAK,SAAS,CAAC;EACpQ;EACA;EACA,OAAOyC,mBAAmBA,CAACpB,IAAI,EAAEqB,KAAK,EAAE;IACpC,QAAQrB,IAAI;MACR,KAAKpC,0BAA0B,CAAC0D,uBAAuB;QACnD5F,EAAE,CAACmD,aAAa,GAAGwC,KAAK;QACxB;MACJ,KAAKzD,0BAA0B,CAAC2D,mBAAmB;QAC/C7F,EAAE,CAACqD,iBAAiB,GAAGsC,KAAK;QAC5B;MACJ;QAAS;IACb;EACJ;EACA;EACA;EACAG,eAAeA,CAAA,EAAG,CAAE;EACpB;EACA,IAAIC,eAAeA,CAAA,EAAG;IAClB,OAAO/D,sBAAsB,CAAC,IAAI,EAAEpB,0BAA0B,EAAE,GAAG,CAAC;EACxE;EACA;EACAoF,YAAYA,CAAC1B,IAAI,EAAEqB,KAAK,EAAE;IACtB,QAAQrB,IAAI;MACR,KAAKpC,0BAA0B,CAAC+D,eAAe;QAC3CjE,sBAAsB,CAAC,IAAI,EAAEjC,0BAA0B,EAAE,GAAG,EAAEqB,4BAA4B,CAAC,CAACyC,IAAI,CAAC,IAAI,EAAE8B,KAAK,CAAC;QAC7G;MACJ,KAAKzD,0BAA0B,CAAC2D,mBAAmB;QAC/C7D,sBAAsB,CAAC,IAAI,EAAEjC,0BAA0B,EAAE,GAAG,EAAEsB,gCAAgC,CAAC,CAACwC,IAAI,CAAC,IAAI,EAAE8B,KAAK,CAAC;QACjH;MACJ;QAAS;IACb;EACJ;EACA,WAAWO,yBAAyBA,CAAA,EAAG;IACnC,OAAO,CACH,CACIhE,0BAA0B,CAAC0D,uBAAuB,EAClD5F,EAAE,CAACmD,aAAa,CACnB,EACD,CACIjB,0BAA0B,CAAC2D,mBAAmB,EAC9C7F,EAAE,CAACqD,iBAAiB,CACvB,CACJ;EACL;EACA;EACA,IAAI8C,kBAAkBA,CAAA,EAAG;IACrB,OAAO,CACH,CACIjE,0BAA0B,CAAC+D,eAAe,EAC1C,IAAI,CAAChD,KAAK,IAAIjD,EAAE,CAACmD,aAAa,CACjC,EACD,CACIjB,0BAA0B,CAAC2D,mBAAmB,EAC9C7D,sBAAsB,CAAC,IAAI,EAAEhB,0BAA0B,EAAE,GAAG,CAAC,IAAIhB,EAAE,CAACqD,iBAAiB,CACxF,EACD,CAACnB,0BAA0B,CAACkE,cAAc,EAAEpE,sBAAsB,CAAC,IAAI,EAAErB,gCAAgC,EAAE,GAAG,CAAC,CAAC,CACnH;EACL;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA0F,cAAcA,CAAA,EAAG;IACb,KAAK,CAACA,cAAc,CAAC,CAAC;IACtB;IACA,IAAI,CAACC,GAAG,CAACC,SAAS,CAACC,MAAM,CAAC,6BAA6B,EAAE,IAAI,CAAC;EAClE;EACA;EACAC,aAAaA,CAAA,EAAG;IACZ,KAAK,CAACA,aAAa,CAAC,CAAC;IACrB;IACA,IAAI,CAACH,GAAG,CAACC,SAAS,CAACC,MAAM,CAAC,6BAA6B,EAAE,KAAK,CAAC;EACnE;EACA;EACAE,iBAAiBA,CAAA,EAAG;IAChB,OAAO,KAAK,CAACA,iBAAiB,CAAC1E,sBAAsB,CAAC,IAAI,EAAEjC,0BAA0B,EAAE,GAAG,EAAE4B,4BAA4B,CAAC,CAACkC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC1I;EACA;EACA8C,kBAAkBA,CAAA,EAAG;IACjB;IACA;IACA,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EACjB;EACA;EACAC,OAAOA,CAACC,EAAE,EAAEC,EAAE,EAAE;IACZ,OAAO,KAAK,CAACF,OAAO,CAACC,EAAE,EAAEC,EAAE,EAAE9E,sBAAsB,CAAC,IAAI,EAAEjC,0BAA0B,EAAE,GAAG,EAAE4B,4BAA4B,CAAC,CAACkC,IAAI,CAAC,IAAI,CAAC,CAAC;EACxI;EACA;EACAkD,SAASA,CAAA,EAAG;IACR,IAAI,CAACC,MAAM,CAACC,iBAAiB,CAAC,IAAI,CAAC;IACnC,IAAI,CAACX,GAAG,CAACY,KAAK,CAAC,CAAC;EACpB;EACA;EACAC,MAAMA,CAAA,EAAG;IACLnF,sBAAsB,CAAC,IAAI,EAAEjC,0BAA0B,EAAE,GAAG,EAAEuB,+BAA+B,CAAC,CAACuC,IAAI,CAAC,IAAI,CAAC;IACzG;IACA;IACA;IACA,KAAK,CAACsD,MAAM,CAAC,CAAC;EAClB;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACJ,MAAM,EAAE;MACd;IACJ;IACA,KAAK,CAACI,OAAO,CAAC,CAAC;IACf,IAAI,IAAI,CAACd,GAAG,KAAK,IAAI,EAAE;MACnB;IACJ;IACAtE,sBAAsB,CAAC,IAAI,EAAEjC,0BAA0B,EAAE,GAAG,EAAEwB,+BAA+B,CAAC,CAACsC,IAAI,CAAC,IAAI,CAAC;IACzG,IAAI,CAAC,IAAI,CAACwD,eAAe,EAAE;MACvB;MACA;MACA,IAAI,CAACL,MAAM,CAAChE,GAAG,CAAC,IAAI,CAAC;IACzB;EACJ;EACAsE,SAASA,CAACN,MAAM,EAAE;IACd,IAAI9B,EAAE,EAAEC,EAAE;IACV,IAAIoC,cAAc,GAAG,KAAK;IAC1B,IAAI,IAAI,CAACP,MAAM,IAAI,CAACA,MAAM,EAAE;MACxBhF,sBAAsB,CAAC,IAAI,EAAEjC,0BAA0B,EAAE,GAAG,EAAEuB,+BAA+B,CAAC,CAACuC,IAAI,CAAC,IAAI,CAAC;IAC7G,CAAC,MACI,IAAImD,MAAM,EAAE;MACbhF,sBAAsB,CAAC,IAAI,EAAEjC,0BAA0B,EAAE,GAAG,EAAEwB,+BAA+B,CAAC,CAACsC,IAAI,CAAC,IAAI,EAAEmD,MAAM,CAAC;MACjH;MACA;MACAO,cAAc,GACV,CAAC,IAAI,CAACP,MAAM,KACP,CAAC,CAAC9B,EAAE,GAAG,IAAI,CAACoB,GAAG,MAAM,IAAI,IAAIpB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqB,SAAS,CAACiB,QAAQ,CAAC,gBAAgB,CAAC,MACzF,CAACrC,EAAE,GAAG,IAAI,CAACmB,GAAG,MAAM,IAAI,IAAInB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACoB,SAAS,CAACiB,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;IAC3G;IACA,KAAK,CAACF,SAAS,CAACN,MAAM,CAAC;IACvB,IAAI,CAACS,IAAI,CAAC,IAAI,CAACC,UAAU,CAAC;IAC1B,IAAIH,cAAc,EAAE;MAChB;MACA,IAAI,CAACI,MAAM,CAAC,CAAC;IACjB;EACJ;EACA;EACAzD,MAAMA,CAAC0D,KAAK,EAAE;IACV;IACA,MAAM;MAAEC;IAAU,CAAC,GAAG,IAAI,CAACb,MAAM;IACjC,IAAIc,GAAG;IACP,IAAI9F,sBAAsB,CAAC,IAAI,EAAErB,gCAAgC,EAAE,GAAG,CAAC,EAAE;MACrEiH,KAAK,GAAG,CAACA,KAAK,GAAG,IAAI,CAACzD,QAAQ,GAAG,GAAG,IAAI,GAAG;MAC3C2D,GAAG,GAAG9F,sBAAsB,CAAChC,EAAE,EAAEA,EAAE,EAAE,GAAG,EAAEwB,2BAA2B,CAAC,CAACqC,IAAI,CAAC7D,EAAE,EAAEgC,sBAAsB,CAAC,IAAI,EAAEvB,kCAAkC,EAAE,GAAG,CAAC,CAACqH,GAAG,EAAEF,KAAK,CAAC;IACrK,CAAC,MACI;MACD;MACAE,GAAG,GAAG9F,sBAAsB,CAAChC,EAAE,EAAEA,EAAE,EAAE,GAAG,EAAEwB,2BAA2B,CAAC,CAACqC,IAAI,CAAC7D,EAAE,EAAE,IAAI,EAAE4H,KAAK,CAAC;IAChG;IACAC,SAAS,CAAC3D,MAAM,CAAClC,sBAAsB,CAAC,IAAI,EAAEtB,mBAAmB,EAAE,GAAG,CAAC,EAAEkH,KAAK,CAAC;IAC/EC,SAAS,CAAC3D,MAAM,CAAClC,sBAAsB,CAAC,IAAI,EAAElB,0BAA0B,EAAE,GAAG,CAAC,EAAE8G,KAAK,CAAC;IACtFC,SAAS,CAACE,SAAS,CAAC/F,sBAAsB,CAAC,IAAI,EAAEtB,mBAAmB,EAAE,GAAG,CAAC,EAAEoH,GAAG,CAAC;IAChFD,SAAS,CAACE,SAAS,CAAC/F,sBAAsB,CAAC,IAAI,EAAElB,0BAA0B,EAAE,GAAG,CAAC,EAAEkB,sBAAsB,CAAChC,EAAE,EAAEA,EAAE,EAAE,GAAG,EAAEwB,2BAA2B,CAAC,CAACqC,IAAI,CAAC7D,EAAE,EAAEgC,sBAAsB,CAAC,IAAI,EAAE3B,8BAA8B,EAAE,GAAG,CAAC,CAACyH,GAAG,EAAEF,KAAK,CAAC,CAAC;EAC/O;EACA;EACAI,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAAC1B,GAAG,EAAE;MACV,OAAO,IAAI,CAACA,GAAG;IACnB;IACA,MAAMA,GAAG,GAAG,KAAK,CAAC0B,MAAM,CAAC,CAAC;IAC1B,IAAIhG,sBAAsB,CAAC,IAAI,EAAEjB,qBAAqB,EAAE,GAAG,CAAC,EAAE;MAC1DuF,GAAG,CAAC2B,YAAY,CAAC,YAAY,EAAEjG,sBAAsB,CAAC,IAAI,EAAEjB,qBAAqB,EAAE,GAAG,CAAC,CAAC;MACxFuF,GAAG,CAAC2B,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC;IACpC;IACA,IAAIjG,sBAAsB,CAAC,IAAI,EAAErB,gCAAgC,EAAE,GAAG,CAAC,EAAE;MACrE2F,GAAG,CAACC,SAAS,CAACvD,GAAG,CAAC,MAAM,CAAC;IAC7B,CAAC,MACI;MACD,IAAI,CAACsD,GAAG,CAAC4B,gBAAgB,CAAC,SAAS,EAAElG,sBAAsB,CAAC,IAAI,EAAEjC,0BAA0B,EAAE,GAAG,EAAE0B,wBAAwB,CAAC,CAAC0G,IAAI,CAAC,IAAI,CAAC,EAAE;QACrIC,MAAM,EAAE,IAAI,CAAC7D,UAAU,CAAC8D;MAC5B,CAAC,CAAC;IACN;IACA,MAAMC,YAAY,GAAIrG,sBAAsB,CAAC,IAAI,EAAEzB,6BAA6B,EAAE+H,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,EAAE,GAAG,CAAE;IACtHlC,GAAG,CAACmC,MAAM,CAACH,YAAY,CAAC;IACxBA,YAAY,CAACL,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IAChDK,YAAY,CAACI,SAAS,GAAG,qBAAqB;IAC9CJ,YAAY,CAACK,KAAK,CAACC,QAAQ,GAAG5G,sBAAsB,CAAC,IAAI,EAAE5B,2BAA2B,EAAE,GAAG,CAAC;IAC5F,MAAM,CAACyI,WAAW,EAAEC,YAAY,CAAC,GAAG,IAAI,CAACC,gBAAgB;IACzD,IAAI,CAACC,OAAO,CAAC,IAAI,CAACC,KAAK,GAAGJ,WAAW,EAAE,IAAI,CAACK,MAAM,GAAGJ,YAAY,CAAC;IAClExG,UAAU,CAAC,IAAI,EAAEN,sBAAsB,CAAC,IAAI,EAAExB,6BAA6B,EAAE,GAAG,CAAC,EAAE,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;IACnH,IAAI,CAACiG,aAAa,CAAC,CAAC;IACpB,OAAOH,GAAG;EACd;EACA6C,WAAWA,CAAA,EAAG;IACV,IAAI,CAACnC,MAAM,CAACa,SAAS,CAACuB,QAAQ,CAACpH,sBAAsB,CAAC,IAAI,EAAElB,0BAA0B,EAAE,GAAG,CAAC,EAAE,SAAS,CAAC;EAC5G;EACAuI,YAAYA,CAAA,EAAG;IACX,IAAI,CAACrC,MAAM,CAACa,SAAS,CAACyB,WAAW,CAACtH,sBAAsB,CAAC,IAAI,EAAElB,0BAA0B,EAAE,GAAG,CAAC,EAAE,SAAS,CAAC;EAC/G;EACAyI,UAAUA,CAACC,SAAS,EAAE;IAClB,IAAI,CAACxC,MAAM,CAACyC,QAAQ,CAAC,IAAI,CAAC;IAC1B,QAAQD,SAAS;MACb,KAAK,CAAC,CAAC;MACP,KAAK,CAAC,CAAC;QACHxH,sBAAsB,CAAC,IAAI,EAAEjC,0BAA0B,EAAE,GAAG,EAAE2B,yBAAyB,CAAC,CAACmC,IAAI,CAAC,IAAI,EAAE,aAAc,IAAI,CAAC;QACvH;MACJ,KAAK,CAAC,CAAC;MACP,KAAK,CAAC,CAAC;QACH7B,sBAAsB,CAAC,IAAI,EAAEjC,0BAA0B,EAAE,GAAG,EAAE2B,yBAAyB,CAAC,CAACmC,IAAI,CAAC,IAAI,EAAE,aAAc,KAAK,CAAC;QACxH;MACJ;QAAS;IACb;EACJ;EACA;EACA8D,MAAMA,CAAA,EAAG;IACL,IAAIzC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IACd,KAAK,CAACuC,MAAM,CAAC,CAAC;IACd,IAAI,CAAC3F,sBAAsB,CAAC,IAAI,EAAElB,0BAA0B,EAAE,GAAG,CAAC,EAAE;MAChE;IACJ;IACA,CAACoE,EAAE,GAAG,IAAI,CAAC8B,MAAM,MAAM,IAAI,IAAI9B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC2C,SAAS,CAACyB,WAAW,CAACtH,sBAAsB,CAAC,IAAI,EAAElB,0BAA0B,EAAE,GAAG,CAAC,EAAE,SAAS,CAAC;IAC1J,CAACqE,EAAE,GAAG,IAAI,CAAC6B,MAAM,MAAM,IAAI,IAAI7B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0C,SAAS,CAACuB,QAAQ,CAACpH,sBAAsB,CAAC,IAAI,EAAElB,0BAA0B,EAAE,GAAG,CAAC,EAAE,UAAU,CAAC;IACxJ,CAACsE,EAAE,GAAG,IAAI,CAAC4B,MAAM,MAAM,IAAI,IAAI5B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyC,SAAS,CAACuB,QAAQ,CAACpH,sBAAsB,CAAC,IAAI,EAAElB,0BAA0B,EAAE,GAAG,CAAC,EAAE,YAAY,CAAC;EAC9J;EACA;EACA2I,QAAQA,CAAA,EAAG;IACP,IAAIvE,EAAE,EAAEC,EAAE;IACV,KAAK,CAACsE,QAAQ,CAAC,CAAC;IAChB,IAAI,CAACzH,sBAAsB,CAAC,IAAI,EAAElB,0BAA0B,EAAE,GAAG,CAAC,EAAE;MAChE;IACJ;IACA,CAACoE,EAAE,GAAG,IAAI,CAAC8B,MAAM,MAAM,IAAI,IAAI9B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC2C,SAAS,CAACyB,WAAW,CAACtH,sBAAsB,CAAC,IAAI,EAAElB,0BAA0B,EAAE,GAAG,CAAC,EAAE,UAAU,CAAC;IAC3J,CAACqE,EAAE,GAAG,IAAI,CAAC6B,MAAM,MAAM,IAAI,IAAI7B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0C,SAAS,CAACyB,WAAW,CAACtH,sBAAsB,CAAC,IAAI,EAAElB,0BAA0B,EAAE,GAAG,CAAC,EAAE,YAAY,CAAC;IAC7J,IAAI,CAACkB,sBAAsB,CAAC,IAAI,EAAErB,gCAAgC,EAAE,GAAG,CAAC,EAAE;MACtEqB,sBAAsB,CAAC,IAAI,EAAEjC,0BAA0B,EAAE,GAAG,EAAE2B,yBAAyB,CAAC,CAACmC,IAAI,CAAC,IAAI,EAAE,aAAc,KAAK,CAAC;IAC5H;EACJ;EACA;EACA,IAAI6F,gBAAgBA,CAAA,EAAG;IACnB,OAAO,CAAC1H,sBAAsB,CAAC,IAAI,EAAErB,gCAAgC,EAAE,GAAG,CAAC;EAC/E;EACA;EACA8G,IAAIA,CAACkC,OAAO,GAAG,IAAI,CAACjC,UAAU,EAAE;IAC5B,KAAK,CAACD,IAAI,CAACkC,OAAO,CAAC;IACnB,IAAI,IAAI,CAAC3C,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAACa,SAAS,CAACJ,IAAI,CAACzF,sBAAsB,CAAC,IAAI,EAAEtB,mBAAmB,EAAE,GAAG,CAAC,EAAEiJ,OAAO,CAAC;MAC3F,IAAI,CAAC3C,MAAM,CAACa,SAAS,CAACJ,IAAI,CAACzF,sBAAsB,CAAC,IAAI,EAAElB,0BAA0B,EAAE,GAAG,CAAC,EAAE6I,OAAO,CAAC;IACtG;EACJ;EACA,OAAOC,iBAAiBA,CAAC5C,MAAM,EAAE6C,KAAK,EAAE;IAAEC,MAAM,EAAEC,SAAS;IAAEC,CAAC;IAAEC;EAAE,CAAC,EAAE;IACjE,MAAM;MAAED,CAAC,EAAEE,MAAM;MAAED,CAAC,EAAEE,MAAM;MAAElB,KAAK,EAAEJ,WAAW;MAAEK,MAAM,EAAEJ;IAAa,CAAC,GAAGiB,SAAS,CAACK,qBAAqB,CAAC,CAAC;IAC5G,MAAMC,EAAE,GAAG,IAAIC,eAAe,CAAC,CAAC;IAChC,MAAMlC,MAAM,GAAGpB,MAAM,CAACuD,cAAc,CAACF,EAAE,CAAC;IACxC,MAAMG,WAAW,GAAGC,CAAC,IAAI;MACrB;MACAA,CAAC,CAACC,cAAc,CAAC,CAAC;MAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACvB,CAAC;IACD,MAAMC,iBAAiB,GAAGH,CAAC,IAAI;MAC3BJ,EAAE,CAACQ,KAAK,CAAC,CAAC;MACV7I,sBAAsB,CAAC,IAAI,EAAEhC,EAAE,EAAE,GAAG,EAAE+B,6BAA6B,CAAC,CAAC8B,IAAI,CAAC,IAAI,EAAEmD,MAAM,EAAEyD,CAAC,CAAC;IAC9F,CAAC;IACDK,MAAM,CAAC5C,gBAAgB,CAAC,MAAM,EAAE0C,iBAAiB,EAAE;MAAExC;IAAO,CAAC,CAAC;IAC9D0C,MAAM,CAAC5C,gBAAgB,CAAC,WAAW,EAAE0C,iBAAiB,EAAE;MAAExC;IAAO,CAAC,CAAC;IACnE0C,MAAM,CAAC5C,gBAAgB,CAAC,aAAa,EAAEsC,WAAW,EAAE;MAChDO,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE,KAAK;MACd5C;IACJ,CAAC,CAAC;IACF0C,MAAM,CAAC5C,gBAAgB,CAAC,aAAa,EAAE7F,aAAa,EAAE;MAAE+F;IAAO,CAAC,CAAC;IACjE2B,SAAS,CAAC7B,gBAAgB,CAAC,aAAa,EAAElG,sBAAsB,CAAC,IAAI,EAAEhC,EAAE,EAAE,GAAG,EAAE8B,8BAA8B,CAAC,CAACqG,IAAI,CAAC,IAAI,EAAEnB,MAAM,CAAC,EAAE;MAAEoB;IAAO,CAAC,CAAC;IAC/I,IAAI,CAAC6C,cAAc,GAAG,IAAIzI,YAAY,CAAC;MAAEwH,CAAC;MAAEC;IAAE,CAAC,EAAE,CAACC,MAAM,EAAEC,MAAM,EAAEtB,WAAW,EAAEC,YAAY,CAAC,EAAE9B,MAAM,CAACkE,KAAK,EAAE,IAAI,CAAC7H,iBAAiB,GAAG,CAAC,EAAEwG,KAAK,EAC7I,mBAAoB,KAAK,CAAC;IAC1B,CAAC;MAAEsB,EAAE,EAAE,IAAI,CAACC,gBAAgB;MAAEC,UAAU,EAAE,IAAI,CAACC;IAAqB,CAAC,GACjEtE,MAAM,CAACa,SAAS,CAACpC,SAAS,CAAC,IAAI,CAACwF,cAAc,EAAE,IAAI,CAAC9H,aAAa,EAAE,IAAI,CAACI,eAAe,EACxF,uBAAwB,IAAI,CAAC;EACrC;EACA;EACA,OAAOgI,WAAWA,CAAC3G,IAAI,EAAEoC,MAAM,EAAE/B,SAAS,EAAE;IACxC,MAAMuG,MAAM,GAAG,KAAK,CAACD,WAAW,CAAC3G,IAAI,EAAEoC,MAAM,EAAE/B,SAAS,CAAC;IACzD,MAAM;MAAEwG,IAAI,EAAE,CAACC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,CAAC;MAAE5I,KAAK;MAAE6I;IAAW,CAAC,GAAGlH,IAAI;IAC9D;IACA4G,MAAM,CAACvI,KAAK,GAAGb,IAAI,CAAC2J,YAAY,CAAC,GAAG9I,KAAK,CAAC;IAC1ChB,sBAAsB,CAACuJ,MAAM,EAAE3K,wBAAwB,EAAE+D,IAAI,CAACtB,OAAO,EAAE,GAAG,CAAC;IAC3E,MAAM,CAAC0I,SAAS,EAAEC,UAAU,CAAC,GAAGT,MAAM,CAACU,cAAc;IACrDV,MAAM,CAACvC,KAAK,GAAG,CAAC2C,GAAG,GAAGF,GAAG,IAAIM,SAAS;IACtCR,MAAM,CAACtC,MAAM,GAAG,CAAC2C,GAAG,GAAGF,GAAG,IAAIM,UAAU;IACxC,MAAMzI,KAAK,GAAIvB,sBAAsB,CAACuJ,MAAM,EAAErL,sBAAsB,EAAE,EAAE,EAAE,GAAG,CAAE;IAC/E,KAAK,IAAIgM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,UAAU,CAACM,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MAC3C3I,KAAK,CAAC6I,IAAI,CAAC;QACPrC,CAAC,EAAE,CAAC8B,UAAU,CAAC,CAAC,CAAC,GAAGF,GAAG,IAAII,SAAS;QACpC/B,CAAC,EAAE,CAAC4B,GAAG,IAAI,CAAC,GAAGC,UAAU,CAACK,CAAC,GAAG,CAAC,CAAC,CAAC,IAAIF,UAAU;QAC/ChD,KAAK,EAAE,CAAC6C,UAAU,CAACK,CAAC,GAAG,CAAC,CAAC,GAAGL,UAAU,CAACK,CAAC,CAAC,IAAIH,SAAS;QACtD9C,MAAM,EAAE,CAAC4C,UAAU,CAACK,CAAC,GAAG,CAAC,CAAC,GAAGL,UAAU,CAACK,CAAC,GAAG,CAAC,CAAC,IAAIF;MACtD,CAAC,CAAC;IACN;IACAjK,sBAAsB,CAACwJ,MAAM,EAAEzL,0BAA0B,EAAE,GAAG,EAAEmB,+BAA+B,CAAC,CAAC2C,IAAI,CAAC2H,MAAM,CAAC;IAC7G,OAAOA,MAAM;EACjB;EACA;EACAc,SAASA,CAACC,YAAY,GAAG,KAAK,EAAE;IAC5B;IACA,IAAI,IAAI,CAACC,OAAO,CAAC,CAAC,IAAID,YAAY,EAAE;MAChC,OAAO,IAAI;IACf;IACA,MAAMd,IAAI,GAAG,IAAI,CAAC7E,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;IAC/B,MAAM3D,KAAK,GAAGV,gBAAgB,CAACkK,aAAa,CAACC,OAAO,CAAC,IAAI,CAACzJ,KAAK,CAAC;IAChE,OAAO;MACH0J,cAAc,EAAExK,oBAAoB,CAACyK,SAAS;MAC9C3J,KAAK;MACLK,OAAO,EAAEtB,sBAAsB,CAAC,IAAI,EAAEnB,wBAAwB,EAAE,GAAG,CAAC;MACpEuC,SAAS,EAAEpB,sBAAsB,CAAC,IAAI,EAAEhB,0BAA0B,EAAE,GAAG,CAAC;MACxE8K,UAAU,EAAE9J,sBAAsB,CAAC,IAAI,EAAEjC,0BAA0B,EAAE,GAAG,EAAE6B,+BAA+B,CAAC,CAACiC,IAAI,CAAC,IAAI,CAAC;MACrHgJ,QAAQ,EAAE7K,sBAAsB,CAAC,IAAI,EAAEjC,0BAA0B,EAAE,GAAG,EAAE8B,kCAAkC,CAAC,CAACgC,IAAI,CAAC,IAAI,EAAE4H,IAAI,CAAC;MAC5HqB,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBrB,IAAI;MACJtH,QAAQ,EAAEnC,sBAAsB,CAAC,IAAI,EAAEjC,0BAA0B,EAAE,GAAG,EAAE4B,4BAA4B,CAAC,CAACkC,IAAI,CAAC,IAAI,CAAC;MAChHkJ,kBAAkB,EAAE,IAAI,CAACC;IAC7B,CAAC;EACL;EACA,OAAOC,uBAAuBA,CAAA,EAAG;IAC7B,OAAO,KAAK;EAChB;EACA;EACA;EACA;EACA;EACAC,MAAMA,CAAA,EAAG;IACL,MAAMtI,IAAI,GAAG,IAAI,CAAC0H,SAAS,CAAC,CAAC;IAC7B,OAAO1H,IAAI;EACf;AACJ;AACA5E,EAAE,GAAG0C,eAAe,EAAEzC,2BAA2B,GAAG,IAAIkN,OAAO,CAAC,CAAC,EAAEjN,6BAA6B,GAAG,IAAIiN,OAAO,CAAC,CAAC,EAAEhN,sBAAsB,GAAG,IAAIgN,OAAO,CAAC,CAAC,EAAE/M,2BAA2B,GAAG,IAAI+M,OAAO,CAAC,CAAC,EAAE9M,8BAA8B,GAAG,IAAI8M,OAAO,CAAC,CAAC,EAAE7M,0BAA0B,GAAG,IAAI6M,OAAO,CAAC,CAAC,EAAE5M,4BAA4B,GAAG,IAAI4M,OAAO,CAAC,CAAC,EAAE3M,6BAA6B,GAAG,IAAI2M,OAAO,CAAC,CAAC,EAAE1M,kCAAkC,GAAG,IAAI0M,OAAO,CAAC,CAAC,EAAEzM,mBAAmB,GAAG,IAAIyM,OAAO,CAAC,CAAC,EAAExM,gCAAgC,GAAG,IAAIwM,OAAO,CAAC,CAAC,EAAEvM,0BAA0B,GAAG,IAAIuM,OAAO,CAAC,CAAC,EAAEtM,wBAAwB,GAAG,IAAIsM,OAAO,CAAC,CAAC,EAAErM,0BAA0B,GAAG,IAAIqM,OAAO,CAAC,CAAC,EAAEpM,qBAAqB,GAAG,IAAIoM,OAAO,CAAC,CAAC,EAAEnM,0BAA0B,GAAG,IAAImM,OAAO,CAAC,CAAC,EAAElM,iCAAiC,GAAG,IAAIkM,OAAO,CAAC,CAAC,EAAEpN,0BAA0B,GAAG,IAAIqN,OAAO,CAAC,CAAC,EAAElM,+BAA+B,GAAG,SAASA,+BAA+BA,CAAA,EAAG;EACx4B,MAAMmM,QAAQ,GAAG,IAAI5K,QAAQ,CAACT,sBAAsB,CAAC,IAAI,EAAE7B,sBAAsB,EAAE,GAAG,CAAC,EAAE,mBAAoB,KAAK,CAAC;EACnH8B,sBAAsB,CAAC,IAAI,EAAExB,kCAAkC,EAAE4M,QAAQ,CAACC,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC;EAC7F,CAAC;IACGtD,CAAC,EAAE,IAAI,CAACA,CAAC;IACTC,CAAC,EAAE,IAAI,CAACA,CAAC;IACThB,KAAK,EAAE,IAAI,CAACA,KAAK;IACjBC,MAAM,EAAE,IAAI,CAACA;EACjB,CAAC,GAAGlH,sBAAsB,CAAC,IAAI,EAAEvB,kCAAkC,EAAE,GAAG,CAAC,CAACqH,GAAG;EAC7E,MAAMyF,kBAAkB,GAAG,IAAI9K,QAAQ,CAACT,sBAAsB,CAAC,IAAI,EAAE7B,sBAAsB,EAAE,GAAG,CAAC,EACjG,mBAAoB,MAAM,EAC1B,mBAAoB,KAAK,EAAE,IAAI,CAACoE,UAAU,CAACiF,SAAS,KAAK,KAAK,CAAC;EAC/DvH,sBAAsB,CAAC,IAAI,EAAE5B,8BAA8B,EAAEkN,kBAAkB,CAACD,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC;EACnG;EACA,MAAM;IAAEE;EAAU,CAAC,GAAGxL,sBAAsB,CAAC,IAAI,EAAE3B,8BAA8B,EAAE,GAAG,CAAC,CAACyH,GAAG;EAC3F7F,sBAAsB,CAAC,IAAI,EAAErB,0BAA0B,EAAE,CACrD,CAAC4M,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAACxD,CAAC,IAAI,IAAI,CAACf,KAAK,EACpC,CAACuE,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAACvD,CAAC,IAAI,IAAI,CAACf,MAAM,CACxC,EAAE,GAAG,CAAC;AACX,CAAC,EAAE/H,mCAAmC,GAAG,SAASA,mCAAmCA,CAAC;EAAEsM,iBAAiB;EAAE7J,WAAW;EAAEyH;AAAW,CAAC,EAAE;EAClIpJ,sBAAsB,CAAC,IAAI,EAAExB,kCAAkC,EAAEgN,iBAAiB,EAAE,GAAG,CAAC;EACxF,MAAMC,cAAc,GAAG,GAAG;EAC1BzL,sBAAsB,CAAC,IAAI,EAAE5B,8BAA8B,EAAEoN,iBAAiB,CAACE,aAAa;EAC5F;AACJ;EACI3L,sBAAsB,CAAC,IAAI,EAAEhB,0BAA0B,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG0M,cAAc,EAClF,mBAAoB,MAAM,CAAC,EAAE,GAAG,CAAC;EACjC,IAAI9J,WAAW,IAAI,CAAC,EAAE;IAClB3B,sBAAsB,CAAC,IAAI,EAAEvB,mBAAmB,EAAEkD,WAAW,EAAE,GAAG,CAAC;IACnE3B,sBAAsB,CAAC,IAAI,EAAE7B,2BAA2B,EAAEiL,UAAU,EAAE,GAAG,CAAC;IAC1E;IACA;IACA,IAAI,CAACrE,MAAM,CAACa,SAAS,CAAC+F,YAAY,CAAChK,WAAW,EAAE6J,iBAAiB,CAAC;IAClExL,sBAAsB,CAAC,IAAI,EAAEnB,0BAA0B,EAAE,IAAI,CAACkG,MAAM,CAACa,SAAS,CAACgG,gBAAgB,CAAC7L,sBAAsB,CAAC,IAAI,EAAE3B,8BAA8B,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC;EAC5K,CAAC,MACI,IAAI,IAAI,CAAC2G,MAAM,EAAE;IAClB,MAAMY,KAAK,GAAG,IAAI,CAACZ,MAAM,CAAC8G,QAAQ,CAAC3J,QAAQ;IAC3C,IAAI,CAAC6C,MAAM,CAACa,SAAS,CAACkG,UAAU,CAAC/L,sBAAsB,CAAC,IAAI,EAAEtB,mBAAmB,EAAE,GAAG,CAAC,EAAE+M,iBAAiB,CAAC;IAC3G,IAAI,CAACzG,MAAM,CAACa,SAAS,CAACE,SAAS,CAAC/F,sBAAsB,CAAC,IAAI,EAAEtB,mBAAmB,EAAE,GAAG,CAAC,EAAEsB,sBAAsB,CAAChC,EAAE,EAAEA,EAAE,EAAE,GAAG,EAAEwB,2BAA2B,CAAC,CAACqC,IAAI,CAAC7D,EAAE,EAAEgC,sBAAsB,CAAC,IAAI,EAAEvB,kCAAkC,EAAE,GAAG,CAAC,CAACqH,GAAG,EAAE,CAACF,KAAK,GAAG,IAAI,CAACzD,QAAQ,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC;IAClR,IAAI,CAAC6C,MAAM,CAACa,SAAS,CAACkG,UAAU,CAAC/L,sBAAsB,CAAC,IAAI,EAAElB,0BAA0B,EAAE,GAAG,CAAC,EAAEkB,sBAAsB,CAAC,IAAI,EAAE3B,8BAA8B,EAAE,GAAG,CAAC,CAAC;IAClK,IAAI,CAAC2G,MAAM,CAACa,SAAS,CAACE,SAAS,CAAC/F,sBAAsB,CAAC,IAAI,EAAElB,0BAA0B,EAAE,GAAG,CAAC,EAAEkB,sBAAsB,CAAChC,EAAE,EAAEA,EAAE,EAAE,GAAG,EAAEwB,2BAA2B,CAAC,CAACqC,IAAI,CAAC7D,EAAE,EAAEgC,sBAAsB,CAAC,IAAI,EAAE3B,8BAA8B,EAAE,GAAG,CAAC,CAACyH,GAAG,EAAEF,KAAK,CAAC,CAAC;EAC3P;EACA,MAAM;IAAEoC,CAAC;IAAEC,CAAC;IAAEhB,KAAK;IAAEC;EAAO,CAAC,GAAGuE,iBAAiB,CAAC3F,GAAG;EACrD,QAAQ,IAAI,CAAC3D,QAAQ;IACjB,KAAK,CAAC;MACF,IAAI,CAAC6F,CAAC,GAAGA,CAAC;MACV,IAAI,CAACC,CAAC,GAAGA,CAAC;MACV,IAAI,CAAChB,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;MACpB;IACJ,KAAK,EAAE;MAAE;QACL,MAAM,CAAC8C,SAAS,EAAEC,UAAU,CAAC,GAAG,IAAI,CAAClD,gBAAgB;QACrD,IAAI,CAACiB,CAAC,GAAGC,CAAC;QACV,IAAI,CAACA,CAAC,GAAG,CAAC,GAAGD,CAAC;QACd,IAAI,CAACf,KAAK,GAAIA,KAAK,GAAGgD,UAAU,GAAID,SAAS;QAC7C,IAAI,CAAC9C,MAAM,GAAIA,MAAM,GAAG8C,SAAS,GAAIC,UAAU;QAC/C;MACJ;IACA,KAAK,GAAG;MACJ,IAAI,CAACjC,CAAC,GAAG,CAAC,GAAGA,CAAC;MACd,IAAI,CAACC,CAAC,GAAG,CAAC,GAAGA,CAAC;MACd,IAAI,CAAChB,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;MACpB;IACJ,KAAK,GAAG;MAAE;QACN,MAAM,CAAC8C,SAAS,EAAEC,UAAU,CAAC,GAAG,IAAI,CAAClD,gBAAgB;QACrD,IAAI,CAACiB,CAAC,GAAG,CAAC,GAAGC,CAAC;QACd,IAAI,CAACA,CAAC,GAAGD,CAAC;QACV,IAAI,CAACf,KAAK,GAAIA,KAAK,GAAGgD,UAAU,GAAID,SAAS;QAC7C,IAAI,CAAC9C,MAAM,GAAIA,MAAM,GAAG8C,SAAS,GAAIC,UAAU;QAC/C;MACJ;IACA;MAAS;EACb;EACA,MAAM;IAAEuB;EAAU,CAAC,GAAGxL,sBAAsB,CAAC,IAAI,EAAE3B,8BAA8B,EAAE,GAAG,CAAC,CAACyH,GAAG;EAC3F7F,sBAAsB,CAAC,IAAI,EAAErB,0BAA0B,EAAE,CAAC,CAAC4M,SAAS,CAAC,CAAC,CAAC,GAAGxD,CAAC,IAAIf,KAAK,EAAE,CAACuE,SAAS,CAAC,CAAC,CAAC,GAAGvD,CAAC,IAAIf,MAAM,CAAC,EAAE,GAAG,CAAC;AAC5H,CAAC,EAAE9H,4BAA4B,GAAG,SAASA,4BAA4BA,CAAC6B,KAAK,EAAE;EAC3E,IAAI,CAACA,KAAK,EAAE;IACR;EACJ;EACA,MAAM+K,QAAQ,GAAGC,GAAG,IAAI;IACpB,IAAI/I,EAAE;IACN,IAAI,CAACjC,KAAK,GAAGgL,GAAG;IAChB,CAAC/I,EAAE,GAAG,IAAI,CAAC8B,MAAM,MAAM,IAAI,IAAI9B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC2C,SAAS,CAACqG,WAAW,CAAClM,sBAAsB,CAAC,IAAI,EAAEtB,mBAAmB,EAAE,GAAG,CAAC,EAAEuN,GAAG,CAAC;IAC7I;EACJ,CAAC;EACD,MAAME,UAAU,GAAG,IAAI,CAAClL,KAAK;EAC7B,IAAI,CAACmL,WAAW,CAAC;IACbC,GAAG,EAAEL,QAAQ,CAAC7F,IAAI,CAAC,IAAI,EAAElF,KAAK,CAAC;IAC/BqL,IAAI,EAAEN,QAAQ,CAAC7F,IAAI,CAAC,IAAI,EAAEgG,UAAU,CAAC;IACrCI,IAAI,EAAE,IAAI,CAAChK,UAAU,CAACiK,QAAQ,CAACrG,IAAI,CAAC,IAAI,CAAC5D,UAAU,EAAE,IAAI,CAAC;IAC1DkK,QAAQ,EAAE,IAAI;IACdnK,IAAI,EAAEpC,0BAA0B,CAAC+D,eAAe;IAChDyI,mBAAmB,EAAE,IAAI;IACzBC,QAAQ,EAAE;EACd,CAAC,CAAC;EACF;EACA;EACA;EACA;EACA;EACA;EACA;AACJ,CAAC,EAAEtN,gCAAgC,GAAG,SAASA,gCAAgCA,CAAC+B,SAAS,EAAE;EACvF,IAAI,CAACA,SAAS,EAAE;IACZ;EAAA;EAEJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACJ,CAAC,EAAE9B,+BAA+B,GAAG,SAASA,+BAA+BA,CAAA,EAAG;EAC5E,IAAIU,sBAAsB,CAAC,IAAI,EAAEtB,mBAAmB,EAAE,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,CAACsG,MAAM,EAAE;IACjF;EACJ;EACA,IAAI,CAACA,MAAM,CAACa,SAAS,CAACV,MAAM,CAACnF,sBAAsB,CAAC,IAAI,EAAEtB,mBAAmB,EAAE,GAAG,CAAC,CAAC;EACpFuB,sBAAsB,CAAC,IAAI,EAAEvB,mBAAmB,EAAE,IAAI,EAAE,GAAG,CAAC;EAC5D,IAAI,CAACsG,MAAM,CAACa,SAAS,CAACV,MAAM,CAACnF,sBAAsB,CAAC,IAAI,EAAElB,0BAA0B,EAAE,GAAG,CAAC,CAAC;EAC3FmB,sBAAsB,CAAC,IAAI,EAAEnB,0BAA0B,EAAE,IAAI,EAAE,GAAG,CAAC;AACvE,CAAC,EAAES,+BAA+B,GAAG,SAASA,+BAA+BA,CAACyF,MAAM,GAAG,IAAI,CAACA,MAAM,EAAE;EAChG,IAAI9B,EAAE,EAAEC,EAAE;EACV,IAAInD,sBAAsB,CAAC,IAAI,EAAEtB,mBAAmB,EAAE,GAAG,CAAC,KAAK,IAAI,EAAE;IACjE;EACJ;EACCwE,EAAE,GAAG,IAAI,EAAEC,EAAE,GAAG,IAAI,EAAE;IAAEgG,EAAE,EAAG;MAAE,IAAIxF,KAAKA,CAACP,EAAE,EAAE;QAAEnD,sBAAsB,CAACiD,EAAE,EAAExE,mBAAmB,EAAE0E,EAAE,EAAE,GAAG,CAAC;MAAE;IAAE,CAAC,CAAEO,KAAK;IAAE0F,UAAU,EAAG;MAAE,IAAI1F,KAAKA,CAACP,EAAE,EAAE;QAAEnD,sBAAsB,CAACkD,EAAE,EAAE/E,2BAA2B,EAAEgF,EAAE,EAAE,GAAG,CAAC;MAAE;IAAE,CAAC,CAAEO;EAAM,CAAC,GAC/NqB,MAAM,CAACa,SAAS,CAACpC,SAAS,CAACzD,sBAAsB,CAAC,IAAI,EAAEvB,kCAAkC,EAAE,GAAG,CAAC,EAAE,IAAI,CAACwC,KAAK,EAAEjB,sBAAsB,CAAC,IAAI,EAAEnB,wBAAwB,EAAE,GAAG,CAAC,CAAC;EAC9KoB,sBAAsB,CAAC,IAAI,EAAEnB,0BAA0B,EAAEkG,MAAM,CAACa,SAAS,CAACgG,gBAAgB,CAAC7L,sBAAsB,CAAC,IAAI,EAAE3B,8BAA8B,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC;EACnK;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAI2B,sBAAsB,CAAC,IAAI,EAAExB,6BAA6B,EAAE,GAAG,CAAC,EAAE;IAClEwB,sBAAsB,CAAC,IAAI,EAAExB,6BAA6B,EAAE,GAAG,CAAC,CAACmI,KAAK,CAACC,QAAQ,GAAG5G,sBAAsB,CAAC,IAAI,EAAE5B,2BAA2B,EAAE,GAAG,CAAC;EACpJ;AACJ,CAAC,EAAEoB,2BAA2B,GAAG,SAASA,2BAA2BA,CAAC;EAAEwI,CAAC;EAAEC,CAAC;EAAEhB,KAAK;EAAEC;AAAO,CAAC,EAAEtB,KAAK,EAAE;EAClG,QAAQA,KAAK;IACT,KAAK,EAAE;MACH,OAAO;QACHoC,CAAC,EAAE,CAAC,GAAGC,CAAC,GAAGf,MAAM;QACjBe,CAAC,EAAED,CAAC;QACJf,KAAK,EAAEC,MAAM;QACbA,MAAM,EAAED;MACZ,CAAC;IACL,KAAK,GAAG;MACJ,OAAO;QACHe,CAAC,EAAE,CAAC,GAAGA,CAAC,GAAGf,KAAK;QAChBgB,CAAC,EAAE,CAAC,GAAGA,CAAC,GAAGf,MAAM;QACjBD,KAAK;QACLC;MACJ,CAAC;IACL,KAAK,GAAG;MACJ,OAAO;QACHc,CAAC,EAAEC,CAAC;QACJA,CAAC,EAAE,CAAC,GAAGD,CAAC,GAAGf,KAAK;QAChBA,KAAK,EAAEC,MAAM;QACbA,MAAM,EAAED;MACZ,CAAC;IACL;MAAS;EACb;EACA,OAAO;IACHe,CAAC;IACDC,CAAC;IACDhB,KAAK;IACLC;EACJ,CAAC;AACL,CAAC,EAAEzH,wBAAwB,GAAG,SAASA,wBAAwBA,CAACmN,KAAK,EAAE;EACnE,IAAI,CAACA,KAAK,EAAE;IACR;EAAA;EAEJ;AACJ,CAAC,EAAElN,yBAAyB,GAAG,SAASA,yBAAyBA,CAACmN,KAAK,EAAE;EACrE,IAAI,CAAC7M,sBAAsB,CAAC,IAAI,EAAE/B,2BAA2B,EAAE,GAAG,CAAC,EAAE;IACjE;EACJ;EACA,MAAM6O,SAAS,GAAGhE,MAAM,CAACiE,YAAY,CAAC,CAAC;EACvC,IAAIF,KAAK,EAAE;IACPC,SAAS,CAACE,WAAW,CAAChN,sBAAsB,CAAC,IAAI,EAAE/B,2BAA2B,EAAE,GAAG,CAAC,EAAE+B,sBAAsB,CAAC,IAAI,EAAE9B,6BAA6B,EAAE,GAAG,CAAC,CAAC;EAC3J,CAAC,MACI;IACD4O,SAAS,CAACE,WAAW,CAAChN,sBAAsB,CAAC,IAAI,EAAE1B,0BAA0B,EAAE,GAAG,CAAC,EAAE0B,sBAAsB,CAAC,IAAI,EAAEzB,4BAA4B,EAAE,GAAG,CAAC,CAAC;EACzJ;AACJ,CAAC,EAAEoB,4BAA4B,GAAG,SAASA,4BAA4BA,CAAA,EAAG;EACtE;EACA;EACA,OAAOK,sBAAsB,CAAC,IAAI,EAAErB,gCAAgC,EAAE,GAAG,CAAC,GAAG,IAAI,CAACwD,QAAQ,GAAG,CAAC;AAClG,CAAC,EAAEvC,+BAA+B,GAAG,SAASA,+BAA+BA,CAAA,EAAG;EAC5E,IAAII,sBAAsB,CAAC,IAAI,EAAErB,gCAAgC,EAAE,GAAG,CAAC,EAAE;IACrE,OAAO,IAAI;EACf;EACA,MAAM,CAACqL,SAAS,EAAEC,UAAU,CAAC,GAAG,IAAI,CAACC,cAAc;EACnD,MAAM,CAAC+C,KAAK,EAAEC,KAAK,CAAC,GAAG,IAAI,CAACC,eAAe;EAC3C,MAAM3L,KAAK,GAAGxB,sBAAsB,CAAC,IAAI,EAAE7B,sBAAsB,EAAE,GAAG,CAAC;EACvE,MAAM2L,UAAU,GAAG,IAAIsD,YAAY,CAAC5L,KAAK,CAAC4I,MAAM,GAAG,CAAC,CAAC;EACrD,IAAID,CAAC,GAAG,CAAC;EACT,KAAK,MAAM;IAAEnC,CAAC;IAAEC,CAAC;IAAEhB,KAAK;IAAEC;EAAO,CAAC,IAAI1F,KAAK,EAAE;IACzC,MAAM6L,EAAE,GAAGrF,CAAC,GAAGgC,SAAS,GAAGiD,KAAK;IAChC,MAAMK,EAAE,GAAG,CAAC,CAAC,GAAGrF,CAAC,GAAGf,MAAM,IAAI+C,UAAU,GAAGiD,KAAK;IAChD;IACA;IACA;IACA;IACApD,UAAU,CAACK,CAAC,CAAC,GAAGL,UAAU,CAACK,CAAC,GAAG,CAAC,CAAC,GAAGkD,EAAE;IACtCvD,UAAU,CAACK,CAAC,GAAG,CAAC,CAAC,GAAGL,UAAU,CAACK,CAAC,GAAG,CAAC,CAAC,GAAGmD,EAAE;IAC1CxD,UAAU,CAACK,CAAC,GAAG,CAAC,CAAC,GAAGL,UAAU,CAACK,CAAC,GAAG,CAAC,CAAC,GAAGkD,EAAE,GAAGpG,KAAK,GAAG+C,SAAS;IAC9DF,UAAU,CAACK,CAAC,GAAG,CAAC,CAAC,GAAGL,UAAU,CAACK,CAAC,GAAG,CAAC,CAAC,GAAGmD,EAAE,GAAGpG,MAAM,GAAG+C,UAAU;IAChEE,CAAC,IAAI,CAAC;EACV;EACA,OAAOL,UAAU;AACrB,CAAC,EAAEjK,kCAAkC,GAAG,SAASA,kCAAkCA,CAAC4J,IAAI,EAAE;EACtF,OAAOzJ,sBAAsB,CAAC,IAAI,EAAEvB,kCAAkC,EAAE,GAAG,CAAC,CAAC6L,SAAS,CAACb,IAAI,EAAEzJ,sBAAsB,CAAC,IAAI,EAAEjC,0BAA0B,EAAE,GAAG,EAAE4B,4BAA4B,CAAC,CAACkC,IAAI,CAAC,IAAI,CAAC,CAAC;AACxM,CAAC,EAAE/B,8BAA8B,GAAG,SAASA,8BAA8BA,CAACkF,MAAM,EAAE4H,KAAK,EAAE;EACvF,IAAI,IAAI,CAAC3D,cAAc,CAACjI,GAAG,CAAC4L,KAAK,CAAC,EAAE;IAChC;IACA5H,MAAM,CAACa,SAAS,CAAC0H,UAAU,CAAC,IAAI,CAACnE,gBAAgB,EAAE,IAAI,CAACH,cAAc,CAAC;EAC3E;AACJ,CAAC,EAAElJ,6BAA6B,GAAG,SAASA,6BAA6BA,CAACiF,MAAM,EAAE4H,KAAK,EAAE;EACrF,IAAI,CAAC,IAAI,CAAC3D,cAAc,CAACuB,OAAO,CAAC,CAAC,EAAE;IAChCxF,MAAM,CAACwI,qBAAqB,CAACZ,KAAK,EAAE,KAAK,EAAE;MACvChL,WAAW,EAAE,IAAI,CAACwH,gBAAgB;MAClCqC,iBAAiB,EAAE,IAAI,CAACxC,cAAc,CAACqC,WAAW,CAAC,CAAC;MACpDjC,UAAU,EAAE,IAAI,CAACC,oBAAoB;MACrC7H,gBAAgB,EAAE;IACtB,CAAC,CAAC;EACN,CAAC,MACI;IACDuD,MAAM,CAACa,SAAS,CAAC4H,mBAAmB,CAAC,IAAI,CAACrE,gBAAgB,CAAC;EAC/D;EACA,IAAI,CAACA,gBAAgB,GAAG,CAAC,CAAC;EAC1B,IAAI,CAACH,cAAc,GAAG,IAAI;EAC1B,IAAI,CAACK,oBAAoB,GAAG,EAAE;AAClC,CAAC;AACD5I,eAAe,CAACS,aAAa,GAAG,IAAI;AACpCT,eAAe,CAACa,eAAe,GAAG,CAAC;AACnCb,eAAe,CAACW,iBAAiB,GAAG,EAAE;AACtCX,eAAe,CAACgN,KAAK,GAAG,WAAW;AACnChN,eAAe,CAACiN,WAAW,GAAGxN,oBAAoB,CAACyK,SAAS;AAC5DlK,eAAe,CAAC0I,gBAAgB,GAAG,CAAC,CAAC;AACrC1I,eAAe,CAACuI,cAAc,GAAG,IAAI;AACrCvI,eAAe,CAAC4I,oBAAoB,GAAG,EAAE;AACzC,SAAS5I,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}