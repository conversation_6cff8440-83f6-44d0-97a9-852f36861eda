{"ast": null, "code": "/* Copyright 2022 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { shadow, Util } from \"../shared/utils\";\n// import {} from \"pdfjs-dist/legacy/build/pdf.mjs\";\nimport { getColorValues, getRGB } from \"../shared/display_utils\";\nexport class ColorManager {\n  get _colors() {\n    if (\n    // typeof PDFJSDev !== \"undefined\" &&\n    // PDFJSDev.test(\"LIB\") &&\n    typeof document === \"undefined\") {\n      return shadow(this, \"_colors\", ColorManager._colorsMapping);\n    }\n    const colors = new Map([[\"CanvasText\", null], [\"Canvas\", null]]);\n    getColorValues(colors);\n    return shadow(this, \"_colors\", colors);\n  }\n  /*\n   * In High Contrast Mode, the color on the screen is not always the\n   * real color used in the pdf.\n   * For example in some cases white can appear to be black but when saving\n   * we want to have white.\n   * @param {string} color\n   * @returns {Array<number>}\n   */\n  convert(color) {\n    const rgb = getRGB(color);\n    if (!window.matchMedia(\"(forced-colors: active)\").matches) {\n      return rgb;\n    }\n    for (const [name, RGB] of this._colors) {\n      if (RGB.every((x, i) => x === rgb[i])) {\n        return ColorManager._colorsMapping.get(name);\n      }\n    }\n    return rgb;\n  }\n  /*\n   * An input element must have its color value as a hex string\n   * and not as color name.\n   * So this function converts a name into an hex string.\n   * @param {string} name\n   * @returns {string}\n   */\n  getHexCode(name) {\n    const rgb = this._colors.get(name);\n    if (!rgb) {\n      return name;\n    }\n    // @ts-expect-error TS(2556):\n    return Util.makeHexColor(...rgb);\n  }\n}\nColorManager._colorsMapping = new Map([[\"CanvasText\", [0, 0, 0]], [\"Canvas\", [255, 255, 255]]]);", "map": {"version": 3, "names": ["shadow", "<PERSON><PERSON>", "getColorValues", "getRGB", "ColorManager", "_colors", "document", "_colorsMapping", "colors", "Map", "convert", "color", "rgb", "window", "matchMedia", "matches", "name", "RGB", "every", "x", "i", "get", "getHexCode", "makeHexColor"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-pdfviewer-common/dist/es/annotations/helpers/color-manager.js"], "sourcesContent": ["/* Copyright 2022 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { shadow, Util } from \"../shared/utils\";\n// import {} from \"pdfjs-dist/legacy/build/pdf.mjs\";\nimport { getColorValues, getRGB } from \"../shared/display_utils\";\nexport class ColorManager {\n    get _colors() {\n        if (\n        // typeof PDFJSDev !== \"undefined\" &&\n        // PDFJSDev.test(\"LIB\") &&\n        typeof document === \"undefined\") {\n            return shadow(this, \"_colors\", ColorManager._colorsMapping);\n        }\n        const colors = new Map([\n            [\"CanvasText\", null],\n            [\"Canvas\", null]\n        ]);\n        getColorValues(colors);\n        return shadow(this, \"_colors\", colors);\n    }\n    /*\n     * In High Contrast Mode, the color on the screen is not always the\n     * real color used in the pdf.\n     * For example in some cases white can appear to be black but when saving\n     * we want to have white.\n     * @param {string} color\n     * @returns {Array<number>}\n     */\n    convert(color) {\n        const rgb = getRGB(color);\n        if (!window.matchMedia(\"(forced-colors: active)\").matches) {\n            return rgb;\n        }\n        for (const [name, RGB] of this._colors) {\n            if (RGB.every((x, i) => x === rgb[i])) {\n                return ColorManager._colorsMapping.get(name);\n            }\n        }\n        return rgb;\n    }\n    /*\n     * An input element must have its color value as a hex string\n     * and not as color name.\n     * So this function converts a name into an hex string.\n     * @param {string} name\n     * @returns {string}\n     */\n    getHexCode(name) {\n        const rgb = this._colors.get(name);\n        if (!rgb) {\n            return name;\n        }\n        // @ts-expect-error TS(2556):\n        return Util.makeHexColor(...rgb);\n    }\n}\nColorManager._colorsMapping = new Map([\n    [\"CanvasText\", [0, 0, 0]],\n    [\"Canvas\", [255, 255, 255]]\n]);\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAM,EAAEC,IAAI,QAAQ,iBAAiB;AAC9C;AACA,SAASC,cAAc,EAAEC,MAAM,QAAQ,yBAAyB;AAChE,OAAO,MAAMC,YAAY,CAAC;EACtB,IAAIC,OAAOA,CAAA,EAAG;IACV;IACA;IACA;IACA,OAAOC,QAAQ,KAAK,WAAW,EAAE;MAC7B,OAAON,MAAM,CAAC,IAAI,EAAE,SAAS,EAAEI,YAAY,CAACG,cAAc,CAAC;IAC/D;IACA,MAAMC,MAAM,GAAG,IAAIC,GAAG,CAAC,CACnB,CAAC,YAAY,EAAE,IAAI,CAAC,EACpB,CAAC,QAAQ,EAAE,IAAI,CAAC,CACnB,CAAC;IACFP,cAAc,CAACM,MAAM,CAAC;IACtB,OAAOR,MAAM,CAAC,IAAI,EAAE,SAAS,EAAEQ,MAAM,CAAC;EAC1C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,OAAOA,CAACC,KAAK,EAAE;IACX,MAAMC,GAAG,GAAGT,MAAM,CAACQ,KAAK,CAAC;IACzB,IAAI,CAACE,MAAM,CAACC,UAAU,CAAC,yBAAyB,CAAC,CAACC,OAAO,EAAE;MACvD,OAAOH,GAAG;IACd;IACA,KAAK,MAAM,CAACI,IAAI,EAAEC,GAAG,CAAC,IAAI,IAAI,CAACZ,OAAO,EAAE;MACpC,IAAIY,GAAG,CAACC,KAAK,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,KAAKP,GAAG,CAACQ,CAAC,CAAC,CAAC,EAAE;QACnC,OAAOhB,YAAY,CAACG,cAAc,CAACc,GAAG,CAACL,IAAI,CAAC;MAChD;IACJ;IACA,OAAOJ,GAAG;EACd;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIU,UAAUA,CAACN,IAAI,EAAE;IACb,MAAMJ,GAAG,GAAG,IAAI,CAACP,OAAO,CAACgB,GAAG,CAACL,IAAI,CAAC;IAClC,IAAI,CAACJ,GAAG,EAAE;MACN,OAAOI,IAAI;IACf;IACA;IACA,OAAOf,IAAI,CAACsB,YAAY,CAAC,GAAGX,GAAG,CAAC;EACpC;AACJ;AACAR,YAAY,CAACG,cAAc,GAAG,IAAIE,GAAG,CAAC,CAClC,CAAC,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACzB,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAC9B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}