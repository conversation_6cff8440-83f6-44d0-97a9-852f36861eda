{"ast": null, "code": "import { default as elementStyles } from './element-styles';\nexport default function elementPadding(element) {\n  var ref = elementStyles(element, [\"paddingLeft\", \"paddingTop\"]);\n  var paddingLeft = ref.paddingLeft;\n  var paddingTop = ref.paddingTop;\n  return {\n    top: parseFloat(paddingTop),\n    left: parseFloat(paddingLeft)\n  };\n}", "map": {"version": 3, "names": ["default", "elementStyles", "elementPadding", "element", "ref", "paddingLeft", "paddingTop", "top", "parseFloat", "left"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/util/element-padding.js"], "sourcesContent": ["import { default as elementStyles } from './element-styles';\n\nexport default function elementPadding(element) {\n    var ref = elementStyles(element, [ \"paddingLeft\", \"paddingTop\" ]);\n    var paddingLeft = ref.paddingLeft;\n    var paddingTop = ref.paddingTop;\n    return {\n        top: parseFloat(paddingTop),\n        left: parseFloat(paddingLeft)\n    };\n}\n"], "mappings": "AAAA,SAASA,OAAO,IAAIC,aAAa,QAAQ,kBAAkB;AAE3D,eAAe,SAASC,cAAcA,CAACC,OAAO,EAAE;EAC5C,IAAIC,GAAG,GAAGH,aAAa,CAACE,OAAO,EAAE,CAAE,aAAa,EAAE,YAAY,CAAE,CAAC;EACjE,IAAIE,WAAW,GAAGD,GAAG,CAACC,WAAW;EACjC,IAAIC,UAAU,GAAGF,GAAG,CAACE,UAAU;EAC/B,OAAO;IACHC,GAAG,EAAEC,UAAU,CAACF,UAAU,CAAC;IAC3BG,IAAI,EAAED,UAAU,CAACH,WAAW;EAChC,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}