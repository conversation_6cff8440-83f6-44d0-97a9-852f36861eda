{"ast": null, "code": "import { OperationalServiceTypes } from '@iris/discovery.fe.client';\nconst w = window;\nconst config = {\n  logLevel: process.env.REACT_APP_LOG_LEVEL || w._env_ && w._env_.REACT_APP_LOG_LEVEL || 'debug',\n  discoveryURL: process.env.REACT_APP_DISCOVERY_URL || w._env_ && w._env_.REACT_APP_DISCOVERY_URL || 'https://discovery.dev.conarc.net',\n  api: {\n    [OperationalServiceTypes.UserService]: {\n      userNavigationMenu: `api/users/portal-menu`,\n      userPermissions: `api/permissions/logged-user-permission`,\n      userPreference: `api/users/logged-user-preference`,\n      evaluateTnc: `api/users/terms-and-conditions/evaluate`,\n      acceptTnc: `api/users/terms-and-conditions/accept`\n    },\n    [OperationalServiceTypes.FileManagementService]: {\n      getPublishedFilesColumns: `api/files/published-files/columns`,\n      getPublishedFiles: `api/files/published-files`,\n      getPublishedFilesCount: `api/files/published-files?count=true`,\n      downloadFile: `api/files/published-files/{id}`,\n      downloadZipFile: `api/files/published-files/zip`\n    },\n    [OperationalServiceTypes.PortalService]: {\n      getSubmittedFiles: `api/files/submitted`,\n      portalFilesUpload: `api/files`,\n      downloadPortalZipFile: `api/files/submitted/zip`,\n      downloadPortalFile: `api/files/submitted/{id}`,\n      siteSearch: `api/sites`,\n      softDeleteFile: `api/Files`\n    },\n    [OperationalServiceTypes.FileStorageService]: {\n      uploadFiles: `api/files`\n    },\n    [OperationalServiceTypes.MasterDataService]: {\n      themes: `api/themes`\n    },\n    [OperationalServiceTypes.Discovery]: {\n      getTenantContexts: `${process.env.REACT_APP_DISCOVERY_URL}api/DiscoveryContext/GetTenantContexts`\n    }\n  },\n  inputDebounceInterval: 500,\n  dateFormat: 'YYYY-MM-DD',\n  idleTime: 6000000,\n  clientId: '0oa4i1277i8t0vXxm0x7'\n};\nconst changeApi = (() => {\n  Object.keys(config.api).forEach(e => {\n    Object.keys(config.api[e]).forEach(v => {\n      config.api[e][v] = e + '/' + config.api[e][v];\n    });\n  });\n  return config;\n})();\nexport default changeApi;", "map": {"version": 3, "names": ["OperationalServiceTypes", "w", "window", "config", "logLevel", "process", "env", "REACT_APP_LOG_LEVEL", "_env_", "discoveryURL", "REACT_APP_DISCOVERY_URL", "api", "UserService", "userNavigationMenu", "userPermissions", "userPreference", "evaluateTnc", "acceptTnc", "FileManagementService", "getPublishedFilesColumns", "getPublishedFiles", "getPublishedFilesCount", "downloadFile", "downloadZipFile", "PortalService", "getSubmittedFiles", "portalFilesUpload", "downloadPortalZipFile", "downloadPortalFile", "siteSearch", "softDeleteFile", "FileStorageService", "uploadFiles", "MasterDataService", "themes", "Discovery", "getTenantContexts", "inputDebounceInterval", "dateFormat", "idleTime", "clientId", "changeApi", "Object", "keys", "for<PERSON>ach", "e", "v"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/src/app/utils/config/endpoints.ts"], "sourcesContent": ["import AppConfig from '@app/types/AppConfig';\r\nimport { OperationalServiceTypes } from '@iris/discovery.fe.client';\r\n\r\nconst w = window as any;\r\n\r\nconst config: AppConfig = {\r\n  logLevel: process.env.REACT_APP_LOG_LEVEL || (w._env_ && w._env_.REACT_APP_LOG_LEVEL) || 'debug',\r\n  discoveryURL: process.env.REACT_APP_DISCOVERY_URL || (w._env_ && w._env_.REACT_APP_DISCOVERY_URL) || 'https://discovery.dev.conarc.net',\r\n  api: {\r\n    [OperationalServiceTypes.UserService]: {\r\n      userNavigationMenu: `api/users/portal-menu`,\r\n      userPermissions: `api/permissions/logged-user-permission`,\r\n      userPreference: `api/users/logged-user-preference`,\r\n      evaluateTnc: `api/users/terms-and-conditions/evaluate`,\r\n      acceptTnc: `api/users/terms-and-conditions/accept`,\r\n    },\r\n    [OperationalServiceTypes.FileManagementService]: {\r\n      getPublishedFilesColumns: `api/files/published-files/columns`,\r\n      getPublishedFiles: `api/files/published-files`,\r\n      getPublishedFilesCount: `api/files/published-files?count=true`,\r\n      downloadFile: `api/files/published-files/{id}`,\r\n      downloadZipFile: `api/files/published-files/zip`,     \r\n    },\r\n    [OperationalServiceTypes.PortalService]: {\r\n      getSubmittedFiles: `api/files/submitted`,\r\n      portalFilesUpload: `api/files`,\r\n      downloadPortalZipFile: `api/files/submitted/zip`,\r\n      downloadPortalFile: `api/files/submitted/{id}`,\r\n      siteSearch: `api/sites`,\r\n      softDeleteFile:`api/Files`,\r\n    },\r\n    [OperationalServiceTypes.FileStorageService]: {\r\n      uploadFiles: `api/files`,\r\n    },\r\n    [OperationalServiceTypes.MasterDataService]: {\r\n      themes: `api/themes`,\r\n    },\r\n    [OperationalServiceTypes.Discovery]: {\r\n      getTenantContexts: `${process.env.REACT_APP_DISCOVERY_URL}api/DiscoveryContext/GetTenantContexts`,\r\n    }\r\n  },\r\n  inputDebounceInterval: 500,\r\n  dateFormat: 'YYYY-MM-DD',\r\n  idleTime: 6000000,\r\n  clientId: '0oa4i1277i8t0vXxm0x7',\r\n};\r\n\r\nconst changeApi: AppConfig = ((): AppConfig => {\r\n  Object.keys(config.api).forEach((e) => {\r\n    Object.keys((config.api as any)[e]).forEach((v) => {\r\n      (config.api as any)[e][v] = e + '/' + (config.api as any)[e][v];\r\n    });\r\n  });\r\n  return config;\r\n})();\r\n\r\nexport default changeApi;\r\n"], "mappings": "AACA,SAASA,uBAAuB,QAAQ,2BAA2B;AAEnE,MAAMC,CAAC,GAAGC,MAAa;AAEvB,MAAMC,MAAiB,GAAG;EACxBC,QAAQ,EAAEC,OAAO,CAACC,GAAG,CAACC,mBAAmB,IAAKN,CAAC,CAACO,KAAK,IAAIP,CAAC,CAACO,KAAK,CAACD,mBAAoB,IAAI,OAAO;EAChGE,YAAY,EAAEJ,OAAO,CAACC,GAAG,CAACI,uBAAuB,IAAKT,CAAC,CAACO,KAAK,IAAIP,CAAC,CAACO,KAAK,CAACE,uBAAwB,IAAI,kCAAkC;EACvIC,GAAG,EAAE;IACH,CAACX,uBAAuB,CAACY,WAAW,GAAG;MACrCC,kBAAkB,EAAE,uBAAuB;MAC3CC,eAAe,EAAE,wCAAwC;MACzDC,cAAc,EAAE,kCAAkC;MAClDC,WAAW,EAAE,yCAAyC;MACtDC,SAAS,EAAE;IACb,CAAC;IACD,CAACjB,uBAAuB,CAACkB,qBAAqB,GAAG;MAC/CC,wBAAwB,EAAE,mCAAmC;MAC7DC,iBAAiB,EAAE,2BAA2B;MAC9CC,sBAAsB,EAAE,sCAAsC;MAC9DC,YAAY,EAAE,gCAAgC;MAC9CC,eAAe,EAAE;IACnB,CAAC;IACD,CAACvB,uBAAuB,CAACwB,aAAa,GAAG;MACvCC,iBAAiB,EAAE,qBAAqB;MACxCC,iBAAiB,EAAE,WAAW;MAC9BC,qBAAqB,EAAE,yBAAyB;MAChDC,kBAAkB,EAAE,0BAA0B;MAC9CC,UAAU,EAAE,WAAW;MACvBC,cAAc,EAAC;IACjB,CAAC;IACD,CAAC9B,uBAAuB,CAAC+B,kBAAkB,GAAG;MAC5CC,WAAW,EAAE;IACf,CAAC;IACD,CAAChC,uBAAuB,CAACiC,iBAAiB,GAAG;MAC3CC,MAAM,EAAE;IACV,CAAC;IACD,CAAClC,uBAAuB,CAACmC,SAAS,GAAG;MACnCC,iBAAiB,EAAE,GAAG/B,OAAO,CAACC,GAAG,CAACI,uBAAuB;IAC3D;EACF,CAAC;EACD2B,qBAAqB,EAAE,GAAG;EAC1BC,UAAU,EAAE,YAAY;EACxBC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE;AACZ,CAAC;AAED,MAAMC,SAAoB,GAAG,CAAC,MAAiB;EAC7CC,MAAM,CAACC,IAAI,CAACxC,MAAM,CAACQ,GAAG,CAAC,CAACiC,OAAO,CAAEC,CAAC,IAAK;IACrCH,MAAM,CAACC,IAAI,CAAExC,MAAM,CAACQ,GAAG,CAASkC,CAAC,CAAC,CAAC,CAACD,OAAO,CAAEE,CAAC,IAAK;MAChD3C,MAAM,CAACQ,GAAG,CAASkC,CAAC,CAAC,CAACC,CAAC,CAAC,GAAGD,CAAC,GAAG,GAAG,GAAI1C,MAAM,CAACQ,GAAG,CAASkC,CAAC,CAAC,CAACC,CAAC,CAAC;IACjE,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO3C,MAAM;AACf,CAAC,EAAE,CAAC;AAEJ,eAAesC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}