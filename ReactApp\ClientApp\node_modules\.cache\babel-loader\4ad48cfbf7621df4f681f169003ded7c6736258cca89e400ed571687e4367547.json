{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as t from \"react\";\nimport s from \"prop-types\";\nimport { classNames as l } from \"@progress/kendo-react-common\";\nconst c = t.forwardRef((e, m) => {\n    const a = t.useRef(null),\n      r = t.useRef(null),\n      i = t.useCallback(() => {\n        r.current && r.current.focus();\n      }, [r]);\n    return t.useImperativeHandle(a, () => ({\n      element: r.current,\n      focus: i,\n      props: e\n    })), t.useImperativeHandle(m, () => a.current), /* @__PURE__ */t.createElement(\"li\", {\n      ref: r,\n      id: e.id,\n      style: e.style,\n      className: l(\"k-breadcrumb-item\", {\n        \"k-breadcrumb-root-item\": e.isFirstItem,\n        \"k-breadcrumb-last-item\": e.isLastItem\n      }, e.className)\n    }, e.children);\n  }),\n  n = {\n    id: s.string,\n    className: s.string,\n    children: s.any,\n    style: s.object\n  };\nc.displayName = \"KendoReactBreadcrumbListItem\";\nc.propTypes = n;\nexport { c as BreadcrumbListItem };", "map": {"version": 3, "names": ["t", "s", "classNames", "l", "c", "forwardRef", "e", "m", "a", "useRef", "r", "i", "useCallback", "current", "focus", "useImperativeHandle", "element", "props", "createElement", "ref", "id", "style", "className", "isFirstItem", "isLastItem", "children", "n", "string", "any", "object", "displayName", "propTypes", "BreadcrumbListItem"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/breadcrumb/BreadcrumbListItem.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as t from \"react\";\nimport s from \"prop-types\";\nimport { classNames as l } from \"@progress/kendo-react-common\";\nconst c = t.forwardRef(\n  (e, m) => {\n    const a = t.useRef(null), r = t.useRef(null), i = t.useCallback(() => {\n      r.current && r.current.focus();\n    }, [r]);\n    return t.useImperativeHandle(a, () => ({\n      element: r.current,\n      focus: i,\n      props: e\n    })), t.useImperativeHandle(\n      m,\n      () => a.current\n    ), /* @__PURE__ */ t.createElement(\n      \"li\",\n      {\n        ref: r,\n        id: e.id,\n        style: e.style,\n        className: l(\n          \"k-breadcrumb-item\",\n          {\n            \"k-breadcrumb-root-item\": e.isFirstItem,\n            \"k-breadcrumb-last-item\": e.isLastItem\n          },\n          e.className\n        )\n      },\n      e.children\n    );\n  }\n), n = {\n  id: s.string,\n  className: s.string,\n  children: s.any,\n  style: s.object\n};\nc.displayName = \"KendoReactBreadcrumbListItem\";\nc.propTypes = n;\nexport {\n  c as BreadcrumbListItem\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AAC9D,MAAMC,CAAC,GAAGJ,CAAC,CAACK,UAAU,CACpB,CAACC,CAAC,EAAEC,CAAC,KAAK;IACR,MAAMC,CAAC,GAAGR,CAAC,CAACS,MAAM,CAAC,IAAI,CAAC;MAAEC,CAAC,GAAGV,CAAC,CAACS,MAAM,CAAC,IAAI,CAAC;MAAEE,CAAC,GAAGX,CAAC,CAACY,WAAW,CAAC,MAAM;QACpEF,CAAC,CAACG,OAAO,IAAIH,CAAC,CAACG,OAAO,CAACC,KAAK,CAAC,CAAC;MAChC,CAAC,EAAE,CAACJ,CAAC,CAAC,CAAC;IACP,OAAOV,CAAC,CAACe,mBAAmB,CAACP,CAAC,EAAE,OAAO;MACrCQ,OAAO,EAAEN,CAAC,CAACG,OAAO;MAClBC,KAAK,EAAEH,CAAC;MACRM,KAAK,EAAEX;IACT,CAAC,CAAC,CAAC,EAAEN,CAAC,CAACe,mBAAmB,CACxBR,CAAC,EACD,MAAMC,CAAC,CAACK,OACV,CAAC,EAAE,eAAgBb,CAAC,CAACkB,aAAa,CAChC,IAAI,EACJ;MACEC,GAAG,EAAET,CAAC;MACNU,EAAE,EAAEd,CAAC,CAACc,EAAE;MACRC,KAAK,EAAEf,CAAC,CAACe,KAAK;MACdC,SAAS,EAAEnB,CAAC,CACV,mBAAmB,EACnB;QACE,wBAAwB,EAAEG,CAAC,CAACiB,WAAW;QACvC,wBAAwB,EAAEjB,CAAC,CAACkB;MAC9B,CAAC,EACDlB,CAAC,CAACgB,SACJ;IACF,CAAC,EACDhB,CAAC,CAACmB,QACJ,CAAC;EACH,CACF,CAAC;EAAEC,CAAC,GAAG;IACLN,EAAE,EAAEnB,CAAC,CAAC0B,MAAM;IACZL,SAAS,EAAErB,CAAC,CAAC0B,MAAM;IACnBF,QAAQ,EAAExB,CAAC,CAAC2B,GAAG;IACfP,KAAK,EAAEpB,CAAC,CAAC4B;EACX,CAAC;AACDzB,CAAC,CAAC0B,WAAW,GAAG,8BAA8B;AAC9C1B,CAAC,CAAC2B,SAAS,GAAGL,CAAC;AACf,SACEtB,CAAC,IAAI4B,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}