{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as d from \"react\";\nimport a from \"prop-types\";\nimport { getScrollbarWidth as f, canUseDOM as y, classNames as m, Draggable as x } from \"@progress/kendo-react-common\";\nimport { ResizeHandlers as E } from \"./ResizeHandlers.mjs\";\nconst g = 200,\n  p = class p extends d.Component {\n    constructor() {\n      super(...arguments), this.state = {\n        rtl: !1,\n        visibleHint: !1\n      }, this.oldSize = {}, this.draggable = null, this.dragging = !1, this.resizing = !1, this.element = null, this.hintElement = null, this.ignoreDrag = !1, this.pressOffset = {\n        x: 0,\n        y: 0\n      }, this.pressXY = {\n        x: 0,\n        y: 0\n      }, this.currentTranslate = {\n        x: 0,\n        y: 0\n      }, this.preventDataOps = void 0, this.handleResize = (t, e) => {\n        if (e.end) {\n          this.handleRelease();\n          return;\n        }\n        if (!this.element || !this.hintElement) return;\n        const n = t.clientX,\n          l = t.clientY;\n        this.resizing = !0;\n        const r = (e.direction !== \"ns\" ? n - this.pressXY.x : 0) * (this.state.rtl ? -1 : 1),\n          s = e.direction !== \"ew\" ? l - this.pressXY.y : 0;\n        if (this.dragElement && (this.state.rtl ? this.dragElement.style.marginLeft = -r + \"px\" : this.dragElement.style.marginRight = -r + \"px\", this.dragElement.style.height = `calc(100% + ${s}px)`), this.hintElement.classList.add(\"k-layout-item-hint-resize\"), this.preventDataOps) return;\n        let i = 0,\n          o = 0;\n        const h = this.element.getBoundingClientRect();\n        r > h.width / this.props.defaultPosition.colSpan / 3 && (i = 1), r < -h.width / this.props.defaultPosition.colSpan / 1.25 && (i = -1), s > h.height / this.props.defaultPosition.rowSpan / 3 && (o = 1), s < -h.height / this.props.defaultPosition.rowSpan / 1.25 && (o = -1), (i !== 0 || o !== 0) && this.props.update(this.props.index, 0, 0, o, i);\n      }, this.handlePress = t => {\n        if (!this.dragElement) return;\n        if (this.pressXY = {\n          x: t.event.clientX,\n          y: t.event.clientY\n        }, this.ignoreDrag = !1, this.props.ignoreDrag && this.props.ignoreDrag(t.event.originalEvent)) {\n          this.ignoreDrag = !0;\n          return;\n        }\n        this.element && (this.element.style.zIndex = \"10\", this.setState({\n          visibleHint: !0\n        })), this.dragElement.classList.remove(\"k-cursor-move\"), this.dragElement.classList.add(\"k-cursor-grabbing\");\n        const e = this.dragElement.getBoundingClientRect();\n        this.pressOffset = {\n          x: t.event.clientX - e.x,\n          y: t.event.clientY - e.y\n        }, this.props.onPress();\n      }, this.handleDrag = t => {\n        var i;\n        if (this.ignoreDrag) return;\n        const e = this.dragElement;\n        if (t.event.originalEvent.defaultPrevented || !e) return;\n        this.dragging = !0, t.event.originalEvent.preventDefault();\n        const n = e.getBoundingClientRect();\n        if (this.currentTranslate = {\n          x: t.event.clientX - n.x - this.pressOffset.x + this.currentTranslate.x,\n          y: t.event.clientY - n.y - this.pressOffset.y + this.currentTranslate.y\n        }, e.style.transform = `translate(${this.currentTranslate.x}px, ${this.currentTranslate.y}px)`, e.style.transition = \"transform 0s\", this.preventDataOps) return;\n        let l = 0,\n          r = 0;\n        this.currentTranslate.y > 0.7 * n.height / this.props.defaultPosition.rowSpan && (r = 1), this.currentTranslate.y < 0.7 * -n.height / this.props.defaultPosition.rowSpan && (r = -1), this.currentTranslate.x > 0.7 * n.width / this.props.defaultPosition.colSpan && (l = 1), this.currentTranslate.x < 0.7 * -n.width / this.props.defaultPosition.colSpan && (l = -1), this.props.update(this.props.index, r, this.state.rtl ? -l : l, 0, 0);\n        const s = (i = this.element) == null ? void 0 : i.closest(\".k-tilelayout\");\n        if (s && this.hintElement) {\n          const o = s.getBoundingClientRect(),\n            h = f() || 50;\n          t.event.clientX < o.left - h || t.event.clientX > o.right - h || t.event.clientY < o.top || t.event.clientY > o.bottom ? this.hintElement.style.display = \"none\" : this.hintElement.style.display = \"block\";\n        }\n      }, this.handleRelease = () => {\n        this.dragging = this.resizing = !1, this.currentTranslate = {\n          x: 0,\n          y: 0\n        }, this.element && this.hintElement && (this.element.style.zIndex = \"1\", this.hintElement.classList.remove(\"k-layout-item-hint-resize\"), this.setState({\n          visibleHint: !1\n        }));\n        const t = this.dragElement;\n        t && (t.style.transform = \"translate(0px, 0px)\", t.style.transition = `transform ${g}ms cubic-bezier(0.2, 0, 0, 1) 0s`, t.style.marginRight = \"0px\", t.style.marginLeft = \"0px\", t.style.height = \"100%\", t.classList.remove(\"k-cursor-grabbing\"), t.classList.add(\"k-cursor-move\")), this.props.onRelease();\n      }, this.handleMouseDown = () => {\n        this.setInitialHintPosition();\n      }, this.handleMouseUp = () => {\n        setTimeout(() => {\n          this.setInitialHintPosition();\n        }, 100);\n      }, this.calcNewHintPosition = () => {\n        if (!this.dragElement || !this.hintElement) return;\n        const t = this.dragElement.getBoundingClientRect(),\n          e = t.top + this.currentTranslate.y,\n          n = t.left + this.currentTranslate.x;\n        this.hintElement.style.top = `${e}px`, this.hintElement.style.left = `${n}px`, this.hintElement.style.display = \"block\";\n      };\n    }\n    get reorderable() {\n      return this.props.reorderable !== void 0 ? this.props.reorderable : p.defaultProps.reorderable;\n    }\n    get dragElement() {\n      return this.draggable ? this.draggable.element : void 0;\n    }\n    componentDidMount() {\n      this.element && (getComputedStyle(this.element).direction === \"rtl\" && this.setState({\n        rtl: !0\n      }), this.hintElement && (this.hintElement.style.height = this.element.offsetHeight + \"px\", this.hintElement.style.width = this.element.offsetWidth + \"px\"));\n    }\n    render() {\n      y && clearTimeout && typeof clearTimeout == \"function\" && (clearTimeout(this.preventDataOps), this.preventDataOps = window.setTimeout(() => {\n        this.preventDataOps = void 0;\n      }, 200));\n      const t = this.props.defaultPosition,\n        e = this.props.resizable !== void 0 ? this.props.resizable : p.defaultProps.resizable,\n        n = {\n          gridColumnStart: t.col,\n          gridColumnEnd: `span ${t.colSpan}`,\n          gridRowStart: t.row,\n          gridRowEnd: `span ${t.rowSpan}`,\n          outline: \"none\",\n          order: t.order,\n          display: \"block\",\n          ...this.props.hintStyle\n        },\n        l = {\n          gridColumnStart: t.col,\n          gridColumnEnd: `span ${t.colSpan}`,\n          gridRowStart: t.row,\n          gridRowEnd: `span ${t.rowSpan}`,\n          order: t.order\n        },\n        r = /* @__PURE__ */d.createElement(\"div\", {\n          ref: s => {\n            this.draggable = s ? {\n              element: s\n            } : null;\n          },\n          role: \"listitem\",\n          tabIndex: 0,\n          \"aria-labelledby\": typeof this.props.header == \"string\" ? this.props.header : `tile-${this.props.index}`,\n          \"aria-keyshortcuts\": \"Enter\",\n          className: m(\"k-tilelayout-item k-card\", {\n            \"k-cursor-move\": this.reorderable\n          }, this.props.className),\n          style: {\n            height: \"100%\",\n            ...l,\n            ...this.props.style\n          },\n          onMouseDown: this.handleMouseDown,\n          onMouseUp: this.handleMouseUp\n        }, this.props.children, /* @__PURE__ */d.createElement(E, {\n          onPress: this.handlePress,\n          onResize: this.handleResize,\n          resizable: e,\n          rtl: this.state.rtl\n        }));\n      return /* @__PURE__ */d.createElement(d.Fragment, null, this.state.visibleHint && /* @__PURE__ */d.createElement(\"div\", {\n        ref: s => {\n          this.hintElement = s;\n        },\n        style: {\n          position: \"fixed\",\n          ...n\n        },\n        className: m(\"k-layout-item-hint\", this.props.hintClassName)\n      }), /* @__PURE__ */d.createElement(x, {\n        ref: s => {\n          this.draggable = s, this.element = s ? s.element : null;\n        },\n        onDrag: this.props.reorderable ? this.handleDrag : void 0,\n        onRelease: this.props.reorderable ? this.handleRelease : void 0,\n        onPress: this.props.reorderable ? this.handlePress : void 0\n      }, r));\n    }\n    /**\n     * @hidden\n     */\n    getSnapshotBeforeUpdate(t) {\n      return this.oldSize = {}, this.dragElement && (this.oldSize = this.dragElement.getBoundingClientRect()), null;\n    }\n    /**\n     * @hidden\n     */\n    setInitialHintPosition() {\n      if (this.element && this.hintElement) {\n        const t = this.element.getBoundingClientRect();\n        this.hintElement.style.top = t.top + \"px\", this.hintElement.style.left = t.left + \"px\", this.hintElement.style.height = this.element.offsetHeight + \"px\", this.hintElement.style.width = this.element.offsetWidth + \"px\";\n      }\n    }\n    /**\n     * @hidden\n     */\n    componentDidUpdate(t) {\n      const e = this.dragElement;\n      if (!e) return;\n      const n = e.getBoundingClientRect(),\n        l = this.oldSize;\n      if (this.resizing) {\n        const i = n.width - l.width;\n        if (this.state.rtl) {\n          const c = parseFloat(e.style.marginLeft || \"0\");\n          e.style.marginLeft = c - i + \"px\";\n        } else {\n          const c = parseFloat(e.style.marginRight || \"0\");\n          e.style.marginRight = c + i + \"px\";\n        }\n        this.pressXY.x += this.state.rtl ? -i : i;\n        const o = n.height - l.height,\n          h = parseFloat(e.style.height.substring(12));\n        e.style.height = `calc(100% + ${h + o}px)`, this.pressXY.y += o;\n      }\n      const r = l.left - n.left,\n        s = l.top - n.top;\n      if (!(r === 0 && s === 0)) {\n        if (this.dragging) {\n          (t.defaultPosition.order !== this.props.defaultPosition.order || t.defaultPosition.col !== this.props.defaultPosition.col) && (this.currentTranslate.x = 0, this.currentTranslate.y = 0, e.style.transform = \"\", this.calcNewHintPosition());\n          return;\n        }\n        Math.abs(s) < 15 && Math.abs(r) < 15 || requestAnimationFrame(() => {\n          const i = this.element;\n          i && (i.style.transform = `translate(${r}px, ${s}px)`, i.style.transition = \"transform 0s\", requestAnimationFrame(() => {\n            i.style.transform = \"\", i.style.transition = `transform ${g}ms cubic-bezier(0.2, 0, 0, 1) 0s`;\n          }));\n        });\n      }\n    }\n  };\np.propTypes = {\n  defaultPosition: a.object.isRequired,\n  style: a.object,\n  className: a.string,\n  hintStyle: a.object,\n  hintClassName: a.string,\n  header: a.any,\n  body: a.any,\n  item: a.any,\n  resizable: a.oneOf([\"horizontal\", \"vertical\", !0, !1]),\n  reorderable: a.bool\n}, p.displayName = \"KendoTileLayoutItem\", p.defaultProps = {\n  resizable: !0,\n  reorderable: !0\n};\nlet u = p;\nexport { u as InternalTile };", "map": {"version": 3, "names": ["d", "a", "getScrollbarWidth", "f", "canUseDOM", "y", "classNames", "m", "Draggable", "x", "ResizeHandlers", "E", "g", "p", "Component", "constructor", "arguments", "state", "rtl", "visibleHint", "oldSize", "draggable", "dragging", "resizing", "element", "hintElement", "ignoreDrag", "pressOffset", "pressXY", "currentTranslate", "preventDataOps", "handleResize", "t", "e", "end", "handleRelease", "n", "clientX", "l", "clientY", "r", "direction", "s", "dragElement", "style", "marginLeft", "marginRight", "height", "classList", "add", "i", "o", "h", "getBoundingClientRect", "width", "props", "defaultPosition", "colSpan", "rowSpan", "update", "index", "handlePress", "event", "originalEvent", "zIndex", "setState", "remove", "onPress", "handleDrag", "defaultPrevented", "preventDefault", "transform", "transition", "closest", "left", "right", "top", "bottom", "display", "onRelease", "handleMouseDown", "setInitialHintPosition", "handleMouseUp", "setTimeout", "calcNewHintPosition", "reorderable", "defaultProps", "componentDidMount", "getComputedStyle", "offsetHeight", "offsetWidth", "render", "clearTimeout", "window", "resizable", "gridColumnStart", "col", "gridColumnEnd", "gridRowStart", "row", "gridRowEnd", "outline", "order", "hintStyle", "createElement", "ref", "role", "tabIndex", "header", "className", "onMouseDown", "onMouseUp", "children", "onResize", "Fragment", "position", "hintClassName", "onDrag", "getSnapshotBeforeUpdate", "componentDidUpdate", "c", "parseFloat", "substring", "Math", "abs", "requestAnimationFrame", "propTypes", "object", "isRequired", "string", "any", "body", "item", "oneOf", "bool", "displayName", "u", "InternalTile"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/tilelayout/InternalTile.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as d from \"react\";\nimport a from \"prop-types\";\nimport { getScrollbarWidth as f, canUseDOM as y, classNames as m, Draggable as x } from \"@progress/kendo-react-common\";\nimport { ResizeHandlers as E } from \"./ResizeHandlers.mjs\";\nconst g = 200, p = class p extends d.Component {\n  constructor() {\n    super(...arguments), this.state = {\n      rtl: !1,\n      visibleHint: !1\n    }, this.oldSize = {}, this.draggable = null, this.dragging = !1, this.resizing = !1, this.element = null, this.hintElement = null, this.ignoreDrag = !1, this.pressOffset = { x: 0, y: 0 }, this.pressXY = { x: 0, y: 0 }, this.currentTranslate = { x: 0, y: 0 }, this.preventDataOps = void 0, this.handleResize = (t, e) => {\n      if (e.end) {\n        this.handleRelease();\n        return;\n      }\n      if (!this.element || !this.hintElement)\n        return;\n      const n = t.clientX, l = t.clientY;\n      this.resizing = !0;\n      const r = (e.direction !== \"ns\" ? n - this.pressXY.x : 0) * (this.state.rtl ? -1 : 1), s = e.direction !== \"ew\" ? l - this.pressXY.y : 0;\n      if (this.dragElement && (this.state.rtl ? this.dragElement.style.marginLeft = -r + \"px\" : this.dragElement.style.marginRight = -r + \"px\", this.dragElement.style.height = `calc(100% + ${s}px)`), this.hintElement.classList.add(\"k-layout-item-hint-resize\"), this.preventDataOps)\n        return;\n      let i = 0, o = 0;\n      const h = this.element.getBoundingClientRect();\n      r > h.width / this.props.defaultPosition.colSpan / 3 && (i = 1), r < -h.width / this.props.defaultPosition.colSpan / 1.25 && (i = -1), s > h.height / this.props.defaultPosition.rowSpan / 3 && (o = 1), s < -h.height / this.props.defaultPosition.rowSpan / 1.25 && (o = -1), (i !== 0 || o !== 0) && this.props.update(this.props.index, 0, 0, o, i);\n    }, this.handlePress = (t) => {\n      if (!this.dragElement)\n        return;\n      if (this.pressXY = {\n        x: t.event.clientX,\n        y: t.event.clientY\n      }, this.ignoreDrag = !1, this.props.ignoreDrag && this.props.ignoreDrag(t.event.originalEvent)) {\n        this.ignoreDrag = !0;\n        return;\n      }\n      this.element && (this.element.style.zIndex = \"10\", this.setState({ visibleHint: !0 })), this.dragElement.classList.remove(\"k-cursor-move\"), this.dragElement.classList.add(\"k-cursor-grabbing\");\n      const e = this.dragElement.getBoundingClientRect();\n      this.pressOffset = {\n        x: t.event.clientX - e.x,\n        y: t.event.clientY - e.y\n      }, this.props.onPress();\n    }, this.handleDrag = (t) => {\n      var i;\n      if (this.ignoreDrag)\n        return;\n      const e = this.dragElement;\n      if (t.event.originalEvent.defaultPrevented || !e)\n        return;\n      this.dragging = !0, t.event.originalEvent.preventDefault();\n      const n = e.getBoundingClientRect();\n      if (this.currentTranslate = {\n        x: t.event.clientX - n.x - this.pressOffset.x + this.currentTranslate.x,\n        y: t.event.clientY - n.y - this.pressOffset.y + this.currentTranslate.y\n      }, e.style.transform = `translate(${this.currentTranslate.x}px, ${this.currentTranslate.y}px)`, e.style.transition = \"transform 0s\", this.preventDataOps)\n        return;\n      let l = 0, r = 0;\n      this.currentTranslate.y > 0.7 * n.height / this.props.defaultPosition.rowSpan && (r = 1), this.currentTranslate.y < 0.7 * -n.height / this.props.defaultPosition.rowSpan && (r = -1), this.currentTranslate.x > 0.7 * n.width / this.props.defaultPosition.colSpan && (l = 1), this.currentTranslate.x < 0.7 * -n.width / this.props.defaultPosition.colSpan && (l = -1), this.props.update(this.props.index, r, this.state.rtl ? -l : l, 0, 0);\n      const s = (i = this.element) == null ? void 0 : i.closest(\".k-tilelayout\");\n      if (s && this.hintElement) {\n        const o = s.getBoundingClientRect(), h = f() || 50;\n        t.event.clientX < o.left - h || t.event.clientX > o.right - h || t.event.clientY < o.top || t.event.clientY > o.bottom ? this.hintElement.style.display = \"none\" : this.hintElement.style.display = \"block\";\n      }\n    }, this.handleRelease = () => {\n      this.dragging = this.resizing = !1, this.currentTranslate = { x: 0, y: 0 }, this.element && this.hintElement && (this.element.style.zIndex = \"1\", this.hintElement.classList.remove(\"k-layout-item-hint-resize\"), this.setState({ visibleHint: !1 }));\n      const t = this.dragElement;\n      t && (t.style.transform = \"translate(0px, 0px)\", t.style.transition = `transform ${g}ms cubic-bezier(0.2, 0, 0, 1) 0s`, t.style.marginRight = \"0px\", t.style.marginLeft = \"0px\", t.style.height = \"100%\", t.classList.remove(\"k-cursor-grabbing\"), t.classList.add(\"k-cursor-move\")), this.props.onRelease();\n    }, this.handleMouseDown = () => {\n      this.setInitialHintPosition();\n    }, this.handleMouseUp = () => {\n      setTimeout(() => {\n        this.setInitialHintPosition();\n      }, 100);\n    }, this.calcNewHintPosition = () => {\n      if (!this.dragElement || !this.hintElement)\n        return;\n      const t = this.dragElement.getBoundingClientRect(), e = t.top + this.currentTranslate.y, n = t.left + this.currentTranslate.x;\n      this.hintElement.style.top = `${e}px`, this.hintElement.style.left = `${n}px`, this.hintElement.style.display = \"block\";\n    };\n  }\n  get reorderable() {\n    return this.props.reorderable !== void 0 ? this.props.reorderable : p.defaultProps.reorderable;\n  }\n  get dragElement() {\n    return this.draggable ? this.draggable.element : void 0;\n  }\n  componentDidMount() {\n    this.element && (getComputedStyle(this.element).direction === \"rtl\" && this.setState({\n      rtl: !0\n    }), this.hintElement && (this.hintElement.style.height = this.element.offsetHeight + \"px\", this.hintElement.style.width = this.element.offsetWidth + \"px\"));\n  }\n  render() {\n    y && clearTimeout && typeof clearTimeout == \"function\" && (clearTimeout(this.preventDataOps), this.preventDataOps = window.setTimeout(() => {\n      this.preventDataOps = void 0;\n    }, 200));\n    const t = this.props.defaultPosition, e = this.props.resizable !== void 0 ? this.props.resizable : p.defaultProps.resizable, n = {\n      gridColumnStart: t.col,\n      gridColumnEnd: `span ${t.colSpan}`,\n      gridRowStart: t.row,\n      gridRowEnd: `span ${t.rowSpan}`,\n      outline: \"none\",\n      order: t.order,\n      display: \"block\",\n      ...this.props.hintStyle\n    }, l = {\n      gridColumnStart: t.col,\n      gridColumnEnd: `span ${t.colSpan}`,\n      gridRowStart: t.row,\n      gridRowEnd: `span ${t.rowSpan}`,\n      order: t.order\n    }, r = /* @__PURE__ */ d.createElement(\n      \"div\",\n      {\n        ref: (s) => {\n          this.draggable = s ? { element: s } : null;\n        },\n        role: \"listitem\",\n        tabIndex: 0,\n        \"aria-labelledby\": typeof this.props.header == \"string\" ? this.props.header : `tile-${this.props.index}`,\n        \"aria-keyshortcuts\": \"Enter\",\n        className: m(\n          \"k-tilelayout-item k-card\",\n          { \"k-cursor-move\": this.reorderable },\n          this.props.className\n        ),\n        style: { height: \"100%\", ...l, ...this.props.style },\n        onMouseDown: this.handleMouseDown,\n        onMouseUp: this.handleMouseUp\n      },\n      this.props.children,\n      /* @__PURE__ */ d.createElement(\n        E,\n        {\n          onPress: this.handlePress,\n          onResize: this.handleResize,\n          resizable: e,\n          rtl: this.state.rtl\n        }\n      )\n    );\n    return /* @__PURE__ */ d.createElement(d.Fragment, null, this.state.visibleHint && /* @__PURE__ */ d.createElement(\n      \"div\",\n      {\n        ref: (s) => {\n          this.hintElement = s;\n        },\n        style: { position: \"fixed\", ...n },\n        className: m(\"k-layout-item-hint\", this.props.hintClassName)\n      }\n    ), /* @__PURE__ */ d.createElement(\n      x,\n      {\n        ref: (s) => {\n          this.draggable = s, this.element = s ? s.element : null;\n        },\n        onDrag: this.props.reorderable ? this.handleDrag : void 0,\n        onRelease: this.props.reorderable ? this.handleRelease : void 0,\n        onPress: this.props.reorderable ? this.handlePress : void 0\n      },\n      r\n    ));\n  }\n  /**\n   * @hidden\n   */\n  getSnapshotBeforeUpdate(t) {\n    return this.oldSize = {}, this.dragElement && (this.oldSize = this.dragElement.getBoundingClientRect()), null;\n  }\n  /**\n   * @hidden\n   */\n  setInitialHintPosition() {\n    if (this.element && this.hintElement) {\n      const t = this.element.getBoundingClientRect();\n      this.hintElement.style.top = t.top + \"px\", this.hintElement.style.left = t.left + \"px\", this.hintElement.style.height = this.element.offsetHeight + \"px\", this.hintElement.style.width = this.element.offsetWidth + \"px\";\n    }\n  }\n  /**\n   * @hidden\n   */\n  componentDidUpdate(t) {\n    const e = this.dragElement;\n    if (!e)\n      return;\n    const n = e.getBoundingClientRect(), l = this.oldSize;\n    if (this.resizing) {\n      const i = n.width - l.width;\n      if (this.state.rtl) {\n        const c = parseFloat(e.style.marginLeft || \"0\");\n        e.style.marginLeft = c - i + \"px\";\n      } else {\n        const c = parseFloat(e.style.marginRight || \"0\");\n        e.style.marginRight = c + i + \"px\";\n      }\n      this.pressXY.x += this.state.rtl ? -i : i;\n      const o = n.height - l.height, h = parseFloat(e.style.height.substring(12));\n      e.style.height = `calc(100% + ${h + o}px)`, this.pressXY.y += o;\n    }\n    const r = l.left - n.left, s = l.top - n.top;\n    if (!(r === 0 && s === 0)) {\n      if (this.dragging) {\n        (t.defaultPosition.order !== this.props.defaultPosition.order || t.defaultPosition.col !== this.props.defaultPosition.col) && (this.currentTranslate.x = 0, this.currentTranslate.y = 0, e.style.transform = \"\", this.calcNewHintPosition());\n        return;\n      }\n      Math.abs(s) < 15 && Math.abs(r) < 15 || requestAnimationFrame(() => {\n        const i = this.element;\n        i && (i.style.transform = `translate(${r}px, ${s}px)`, i.style.transition = \"transform 0s\", requestAnimationFrame(() => {\n          i.style.transform = \"\", i.style.transition = `transform ${g}ms cubic-bezier(0.2, 0, 0, 1) 0s`;\n        }));\n      });\n    }\n  }\n};\np.propTypes = {\n  defaultPosition: a.object.isRequired,\n  style: a.object,\n  className: a.string,\n  hintStyle: a.object,\n  hintClassName: a.string,\n  header: a.any,\n  body: a.any,\n  item: a.any,\n  resizable: a.oneOf([\"horizontal\", \"vertical\", !0, !1]),\n  reorderable: a.bool\n}, p.displayName = \"KendoTileLayoutItem\", p.defaultProps = {\n  resizable: !0,\n  reorderable: !0\n};\nlet u = p;\nexport {\n  u as InternalTile\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,iBAAiB,IAAIC,CAAC,EAAEC,SAAS,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,EAAEC,SAAS,IAAIC,CAAC,QAAQ,8BAA8B;AACtH,SAASC,cAAc,IAAIC,CAAC,QAAQ,sBAAsB;AAC1D,MAAMC,CAAC,GAAG,GAAG;EAAEC,CAAC,GAAG,MAAMA,CAAC,SAASb,CAAC,CAACc,SAAS,CAAC;IAC7CC,WAAWA,CAAA,EAAG;MACZ,KAAK,CAAC,GAAGC,SAAS,CAAC,EAAE,IAAI,CAACC,KAAK,GAAG;QAChCC,GAAG,EAAE,CAAC,CAAC;QACPC,WAAW,EAAE,CAAC;MAChB,CAAC,EAAE,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE,IAAI,CAACC,SAAS,GAAG,IAAI,EAAE,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC,EAAE,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC,EAAE,IAAI,CAACC,OAAO,GAAG,IAAI,EAAE,IAAI,CAACC,WAAW,GAAG,IAAI,EAAE,IAAI,CAACC,UAAU,GAAG,CAAC,CAAC,EAAE,IAAI,CAACC,WAAW,GAAG;QAAElB,CAAC,EAAE,CAAC;QAAEJ,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAACuB,OAAO,GAAG;QAAEnB,CAAC,EAAE,CAAC;QAAEJ,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAACwB,gBAAgB,GAAG;QAAEpB,CAAC,EAAE,CAAC;QAAEJ,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAACyB,cAAc,GAAG,KAAK,CAAC,EAAE,IAAI,CAACC,YAAY,GAAG,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC7T,IAAIA,CAAC,CAACC,GAAG,EAAE;UACT,IAAI,CAACC,aAAa,CAAC,CAAC;UACpB;QACF;QACA,IAAI,CAAC,IAAI,CAACX,OAAO,IAAI,CAAC,IAAI,CAACC,WAAW,EACpC;QACF,MAAMW,CAAC,GAAGJ,CAAC,CAACK,OAAO;UAAEC,CAAC,GAAGN,CAAC,CAACO,OAAO;QAClC,IAAI,CAAChB,QAAQ,GAAG,CAAC,CAAC;QAClB,MAAMiB,CAAC,GAAG,CAACP,CAAC,CAACQ,SAAS,KAAK,IAAI,GAAGL,CAAC,GAAG,IAAI,CAACR,OAAO,CAACnB,CAAC,GAAG,CAAC,KAAK,IAAI,CAACQ,KAAK,CAACC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;UAAEwB,CAAC,GAAGT,CAAC,CAACQ,SAAS,KAAK,IAAI,GAAGH,CAAC,GAAG,IAAI,CAACV,OAAO,CAACvB,CAAC,GAAG,CAAC;QACxI,IAAI,IAAI,CAACsC,WAAW,KAAK,IAAI,CAAC1B,KAAK,CAACC,GAAG,GAAG,IAAI,CAACyB,WAAW,CAACC,KAAK,CAACC,UAAU,GAAG,CAACL,CAAC,GAAG,IAAI,GAAG,IAAI,CAACG,WAAW,CAACC,KAAK,CAACE,WAAW,GAAG,CAACN,CAAC,GAAG,IAAI,EAAE,IAAI,CAACG,WAAW,CAACC,KAAK,CAACG,MAAM,GAAG,eAAeL,CAAC,KAAK,CAAC,EAAE,IAAI,CAACjB,WAAW,CAACuB,SAAS,CAACC,GAAG,CAAC,2BAA2B,CAAC,EAAE,IAAI,CAACnB,cAAc,EAChR;QACF,IAAIoB,CAAC,GAAG,CAAC;UAAEC,CAAC,GAAG,CAAC;QAChB,MAAMC,CAAC,GAAG,IAAI,CAAC5B,OAAO,CAAC6B,qBAAqB,CAAC,CAAC;QAC9Cb,CAAC,GAAGY,CAAC,CAACE,KAAK,GAAG,IAAI,CAACC,KAAK,CAACC,eAAe,CAACC,OAAO,GAAG,CAAC,KAAKP,CAAC,GAAG,CAAC,CAAC,EAAEV,CAAC,GAAG,CAACY,CAAC,CAACE,KAAK,GAAG,IAAI,CAACC,KAAK,CAACC,eAAe,CAACC,OAAO,GAAG,IAAI,KAAKP,CAAC,GAAG,CAAC,CAAC,CAAC,EAAER,CAAC,GAAGU,CAAC,CAACL,MAAM,GAAG,IAAI,CAACQ,KAAK,CAACC,eAAe,CAACE,OAAO,GAAG,CAAC,KAAKP,CAAC,GAAG,CAAC,CAAC,EAAET,CAAC,GAAG,CAACU,CAAC,CAACL,MAAM,GAAG,IAAI,CAACQ,KAAK,CAACC,eAAe,CAACE,OAAO,GAAG,IAAI,KAAKP,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAACD,CAAC,KAAK,CAAC,IAAIC,CAAC,KAAK,CAAC,KAAK,IAAI,CAACI,KAAK,CAACI,MAAM,CAAC,IAAI,CAACJ,KAAK,CAACK,KAAK,EAAE,CAAC,EAAE,CAAC,EAAET,CAAC,EAAED,CAAC,CAAC;MACzV,CAAC,EAAE,IAAI,CAACW,WAAW,GAAI7B,CAAC,IAAK;QAC3B,IAAI,CAAC,IAAI,CAACW,WAAW,EACnB;QACF,IAAI,IAAI,CAACf,OAAO,GAAG;UACjBnB,CAAC,EAAEuB,CAAC,CAAC8B,KAAK,CAACzB,OAAO;UAClBhC,CAAC,EAAE2B,CAAC,CAAC8B,KAAK,CAACvB;QACb,CAAC,EAAE,IAAI,CAACb,UAAU,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC6B,KAAK,CAAC7B,UAAU,IAAI,IAAI,CAAC6B,KAAK,CAAC7B,UAAU,CAACM,CAAC,CAAC8B,KAAK,CAACC,aAAa,CAAC,EAAE;UAC9F,IAAI,CAACrC,UAAU,GAAG,CAAC,CAAC;UACpB;QACF;QACA,IAAI,CAACF,OAAO,KAAK,IAAI,CAACA,OAAO,CAACoB,KAAK,CAACoB,MAAM,GAAG,IAAI,EAAE,IAAI,CAACC,QAAQ,CAAC;UAAE9C,WAAW,EAAE,CAAC;QAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAACwB,WAAW,CAACK,SAAS,CAACkB,MAAM,CAAC,eAAe,CAAC,EAAE,IAAI,CAACvB,WAAW,CAACK,SAAS,CAACC,GAAG,CAAC,mBAAmB,CAAC;QAC/L,MAAMhB,CAAC,GAAG,IAAI,CAACU,WAAW,CAACU,qBAAqB,CAAC,CAAC;QAClD,IAAI,CAAC1B,WAAW,GAAG;UACjBlB,CAAC,EAAEuB,CAAC,CAAC8B,KAAK,CAACzB,OAAO,GAAGJ,CAAC,CAACxB,CAAC;UACxBJ,CAAC,EAAE2B,CAAC,CAAC8B,KAAK,CAACvB,OAAO,GAAGN,CAAC,CAAC5B;QACzB,CAAC,EAAE,IAAI,CAACkD,KAAK,CAACY,OAAO,CAAC,CAAC;MACzB,CAAC,EAAE,IAAI,CAACC,UAAU,GAAIpC,CAAC,IAAK;QAC1B,IAAIkB,CAAC;QACL,IAAI,IAAI,CAACxB,UAAU,EACjB;QACF,MAAMO,CAAC,GAAG,IAAI,CAACU,WAAW;QAC1B,IAAIX,CAAC,CAAC8B,KAAK,CAACC,aAAa,CAACM,gBAAgB,IAAI,CAACpC,CAAC,EAC9C;QACF,IAAI,CAACX,QAAQ,GAAG,CAAC,CAAC,EAAEU,CAAC,CAAC8B,KAAK,CAACC,aAAa,CAACO,cAAc,CAAC,CAAC;QAC1D,MAAMlC,CAAC,GAAGH,CAAC,CAACoB,qBAAqB,CAAC,CAAC;QACnC,IAAI,IAAI,CAACxB,gBAAgB,GAAG;UAC1BpB,CAAC,EAAEuB,CAAC,CAAC8B,KAAK,CAACzB,OAAO,GAAGD,CAAC,CAAC3B,CAAC,GAAG,IAAI,CAACkB,WAAW,CAAClB,CAAC,GAAG,IAAI,CAACoB,gBAAgB,CAACpB,CAAC;UACvEJ,CAAC,EAAE2B,CAAC,CAAC8B,KAAK,CAACvB,OAAO,GAAGH,CAAC,CAAC/B,CAAC,GAAG,IAAI,CAACsB,WAAW,CAACtB,CAAC,GAAG,IAAI,CAACwB,gBAAgB,CAACxB;QACxE,CAAC,EAAE4B,CAAC,CAACW,KAAK,CAAC2B,SAAS,GAAG,aAAa,IAAI,CAAC1C,gBAAgB,CAACpB,CAAC,OAAO,IAAI,CAACoB,gBAAgB,CAACxB,CAAC,KAAK,EAAE4B,CAAC,CAACW,KAAK,CAAC4B,UAAU,GAAG,cAAc,EAAE,IAAI,CAAC1C,cAAc,EACtJ;QACF,IAAIQ,CAAC,GAAG,CAAC;UAAEE,CAAC,GAAG,CAAC;QAChB,IAAI,CAACX,gBAAgB,CAACxB,CAAC,GAAG,GAAG,GAAG+B,CAAC,CAACW,MAAM,GAAG,IAAI,CAACQ,KAAK,CAACC,eAAe,CAACE,OAAO,KAAKlB,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACX,gBAAgB,CAACxB,CAAC,GAAG,GAAG,GAAG,CAAC+B,CAAC,CAACW,MAAM,GAAG,IAAI,CAACQ,KAAK,CAACC,eAAe,CAACE,OAAO,KAAKlB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAACX,gBAAgB,CAACpB,CAAC,GAAG,GAAG,GAAG2B,CAAC,CAACkB,KAAK,GAAG,IAAI,CAACC,KAAK,CAACC,eAAe,CAACC,OAAO,KAAKnB,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACT,gBAAgB,CAACpB,CAAC,GAAG,GAAG,GAAG,CAAC2B,CAAC,CAACkB,KAAK,GAAG,IAAI,CAACC,KAAK,CAACC,eAAe,CAACC,OAAO,KAAKnB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAACiB,KAAK,CAACI,MAAM,CAAC,IAAI,CAACJ,KAAK,CAACK,KAAK,EAAEpB,CAAC,EAAE,IAAI,CAACvB,KAAK,CAACC,GAAG,GAAG,CAACoB,CAAC,GAAGA,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC/a,MAAMI,CAAC,GAAG,CAACQ,CAAC,GAAG,IAAI,CAAC1B,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG0B,CAAC,CAACuB,OAAO,CAAC,eAAe,CAAC;QAC1E,IAAI/B,CAAC,IAAI,IAAI,CAACjB,WAAW,EAAE;UACzB,MAAM0B,CAAC,GAAGT,CAAC,CAACW,qBAAqB,CAAC,CAAC;YAAED,CAAC,GAAGjD,CAAC,CAAC,CAAC,IAAI,EAAE;UAClD6B,CAAC,CAAC8B,KAAK,CAACzB,OAAO,GAAGc,CAAC,CAACuB,IAAI,GAAGtB,CAAC,IAAIpB,CAAC,CAAC8B,KAAK,CAACzB,OAAO,GAAGc,CAAC,CAACwB,KAAK,GAAGvB,CAAC,IAAIpB,CAAC,CAAC8B,KAAK,CAACvB,OAAO,GAAGY,CAAC,CAACyB,GAAG,IAAI5C,CAAC,CAAC8B,KAAK,CAACvB,OAAO,GAAGY,CAAC,CAAC0B,MAAM,GAAG,IAAI,CAACpD,WAAW,CAACmB,KAAK,CAACkC,OAAO,GAAG,MAAM,GAAG,IAAI,CAACrD,WAAW,CAACmB,KAAK,CAACkC,OAAO,GAAG,OAAO;QAC7M;MACF,CAAC,EAAE,IAAI,CAAC3C,aAAa,GAAG,MAAM;QAC5B,IAAI,CAACb,QAAQ,GAAG,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC,EAAE,IAAI,CAACM,gBAAgB,GAAG;UAAEpB,CAAC,EAAE,CAAC;UAAEJ,CAAC,EAAE;QAAE,CAAC,EAAE,IAAI,CAACmB,OAAO,IAAI,IAAI,CAACC,WAAW,KAAK,IAAI,CAACD,OAAO,CAACoB,KAAK,CAACoB,MAAM,GAAG,GAAG,EAAE,IAAI,CAACvC,WAAW,CAACuB,SAAS,CAACkB,MAAM,CAAC,2BAA2B,CAAC,EAAE,IAAI,CAACD,QAAQ,CAAC;UAAE9C,WAAW,EAAE,CAAC;QAAE,CAAC,CAAC,CAAC;QACrP,MAAMa,CAAC,GAAG,IAAI,CAACW,WAAW;QAC1BX,CAAC,KAAKA,CAAC,CAACY,KAAK,CAAC2B,SAAS,GAAG,qBAAqB,EAAEvC,CAAC,CAACY,KAAK,CAAC4B,UAAU,GAAG,aAAa5D,CAAC,kCAAkC,EAAEoB,CAAC,CAACY,KAAK,CAACE,WAAW,GAAG,KAAK,EAAEd,CAAC,CAACY,KAAK,CAACC,UAAU,GAAG,KAAK,EAAEb,CAAC,CAACY,KAAK,CAACG,MAAM,GAAG,MAAM,EAAEf,CAAC,CAACgB,SAAS,CAACkB,MAAM,CAAC,mBAAmB,CAAC,EAAElC,CAAC,CAACgB,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAACM,KAAK,CAACwB,SAAS,CAAC,CAAC;MAC9S,CAAC,EAAE,IAAI,CAACC,eAAe,GAAG,MAAM;QAC9B,IAAI,CAACC,sBAAsB,CAAC,CAAC;MAC/B,CAAC,EAAE,IAAI,CAACC,aAAa,GAAG,MAAM;QAC5BC,UAAU,CAAC,MAAM;UACf,IAAI,CAACF,sBAAsB,CAAC,CAAC;QAC/B,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,EAAE,IAAI,CAACG,mBAAmB,GAAG,MAAM;QAClC,IAAI,CAAC,IAAI,CAACzC,WAAW,IAAI,CAAC,IAAI,CAAClB,WAAW,EACxC;QACF,MAAMO,CAAC,GAAG,IAAI,CAACW,WAAW,CAACU,qBAAqB,CAAC,CAAC;UAAEpB,CAAC,GAAGD,CAAC,CAAC4C,GAAG,GAAG,IAAI,CAAC/C,gBAAgB,CAACxB,CAAC;UAAE+B,CAAC,GAAGJ,CAAC,CAAC0C,IAAI,GAAG,IAAI,CAAC7C,gBAAgB,CAACpB,CAAC;QAC7H,IAAI,CAACgB,WAAW,CAACmB,KAAK,CAACgC,GAAG,GAAG,GAAG3C,CAAC,IAAI,EAAE,IAAI,CAACR,WAAW,CAACmB,KAAK,CAAC8B,IAAI,GAAG,GAAGtC,CAAC,IAAI,EAAE,IAAI,CAACX,WAAW,CAACmB,KAAK,CAACkC,OAAO,GAAG,OAAO;MACzH,CAAC;IACH;IACA,IAAIO,WAAWA,CAAA,EAAG;MAChB,OAAO,IAAI,CAAC9B,KAAK,CAAC8B,WAAW,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC9B,KAAK,CAAC8B,WAAW,GAAGxE,CAAC,CAACyE,YAAY,CAACD,WAAW;IAChG;IACA,IAAI1C,WAAWA,CAAA,EAAG;MAChB,OAAO,IAAI,CAACtB,SAAS,GAAG,IAAI,CAACA,SAAS,CAACG,OAAO,GAAG,KAAK,CAAC;IACzD;IACA+D,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAAC/D,OAAO,KAAKgE,gBAAgB,CAAC,IAAI,CAAChE,OAAO,CAAC,CAACiB,SAAS,KAAK,KAAK,IAAI,IAAI,CAACwB,QAAQ,CAAC;QACnF/C,GAAG,EAAE,CAAC;MACR,CAAC,CAAC,EAAE,IAAI,CAACO,WAAW,KAAK,IAAI,CAACA,WAAW,CAACmB,KAAK,CAACG,MAAM,GAAG,IAAI,CAACvB,OAAO,CAACiE,YAAY,GAAG,IAAI,EAAE,IAAI,CAAChE,WAAW,CAACmB,KAAK,CAACU,KAAK,GAAG,IAAI,CAAC9B,OAAO,CAACkE,WAAW,GAAG,IAAI,CAAC,CAAC;IAC7J;IACAC,MAAMA,CAAA,EAAG;MACPtF,CAAC,IAAIuF,YAAY,IAAI,OAAOA,YAAY,IAAI,UAAU,KAAKA,YAAY,CAAC,IAAI,CAAC9D,cAAc,CAAC,EAAE,IAAI,CAACA,cAAc,GAAG+D,MAAM,CAACV,UAAU,CAAC,MAAM;QAC1I,IAAI,CAACrD,cAAc,GAAG,KAAK,CAAC;MAC9B,CAAC,EAAE,GAAG,CAAC,CAAC;MACR,MAAME,CAAC,GAAG,IAAI,CAACuB,KAAK,CAACC,eAAe;QAAEvB,CAAC,GAAG,IAAI,CAACsB,KAAK,CAACuC,SAAS,KAAK,KAAK,CAAC,GAAG,IAAI,CAACvC,KAAK,CAACuC,SAAS,GAAGjF,CAAC,CAACyE,YAAY,CAACQ,SAAS;QAAE1D,CAAC,GAAG;UAC/H2D,eAAe,EAAE/D,CAAC,CAACgE,GAAG;UACtBC,aAAa,EAAE,QAAQjE,CAAC,CAACyB,OAAO,EAAE;UAClCyC,YAAY,EAAElE,CAAC,CAACmE,GAAG;UACnBC,UAAU,EAAE,QAAQpE,CAAC,CAAC0B,OAAO,EAAE;UAC/B2C,OAAO,EAAE,MAAM;UACfC,KAAK,EAAEtE,CAAC,CAACsE,KAAK;UACdxB,OAAO,EAAE,OAAO;UAChB,GAAG,IAAI,CAACvB,KAAK,CAACgD;QAChB,CAAC;QAAEjE,CAAC,GAAG;UACLyD,eAAe,EAAE/D,CAAC,CAACgE,GAAG;UACtBC,aAAa,EAAE,QAAQjE,CAAC,CAACyB,OAAO,EAAE;UAClCyC,YAAY,EAAElE,CAAC,CAACmE,GAAG;UACnBC,UAAU,EAAE,QAAQpE,CAAC,CAAC0B,OAAO,EAAE;UAC/B4C,KAAK,EAAEtE,CAAC,CAACsE;QACX,CAAC;QAAE9D,CAAC,GAAG,eAAgBxC,CAAC,CAACwG,aAAa,CACpC,KAAK,EACL;UACEC,GAAG,EAAG/D,CAAC,IAAK;YACV,IAAI,CAACrB,SAAS,GAAGqB,CAAC,GAAG;cAAElB,OAAO,EAAEkB;YAAE,CAAC,GAAG,IAAI;UAC5C,CAAC;UACDgE,IAAI,EAAE,UAAU;UAChBC,QAAQ,EAAE,CAAC;UACX,iBAAiB,EAAE,OAAO,IAAI,CAACpD,KAAK,CAACqD,MAAM,IAAI,QAAQ,GAAG,IAAI,CAACrD,KAAK,CAACqD,MAAM,GAAG,QAAQ,IAAI,CAACrD,KAAK,CAACK,KAAK,EAAE;UACxG,mBAAmB,EAAE,OAAO;UAC5BiD,SAAS,EAAEtG,CAAC,CACV,0BAA0B,EAC1B;YAAE,eAAe,EAAE,IAAI,CAAC8E;UAAY,CAAC,EACrC,IAAI,CAAC9B,KAAK,CAACsD,SACb,CAAC;UACDjE,KAAK,EAAE;YAAEG,MAAM,EAAE,MAAM;YAAE,GAAGT,CAAC;YAAE,GAAG,IAAI,CAACiB,KAAK,CAACX;UAAM,CAAC;UACpDkE,WAAW,EAAE,IAAI,CAAC9B,eAAe;UACjC+B,SAAS,EAAE,IAAI,CAAC7B;QAClB,CAAC,EACD,IAAI,CAAC3B,KAAK,CAACyD,QAAQ,EACnB,eAAgBhH,CAAC,CAACwG,aAAa,CAC7B7F,CAAC,EACD;UACEwD,OAAO,EAAE,IAAI,CAACN,WAAW;UACzBoD,QAAQ,EAAE,IAAI,CAAClF,YAAY;UAC3B+D,SAAS,EAAE7D,CAAC;UACZf,GAAG,EAAE,IAAI,CAACD,KAAK,CAACC;QAClB,CACF,CACF,CAAC;MACD,OAAO,eAAgBlB,CAAC,CAACwG,aAAa,CAACxG,CAAC,CAACkH,QAAQ,EAAE,IAAI,EAAE,IAAI,CAACjG,KAAK,CAACE,WAAW,IAAI,eAAgBnB,CAAC,CAACwG,aAAa,CAChH,KAAK,EACL;QACEC,GAAG,EAAG/D,CAAC,IAAK;UACV,IAAI,CAACjB,WAAW,GAAGiB,CAAC;QACtB,CAAC;QACDE,KAAK,EAAE;UAAEuE,QAAQ,EAAE,OAAO;UAAE,GAAG/E;QAAE,CAAC;QAClCyE,SAAS,EAAEtG,CAAC,CAAC,oBAAoB,EAAE,IAAI,CAACgD,KAAK,CAAC6D,aAAa;MAC7D,CACF,CAAC,EAAE,eAAgBpH,CAAC,CAACwG,aAAa,CAChC/F,CAAC,EACD;QACEgG,GAAG,EAAG/D,CAAC,IAAK;UACV,IAAI,CAACrB,SAAS,GAAGqB,CAAC,EAAE,IAAI,CAAClB,OAAO,GAAGkB,CAAC,GAAGA,CAAC,CAAClB,OAAO,GAAG,IAAI;QACzD,CAAC;QACD6F,MAAM,EAAE,IAAI,CAAC9D,KAAK,CAAC8B,WAAW,GAAG,IAAI,CAACjB,UAAU,GAAG,KAAK,CAAC;QACzDW,SAAS,EAAE,IAAI,CAACxB,KAAK,CAAC8B,WAAW,GAAG,IAAI,CAAClD,aAAa,GAAG,KAAK,CAAC;QAC/DgC,OAAO,EAAE,IAAI,CAACZ,KAAK,CAAC8B,WAAW,GAAG,IAAI,CAACxB,WAAW,GAAG,KAAK;MAC5D,CAAC,EACDrB,CACF,CAAC,CAAC;IACJ;IACA;AACF;AACA;IACE8E,uBAAuBA,CAACtF,CAAC,EAAE;MACzB,OAAO,IAAI,CAACZ,OAAO,GAAG,CAAC,CAAC,EAAE,IAAI,CAACuB,WAAW,KAAK,IAAI,CAACvB,OAAO,GAAG,IAAI,CAACuB,WAAW,CAACU,qBAAqB,CAAC,CAAC,CAAC,EAAE,IAAI;IAC/G;IACA;AACF;AACA;IACE4B,sBAAsBA,CAAA,EAAG;MACvB,IAAI,IAAI,CAACzD,OAAO,IAAI,IAAI,CAACC,WAAW,EAAE;QACpC,MAAMO,CAAC,GAAG,IAAI,CAACR,OAAO,CAAC6B,qBAAqB,CAAC,CAAC;QAC9C,IAAI,CAAC5B,WAAW,CAACmB,KAAK,CAACgC,GAAG,GAAG5C,CAAC,CAAC4C,GAAG,GAAG,IAAI,EAAE,IAAI,CAACnD,WAAW,CAACmB,KAAK,CAAC8B,IAAI,GAAG1C,CAAC,CAAC0C,IAAI,GAAG,IAAI,EAAE,IAAI,CAACjD,WAAW,CAACmB,KAAK,CAACG,MAAM,GAAG,IAAI,CAACvB,OAAO,CAACiE,YAAY,GAAG,IAAI,EAAE,IAAI,CAAChE,WAAW,CAACmB,KAAK,CAACU,KAAK,GAAG,IAAI,CAAC9B,OAAO,CAACkE,WAAW,GAAG,IAAI;MAC1N;IACF;IACA;AACF;AACA;IACE6B,kBAAkBA,CAACvF,CAAC,EAAE;MACpB,MAAMC,CAAC,GAAG,IAAI,CAACU,WAAW;MAC1B,IAAI,CAACV,CAAC,EACJ;MACF,MAAMG,CAAC,GAAGH,CAAC,CAACoB,qBAAqB,CAAC,CAAC;QAAEf,CAAC,GAAG,IAAI,CAAClB,OAAO;MACrD,IAAI,IAAI,CAACG,QAAQ,EAAE;QACjB,MAAM2B,CAAC,GAAGd,CAAC,CAACkB,KAAK,GAAGhB,CAAC,CAACgB,KAAK;QAC3B,IAAI,IAAI,CAACrC,KAAK,CAACC,GAAG,EAAE;UAClB,MAAMsG,CAAC,GAAGC,UAAU,CAACxF,CAAC,CAACW,KAAK,CAACC,UAAU,IAAI,GAAG,CAAC;UAC/CZ,CAAC,CAACW,KAAK,CAACC,UAAU,GAAG2E,CAAC,GAAGtE,CAAC,GAAG,IAAI;QACnC,CAAC,MAAM;UACL,MAAMsE,CAAC,GAAGC,UAAU,CAACxF,CAAC,CAACW,KAAK,CAACE,WAAW,IAAI,GAAG,CAAC;UAChDb,CAAC,CAACW,KAAK,CAACE,WAAW,GAAG0E,CAAC,GAAGtE,CAAC,GAAG,IAAI;QACpC;QACA,IAAI,CAACtB,OAAO,CAACnB,CAAC,IAAI,IAAI,CAACQ,KAAK,CAACC,GAAG,GAAG,CAACgC,CAAC,GAAGA,CAAC;QACzC,MAAMC,CAAC,GAAGf,CAAC,CAACW,MAAM,GAAGT,CAAC,CAACS,MAAM;UAAEK,CAAC,GAAGqE,UAAU,CAACxF,CAAC,CAACW,KAAK,CAACG,MAAM,CAAC2E,SAAS,CAAC,EAAE,CAAC,CAAC;QAC3EzF,CAAC,CAACW,KAAK,CAACG,MAAM,GAAG,eAAeK,CAAC,GAAGD,CAAC,KAAK,EAAE,IAAI,CAACvB,OAAO,CAACvB,CAAC,IAAI8C,CAAC;MACjE;MACA,MAAMX,CAAC,GAAGF,CAAC,CAACoC,IAAI,GAAGtC,CAAC,CAACsC,IAAI;QAAEhC,CAAC,GAAGJ,CAAC,CAACsC,GAAG,GAAGxC,CAAC,CAACwC,GAAG;MAC5C,IAAI,EAAEpC,CAAC,KAAK,CAAC,IAAIE,CAAC,KAAK,CAAC,CAAC,EAAE;QACzB,IAAI,IAAI,CAACpB,QAAQ,EAAE;UACjB,CAACU,CAAC,CAACwB,eAAe,CAAC8C,KAAK,KAAK,IAAI,CAAC/C,KAAK,CAACC,eAAe,CAAC8C,KAAK,IAAItE,CAAC,CAACwB,eAAe,CAACwC,GAAG,KAAK,IAAI,CAACzC,KAAK,CAACC,eAAe,CAACwC,GAAG,MAAM,IAAI,CAACnE,gBAAgB,CAACpB,CAAC,GAAG,CAAC,EAAE,IAAI,CAACoB,gBAAgB,CAACxB,CAAC,GAAG,CAAC,EAAE4B,CAAC,CAACW,KAAK,CAAC2B,SAAS,GAAG,EAAE,EAAE,IAAI,CAACa,mBAAmB,CAAC,CAAC,CAAC;UAC5O;QACF;QACAuC,IAAI,CAACC,GAAG,CAAClF,CAAC,CAAC,GAAG,EAAE,IAAIiF,IAAI,CAACC,GAAG,CAACpF,CAAC,CAAC,GAAG,EAAE,IAAIqF,qBAAqB,CAAC,MAAM;UAClE,MAAM3E,CAAC,GAAG,IAAI,CAAC1B,OAAO;UACtB0B,CAAC,KAAKA,CAAC,CAACN,KAAK,CAAC2B,SAAS,GAAG,aAAa/B,CAAC,OAAOE,CAAC,KAAK,EAAEQ,CAAC,CAACN,KAAK,CAAC4B,UAAU,GAAG,cAAc,EAAEqD,qBAAqB,CAAC,MAAM;YACtH3E,CAAC,CAACN,KAAK,CAAC2B,SAAS,GAAG,EAAE,EAAErB,CAAC,CAACN,KAAK,CAAC4B,UAAU,GAAG,aAAa5D,CAAC,kCAAkC;UAC/F,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;MACJ;IACF;EACF,CAAC;AACDC,CAAC,CAACiH,SAAS,GAAG;EACZtE,eAAe,EAAEvD,CAAC,CAAC8H,MAAM,CAACC,UAAU;EACpCpF,KAAK,EAAE3C,CAAC,CAAC8H,MAAM;EACflB,SAAS,EAAE5G,CAAC,CAACgI,MAAM;EACnB1B,SAAS,EAAEtG,CAAC,CAAC8H,MAAM;EACnBX,aAAa,EAAEnH,CAAC,CAACgI,MAAM;EACvBrB,MAAM,EAAE3G,CAAC,CAACiI,GAAG;EACbC,IAAI,EAAElI,CAAC,CAACiI,GAAG;EACXE,IAAI,EAAEnI,CAAC,CAACiI,GAAG;EACXpC,SAAS,EAAE7F,CAAC,CAACoI,KAAK,CAAC,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACtDhD,WAAW,EAAEpF,CAAC,CAACqI;AACjB,CAAC,EAAEzH,CAAC,CAAC0H,WAAW,GAAG,qBAAqB,EAAE1H,CAAC,CAACyE,YAAY,GAAG;EACzDQ,SAAS,EAAE,CAAC,CAAC;EACbT,WAAW,EAAE,CAAC;AAChB,CAAC;AACD,IAAImD,CAAC,GAAG3H,CAAC;AACT,SACE2H,CAAC,IAAIC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}