{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\n\"use client\";\n\nimport { loadMessages as e } from \"./loadMessages.mjs\";\nconst n = o => {\n  const {\n    data: r,\n    language: a,\n    children: t\n  } = o;\n  return a && e(r, a), t;\n};\nexport { n as LocalizationDataProvider };", "map": {"version": 3, "names": ["loadMessages", "e", "n", "o", "data", "r", "language", "a", "children", "t", "LocalizationDataProvider"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-intl/Localization/LocalizationDataProvider.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\n\"use client\";\nimport { loadMessages as e } from \"./loadMessages.mjs\";\nconst n = (o) => {\n  const { data: r, language: a, children: t } = o;\n  return a && e(r, a), t;\n};\nexport {\n  n as LocalizationDataProvider\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;;AACZ,SAASA,YAAY,IAAIC,CAAC,QAAQ,oBAAoB;AACtD,MAAMC,CAAC,GAAIC,CAAC,IAAK;EACf,MAAM;IAAEC,IAAI,EAAEC,CAAC;IAAEC,QAAQ,EAAEC,CAAC;IAAEC,QAAQ,EAAEC;EAAE,CAAC,GAAGN,CAAC;EAC/C,OAAOI,CAAC,IAAIN,CAAC,CAACI,CAAC,EAAEE,CAAC,CAAC,EAAEE,CAAC;AACxB,CAAC;AACD,SACEP,CAAC,IAAIQ,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}