{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport t from \"prop-types\";\nimport { classNames as p } from \"@progress/kendo-react-common\";\nconst s = e.forwardRef((n, a) => {\n  const {\n      children: o,\n      className: c,\n      style: l\n    } = n,\n    r = e.useRef(null);\n  e.useImperativeHandle(a, () => ({\n    element: r.current\n  }));\n  const m = p({\n    \"k-drawer-content\": !0\n  }, c);\n  return /* @__PURE__ */e.createElement(\"div\", {\n    className: m,\n    style: l,\n    ref: r\n  }, o);\n});\ns.propTypes = {\n  children: t.any,\n  className: t.string,\n  style: t.object\n};\ns.displayName = \"KendoDrawerContent\";\nexport { s as DrawerContent };", "map": {"version": 3, "names": ["e", "t", "classNames", "p", "s", "forwardRef", "n", "a", "children", "o", "className", "c", "style", "l", "r", "useRef", "useImperativeHandle", "element", "current", "m", "createElement", "ref", "propTypes", "any", "string", "object", "displayName", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/drawer/DrawerContent.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport t from \"prop-types\";\nimport { classNames as p } from \"@progress/kendo-react-common\";\nconst s = e.forwardRef((n, a) => {\n  const { children: o, className: c, style: l } = n, r = e.useRef(null);\n  e.useImperativeHandle(\n    a,\n    () => ({\n      element: r.current\n    })\n  );\n  const m = p(\n    {\n      \"k-drawer-content\": !0\n    },\n    c\n  );\n  return /* @__PURE__ */ e.createElement(\"div\", { className: m, style: l, ref: r }, o);\n});\ns.propTypes = {\n  children: t.any,\n  className: t.string,\n  style: t.object\n};\ns.displayName = \"KendoDrawerContent\";\nexport {\n  s as DrawerContent\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AAC9D,MAAMC,CAAC,GAAGJ,CAAC,CAACK,UAAU,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;EAC/B,MAAM;MAAEC,QAAQ,EAAEC,CAAC;MAAEC,SAAS,EAAEC,CAAC;MAAEC,KAAK,EAAEC;IAAE,CAAC,GAAGP,CAAC;IAAEQ,CAAC,GAAGd,CAAC,CAACe,MAAM,CAAC,IAAI,CAAC;EACrEf,CAAC,CAACgB,mBAAmB,CACnBT,CAAC,EACD,OAAO;IACLU,OAAO,EAAEH,CAAC,CAACI;EACb,CAAC,CACH,CAAC;EACD,MAAMC,CAAC,GAAGhB,CAAC,CACT;IACE,kBAAkB,EAAE,CAAC;EACvB,CAAC,EACDQ,CACF,CAAC;EACD,OAAO,eAAgBX,CAAC,CAACoB,aAAa,CAAC,KAAK,EAAE;IAAEV,SAAS,EAAES,CAAC;IAAEP,KAAK,EAAEC,CAAC;IAAEQ,GAAG,EAAEP;EAAE,CAAC,EAAEL,CAAC,CAAC;AACtF,CAAC,CAAC;AACFL,CAAC,CAACkB,SAAS,GAAG;EACZd,QAAQ,EAAEP,CAAC,CAACsB,GAAG;EACfb,SAAS,EAAET,CAAC,CAACuB,MAAM;EACnBZ,KAAK,EAAEX,CAAC,CAACwB;AACX,CAAC;AACDrB,CAAC,CAACsB,WAAW,GAAG,oBAAoB;AACpC,SACEtB,CAAC,IAAIuB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}