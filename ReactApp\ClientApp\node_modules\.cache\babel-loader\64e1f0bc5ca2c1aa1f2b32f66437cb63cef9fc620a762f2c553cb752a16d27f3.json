{"ast": null, "code": "import { normalizeYear } from './normalize-year';\n/**\n * A function that returns a `Date` object of the last year in a decade.\n *\n * @param date - The start date value.\n * @returns - The last year in a decade.\n *\n * @example\n * ```ts-no-run\n * lastYearOfDecade(new Date(2017, 0, 1)); // 2019-1-1\n * lastYearOfDecade(new Date(2007, 10, 22)); // 2009-11-22\n * lastYearOfDecade(new Date(2026, 0, 1)); // 2029-1-1\n * ```\n */\nexport var lastYearOfDecade = function (value) {\n  return normalizeYear(value, function (y) {\n    return y - y % 10 + 9;\n  });\n};", "map": {"version": 3, "names": ["normalizeYear", "lastYearOfDecade", "value", "y"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/last-year-of-decade.js"], "sourcesContent": ["import { normalizeYear } from './normalize-year';\n/**\n * A function that returns a `Date` object of the last year in a decade.\n *\n * @param date - The start date value.\n * @returns - The last year in a decade.\n *\n * @example\n * ```ts-no-run\n * lastYearOfDecade(new Date(2017, 0, 1)); // 2019-1-1\n * lastYearOfDecade(new Date(2007, 10, 22)); // 2009-11-22\n * lastYearOfDecade(new Date(2026, 0, 1)); // 2029-1-1\n * ```\n */\nexport var lastYearOfDecade = function (value) { return (normalizeYear(value, function (y) { return y - (y % 10) + 9; })); };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,kBAAkB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,gBAAgB,GAAG,SAAAA,CAAUC,KAAK,EAAE;EAAE,OAAQF,aAAa,CAACE,KAAK,EAAE,UAAUC,CAAC,EAAE;IAAE,OAAOA,CAAC,GAAIA,CAAC,GAAG,EAAG,GAAG,CAAC;EAAE,CAAC,CAAC;AAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}