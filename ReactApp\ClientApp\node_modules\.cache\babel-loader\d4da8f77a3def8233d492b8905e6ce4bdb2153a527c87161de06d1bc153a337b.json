{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as o from \"react\";\nimport t from \"prop-types\";\nimport { useUnstyled as d, classNames as c, uHint as l } from \"@progress/kendo-react-common\";\nconst n = r => {\n  const e = {\n      direction: \"start\",\n      ...r\n    },\n    s = d(),\n    i = s && s.uHint,\n    a = o.useMemo(() => c(l.wrapper({\n      c: i,\n      direction: e.direction,\n      disabled: e.editorDisabled === !0\n    }), e.className), [i, e.direction, e.editorDisabled, e.className]);\n  return /* @__PURE__ */o.createElement(\"div\", {\n    id: e.id,\n    style: e.style,\n    className: a\n  }, e.children);\n};\nn.propTypes = {\n  id: t.string,\n  direction: t.oneOf([\"start\", \"end\"]),\n  children: t.oneOfType([t.element, t.node]),\n  style: t.object,\n  className: t.string,\n  editorDisabled: t.bool\n};\nn.displayName = \"KendoReactHint\";\nexport { n as Hint };", "map": {"version": 3, "names": ["o", "t", "useUnstyled", "d", "classNames", "c", "uHint", "l", "n", "r", "e", "direction", "s", "i", "a", "useMemo", "wrapper", "disabled", "editorDisabled", "className", "createElement", "id", "style", "children", "propTypes", "string", "oneOf", "oneOfType", "element", "node", "object", "bool", "displayName", "Hint"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-labels/Hint.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as o from \"react\";\nimport t from \"prop-types\";\nimport { useUnstyled as d, classNames as c, uHint as l } from \"@progress/kendo-react-common\";\nconst n = (r) => {\n  const e = {\n    direction: \"start\",\n    ...r\n  }, s = d(), i = s && s.uHint, a = o.useMemo(\n    () => c(\n      l.wrapper({\n        c: i,\n        direction: e.direction,\n        disabled: e.editorDisabled === !0\n      }),\n      e.className\n    ),\n    [i, e.direction, e.editorDisabled, e.className]\n  );\n  return /* @__PURE__ */ o.createElement(\"div\", { id: e.id, style: e.style, className: a }, e.children);\n};\nn.propTypes = {\n  id: t.string,\n  direction: t.oneOf([\"start\", \"end\"]),\n  children: t.oneOfType([t.element, t.node]),\n  style: t.object,\n  className: t.string,\n  editorDisabled: t.bool\n};\nn.displayName = \"KendoReactHint\";\nexport {\n  n as Hint\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,WAAW,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,EAAEC,KAAK,IAAIC,CAAC,QAAQ,8BAA8B;AAC5F,MAAMC,CAAC,GAAIC,CAAC,IAAK;EACf,MAAMC,CAAC,GAAG;MACRC,SAAS,EAAE,OAAO;MAClB,GAAGF;IACL,CAAC;IAAEG,CAAC,GAAGT,CAAC,CAAC,CAAC;IAAEU,CAAC,GAAGD,CAAC,IAAIA,CAAC,CAACN,KAAK;IAAEQ,CAAC,GAAGd,CAAC,CAACe,OAAO,CACzC,MAAMV,CAAC,CACLE,CAAC,CAACS,OAAO,CAAC;MACRX,CAAC,EAAEQ,CAAC;MACJF,SAAS,EAAED,CAAC,CAACC,SAAS;MACtBM,QAAQ,EAAEP,CAAC,CAACQ,cAAc,KAAK,CAAC;IAClC,CAAC,CAAC,EACFR,CAAC,CAACS,SACJ,CAAC,EACD,CAACN,CAAC,EAAEH,CAAC,CAACC,SAAS,EAAED,CAAC,CAACQ,cAAc,EAAER,CAAC,CAACS,SAAS,CAChD,CAAC;EACD,OAAO,eAAgBnB,CAAC,CAACoB,aAAa,CAAC,KAAK,EAAE;IAAEC,EAAE,EAAEX,CAAC,CAACW,EAAE;IAAEC,KAAK,EAAEZ,CAAC,CAACY,KAAK;IAAEH,SAAS,EAAEL;EAAE,CAAC,EAAEJ,CAAC,CAACa,QAAQ,CAAC;AACvG,CAAC;AACDf,CAAC,CAACgB,SAAS,GAAG;EACZH,EAAE,EAAEpB,CAAC,CAACwB,MAAM;EACZd,SAAS,EAAEV,CAAC,CAACyB,KAAK,CAAC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;EACpCH,QAAQ,EAAEtB,CAAC,CAAC0B,SAAS,CAAC,CAAC1B,CAAC,CAAC2B,OAAO,EAAE3B,CAAC,CAAC4B,IAAI,CAAC,CAAC;EAC1CP,KAAK,EAAErB,CAAC,CAAC6B,MAAM;EACfX,SAAS,EAAElB,CAAC,CAACwB,MAAM;EACnBP,cAAc,EAAEjB,CAAC,CAAC8B;AACpB,CAAC;AACDvB,CAAC,CAACwB,WAAW,GAAG,gBAAgB;AAChC,SACExB,CAAC,IAAIyB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}