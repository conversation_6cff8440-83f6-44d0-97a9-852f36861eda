{"ast": null, "code": "import Node from './node';\nimport { parseColor } from '../common';\nimport { isTransparent, valueOrDefault } from '../util';\nimport LinearGradient from '../gradients/linear-gradient';\nimport RadialGradient from '../gradients/radial-gradient';\nimport { DASH_ARRAYS, SOLID, BUTT, PATTERN } from '../core/constants';\nimport renderPath from './utils/render-path';\nfunction addGradientStops(gradient, stops) {\n  for (var idx = 0; idx < stops.length; idx++) {\n    var stop = stops[idx];\n    var color = parseColor(stop.color());\n    color.a *= stop.opacity();\n    gradient.addColorStop(stop.offset(), color.toCssRgba());\n  }\n}\nvar PathNode = function (Node) {\n  function PathNode() {\n    Node.apply(this, arguments);\n  }\n  if (Node) PathNode.__proto__ = Node;\n  PathNode.prototype = Object.create(Node && Node.prototype);\n  PathNode.prototype.constructor = PathNode;\n  PathNode.prototype.renderTo = function renderTo(ctx) {\n    ctx.save();\n    this.setTransform(ctx);\n    this.setClip(ctx);\n    this.setOpacity(ctx);\n    ctx.beginPath();\n    this.renderPoints(ctx, this.srcElement);\n    this.setLineDash(ctx);\n    this.setLineCap(ctx);\n    this.setLineJoin(ctx);\n    this.setFill(ctx);\n    this.setStroke(ctx);\n    ctx.restore();\n  };\n  PathNode.prototype.setFill = function setFill(ctx) {\n    var fill = this.srcElement.options.fill;\n    var hasFill = false;\n    if (fill) {\n      if (fill.nodeType === \"Gradient\") {\n        this.setGradientFill(ctx, fill);\n        hasFill = true;\n      } else if (fill.nodeType === PATTERN) {\n        this.setPatternFill(ctx, fill);\n        hasFill = true;\n      } else if (!isTransparent(fill.color)) {\n        ctx.fillStyle = fill.color;\n        ctx.save();\n        this.globalAlpha(ctx, fill.opacity);\n        ctx.fill();\n        ctx.restore();\n        hasFill = true;\n      }\n    }\n    return hasFill;\n  };\n  PathNode.prototype.setGradientFill = function setGradientFill(ctx, fill) {\n    var bbox = this.srcElement.rawBBox();\n    var gradient;\n    if (fill instanceof LinearGradient) {\n      var start = fill.start();\n      var end = fill.end();\n      gradient = ctx.createLinearGradient(start.x, start.y, end.x, end.y);\n    } else if (fill instanceof RadialGradient) {\n      var center = fill.center();\n      gradient = ctx.createRadialGradient(center.x, center.y, 0, center.x, center.y, fill.radius());\n    }\n    addGradientStops(gradient, fill.stops);\n    ctx.save();\n    if (!fill.userSpace()) {\n      ctx.transform(bbox.width(), 0, 0, bbox.height(), bbox.origin.x, bbox.origin.y);\n    }\n    ctx.fillStyle = gradient;\n    ctx.fill();\n    ctx.restore();\n  };\n  PathNode.prototype.setPatternFill = function setPatternFill(ctx, pattern) {\n    var size = pattern.size();\n    var patternCanvas = document.createElement(\"canvas\");\n    var patternContext = patternCanvas.getContext(\"2d\");\n    patternCanvas.width = size.getWidth();\n    patternCanvas.height = size.getHeight();\n    this.childNodes.length = 0;\n    this.loadElements(pattern.children);\n    var childNodes = this.childNodes;\n    for (var i = 0; i < childNodes.length; i++) {\n      var child = childNodes[i];\n      child.renderTo(patternContext);\n    }\n    ctx.save();\n    ctx.fillStyle = ctx.createPattern(patternCanvas, \"repeat\");\n    ctx.fill();\n    ctx.restore();\n  };\n  PathNode.prototype.setStroke = function setStroke(ctx) {\n    var stroke = this.srcElement.options.stroke;\n    if (stroke && !isTransparent(stroke.color) && stroke.width > 0) {\n      ctx.strokeStyle = stroke.color;\n      ctx.lineWidth = valueOrDefault(stroke.width, 1);\n      ctx.lineJoin = valueOrDefault(stroke.lineJoin, ctx.lineJoin);\n      ctx.save();\n      this.globalAlpha(ctx, stroke.opacity);\n      ctx.stroke();\n      ctx.restore();\n      return true;\n    }\n  };\n  PathNode.prototype.dashType = function dashType() {\n    var stroke = this.srcElement.options.stroke;\n    if (stroke && stroke.dashType) {\n      return stroke.dashType.toLowerCase();\n    }\n  };\n  PathNode.prototype.setLineDash = function setLineDash(ctx) {\n    var dashType = this.dashType();\n    if (dashType && dashType !== SOLID) {\n      var dashArray = DASH_ARRAYS[dashType];\n      if (ctx.setLineDash) {\n        ctx.setLineDash(dashArray);\n      } else {\n        ctx.mozDash = dashArray;\n        ctx.webkitLineDash = dashArray;\n      }\n    }\n  };\n  PathNode.prototype.setLineCap = function setLineCap(ctx) {\n    var dashType = this.dashType();\n    var stroke = this.srcElement.options.stroke;\n    if (dashType && dashType !== SOLID) {\n      ctx.lineCap = BUTT;\n    } else if (stroke && stroke.lineCap) {\n      ctx.lineCap = stroke.lineCap;\n    }\n  };\n  PathNode.prototype.setLineJoin = function setLineJoin(ctx) {\n    var stroke = this.srcElement.options.stroke;\n    if (stroke && stroke.lineJoin) {\n      ctx.lineJoin = stroke.lineJoin;\n    }\n  };\n  PathNode.prototype.renderPoints = function renderPoints(ctx, path) {\n    renderPath(ctx, path);\n  };\n  return PathNode;\n}(Node);\nexport default PathNode;", "map": {"version": 3, "names": ["Node", "parseColor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valueOrDefault", "LinearGradient", "RadialGrad<PERSON>", "DASH_ARRAYS", "SOLID", "BUTT", "PATTERN", "<PERSON><PERSON><PERSON>", "addGradientStops", "gradient", "stops", "idx", "length", "stop", "color", "a", "opacity", "addColorStop", "offset", "toCssRgba", "PathNode", "apply", "arguments", "__proto__", "prototype", "Object", "create", "constructor", "renderTo", "ctx", "save", "setTransform", "setClip", "setOpacity", "beginPath", "renderPoints", "srcElement", "setLineDash", "setLineCap", "setLineJoin", "setFill", "setStroke", "restore", "fill", "options", "hasFill", "nodeType", "setGradientFill", "setPatternFill", "fillStyle", "globalAlpha", "bbox", "rawBBox", "start", "end", "createLinearGradient", "x", "y", "center", "createRadialGradient", "radius", "userSpace", "transform", "width", "height", "origin", "pattern", "size", "patternCanvas", "document", "createElement", "patternContext", "getContext", "getWidth", "getHeight", "childNodes", "loadElements", "children", "i", "child", "createPattern", "stroke", "strokeStyle", "lineWidth", "lineJoin", "dashType", "toLowerCase", "dashArray", "mozDash", "webkitLineDash", "lineCap", "path"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/canvas/path-node.js"], "sourcesContent": ["import Node from './node';\nimport { parseColor } from '../common';\nimport { isTransparent, valueOrDefault } from '../util';\nimport LinearGradient from '../gradients/linear-gradient';\nimport RadialGradient from '../gradients/radial-gradient';\nimport { DASH_ARRAYS, SOLID, BUTT, PATTERN } from '../core/constants';\nimport renderPath from './utils/render-path';\n\nfunction addGradientStops(gradient, stops) {\n    for (var idx = 0; idx < stops.length; idx++) {\n        var stop = stops[idx];\n        var color = parseColor(stop.color());\n\n        color.a *= stop.opacity();\n\n        gradient.addColorStop(stop.offset(), color.toCssRgba());\n    }\n}\n\nvar PathNode = (function (Node) {\n    function PathNode () {\n        Node.apply(this, arguments);\n    }\n\n    if ( Node ) PathNode.__proto__ = Node;\n    PathNode.prototype = Object.create( Node && Node.prototype );\n    PathNode.prototype.constructor = PathNode;\n\n    PathNode.prototype.renderTo = function renderTo (ctx) {\n        ctx.save();\n\n        this.setTransform(ctx);\n        this.setClip(ctx);\n        this.setOpacity(ctx);\n\n        ctx.beginPath();\n\n        this.renderPoints(ctx, this.srcElement);\n\n        this.setLineDash(ctx);\n        this.setLineCap(ctx);\n        this.setLineJoin(ctx);\n\n        this.setFill(ctx);\n        this.setStroke(ctx);\n\n        ctx.restore();\n    };\n\n    PathNode.prototype.setFill = function setFill (ctx) {\n        var fill = this.srcElement.options.fill;\n        var hasFill = false;\n\n        if (fill) {\n            if (fill.nodeType === \"Gradient\") {\n                this.setGradientFill(ctx, fill);\n                hasFill = true;\n            } else if (fill.nodeType === PATTERN) {\n                this.setPatternFill(ctx, fill);\n                hasFill = true;\n            } else if (!isTransparent(fill.color)) {\n                ctx.fillStyle = fill.color;\n\n                ctx.save();\n                this.globalAlpha(ctx, fill.opacity);\n                ctx.fill();\n                ctx.restore();\n\n                hasFill = true;\n            }\n        }\n\n        return hasFill;\n    };\n\n    PathNode.prototype.setGradientFill = function setGradientFill (ctx, fill) {\n        var bbox = this.srcElement.rawBBox();\n        var gradient;\n\n        if (fill instanceof LinearGradient) {\n            var start = fill.start();\n            var end = fill.end();\n            gradient = ctx.createLinearGradient(start.x, start.y, end.x, end.y);\n        } else if (fill instanceof RadialGradient) {\n            var center = fill.center();\n            gradient = ctx.createRadialGradient(center.x, center.y, 0, center.x, center.y, fill.radius());\n        }\n\n        addGradientStops(gradient, fill.stops);\n\n        ctx.save();\n\n        if (!fill.userSpace()) {\n            ctx.transform(bbox.width(), 0, 0, bbox.height(), bbox.origin.x, bbox.origin.y);\n        }\n        ctx.fillStyle = gradient;\n        ctx.fill();\n\n        ctx.restore();\n    };\n\n    PathNode.prototype.setPatternFill = function setPatternFill (ctx, pattern) {\n        var size = pattern.size();\n        var patternCanvas = document.createElement(\"canvas\");\n        var patternContext = patternCanvas.getContext(\"2d\");\n\n        patternCanvas.width = size.getWidth();\n        patternCanvas.height = size.getHeight();\n\n        this.childNodes.length = 0;\n        this.loadElements(pattern.children);\n\n        var childNodes = this.childNodes;\n        for (var i = 0; i < childNodes.length; i++) {\n            var child = childNodes[i];\n            child.renderTo(patternContext);\n        }\n\n        ctx.save();\n        ctx.fillStyle = ctx.createPattern(patternCanvas, \"repeat\");\n        ctx.fill();\n        ctx.restore();\n    };\n\n    PathNode.prototype.setStroke = function setStroke (ctx) {\n        var stroke = this.srcElement.options.stroke;\n        if (stroke && !isTransparent(stroke.color) && stroke.width > 0) {\n            ctx.strokeStyle = stroke.color;\n            ctx.lineWidth = valueOrDefault(stroke.width, 1);\n            ctx.lineJoin = valueOrDefault(stroke.lineJoin, ctx.lineJoin);\n\n            ctx.save();\n            this.globalAlpha(ctx, stroke.opacity);\n            ctx.stroke();\n            ctx.restore();\n\n            return true;\n        }\n    };\n\n    PathNode.prototype.dashType = function dashType () {\n        var stroke = this.srcElement.options.stroke;\n        if (stroke && stroke.dashType) {\n            return stroke.dashType.toLowerCase();\n        }\n    };\n\n    PathNode.prototype.setLineDash = function setLineDash (ctx) {\n        var dashType = this.dashType();\n        if (dashType && dashType !== SOLID) {\n            var dashArray = DASH_ARRAYS[dashType];\n            if (ctx.setLineDash) {\n                ctx.setLineDash(dashArray);\n            } else {\n                ctx.mozDash = dashArray;\n                ctx.webkitLineDash = dashArray;\n            }\n        }\n    };\n\n    PathNode.prototype.setLineCap = function setLineCap (ctx) {\n        var dashType = this.dashType();\n        var stroke = this.srcElement.options.stroke;\n        if (dashType && dashType !== SOLID) {\n            ctx.lineCap = BUTT;\n        } else if (stroke && stroke.lineCap) {\n            ctx.lineCap = stroke.lineCap;\n        }\n    };\n\n    PathNode.prototype.setLineJoin = function setLineJoin (ctx) {\n        var stroke = this.srcElement.options.stroke;\n        if (stroke && stroke.lineJoin) {\n            ctx.lineJoin = stroke.lineJoin;\n        }\n    };\n\n    PathNode.prototype.renderPoints = function renderPoints (ctx, path) {\n        renderPath(ctx, path);\n    };\n\n    return PathNode;\n}(Node));\n\nexport default PathNode;\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,QAAQ;AACzB,SAASC,UAAU,QAAQ,WAAW;AACtC,SAASC,aAAa,EAAEC,cAAc,QAAQ,SAAS;AACvD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,SAASC,WAAW,EAAEC,KAAK,EAAEC,IAAI,EAAEC,OAAO,QAAQ,mBAAmB;AACrE,OAAOC,UAAU,MAAM,qBAAqB;AAE5C,SAASC,gBAAgBA,CAACC,QAAQ,EAAEC,KAAK,EAAE;EACvC,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGD,KAAK,CAACE,MAAM,EAAED,GAAG,EAAE,EAAE;IACzC,IAAIE,IAAI,GAAGH,KAAK,CAACC,GAAG,CAAC;IACrB,IAAIG,KAAK,GAAGhB,UAAU,CAACe,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC;IAEpCA,KAAK,CAACC,CAAC,IAAIF,IAAI,CAACG,OAAO,CAAC,CAAC;IAEzBP,QAAQ,CAACQ,YAAY,CAACJ,IAAI,CAACK,MAAM,CAAC,CAAC,EAAEJ,KAAK,CAACK,SAAS,CAAC,CAAC,CAAC;EAC3D;AACJ;AAEA,IAAIC,QAAQ,GAAI,UAAUvB,IAAI,EAAE;EAC5B,SAASuB,QAAQA,CAAA,EAAI;IACjBvB,IAAI,CAACwB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EAC/B;EAEA,IAAKzB,IAAI,EAAGuB,QAAQ,CAACG,SAAS,GAAG1B,IAAI;EACrCuB,QAAQ,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAE7B,IAAI,IAAIA,IAAI,CAAC2B,SAAU,CAAC;EAC5DJ,QAAQ,CAACI,SAAS,CAACG,WAAW,GAAGP,QAAQ;EAEzCA,QAAQ,CAACI,SAAS,CAACI,QAAQ,GAAG,SAASA,QAAQA,CAAEC,GAAG,EAAE;IAClDA,GAAG,CAACC,IAAI,CAAC,CAAC;IAEV,IAAI,CAACC,YAAY,CAACF,GAAG,CAAC;IACtB,IAAI,CAACG,OAAO,CAACH,GAAG,CAAC;IACjB,IAAI,CAACI,UAAU,CAACJ,GAAG,CAAC;IAEpBA,GAAG,CAACK,SAAS,CAAC,CAAC;IAEf,IAAI,CAACC,YAAY,CAACN,GAAG,EAAE,IAAI,CAACO,UAAU,CAAC;IAEvC,IAAI,CAACC,WAAW,CAACR,GAAG,CAAC;IACrB,IAAI,CAACS,UAAU,CAACT,GAAG,CAAC;IACpB,IAAI,CAACU,WAAW,CAACV,GAAG,CAAC;IAErB,IAAI,CAACW,OAAO,CAACX,GAAG,CAAC;IACjB,IAAI,CAACY,SAAS,CAACZ,GAAG,CAAC;IAEnBA,GAAG,CAACa,OAAO,CAAC,CAAC;EACjB,CAAC;EAEDtB,QAAQ,CAACI,SAAS,CAACgB,OAAO,GAAG,SAASA,OAAOA,CAAEX,GAAG,EAAE;IAChD,IAAIc,IAAI,GAAG,IAAI,CAACP,UAAU,CAACQ,OAAO,CAACD,IAAI;IACvC,IAAIE,OAAO,GAAG,KAAK;IAEnB,IAAIF,IAAI,EAAE;MACN,IAAIA,IAAI,CAACG,QAAQ,KAAK,UAAU,EAAE;QAC9B,IAAI,CAACC,eAAe,CAAClB,GAAG,EAAEc,IAAI,CAAC;QAC/BE,OAAO,GAAG,IAAI;MAClB,CAAC,MAAM,IAAIF,IAAI,CAACG,QAAQ,KAAKxC,OAAO,EAAE;QAClC,IAAI,CAAC0C,cAAc,CAACnB,GAAG,EAAEc,IAAI,CAAC;QAC9BE,OAAO,GAAG,IAAI;MAClB,CAAC,MAAM,IAAI,CAAC9C,aAAa,CAAC4C,IAAI,CAAC7B,KAAK,CAAC,EAAE;QACnCe,GAAG,CAACoB,SAAS,GAAGN,IAAI,CAAC7B,KAAK;QAE1Be,GAAG,CAACC,IAAI,CAAC,CAAC;QACV,IAAI,CAACoB,WAAW,CAACrB,GAAG,EAAEc,IAAI,CAAC3B,OAAO,CAAC;QACnCa,GAAG,CAACc,IAAI,CAAC,CAAC;QACVd,GAAG,CAACa,OAAO,CAAC,CAAC;QAEbG,OAAO,GAAG,IAAI;MAClB;IACJ;IAEA,OAAOA,OAAO;EAClB,CAAC;EAEDzB,QAAQ,CAACI,SAAS,CAACuB,eAAe,GAAG,SAASA,eAAeA,CAAElB,GAAG,EAAEc,IAAI,EAAE;IACtE,IAAIQ,IAAI,GAAG,IAAI,CAACf,UAAU,CAACgB,OAAO,CAAC,CAAC;IACpC,IAAI3C,QAAQ;IAEZ,IAAIkC,IAAI,YAAY1C,cAAc,EAAE;MAChC,IAAIoD,KAAK,GAAGV,IAAI,CAACU,KAAK,CAAC,CAAC;MACxB,IAAIC,GAAG,GAAGX,IAAI,CAACW,GAAG,CAAC,CAAC;MACpB7C,QAAQ,GAAGoB,GAAG,CAAC0B,oBAAoB,CAACF,KAAK,CAACG,CAAC,EAAEH,KAAK,CAACI,CAAC,EAAEH,GAAG,CAACE,CAAC,EAAEF,GAAG,CAACG,CAAC,CAAC;IACvE,CAAC,MAAM,IAAId,IAAI,YAAYzC,cAAc,EAAE;MACvC,IAAIwD,MAAM,GAAGf,IAAI,CAACe,MAAM,CAAC,CAAC;MAC1BjD,QAAQ,GAAGoB,GAAG,CAAC8B,oBAAoB,CAACD,MAAM,CAACF,CAAC,EAAEE,MAAM,CAACD,CAAC,EAAE,CAAC,EAAEC,MAAM,CAACF,CAAC,EAAEE,MAAM,CAACD,CAAC,EAAEd,IAAI,CAACiB,MAAM,CAAC,CAAC,CAAC;IACjG;IAEApD,gBAAgB,CAACC,QAAQ,EAAEkC,IAAI,CAACjC,KAAK,CAAC;IAEtCmB,GAAG,CAACC,IAAI,CAAC,CAAC;IAEV,IAAI,CAACa,IAAI,CAACkB,SAAS,CAAC,CAAC,EAAE;MACnBhC,GAAG,CAACiC,SAAS,CAACX,IAAI,CAACY,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEZ,IAAI,CAACa,MAAM,CAAC,CAAC,EAAEb,IAAI,CAACc,MAAM,CAACT,CAAC,EAAEL,IAAI,CAACc,MAAM,CAACR,CAAC,CAAC;IAClF;IACA5B,GAAG,CAACoB,SAAS,GAAGxC,QAAQ;IACxBoB,GAAG,CAACc,IAAI,CAAC,CAAC;IAEVd,GAAG,CAACa,OAAO,CAAC,CAAC;EACjB,CAAC;EAEDtB,QAAQ,CAACI,SAAS,CAACwB,cAAc,GAAG,SAASA,cAAcA,CAAEnB,GAAG,EAAEqC,OAAO,EAAE;IACvE,IAAIC,IAAI,GAAGD,OAAO,CAACC,IAAI,CAAC,CAAC;IACzB,IAAIC,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IACpD,IAAIC,cAAc,GAAGH,aAAa,CAACI,UAAU,CAAC,IAAI,CAAC;IAEnDJ,aAAa,CAACL,KAAK,GAAGI,IAAI,CAACM,QAAQ,CAAC,CAAC;IACrCL,aAAa,CAACJ,MAAM,GAAGG,IAAI,CAACO,SAAS,CAAC,CAAC;IAEvC,IAAI,CAACC,UAAU,CAAC/D,MAAM,GAAG,CAAC;IAC1B,IAAI,CAACgE,YAAY,CAACV,OAAO,CAACW,QAAQ,CAAC;IAEnC,IAAIF,UAAU,GAAG,IAAI,CAACA,UAAU;IAChC,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,UAAU,CAAC/D,MAAM,EAAEkE,CAAC,EAAE,EAAE;MACxC,IAAIC,KAAK,GAAGJ,UAAU,CAACG,CAAC,CAAC;MACzBC,KAAK,CAACnD,QAAQ,CAAC2C,cAAc,CAAC;IAClC;IAEA1C,GAAG,CAACC,IAAI,CAAC,CAAC;IACVD,GAAG,CAACoB,SAAS,GAAGpB,GAAG,CAACmD,aAAa,CAACZ,aAAa,EAAE,QAAQ,CAAC;IAC1DvC,GAAG,CAACc,IAAI,CAAC,CAAC;IACVd,GAAG,CAACa,OAAO,CAAC,CAAC;EACjB,CAAC;EAEDtB,QAAQ,CAACI,SAAS,CAACiB,SAAS,GAAG,SAASA,SAASA,CAAEZ,GAAG,EAAE;IACpD,IAAIoD,MAAM,GAAG,IAAI,CAAC7C,UAAU,CAACQ,OAAO,CAACqC,MAAM;IAC3C,IAAIA,MAAM,IAAI,CAAClF,aAAa,CAACkF,MAAM,CAACnE,KAAK,CAAC,IAAImE,MAAM,CAAClB,KAAK,GAAG,CAAC,EAAE;MAC5DlC,GAAG,CAACqD,WAAW,GAAGD,MAAM,CAACnE,KAAK;MAC9Be,GAAG,CAACsD,SAAS,GAAGnF,cAAc,CAACiF,MAAM,CAAClB,KAAK,EAAE,CAAC,CAAC;MAC/ClC,GAAG,CAACuD,QAAQ,GAAGpF,cAAc,CAACiF,MAAM,CAACG,QAAQ,EAAEvD,GAAG,CAACuD,QAAQ,CAAC;MAE5DvD,GAAG,CAACC,IAAI,CAAC,CAAC;MACV,IAAI,CAACoB,WAAW,CAACrB,GAAG,EAAEoD,MAAM,CAACjE,OAAO,CAAC;MACrCa,GAAG,CAACoD,MAAM,CAAC,CAAC;MACZpD,GAAG,CAACa,OAAO,CAAC,CAAC;MAEb,OAAO,IAAI;IACf;EACJ,CAAC;EAEDtB,QAAQ,CAACI,SAAS,CAAC6D,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;IAC/C,IAAIJ,MAAM,GAAG,IAAI,CAAC7C,UAAU,CAACQ,OAAO,CAACqC,MAAM;IAC3C,IAAIA,MAAM,IAAIA,MAAM,CAACI,QAAQ,EAAE;MAC3B,OAAOJ,MAAM,CAACI,QAAQ,CAACC,WAAW,CAAC,CAAC;IACxC;EACJ,CAAC;EAEDlE,QAAQ,CAACI,SAAS,CAACa,WAAW,GAAG,SAASA,WAAWA,CAAER,GAAG,EAAE;IACxD,IAAIwD,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC;IAC9B,IAAIA,QAAQ,IAAIA,QAAQ,KAAKjF,KAAK,EAAE;MAChC,IAAImF,SAAS,GAAGpF,WAAW,CAACkF,QAAQ,CAAC;MACrC,IAAIxD,GAAG,CAACQ,WAAW,EAAE;QACjBR,GAAG,CAACQ,WAAW,CAACkD,SAAS,CAAC;MAC9B,CAAC,MAAM;QACH1D,GAAG,CAAC2D,OAAO,GAAGD,SAAS;QACvB1D,GAAG,CAAC4D,cAAc,GAAGF,SAAS;MAClC;IACJ;EACJ,CAAC;EAEDnE,QAAQ,CAACI,SAAS,CAACc,UAAU,GAAG,SAASA,UAAUA,CAAET,GAAG,EAAE;IACtD,IAAIwD,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC;IAC9B,IAAIJ,MAAM,GAAG,IAAI,CAAC7C,UAAU,CAACQ,OAAO,CAACqC,MAAM;IAC3C,IAAII,QAAQ,IAAIA,QAAQ,KAAKjF,KAAK,EAAE;MAChCyB,GAAG,CAAC6D,OAAO,GAAGrF,IAAI;IACtB,CAAC,MAAM,IAAI4E,MAAM,IAAIA,MAAM,CAACS,OAAO,EAAE;MACjC7D,GAAG,CAAC6D,OAAO,GAAGT,MAAM,CAACS,OAAO;IAChC;EACJ,CAAC;EAEDtE,QAAQ,CAACI,SAAS,CAACe,WAAW,GAAG,SAASA,WAAWA,CAAEV,GAAG,EAAE;IACxD,IAAIoD,MAAM,GAAG,IAAI,CAAC7C,UAAU,CAACQ,OAAO,CAACqC,MAAM;IAC3C,IAAIA,MAAM,IAAIA,MAAM,CAACG,QAAQ,EAAE;MAC3BvD,GAAG,CAACuD,QAAQ,GAAGH,MAAM,CAACG,QAAQ;IAClC;EACJ,CAAC;EAEDhE,QAAQ,CAACI,SAAS,CAACW,YAAY,GAAG,SAASA,YAAYA,CAAEN,GAAG,EAAE8D,IAAI,EAAE;IAChEpF,UAAU,CAACsB,GAAG,EAAE8D,IAAI,CAAC;EACzB,CAAC;EAED,OAAOvE,QAAQ;AACnB,CAAC,CAACvB,IAAI,CAAE;AAER,eAAeuB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}