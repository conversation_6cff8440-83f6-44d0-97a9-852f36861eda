{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst d = s => s.sort((a, t) => a.date.getTime() - t.date.getTime()),\n  o = s => {\n    let e;\n    return s.reduce((t, r) => (e !== r.date.getFullYear() ? (e = r.date.getFullYear(), t.push({\n      yearFlag: e\n    }, r)) : t.push(r), t), []);\n  };\nexport { o as addYearsFlags, d as sortEventList };", "map": {"version": 3, "names": ["d", "s", "sort", "a", "t", "date", "getTime", "o", "e", "reduce", "r", "getFullYear", "push", "yearFlag", "addYearsFlags", "sortEventList"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/timeline/utils.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst d = (s) => s.sort((a, t) => a.date.getTime() - t.date.getTime()), o = (s) => {\n  let e;\n  return s.reduce(\n    (t, r) => (e !== r.date.getFullYear() ? (e = r.date.getFullYear(), t.push({ yearFlag: e }, r)) : t.push(r), t),\n    []\n  );\n};\nexport {\n  o as addYearsFlags,\n  d as sortEventList\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAIC,CAAC,IAAKA,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,IAAI,CAACC,OAAO,CAAC,CAAC,GAAGF,CAAC,CAACC,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC;EAAEC,CAAC,GAAIN,CAAC,IAAK;IACjF,IAAIO,CAAC;IACL,OAAOP,CAAC,CAACQ,MAAM,CACb,CAACL,CAAC,EAAEM,CAAC,MAAMF,CAAC,KAAKE,CAAC,CAACL,IAAI,CAACM,WAAW,CAAC,CAAC,IAAIH,CAAC,GAAGE,CAAC,CAACL,IAAI,CAACM,WAAW,CAAC,CAAC,EAAEP,CAAC,CAACQ,IAAI,CAAC;MAAEC,QAAQ,EAAEL;IAAE,CAAC,EAAEE,CAAC,CAAC,IAAIN,CAAC,CAACQ,IAAI,CAACF,CAAC,CAAC,EAAEN,CAAC,CAAC,EAC9G,EACF,CAAC;EACH,CAAC;AACD,SACEG,CAAC,IAAIO,aAAa,EAClBd,CAAC,IAAIe,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}