{"ast": null, "code": "import { Path } from '../shapes/path';\nimport PathNode from './path-node';\nvar RectNode = function (PathNode) {\n  function RectNode() {\n    PathNode.apply(this, arguments);\n  }\n  if (PathNode) RectNode.__proto__ = PathNode;\n  RectNode.prototype = Object.create(PathNode && PathNode.prototype);\n  RectNode.prototype.constructor = RectNode;\n  RectNode.prototype.renderPoints = function renderPoints(ctx) {\n    var geometry = this.srcElement.geometry();\n    var ref = geometry.cornerRadius;\n    var rx = ref[0];\n    var ry = ref[1];\n    if (rx === 0 && ry === 0) {\n      var origin = geometry.origin;\n      var size = geometry.size;\n      ctx.rect(origin.x, origin.y, size.width, size.height);\n    } else {\n      PathNode.prototype.renderPoints.call(this, ctx, Path.fromRect(geometry));\n    }\n  };\n  return RectNode;\n}(PathNode);\nexport default RectNode;", "map": {"version": 3, "names": ["Path", "PathNode", "RectNode", "apply", "arguments", "__proto__", "prototype", "Object", "create", "constructor", "renderPoints", "ctx", "geometry", "srcElement", "ref", "cornerRadius", "rx", "ry", "origin", "size", "rect", "x", "y", "width", "height", "call", "fromRect"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/canvas/rect-node.js"], "sourcesContent": ["import { Path } from '../shapes/path';\nimport PathNode from './path-node';\n\nvar RectNode = (function (PathNode) {\n    function RectNode () {\n        PathNode.apply(this, arguments);\n    }\n\n    if ( PathNode ) RectNode.__proto__ = PathNode;\n    RectNode.prototype = Object.create( PathNode && PathNode.prototype );\n    RectNode.prototype.constructor = RectNode;\n\n    RectNode.prototype.renderPoints = function renderPoints (ctx) {\n        var geometry = this.srcElement.geometry();\n        var ref = geometry.cornerRadius;\n        var rx = ref[0];\n        var ry = ref[1];\n\n        if (rx === 0 && ry === 0) {\n            var origin = geometry.origin;\n            var size = geometry.size;\n            ctx.rect(origin.x, origin.y, size.width, size.height);\n        } else {\n            PathNode.prototype.renderPoints.call(this, ctx, Path.fromRect(geometry));\n        }\n    };\n\n    return RectNode;\n}(PathNode));\n\nexport default RectNode;\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,gBAAgB;AACrC,OAAOC,QAAQ,MAAM,aAAa;AAElC,IAAIC,QAAQ,GAAI,UAAUD,QAAQ,EAAE;EAChC,SAASC,QAAQA,CAAA,EAAI;IACjBD,QAAQ,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACnC;EAEA,IAAKH,QAAQ,EAAGC,QAAQ,CAACG,SAAS,GAAGJ,QAAQ;EAC7CC,QAAQ,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAEP,QAAQ,IAAIA,QAAQ,CAACK,SAAU,CAAC;EACpEJ,QAAQ,CAACI,SAAS,CAACG,WAAW,GAAGP,QAAQ;EAEzCA,QAAQ,CAACI,SAAS,CAACI,YAAY,GAAG,SAASA,YAAYA,CAAEC,GAAG,EAAE;IAC1D,IAAIC,QAAQ,GAAG,IAAI,CAACC,UAAU,CAACD,QAAQ,CAAC,CAAC;IACzC,IAAIE,GAAG,GAAGF,QAAQ,CAACG,YAAY;IAC/B,IAAIC,EAAE,GAAGF,GAAG,CAAC,CAAC,CAAC;IACf,IAAIG,EAAE,GAAGH,GAAG,CAAC,CAAC,CAAC;IAEf,IAAIE,EAAE,KAAK,CAAC,IAAIC,EAAE,KAAK,CAAC,EAAE;MACtB,IAAIC,MAAM,GAAGN,QAAQ,CAACM,MAAM;MAC5B,IAAIC,IAAI,GAAGP,QAAQ,CAACO,IAAI;MACxBR,GAAG,CAACS,IAAI,CAACF,MAAM,CAACG,CAAC,EAAEH,MAAM,CAACI,CAAC,EAAEH,IAAI,CAACI,KAAK,EAAEJ,IAAI,CAACK,MAAM,CAAC;IACzD,CAAC,MAAM;MACHvB,QAAQ,CAACK,SAAS,CAACI,YAAY,CAACe,IAAI,CAAC,IAAI,EAAEd,GAAG,EAAEX,IAAI,CAAC0B,QAAQ,CAACd,QAAQ,CAAC,CAAC;IAC5E;EACJ,CAAC;EAED,OAAOV,QAAQ;AACnB,CAAC,CAACD,QAAQ,CAAE;AAEZ,eAAeC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}