{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { isEqual as c } from \"@progress/kendo-date-math\";\nimport { MIDNIGHT_DATE as p } from \"../../utils.mjs\";\nimport { setSeconds as r, range as x } from \"../utils.mjs\";\nconst m = 60,\n  l = n => t => t % n,\n  g = l(m),\n  S = (n, t) => s => g(n + s * t),\n  a = (n, t) => g(m + n - t),\n  u = n => (t, s) => !s || t.getMinutes() === s.getMinutes() && t.getHours() === s.getHours() ? t : r(t, n),\n  I = u(0),\n  L = u(m - 1);\nclass D {\n  constructor(t) {\n    this.intl = t, this.toListItem = null, this.min = null, this.max = null, this.step = 0, this.insertUndividedMax = !1;\n  }\n  apply(t, s) {\n    return r(t, s.getSeconds());\n  }\n  configure(t) {\n    const {\n      insertUndividedMax: s = this.insertUndividedMax,\n      min: i = this.min,\n      max: o = this.max,\n      part: e,\n      step: h = this.step\n    } = t;\n    this.insertUndividedMax = s, this.toListItem = M => {\n      const d = r(p, M);\n      return {\n        text: this.intl.formatDate(d, e.pattern),\n        value: d\n      };\n    }, this.min = i, this.max = o, this.step = h;\n  }\n  data(t) {\n    const [s] = this.range(t),\n      i = S(s, this.step),\n      o = h => this.toListItem && this.toListItem(i(h)),\n      e = x(0, this.countFromMin(t)).map(o);\n    return this.addLast(e), t && this.addMissing(e, t), e;\n  }\n  isRangeChanged(t, s) {\n    return this.min !== null && this.max !== null && (!c(this.min, t) || !c(this.max, s));\n  }\n  limitRange(t, s, i) {\n    return [I(t, i), L(s, i)];\n  }\n  total(t) {\n    const s = this.insertUndividedMax && this.isLastMissing(t) ? 1 : 0,\n      i = this.isMissing(t) ? 1 : 0;\n    return this.countFromMin(t) + i + s;\n  }\n  selectedIndex(t) {\n    return Math.ceil(this.divideByStep(t));\n  }\n  valueInList(t) {\n    return t ? this.insertUndividedMax && this.lastSecond(t) === t.getSeconds() || !this.isMissing(t) : !0;\n  }\n  divideByStep(t) {\n    return a(t.getSeconds(), this.min.getSeconds()) / this.step;\n  }\n  addLast(t, s) {\n    return this.insertUndividedMax && this.isLastMissing(s) && this.toListItem && t.push(this.toListItem(this.lastSecond(s))), t;\n  }\n  addMissing(t, s) {\n    if (this.valueInList(s)) return t;\n    if (this.toListItem) {\n      const i = this.toListItem(s.getSeconds());\n      t.splice(this.selectedIndex(s), 0, i);\n    }\n    return t;\n  }\n  countFromMin(t) {\n    const [s, i] = this.range(t);\n    return Math.floor(a(i, s) / this.step) + 1;\n  }\n  isMissing(t) {\n    return t ? this.selectedIndex(t) !== this.divideByStep(t) : !1;\n  }\n  isLastMissing(t) {\n    return this.max !== null && this.isMissing(r(this.max, this.lastSecond(t)));\n  }\n  lastSecond(t) {\n    return this.range(t)[1];\n  }\n  range(t) {\n    const [s, i] = this.limitRange(this.min, this.max, t);\n    return [s.getSeconds(), i.getSeconds()];\n  }\n}\nexport { D as SecondsService };", "map": {"version": 3, "names": ["isEqual", "c", "MIDNIGHT_DATE", "p", "setSeconds", "r", "range", "x", "m", "l", "n", "t", "g", "S", "s", "a", "u", "getMinutes", "getHours", "I", "L", "D", "constructor", "intl", "toListItem", "min", "max", "step", "insertUndividedMax", "apply", "getSeconds", "configure", "i", "o", "part", "e", "h", "M", "d", "text", "formatDate", "pattern", "value", "data", "countFromMin", "map", "addLast", "addMissing", "isRangeChanged", "limitRange", "total", "isLastMissing", "isMissing", "selectedIndex", "Math", "ceil", "divideByStep", "valueInList", "lastSecond", "push", "splice", "floor", "SecondsService"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dateinputs/timepicker/services/SecondsService.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { isEqual as c } from \"@progress/kendo-date-math\";\nimport { MIDNIGHT_DATE as p } from \"../../utils.mjs\";\nimport { setSeconds as r, range as x } from \"../utils.mjs\";\nconst m = 60, l = (n) => (t) => t % n, g = l(m), S = (n, t) => (s) => g(n + s * t), a = (n, t) => g(m + n - t), u = (n) => (t, s) => !s || t.getMinutes() === s.getMinutes() && t.getHours() === s.getHours() ? t : r(t, n), I = u(0), L = u(m - 1);\nclass D {\n  constructor(t) {\n    this.intl = t, this.toListItem = null, this.min = null, this.max = null, this.step = 0, this.insertUndividedMax = !1;\n  }\n  apply(t, s) {\n    return r(t, s.getSeconds());\n  }\n  configure(t) {\n    const {\n      insertUndividedMax: s = this.insertUndividedMax,\n      min: i = this.min,\n      max: o = this.max,\n      part: e,\n      step: h = this.step\n    } = t;\n    this.insertUndividedMax = s, this.toListItem = (M) => {\n      const d = r(p, M);\n      return {\n        text: this.intl.formatDate(d, e.pattern),\n        value: d\n      };\n    }, this.min = i, this.max = o, this.step = h;\n  }\n  data(t) {\n    const [s] = this.range(t), i = S(s, this.step), o = (h) => this.toListItem && this.toListItem(i(h)), e = x(0, this.countFromMin(t)).map(o);\n    return this.addLast(e), t && this.addMissing(e, t), e;\n  }\n  isRangeChanged(t, s) {\n    return this.min !== null && this.max !== null && (!c(this.min, t) || !c(this.max, s));\n  }\n  limitRange(t, s, i) {\n    return [I(t, i), L(s, i)];\n  }\n  total(t) {\n    const s = this.insertUndividedMax && this.isLastMissing(t) ? 1 : 0, i = this.isMissing(t) ? 1 : 0;\n    return this.countFromMin(t) + i + s;\n  }\n  selectedIndex(t) {\n    return Math.ceil(this.divideByStep(t));\n  }\n  valueInList(t) {\n    return t ? this.insertUndividedMax && this.lastSecond(t) === t.getSeconds() || !this.isMissing(t) : !0;\n  }\n  divideByStep(t) {\n    return a(t.getSeconds(), this.min.getSeconds()) / this.step;\n  }\n  addLast(t, s) {\n    return this.insertUndividedMax && this.isLastMissing(s) && this.toListItem && t.push(this.toListItem(this.lastSecond(s))), t;\n  }\n  addMissing(t, s) {\n    if (this.valueInList(s))\n      return t;\n    if (this.toListItem) {\n      const i = this.toListItem(s.getSeconds());\n      t.splice(this.selectedIndex(s), 0, i);\n    }\n    return t;\n  }\n  countFromMin(t) {\n    const [s, i] = this.range(t);\n    return Math.floor(a(i, s) / this.step) + 1;\n  }\n  isMissing(t) {\n    return t ? this.selectedIndex(t) !== this.divideByStep(t) : !1;\n  }\n  isLastMissing(t) {\n    return this.max !== null && this.isMissing(r(this.max, this.lastSecond(t)));\n  }\n  lastSecond(t) {\n    return this.range(t)[1];\n  }\n  range(t) {\n    const [s, i] = this.limitRange(this.min, this.max, t);\n    return [s.getSeconds(), i.getSeconds()];\n  }\n}\nexport {\n  D as SecondsService\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,OAAO,IAAIC,CAAC,QAAQ,2BAA2B;AACxD,SAASC,aAAa,IAAIC,CAAC,QAAQ,iBAAiB;AACpD,SAASC,UAAU,IAAIC,CAAC,EAAEC,KAAK,IAAIC,CAAC,QAAQ,cAAc;AAC1D,MAAMC,CAAC,GAAG,EAAE;EAAEC,CAAC,GAAIC,CAAC,IAAMC,CAAC,IAAKA,CAAC,GAAGD,CAAC;EAAEE,CAAC,GAAGH,CAAC,CAACD,CAAC,CAAC;EAAEK,CAAC,GAAGA,CAACH,CAAC,EAAEC,CAAC,KAAMG,CAAC,IAAKF,CAAC,CAACF,CAAC,GAAGI,CAAC,GAAGH,CAAC,CAAC;EAAEI,CAAC,GAAGA,CAACL,CAAC,EAAEC,CAAC,KAAKC,CAAC,CAACJ,CAAC,GAAGE,CAAC,GAAGC,CAAC,CAAC;EAAEK,CAAC,GAAIN,CAAC,IAAK,CAACC,CAAC,EAAEG,CAAC,KAAK,CAACA,CAAC,IAAIH,CAAC,CAACM,UAAU,CAAC,CAAC,KAAKH,CAAC,CAACG,UAAU,CAAC,CAAC,IAAIN,CAAC,CAACO,QAAQ,CAAC,CAAC,KAAKJ,CAAC,CAACI,QAAQ,CAAC,CAAC,GAAGP,CAAC,GAAGN,CAAC,CAACM,CAAC,EAAED,CAAC,CAAC;EAAES,CAAC,GAAGH,CAAC,CAAC,CAAC,CAAC;EAAEI,CAAC,GAAGJ,CAAC,CAACR,CAAC,GAAG,CAAC,CAAC;AACnP,MAAMa,CAAC,CAAC;EACNC,WAAWA,CAACX,CAAC,EAAE;IACb,IAAI,CAACY,IAAI,GAAGZ,CAAC,EAAE,IAAI,CAACa,UAAU,GAAG,IAAI,EAAE,IAAI,CAACC,GAAG,GAAG,IAAI,EAAE,IAAI,CAACC,GAAG,GAAG,IAAI,EAAE,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE,IAAI,CAACC,kBAAkB,GAAG,CAAC,CAAC;EACtH;EACAC,KAAKA,CAAClB,CAAC,EAAEG,CAAC,EAAE;IACV,OAAOT,CAAC,CAACM,CAAC,EAAEG,CAAC,CAACgB,UAAU,CAAC,CAAC,CAAC;EAC7B;EACAC,SAASA,CAACpB,CAAC,EAAE;IACX,MAAM;MACJiB,kBAAkB,EAAEd,CAAC,GAAG,IAAI,CAACc,kBAAkB;MAC/CH,GAAG,EAAEO,CAAC,GAAG,IAAI,CAACP,GAAG;MACjBC,GAAG,EAAEO,CAAC,GAAG,IAAI,CAACP,GAAG;MACjBQ,IAAI,EAAEC,CAAC;MACPR,IAAI,EAAES,CAAC,GAAG,IAAI,CAACT;IACjB,CAAC,GAAGhB,CAAC;IACL,IAAI,CAACiB,kBAAkB,GAAGd,CAAC,EAAE,IAAI,CAACU,UAAU,GAAIa,CAAC,IAAK;MACpD,MAAMC,CAAC,GAAGjC,CAAC,CAACF,CAAC,EAAEkC,CAAC,CAAC;MACjB,OAAO;QACLE,IAAI,EAAE,IAAI,CAAChB,IAAI,CAACiB,UAAU,CAACF,CAAC,EAAEH,CAAC,CAACM,OAAO,CAAC;QACxCC,KAAK,EAAEJ;MACT,CAAC;IACH,CAAC,EAAE,IAAI,CAACb,GAAG,GAAGO,CAAC,EAAE,IAAI,CAACN,GAAG,GAAGO,CAAC,EAAE,IAAI,CAACN,IAAI,GAAGS,CAAC;EAC9C;EACAO,IAAIA,CAAChC,CAAC,EAAE;IACN,MAAM,CAACG,CAAC,CAAC,GAAG,IAAI,CAACR,KAAK,CAACK,CAAC,CAAC;MAAEqB,CAAC,GAAGnB,CAAC,CAACC,CAAC,EAAE,IAAI,CAACa,IAAI,CAAC;MAAEM,CAAC,GAAIG,CAAC,IAAK,IAAI,CAACZ,UAAU,IAAI,IAAI,CAACA,UAAU,CAACQ,CAAC,CAACI,CAAC,CAAC,CAAC;MAAED,CAAC,GAAG5B,CAAC,CAAC,CAAC,EAAE,IAAI,CAACqC,YAAY,CAACjC,CAAC,CAAC,CAAC,CAACkC,GAAG,CAACZ,CAAC,CAAC;IAC1I,OAAO,IAAI,CAACa,OAAO,CAACX,CAAC,CAAC,EAAExB,CAAC,IAAI,IAAI,CAACoC,UAAU,CAACZ,CAAC,EAAExB,CAAC,CAAC,EAAEwB,CAAC;EACvD;EACAa,cAAcA,CAACrC,CAAC,EAAEG,CAAC,EAAE;IACnB,OAAO,IAAI,CAACW,GAAG,KAAK,IAAI,IAAI,IAAI,CAACC,GAAG,KAAK,IAAI,KAAK,CAACzB,CAAC,CAAC,IAAI,CAACwB,GAAG,EAAEd,CAAC,CAAC,IAAI,CAACV,CAAC,CAAC,IAAI,CAACyB,GAAG,EAAEZ,CAAC,CAAC,CAAC;EACvF;EACAmC,UAAUA,CAACtC,CAAC,EAAEG,CAAC,EAAEkB,CAAC,EAAE;IAClB,OAAO,CAACb,CAAC,CAACR,CAAC,EAAEqB,CAAC,CAAC,EAAEZ,CAAC,CAACN,CAAC,EAAEkB,CAAC,CAAC,CAAC;EAC3B;EACAkB,KAAKA,CAACvC,CAAC,EAAE;IACP,MAAMG,CAAC,GAAG,IAAI,CAACc,kBAAkB,IAAI,IAAI,CAACuB,aAAa,CAACxC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;MAAEqB,CAAC,GAAG,IAAI,CAACoB,SAAS,CAACzC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IACjG,OAAO,IAAI,CAACiC,YAAY,CAACjC,CAAC,CAAC,GAAGqB,CAAC,GAAGlB,CAAC;EACrC;EACAuC,aAAaA,CAAC1C,CAAC,EAAE;IACf,OAAO2C,IAAI,CAACC,IAAI,CAAC,IAAI,CAACC,YAAY,CAAC7C,CAAC,CAAC,CAAC;EACxC;EACA8C,WAAWA,CAAC9C,CAAC,EAAE;IACb,OAAOA,CAAC,GAAG,IAAI,CAACiB,kBAAkB,IAAI,IAAI,CAAC8B,UAAU,CAAC/C,CAAC,CAAC,KAAKA,CAAC,CAACmB,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAACsB,SAAS,CAACzC,CAAC,CAAC,GAAG,CAAC,CAAC;EACxG;EACA6C,YAAYA,CAAC7C,CAAC,EAAE;IACd,OAAOI,CAAC,CAACJ,CAAC,CAACmB,UAAU,CAAC,CAAC,EAAE,IAAI,CAACL,GAAG,CAACK,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAACH,IAAI;EAC7D;EACAmB,OAAOA,CAACnC,CAAC,EAAEG,CAAC,EAAE;IACZ,OAAO,IAAI,CAACc,kBAAkB,IAAI,IAAI,CAACuB,aAAa,CAACrC,CAAC,CAAC,IAAI,IAAI,CAACU,UAAU,IAAIb,CAAC,CAACgD,IAAI,CAAC,IAAI,CAACnC,UAAU,CAAC,IAAI,CAACkC,UAAU,CAAC5C,CAAC,CAAC,CAAC,CAAC,EAAEH,CAAC;EAC9H;EACAoC,UAAUA,CAACpC,CAAC,EAAEG,CAAC,EAAE;IACf,IAAI,IAAI,CAAC2C,WAAW,CAAC3C,CAAC,CAAC,EACrB,OAAOH,CAAC;IACV,IAAI,IAAI,CAACa,UAAU,EAAE;MACnB,MAAMQ,CAAC,GAAG,IAAI,CAACR,UAAU,CAACV,CAAC,CAACgB,UAAU,CAAC,CAAC,CAAC;MACzCnB,CAAC,CAACiD,MAAM,CAAC,IAAI,CAACP,aAAa,CAACvC,CAAC,CAAC,EAAE,CAAC,EAAEkB,CAAC,CAAC;IACvC;IACA,OAAOrB,CAAC;EACV;EACAiC,YAAYA,CAACjC,CAAC,EAAE;IACd,MAAM,CAACG,CAAC,EAAEkB,CAAC,CAAC,GAAG,IAAI,CAAC1B,KAAK,CAACK,CAAC,CAAC;IAC5B,OAAO2C,IAAI,CAACO,KAAK,CAAC9C,CAAC,CAACiB,CAAC,EAAElB,CAAC,CAAC,GAAG,IAAI,CAACa,IAAI,CAAC,GAAG,CAAC;EAC5C;EACAyB,SAASA,CAACzC,CAAC,EAAE;IACX,OAAOA,CAAC,GAAG,IAAI,CAAC0C,aAAa,CAAC1C,CAAC,CAAC,KAAK,IAAI,CAAC6C,YAAY,CAAC7C,CAAC,CAAC,GAAG,CAAC,CAAC;EAChE;EACAwC,aAAaA,CAACxC,CAAC,EAAE;IACf,OAAO,IAAI,CAACe,GAAG,KAAK,IAAI,IAAI,IAAI,CAAC0B,SAAS,CAAC/C,CAAC,CAAC,IAAI,CAACqB,GAAG,EAAE,IAAI,CAACgC,UAAU,CAAC/C,CAAC,CAAC,CAAC,CAAC;EAC7E;EACA+C,UAAUA,CAAC/C,CAAC,EAAE;IACZ,OAAO,IAAI,CAACL,KAAK,CAACK,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB;EACAL,KAAKA,CAACK,CAAC,EAAE;IACP,MAAM,CAACG,CAAC,EAAEkB,CAAC,CAAC,GAAG,IAAI,CAACiB,UAAU,CAAC,IAAI,CAACxB,GAAG,EAAE,IAAI,CAACC,GAAG,EAAEf,CAAC,CAAC;IACrD,OAAO,CAACG,CAAC,CAACgB,UAAU,CAAC,CAAC,EAAEE,CAAC,CAACF,UAAU,CAAC,CAAC,CAAC;EACzC;AACF;AACA,SACET,CAAC,IAAIyC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}