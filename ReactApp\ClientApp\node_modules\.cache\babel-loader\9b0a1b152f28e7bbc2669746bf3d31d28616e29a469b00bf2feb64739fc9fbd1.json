{"ast": null, "code": "import OptionsStore from '../core/options-store';\nimport withOptions from '../mixins/with-options';\nimport HasObservers from '../core/has-observers';\nimport { defined } from '../util';\nvar options = [\"offset\", \"color\", \"opacity\"];\nvar GradientStop = function (superclass) {\n  function GradientStop(offset, color, opacity) {\n    superclass.call(this);\n    this.options = new OptionsStore({\n      offset: offset,\n      color: color,\n      opacity: defined(opacity) ? opacity : 1\n    });\n    this.options.addObserver(this);\n  }\n  if (superclass) GradientStop.__proto__ = superclass;\n  GradientStop.prototype = Object.create(superclass && superclass.prototype);\n  GradientStop.prototype.constructor = GradientStop;\n  GradientStop.create = function create(arg) {\n    if (defined(arg)) {\n      var stop;\n      if (arg instanceof GradientStop) {\n        stop = arg;\n      } else if (arg.length > 1) {\n        stop = new GradientStop(arg[0], arg[1], arg[2]);\n      } else {\n        stop = new GradientStop(arg.offset, arg.color, arg.opacity);\n      }\n      return stop;\n    }\n  };\n  return GradientStop;\n}(withOptions(HasObservers, options));\nexport default GradientStop;", "map": {"version": 3, "names": ["OptionsStore", "withOptions", "HasObservers", "defined", "options", "GradientStop", "superclass", "offset", "color", "opacity", "call", "addObserver", "__proto__", "prototype", "Object", "create", "constructor", "arg", "stop", "length"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/gradients/gradient-stop.js"], "sourcesContent": ["import OptionsStore from '../core/options-store';\nimport withOptions from '../mixins/with-options';\nimport HasObservers from '../core/has-observers';\nimport { defined } from '../util';\n\n\nvar options = [ \"offset\", \"color\", \"opacity\" ];\n\nvar GradientStop = (function (superclass) {\n    function GradientStop(offset, color, opacity) {\n        superclass.call(this);\n\n        this.options = new OptionsStore({\n            offset: offset,\n            color: color,\n            opacity: defined(opacity) ? opacity : 1\n        });\n\n        this.options.addObserver(this);\n    }\n\n    if ( superclass ) GradientStop.__proto__ = superclass;\n    GradientStop.prototype = Object.create( superclass && superclass.prototype );\n    GradientStop.prototype.constructor = GradientStop;\n\n    GradientStop.create = function create (arg) {\n        if (defined(arg)) {\n            var stop;\n            if (arg instanceof GradientStop) {\n                stop = arg;\n            } else if (arg.length > 1) {\n                stop = new GradientStop(arg[0], arg[1], arg[2]);\n            } else {\n                stop = new GradientStop(arg.offset, arg.color, arg.opacity);\n            }\n\n            return stop;\n        }\n    };\n\n    return GradientStop;\n}(withOptions(HasObservers, options)));\n\nexport default GradientStop;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,uBAAuB;AAChD,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,YAAY,MAAM,uBAAuB;AAChD,SAASC,OAAO,QAAQ,SAAS;AAGjC,IAAIC,OAAO,GAAG,CAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAE;AAE9C,IAAIC,YAAY,GAAI,UAAUC,UAAU,EAAE;EACtC,SAASD,YAAYA,CAACE,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAE;IAC1CH,UAAU,CAACI,IAAI,CAAC,IAAI,CAAC;IAErB,IAAI,CAACN,OAAO,GAAG,IAAIJ,YAAY,CAAC;MAC5BO,MAAM,EAAEA,MAAM;MACdC,KAAK,EAAEA,KAAK;MACZC,OAAO,EAAEN,OAAO,CAACM,OAAO,CAAC,GAAGA,OAAO,GAAG;IAC1C,CAAC,CAAC;IAEF,IAAI,CAACL,OAAO,CAACO,WAAW,CAAC,IAAI,CAAC;EAClC;EAEA,IAAKL,UAAU,EAAGD,YAAY,CAACO,SAAS,GAAGN,UAAU;EACrDD,YAAY,CAACQ,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAET,UAAU,IAAIA,UAAU,CAACO,SAAU,CAAC;EAC5ER,YAAY,CAACQ,SAAS,CAACG,WAAW,GAAGX,YAAY;EAEjDA,YAAY,CAACU,MAAM,GAAG,SAASA,MAAMA,CAAEE,GAAG,EAAE;IACxC,IAAId,OAAO,CAACc,GAAG,CAAC,EAAE;MACd,IAAIC,IAAI;MACR,IAAID,GAAG,YAAYZ,YAAY,EAAE;QAC7Ba,IAAI,GAAGD,GAAG;MACd,CAAC,MAAM,IAAIA,GAAG,CAACE,MAAM,GAAG,CAAC,EAAE;QACvBD,IAAI,GAAG,IAAIb,YAAY,CAACY,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC;MACnD,CAAC,MAAM;QACHC,IAAI,GAAG,IAAIb,YAAY,CAACY,GAAG,CAACV,MAAM,EAAEU,GAAG,CAACT,KAAK,EAAES,GAAG,CAACR,OAAO,CAAC;MAC/D;MAEA,OAAOS,IAAI;IACf;EACJ,CAAC;EAED,OAAOb,YAAY;AACvB,CAAC,CAACJ,WAAW,CAACC,YAAY,EAAEE,OAAO,CAAC,CAAE;AAEtC,eAAeC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}