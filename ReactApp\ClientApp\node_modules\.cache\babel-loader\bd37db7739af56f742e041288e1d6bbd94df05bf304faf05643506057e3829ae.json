{"ast": null, "code": "import { setYear } from './set-year';\n/**\n * @hidden\n */\nexport var normalizeYear = function (value, year) {\n  return setYear(value, year(value.getFullYear()));\n};", "map": {"version": 3, "names": ["setYear", "normalizeYear", "value", "year", "getFullYear"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/normalize-year.js"], "sourcesContent": ["import { setYear } from './set-year';\n/**\n * @hidden\n */\nexport var normalizeYear = function (value, year) { return (setYear(value, year(value.getFullYear()))); };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC;AACA;AACA;AACA,OAAO,IAAIC,aAAa,GAAG,SAAAA,CAAUC,KAAK,EAAEC,IAAI,EAAE;EAAE,OAAQH,OAAO,CAACE,KAAK,EAAEC,IAAI,CAACD,KAAK,CAACE,WAAW,CAAC,CAAC,CAAC,CAAC;AAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}