{"ast": null, "code": "/**\n * A function that calculates duration in years between two `Date` objects.\n *\n * @param start - The start date value.\n * @param end - The end date value.\n * @returns - The duration in years.\n *\n * @example\n * ```ts-no-run\n * durationInYears(new Date(2016, 0, 1), new Date(2028, 0, 1)); // 12\n * durationInYears(new Date(2016, 0, 1), new Date(2022, 0, 1)); // 6\n * durationInYears(new Date(2016, 0, 1), new Date(2016, 0, 1)); // 0\n * ```\n */\nexport var durationInYears = function (start, end) {\n  return end.getFullYear() - start.getFullYear();\n};", "map": {"version": 3, "names": ["durationInYears", "start", "end", "getFullYear"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/duration-in-years.js"], "sourcesContent": ["/**\n * A function that calculates duration in years between two `Date` objects.\n *\n * @param start - The start date value.\n * @param end - The end date value.\n * @returns - The duration in years.\n *\n * @example\n * ```ts-no-run\n * durationInYears(new Date(2016, 0, 1), new Date(2028, 0, 1)); // 12\n * durationInYears(new Date(2016, 0, 1), new Date(2022, 0, 1)); // 6\n * durationInYears(new Date(2016, 0, 1), new Date(2016, 0, 1)); // 0\n * ```\n */\nexport var durationInYears = function (start, end) { return (end.getFullYear() - start.getFullYear()); };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIA,eAAe,GAAG,SAAAA,CAAUC,KAAK,EAAEC,GAAG,EAAE;EAAE,OAAQA,GAAG,CAACC,WAAW,CAAC,CAAC,GAAGF,KAAK,CAACE,WAAW,CAAC,CAAC;AAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}