{"ast": null, "code": "import Element from './element';\nimport traversable from '../mixins/traversable';\nimport { append } from '../util';\nimport elementsBoundingBox from './utils/elements-bounding-box';\nimport elementsClippedBoundingBox from './utils/elements-clippend-bounding-box';\nvar Group = function (superclass) {\n  function Group(options) {\n    superclass.call(this, options);\n    this.children = [];\n  }\n  if (superclass) Group.__proto__ = superclass;\n  Group.prototype = Object.create(superclass && superclass.prototype);\n  Group.prototype.constructor = Group;\n  var prototypeAccessors = {\n    nodeType: {\n      configurable: true\n    }\n  };\n  prototypeAccessors.nodeType.get = function () {\n    return \"Group\";\n  };\n  Group.prototype.childrenChange = function childrenChange(action, items, index) {\n    this.trigger(\"childrenChange\", {\n      action: action,\n      items: items,\n      index: index\n    });\n  };\n  Group.prototype.append = function append$1() {\n    append(this.children, arguments);\n    this._reparent(arguments, this);\n    this.childrenChange(\"add\", arguments);\n    return this;\n  };\n  Group.prototype.insert = function insert(index, element) {\n    this.children.splice(index, 0, element);\n    element.parent = this;\n    this.childrenChange(\"add\", [element], index);\n    return this;\n  };\n  Group.prototype.insertAt = function insertAt(element, index) {\n    return this.insert(index, element);\n  };\n  Group.prototype.remove = function remove(element) {\n    var index = this.children.indexOf(element);\n    if (index >= 0) {\n      this.children.splice(index, 1);\n      element.parent = null;\n      this.childrenChange(\"remove\", [element], index);\n    }\n    return this;\n  };\n  Group.prototype.removeAt = function removeAt(index) {\n    if (0 <= index && index < this.children.length) {\n      var element = this.children[index];\n      this.children.splice(index, 1);\n      element.parent = null;\n      this.childrenChange(\"remove\", [element], index);\n    }\n    return this;\n  };\n  Group.prototype.clear = function clear() {\n    var items = this.children;\n    this.children = [];\n    this._reparent(items, null);\n    this.childrenChange(\"remove\", items, 0);\n    return this;\n  };\n  Group.prototype.bbox = function bbox(transformation) {\n    return elementsBoundingBox(this.children, true, this.currentTransform(transformation));\n  };\n  Group.prototype.rawBBox = function rawBBox() {\n    return elementsBoundingBox(this.children, false);\n  };\n  Group.prototype._clippedBBox = function _clippedBBox(transformation) {\n    return elementsClippedBoundingBox(this.children, this.currentTransform(transformation));\n  };\n  Group.prototype.currentTransform = function currentTransform(transformation) {\n    return Element.prototype.currentTransform.call(this, transformation) || null;\n  };\n  Group.prototype.containsPoint = function containsPoint(point, parentTransform) {\n    if (this.visible()) {\n      var children = this.children;\n      var transform = this.currentTransform(parentTransform);\n      for (var idx = 0; idx < children.length; idx++) {\n        if (children[idx].containsPoint(point, transform)) {\n          return true;\n        }\n      }\n    }\n    return false;\n  };\n  Group.prototype._reparent = function _reparent(elements, newParent) {\n    var this$1 = this;\n    for (var i = 0; i < elements.length; i++) {\n      var child = elements[i];\n      var parent = child.parent;\n      if (parent && parent !== this$1 && parent.remove) {\n        parent.remove(child);\n      }\n      child.parent = newParent;\n    }\n  };\n  Object.defineProperties(Group.prototype, prototypeAccessors);\n  return Group;\n}(traversable(Element, \"children\"));\nexport default Group;", "map": {"version": 3, "names": ["Element", "traversable", "append", "elementsBoundingBox", "elementsClippedBoundingBox", "Group", "superclass", "options", "call", "children", "__proto__", "prototype", "Object", "create", "constructor", "prototypeAccessors", "nodeType", "configurable", "get", "<PERSON><PERSON><PERSON><PERSON>", "action", "items", "index", "trigger", "append$1", "arguments", "_reparent", "insert", "element", "splice", "parent", "insertAt", "remove", "indexOf", "removeAt", "length", "clear", "bbox", "transformation", "currentTransform", "rawBBox", "_clipped<PERSON>ox", "containsPoint", "point", "parentTransform", "visible", "transform", "idx", "elements", "newParent", "this$1", "i", "child", "defineProperties"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/shapes/group.js"], "sourcesContent": ["import Element from './element';\nimport traversable from '../mixins/traversable';\nimport { append } from '../util';\nimport elementsBoundingBox from './utils/elements-bounding-box';\nimport elementsClippedBoundingBox from './utils/elements-clippend-bounding-box';\n\n\nvar Group = (function (superclass) {\n    function Group(options) {\n        superclass.call(this, options);\n        this.children = [];\n    }\n\n    if ( superclass ) Group.__proto__ = superclass;\n    Group.prototype = Object.create( superclass && superclass.prototype );\n    Group.prototype.constructor = Group;\n\n    var prototypeAccessors = { nodeType: { configurable: true } };\n\n    prototypeAccessors.nodeType.get = function () {\n        return \"Group\";\n    };\n\n    Group.prototype.childrenChange = function childrenChange (action, items, index) {\n        this.trigger(\"childrenChange\",{\n            action: action,\n            items: items,\n            index: index\n        });\n    };\n\n    Group.prototype.append = function append$1 () {\n        append(this.children, arguments);\n        this._reparent(arguments, this);\n\n        this.childrenChange(\"add\", arguments);\n\n        return this;\n    };\n\n    Group.prototype.insert = function insert (index, element) {\n        this.children.splice(index, 0, element);\n        element.parent = this;\n\n        this.childrenChange(\"add\", [ element ], index);\n\n        return this;\n    };\n\n    Group.prototype.insertAt = function insertAt (element, index) {\n        return this.insert(index, element);\n    };\n\n    Group.prototype.remove = function remove (element) {\n        var index = this.children.indexOf(element);\n        if (index >= 0) {\n            this.children.splice(index, 1);\n            element.parent = null;\n            this.childrenChange(\"remove\", [ element ], index);\n        }\n\n        return this;\n    };\n\n    Group.prototype.removeAt = function removeAt (index) {\n        if (0 <= index && index < this.children.length) {\n            var element = this.children[index];\n            this.children.splice(index, 1);\n            element.parent = null;\n            this.childrenChange(\"remove\", [ element ], index);\n        }\n\n        return this;\n    };\n\n    Group.prototype.clear = function clear () {\n        var items = this.children;\n        this.children = [];\n        this._reparent(items, null);\n\n        this.childrenChange(\"remove\", items, 0);\n\n        return this;\n    };\n\n    Group.prototype.bbox = function bbox (transformation) {\n        return elementsBoundingBox(this.children, true, this.currentTransform(transformation));\n    };\n\n    Group.prototype.rawBBox = function rawBBox () {\n        return elementsBoundingBox(this.children, false);\n    };\n\n    Group.prototype._clippedBBox = function _clippedBBox (transformation) {\n        return elementsClippedBoundingBox(this.children, this.currentTransform(transformation));\n    };\n\n    Group.prototype.currentTransform = function currentTransform (transformation) {\n        return Element.prototype.currentTransform.call(this, transformation) || null;\n    };\n\n    Group.prototype.containsPoint = function containsPoint (point, parentTransform) {\n        if (this.visible()) {\n            var children = this.children;\n            var transform = this.currentTransform(parentTransform);\n            for (var idx = 0; idx < children.length; idx++) {\n                if (children[idx].containsPoint(point, transform)) {\n                    return true;\n                }\n            }\n        }\n        return false;\n    };\n\n    Group.prototype._reparent = function _reparent (elements, newParent) {\n        var this$1 = this;\n\n        for (var i = 0; i < elements.length; i++) {\n            var child = elements[i];\n            var parent = child.parent;\n            if (parent && parent !== this$1 && parent.remove) {\n                parent.remove(child);\n            }\n\n            child.parent = newParent;\n        }\n    };\n\n    Object.defineProperties( Group.prototype, prototypeAccessors );\n\n    return Group;\n}(traversable(Element, \"children\")));\n\nexport default Group;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,WAAW;AAC/B,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,SAASC,MAAM,QAAQ,SAAS;AAChC,OAAOC,mBAAmB,MAAM,+BAA+B;AAC/D,OAAOC,0BAA0B,MAAM,wCAAwC;AAG/E,IAAIC,KAAK,GAAI,UAAUC,UAAU,EAAE;EAC/B,SAASD,KAAKA,CAACE,OAAO,EAAE;IACpBD,UAAU,CAACE,IAAI,CAAC,IAAI,EAAED,OAAO,CAAC;IAC9B,IAAI,CAACE,QAAQ,GAAG,EAAE;EACtB;EAEA,IAAKH,UAAU,EAAGD,KAAK,CAACK,SAAS,GAAGJ,UAAU;EAC9CD,KAAK,CAACM,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAEP,UAAU,IAAIA,UAAU,CAACK,SAAU,CAAC;EACrEN,KAAK,CAACM,SAAS,CAACG,WAAW,GAAGT,KAAK;EAEnC,IAAIU,kBAAkB,GAAG;IAAEC,QAAQ,EAAE;MAAEC,YAAY,EAAE;IAAK;EAAE,CAAC;EAE7DF,kBAAkB,CAACC,QAAQ,CAACE,GAAG,GAAG,YAAY;IAC1C,OAAO,OAAO;EAClB,CAAC;EAEDb,KAAK,CAACM,SAAS,CAACQ,cAAc,GAAG,SAASA,cAAcA,CAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAE;IAC5E,IAAI,CAACC,OAAO,CAAC,gBAAgB,EAAC;MAC1BH,MAAM,EAAEA,MAAM;MACdC,KAAK,EAAEA,KAAK;MACZC,KAAK,EAAEA;IACX,CAAC,CAAC;EACN,CAAC;EAEDjB,KAAK,CAACM,SAAS,CAACT,MAAM,GAAG,SAASsB,QAAQA,CAAA,EAAI;IAC1CtB,MAAM,CAAC,IAAI,CAACO,QAAQ,EAAEgB,SAAS,CAAC;IAChC,IAAI,CAACC,SAAS,CAACD,SAAS,EAAE,IAAI,CAAC;IAE/B,IAAI,CAACN,cAAc,CAAC,KAAK,EAAEM,SAAS,CAAC;IAErC,OAAO,IAAI;EACf,CAAC;EAEDpB,KAAK,CAACM,SAAS,CAACgB,MAAM,GAAG,SAASA,MAAMA,CAAEL,KAAK,EAAEM,OAAO,EAAE;IACtD,IAAI,CAACnB,QAAQ,CAACoB,MAAM,CAACP,KAAK,EAAE,CAAC,EAAEM,OAAO,CAAC;IACvCA,OAAO,CAACE,MAAM,GAAG,IAAI;IAErB,IAAI,CAACX,cAAc,CAAC,KAAK,EAAE,CAAES,OAAO,CAAE,EAAEN,KAAK,CAAC;IAE9C,OAAO,IAAI;EACf,CAAC;EAEDjB,KAAK,CAACM,SAAS,CAACoB,QAAQ,GAAG,SAASA,QAAQA,CAAEH,OAAO,EAAEN,KAAK,EAAE;IAC1D,OAAO,IAAI,CAACK,MAAM,CAACL,KAAK,EAAEM,OAAO,CAAC;EACtC,CAAC;EAEDvB,KAAK,CAACM,SAAS,CAACqB,MAAM,GAAG,SAASA,MAAMA,CAAEJ,OAAO,EAAE;IAC/C,IAAIN,KAAK,GAAG,IAAI,CAACb,QAAQ,CAACwB,OAAO,CAACL,OAAO,CAAC;IAC1C,IAAIN,KAAK,IAAI,CAAC,EAAE;MACZ,IAAI,CAACb,QAAQ,CAACoB,MAAM,CAACP,KAAK,EAAE,CAAC,CAAC;MAC9BM,OAAO,CAACE,MAAM,GAAG,IAAI;MACrB,IAAI,CAACX,cAAc,CAAC,QAAQ,EAAE,CAAES,OAAO,CAAE,EAAEN,KAAK,CAAC;IACrD;IAEA,OAAO,IAAI;EACf,CAAC;EAEDjB,KAAK,CAACM,SAAS,CAACuB,QAAQ,GAAG,SAASA,QAAQA,CAAEZ,KAAK,EAAE;IACjD,IAAI,CAAC,IAAIA,KAAK,IAAIA,KAAK,GAAG,IAAI,CAACb,QAAQ,CAAC0B,MAAM,EAAE;MAC5C,IAAIP,OAAO,GAAG,IAAI,CAACnB,QAAQ,CAACa,KAAK,CAAC;MAClC,IAAI,CAACb,QAAQ,CAACoB,MAAM,CAACP,KAAK,EAAE,CAAC,CAAC;MAC9BM,OAAO,CAACE,MAAM,GAAG,IAAI;MACrB,IAAI,CAACX,cAAc,CAAC,QAAQ,EAAE,CAAES,OAAO,CAAE,EAAEN,KAAK,CAAC;IACrD;IAEA,OAAO,IAAI;EACf,CAAC;EAEDjB,KAAK,CAACM,SAAS,CAACyB,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAI;IACtC,IAAIf,KAAK,GAAG,IAAI,CAACZ,QAAQ;IACzB,IAAI,CAACA,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACiB,SAAS,CAACL,KAAK,EAAE,IAAI,CAAC;IAE3B,IAAI,CAACF,cAAc,CAAC,QAAQ,EAAEE,KAAK,EAAE,CAAC,CAAC;IAEvC,OAAO,IAAI;EACf,CAAC;EAEDhB,KAAK,CAACM,SAAS,CAAC0B,IAAI,GAAG,SAASA,IAAIA,CAAEC,cAAc,EAAE;IAClD,OAAOnC,mBAAmB,CAAC,IAAI,CAACM,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC8B,gBAAgB,CAACD,cAAc,CAAC,CAAC;EAC1F,CAAC;EAEDjC,KAAK,CAACM,SAAS,CAAC6B,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;IAC1C,OAAOrC,mBAAmB,CAAC,IAAI,CAACM,QAAQ,EAAE,KAAK,CAAC;EACpD,CAAC;EAEDJ,KAAK,CAACM,SAAS,CAAC8B,YAAY,GAAG,SAASA,YAAYA,CAAEH,cAAc,EAAE;IAClE,OAAOlC,0BAA0B,CAAC,IAAI,CAACK,QAAQ,EAAE,IAAI,CAAC8B,gBAAgB,CAACD,cAAc,CAAC,CAAC;EAC3F,CAAC;EAEDjC,KAAK,CAACM,SAAS,CAAC4B,gBAAgB,GAAG,SAASA,gBAAgBA,CAAED,cAAc,EAAE;IAC1E,OAAOtC,OAAO,CAACW,SAAS,CAAC4B,gBAAgB,CAAC/B,IAAI,CAAC,IAAI,EAAE8B,cAAc,CAAC,IAAI,IAAI;EAChF,CAAC;EAEDjC,KAAK,CAACM,SAAS,CAAC+B,aAAa,GAAG,SAASA,aAAaA,CAAEC,KAAK,EAAEC,eAAe,EAAE;IAC5E,IAAI,IAAI,CAACC,OAAO,CAAC,CAAC,EAAE;MAChB,IAAIpC,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC5B,IAAIqC,SAAS,GAAG,IAAI,CAACP,gBAAgB,CAACK,eAAe,CAAC;MACtD,KAAK,IAAIG,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGtC,QAAQ,CAAC0B,MAAM,EAAEY,GAAG,EAAE,EAAE;QAC5C,IAAItC,QAAQ,CAACsC,GAAG,CAAC,CAACL,aAAa,CAACC,KAAK,EAAEG,SAAS,CAAC,EAAE;UAC/C,OAAO,IAAI;QACf;MACJ;IACJ;IACA,OAAO,KAAK;EAChB,CAAC;EAEDzC,KAAK,CAACM,SAAS,CAACe,SAAS,GAAG,SAASA,SAASA,CAAEsB,QAAQ,EAAEC,SAAS,EAAE;IACjE,IAAIC,MAAM,GAAG,IAAI;IAEjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,QAAQ,CAACb,MAAM,EAAEgB,CAAC,EAAE,EAAE;MACtC,IAAIC,KAAK,GAAGJ,QAAQ,CAACG,CAAC,CAAC;MACvB,IAAIrB,MAAM,GAAGsB,KAAK,CAACtB,MAAM;MACzB,IAAIA,MAAM,IAAIA,MAAM,KAAKoB,MAAM,IAAIpB,MAAM,CAACE,MAAM,EAAE;QAC9CF,MAAM,CAACE,MAAM,CAACoB,KAAK,CAAC;MACxB;MAEAA,KAAK,CAACtB,MAAM,GAAGmB,SAAS;IAC5B;EACJ,CAAC;EAEDrC,MAAM,CAACyC,gBAAgB,CAAEhD,KAAK,CAACM,SAAS,EAAEI,kBAAmB,CAAC;EAE9D,OAAOV,KAAK;AAChB,CAAC,CAACJ,WAAW,CAACD,OAAO,EAAE,UAAU,CAAC,CAAE;AAEpC,eAAeK,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}