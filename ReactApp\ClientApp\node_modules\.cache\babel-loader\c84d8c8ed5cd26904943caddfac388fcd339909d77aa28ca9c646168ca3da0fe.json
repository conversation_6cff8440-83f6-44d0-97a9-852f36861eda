{"ast": null, "code": "import LinearGradient from '../gradients/linear-gradient';\nimport RadialGradient from '../gradients/radial-gradient';\nimport LinearGradientNode from './linear-gradient-node';\nimport RadialGradientNode from './radial-gradient-node';\nimport { PatternNode } from './pattern-node';\nimport Node from './node';\nimport ClipNode from './clip-node';\nimport { PATTERN } from '../core/constants';\nvar DefinitionNode = function (Node) {\n  function DefinitionNode() {\n    Node.call(this);\n    this.definitionMap = {};\n  }\n  if (Node) DefinitionNode.__proto__ = Node;\n  DefinitionNode.prototype = Object.create(Node && Node.prototype);\n  DefinitionNode.prototype.constructor = DefinitionNode;\n  DefinitionNode.prototype.attachTo = function attachTo(domElement) {\n    this.element = domElement;\n  };\n  DefinitionNode.prototype.template = function template() {\n    return \"<defs>\" + this.renderChildren() + \"</defs>\";\n  };\n  DefinitionNode.prototype.definitionChange = function definitionChange(e) {\n    var definitions = e.definitions;\n    var action = e.action;\n    if (action === \"add\") {\n      this.addDefinitions(definitions);\n    } else if (action === \"remove\") {\n      this.removeDefinitions(definitions);\n    }\n  };\n  DefinitionNode.prototype.createDefinition = function createDefinition(type, item) {\n    var nodeType;\n    if (type === \"clip\") {\n      nodeType = ClipNode;\n    } else if (type === \"fill\") {\n      if (item instanceof LinearGradient) {\n        nodeType = LinearGradientNode;\n      } else if (item instanceof RadialGradient) {\n        nodeType = RadialGradientNode;\n      } else if (item.nodeType === PATTERN) {\n        nodeType = PatternNode;\n      }\n    }\n    return new nodeType(item);\n  };\n  DefinitionNode.prototype.addDefinitions = function addDefinitions(definitions) {\n    var this$1 = this;\n    for (var field in definitions) {\n      this$1.addDefinition(field, definitions[field]);\n    }\n  };\n  DefinitionNode.prototype.addDefinition = function addDefinition(type, srcElement) {\n    var ref = this;\n    var element = ref.element;\n    var definitionMap = ref.definitionMap;\n    var id = srcElement.id;\n    var mapItem = definitionMap[id];\n    if (!mapItem) {\n      var node = this.createDefinition(type, srcElement);\n      definitionMap[id] = {\n        element: node,\n        count: 1\n      };\n      this.append(node);\n      if (element) {\n        node.attachTo(this.element);\n      }\n    } else {\n      mapItem.count++;\n    }\n  };\n  DefinitionNode.prototype.removeDefinitions = function removeDefinitions(definitions) {\n    var this$1 = this;\n    for (var field in definitions) {\n      this$1.removeDefinition(definitions[field]);\n    }\n  };\n  DefinitionNode.prototype.removeDefinition = function removeDefinition(srcElement) {\n    var definitionMap = this.definitionMap;\n    var id = srcElement.id;\n    var mapItem = definitionMap[id];\n    if (mapItem) {\n      mapItem.count--;\n      if (mapItem.count === 0) {\n        this.remove(this.childNodes.indexOf(mapItem.element), 1);\n        delete definitionMap[id];\n      }\n    }\n  };\n  return DefinitionNode;\n}(Node);\nexport default DefinitionNode;", "map": {"version": 3, "names": ["LinearGradient", "RadialGrad<PERSON>", "LinearGradientNode", "RadialGradientNode", "PatternNode", "Node", "ClipNode", "PATTERN", "DefinitionNode", "call", "definitionMap", "__proto__", "prototype", "Object", "create", "constructor", "attachTo", "dom<PERSON>lement", "element", "template", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "definitionChange", "e", "definitions", "action", "addDefinitions", "removeDefinitions", "createDefinition", "type", "item", "nodeType", "this$1", "field", "addDefinition", "srcElement", "ref", "id", "mapItem", "node", "count", "append", "removeDefinition", "remove", "childNodes", "indexOf"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/svg/definition-node.js"], "sourcesContent": ["import LinearGradient from '../gradients/linear-gradient';\nimport RadialGradient from '../gradients/radial-gradient';\nimport LinearGradientNode from './linear-gradient-node';\nimport RadialGradientNode from './radial-gradient-node';\nimport { PatternNode } from './pattern-node';\nimport Node from './node';\nimport ClipNode from './clip-node';\nimport { PATTERN } from '../core/constants';\n\nvar DefinitionNode = (function (Node) {\n    function DefinitionNode() {\n        Node.call(this);\n        this.definitionMap = {};\n    }\n\n    if ( Node ) DefinitionNode.__proto__ = Node;\n    DefinitionNode.prototype = Object.create( Node && Node.prototype );\n    DefinitionNode.prototype.constructor = DefinitionNode;\n\n    DefinitionNode.prototype.attachTo = function attachTo (domElement) {\n        this.element = domElement;\n    };\n\n    DefinitionNode.prototype.template = function template () {\n        return (\"<defs>\" + (this.renderChildren()) + \"</defs>\");\n    };\n\n    DefinitionNode.prototype.definitionChange = function definitionChange (e) {\n        var definitions = e.definitions;\n        var action = e.action;\n\n        if (action === \"add\") {\n            this.addDefinitions(definitions);\n        } else if (action === \"remove\") {\n            this.removeDefinitions(definitions);\n        }\n    };\n\n    DefinitionNode.prototype.createDefinition = function createDefinition (type, item) {\n        var nodeType;\n        if (type === \"clip\") {\n            nodeType = ClipNode;\n        } else if (type === \"fill\") {\n            if (item instanceof LinearGradient) {\n                nodeType = LinearGradientNode;\n            } else if (item instanceof RadialGradient) {\n                nodeType = RadialGradientNode;\n            } else if (item.nodeType === PATTERN) {\n                nodeType = PatternNode;\n            }\n        }\n        return new nodeType(item);\n    };\n\n    DefinitionNode.prototype.addDefinitions = function addDefinitions (definitions) {\n        var this$1 = this;\n\n        for (var field in definitions) {\n            this$1.addDefinition(field, definitions[field]);\n        }\n    };\n\n    DefinitionNode.prototype.addDefinition = function addDefinition (type, srcElement) {\n        var ref = this;\n        var element = ref.element;\n        var definitionMap = ref.definitionMap;\n        var id = srcElement.id;\n        var mapItem = definitionMap[id];\n        if (!mapItem) {\n            var node = this.createDefinition(type, srcElement);\n            definitionMap[id] = {\n                element: node,\n                count: 1\n            };\n            this.append(node);\n            if (element) {\n                node.attachTo(this.element);\n            }\n        } else {\n            mapItem.count++;\n        }\n    };\n\n    DefinitionNode.prototype.removeDefinitions = function removeDefinitions (definitions) {\n        var this$1 = this;\n\n        for (var field in definitions) {\n            this$1.removeDefinition(definitions[field]);\n        }\n    };\n\n    DefinitionNode.prototype.removeDefinition = function removeDefinition (srcElement) {\n        var definitionMap = this.definitionMap;\n        var id = srcElement.id;\n        var mapItem = definitionMap[id];\n\n        if (mapItem) {\n            mapItem.count--;\n            if (mapItem.count === 0) {\n                this.remove(this.childNodes.indexOf(mapItem.element), 1);\n                delete definitionMap[id];\n            }\n        }\n    };\n\n    return DefinitionNode;\n}(Node));\n\nexport default DefinitionNode;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,8BAA8B;AACzD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,kBAAkB,MAAM,wBAAwB;AACvD,OAAOC,kBAAkB,MAAM,wBAAwB;AACvD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,QAAQ,MAAM,aAAa;AAClC,SAASC,OAAO,QAAQ,mBAAmB;AAE3C,IAAIC,cAAc,GAAI,UAAUH,IAAI,EAAE;EAClC,SAASG,cAAcA,CAAA,EAAG;IACtBH,IAAI,CAACI,IAAI,CAAC,IAAI,CAAC;IACf,IAAI,CAACC,aAAa,GAAG,CAAC,CAAC;EAC3B;EAEA,IAAKL,IAAI,EAAGG,cAAc,CAACG,SAAS,GAAGN,IAAI;EAC3CG,cAAc,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAET,IAAI,IAAIA,IAAI,CAACO,SAAU,CAAC;EAClEJ,cAAc,CAACI,SAAS,CAACG,WAAW,GAAGP,cAAc;EAErDA,cAAc,CAACI,SAAS,CAACI,QAAQ,GAAG,SAASA,QAAQA,CAAEC,UAAU,EAAE;IAC/D,IAAI,CAACC,OAAO,GAAGD,UAAU;EAC7B,CAAC;EAEDT,cAAc,CAACI,SAAS,CAACO,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;IACrD,OAAQ,QAAQ,GAAI,IAAI,CAACC,cAAc,CAAC,CAAE,GAAG,SAAS;EAC1D,CAAC;EAEDZ,cAAc,CAACI,SAAS,CAACS,gBAAgB,GAAG,SAASA,gBAAgBA,CAAEC,CAAC,EAAE;IACtE,IAAIC,WAAW,GAAGD,CAAC,CAACC,WAAW;IAC/B,IAAIC,MAAM,GAAGF,CAAC,CAACE,MAAM;IAErB,IAAIA,MAAM,KAAK,KAAK,EAAE;MAClB,IAAI,CAACC,cAAc,CAACF,WAAW,CAAC;IACpC,CAAC,MAAM,IAAIC,MAAM,KAAK,QAAQ,EAAE;MAC5B,IAAI,CAACE,iBAAiB,CAACH,WAAW,CAAC;IACvC;EACJ,CAAC;EAEDf,cAAc,CAACI,SAAS,CAACe,gBAAgB,GAAG,SAASA,gBAAgBA,CAAEC,IAAI,EAAEC,IAAI,EAAE;IAC/E,IAAIC,QAAQ;IACZ,IAAIF,IAAI,KAAK,MAAM,EAAE;MACjBE,QAAQ,GAAGxB,QAAQ;IACvB,CAAC,MAAM,IAAIsB,IAAI,KAAK,MAAM,EAAE;MACxB,IAAIC,IAAI,YAAY7B,cAAc,EAAE;QAChC8B,QAAQ,GAAG5B,kBAAkB;MACjC,CAAC,MAAM,IAAI2B,IAAI,YAAY5B,cAAc,EAAE;QACvC6B,QAAQ,GAAG3B,kBAAkB;MACjC,CAAC,MAAM,IAAI0B,IAAI,CAACC,QAAQ,KAAKvB,OAAO,EAAE;QAClCuB,QAAQ,GAAG1B,WAAW;MAC1B;IACJ;IACA,OAAO,IAAI0B,QAAQ,CAACD,IAAI,CAAC;EAC7B,CAAC;EAEDrB,cAAc,CAACI,SAAS,CAACa,cAAc,GAAG,SAASA,cAAcA,CAAEF,WAAW,EAAE;IAC5E,IAAIQ,MAAM,GAAG,IAAI;IAEjB,KAAK,IAAIC,KAAK,IAAIT,WAAW,EAAE;MAC3BQ,MAAM,CAACE,aAAa,CAACD,KAAK,EAAET,WAAW,CAACS,KAAK,CAAC,CAAC;IACnD;EACJ,CAAC;EAEDxB,cAAc,CAACI,SAAS,CAACqB,aAAa,GAAG,SAASA,aAAaA,CAAEL,IAAI,EAAEM,UAAU,EAAE;IAC/E,IAAIC,GAAG,GAAG,IAAI;IACd,IAAIjB,OAAO,GAAGiB,GAAG,CAACjB,OAAO;IACzB,IAAIR,aAAa,GAAGyB,GAAG,CAACzB,aAAa;IACrC,IAAI0B,EAAE,GAAGF,UAAU,CAACE,EAAE;IACtB,IAAIC,OAAO,GAAG3B,aAAa,CAAC0B,EAAE,CAAC;IAC/B,IAAI,CAACC,OAAO,EAAE;MACV,IAAIC,IAAI,GAAG,IAAI,CAACX,gBAAgB,CAACC,IAAI,EAAEM,UAAU,CAAC;MAClDxB,aAAa,CAAC0B,EAAE,CAAC,GAAG;QAChBlB,OAAO,EAAEoB,IAAI;QACbC,KAAK,EAAE;MACX,CAAC;MACD,IAAI,CAACC,MAAM,CAACF,IAAI,CAAC;MACjB,IAAIpB,OAAO,EAAE;QACToB,IAAI,CAACtB,QAAQ,CAAC,IAAI,CAACE,OAAO,CAAC;MAC/B;IACJ,CAAC,MAAM;MACHmB,OAAO,CAACE,KAAK,EAAE;IACnB;EACJ,CAAC;EAED/B,cAAc,CAACI,SAAS,CAACc,iBAAiB,GAAG,SAASA,iBAAiBA,CAAEH,WAAW,EAAE;IAClF,IAAIQ,MAAM,GAAG,IAAI;IAEjB,KAAK,IAAIC,KAAK,IAAIT,WAAW,EAAE;MAC3BQ,MAAM,CAACU,gBAAgB,CAAClB,WAAW,CAACS,KAAK,CAAC,CAAC;IAC/C;EACJ,CAAC;EAEDxB,cAAc,CAACI,SAAS,CAAC6B,gBAAgB,GAAG,SAASA,gBAAgBA,CAAEP,UAAU,EAAE;IAC/E,IAAIxB,aAAa,GAAG,IAAI,CAACA,aAAa;IACtC,IAAI0B,EAAE,GAAGF,UAAU,CAACE,EAAE;IACtB,IAAIC,OAAO,GAAG3B,aAAa,CAAC0B,EAAE,CAAC;IAE/B,IAAIC,OAAO,EAAE;MACTA,OAAO,CAACE,KAAK,EAAE;MACf,IAAIF,OAAO,CAACE,KAAK,KAAK,CAAC,EAAE;QACrB,IAAI,CAACG,MAAM,CAAC,IAAI,CAACC,UAAU,CAACC,OAAO,CAACP,OAAO,CAACnB,OAAO,CAAC,EAAE,CAAC,CAAC;QACxD,OAAOR,aAAa,CAAC0B,EAAE,CAAC;MAC5B;IACJ;EACJ,CAAC;EAED,OAAO5B,cAAc;AACzB,CAAC,CAACH,IAAI,CAAE;AAER,eAAeG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}