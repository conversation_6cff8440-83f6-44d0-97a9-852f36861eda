{"ast": null, "code": "import { getDate } from './get-date';\nimport { isEqual } from './is-equal';\n/**\n * A function that compares the date portions of 2 dates.\n *\n * @param candidate - The candidate date.\n * @param expected - The expected date.\n * @returns - A Boolean value whether the values are equal.\n *\n * @example\n * ```ts-no-run\n * isEqualDate(new Date(2016, 0, 1, 10), new Date(2016, 0, 1, 20)); // true\n * isEqualDate(new Date(2016, 0, 1, 10), new Date(2016, 0, 2, 10)); // false\n * ```\n */\nexport var isEqualDate = function (candidate, expected) {\n  if (!candidate && !expected) {\n    return true;\n  }\n  return candidate && expected && isEqual(getDate(candidate), getDate(expected));\n};", "map": {"version": 3, "names": ["getDate", "isEqual", "isEqualDate", "candidate", "expected"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/is-equal-date.js"], "sourcesContent": ["import { getDate } from './get-date';\nimport { isEqual } from './is-equal';\n/**\n * A function that compares the date portions of 2 dates.\n *\n * @param candidate - The candidate date.\n * @param expected - The expected date.\n * @returns - A Boolean value whether the values are equal.\n *\n * @example\n * ```ts-no-run\n * isEqualDate(new Date(2016, 0, 1, 10), new Date(2016, 0, 1, 20)); // true\n * isEqualDate(new Date(2016, 0, 1, 10), new Date(2016, 0, 2, 10)); // false\n * ```\n */\nexport var isEqualDate = function (candidate, expected) {\n    if (!candidate && !expected) {\n        return true;\n    }\n    return candidate && expected && isEqual(getDate(candidate), getDate(expected));\n};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,OAAO,QAAQ,YAAY;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,WAAW,GAAG,SAAAA,CAAUC,SAAS,EAAEC,QAAQ,EAAE;EACpD,IAAI,CAACD,SAAS,IAAI,CAACC,QAAQ,EAAE;IACzB,OAAO,IAAI;EACf;EACA,OAAOD,SAAS,IAAIC,QAAQ,IAAIH,OAAO,CAACD,OAAO,CAACG,SAAS,CAAC,EAAEH,OAAO,CAACI,QAAQ,CAAC,CAAC;AAClF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}