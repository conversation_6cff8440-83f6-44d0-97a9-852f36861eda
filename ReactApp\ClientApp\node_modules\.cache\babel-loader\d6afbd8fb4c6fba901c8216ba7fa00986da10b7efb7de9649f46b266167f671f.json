{"ast": null, "code": "import { adjustDST } from './adjust-dst';\n/**\n * A function which returns a new `Date` instance.\n *\n * @param year - The year value.\n * @param month - The month value.\n * @param day - The day value.\n * @param hours - The hours value.\n * @param minutes - The minutes value.\n * @param seconds - The seconds value.\n * @param milliseconds - milliseconds value.\n * @returns The date instance.\n *\n * @example\n * ```ts-no-run\n * createDate(2016, 0, 15); // 2016-01-15 00:00:00\n * createDate(2016, 0, 15, 22, 22, 20); // 2016-01-15 22:22:20\n * ```\n */\nexport var createDate = function (year, month, day, hours, minutes, seconds, milliseconds) {\n  if (hours === void 0) {\n    hours = 0;\n  }\n  if (minutes === void 0) {\n    minutes = 0;\n  }\n  if (seconds === void 0) {\n    seconds = 0;\n  }\n  if (milliseconds === void 0) {\n    milliseconds = 0;\n  }\n  var date = new Date(year, month, day, hours, minutes, seconds, milliseconds);\n  if (year > -1 && year < 100) {\n    date.setFullYear(date.getFullYear() - 1900);\n  }\n  return adjustDST(date, hours);\n};", "map": {"version": 3, "names": ["adjustDST", "createDate", "year", "month", "day", "hours", "minutes", "seconds", "milliseconds", "date", "Date", "setFullYear", "getFullYear"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/create-date.js"], "sourcesContent": ["import { adjustDST } from './adjust-dst';\n/**\n * A function which returns a new `Date` instance.\n *\n * @param year - The year value.\n * @param month - The month value.\n * @param day - The day value.\n * @param hours - The hours value.\n * @param minutes - The minutes value.\n * @param seconds - The seconds value.\n * @param milliseconds - milliseconds value.\n * @returns The date instance.\n *\n * @example\n * ```ts-no-run\n * createDate(2016, 0, 15); // 2016-01-15 00:00:00\n * createDate(2016, 0, 15, 22, 22, 20); // 2016-01-15 22:22:20\n * ```\n */\nexport var createDate = function (year, month, day, hours, minutes, seconds, milliseconds) {\n    if (hours === void 0) { hours = 0; }\n    if (minutes === void 0) { minutes = 0; }\n    if (seconds === void 0) { seconds = 0; }\n    if (milliseconds === void 0) { milliseconds = 0; }\n    var date = new Date(year, month, day, hours, minutes, seconds, milliseconds);\n    if (year > -1 && year < 100) {\n        date.setFullYear(date.getFullYear() - 1900);\n    }\n    return adjustDST(date, hours);\n};\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,cAAc;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,UAAU,GAAG,SAAAA,CAAUC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,YAAY,EAAE;EACvF,IAAIH,KAAK,KAAK,KAAK,CAAC,EAAE;IAAEA,KAAK,GAAG,CAAC;EAAE;EACnC,IAAIC,OAAO,KAAK,KAAK,CAAC,EAAE;IAAEA,OAAO,GAAG,CAAC;EAAE;EACvC,IAAIC,OAAO,KAAK,KAAK,CAAC,EAAE;IAAEA,OAAO,GAAG,CAAC;EAAE;EACvC,IAAIC,YAAY,KAAK,KAAK,CAAC,EAAE;IAAEA,YAAY,GAAG,CAAC;EAAE;EACjD,IAAIC,IAAI,GAAG,IAAIC,IAAI,CAACR,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,YAAY,CAAC;EAC5E,IAAIN,IAAI,GAAG,CAAC,CAAC,IAAIA,IAAI,GAAG,GAAG,EAAE;IACzBO,IAAI,CAACE,WAAW,CAACF,IAAI,CAACG,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC;EAC/C;EACA,OAAOZ,SAAS,CAACS,IAAI,EAAEJ,KAAK,CAAC;AACjC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}