{"ast": null, "code": "import { isPresent, isBlank } from '../utils';\nimport { getter } from '../accessor';\nvar compare = function (a, b) {\n  if (isBlank(a)) {\n    return a === b ? 0 : -1;\n  }\n  if (isBlank(b)) {\n    return 1;\n  }\n  if (a.localeCompare) {\n    return a.localeCompare(b);\n  }\n  return a > b ? 1 : a < b ? -1 : 0;\n};\nvar compareDesc = function (a, b) {\n  return compare(b, a);\n};\nvar descriptorAsFunc = function (descriptor) {\n  if (typeof descriptor.compare === 'function') {\n    return descriptor.compare;\n  }\n  var prop = getter(descriptor.field, true);\n  return function (a, b) {\n    return (descriptor.dir === 'asc' ? compare : compareDesc)(prop(a), prop(b));\n  };\n};\nvar initial = function (_a, _b) {\n  return 0;\n};\n// tslint:disable:max-line-length\n/**\n * Converts the `SortDescriptors` into a [Comparer]({% slug api_kendo-data-query_comparer %}) function that can be used through `Array.sort`. If multiple descriptors are provided, sorting is applied in a right-to-left order.\n * @param {SortDescriptor[]} descriptors - The descriptors which will be converted.\n * @returns {Comparer} - The produced function.\n *\n * @example\n * ```ts\n * import { composeSortDescriptors } from '@progress/kendo-data-query';\n *\n * const data = [{ name: \"Pork\" }, { name: \"Pepper\" }, { name: \"Beef\" } ];\n * const comparer = composeSortDescriptors([{ field: \"name\", dir: \"asc\" }]);\n * const result = data.sort(comparer);\n * // output: [{ name: \"Beef\" }, { name: \"Pepper\" }, { name: \"Pork\" }];\n * ```\n */\n// tslint:enable:max-line-length\nexport var composeSortDescriptors = function (descriptors) {\n  return descriptors.filter(function (x) {\n    return isPresent(x.dir) || isPresent(x.compare);\n  }).map(function (descriptor) {\n    return descriptorAsFunc(descriptor);\n  }).reduce(function (acc, curr) {\n    return function (a, b) {\n      return acc(a, b) || curr(a, b);\n    };\n  }, initial);\n};", "map": {"version": 3, "names": ["isPresent", "isBlank", "getter", "compare", "a", "b", "localeCompare", "compareDesc", "descriptorAs<PERSON>unc", "descriptor", "prop", "field", "dir", "initial", "_a", "_b", "composeSortDescriptors", "descriptors", "filter", "x", "map", "reduce", "acc", "curr"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-data-query/dist/es/sorting/sort-array.operator.js"], "sourcesContent": ["import { isPresent, isBlank } from '../utils';\nimport { getter } from '../accessor';\nvar compare = function (a, b) {\n    if (isBlank(a)) {\n        return a === b ? 0 : -1;\n    }\n    if (isBlank(b)) {\n        return 1;\n    }\n    if (a.localeCompare) {\n        return a.localeCompare(b);\n    }\n    return a > b ? 1 : (a < b ? -1 : 0);\n};\nvar compareDesc = function (a, b) { return compare(b, a); };\nvar descriptorAsFunc = function (descriptor) {\n    if (typeof descriptor.compare === 'function') {\n        return descriptor.compare;\n    }\n    var prop = getter(descriptor.field, true);\n    return function (a, b) { return (descriptor.dir === 'asc' ? compare : compareDesc)(prop(a), prop(b)); };\n};\nvar initial = function (_a, _b) { return 0; };\n// tslint:disable:max-line-length\n/**\n * Converts the `SortDescriptors` into a [Comparer]({% slug api_kendo-data-query_comparer %}) function that can be used through `Array.sort`. If multiple descriptors are provided, sorting is applied in a right-to-left order.\n * @param {SortDescriptor[]} descriptors - The descriptors which will be converted.\n * @returns {Comparer} - The produced function.\n *\n * @example\n * ```ts\n * import { composeSortDescriptors } from '@progress/kendo-data-query';\n *\n * const data = [{ name: \"Pork\" }, { name: \"Pepper\" }, { name: \"Beef\" } ];\n * const comparer = composeSortDescriptors([{ field: \"name\", dir: \"asc\" }]);\n * const result = data.sort(comparer);\n * // output: [{ name: \"Beef\" }, { name: \"Pepper\" }, { name: \"Pork\" }];\n * ```\n */\n// tslint:enable:max-line-length\nexport var composeSortDescriptors = function (descriptors) { return (descriptors\n    .filter(function (x) { return isPresent(x.dir) || isPresent(x.compare); })\n    .map(function (descriptor) { return descriptorAsFunc(descriptor); })\n    .reduce(function (acc, curr) { return function (a, b) { return acc(a, b) || curr(a, b); }; }, initial)); };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,OAAO,QAAQ,UAAU;AAC7C,SAASC,MAAM,QAAQ,aAAa;AACpC,IAAIC,OAAO,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;EAC1B,IAAIJ,OAAO,CAACG,CAAC,CAAC,EAAE;IACZ,OAAOA,CAAC,KAAKC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAC3B;EACA,IAAIJ,OAAO,CAACI,CAAC,CAAC,EAAE;IACZ,OAAO,CAAC;EACZ;EACA,IAAID,CAAC,CAACE,aAAa,EAAE;IACjB,OAAOF,CAAC,CAACE,aAAa,CAACD,CAAC,CAAC;EAC7B;EACA,OAAOD,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAID,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAE;AACvC,CAAC;AACD,IAAIE,WAAW,GAAG,SAAAA,CAAUH,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAOF,OAAO,CAACE,CAAC,EAAED,CAAC,CAAC;AAAE,CAAC;AAC3D,IAAII,gBAAgB,GAAG,SAAAA,CAAUC,UAAU,EAAE;EACzC,IAAI,OAAOA,UAAU,CAACN,OAAO,KAAK,UAAU,EAAE;IAC1C,OAAOM,UAAU,CAACN,OAAO;EAC7B;EACA,IAAIO,IAAI,GAAGR,MAAM,CAACO,UAAU,CAACE,KAAK,EAAE,IAAI,CAAC;EACzC,OAAO,UAAUP,CAAC,EAAEC,CAAC,EAAE;IAAE,OAAO,CAACI,UAAU,CAACG,GAAG,KAAK,KAAK,GAAGT,OAAO,GAAGI,WAAW,EAAEG,IAAI,CAACN,CAAC,CAAC,EAAEM,IAAI,CAACL,CAAC,CAAC,CAAC;EAAE,CAAC;AAC3G,CAAC;AACD,IAAIQ,OAAO,GAAG,SAAAA,CAAUC,EAAE,EAAEC,EAAE,EAAE;EAAE,OAAO,CAAC;AAAE,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,sBAAsB,GAAG,SAAAA,CAAUC,WAAW,EAAE;EAAE,OAAQA,WAAW,CAC3EC,MAAM,CAAC,UAAUC,CAAC,EAAE;IAAE,OAAOnB,SAAS,CAACmB,CAAC,CAACP,GAAG,CAAC,IAAIZ,SAAS,CAACmB,CAAC,CAAChB,OAAO,CAAC;EAAE,CAAC,CAAC,CACzEiB,GAAG,CAAC,UAAUX,UAAU,EAAE;IAAE,OAAOD,gBAAgB,CAACC,UAAU,CAAC;EAAE,CAAC,CAAC,CACnEY,MAAM,CAAC,UAAUC,GAAG,EAAEC,IAAI,EAAE;IAAE,OAAO,UAAUnB,CAAC,EAAEC,CAAC,EAAE;MAAE,OAAOiB,GAAG,CAAClB,CAAC,EAAEC,CAAC,CAAC,IAAIkB,IAAI,CAACnB,CAAC,EAAEC,CAAC,CAAC;IAAE,CAAC;EAAE,CAAC,EAAEQ,OAAO,CAAC;AAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}