import React, { useEffect, useState } from 'react';
import { PDFViewer } from '@progress/kendo-react-pdf-viewer';
import { Button } from '@progress/kendo-react-buttons';
import styles from './index.module.less';
import { FiRefreshCw, FiDownload, FiPrinter } from 'react-icons/fi';
import { useTermsAndConditions } from '@app/hooks/useTermsAndConditions';
import { Spin } from 'antd';

const TermsAndConditions: React.FC = () => {
  const {
    tncDocument,
    pdfViewerRef,
    tncLoading,
    tncRefetch,
    isAccepting,
    onDocumentLoad,
    handleAgree,
    clickToolbarButtonByTitle
  } = useTermsAndConditions();
 const [pdfBlobUrl, setPdfBlobUrl] = useState<string>("");

     useEffect(() => {
      const fetchPdfWithAuth = async () => {
      try {
        const response = await fetch(
          `https://usermanagement.dev.irispme.com/NBT1${tncDocument.document?.documentUrl}`,
          {
            headers: {
              Authorization: `Bearer ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`,
            },
          }
        );

        if (!response.ok) {
          throw new Error("Failed to fetch PDF");
        }

        const blob = await response.blob();
        const blobUrl = URL.createObjectURL(blob);
        setPdfBlobUrl(blobUrl);
      } catch (error) {
        console.error("Error fetching PDF:", error);
      }
    };

    fetchPdfWithAuth();

    return () => {
      if (pdfBlobUrl) {
        URL.revokeObjectURL(pdfBlobUrl);
      }
    };
  }, []);

  if (!tncDocument || !tncDocument.document) return null;

  return (
    <div className={styles.dialogMainContainer}>
      <div className={styles.pdfViewContainer}>
        <PDFViewer
          url={pdfBlobUrl}
          onLoad={onDocumentLoad}
          ref={pdfViewerRef}
          tools={['pager', 'print', 'download', 'selection', 'zoomInOut']}
          style={{ height: '100%' }}
          zoom={0.9}
        />
      </div>

      <div className={styles.agreement_text}>
        {tncDocument.document.statementOfAgreement}
      </div>

      <div className={styles.dialogActionsBar}>
        <div className={styles.dialogActionsBar_left}>
          <Button
            startIcon={tncLoading ? <Spin size='small' /> : <FiRefreshCw />}
            disabled={tncLoading}
            themeColor="primary"
            fillMode="outline"
            onClick={tncRefetch}
          >
            Refresh
          </Button>

          <Button
            startIcon={<FiDownload />}
            themeColor="primary"
            fillMode="outline"
            onClick={() => clickToolbarButtonByTitle('Download')}
            disabled={tncLoading}
          >
            Download
          </Button>

          <Button
            startIcon={<FiPrinter />}
            disabled={tncLoading}
            themeColor="primary"
            fillMode="outline"
            onClick={() => clickToolbarButtonByTitle('Print')}
          >
            Print
          </Button>
        </div>

        <Button
          themeColor="primary"
          fillMode="solid"
          onClick={handleAgree}
          disabled={isAccepting || tncLoading}
          startIcon={isAccepting ? <Spin size='small' /> : undefined}
        >
          AGREE & CONTINUE
        </Button>
      </div>
    </div>
  );
};

export default TermsAndConditions;
