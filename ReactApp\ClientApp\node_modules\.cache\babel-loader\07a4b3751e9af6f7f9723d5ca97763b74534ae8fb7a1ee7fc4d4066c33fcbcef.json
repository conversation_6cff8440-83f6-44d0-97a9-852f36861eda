{"ast": null, "code": "import { defined } from '../util';\nfunction geometryAccessor(name) {\n  var fieldName = \"_\" + name;\n  return function (value) {\n    if (defined(value)) {\n      this._observerField(fieldName, value);\n      this.geometryChange();\n      return this;\n    }\n    return this[fieldName];\n  };\n}\nfunction defineGeometryAccessors(fn, names) {\n  for (var i = 0; i < names.length; i++) {\n    fn[names[i]] = geometryAccessor(names[i]);\n  }\n}\nvar withGeometry = function (TBase, names) {\n  if (names === void 0) names = [\"geometry\"];\n  var result = function (TBase) {\n    function result() {\n      TBase.apply(this, arguments);\n    }\n    if (TBase) result.__proto__ = TBase;\n    result.prototype = Object.create(TBase && TBase.prototype);\n    result.prototype.constructor = result;\n    return result;\n  }(TBase);\n  defineGeometryAccessors(result.prototype, names);\n  return result;\n};\nexport default withGeometry;", "map": {"version": 3, "names": ["defined", "geometryAccessor", "name", "fieldName", "value", "_observer<PERSON>ield", "geometryChange", "defineGeometryAccessors", "fn", "names", "i", "length", "withGeometry", "TBase", "result", "apply", "arguments", "__proto__", "prototype", "Object", "create", "constructor"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/mixins/with-geometry.js"], "sourcesContent": ["import { defined } from '../util';\n\nfunction geometryAccessor(name) {\n    var fieldName = \"_\" + name;\n    return function(value) {\n        if (defined(value)) {\n            this._observerField(fieldName, value);\n            this.geometryChange();\n            return this;\n        }\n\n        return this[fieldName];\n    };\n}\n\nfunction defineGeometryAccessors(fn, names) {\n    for (var i = 0; i < names.length; i++) {\n        fn[names[i]] = geometryAccessor(names[i]);\n    }\n}\n\nvar withGeometry = function (TBase, names) {\n    if ( names === void 0 ) names = [ \"geometry\" ];\n\n    var result = (function (TBase) {\n        function result () {\n            TBase.apply(this, arguments);\n        }if ( TBase ) result.__proto__ = TBase;\n        result.prototype = Object.create( TBase && TBase.prototype );\n        result.prototype.constructor = result;\n\n        \n\n        return result;\n    }(TBase));\n    defineGeometryAccessors(result.prototype, names);\n\n    return result;\n};\n\nexport default withGeometry;\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,SAAS;AAEjC,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EAC5B,IAAIC,SAAS,GAAG,GAAG,GAAGD,IAAI;EAC1B,OAAO,UAASE,KAAK,EAAE;IACnB,IAAIJ,OAAO,CAACI,KAAK,CAAC,EAAE;MAChB,IAAI,CAACC,cAAc,CAACF,SAAS,EAAEC,KAAK,CAAC;MACrC,IAAI,CAACE,cAAc,CAAC,CAAC;MACrB,OAAO,IAAI;IACf;IAEA,OAAO,IAAI,CAACH,SAAS,CAAC;EAC1B,CAAC;AACL;AAEA,SAASI,uBAAuBA,CAACC,EAAE,EAAEC,KAAK,EAAE;EACxC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IACnCF,EAAE,CAACC,KAAK,CAACC,CAAC,CAAC,CAAC,GAAGT,gBAAgB,CAACQ,KAAK,CAACC,CAAC,CAAC,CAAC;EAC7C;AACJ;AAEA,IAAIE,YAAY,GAAG,SAAAA,CAAUC,KAAK,EAAEJ,KAAK,EAAE;EACvC,IAAKA,KAAK,KAAK,KAAK,CAAC,EAAGA,KAAK,GAAG,CAAE,UAAU,CAAE;EAE9C,IAAIK,MAAM,GAAI,UAAUD,KAAK,EAAE;IAC3B,SAASC,MAAMA,CAAA,EAAI;MACfD,KAAK,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAChC;IAAC,IAAKH,KAAK,EAAGC,MAAM,CAACG,SAAS,GAAGJ,KAAK;IACtCC,MAAM,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAEP,KAAK,IAAIA,KAAK,CAACK,SAAU,CAAC;IAC5DJ,MAAM,CAACI,SAAS,CAACG,WAAW,GAAGP,MAAM;IAIrC,OAAOA,MAAM;EACjB,CAAC,CAACD,KAAK,CAAE;EACTN,uBAAuB,CAACO,MAAM,CAACI,SAAS,EAAET,KAAK,CAAC;EAEhD,OAAOK,MAAM;AACjB,CAAC;AAED,eAAeF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}