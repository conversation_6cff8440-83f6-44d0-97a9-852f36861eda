{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nfunction n(r, t) {\n  let e = r;\n  for (; e && e !== t;) e = e.parentNode;\n  return !!e;\n}\nexport { n as hasParent };", "map": {"version": 3, "names": ["n", "r", "t", "e", "parentNode", "hasParent"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-inputs/signature/utils/index.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nfunction n(r, t) {\n  let e = r;\n  for (; e && e !== t; )\n    e = e.parentNode;\n  return !!e;\n}\nexport {\n  n as hasParent\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,CAACA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACf,IAAIC,CAAC,GAAGF,CAAC;EACT,OAAOE,CAAC,IAAIA,CAAC,KAAKD,CAAC,GACjBC,CAAC,GAAGA,CAAC,CAACC,UAAU;EAClB,OAAO,CAAC,CAACD,CAAC;AACZ;AACA,SACEH,CAAC,IAAIK,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}