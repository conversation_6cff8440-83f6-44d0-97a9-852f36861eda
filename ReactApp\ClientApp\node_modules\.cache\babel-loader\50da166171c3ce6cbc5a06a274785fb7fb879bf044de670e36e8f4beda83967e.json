{"ast": null, "code": "import { __makeTemplateObject } from \"tslib\";\nimport { isPresent, isNotNullOrEmptyString } from './utils';\nimport { serializeFilter } from './odata-filtering.operators';\nimport { ifElse, constant } from './funcs';\nvar serializeSort = function (orderby) {\n  var str = orderby.filter(function (sort) {\n    return isPresent(sort.dir);\n  }).map(function (sort) {\n    var order = sort.field.replace(/\\./g, \"/\");\n    return sort.dir === \"desc\" ? order + \" desc\" : order;\n  }).join(\",\");\n  return str ? \"$orderby=\".concat(str) : str;\n};\nvar emptyString = constant('');\nvar concat = function (a) {\n  return function (b) {\n    return a + b;\n  };\n};\nvar serializeKey = function (strings, val) {\n  return ifElse(isPresent, concat(strings[0]), emptyString)(val);\n};\nvar rules = function (settings, state) {\n  return function (key) {\n    return {\n      \"filter\": serializeFilter(state.filter || {}, settings),\n      \"skip\": serializeKey(templateObject_1 || (templateObject_1 = __makeTemplateObject([\"$skip=\", \"\"], [\"$skip=\", \"\"])), state.skip),\n      \"sort\": serializeSort(state.sort || []),\n      \"take\": serializeKey(templateObject_2 || (templateObject_2 = __makeTemplateObject([\"$top=\", \"\"], [\"$top=\", \"\"])), state.take)\n    }[key];\n  };\n};\n// tslint:enable:max-line-length\n/**\n * Converts a [State]({% slug api_kendo-data-query_state %}) into an OData v4 compatible string.\n *\n * @param {State} state - The state that will be serialized.\n * @param {ODataSettings} settings - The settings that are used during the serialization.\n * @returns {string} - The serialized state.\n */\nexport var toODataString = function (state, settings) {\n  if (settings === void 0) {\n    settings = {};\n  }\n  return Object.keys(state).map(rules(settings, state)).filter(isNotNullOrEmptyString).join('&');\n};\nvar templateObject_1, templateObject_2;", "map": {"version": 3, "names": ["__makeTemplateObject", "isPresent", "isNotNullOrEmptyString", "serializeFilter", "ifElse", "constant", "serializeSort", "orderby", "str", "filter", "sort", "dir", "map", "order", "field", "replace", "join", "concat", "emptyString", "a", "b", "serialize<PERSON>ey", "strings", "val", "rules", "settings", "state", "key", "templateObject_1", "skip", "templateObject_2", "take", "toODataString", "Object", "keys"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-data-query/dist/es/odata.operators.js"], "sourcesContent": ["import { __makeTemplateObject } from \"tslib\";\nimport { isPresent, isNotNullOrEmptyString } from './utils';\nimport { serializeFilter } from './odata-filtering.operators';\nimport { ifElse, constant } from './funcs';\nvar serializeSort = function (orderby) {\n    var str = orderby\n        .filter(function (sort) { return isPresent(sort.dir); })\n        .map(function (sort) {\n        var order = sort.field.replace(/\\./g, \"/\");\n        return sort.dir === \"desc\" ? order + \" desc\" : order;\n    }).join(\",\");\n    return str ? \"$orderby=\".concat(str) : str;\n};\nvar emptyString = constant('');\nvar concat = function (a) { return function (b) { return a + b; }; };\nvar serializeKey = function (strings, val) { return ifElse(isPresent, concat(strings[0]), emptyString)(val); };\nvar rules = function (settings, state) { return function (key) { return ({\n    \"filter\": serializeFilter(state.filter || {}, settings),\n    \"skip\": serializeKey(templateObject_1 || (templateObject_1 = __makeTemplateObject([\"$skip=\", \"\"], [\"$skip=\", \"\"])), state.skip),\n    \"sort\": serializeSort(state.sort || []),\n    \"take\": serializeKey(templateObject_2 || (templateObject_2 = __makeTemplateObject([\"$top=\", \"\"], [\"$top=\", \"\"])), state.take)\n}[key]); }; };\n// tslint:enable:max-line-length\n/**\n * Converts a [State]({% slug api_kendo-data-query_state %}) into an OData v4 compatible string.\n *\n * @param {State} state - The state that will be serialized.\n * @param {ODataSettings} settings - The settings that are used during the serialization.\n * @returns {string} - The serialized state.\n */\nexport var toODataString = function (state, settings) {\n    if (settings === void 0) { settings = {}; }\n    return (Object.keys(state)\n        .map(rules(settings, state))\n        .filter(isNotNullOrEmptyString)\n        .join('&'));\n};\nvar templateObject_1, templateObject_2;\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,OAAO;AAC5C,SAASC,SAAS,EAAEC,sBAAsB,QAAQ,SAAS;AAC3D,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,MAAM,EAAEC,QAAQ,QAAQ,SAAS;AAC1C,IAAIC,aAAa,GAAG,SAAAA,CAAUC,OAAO,EAAE;EACnC,IAAIC,GAAG,GAAGD,OAAO,CACZE,MAAM,CAAC,UAAUC,IAAI,EAAE;IAAE,OAAOT,SAAS,CAACS,IAAI,CAACC,GAAG,CAAC;EAAE,CAAC,CAAC,CACvDC,GAAG,CAAC,UAAUF,IAAI,EAAE;IACrB,IAAIG,KAAK,GAAGH,IAAI,CAACI,KAAK,CAACC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;IAC1C,OAAOL,IAAI,CAACC,GAAG,KAAK,MAAM,GAAGE,KAAK,GAAG,OAAO,GAAGA,KAAK;EACxD,CAAC,CAAC,CAACG,IAAI,CAAC,GAAG,CAAC;EACZ,OAAOR,GAAG,GAAG,WAAW,CAACS,MAAM,CAACT,GAAG,CAAC,GAAGA,GAAG;AAC9C,CAAC;AACD,IAAIU,WAAW,GAAGb,QAAQ,CAAC,EAAE,CAAC;AAC9B,IAAIY,MAAM,GAAG,SAAAA,CAAUE,CAAC,EAAE;EAAE,OAAO,UAAUC,CAAC,EAAE;IAAE,OAAOD,CAAC,GAAGC,CAAC;EAAE,CAAC;AAAE,CAAC;AACpE,IAAIC,YAAY,GAAG,SAAAA,CAAUC,OAAO,EAAEC,GAAG,EAAE;EAAE,OAAOnB,MAAM,CAACH,SAAS,EAAEgB,MAAM,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC,EAAEJ,WAAW,CAAC,CAACK,GAAG,CAAC;AAAE,CAAC;AAC9G,IAAIC,KAAK,GAAG,SAAAA,CAAUC,QAAQ,EAAEC,KAAK,EAAE;EAAE,OAAO,UAAUC,GAAG,EAAE;IAAE,OAAQ;MACrE,QAAQ,EAAExB,eAAe,CAACuB,KAAK,CAACjB,MAAM,IAAI,CAAC,CAAC,EAAEgB,QAAQ,CAAC;MACvD,MAAM,EAAEJ,YAAY,CAACO,gBAAgB,KAAKA,gBAAgB,GAAG5B,oBAAoB,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE0B,KAAK,CAACG,IAAI,CAAC;MAC/H,MAAM,EAAEvB,aAAa,CAACoB,KAAK,CAAChB,IAAI,IAAI,EAAE,CAAC;MACvC,MAAM,EAAEW,YAAY,CAACS,gBAAgB,KAAKA,gBAAgB,GAAG9B,oBAAoB,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE0B,KAAK,CAACK,IAAI;IAChI,CAAC,CAACJ,GAAG,CAAC;EAAG,CAAC;AAAE,CAAC;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIK,aAAa,GAAG,SAAAA,CAAUN,KAAK,EAAED,QAAQ,EAAE;EAClD,IAAIA,QAAQ,KAAK,KAAK,CAAC,EAAE;IAAEA,QAAQ,GAAG,CAAC,CAAC;EAAE;EAC1C,OAAQQ,MAAM,CAACC,IAAI,CAACR,KAAK,CAAC,CACrBd,GAAG,CAACY,KAAK,CAACC,QAAQ,EAAEC,KAAK,CAAC,CAAC,CAC3BjB,MAAM,CAACP,sBAAsB,CAAC,CAC9Bc,IAAI,CAAC,GAAG,CAAC;AAClB,CAAC;AACD,IAAIY,gBAAgB,EAAEE,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}