{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as r from \"react\";\nimport s from \"prop-types\";\nimport { classNames as t } from \"@progress/kendo-react-common\";\nconst a = e => /* @__PURE__ */r.createElement(\"div\", {\n  style: e.style,\n  className: t(\"k-card-footer\", e.className)\n}, e.children);\na.propTypes = {\n  className: s.string\n};\nexport { a as CardFooter };", "map": {"version": 3, "names": ["r", "s", "classNames", "t", "a", "e", "createElement", "style", "className", "children", "propTypes", "string", "<PERSON><PERSON><PERSON>er"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/card/CardFooter.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as r from \"react\";\nimport s from \"prop-types\";\nimport { classNames as t } from \"@progress/kendo-react-common\";\nconst a = (e) => /* @__PURE__ */ r.createElement(\"div\", { style: e.style, className: t(\"k-card-footer\", e.className) }, e.children);\na.propTypes = {\n  className: s.string\n};\nexport {\n  a as CardFooter\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AAC9D,MAAMC,CAAC,GAAIC,CAAC,IAAK,eAAgBL,CAAC,CAACM,aAAa,CAAC,KAAK,EAAE;EAAEC,KAAK,EAAEF,CAAC,CAACE,KAAK;EAAEC,SAAS,EAAEL,CAAC,CAAC,eAAe,EAAEE,CAAC,CAACG,SAAS;AAAE,CAAC,EAAEH,CAAC,CAACI,QAAQ,CAAC;AACnIL,CAAC,CAACM,SAAS,GAAG;EACZF,SAAS,EAAEP,CAAC,CAACU;AACf,CAAC;AACD,SACEP,CAAC,IAAIQ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}