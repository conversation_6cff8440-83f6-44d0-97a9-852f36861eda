{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nfunction getUseId() {\n  // We need fully clone React function here to avoid webpack warning React 17 do not export `useId`\n  var fullClone = _objectSpread({}, React);\n  return fullClone.useId;\n}\nvar uuid = 0;\n\n/** @private Note only worked in develop env. Not work in production. */\nexport function resetUuid() {\n  if (process.env.NODE_ENV !== 'production') {\n    uuid = 0;\n  }\n}\nvar useOriginId = getUseId();\nexport default useOriginId ?\n// Use React `useId`\nfunction useId(id) {\n  var reactId = useOriginId();\n\n  // Developer passed id is single source of truth\n  if (id) {\n    return id;\n  }\n\n  // Test env always return mock id\n  if (process.env.NODE_ENV === 'test') {\n    return 'test-id';\n  }\n  return reactId;\n} :\n// Use compatible of `useId`\nfunction useCompatId(id) {\n  // Inner id for accessibility usage. Only work in client side\n  var _React$useState = React.useState('ssr-id'),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    innerId = _React$useState2[0],\n    setInnerId = _React$useState2[1];\n  React.useEffect(function () {\n    var nextId = uuid;\n    uuid += 1;\n    setInnerId(\"rc_unique_\".concat(nextId));\n  }, []);\n\n  // Developer passed id is single source of truth\n  if (id) {\n    return id;\n  }\n\n  // Test env always return mock id\n  if (process.env.NODE_ENV === 'test') {\n    return 'test-id';\n  }\n\n  // Return react native id or inner id\n  return innerId;\n};", "map": {"version": 3, "names": ["_slicedToArray", "_objectSpread", "React", "getUseId", "fullClone", "useId", "uuid", "resetUuid", "process", "env", "NODE_ENV", "useOriginId", "id", "reactId", "useCompatId", "_React$useState", "useState", "_React$useState2", "innerId", "setInnerId", "useEffect", "nextId", "concat"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/rc-util/es/hooks/useId.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nfunction getUseId() {\n  // We need fully clone React function here to avoid webpack warning React 17 do not export `useId`\n  var fullClone = _objectSpread({}, React);\n  return fullClone.useId;\n}\nvar uuid = 0;\n\n/** @private Note only worked in develop env. Not work in production. */\nexport function resetUuid() {\n  if (process.env.NODE_ENV !== 'production') {\n    uuid = 0;\n  }\n}\nvar useOriginId = getUseId();\nexport default useOriginId ?\n// Use React `useId`\nfunction useId(id) {\n  var reactId = useOriginId();\n\n  // Developer passed id is single source of truth\n  if (id) {\n    return id;\n  }\n\n  // Test env always return mock id\n  if (process.env.NODE_ENV === 'test') {\n    return 'test-id';\n  }\n  return reactId;\n} :\n// Use compatible of `useId`\nfunction useCompatId(id) {\n  // Inner id for accessibility usage. Only work in client side\n  var _React$useState = React.useState('ssr-id'),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    innerId = _React$useState2[0],\n    setInnerId = _React$useState2[1];\n  React.useEffect(function () {\n    var nextId = uuid;\n    uuid += 1;\n    setInnerId(\"rc_unique_\".concat(nextId));\n  }, []);\n\n  // Developer passed id is single source of truth\n  if (id) {\n    return id;\n  }\n\n  // Test env always return mock id\n  if (process.env.NODE_ENV === 'test') {\n    return 'test-id';\n  }\n\n  // Return react native id or inner id\n  return innerId;\n};"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQA,CAAA,EAAG;EAClB;EACA,IAAIC,SAAS,GAAGH,aAAa,CAAC,CAAC,CAAC,EAAEC,KAAK,CAAC;EACxC,OAAOE,SAAS,CAACC,KAAK;AACxB;AACA,IAAIC,IAAI,GAAG,CAAC;;AAEZ;AACA,OAAO,SAASC,SAASA,CAAA,EAAG;EAC1B,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCJ,IAAI,GAAG,CAAC;EACV;AACF;AACA,IAAIK,WAAW,GAAGR,QAAQ,CAAC,CAAC;AAC5B,eAAeQ,WAAW;AAC1B;AACA,SAASN,KAAKA,CAACO,EAAE,EAAE;EACjB,IAAIC,OAAO,GAAGF,WAAW,CAAC,CAAC;;EAE3B;EACA,IAAIC,EAAE,EAAE;IACN,OAAOA,EAAE;EACX;;EAEA;EACA,IAAIJ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;IACnC,OAAO,SAAS;EAClB;EACA,OAAOG,OAAO;AAChB,CAAC;AACD;AACA,SAASC,WAAWA,CAACF,EAAE,EAAE;EACvB;EACA,IAAIG,eAAe,GAAGb,KAAK,CAACc,QAAQ,CAAC,QAAQ,CAAC;IAC5CC,gBAAgB,GAAGjB,cAAc,CAACe,eAAe,EAAE,CAAC,CAAC;IACrDG,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7BE,UAAU,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAClCf,KAAK,CAACkB,SAAS,CAAC,YAAY;IAC1B,IAAIC,MAAM,GAAGf,IAAI;IACjBA,IAAI,IAAI,CAAC;IACTa,UAAU,CAAC,YAAY,CAACG,MAAM,CAACD,MAAM,CAAC,CAAC;EACzC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAIT,EAAE,EAAE;IACN,OAAOA,EAAE;EACX;;EAEA;EACA,IAAIJ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;IACnC,OAAO,SAAS;EAClB;;EAEA;EACA,OAAOQ,OAAO;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}