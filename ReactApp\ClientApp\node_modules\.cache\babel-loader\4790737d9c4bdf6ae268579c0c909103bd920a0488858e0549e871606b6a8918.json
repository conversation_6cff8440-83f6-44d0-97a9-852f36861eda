{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as r from \"react\";\nimport e from \"prop-types\";\nimport { provideLocalizationService as h, registerForLocalization as u } from \"@progress/kendo-react-intl\";\nimport { classNames as f, uCalendar as g } from \"@progress/kendo-react-common\";\nimport { Button as x } from \"@progress/kendo-react-buttons\";\nimport { getDate as n } from \"@progress/kendo-date-math\";\nimport { today as l, messages as v } from \"../../messages/index.mjs\";\nimport { dateInRange as C, getToday as m, MIN_DATE as k, MAX_DATE as I, isInRange as y } from \"../../utils.mjs\";\nconst t = class t extends r.Component {\n  constructor() {\n    super(...arguments), this.localization = null, this.handleClick = i => {\n      if (this.todayIsInRange && this.props.onClick) {\n        const s = {\n          syntheticEvent: i,\n          nativeEvent: i.nativeEvent,\n          value: C(m(), this.min, this.max),\n          target: this,\n          isTodayClick: !0\n        };\n        this.props.onClick.call(void 0, s);\n      }\n    };\n  }\n  get min() {\n    return this.props.min !== void 0 ? this.props.min : t.defaultProps.min;\n  }\n  get max() {\n    return this.props.max !== void 0 ? this.props.max : t.defaultProps.max;\n  }\n  get todayIsInRange() {\n    return y(m(), n(this.min), n(this.max));\n  }\n  render() {\n    const {\n        disabled: i,\n        tabIndex: s,\n        unstyled: o\n      } = this.props,\n      p = o && o.uCalendar;\n    this.localization = h(this);\n    const c = this.localization.toLanguageString(l, v[l]),\n      d = f(g.today({\n        c: p,\n        disabled: i\n      }));\n    return /* @__PURE__ */r.createElement(x, {\n      className: d,\n      onClick: this.handleClick,\n      tabIndex: s,\n      fillMode: \"flat\",\n      themeColor: \"base\",\n      role: \"link\"\n    }, c);\n  }\n};\nt.propTypes = {\n  max: e.instanceOf(Date).isRequired,\n  min: e.instanceOf(Date).isRequired,\n  onClick: e.func,\n  disabled: e.bool\n}, t.defaultProps = {\n  min: k,\n  max: I\n};\nlet a = t;\nu(a);\nexport { a as TodayCommand };", "map": {"version": 3, "names": ["r", "e", "provideLocalizationService", "h", "registerForLocalization", "u", "classNames", "f", "uCalendar", "g", "<PERSON><PERSON>", "x", "getDate", "n", "today", "l", "messages", "v", "dateInRange", "C", "get<PERSON><PERSON>y", "m", "MIN_DATE", "k", "MAX_DATE", "I", "isInRange", "y", "t", "Component", "constructor", "arguments", "localization", "handleClick", "i", "todayIsInRange", "props", "onClick", "s", "syntheticEvent", "nativeEvent", "value", "min", "max", "target", "isTodayClick", "call", "defaultProps", "render", "disabled", "tabIndex", "unstyled", "o", "p", "c", "toLanguageString", "d", "createElement", "className", "fillMode", "themeColor", "role", "propTypes", "instanceOf", "Date", "isRequired", "func", "bool", "a", "TodayCommand"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dateinputs/calendar/components/TodayCommand.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as r from \"react\";\nimport e from \"prop-types\";\nimport { provideLocalizationService as h, registerForLocalization as u } from \"@progress/kendo-react-intl\";\nimport { classNames as f, uCalendar as g } from \"@progress/kendo-react-common\";\nimport { Button as x } from \"@progress/kendo-react-buttons\";\nimport { getDate as n } from \"@progress/kendo-date-math\";\nimport { today as l, messages as v } from \"../../messages/index.mjs\";\nimport { dateInRange as C, getToday as m, MIN_DATE as k, MAX_DATE as I, isInRange as y } from \"../../utils.mjs\";\nconst t = class t extends r.Component {\n  constructor() {\n    super(...arguments), this.localization = null, this.handleClick = (i) => {\n      if (this.todayIsInRange && this.props.onClick) {\n        const s = {\n          syntheticEvent: i,\n          nativeEvent: i.nativeEvent,\n          value: C(m(), this.min, this.max),\n          target: this,\n          isTodayClick: !0\n        };\n        this.props.onClick.call(void 0, s);\n      }\n    };\n  }\n  get min() {\n    return this.props.min !== void 0 ? this.props.min : t.defaultProps.min;\n  }\n  get max() {\n    return this.props.max !== void 0 ? this.props.max : t.defaultProps.max;\n  }\n  get todayIsInRange() {\n    return y(m(), n(this.min), n(this.max));\n  }\n  render() {\n    const { disabled: i, tabIndex: s, unstyled: o } = this.props, p = o && o.uCalendar;\n    this.localization = h(this);\n    const c = this.localization.toLanguageString(l, v[l]), d = f(g.today({ c: p, disabled: i }));\n    return /* @__PURE__ */ r.createElement(\n      x,\n      {\n        className: d,\n        onClick: this.handleClick,\n        tabIndex: s,\n        fillMode: \"flat\",\n        themeColor: \"base\",\n        role: \"link\"\n      },\n      c\n    );\n  }\n};\nt.propTypes = {\n  max: e.instanceOf(Date).isRequired,\n  min: e.instanceOf(Date).isRequired,\n  onClick: e.func,\n  disabled: e.bool\n}, t.defaultProps = {\n  min: k,\n  max: I\n};\nlet a = t;\nu(a);\nexport {\n  a as TodayCommand\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,0BAA0B,IAAIC,CAAC,EAAEC,uBAAuB,IAAIC,CAAC,QAAQ,4BAA4B;AAC1G,SAASC,UAAU,IAAIC,CAAC,EAAEC,SAAS,IAAIC,CAAC,QAAQ,8BAA8B;AAC9E,SAASC,MAAM,IAAIC,CAAC,QAAQ,+BAA+B;AAC3D,SAASC,OAAO,IAAIC,CAAC,QAAQ,2BAA2B;AACxD,SAASC,KAAK,IAAIC,CAAC,EAAEC,QAAQ,IAAIC,CAAC,QAAQ,0BAA0B;AACpE,SAASC,WAAW,IAAIC,CAAC,EAAEC,QAAQ,IAAIC,CAAC,EAAEC,QAAQ,IAAIC,CAAC,EAAEC,QAAQ,IAAIC,CAAC,EAAEC,SAAS,IAAIC,CAAC,QAAQ,iBAAiB;AAC/G,MAAMC,CAAC,GAAG,MAAMA,CAAC,SAAS5B,CAAC,CAAC6B,SAAS,CAAC;EACpCC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,GAAGC,SAAS,CAAC,EAAE,IAAI,CAACC,YAAY,GAAG,IAAI,EAAE,IAAI,CAACC,WAAW,GAAIC,CAAC,IAAK;MACvE,IAAI,IAAI,CAACC,cAAc,IAAI,IAAI,CAACC,KAAK,CAACC,OAAO,EAAE;QAC7C,MAAMC,CAAC,GAAG;UACRC,cAAc,EAAEL,CAAC;UACjBM,WAAW,EAAEN,CAAC,CAACM,WAAW;UAC1BC,KAAK,EAAEtB,CAAC,CAACE,CAAC,CAAC,CAAC,EAAE,IAAI,CAACqB,GAAG,EAAE,IAAI,CAACC,GAAG,CAAC;UACjCC,MAAM,EAAE,IAAI;UACZC,YAAY,EAAE,CAAC;QACjB,CAAC;QACD,IAAI,CAACT,KAAK,CAACC,OAAO,CAACS,IAAI,CAAC,KAAK,CAAC,EAAER,CAAC,CAAC;MACpC;IACF,CAAC;EACH;EACA,IAAII,GAAGA,CAAA,EAAG;IACR,OAAO,IAAI,CAACN,KAAK,CAACM,GAAG,KAAK,KAAK,CAAC,GAAG,IAAI,CAACN,KAAK,CAACM,GAAG,GAAGd,CAAC,CAACmB,YAAY,CAACL,GAAG;EACxE;EACA,IAAIC,GAAGA,CAAA,EAAG;IACR,OAAO,IAAI,CAACP,KAAK,CAACO,GAAG,KAAK,KAAK,CAAC,GAAG,IAAI,CAACP,KAAK,CAACO,GAAG,GAAGf,CAAC,CAACmB,YAAY,CAACJ,GAAG;EACxE;EACA,IAAIR,cAAcA,CAAA,EAAG;IACnB,OAAOR,CAAC,CAACN,CAAC,CAAC,CAAC,EAAER,CAAC,CAAC,IAAI,CAAC6B,GAAG,CAAC,EAAE7B,CAAC,CAAC,IAAI,CAAC8B,GAAG,CAAC,CAAC;EACzC;EACAK,MAAMA,CAAA,EAAG;IACP,MAAM;QAAEC,QAAQ,EAAEf,CAAC;QAAEgB,QAAQ,EAAEZ,CAAC;QAAEa,QAAQ,EAAEC;MAAE,CAAC,GAAG,IAAI,CAAChB,KAAK;MAAEiB,CAAC,GAAGD,CAAC,IAAIA,CAAC,CAAC5C,SAAS;IAClF,IAAI,CAACwB,YAAY,GAAG7B,CAAC,CAAC,IAAI,CAAC;IAC3B,MAAMmD,CAAC,GAAG,IAAI,CAACtB,YAAY,CAACuB,gBAAgB,CAACxC,CAAC,EAAEE,CAAC,CAACF,CAAC,CAAC,CAAC;MAAEyC,CAAC,GAAGjD,CAAC,CAACE,CAAC,CAACK,KAAK,CAAC;QAAEwC,CAAC,EAAED,CAAC;QAAEJ,QAAQ,EAAEf;MAAE,CAAC,CAAC,CAAC;IAC5F,OAAO,eAAgBlC,CAAC,CAACyD,aAAa,CACpC9C,CAAC,EACD;MACE+C,SAAS,EAAEF,CAAC;MACZnB,OAAO,EAAE,IAAI,CAACJ,WAAW;MACzBiB,QAAQ,EAAEZ,CAAC;MACXqB,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,MAAM;MAClBC,IAAI,EAAE;IACR,CAAC,EACDP,CACF,CAAC;EACH;AACF,CAAC;AACD1B,CAAC,CAACkC,SAAS,GAAG;EACZnB,GAAG,EAAE1C,CAAC,CAAC8D,UAAU,CAACC,IAAI,CAAC,CAACC,UAAU;EAClCvB,GAAG,EAAEzC,CAAC,CAAC8D,UAAU,CAACC,IAAI,CAAC,CAACC,UAAU;EAClC5B,OAAO,EAAEpC,CAAC,CAACiE,IAAI;EACfjB,QAAQ,EAAEhD,CAAC,CAACkE;AACd,CAAC,EAAEvC,CAAC,CAACmB,YAAY,GAAG;EAClBL,GAAG,EAAEnB,CAAC;EACNoB,GAAG,EAAElB;AACP,CAAC;AACD,IAAI2C,CAAC,GAAGxC,CAAC;AACTvB,CAAC,CAAC+D,CAAC,CAAC;AACJ,SACEA,CAAC,IAAIC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}