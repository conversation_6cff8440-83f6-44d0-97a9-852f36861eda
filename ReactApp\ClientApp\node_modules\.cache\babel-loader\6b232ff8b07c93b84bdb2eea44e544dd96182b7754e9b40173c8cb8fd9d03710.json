{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as o from \"react\";\nimport n from \"prop-types\";\nimport { Animation as N } from \"./Animation.mjs\";\nimport p from \"./util.mjs\";\nconst P = a => {\n    const [c, E] = o.useState(),\n      [m, f] = o.useState(),\n      [s, u] = o.useState({}),\n      {\n        appear: g = r.appear,\n        enter: y = r.enter,\n        exit: W = r.exit,\n        transitionEnterDuration: H = r.transitionEnterDuration,\n        transitionExitDuration: v = r.transitionExitDuration,\n        direction: x = r.direction,\n        children: D,\n        childFactory: h,\n        ...T\n      } = a;\n    let i;\n    x === \"vertical\" ? i = {\n      maxHeight: c ? `${c}px` : \"\"\n    } : i = {\n      maxWidth: m ? `${m}px` : \"\"\n    };\n    const O = {\n      maxHeight: i.maxHeight,\n      maxWidth: i.maxWidth\n    };\n    o.useEffect(() => {\n      a && s.name && a[s.name] && a[s.name].call(void 0, s.event);\n    }, [c, m, s]);\n    const S = t => {\n        const {\n          onBeforeEnter: e\n        } = a;\n        e && e.call(void 0, t), l(t, \"onEnter\");\n      },\n      F = t => {\n        l(t, \"onEntering\");\n      },\n      M = t => {\n        l(t, \"onExit\");\n      },\n      l = (t, e) => {\n        const d = t.animatedElement.firstChild;\n        if (d) {\n          const w = p.outerHeight(d),\n            C = p.outerWidth(d);\n          E(w), f(C), u({\n            name: e,\n            event: t\n          });\n        }\n      },\n      $ = t => {\n        const e = h ? h(t) : t;\n        return e.props.in ? e : o.cloneElement(e, {\n          ...e.props,\n          style: {\n            ...e.props.style,\n            maxHeight: i.maxHeight,\n            maxWidth: i.maxWidth\n          }\n        });\n      };\n    return /* @__PURE__ */o.createElement(N, {\n      ...T,\n      appear: g,\n      enter: y,\n      exit: W,\n      transitionEnterDuration: H,\n      transitionExitDuration: v,\n      childFactory: $,\n      onEnter: S,\n      onEntering: F,\n      onExit: M,\n      animationEnteringStyle: O,\n      transitionName: `reveal-${x}`\n    }, D);\n  },\n  r = {\n    appear: !1,\n    enter: !0,\n    exit: !0,\n    transitionEnterDuration: 300,\n    transitionExitDuration: 300,\n    direction: \"vertical\"\n  };\nP.propTypes = {\n  children: n.oneOfType([n.arrayOf(n.node), n.node]),\n  childFactory: n.any,\n  className: n.string,\n  direction: n.oneOf([\"horizontal\", \"vertical\"]),\n  component: n.node,\n  id: n.string,\n  style: n.any\n};\nexport { P as Reveal };", "map": {"version": 3, "names": ["o", "n", "Animation", "N", "p", "P", "a", "c", "E", "useState", "m", "f", "s", "u", "appear", "g", "r", "enter", "y", "exit", "W", "transitionEnterDuration", "H", "transitionExitDuration", "v", "direction", "x", "children", "D", "childFactory", "h", "T", "i", "maxHeight", "max<PERSON><PERSON><PERSON>", "O", "useEffect", "name", "call", "event", "S", "t", "onBeforeEnter", "e", "l", "F", "M", "d", "animatedElement", "<PERSON><PERSON><PERSON><PERSON>", "w", "outerHeight", "C", "outerWidth", "$", "props", "in", "cloneElement", "style", "createElement", "onEnter", "onEntering", "onExit", "animationEnteringStyle", "transitionName", "propTypes", "oneOfType", "arrayOf", "node", "any", "className", "string", "oneOf", "component", "id", "Reveal"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-animation/Reveal.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as o from \"react\";\nimport n from \"prop-types\";\nimport { Animation as N } from \"./Animation.mjs\";\nimport p from \"./util.mjs\";\nconst P = (a) => {\n  const [c, E] = o.useState(), [m, f] = o.useState(), [s, u] = o.useState({}), {\n    appear: g = r.appear,\n    enter: y = r.enter,\n    exit: W = r.exit,\n    transitionEnterDuration: H = r.transitionEnterDuration,\n    transitionExitDuration: v = r.transitionExitDuration,\n    direction: x = r.direction,\n    children: D,\n    childFactory: h,\n    ...T\n  } = a;\n  let i;\n  x === \"vertical\" ? i = { maxHeight: c ? `${c}px` : \"\" } : i = { maxWidth: m ? `${m}px` : \"\" };\n  const O = {\n    maxHeight: i.maxHeight,\n    maxWidth: i.maxWidth\n  };\n  o.useEffect(() => {\n    a && s.name && a[s.name] && a[s.name].call(void 0, s.event);\n  }, [c, m, s]);\n  const S = (t) => {\n    const { onBeforeEnter: e } = a;\n    e && e.call(void 0, t), l(t, \"onEnter\");\n  }, F = (t) => {\n    l(t, \"onEntering\");\n  }, M = (t) => {\n    l(t, \"onExit\");\n  }, l = (t, e) => {\n    const d = t.animatedElement.firstChild;\n    if (d) {\n      const w = p.outerHeight(d), C = p.outerWidth(d);\n      E(w), f(C), u({\n        name: e,\n        event: t\n      });\n    }\n  }, $ = (t) => {\n    const e = h ? h(t) : t;\n    return e.props.in ? e : o.cloneElement(e, {\n      ...e.props,\n      style: {\n        ...e.props.style,\n        maxHeight: i.maxHeight,\n        maxWidth: i.maxWidth\n      }\n    });\n  };\n  return /* @__PURE__ */ o.createElement(\n    N,\n    {\n      ...T,\n      appear: g,\n      enter: y,\n      exit: W,\n      transitionEnterDuration: H,\n      transitionExitDuration: v,\n      childFactory: $,\n      onEnter: S,\n      onEntering: F,\n      onExit: M,\n      animationEnteringStyle: O,\n      transitionName: `reveal-${x}`\n    },\n    D\n  );\n}, r = {\n  appear: !1,\n  enter: !0,\n  exit: !0,\n  transitionEnterDuration: 300,\n  transitionExitDuration: 300,\n  direction: \"vertical\"\n};\nP.propTypes = {\n  children: n.oneOfType([n.arrayOf(n.node), n.node]),\n  childFactory: n.any,\n  className: n.string,\n  direction: n.oneOf([\"horizontal\", \"vertical\"]),\n  component: n.node,\n  id: n.string,\n  style: n.any\n};\nexport {\n  P as Reveal\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,SAAS,IAAIC,CAAC,QAAQ,iBAAiB;AAChD,OAAOC,CAAC,MAAM,YAAY;AAC1B,MAAMC,CAAC,GAAIC,CAAC,IAAK;IACf,MAAM,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAGR,CAAC,CAACS,QAAQ,CAAC,CAAC;MAAE,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAGX,CAAC,CAACS,QAAQ,CAAC,CAAC;MAAE,CAACG,CAAC,EAAEC,CAAC,CAAC,GAAGb,CAAC,CAACS,QAAQ,CAAC,CAAC,CAAC,CAAC;MAAE;QAC3EK,MAAM,EAAEC,CAAC,GAAGC,CAAC,CAACF,MAAM;QACpBG,KAAK,EAAEC,CAAC,GAAGF,CAAC,CAACC,KAAK;QAClBE,IAAI,EAAEC,CAAC,GAAGJ,CAAC,CAACG,IAAI;QAChBE,uBAAuB,EAAEC,CAAC,GAAGN,CAAC,CAACK,uBAAuB;QACtDE,sBAAsB,EAAEC,CAAC,GAAGR,CAAC,CAACO,sBAAsB;QACpDE,SAAS,EAAEC,CAAC,GAAGV,CAAC,CAACS,SAAS;QAC1BE,QAAQ,EAAEC,CAAC;QACXC,YAAY,EAAEC,CAAC;QACf,GAAGC;MACL,CAAC,GAAGzB,CAAC;IACL,IAAI0B,CAAC;IACLN,CAAC,KAAK,UAAU,GAAGM,CAAC,GAAG;MAAEC,SAAS,EAAE1B,CAAC,GAAG,GAAGA,CAAC,IAAI,GAAG;IAAG,CAAC,GAAGyB,CAAC,GAAG;MAAEE,QAAQ,EAAExB,CAAC,GAAG,GAAGA,CAAC,IAAI,GAAG;IAAG,CAAC;IAC7F,MAAMyB,CAAC,GAAG;MACRF,SAAS,EAAED,CAAC,CAACC,SAAS;MACtBC,QAAQ,EAAEF,CAAC,CAACE;IACd,CAAC;IACDlC,CAAC,CAACoC,SAAS,CAAC,MAAM;MAChB9B,CAAC,IAAIM,CAAC,CAACyB,IAAI,IAAI/B,CAAC,CAACM,CAAC,CAACyB,IAAI,CAAC,IAAI/B,CAAC,CAACM,CAAC,CAACyB,IAAI,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC,EAAE1B,CAAC,CAAC2B,KAAK,CAAC;IAC7D,CAAC,EAAE,CAAChC,CAAC,EAAEG,CAAC,EAAEE,CAAC,CAAC,CAAC;IACb,MAAM4B,CAAC,GAAIC,CAAC,IAAK;QACf,MAAM;UAAEC,aAAa,EAAEC;QAAE,CAAC,GAAGrC,CAAC;QAC9BqC,CAAC,IAAIA,CAAC,CAACL,IAAI,CAAC,KAAK,CAAC,EAAEG,CAAC,CAAC,EAAEG,CAAC,CAACH,CAAC,EAAE,SAAS,CAAC;MACzC,CAAC;MAAEI,CAAC,GAAIJ,CAAC,IAAK;QACZG,CAAC,CAACH,CAAC,EAAE,YAAY,CAAC;MACpB,CAAC;MAAEK,CAAC,GAAIL,CAAC,IAAK;QACZG,CAAC,CAACH,CAAC,EAAE,QAAQ,CAAC;MAChB,CAAC;MAAEG,CAAC,GAAGA,CAACH,CAAC,EAAEE,CAAC,KAAK;QACf,MAAMI,CAAC,GAAGN,CAAC,CAACO,eAAe,CAACC,UAAU;QACtC,IAAIF,CAAC,EAAE;UACL,MAAMG,CAAC,GAAG9C,CAAC,CAAC+C,WAAW,CAACJ,CAAC,CAAC;YAAEK,CAAC,GAAGhD,CAAC,CAACiD,UAAU,CAACN,CAAC,CAAC;UAC/CvC,CAAC,CAAC0C,CAAC,CAAC,EAAEvC,CAAC,CAACyC,CAAC,CAAC,EAAEvC,CAAC,CAAC;YACZwB,IAAI,EAAEM,CAAC;YACPJ,KAAK,EAAEE;UACT,CAAC,CAAC;QACJ;MACF,CAAC;MAAEa,CAAC,GAAIb,CAAC,IAAK;QACZ,MAAME,CAAC,GAAGb,CAAC,GAAGA,CAAC,CAACW,CAAC,CAAC,GAAGA,CAAC;QACtB,OAAOE,CAAC,CAACY,KAAK,CAACC,EAAE,GAAGb,CAAC,GAAG3C,CAAC,CAACyD,YAAY,CAACd,CAAC,EAAE;UACxC,GAAGA,CAAC,CAACY,KAAK;UACVG,KAAK,EAAE;YACL,GAAGf,CAAC,CAACY,KAAK,CAACG,KAAK;YAChBzB,SAAS,EAAED,CAAC,CAACC,SAAS;YACtBC,QAAQ,EAAEF,CAAC,CAACE;UACd;QACF,CAAC,CAAC;MACJ,CAAC;IACD,OAAO,eAAgBlC,CAAC,CAAC2D,aAAa,CACpCxD,CAAC,EACD;MACE,GAAG4B,CAAC;MACJjB,MAAM,EAAEC,CAAC;MACTE,KAAK,EAAEC,CAAC;MACRC,IAAI,EAAEC,CAAC;MACPC,uBAAuB,EAAEC,CAAC;MAC1BC,sBAAsB,EAAEC,CAAC;MACzBK,YAAY,EAAEyB,CAAC;MACfM,OAAO,EAAEpB,CAAC;MACVqB,UAAU,EAAEhB,CAAC;MACbiB,MAAM,EAAEhB,CAAC;MACTiB,sBAAsB,EAAE5B,CAAC;MACzB6B,cAAc,EAAE,UAAUtC,CAAC;IAC7B,CAAC,EACDE,CACF,CAAC;EACH,CAAC;EAAEZ,CAAC,GAAG;IACLF,MAAM,EAAE,CAAC,CAAC;IACVG,KAAK,EAAE,CAAC,CAAC;IACTE,IAAI,EAAE,CAAC,CAAC;IACRE,uBAAuB,EAAE,GAAG;IAC5BE,sBAAsB,EAAE,GAAG;IAC3BE,SAAS,EAAE;EACb,CAAC;AACDpB,CAAC,CAAC4D,SAAS,GAAG;EACZtC,QAAQ,EAAE1B,CAAC,CAACiE,SAAS,CAAC,CAACjE,CAAC,CAACkE,OAAO,CAAClE,CAAC,CAACmE,IAAI,CAAC,EAAEnE,CAAC,CAACmE,IAAI,CAAC,CAAC;EAClDvC,YAAY,EAAE5B,CAAC,CAACoE,GAAG;EACnBC,SAAS,EAAErE,CAAC,CAACsE,MAAM;EACnB9C,SAAS,EAAExB,CAAC,CAACuE,KAAK,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;EAC9CC,SAAS,EAAExE,CAAC,CAACmE,IAAI;EACjBM,EAAE,EAAEzE,CAAC,CAACsE,MAAM;EACZb,KAAK,EAAEzD,CAAC,CAACoE;AACX,CAAC;AACD,SACEhE,CAAC,IAAIsE,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}