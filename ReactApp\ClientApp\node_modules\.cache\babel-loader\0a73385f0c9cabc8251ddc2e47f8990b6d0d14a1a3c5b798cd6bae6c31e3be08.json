{"ast": null, "code": "//The error is represented by unique name and corresponding message\n//The message can contain placeholders with index, e.g. {0}, {1}\n\nexport default {\n  \"NoLocale\": \"Missing locale info for '{0}'\",\n  \"NoCurrency\": \"Cannot determine currency information. Please load the locale currencies data.\",\n  \"NoSupplementalCurrency\": \"Cannot determine currency. Please load the supplemental currencyData.\",\n  \"NoCurrencyRegion\": \"No currency data for region '{0}'\",\n  \"NoCurrencyDisplay\": \"Cannot determine currency display information. Please load the locale currencies data. The default culture does not include the all currencies data.\",\n  \"NoGMTInfo\": \"Cannot determine locale GMT format. Please load the locale timeZoneNames data.\",\n  \"NoWeekData\": \"Cannot determine locale first day of week. Please load the supplemental weekData.\",\n  \"NoFirstDay\": \"Cannot determine locale first day of week. Please load the supplemental weekData. The default culture includes only the 'en-US' first day info.\",\n  \"NoValidCurrency\": \"Cannot determine a default currency for the {0} locale. Please specify explicitly the currency with the format options.\",\n  \"NoDateFieldNames\": \"Cannot determine the locale date field names. Please load the locale dateFields data.\"\n};", "map": {"version": 3, "names": [], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-intl/dist/es/error-details.js"], "sourcesContent": ["//The error is represented by unique name and corresponding message\n//The message can contain placeholders with index, e.g. {0}, {1}\n\nexport default {\n    \"NoLocale\": \"Missing locale info for '{0}'\",\n    \"NoCurrency\": \"Cannot determine currency information. Please load the locale currencies data.\",\n    \"NoSupplementalCurrency\": \"Cannot determine currency. Please load the supplemental currencyData.\",\n    \"NoCurrencyRegion\": \"No currency data for region '{0}'\",\n    \"NoCurrencyDisplay\": \"Cannot determine currency display information. Please load the locale currencies data. The default culture does not include the all currencies data.\",\n    \"NoGMTInfo\": \"Cannot determine locale GMT format. Please load the locale timeZoneNames data.\",\n    \"NoWeekData\": \"Cannot determine locale first day of week. Please load the supplemental weekData.\",\n    \"NoFirstDay\": \"Cannot determine locale first day of week. Please load the supplemental weekData. The default culture includes only the 'en-US' first day info.\",\n    \"NoValidCurrency\": \"Cannot determine a default currency for the {0} locale. Please specify explicitly the currency with the format options.\",\n    \"NoDateFieldNames\": \"Cannot determine the locale date field names. Please load the locale dateFields data.\"\n};\n"], "mappings": "AAAA;AACA;;AAEA,eAAe;EACX,UAAU,EAAE,+BAA+B;EAC3C,YAAY,EAAE,gFAAgF;EAC9F,wBAAwB,EAAE,uEAAuE;EACjG,kBAAkB,EAAE,mCAAmC;EACvD,mBAAmB,EAAE,sJAAsJ;EAC3K,WAAW,EAAE,gFAAgF;EAC7F,YAAY,EAAE,mFAAmF;EACjG,YAAY,EAAE,iJAAiJ;EAC/J,iBAAiB,EAAE,yHAAyH;EAC5I,kBAAkB,EAAE;AACxB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}