{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst e = {\n  name: \"@progress/kendo-react-layout\",\n  productName: \"KendoReact\",\n  productCode: \"KENDOUIREACT\",\n  productCodes: [\"KENDOUIREACT\"],\n  publishDate: **********,\n  version: \"11.0.0\",\n  licensingDocsUrl: \"https://www.telerik.com/kendo-react-ui/components/my-license/\"\n};\nexport { e as packageMetadata };", "map": {"version": 3, "names": ["e", "name", "productName", "productCode", "productCodes", "publishDate", "version", "licensingDocsUrl", "packageMetadata"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/package-metadata.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst e = {\n  name: \"@progress/kendo-react-layout\",\n  productName: \"KendoReact\",\n  productCode: \"KENDOUIREACT\",\n  productCodes: [\"KENDOUIREACT\"],\n  publishDate: **********,\n  version: \"11.0.0\",\n  licensingDocsUrl: \"https://www.telerik.com/kendo-react-ui/components/my-license/\"\n};\nexport {\n  e as packageMetadata\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAG;EACRC,IAAI,EAAE,8BAA8B;EACpCC,WAAW,EAAE,YAAY;EACzBC,WAAW,EAAE,cAAc;EAC3BC,YAAY,EAAE,CAAC,cAAc,CAAC;EAC9BC,WAAW,EAAE,UAAU;EACvBC,OAAO,EAAE,QAAQ;EACjBC,gBAAgB,EAAE;AACpB,CAAC;AACD,SACEP,CAAC,IAAIQ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}