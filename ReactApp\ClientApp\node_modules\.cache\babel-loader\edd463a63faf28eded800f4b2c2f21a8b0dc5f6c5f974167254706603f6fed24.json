{"ast": null, "code": "export function swing(position) {\n  return 0.5 - Math.cos(position * Math.PI) / 2;\n}\nexport function linear(position) {\n  return position;\n}\nexport function easeOutElastic(position, time, start, diff) {\n  var s = 1.70158,\n    p = 0,\n    a = diff;\n  if (position === 0) {\n    return start;\n  }\n  if (position === 1) {\n    return start + diff;\n  }\n  if (!p) {\n    p = 0.5;\n  }\n  if (a < Math.abs(diff)) {\n    a = diff;\n    s = p / 4;\n  } else {\n    s = p / (2 * Math.PI) * Math.asin(diff / a);\n  }\n  return a * Math.pow(2, -10 * position) * Math.sin((Number(position) - s) * (1.1 * Math.PI) / p) + diff + start;\n}", "map": {"version": 3, "names": ["swing", "position", "Math", "cos", "PI", "linear", "easeOutElastic", "time", "start", "diff", "s", "p", "a", "abs", "asin", "pow", "sin", "Number"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/animations/easing-functions.js"], "sourcesContent": ["export function swing(position) {\n    return 0.5 - Math.cos(position * Math.PI) / 2;\n}\n\nexport function linear(position) {\n    return position;\n}\n\nexport function easeOutElastic(position, time, start, diff) {\n    var s = 1.70158,\n        p = 0,\n        a = diff;\n\n    if (position === 0) {\n        return start;\n    }\n\n    if (position === 1) {\n        return start + diff;\n    }\n\n    if (!p) {\n        p = 0.5;\n    }\n\n    if (a < Math.abs(diff)) {\n        a = diff;\n        s = p / 4;\n    } else {\n        s = p / (2 * Math.PI) * Math.asin(diff / a);\n    }\n\n    return a * Math.pow(2, -10 * position) *\n           Math.sin((Number(position) - s) * (1.1 * Math.PI) / p) + diff + start;\n}\n"], "mappings": "AAAA,OAAO,SAASA,KAAKA,CAACC,QAAQ,EAAE;EAC5B,OAAO,GAAG,GAAGC,IAAI,CAACC,GAAG,CAACF,QAAQ,GAAGC,IAAI,CAACE,EAAE,CAAC,GAAG,CAAC;AACjD;AAEA,OAAO,SAASC,MAAMA,CAACJ,QAAQ,EAAE;EAC7B,OAAOA,QAAQ;AACnB;AAEA,OAAO,SAASK,cAAcA,CAACL,QAAQ,EAAEM,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAE;EACxD,IAAIC,CAAC,GAAG,OAAO;IACXC,CAAC,GAAG,CAAC;IACLC,CAAC,GAAGH,IAAI;EAEZ,IAAIR,QAAQ,KAAK,CAAC,EAAE;IAChB,OAAOO,KAAK;EAChB;EAEA,IAAIP,QAAQ,KAAK,CAAC,EAAE;IAChB,OAAOO,KAAK,GAAGC,IAAI;EACvB;EAEA,IAAI,CAACE,CAAC,EAAE;IACJA,CAAC,GAAG,GAAG;EACX;EAEA,IAAIC,CAAC,GAAGV,IAAI,CAACW,GAAG,CAACJ,IAAI,CAAC,EAAE;IACpBG,CAAC,GAAGH,IAAI;IACRC,CAAC,GAAGC,CAAC,GAAG,CAAC;EACb,CAAC,MAAM;IACHD,CAAC,GAAGC,CAAC,IAAI,CAAC,GAAGT,IAAI,CAACE,EAAE,CAAC,GAAGF,IAAI,CAACY,IAAI,CAACL,IAAI,GAAGG,CAAC,CAAC;EAC/C;EAEA,OAAOA,CAAC,GAAGV,IAAI,CAACa,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,GAAGd,QAAQ,CAAC,GAC/BC,IAAI,CAACc,GAAG,CAAC,CAACC,MAAM,CAAChB,QAAQ,CAAC,GAAGS,CAAC,KAAK,GAAG,GAAGR,IAAI,CAACE,EAAE,CAAC,GAAGO,CAAC,CAAC,GAAGF,IAAI,GAAGD,KAAK;AAChF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}