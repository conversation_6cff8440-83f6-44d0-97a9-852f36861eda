{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport React from 'react';\nexport var offset = 4;\nexport default function dropIndicatorRender(props) {\n  var dropPosition = props.dropPosition,\n    dropLevelOffset = props.dropLevelOffset,\n    prefixCls = props.prefixCls,\n    indent = props.indent,\n    _props$direction = props.direction,\n    direction = _props$direction === void 0 ? 'ltr' : _props$direction;\n  var startPosition = direction === 'ltr' ? 'left' : 'right';\n  var endPosition = direction === 'ltr' ? 'right' : 'left';\n  var style = _defineProperty(_defineProperty({}, startPosition, -dropLevelOffset * indent + offset), endPosition, 0);\n  switch (dropPosition) {\n    case -1:\n      style.top = -3;\n      break;\n    case 1:\n      style.bottom = -3;\n      break;\n    default:\n      // dropPosition === 0\n      style.bottom = -3;\n      style[startPosition] = indent + offset;\n      break;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: style,\n    className: \"\".concat(prefixCls, \"-drop-indicator\")\n  });\n}", "map": {"version": 3, "names": ["_defineProperty", "React", "offset", "dropIndicatorRender", "props", "dropPosition", "dropLevelOffset", "prefixCls", "indent", "_props$direction", "direction", "startPosition", "endPosition", "style", "top", "bottom", "createElement", "className", "concat"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/antd/es/tree/utils/dropIndicator.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport React from 'react';\nexport var offset = 4;\nexport default function dropIndicatorRender(props) {\n  var dropPosition = props.dropPosition,\n    dropLevelOffset = props.dropLevelOffset,\n    prefixCls = props.prefixCls,\n    indent = props.indent,\n    _props$direction = props.direction,\n    direction = _props$direction === void 0 ? 'ltr' : _props$direction;\n  var startPosition = direction === 'ltr' ? 'left' : 'right';\n  var endPosition = direction === 'ltr' ? 'right' : 'left';\n  var style = _defineProperty(_defineProperty({}, startPosition, -dropLevelOffset * indent + offset), endPosition, 0);\n  switch (dropPosition) {\n    case -1:\n      style.top = -3;\n      break;\n    case 1:\n      style.bottom = -3;\n      break;\n    default:\n      // dropPosition === 0\n      style.bottom = -3;\n      style[startPosition] = indent + offset;\n      break;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: style,\n    className: \"\".concat(prefixCls, \"-drop-indicator\")\n  });\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,IAAIC,MAAM,GAAG,CAAC;AACrB,eAAe,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EACjD,IAAIC,YAAY,GAAGD,KAAK,CAACC,YAAY;IACnCC,eAAe,GAAGF,KAAK,CAACE,eAAe;IACvCC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BC,MAAM,GAAGJ,KAAK,CAACI,MAAM;IACrBC,gBAAgB,GAAGL,KAAK,CAACM,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,gBAAgB;EACpE,IAAIE,aAAa,GAAGD,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO;EAC1D,IAAIE,WAAW,GAAGF,SAAS,KAAK,KAAK,GAAG,OAAO,GAAG,MAAM;EACxD,IAAIG,KAAK,GAAGb,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAEW,aAAa,EAAE,CAACL,eAAe,GAAGE,MAAM,GAAGN,MAAM,CAAC,EAAEU,WAAW,EAAE,CAAC,CAAC;EACnH,QAAQP,YAAY;IAClB,KAAK,CAAC,CAAC;MACLQ,KAAK,CAACC,GAAG,GAAG,CAAC,CAAC;MACd;IACF,KAAK,CAAC;MACJD,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC;MACjB;IACF;MACE;MACAF,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC;MACjBF,KAAK,CAACF,aAAa,CAAC,GAAGH,MAAM,GAAGN,MAAM;MACtC;EACJ;EACA,OAAO,aAAaD,KAAK,CAACe,aAAa,CAAC,KAAK,EAAE;IAC7CH,KAAK,EAAEA,KAAK;IACZI,SAAS,EAAE,EAAE,CAACC,MAAM,CAACX,SAAS,EAAE,iBAAiB;EACnD,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}