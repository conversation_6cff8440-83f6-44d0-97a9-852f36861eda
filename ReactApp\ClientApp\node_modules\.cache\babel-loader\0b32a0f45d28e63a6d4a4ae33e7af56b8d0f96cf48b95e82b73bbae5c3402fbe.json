{"ast": null, "code": "import { createDate } from './create-date';\n/**\n * A function which returns the first date of the month.\n *\n * @param date - The initial date.\n * @returns - The first date of the initial date month.\n *\n * @example\n * ```ts-no-run\n * firstDayOfMonth(new Date(2016, 0, 15)); // 2016-01-01\n * ```\n */\nexport var firstDayOfMonth = function (date) {\n  return createDate(date.getFullYear(), date.getMonth(), 1, date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n};", "map": {"version": 3, "names": ["createDate", "firstDayOfMonth", "date", "getFullYear", "getMonth", "getHours", "getMinutes", "getSeconds", "getMilliseconds"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/first-day-of-month.js"], "sourcesContent": ["import { createDate } from './create-date';\n/**\n * A function which returns the first date of the month.\n *\n * @param date - The initial date.\n * @returns - The first date of the initial date month.\n *\n * @example\n * ```ts-no-run\n * firstDayOfMonth(new Date(2016, 0, 15)); // 2016-01-01\n * ```\n */\nexport var firstDayOfMonth = function (date) {\n    return createDate(date.getFullYear(), date.getMonth(), 1, date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n};\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,eAAe,GAAG,SAAAA,CAAUC,IAAI,EAAE;EACzC,OAAOF,UAAU,CAACE,IAAI,CAACC,WAAW,CAAC,CAAC,EAAED,IAAI,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEF,IAAI,CAACG,QAAQ,CAAC,CAAC,EAAEH,IAAI,CAACI,UAAU,CAAC,CAAC,EAAEJ,IAAI,CAACK,UAAU,CAAC,CAAC,EAAEL,IAAI,CAACM,eAAe,CAAC,CAAC,CAAC;AAC5I,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}