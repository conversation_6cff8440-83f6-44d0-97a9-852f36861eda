{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst f = s => s != null && s !== \"\",\n  o = (s, t, i) => !f(s) || isNaN(s) || s <= t ? t : s >= i ? i : s;\nexport { o as fitIntoBounds, f as isPresent };", "map": {"version": 3, "names": ["f", "s", "o", "t", "i", "isNaN", "fitIntoBounds", "isPresent"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-inputs/colors/utils/misc.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst f = (s) => s != null && s !== \"\", o = (s, t, i) => !f(s) || isNaN(s) || s <= t ? t : s >= i ? i : s;\nexport {\n  o as fitIntoBounds,\n  f as isPresent\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAIC,CAAC,IAAKA,CAAC,IAAI,IAAI,IAAIA,CAAC,KAAK,EAAE;EAAEC,CAAC,GAAGA,CAACD,CAAC,EAAEE,CAAC,EAAEC,CAAC,KAAK,CAACJ,CAAC,CAACC,CAAC,CAAC,IAAII,KAAK,CAACJ,CAAC,CAAC,IAAIA,CAAC,IAAIE,CAAC,GAAGA,CAAC,GAAGF,CAAC,IAAIG,CAAC,GAAGA,CAAC,GAAGH,CAAC;AACzG,SACEC,CAAC,IAAII,aAAa,EAClBN,CAAC,IAAIO,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}