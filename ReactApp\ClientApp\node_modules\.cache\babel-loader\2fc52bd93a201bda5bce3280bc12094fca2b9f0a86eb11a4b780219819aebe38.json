{"ast": null, "code": "import Node from './node';\nimport renderAttr from './utils/render-attribute';\nvar ClipNode = function (Node) {\n  function ClipNode(srcElement) {\n    Node.call(this);\n    this.srcElement = srcElement;\n    this.id = srcElement.id;\n    this.load([srcElement]);\n  }\n  if (Node) ClipNode.__proto__ = Node;\n  ClipNode.prototype = Object.create(Node && Node.prototype);\n  ClipNode.prototype.constructor = ClipNode;\n  ClipNode.prototype.renderClipRule = function renderClipRule() {\n    return renderAttr(\"clip-rule\", \"evenodd\");\n  };\n  ClipNode.prototype.template = function template() {\n    return \"<clipPath \" + this.renderClipRule() + \" id='\" + this.id + \"'>\" + this.renderChildren() + \"</clipPath>\";\n  };\n  return ClipNode;\n}(Node);\nexport default ClipNode;", "map": {"version": 3, "names": ["Node", "renderAttr", "ClipNode", "srcElement", "call", "id", "load", "__proto__", "prototype", "Object", "create", "constructor", "renderClipRule", "template", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/svg/clip-node.js"], "sourcesContent": ["import Node from './node';\nimport renderAttr from './utils/render-attribute';\n\nvar ClipNode = (function (Node) {\n    function ClipNode(srcElement) {\n        Node.call(this);\n\n        this.srcElement = srcElement;\n        this.id = srcElement.id;\n\n        this.load([ srcElement ]);\n    }\n\n    if ( Node ) ClipNode.__proto__ = Node;\n    ClipNode.prototype = Object.create( Node && Node.prototype );\n    ClipNode.prototype.constructor = ClipNode;\n\n    ClipNode.prototype.renderClipRule = function renderClipRule () {\n        return renderAttr(\"clip-rule\", \"evenodd\");\n    };\n    ClipNode.prototype.template = function template () {\n        return (\"<clipPath \" + (this.renderClipRule()) + \" id='\" + (this.id) + \"'>\" + (this.renderChildren()) + \"</clipPath>\");\n    };\n\n    return ClipNode;\n}(Node));\n\nexport default ClipNode;"], "mappings": "AAAA,OAAOA,IAAI,MAAM,QAAQ;AACzB,OAAOC,UAAU,MAAM,0BAA0B;AAEjD,IAAIC,QAAQ,GAAI,UAAUF,IAAI,EAAE;EAC5B,SAASE,QAAQA,CAACC,UAAU,EAAE;IAC1BH,IAAI,CAACI,IAAI,CAAC,IAAI,CAAC;IAEf,IAAI,CAACD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACE,EAAE,GAAGF,UAAU,CAACE,EAAE;IAEvB,IAAI,CAACC,IAAI,CAAC,CAAEH,UAAU,CAAE,CAAC;EAC7B;EAEA,IAAKH,IAAI,EAAGE,QAAQ,CAACK,SAAS,GAAGP,IAAI;EACrCE,QAAQ,CAACM,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAEV,IAAI,IAAIA,IAAI,CAACQ,SAAU,CAAC;EAC5DN,QAAQ,CAACM,SAAS,CAACG,WAAW,GAAGT,QAAQ;EAEzCA,QAAQ,CAACM,SAAS,CAACI,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAI;IAC3D,OAAOX,UAAU,CAAC,WAAW,EAAE,SAAS,CAAC;EAC7C,CAAC;EACDC,QAAQ,CAACM,SAAS,CAACK,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;IAC/C,OAAQ,YAAY,GAAI,IAAI,CAACD,cAAc,CAAC,CAAE,GAAG,OAAO,GAAI,IAAI,CAACP,EAAG,GAAG,IAAI,GAAI,IAAI,CAACS,cAAc,CAAC,CAAE,GAAG,aAAa;EACzH,CAAC;EAED,OAAOZ,QAAQ;AACnB,CAAC,CAACF,IAAI,CAAE;AAER,eAAeE,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}