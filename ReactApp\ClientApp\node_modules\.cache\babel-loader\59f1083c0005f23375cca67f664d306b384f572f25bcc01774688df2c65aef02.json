{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport shallowEqual from 'shallowequal';\nexport function createContext() {\n  var Context = /*#__PURE__*/React.createContext(null);\n  var Provider = function Provider(_ref) {\n    var value = _ref.value,\n      children = _ref.children;\n    var valueRef = React.useRef(value);\n    valueRef.current = value;\n    var _React$useState = React.useState(function () {\n        return {\n          getValue: function getValue() {\n            return valueRef.current;\n          },\n          listeners: new Set()\n        };\n      }),\n      _React$useState2 = _slicedToArray(_React$useState, 1),\n      context = _React$useState2[0];\n    useLayoutEffect(function () {\n      context.listeners.forEach(function (listener) {\n        listener(value);\n      });\n    }, [value]);\n    return /*#__PURE__*/React.createElement(Context.Provider, {\n      value: context\n    }, children);\n  };\n  return {\n    Context: Context,\n    Provider: Provider\n  };\n}\nexport function useContextSelector(holder, selector) {\n  var eventSelector = useEvent(selector);\n  var context = React.useContext(holder === null || holder === void 0 ? void 0 : holder.Context);\n  var _ref2 = context || {},\n    listeners = _ref2.listeners,\n    getValue = _ref2.getValue;\n  var _React$useState3 = React.useState(function () {\n      return eventSelector(context ? getValue() : null);\n    }),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    value = _React$useState4[0],\n    setValue = _React$useState4[1];\n  useLayoutEffect(function () {\n    if (!context) {\n      return;\n    }\n    function trigger(nextValue) {\n      setValue(function (prev) {\n        var selectedValue = eventSelector(nextValue);\n        return shallowEqual(prev, selectedValue) ? prev : selectedValue;\n      });\n    }\n    listeners.add(trigger);\n    return function () {\n      listeners.delete(trigger);\n    };\n  }, [context]);\n  return value;\n}", "map": {"version": 3, "names": ["_slicedToArray", "React", "useLayoutEffect", "useEvent", "shallowEqual", "createContext", "Context", "Provider", "_ref", "value", "children", "valueRef", "useRef", "current", "_React$useState", "useState", "getValue", "listeners", "Set", "_React$useState2", "context", "for<PERSON>ach", "listener", "createElement", "useContextSelector", "holder", "selector", "eventSelector", "useContext", "_ref2", "_React$useState3", "_React$useState4", "setValue", "trigger", "nextValue", "prev", "selected<PERSON><PERSON><PERSON>", "add", "delete"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/rc-table/es/ContextSelector/index.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport shallowEqual from 'shallowequal';\nexport function createContext() {\n  var Context = /*#__PURE__*/React.createContext(null);\n\n  var Provider = function Provider(_ref) {\n    var value = _ref.value,\n        children = _ref.children;\n    var valueRef = React.useRef(value);\n    valueRef.current = value;\n\n    var _React$useState = React.useState(function () {\n      return {\n        getValue: function getValue() {\n          return valueRef.current;\n        },\n        listeners: new Set()\n      };\n    }),\n        _React$useState2 = _slicedToArray(_React$useState, 1),\n        context = _React$useState2[0];\n\n    useLayoutEffect(function () {\n      context.listeners.forEach(function (listener) {\n        listener(value);\n      });\n    }, [value]);\n    return /*#__PURE__*/React.createElement(Context.Provider, {\n      value: context\n    }, children);\n  };\n\n  return {\n    Context: Context,\n    Provider: Provider\n  };\n}\nexport function useContextSelector(holder, selector) {\n  var eventSelector = useEvent(selector);\n  var context = React.useContext(holder === null || holder === void 0 ? void 0 : holder.Context);\n\n  var _ref2 = context || {},\n      listeners = _ref2.listeners,\n      getValue = _ref2.getValue;\n\n  var _React$useState3 = React.useState(function () {\n    return eventSelector(context ? getValue() : null);\n  }),\n      _React$useState4 = _slicedToArray(_React$useState3, 2),\n      value = _React$useState4[0],\n      setValue = _React$useState4[1];\n\n  useLayoutEffect(function () {\n    if (!context) {\n      return;\n    }\n\n    function trigger(nextValue) {\n      setValue(function (prev) {\n        var selectedValue = eventSelector(nextValue);\n        return shallowEqual(prev, selectedValue) ? prev : selectedValue;\n      });\n    }\n\n    listeners.add(trigger);\n    return function () {\n      listeners.delete(trigger);\n    };\n  }, [context]);\n  return value;\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,YAAY,MAAM,cAAc;AACvC,OAAO,SAASC,aAAaA,CAAA,EAAG;EAC9B,IAAIC,OAAO,GAAG,aAAaL,KAAK,CAACI,aAAa,CAAC,IAAI,CAAC;EAEpD,IAAIE,QAAQ,GAAG,SAASA,QAAQA,CAACC,IAAI,EAAE;IACrC,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;MAClBC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IAC5B,IAAIC,QAAQ,GAAGV,KAAK,CAACW,MAAM,CAACH,KAAK,CAAC;IAClCE,QAAQ,CAACE,OAAO,GAAGJ,KAAK;IAExB,IAAIK,eAAe,GAAGb,KAAK,CAACc,QAAQ,CAAC,YAAY;QAC/C,OAAO;UACLC,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;YAC5B,OAAOL,QAAQ,CAACE,OAAO;UACzB,CAAC;UACDI,SAAS,EAAE,IAAIC,GAAG,CAAC;QACrB,CAAC;MACH,CAAC,CAAC;MACEC,gBAAgB,GAAGnB,cAAc,CAACc,eAAe,EAAE,CAAC,CAAC;MACrDM,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAEjCjB,eAAe,CAAC,YAAY;MAC1BkB,OAAO,CAACH,SAAS,CAACI,OAAO,CAAC,UAAUC,QAAQ,EAAE;QAC5CA,QAAQ,CAACb,KAAK,CAAC;MACjB,CAAC,CAAC;IACJ,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;IACX,OAAO,aAAaR,KAAK,CAACsB,aAAa,CAACjB,OAAO,CAACC,QAAQ,EAAE;MACxDE,KAAK,EAAEW;IACT,CAAC,EAAEV,QAAQ,CAAC;EACd,CAAC;EAED,OAAO;IACLJ,OAAO,EAAEA,OAAO;IAChBC,QAAQ,EAAEA;EACZ,CAAC;AACH;AACA,OAAO,SAASiB,kBAAkBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EACnD,IAAIC,aAAa,GAAGxB,QAAQ,CAACuB,QAAQ,CAAC;EACtC,IAAIN,OAAO,GAAGnB,KAAK,CAAC2B,UAAU,CAACH,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACnB,OAAO,CAAC;EAE9F,IAAIuB,KAAK,GAAGT,OAAO,IAAI,CAAC,CAAC;IACrBH,SAAS,GAAGY,KAAK,CAACZ,SAAS;IAC3BD,QAAQ,GAAGa,KAAK,CAACb,QAAQ;EAE7B,IAAIc,gBAAgB,GAAG7B,KAAK,CAACc,QAAQ,CAAC,YAAY;MAChD,OAAOY,aAAa,CAACP,OAAO,GAAGJ,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC;IACnD,CAAC,CAAC;IACEe,gBAAgB,GAAG/B,cAAc,CAAC8B,gBAAgB,EAAE,CAAC,CAAC;IACtDrB,KAAK,GAAGsB,gBAAgB,CAAC,CAAC,CAAC;IAC3BC,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EAElC7B,eAAe,CAAC,YAAY;IAC1B,IAAI,CAACkB,OAAO,EAAE;MACZ;IACF;IAEA,SAASa,OAAOA,CAACC,SAAS,EAAE;MAC1BF,QAAQ,CAAC,UAAUG,IAAI,EAAE;QACvB,IAAIC,aAAa,GAAGT,aAAa,CAACO,SAAS,CAAC;QAC5C,OAAO9B,YAAY,CAAC+B,IAAI,EAAEC,aAAa,CAAC,GAAGD,IAAI,GAAGC,aAAa;MACjE,CAAC,CAAC;IACJ;IAEAnB,SAAS,CAACoB,GAAG,CAACJ,OAAO,CAAC;IACtB,OAAO,YAAY;MACjBhB,SAAS,CAACqB,MAAM,CAACL,OAAO,CAAC;IAC3B,CAAC;EACH,CAAC,EAAE,CAACb,OAAO,CAAC,CAAC;EACb,OAAOX,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}