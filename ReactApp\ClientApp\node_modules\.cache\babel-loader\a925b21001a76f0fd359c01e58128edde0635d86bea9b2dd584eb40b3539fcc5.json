{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { LABEL_DECIMALS as a, MIN_RATIO as o } from \"./constants.mjs\";\nconst h = e => {\n    const t = e.toString().split(\".\");\n    return t.length === 1 ? `${t[0]}` : `${t[0]}.${t[1].substr(0, a)}`;\n  },\n  l = (e, t, n) => {\n    const r = Math.abs((t - e) / 100);\n    return Math.abs((n - e) / r);\n  },\n  $ = (e, t, n, r) => {\n    const c = Math.max(n, 0.01),\n      u = 100 / c * 100;\n    e.current && t.current && (e.current.style.width = r ? \"100%\" : `${c}%`, t.current.style.width = r ? \"100%\" : `${u}%`, e.current.style.height = r ? `${c}%` : \"100%\", t.current.style.height = r ? `${u}%` : \"100%\");\n  },\n  g = (e, t, n) => Math.max((n - e) / (t - e), o);\nexport { l as calculatePercentage, g as calculateRatio, h as truncateNumber, $ as updateProgress };", "map": {"version": 3, "names": ["LABEL_DECIMALS", "a", "MIN_RATIO", "o", "h", "e", "t", "toString", "split", "length", "substr", "l", "n", "r", "Math", "abs", "$", "c", "max", "u", "current", "style", "width", "height", "g", "calculatePercentage", "calculateRatio", "truncateNumber", "updateProgress"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-progressbars/common/utils.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { LABEL_DECIMALS as a, MIN_RATIO as o } from \"./constants.mjs\";\nconst h = (e) => {\n  const t = e.toString().split(\".\");\n  return t.length === 1 ? `${t[0]}` : `${t[0]}.${t[1].substr(0, a)}`;\n}, l = (e, t, n) => {\n  const r = Math.abs((t - e) / 100);\n  return Math.abs((n - e) / r);\n}, $ = (e, t, n, r) => {\n  const c = Math.max(n, 0.01), u = 100 / c * 100;\n  e.current && t.current && (e.current.style.width = r ? \"100%\" : `${c}%`, t.current.style.width = r ? \"100%\" : `${u}%`, e.current.style.height = r ? `${c}%` : \"100%\", t.current.style.height = r ? `${u}%` : \"100%\");\n}, g = (e, t, n) => Math.max((n - e) / (t - e), o);\nexport {\n  l as calculatePercentage,\n  g as calculateRatio,\n  h as truncateNumber,\n  $ as updateProgress\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,cAAc,IAAIC,CAAC,EAAEC,SAAS,IAAIC,CAAC,QAAQ,iBAAiB;AACrE,MAAMC,CAAC,GAAIC,CAAC,IAAK;IACf,MAAMC,CAAC,GAAGD,CAAC,CAACE,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;IACjC,OAAOF,CAAC,CAACG,MAAM,KAAK,CAAC,GAAG,GAAGH,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAGA,CAAC,CAAC,CAAC,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAACI,MAAM,CAAC,CAAC,EAAET,CAAC,CAAC,EAAE;EACpE,CAAC;EAAEU,CAAC,GAAGA,CAACN,CAAC,EAAEC,CAAC,EAAEM,CAAC,KAAK;IAClB,MAAMC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAACT,CAAC,GAAGD,CAAC,IAAI,GAAG,CAAC;IACjC,OAAOS,IAAI,CAACC,GAAG,CAAC,CAACH,CAAC,GAAGP,CAAC,IAAIQ,CAAC,CAAC;EAC9B,CAAC;EAAEG,CAAC,GAAGA,CAACX,CAAC,EAAEC,CAAC,EAAEM,CAAC,EAAEC,CAAC,KAAK;IACrB,MAAMI,CAAC,GAAGH,IAAI,CAACI,GAAG,CAACN,CAAC,EAAE,IAAI,CAAC;MAAEO,CAAC,GAAG,GAAG,GAAGF,CAAC,GAAG,GAAG;IAC9CZ,CAAC,CAACe,OAAO,IAAId,CAAC,CAACc,OAAO,KAAKf,CAAC,CAACe,OAAO,CAACC,KAAK,CAACC,KAAK,GAAGT,CAAC,GAAG,MAAM,GAAG,GAAGI,CAAC,GAAG,EAAEX,CAAC,CAACc,OAAO,CAACC,KAAK,CAACC,KAAK,GAAGT,CAAC,GAAG,MAAM,GAAG,GAAGM,CAAC,GAAG,EAAEd,CAAC,CAACe,OAAO,CAACC,KAAK,CAACE,MAAM,GAAGV,CAAC,GAAG,GAAGI,CAAC,GAAG,GAAG,MAAM,EAAEX,CAAC,CAACc,OAAO,CAACC,KAAK,CAACE,MAAM,GAAGV,CAAC,GAAG,GAAGM,CAAC,GAAG,GAAG,MAAM,CAAC;EACtN,CAAC;EAAEK,CAAC,GAAGA,CAACnB,CAAC,EAAEC,CAAC,EAAEM,CAAC,KAAKE,IAAI,CAACI,GAAG,CAAC,CAACN,CAAC,GAAGP,CAAC,KAAKC,CAAC,GAAGD,CAAC,CAAC,EAAEF,CAAC,CAAC;AAClD,SACEQ,CAAC,IAAIc,mBAAmB,EACxBD,CAAC,IAAIE,cAAc,EACnBtB,CAAC,IAAIuB,cAAc,EACnBX,CAAC,IAAIY,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}