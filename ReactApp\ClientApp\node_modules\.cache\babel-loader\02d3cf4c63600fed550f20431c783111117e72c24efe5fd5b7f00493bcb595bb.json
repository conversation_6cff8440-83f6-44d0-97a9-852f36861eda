{"ast": null, "code": "import elementScrollPosition from './element-scroll-position';\nimport parentScrollPosition from './parent-scroll-position';\nexport default function (offsetParentElement, element) {\n  return (\n    // eslint-disable-line no-arrow-condition\n    offsetParentElement ? elementScrollPosition(offsetParentElement) : parentScrollPosition(element)\n  );\n}\n;", "map": {"version": 3, "names": ["elementScrollPosition", "parentScrollPosition", "offsetParentElement", "element"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-popup-common/dist/es/offset-parent-scroll-position.js"], "sourcesContent": ["import elementScrollPosition from './element-scroll-position';\nimport parentScrollPosition from './parent-scroll-position';\n\nexport default function (offsetParentElement, element) { return ( // eslint-disable-line no-arrow-condition\n    offsetParentElement ? elementScrollPosition(offsetParentElement) : parentScrollPosition(element)\n); };\n"], "mappings": "AAAA,OAAOA,qBAAqB,MAAM,2BAA2B;AAC7D,OAAOC,oBAAoB,MAAM,0BAA0B;AAE3D,eAAe,UAAUC,mBAAmB,EAAEC,OAAO,EAAE;EAAE;IAAS;IAC9DD,mBAAmB,GAAGF,qBAAqB,CAACE,mBAAmB,CAAC,GAAGD,oBAAoB,CAACE,OAAO;EAAC;AACjG;AAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}