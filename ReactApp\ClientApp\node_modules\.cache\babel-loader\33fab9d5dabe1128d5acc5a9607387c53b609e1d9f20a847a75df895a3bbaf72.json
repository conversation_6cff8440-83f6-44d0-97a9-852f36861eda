{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as l from \"react\";\nimport t from \"prop-types\";\nimport { Animation as u } from \"./Animation.mjs\";\nconst x = r => {\n    const {\n        appear: i = n.appear,\n        enter: e = n.enter,\n        exit: o = n.exit,\n        transitionEnterDuration: a = n.transitionEnterDuration,\n        transitionExitDuration: s = n.transitionExitDuration,\n        direction: p = n.direction,\n        children: c,\n        ...d\n      } = r,\n      m = {\n        transitionName: `expand-${p}`\n      };\n    return /* @__PURE__ */l.createElement(u, {\n      ...m,\n      appear: i,\n      enter: e,\n      exit: o,\n      transitionEnterDuration: a,\n      transitionExitDuration: s,\n      ...d\n    }, c);\n  },\n  n = {\n    appear: !1,\n    enter: !0,\n    exit: !0,\n    transitionEnterDuration: 300,\n    transitionExitDuration: 300,\n    direction: \"vertical\"\n  };\nx.propTypes = {\n  children: t.oneOfType([t.arrayOf(t.node), t.node]),\n  childFactory: t.any,\n  className: t.string,\n  direction: t.oneOf([\"horizontal\", \"vertical\"]),\n  component: t.node,\n  id: t.string,\n  style: t.any\n};\nexport { x as Expand };", "map": {"version": 3, "names": ["l", "t", "Animation", "u", "x", "r", "appear", "i", "n", "enter", "e", "exit", "o", "transitionEnterDuration", "a", "transitionExitDuration", "s", "direction", "p", "children", "c", "d", "m", "transitionName", "createElement", "propTypes", "oneOfType", "arrayOf", "node", "childFactory", "any", "className", "string", "oneOf", "component", "id", "style", "Expand"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-animation/Expand.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as l from \"react\";\nimport t from \"prop-types\";\nimport { Animation as u } from \"./Animation.mjs\";\nconst x = (r) => {\n  const {\n    appear: i = n.appear,\n    enter: e = n.enter,\n    exit: o = n.exit,\n    transitionEnterDuration: a = n.transitionEnterDuration,\n    transitionExitDuration: s = n.transitionExitDuration,\n    direction: p = n.direction,\n    children: c,\n    ...d\n  } = r, m = {\n    transitionName: `expand-${p}`\n  };\n  return /* @__PURE__ */ l.createElement(\n    u,\n    {\n      ...m,\n      appear: i,\n      enter: e,\n      exit: o,\n      transitionEnterDuration: a,\n      transitionExitDuration: s,\n      ...d\n    },\n    c\n  );\n}, n = {\n  appear: !1,\n  enter: !0,\n  exit: !0,\n  transitionEnterDuration: 300,\n  transitionExitDuration: 300,\n  direction: \"vertical\"\n};\nx.propTypes = {\n  children: t.oneOfType([t.arrayOf(t.node), t.node]),\n  childFactory: t.any,\n  className: t.string,\n  direction: t.oneOf([\"horizontal\", \"vertical\"]),\n  component: t.node,\n  id: t.string,\n  style: t.any\n};\nexport {\n  x as Expand\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,SAAS,IAAIC,CAAC,QAAQ,iBAAiB;AAChD,MAAMC,CAAC,GAAIC,CAAC,IAAK;IACf,MAAM;QACJC,MAAM,EAAEC,CAAC,GAAGC,CAAC,CAACF,MAAM;QACpBG,KAAK,EAAEC,CAAC,GAAGF,CAAC,CAACC,KAAK;QAClBE,IAAI,EAAEC,CAAC,GAAGJ,CAAC,CAACG,IAAI;QAChBE,uBAAuB,EAAEC,CAAC,GAAGN,CAAC,CAACK,uBAAuB;QACtDE,sBAAsB,EAAEC,CAAC,GAAGR,CAAC,CAACO,sBAAsB;QACpDE,SAAS,EAAEC,CAAC,GAAGV,CAAC,CAACS,SAAS;QAC1BE,QAAQ,EAAEC,CAAC;QACX,GAAGC;MACL,CAAC,GAAGhB,CAAC;MAAEiB,CAAC,GAAG;QACTC,cAAc,EAAE,UAAUL,CAAC;MAC7B,CAAC;IACD,OAAO,eAAgBlB,CAAC,CAACwB,aAAa,CACpCrB,CAAC,EACD;MACE,GAAGmB,CAAC;MACJhB,MAAM,EAAEC,CAAC;MACTE,KAAK,EAAEC,CAAC;MACRC,IAAI,EAAEC,CAAC;MACPC,uBAAuB,EAAEC,CAAC;MAC1BC,sBAAsB,EAAEC,CAAC;MACzB,GAAGK;IACL,CAAC,EACDD,CACF,CAAC;EACH,CAAC;EAAEZ,CAAC,GAAG;IACLF,MAAM,EAAE,CAAC,CAAC;IACVG,KAAK,EAAE,CAAC,CAAC;IACTE,IAAI,EAAE,CAAC,CAAC;IACRE,uBAAuB,EAAE,GAAG;IAC5BE,sBAAsB,EAAE,GAAG;IAC3BE,SAAS,EAAE;EACb,CAAC;AACDb,CAAC,CAACqB,SAAS,GAAG;EACZN,QAAQ,EAAElB,CAAC,CAACyB,SAAS,CAAC,CAACzB,CAAC,CAAC0B,OAAO,CAAC1B,CAAC,CAAC2B,IAAI,CAAC,EAAE3B,CAAC,CAAC2B,IAAI,CAAC,CAAC;EAClDC,YAAY,EAAE5B,CAAC,CAAC6B,GAAG;EACnBC,SAAS,EAAE9B,CAAC,CAAC+B,MAAM;EACnBf,SAAS,EAAEhB,CAAC,CAACgC,KAAK,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;EAC9CC,SAAS,EAAEjC,CAAC,CAAC2B,IAAI;EACjBO,EAAE,EAAElC,CAAC,CAAC+B,MAAM;EACZI,KAAK,EAAEnC,CAAC,CAAC6B;AACX,CAAC;AACD,SACE1B,CAAC,IAAIiC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}