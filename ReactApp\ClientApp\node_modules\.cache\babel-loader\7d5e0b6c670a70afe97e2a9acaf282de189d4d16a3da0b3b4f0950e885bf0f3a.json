{"ast": null, "code": "import Group from './group';\nimport Size from '../geometry/size';\nimport Rect from '../geometry/rect';\nimport Point from '../geometry/point';\nimport createTransform from '../geometry/transform';\nimport translateToPoint from '../alignment/translate-to-point';\nimport alignStart from '../alignment/align-start';\nimport alignStartReverse from '../alignment/align-start-reverse';\nvar DEFAULT_OPTIONS = {\n  alignContent: \"start\",\n  justifyContent: \"start\",\n  alignItems: \"start\",\n  spacing: 0,\n  orientation: \"horizontal\",\n  lineSpacing: 0,\n  wrap: true,\n  revers: false\n};\nvar forEach = function (elements, callback) {\n  elements.forEach(callback);\n};\nvar forEachReverse = function (elements, callback) {\n  var length = elements.length;\n  for (var idx = length - 1; idx >= 0; idx--) {\n    callback(elements[idx], idx);\n  }\n};\nvar Layout = function (Group) {\n  function Layout(rect, options) {\n    Group.call(this, Object.assign({}, DEFAULT_OPTIONS, options));\n    this._rect = rect;\n    this._fieldMap = {};\n  }\n  if (Group) Layout.__proto__ = Group;\n  Layout.prototype = Object.create(Group && Group.prototype);\n  Layout.prototype.constructor = Layout;\n  Layout.prototype.rect = function rect(value) {\n    if (value) {\n      this._rect = value;\n      return this;\n    }\n    return this._rect;\n  };\n  Layout.prototype._initMap = function _initMap() {\n    var options = this.options;\n    var fieldMap = this._fieldMap;\n    if (options.orientation === \"horizontal\") {\n      fieldMap.sizeField = \"width\";\n      fieldMap.groupsSizeField = \"height\";\n      fieldMap.groupAxis = \"x\";\n      fieldMap.groupsAxis = \"y\";\n    } else {\n      fieldMap.sizeField = \"height\";\n      fieldMap.groupsSizeField = \"width\";\n      fieldMap.groupAxis = \"y\";\n      fieldMap.groupsAxis = \"x\";\n    }\n    if (options.reverse) {\n      this.forEach = forEachReverse;\n      this.justifyAlign = alignStartReverse;\n    } else {\n      this.forEach = forEach;\n      this.justifyAlign = alignStart;\n    }\n  };\n  Layout.prototype.reflow = function reflow() {\n    var this$1 = this;\n    if (!this._rect || this.children.length === 0) {\n      return;\n    }\n    this._initMap();\n    if (this.options.transform) {\n      this.transform(null);\n    }\n    var options = this.options;\n    var rect = this._rect;\n    var ref = this._initGroups();\n    var groups = ref.groups;\n    var groupsSize = ref.groupsSize;\n    var ref$1 = this._fieldMap;\n    var sizeField = ref$1.sizeField;\n    var groupsSizeField = ref$1.groupsSizeField;\n    var groupAxis = ref$1.groupAxis;\n    var groupsAxis = ref$1.groupsAxis;\n    var groupOrigin = new Point();\n    var elementOrigin = new Point();\n    var size = new Size();\n    var groupStart = alignStart(groupsSize, rect, options.alignContent, groupsAxis, groupsSizeField);\n    var elementStart, group, groupBox;\n    var arrangeElements = function (bbox, idx) {\n      var element = group.elements[idx];\n      elementOrigin[groupAxis] = elementStart;\n      elementOrigin[groupsAxis] = alignStart(bbox.size[groupsSizeField], groupBox, options.alignItems, groupsAxis, groupsSizeField);\n      translateToPoint(elementOrigin, bbox, element);\n      elementStart += bbox.size[sizeField] + options.spacing;\n    };\n    for (var groupIdx = 0; groupIdx < groups.length; groupIdx++) {\n      group = groups[groupIdx];\n      groupOrigin[groupAxis] = elementStart = this$1.justifyAlign(group.size, rect, options.justifyContent, groupAxis, sizeField);\n      groupOrigin[groupsAxis] = groupStart;\n      size[sizeField] = group.size;\n      size[groupsSizeField] = group.lineSize;\n      groupBox = new Rect(groupOrigin, size);\n      this$1.forEach(group.bboxes, arrangeElements);\n      groupStart += group.lineSize + options.lineSpacing;\n    }\n    if (!options.wrap && group.size > rect.size[sizeField]) {\n      var scale = rect.size[sizeField] / groupBox.size[sizeField];\n      var scaledStart = groupBox.topLeft().scale(scale, scale);\n      var scaledSize = groupBox.size[groupsSizeField] * scale;\n      var newStart = alignStart(scaledSize, rect, options.alignContent, groupsAxis, groupsSizeField);\n      var transform = createTransform();\n      if (groupAxis === \"x\") {\n        transform.translate(rect.origin.x - scaledStart.x, newStart - scaledStart.y);\n      } else {\n        transform.translate(newStart - scaledStart.x, rect.origin.y - scaledStart.y);\n      }\n      transform.scale(scale, scale);\n      this.transform(transform);\n    }\n  };\n  Layout.prototype._initGroups = function _initGroups() {\n    var this$1 = this;\n    var ref = this;\n    var options = ref.options;\n    var children = ref.children;\n    var lineSpacing = options.lineSpacing;\n    var wrap = options.wrap;\n    var spacing = options.spacing;\n    var sizeField = this._fieldMap.sizeField;\n    var group = this._newGroup();\n    var groups = [];\n    var addGroup = function () {\n      groups.push(group);\n      groupsSize += group.lineSize + lineSpacing;\n    };\n    var groupsSize = -lineSpacing;\n    for (var idx = 0; idx < children.length; idx++) {\n      var element = children[idx];\n      var bbox = children[idx].clippedBBox();\n      if (element.visible() && bbox) {\n        if (wrap && group.size + bbox.size[sizeField] + spacing > this$1._rect.size[sizeField]) {\n          if (group.bboxes.length === 0) {\n            this$1._addToGroup(group, bbox, element);\n            addGroup();\n            group = this$1._newGroup();\n          } else {\n            addGroup();\n            group = this$1._newGroup();\n            this$1._addToGroup(group, bbox, element);\n          }\n        } else {\n          this$1._addToGroup(group, bbox, element);\n        }\n      }\n    }\n    if (group.bboxes.length) {\n      addGroup();\n    }\n    return {\n      groups: groups,\n      groupsSize: groupsSize\n    };\n  };\n  Layout.prototype._addToGroup = function _addToGroup(group, bbox, element) {\n    group.size += bbox.size[this._fieldMap.sizeField] + this.options.spacing;\n    group.lineSize = Math.max(bbox.size[this._fieldMap.groupsSizeField], group.lineSize);\n    group.bboxes.push(bbox);\n    group.elements.push(element);\n  };\n  Layout.prototype._newGroup = function _newGroup() {\n    return {\n      lineSize: 0,\n      size: -this.options.spacing,\n      bboxes: [],\n      elements: []\n    };\n  };\n  return Layout;\n}(Group);\nexport default Layout;", "map": {"version": 3, "names": ["Group", "Size", "Rect", "Point", "createTransform", "translateToPoint", "alignStart", "alignStartReverse", "DEFAULT_OPTIONS", "align<PERSON><PERSON><PERSON>", "justifyContent", "alignItems", "spacing", "orientation", "lineSpacing", "wrap", "revers", "for<PERSON>ach", "elements", "callback", "forEachReverse", "length", "idx", "Layout", "rect", "options", "call", "Object", "assign", "_rect", "_fieldMap", "__proto__", "prototype", "create", "constructor", "value", "_initMap", "fieldMap", "sizeField", "groupsSizeField", "groupAxis", "groupsAxis", "reverse", "justifyAlign", "reflow", "this$1", "children", "transform", "ref", "_initGroups", "groups", "groupsSize", "ref$1", "groupOrigin", "elementOrigin", "size", "groupStart", "elementStart", "group", "groupBox", "arrangeElements", "bbox", "element", "groupIdx", "lineSize", "bboxes", "scale", "scaledStart", "topLeft", "scaledSize", "newStart", "translate", "origin", "x", "y", "_newGroup", "addGroup", "push", "clippedBBox", "visible", "_addToGroup", "Math", "max"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/shapes/layout.js"], "sourcesContent": ["import Group from './group';\nimport Size from '../geometry/size';\nimport Rect from '../geometry/rect';\nimport Point from '../geometry/point';\nimport createTransform from '../geometry/transform';\nimport translateToPoint from '../alignment/translate-to-point';\nimport alignStart from '../alignment/align-start';\nimport alignStartReverse from '../alignment/align-start-reverse';\n\nvar DEFAULT_OPTIONS = {\n    alignContent: \"start\",\n    justifyContent: \"start\",\n    alignItems: \"start\",\n    spacing: 0,\n    orientation: \"horizontal\",\n    lineSpacing: 0,\n    wrap: true,\n    revers: false\n};\n\nvar forEach = function (elements, callback) {\n    elements.forEach(callback);\n};\n\nvar forEachReverse = function (elements, callback) {\n    var length = elements.length;\n\n    for (var idx = length - 1; idx >= 0; idx--) {\n        callback(elements[idx], idx);\n    }\n};\n\nvar Layout = (function (Group) {\n    function Layout(rect, options) {\n        Group.call(this, Object.assign({}, DEFAULT_OPTIONS, options));\n        this._rect = rect;\n        this._fieldMap = {};\n    }\n\n    if ( Group ) Layout.__proto__ = Group;\n    Layout.prototype = Object.create( Group && Group.prototype );\n    Layout.prototype.constructor = Layout;\n\n    Layout.prototype.rect = function rect (value) {\n        if (value) {\n            this._rect = value;\n            return this;\n        }\n\n        return this._rect;\n    };\n\n    Layout.prototype._initMap = function _initMap () {\n        var options = this.options;\n        var fieldMap = this._fieldMap;\n        if (options.orientation === \"horizontal\") {\n            fieldMap.sizeField = \"width\";\n            fieldMap.groupsSizeField = \"height\";\n            fieldMap.groupAxis = \"x\";\n            fieldMap.groupsAxis = \"y\";\n        } else {\n            fieldMap.sizeField = \"height\";\n            fieldMap.groupsSizeField = \"width\";\n            fieldMap.groupAxis = \"y\";\n            fieldMap.groupsAxis = \"x\";\n        }\n\n        if (options.reverse) {\n            this.forEach = forEachReverse;\n            this.justifyAlign = alignStartReverse;\n        } else {\n            this.forEach = forEach;\n            this.justifyAlign = alignStart;\n        }\n    };\n\n    Layout.prototype.reflow = function reflow () {\n        var this$1 = this;\n\n        if (!this._rect || this.children.length === 0) {\n            return;\n        }\n        this._initMap();\n\n        if (this.options.transform) {\n            this.transform(null);\n        }\n\n        var options = this.options;\n        var rect = this._rect;\n        var ref = this._initGroups();\n        var groups = ref.groups;\n        var groupsSize = ref.groupsSize;\n        var ref$1 = this._fieldMap;\n        var sizeField = ref$1.sizeField;\n        var groupsSizeField = ref$1.groupsSizeField;\n        var groupAxis = ref$1.groupAxis;\n        var groupsAxis = ref$1.groupsAxis;\n        var groupOrigin = new Point();\n        var elementOrigin = new Point();\n        var size = new Size();\n        var groupStart = alignStart(groupsSize, rect, options.alignContent, groupsAxis, groupsSizeField);\n        var elementStart, group, groupBox;\n\n        var arrangeElements = function (bbox, idx) {\n            var element = group.elements[idx];\n\n            elementOrigin[groupAxis] = elementStart;\n            elementOrigin[groupsAxis] = alignStart(bbox.size[groupsSizeField], groupBox, options.alignItems, groupsAxis, groupsSizeField);\n            translateToPoint(elementOrigin, bbox, element);\n            elementStart += bbox.size[sizeField] + options.spacing;\n        };\n\n        for (var groupIdx = 0; groupIdx < groups.length; groupIdx++) {\n            group = groups[groupIdx];\n            groupOrigin[groupAxis] = elementStart = this$1.justifyAlign(group.size, rect, options.justifyContent, groupAxis, sizeField);\n            groupOrigin[groupsAxis] = groupStart;\n            size[sizeField] = group.size;\n            size[groupsSizeField] = group.lineSize;\n            groupBox = new Rect(groupOrigin, size);\n            this$1.forEach(group.bboxes, arrangeElements);\n\n            groupStart += group.lineSize + options.lineSpacing;\n        }\n\n        if (!options.wrap && group.size > rect.size[sizeField]) {\n            var scale = rect.size[sizeField] / groupBox.size[sizeField];\n            var scaledStart = groupBox.topLeft().scale(scale, scale);\n            var scaledSize = groupBox.size[groupsSizeField] * scale;\n            var newStart = alignStart(scaledSize, rect, options.alignContent, groupsAxis, groupsSizeField);\n            var transform = createTransform();\n            if (groupAxis === \"x\") {\n                transform.translate(rect.origin.x - scaledStart.x, newStart - scaledStart.y);\n            } else {\n                transform.translate(newStart - scaledStart.x, rect.origin.y - scaledStart.y);\n            }\n            transform.scale(scale, scale);\n\n            this.transform(transform);\n        }\n    };\n\n    Layout.prototype._initGroups = function _initGroups () {\n        var this$1 = this;\n\n        var ref = this;\n        var options = ref.options;\n        var children = ref.children;\n        var lineSpacing = options.lineSpacing;\n        var wrap = options.wrap;\n        var spacing = options.spacing;\n        var sizeField = this._fieldMap.sizeField;\n        var group = this._newGroup();\n        var groups = [];\n        var addGroup = function() {\n            groups.push(group);\n            groupsSize += group.lineSize + lineSpacing;\n        };\n        var groupsSize = -lineSpacing;\n\n        for (var idx = 0; idx < children.length; idx++) {\n            var element = children[idx];\n            var bbox = children[idx].clippedBBox();\n            if (element.visible() && bbox) {\n                if (wrap && group.size + bbox.size[sizeField] + spacing > this$1._rect.size[sizeField]) {\n                    if (group.bboxes.length === 0) {\n                        this$1._addToGroup(group, bbox, element);\n                        addGroup();\n                        group = this$1._newGroup();\n                    } else {\n                        addGroup();\n                        group = this$1._newGroup();\n                        this$1._addToGroup(group, bbox, element);\n                    }\n                } else {\n                    this$1._addToGroup(group, bbox, element);\n                }\n            }\n        }\n\n        if (group.bboxes.length) {\n            addGroup();\n        }\n\n        return {\n            groups: groups,\n            groupsSize: groupsSize\n        };\n    };\n\n    Layout.prototype._addToGroup = function _addToGroup (group, bbox, element) {\n        group.size += bbox.size[this._fieldMap.sizeField] + this.options.spacing;\n        group.lineSize = Math.max(bbox.size[this._fieldMap.groupsSizeField], group.lineSize);\n        group.bboxes.push(bbox);\n        group.elements.push(element);\n    };\n\n    Layout.prototype._newGroup = function _newGroup () {\n        return {\n            lineSize: 0,\n            size: -this.options.spacing,\n            bboxes: [],\n            elements: []\n        };\n    };\n\n    return Layout;\n}(Group));\n\nexport default Layout;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,SAAS;AAC3B,OAAOC,IAAI,MAAM,kBAAkB;AACnC,OAAOC,IAAI,MAAM,kBAAkB;AACnC,OAAOC,KAAK,MAAM,mBAAmB;AACrC,OAAOC,eAAe,MAAM,uBAAuB;AACnD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,iBAAiB,MAAM,kCAAkC;AAEhE,IAAIC,eAAe,GAAG;EAClBC,YAAY,EAAE,OAAO;EACrBC,cAAc,EAAE,OAAO;EACvBC,UAAU,EAAE,OAAO;EACnBC,OAAO,EAAE,CAAC;EACVC,WAAW,EAAE,YAAY;EACzBC,WAAW,EAAE,CAAC;EACdC,IAAI,EAAE,IAAI;EACVC,MAAM,EAAE;AACZ,CAAC;AAED,IAAIC,OAAO,GAAG,SAAAA,CAAUC,QAAQ,EAAEC,QAAQ,EAAE;EACxCD,QAAQ,CAACD,OAAO,CAACE,QAAQ,CAAC;AAC9B,CAAC;AAED,IAAIC,cAAc,GAAG,SAAAA,CAAUF,QAAQ,EAAEC,QAAQ,EAAE;EAC/C,IAAIE,MAAM,GAAGH,QAAQ,CAACG,MAAM;EAE5B,KAAK,IAAIC,GAAG,GAAGD,MAAM,GAAG,CAAC,EAAEC,GAAG,IAAI,CAAC,EAAEA,GAAG,EAAE,EAAE;IACxCH,QAAQ,CAACD,QAAQ,CAACI,GAAG,CAAC,EAAEA,GAAG,CAAC;EAChC;AACJ,CAAC;AAED,IAAIC,MAAM,GAAI,UAAUvB,KAAK,EAAE;EAC3B,SAASuB,MAAMA,CAACC,IAAI,EAAEC,OAAO,EAAE;IAC3BzB,KAAK,CAAC0B,IAAI,CAAC,IAAI,EAAEC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEpB,eAAe,EAAEiB,OAAO,CAAC,CAAC;IAC7D,IAAI,CAACI,KAAK,GAAGL,IAAI;IACjB,IAAI,CAACM,SAAS,GAAG,CAAC,CAAC;EACvB;EAEA,IAAK9B,KAAK,EAAGuB,MAAM,CAACQ,SAAS,GAAG/B,KAAK;EACrCuB,MAAM,CAACS,SAAS,GAAGL,MAAM,CAACM,MAAM,CAAEjC,KAAK,IAAIA,KAAK,CAACgC,SAAU,CAAC;EAC5DT,MAAM,CAACS,SAAS,CAACE,WAAW,GAAGX,MAAM;EAErCA,MAAM,CAACS,SAAS,CAACR,IAAI,GAAG,SAASA,IAAIA,CAAEW,KAAK,EAAE;IAC1C,IAAIA,KAAK,EAAE;MACP,IAAI,CAACN,KAAK,GAAGM,KAAK;MAClB,OAAO,IAAI;IACf;IAEA,OAAO,IAAI,CAACN,KAAK;EACrB,CAAC;EAEDN,MAAM,CAACS,SAAS,CAACI,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;IAC7C,IAAIX,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,IAAIY,QAAQ,GAAG,IAAI,CAACP,SAAS;IAC7B,IAAIL,OAAO,CAACZ,WAAW,KAAK,YAAY,EAAE;MACtCwB,QAAQ,CAACC,SAAS,GAAG,OAAO;MAC5BD,QAAQ,CAACE,eAAe,GAAG,QAAQ;MACnCF,QAAQ,CAACG,SAAS,GAAG,GAAG;MACxBH,QAAQ,CAACI,UAAU,GAAG,GAAG;IAC7B,CAAC,MAAM;MACHJ,QAAQ,CAACC,SAAS,GAAG,QAAQ;MAC7BD,QAAQ,CAACE,eAAe,GAAG,OAAO;MAClCF,QAAQ,CAACG,SAAS,GAAG,GAAG;MACxBH,QAAQ,CAACI,UAAU,GAAG,GAAG;IAC7B;IAEA,IAAIhB,OAAO,CAACiB,OAAO,EAAE;MACjB,IAAI,CAACzB,OAAO,GAAGG,cAAc;MAC7B,IAAI,CAACuB,YAAY,GAAGpC,iBAAiB;IACzC,CAAC,MAAM;MACH,IAAI,CAACU,OAAO,GAAGA,OAAO;MACtB,IAAI,CAAC0B,YAAY,GAAGrC,UAAU;IAClC;EACJ,CAAC;EAEDiB,MAAM,CAACS,SAAS,CAACY,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAI;IACzC,IAAIC,MAAM,GAAG,IAAI;IAEjB,IAAI,CAAC,IAAI,CAAChB,KAAK,IAAI,IAAI,CAACiB,QAAQ,CAACzB,MAAM,KAAK,CAAC,EAAE;MAC3C;IACJ;IACA,IAAI,CAACe,QAAQ,CAAC,CAAC;IAEf,IAAI,IAAI,CAACX,OAAO,CAACsB,SAAS,EAAE;MACxB,IAAI,CAACA,SAAS,CAAC,IAAI,CAAC;IACxB;IAEA,IAAItB,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,IAAID,IAAI,GAAG,IAAI,CAACK,KAAK;IACrB,IAAImB,GAAG,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;IAC5B,IAAIC,MAAM,GAAGF,GAAG,CAACE,MAAM;IACvB,IAAIC,UAAU,GAAGH,GAAG,CAACG,UAAU;IAC/B,IAAIC,KAAK,GAAG,IAAI,CAACtB,SAAS;IAC1B,IAAIQ,SAAS,GAAGc,KAAK,CAACd,SAAS;IAC/B,IAAIC,eAAe,GAAGa,KAAK,CAACb,eAAe;IAC3C,IAAIC,SAAS,GAAGY,KAAK,CAACZ,SAAS;IAC/B,IAAIC,UAAU,GAAGW,KAAK,CAACX,UAAU;IACjC,IAAIY,WAAW,GAAG,IAAIlD,KAAK,CAAC,CAAC;IAC7B,IAAImD,aAAa,GAAG,IAAInD,KAAK,CAAC,CAAC;IAC/B,IAAIoD,IAAI,GAAG,IAAItD,IAAI,CAAC,CAAC;IACrB,IAAIuD,UAAU,GAAGlD,UAAU,CAAC6C,UAAU,EAAE3B,IAAI,EAAEC,OAAO,CAAChB,YAAY,EAAEgC,UAAU,EAAEF,eAAe,CAAC;IAChG,IAAIkB,YAAY,EAAEC,KAAK,EAAEC,QAAQ;IAEjC,IAAIC,eAAe,GAAG,SAAAA,CAAUC,IAAI,EAAEvC,GAAG,EAAE;MACvC,IAAIwC,OAAO,GAAGJ,KAAK,CAACxC,QAAQ,CAACI,GAAG,CAAC;MAEjCgC,aAAa,CAACd,SAAS,CAAC,GAAGiB,YAAY;MACvCH,aAAa,CAACb,UAAU,CAAC,GAAGnC,UAAU,CAACuD,IAAI,CAACN,IAAI,CAAChB,eAAe,CAAC,EAAEoB,QAAQ,EAAElC,OAAO,CAACd,UAAU,EAAE8B,UAAU,EAAEF,eAAe,CAAC;MAC7HlC,gBAAgB,CAACiD,aAAa,EAAEO,IAAI,EAAEC,OAAO,CAAC;MAC9CL,YAAY,IAAII,IAAI,CAACN,IAAI,CAACjB,SAAS,CAAC,GAAGb,OAAO,CAACb,OAAO;IAC1D,CAAC;IAED,KAAK,IAAImD,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAGb,MAAM,CAAC7B,MAAM,EAAE0C,QAAQ,EAAE,EAAE;MACzDL,KAAK,GAAGR,MAAM,CAACa,QAAQ,CAAC;MACxBV,WAAW,CAACb,SAAS,CAAC,GAAGiB,YAAY,GAAGZ,MAAM,CAACF,YAAY,CAACe,KAAK,CAACH,IAAI,EAAE/B,IAAI,EAAEC,OAAO,CAACf,cAAc,EAAE8B,SAAS,EAAEF,SAAS,CAAC;MAC3He,WAAW,CAACZ,UAAU,CAAC,GAAGe,UAAU;MACpCD,IAAI,CAACjB,SAAS,CAAC,GAAGoB,KAAK,CAACH,IAAI;MAC5BA,IAAI,CAAChB,eAAe,CAAC,GAAGmB,KAAK,CAACM,QAAQ;MACtCL,QAAQ,GAAG,IAAIzD,IAAI,CAACmD,WAAW,EAAEE,IAAI,CAAC;MACtCV,MAAM,CAAC5B,OAAO,CAACyC,KAAK,CAACO,MAAM,EAAEL,eAAe,CAAC;MAE7CJ,UAAU,IAAIE,KAAK,CAACM,QAAQ,GAAGvC,OAAO,CAACX,WAAW;IACtD;IAEA,IAAI,CAACW,OAAO,CAACV,IAAI,IAAI2C,KAAK,CAACH,IAAI,GAAG/B,IAAI,CAAC+B,IAAI,CAACjB,SAAS,CAAC,EAAE;MACpD,IAAI4B,KAAK,GAAG1C,IAAI,CAAC+B,IAAI,CAACjB,SAAS,CAAC,GAAGqB,QAAQ,CAACJ,IAAI,CAACjB,SAAS,CAAC;MAC3D,IAAI6B,WAAW,GAAGR,QAAQ,CAACS,OAAO,CAAC,CAAC,CAACF,KAAK,CAACA,KAAK,EAAEA,KAAK,CAAC;MACxD,IAAIG,UAAU,GAAGV,QAAQ,CAACJ,IAAI,CAAChB,eAAe,CAAC,GAAG2B,KAAK;MACvD,IAAII,QAAQ,GAAGhE,UAAU,CAAC+D,UAAU,EAAE7C,IAAI,EAAEC,OAAO,CAAChB,YAAY,EAAEgC,UAAU,EAAEF,eAAe,CAAC;MAC9F,IAAIQ,SAAS,GAAG3C,eAAe,CAAC,CAAC;MACjC,IAAIoC,SAAS,KAAK,GAAG,EAAE;QACnBO,SAAS,CAACwB,SAAS,CAAC/C,IAAI,CAACgD,MAAM,CAACC,CAAC,GAAGN,WAAW,CAACM,CAAC,EAAEH,QAAQ,GAAGH,WAAW,CAACO,CAAC,CAAC;MAChF,CAAC,MAAM;QACH3B,SAAS,CAACwB,SAAS,CAACD,QAAQ,GAAGH,WAAW,CAACM,CAAC,EAAEjD,IAAI,CAACgD,MAAM,CAACE,CAAC,GAAGP,WAAW,CAACO,CAAC,CAAC;MAChF;MACA3B,SAAS,CAACmB,KAAK,CAACA,KAAK,EAAEA,KAAK,CAAC;MAE7B,IAAI,CAACnB,SAAS,CAACA,SAAS,CAAC;IAC7B;EACJ,CAAC;EAEDxB,MAAM,CAACS,SAAS,CAACiB,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAI;IACnD,IAAIJ,MAAM,GAAG,IAAI;IAEjB,IAAIG,GAAG,GAAG,IAAI;IACd,IAAIvB,OAAO,GAAGuB,GAAG,CAACvB,OAAO;IACzB,IAAIqB,QAAQ,GAAGE,GAAG,CAACF,QAAQ;IAC3B,IAAIhC,WAAW,GAAGW,OAAO,CAACX,WAAW;IACrC,IAAIC,IAAI,GAAGU,OAAO,CAACV,IAAI;IACvB,IAAIH,OAAO,GAAGa,OAAO,CAACb,OAAO;IAC7B,IAAI0B,SAAS,GAAG,IAAI,CAACR,SAAS,CAACQ,SAAS;IACxC,IAAIoB,KAAK,GAAG,IAAI,CAACiB,SAAS,CAAC,CAAC;IAC5B,IAAIzB,MAAM,GAAG,EAAE;IACf,IAAI0B,QAAQ,GAAG,SAAAA,CAAA,EAAW;MACtB1B,MAAM,CAAC2B,IAAI,CAACnB,KAAK,CAAC;MAClBP,UAAU,IAAIO,KAAK,CAACM,QAAQ,GAAGlD,WAAW;IAC9C,CAAC;IACD,IAAIqC,UAAU,GAAG,CAACrC,WAAW;IAE7B,KAAK,IAAIQ,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGwB,QAAQ,CAACzB,MAAM,EAAEC,GAAG,EAAE,EAAE;MAC5C,IAAIwC,OAAO,GAAGhB,QAAQ,CAACxB,GAAG,CAAC;MAC3B,IAAIuC,IAAI,GAAGf,QAAQ,CAACxB,GAAG,CAAC,CAACwD,WAAW,CAAC,CAAC;MACtC,IAAIhB,OAAO,CAACiB,OAAO,CAAC,CAAC,IAAIlB,IAAI,EAAE;QAC3B,IAAI9C,IAAI,IAAI2C,KAAK,CAACH,IAAI,GAAGM,IAAI,CAACN,IAAI,CAACjB,SAAS,CAAC,GAAG1B,OAAO,GAAGiC,MAAM,CAAChB,KAAK,CAAC0B,IAAI,CAACjB,SAAS,CAAC,EAAE;UACpF,IAAIoB,KAAK,CAACO,MAAM,CAAC5C,MAAM,KAAK,CAAC,EAAE;YAC3BwB,MAAM,CAACmC,WAAW,CAACtB,KAAK,EAAEG,IAAI,EAAEC,OAAO,CAAC;YACxCc,QAAQ,CAAC,CAAC;YACVlB,KAAK,GAAGb,MAAM,CAAC8B,SAAS,CAAC,CAAC;UAC9B,CAAC,MAAM;YACHC,QAAQ,CAAC,CAAC;YACVlB,KAAK,GAAGb,MAAM,CAAC8B,SAAS,CAAC,CAAC;YAC1B9B,MAAM,CAACmC,WAAW,CAACtB,KAAK,EAAEG,IAAI,EAAEC,OAAO,CAAC;UAC5C;QACJ,CAAC,MAAM;UACHjB,MAAM,CAACmC,WAAW,CAACtB,KAAK,EAAEG,IAAI,EAAEC,OAAO,CAAC;QAC5C;MACJ;IACJ;IAEA,IAAIJ,KAAK,CAACO,MAAM,CAAC5C,MAAM,EAAE;MACrBuD,QAAQ,CAAC,CAAC;IACd;IAEA,OAAO;MACH1B,MAAM,EAAEA,MAAM;MACdC,UAAU,EAAEA;IAChB,CAAC;EACL,CAAC;EAED5B,MAAM,CAACS,SAAS,CAACgD,WAAW,GAAG,SAASA,WAAWA,CAAEtB,KAAK,EAAEG,IAAI,EAAEC,OAAO,EAAE;IACvEJ,KAAK,CAACH,IAAI,IAAIM,IAAI,CAACN,IAAI,CAAC,IAAI,CAACzB,SAAS,CAACQ,SAAS,CAAC,GAAG,IAAI,CAACb,OAAO,CAACb,OAAO;IACxE8C,KAAK,CAACM,QAAQ,GAAGiB,IAAI,CAACC,GAAG,CAACrB,IAAI,CAACN,IAAI,CAAC,IAAI,CAACzB,SAAS,CAACS,eAAe,CAAC,EAAEmB,KAAK,CAACM,QAAQ,CAAC;IACpFN,KAAK,CAACO,MAAM,CAACY,IAAI,CAAChB,IAAI,CAAC;IACvBH,KAAK,CAACxC,QAAQ,CAAC2D,IAAI,CAACf,OAAO,CAAC;EAChC,CAAC;EAEDvC,MAAM,CAACS,SAAS,CAAC2C,SAAS,GAAG,SAASA,SAASA,CAAA,EAAI;IAC/C,OAAO;MACHX,QAAQ,EAAE,CAAC;MACXT,IAAI,EAAE,CAAC,IAAI,CAAC9B,OAAO,CAACb,OAAO;MAC3BqD,MAAM,EAAE,EAAE;MACV/C,QAAQ,EAAE;IACd,CAAC;EACL,CAAC;EAED,OAAOK,MAAM;AACjB,CAAC,CAACvB,KAAK,CAAE;AAET,eAAeuB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}