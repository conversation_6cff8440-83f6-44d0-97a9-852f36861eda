{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { cloneDate as g, addDays as f } from \"@progress/kendo-date-math\";\nimport { TIME_PART as l } from \"./models/TimePart.mjs\";\nimport { setTime as u, MIDNIGHT_DATE as c } from \"../utils.mjs\";\nconst a = e => (t, r) => {\n    const n = g(t);\n    return n[e](r), n;\n  },\n  p = [{\n    type: l.hour,\n    getter: e => e.getHours()\n  }, {\n    type: l.minute,\n    getter: e => e.getMinutes()\n  }, {\n    type: l.second,\n    getter: e => e.getSeconds()\n  }, {\n    type: l.millisecond,\n    getter: e => e.getMilliseconds()\n  }],\n  V = e => (t, r) => e(t),\n  T = e => (t, r) => e(r),\n  M = e => e.reduce((t, r) => (t[r.type] = r.type, t), {}),\n  y = e => t => e[t.type] ? T(t.getter) : V(t.getter),\n  H = e => t => e.map(y(M(t))),\n  d = (e, t, r, n) => s => {\n    const o = e(s),\n      m = e(r);\n    return n === \"hour\" ? o - (o - m) % t : s.getTime() <= r.getTime() && o !== 0 && o <= m ? Math.ceil(o / t) * t : o - o % t;\n  },\n  h = e => (t, r) => e.map(n => {\n    const s = Math.floor(t[n.type]);\n    return s ? d(n.getter, s, r, n.type) : n.getter;\n  }),\n  S = H(p),\n  R = h(p),\n  _ = e => (t, r) => (t.setHours(...e.map(n => n(t, r))), t),\n  x = e => t => {\n    const r = g(t);\n    return r.setHours(...e.map(n => n(r))), r;\n  },\n  G = a(\"setHours\"),\n  w = a(\"setMinutes\"),\n  A = a(\"setSeconds\"),\n  B = () => /* @__PURE__ */new Date(),\n  E = (e, t, r = 1) => {\n    const n = [];\n    for (let s = e; s < t; s = s + r) n.push(s);\n    return n;\n  },\n  i = (e, t, r) => ({\n    candidateValue: u(c, e),\n    maxValue: f(u(c, r), t.getHours() < r.getHours() ? 0 : 1),\n    minValue: u(c, t)\n  }),\n  F = (e, t, r) => {\n    if (!e || !t || !r) return e;\n    const {\n      candidateValue: n,\n      minValue: s,\n      maxValue: o\n    } = i(e, t, r);\n    return n < s ? u(e, t) : n > o ? u(e, r) : e;\n  },\n  N = (e, t, r) => {\n    if (!e || !t || !r) return !0;\n    const {\n      candidateValue: n,\n      minValue: s,\n      maxValue: o\n    } = i(e, t, r);\n    return s <= n && n <= o;\n  },\n  P = (e, t, r) => {\n    if (e === null) return !0;\n    const {\n      candidateValue: n,\n      minValue: s,\n      maxValue: o\n    } = i(e, t, r);\n    return s <= n && n <= o;\n  },\n  v = (e, t) => {\n    if (e === null || t === null) return !1;\n    const r = u(c, e),\n      n = u(c, t);\n    return r.getTime() < n.getHours();\n  },\n  O = (e, t) => {\n    if (e === null || t === null) return !1;\n    const r = u(c, e);\n    return u(c, t).getTime() < r.getHours();\n  };\nexport { S as generateGetters, R as generateSnappers, B as getNow, O as isBiggerThanMax, P as isInRange, N as isInTimeRange, v as isSmallerThanMin, E as range, G as setHours, w as setMinutes, A as setSeconds, x as snapTime, F as timeInRange, _ as valueMerger };", "map": {"version": 3, "names": ["cloneDate", "g", "addDays", "f", "TIME_PART", "l", "setTime", "u", "MIDNIGHT_DATE", "c", "a", "e", "t", "r", "n", "p", "type", "hour", "getter", "getHours", "minute", "getMinutes", "second", "getSeconds", "millisecond", "getMilliseconds", "V", "T", "M", "reduce", "y", "H", "map", "d", "s", "o", "m", "getTime", "Math", "ceil", "h", "floor", "S", "R", "_", "setHours", "x", "G", "w", "A", "B", "Date", "E", "push", "i", "<PERSON><PERSON><PERSON><PERSON>", "maxValue", "minValue", "F", "N", "P", "v", "O", "generateGetters", "generateSnappers", "getNow", "isBiggerThanMax", "isInRange", "isInTimeRange", "isSmallerThanMin", "range", "setMinutes", "setSeconds", "snapTime", "timeInRange", "valueMerger"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dateinputs/timepicker/utils.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { cloneDate as g, addDays as f } from \"@progress/kendo-date-math\";\nimport { TIME_PART as l } from \"./models/TimePart.mjs\";\nimport { setTime as u, MIDNIGHT_DATE as c } from \"../utils.mjs\";\nconst a = (e) => (t, r) => {\n  const n = g(t);\n  return n[e](r), n;\n}, p = [\n  { type: l.hour, getter: (e) => e.getHours() },\n  { type: l.minute, getter: (e) => e.getMinutes() },\n  { type: l.second, getter: (e) => e.getSeconds() },\n  { type: l.millisecond, getter: (e) => e.getMilliseconds() }\n], V = (e) => (t, r) => e(t), T = (e) => (t, r) => e(r), M = (e) => e.reduce((t, r) => (t[r.type] = r.type, t), {}), y = (e) => (t) => e[t.type] ? T(t.getter) : V(t.getter), H = (e) => (t) => e.map(y(M(t))), d = (e, t, r, n) => (s) => {\n  const o = e(s), m = e(r);\n  return n === \"hour\" ? o - (o - m) % t : s.getTime() <= r.getTime() && o !== 0 && o <= m ? Math.ceil(o / t) * t : o - o % t;\n}, h = (e) => (t, r) => e.map((n) => {\n  const s = Math.floor(t[n.type]);\n  return s ? d(n.getter, s, r, n.type) : n.getter;\n}), S = H(p), R = h(p), _ = (e) => (t, r) => (t.setHours(...e.map((n) => n(t, r))), t), x = (e) => (t) => {\n  const r = g(t);\n  return r.setHours(...e.map((n) => n(r))), r;\n}, G = a(\"setHours\"), w = a(\"setMinutes\"), A = a(\"setSeconds\"), B = () => /* @__PURE__ */ new Date(), E = (e, t, r = 1) => {\n  const n = [];\n  for (let s = e; s < t; s = s + r)\n    n.push(s);\n  return n;\n}, i = (e, t, r) => ({\n  candidateValue: u(c, e),\n  maxValue: f(u(c, r), t.getHours() < r.getHours() ? 0 : 1),\n  minValue: u(c, t)\n}), F = (e, t, r) => {\n  if (!e || !t || !r)\n    return e;\n  const { candidateValue: n, minValue: s, maxValue: o } = i(e, t, r);\n  return n < s ? u(e, t) : n > o ? u(e, r) : e;\n}, N = (e, t, r) => {\n  if (!e || !t || !r)\n    return !0;\n  const { candidateValue: n, minValue: s, maxValue: o } = i(e, t, r);\n  return s <= n && n <= o;\n}, P = (e, t, r) => {\n  if (e === null)\n    return !0;\n  const { candidateValue: n, minValue: s, maxValue: o } = i(e, t, r);\n  return s <= n && n <= o;\n}, v = (e, t) => {\n  if (e === null || t === null)\n    return !1;\n  const r = u(c, e), n = u(c, t);\n  return r.getTime() < n.getHours();\n}, O = (e, t) => {\n  if (e === null || t === null)\n    return !1;\n  const r = u(c, e);\n  return u(c, t).getTime() < r.getHours();\n};\nexport {\n  S as generateGetters,\n  R as generateSnappers,\n  B as getNow,\n  O as isBiggerThanMax,\n  P as isInRange,\n  N as isInTimeRange,\n  v as isSmallerThanMin,\n  E as range,\n  G as setHours,\n  w as setMinutes,\n  A as setSeconds,\n  x as snapTime,\n  F as timeInRange,\n  _ as valueMerger\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,IAAIC,CAAC,EAAEC,OAAO,IAAIC,CAAC,QAAQ,2BAA2B;AACxE,SAASC,SAAS,IAAIC,CAAC,QAAQ,uBAAuB;AACtD,SAASC,OAAO,IAAIC,CAAC,EAAEC,aAAa,IAAIC,CAAC,QAAQ,cAAc;AAC/D,MAAMC,CAAC,GAAIC,CAAC,IAAK,CAACC,CAAC,EAAEC,CAAC,KAAK;IACzB,MAAMC,CAAC,GAAGb,CAAC,CAACW,CAAC,CAAC;IACd,OAAOE,CAAC,CAACH,CAAC,CAAC,CAACE,CAAC,CAAC,EAAEC,CAAC;EACnB,CAAC;EAAEC,CAAC,GAAG,CACL;IAAEC,IAAI,EAAEX,CAAC,CAACY,IAAI;IAAEC,MAAM,EAAGP,CAAC,IAAKA,CAAC,CAACQ,QAAQ,CAAC;EAAE,CAAC,EAC7C;IAAEH,IAAI,EAAEX,CAAC,CAACe,MAAM;IAAEF,MAAM,EAAGP,CAAC,IAAKA,CAAC,CAACU,UAAU,CAAC;EAAE,CAAC,EACjD;IAAEL,IAAI,EAAEX,CAAC,CAACiB,MAAM;IAAEJ,MAAM,EAAGP,CAAC,IAAKA,CAAC,CAACY,UAAU,CAAC;EAAE,CAAC,EACjD;IAAEP,IAAI,EAAEX,CAAC,CAACmB,WAAW;IAAEN,MAAM,EAAGP,CAAC,IAAKA,CAAC,CAACc,eAAe,CAAC;EAAE,CAAC,CAC5D;EAAEC,CAAC,GAAIf,CAAC,IAAK,CAACC,CAAC,EAAEC,CAAC,KAAKF,CAAC,CAACC,CAAC,CAAC;EAAEe,CAAC,GAAIhB,CAAC,IAAK,CAACC,CAAC,EAAEC,CAAC,KAAKF,CAAC,CAACE,CAAC,CAAC;EAAEe,CAAC,GAAIjB,CAAC,IAAKA,CAAC,CAACkB,MAAM,CAAC,CAACjB,CAAC,EAAEC,CAAC,MAAMD,CAAC,CAACC,CAAC,CAACG,IAAI,CAAC,GAAGH,CAAC,CAACG,IAAI,EAAEJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAEkB,CAAC,GAAInB,CAAC,IAAMC,CAAC,IAAKD,CAAC,CAACC,CAAC,CAACI,IAAI,CAAC,GAAGW,CAAC,CAACf,CAAC,CAACM,MAAM,CAAC,GAAGQ,CAAC,CAACd,CAAC,CAACM,MAAM,CAAC;EAAEa,CAAC,GAAIpB,CAAC,IAAMC,CAAC,IAAKD,CAAC,CAACqB,GAAG,CAACF,CAAC,CAACF,CAAC,CAAChB,CAAC,CAAC,CAAC,CAAC;EAAEqB,CAAC,GAAGA,CAACtB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAMoB,CAAC,IAAK;IACzO,MAAMC,CAAC,GAAGxB,CAAC,CAACuB,CAAC,CAAC;MAAEE,CAAC,GAAGzB,CAAC,CAACE,CAAC,CAAC;IACxB,OAAOC,CAAC,KAAK,MAAM,GAAGqB,CAAC,GAAG,CAACA,CAAC,GAAGC,CAAC,IAAIxB,CAAC,GAAGsB,CAAC,CAACG,OAAO,CAAC,CAAC,IAAIxB,CAAC,CAACwB,OAAO,CAAC,CAAC,IAAIF,CAAC,KAAK,CAAC,IAAIA,CAAC,IAAIC,CAAC,GAAGE,IAAI,CAACC,IAAI,CAACJ,CAAC,GAAGvB,CAAC,CAAC,GAAGA,CAAC,GAAGuB,CAAC,GAAGA,CAAC,GAAGvB,CAAC;EAC5H,CAAC;EAAE4B,CAAC,GAAI7B,CAAC,IAAK,CAACC,CAAC,EAAEC,CAAC,KAAKF,CAAC,CAACqB,GAAG,CAAElB,CAAC,IAAK;IACnC,MAAMoB,CAAC,GAAGI,IAAI,CAACG,KAAK,CAAC7B,CAAC,CAACE,CAAC,CAACE,IAAI,CAAC,CAAC;IAC/B,OAAOkB,CAAC,GAAGD,CAAC,CAACnB,CAAC,CAACI,MAAM,EAAEgB,CAAC,EAAErB,CAAC,EAAEC,CAAC,CAACE,IAAI,CAAC,GAAGF,CAAC,CAACI,MAAM;EACjD,CAAC,CAAC;EAAEwB,CAAC,GAAGX,CAAC,CAAChB,CAAC,CAAC;EAAE4B,CAAC,GAAGH,CAAC,CAACzB,CAAC,CAAC;EAAE6B,CAAC,GAAIjC,CAAC,IAAK,CAACC,CAAC,EAAEC,CAAC,MAAMD,CAAC,CAACiC,QAAQ,CAAC,GAAGlC,CAAC,CAACqB,GAAG,CAAElB,CAAC,IAAKA,CAAC,CAACF,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC,EAAED,CAAC,CAAC;EAAEkC,CAAC,GAAInC,CAAC,IAAMC,CAAC,IAAK;IACxG,MAAMC,CAAC,GAAGZ,CAAC,CAACW,CAAC,CAAC;IACd,OAAOC,CAAC,CAACgC,QAAQ,CAAC,GAAGlC,CAAC,CAACqB,GAAG,CAAElB,CAAC,IAAKA,CAAC,CAACD,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC;EAC7C,CAAC;EAAEkC,CAAC,GAAGrC,CAAC,CAAC,UAAU,CAAC;EAAEsC,CAAC,GAAGtC,CAAC,CAAC,YAAY,CAAC;EAAEuC,CAAC,GAAGvC,CAAC,CAAC,YAAY,CAAC;EAAEwC,CAAC,GAAGA,CAAA,KAAM,eAAgB,IAAIC,IAAI,CAAC,CAAC;EAAEC,CAAC,GAAGA,CAACzC,CAAC,EAAEC,CAAC,EAAEC,CAAC,GAAG,CAAC,KAAK;IACzH,MAAMC,CAAC,GAAG,EAAE;IACZ,KAAK,IAAIoB,CAAC,GAAGvB,CAAC,EAAEuB,CAAC,GAAGtB,CAAC,EAAEsB,CAAC,GAAGA,CAAC,GAAGrB,CAAC,EAC9BC,CAAC,CAACuC,IAAI,CAACnB,CAAC,CAAC;IACX,OAAOpB,CAAC;EACV,CAAC;EAAEwC,CAAC,GAAGA,CAAC3C,CAAC,EAAEC,CAAC,EAAEC,CAAC,MAAM;IACnB0C,cAAc,EAAEhD,CAAC,CAACE,CAAC,EAAEE,CAAC,CAAC;IACvB6C,QAAQ,EAAErD,CAAC,CAACI,CAAC,CAACE,CAAC,EAAEI,CAAC,CAAC,EAAED,CAAC,CAACO,QAAQ,CAAC,CAAC,GAAGN,CAAC,CAACM,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACzDsC,QAAQ,EAAElD,CAAC,CAACE,CAAC,EAAEG,CAAC;EAClB,CAAC,CAAC;EAAE8C,CAAC,GAAGA,CAAC/C,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;IACnB,IAAI,CAACF,CAAC,IAAI,CAACC,CAAC,IAAI,CAACC,CAAC,EAChB,OAAOF,CAAC;IACV,MAAM;MAAE4C,cAAc,EAAEzC,CAAC;MAAE2C,QAAQ,EAAEvB,CAAC;MAAEsB,QAAQ,EAAErB;IAAE,CAAC,GAAGmB,CAAC,CAAC3C,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;IAClE,OAAOC,CAAC,GAAGoB,CAAC,GAAG3B,CAAC,CAACI,CAAC,EAAEC,CAAC,CAAC,GAAGE,CAAC,GAAGqB,CAAC,GAAG5B,CAAC,CAACI,CAAC,EAAEE,CAAC,CAAC,GAAGF,CAAC;EAC9C,CAAC;EAAEgD,CAAC,GAAGA,CAAChD,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;IAClB,IAAI,CAACF,CAAC,IAAI,CAACC,CAAC,IAAI,CAACC,CAAC,EAChB,OAAO,CAAC,CAAC;IACX,MAAM;MAAE0C,cAAc,EAAEzC,CAAC;MAAE2C,QAAQ,EAAEvB,CAAC;MAAEsB,QAAQ,EAAErB;IAAE,CAAC,GAAGmB,CAAC,CAAC3C,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;IAClE,OAAOqB,CAAC,IAAIpB,CAAC,IAAIA,CAAC,IAAIqB,CAAC;EACzB,CAAC;EAAEyB,CAAC,GAAGA,CAACjD,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;IAClB,IAAIF,CAAC,KAAK,IAAI,EACZ,OAAO,CAAC,CAAC;IACX,MAAM;MAAE4C,cAAc,EAAEzC,CAAC;MAAE2C,QAAQ,EAAEvB,CAAC;MAAEsB,QAAQ,EAAErB;IAAE,CAAC,GAAGmB,CAAC,CAAC3C,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;IAClE,OAAOqB,CAAC,IAAIpB,CAAC,IAAIA,CAAC,IAAIqB,CAAC;EACzB,CAAC;EAAE0B,CAAC,GAAGA,CAAClD,CAAC,EAAEC,CAAC,KAAK;IACf,IAAID,CAAC,KAAK,IAAI,IAAIC,CAAC,KAAK,IAAI,EAC1B,OAAO,CAAC,CAAC;IACX,MAAMC,CAAC,GAAGN,CAAC,CAACE,CAAC,EAAEE,CAAC,CAAC;MAAEG,CAAC,GAAGP,CAAC,CAACE,CAAC,EAAEG,CAAC,CAAC;IAC9B,OAAOC,CAAC,CAACwB,OAAO,CAAC,CAAC,GAAGvB,CAAC,CAACK,QAAQ,CAAC,CAAC;EACnC,CAAC;EAAE2C,CAAC,GAAGA,CAACnD,CAAC,EAAEC,CAAC,KAAK;IACf,IAAID,CAAC,KAAK,IAAI,IAAIC,CAAC,KAAK,IAAI,EAC1B,OAAO,CAAC,CAAC;IACX,MAAMC,CAAC,GAAGN,CAAC,CAACE,CAAC,EAAEE,CAAC,CAAC;IACjB,OAAOJ,CAAC,CAACE,CAAC,EAAEG,CAAC,CAAC,CAACyB,OAAO,CAAC,CAAC,GAAGxB,CAAC,CAACM,QAAQ,CAAC,CAAC;EACzC,CAAC;AACD,SACEuB,CAAC,IAAIqB,eAAe,EACpBpB,CAAC,IAAIqB,gBAAgB,EACrBd,CAAC,IAAIe,MAAM,EACXH,CAAC,IAAII,eAAe,EACpBN,CAAC,IAAIO,SAAS,EACdR,CAAC,IAAIS,aAAa,EAClBP,CAAC,IAAIQ,gBAAgB,EACrBjB,CAAC,IAAIkB,KAAK,EACVvB,CAAC,IAAIF,QAAQ,EACbG,CAAC,IAAIuB,UAAU,EACftB,CAAC,IAAIuB,UAAU,EACf1B,CAAC,IAAI2B,QAAQ,EACbf,CAAC,IAAIgB,WAAW,EAChB9B,CAAC,IAAI+B,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}