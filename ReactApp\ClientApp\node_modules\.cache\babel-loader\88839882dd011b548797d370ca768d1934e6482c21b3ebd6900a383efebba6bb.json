{"ast": null, "code": "import withPoints from '../mixins/with-points';\nimport Point from '../geometry/point';\nimport Gradient from './gradient';\nvar points = [\"start\", \"end\"];\nvar LinearGradient = function (superclass) {\n  function LinearGradient(options) {\n    if (options === void 0) options = {};\n    superclass.call(this, options);\n    this.start(options.start || new Point());\n    this.end(options.end || new Point(1, 0));\n  }\n  if (superclass) LinearGradient.__proto__ = superclass;\n  LinearGradient.prototype = Object.create(superclass && superclass.prototype);\n  LinearGradient.prototype.constructor = LinearGradient;\n  return LinearGradient;\n}(withPoints(Gradient, points));\nexport default LinearGradient;", "map": {"version": 3, "names": ["withPoints", "Point", "Gradient", "points", "LinearGradient", "superclass", "options", "call", "start", "end", "__proto__", "prototype", "Object", "create", "constructor"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/gradients/linear-gradient.js"], "sourcesContent": ["import withPoints from '../mixins/with-points';\nimport Point from '../geometry/point';\nimport Gradient from './gradient';\n\n\nvar points = [ \"start\", \"end\" ];\n\nvar LinearGradient = (function (superclass) {\n    function LinearGradient(options) {\n        if ( options === void 0 ) options = {};\n\n        superclass.call(this, options);\n\n        this.start(options.start || new Point());\n        this.end(options.end || new Point(1, 0));\n    }\n\n    if ( superclass ) LinearGradient.__proto__ = superclass;\n    LinearGradient.prototype = Object.create( superclass && superclass.prototype );\n    LinearGradient.prototype.constructor = LinearGradient;\n\n    return LinearGradient;\n}(withPoints(Gradient, points)));\n\nexport default LinearGradient;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,KAAK,MAAM,mBAAmB;AACrC,OAAOC,QAAQ,MAAM,YAAY;AAGjC,IAAIC,MAAM,GAAG,CAAE,OAAO,EAAE,KAAK,CAAE;AAE/B,IAAIC,cAAc,GAAI,UAAUC,UAAU,EAAE;EACxC,SAASD,cAAcA,CAACE,OAAO,EAAE;IAC7B,IAAKA,OAAO,KAAK,KAAK,CAAC,EAAGA,OAAO,GAAG,CAAC,CAAC;IAEtCD,UAAU,CAACE,IAAI,CAAC,IAAI,EAAED,OAAO,CAAC;IAE9B,IAAI,CAACE,KAAK,CAACF,OAAO,CAACE,KAAK,IAAI,IAAIP,KAAK,CAAC,CAAC,CAAC;IACxC,IAAI,CAACQ,GAAG,CAACH,OAAO,CAACG,GAAG,IAAI,IAAIR,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC5C;EAEA,IAAKI,UAAU,EAAGD,cAAc,CAACM,SAAS,GAAGL,UAAU;EACvDD,cAAc,CAACO,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAER,UAAU,IAAIA,UAAU,CAACM,SAAU,CAAC;EAC9EP,cAAc,CAACO,SAAS,CAACG,WAAW,GAAGV,cAAc;EAErD,OAAOA,cAAc;AACzB,CAAC,CAACJ,UAAU,CAACE,QAAQ,EAAEC,MAAM,CAAC,CAAE;AAEhC,eAAeC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}