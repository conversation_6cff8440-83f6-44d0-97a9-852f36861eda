{"ast": null, "code": "export default function append(first, second) {\n  first.push.apply(first, second);\n  return first;\n}", "map": {"version": 3, "names": ["append", "first", "second", "push", "apply"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/util/append.js"], "sourcesContent": ["export default function append(first, second) {\n    first.push.apply(first, second);\n    return first;\n}"], "mappings": "AAAA,eAAe,SAASA,MAAMA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC1CD,KAAK,CAACE,IAAI,CAACC,KAAK,CAACH,KAAK,EAAEC,MAAM,CAAC;EAC/B,OAAOD,KAAK;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}