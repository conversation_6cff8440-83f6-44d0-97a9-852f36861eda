{"ast": null, "code": "import { getZoneRules } from './get-zone';\nimport { formatMessage, NO_TZ_INFO } from '../errors';\n/**\n * @hidden\n *\n * A function that finds zone rules which become applicable after specific time.\n */\nexport var findZone = function (timezone, utcTime) {\n  if (utcTime === void 0) {\n    utcTime = new Date().getTime();\n  }\n  if (timezone === 'Etc/UTC' || timezone === 'Etc/GMT') {\n    return [0, \"-\", \"UTC\", null];\n  }\n  var zoneRules = getZoneRules(timezone);\n  var idx = zoneRules.length - 1;\n  for (; idx >= 0; idx--) {\n    var until = zoneRules[idx][3];\n    if (until && utcTime > until) {\n      break;\n    }\n  }\n  var zone = zoneRules[idx + 1];\n  if (!zone) {\n    throw new Error(formatMessage(NO_TZ_INFO, timezone));\n  }\n  return zone;\n};", "map": {"version": 3, "names": ["getZoneRules", "formatMessage", "NO_TZ_INFO", "findZone", "timezone", "utcTime", "Date", "getTime", "zoneRules", "idx", "length", "until", "zone", "Error"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/tz/find-zone.js"], "sourcesContent": ["import { getZoneRules } from './get-zone';\nimport { formatMessage, NO_TZ_INFO } from '../errors';\n/**\n * @hidden\n *\n * A function that finds zone rules which become applicable after specific time.\n */\nexport var findZone = function (timezone, utcTime) {\n    if (utcTime === void 0) { utcTime = new Date().getTime(); }\n    if (timezone === 'Etc/UTC' || timezone === 'Etc/GMT') {\n        return [0, \"-\", \"UTC\", null];\n    }\n    var zoneRules = getZoneRules(timezone);\n    var idx = zoneRules.length - 1;\n    for (; idx >= 0; idx--) {\n        var until = zoneRules[idx][3];\n        if (until && utcTime > until) {\n            break;\n        }\n    }\n    var zone = zoneRules[idx + 1];\n    if (!zone) {\n        throw new Error(formatMessage(NO_TZ_INFO, timezone));\n    }\n    return zone;\n};\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,YAAY;AACzC,SAASC,aAAa,EAAEC,UAAU,QAAQ,WAAW;AACrD;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,QAAQ,GAAG,SAAAA,CAAUC,QAAQ,EAAEC,OAAO,EAAE;EAC/C,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IAAEA,OAAO,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EAAE;EAC1D,IAAIH,QAAQ,KAAK,SAAS,IAAIA,QAAQ,KAAK,SAAS,EAAE;IAClD,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC;EAChC;EACA,IAAII,SAAS,GAAGR,YAAY,CAACI,QAAQ,CAAC;EACtC,IAAIK,GAAG,GAAGD,SAAS,CAACE,MAAM,GAAG,CAAC;EAC9B,OAAOD,GAAG,IAAI,CAAC,EAAEA,GAAG,EAAE,EAAE;IACpB,IAAIE,KAAK,GAAGH,SAAS,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7B,IAAIE,KAAK,IAAIN,OAAO,GAAGM,KAAK,EAAE;MAC1B;IACJ;EACJ;EACA,IAAIC,IAAI,GAAGJ,SAAS,CAACC,GAAG,GAAG,CAAC,CAAC;EAC7B,IAAI,CAACG,IAAI,EAAE;IACP,MAAM,IAAIC,KAAK,CAACZ,aAAa,CAACC,UAAU,EAAEE,QAAQ,CAAC,CAAC;EACxD;EACA,OAAOQ,IAAI;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}