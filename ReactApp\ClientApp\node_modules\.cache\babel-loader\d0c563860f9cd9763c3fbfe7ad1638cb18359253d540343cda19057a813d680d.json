{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst d = 16.666666666666668,\n  g = function (u, f, t = {}) {\n    let e,\n      n,\n      l,\n      r,\n      i = 0;\n    t = t || {};\n    const s = function () {\n      i = t.leading === !1 ? 0 : (/* @__PURE__ */new Date()).getTime(), e = void 0, r = u.apply(n, l), e || (n = l = null);\n    };\n    return function () {\n      const o = (/* @__PURE__ */new Date()).getTime();\n      !i && t.leading === !1 && (i = o);\n      const a = f - (o - i);\n      return n = void 0, l = arguments, a <= 0 || a > f ? (e && (clearTimeout(e), e = void 0), i = o, r = u.apply(n, l), e || (n = l = null)) : !e && t.trailing !== !1 && (e = window.setTimeout(s, a)), r;\n    };\n  };\nexport { d as FRAME_DURATION, g as throttle };", "map": {"version": 3, "names": ["d", "g", "u", "f", "t", "e", "n", "l", "r", "i", "s", "leading", "Date", "getTime", "apply", "o", "a", "arguments", "clearTimeout", "trailing", "window", "setTimeout", "FRAME_DURATION", "throttle"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-popup/util.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst d = 16.666666666666668, g = function(u, f, t = {}) {\n  let e, n, l, r, i = 0;\n  t = t || {};\n  const s = function() {\n    i = t.leading === !1 ? 0 : (/* @__PURE__ */ new Date()).getTime(), e = void 0, r = u.apply(n, l), e || (n = l = null);\n  };\n  return function() {\n    const o = (/* @__PURE__ */ new Date()).getTime();\n    !i && t.leading === !1 && (i = o);\n    const a = f - (o - i);\n    return n = void 0, l = arguments, a <= 0 || a > f ? (e && (clearTimeout(e), e = void 0), i = o, r = u.apply(n, l), e || (n = l = null)) : !e && t.trailing !== !1 && (e = window.setTimeout(s, a)), r;\n  };\n};\nexport {\n  d as FRAME_DURATION,\n  g as throttle\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAG,kBAAkB;EAAEC,CAAC,GAAG,SAAAA,CAASC,CAAC,EAAEC,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC,EAAE;IACvD,IAAIC,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC,CAAC,GAAG,CAAC;IACrBL,CAAC,GAAGA,CAAC,IAAI,CAAC,CAAC;IACX,MAAMM,CAAC,GAAG,SAAAA,CAAA,EAAW;MACnBD,CAAC,GAAGL,CAAC,CAACO,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,eAAgB,IAAIC,IAAI,CAAC,CAAC,EAAEC,OAAO,CAAC,CAAC,EAAER,CAAC,GAAG,KAAK,CAAC,EAAEG,CAAC,GAAGN,CAAC,CAACY,KAAK,CAACR,CAAC,EAAEC,CAAC,CAAC,EAAEF,CAAC,KAAKC,CAAC,GAAGC,CAAC,GAAG,IAAI,CAAC;IACvH,CAAC;IACD,OAAO,YAAW;MAChB,MAAMQ,CAAC,GAAG,CAAC,eAAgB,IAAIH,IAAI,CAAC,CAAC,EAAEC,OAAO,CAAC,CAAC;MAChD,CAACJ,CAAC,IAAIL,CAAC,CAACO,OAAO,KAAK,CAAC,CAAC,KAAKF,CAAC,GAAGM,CAAC,CAAC;MACjC,MAAMC,CAAC,GAAGb,CAAC,IAAIY,CAAC,GAAGN,CAAC,CAAC;MACrB,OAAOH,CAAC,GAAG,KAAK,CAAC,EAAEC,CAAC,GAAGU,SAAS,EAAED,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGb,CAAC,IAAIE,CAAC,KAAKa,YAAY,CAACb,CAAC,CAAC,EAAEA,CAAC,GAAG,KAAK,CAAC,CAAC,EAAEI,CAAC,GAAGM,CAAC,EAAEP,CAAC,GAAGN,CAAC,CAACY,KAAK,CAACR,CAAC,EAAEC,CAAC,CAAC,EAAEF,CAAC,KAAKC,CAAC,GAAGC,CAAC,GAAG,IAAI,CAAC,IAAI,CAACF,CAAC,IAAID,CAAC,CAACe,QAAQ,KAAK,CAAC,CAAC,KAAKd,CAAC,GAAGe,MAAM,CAACC,UAAU,CAACX,CAAC,EAAEM,CAAC,CAAC,CAAC,EAAER,CAAC;IACvM,CAAC;EACH,CAAC;AACD,SACER,CAAC,IAAIsB,cAAc,EACnBrB,CAAC,IAAIsB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}