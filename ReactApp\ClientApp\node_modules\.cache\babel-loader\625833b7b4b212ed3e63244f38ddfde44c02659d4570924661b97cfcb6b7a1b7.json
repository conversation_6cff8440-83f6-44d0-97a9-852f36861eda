{"ast": null, "code": "export default function bindEvents(element, events) {\n  for (var eventName in events) {\n    var eventNames = eventName.trim().split(\" \");\n    for (var idx = 0; idx < eventNames.length; idx++) {\n      element.addEventListener(eventNames[idx], events[eventName], false);\n    }\n  }\n}", "map": {"version": 3, "names": ["bindEvents", "element", "events", "eventName", "eventNames", "trim", "split", "idx", "length", "addEventListener"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/util/bind-events.js"], "sourcesContent": ["export default function bindEvents(element, events) {\n    for (var eventName in events) {\n        var eventNames = eventName.trim().split(\" \");\n        for (var idx = 0; idx < eventNames.length; idx++) {\n            element.addEventListener(eventNames[idx], events[eventName], false);\n        }\n    }\n}"], "mappings": "AAAA,eAAe,SAASA,UAAUA,CAACC,OAAO,EAAEC,MAAM,EAAE;EAChD,KAAK,IAAIC,SAAS,IAAID,MAAM,EAAE;IAC1B,IAAIE,UAAU,GAAGD,SAAS,CAACE,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;IAC5C,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGH,UAAU,CAACI,MAAM,EAAED,GAAG,EAAE,EAAE;MAC9CN,OAAO,CAACQ,gBAAgB,CAACL,UAAU,CAACG,GAAG,CAAC,EAAEL,MAAM,CAACC,SAAS,CAAC,EAAE,KAAK,CAAC;IACvE;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}