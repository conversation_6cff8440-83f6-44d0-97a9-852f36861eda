{"ast": null, "code": "export default function toCubicPolynomial(points, field) {\n  return [-points[0][field] + 3 * points[1][field] - 3 * points[2][field] + points[3][field], 3 * (points[0][field] - 2 * points[1][field] + points[2][field]), 3 * (-points[0][field] + points[1][field]), points[0][field]];\n}", "map": {"version": 3, "names": ["toCubicPolynomial", "points", "field"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/geometry/math/to-cubic-polynomial.js"], "sourcesContent": ["export default function toCubicPolynomial(points, field) {\n    return [ -points[0][field] + 3 * points[1][field] - 3 * points[2][field] + points[3][field],\n        3 * (points[0][field] - 2 * points[1][field] + points[2][field]),\n        3 * (-points[0][field] + points[1][field]),\n        points[0][field]\n    ];\n}"], "mappings": "AAAA,eAAe,SAASA,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACrD,OAAO,CAAE,CAACD,MAAM,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,GAAGD,MAAM,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,GAAGD,MAAM,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAGD,MAAM,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,EACvF,CAAC,IAAID,MAAM,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,GAAGD,MAAM,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAGD,MAAM,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAChE,CAAC,IAAI,CAACD,MAAM,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAGD,MAAM,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAC1CD,MAAM,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CACnB;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}