{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { treeIdUtils as s, removeItem as R, addItem as l } from \"@progress/kendo-react-common\";\nimport { CHILDREN_FIELD as C } from \"./utils/consts.mjs\";\nfunction N(u, f, v, o, n, r) {\n  const t = r || C;\n  if (!B()) return p();\n  const e = s.getItemById(u, f, t);\n  if (!e) return p();\n  if (!n || n === f) {\n    if (!y()) return p();\n    const d = R(u, t, f),\n      m = l(e, v, t, s.getDecrementedItemIdAfterRemoval(u, o), d);\n    return n ? {\n      sourceData: m,\n      targetData: m\n    } : m;\n  }\n  const F = R(u, t, f),\n    _ = l(e, v, t, o, n);\n  return {\n    sourceData: F,\n    targetData: _\n  };\n  function p() {\n    return n ? {\n      sourceData: f,\n      targetData: n\n    } : f;\n  }\n  function y() {\n    return !`${o}_`.startsWith(`${u}_`);\n  }\n  function B() {\n    if (!f || !f.length || !u || !o || n && !n.length) return !1;\n    const d = !n || n === f ? f : n;\n    return !!s.getItemById(o, d, t);\n  }\n}\nexport { N as moveTreeViewItem };", "map": {"version": 3, "names": ["treeIdUtils", "s", "removeItem", "R", "addItem", "l", "CHILDREN_FIELD", "C", "N", "u", "f", "v", "o", "n", "r", "t", "B", "p", "e", "getItemById", "y", "d", "m", "getDecrementedItemIdAfterRemoval", "sourceData", "targetData", "F", "_", "startsWith", "length", "moveTreeViewItem"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-treeview/moveTreeViewItem.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { treeIdUtils as s, removeItem as R, addItem as l } from \"@progress/kendo-react-common\";\nimport { CHILDREN_FIELD as C } from \"./utils/consts.mjs\";\nfunction N(u, f, v, o, n, r) {\n  const t = r || C;\n  if (!B())\n    return p();\n  const e = s.getItemById(u, f, t);\n  if (!e)\n    return p();\n  if (!n || n === f) {\n    if (!y())\n      return p();\n    const d = R(u, t, f), m = l(\n      e,\n      v,\n      t,\n      s.getDecrementedItemIdAfterRemoval(u, o),\n      d\n    );\n    return n ? { sourceData: m, targetData: m } : m;\n  }\n  const F = R(u, t, f), _ = l(e, v, t, o, n);\n  return { sourceData: F, targetData: _ };\n  function p() {\n    return n ? { sourceData: f, targetData: n } : f;\n  }\n  function y() {\n    return !`${o}_`.startsWith(`${u}_`);\n  }\n  function B() {\n    if (!f || !f.length || !u || !o || n && !n.length)\n      return !1;\n    const d = !n || n === f ? f : n;\n    return !!s.getItemById(o, d, t);\n  }\n}\nexport {\n  N as moveTreeViewItem\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,WAAW,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,EAAEC,OAAO,IAAIC,CAAC,QAAQ,8BAA8B;AAC9F,SAASC,cAAc,IAAIC,CAAC,QAAQ,oBAAoB;AACxD,SAASC,CAACA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC3B,MAAMC,CAAC,GAAGD,CAAC,IAAIP,CAAC;EAChB,IAAI,CAACS,CAAC,CAAC,CAAC,EACN,OAAOC,CAAC,CAAC,CAAC;EACZ,MAAMC,CAAC,GAAGjB,CAAC,CAACkB,WAAW,CAACV,CAAC,EAAEC,CAAC,EAAEK,CAAC,CAAC;EAChC,IAAI,CAACG,CAAC,EACJ,OAAOD,CAAC,CAAC,CAAC;EACZ,IAAI,CAACJ,CAAC,IAAIA,CAAC,KAAKH,CAAC,EAAE;IACjB,IAAI,CAACU,CAAC,CAAC,CAAC,EACN,OAAOH,CAAC,CAAC,CAAC;IACZ,MAAMI,CAAC,GAAGlB,CAAC,CAACM,CAAC,EAAEM,CAAC,EAAEL,CAAC,CAAC;MAAEY,CAAC,GAAGjB,CAAC,CACzBa,CAAC,EACDP,CAAC,EACDI,CAAC,EACDd,CAAC,CAACsB,gCAAgC,CAACd,CAAC,EAAEG,CAAC,CAAC,EACxCS,CACF,CAAC;IACD,OAAOR,CAAC,GAAG;MAAEW,UAAU,EAAEF,CAAC;MAAEG,UAAU,EAAEH;IAAE,CAAC,GAAGA,CAAC;EACjD;EACA,MAAMI,CAAC,GAAGvB,CAAC,CAACM,CAAC,EAAEM,CAAC,EAAEL,CAAC,CAAC;IAAEiB,CAAC,GAAGtB,CAAC,CAACa,CAAC,EAAEP,CAAC,EAAEI,CAAC,EAAEH,CAAC,EAAEC,CAAC,CAAC;EAC1C,OAAO;IAAEW,UAAU,EAAEE,CAAC;IAAED,UAAU,EAAEE;EAAE,CAAC;EACvC,SAASV,CAACA,CAAA,EAAG;IACX,OAAOJ,CAAC,GAAG;MAAEW,UAAU,EAAEd,CAAC;MAAEe,UAAU,EAAEZ;IAAE,CAAC,GAAGH,CAAC;EACjD;EACA,SAASU,CAACA,CAAA,EAAG;IACX,OAAO,CAAC,GAAGR,CAAC,GAAG,CAACgB,UAAU,CAAC,GAAGnB,CAAC,GAAG,CAAC;EACrC;EACA,SAASO,CAACA,CAAA,EAAG;IACX,IAAI,CAACN,CAAC,IAAI,CAACA,CAAC,CAACmB,MAAM,IAAI,CAACpB,CAAC,IAAI,CAACG,CAAC,IAAIC,CAAC,IAAI,CAACA,CAAC,CAACgB,MAAM,EAC/C,OAAO,CAAC,CAAC;IACX,MAAMR,CAAC,GAAG,CAACR,CAAC,IAAIA,CAAC,KAAKH,CAAC,GAAGA,CAAC,GAAGG,CAAC;IAC/B,OAAO,CAAC,CAACZ,CAAC,CAACkB,WAAW,CAACP,CAAC,EAAES,CAAC,EAAEN,CAAC,CAAC;EACjC;AACF;AACA,SACEP,CAAC,IAAIsB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}