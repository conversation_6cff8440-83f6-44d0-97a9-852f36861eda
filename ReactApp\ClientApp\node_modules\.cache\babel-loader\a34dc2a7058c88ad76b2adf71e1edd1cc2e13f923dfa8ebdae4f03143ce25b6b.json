{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { isPresent as i } from \"./utils.mjs\";\nimport { Keys as n } from \"@progress/kendo-react-common\";\nclass o {\n  navigate(e) {\n    const t = e.keyCode;\n    if (t === n.up || t === n.left) return this.next({\n      current: e.current,\n      min: e.min,\n      max: e.max,\n      step: e.skipItems ? e.skipItems : -1\n    });\n    if (t === n.down || t === n.right) return this.next({\n      current: e.current,\n      min: e.min,\n      max: e.max,\n      step: e.skipItems ? e.skipItems : 1\n    });\n    if (t === n.home) return 0;\n    if (t === n.end) return e.max;\n  }\n  next(e) {\n    return i(e.current) ? Math.min(e.max, Math.max(e.current + e.step, e.min)) : e.min;\n  }\n}\nexport { o as Navigation };", "map": {"version": 3, "names": ["isPresent", "i", "Keys", "n", "o", "navigate", "e", "t", "keyCode", "up", "left", "next", "current", "min", "max", "step", "skipItems", "down", "right", "home", "end", "Math", "Navigation"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dropdowns/common/Navigation.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { isPresent as i } from \"./utils.mjs\";\nimport { Keys as n } from \"@progress/kendo-react-common\";\nclass o {\n  navigate(e) {\n    const t = e.keyCode;\n    if (t === n.up || t === n.left)\n      return this.next({\n        current: e.current,\n        min: e.min,\n        max: e.max,\n        step: e.skipItems ? e.skipItems : -1\n      });\n    if (t === n.down || t === n.right)\n      return this.next({\n        current: e.current,\n        min: e.min,\n        max: e.max,\n        step: e.skipItems ? e.skipItems : 1\n      });\n    if (t === n.home)\n      return 0;\n    if (t === n.end)\n      return e.max;\n  }\n  next(e) {\n    return i(e.current) ? Math.min(e.max, Math.max(e.current + e.step, e.min)) : e.min;\n  }\n}\nexport {\n  o as Navigation\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,IAAIC,CAAC,QAAQ,aAAa;AAC5C,SAASC,IAAI,IAAIC,CAAC,QAAQ,8BAA8B;AACxD,MAAMC,CAAC,CAAC;EACNC,QAAQA,CAACC,CAAC,EAAE;IACV,MAAMC,CAAC,GAAGD,CAAC,CAACE,OAAO;IACnB,IAAID,CAAC,KAAKJ,CAAC,CAACM,EAAE,IAAIF,CAAC,KAAKJ,CAAC,CAACO,IAAI,EAC5B,OAAO,IAAI,CAACC,IAAI,CAAC;MACfC,OAAO,EAAEN,CAAC,CAACM,OAAO;MAClBC,GAAG,EAAEP,CAAC,CAACO,GAAG;MACVC,GAAG,EAAER,CAAC,CAACQ,GAAG;MACVC,IAAI,EAAET,CAAC,CAACU,SAAS,GAAGV,CAAC,CAACU,SAAS,GAAG,CAAC;IACrC,CAAC,CAAC;IACJ,IAAIT,CAAC,KAAKJ,CAAC,CAACc,IAAI,IAAIV,CAAC,KAAKJ,CAAC,CAACe,KAAK,EAC/B,OAAO,IAAI,CAACP,IAAI,CAAC;MACfC,OAAO,EAAEN,CAAC,CAACM,OAAO;MAClBC,GAAG,EAAEP,CAAC,CAACO,GAAG;MACVC,GAAG,EAAER,CAAC,CAACQ,GAAG;MACVC,IAAI,EAAET,CAAC,CAACU,SAAS,GAAGV,CAAC,CAACU,SAAS,GAAG;IACpC,CAAC,CAAC;IACJ,IAAIT,CAAC,KAAKJ,CAAC,CAACgB,IAAI,EACd,OAAO,CAAC;IACV,IAAIZ,CAAC,KAAKJ,CAAC,CAACiB,GAAG,EACb,OAAOd,CAAC,CAACQ,GAAG;EAChB;EACAH,IAAIA,CAACL,CAAC,EAAE;IACN,OAAOL,CAAC,CAACK,CAAC,CAACM,OAAO,CAAC,GAAGS,IAAI,CAACR,GAAG,CAACP,CAAC,CAACQ,GAAG,EAAEO,IAAI,CAACP,GAAG,CAACR,CAAC,CAACM,OAAO,GAAGN,CAAC,CAACS,IAAI,EAAET,CAAC,CAACO,GAAG,CAAC,CAAC,GAAGP,CAAC,CAACO,GAAG;EACpF;AACF;AACA,SACET,CAAC,IAAIkB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}