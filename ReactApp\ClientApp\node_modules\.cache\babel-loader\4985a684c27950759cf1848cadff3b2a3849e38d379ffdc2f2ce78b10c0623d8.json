{"ast": null, "code": "import { cloneDate } from './clone-date';\nimport { Day } from './day.enum';\n/**\n *  A function which returns the first date of the current week.\n *\n * @param date - The initial date.\n * @param weekStartDay [default: Day.Sunday] - The first day of the week.\n * @returns - The first date of the current week.\n *\n * @example\n * ```ts-no-run\n * firstDayInWeek(new Date(2016, 0, 15)); // 2016-01-10\n * firstDayInWeek(new Date(2016, 0, 15), Day.Monday); // 2016-01-11\n * ```\n */\nexport var firstDayInWeek = function (date, weekStartDay) {\n  if (weekStartDay === void 0) {\n    weekStartDay = Day.Sunday;\n  }\n  var first = cloneDate(date);\n  while (first.getDay() !== weekStartDay) {\n    first.setDate(first.getDate() - 1);\n  }\n  return first;\n};", "map": {"version": 3, "names": ["cloneDate", "Day", "firstDayInWeek", "date", "weekStartDay", "Sunday", "first", "getDay", "setDate", "getDate"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/first-day-in-week.js"], "sourcesContent": ["import { cloneDate } from './clone-date';\nimport { Day } from './day.enum';\n/**\n *  A function which returns the first date of the current week.\n *\n * @param date - The initial date.\n * @param weekStartDay [default: Day.Sunday] - The first day of the week.\n * @returns - The first date of the current week.\n *\n * @example\n * ```ts-no-run\n * firstDayInWeek(new Date(2016, 0, 15)); // 2016-01-10\n * firstDayInWeek(new Date(2016, 0, 15), Day.Monday); // 2016-01-11\n * ```\n */\nexport var firstDayInWeek = function (date, weekStartDay) {\n    if (weekStartDay === void 0) { weekStartDay = Day.Sunday; }\n    var first = cloneDate(date);\n    while (first.getDay() !== weekStartDay) {\n        first.setDate(first.getDate() - 1);\n    }\n    return first;\n};\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,cAAc;AACxC,SAASC,GAAG,QAAQ,YAAY;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,cAAc,GAAG,SAAAA,CAAUC,IAAI,EAAEC,YAAY,EAAE;EACtD,IAAIA,YAAY,KAAK,KAAK,CAAC,EAAE;IAAEA,YAAY,GAAGH,GAAG,CAACI,MAAM;EAAE;EAC1D,IAAIC,KAAK,GAAGN,SAAS,CAACG,IAAI,CAAC;EAC3B,OAAOG,KAAK,CAACC,MAAM,CAAC,CAAC,KAAKH,YAAY,EAAE;IACpCE,KAAK,CAACE,OAAO,CAACF,KAAK,CAACG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;EACtC;EACA,OAAOH,KAAK;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}