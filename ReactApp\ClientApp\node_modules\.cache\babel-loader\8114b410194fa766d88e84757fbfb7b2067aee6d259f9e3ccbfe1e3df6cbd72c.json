{"ast": null, "code": "/** @hidden */\nexport var NO_TZ_INFO = 'The required {0} timezone information is not provided!';\n/** @hidden */\nexport var INVALID_TZ_STRUCTURE = 'The provided timezone information has invalid stucture!';\nvar formatRegExp = /\\{(\\d+)}?\\}/g;\nvar flatten = function (arr) {\n  return arr.reduce(function (a, b) {\n    return a.concat(b);\n  }, []);\n};\n/** @hidden */\nexport var formatMessage = function (message) {\n  var values = [];\n  for (var _i = 1; _i < arguments.length; _i++) {\n    values[_i - 1] = arguments[_i];\n  }\n  var flattenValues = flatten(values);\n  return message.replace(formatRegExp, function (_, index) {\n    return flattenValues[parseInt(index, 10)];\n  });\n};", "map": {"version": 3, "names": ["NO_TZ_INFO", "INVALID_TZ_STRUCTURE", "formatRegExp", "flatten", "arr", "reduce", "a", "b", "concat", "formatMessage", "message", "values", "_i", "arguments", "length", "flattenV<PERSON>ues", "replace", "_", "index", "parseInt"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/errors.js"], "sourcesContent": ["/** @hidden */\nexport var NO_TZ_INFO = 'The required {0} timezone information is not provided!';\n/** @hidden */\nexport var INVALID_TZ_STRUCTURE = 'The provided timezone information has invalid stucture!';\nvar formatRegExp = /\\{(\\d+)}?\\}/g;\nvar flatten = function (arr) { return arr.reduce(function (a, b) { return a.concat(b); }, []); };\n/** @hidden */\nexport var formatMessage = function (message) {\n    var values = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        values[_i - 1] = arguments[_i];\n    }\n    var flattenValues = flatten(values);\n    return message.replace(formatRegExp, function (_, index) { return flattenValues[parseInt(index, 10)]; });\n};\n"], "mappings": "AAAA;AACA,OAAO,IAAIA,UAAU,GAAG,wDAAwD;AAChF;AACA,OAAO,IAAIC,oBAAoB,GAAG,yDAAyD;AAC3F,IAAIC,YAAY,GAAG,cAAc;AACjC,IAAIC,OAAO,GAAG,SAAAA,CAAUC,GAAG,EAAE;EAAE,OAAOA,GAAG,CAACC,MAAM,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IAAE,OAAOD,CAAC,CAACE,MAAM,CAACD,CAAC,CAAC;EAAE,CAAC,EAAE,EAAE,CAAC;AAAE,CAAC;AAChG;AACA,OAAO,IAAIE,aAAa,GAAG,SAAAA,CAAUC,OAAO,EAAE;EAC1C,IAAIC,MAAM,GAAG,EAAE;EACf,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CD,MAAM,CAACC,EAAE,GAAG,CAAC,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EAClC;EACA,IAAIG,aAAa,GAAGZ,OAAO,CAACQ,MAAM,CAAC;EACnC,OAAOD,OAAO,CAACM,OAAO,CAACd,YAAY,EAAE,UAAUe,CAAC,EAAEC,KAAK,EAAE;IAAE,OAAOH,aAAa,CAACI,QAAQ,CAACD,KAAK,EAAE,EAAE,CAAC,CAAC;EAAE,CAAC,CAAC;AAC5G,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}