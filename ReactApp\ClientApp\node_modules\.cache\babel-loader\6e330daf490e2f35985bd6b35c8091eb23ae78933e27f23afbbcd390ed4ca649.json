{"ast": null, "code": "import Rect from '../../geometry/rect';\nexport default function elementsBoundingBox(elements, applyTransform, transformation) {\n  var boundingBox;\n  for (var i = 0; i < elements.length; i++) {\n    var element = elements[i];\n    if (element.visible()) {\n      var elementBoundingBox = applyTransform ? element.bbox(transformation) : element.rawBBox();\n      if (elementBoundingBox) {\n        if (boundingBox) {\n          boundingBox = Rect.union(boundingBox, elementBoundingBox);\n        } else {\n          boundingBox = elementBoundingBox;\n        }\n      }\n    }\n  }\n  return boundingBox;\n}", "map": {"version": 3, "names": ["Rect", "elementsBoundingBox", "elements", "applyTransform", "transformation", "boundingBox", "i", "length", "element", "visible", "elementBoundingBox", "bbox", "rawBBox", "union"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/shapes/utils/elements-bounding-box.js"], "sourcesContent": ["import Rect from '../../geometry/rect';\n\nexport default function elementsBoundingBox(elements, applyTransform, transformation) {\n    var boundingBox;\n\n    for (var i = 0; i < elements.length; i++) {\n        var element = elements[i];\n        if (element.visible()) {\n            var elementBoundingBox = applyTransform ? element.bbox(transformation) : element.rawBBox();\n            if (elementBoundingBox) {\n                if (boundingBox) {\n                    boundingBox = Rect.union(boundingBox, elementBoundingBox);\n                } else {\n                    boundingBox = elementBoundingBox;\n                }\n            }\n        }\n    }\n\n    return boundingBox;\n}\n\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,qBAAqB;AAEtC,eAAe,SAASC,mBAAmBA,CAACC,QAAQ,EAAEC,cAAc,EAAEC,cAAc,EAAE;EAClF,IAAIC,WAAW;EAEf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,QAAQ,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;IACtC,IAAIE,OAAO,GAAGN,QAAQ,CAACI,CAAC,CAAC;IACzB,IAAIE,OAAO,CAACC,OAAO,CAAC,CAAC,EAAE;MACnB,IAAIC,kBAAkB,GAAGP,cAAc,GAAGK,OAAO,CAACG,IAAI,CAACP,cAAc,CAAC,GAAGI,OAAO,CAACI,OAAO,CAAC,CAAC;MAC1F,IAAIF,kBAAkB,EAAE;QACpB,IAAIL,WAAW,EAAE;UACbA,WAAW,GAAGL,IAAI,CAACa,KAAK,CAACR,WAAW,EAAEK,kBAAkB,CAAC;QAC7D,CAAC,MAAM;UACHL,WAAW,GAAGK,kBAAkB;QACpC;MACJ;IACJ;EACJ;EAEA,OAAOL,WAAW;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}