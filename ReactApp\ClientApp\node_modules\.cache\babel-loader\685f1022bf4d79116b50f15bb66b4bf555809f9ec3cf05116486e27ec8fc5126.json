{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as r from \"react\";\nconst o = t => {\n  const e = r.useRef(void 0);\n  return r.useEffect(() => {\n    e.current = t;\n  }), e.current;\n};\nexport { o as usePrevious };", "map": {"version": 3, "names": ["r", "o", "t", "e", "useRef", "useEffect", "current", "usePrevious"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-progressbars/progressbar/hooks/usePrevious.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as r from \"react\";\nconst o = (t) => {\n  const e = r.useRef(void 0);\n  return r.useEffect(() => {\n    e.current = t;\n  }), e.current;\n};\nexport {\n  o as usePrevious\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,MAAMC,CAAC,GAAIC,CAAC,IAAK;EACf,MAAMC,CAAC,GAAGH,CAAC,CAACI,MAAM,CAAC,KAAK,CAAC,CAAC;EAC1B,OAAOJ,CAAC,CAACK,SAAS,CAAC,MAAM;IACvBF,CAAC,CAACG,OAAO,GAAGJ,CAAC;EACf,CAAC,CAAC,EAAEC,CAAC,CAACG,OAAO;AACf,CAAC;AACD,SACEL,CAAC,IAAIM,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}