{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst e = \"multiviewcalendar.prevView\",\n  t = \"multiviewcalendar.nextView\",\n  a = \"dateinput.increment\",\n  c = \"dateinput.decrement\",\n  n = \"dateinput.clear\",\n  i = \"calendar.today\",\n  r = \"datepicker.toggleCalendar\",\n  o = \"daterangepicker.swapStartEnd\",\n  l = \"daterangepicker.start\",\n  s = \"daterangepicker.end\",\n  d = \"daterangepicker.separator\",\n  g = \"datetimepicker.toggleDateTimeSelector\",\n  p = \"timepicker.now\",\n  m = \"timepicker.selectNow\",\n  k = \"timepicker.cancel\",\n  w = \"timepicker.set\",\n  S = \"timepicker.toggleTimeSelector\",\n  T = \"timepicker.toggleClock\",\n  v = \"datetimepicker.date\",\n  u = \"datetimepicker.time\",\n  C = \"datetimepicker.cancel\",\n  N = \"datetimepicker.set\",\n  P = \"daterangepicker.cancel\",\n  V = \"daterangepicker.set\",\n  x = {\n    [i]: \"Today\",\n    [p]: \"NOW\",\n    [w]: \"Set\",\n    [k]: \"Cancel\",\n    [v]: \"Date\",\n    [u]: \"Time\",\n    [C]: \"Cancel\",\n    [N]: \"Set\",\n    [P]: \"Cancel\",\n    [V]: \"Set\",\n    [l]: \"Start\",\n    [s]: \"End\",\n    [d]: \" \",\n    [m]: \"Select Now\",\n    [S]: \"Toggle TimeSelector\",\n    [T]: \"Toggle Clock\",\n    [a]: \"Increase value\",\n    [c]: \"Decrease value\",\n    [n]: \"clear\",\n    [r]: \"Toggle calendar\",\n    [e]: \"Navigate to previous view\",\n    [t]: \"Navigate to next view\",\n    [o]: \"Swap start and end values\",\n    [g]: \"Toggle date-time selector\"\n  };\nexport { n as clear, v as date, P as dateRangePickerCancel, V as dateRangePickerSet, C as dateTimePickerCancel, N as dateTimePickerSet, c as decreaseValue, s as end, a as increaseValue, x as messages, t as nextView, p as now, e as prevView, m as selectNow, d as separator, l as start, o as swapStartEnd, u as time, k as timePickerCancel, w as timePickerSet, i as today, r as toggleCalendar, T as toggleClock, g as toggleDateTimeSelector, S as toggleTimeSelector };", "map": {"version": 3, "names": ["e", "t", "a", "c", "n", "i", "r", "o", "l", "s", "d", "g", "p", "m", "k", "w", "S", "T", "v", "u", "C", "N", "P", "V", "x", "clear", "date", "dateRangePickerCancel", "dateRangePickerSet", "dateTimePickerCancel", "dateTimePickerSet", "decreaseValue", "end", "increaseValue", "messages", "next<PERSON>iew", "now", "prevView", "selectNow", "separator", "start", "swapStartEnd", "time", "timePickerCancel", "timePickerSet", "today", "toggleCalendar", "toggleClock", "toggleDateTimeSelector", "toggleTimeSelector"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dateinputs/messages/index.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst e = \"multiviewcalendar.prevView\", t = \"multiviewcalendar.nextView\", a = \"dateinput.increment\", c = \"dateinput.decrement\", n = \"dateinput.clear\", i = \"calendar.today\", r = \"datepicker.toggleCalendar\", o = \"daterangepicker.swapStartEnd\", l = \"daterangepicker.start\", s = \"daterangepicker.end\", d = \"daterangepicker.separator\", g = \"datetimepicker.toggleDateTimeSelector\", p = \"timepicker.now\", m = \"timepicker.selectNow\", k = \"timepicker.cancel\", w = \"timepicker.set\", S = \"timepicker.toggleTimeSelector\", T = \"timepicker.toggleClock\", v = \"datetimepicker.date\", u = \"datetimepicker.time\", C = \"datetimepicker.cancel\", N = \"datetimepicker.set\", P = \"daterangepicker.cancel\", V = \"daterangepicker.set\", x = {\n  [i]: \"Today\",\n  [p]: \"NOW\",\n  [w]: \"Set\",\n  [k]: \"Cancel\",\n  [v]: \"Date\",\n  [u]: \"Time\",\n  [C]: \"Cancel\",\n  [N]: \"Set\",\n  [P]: \"Cancel\",\n  [V]: \"Set\",\n  [l]: \"Start\",\n  [s]: \"End\",\n  [d]: \" \",\n  [m]: \"Select Now\",\n  [S]: \"Toggle TimeSelector\",\n  [T]: \"Toggle Clock\",\n  [a]: \"Increase value\",\n  [c]: \"Decrease value\",\n  [n]: \"clear\",\n  [r]: \"Toggle calendar\",\n  [e]: \"Navigate to previous view\",\n  [t]: \"Navigate to next view\",\n  [o]: \"Swap start and end values\",\n  [g]: \"Toggle date-time selector\"\n};\nexport {\n  n as clear,\n  v as date,\n  P as dateRangePickerCancel,\n  V as dateRangePickerSet,\n  C as dateTimePickerCancel,\n  N as dateTimePickerSet,\n  c as decreaseValue,\n  s as end,\n  a as increaseValue,\n  x as messages,\n  t as nextView,\n  p as now,\n  e as prevView,\n  m as selectNow,\n  d as separator,\n  l as start,\n  o as swapStartEnd,\n  u as time,\n  k as timePickerCancel,\n  w as timePickerSet,\n  i as today,\n  r as toggleCalendar,\n  T as toggleClock,\n  g as toggleDateTimeSelector,\n  S as toggleTimeSelector\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAG,4BAA4B;EAAEC,CAAC,GAAG,4BAA4B;EAAEC,CAAC,GAAG,qBAAqB;EAAEC,CAAC,GAAG,qBAAqB;EAAEC,CAAC,GAAG,iBAAiB;EAAEC,CAAC,GAAG,gBAAgB;EAAEC,CAAC,GAAG,2BAA2B;EAAEC,CAAC,GAAG,8BAA8B;EAAEC,CAAC,GAAG,uBAAuB;EAAEC,CAAC,GAAG,qBAAqB;EAAEC,CAAC,GAAG,2BAA2B;EAAEC,CAAC,GAAG,uCAAuC;EAAEC,CAAC,GAAG,gBAAgB;EAAEC,CAAC,GAAG,sBAAsB;EAAEC,CAAC,GAAG,mBAAmB;EAAEC,CAAC,GAAG,gBAAgB;EAAEC,CAAC,GAAG,+BAA+B;EAAEC,CAAC,GAAG,wBAAwB;EAAEC,CAAC,GAAG,qBAAqB;EAAEC,CAAC,GAAG,qBAAqB;EAAEC,CAAC,GAAG,uBAAuB;EAAEC,CAAC,GAAG,oBAAoB;EAAEC,CAAC,GAAG,wBAAwB;EAAEC,CAAC,GAAG,qBAAqB;EAAEC,CAAC,GAAG;IACpsB,CAACnB,CAAC,GAAG,OAAO;IACZ,CAACO,CAAC,GAAG,KAAK;IACV,CAACG,CAAC,GAAG,KAAK;IACV,CAACD,CAAC,GAAG,QAAQ;IACb,CAACI,CAAC,GAAG,MAAM;IACX,CAACC,CAAC,GAAG,MAAM;IACX,CAACC,CAAC,GAAG,QAAQ;IACb,CAACC,CAAC,GAAG,KAAK;IACV,CAACC,CAAC,GAAG,QAAQ;IACb,CAACC,CAAC,GAAG,KAAK;IACV,CAACf,CAAC,GAAG,OAAO;IACZ,CAACC,CAAC,GAAG,KAAK;IACV,CAACC,CAAC,GAAG,GAAG;IACR,CAACG,CAAC,GAAG,YAAY;IACjB,CAACG,CAAC,GAAG,qBAAqB;IAC1B,CAACC,CAAC,GAAG,cAAc;IACnB,CAACf,CAAC,GAAG,gBAAgB;IACrB,CAACC,CAAC,GAAG,gBAAgB;IACrB,CAACC,CAAC,GAAG,OAAO;IACZ,CAACE,CAAC,GAAG,iBAAiB;IACtB,CAACN,CAAC,GAAG,2BAA2B;IAChC,CAACC,CAAC,GAAG,uBAAuB;IAC5B,CAACM,CAAC,GAAG,2BAA2B;IAChC,CAACI,CAAC,GAAG;EACP,CAAC;AACD,SACEP,CAAC,IAAIqB,KAAK,EACVP,CAAC,IAAIQ,IAAI,EACTJ,CAAC,IAAIK,qBAAqB,EAC1BJ,CAAC,IAAIK,kBAAkB,EACvBR,CAAC,IAAIS,oBAAoB,EACzBR,CAAC,IAAIS,iBAAiB,EACtB3B,CAAC,IAAI4B,aAAa,EAClBtB,CAAC,IAAIuB,GAAG,EACR9B,CAAC,IAAI+B,aAAa,EAClBT,CAAC,IAAIU,QAAQ,EACbjC,CAAC,IAAIkC,QAAQ,EACbvB,CAAC,IAAIwB,GAAG,EACRpC,CAAC,IAAIqC,QAAQ,EACbxB,CAAC,IAAIyB,SAAS,EACd5B,CAAC,IAAI6B,SAAS,EACd/B,CAAC,IAAIgC,KAAK,EACVjC,CAAC,IAAIkC,YAAY,EACjBtB,CAAC,IAAIuB,IAAI,EACT5B,CAAC,IAAI6B,gBAAgB,EACrB5B,CAAC,IAAI6B,aAAa,EAClBvC,CAAC,IAAIwC,KAAK,EACVvC,CAAC,IAAIwC,cAAc,EACnB7B,CAAC,IAAI8B,WAAW,EAChBpC,CAAC,IAAIqC,sBAAsB,EAC3BhC,CAAC,IAAIiC,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}