{"ast": null, "code": "/* Copyright 2022 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar _AnnotationEditor_instances, _a, _AnnotationEditor_x, _AnnotationEditor_y, _AnnotationEditor_disabled, _AnnotationEditor_keepAspectRatio, _AnnotationEditor_focusAC, _AnnotationEditor_hasBeenClicked, _AnnotationEditor_initialPosition, _AnnotationEditor_isEditing, _AnnotationEditor_isInEditMode, _AnnotationEditor_moveInDOMTimeout, _AnnotationEditor_prevDragX, _AnnotationEditor_prevDragY, _AnnotationEditor_isDraggable, _AnnotationEditor_zIndex, _AnnotationEditor_translate, _AnnotationEditor_rotatePoint, _AnnotationEditor_selectOnPointerEvent, _AnnotationEditor_setUpDragSession, _AnnotationEditor_addFocusListeners, _AnnotationEditor_stopResizing;\nimport { __classPrivateFieldGet, __classPrivateFieldSet } from \"tslib\";\n// import { AnnotationEditorType, shadow } from \"../../shared/utils\";\n// import { AnnotationEditorType } from \"../../shared/utils\";\n// import { FeatureTest } from \"../../shared/utils\";\nimport { IdManager } from \"../helpers/id-manager\";\nimport { ColorManager } from \"../helpers/color-manager\";\nimport { bindEvents } from \"../helpers/tools\";\nimport { shadow, FeatureTest } from \"pdfjs-dist/legacy/build/pdf.mjs\";\nimport { AnnotationEditorType } from \"../shared/utils\";\nexport class AnnotationEditor {\n  constructor(parameters) {\n    // if (this.constructor === AnnotationEditor) {\n    //     unreachable(\"Cannot initialize AnnotationEditor.\");\n    // }\n    _AnnotationEditor_instances.add(this);\n    // todo: properties\n    this.parent = null;\n    this.id = null;\n    this.width = null;\n    this.height = null;\n    this.pageIndex = 0;\n    this.name = \"\";\n    this.div = null;\n    this.annotationElementId = null;\n    this._willKeepAspectRatio = false;\n    // _initialOptions = {};\n    this._structTreeParentId = null;\n    this.rotation = 0;\n    this.pageRotation = 0;\n    this.pageDimensions = [];\n    this.pageTranslation = [];\n    // x = 0;\n    // y = 0;\n    _AnnotationEditor_x.set(this, 0);\n    _AnnotationEditor_y.set(this, 0);\n    this.isAttachedToDOM = false;\n    this.deleted = false;\n    // todo: end\n    // #accessibilityData = null;\n    // #allResizerDivs = null;\n    // #altText = null;\n    _AnnotationEditor_disabled.set(this, false);\n    _AnnotationEditor_keepAspectRatio.set(this, false);\n    // #resizersDiv = null;\n    // #savedDimensions = null;\n    _AnnotationEditor_focusAC.set(this, null);\n    // #focusedResizerName = \"\";\n    _AnnotationEditor_hasBeenClicked.set(this, false);\n    _AnnotationEditor_initialPosition.set(this, null);\n    _AnnotationEditor_isEditing.set(this, false);\n    _AnnotationEditor_isInEditMode.set(this, false);\n    // #isResizerEnabledForKeyboard = false;\n    _AnnotationEditor_moveInDOMTimeout.set(this, null);\n    _AnnotationEditor_prevDragX.set(this, 0);\n    _AnnotationEditor_prevDragY.set(this, 0);\n    // #telemetryTimeouts = null;\n    this._editToolbar = null;\n    this._initialOptions = Object.create(null);\n    this._isVisible = true;\n    this._uiManager = null;\n    this._focusEventsAllowed = true;\n    this._l10nPromise = null;\n    _AnnotationEditor_isDraggable.set(this, false);\n    _AnnotationEditor_zIndex.set(this, _a._zIndex++);\n    this.parent = parameters.parent;\n    this.id = parameters.id || _a.idManager.id;\n    this.width = this.height = null;\n    this.pageIndex = parameters.parent.pageIndex;\n    this.name = parameters.name;\n    this.div = null;\n    this._uiManager = parameters.uiManager;\n    this.annotationElementId = null;\n    this._willKeepAspectRatio = false;\n    this._initialOptions.isCentered = parameters.isCentered;\n    this._structTreeParentId = null;\n    const {\n      rotation,\n      rawDims: {\n        pageWidth,\n        pageHeight,\n        pageX,\n        pageY\n      }\n    } = this.parent.viewport;\n    this.rotation = rotation;\n    this.pageRotation = (360 + rotation - this._uiManager.viewParameters.rotation) % 360;\n    this.pageDimensions = [pageWidth, pageHeight];\n    this.pageTranslation = [pageX, pageY];\n    const [width, height] = this.parentDimensions;\n    this.x = parameters.x / width;\n    this.y = parameters.y / height;\n    this.isAttachedToDOM = false;\n    this.deleted = false;\n  }\n  get x() {\n    return __classPrivateFieldGet(this, _AnnotationEditor_x, \"f\");\n  }\n  set x(value) {\n    __classPrivateFieldSet(this, _AnnotationEditor_x, value, \"f\");\n  }\n  get y() {\n    return __classPrivateFieldGet(this, _AnnotationEditor_y, \"f\");\n  }\n  set y(value) {\n    __classPrivateFieldSet(this, _AnnotationEditor_y, value, \"f\");\n  }\n  get editorType() {\n    return Object.getPrototypeOf(this).constructor._type;\n  }\n  static get _defaultLineColor() {\n    return shadow(this, \"_defaultLineColor\", this._colorManager.getHexCode(\"CanvasText\"));\n  }\n  static deleteAnnotationElement(editor) {\n    const fakeEditor = new FakeEditor({\n      id: editor.parent.getNextId(),\n      parent: editor.parent,\n      uiManager: editor._uiManager\n    });\n    fakeEditor.annotationElementId = editor.annotationElementId;\n    fakeEditor.deleted = true;\n    fakeEditor._uiManager.addToAnnotationStorage(fakeEditor);\n  }\n  static initialize(l10n, uiManager, options) {\n    if (!l10n || !uiManager || !options) {\n      /* no-empty */\n    }\n    // static initialize() {\n    // AnnotationEditor._l10nPromise ||= new Map(\n    //     [\n    //         \"pdfjs-editor-alt-text-button-label\",\n    //         \"pdfjs-editor-alt-text-edit-button-label\",\n    //         \"pdfjs-editor-alt-text-decorative-tooltip\",\n    //         \"pdfjs-editor-new-alt-text-added-button-label\",\n    //         \"pdfjs-editor-new-alt-text-missing-button-label\",\n    //         \"pdfjs-editor-new-alt-text-to-review-button-label\",\n    //         \"pdfjs-editor-resizer-label-topLeft\",\n    //         \"pdfjs-editor-resizer-label-topMiddle\",\n    //         \"pdfjs-editor-resizer-label-topRight\",\n    //         \"pdfjs-editor-resizer-label-middleRight\",\n    //         \"pdfjs-editor-resizer-label-bottomRight\",\n    //         \"pdfjs-editor-resizer-label-bottomMiddle\",\n    //         \"pdfjs-editor-resizer-label-bottomLeft\",\n    //         \"pdfjs-editor-resizer-label-middleLeft\",\n    //     ].map(str => [\n    //         str,\n    //         l10n.get(str.replaceAll(/([A-Z])/g, c => `-${c.toLowerCase()}`)),\n    //     ])\n    // );\n    // // The string isn't in the above list because the string has a parameter\n    // // (i.e. the guessed text) and we must pass it to the l10n function to get\n    // // the correct translation.\n    // AnnotationEditor._l10nPromise.set(\n    //     \"pdfjs-editor-new-alt-text-generated-alt-text-with-disclaimer\",\n    //     l10n.get.bind(\n    //         l10n,\n    //         \"pdfjs-editor-new-alt-text-generated-alt-text-with-disclaimer\"\n    //     )\n    // );\n    // if (options?.strings) {\n    //     for (const str of options.strings) {\n    //         AnnotationEditor._l10nPromise.set(str, l10n.get(str));\n    //     }\n    // }\n    // if (AnnotationEditor._borderLineWidth !== -1) {\n    //     return;\n    // }\n    // const style = getComputedStyle(document.documentElement);\n    // AnnotationEditor._borderLineWidth =\n    //     parseFloat(style.getPropertyValue(\"--outline-width\")) || 0;\n  }\n  /**\n   * Update the default parameters for this type of editor.\n   * @param {number} _type\n   * @param {*} _value\n   */\n  static updateDefaultParams(_type, _value) {\n    // if (!_type || !_value) {\n    // }\n  }\n  /**\n   * Get the default properties to set in the UI for this type of editor.\n   * @returns {Array}\n   */\n  static get defaultPropertiesToUpdate() {\n    return [];\n  }\n  /**\n   * Check if this kind of editor is able to handle the given mime type for\n   * pasting.\n   * @param {string} mime\n   * @returns {boolean}\n   */\n  static isHandlingMimeForPasting(mime) {\n    if (!mime) {\n      return;\n    }\n    return false;\n  }\n  /**\n   * Extract the data from the clipboard item and delegate the creation of the\n   * editor to the parent.\n   * @param {DataTransferItem} item\n   * @param {AnnotationEditorLayer} parent\n   */\n  static paste(item, parent) {\n    if (!item || !parent) {\n      /* no-empty */\n    }\n    // unreachable(\"Not implemented\");\n  }\n  /**\n   * Get the properties to update in the UI for this editor.\n   * @returns {Array}\n   */\n  get propertiesToUpdate() {\n    return [];\n  }\n  get _isDraggable() {\n    return __classPrivateFieldGet(this, _AnnotationEditor_isDraggable, \"f\");\n  }\n  set _isDraggable(value) {\n    var _b;\n    __classPrivateFieldSet(this, _AnnotationEditor_isDraggable, value, \"f\");\n    // this.div?.classList.toggle(\"draggable\", value);\n    (_b = this.div) === null || _b === void 0 ? void 0 : _b.classList.toggle(\"k-draggable\", value);\n  }\n  /**\n   * @returns {boolean} true if the editor handles the Enter key itself.\n   */\n  get isEnterHandled() {\n    return true;\n  }\n  center() {\n    const [pageWidth, pageHeight] = this.pageDimensions;\n    switch (this.parentRotation) {\n      case 90:\n        this.x -= this.height * pageHeight / (pageWidth * 2);\n        this.y += this.width * pageWidth / (pageHeight * 2);\n        break;\n      case 180:\n        this.x += this.width / 2;\n        this.y += this.height / 2;\n        break;\n      case 270:\n        this.x += this.height * pageHeight / (pageWidth * 2);\n        this.y -= this.width * pageWidth / (pageHeight * 2);\n        break;\n      default:\n        this.x -= this.width / 2;\n        this.y -= this.height / 2;\n        break;\n    }\n    this.fixAndSetPosition();\n  }\n  /**\n   * Add some commands into the CommandManager (undo/redo stuff).\n   * @param {Object} params\n   */\n  addCommands(params) {\n    this._uiManager.addCommands(params);\n  }\n  get currentLayer() {\n    return this._uiManager.currentLayer;\n  }\n  /**\n   * This editor will be behind the others.\n   */\n  setInBackground() {\n    this.div.style.zIndex = 0;\n  }\n  /**\n   * This editor will be in the foreground.\n   */\n  setInForeground() {\n    this.div.style.zIndex = __classPrivateFieldGet(this, _AnnotationEditor_zIndex, \"f\");\n  }\n  setParent(parent) {\n    if (parent !== null) {\n      this.pageIndex = parent.pageIndex;\n      this.pageDimensions = parent.pageDimensions;\n    } else {\n      // The editor is being removed from the DOM, so we need to stop resizing.\n      __classPrivateFieldGet(this, _AnnotationEditor_instances, \"m\", _AnnotationEditor_stopResizing).call(this);\n    }\n    this.parent = parent;\n  }\n  /**\n   * onfocus callback.\n   */\n  focusin(event) {\n    if (!event) {\n      return;\n    }\n    if (!this._focusEventsAllowed) {\n      return;\n    }\n    if (!__classPrivateFieldGet(this, _AnnotationEditor_hasBeenClicked, \"f\")) {\n      this.parent.setSelected(this);\n    } else {\n      __classPrivateFieldSet(this, _AnnotationEditor_hasBeenClicked, false, \"f\");\n    }\n  }\n  /**\n   * onblur callback.\n   * @param {FocusEvent} event\n   */\n  focusout(event) {\n    var _b;\n    if (!this._focusEventsAllowed) {\n      return;\n    }\n    if (!this.isAttachedToDOM) {\n      return;\n    }\n    // In case of focusout, the relatedTarget is the element which\n    // is grabbing the focus.\n    // So if the related target is an element under the div for this\n    // editor, then the editor isn't unactive.\n    const target = event.relatedTarget;\n    if (target === null || target === void 0 ? void 0 : target.closest(`#${this.id}`)) {\n      return;\n    }\n    event.preventDefault();\n    if (!((_b = this.parent) === null || _b === void 0 ? void 0 : _b.isMultipleSelection)) {\n      this.commitOrRemove();\n    }\n  }\n  commitOrRemove() {\n    if (this.isEmpty()) {\n      this.remove();\n    } else {\n      this.commit();\n    }\n  }\n  /**\n   * Commit the data contained in this editor.\n   */\n  commit() {\n    this.addToAnnotationStorage();\n  }\n  addToAnnotationStorage() {\n    this._uiManager.addToAnnotationStorage(this);\n  }\n  /**\n   * Set the editor position within its parent.\n   * @param {number} x\n   * @param {number} y\n   * @param {number} tx - x-translation in screen coordinates.\n   * @param {number} ty - y-translation in screen coordinates.\n   */\n  setAt(x, y, tx, ty) {\n    const [width, height] = this.parentDimensions;\n    [tx, ty] = this.screenToPageTranslation(tx, ty);\n    this.x = (x + tx) / width;\n    this.y = (y + ty) / height;\n    this.fixAndSetPosition();\n  }\n  /**\n   * Translate the editor position within its parent.\n   * @param {number} x - x-translation in screen coordinates.\n   * @param {number} y - y-translation in screen coordinates.\n   */\n  translate(x, y) {\n    // We don't change the initial position because the move here hasn't been\n    // done by the user.\n    __classPrivateFieldGet(this, _AnnotationEditor_instances, \"m\", _AnnotationEditor_translate).call(this, this.parentDimensions, x, y);\n  }\n  /**\n   * Translate the editor position within its page and adjust the scroll\n   * in order to have the editor in the view.\n   * @param {number} x - x-translation in page coordinates.\n   * @param {number} y - y-translation in page coordinates.\n   */\n  translateInPage(x, y) {\n    __classPrivateFieldSet(this, _AnnotationEditor_initialPosition, __classPrivateFieldGet(this, _AnnotationEditor_initialPosition, \"f\") || [this.x, this.y], \"f\");\n    __classPrivateFieldGet(this, _AnnotationEditor_instances, \"m\", _AnnotationEditor_translate).call(this, this.pageDimensions, x, y);\n    this.div.scrollIntoView({\n      block: \"nearest\"\n    });\n  }\n  drag(tx, ty) {\n    __classPrivateFieldSet(this, _AnnotationEditor_initialPosition, __classPrivateFieldGet(this, _AnnotationEditor_initialPosition, \"f\") || [this.x, this.y], \"f\");\n    const [parentWidth, parentHeight] = this.parentDimensions;\n    this.x += tx / parentWidth;\n    this.y += ty / parentHeight;\n    if (this.parent && (this.x < 0 || this.x > 1 || this.y < 0 || this.y > 1)) {\n      // It's possible to not have a parent: for example, when the user is\n      // dragging all the selected editors but this one on a page which has been\n      // destroyed.\n      // It's why we need to check for it. In such a situation, it isn't really\n      // a problem to not find a new parent: it's something which is related to\n      // what the user is seeing, hence it depends on how pages are layed out.\n      // The element will be outside of its parent so change the parent.\n      const {\n        x: xValue,\n        y: yValue\n      } = this.div.getBoundingClientRect();\n      if (this.parent.findNewParent(this, xValue, yValue)) {\n        this.x -= Math.floor(this.x);\n        this.y -= Math.floor(this.y);\n      }\n    }\n    // The editor can be moved wherever the user wants, so we don't need to fix\n    // the position: it'll be done when the user will release the mouse button.\n    let {\n      x,\n      y\n    } = this;\n    const [bx, by] = this.getBaseTranslation();\n    x += bx;\n    y += by;\n    this.div.style.left = `${(100 * x).toFixed(2)}%`;\n    this.div.style.top = `${(100 * y).toFixed(2)}%`;\n    this.div.scrollIntoView({\n      block: \"nearest\"\n    });\n  }\n  get _hasBeenMoved() {\n    return !!__classPrivateFieldGet(this, _AnnotationEditor_initialPosition, \"f\") && (__classPrivateFieldGet(this, _AnnotationEditor_initialPosition, \"f\")[0] !== this.x || __classPrivateFieldGet(this, _AnnotationEditor_initialPosition, \"f\")[1] !== this.y);\n  }\n  /**\n   * Get the translation to take into account the editor border.\n   * The CSS engine positions the element by taking the border into account so\n   * we must apply the opposite translation to have the editor in the right\n   * position.\n   * @returns {Array<number>}\n   */\n  getBaseTranslation() {\n    const [parentWidth, parentHeight] = this.parentDimensions;\n    const {\n      _borderLineWidth\n    } = _a;\n    const x = _borderLineWidth / parentWidth;\n    const y = _borderLineWidth / parentHeight;\n    switch (this.rotation) {\n      case 90:\n        return [-x, y];\n      case 180:\n        return [x, y];\n      case 270:\n        return [x, -y];\n      default:\n        return [-x, -y];\n    }\n  }\n  /**\n   * @returns {boolean} true if position must be fixed (i.e. make the x and y\n   * living in the page).\n   */\n  get _mustFixPosition() {\n    return true;\n  }\n  /**\n   * Fix the position of the editor in order to keep it inside its parent page.\n   * @param {number} [rotation] - the rotation of the page.\n   */\n  fixAndSetPosition(rotation = this.rotation) {\n    if (rotation === undefined) {\n      return;\n    }\n    const [pageWidth, pageHeight] = this.pageDimensions;\n    let {\n      x,\n      y,\n      width,\n      height\n    } = this;\n    width *= pageWidth;\n    height *= pageHeight;\n    x *= pageWidth;\n    y *= pageHeight;\n    if (this._mustFixPosition) {\n      switch (rotation) {\n        case 0:\n          x = Math.max(0, Math.min(pageWidth - width, x));\n          y = Math.max(0, Math.min(pageHeight - height, y));\n          break;\n        case 90:\n          x = Math.max(0, Math.min(pageWidth - height, x));\n          y = Math.min(pageHeight, Math.max(width, y));\n          break;\n        case 180:\n          x = Math.min(pageWidth, Math.max(width, x));\n          y = Math.min(pageHeight, Math.max(height, y));\n          break;\n        case 270:\n          x = Math.min(pageWidth, Math.max(height, x));\n          y = Math.max(0, Math.min(pageHeight - width, y));\n          break;\n        default:\n          break;\n      }\n    }\n    this.x = x /= pageWidth;\n    this.y = y /= pageHeight;\n    const [bx, by] = this.getBaseTranslation();\n    x += bx;\n    y += by;\n    const {\n      style\n    } = this.div;\n    style.left = `${(100 * x).toFixed(2)}%`;\n    style.top = `${(100 * y).toFixed(2)}%`;\n    this.moveInDOM();\n  }\n  /**\n   * Convert a screen translation into a page one.\n   * @param {number} x\n   * @param {number} y\n   */\n  screenToPageTranslation(x, y) {\n    return __classPrivateFieldGet(_a, _a, \"m\", _AnnotationEditor_rotatePoint).call(_a, x, y, this.parentRotation);\n  }\n  /**\n   * Convert a page translation into a screen one.\n   * @param {number} x\n   * @param {number} y\n   */\n  pageTranslationToScreen(x, y) {\n    return __classPrivateFieldGet(_a, _a, \"m\", _AnnotationEditor_rotatePoint).call(_a, x, y, 360 - this.parentRotation);\n  }\n  // #getRotationMatrix(rotation) {\n  //     switch (rotation) {\n  //         case 90: {\n  //             const [pageWidth, pageHeight] = this.pageDimensions;\n  //             return [0, -pageWidth / pageHeight, pageHeight / pageWidth, 0];\n  //         }\n  //         case 180:\n  //             return [-1, 0, 0, -1];\n  //         case 270: {\n  //             const [pageWidth, pageHeight] = this.pageDimensions;\n  //             return [0, pageWidth / pageHeight, -pageHeight / pageWidth, 0];\n  //         }\n  //         default:\n  //             return [1, 0, 0, 1];\n  //     }\n  // }\n  get parentScale() {\n    return this._uiManager.viewParameters.realScale;\n  }\n  get parentRotation() {\n    return (this._uiManager.viewParameters.rotation + this.pageRotation) % 360;\n  }\n  get parentDimensions() {\n    const {\n      parentScale,\n      pageDimensions: [pageWidth, pageHeight]\n    } = this;\n    const scaledWidth = pageWidth * parentScale;\n    const scaledHeight = pageHeight * parentScale;\n    return FeatureTest.isCSSRoundSupported ? [Math.round(scaledWidth), Math.round(scaledHeight)] : [scaledWidth, scaledHeight];\n  }\n  /**\n   * Set the dimensions of this editor.\n   * @param {number} width\n   * @param {number} height\n   */\n  setDims(width, height) {\n    const [parentWidth, parentHeight] = this.parentDimensions;\n    this.div.style.width = `${(100 * width / parentWidth).toFixed(2)}%`;\n    if (!__classPrivateFieldGet(this, _AnnotationEditor_keepAspectRatio, \"f\")) {\n      this.div.style.height = `${(100 * height / parentHeight).toFixed(2)}%`;\n    }\n  }\n  fixDims() {\n    //     const { style } = this.div;\n    //     const { height, width } = style;\n    //     const widthPercent = width.endsWith(\"%\");\n    //     const heightPercent = !this.#keepAspectRatio && height.endsWith(\"%\");\n    //     if (widthPercent && heightPercent) {\n    //         return;\n    //     }\n    //     const [parentWidth, parentHeight] = this.parentDimensions;\n    //     if (!widthPercent) {\n    //         style.width = `${((100 * parseFloat(width)) / parentWidth).toFixed(2)}%`;\n    //     }\n    //     if (!this.#keepAspectRatio && !heightPercent) {\n    //         style.height = `${((100 * parseFloat(height)) / parentHeight).toFixed(\n    //             2\n    //         )}%`;\n    //     }\n  }\n  /**\n   * Get the translation used to position this editor when it's created.\n   * @returns {Array<number>}\n   */\n  getInitialTranslation() {\n    return [0, 0];\n  }\n  // #createResizers() {\n  //     if (this.#resizersDiv) {\n  //         return;\n  //     }\n  //     this.#resizersDiv = document.createElement(\"div\");\n  //     this.#resizersDiv.classList.add(\"resizers\");\n  //     // When the resizers are used with the keyboard, they're focusable, hence\n  //     // we want to have them in this order (top left, top middle, top right, ...)\n  //     // in the DOM to have the focus order correct.\n  //     const classes = this._willKeepAspectRatio\n  //         ? [\"topLeft\", \"topRight\", \"bottomRight\", \"bottomLeft\"]\n  //         : [\n  //             \"topLeft\",\n  //             \"topMiddle\",\n  //             \"topRight\",\n  //             \"middleRight\",\n  //             \"bottomRight\",\n  //             \"bottomMiddle\",\n  //             \"bottomLeft\",\n  //             \"middleLeft\",\n  //         ];\n  //     const signal = this._uiManager._signal;\n  //     for (const name of classes) {\n  //         const div = document.createElement(\"div\");\n  //         this.#resizersDiv.append(div);\n  //         div.classList.add(\"resizer\", name);\n  //         div.setAttribute(\"data-resizer-name\", name);\n  //         div.addEventListener(\n  //             \"pointerdown\",\n  //             this.#resizerPointerdown.bind(this, name),\n  //             { signal }\n  //         );\n  //         div.addEventListener(\"contextmenu\", noContextMenu, { signal });\n  //         div.tabIndex = -1;\n  //     }\n  //     this.div.prepend(this.#resizersDiv);\n  // }\n  // #resizerPointerdown(name, event) {\n  //     event.preventDefault();\n  //     const { isMac } = FeatureTest.platform;\n  //     if (event.button !== 0 || (event.ctrlKey && isMac)) {\n  //         return;\n  //     }\n  //     this.#altText?.toggle(false);\n  //     const savedDraggable = this._isDraggable;\n  //     this._isDraggable = false;\n  //     const ac = new AbortController();\n  //     const signal = this._uiManager.combinedSignal(ac);\n  //     this.parent.togglePointerEvents(false);\n  //     window.addEventListener(\n  //         \"pointermove\",\n  //         this.#resizerPointermove.bind(this, name),\n  //         { passive: true, capture: true, signal }\n  //     );\n  //     window.addEventListener(\"contextmenu\", noContextMenu, { signal });\n  //     const savedX = this.x;\n  //     const savedY = this.y;\n  //     const savedWidth = this.width;\n  //     const savedHeight = this.height;\n  //     const savedParentCursor = this.parent.div.style.cursor;\n  //     const savedCursor = this.div.style.cursor;\n  //     this.div.style.cursor = this.parent.div.style.cursor =\n  //         window.getComputedStyle(event.target).cursor;\n  //     const pointerUpCallback = () => {\n  //         ac.abort();\n  //         this.parent.togglePointerEvents(true);\n  //         this.#altText?.toggle(true);\n  //         this._isDraggable = savedDraggable;\n  //         this.parent.div.style.cursor = savedParentCursor;\n  //         this.div.style.cursor = savedCursor;\n  //         this.#addResizeToUndoStack(savedX, savedY, savedWidth, savedHeight);\n  //     };\n  //     window.addEventListener(\"pointerup\", pointerUpCallback, { signal });\n  //     // If the user switches to another window (with alt+tab), then we end the\n  //     // resize session.\n  //     window.addEventListener(\"blur\", pointerUpCallback, { signal });\n  // }\n  // #addResizeToUndoStack(savedX, savedY, savedWidth, savedHeight) {\n  //     const newX = this.x;\n  //     const newY = this.y;\n  //     const newWidth = this.width;\n  //     const newHeight = this.height;\n  //     if (\n  //         newX === savedX &&\n  //         newY === savedY &&\n  //         newWidth === savedWidth &&\n  //         newHeight === savedHeight\n  //     ) {\n  //         return;\n  //     }\n  //     this.addCommands({\n  //         cmd: () => {\n  //             this.width = newWidth;\n  //             this.height = newHeight;\n  //             this.x = newX;\n  //             this.y = newY;\n  //             const [parentWidth, parentHeight] = this.parentDimensions;\n  //             this.setDims(parentWidth * newWidth, parentHeight * newHeight);\n  //             this.fixAndSetPosition();\n  //         },\n  //         undo: () => {\n  //             this.width = savedWidth;\n  //             this.height = savedHeight;\n  //             this.x = savedX;\n  //             this.y = savedY;\n  //             const [parentWidth, parentHeight] = this.parentDimensions;\n  //             this.setDims(parentWidth * savedWidth, parentHeight * savedHeight);\n  //             this.fixAndSetPosition();\n  //         },\n  //         mustExec: true,\n  //     });\n  // }\n  // #resizerPointermove(name, event) {\n  //     const [parentWidth, parentHeight] = this.parentDimensions;\n  //     const savedX = this.x;\n  //     const savedY = this.y;\n  //     const savedWidth = this.width;\n  //     const savedHeight = this.height;\n  //     const minWidth = AnnotationEditor.MIN_SIZE / parentWidth;\n  //     const minHeight = AnnotationEditor.MIN_SIZE / parentHeight;\n  //     // 10000 because we multiply by 100 and use toFixed(2) in fixAndSetPosition.\n  //     // Without rounding, the positions of the corners other than the top left\n  //     // one can be slightly wrong.\n  //     const round = x => Math.round(x * 10000) / 10000;\n  //     const rotationMatrix = this.#getRotationMatrix(this.rotation);\n  //     const transf = (x, y) => [\n  //         rotationMatrix[0] * x + rotationMatrix[2] * y,\n  //         rotationMatrix[1] * x + rotationMatrix[3] * y,\n  //     ];\n  //     const invRotationMatrix = this.#getRotationMatrix(360 - this.rotation);\n  //     const invTransf = (x, y) => [\n  //         invRotationMatrix[0] * x + invRotationMatrix[2] * y,\n  //         invRotationMatrix[1] * x + invRotationMatrix[3] * y,\n  //     ];\n  //     let getPoint;\n  //     let getOpposite;\n  //     let isDiagonal = false;\n  //     let isHorizontal = false;\n  //     switch (name) {\n  //         case \"topLeft\":\n  //             isDiagonal = true;\n  //             getPoint = (w, h) => [0, 0];\n  //             getOpposite = (w, h) => [w, h];\n  //             break;\n  //         case \"topMiddle\":\n  //             getPoint = (w, h) => [w / 2, 0];\n  //             getOpposite = (w, h) => [w / 2, h];\n  //             break;\n  //         case \"topRight\":\n  //             isDiagonal = true;\n  //             getPoint = (w, h) => [w, 0];\n  //             getOpposite = (w, h) => [0, h];\n  //             break;\n  //         case \"middleRight\":\n  //             isHorizontal = true;\n  //             getPoint = (w, h) => [w, h / 2];\n  //             getOpposite = (w, h) => [0, h / 2];\n  //             break;\n  //         case \"bottomRight\":\n  //             isDiagonal = true;\n  //             getPoint = (w, h) => [w, h];\n  //             getOpposite = (w, h) => [0, 0];\n  //             break;\n  //         case \"bottomMiddle\":\n  //             getPoint = (w, h) => [w / 2, h];\n  //             getOpposite = (w, h) => [w / 2, 0];\n  //             break;\n  //         case \"bottomLeft\":\n  //             isDiagonal = true;\n  //             getPoint = (w, h) => [0, h];\n  //             getOpposite = (w, h) => [w, 0];\n  //             break;\n  //         case \"middleLeft\":\n  //             isHorizontal = true;\n  //             getPoint = (w, h) => [0, h / 2];\n  //             getOpposite = (w, h) => [w, h / 2];\n  //             break;\n  //     }\n  //     const point = getPoint(savedWidth, savedHeight);\n  //     const oppositePoint = getOpposite(savedWidth, savedHeight);\n  //     let transfOppositePoint = transf(...oppositePoint);\n  //     const oppositeX = round(savedX + transfOppositePoint[0]);\n  //     const oppositeY = round(savedY + transfOppositePoint[1]);\n  //     let ratioX = 1;\n  //     let ratioY = 1;\n  //     let [deltaX, deltaY] = this.screenToPageTranslation(\n  //         event.movementX,\n  //         event.movementY\n  //     );\n  //     [deltaX, deltaY] = invTransf(deltaX / parentWidth, deltaY / parentHeight);\n  //     if (isDiagonal) {\n  //         const oldDiag = Math.hypot(savedWidth, savedHeight);\n  //         ratioX = ratioY = Math.max(\n  //             Math.min(\n  //                 Math.hypot(\n  //                     oppositePoint[0] - point[0] - deltaX,\n  //                     oppositePoint[1] - point[1] - deltaY\n  //                 ) / oldDiag,\n  //                 // Avoid the editor to be larger than the page.\n  //                 1 / savedWidth,\n  //                 1 / savedHeight\n  //             ),\n  //             // Avoid the editor to be smaller than the minimum size.\n  //             minWidth / savedWidth,\n  //             minHeight / savedHeight\n  //         );\n  //     } else if (isHorizontal) {\n  //         ratioX =\n  //             Math.max(\n  //                 minWidth,\n  //                 Math.min(1, Math.abs(oppositePoint[0] - point[0] - deltaX))\n  //             ) / savedWidth;\n  //     } else {\n  //         ratioY =\n  //             Math.max(\n  //                 minHeight,\n  //                 Math.min(1, Math.abs(oppositePoint[1] - point[1] - deltaY))\n  //             ) / savedHeight;\n  //     }\n  //     const newWidth = round(savedWidth * ratioX);\n  //     const newHeight = round(savedHeight * ratioY);\n  //     transfOppositePoint = transf(...getOpposite(newWidth, newHeight));\n  //     const newX = oppositeX - transfOppositePoint[0];\n  //     const newY = oppositeY - transfOppositePoint[1];\n  //     this.width = newWidth;\n  //     this.height = newHeight;\n  //     this.x = newX;\n  //     this.y = newY;\n  //     this.setDims(parentWidth * newWidth, parentHeight * newHeight);\n  //     this.fixAndSetPosition();\n  // }\n  // /**\n  //  * Called when the alt text dialog is closed.\n  //  */\n  // altTextFinish() {\n  //     this.#altText?.finish();\n  // }\n  // /**\n  //  * Add a toolbar for this editor.\n  //  * @returns {Promise<EditorToolbar|null>}\n  //  */\n  // async addEditToolbar() {\n  addEditToolbar() {\n    var _b;\n    (_b = this._uiManager) === null || _b === void 0 ? void 0 : _b.showEditorToolBar(this.div);\n    // if (this._editToolbar || this.#isInEditMode) {\n    //     return this._editToolbar;\n    // }\n    // this._editToolbar = new EditorToolbar(this);\n    // this.div.append(this._editToolbar.render());\n    // if (this.#altText) {\n    //     this._editToolbar.addAltTextButton(await this.#altText.render());\n    // }\n    // return this._editToolbar;\n  }\n  removeEditToolbar() {\n    this._uiManager.hideEditorToolBar();\n    // if (!this._editToolbar) {\n    //     return;\n    // }\n    // this._editToolbar.remove();\n    // this._editToolbar = null;\n    // // We destroy the alt text but we don't null it because we want to be able\n    // // to restore it in case the user undoes the deletion.\n    // this.#altText?.destroy();\n  }\n  getClientDimensions() {\n    return this.div.getBoundingClientRect();\n  }\n  // async addAltTextButton() {\n  //     if (this.#altText) {\n  //         return;\n  //     }\n  //     AltText.initialize(AnnotationEditor._l10nPromise);\n  //     this.#altText = new AltText(this);\n  //     if (this.#accessibilityData) {\n  //         this.#altText.data = this.#accessibilityData;\n  //         this.#accessibilityData = null;\n  //     }\n  //     await this.addEditToolbar();\n  // }\n  // get altTextData() {\n  //     return this.#altText?.data;\n  // }\n  // /**\n  //  * Set the alt text data.\n  //  */\n  // set altTextData(data) {\n  //     if (!this.#altText) {\n  //         return;\n  //     }\n  //     this.#altText.data = data;\n  // }\n  // get guessedAltText() {\n  //     return this.#altText?.guessedText;\n  // }\n  // async setGuessedAltText(text) {\n  //     await this.#altText?.setGuessedText(text);\n  // }\n  // serializeAltText(isForCopying) {\n  //     return this.#altText?.serialize(isForCopying);\n  // }\n  // hasAltText() {\n  //     return !!this.#altText && !this.#altText.isEmpty();\n  // }\n  // hasAltTextData() {\n  //     return this.#altText?.hasData() ?? false;\n  // }\n  /**\n   * Render this editor in a div.\n   * @returns {HTMLDivElement | null}\n   */\n  render() {\n    this.div = document.createElement(\"div\");\n    this.div.setAttribute(\"data-editor-rotation\", (360 - this.rotation) % 360);\n    this.div.className = this.name;\n    this.div.setAttribute(\"id\", this.id);\n    this.div.tabIndex = __classPrivateFieldGet(this, _AnnotationEditor_disabled, \"f\") ? -1 : 0;\n    if (!this._isVisible) {\n      // this.div.classList.add(\"hidden\");\n      this.div.classList.add(\"k-hidden\");\n    }\n    this.setInForeground();\n    __classPrivateFieldGet(this, _AnnotationEditor_instances, \"m\", _AnnotationEditor_addFocusListeners).call(this);\n    const [parentWidth, parentHeight] = this.parentDimensions;\n    if (this.parentRotation % 180 !== 0) {\n      this.div.style.maxWidth = `${(100 * parentHeight / parentWidth).toFixed(2)}%`;\n      this.div.style.maxHeight = `${(100 * parentWidth / parentHeight).toFixed(2)}%`;\n    }\n    const [tx, ty] = this.getInitialTranslation();\n    this.translate(tx, ty);\n    bindEvents(this, this.div, [\"pointerdown\"]);\n    return this.div;\n  }\n  /**\n   * Onpointerdown callback.\n   * @param {PointerEvent} event\n   */\n  pointerdown(event) {\n    var _b;\n    const {\n      isMac\n    } = FeatureTest.platform;\n    if (event.button !== 0 || event.ctrlKey && isMac) {\n      // Avoid to focus this editor because of a non-left click.\n      event.preventDefault();\n      return;\n    }\n    __classPrivateFieldSet(this, _AnnotationEditor_hasBeenClicked, true, \"f\");\n    if (this._isDraggable) {\n      (_b = this._uiManager) === null || _b === void 0 ? void 0 : _b.hideEditorToolBar();\n      __classPrivateFieldGet(this, _AnnotationEditor_instances, \"m\", _AnnotationEditor_setUpDragSession).call(this, event);\n      return;\n    }\n    __classPrivateFieldGet(this, _AnnotationEditor_instances, \"m\", _AnnotationEditor_selectOnPointerEvent).call(this, event);\n  }\n  moveInDOM() {\n    // Moving the editor in the DOM can be expensive, so we wait a bit before.\n    // It's important to not block the UI (for example when changing the font\n    // size in a FreeText).\n    if (__classPrivateFieldGet(this, _AnnotationEditor_moveInDOMTimeout, \"f\")) {\n      clearTimeout(__classPrivateFieldGet(this, _AnnotationEditor_moveInDOMTimeout, \"f\"));\n    }\n    __classPrivateFieldSet(this, _AnnotationEditor_moveInDOMTimeout, setTimeout(() => {\n      var _b;\n      __classPrivateFieldSet(this, _AnnotationEditor_moveInDOMTimeout, null, \"f\");\n      (_b = this.parent) === null || _b === void 0 ? void 0 : _b.moveEditorInDOM(this);\n    }, 0), \"f\");\n  }\n  _setParentAndPosition(parent, x, y) {\n    parent.changeParent(this);\n    this.x = x;\n    this.y = y;\n    this.fixAndSetPosition();\n  }\n  /*\n   * Convert the current rect into a page one.\n   * @param {number} tx - x-translation in screen coordinates.\n   * @param {number} ty - y-translation in screen coordinates.\n   * @param {number} [rotation] - the rotation of the page.\n   */\n  getRect(tx, ty, rotation = this.rotation) {\n    const scale = this.parentScale;\n    const [pageWidth, pageHeight] = this.pageDimensions;\n    const [pageX, pageY] = this.pageTranslation;\n    const shiftX = tx / scale;\n    const shiftY = ty / scale;\n    const x = this.x * pageWidth;\n    const y = this.y * pageHeight;\n    const width = this.width * pageWidth;\n    const height = this.height * pageHeight;\n    switch (rotation) {\n      case 0:\n        return [x + shiftX + pageX, pageHeight - y - shiftY - height + pageY, x + shiftX + width + pageX, pageHeight - y - shiftY + pageY];\n      case 90:\n        return [x + shiftY + pageX, pageHeight - y + shiftX + pageY, x + shiftY + height + pageX, pageHeight - y + shiftX + width + pageY];\n      case 180:\n        return [x - shiftX - width + pageX, pageHeight - y + shiftY + pageY, x - shiftX + pageX, pageHeight - y + shiftY + height + pageY];\n      case 270:\n        return [x - shiftY - height + pageX, pageHeight - y - shiftX - width + pageY, x - shiftY + pageX, pageHeight - y - shiftX + pageY];\n      default:\n        throw new Error(\"Invalid rotation\");\n    }\n  }\n  getRectInCurrentCoords(rect, pageHeight) {\n    const [x1, y1, x2, y2] = rect;\n    const width = x2 - x1;\n    const height = y2 - y1;\n    switch (this.rotation) {\n      case 0:\n        return [x1, pageHeight - y2, width, height];\n      case 90:\n        return [x1, pageHeight - y1, height, width];\n      case 180:\n        return [x2, pageHeight - y1, width, height];\n      case 270:\n        return [x2, pageHeight - y2, height, width];\n      default:\n        // throw new Error(\"Invalid rotation\");\n        break;\n    }\n  }\n  // /**\n  //  * Executed once this editor has been rendered.\n  //  */\n  onceAdded() {}\n  // /**\n  //  * Check if the editor contains something.\n  //  * @returns {boolean}\n  //  */\n  isEmpty() {\n    return false;\n  }\n  /**\n   * Enable edit mode.\n   */\n  enableEditMode() {\n    __classPrivateFieldSet(this, _AnnotationEditor_isInEditMode, true, \"f\");\n  }\n  /**\n   * Disable edit mode.\n   */\n  disableEditMode() {\n    __classPrivateFieldSet(this, _AnnotationEditor_isInEditMode, false, \"f\");\n  }\n  /**\n   * Check if the editor is edited.\n   * @returns {boolean}\n   */\n  isInEditMode() {\n    return __classPrivateFieldGet(this, _AnnotationEditor_isInEditMode, \"f\");\n  }\n  // /**\n  //  * If it returns true, then this editor handles the keyboard\n  //  * events itself.\n  //  * @returns {boolean}\n  //  */\n  // shouldGetKeyboardEvents() {\n  //     return this.#isResizerEnabledForKeyboard;\n  // }\n  /**\n   * Check if this editor needs to be rebuilt or not.\n   * @returns {boolean}\n   */\n  needsToBeRebuilt() {\n    return this.div && !this.isAttachedToDOM;\n  }\n  /**\n   * Rebuild the editor in case it has been removed on undo.\n   *\n   * To implement in subclasses.\n   */\n  rebuild() {\n    __classPrivateFieldGet(this, _AnnotationEditor_instances, \"m\", _AnnotationEditor_addFocusListeners).call(this);\n  }\n  /**\n   * Rotate the editor.\n   * @param {number} angle\n   */\n  rotate(angle) {\n    if (angle === undefined || angle === null) {\n      /* no-empty */\n    }\n  }\n  /**\n   * Serialize the editor.\n   * The result of the serialization will be used to construct a\n   * new annotation to add to the pdf document.\n   *\n   * To implement in subclasses.\n   * @param {boolean} [_isForCopying]\n   * @param {Object | null} [_context]\n   * @returns {Object | null}\n   */\n  serialize(_isForCopying = false, _context = null) {\n    // serialize() {\n  }\n  /**\n   * Deserialize the editor.\n   * The result of the deserialization is a new editor.\n   *\n   * @param {Object} data\n   * @param {AnnotationEditorLayer} parent\n   * @param {AnnotationEditorUIManager} uiManager\n   * @returns {AnnotationEditor | null}\n   */\n  static deserialize(data, parent, uiManager) {\n    // @ts-expect-error TS(2556):\n    const editor = new this.prototype.constructor({\n      parent,\n      id: parent.getNextId(),\n      uiManager\n    });\n    editor.rotation = data.rotation;\n    // editor.#accessibilityData = data.accessibilityData;\n    const [pageWidth, pageHeight] = editor.pageDimensions;\n    const [x, y, width, height] = editor.getRectInCurrentCoords(data.rect, pageHeight);\n    editor.x = x / pageWidth;\n    editor.y = y / pageHeight;\n    editor.width = width / pageWidth;\n    editor.height = height / pageHeight;\n    return editor;\n  }\n  /**\n   * Check if an existing annotation associated with this editor has been\n   * modified.\n   * @returns {boolean}\n   */\n  get hasBeenModified() {\n    return !!this.annotationElementId && (this.deleted || this.serialize() !== null);\n  }\n  /**\n   * Remove this editor.\n   * It's used on ctrl+backspace action.\n   */\n  remove() {\n    var _b;\n    (_b = __classPrivateFieldGet(this, _AnnotationEditor_focusAC, \"f\")) === null || _b === void 0 ? void 0 : _b.abort();\n    __classPrivateFieldSet(this, _AnnotationEditor_focusAC, null, \"f\");\n    if (!this.isEmpty()) {\n      // The editor is removed but it can be back at some point thanks to\n      // undo/redo so we must commit it before.\n      this.commit();\n    }\n    if (this.parent) {\n      this.parent.remove(this);\n    } else {\n      this._uiManager.removeEditor(this);\n    }\n    if (__classPrivateFieldGet(this, _AnnotationEditor_moveInDOMTimeout, \"f\")) {\n      clearTimeout(__classPrivateFieldGet(this, _AnnotationEditor_moveInDOMTimeout, \"f\"));\n      __classPrivateFieldSet(this, _AnnotationEditor_moveInDOMTimeout, null, \"f\");\n    }\n    __classPrivateFieldGet(this, _AnnotationEditor_instances, \"m\", _AnnotationEditor_stopResizing).call(this);\n    this.removeEditToolbar();\n    // if (this.#telemetryTimeouts) {\n    //     for (const timeout of this.#telemetryTimeouts.values()) {\n    //         clearTimeout(timeout);\n    //     }\n    //     this.#telemetryTimeouts = null;\n    // }\n    this.parent = null;\n  }\n  /**\n   * @returns {boolean} true if this editor can be resized.\n   */\n  get isResizable() {\n    return false;\n  }\n  /**\n   * Add the resizers to this editor.\n   */\n  makeResizable() {\n    //     if (this.isResizable) {\n    //         this.#createResizers();\n    //         this.#resizersDiv.classList.remove(\"hidden\");\n    //         this.#resizersDiv.classList.remove(\"k-hidden\");\n    //         bindEvents(this, this.div, [\"keydown\"]);\n    //     }\n  }\n  get toolbarPosition() {\n    return null;\n  }\n  _stopResizingWithKeyboard() {\n    //     this.#stopResizing();\n    //     this.div.focus();\n  }\n  /**\n   * Select this editor.\n   */\n  select() {\n    var _b;\n    // this.makeResizable();\n    // this.div?.classList.add(\"selectedEditor\");\n    (_b = this.div) === null || _b === void 0 ? void 0 : _b.classList.add(\"k-selected\");\n    // todo: manually show annotation toolbar\n    this.addEditToolbar();\n    // if (!this._editToolbar) {\n    //     this.addEditToolbar().then(() => {\n    //         if (this.div?.classList.contains(\"k-selected selectedEditor\")) {\n    //             // The editor can have been unselected while we were waiting for the\n    //             // edit toolbar to be created, hence we want to be sure that this\n    //             // editor is still selected.\n    //             this._editToolbar?.show();\n    //         }\n    //     });\n    //     return;\n    // }\n    // this._editToolbar?.show();\n    // this.#altText?.toggleAltTextBadge(false);\n  }\n  /**\n   * Unselect this editor.\n   */\n  unselect() {\n    var _b, _c;\n    // this.#resizersDiv?.classList.add(\"hidden\");\n    // this.div?.classList.remove(\"selectedEditor\");\n    (_b = this.div) === null || _b === void 0 ? void 0 : _b.classList.remove(\"k-selected\");\n    if ((_c = this.div) === null || _c === void 0 ? void 0 : _c.contains(document.activeElement)) {\n      // Don't use this.div.blur() because we don't know where the focus will\n      // go.\n      this._uiManager.currentLayer.div.focus({\n        preventScroll: true\n      });\n    }\n    // this._editToolbar?.hide();\n    // this.#altText?.toggleAltTextBadge(true);\n  }\n  /**\n   * Update some parameters which have been changed through the UI.\n   * @param {number} type\n   * @param {*} value\n   */\n  updateParams(type, value) {\n    if (type === undefined || !value) {\n      /* no-empty */\n    }\n  }\n  /**\n   * When the user disables the editing mode some editors can change some of\n   * their properties.\n   */\n  disableEditing() {}\n  /**\n   * When the user enables the editing mode some editors can change some of\n   * their properties.\n   */\n  enableEditing() {}\n  /**\n   * The editor is about to be edited.\n   */\n  enterInEditMode() {}\n  /**\n   * @returns {HTMLElement | null} the element requiring an alt text.\n   */\n  getImageForAltText() {\n    return null;\n  }\n  /**\n   * Get the div which really contains the displayed content.\n   * @returns {HTMLDivElement | undefined}\n   */\n  get contentDiv() {\n    return this.div;\n  }\n  /**\n   * If true then the editor is currently edited.\n   * @type {boolean}\n   */\n  get isEditing() {\n    return __classPrivateFieldGet(this, _AnnotationEditor_isEditing, \"f\");\n  }\n  /**\n   * When set to true, it means that this editor is currently edited.\n   * @param {boolean} value\n   */\n  set isEditing(value) {\n    __classPrivateFieldSet(this, _AnnotationEditor_isEditing, value, \"f\");\n    if (!this.parent) {\n      return;\n    }\n    if (value) {\n      this.parent.setSelected(this);\n      this.parent.setActiveEditor(this);\n    } else {\n      this.parent.setActiveEditor(null);\n    }\n  }\n  /**\n   * Set the aspect ratio to use when resizing.\n   * @param {number} width\n   * @param {number} height\n   */\n  setAspectRatio(width, height) {\n    __classPrivateFieldSet(this, _AnnotationEditor_keepAspectRatio, true, \"f\");\n    const aspectRatio = width / height;\n    const {\n      style\n    } = this.div;\n    style.aspectRatio = aspectRatio;\n    style.height = \"auto\";\n  }\n  static get MIN_SIZE() {\n    return 16;\n  }\n  static canCreateNewEmptyEditor() {\n    return true;\n  }\n  /**\n   * Get the data to report to the telemetry when the editor is added.\n   * @returns {Object}\n   */\n  get telemetryInitialData() {\n    return {\n      action: \"added\"\n    };\n  }\n  /**\n   * The telemetry data to use when saving/printing.\n   * @returns {Object|null}\n   */\n  get telemetryFinalData() {\n    return null;\n  }\n  _reportTelemetry() {}\n  // _reportTelemetry(data, mustWait = false) {\n  //     if (mustWait) {\n  //         this.#telemetryTimeouts ||= new Map();\n  //         const { action } = data;\n  //         let timeout = this.#telemetryTimeouts.get(action);\n  //         if (timeout) {\n  //             clearTimeout(timeout);\n  //         }\n  //         timeout = setTimeout(() => {\n  //             this._reportTelemetry(data);\n  //             this.#telemetryTimeouts.delete(action);\n  //             if (this.#telemetryTimeouts.size === 0) {\n  //                 this.#telemetryTimeouts = null;\n  //             }\n  //         }, AnnotationEditor._telemetryTimeout);\n  //         this.#telemetryTimeouts.set(action, timeout);\n  //         return;\n  //     }\n  //     data.type ||= this.editorType;\n  //     this._uiManager._eventBus.dispatch(\"reporttelemetry\", {\n  //         source: this,\n  //         details: {\n  //             type: \"editing\",\n  //             data,\n  //         },\n  //     });\n  // }\n  /**\n   * Show or hide this editor.\n   * @param {boolean|undefined} visible\n   */\n  show(visible = this._isVisible) {\n    // this.div.classList.toggle(\"hidden\", !visible);\n    this.div.classList.toggle(\"k-hidden\", !visible);\n    this._isVisible = visible;\n  }\n  enable() {\n    if (this.div) {\n      this.div.tabIndex = 0;\n    }\n    __classPrivateFieldSet(this, _AnnotationEditor_disabled, false, \"f\");\n  }\n  disable() {\n    if (this.div) {\n      this.div.tabIndex = -1;\n    }\n    __classPrivateFieldSet(this, _AnnotationEditor_disabled, true, \"f\");\n  }\n  /**\n   * Render an annotation in the annotation layer.\n   * @param {Object} annotation\n   * @returns {HTMLElement}\n   */\n  renderAnnotationElement(annotation) {\n    let content = annotation.container.querySelector(\".annotationContent\") || annotation.container.querySelector(\".k-annotation-content\");\n    if (!content) {\n      content = document.createElement(\"div\");\n      // content.classList.add(\"annotationContent\", this.editorType);\n      content.classList.add(\"k-annotation-content\", this.editorType);\n      annotation.container.prepend(content);\n    } else if (content.nodeName === \"CANVAS\") {\n      const canvas = content;\n      content = document.createElement(\"div\");\n      // content.classList.add(\"annotationContent\", this.editorType);\n      content.classList.add(\"k-annotation-content\", this.editorType);\n      canvas.before(content);\n    }\n    return content;\n  }\n  resetAnnotationElement(annotation) {\n    const {\n      firstChild\n    } = annotation.container;\n    if (firstChild.nodeName === \"DIV\" && (firstChild.classList.contains(\"annotationContent\") || firstChild.classList.contains(\"k-annotation-content\"))) {\n      firstChild.remove();\n    }\n  }\n}\n_a = AnnotationEditor, _AnnotationEditor_x = new WeakMap(), _AnnotationEditor_y = new WeakMap(), _AnnotationEditor_disabled = new WeakMap(), _AnnotationEditor_keepAspectRatio = new WeakMap(), _AnnotationEditor_focusAC = new WeakMap(), _AnnotationEditor_hasBeenClicked = new WeakMap(), _AnnotationEditor_initialPosition = new WeakMap(), _AnnotationEditor_isEditing = new WeakMap(), _AnnotationEditor_isInEditMode = new WeakMap(), _AnnotationEditor_moveInDOMTimeout = new WeakMap(), _AnnotationEditor_prevDragX = new WeakMap(), _AnnotationEditor_prevDragY = new WeakMap(), _AnnotationEditor_isDraggable = new WeakMap(), _AnnotationEditor_zIndex = new WeakMap(), _AnnotationEditor_instances = new WeakSet(), _AnnotationEditor_translate = function _AnnotationEditor_translate([width, height], x, y) {\n  [x, y] = this.screenToPageTranslation(x, y);\n  this.x += x / width;\n  this.y += y / height;\n  this.fixAndSetPosition();\n}, _AnnotationEditor_rotatePoint = function _AnnotationEditor_rotatePoint(x, y, angle) {\n  switch (angle) {\n    case 90:\n      return [y, -x];\n    case 180:\n      return [-x, -y];\n    case 270:\n      return [-y, x];\n    default:\n      return [x, y];\n  }\n}, _AnnotationEditor_selectOnPointerEvent = function _AnnotationEditor_selectOnPointerEvent(event) {\n  const {\n    isMac\n  } = FeatureTest.platform;\n  const highlightEditor = event.target.closest(\".k-highlight-editor\");\n  const freeTextEditor = event.target.closest(\".k-free-text-editor\");\n  const annotationEditorMode = this._uiManager.getMode();\n  // todo: manually prevent selecting a highlight annotation in freetext mode\n  if (annotationEditorMode === AnnotationEditorType.HIGHLIGHT && !highlightEditor || annotationEditorMode === AnnotationEditorType.FREETEXT && !freeTextEditor) {\n    return;\n  }\n  if (event.ctrlKey && !isMac || event.shiftKey || event.metaKey && isMac) {\n    this.parent.toggleSelected(this);\n  } else {\n    this.parent.setSelected(this);\n  }\n}, _AnnotationEditor_setUpDragSession = function _AnnotationEditor_setUpDragSession(event) {\n  const isSelected = this._uiManager.isSelected(this);\n  this._uiManager.setUpDragSession();\n  const ac = new AbortController();\n  const signal = this._uiManager.combinedSignal(ac);\n  if (isSelected) {\n    // this.div.classList.add(\"moving\");\n    __classPrivateFieldSet(this, _AnnotationEditor_prevDragX, event.clientX, \"f\");\n    __classPrivateFieldSet(this, _AnnotationEditor_prevDragY, event.clientY, \"f\");\n    const pointerMoveCallback = e => {\n      const {\n        clientX: x,\n        clientY: y\n      } = e;\n      const [tx, ty] = this.screenToPageTranslation(x - __classPrivateFieldGet(this, _AnnotationEditor_prevDragX, \"f\"), y - __classPrivateFieldGet(this, _AnnotationEditor_prevDragY, \"f\"));\n      __classPrivateFieldSet(this, _AnnotationEditor_prevDragX, x, \"f\");\n      __classPrivateFieldSet(this, _AnnotationEditor_prevDragY, y, \"f\");\n      this._uiManager.dragSelectedEditors(tx, ty);\n    };\n    window.addEventListener(\"pointermove\", pointerMoveCallback, {\n      passive: true,\n      capture: true,\n      signal\n    });\n  }\n  const pointerUpCallback = () => {\n    ac.abort();\n    if (isSelected) {\n      this.div.classList.remove(\"moving\");\n    }\n    __classPrivateFieldSet(this, _AnnotationEditor_hasBeenClicked, false, \"f\");\n    if (!this._uiManager.endDragSession()) {\n      __classPrivateFieldGet(this, _AnnotationEditor_instances, \"m\", _AnnotationEditor_selectOnPointerEvent).call(this, event);\n    }\n  };\n  window.addEventListener(\"pointerup\", pointerUpCallback, {\n    signal\n  });\n  // If the user is using alt+tab during the dragging session, the pointerup\n  // event could be not fired, but a blur event is fired so we can use it in\n  // order to interrupt the dragging session.\n  window.addEventListener(\"blur\", pointerUpCallback, {\n    signal\n  });\n}, _AnnotationEditor_addFocusListeners = function _AnnotationEditor_addFocusListeners() {\n  if (__classPrivateFieldGet(this, _AnnotationEditor_focusAC, \"f\") || !this.div) {\n    return;\n  }\n  __classPrivateFieldSet(this, _AnnotationEditor_focusAC, new AbortController(), \"f\");\n  const signal = this._uiManager.combinedSignal(__classPrivateFieldGet(this, _AnnotationEditor_focusAC, \"f\"));\n  this.div.addEventListener(\"focusin\", this.focusin.bind(this), {\n    signal\n  });\n  this.div.addEventListener(\"focusout\", this.focusout.bind(this), {\n    signal\n  });\n}, _AnnotationEditor_stopResizing = function _AnnotationEditor_stopResizing() {\n  // this.#isResizerEnabledForKeyboard = false;\n  // this.#setResizerTabIndex(-1);\n  // if (this.#savedDimensions) {\n  //     const { savedX, savedY, savedWidth, savedHeight } = this.#savedDimensions;\n  //     this.#addResizeToUndoStack(savedX, savedY, savedWidth, savedHeight);\n  //     this.#savedDimensions = null;\n  // }\n};\nAnnotationEditor.idManager = new IdManager(\"annotation_editor_id_\");\nAnnotationEditor._borderLineWidth = -1;\nAnnotationEditor._colorManager = new ColorManager();\nAnnotationEditor._zIndex = 1;\n// Time to wait (in ms) before sending the telemetry data.\n// We wait a bit to avoid sending too many requests when changing something\n// like the thickness of a line.\nAnnotationEditor._telemetryTimeout = 1000;\n// This class is used to fake an editor which has been deleted.\nclass FakeEditor extends AnnotationEditor {\n  constructor(params) {\n    super(params);\n    this.annotationElementId = params.annotationElementId;\n    this.deleted = true;\n  }\n  serialize() {\n    return {\n      id: this.annotationElementId,\n      deleted: true,\n      pageIndex: this.pageIndex\n    };\n  }\n}", "map": {"version": 3, "names": ["_AnnotationEditor_instances", "_a", "_AnnotationEditor_x", "_AnnotationEditor_y", "_AnnotationEditor_disabled", "_AnnotationEditor_keepAspectRatio", "_AnnotationEditor_focusAC", "_AnnotationEditor_hasBeenClicked", "_AnnotationEditor_initialPosition", "_AnnotationEditor_isEditing", "_AnnotationEditor_isInEditMode", "_AnnotationEditor_moveInDOMTimeout", "_AnnotationEditor_prevDragX", "_AnnotationEditor_prevDragY", "_AnnotationEditor_isDraggable", "_AnnotationEditor_zIndex", "_AnnotationEditor_translate", "_AnnotationEditor_rotatePoint", "_AnnotationEditor_selectOnPointerEvent", "_AnnotationEditor_setUpDragSession", "_AnnotationEditor_addFocusListeners", "_AnnotationEditor_stopResizing", "__classPrivateFieldGet", "__classPrivateFieldSet", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ColorManager", "bindEvents", "shadow", "FeatureTest", "AnnotationEditorType", "AnnotationEditor", "constructor", "parameters", "add", "parent", "id", "width", "height", "pageIndex", "name", "div", "annotationElementId", "_willKeepAspectRatio", "_structTreeParentId", "rotation", "pageRotation", "pageDimensions", "pageTranslation", "set", "isAttachedToDOM", "deleted", "_editToolbar", "_initialOptions", "Object", "create", "_isVisible", "_uiManager", "_focusEventsAllowed", "_l10nPromise", "_zIndex", "idManager", "uiManager", "isCentered", "rawDims", "pageWidth", "pageHeight", "pageX", "pageY", "viewport", "viewParameters", "parentDimensions", "x", "y", "value", "editorType", "getPrototypeOf", "_type", "_defaultLineColor", "_colorManager", "getHexCode", "deleteAnnotationElement", "editor", "fakeEditor", "FakeEditor", "getNextId", "addToAnnotationStorage", "initialize", "l10n", "options", "updateDefaultParams", "_value", "defaultPropertiesToUpdate", "isHandlingMimeForPasting", "mime", "paste", "item", "propertiesToUpdate", "_isDraggable", "_b", "classList", "toggle", "isEnterHandled", "center", "parentRotation", "fixAndSetPosition", "addCommands", "params", "<PERSON><PERSON><PERSON><PERSON>", "setInBackground", "style", "zIndex", "setInForeground", "setParent", "call", "focusin", "event", "setSelected", "focusout", "target", "relatedTarget", "closest", "preventDefault", "isMultipleSelection", "commitOr<PERSON><PERSON>ove", "isEmpty", "remove", "commit", "setAt", "tx", "ty", "screenToPageTranslation", "translate", "translateInPage", "scrollIntoView", "block", "drag", "parentWidth", "parentHeight", "xValue", "yValue", "getBoundingClientRect", "find<PERSON><PERSON><PERSON><PERSON><PERSON>", "Math", "floor", "bx", "by", "getBaseTranslation", "left", "toFixed", "top", "_hasBeenMoved", "_borderLineWidth", "_mustFixPosition", "undefined", "max", "min", "moveInDOM", "pageTranslationToScreen", "parentScale", "realScale", "scaledWidth", "scaledHeight", "isCSSRoundSupported", "round", "setDims", "fixDims", "getInitialTranslation", "addEditToolbar", "showEditorToolBar", "removeEditToolbar", "hideEditorToolBar", "getClientDimensions", "render", "document", "createElement", "setAttribute", "className", "tabIndex", "max<PERSON><PERSON><PERSON>", "maxHeight", "pointerdown", "isMac", "platform", "button", "ctrl<PERSON>ey", "clearTimeout", "setTimeout", "moveEditorInDOM", "_setParentAndPosition", "changeParent", "getRect", "scale", "shiftX", "shiftY", "Error", "getRectInCurrentCoords", "rect", "x1", "y1", "x2", "y2", "onceAdded", "enableEditMode", "disableEditMode", "isInEditMode", "needsToBeRebuilt", "rebuild", "rotate", "angle", "serialize", "_isForCopying", "_context", "deserialize", "data", "prototype", "hasBeenModified", "abort", "removeEditor", "isResizable", "makeResizable", "toolbarPosition", "_stopResizingWithKeyboard", "select", "unselect", "_c", "contains", "activeElement", "focus", "preventScroll", "updateParams", "type", "disableEditing", "enableEditing", "enterInEditMode", "getImageForAltText", "contentDiv", "isEditing", "setActiveEditor", "setAspectRatio", "aspectRatio", "MIN_SIZE", "canCreateNewEmptyEditor", "telemetryInitialData", "action", "telemetryFinalData", "_reportTelemetry", "show", "visible", "enable", "disable", "renderAnnotationElement", "annotation", "content", "container", "querySelector", "prepend", "nodeName", "canvas", "before", "resetAnnotationElement", "<PERSON><PERSON><PERSON><PERSON>", "WeakMap", "WeakSet", "highlightEditor", "freeTextEditor", "annotationEditorMode", "getMode", "HIGHLIGHT", "FREETEXT", "shift<PERSON>ey", "metaKey", "toggleSelected", "isSelected", "setUpDragSession", "ac", "AbortController", "signal", "combinedSignal", "clientX", "clientY", "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>", "e", "dragSelectedEditors", "window", "addEventListener", "passive", "capture", "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>", "endDragSession", "bind", "_telemetryTimeout"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-pdfviewer-common/dist/es/annotations/editors/annotation-editor.js"], "sourcesContent": ["/* Copyright 2022 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar _AnnotationEditor_instances, _a, _AnnotationEditor_x, _AnnotationEditor_y, _AnnotationEditor_disabled, _AnnotationEditor_keepAspectRatio, _AnnotationEditor_focusAC, _AnnotationEditor_hasBeenClicked, _AnnotationEditor_initialPosition, _AnnotationEditor_isEditing, _AnnotationEditor_isInEditMode, _AnnotationEditor_moveInDOMTimeout, _AnnotationEditor_prevDragX, _AnnotationEditor_prevDragY, _AnnotationEditor_isDraggable, _AnnotationEditor_zIndex, _AnnotationEditor_translate, _AnnotationEditor_rotatePoint, _AnnotationEditor_selectOnPointerEvent, _AnnotationEditor_setUpDragSession, _AnnotationEditor_addFocusListeners, _AnnotationEditor_stopResizing;\nimport { __classPrivateFieldGet, __classPrivateFieldSet } from \"tslib\";\n// import { AnnotationEditorType, shadow } from \"../../shared/utils\";\n// import { AnnotationEditorType } from \"../../shared/utils\";\n// import { FeatureTest } from \"../../shared/utils\";\nimport { IdManager } from \"../helpers/id-manager\";\nimport { ColorManager } from \"../helpers/color-manager\";\nimport { bindEvents } from \"../helpers/tools\";\nimport { shadow, FeatureTest } from \"pdfjs-dist/legacy/build/pdf.mjs\";\nimport { AnnotationEditorType } from \"../shared/utils\";\nexport class AnnotationEditor {\n    constructor(parameters) {\n        // if (this.constructor === AnnotationEditor) {\n        //     unreachable(\"Cannot initialize AnnotationEditor.\");\n        // }\n        _AnnotationEditor_instances.add(this);\n        // todo: properties\n        this.parent = null;\n        this.id = null;\n        this.width = null;\n        this.height = null;\n        this.pageIndex = 0;\n        this.name = \"\";\n        this.div = null;\n        this.annotationElementId = null;\n        this._willKeepAspectRatio = false;\n        // _initialOptions = {};\n        this._structTreeParentId = null;\n        this.rotation = 0;\n        this.pageRotation = 0;\n        this.pageDimensions = [];\n        this.pageTranslation = [];\n        // x = 0;\n        // y = 0;\n        _AnnotationEditor_x.set(this, 0);\n        _AnnotationEditor_y.set(this, 0);\n        this.isAttachedToDOM = false;\n        this.deleted = false;\n        // todo: end\n        // #accessibilityData = null;\n        // #allResizerDivs = null;\n        // #altText = null;\n        _AnnotationEditor_disabled.set(this, false);\n        _AnnotationEditor_keepAspectRatio.set(this, false);\n        // #resizersDiv = null;\n        // #savedDimensions = null;\n        _AnnotationEditor_focusAC.set(this, null);\n        // #focusedResizerName = \"\";\n        _AnnotationEditor_hasBeenClicked.set(this, false);\n        _AnnotationEditor_initialPosition.set(this, null);\n        _AnnotationEditor_isEditing.set(this, false);\n        _AnnotationEditor_isInEditMode.set(this, false);\n        // #isResizerEnabledForKeyboard = false;\n        _AnnotationEditor_moveInDOMTimeout.set(this, null);\n        _AnnotationEditor_prevDragX.set(this, 0);\n        _AnnotationEditor_prevDragY.set(this, 0);\n        // #telemetryTimeouts = null;\n        this._editToolbar = null;\n        this._initialOptions = Object.create(null);\n        this._isVisible = true;\n        this._uiManager = null;\n        this._focusEventsAllowed = true;\n        this._l10nPromise = null;\n        _AnnotationEditor_isDraggable.set(this, false);\n        _AnnotationEditor_zIndex.set(this, _a._zIndex++);\n        this.parent = parameters.parent;\n        this.id = parameters.id || _a.idManager.id;\n        this.width = this.height = null;\n        this.pageIndex = parameters.parent.pageIndex;\n        this.name = parameters.name;\n        this.div = null;\n        this._uiManager = parameters.uiManager;\n        this.annotationElementId = null;\n        this._willKeepAspectRatio = false;\n        this._initialOptions.isCentered = parameters.isCentered;\n        this._structTreeParentId = null;\n        const { rotation, rawDims: { pageWidth, pageHeight, pageX, pageY } } = this.parent.viewport;\n        this.rotation = rotation;\n        this.pageRotation =\n            (360 + rotation - this._uiManager.viewParameters.rotation) % 360;\n        this.pageDimensions = [pageWidth, pageHeight];\n        this.pageTranslation = [pageX, pageY];\n        const [width, height] = this.parentDimensions;\n        this.x = parameters.x / width;\n        this.y = parameters.y / height;\n        this.isAttachedToDOM = false;\n        this.deleted = false;\n    }\n    get x() {\n        return __classPrivateFieldGet(this, _AnnotationEditor_x, \"f\");\n    }\n    set x(value) {\n        __classPrivateFieldSet(this, _AnnotationEditor_x, value, \"f\");\n    }\n    get y() {\n        return __classPrivateFieldGet(this, _AnnotationEditor_y, \"f\");\n    }\n    set y(value) {\n        __classPrivateFieldSet(this, _AnnotationEditor_y, value, \"f\");\n    }\n    get editorType() {\n        return Object.getPrototypeOf(this).constructor._type;\n    }\n    static get _defaultLineColor() {\n        return shadow(this, \"_defaultLineColor\", this._colorManager.getHexCode(\"CanvasText\"));\n    }\n    static deleteAnnotationElement(editor) {\n        const fakeEditor = new FakeEditor({\n            id: editor.parent.getNextId(),\n            parent: editor.parent,\n            uiManager: editor._uiManager\n        });\n        fakeEditor.annotationElementId = editor.annotationElementId;\n        fakeEditor.deleted = true;\n        fakeEditor._uiManager.addToAnnotationStorage(fakeEditor);\n    }\n    static initialize(l10n, uiManager, options) {\n        if (!l10n || !uiManager || !options) {\n            /* no-empty */\n        }\n        // static initialize() {\n        // AnnotationEditor._l10nPromise ||= new Map(\n        //     [\n        //         \"pdfjs-editor-alt-text-button-label\",\n        //         \"pdfjs-editor-alt-text-edit-button-label\",\n        //         \"pdfjs-editor-alt-text-decorative-tooltip\",\n        //         \"pdfjs-editor-new-alt-text-added-button-label\",\n        //         \"pdfjs-editor-new-alt-text-missing-button-label\",\n        //         \"pdfjs-editor-new-alt-text-to-review-button-label\",\n        //         \"pdfjs-editor-resizer-label-topLeft\",\n        //         \"pdfjs-editor-resizer-label-topMiddle\",\n        //         \"pdfjs-editor-resizer-label-topRight\",\n        //         \"pdfjs-editor-resizer-label-middleRight\",\n        //         \"pdfjs-editor-resizer-label-bottomRight\",\n        //         \"pdfjs-editor-resizer-label-bottomMiddle\",\n        //         \"pdfjs-editor-resizer-label-bottomLeft\",\n        //         \"pdfjs-editor-resizer-label-middleLeft\",\n        //     ].map(str => [\n        //         str,\n        //         l10n.get(str.replaceAll(/([A-Z])/g, c => `-${c.toLowerCase()}`)),\n        //     ])\n        // );\n        // // The string isn't in the above list because the string has a parameter\n        // // (i.e. the guessed text) and we must pass it to the l10n function to get\n        // // the correct translation.\n        // AnnotationEditor._l10nPromise.set(\n        //     \"pdfjs-editor-new-alt-text-generated-alt-text-with-disclaimer\",\n        //     l10n.get.bind(\n        //         l10n,\n        //         \"pdfjs-editor-new-alt-text-generated-alt-text-with-disclaimer\"\n        //     )\n        // );\n        // if (options?.strings) {\n        //     for (const str of options.strings) {\n        //         AnnotationEditor._l10nPromise.set(str, l10n.get(str));\n        //     }\n        // }\n        // if (AnnotationEditor._borderLineWidth !== -1) {\n        //     return;\n        // }\n        // const style = getComputedStyle(document.documentElement);\n        // AnnotationEditor._borderLineWidth =\n        //     parseFloat(style.getPropertyValue(\"--outline-width\")) || 0;\n    }\n    /**\n     * Update the default parameters for this type of editor.\n     * @param {number} _type\n     * @param {*} _value\n     */\n    static updateDefaultParams(_type, _value) {\n        // if (!_type || !_value) {\n        // }\n    }\n    /**\n     * Get the default properties to set in the UI for this type of editor.\n     * @returns {Array}\n     */\n    static get defaultPropertiesToUpdate() {\n        return [];\n    }\n    /**\n     * Check if this kind of editor is able to handle the given mime type for\n     * pasting.\n     * @param {string} mime\n     * @returns {boolean}\n     */\n    static isHandlingMimeForPasting(mime) {\n        if (!mime) {\n            return;\n        }\n        return false;\n    }\n    /**\n     * Extract the data from the clipboard item and delegate the creation of the\n     * editor to the parent.\n     * @param {DataTransferItem} item\n     * @param {AnnotationEditorLayer} parent\n     */\n    static paste(item, parent) {\n        if (!item || !parent) {\n            /* no-empty */\n        }\n        // unreachable(\"Not implemented\");\n    }\n    /**\n     * Get the properties to update in the UI for this editor.\n     * @returns {Array}\n     */\n    get propertiesToUpdate() {\n        return [];\n    }\n    get _isDraggable() {\n        return __classPrivateFieldGet(this, _AnnotationEditor_isDraggable, \"f\");\n    }\n    set _isDraggable(value) {\n        var _b;\n        __classPrivateFieldSet(this, _AnnotationEditor_isDraggable, value, \"f\");\n        // this.div?.classList.toggle(\"draggable\", value);\n        (_b = this.div) === null || _b === void 0 ? void 0 : _b.classList.toggle(\"k-draggable\", value);\n    }\n    /**\n     * @returns {boolean} true if the editor handles the Enter key itself.\n     */\n    get isEnterHandled() {\n        return true;\n    }\n    center() {\n        const [pageWidth, pageHeight] = this.pageDimensions;\n        switch (this.parentRotation) {\n            case 90:\n                this.x -= (this.height * pageHeight) / (pageWidth * 2);\n                this.y += (this.width * pageWidth) / (pageHeight * 2);\n                break;\n            case 180:\n                this.x += this.width / 2;\n                this.y += this.height / 2;\n                break;\n            case 270:\n                this.x += (this.height * pageHeight) / (pageWidth * 2);\n                this.y -= (this.width * pageWidth) / (pageHeight * 2);\n                break;\n            default:\n                this.x -= this.width / 2;\n                this.y -= this.height / 2;\n                break;\n        }\n        this.fixAndSetPosition();\n    }\n    /**\n     * Add some commands into the CommandManager (undo/redo stuff).\n     * @param {Object} params\n     */\n    addCommands(params) {\n        this._uiManager.addCommands(params);\n    }\n    get currentLayer() {\n        return this._uiManager.currentLayer;\n    }\n    /**\n     * This editor will be behind the others.\n     */\n    setInBackground() {\n        this.div.style.zIndex = 0;\n    }\n    /**\n     * This editor will be in the foreground.\n     */\n    setInForeground() {\n        this.div.style.zIndex = __classPrivateFieldGet(this, _AnnotationEditor_zIndex, \"f\");\n    }\n    setParent(parent) {\n        if (parent !== null) {\n            this.pageIndex = parent.pageIndex;\n            this.pageDimensions = parent.pageDimensions;\n        }\n        else {\n            // The editor is being removed from the DOM, so we need to stop resizing.\n            __classPrivateFieldGet(this, _AnnotationEditor_instances, \"m\", _AnnotationEditor_stopResizing).call(this);\n        }\n        this.parent = parent;\n    }\n    /**\n     * onfocus callback.\n     */\n    focusin(event) {\n        if (!event) {\n            return;\n        }\n        if (!this._focusEventsAllowed) {\n            return;\n        }\n        if (!__classPrivateFieldGet(this, _AnnotationEditor_hasBeenClicked, \"f\")) {\n            this.parent.setSelected(this);\n        }\n        else {\n            __classPrivateFieldSet(this, _AnnotationEditor_hasBeenClicked, false, \"f\");\n        }\n    }\n    /**\n     * onblur callback.\n     * @param {FocusEvent} event\n     */\n    focusout(event) {\n        var _b;\n        if (!this._focusEventsAllowed) {\n            return;\n        }\n        if (!this.isAttachedToDOM) {\n            return;\n        }\n        // In case of focusout, the relatedTarget is the element which\n        // is grabbing the focus.\n        // So if the related target is an element under the div for this\n        // editor, then the editor isn't unactive.\n        const target = event.relatedTarget;\n        if (target === null || target === void 0 ? void 0 : target.closest(`#${this.id}`)) {\n            return;\n        }\n        event.preventDefault();\n        if (!((_b = this.parent) === null || _b === void 0 ? void 0 : _b.isMultipleSelection)) {\n            this.commitOrRemove();\n        }\n    }\n    commitOrRemove() {\n        if (this.isEmpty()) {\n            this.remove();\n        }\n        else {\n            this.commit();\n        }\n    }\n    /**\n     * Commit the data contained in this editor.\n     */\n    commit() {\n        this.addToAnnotationStorage();\n    }\n    addToAnnotationStorage() {\n        this._uiManager.addToAnnotationStorage(this);\n    }\n    /**\n     * Set the editor position within its parent.\n     * @param {number} x\n     * @param {number} y\n     * @param {number} tx - x-translation in screen coordinates.\n     * @param {number} ty - y-translation in screen coordinates.\n     */\n    setAt(x, y, tx, ty) {\n        const [width, height] = this.parentDimensions;\n        [tx, ty] = this.screenToPageTranslation(tx, ty);\n        this.x = (x + tx) / width;\n        this.y = (y + ty) / height;\n        this.fixAndSetPosition();\n    }\n    /**\n     * Translate the editor position within its parent.\n     * @param {number} x - x-translation in screen coordinates.\n     * @param {number} y - y-translation in screen coordinates.\n     */\n    translate(x, y) {\n        // We don't change the initial position because the move here hasn't been\n        // done by the user.\n        __classPrivateFieldGet(this, _AnnotationEditor_instances, \"m\", _AnnotationEditor_translate).call(this, this.parentDimensions, x, y);\n    }\n    /**\n     * Translate the editor position within its page and adjust the scroll\n     * in order to have the editor in the view.\n     * @param {number} x - x-translation in page coordinates.\n     * @param {number} y - y-translation in page coordinates.\n     */\n    translateInPage(x, y) {\n        __classPrivateFieldSet(this, _AnnotationEditor_initialPosition, __classPrivateFieldGet(this, _AnnotationEditor_initialPosition, \"f\") || [this.x, this.y], \"f\");\n        __classPrivateFieldGet(this, _AnnotationEditor_instances, \"m\", _AnnotationEditor_translate).call(this, this.pageDimensions, x, y);\n        this.div.scrollIntoView({ block: \"nearest\" });\n    }\n    drag(tx, ty) {\n        __classPrivateFieldSet(this, _AnnotationEditor_initialPosition, __classPrivateFieldGet(this, _AnnotationEditor_initialPosition, \"f\") || [this.x, this.y], \"f\");\n        const [parentWidth, parentHeight] = this.parentDimensions;\n        this.x += tx / parentWidth;\n        this.y += ty / parentHeight;\n        if (this.parent && (this.x < 0 || this.x > 1 || this.y < 0 || this.y > 1)) {\n            // It's possible to not have a parent: for example, when the user is\n            // dragging all the selected editors but this one on a page which has been\n            // destroyed.\n            // It's why we need to check for it. In such a situation, it isn't really\n            // a problem to not find a new parent: it's something which is related to\n            // what the user is seeing, hence it depends on how pages are layed out.\n            // The element will be outside of its parent so change the parent.\n            const { x: xValue, y: yValue } = this.div.getBoundingClientRect();\n            if (this.parent.findNewParent(this, xValue, yValue)) {\n                this.x -= Math.floor(this.x);\n                this.y -= Math.floor(this.y);\n            }\n        }\n        // The editor can be moved wherever the user wants, so we don't need to fix\n        // the position: it'll be done when the user will release the mouse button.\n        let { x, y } = this;\n        const [bx, by] = this.getBaseTranslation();\n        x += bx;\n        y += by;\n        this.div.style.left = `${(100 * x).toFixed(2)}%`;\n        this.div.style.top = `${(100 * y).toFixed(2)}%`;\n        this.div.scrollIntoView({ block: \"nearest\" });\n    }\n    get _hasBeenMoved() {\n        return (!!__classPrivateFieldGet(this, _AnnotationEditor_initialPosition, \"f\") &&\n            (__classPrivateFieldGet(this, _AnnotationEditor_initialPosition, \"f\")[0] !== this.x ||\n                __classPrivateFieldGet(this, _AnnotationEditor_initialPosition, \"f\")[1] !== this.y));\n    }\n    /**\n     * Get the translation to take into account the editor border.\n     * The CSS engine positions the element by taking the border into account so\n     * we must apply the opposite translation to have the editor in the right\n     * position.\n     * @returns {Array<number>}\n     */\n    getBaseTranslation() {\n        const [parentWidth, parentHeight] = this.parentDimensions;\n        const { _borderLineWidth } = _a;\n        const x = _borderLineWidth / parentWidth;\n        const y = _borderLineWidth / parentHeight;\n        switch (this.rotation) {\n            case 90:\n                return [-x, y];\n            case 180:\n                return [x, y];\n            case 270:\n                return [x, -y];\n            default:\n                return [-x, -y];\n        }\n    }\n    /**\n     * @returns {boolean} true if position must be fixed (i.e. make the x and y\n     * living in the page).\n     */\n    get _mustFixPosition() {\n        return true;\n    }\n    /**\n     * Fix the position of the editor in order to keep it inside its parent page.\n     * @param {number} [rotation] - the rotation of the page.\n     */\n    fixAndSetPosition(rotation = this.rotation) {\n        if (rotation === undefined) {\n            return;\n        }\n        const [pageWidth, pageHeight] = this.pageDimensions;\n        let { x, y, width, height } = this;\n        width *= pageWidth;\n        height *= pageHeight;\n        x *= pageWidth;\n        y *= pageHeight;\n        if (this._mustFixPosition) {\n            switch (rotation) {\n                case 0:\n                    x = Math.max(0, Math.min(pageWidth - width, x));\n                    y = Math.max(0, Math.min(pageHeight - height, y));\n                    break;\n                case 90:\n                    x = Math.max(0, Math.min(pageWidth - height, x));\n                    y = Math.min(pageHeight, Math.max(width, y));\n                    break;\n                case 180:\n                    x = Math.min(pageWidth, Math.max(width, x));\n                    y = Math.min(pageHeight, Math.max(height, y));\n                    break;\n                case 270:\n                    x = Math.min(pageWidth, Math.max(height, x));\n                    y = Math.max(0, Math.min(pageHeight - width, y));\n                    break;\n                default: break;\n            }\n        }\n        this.x = x /= pageWidth;\n        this.y = y /= pageHeight;\n        const [bx, by] = this.getBaseTranslation();\n        x += bx;\n        y += by;\n        const { style } = this.div;\n        style.left = `${(100 * x).toFixed(2)}%`;\n        style.top = `${(100 * y).toFixed(2)}%`;\n        this.moveInDOM();\n    }\n    /**\n     * Convert a screen translation into a page one.\n     * @param {number} x\n     * @param {number} y\n     */\n    screenToPageTranslation(x, y) {\n        return __classPrivateFieldGet(_a, _a, \"m\", _AnnotationEditor_rotatePoint).call(_a, x, y, this.parentRotation);\n    }\n    /**\n     * Convert a page translation into a screen one.\n     * @param {number} x\n     * @param {number} y\n     */\n    pageTranslationToScreen(x, y) {\n        return __classPrivateFieldGet(_a, _a, \"m\", _AnnotationEditor_rotatePoint).call(_a, x, y, 360 - this.parentRotation);\n    }\n    // #getRotationMatrix(rotation) {\n    //     switch (rotation) {\n    //         case 90: {\n    //             const [pageWidth, pageHeight] = this.pageDimensions;\n    //             return [0, -pageWidth / pageHeight, pageHeight / pageWidth, 0];\n    //         }\n    //         case 180:\n    //             return [-1, 0, 0, -1];\n    //         case 270: {\n    //             const [pageWidth, pageHeight] = this.pageDimensions;\n    //             return [0, pageWidth / pageHeight, -pageHeight / pageWidth, 0];\n    //         }\n    //         default:\n    //             return [1, 0, 0, 1];\n    //     }\n    // }\n    get parentScale() {\n        return this._uiManager.viewParameters.realScale;\n    }\n    get parentRotation() {\n        return (this._uiManager.viewParameters.rotation + this.pageRotation) % 360;\n    }\n    get parentDimensions() {\n        const { parentScale, pageDimensions: [pageWidth, pageHeight] } = this;\n        const scaledWidth = pageWidth * parentScale;\n        const scaledHeight = pageHeight * parentScale;\n        return FeatureTest.isCSSRoundSupported\n            ? [Math.round(scaledWidth), Math.round(scaledHeight)]\n            : [scaledWidth, scaledHeight];\n    }\n    /**\n     * Set the dimensions of this editor.\n     * @param {number} width\n     * @param {number} height\n     */\n    setDims(width, height) {\n        const [parentWidth, parentHeight] = this.parentDimensions;\n        this.div.style.width = `${((100 * width) / parentWidth).toFixed(2)}%`;\n        if (!__classPrivateFieldGet(this, _AnnotationEditor_keepAspectRatio, \"f\")) {\n            this.div.style.height = `${((100 * height) / parentHeight).toFixed(2)}%`;\n        }\n    }\n    fixDims() {\n        //     const { style } = this.div;\n        //     const { height, width } = style;\n        //     const widthPercent = width.endsWith(\"%\");\n        //     const heightPercent = !this.#keepAspectRatio && height.endsWith(\"%\");\n        //     if (widthPercent && heightPercent) {\n        //         return;\n        //     }\n        //     const [parentWidth, parentHeight] = this.parentDimensions;\n        //     if (!widthPercent) {\n        //         style.width = `${((100 * parseFloat(width)) / parentWidth).toFixed(2)}%`;\n        //     }\n        //     if (!this.#keepAspectRatio && !heightPercent) {\n        //         style.height = `${((100 * parseFloat(height)) / parentHeight).toFixed(\n        //             2\n        //         )}%`;\n        //     }\n    }\n    /**\n     * Get the translation used to position this editor when it's created.\n     * @returns {Array<number>}\n     */\n    getInitialTranslation() {\n        return [0, 0];\n    }\n    // #createResizers() {\n    //     if (this.#resizersDiv) {\n    //         return;\n    //     }\n    //     this.#resizersDiv = document.createElement(\"div\");\n    //     this.#resizersDiv.classList.add(\"resizers\");\n    //     // When the resizers are used with the keyboard, they're focusable, hence\n    //     // we want to have them in this order (top left, top middle, top right, ...)\n    //     // in the DOM to have the focus order correct.\n    //     const classes = this._willKeepAspectRatio\n    //         ? [\"topLeft\", \"topRight\", \"bottomRight\", \"bottomLeft\"]\n    //         : [\n    //             \"topLeft\",\n    //             \"topMiddle\",\n    //             \"topRight\",\n    //             \"middleRight\",\n    //             \"bottomRight\",\n    //             \"bottomMiddle\",\n    //             \"bottomLeft\",\n    //             \"middleLeft\",\n    //         ];\n    //     const signal = this._uiManager._signal;\n    //     for (const name of classes) {\n    //         const div = document.createElement(\"div\");\n    //         this.#resizersDiv.append(div);\n    //         div.classList.add(\"resizer\", name);\n    //         div.setAttribute(\"data-resizer-name\", name);\n    //         div.addEventListener(\n    //             \"pointerdown\",\n    //             this.#resizerPointerdown.bind(this, name),\n    //             { signal }\n    //         );\n    //         div.addEventListener(\"contextmenu\", noContextMenu, { signal });\n    //         div.tabIndex = -1;\n    //     }\n    //     this.div.prepend(this.#resizersDiv);\n    // }\n    // #resizerPointerdown(name, event) {\n    //     event.preventDefault();\n    //     const { isMac } = FeatureTest.platform;\n    //     if (event.button !== 0 || (event.ctrlKey && isMac)) {\n    //         return;\n    //     }\n    //     this.#altText?.toggle(false);\n    //     const savedDraggable = this._isDraggable;\n    //     this._isDraggable = false;\n    //     const ac = new AbortController();\n    //     const signal = this._uiManager.combinedSignal(ac);\n    //     this.parent.togglePointerEvents(false);\n    //     window.addEventListener(\n    //         \"pointermove\",\n    //         this.#resizerPointermove.bind(this, name),\n    //         { passive: true, capture: true, signal }\n    //     );\n    //     window.addEventListener(\"contextmenu\", noContextMenu, { signal });\n    //     const savedX = this.x;\n    //     const savedY = this.y;\n    //     const savedWidth = this.width;\n    //     const savedHeight = this.height;\n    //     const savedParentCursor = this.parent.div.style.cursor;\n    //     const savedCursor = this.div.style.cursor;\n    //     this.div.style.cursor = this.parent.div.style.cursor =\n    //         window.getComputedStyle(event.target).cursor;\n    //     const pointerUpCallback = () => {\n    //         ac.abort();\n    //         this.parent.togglePointerEvents(true);\n    //         this.#altText?.toggle(true);\n    //         this._isDraggable = savedDraggable;\n    //         this.parent.div.style.cursor = savedParentCursor;\n    //         this.div.style.cursor = savedCursor;\n    //         this.#addResizeToUndoStack(savedX, savedY, savedWidth, savedHeight);\n    //     };\n    //     window.addEventListener(\"pointerup\", pointerUpCallback, { signal });\n    //     // If the user switches to another window (with alt+tab), then we end the\n    //     // resize session.\n    //     window.addEventListener(\"blur\", pointerUpCallback, { signal });\n    // }\n    // #addResizeToUndoStack(savedX, savedY, savedWidth, savedHeight) {\n    //     const newX = this.x;\n    //     const newY = this.y;\n    //     const newWidth = this.width;\n    //     const newHeight = this.height;\n    //     if (\n    //         newX === savedX &&\n    //         newY === savedY &&\n    //         newWidth === savedWidth &&\n    //         newHeight === savedHeight\n    //     ) {\n    //         return;\n    //     }\n    //     this.addCommands({\n    //         cmd: () => {\n    //             this.width = newWidth;\n    //             this.height = newHeight;\n    //             this.x = newX;\n    //             this.y = newY;\n    //             const [parentWidth, parentHeight] = this.parentDimensions;\n    //             this.setDims(parentWidth * newWidth, parentHeight * newHeight);\n    //             this.fixAndSetPosition();\n    //         },\n    //         undo: () => {\n    //             this.width = savedWidth;\n    //             this.height = savedHeight;\n    //             this.x = savedX;\n    //             this.y = savedY;\n    //             const [parentWidth, parentHeight] = this.parentDimensions;\n    //             this.setDims(parentWidth * savedWidth, parentHeight * savedHeight);\n    //             this.fixAndSetPosition();\n    //         },\n    //         mustExec: true,\n    //     });\n    // }\n    // #resizerPointermove(name, event) {\n    //     const [parentWidth, parentHeight] = this.parentDimensions;\n    //     const savedX = this.x;\n    //     const savedY = this.y;\n    //     const savedWidth = this.width;\n    //     const savedHeight = this.height;\n    //     const minWidth = AnnotationEditor.MIN_SIZE / parentWidth;\n    //     const minHeight = AnnotationEditor.MIN_SIZE / parentHeight;\n    //     // 10000 because we multiply by 100 and use toFixed(2) in fixAndSetPosition.\n    //     // Without rounding, the positions of the corners other than the top left\n    //     // one can be slightly wrong.\n    //     const round = x => Math.round(x * 10000) / 10000;\n    //     const rotationMatrix = this.#getRotationMatrix(this.rotation);\n    //     const transf = (x, y) => [\n    //         rotationMatrix[0] * x + rotationMatrix[2] * y,\n    //         rotationMatrix[1] * x + rotationMatrix[3] * y,\n    //     ];\n    //     const invRotationMatrix = this.#getRotationMatrix(360 - this.rotation);\n    //     const invTransf = (x, y) => [\n    //         invRotationMatrix[0] * x + invRotationMatrix[2] * y,\n    //         invRotationMatrix[1] * x + invRotationMatrix[3] * y,\n    //     ];\n    //     let getPoint;\n    //     let getOpposite;\n    //     let isDiagonal = false;\n    //     let isHorizontal = false;\n    //     switch (name) {\n    //         case \"topLeft\":\n    //             isDiagonal = true;\n    //             getPoint = (w, h) => [0, 0];\n    //             getOpposite = (w, h) => [w, h];\n    //             break;\n    //         case \"topMiddle\":\n    //             getPoint = (w, h) => [w / 2, 0];\n    //             getOpposite = (w, h) => [w / 2, h];\n    //             break;\n    //         case \"topRight\":\n    //             isDiagonal = true;\n    //             getPoint = (w, h) => [w, 0];\n    //             getOpposite = (w, h) => [0, h];\n    //             break;\n    //         case \"middleRight\":\n    //             isHorizontal = true;\n    //             getPoint = (w, h) => [w, h / 2];\n    //             getOpposite = (w, h) => [0, h / 2];\n    //             break;\n    //         case \"bottomRight\":\n    //             isDiagonal = true;\n    //             getPoint = (w, h) => [w, h];\n    //             getOpposite = (w, h) => [0, 0];\n    //             break;\n    //         case \"bottomMiddle\":\n    //             getPoint = (w, h) => [w / 2, h];\n    //             getOpposite = (w, h) => [w / 2, 0];\n    //             break;\n    //         case \"bottomLeft\":\n    //             isDiagonal = true;\n    //             getPoint = (w, h) => [0, h];\n    //             getOpposite = (w, h) => [w, 0];\n    //             break;\n    //         case \"middleLeft\":\n    //             isHorizontal = true;\n    //             getPoint = (w, h) => [0, h / 2];\n    //             getOpposite = (w, h) => [w, h / 2];\n    //             break;\n    //     }\n    //     const point = getPoint(savedWidth, savedHeight);\n    //     const oppositePoint = getOpposite(savedWidth, savedHeight);\n    //     let transfOppositePoint = transf(...oppositePoint);\n    //     const oppositeX = round(savedX + transfOppositePoint[0]);\n    //     const oppositeY = round(savedY + transfOppositePoint[1]);\n    //     let ratioX = 1;\n    //     let ratioY = 1;\n    //     let [deltaX, deltaY] = this.screenToPageTranslation(\n    //         event.movementX,\n    //         event.movementY\n    //     );\n    //     [deltaX, deltaY] = invTransf(deltaX / parentWidth, deltaY / parentHeight);\n    //     if (isDiagonal) {\n    //         const oldDiag = Math.hypot(savedWidth, savedHeight);\n    //         ratioX = ratioY = Math.max(\n    //             Math.min(\n    //                 Math.hypot(\n    //                     oppositePoint[0] - point[0] - deltaX,\n    //                     oppositePoint[1] - point[1] - deltaY\n    //                 ) / oldDiag,\n    //                 // Avoid the editor to be larger than the page.\n    //                 1 / savedWidth,\n    //                 1 / savedHeight\n    //             ),\n    //             // Avoid the editor to be smaller than the minimum size.\n    //             minWidth / savedWidth,\n    //             minHeight / savedHeight\n    //         );\n    //     } else if (isHorizontal) {\n    //         ratioX =\n    //             Math.max(\n    //                 minWidth,\n    //                 Math.min(1, Math.abs(oppositePoint[0] - point[0] - deltaX))\n    //             ) / savedWidth;\n    //     } else {\n    //         ratioY =\n    //             Math.max(\n    //                 minHeight,\n    //                 Math.min(1, Math.abs(oppositePoint[1] - point[1] - deltaY))\n    //             ) / savedHeight;\n    //     }\n    //     const newWidth = round(savedWidth * ratioX);\n    //     const newHeight = round(savedHeight * ratioY);\n    //     transfOppositePoint = transf(...getOpposite(newWidth, newHeight));\n    //     const newX = oppositeX - transfOppositePoint[0];\n    //     const newY = oppositeY - transfOppositePoint[1];\n    //     this.width = newWidth;\n    //     this.height = newHeight;\n    //     this.x = newX;\n    //     this.y = newY;\n    //     this.setDims(parentWidth * newWidth, parentHeight * newHeight);\n    //     this.fixAndSetPosition();\n    // }\n    // /**\n    //  * Called when the alt text dialog is closed.\n    //  */\n    // altTextFinish() {\n    //     this.#altText?.finish();\n    // }\n    // /**\n    //  * Add a toolbar for this editor.\n    //  * @returns {Promise<EditorToolbar|null>}\n    //  */\n    // async addEditToolbar() {\n    addEditToolbar() {\n        var _b;\n        (_b = this._uiManager) === null || _b === void 0 ? void 0 : _b.showEditorToolBar(this.div);\n        // if (this._editToolbar || this.#isInEditMode) {\n        //     return this._editToolbar;\n        // }\n        // this._editToolbar = new EditorToolbar(this);\n        // this.div.append(this._editToolbar.render());\n        // if (this.#altText) {\n        //     this._editToolbar.addAltTextButton(await this.#altText.render());\n        // }\n        // return this._editToolbar;\n    }\n    removeEditToolbar() {\n        this._uiManager.hideEditorToolBar();\n        // if (!this._editToolbar) {\n        //     return;\n        // }\n        // this._editToolbar.remove();\n        // this._editToolbar = null;\n        // // We destroy the alt text but we don't null it because we want to be able\n        // // to restore it in case the user undoes the deletion.\n        // this.#altText?.destroy();\n    }\n    getClientDimensions() {\n        return this.div.getBoundingClientRect();\n    }\n    // async addAltTextButton() {\n    //     if (this.#altText) {\n    //         return;\n    //     }\n    //     AltText.initialize(AnnotationEditor._l10nPromise);\n    //     this.#altText = new AltText(this);\n    //     if (this.#accessibilityData) {\n    //         this.#altText.data = this.#accessibilityData;\n    //         this.#accessibilityData = null;\n    //     }\n    //     await this.addEditToolbar();\n    // }\n    // get altTextData() {\n    //     return this.#altText?.data;\n    // }\n    // /**\n    //  * Set the alt text data.\n    //  */\n    // set altTextData(data) {\n    //     if (!this.#altText) {\n    //         return;\n    //     }\n    //     this.#altText.data = data;\n    // }\n    // get guessedAltText() {\n    //     return this.#altText?.guessedText;\n    // }\n    // async setGuessedAltText(text) {\n    //     await this.#altText?.setGuessedText(text);\n    // }\n    // serializeAltText(isForCopying) {\n    //     return this.#altText?.serialize(isForCopying);\n    // }\n    // hasAltText() {\n    //     return !!this.#altText && !this.#altText.isEmpty();\n    // }\n    // hasAltTextData() {\n    //     return this.#altText?.hasData() ?? false;\n    // }\n    /**\n     * Render this editor in a div.\n     * @returns {HTMLDivElement | null}\n     */\n    render() {\n        this.div = document.createElement(\"div\");\n        this.div.setAttribute(\"data-editor-rotation\", (360 - this.rotation) % 360);\n        this.div.className = this.name;\n        this.div.setAttribute(\"id\", this.id);\n        this.div.tabIndex = __classPrivateFieldGet(this, _AnnotationEditor_disabled, \"f\") ? -1 : 0;\n        if (!this._isVisible) {\n            // this.div.classList.add(\"hidden\");\n            this.div.classList.add(\"k-hidden\");\n        }\n        this.setInForeground();\n        __classPrivateFieldGet(this, _AnnotationEditor_instances, \"m\", _AnnotationEditor_addFocusListeners).call(this);\n        const [parentWidth, parentHeight] = this.parentDimensions;\n        if (this.parentRotation % 180 !== 0) {\n            this.div.style.maxWidth = `${((100 * parentHeight) / parentWidth).toFixed(2)}%`;\n            this.div.style.maxHeight = `${((100 * parentWidth) /\n                parentHeight).toFixed(2)}%`;\n        }\n        const [tx, ty] = this.getInitialTranslation();\n        this.translate(tx, ty);\n        bindEvents(this, this.div, [\"pointerdown\"]);\n        return this.div;\n    }\n    /**\n     * Onpointerdown callback.\n     * @param {PointerEvent} event\n     */\n    pointerdown(event) {\n        var _b;\n        const { isMac } = FeatureTest.platform;\n        if (event.button !== 0 || (event.ctrlKey && isMac)) {\n            // Avoid to focus this editor because of a non-left click.\n            event.preventDefault();\n            return;\n        }\n        __classPrivateFieldSet(this, _AnnotationEditor_hasBeenClicked, true, \"f\");\n        if (this._isDraggable) {\n            (_b = this._uiManager) === null || _b === void 0 ? void 0 : _b.hideEditorToolBar();\n            __classPrivateFieldGet(this, _AnnotationEditor_instances, \"m\", _AnnotationEditor_setUpDragSession).call(this, event);\n            return;\n        }\n        __classPrivateFieldGet(this, _AnnotationEditor_instances, \"m\", _AnnotationEditor_selectOnPointerEvent).call(this, event);\n    }\n    moveInDOM() {\n        // Moving the editor in the DOM can be expensive, so we wait a bit before.\n        // It's important to not block the UI (for example when changing the font\n        // size in a FreeText).\n        if (__classPrivateFieldGet(this, _AnnotationEditor_moveInDOMTimeout, \"f\")) {\n            clearTimeout(__classPrivateFieldGet(this, _AnnotationEditor_moveInDOMTimeout, \"f\"));\n        }\n        __classPrivateFieldSet(this, _AnnotationEditor_moveInDOMTimeout, setTimeout(() => {\n            var _b;\n            __classPrivateFieldSet(this, _AnnotationEditor_moveInDOMTimeout, null, \"f\");\n            (_b = this.parent) === null || _b === void 0 ? void 0 : _b.moveEditorInDOM(this);\n        }, 0), \"f\");\n    }\n    _setParentAndPosition(parent, x, y) {\n        parent.changeParent(this);\n        this.x = x;\n        this.y = y;\n        this.fixAndSetPosition();\n    }\n    /*\n     * Convert the current rect into a page one.\n     * @param {number} tx - x-translation in screen coordinates.\n     * @param {number} ty - y-translation in screen coordinates.\n     * @param {number} [rotation] - the rotation of the page.\n     */\n    getRect(tx, ty, rotation = this.rotation) {\n        const scale = this.parentScale;\n        const [pageWidth, pageHeight] = this.pageDimensions;\n        const [pageX, pageY] = this.pageTranslation;\n        const shiftX = tx / scale;\n        const shiftY = ty / scale;\n        const x = this.x * pageWidth;\n        const y = this.y * pageHeight;\n        const width = this.width * pageWidth;\n        const height = this.height * pageHeight;\n        switch (rotation) {\n            case 0:\n                return [\n                    x + shiftX + pageX,\n                    pageHeight - y - shiftY - height + pageY,\n                    x + shiftX + width + pageX,\n                    pageHeight - y - shiftY + pageY\n                ];\n            case 90:\n                return [\n                    x + shiftY + pageX,\n                    pageHeight - y + shiftX + pageY,\n                    x + shiftY + height + pageX,\n                    pageHeight - y + shiftX + width + pageY\n                ];\n            case 180:\n                return [\n                    x - shiftX - width + pageX,\n                    pageHeight - y + shiftY + pageY,\n                    x - shiftX + pageX,\n                    pageHeight - y + shiftY + height + pageY\n                ];\n            case 270:\n                return [\n                    x - shiftY - height + pageX,\n                    pageHeight - y - shiftX - width + pageY,\n                    x - shiftY + pageX,\n                    pageHeight - y - shiftX + pageY\n                ];\n            default:\n                throw new Error(\"Invalid rotation\");\n        }\n    }\n    getRectInCurrentCoords(rect, pageHeight) {\n        const [x1, y1, x2, y2] = rect;\n        const width = x2 - x1;\n        const height = y2 - y1;\n        switch (this.rotation) {\n            case 0:\n                return [x1, pageHeight - y2, width, height];\n            case 90:\n                return [x1, pageHeight - y1, height, width];\n            case 180:\n                return [x2, pageHeight - y1, width, height];\n            case 270:\n                return [x2, pageHeight - y2, height, width];\n            default:\n                // throw new Error(\"Invalid rotation\");\n                break;\n        }\n    }\n    // /**\n    //  * Executed once this editor has been rendered.\n    //  */\n    onceAdded() { }\n    // /**\n    //  * Check if the editor contains something.\n    //  * @returns {boolean}\n    //  */\n    isEmpty() {\n        return false;\n    }\n    /**\n     * Enable edit mode.\n     */\n    enableEditMode() {\n        __classPrivateFieldSet(this, _AnnotationEditor_isInEditMode, true, \"f\");\n    }\n    /**\n     * Disable edit mode.\n     */\n    disableEditMode() {\n        __classPrivateFieldSet(this, _AnnotationEditor_isInEditMode, false, \"f\");\n    }\n    /**\n     * Check if the editor is edited.\n     * @returns {boolean}\n     */\n    isInEditMode() {\n        return __classPrivateFieldGet(this, _AnnotationEditor_isInEditMode, \"f\");\n    }\n    // /**\n    //  * If it returns true, then this editor handles the keyboard\n    //  * events itself.\n    //  * @returns {boolean}\n    //  */\n    // shouldGetKeyboardEvents() {\n    //     return this.#isResizerEnabledForKeyboard;\n    // }\n    /**\n     * Check if this editor needs to be rebuilt or not.\n     * @returns {boolean}\n     */\n    needsToBeRebuilt() {\n        return this.div && !this.isAttachedToDOM;\n    }\n    /**\n     * Rebuild the editor in case it has been removed on undo.\n     *\n     * To implement in subclasses.\n     */\n    rebuild() {\n        __classPrivateFieldGet(this, _AnnotationEditor_instances, \"m\", _AnnotationEditor_addFocusListeners).call(this);\n    }\n    /**\n     * Rotate the editor.\n     * @param {number} angle\n     */\n    rotate(angle) {\n        if (angle === undefined || angle === null) {\n            /* no-empty */\n        }\n    }\n    /**\n     * Serialize the editor.\n     * The result of the serialization will be used to construct a\n     * new annotation to add to the pdf document.\n     *\n     * To implement in subclasses.\n     * @param {boolean} [_isForCopying]\n     * @param {Object | null} [_context]\n     * @returns {Object | null}\n     */\n    serialize(_isForCopying = false, _context = null) {\n        // serialize() {\n    }\n    /**\n     * Deserialize the editor.\n     * The result of the deserialization is a new editor.\n     *\n     * @param {Object} data\n     * @param {AnnotationEditorLayer} parent\n     * @param {AnnotationEditorUIManager} uiManager\n     * @returns {AnnotationEditor | null}\n     */\n    static deserialize(data, parent, uiManager) {\n        // @ts-expect-error TS(2556):\n        const editor = new this.prototype.constructor({\n            parent,\n            id: parent.getNextId(),\n            uiManager\n        });\n        editor.rotation = data.rotation;\n        // editor.#accessibilityData = data.accessibilityData;\n        const [pageWidth, pageHeight] = editor.pageDimensions;\n        const [x, y, width, height] = editor.getRectInCurrentCoords(data.rect, pageHeight);\n        editor.x = x / pageWidth;\n        editor.y = y / pageHeight;\n        editor.width = width / pageWidth;\n        editor.height = height / pageHeight;\n        return editor;\n    }\n    /**\n     * Check if an existing annotation associated with this editor has been\n     * modified.\n     * @returns {boolean}\n     */\n    get hasBeenModified() {\n        return (!!this.annotationElementId && (this.deleted || this.serialize() !== null));\n    }\n    /**\n     * Remove this editor.\n     * It's used on ctrl+backspace action.\n     */\n    remove() {\n        var _b;\n        (_b = __classPrivateFieldGet(this, _AnnotationEditor_focusAC, \"f\")) === null || _b === void 0 ? void 0 : _b.abort();\n        __classPrivateFieldSet(this, _AnnotationEditor_focusAC, null, \"f\");\n        if (!this.isEmpty()) {\n            // The editor is removed but it can be back at some point thanks to\n            // undo/redo so we must commit it before.\n            this.commit();\n        }\n        if (this.parent) {\n            this.parent.remove(this);\n        }\n        else {\n            this._uiManager.removeEditor(this);\n        }\n        if (__classPrivateFieldGet(this, _AnnotationEditor_moveInDOMTimeout, \"f\")) {\n            clearTimeout(__classPrivateFieldGet(this, _AnnotationEditor_moveInDOMTimeout, \"f\"));\n            __classPrivateFieldSet(this, _AnnotationEditor_moveInDOMTimeout, null, \"f\");\n        }\n        __classPrivateFieldGet(this, _AnnotationEditor_instances, \"m\", _AnnotationEditor_stopResizing).call(this);\n        this.removeEditToolbar();\n        // if (this.#telemetryTimeouts) {\n        //     for (const timeout of this.#telemetryTimeouts.values()) {\n        //         clearTimeout(timeout);\n        //     }\n        //     this.#telemetryTimeouts = null;\n        // }\n        this.parent = null;\n    }\n    /**\n     * @returns {boolean} true if this editor can be resized.\n     */\n    get isResizable() {\n        return false;\n    }\n    /**\n     * Add the resizers to this editor.\n     */\n    makeResizable() {\n        //     if (this.isResizable) {\n        //         this.#createResizers();\n        //         this.#resizersDiv.classList.remove(\"hidden\");\n        //         this.#resizersDiv.classList.remove(\"k-hidden\");\n        //         bindEvents(this, this.div, [\"keydown\"]);\n        //     }\n    }\n    get toolbarPosition() {\n        return null;\n    }\n    _stopResizingWithKeyboard() {\n        //     this.#stopResizing();\n        //     this.div.focus();\n    }\n    /**\n     * Select this editor.\n     */\n    select() {\n        var _b;\n        // this.makeResizable();\n        // this.div?.classList.add(\"selectedEditor\");\n        (_b = this.div) === null || _b === void 0 ? void 0 : _b.classList.add(\"k-selected\");\n        // todo: manually show annotation toolbar\n        this.addEditToolbar();\n        // if (!this._editToolbar) {\n        //     this.addEditToolbar().then(() => {\n        //         if (this.div?.classList.contains(\"k-selected selectedEditor\")) {\n        //             // The editor can have been unselected while we were waiting for the\n        //             // edit toolbar to be created, hence we want to be sure that this\n        //             // editor is still selected.\n        //             this._editToolbar?.show();\n        //         }\n        //     });\n        //     return;\n        // }\n        // this._editToolbar?.show();\n        // this.#altText?.toggleAltTextBadge(false);\n    }\n    /**\n     * Unselect this editor.\n     */\n    unselect() {\n        var _b, _c;\n        // this.#resizersDiv?.classList.add(\"hidden\");\n        // this.div?.classList.remove(\"selectedEditor\");\n        (_b = this.div) === null || _b === void 0 ? void 0 : _b.classList.remove(\"k-selected\");\n        if ((_c = this.div) === null || _c === void 0 ? void 0 : _c.contains(document.activeElement)) {\n            // Don't use this.div.blur() because we don't know where the focus will\n            // go.\n            this._uiManager.currentLayer.div.focus({\n                preventScroll: true\n            });\n        }\n        // this._editToolbar?.hide();\n        // this.#altText?.toggleAltTextBadge(true);\n    }\n    /**\n     * Update some parameters which have been changed through the UI.\n     * @param {number} type\n     * @param {*} value\n     */\n    updateParams(type, value) {\n        if (type === undefined || !value) {\n            /* no-empty */\n        }\n    }\n    /**\n     * When the user disables the editing mode some editors can change some of\n     * their properties.\n     */\n    disableEditing() { }\n    /**\n     * When the user enables the editing mode some editors can change some of\n     * their properties.\n     */\n    enableEditing() { }\n    /**\n     * The editor is about to be edited.\n     */\n    enterInEditMode() { }\n    /**\n     * @returns {HTMLElement | null} the element requiring an alt text.\n     */\n    getImageForAltText() {\n        return null;\n    }\n    /**\n     * Get the div which really contains the displayed content.\n     * @returns {HTMLDivElement | undefined}\n     */\n    get contentDiv() {\n        return this.div;\n    }\n    /**\n     * If true then the editor is currently edited.\n     * @type {boolean}\n     */\n    get isEditing() {\n        return __classPrivateFieldGet(this, _AnnotationEditor_isEditing, \"f\");\n    }\n    /**\n     * When set to true, it means that this editor is currently edited.\n     * @param {boolean} value\n     */\n    set isEditing(value) {\n        __classPrivateFieldSet(this, _AnnotationEditor_isEditing, value, \"f\");\n        if (!this.parent) {\n            return;\n        }\n        if (value) {\n            this.parent.setSelected(this);\n            this.parent.setActiveEditor(this);\n        }\n        else {\n            this.parent.setActiveEditor(null);\n        }\n    }\n    /**\n     * Set the aspect ratio to use when resizing.\n     * @param {number} width\n     * @param {number} height\n     */\n    setAspectRatio(width, height) {\n        __classPrivateFieldSet(this, _AnnotationEditor_keepAspectRatio, true, \"f\");\n        const aspectRatio = width / height;\n        const { style } = this.div;\n        style.aspectRatio = aspectRatio;\n        style.height = \"auto\";\n    }\n    static get MIN_SIZE() {\n        return 16;\n    }\n    static canCreateNewEmptyEditor() {\n        return true;\n    }\n    /**\n     * Get the data to report to the telemetry when the editor is added.\n     * @returns {Object}\n     */\n    get telemetryInitialData() {\n        return { action: \"added\" };\n    }\n    /**\n     * The telemetry data to use when saving/printing.\n     * @returns {Object|null}\n     */\n    get telemetryFinalData() {\n        return null;\n    }\n    _reportTelemetry() { }\n    // _reportTelemetry(data, mustWait = false) {\n    //     if (mustWait) {\n    //         this.#telemetryTimeouts ||= new Map();\n    //         const { action } = data;\n    //         let timeout = this.#telemetryTimeouts.get(action);\n    //         if (timeout) {\n    //             clearTimeout(timeout);\n    //         }\n    //         timeout = setTimeout(() => {\n    //             this._reportTelemetry(data);\n    //             this.#telemetryTimeouts.delete(action);\n    //             if (this.#telemetryTimeouts.size === 0) {\n    //                 this.#telemetryTimeouts = null;\n    //             }\n    //         }, AnnotationEditor._telemetryTimeout);\n    //         this.#telemetryTimeouts.set(action, timeout);\n    //         return;\n    //     }\n    //     data.type ||= this.editorType;\n    //     this._uiManager._eventBus.dispatch(\"reporttelemetry\", {\n    //         source: this,\n    //         details: {\n    //             type: \"editing\",\n    //             data,\n    //         },\n    //     });\n    // }\n    /**\n     * Show or hide this editor.\n     * @param {boolean|undefined} visible\n     */\n    show(visible = this._isVisible) {\n        // this.div.classList.toggle(\"hidden\", !visible);\n        this.div.classList.toggle(\"k-hidden\", !visible);\n        this._isVisible = visible;\n    }\n    enable() {\n        if (this.div) {\n            this.div.tabIndex = 0;\n        }\n        __classPrivateFieldSet(this, _AnnotationEditor_disabled, false, \"f\");\n    }\n    disable() {\n        if (this.div) {\n            this.div.tabIndex = -1;\n        }\n        __classPrivateFieldSet(this, _AnnotationEditor_disabled, true, \"f\");\n    }\n    /**\n     * Render an annotation in the annotation layer.\n     * @param {Object} annotation\n     * @returns {HTMLElement}\n     */\n    renderAnnotationElement(annotation) {\n        let content = annotation.container.querySelector(\".annotationContent\") ||\n            annotation.container.querySelector(\".k-annotation-content\");\n        if (!content) {\n            content = document.createElement(\"div\");\n            // content.classList.add(\"annotationContent\", this.editorType);\n            content.classList.add(\"k-annotation-content\", this.editorType);\n            annotation.container.prepend(content);\n        }\n        else if (content.nodeName === \"CANVAS\") {\n            const canvas = content;\n            content = document.createElement(\"div\");\n            // content.classList.add(\"annotationContent\", this.editorType);\n            content.classList.add(\"k-annotation-content\", this.editorType);\n            canvas.before(content);\n        }\n        return content;\n    }\n    resetAnnotationElement(annotation) {\n        const { firstChild } = annotation.container;\n        if (firstChild.nodeName === \"DIV\" &&\n            (firstChild.classList.contains(\"annotationContent\") ||\n                firstChild.classList.contains(\"k-annotation-content\"))) {\n            firstChild.remove();\n        }\n    }\n}\n_a = AnnotationEditor, _AnnotationEditor_x = new WeakMap(), _AnnotationEditor_y = new WeakMap(), _AnnotationEditor_disabled = new WeakMap(), _AnnotationEditor_keepAspectRatio = new WeakMap(), _AnnotationEditor_focusAC = new WeakMap(), _AnnotationEditor_hasBeenClicked = new WeakMap(), _AnnotationEditor_initialPosition = new WeakMap(), _AnnotationEditor_isEditing = new WeakMap(), _AnnotationEditor_isInEditMode = new WeakMap(), _AnnotationEditor_moveInDOMTimeout = new WeakMap(), _AnnotationEditor_prevDragX = new WeakMap(), _AnnotationEditor_prevDragY = new WeakMap(), _AnnotationEditor_isDraggable = new WeakMap(), _AnnotationEditor_zIndex = new WeakMap(), _AnnotationEditor_instances = new WeakSet(), _AnnotationEditor_translate = function _AnnotationEditor_translate([width, height], x, y) {\n    [x, y] = this.screenToPageTranslation(x, y);\n    this.x += x / width;\n    this.y += y / height;\n    this.fixAndSetPosition();\n}, _AnnotationEditor_rotatePoint = function _AnnotationEditor_rotatePoint(x, y, angle) {\n    switch (angle) {\n        case 90:\n            return [y, -x];\n        case 180:\n            return [-x, -y];\n        case 270:\n            return [-y, x];\n        default:\n            return [x, y];\n    }\n}, _AnnotationEditor_selectOnPointerEvent = function _AnnotationEditor_selectOnPointerEvent(event) {\n    const { isMac } = FeatureTest.platform;\n    const highlightEditor = event.target.closest(\".k-highlight-editor\");\n    const freeTextEditor = event.target.closest(\".k-free-text-editor\");\n    const annotationEditorMode = this._uiManager.getMode();\n    // todo: manually prevent selecting a highlight annotation in freetext mode\n    if ((annotationEditorMode === AnnotationEditorType.HIGHLIGHT && !highlightEditor) ||\n        (annotationEditorMode === AnnotationEditorType.FREETEXT && !freeTextEditor)) {\n        return;\n    }\n    if ((event.ctrlKey && !isMac) ||\n        event.shiftKey ||\n        (event.metaKey && isMac)) {\n        this.parent.toggleSelected(this);\n    }\n    else {\n        this.parent.setSelected(this);\n    }\n}, _AnnotationEditor_setUpDragSession = function _AnnotationEditor_setUpDragSession(event) {\n    const isSelected = this._uiManager.isSelected(this);\n    this._uiManager.setUpDragSession();\n    const ac = new AbortController();\n    const signal = this._uiManager.combinedSignal(ac);\n    if (isSelected) {\n        // this.div.classList.add(\"moving\");\n        __classPrivateFieldSet(this, _AnnotationEditor_prevDragX, event.clientX, \"f\");\n        __classPrivateFieldSet(this, _AnnotationEditor_prevDragY, event.clientY, \"f\");\n        const pointerMoveCallback = e => {\n            const { clientX: x, clientY: y } = e;\n            const [tx, ty] = this.screenToPageTranslation(x - __classPrivateFieldGet(this, _AnnotationEditor_prevDragX, \"f\"), y - __classPrivateFieldGet(this, _AnnotationEditor_prevDragY, \"f\"));\n            __classPrivateFieldSet(this, _AnnotationEditor_prevDragX, x, \"f\");\n            __classPrivateFieldSet(this, _AnnotationEditor_prevDragY, y, \"f\");\n            this._uiManager.dragSelectedEditors(tx, ty);\n        };\n        window.addEventListener(\"pointermove\", pointerMoveCallback, {\n            passive: true,\n            capture: true,\n            signal\n        });\n    }\n    const pointerUpCallback = () => {\n        ac.abort();\n        if (isSelected) {\n            this.div.classList.remove(\"moving\");\n        }\n        __classPrivateFieldSet(this, _AnnotationEditor_hasBeenClicked, false, \"f\");\n        if (!this._uiManager.endDragSession()) {\n            __classPrivateFieldGet(this, _AnnotationEditor_instances, \"m\", _AnnotationEditor_selectOnPointerEvent).call(this, event);\n        }\n    };\n    window.addEventListener(\"pointerup\", pointerUpCallback, { signal });\n    // If the user is using alt+tab during the dragging session, the pointerup\n    // event could be not fired, but a blur event is fired so we can use it in\n    // order to interrupt the dragging session.\n    window.addEventListener(\"blur\", pointerUpCallback, { signal });\n}, _AnnotationEditor_addFocusListeners = function _AnnotationEditor_addFocusListeners() {\n    if (__classPrivateFieldGet(this, _AnnotationEditor_focusAC, \"f\") || !this.div) {\n        return;\n    }\n    __classPrivateFieldSet(this, _AnnotationEditor_focusAC, new AbortController(), \"f\");\n    const signal = this._uiManager.combinedSignal(__classPrivateFieldGet(this, _AnnotationEditor_focusAC, \"f\"));\n    this.div.addEventListener(\"focusin\", this.focusin.bind(this), { signal });\n    this.div.addEventListener(\"focusout\", this.focusout.bind(this), { signal });\n}, _AnnotationEditor_stopResizing = function _AnnotationEditor_stopResizing() {\n    // this.#isResizerEnabledForKeyboard = false;\n    // this.#setResizerTabIndex(-1);\n    // if (this.#savedDimensions) {\n    //     const { savedX, savedY, savedWidth, savedHeight } = this.#savedDimensions;\n    //     this.#addResizeToUndoStack(savedX, savedY, savedWidth, savedHeight);\n    //     this.#savedDimensions = null;\n    // }\n};\nAnnotationEditor.idManager = new IdManager(\"annotation_editor_id_\");\nAnnotationEditor._borderLineWidth = -1;\nAnnotationEditor._colorManager = new ColorManager();\nAnnotationEditor._zIndex = 1;\n// Time to wait (in ms) before sending the telemetry data.\n// We wait a bit to avoid sending too many requests when changing something\n// like the thickness of a line.\nAnnotationEditor._telemetryTimeout = 1000;\n// This class is used to fake an editor which has been deleted.\nclass FakeEditor extends AnnotationEditor {\n    constructor(params) {\n        super(params);\n        this.annotationElementId = params.annotationElementId;\n        this.deleted = true;\n    }\n    serialize() {\n        return {\n            id: this.annotationElementId,\n            deleted: true,\n            pageIndex: this.pageIndex\n        };\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,2BAA2B,EAAEC,EAAE,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAEC,0BAA0B,EAAEC,iCAAiC,EAAEC,yBAAyB,EAAEC,gCAAgC,EAAEC,iCAAiC,EAAEC,2BAA2B,EAAEC,8BAA8B,EAAEC,kCAAkC,EAAEC,2BAA2B,EAAEC,2BAA2B,EAAEC,6BAA6B,EAAEC,wBAAwB,EAAEC,2BAA2B,EAAEC,6BAA6B,EAAEC,sCAAsC,EAAEC,kCAAkC,EAAEC,mCAAmC,EAAEC,8BAA8B;AAC7oB,SAASC,sBAAsB,EAAEC,sBAAsB,QAAQ,OAAO;AACtE;AACA;AACA;AACA,SAASC,SAAS,QAAQ,uBAAuB;AACjD,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,MAAM,EAAEC,WAAW,QAAQ,iCAAiC;AACrE,SAASC,oBAAoB,QAAQ,iBAAiB;AACtD,OAAO,MAAMC,gBAAgB,CAAC;EAC1BC,WAAWA,CAACC,UAAU,EAAE;IACpB;IACA;IACA;IACAhC,2BAA2B,CAACiC,GAAG,CAAC,IAAI,CAAC;IACrC;IACA,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,EAAE,GAAG,IAAI;IACd,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,IAAI,GAAG,EAAE;IACd,IAAI,CAACC,GAAG,GAAG,IAAI;IACf,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACC,oBAAoB,GAAG,KAAK;IACjC;IACA,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB;IACA;IACA7C,mBAAmB,CAAC8C,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;IAChC7C,mBAAmB,CAAC6C,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;IAChC,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB;IACA;IACA;IACA;IACA9C,0BAA0B,CAAC4C,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC;IAC3C3C,iCAAiC,CAAC2C,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC;IAClD;IACA;IACA1C,yBAAyB,CAAC0C,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IACzC;IACAzC,gCAAgC,CAACyC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC;IACjDxC,iCAAiC,CAACwC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IACjDvC,2BAA2B,CAACuC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC;IAC5CtC,8BAA8B,CAACsC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC;IAC/C;IACArC,kCAAkC,CAACqC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAClDpC,2BAA2B,CAACoC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;IACxCnC,2BAA2B,CAACmC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;IACxC;IACA,IAAI,CAACG,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,eAAe,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC1C,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB5C,6BAA6B,CAACkC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC;IAC9CjC,wBAAwB,CAACiC,GAAG,CAAC,IAAI,EAAE/C,EAAE,CAAC0D,OAAO,EAAE,CAAC;IAChD,IAAI,CAACzB,MAAM,GAAGF,UAAU,CAACE,MAAM;IAC/B,IAAI,CAACC,EAAE,GAAGH,UAAU,CAACG,EAAE,IAAIlC,EAAE,CAAC2D,SAAS,CAACzB,EAAE;IAC1C,IAAI,CAACC,KAAK,GAAG,IAAI,CAACC,MAAM,GAAG,IAAI;IAC/B,IAAI,CAACC,SAAS,GAAGN,UAAU,CAACE,MAAM,CAACI,SAAS;IAC5C,IAAI,CAACC,IAAI,GAAGP,UAAU,CAACO,IAAI;IAC3B,IAAI,CAACC,GAAG,GAAG,IAAI;IACf,IAAI,CAACgB,UAAU,GAAGxB,UAAU,CAAC6B,SAAS;IACtC,IAAI,CAACpB,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACC,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACU,eAAe,CAACU,UAAU,GAAG9B,UAAU,CAAC8B,UAAU;IACvD,IAAI,CAACnB,mBAAmB,GAAG,IAAI;IAC/B,MAAM;MAAEC,QAAQ;MAAEmB,OAAO,EAAE;QAAEC,SAAS;QAAEC,UAAU;QAAEC,KAAK;QAAEC;MAAM;IAAE,CAAC,GAAG,IAAI,CAACjC,MAAM,CAACkC,QAAQ;IAC3F,IAAI,CAACxB,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,YAAY,GACb,CAAC,GAAG,GAAGD,QAAQ,GAAG,IAAI,CAACY,UAAU,CAACa,cAAc,CAACzB,QAAQ,IAAI,GAAG;IACpE,IAAI,CAACE,cAAc,GAAG,CAACkB,SAAS,EAAEC,UAAU,CAAC;IAC7C,IAAI,CAAClB,eAAe,GAAG,CAACmB,KAAK,EAAEC,KAAK,CAAC;IACrC,MAAM,CAAC/B,KAAK,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACiC,gBAAgB;IAC7C,IAAI,CAACC,CAAC,GAAGvC,UAAU,CAACuC,CAAC,GAAGnC,KAAK;IAC7B,IAAI,CAACoC,CAAC,GAAGxC,UAAU,CAACwC,CAAC,GAAGnC,MAAM;IAC9B,IAAI,CAACY,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,OAAO,GAAG,KAAK;EACxB;EACA,IAAIqB,CAACA,CAAA,EAAG;IACJ,OAAOjD,sBAAsB,CAAC,IAAI,EAAEpB,mBAAmB,EAAE,GAAG,CAAC;EACjE;EACA,IAAIqE,CAACA,CAACE,KAAK,EAAE;IACTlD,sBAAsB,CAAC,IAAI,EAAErB,mBAAmB,EAAEuE,KAAK,EAAE,GAAG,CAAC;EACjE;EACA,IAAID,CAACA,CAAA,EAAG;IACJ,OAAOlD,sBAAsB,CAAC,IAAI,EAAEnB,mBAAmB,EAAE,GAAG,CAAC;EACjE;EACA,IAAIqE,CAACA,CAACC,KAAK,EAAE;IACTlD,sBAAsB,CAAC,IAAI,EAAEpB,mBAAmB,EAAEsE,KAAK,EAAE,GAAG,CAAC;EACjE;EACA,IAAIC,UAAUA,CAAA,EAAG;IACb,OAAOrB,MAAM,CAACsB,cAAc,CAAC,IAAI,CAAC,CAAC5C,WAAW,CAAC6C,KAAK;EACxD;EACA,WAAWC,iBAAiBA,CAAA,EAAG;IAC3B,OAAOlD,MAAM,CAAC,IAAI,EAAE,mBAAmB,EAAE,IAAI,CAACmD,aAAa,CAACC,UAAU,CAAC,YAAY,CAAC,CAAC;EACzF;EACA,OAAOC,uBAAuBA,CAACC,MAAM,EAAE;IACnC,MAAMC,UAAU,GAAG,IAAIC,UAAU,CAAC;MAC9BhD,EAAE,EAAE8C,MAAM,CAAC/C,MAAM,CAACkD,SAAS,CAAC,CAAC;MAC7BlD,MAAM,EAAE+C,MAAM,CAAC/C,MAAM;MACrB2B,SAAS,EAAEoB,MAAM,CAACzB;IACtB,CAAC,CAAC;IACF0B,UAAU,CAACzC,mBAAmB,GAAGwC,MAAM,CAACxC,mBAAmB;IAC3DyC,UAAU,CAAChC,OAAO,GAAG,IAAI;IACzBgC,UAAU,CAAC1B,UAAU,CAAC6B,sBAAsB,CAACH,UAAU,CAAC;EAC5D;EACA,OAAOI,UAAUA,CAACC,IAAI,EAAE1B,SAAS,EAAE2B,OAAO,EAAE;IACxC,IAAI,CAACD,IAAI,IAAI,CAAC1B,SAAS,IAAI,CAAC2B,OAAO,EAAE;MACjC;IAAA;IAEJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI,OAAOC,mBAAmBA,CAACb,KAAK,EAAEc,MAAM,EAAE;IACtC;IACA;EAAA;EAEJ;AACJ;AACA;AACA;EACI,WAAWC,yBAAyBA,CAAA,EAAG;IACnC,OAAO,EAAE;EACb;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,OAAOC,wBAAwBA,CAACC,IAAI,EAAE;IAClC,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,OAAO,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,OAAOC,KAAKA,CAACC,IAAI,EAAE7D,MAAM,EAAE;IACvB,IAAI,CAAC6D,IAAI,IAAI,CAAC7D,MAAM,EAAE;MAClB;IAAA;IAEJ;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAI8D,kBAAkBA,CAAA,EAAG;IACrB,OAAO,EAAE;EACb;EACA,IAAIC,YAAYA,CAAA,EAAG;IACf,OAAO3E,sBAAsB,CAAC,IAAI,EAAER,6BAA6B,EAAE,GAAG,CAAC;EAC3E;EACA,IAAImF,YAAYA,CAACxB,KAAK,EAAE;IACpB,IAAIyB,EAAE;IACN3E,sBAAsB,CAAC,IAAI,EAAET,6BAA6B,EAAE2D,KAAK,EAAE,GAAG,CAAC;IACvE;IACA,CAACyB,EAAE,GAAG,IAAI,CAAC1D,GAAG,MAAM,IAAI,IAAI0D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,SAAS,CAACC,MAAM,CAAC,aAAa,EAAE3B,KAAK,CAAC;EAClG;EACA;AACJ;AACA;EACI,IAAI4B,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI;EACf;EACAC,MAAMA,CAAA,EAAG;IACL,MAAM,CAACtC,SAAS,EAAEC,UAAU,CAAC,GAAG,IAAI,CAACnB,cAAc;IACnD,QAAQ,IAAI,CAACyD,cAAc;MACvB,KAAK,EAAE;QACH,IAAI,CAAChC,CAAC,IAAK,IAAI,CAAClC,MAAM,GAAG4B,UAAU,IAAKD,SAAS,GAAG,CAAC,CAAC;QACtD,IAAI,CAACQ,CAAC,IAAK,IAAI,CAACpC,KAAK,GAAG4B,SAAS,IAAKC,UAAU,GAAG,CAAC,CAAC;QACrD;MACJ,KAAK,GAAG;QACJ,IAAI,CAACM,CAAC,IAAI,IAAI,CAACnC,KAAK,GAAG,CAAC;QACxB,IAAI,CAACoC,CAAC,IAAI,IAAI,CAACnC,MAAM,GAAG,CAAC;QACzB;MACJ,KAAK,GAAG;QACJ,IAAI,CAACkC,CAAC,IAAK,IAAI,CAAClC,MAAM,GAAG4B,UAAU,IAAKD,SAAS,GAAG,CAAC,CAAC;QACtD,IAAI,CAACQ,CAAC,IAAK,IAAI,CAACpC,KAAK,GAAG4B,SAAS,IAAKC,UAAU,GAAG,CAAC,CAAC;QACrD;MACJ;QACI,IAAI,CAACM,CAAC,IAAI,IAAI,CAACnC,KAAK,GAAG,CAAC;QACxB,IAAI,CAACoC,CAAC,IAAI,IAAI,CAACnC,MAAM,GAAG,CAAC;QACzB;IACR;IACA,IAAI,CAACmE,iBAAiB,CAAC,CAAC;EAC5B;EACA;AACJ;AACA;AACA;EACIC,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAAClD,UAAU,CAACiD,WAAW,CAACC,MAAM,CAAC;EACvC;EACA,IAAIC,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACnD,UAAU,CAACmD,YAAY;EACvC;EACA;AACJ;AACA;EACIC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACpE,GAAG,CAACqE,KAAK,CAACC,MAAM,GAAG,CAAC;EAC7B;EACA;AACJ;AACA;EACIC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACvE,GAAG,CAACqE,KAAK,CAACC,MAAM,GAAGxF,sBAAsB,CAAC,IAAI,EAAEP,wBAAwB,EAAE,GAAG,CAAC;EACvF;EACAiG,SAASA,CAAC9E,MAAM,EAAE;IACd,IAAIA,MAAM,KAAK,IAAI,EAAE;MACjB,IAAI,CAACI,SAAS,GAAGJ,MAAM,CAACI,SAAS;MACjC,IAAI,CAACQ,cAAc,GAAGZ,MAAM,CAACY,cAAc;IAC/C,CAAC,MACI;MACD;MACAxB,sBAAsB,CAAC,IAAI,EAAEtB,2BAA2B,EAAE,GAAG,EAAEqB,8BAA8B,CAAC,CAAC4F,IAAI,CAAC,IAAI,CAAC;IAC7G;IACA,IAAI,CAAC/E,MAAM,GAAGA,MAAM;EACxB;EACA;AACJ;AACA;EACIgF,OAAOA,CAACC,KAAK,EAAE;IACX,IAAI,CAACA,KAAK,EAAE;MACR;IACJ;IACA,IAAI,CAAC,IAAI,CAAC1D,mBAAmB,EAAE;MAC3B;IACJ;IACA,IAAI,CAACnC,sBAAsB,CAAC,IAAI,EAAEf,gCAAgC,EAAE,GAAG,CAAC,EAAE;MACtE,IAAI,CAAC2B,MAAM,CAACkF,WAAW,CAAC,IAAI,CAAC;IACjC,CAAC,MACI;MACD7F,sBAAsB,CAAC,IAAI,EAAEhB,gCAAgC,EAAE,KAAK,EAAE,GAAG,CAAC;IAC9E;EACJ;EACA;AACJ;AACA;AACA;EACI8G,QAAQA,CAACF,KAAK,EAAE;IACZ,IAAIjB,EAAE;IACN,IAAI,CAAC,IAAI,CAACzC,mBAAmB,EAAE;MAC3B;IACJ;IACA,IAAI,CAAC,IAAI,CAACR,eAAe,EAAE;MACvB;IACJ;IACA;IACA;IACA;IACA;IACA,MAAMqE,MAAM,GAAGH,KAAK,CAACI,aAAa;IAClC,IAAID,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACE,OAAO,CAAC,IAAI,IAAI,CAACrF,EAAE,EAAE,CAAC,EAAE;MAC/E;IACJ;IACAgF,KAAK,CAACM,cAAc,CAAC,CAAC;IACtB,IAAI,EAAE,CAACvB,EAAE,GAAG,IAAI,CAAChE,MAAM,MAAM,IAAI,IAAIgE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwB,mBAAmB,CAAC,EAAE;MACnF,IAAI,CAACC,cAAc,CAAC,CAAC;IACzB;EACJ;EACAA,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACC,OAAO,CAAC,CAAC,EAAE;MAChB,IAAI,CAACC,MAAM,CAAC,CAAC;IACjB,CAAC,MACI;MACD,IAAI,CAACC,MAAM,CAAC,CAAC;IACjB;EACJ;EACA;AACJ;AACA;EACIA,MAAMA,CAAA,EAAG;IACL,IAAI,CAACzC,sBAAsB,CAAC,CAAC;EACjC;EACAA,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAAC7B,UAAU,CAAC6B,sBAAsB,CAAC,IAAI,CAAC;EAChD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI0C,KAAKA,CAACxD,CAAC,EAAEC,CAAC,EAAEwD,EAAE,EAAEC,EAAE,EAAE;IAChB,MAAM,CAAC7F,KAAK,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACiC,gBAAgB;IAC7C,CAAC0D,EAAE,EAAEC,EAAE,CAAC,GAAG,IAAI,CAACC,uBAAuB,CAACF,EAAE,EAAEC,EAAE,CAAC;IAC/C,IAAI,CAAC1D,CAAC,GAAG,CAACA,CAAC,GAAGyD,EAAE,IAAI5F,KAAK;IACzB,IAAI,CAACoC,CAAC,GAAG,CAACA,CAAC,GAAGyD,EAAE,IAAI5F,MAAM;IAC1B,IAAI,CAACmE,iBAAiB,CAAC,CAAC;EAC5B;EACA;AACJ;AACA;AACA;AACA;EACI2B,SAASA,CAAC5D,CAAC,EAAEC,CAAC,EAAE;IACZ;IACA;IACAlD,sBAAsB,CAAC,IAAI,EAAEtB,2BAA2B,EAAE,GAAG,EAAEgB,2BAA2B,CAAC,CAACiG,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC3C,gBAAgB,EAAEC,CAAC,EAAEC,CAAC,CAAC;EACvI;EACA;AACJ;AACA;AACA;AACA;AACA;EACI4D,eAAeA,CAAC7D,CAAC,EAAEC,CAAC,EAAE;IAClBjD,sBAAsB,CAAC,IAAI,EAAEf,iCAAiC,EAAEc,sBAAsB,CAAC,IAAI,EAAEd,iCAAiC,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC+D,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC,EAAE,GAAG,CAAC;IAC9JlD,sBAAsB,CAAC,IAAI,EAAEtB,2BAA2B,EAAE,GAAG,EAAEgB,2BAA2B,CAAC,CAACiG,IAAI,CAAC,IAAI,EAAE,IAAI,CAACnE,cAAc,EAAEyB,CAAC,EAAEC,CAAC,CAAC;IACjI,IAAI,CAAChC,GAAG,CAAC6F,cAAc,CAAC;MAAEC,KAAK,EAAE;IAAU,CAAC,CAAC;EACjD;EACAC,IAAIA,CAACP,EAAE,EAAEC,EAAE,EAAE;IACT1G,sBAAsB,CAAC,IAAI,EAAEf,iCAAiC,EAAEc,sBAAsB,CAAC,IAAI,EAAEd,iCAAiC,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC+D,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC,EAAE,GAAG,CAAC;IAC9J,MAAM,CAACgE,WAAW,EAAEC,YAAY,CAAC,GAAG,IAAI,CAACnE,gBAAgB;IACzD,IAAI,CAACC,CAAC,IAAIyD,EAAE,GAAGQ,WAAW;IAC1B,IAAI,CAAChE,CAAC,IAAIyD,EAAE,GAAGQ,YAAY;IAC3B,IAAI,IAAI,CAACvG,MAAM,KAAK,IAAI,CAACqC,CAAC,GAAG,CAAC,IAAI,IAAI,CAACA,CAAC,GAAG,CAAC,IAAI,IAAI,CAACC,CAAC,GAAG,CAAC,IAAI,IAAI,CAACA,CAAC,GAAG,CAAC,CAAC,EAAE;MACvE;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAM;QAAED,CAAC,EAAEmE,MAAM;QAAElE,CAAC,EAAEmE;MAAO,CAAC,GAAG,IAAI,CAACnG,GAAG,CAACoG,qBAAqB,CAAC,CAAC;MACjE,IAAI,IAAI,CAAC1G,MAAM,CAAC2G,aAAa,CAAC,IAAI,EAAEH,MAAM,EAAEC,MAAM,CAAC,EAAE;QACjD,IAAI,CAACpE,CAAC,IAAIuE,IAAI,CAACC,KAAK,CAAC,IAAI,CAACxE,CAAC,CAAC;QAC5B,IAAI,CAACC,CAAC,IAAIsE,IAAI,CAACC,KAAK,CAAC,IAAI,CAACvE,CAAC,CAAC;MAChC;IACJ;IACA;IACA;IACA,IAAI;MAAED,CAAC;MAAEC;IAAE,CAAC,GAAG,IAAI;IACnB,MAAM,CAACwE,EAAE,EAAEC,EAAE,CAAC,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC1C3E,CAAC,IAAIyE,EAAE;IACPxE,CAAC,IAAIyE,EAAE;IACP,IAAI,CAACzG,GAAG,CAACqE,KAAK,CAACsC,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG5E,CAAC,EAAE6E,OAAO,CAAC,CAAC,CAAC,GAAG;IAChD,IAAI,CAAC5G,GAAG,CAACqE,KAAK,CAACwC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG7E,CAAC,EAAE4E,OAAO,CAAC,CAAC,CAAC,GAAG;IAC/C,IAAI,CAAC5G,GAAG,CAAC6F,cAAc,CAAC;MAAEC,KAAK,EAAE;IAAU,CAAC,CAAC;EACjD;EACA,IAAIgB,aAAaA,CAAA,EAAG;IAChB,OAAQ,CAAC,CAAChI,sBAAsB,CAAC,IAAI,EAAEd,iCAAiC,EAAE,GAAG,CAAC,KACzEc,sBAAsB,CAAC,IAAI,EAAEd,iCAAiC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC+D,CAAC,IAC/EjD,sBAAsB,CAAC,IAAI,EAAEd,iCAAiC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAACgE,CAAC,CAAC;EAC/F;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI0E,kBAAkBA,CAAA,EAAG;IACjB,MAAM,CAACV,WAAW,EAAEC,YAAY,CAAC,GAAG,IAAI,CAACnE,gBAAgB;IACzD,MAAM;MAAEiF;IAAiB,CAAC,GAAGtJ,EAAE;IAC/B,MAAMsE,CAAC,GAAGgF,gBAAgB,GAAGf,WAAW;IACxC,MAAMhE,CAAC,GAAG+E,gBAAgB,GAAGd,YAAY;IACzC,QAAQ,IAAI,CAAC7F,QAAQ;MACjB,KAAK,EAAE;QACH,OAAO,CAAC,CAAC2B,CAAC,EAAEC,CAAC,CAAC;MAClB,KAAK,GAAG;QACJ,OAAO,CAACD,CAAC,EAAEC,CAAC,CAAC;MACjB,KAAK,GAAG;QACJ,OAAO,CAACD,CAAC,EAAE,CAACC,CAAC,CAAC;MAClB;QACI,OAAO,CAAC,CAACD,CAAC,EAAE,CAACC,CAAC,CAAC;IACvB;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIgF,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIhD,iBAAiBA,CAAC5D,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE;IACxC,IAAIA,QAAQ,KAAK6G,SAAS,EAAE;MACxB;IACJ;IACA,MAAM,CAACzF,SAAS,EAAEC,UAAU,CAAC,GAAG,IAAI,CAACnB,cAAc;IACnD,IAAI;MAAEyB,CAAC;MAAEC,CAAC;MAAEpC,KAAK;MAAEC;IAAO,CAAC,GAAG,IAAI;IAClCD,KAAK,IAAI4B,SAAS;IAClB3B,MAAM,IAAI4B,UAAU;IACpBM,CAAC,IAAIP,SAAS;IACdQ,CAAC,IAAIP,UAAU;IACf,IAAI,IAAI,CAACuF,gBAAgB,EAAE;MACvB,QAAQ5G,QAAQ;QACZ,KAAK,CAAC;UACF2B,CAAC,GAAGuE,IAAI,CAACY,GAAG,CAAC,CAAC,EAAEZ,IAAI,CAACa,GAAG,CAAC3F,SAAS,GAAG5B,KAAK,EAAEmC,CAAC,CAAC,CAAC;UAC/CC,CAAC,GAAGsE,IAAI,CAACY,GAAG,CAAC,CAAC,EAAEZ,IAAI,CAACa,GAAG,CAAC1F,UAAU,GAAG5B,MAAM,EAAEmC,CAAC,CAAC,CAAC;UACjD;QACJ,KAAK,EAAE;UACHD,CAAC,GAAGuE,IAAI,CAACY,GAAG,CAAC,CAAC,EAAEZ,IAAI,CAACa,GAAG,CAAC3F,SAAS,GAAG3B,MAAM,EAAEkC,CAAC,CAAC,CAAC;UAChDC,CAAC,GAAGsE,IAAI,CAACa,GAAG,CAAC1F,UAAU,EAAE6E,IAAI,CAACY,GAAG,CAACtH,KAAK,EAAEoC,CAAC,CAAC,CAAC;UAC5C;QACJ,KAAK,GAAG;UACJD,CAAC,GAAGuE,IAAI,CAACa,GAAG,CAAC3F,SAAS,EAAE8E,IAAI,CAACY,GAAG,CAACtH,KAAK,EAAEmC,CAAC,CAAC,CAAC;UAC3CC,CAAC,GAAGsE,IAAI,CAACa,GAAG,CAAC1F,UAAU,EAAE6E,IAAI,CAACY,GAAG,CAACrH,MAAM,EAAEmC,CAAC,CAAC,CAAC;UAC7C;QACJ,KAAK,GAAG;UACJD,CAAC,GAAGuE,IAAI,CAACa,GAAG,CAAC3F,SAAS,EAAE8E,IAAI,CAACY,GAAG,CAACrH,MAAM,EAAEkC,CAAC,CAAC,CAAC;UAC5CC,CAAC,GAAGsE,IAAI,CAACY,GAAG,CAAC,CAAC,EAAEZ,IAAI,CAACa,GAAG,CAAC1F,UAAU,GAAG7B,KAAK,EAAEoC,CAAC,CAAC,CAAC;UAChD;QACJ;UAAS;MACb;IACJ;IACA,IAAI,CAACD,CAAC,GAAGA,CAAC,IAAIP,SAAS;IACvB,IAAI,CAACQ,CAAC,GAAGA,CAAC,IAAIP,UAAU;IACxB,MAAM,CAAC+E,EAAE,EAAEC,EAAE,CAAC,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC1C3E,CAAC,IAAIyE,EAAE;IACPxE,CAAC,IAAIyE,EAAE;IACP,MAAM;MAAEpC;IAAM,CAAC,GAAG,IAAI,CAACrE,GAAG;IAC1BqE,KAAK,CAACsC,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG5E,CAAC,EAAE6E,OAAO,CAAC,CAAC,CAAC,GAAG;IACvCvC,KAAK,CAACwC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG7E,CAAC,EAAE4E,OAAO,CAAC,CAAC,CAAC,GAAG;IACtC,IAAI,CAACQ,SAAS,CAAC,CAAC;EACpB;EACA;AACJ;AACA;AACA;AACA;EACI1B,uBAAuBA,CAAC3D,CAAC,EAAEC,CAAC,EAAE;IAC1B,OAAOlD,sBAAsB,CAACrB,EAAE,EAAEA,EAAE,EAAE,GAAG,EAAEgB,6BAA6B,CAAC,CAACgG,IAAI,CAAChH,EAAE,EAAEsE,CAAC,EAAEC,CAAC,EAAE,IAAI,CAAC+B,cAAc,CAAC;EACjH;EACA;AACJ;AACA;AACA;AACA;EACIsD,uBAAuBA,CAACtF,CAAC,EAAEC,CAAC,EAAE;IAC1B,OAAOlD,sBAAsB,CAACrB,EAAE,EAAEA,EAAE,EAAE,GAAG,EAAEgB,6BAA6B,CAAC,CAACgG,IAAI,CAAChH,EAAE,EAAEsE,CAAC,EAAEC,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC+B,cAAc,CAAC;EACvH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIuD,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACtG,UAAU,CAACa,cAAc,CAAC0F,SAAS;EACnD;EACA,IAAIxD,cAAcA,CAAA,EAAG;IACjB,OAAO,CAAC,IAAI,CAAC/C,UAAU,CAACa,cAAc,CAACzB,QAAQ,GAAG,IAAI,CAACC,YAAY,IAAI,GAAG;EAC9E;EACA,IAAIyB,gBAAgBA,CAAA,EAAG;IACnB,MAAM;MAAEwF,WAAW;MAAEhH,cAAc,EAAE,CAACkB,SAAS,EAAEC,UAAU;IAAE,CAAC,GAAG,IAAI;IACrE,MAAM+F,WAAW,GAAGhG,SAAS,GAAG8F,WAAW;IAC3C,MAAMG,YAAY,GAAGhG,UAAU,GAAG6F,WAAW;IAC7C,OAAOlI,WAAW,CAACsI,mBAAmB,GAChC,CAACpB,IAAI,CAACqB,KAAK,CAACH,WAAW,CAAC,EAAElB,IAAI,CAACqB,KAAK,CAACF,YAAY,CAAC,CAAC,GACnD,CAACD,WAAW,EAAEC,YAAY,CAAC;EACrC;EACA;AACJ;AACA;AACA;AACA;EACIG,OAAOA,CAAChI,KAAK,EAAEC,MAAM,EAAE;IACnB,MAAM,CAACmG,WAAW,EAAEC,YAAY,CAAC,GAAG,IAAI,CAACnE,gBAAgB;IACzD,IAAI,CAAC9B,GAAG,CAACqE,KAAK,CAACzE,KAAK,GAAG,GAAG,CAAE,GAAG,GAAGA,KAAK,GAAIoG,WAAW,EAAEY,OAAO,CAAC,CAAC,CAAC,GAAG;IACrE,IAAI,CAAC9H,sBAAsB,CAAC,IAAI,EAAEjB,iCAAiC,EAAE,GAAG,CAAC,EAAE;MACvE,IAAI,CAACmC,GAAG,CAACqE,KAAK,CAACxE,MAAM,GAAG,GAAG,CAAE,GAAG,GAAGA,MAAM,GAAIoG,YAAY,EAAEW,OAAO,CAAC,CAAC,CAAC,GAAG;IAC5E;EACJ;EACAiB,OAAOA,CAAA,EAAG;IACN;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA;EAEJ;AACJ;AACA;AACA;EACIC,qBAAqBA,CAAA,EAAG;IACpB,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EACjB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,cAAcA,CAAA,EAAG;IACb,IAAIrE,EAAE;IACN,CAACA,EAAE,GAAG,IAAI,CAAC1C,UAAU,MAAM,IAAI,IAAI0C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsE,iBAAiB,CAAC,IAAI,CAAChI,GAAG,CAAC;IAC1F;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACJ;EACAiI,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACjH,UAAU,CAACkH,iBAAiB,CAAC,CAAC;IACnC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACJ;EACAC,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACnI,GAAG,CAACoG,qBAAqB,CAAC,CAAC;EAC3C;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACJ;AACA;AACA;EACIgC,MAAMA,CAAA,EAAG;IACL,IAAI,CAACpI,GAAG,GAAGqI,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACxC,IAAI,CAACtI,GAAG,CAACuI,YAAY,CAAC,sBAAsB,EAAE,CAAC,GAAG,GAAG,IAAI,CAACnI,QAAQ,IAAI,GAAG,CAAC;IAC1E,IAAI,CAACJ,GAAG,CAACwI,SAAS,GAAG,IAAI,CAACzI,IAAI;IAC9B,IAAI,CAACC,GAAG,CAACuI,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC5I,EAAE,CAAC;IACpC,IAAI,CAACK,GAAG,CAACyI,QAAQ,GAAG3J,sBAAsB,CAAC,IAAI,EAAElB,0BAA0B,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAC1F,IAAI,CAAC,IAAI,CAACmD,UAAU,EAAE;MAClB;MACA,IAAI,CAACf,GAAG,CAAC2D,SAAS,CAAClE,GAAG,CAAC,UAAU,CAAC;IACtC;IACA,IAAI,CAAC8E,eAAe,CAAC,CAAC;IACtBzF,sBAAsB,CAAC,IAAI,EAAEtB,2BAA2B,EAAE,GAAG,EAAEoB,mCAAmC,CAAC,CAAC6F,IAAI,CAAC,IAAI,CAAC;IAC9G,MAAM,CAACuB,WAAW,EAAEC,YAAY,CAAC,GAAG,IAAI,CAACnE,gBAAgB;IACzD,IAAI,IAAI,CAACiC,cAAc,GAAG,GAAG,KAAK,CAAC,EAAE;MACjC,IAAI,CAAC/D,GAAG,CAACqE,KAAK,CAACqE,QAAQ,GAAG,GAAG,CAAE,GAAG,GAAGzC,YAAY,GAAID,WAAW,EAAEY,OAAO,CAAC,CAAC,CAAC,GAAG;MAC/E,IAAI,CAAC5G,GAAG,CAACqE,KAAK,CAACsE,SAAS,GAAG,GAAG,CAAE,GAAG,GAAG3C,WAAW,GAC7CC,YAAY,EAAEW,OAAO,CAAC,CAAC,CAAC,GAAG;IACnC;IACA,MAAM,CAACpB,EAAE,EAAEC,EAAE,CAAC,GAAG,IAAI,CAACqC,qBAAqB,CAAC,CAAC;IAC7C,IAAI,CAACnC,SAAS,CAACH,EAAE,EAAEC,EAAE,CAAC;IACtBvG,UAAU,CAAC,IAAI,EAAE,IAAI,CAACc,GAAG,EAAE,CAAC,aAAa,CAAC,CAAC;IAC3C,OAAO,IAAI,CAACA,GAAG;EACnB;EACA;AACJ;AACA;AACA;EACI4I,WAAWA,CAACjE,KAAK,EAAE;IACf,IAAIjB,EAAE;IACN,MAAM;MAAEmF;IAAM,CAAC,GAAGzJ,WAAW,CAAC0J,QAAQ;IACtC,IAAInE,KAAK,CAACoE,MAAM,KAAK,CAAC,IAAKpE,KAAK,CAACqE,OAAO,IAAIH,KAAM,EAAE;MAChD;MACAlE,KAAK,CAACM,cAAc,CAAC,CAAC;MACtB;IACJ;IACAlG,sBAAsB,CAAC,IAAI,EAAEhB,gCAAgC,EAAE,IAAI,EAAE,GAAG,CAAC;IACzE,IAAI,IAAI,CAAC0F,YAAY,EAAE;MACnB,CAACC,EAAE,GAAG,IAAI,CAAC1C,UAAU,MAAM,IAAI,IAAI0C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwE,iBAAiB,CAAC,CAAC;MAClFpJ,sBAAsB,CAAC,IAAI,EAAEtB,2BAA2B,EAAE,GAAG,EAAEmB,kCAAkC,CAAC,CAAC8F,IAAI,CAAC,IAAI,EAAEE,KAAK,CAAC;MACpH;IACJ;IACA7F,sBAAsB,CAAC,IAAI,EAAEtB,2BAA2B,EAAE,GAAG,EAAEkB,sCAAsC,CAAC,CAAC+F,IAAI,CAAC,IAAI,EAAEE,KAAK,CAAC;EAC5H;EACAyC,SAASA,CAAA,EAAG;IACR;IACA;IACA;IACA,IAAItI,sBAAsB,CAAC,IAAI,EAAEX,kCAAkC,EAAE,GAAG,CAAC,EAAE;MACvE8K,YAAY,CAACnK,sBAAsB,CAAC,IAAI,EAAEX,kCAAkC,EAAE,GAAG,CAAC,CAAC;IACvF;IACAY,sBAAsB,CAAC,IAAI,EAAEZ,kCAAkC,EAAE+K,UAAU,CAAC,MAAM;MAC9E,IAAIxF,EAAE;MACN3E,sBAAsB,CAAC,IAAI,EAAEZ,kCAAkC,EAAE,IAAI,EAAE,GAAG,CAAC;MAC3E,CAACuF,EAAE,GAAG,IAAI,CAAChE,MAAM,MAAM,IAAI,IAAIgE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyF,eAAe,CAAC,IAAI,CAAC;IACpF,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;EACf;EACAC,qBAAqBA,CAAC1J,MAAM,EAAEqC,CAAC,EAAEC,CAAC,EAAE;IAChCtC,MAAM,CAAC2J,YAAY,CAAC,IAAI,CAAC;IACzB,IAAI,CAACtH,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACgC,iBAAiB,CAAC,CAAC;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIsF,OAAOA,CAAC9D,EAAE,EAAEC,EAAE,EAAErF,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE;IACtC,MAAMmJ,KAAK,GAAG,IAAI,CAACjC,WAAW;IAC9B,MAAM,CAAC9F,SAAS,EAAEC,UAAU,CAAC,GAAG,IAAI,CAACnB,cAAc;IACnD,MAAM,CAACoB,KAAK,EAAEC,KAAK,CAAC,GAAG,IAAI,CAACpB,eAAe;IAC3C,MAAMiJ,MAAM,GAAGhE,EAAE,GAAG+D,KAAK;IACzB,MAAME,MAAM,GAAGhE,EAAE,GAAG8D,KAAK;IACzB,MAAMxH,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGP,SAAS;IAC5B,MAAMQ,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGP,UAAU;IAC7B,MAAM7B,KAAK,GAAG,IAAI,CAACA,KAAK,GAAG4B,SAAS;IACpC,MAAM3B,MAAM,GAAG,IAAI,CAACA,MAAM,GAAG4B,UAAU;IACvC,QAAQrB,QAAQ;MACZ,KAAK,CAAC;QACF,OAAO,CACH2B,CAAC,GAAGyH,MAAM,GAAG9H,KAAK,EAClBD,UAAU,GAAGO,CAAC,GAAGyH,MAAM,GAAG5J,MAAM,GAAG8B,KAAK,EACxCI,CAAC,GAAGyH,MAAM,GAAG5J,KAAK,GAAG8B,KAAK,EAC1BD,UAAU,GAAGO,CAAC,GAAGyH,MAAM,GAAG9H,KAAK,CAClC;MACL,KAAK,EAAE;QACH,OAAO,CACHI,CAAC,GAAG0H,MAAM,GAAG/H,KAAK,EAClBD,UAAU,GAAGO,CAAC,GAAGwH,MAAM,GAAG7H,KAAK,EAC/BI,CAAC,GAAG0H,MAAM,GAAG5J,MAAM,GAAG6B,KAAK,EAC3BD,UAAU,GAAGO,CAAC,GAAGwH,MAAM,GAAG5J,KAAK,GAAG+B,KAAK,CAC1C;MACL,KAAK,GAAG;QACJ,OAAO,CACHI,CAAC,GAAGyH,MAAM,GAAG5J,KAAK,GAAG8B,KAAK,EAC1BD,UAAU,GAAGO,CAAC,GAAGyH,MAAM,GAAG9H,KAAK,EAC/BI,CAAC,GAAGyH,MAAM,GAAG9H,KAAK,EAClBD,UAAU,GAAGO,CAAC,GAAGyH,MAAM,GAAG5J,MAAM,GAAG8B,KAAK,CAC3C;MACL,KAAK,GAAG;QACJ,OAAO,CACHI,CAAC,GAAG0H,MAAM,GAAG5J,MAAM,GAAG6B,KAAK,EAC3BD,UAAU,GAAGO,CAAC,GAAGwH,MAAM,GAAG5J,KAAK,GAAG+B,KAAK,EACvCI,CAAC,GAAG0H,MAAM,GAAG/H,KAAK,EAClBD,UAAU,GAAGO,CAAC,GAAGwH,MAAM,GAAG7H,KAAK,CAClC;MACL;QACI,MAAM,IAAI+H,KAAK,CAAC,kBAAkB,CAAC;IAC3C;EACJ;EACAC,sBAAsBA,CAACC,IAAI,EAAEnI,UAAU,EAAE;IACrC,MAAM,CAACoI,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAGJ,IAAI;IAC7B,MAAMhK,KAAK,GAAGmK,EAAE,GAAGF,EAAE;IACrB,MAAMhK,MAAM,GAAGmK,EAAE,GAAGF,EAAE;IACtB,QAAQ,IAAI,CAAC1J,QAAQ;MACjB,KAAK,CAAC;QACF,OAAO,CAACyJ,EAAE,EAAEpI,UAAU,GAAGuI,EAAE,EAAEpK,KAAK,EAAEC,MAAM,CAAC;MAC/C,KAAK,EAAE;QACH,OAAO,CAACgK,EAAE,EAAEpI,UAAU,GAAGqI,EAAE,EAAEjK,MAAM,EAAED,KAAK,CAAC;MAC/C,KAAK,GAAG;QACJ,OAAO,CAACmK,EAAE,EAAEtI,UAAU,GAAGqI,EAAE,EAAElK,KAAK,EAAEC,MAAM,CAAC;MAC/C,KAAK,GAAG;QACJ,OAAO,CAACkK,EAAE,EAAEtI,UAAU,GAAGuI,EAAE,EAAEnK,MAAM,EAAED,KAAK,CAAC;MAC/C;QACI;QACA;IACR;EACJ;EACA;EACA;EACA;EACAqK,SAASA,CAAA,EAAG,CAAE;EACd;EACA;EACA;EACA;EACA7E,OAAOA,CAAA,EAAG;IACN,OAAO,KAAK;EAChB;EACA;AACJ;AACA;EACI8E,cAAcA,CAAA,EAAG;IACbnL,sBAAsB,CAAC,IAAI,EAAEb,8BAA8B,EAAE,IAAI,EAAE,GAAG,CAAC;EAC3E;EACA;AACJ;AACA;EACIiM,eAAeA,CAAA,EAAG;IACdpL,sBAAsB,CAAC,IAAI,EAAEb,8BAA8B,EAAE,KAAK,EAAE,GAAG,CAAC;EAC5E;EACA;AACJ;AACA;AACA;EACIkM,YAAYA,CAAA,EAAG;IACX,OAAOtL,sBAAsB,CAAC,IAAI,EAAEZ,8BAA8B,EAAE,GAAG,CAAC;EAC5E;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACJ;AACA;AACA;EACImM,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACrK,GAAG,IAAI,CAAC,IAAI,CAACS,eAAe;EAC5C;EACA;AACJ;AACA;AACA;AACA;EACI6J,OAAOA,CAAA,EAAG;IACNxL,sBAAsB,CAAC,IAAI,EAAEtB,2BAA2B,EAAE,GAAG,EAAEoB,mCAAmC,CAAC,CAAC6F,IAAI,CAAC,IAAI,CAAC;EAClH;EACA;AACJ;AACA;AACA;EACI8F,MAAMA,CAACC,KAAK,EAAE;IACV,IAAIA,KAAK,KAAKvD,SAAS,IAAIuD,KAAK,KAAK,IAAI,EAAE;MACvC;IAAA;EAER;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,SAASA,CAACC,aAAa,GAAG,KAAK,EAAEC,QAAQ,GAAG,IAAI,EAAE;IAC9C;EAAA;EAEJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,WAAWA,CAACC,IAAI,EAAEnL,MAAM,EAAE2B,SAAS,EAAE;IACxC;IACA,MAAMoB,MAAM,GAAG,IAAI,IAAI,CAACqI,SAAS,CAACvL,WAAW,CAAC;MAC1CG,MAAM;MACNC,EAAE,EAAED,MAAM,CAACkD,SAAS,CAAC,CAAC;MACtBvB;IACJ,CAAC,CAAC;IACFoB,MAAM,CAACrC,QAAQ,GAAGyK,IAAI,CAACzK,QAAQ;IAC/B;IACA,MAAM,CAACoB,SAAS,EAAEC,UAAU,CAAC,GAAGgB,MAAM,CAACnC,cAAc;IACrD,MAAM,CAACyB,CAAC,EAAEC,CAAC,EAAEpC,KAAK,EAAEC,MAAM,CAAC,GAAG4C,MAAM,CAACkH,sBAAsB,CAACkB,IAAI,CAACjB,IAAI,EAAEnI,UAAU,CAAC;IAClFgB,MAAM,CAACV,CAAC,GAAGA,CAAC,GAAGP,SAAS;IACxBiB,MAAM,CAACT,CAAC,GAAGA,CAAC,GAAGP,UAAU;IACzBgB,MAAM,CAAC7C,KAAK,GAAGA,KAAK,GAAG4B,SAAS;IAChCiB,MAAM,CAAC5C,MAAM,GAAGA,MAAM,GAAG4B,UAAU;IACnC,OAAOgB,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIsI,eAAeA,CAAA,EAAG;IAClB,OAAQ,CAAC,CAAC,IAAI,CAAC9K,mBAAmB,KAAK,IAAI,CAACS,OAAO,IAAI,IAAI,CAAC+J,SAAS,CAAC,CAAC,KAAK,IAAI,CAAC;EACrF;EACA;AACJ;AACA;AACA;EACIpF,MAAMA,CAAA,EAAG;IACL,IAAI3B,EAAE;IACN,CAACA,EAAE,GAAG5E,sBAAsB,CAAC,IAAI,EAAEhB,yBAAyB,EAAE,GAAG,CAAC,MAAM,IAAI,IAAI4F,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsH,KAAK,CAAC,CAAC;IACnHjM,sBAAsB,CAAC,IAAI,EAAEjB,yBAAyB,EAAE,IAAI,EAAE,GAAG,CAAC;IAClE,IAAI,CAAC,IAAI,CAACsH,OAAO,CAAC,CAAC,EAAE;MACjB;MACA;MACA,IAAI,CAACE,MAAM,CAAC,CAAC;IACjB;IACA,IAAI,IAAI,CAAC5F,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAAC2F,MAAM,CAAC,IAAI,CAAC;IAC5B,CAAC,MACI;MACD,IAAI,CAACrE,UAAU,CAACiK,YAAY,CAAC,IAAI,CAAC;IACtC;IACA,IAAInM,sBAAsB,CAAC,IAAI,EAAEX,kCAAkC,EAAE,GAAG,CAAC,EAAE;MACvE8K,YAAY,CAACnK,sBAAsB,CAAC,IAAI,EAAEX,kCAAkC,EAAE,GAAG,CAAC,CAAC;MACnFY,sBAAsB,CAAC,IAAI,EAAEZ,kCAAkC,EAAE,IAAI,EAAE,GAAG,CAAC;IAC/E;IACAW,sBAAsB,CAAC,IAAI,EAAEtB,2BAA2B,EAAE,GAAG,EAAEqB,8BAA8B,CAAC,CAAC4F,IAAI,CAAC,IAAI,CAAC;IACzG,IAAI,CAACwD,iBAAiB,CAAC,CAAC;IACxB;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACvI,MAAM,GAAG,IAAI;EACtB;EACA;AACJ;AACA;EACI,IAAIwL,WAAWA,CAAA,EAAG;IACd,OAAO,KAAK;EAChB;EACA;AACJ;AACA;EACIC,aAAaA,CAAA,EAAG;IACZ;IACA;IACA;IACA;IACA;IACA;EAAA;EAEJ,IAAIC,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI;EACf;EACAC,yBAAyBA,CAAA,EAAG;IACxB;IACA;EAAA;EAEJ;AACJ;AACA;EACIC,MAAMA,CAAA,EAAG;IACL,IAAI5H,EAAE;IACN;IACA;IACA,CAACA,EAAE,GAAG,IAAI,CAAC1D,GAAG,MAAM,IAAI,IAAI0D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,SAAS,CAAClE,GAAG,CAAC,YAAY,CAAC;IACnF;IACA,IAAI,CAACsI,cAAc,CAAC,CAAC;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACJ;EACA;AACJ;AACA;EACIwD,QAAQA,CAAA,EAAG;IACP,IAAI7H,EAAE,EAAE8H,EAAE;IACV;IACA;IACA,CAAC9H,EAAE,GAAG,IAAI,CAAC1D,GAAG,MAAM,IAAI,IAAI0D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,SAAS,CAAC0B,MAAM,CAAC,YAAY,CAAC;IACtF,IAAI,CAACmG,EAAE,GAAG,IAAI,CAACxL,GAAG,MAAM,IAAI,IAAIwL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,QAAQ,CAACpD,QAAQ,CAACqD,aAAa,CAAC,EAAE;MAC1F;MACA;MACA,IAAI,CAAC1K,UAAU,CAACmD,YAAY,CAACnE,GAAG,CAAC2L,KAAK,CAAC;QACnCC,aAAa,EAAE;MACnB,CAAC,CAAC;IACN;IACA;IACA;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIC,YAAYA,CAACC,IAAI,EAAE7J,KAAK,EAAE;IACtB,IAAI6J,IAAI,KAAK7E,SAAS,IAAI,CAAChF,KAAK,EAAE;MAC9B;IAAA;EAER;EACA;AACJ;AACA;AACA;EACI8J,cAAcA,CAAA,EAAG,CAAE;EACnB;AACJ;AACA;AACA;EACIC,aAAaA,CAAA,EAAG,CAAE;EAClB;AACJ;AACA;EACIC,eAAeA,CAAA,EAAG,CAAE;EACpB;AACJ;AACA;EACIC,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACI,IAAIC,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACnM,GAAG;EACnB;EACA;AACJ;AACA;AACA;EACI,IAAIoM,SAASA,CAAA,EAAG;IACZ,OAAOtN,sBAAsB,CAAC,IAAI,EAAEb,2BAA2B,EAAE,GAAG,CAAC;EACzE;EACA;AACJ;AACA;AACA;EACI,IAAImO,SAASA,CAACnK,KAAK,EAAE;IACjBlD,sBAAsB,CAAC,IAAI,EAAEd,2BAA2B,EAAEgE,KAAK,EAAE,GAAG,CAAC;IACrE,IAAI,CAAC,IAAI,CAACvC,MAAM,EAAE;MACd;IACJ;IACA,IAAIuC,KAAK,EAAE;MACP,IAAI,CAACvC,MAAM,CAACkF,WAAW,CAAC,IAAI,CAAC;MAC7B,IAAI,CAAClF,MAAM,CAAC2M,eAAe,CAAC,IAAI,CAAC;IACrC,CAAC,MACI;MACD,IAAI,CAAC3M,MAAM,CAAC2M,eAAe,CAAC,IAAI,CAAC;IACrC;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIC,cAAcA,CAAC1M,KAAK,EAAEC,MAAM,EAAE;IAC1Bd,sBAAsB,CAAC,IAAI,EAAElB,iCAAiC,EAAE,IAAI,EAAE,GAAG,CAAC;IAC1E,MAAM0O,WAAW,GAAG3M,KAAK,GAAGC,MAAM;IAClC,MAAM;MAAEwE;IAAM,CAAC,GAAG,IAAI,CAACrE,GAAG;IAC1BqE,KAAK,CAACkI,WAAW,GAAGA,WAAW;IAC/BlI,KAAK,CAACxE,MAAM,GAAG,MAAM;EACzB;EACA,WAAW2M,QAAQA,CAAA,EAAG;IAClB,OAAO,EAAE;EACb;EACA,OAAOC,uBAAuBA,CAAA,EAAG;IAC7B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACI,IAAIC,oBAAoBA,CAAA,EAAG;IACvB,OAAO;MAAEC,MAAM,EAAE;IAAQ,CAAC;EAC9B;EACA;AACJ;AACA;AACA;EACI,IAAIC,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI;EACf;EACAC,gBAAgBA,CAAA,EAAG,CAAE;EACrB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACJ;AACA;AACA;EACIC,IAAIA,CAACC,OAAO,GAAG,IAAI,CAAChM,UAAU,EAAE;IAC5B;IACA,IAAI,CAACf,GAAG,CAAC2D,SAAS,CAACC,MAAM,CAAC,UAAU,EAAE,CAACmJ,OAAO,CAAC;IAC/C,IAAI,CAAChM,UAAU,GAAGgM,OAAO;EAC7B;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAAChN,GAAG,EAAE;MACV,IAAI,CAACA,GAAG,CAACyI,QAAQ,GAAG,CAAC;IACzB;IACA1J,sBAAsB,CAAC,IAAI,EAAEnB,0BAA0B,EAAE,KAAK,EAAE,GAAG,CAAC;EACxE;EACAqP,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACjN,GAAG,EAAE;MACV,IAAI,CAACA,GAAG,CAACyI,QAAQ,GAAG,CAAC,CAAC;IAC1B;IACA1J,sBAAsB,CAAC,IAAI,EAAEnB,0BAA0B,EAAE,IAAI,EAAE,GAAG,CAAC;EACvE;EACA;AACJ;AACA;AACA;AACA;EACIsP,uBAAuBA,CAACC,UAAU,EAAE;IAChC,IAAIC,OAAO,GAAGD,UAAU,CAACE,SAAS,CAACC,aAAa,CAAC,oBAAoB,CAAC,IAClEH,UAAU,CAACE,SAAS,CAACC,aAAa,CAAC,uBAAuB,CAAC;IAC/D,IAAI,CAACF,OAAO,EAAE;MACVA,OAAO,GAAG/E,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACvC;MACA8E,OAAO,CAACzJ,SAAS,CAAClE,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACyC,UAAU,CAAC;MAC9DiL,UAAU,CAACE,SAAS,CAACE,OAAO,CAACH,OAAO,CAAC;IACzC,CAAC,MACI,IAAIA,OAAO,CAACI,QAAQ,KAAK,QAAQ,EAAE;MACpC,MAAMC,MAAM,GAAGL,OAAO;MACtBA,OAAO,GAAG/E,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACvC;MACA8E,OAAO,CAACzJ,SAAS,CAAClE,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACyC,UAAU,CAAC;MAC9DuL,MAAM,CAACC,MAAM,CAACN,OAAO,CAAC;IAC1B;IACA,OAAOA,OAAO;EAClB;EACAO,sBAAsBA,CAACR,UAAU,EAAE;IAC/B,MAAM;MAAES;IAAW,CAAC,GAAGT,UAAU,CAACE,SAAS;IAC3C,IAAIO,UAAU,CAACJ,QAAQ,KAAK,KAAK,KAC5BI,UAAU,CAACjK,SAAS,CAAC8H,QAAQ,CAAC,mBAAmB,CAAC,IAC/CmC,UAAU,CAACjK,SAAS,CAAC8H,QAAQ,CAAC,sBAAsB,CAAC,CAAC,EAAE;MAC5DmC,UAAU,CAACvI,MAAM,CAAC,CAAC;IACvB;EACJ;AACJ;AACA5H,EAAE,GAAG6B,gBAAgB,EAAE5B,mBAAmB,GAAG,IAAImQ,OAAO,CAAC,CAAC,EAAElQ,mBAAmB,GAAG,IAAIkQ,OAAO,CAAC,CAAC,EAAEjQ,0BAA0B,GAAG,IAAIiQ,OAAO,CAAC,CAAC,EAAEhQ,iCAAiC,GAAG,IAAIgQ,OAAO,CAAC,CAAC,EAAE/P,yBAAyB,GAAG,IAAI+P,OAAO,CAAC,CAAC,EAAE9P,gCAAgC,GAAG,IAAI8P,OAAO,CAAC,CAAC,EAAE7P,iCAAiC,GAAG,IAAI6P,OAAO,CAAC,CAAC,EAAE5P,2BAA2B,GAAG,IAAI4P,OAAO,CAAC,CAAC,EAAE3P,8BAA8B,GAAG,IAAI2P,OAAO,CAAC,CAAC,EAAE1P,kCAAkC,GAAG,IAAI0P,OAAO,CAAC,CAAC,EAAEzP,2BAA2B,GAAG,IAAIyP,OAAO,CAAC,CAAC,EAAExP,2BAA2B,GAAG,IAAIwP,OAAO,CAAC,CAAC,EAAEvP,6BAA6B,GAAG,IAAIuP,OAAO,CAAC,CAAC,EAAEtP,wBAAwB,GAAG,IAAIsP,OAAO,CAAC,CAAC,EAAErQ,2BAA2B,GAAG,IAAIsQ,OAAO,CAAC,CAAC,EAAEtP,2BAA2B,GAAG,SAASA,2BAA2BA,CAAC,CAACoB,KAAK,EAAEC,MAAM,CAAC,EAAEkC,CAAC,EAAEC,CAAC,EAAE;EACvxB,CAACD,CAAC,EAAEC,CAAC,CAAC,GAAG,IAAI,CAAC0D,uBAAuB,CAAC3D,CAAC,EAAEC,CAAC,CAAC;EAC3C,IAAI,CAACD,CAAC,IAAIA,CAAC,GAAGnC,KAAK;EACnB,IAAI,CAACoC,CAAC,IAAIA,CAAC,GAAGnC,MAAM;EACpB,IAAI,CAACmE,iBAAiB,CAAC,CAAC;AAC5B,CAAC,EAAEvF,6BAA6B,GAAG,SAASA,6BAA6BA,CAACsD,CAAC,EAAEC,CAAC,EAAEwI,KAAK,EAAE;EACnF,QAAQA,KAAK;IACT,KAAK,EAAE;MACH,OAAO,CAACxI,CAAC,EAAE,CAACD,CAAC,CAAC;IAClB,KAAK,GAAG;MACJ,OAAO,CAAC,CAACA,CAAC,EAAE,CAACC,CAAC,CAAC;IACnB,KAAK,GAAG;MACJ,OAAO,CAAC,CAACA,CAAC,EAAED,CAAC,CAAC;IAClB;MACI,OAAO,CAACA,CAAC,EAAEC,CAAC,CAAC;EACrB;AACJ,CAAC,EAAEtD,sCAAsC,GAAG,SAASA,sCAAsCA,CAACiG,KAAK,EAAE;EAC/F,MAAM;IAAEkE;EAAM,CAAC,GAAGzJ,WAAW,CAAC0J,QAAQ;EACtC,MAAMiF,eAAe,GAAGpJ,KAAK,CAACG,MAAM,CAACE,OAAO,CAAC,qBAAqB,CAAC;EACnE,MAAMgJ,cAAc,GAAGrJ,KAAK,CAACG,MAAM,CAACE,OAAO,CAAC,qBAAqB,CAAC;EAClE,MAAMiJ,oBAAoB,GAAG,IAAI,CAACjN,UAAU,CAACkN,OAAO,CAAC,CAAC;EACtD;EACA,IAAKD,oBAAoB,KAAK5O,oBAAoB,CAAC8O,SAAS,IAAI,CAACJ,eAAe,IAC3EE,oBAAoB,KAAK5O,oBAAoB,CAAC+O,QAAQ,IAAI,CAACJ,cAAe,EAAE;IAC7E;EACJ;EACA,IAAKrJ,KAAK,CAACqE,OAAO,IAAI,CAACH,KAAK,IACxBlE,KAAK,CAAC0J,QAAQ,IACb1J,KAAK,CAAC2J,OAAO,IAAIzF,KAAM,EAAE;IAC1B,IAAI,CAACnJ,MAAM,CAAC6O,cAAc,CAAC,IAAI,CAAC;EACpC,CAAC,MACI;IACD,IAAI,CAAC7O,MAAM,CAACkF,WAAW,CAAC,IAAI,CAAC;EACjC;AACJ,CAAC,EAAEjG,kCAAkC,GAAG,SAASA,kCAAkCA,CAACgG,KAAK,EAAE;EACvF,MAAM6J,UAAU,GAAG,IAAI,CAACxN,UAAU,CAACwN,UAAU,CAAC,IAAI,CAAC;EACnD,IAAI,CAACxN,UAAU,CAACyN,gBAAgB,CAAC,CAAC;EAClC,MAAMC,EAAE,GAAG,IAAIC,eAAe,CAAC,CAAC;EAChC,MAAMC,MAAM,GAAG,IAAI,CAAC5N,UAAU,CAAC6N,cAAc,CAACH,EAAE,CAAC;EACjD,IAAIF,UAAU,EAAE;IACZ;IACAzP,sBAAsB,CAAC,IAAI,EAAEX,2BAA2B,EAAEuG,KAAK,CAACmK,OAAO,EAAE,GAAG,CAAC;IAC7E/P,sBAAsB,CAAC,IAAI,EAAEV,2BAA2B,EAAEsG,KAAK,CAACoK,OAAO,EAAE,GAAG,CAAC;IAC7E,MAAMC,mBAAmB,GAAGC,CAAC,IAAI;MAC7B,MAAM;QAAEH,OAAO,EAAE/M,CAAC;QAAEgN,OAAO,EAAE/M;MAAE,CAAC,GAAGiN,CAAC;MACpC,MAAM,CAACzJ,EAAE,EAAEC,EAAE,CAAC,GAAG,IAAI,CAACC,uBAAuB,CAAC3D,CAAC,GAAGjD,sBAAsB,CAAC,IAAI,EAAEV,2BAA2B,EAAE,GAAG,CAAC,EAAE4D,CAAC,GAAGlD,sBAAsB,CAAC,IAAI,EAAET,2BAA2B,EAAE,GAAG,CAAC,CAAC;MACrLU,sBAAsB,CAAC,IAAI,EAAEX,2BAA2B,EAAE2D,CAAC,EAAE,GAAG,CAAC;MACjEhD,sBAAsB,CAAC,IAAI,EAAEV,2BAA2B,EAAE2D,CAAC,EAAE,GAAG,CAAC;MACjE,IAAI,CAAChB,UAAU,CAACkO,mBAAmB,CAAC1J,EAAE,EAAEC,EAAE,CAAC;IAC/C,CAAC;IACD0J,MAAM,CAACC,gBAAgB,CAAC,aAAa,EAAEJ,mBAAmB,EAAE;MACxDK,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE,IAAI;MACbV;IACJ,CAAC,CAAC;EACN;EACA,MAAMW,iBAAiB,GAAGA,CAAA,KAAM;IAC5Bb,EAAE,CAAC1D,KAAK,CAAC,CAAC;IACV,IAAIwD,UAAU,EAAE;MACZ,IAAI,CAACxO,GAAG,CAAC2D,SAAS,CAAC0B,MAAM,CAAC,QAAQ,CAAC;IACvC;IACAtG,sBAAsB,CAAC,IAAI,EAAEhB,gCAAgC,EAAE,KAAK,EAAE,GAAG,CAAC;IAC1E,IAAI,CAAC,IAAI,CAACiD,UAAU,CAACwO,cAAc,CAAC,CAAC,EAAE;MACnC1Q,sBAAsB,CAAC,IAAI,EAAEtB,2BAA2B,EAAE,GAAG,EAAEkB,sCAAsC,CAAC,CAAC+F,IAAI,CAAC,IAAI,EAAEE,KAAK,CAAC;IAC5H;EACJ,CAAC;EACDwK,MAAM,CAACC,gBAAgB,CAAC,WAAW,EAAEG,iBAAiB,EAAE;IAAEX;EAAO,CAAC,CAAC;EACnE;EACA;EACA;EACAO,MAAM,CAACC,gBAAgB,CAAC,MAAM,EAAEG,iBAAiB,EAAE;IAAEX;EAAO,CAAC,CAAC;AAClE,CAAC,EAAEhQ,mCAAmC,GAAG,SAASA,mCAAmCA,CAAA,EAAG;EACpF,IAAIE,sBAAsB,CAAC,IAAI,EAAEhB,yBAAyB,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAACkC,GAAG,EAAE;IAC3E;EACJ;EACAjB,sBAAsB,CAAC,IAAI,EAAEjB,yBAAyB,EAAE,IAAI6Q,eAAe,CAAC,CAAC,EAAE,GAAG,CAAC;EACnF,MAAMC,MAAM,GAAG,IAAI,CAAC5N,UAAU,CAAC6N,cAAc,CAAC/P,sBAAsB,CAAC,IAAI,EAAEhB,yBAAyB,EAAE,GAAG,CAAC,CAAC;EAC3G,IAAI,CAACkC,GAAG,CAACoP,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC1K,OAAO,CAAC+K,IAAI,CAAC,IAAI,CAAC,EAAE;IAAEb;EAAO,CAAC,CAAC;EACzE,IAAI,CAAC5O,GAAG,CAACoP,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAACvK,QAAQ,CAAC4K,IAAI,CAAC,IAAI,CAAC,EAAE;IAAEb;EAAO,CAAC,CAAC;AAC/E,CAAC,EAAE/P,8BAA8B,GAAG,SAASA,8BAA8BA,CAAA,EAAG;EAC1E;EACA;EACA;EACA;EACA;EACA;EACA;AAAA,CACH;AACDS,gBAAgB,CAAC8B,SAAS,GAAG,IAAIpC,SAAS,CAAC,uBAAuB,CAAC;AACnEM,gBAAgB,CAACyH,gBAAgB,GAAG,CAAC,CAAC;AACtCzH,gBAAgB,CAACgD,aAAa,GAAG,IAAIrD,YAAY,CAAC,CAAC;AACnDK,gBAAgB,CAAC6B,OAAO,GAAG,CAAC;AAC5B;AACA;AACA;AACA7B,gBAAgB,CAACoQ,iBAAiB,GAAG,IAAI;AACzC;AACA,MAAM/M,UAAU,SAASrD,gBAAgB,CAAC;EACtCC,WAAWA,CAAC2E,MAAM,EAAE;IAChB,KAAK,CAACA,MAAM,CAAC;IACb,IAAI,CAACjE,mBAAmB,GAAGiE,MAAM,CAACjE,mBAAmB;IACrD,IAAI,CAACS,OAAO,GAAG,IAAI;EACvB;EACA+J,SAASA,CAAA,EAAG;IACR,OAAO;MACH9K,EAAE,EAAE,IAAI,CAACM,mBAAmB;MAC5BS,OAAO,EAAE,IAAI;MACbZ,SAAS,EAAE,IAAI,CAACA;IACpB,CAAC;EACL;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}