{"ast": null, "code": "import { addYears } from './add-years';\n/**\n * A function that adds and subtracts centuries from a `Date` object.\n *\n * @param date - The initial date value.\n * @param offset - The number of centuries to add or subtract from the date.\n * @returns - A new `Date` instance.\n *\n * @example\n * ```ts-no-run\n * addCenturies(new Date(2016, 5, 1), 5); // 2516-6-1\n * addCenturies(new Date(2016, 5, 1), -5); // 1516-6-1\n * ```\n */\nexport var addCenturies = function (value, offset) {\n  return addYears(value, 100 * offset);\n};", "map": {"version": 3, "names": ["addYears", "addCenturies", "value", "offset"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/add-centuries.js"], "sourcesContent": ["import { addYears } from './add-years';\n/**\n * A function that adds and subtracts centuries from a `Date` object.\n *\n * @param date - The initial date value.\n * @param offset - The number of centuries to add or subtract from the date.\n * @returns - A new `Date` instance.\n *\n * @example\n * ```ts-no-run\n * addCenturies(new Date(2016, 5, 1), 5); // 2516-6-1\n * addCenturies(new Date(2016, 5, 1), -5); // 1516-6-1\n * ```\n */\nexport var addCenturies = function (value, offset) {\n    return addYears(value, 100 * offset);\n};\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,aAAa;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,YAAY,GAAG,SAAAA,CAAUC,KAAK,EAAEC,MAAM,EAAE;EAC/C,OAAOH,QAAQ,CAACE,KAAK,EAAE,GAAG,GAAGC,MAAM,CAAC;AACxC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}