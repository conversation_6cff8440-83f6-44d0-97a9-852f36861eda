import { FileAreaSettings } from '@app/types/fileAreaTypes';
import MenuItem from './types/MenuItemType';
import { WEBEndpointType } from '@iris/discovery.fe.client';
import { CustomUserClaims, UserClaims } from '@okta/okta-auth-js';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import constants from './utils/Constants';
import { RootState } from './store/store';
import { EvaluateTncResponse, TncDocument, TncDocumentState, TncStatus } from './types/tncTypes';
import { apiSlice as tncApiSlice } from '../app/api/tncApiSlice';

export interface IAppState {
  endPoints: WEBEndpointType[] | null;
  tenant: string | null;
  user: null | UserClaims<CustomUserClaims>;
  selectedSite: string;
  publishedFileCount: number | undefined;
  menuItems: MenuItem[] | [];
  dynamicBreadcrumbValues: [];
  fileAreaSettings: FileAreaSettings;
  apiError: string | undefined;
  menuApiError: string | undefined;
  authUrl: string | undefined;
  tenantContext: any | null;
  tnc: TncDocumentState;
}

const defaultSettings: FileAreaSettings = {
  urlFileUpload: false,
  internalFileStatusUpdate: false,
  internalFilesEmail: false,
  internalFileSetAssignee: false,
  internalFileSetProject: false,
  internalFilesDownload: false,
  internalFilesCheckinCheckout: false,
  internalFilesRecategorize: false,
  fileAreaFolderStructure: false,
  fileAreaManageEmailSubscription: false,
  fileAreaManageTags: false,
  internalFilesAssign: false,
  internalFilesCopy: false,
  internalFilesDelete: false,
  internalFilesHistory: false,
  internalFilesMove: false,
  internalFilesRename: false,
  internalFilesUpdate: false,
  internalFilesUpload: false,
  internalFilesView: false,
  internalFilesViewAssignmentHistory: false,
  internalFilesViewCheckoutHistory: false,
  internalFilesViewProperties: false,
  internalFilesViewVersionHistory: false,
  portalFilesView: false,
  internalFilesPublishUnPublish: false,
};

export const initialState: IAppState = {
  endPoints: null,
  tenant: localStorage.getItem(constants.tenatKey),
  user: null,
  menuItems: [],
  selectedSite: 'N/A',
  publishedFileCount: undefined,
  dynamicBreadcrumbValues: [],
  fileAreaSettings: defaultSettings,
  apiError: undefined,
  menuApiError: undefined,
  authUrl: undefined,
  tenantContext: null,
  tnc: {
    isValidated: false,
    document: null,
  }
};

export type IAppInitializing = IAppState & { isInitializing: boolean };

export const appSlice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    setTenant: (state: IAppState, action: PayloadAction<string | null>) => {
      if (action.payload) localStorage.setItem(constants.tenatKey, action.payload);
      else localStorage.removeItem(constants.tenatKey);
      state.endPoints = null;
      state.tenant = action.payload;
    },
    setEndpoints: (state: IAppState, action: PayloadAction<WEBEndpointType[]>) => {
      state.endPoints = action.payload;
    },
    setUser: (state: IAppState, action: PayloadAction<UserClaims<CustomUserClaims>>) => {
      state.user = action.payload;
    },
    setSelectedSite: (state: IAppState, action: PayloadAction<string>) => {
      state.selectedSite = action.payload;
    },
    setPublishedFileCount: (state: IAppState, action: PayloadAction<number | undefined>) => {
      state.publishedFileCount = action.payload;
    },
    setMenu: (state: IAppState, action: PayloadAction<MenuItem[]>) => {
      state.menuItems = action.payload;
    },
    setMenuApiError: (state: IAppState, action: PayloadAction<string | undefined>) => {
      state.menuApiError = action.payload;
    },
    setApiError: (state: IAppState, action: PayloadAction<string | undefined>) => {
      state.apiError = action.payload;
    },
    setTenantContext: (state: any, action: PayloadAction<string>) => {
      state.tenantContext = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addMatcher(tncApiSlice.endpoints.evaluateTnc.matchFulfilled, handleEvaluateTncSuccess)
      .addMatcher(tncApiSlice.endpoints.acceptTnc.matchFulfilled, handleAcceptTncSuccess);
  },
});

const handleEvaluateTncSuccess = (state: IAppState, action: PayloadAction<EvaluateTncResponse>) => {
  const { status, termsAndConditionsId, statementOfAgreement, triggerType, file } = action.payload;

  if (status === TncStatus.ACCEPTED) {
    state.tnc.isValidated = true;
  } else if (status === TncStatus.PENDING_ACCEPTANCE) {
    state.tnc.document = {
      termsAndConditionsId: termsAndConditionsId!,
      statementOfAgreement: statementOfAgreement!,
      triggerType: triggerType!,
      ...file!
    };
  }
};

const handleAcceptTncSuccess = (state: IAppState) => {
  state.tnc.isValidated = true;
  state.tnc.document = null;
};

export const { setEndpoints, setTenant, setUser, setMenu, setSelectedSite, setPublishedFileCount, setApiError, setMenuApiError, setTenantContext } = appSlice.actions;
export const selectAppState = (state: RootState): IAppState => state.app;
export const selectAppInitializing = (state: RootState): IAppInitializing => {
  return {
    ...state.app,
    isInitializing: !(state.app.tenant && state.app.tenantContext && state.app.endPoints && state.app.menuItems.length && state.app.selectedSite && !state.app.apiError),
  };
};

export default appSlice.reducer;
