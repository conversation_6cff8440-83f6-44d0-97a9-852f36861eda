{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport t from \"prop-types\";\nimport { classNames as r } from \"@progress/kendo-react-common\";\nconst s = class s extends e.Component {\n  /**\n   * @hidden\n   */\n  render() {\n    return this.props.url ? /* @__PURE__ */e.createElement(\"a\", {\n      className: this.getMenuItemClassName(),\n      role: \"presentation\",\n      href: this.props.url,\n      tabIndex: -1\n    }, this.props.children) : /* @__PURE__ */e.createElement(\"span\", {\n      id: this.props.id,\n      className: r(this.getMenuItemClassName(), this.props.className),\n      style: this.props.style,\n      role: \"presentation\"\n    }, this.props.children);\n  }\n  getMenuItemClassName() {\n    return r(\"k-link\", \"k-menu-link\", {\n      \"k-active\": this.props.opened\n    });\n  }\n};\ns.propTypes = {\n  opened: t.bool,\n  url: t.string\n};\nlet p = s;\nexport { p as MenuItemLink };", "map": {"version": 3, "names": ["e", "t", "classNames", "r", "s", "Component", "render", "props", "url", "createElement", "className", "getMenuItemClassName", "role", "href", "tabIndex", "children", "id", "style", "opened", "propTypes", "bool", "string", "p", "MenuItemLink"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/menu/components/MenuItemLink.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport t from \"prop-types\";\nimport { classNames as r } from \"@progress/kendo-react-common\";\nconst s = class s extends e.Component {\n  /**\n   * @hidden\n   */\n  render() {\n    return this.props.url ? /* @__PURE__ */ e.createElement(\"a\", { className: this.getMenuItemClassName(), role: \"presentation\", href: this.props.url, tabIndex: -1 }, this.props.children) : /* @__PURE__ */ e.createElement(\n      \"span\",\n      {\n        id: this.props.id,\n        className: r(this.getMenuItemClassName(), this.props.className),\n        style: this.props.style,\n        role: \"presentation\"\n      },\n      this.props.children\n    );\n  }\n  getMenuItemClassName() {\n    return r(\"k-link\", \"k-menu-link\", {\n      \"k-active\": this.props.opened\n    });\n  }\n};\ns.propTypes = {\n  opened: t.bool,\n  url: t.string\n};\nlet p = s;\nexport {\n  p as MenuItemLink\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AAC9D,MAAMC,CAAC,GAAG,MAAMA,CAAC,SAASJ,CAAC,CAACK,SAAS,CAAC;EACpC;AACF;AACA;EACEC,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK,CAACC,GAAG,GAAG,eAAgBR,CAAC,CAACS,aAAa,CAAC,GAAG,EAAE;MAAEC,SAAS,EAAE,IAAI,CAACC,oBAAoB,CAAC,CAAC;MAAEC,IAAI,EAAE,cAAc;MAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACC,GAAG;MAAEM,QAAQ,EAAE,CAAC;IAAE,CAAC,EAAE,IAAI,CAACP,KAAK,CAACQ,QAAQ,CAAC,GAAG,eAAgBf,CAAC,CAACS,aAAa,CACvN,MAAM,EACN;MACEO,EAAE,EAAE,IAAI,CAACT,KAAK,CAACS,EAAE;MACjBN,SAAS,EAAEP,CAAC,CAAC,IAAI,CAACQ,oBAAoB,CAAC,CAAC,EAAE,IAAI,CAACJ,KAAK,CAACG,SAAS,CAAC;MAC/DO,KAAK,EAAE,IAAI,CAACV,KAAK,CAACU,KAAK;MACvBL,IAAI,EAAE;IACR,CAAC,EACD,IAAI,CAACL,KAAK,CAACQ,QACb,CAAC;EACH;EACAJ,oBAAoBA,CAAA,EAAG;IACrB,OAAOR,CAAC,CAAC,QAAQ,EAAE,aAAa,EAAE;MAChC,UAAU,EAAE,IAAI,CAACI,KAAK,CAACW;IACzB,CAAC,CAAC;EACJ;AACF,CAAC;AACDd,CAAC,CAACe,SAAS,GAAG;EACZD,MAAM,EAAEjB,CAAC,CAACmB,IAAI;EACdZ,GAAG,EAAEP,CAAC,CAACoB;AACT,CAAC;AACD,IAAIC,CAAC,GAAGlB,CAAC;AACT,SACEkB,CAAC,IAAIC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}