{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { isPresent as e } from \"./misc.mjs\";\nimport { parseColor as n } from \"@progress/kendo-drawing\";\nclass f {\n  constructor() {\n    this.colorRows = [];\n  }\n  setColorMatrix(o, r) {\n    if (this.colorRows = [], !!(e(o) && o.length)) {\n      r = r || o.length;\n      for (let t = 0; t < o.length; t += r) {\n        const s = o.slice(t, r + t);\n        this.colorRows.push(s);\n      }\n    }\n  }\n  getCellCoordsFor(o) {\n    if (!e(o)) return;\n    const r = o && n(o, !0),\n      t = [o];\n    e(r) && t.push(r.toCss(), r.toCssRgba());\n    for (let s = 0; s < this.colorRows.length; s++) for (let i = 0; i < this.colorRows[s].length; i++) if (t.some(l => l === this.colorRows[s][i])) return {\n      row: s,\n      col: i\n    };\n  }\n  getColorAt(o) {\n    if (e(o) && e(this.colorRows[o.row])) return this.colorRows[o.row][o.col];\n  }\n  getNextCell(o, r, t) {\n    if (!(e(o) && e(o.row) && e(o.col))) return {\n      row: 0,\n      col: 0\n    };\n    const s = this.clampIndex(o.row + t, this.colorRows.length - 1),\n      i = this.clampIndex(o.col + r, this.colorRows[s].length - 1);\n    return {\n      row: s,\n      col: i\n    };\n  }\n  clampIndex(o, r) {\n    return o < 0 ? 0 : o > r ? r : o;\n  }\n}\nexport { f as ColorPaletteService };", "map": {"version": 3, "names": ["isPresent", "e", "parseColor", "n", "f", "constructor", "colorRows", "setColorMatrix", "o", "r", "length", "t", "s", "slice", "push", "getCellCoordsFor", "to<PERSON>s", "toCssRgba", "i", "some", "l", "row", "col", "getColorAt", "getNextCell", "clampIndex", "ColorPaletteService"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-inputs/colors/utils/color-palette.service.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { isPresent as e } from \"./misc.mjs\";\nimport { parseColor as n } from \"@progress/kendo-drawing\";\nclass f {\n  constructor() {\n    this.colorRows = [];\n  }\n  setColorMatrix(o, r) {\n    if (this.colorRows = [], !!(e(o) && o.length)) {\n      r = r || o.length;\n      for (let t = 0; t < o.length; t += r) {\n        const s = o.slice(t, r + t);\n        this.colorRows.push(s);\n      }\n    }\n  }\n  getCellCoordsFor(o) {\n    if (!e(o))\n      return;\n    const r = o && n(o, !0), t = [o];\n    e(r) && t.push(r.toCss(), r.toCssRgba());\n    for (let s = 0; s < this.colorRows.length; s++)\n      for (let i = 0; i < this.colorRows[s].length; i++)\n        if (t.some((l) => l === this.colorRows[s][i]))\n          return { row: s, col: i };\n  }\n  getColorAt(o) {\n    if (e(o) && e(this.colorRows[o.row]))\n      return this.colorRows[o.row][o.col];\n  }\n  getNextCell(o, r, t) {\n    if (!(e(o) && e(o.row) && e(o.col)))\n      return { row: 0, col: 0 };\n    const s = this.clampIndex(o.row + t, this.colorRows.length - 1), i = this.clampIndex(o.col + r, this.colorRows[s].length - 1);\n    return { row: s, col: i };\n  }\n  clampIndex(o, r) {\n    return o < 0 ? 0 : o > r ? r : o;\n  }\n}\nexport {\n  f as ColorPaletteService\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,IAAIC,CAAC,QAAQ,YAAY;AAC3C,SAASC,UAAU,IAAIC,CAAC,QAAQ,yBAAyB;AACzD,MAAMC,CAAC,CAAC;EACNC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,SAAS,GAAG,EAAE;EACrB;EACAC,cAAcA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACnB,IAAI,IAAI,CAACH,SAAS,GAAG,EAAE,EAAE,CAAC,EAAEL,CAAC,CAACO,CAAC,CAAC,IAAIA,CAAC,CAACE,MAAM,CAAC,EAAE;MAC7CD,CAAC,GAAGA,CAAC,IAAID,CAAC,CAACE,MAAM;MACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,CAACE,MAAM,EAAEC,CAAC,IAAIF,CAAC,EAAE;QACpC,MAAMG,CAAC,GAAGJ,CAAC,CAACK,KAAK,CAACF,CAAC,EAAEF,CAAC,GAAGE,CAAC,CAAC;QAC3B,IAAI,CAACL,SAAS,CAACQ,IAAI,CAACF,CAAC,CAAC;MACxB;IACF;EACF;EACAG,gBAAgBA,CAACP,CAAC,EAAE;IAClB,IAAI,CAACP,CAAC,CAACO,CAAC,CAAC,EACP;IACF,MAAMC,CAAC,GAAGD,CAAC,IAAIL,CAAC,CAACK,CAAC,EAAE,CAAC,CAAC,CAAC;MAAEG,CAAC,GAAG,CAACH,CAAC,CAAC;IAChCP,CAAC,CAACQ,CAAC,CAAC,IAAIE,CAAC,CAACG,IAAI,CAACL,CAAC,CAACO,KAAK,CAAC,CAAC,EAAEP,CAAC,CAACQ,SAAS,CAAC,CAAC,CAAC;IACxC,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACN,SAAS,CAACI,MAAM,EAAEE,CAAC,EAAE,EAC5C,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACZ,SAAS,CAACM,CAAC,CAAC,CAACF,MAAM,EAAEQ,CAAC,EAAE,EAC/C,IAAIP,CAAC,CAACQ,IAAI,CAAEC,CAAC,IAAKA,CAAC,KAAK,IAAI,CAACd,SAAS,CAACM,CAAC,CAAC,CAACM,CAAC,CAAC,CAAC,EAC3C,OAAO;MAAEG,GAAG,EAAET,CAAC;MAAEU,GAAG,EAAEJ;IAAE,CAAC;EACjC;EACAK,UAAUA,CAACf,CAAC,EAAE;IACZ,IAAIP,CAAC,CAACO,CAAC,CAAC,IAAIP,CAAC,CAAC,IAAI,CAACK,SAAS,CAACE,CAAC,CAACa,GAAG,CAAC,CAAC,EAClC,OAAO,IAAI,CAACf,SAAS,CAACE,CAAC,CAACa,GAAG,CAAC,CAACb,CAAC,CAACc,GAAG,CAAC;EACvC;EACAE,WAAWA,CAAChB,CAAC,EAAEC,CAAC,EAAEE,CAAC,EAAE;IACnB,IAAI,EAAEV,CAAC,CAACO,CAAC,CAAC,IAAIP,CAAC,CAACO,CAAC,CAACa,GAAG,CAAC,IAAIpB,CAAC,CAACO,CAAC,CAACc,GAAG,CAAC,CAAC,EACjC,OAAO;MAAED,GAAG,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAE,CAAC;IAC3B,MAAMV,CAAC,GAAG,IAAI,CAACa,UAAU,CAACjB,CAAC,CAACa,GAAG,GAAGV,CAAC,EAAE,IAAI,CAACL,SAAS,CAACI,MAAM,GAAG,CAAC,CAAC;MAAEQ,CAAC,GAAG,IAAI,CAACO,UAAU,CAACjB,CAAC,CAACc,GAAG,GAAGb,CAAC,EAAE,IAAI,CAACH,SAAS,CAACM,CAAC,CAAC,CAACF,MAAM,GAAG,CAAC,CAAC;IAC7H,OAAO;MAAEW,GAAG,EAAET,CAAC;MAAEU,GAAG,EAAEJ;IAAE,CAAC;EAC3B;EACAO,UAAUA,CAACjB,CAAC,EAAEC,CAAC,EAAE;IACf,OAAOD,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC,GAAGD,CAAC;EAClC;AACF;AACA,SACEJ,CAAC,IAAIsB,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}