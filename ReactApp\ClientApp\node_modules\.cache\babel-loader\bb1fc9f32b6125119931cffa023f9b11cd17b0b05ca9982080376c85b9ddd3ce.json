{"ast": null, "code": "import { getLocaleInfo } from './info';\nimport { EMPTY } from '../common/constants';\nfunction lowerArray(arr) {\n  var result = [];\n  for (var idx = 0; idx < arr.length; idx++) {\n    result.push(arr[idx].toLowerCase());\n  }\n  return result;\n}\nfunction lowerObject(obj) {\n  var result = {};\n  for (var field in obj) {\n    result[field] = obj[field].toLowerCase();\n  }\n  return result;\n}\nfunction cloneLower(obj) {\n  var result = Array.isArray(obj) ? lowerArray(obj) : lowerObject(obj);\n  return result;\n}\nexport default function dateFormatNames(locale, options) {\n  var type = options.type;\n  var nameType = options.nameType;\n  var standAlone = options.standAlone;\n  var lower = options.lower;\n  var info = getLocaleInfo(locale);\n  var formatType = standAlone ? \"stand-alone\" : \"format\";\n  var lowerNameType = (lower ? \"lower-\" : EMPTY) + nameType;\n  var formatNames = info.calendar[type][formatType];\n  var result = formatNames[lowerNameType];\n  if (!result && lower) {\n    result = formatNames[lowerNameType] = cloneLower(formatNames[nameType]);\n  }\n  return result;\n}", "map": {"version": 3, "names": ["getLocaleInfo", "EMPTY", "lowerArray", "arr", "result", "idx", "length", "push", "toLowerCase", "lowerObject", "obj", "field", "cloneLower", "Array", "isArray", "dateFormatNames", "locale", "options", "type", "nameType", "standAlone", "lower", "info", "formatType", "lowerNameType", "formatNames", "calendar"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-intl/dist/es/cldr/date-format-names.js"], "sourcesContent": ["import { getLocaleInfo } from './info';\nimport { EMPTY } from '../common/constants';\n\nfunction lowerArray(arr) {\n    var result = [];\n    for (var idx = 0; idx < arr.length; idx++) {\n        result.push(arr[idx].toLowerCase());\n    }\n    return result;\n}\n\nfunction lowerObject(obj) {\n    var result = {};\n    for (var field in obj) {\n        result[field] = obj[field].toLowerCase();\n    }\n    return result;\n}\n\nfunction cloneLower(obj) {\n    var result = Array.isArray(obj) ? lowerArray(obj) : lowerObject(obj);\n    return result;\n}\n\nexport default function dateFormatNames(locale, options) {\n    var type = options.type;\n    var nameType = options.nameType;\n    var standAlone = options.standAlone;\n    var lower = options.lower;\n    var info = getLocaleInfo(locale);\n    var formatType = standAlone ? \"stand-alone\" : \"format\";\n    var lowerNameType = (lower ? \"lower-\" : EMPTY) + nameType;\n    var formatNames = info.calendar[type][formatType];\n    var result = formatNames[lowerNameType];\n    if (!result && lower) {\n        result = formatNames[lowerNameType] = cloneLower(formatNames[nameType]);\n    }\n    return result;\n}"], "mappings": "AAAA,SAASA,aAAa,QAAQ,QAAQ;AACtC,SAASC,KAAK,QAAQ,qBAAqB;AAE3C,SAASC,UAAUA,CAACC,GAAG,EAAE;EACrB,IAAIC,MAAM,GAAG,EAAE;EACf,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGF,GAAG,CAACG,MAAM,EAAED,GAAG,EAAE,EAAE;IACvCD,MAAM,CAACG,IAAI,CAACJ,GAAG,CAACE,GAAG,CAAC,CAACG,WAAW,CAAC,CAAC,CAAC;EACvC;EACA,OAAOJ,MAAM;AACjB;AAEA,SAASK,WAAWA,CAACC,GAAG,EAAE;EACtB,IAAIN,MAAM,GAAG,CAAC,CAAC;EACf,KAAK,IAAIO,KAAK,IAAID,GAAG,EAAE;IACnBN,MAAM,CAACO,KAAK,CAAC,GAAGD,GAAG,CAACC,KAAK,CAAC,CAACH,WAAW,CAAC,CAAC;EAC5C;EACA,OAAOJ,MAAM;AACjB;AAEA,SAASQ,UAAUA,CAACF,GAAG,EAAE;EACrB,IAAIN,MAAM,GAAGS,KAAK,CAACC,OAAO,CAACJ,GAAG,CAAC,GAAGR,UAAU,CAACQ,GAAG,CAAC,GAAGD,WAAW,CAACC,GAAG,CAAC;EACpE,OAAON,MAAM;AACjB;AAEA,eAAe,SAASW,eAAeA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACrD,IAAIC,IAAI,GAAGD,OAAO,CAACC,IAAI;EACvB,IAAIC,QAAQ,GAAGF,OAAO,CAACE,QAAQ;EAC/B,IAAIC,UAAU,GAAGH,OAAO,CAACG,UAAU;EACnC,IAAIC,KAAK,GAAGJ,OAAO,CAACI,KAAK;EACzB,IAAIC,IAAI,GAAGtB,aAAa,CAACgB,MAAM,CAAC;EAChC,IAAIO,UAAU,GAAGH,UAAU,GAAG,aAAa,GAAG,QAAQ;EACtD,IAAII,aAAa,GAAG,CAACH,KAAK,GAAG,QAAQ,GAAGpB,KAAK,IAAIkB,QAAQ;EACzD,IAAIM,WAAW,GAAGH,IAAI,CAACI,QAAQ,CAACR,IAAI,CAAC,CAACK,UAAU,CAAC;EACjD,IAAInB,MAAM,GAAGqB,WAAW,CAACD,aAAa,CAAC;EACvC,IAAI,CAACpB,MAAM,IAAIiB,KAAK,EAAE;IAClBjB,MAAM,GAAGqB,WAAW,CAACD,aAAa,CAAC,GAAGZ,UAAU,CAACa,WAAW,CAACN,QAAQ,CAAC,CAAC;EAC3E;EACA,OAAOf,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}