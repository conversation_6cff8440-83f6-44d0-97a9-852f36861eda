{"ast": null, "code": "import { Class } from '../common';\nvar instance;\nvar AnimationFactory = function (Class) {\n  function AnimationFactory() {\n    Class.call(this);\n    this._items = [];\n  }\n  if (Class) AnimationFactory.__proto__ = Class;\n  AnimationFactory.prototype = Object.create(Class && Class.prototype);\n  AnimationFactory.prototype.constructor = AnimationFactory;\n  var staticAccessors = {\n    current: {\n      configurable: true\n    }\n  };\n  staticAccessors.current.get = function () {\n    if (!instance) {\n      instance = new AnimationFactory();\n    }\n    return instance;\n  };\n  AnimationFactory.prototype.register = function register(name, type) {\n    this._items.push({\n      name: name,\n      type: type\n    });\n  };\n  AnimationFactory.prototype.create = function create(element, options) {\n    var items = this._items;\n    var match;\n    if (options && options.type) {\n      var type = options.type.toLowerCase();\n      for (var i = 0; i < items.length; i++) {\n        if (items[i].name.toLowerCase() === type) {\n          match = items[i];\n          break;\n        }\n      }\n    }\n    if (match) {\n      return new match.type(element, options);\n    }\n  };\n  Object.defineProperties(AnimationFactory, staticAccessors);\n  return AnimationFactory;\n}(Class);\nexport default AnimationFactory;", "map": {"version": 3, "names": ["Class", "instance", "AnimationFactory", "call", "_items", "__proto__", "prototype", "Object", "create", "constructor", "staticAccessors", "current", "configurable", "get", "register", "name", "type", "push", "element", "options", "items", "match", "toLowerCase", "i", "length", "defineProperties"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/animations/animation-factory.js"], "sourcesContent": ["import { Class } from '../common';\n\nvar instance;\n\nvar AnimationFactory = (function (Class) {\n    function AnimationFactory() {\n        Class.call(this);\n\n        this._items = [];\n    }\n\n    if ( Class ) AnimationFactory.__proto__ = Class;\n    AnimationFactory.prototype = Object.create( Class && Class.prototype );\n    AnimationFactory.prototype.constructor = AnimationFactory;\n\n    var staticAccessors = { current: { configurable: true } };\n\n    staticAccessors.current.get = function () {\n        if (!instance) {\n            instance = new AnimationFactory();\n        }\n\n        return instance;\n    };\n\n    AnimationFactory.prototype.register = function register (name, type) {\n        this._items.push({\n            name: name,\n            type: type\n        });\n    };\n\n    AnimationFactory.prototype.create = function create (element, options) {\n        var items = this._items;\n        var match;\n\n        if (options && options.type) {\n            var type = options.type.toLowerCase();\n            for (var i = 0; i < items.length; i++) {\n                if (items[i].name.toLowerCase() === type) {\n                    match = items[i];\n                    break;\n                }\n            }\n        }\n\n        if (match) {\n            return new match.type(element, options);\n        }\n    };\n\n    Object.defineProperties( AnimationFactory, staticAccessors );\n\n    return AnimationFactory;\n}(Class));\n\nexport default AnimationFactory;\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,WAAW;AAEjC,IAAIC,QAAQ;AAEZ,IAAIC,gBAAgB,GAAI,UAAUF,KAAK,EAAE;EACrC,SAASE,gBAAgBA,CAAA,EAAG;IACxBF,KAAK,CAACG,IAAI,CAAC,IAAI,CAAC;IAEhB,IAAI,CAACC,MAAM,GAAG,EAAE;EACpB;EAEA,IAAKJ,KAAK,EAAGE,gBAAgB,CAACG,SAAS,GAAGL,KAAK;EAC/CE,gBAAgB,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAER,KAAK,IAAIA,KAAK,CAACM,SAAU,CAAC;EACtEJ,gBAAgB,CAACI,SAAS,CAACG,WAAW,GAAGP,gBAAgB;EAEzD,IAAIQ,eAAe,GAAG;IAAEC,OAAO,EAAE;MAAEC,YAAY,EAAE;IAAK;EAAE,CAAC;EAEzDF,eAAe,CAACC,OAAO,CAACE,GAAG,GAAG,YAAY;IACtC,IAAI,CAACZ,QAAQ,EAAE;MACXA,QAAQ,GAAG,IAAIC,gBAAgB,CAAC,CAAC;IACrC;IAEA,OAAOD,QAAQ;EACnB,CAAC;EAEDC,gBAAgB,CAACI,SAAS,CAACQ,QAAQ,GAAG,SAASA,QAAQA,CAAEC,IAAI,EAAEC,IAAI,EAAE;IACjE,IAAI,CAACZ,MAAM,CAACa,IAAI,CAAC;MACbF,IAAI,EAAEA,IAAI;MACVC,IAAI,EAAEA;IACV,CAAC,CAAC;EACN,CAAC;EAEDd,gBAAgB,CAACI,SAAS,CAACE,MAAM,GAAG,SAASA,MAAMA,CAAEU,OAAO,EAAEC,OAAO,EAAE;IACnE,IAAIC,KAAK,GAAG,IAAI,CAAChB,MAAM;IACvB,IAAIiB,KAAK;IAET,IAAIF,OAAO,IAAIA,OAAO,CAACH,IAAI,EAAE;MACzB,IAAIA,IAAI,GAAGG,OAAO,CAACH,IAAI,CAACM,WAAW,CAAC,CAAC;MACrC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;QACnC,IAAIH,KAAK,CAACG,CAAC,CAAC,CAACR,IAAI,CAACO,WAAW,CAAC,CAAC,KAAKN,IAAI,EAAE;UACtCK,KAAK,GAAGD,KAAK,CAACG,CAAC,CAAC;UAChB;QACJ;MACJ;IACJ;IAEA,IAAIF,KAAK,EAAE;MACP,OAAO,IAAIA,KAAK,CAACL,IAAI,CAACE,OAAO,EAAEC,OAAO,CAAC;IAC3C;EACJ,CAAC;EAEDZ,MAAM,CAACkB,gBAAgB,CAAEvB,gBAAgB,EAAEQ,eAAgB,CAAC;EAE5D,OAAOR,gBAAgB;AAC3B,CAAC,CAACF,KAAK,CAAE;AAET,eAAeE,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}