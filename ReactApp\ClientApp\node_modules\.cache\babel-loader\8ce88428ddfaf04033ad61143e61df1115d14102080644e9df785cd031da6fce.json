{"ast": null, "code": "import { defined } from '../util';\nvar SurfaceCursor = function SurfaceCursor(surface) {\n  surface.bind(\"mouseenter\", this._mouseenter.bind(this));\n  surface.bind(\"mouseleave\", this._mouseleave.bind(this));\n  this.element = surface.element;\n};\nSurfaceCursor.prototype.clear = function clear() {\n  this._resetCursor();\n};\nSurfaceCursor.prototype.destroy = function destroy() {\n  this._resetCursor();\n  delete this.element;\n};\nSurfaceCursor.prototype._mouseenter = function _mouseenter(e) {\n  var cursor = this._shapeCursor(e);\n  if (!cursor) {\n    this._resetCursor();\n  } else {\n    if (!this._current) {\n      this._defaultCursor = this._getCursor();\n    }\n    this._setCursor(cursor);\n  }\n};\nSurfaceCursor.prototype._mouseleave = function _mouseleave() {\n  this._resetCursor();\n};\nSurfaceCursor.prototype._shapeCursor = function _shapeCursor(e) {\n  var shape = e.element;\n  while (shape && !defined(shape.options.cursor)) {\n    shape = shape.parent;\n  }\n  if (shape) {\n    return shape.options.cursor;\n  }\n};\nSurfaceCursor.prototype._getCursor = function _getCursor() {\n  if (this.element) {\n    return this.element.style.cursor;\n  }\n};\nSurfaceCursor.prototype._setCursor = function _setCursor(cursor) {\n  if (this.element) {\n    this.element.style.cursor = cursor;\n    this._current = cursor;\n  }\n};\nSurfaceCursor.prototype._resetCursor = function _resetCursor() {\n  if (this._current) {\n    this._setCursor(this._defaultCursor || \"\");\n    delete this._current;\n  }\n};\nexport default SurfaceCursor;", "map": {"version": 3, "names": ["defined", "SurfaceCursor", "surface", "bind", "_mouseenter", "_mouseleave", "element", "prototype", "clear", "_resetCursor", "destroy", "e", "cursor", "_shapeCursor", "_current", "_defaultCursor", "_get<PERSON>ursor", "_setCursor", "shape", "options", "parent", "style"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/canvas/surface-cursor.js"], "sourcesContent": ["import { defined } from '../util';\n\nvar SurfaceCursor = function SurfaceCursor(surface) {\n    surface.bind(\"mouseenter\", this._mouseenter.bind(this));\n    surface.bind(\"mouseleave\", this._mouseleave.bind(this));\n\n    this.element = surface.element;\n};\n\nSurfaceCursor.prototype.clear = function clear () {\n    this._resetCursor();\n};\n\nSurfaceCursor.prototype.destroy = function destroy () {\n    this._resetCursor();\n    delete this.element;\n};\n\nSurfaceCursor.prototype._mouseenter = function _mouseenter (e) {\n    var cursor = this._shapeCursor(e);\n\n    if (!cursor) {\n        this._resetCursor();\n    } else {\n        if (!this._current) {\n            this._defaultCursor = this._getCursor();\n        }\n\n        this._setCursor(cursor);\n    }\n};\n\nSurfaceCursor.prototype._mouseleave = function _mouseleave () {\n    this._resetCursor();\n};\n\nSurfaceCursor.prototype._shapeCursor = function _shapeCursor (e) {\n    var shape = e.element;\n\n    while (shape && !defined(shape.options.cursor)) {\n        shape = shape.parent;\n    }\n\n    if (shape) {\n        return shape.options.cursor;\n    }\n};\n\nSurfaceCursor.prototype._getCursor = function _getCursor () {\n    if (this.element) {\n        return this.element.style.cursor;\n    }\n};\n\nSurfaceCursor.prototype._setCursor = function _setCursor (cursor) {\n    if (this.element) {\n        this.element.style.cursor = cursor;\n        this._current = cursor;\n    }\n};\n\nSurfaceCursor.prototype._resetCursor = function _resetCursor () {\n    if (this._current) {\n        this._setCursor(this._defaultCursor || \"\");\n        delete this._current;\n    }\n};\n\nexport default SurfaceCursor;\n\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,SAAS;AAEjC,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,OAAO,EAAE;EAChDA,OAAO,CAACC,IAAI,CAAC,YAAY,EAAE,IAAI,CAACC,WAAW,CAACD,IAAI,CAAC,IAAI,CAAC,CAAC;EACvDD,OAAO,CAACC,IAAI,CAAC,YAAY,EAAE,IAAI,CAACE,WAAW,CAACF,IAAI,CAAC,IAAI,CAAC,CAAC;EAEvD,IAAI,CAACG,OAAO,GAAGJ,OAAO,CAACI,OAAO;AAClC,CAAC;AAEDL,aAAa,CAACM,SAAS,CAACC,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAI;EAC9C,IAAI,CAACC,YAAY,CAAC,CAAC;AACvB,CAAC;AAEDR,aAAa,CAACM,SAAS,CAACG,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;EAClD,IAAI,CAACD,YAAY,CAAC,CAAC;EACnB,OAAO,IAAI,CAACH,OAAO;AACvB,CAAC;AAEDL,aAAa,CAACM,SAAS,CAACH,WAAW,GAAG,SAASA,WAAWA,CAAEO,CAAC,EAAE;EAC3D,IAAIC,MAAM,GAAG,IAAI,CAACC,YAAY,CAACF,CAAC,CAAC;EAEjC,IAAI,CAACC,MAAM,EAAE;IACT,IAAI,CAACH,YAAY,CAAC,CAAC;EACvB,CAAC,MAAM;IACH,IAAI,CAAC,IAAI,CAACK,QAAQ,EAAE;MAChB,IAAI,CAACC,cAAc,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAC3C;IAEA,IAAI,CAACC,UAAU,CAACL,MAAM,CAAC;EAC3B;AACJ,CAAC;AAEDX,aAAa,CAACM,SAAS,CAACF,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAI;EAC1D,IAAI,CAACI,YAAY,CAAC,CAAC;AACvB,CAAC;AAEDR,aAAa,CAACM,SAAS,CAACM,YAAY,GAAG,SAASA,YAAYA,CAAEF,CAAC,EAAE;EAC7D,IAAIO,KAAK,GAAGP,CAAC,CAACL,OAAO;EAErB,OAAOY,KAAK,IAAI,CAAClB,OAAO,CAACkB,KAAK,CAACC,OAAO,CAACP,MAAM,CAAC,EAAE;IAC5CM,KAAK,GAAGA,KAAK,CAACE,MAAM;EACxB;EAEA,IAAIF,KAAK,EAAE;IACP,OAAOA,KAAK,CAACC,OAAO,CAACP,MAAM;EAC/B;AACJ,CAAC;AAEDX,aAAa,CAACM,SAAS,CAACS,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAI;EACxD,IAAI,IAAI,CAACV,OAAO,EAAE;IACd,OAAO,IAAI,CAACA,OAAO,CAACe,KAAK,CAACT,MAAM;EACpC;AACJ,CAAC;AAEDX,aAAa,CAACM,SAAS,CAACU,UAAU,GAAG,SAASA,UAAUA,CAAEL,MAAM,EAAE;EAC9D,IAAI,IAAI,CAACN,OAAO,EAAE;IACd,IAAI,CAACA,OAAO,CAACe,KAAK,CAACT,MAAM,GAAGA,MAAM;IAClC,IAAI,CAACE,QAAQ,GAAGF,MAAM;EAC1B;AACJ,CAAC;AAEDX,aAAa,CAACM,SAAS,CAACE,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAI;EAC5D,IAAI,IAAI,CAACK,QAAQ,EAAE;IACf,IAAI,CAACG,UAAU,CAAC,IAAI,CAACF,cAAc,IAAI,EAAE,CAAC;IAC1C,OAAO,IAAI,CAACD,QAAQ;EACxB;AACJ,CAAC;AAED,eAAeb,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}