{"ast": null, "code": "import withPoints from '../mixins/with-points';\nimport Point from '../geometry/point';\nimport Gradient from './gradient';\nimport { defined } from '../util';\nvar RadialGradient = function (superclass) {\n  function RadialGradient(options) {\n    if (options === void 0) options = {};\n    superclass.call(this, options);\n    this.center(options.center || new Point());\n    this._radius = defined(options.radius) ? options.radius : 1;\n    this._fallbackFill = options.fallbackFill;\n  }\n  if (superclass) RadialGradient.__proto__ = superclass;\n  RadialGradient.prototype = Object.create(superclass && superclass.prototype);\n  RadialGradient.prototype.constructor = RadialGradient;\n  RadialGradient.prototype.radius = function radius(value) {\n    if (defined(value)) {\n      this._radius = value;\n      this.geometryChange();\n      return this;\n    }\n    return this._radius;\n  };\n  RadialGradient.prototype.fallbackFill = function fallbackFill(value) {\n    if (defined(value)) {\n      this._fallbackFill = value;\n      this.optionsChange();\n      return this;\n    }\n    return this._fallbackFill;\n  };\n  return RadialGradient;\n}(withPoints(Gradient, [\"center\"]));\nexport default RadialGradient;", "map": {"version": 3, "names": ["withPoints", "Point", "Gradient", "defined", "RadialGrad<PERSON>", "superclass", "options", "call", "center", "_radius", "radius", "_fallbackFill", "fallbackFill", "__proto__", "prototype", "Object", "create", "constructor", "value", "geometryChange", "optionsChange"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/gradients/radial-gradient.js"], "sourcesContent": ["import withPoints from '../mixins/with-points';\nimport Point from '../geometry/point';\nimport Gradient from './gradient';\nimport { defined } from '../util';\n\n\nvar RadialGradient = (function (superclass) {\n    function RadialGradient(options) {\n        if ( options === void 0 ) options = {};\n\n        superclass.call(this, options);\n\n        this.center(options.center || new Point());\n        this._radius = defined(options.radius) ? options.radius : 1;\n        this._fallbackFill = options.fallbackFill;\n    }\n\n    if ( superclass ) RadialGradient.__proto__ = superclass;\n    RadialGradient.prototype = Object.create( superclass && superclass.prototype );\n    RadialGradient.prototype.constructor = RadialGradient;\n\n    RadialGradient.prototype.radius = function radius (value) {\n        if (defined(value)) {\n            this._radius = value;\n            this.geometryChange();\n            return this;\n        }\n\n        return this._radius;\n    };\n\n    RadialGradient.prototype.fallbackFill = function fallbackFill (value) {\n        if (defined(value)) {\n            this._fallbackFill = value;\n            this.optionsChange();\n            return this;\n        }\n\n        return this._fallbackFill;\n    };\n\n    return RadialGradient;\n}(withPoints(Gradient, [ \"center\" ])));\n\nexport default RadialGradient;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,KAAK,MAAM,mBAAmB;AACrC,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,OAAO,QAAQ,SAAS;AAGjC,IAAIC,cAAc,GAAI,UAAUC,UAAU,EAAE;EACxC,SAASD,cAAcA,CAACE,OAAO,EAAE;IAC7B,IAAKA,OAAO,KAAK,KAAK,CAAC,EAAGA,OAAO,GAAG,CAAC,CAAC;IAEtCD,UAAU,CAACE,IAAI,CAAC,IAAI,EAAED,OAAO,CAAC;IAE9B,IAAI,CAACE,MAAM,CAACF,OAAO,CAACE,MAAM,IAAI,IAAIP,KAAK,CAAC,CAAC,CAAC;IAC1C,IAAI,CAACQ,OAAO,GAAGN,OAAO,CAACG,OAAO,CAACI,MAAM,CAAC,GAAGJ,OAAO,CAACI,MAAM,GAAG,CAAC;IAC3D,IAAI,CAACC,aAAa,GAAGL,OAAO,CAACM,YAAY;EAC7C;EAEA,IAAKP,UAAU,EAAGD,cAAc,CAACS,SAAS,GAAGR,UAAU;EACvDD,cAAc,CAACU,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAEX,UAAU,IAAIA,UAAU,CAACS,SAAU,CAAC;EAC9EV,cAAc,CAACU,SAAS,CAACG,WAAW,GAAGb,cAAc;EAErDA,cAAc,CAACU,SAAS,CAACJ,MAAM,GAAG,SAASA,MAAMA,CAAEQ,KAAK,EAAE;IACtD,IAAIf,OAAO,CAACe,KAAK,CAAC,EAAE;MAChB,IAAI,CAACT,OAAO,GAAGS,KAAK;MACpB,IAAI,CAACC,cAAc,CAAC,CAAC;MACrB,OAAO,IAAI;IACf;IAEA,OAAO,IAAI,CAACV,OAAO;EACvB,CAAC;EAEDL,cAAc,CAACU,SAAS,CAACF,YAAY,GAAG,SAASA,YAAYA,CAAEM,KAAK,EAAE;IAClE,IAAIf,OAAO,CAACe,KAAK,CAAC,EAAE;MAChB,IAAI,CAACP,aAAa,GAAGO,KAAK;MAC1B,IAAI,CAACE,aAAa,CAAC,CAAC;MACpB,OAAO,IAAI;IACf;IAEA,OAAO,IAAI,CAACT,aAAa;EAC7B,CAAC;EAED,OAAOP,cAAc;AACzB,CAAC,CAACJ,UAAU,CAACE,QAAQ,EAAE,CAAE,QAAQ,CAAE,CAAC,CAAE;AAEtC,eAAeE,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}