{"ast": null, "code": "import toCubicPolynomial from './to-cubic-polynomial';\nimport solveCubicEquation from './solve-cubic-equation';\nimport calculateCurveAt from './calculate-curve-at';\nimport close from './close';\nexport default function curveIntersectionsCount(points, point, bbox) {\n  var polynomial = toCubicPolynomial(points, \"x\");\n  var roots = solveCubicEquation(polynomial[0], polynomial[1], polynomial[2], polynomial[3] - point.x);\n  var rayIntersection, intersectsRay;\n  var count = 0;\n  for (var i = 0; i < roots.length; i++) {\n    rayIntersection = calculateCurveAt(roots[i], \"y\", points);\n    intersectsRay = close(rayIntersection, point.y) || rayIntersection > point.y;\n    if (intersectsRay && ((roots[i] === 0 || roots[i] === 1) && bbox.bottomRight().x > point.x || 0 < roots[i] && roots[i] < 1)) {\n      count++;\n    }\n  }\n  return count;\n}", "map": {"version": 3, "names": ["toCubicPolynomial", "solveCubicEquation", "calculateCurveAt", "close", "curveIntersectionsCount", "points", "point", "bbox", "polynomial", "roots", "x", "rayIntersection", "intersectsRay", "count", "i", "length", "y", "bottomRight"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/geometry/math/curve-intersections-count.js"], "sourcesContent": ["import toCubicPolynomial from './to-cubic-polynomial';\nimport solveCubicEquation from './solve-cubic-equation';\nimport calculateCurveAt from './calculate-curve-at';\nimport close from './close';\n\nexport default function curveIntersectionsCount(points, point, bbox) {\n    var polynomial = toCubicPolynomial(points, \"x\");\n    var roots = solveCubicEquation(polynomial[0], polynomial[1], polynomial[2], polynomial[3] - point.x);\n    var rayIntersection, intersectsRay;\n    var count = 0;\n    for (var i = 0; i < roots.length; i++) {\n        rayIntersection = calculateCurveAt(roots[i], \"y\", points);\n        intersectsRay = close(rayIntersection, point.y) || rayIntersection > point.y;\n        if (intersectsRay && (((roots[i] === 0 || roots[i] === 1) && bbox.bottomRight().x > point.x) || (0 < roots[i] && roots[i] < 1))) {\n            count++;\n        }\n    }\n\n    return count;\n}"], "mappings": "AAAA,OAAOA,iBAAiB,MAAM,uBAAuB;AACrD,OAAOC,kBAAkB,MAAM,wBAAwB;AACvD,OAAOC,gBAAgB,MAAM,sBAAsB;AACnD,OAAOC,KAAK,MAAM,SAAS;AAE3B,eAAe,SAASC,uBAAuBA,CAACC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAE;EACjE,IAAIC,UAAU,GAAGR,iBAAiB,CAACK,MAAM,EAAE,GAAG,CAAC;EAC/C,IAAII,KAAK,GAAGR,kBAAkB,CAACO,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,GAAGF,KAAK,CAACI,CAAC,CAAC;EACpG,IAAIC,eAAe,EAAEC,aAAa;EAClC,IAAIC,KAAK,GAAG,CAAC;EACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,KAAK,CAACM,MAAM,EAAED,CAAC,EAAE,EAAE;IACnCH,eAAe,GAAGT,gBAAgB,CAACO,KAAK,CAACK,CAAC,CAAC,EAAE,GAAG,EAAET,MAAM,CAAC;IACzDO,aAAa,GAAGT,KAAK,CAACQ,eAAe,EAAEL,KAAK,CAACU,CAAC,CAAC,IAAIL,eAAe,GAAGL,KAAK,CAACU,CAAC;IAC5E,IAAIJ,aAAa,KAAM,CAACH,KAAK,CAACK,CAAC,CAAC,KAAK,CAAC,IAAIL,KAAK,CAACK,CAAC,CAAC,KAAK,CAAC,KAAKP,IAAI,CAACU,WAAW,CAAC,CAAC,CAACP,CAAC,GAAGJ,KAAK,CAACI,CAAC,IAAM,CAAC,GAAGD,KAAK,CAACK,CAAC,CAAC,IAAIL,KAAK,CAACK,CAAC,CAAC,GAAG,CAAE,CAAC,EAAE;MAC7HD,KAAK,EAAE;IACX;EACJ;EAEA,OAAOA,KAAK;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}