{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { Button as r } from \"@progress/kendo-react-buttons\";\nimport * as o from \"react\";\nconst n = e => {\n  const {\n    view: a,\n    ...t\n  } = e;\n  return /* @__PURE__ */o.createElement(r, {\n    type: \"button\",\n    fillMode: \"flat\",\n    themeColor: \"primary\",\n    ...t\n  }, e.children);\n};\nexport { n as CalendarHeaderTitle };", "map": {"version": 3, "names": ["<PERSON><PERSON>", "r", "o", "n", "e", "view", "a", "t", "createElement", "type", "fillMode", "themeColor", "children", "CalendarHeaderTitle"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dateinputs/calendar/components/CalendarHeaderTitle.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { Button as r } from \"@progress/kendo-react-buttons\";\nimport * as o from \"react\";\nconst n = (e) => {\n  const { view: a, ...t } = e;\n  return /* @__PURE__ */ o.createElement(r, { type: \"button\", fillMode: \"flat\", themeColor: \"primary\", ...t }, e.children);\n};\nexport {\n  n as CalendarHeaderTitle\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAM,IAAIC,CAAC,QAAQ,+BAA+B;AAC3D,OAAO,KAAKC,CAAC,MAAM,OAAO;AAC1B,MAAMC,CAAC,GAAIC,CAAC,IAAK;EACf,MAAM;IAAEC,IAAI,EAAEC,CAAC;IAAE,GAAGC;EAAE,CAAC,GAAGH,CAAC;EAC3B,OAAO,eAAgBF,CAAC,CAACM,aAAa,CAACP,CAAC,EAAE;IAAEQ,IAAI,EAAE,QAAQ;IAAEC,QAAQ,EAAE,MAAM;IAAEC,UAAU,EAAE,SAAS;IAAE,GAAGJ;EAAE,CAAC,EAAEH,CAAC,CAACQ,QAAQ,CAAC;AAC1H,CAAC;AACD,SACET,CAAC,IAAIU,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}