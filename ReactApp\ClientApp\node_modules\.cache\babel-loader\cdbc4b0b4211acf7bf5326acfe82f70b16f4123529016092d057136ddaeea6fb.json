{"ast": null, "code": "import { normalizeYear } from './normalize-year';\n/**\n * A function that returns a `Date` object of the last decade in a century.\n *\n * @param date - The start date value.\n * @returns - The last year in a decade.\n *\n * @example\n * ```ts-no-run\n * lastDecadeOfCentury(new Date(2017, 0, 1)); // 2090-1-1\n * lastDecadeOfCentury(new Date(2007, 10, 22)); // 2090-11-22\n * lastDecadeOfCentury(new Date(2126, 0, 1)); // 2190-1-1\n * ```\n */\nexport var lastDecadeOfCentury = function (value) {\n  return normalizeYear(value, function (y) {\n    return y - y % 100 + 90;\n  });\n};", "map": {"version": 3, "names": ["normalizeYear", "lastDecadeOfCentury", "value", "y"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/last-decade-of-century.js"], "sourcesContent": ["import { normalizeYear } from './normalize-year';\n/**\n * A function that returns a `Date` object of the last decade in a century.\n *\n * @param date - The start date value.\n * @returns - The last year in a decade.\n *\n * @example\n * ```ts-no-run\n * lastDecadeOfCentury(new Date(2017, 0, 1)); // 2090-1-1\n * lastDecadeOfCentury(new Date(2007, 10, 22)); // 2090-11-22\n * lastDecadeOfCentury(new Date(2126, 0, 1)); // 2190-1-1\n * ```\n */\nexport var lastDecadeOfCentury = function (value) { return (normalizeYear(value, function (y) { return y - (y % 100) + 90; })); };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,kBAAkB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,mBAAmB,GAAG,SAAAA,CAAUC,KAAK,EAAE;EAAE,OAAQF,aAAa,CAACE,KAAK,EAAE,UAAUC,CAAC,EAAE;IAAE,OAAOA,CAAC,GAAIA,CAAC,GAAG,GAAI,GAAG,EAAE;EAAE,CAAC,CAAC;AAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}