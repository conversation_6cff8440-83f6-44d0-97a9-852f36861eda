{"ast": null, "code": "import Element from './element';\nimport Point from '../geometry/point';\nimport Rect from '../geometry/rect';\nimport toMatrix from '../geometry/to-matrix';\nimport paintable from '../mixins/paintable';\nimport withPoints from '../mixins/with-points';\nimport { defined, measureText } from '../util';\nvar DEFAULT_FONT = \"12px sans-serif\";\nvar DEFAULT_FILL = \"#000\";\nvar Text = function (superclass) {\n  function Text(content, position, options) {\n    if (position === void 0) position = new Point();\n    if (options === void 0) options = {};\n    superclass.call(this, options);\n    this.content(content);\n    this.position(position);\n    if (!this.options.font) {\n      this.options.font = DEFAULT_FONT;\n    }\n    if (!defined(this.options.fill)) {\n      this.fill(DEFAULT_FILL);\n    }\n  }\n  if (superclass) Text.__proto__ = superclass;\n  Text.prototype = Object.create(superclass && superclass.prototype);\n  Text.prototype.constructor = Text;\n  var prototypeAccessors = {\n    nodeType: {\n      configurable: true\n    }\n  };\n  prototypeAccessors.nodeType.get = function () {\n    return \"Text\";\n  };\n  Text.prototype.content = function content(value) {\n    if (defined(value)) {\n      this.options.set(\"content\", value);\n      return this;\n    }\n    return this.options.get(\"content\");\n  };\n  Text.prototype.measure = function measure() {\n    var metrics = measureText(this.content(), {\n      font: this.options.get(\"font\")\n    });\n    return metrics;\n  };\n  Text.prototype.rect = function rect() {\n    var size = this.measure();\n    var pos = this.position().clone();\n    return new Rect(pos, [size.width, size.height]);\n  };\n  Text.prototype.bbox = function bbox(transformation) {\n    var combinedMatrix = toMatrix(this.currentTransform(transformation));\n    return this.rect().bbox(combinedMatrix);\n  };\n  Text.prototype.rawBBox = function rawBBox() {\n    return this.rect().bbox();\n  };\n  Text.prototype._containsPoint = function _containsPoint(point) {\n    return this.rect().containsPoint(point);\n  };\n  Object.defineProperties(Text.prototype, prototypeAccessors);\n  return Text;\n}(paintable(withPoints(Element, [\"position\"])));\nexport default Text;", "map": {"version": 3, "names": ["Element", "Point", "Rect", "toMatrix", "paintable", "withPoints", "defined", "measureText", "DEFAULT_FONT", "DEFAULT_FILL", "Text", "superclass", "content", "position", "options", "call", "font", "fill", "__proto__", "prototype", "Object", "create", "constructor", "prototypeAccessors", "nodeType", "configurable", "get", "value", "set", "measure", "metrics", "rect", "size", "pos", "clone", "width", "height", "bbox", "transformation", "combinedMatrix", "currentTransform", "rawBBox", "_containsPoint", "point", "containsPoint", "defineProperties"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/shapes/text.js"], "sourcesContent": ["import Element from './element';\nimport Point from '../geometry/point';\nimport Rect from '../geometry/rect';\nimport toMatrix from '../geometry/to-matrix';\nimport paintable from '../mixins/paintable';\nimport withPoints from '../mixins/with-points';\nimport { defined, measureText } from '../util';\n\n\nvar DEFAULT_FONT = \"12px sans-serif\";\nvar DEFAULT_FILL = \"#000\";\n\nvar Text = (function (superclass) {\n    function Text(content, position, options) {\n        if ( position === void 0 ) position = new Point();\n        if ( options === void 0 ) options = {};\n\n        superclass.call(this, options);\n\n        this.content(content);\n        this.position(position);\n\n        if (!this.options.font) {\n            this.options.font = DEFAULT_FONT;\n        }\n\n        if (!defined(this.options.fill)) {\n            this.fill(DEFAULT_FILL);\n        }\n    }\n\n    if ( superclass ) Text.__proto__ = superclass;\n    Text.prototype = Object.create( superclass && superclass.prototype );\n    Text.prototype.constructor = Text;\n\n    var prototypeAccessors = { nodeType: { configurable: true } };\n\n    prototypeAccessors.nodeType.get = function () {\n        return \"Text\";\n    };\n\n    Text.prototype.content = function content (value) {\n        if (defined(value)) {\n            this.options.set(\"content\", value);\n            return this;\n        }\n\n        return this.options.get(\"content\");\n    };\n\n    Text.prototype.measure = function measure () {\n        var metrics = measureText(this.content(), {\n            font: this.options.get(\"font\")\n        });\n\n        return metrics;\n    };\n\n    Text.prototype.rect = function rect () {\n        var size = this.measure();\n        var pos = this.position().clone();\n        return new Rect(pos, [ size.width, size.height ]);\n    };\n\n    Text.prototype.bbox = function bbox (transformation) {\n        var combinedMatrix = toMatrix(this.currentTransform(transformation));\n        return this.rect().bbox(combinedMatrix);\n    };\n\n    Text.prototype.rawBBox = function rawBBox () {\n        return this.rect().bbox();\n    };\n\n    Text.prototype._containsPoint = function _containsPoint (point) {\n        return this.rect().containsPoint(point);\n    };\n\n    Object.defineProperties( Text.prototype, prototypeAccessors );\n\n    return Text;\n}(paintable(withPoints(Element, [ \"position\" ]))));\n\nexport default Text;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,WAAW;AAC/B,OAAOC,KAAK,MAAM,mBAAmB;AACrC,OAAOC,IAAI,MAAM,kBAAkB;AACnC,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,SAASC,OAAO,EAAEC,WAAW,QAAQ,SAAS;AAG9C,IAAIC,YAAY,GAAG,iBAAiB;AACpC,IAAIC,YAAY,GAAG,MAAM;AAEzB,IAAIC,IAAI,GAAI,UAAUC,UAAU,EAAE;EAC9B,SAASD,IAAIA,CAACE,OAAO,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IACtC,IAAKD,QAAQ,KAAK,KAAK,CAAC,EAAGA,QAAQ,GAAG,IAAIZ,KAAK,CAAC,CAAC;IACjD,IAAKa,OAAO,KAAK,KAAK,CAAC,EAAGA,OAAO,GAAG,CAAC,CAAC;IAEtCH,UAAU,CAACI,IAAI,CAAC,IAAI,EAAED,OAAO,CAAC;IAE9B,IAAI,CAACF,OAAO,CAACA,OAAO,CAAC;IACrB,IAAI,CAACC,QAAQ,CAACA,QAAQ,CAAC;IAEvB,IAAI,CAAC,IAAI,CAACC,OAAO,CAACE,IAAI,EAAE;MACpB,IAAI,CAACF,OAAO,CAACE,IAAI,GAAGR,YAAY;IACpC;IAEA,IAAI,CAACF,OAAO,CAAC,IAAI,CAACQ,OAAO,CAACG,IAAI,CAAC,EAAE;MAC7B,IAAI,CAACA,IAAI,CAACR,YAAY,CAAC;IAC3B;EACJ;EAEA,IAAKE,UAAU,EAAGD,IAAI,CAACQ,SAAS,GAAGP,UAAU;EAC7CD,IAAI,CAACS,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAEV,UAAU,IAAIA,UAAU,CAACQ,SAAU,CAAC;EACpET,IAAI,CAACS,SAAS,CAACG,WAAW,GAAGZ,IAAI;EAEjC,IAAIa,kBAAkB,GAAG;IAAEC,QAAQ,EAAE;MAAEC,YAAY,EAAE;IAAK;EAAE,CAAC;EAE7DF,kBAAkB,CAACC,QAAQ,CAACE,GAAG,GAAG,YAAY;IAC1C,OAAO,MAAM;EACjB,CAAC;EAEDhB,IAAI,CAACS,SAAS,CAACP,OAAO,GAAG,SAASA,OAAOA,CAAEe,KAAK,EAAE;IAC9C,IAAIrB,OAAO,CAACqB,KAAK,CAAC,EAAE;MAChB,IAAI,CAACb,OAAO,CAACc,GAAG,CAAC,SAAS,EAAED,KAAK,CAAC;MAClC,OAAO,IAAI;IACf;IAEA,OAAO,IAAI,CAACb,OAAO,CAACY,GAAG,CAAC,SAAS,CAAC;EACtC,CAAC;EAEDhB,IAAI,CAACS,SAAS,CAACU,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;IACzC,IAAIC,OAAO,GAAGvB,WAAW,CAAC,IAAI,CAACK,OAAO,CAAC,CAAC,EAAE;MACtCI,IAAI,EAAE,IAAI,CAACF,OAAO,CAACY,GAAG,CAAC,MAAM;IACjC,CAAC,CAAC;IAEF,OAAOI,OAAO;EAClB,CAAC;EAEDpB,IAAI,CAACS,SAAS,CAACY,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAI;IACnC,IAAIC,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IACzB,IAAII,GAAG,GAAG,IAAI,CAACpB,QAAQ,CAAC,CAAC,CAACqB,KAAK,CAAC,CAAC;IACjC,OAAO,IAAIhC,IAAI,CAAC+B,GAAG,EAAE,CAAED,IAAI,CAACG,KAAK,EAAEH,IAAI,CAACI,MAAM,CAAE,CAAC;EACrD,CAAC;EAED1B,IAAI,CAACS,SAAS,CAACkB,IAAI,GAAG,SAASA,IAAIA,CAAEC,cAAc,EAAE;IACjD,IAAIC,cAAc,GAAGpC,QAAQ,CAAC,IAAI,CAACqC,gBAAgB,CAACF,cAAc,CAAC,CAAC;IACpE,OAAO,IAAI,CAACP,IAAI,CAAC,CAAC,CAACM,IAAI,CAACE,cAAc,CAAC;EAC3C,CAAC;EAED7B,IAAI,CAACS,SAAS,CAACsB,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;IACzC,OAAO,IAAI,CAACV,IAAI,CAAC,CAAC,CAACM,IAAI,CAAC,CAAC;EAC7B,CAAC;EAED3B,IAAI,CAACS,SAAS,CAACuB,cAAc,GAAG,SAASA,cAAcA,CAAEC,KAAK,EAAE;IAC5D,OAAO,IAAI,CAACZ,IAAI,CAAC,CAAC,CAACa,aAAa,CAACD,KAAK,CAAC;EAC3C,CAAC;EAEDvB,MAAM,CAACyB,gBAAgB,CAAEnC,IAAI,CAACS,SAAS,EAAEI,kBAAmB,CAAC;EAE7D,OAAOb,IAAI;AACf,CAAC,CAACN,SAAS,CAACC,UAAU,CAACL,OAAO,EAAE,CAAE,UAAU,CAAE,CAAC,CAAC,CAAE;AAElD,eAAeU,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}