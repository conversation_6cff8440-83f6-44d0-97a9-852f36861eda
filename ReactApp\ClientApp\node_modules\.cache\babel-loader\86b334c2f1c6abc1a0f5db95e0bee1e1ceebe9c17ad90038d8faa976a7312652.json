{"ast": null, "code": "export default function createStackElements(elements) {\n  var stackElements = [];\n  for (var idx = 0; idx < elements.length; idx++) {\n    var element = elements[idx];\n    var bbox = element.clippedBBox();\n    if (bbox) {\n      stackElements.push({\n        element: element,\n        bbox: bbox\n      });\n    }\n  }\n  return stackElements;\n}", "map": {"version": 3, "names": ["createStackElements", "elements", "stackElements", "idx", "length", "element", "bbox", "clippedBBox", "push"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/alignment/create-stack-elements.js"], "sourcesContent": ["export default function createStackElements(elements) {\n    var stackElements = [];\n\n    for (var idx = 0; idx < elements.length; idx++) {\n        var element = elements[idx];\n        var bbox = element.clippedBBox();\n        if (bbox) {\n            stackElements.push({\n                element: element,\n                bbox: bbox\n            });\n        }\n    }\n\n    return stackElements;\n}"], "mappings": "AAAA,eAAe,SAASA,mBAAmBA,CAACC,QAAQ,EAAE;EAClD,IAAIC,aAAa,GAAG,EAAE;EAEtB,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGF,QAAQ,CAACG,MAAM,EAAED,GAAG,EAAE,EAAE;IAC5C,IAAIE,OAAO,GAAGJ,QAAQ,CAACE,GAAG,CAAC;IAC3B,IAAIG,IAAI,GAAGD,OAAO,CAACE,WAAW,CAAC,CAAC;IAChC,IAAID,IAAI,EAAE;MACNJ,aAAa,CAACM,IAAI,CAAC;QACfH,OAAO,EAAEA,OAAO;QAChBC,IAAI,EAAEA;MACV,CAAC,CAAC;IACN;EACJ;EAEA,OAAOJ,aAAa;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}