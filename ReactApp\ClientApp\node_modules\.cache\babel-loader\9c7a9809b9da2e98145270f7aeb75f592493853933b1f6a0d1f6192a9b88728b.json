{"ast": null, "code": "import * as React from 'react'; // Tell cell that browser support sticky\n\nvar StickyContext = /*#__PURE__*/React.createContext(false);\nexport default StickyContext;", "map": {"version": 3, "names": ["React", "StickyContext", "createContext"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/rc-table/es/context/StickyContext.js"], "sourcesContent": ["import * as React from 'react'; // Tell cell that browser support sticky\n\nvar StickyContext = /*#__PURE__*/React.createContext(false);\nexport default StickyContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO,CAAC,CAAC;;AAEhC,IAAIC,aAAa,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,KAAK,CAAC;AAC3D,eAAeD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}