{"ast": null, "code": "import StopsArray from './stops-array';\nimport GradientStop from './gradient-stop';\nimport HasObservers from '../core/has-observers';\nimport { defined, definitionId } from '../util';\nvar Gradient = function (HasObservers) {\n  function Gradient(options) {\n    if (options === void 0) options = {};\n    HasObservers.call(this);\n    this.stops = new StopsArray(this._createStops(options.stops));\n    this.stops.addObserver(this);\n    this._userSpace = options.userSpace;\n    this.id = definitionId();\n  }\n  if (HasObservers) Gradient.__proto__ = HasObservers;\n  Gradient.prototype = Object.create(HasObservers && HasObservers.prototype);\n  Gradient.prototype.constructor = Gradient;\n  var prototypeAccessors = {\n    nodeType: {\n      configurable: true\n    }\n  };\n  prototypeAccessors.nodeType.get = function () {\n    return \"Gradient\";\n  };\n  Gradient.prototype.userSpace = function userSpace(value) {\n    if (defined(value)) {\n      this._userSpace = value;\n      this.optionsChange();\n      return this;\n    }\n    return this._userSpace;\n  };\n  Gradient.prototype._createStops = function _createStops(stops) {\n    if (stops === void 0) stops = [];\n    var result = [];\n    for (var idx = 0; idx < stops.length; idx++) {\n      result.push(GradientStop.create(stops[idx]));\n    }\n    return result;\n  };\n  Gradient.prototype.addStop = function addStop(offset, color, opacity) {\n    this.stops.push(new GradientStop(offset, color, opacity));\n  };\n  Gradient.prototype.removeStop = function removeStop(stop) {\n    var index = this.stops.indexOf(stop);\n    if (index >= 0) {\n      this.stops.splice(index, 1);\n    }\n  };\n  Gradient.prototype.optionsChange = function optionsChange(e) {\n    this.trigger(\"optionsChange\", {\n      field: \"gradient\" + (e ? \".\" + e.field : \"\"),\n      value: this\n    });\n  };\n  Gradient.prototype.geometryChange = function geometryChange() {\n    this.optionsChange();\n  };\n  Object.defineProperties(Gradient.prototype, prototypeAccessors);\n  return Gradient;\n}(HasObservers);\nexport default Gradient;", "map": {"version": 3, "names": ["StopsArray", "GradientStop", "HasObservers", "defined", "definitionId", "Gradient", "options", "call", "stops", "_createStops", "addObserver", "_userSpace", "userSpace", "id", "__proto__", "prototype", "Object", "create", "constructor", "prototypeAccessors", "nodeType", "configurable", "get", "value", "optionsChange", "result", "idx", "length", "push", "addStop", "offset", "color", "opacity", "removeStop", "stop", "index", "indexOf", "splice", "e", "trigger", "field", "geometryChange", "defineProperties"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/gradients/gradient.js"], "sourcesContent": ["import StopsArray from './stops-array';\nimport GradientStop from './gradient-stop';\nimport HasObservers from '../core/has-observers';\nimport { defined, definitionId } from '../util';\n\nvar Gradient = (function (HasObservers) {\n    function Gradient(options) {\n        if ( options === void 0 ) options = {};\n\n        HasObservers.call(this);\n\n        this.stops = new StopsArray(this._createStops(options.stops));\n        this.stops.addObserver(this);\n        this._userSpace = options.userSpace;\n        this.id = definitionId();\n    }\n\n    if ( HasObservers ) Gradient.__proto__ = HasObservers;\n    Gradient.prototype = Object.create( HasObservers && HasObservers.prototype );\n    Gradient.prototype.constructor = Gradient;\n\n    var prototypeAccessors = { nodeType: { configurable: true } };\n\n    prototypeAccessors.nodeType.get = function () {\n        return \"Gradient\";\n    };\n\n    Gradient.prototype.userSpace = function userSpace (value) {\n        if (defined(value)) {\n            this._userSpace = value;\n            this.optionsChange();\n            return this;\n        }\n\n        return this._userSpace;\n    };\n\n    Gradient.prototype._createStops = function _createStops (stops) {\n        if ( stops === void 0 ) stops = [];\n\n        var result = [];\n        for (var idx = 0; idx < stops.length; idx++) {\n            result.push(GradientStop.create(stops[idx]));\n        }\n\n        return result;\n    };\n\n    Gradient.prototype.addStop = function addStop (offset, color, opacity) {\n        this.stops.push(new GradientStop(offset, color, opacity));\n    };\n\n    Gradient.prototype.removeStop = function removeStop (stop) {\n        var index = this.stops.indexOf(stop);\n        if (index >= 0) {\n            this.stops.splice(index, 1);\n        }\n    };\n\n    Gradient.prototype.optionsChange = function optionsChange (e) {\n        this.trigger(\"optionsChange\", {\n            field: \"gradient\" + (e ? \".\" + e.field : \"\"),\n            value: this\n        });\n    };\n\n    Gradient.prototype.geometryChange = function geometryChange () {\n        this.optionsChange();\n    };\n\n    Object.defineProperties( Gradient.prototype, prototypeAccessors );\n\n    return Gradient;\n}(HasObservers));\n\nexport default Gradient;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,eAAe;AACtC,OAAOC,YAAY,MAAM,iBAAiB;AAC1C,OAAOC,YAAY,MAAM,uBAAuB;AAChD,SAASC,OAAO,EAAEC,YAAY,QAAQ,SAAS;AAE/C,IAAIC,QAAQ,GAAI,UAAUH,YAAY,EAAE;EACpC,SAASG,QAAQA,CAACC,OAAO,EAAE;IACvB,IAAKA,OAAO,KAAK,KAAK,CAAC,EAAGA,OAAO,GAAG,CAAC,CAAC;IAEtCJ,YAAY,CAACK,IAAI,CAAC,IAAI,CAAC;IAEvB,IAAI,CAACC,KAAK,GAAG,IAAIR,UAAU,CAAC,IAAI,CAACS,YAAY,CAACH,OAAO,CAACE,KAAK,CAAC,CAAC;IAC7D,IAAI,CAACA,KAAK,CAACE,WAAW,CAAC,IAAI,CAAC;IAC5B,IAAI,CAACC,UAAU,GAAGL,OAAO,CAACM,SAAS;IACnC,IAAI,CAACC,EAAE,GAAGT,YAAY,CAAC,CAAC;EAC5B;EAEA,IAAKF,YAAY,EAAGG,QAAQ,CAACS,SAAS,GAAGZ,YAAY;EACrDG,QAAQ,CAACU,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAEf,YAAY,IAAIA,YAAY,CAACa,SAAU,CAAC;EAC5EV,QAAQ,CAACU,SAAS,CAACG,WAAW,GAAGb,QAAQ;EAEzC,IAAIc,kBAAkB,GAAG;IAAEC,QAAQ,EAAE;MAAEC,YAAY,EAAE;IAAK;EAAE,CAAC;EAE7DF,kBAAkB,CAACC,QAAQ,CAACE,GAAG,GAAG,YAAY;IAC1C,OAAO,UAAU;EACrB,CAAC;EAEDjB,QAAQ,CAACU,SAAS,CAACH,SAAS,GAAG,SAASA,SAASA,CAAEW,KAAK,EAAE;IACtD,IAAIpB,OAAO,CAACoB,KAAK,CAAC,EAAE;MAChB,IAAI,CAACZ,UAAU,GAAGY,KAAK;MACvB,IAAI,CAACC,aAAa,CAAC,CAAC;MACpB,OAAO,IAAI;IACf;IAEA,OAAO,IAAI,CAACb,UAAU;EAC1B,CAAC;EAEDN,QAAQ,CAACU,SAAS,CAACN,YAAY,GAAG,SAASA,YAAYA,CAAED,KAAK,EAAE;IAC5D,IAAKA,KAAK,KAAK,KAAK,CAAC,EAAGA,KAAK,GAAG,EAAE;IAElC,IAAIiB,MAAM,GAAG,EAAE;IACf,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGlB,KAAK,CAACmB,MAAM,EAAED,GAAG,EAAE,EAAE;MACzCD,MAAM,CAACG,IAAI,CAAC3B,YAAY,CAACgB,MAAM,CAACT,KAAK,CAACkB,GAAG,CAAC,CAAC,CAAC;IAChD;IAEA,OAAOD,MAAM;EACjB,CAAC;EAEDpB,QAAQ,CAACU,SAAS,CAACc,OAAO,GAAG,SAASA,OAAOA,CAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAE;IACnE,IAAI,CAACxB,KAAK,CAACoB,IAAI,CAAC,IAAI3B,YAAY,CAAC6B,MAAM,EAAEC,KAAK,EAAEC,OAAO,CAAC,CAAC;EAC7D,CAAC;EAED3B,QAAQ,CAACU,SAAS,CAACkB,UAAU,GAAG,SAASA,UAAUA,CAAEC,IAAI,EAAE;IACvD,IAAIC,KAAK,GAAG,IAAI,CAAC3B,KAAK,CAAC4B,OAAO,CAACF,IAAI,CAAC;IACpC,IAAIC,KAAK,IAAI,CAAC,EAAE;MACZ,IAAI,CAAC3B,KAAK,CAAC6B,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IAC/B;EACJ,CAAC;EAED9B,QAAQ,CAACU,SAAS,CAACS,aAAa,GAAG,SAASA,aAAaA,CAAEc,CAAC,EAAE;IAC1D,IAAI,CAACC,OAAO,CAAC,eAAe,EAAE;MAC1BC,KAAK,EAAE,UAAU,IAAIF,CAAC,GAAG,GAAG,GAAGA,CAAC,CAACE,KAAK,GAAG,EAAE,CAAC;MAC5CjB,KAAK,EAAE;IACX,CAAC,CAAC;EACN,CAAC;EAEDlB,QAAQ,CAACU,SAAS,CAAC0B,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAI;IAC3D,IAAI,CAACjB,aAAa,CAAC,CAAC;EACxB,CAAC;EAEDR,MAAM,CAAC0B,gBAAgB,CAAErC,QAAQ,CAACU,SAAS,EAAEI,kBAAmB,CAAC;EAEjE,OAAOd,QAAQ;AACnB,CAAC,CAACH,YAAY,CAAE;AAEhB,eAAeG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}