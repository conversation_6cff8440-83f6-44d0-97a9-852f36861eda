{"ast": null, "code": "/* eslint-disable no-multi-spaces, key-spacing, indent, camelcase, space-before-blocks, eqeqeq, brace-style */\n/* eslint-disable space-infix-ops, space-before-function-paren, array-bracket-spacing, object-curly-spacing */\n/* eslint-disable no-nested-ternary, max-params, default-case, no-else-return, no-empty */\n/* eslint-disable no-param-reassign, no-var, block-scoped-var */\n\n// mergeSort is stable.\nexport default function mergeSort(a, cmp) {\n  if (a.length < 2) {\n    return a.slice();\n  }\n  function merge(a, b) {\n    var r = [],\n      ai = 0,\n      bi = 0,\n      i = 0;\n    while (ai < a.length && bi < b.length) {\n      if (cmp(a[ai], b[bi]) <= 0) {\n        r[i++] = a[ai++];\n      } else {\n        r[i++] = b[bi++];\n      }\n    }\n    if (ai < a.length) {\n      r.push.apply(r, a.slice(ai));\n    }\n    if (bi < b.length) {\n      r.push.apply(r, b.slice(bi));\n    }\n    return r;\n  }\n  return function sort(a) {\n    if (a.length <= 1) {\n      return a;\n    }\n    var m = Math.floor(a.length / 2);\n    var left = a.slice(0, m);\n    var right = a.slice(m);\n    left = sort(left);\n    right = sort(right);\n    return merge(left, right);\n  }(a);\n}", "map": {"version": 3, "names": ["mergeSort", "a", "cmp", "length", "slice", "merge", "b", "r", "ai", "bi", "i", "push", "apply", "sort", "m", "Math", "floor", "left", "right"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/util/merge-sort.js"], "sourcesContent": ["/* eslint-disable no-multi-spaces, key-spacing, indent, camelcase, space-before-blocks, eqeqeq, brace-style */\n/* eslint-disable space-infix-ops, space-before-function-paren, array-bracket-spacing, object-curly-spacing */\n/* eslint-disable no-nested-ternary, max-params, default-case, no-else-return, no-empty */\n/* eslint-disable no-param-reassign, no-var, block-scoped-var */\n\n// mergeSort is stable.\nexport default function mergeSort(a, cmp) {\n    if (a.length < 2) {\n        return a.slice();\n    }\n    function merge(a, b) {\n        var r = [], ai = 0, bi = 0, i = 0;\n        while (ai < a.length && bi < b.length) {\n            if (cmp(a[ai], b[bi]) <= 0) {\n                r[i++] = a[ai++];\n            } else {\n                r[i++] = b[bi++];\n            }\n        }\n        if (ai < a.length) {\n            r.push.apply(r, a.slice(ai));\n        }\n        if (bi < b.length) {\n            r.push.apply(r, b.slice(bi));\n        }\n        return r;\n    }\n    return (function sort(a) {\n        if (a.length <= 1) {\n            return a;\n        }\n        var m = Math.floor(a.length / 2);\n        var left = a.slice(0, m);\n        var right = a.slice(m);\n        left = sort(left);\n        right = sort(right);\n        return merge(left, right);\n    })(a);\n}"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA,eAAe,SAASA,SAASA,CAACC,CAAC,EAAEC,GAAG,EAAE;EACtC,IAAID,CAAC,CAACE,MAAM,GAAG,CAAC,EAAE;IACd,OAAOF,CAAC,CAACG,KAAK,CAAC,CAAC;EACpB;EACA,SAASC,KAAKA,CAACJ,CAAC,EAAEK,CAAC,EAAE;IACjB,IAAIC,CAAC,GAAG,EAAE;MAAEC,EAAE,GAAG,CAAC;MAAEC,EAAE,GAAG,CAAC;MAAEC,CAAC,GAAG,CAAC;IACjC,OAAOF,EAAE,GAAGP,CAAC,CAACE,MAAM,IAAIM,EAAE,GAAGH,CAAC,CAACH,MAAM,EAAE;MACnC,IAAID,GAAG,CAACD,CAAC,CAACO,EAAE,CAAC,EAAEF,CAAC,CAACG,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE;QACxBF,CAAC,CAACG,CAAC,EAAE,CAAC,GAAGT,CAAC,CAACO,EAAE,EAAE,CAAC;MACpB,CAAC,MAAM;QACHD,CAAC,CAACG,CAAC,EAAE,CAAC,GAAGJ,CAAC,CAACG,EAAE,EAAE,CAAC;MACpB;IACJ;IACA,IAAID,EAAE,GAAGP,CAAC,CAACE,MAAM,EAAE;MACfI,CAAC,CAACI,IAAI,CAACC,KAAK,CAACL,CAAC,EAAEN,CAAC,CAACG,KAAK,CAACI,EAAE,CAAC,CAAC;IAChC;IACA,IAAIC,EAAE,GAAGH,CAAC,CAACH,MAAM,EAAE;MACfI,CAAC,CAACI,IAAI,CAACC,KAAK,CAACL,CAAC,EAAED,CAAC,CAACF,KAAK,CAACK,EAAE,CAAC,CAAC;IAChC;IACA,OAAOF,CAAC;EACZ;EACA,OAAQ,SAASM,IAAIA,CAACZ,CAAC,EAAE;IACrB,IAAIA,CAAC,CAACE,MAAM,IAAI,CAAC,EAAE;MACf,OAAOF,CAAC;IACZ;IACA,IAAIa,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACf,CAAC,CAACE,MAAM,GAAG,CAAC,CAAC;IAChC,IAAIc,IAAI,GAAGhB,CAAC,CAACG,KAAK,CAAC,CAAC,EAAEU,CAAC,CAAC;IACxB,IAAII,KAAK,GAAGjB,CAAC,CAACG,KAAK,CAACU,CAAC,CAAC;IACtBG,IAAI,GAAGJ,IAAI,CAACI,IAAI,CAAC;IACjBC,KAAK,GAAGL,IAAI,CAACK,KAAK,CAAC;IACnB,OAAOb,KAAK,CAACY,IAAI,EAAEC,KAAK,CAAC;EAC7B,CAAC,CAAEjB,CAAC,CAAC;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}