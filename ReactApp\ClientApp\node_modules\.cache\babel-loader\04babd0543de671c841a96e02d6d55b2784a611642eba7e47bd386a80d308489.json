{"ast": null, "code": "import parents from './parents';\nimport siblingContainer from './sibling-container';\nexport default function zIndex(anchor, container) {\n  if (!anchor || !container) {\n    return null;\n  }\n  var sibling = siblingContainer(anchor, container);\n  if (!sibling) {\n    return null;\n  }\n  var result = [anchor].concat(parents(anchor, sibling)).reduce(function (index, p) {\n    var zIndexStyle = p.style.zIndex || window.getComputedStyle(p).zIndex;\n    var current = parseInt(zIndexStyle, 10);\n    return current > index ? current : index;\n  }, 0);\n  return result ? result + 1 : null;\n}", "map": {"version": 3, "names": ["parents", "sibling<PERSON><PERSON><PERSON>", "zIndex", "anchor", "container", "sibling", "result", "concat", "reduce", "index", "p", "zIndexStyle", "style", "window", "getComputedStyle", "current", "parseInt"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-popup-common/dist/es/z-index.js"], "sourcesContent": ["import parents from './parents';\nimport siblingContainer from './sibling-container';\n\nexport default function zIndex(anchor, container) {\n    if (!anchor || !container) { return null; }\n\n    var sibling = siblingContainer(anchor, container);\n\n    if (!sibling) { return null; }\n\n    var result = [ anchor ].concat(parents(anchor, sibling)).reduce(\n        function (index, p) {\n            var zIndexStyle = p.style.zIndex || window.getComputedStyle(p).zIndex;\n            var current = parseInt(zIndexStyle, 10);\n            return current > index ? current : index;\n        },\n        0\n    );\n\n    return result ? (result + 1) : null;\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,WAAW;AAC/B,OAAOC,gBAAgB,MAAM,qBAAqB;AAElD,eAAe,SAASC,MAAMA,CAACC,MAAM,EAAEC,SAAS,EAAE;EAC9C,IAAI,CAACD,MAAM,IAAI,CAACC,SAAS,EAAE;IAAE,OAAO,IAAI;EAAE;EAE1C,IAAIC,OAAO,GAAGJ,gBAAgB,CAACE,MAAM,EAAEC,SAAS,CAAC;EAEjD,IAAI,CAACC,OAAO,EAAE;IAAE,OAAO,IAAI;EAAE;EAE7B,IAAIC,MAAM,GAAG,CAAEH,MAAM,CAAE,CAACI,MAAM,CAACP,OAAO,CAACG,MAAM,EAAEE,OAAO,CAAC,CAAC,CAACG,MAAM,CAC3D,UAAUC,KAAK,EAAEC,CAAC,EAAE;IAChB,IAAIC,WAAW,GAAGD,CAAC,CAACE,KAAK,CAACV,MAAM,IAAIW,MAAM,CAACC,gBAAgB,CAACJ,CAAC,CAAC,CAACR,MAAM;IACrE,IAAIa,OAAO,GAAGC,QAAQ,CAACL,WAAW,EAAE,EAAE,CAAC;IACvC,OAAOI,OAAO,GAAGN,KAAK,GAAGM,OAAO,GAAGN,KAAK;EAC5C,CAAC,EACD,CACJ,CAAC;EAED,OAAOH,MAAM,GAAIA,MAAM,GAAG,CAAC,GAAI,IAAI;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}