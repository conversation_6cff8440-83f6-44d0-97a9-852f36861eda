{"ast": null, "code": "import { adjustDST } from './adjust-dst';\nimport { setYear } from './set-year';\n/**\n * A function that adds and subtracts years from a `Date` object.\n *\n * @param date - The initial date value.\n * @param offset - The number of years to add or subtract from the date.\n * @returns - A new `Date` instance.\n *\n * @example\n * ```ts-no-run\n * addYears(new Date(2016, 5, 1), 5); // 2011-6-1\n * addYears(new Date(2016, 5, 1), -5); // 2021-6-1\n * ```\n */\nexport var addYears = function (value, offset) {\n  return adjustDST(setYear(value, value.getFullYear() + offset), value.getHours());\n};", "map": {"version": 3, "names": ["adjustDST", "setYear", "addYears", "value", "offset", "getFullYear", "getHours"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/add-years.js"], "sourcesContent": ["import { adjustDST } from './adjust-dst';\nimport { setYear } from './set-year';\n/**\n * A function that adds and subtracts years from a `Date` object.\n *\n * @param date - The initial date value.\n * @param offset - The number of years to add or subtract from the date.\n * @returns - A new `Date` instance.\n *\n * @example\n * ```ts-no-run\n * addYears(new Date(2016, 5, 1), 5); // 2011-6-1\n * addYears(new Date(2016, 5, 1), -5); // 2021-6-1\n * ```\n */\nexport var addYears = function (value, offset) {\n    return adjustDST(setYear(value, value.getFullYear() + offset), value.getHours());\n};\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,cAAc;AACxC,SAASC,OAAO,QAAQ,YAAY;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,QAAQ,GAAG,SAAAA,CAAUC,KAAK,EAAEC,MAAM,EAAE;EAC3C,OAAOJ,SAAS,CAACC,OAAO,CAACE,KAAK,EAAEA,KAAK,CAACE,WAAW,CAAC,CAAC,GAAGD,MAAM,CAAC,EAAED,KAAK,CAACG,QAAQ,CAAC,CAAC,CAAC;AACpF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}