{"ast": null, "code": "import Group from '../shapes/group';\nimport Point from '../geometry/point';\nimport { Observable } from '../common';\nimport { eventElement, eventCoordinates, elementPadding, elementScale, elementSize, elementOffset } from '../util';\nvar events = [\"click\", \"mouseenter\", \"mouseleave\", \"mousemove\", \"resize\"];\nvar Surface = function (Observable) {\n  function Surface(element, options) {\n    Observable.call(this);\n    this.options = Object.assign({}, options);\n    this.element = element;\n    this.element._kendoExportVisual = this.exportVisual.bind(this);\n    this._click = this._handler(\"click\");\n    this._mouseenter = this._handler(\"mouseenter\");\n    this._mouseleave = this._handler(\"mouseleave\");\n    this._mousemove = this._handler(\"mousemove\");\n    this._visual = new Group();\n    elementSize(element, this.options);\n    this.bind(events, this.options);\n    this._enableTracking();\n  }\n  if (Observable) Surface.__proto__ = Observable;\n  Surface.prototype = Object.create(Observable && Observable.prototype);\n  Surface.prototype.constructor = Surface;\n  Surface.prototype.draw = function draw(element) {\n    this._visual.children.push(element);\n  };\n  Surface.prototype.clear = function clear() {\n    this._visual.children = [];\n  };\n  Surface.prototype.destroy = function destroy() {\n    this._visual = null;\n    this.element._kendoExportVisual = null;\n    this.unbind();\n  };\n  Surface.prototype.eventTarget = function eventTarget(e) {\n    var this$1 = this;\n    var domNode = eventElement(e);\n    var node;\n    while (!node && domNode) {\n      node = domNode._kendoNode;\n      if (domNode === this$1.element) {\n        break;\n      }\n      domNode = domNode.parentElement;\n    }\n    if (node) {\n      return node.srcElement;\n    }\n  };\n  Surface.prototype.exportVisual = function exportVisual() {\n    return this._visual;\n  };\n  Surface.prototype.getSize = function getSize() {\n    return elementSize(this.element);\n  };\n  Surface.prototype.currentSize = function currentSize(size) {\n    if (size) {\n      this._size = size;\n    } else {\n      return this._size;\n    }\n  };\n  Surface.prototype.setSize = function setSize(size) {\n    elementSize(this.element, size);\n    this.currentSize(size);\n    this._resize();\n  };\n  Surface.prototype.resize = function resize(force) {\n    var size = this.getSize();\n    var currentSize = this.currentSize();\n    if (force || (size.width > 0 || size.height > 0) && (!currentSize || size.width !== currentSize.width || size.height !== currentSize.height)) {\n      this.currentSize(size);\n      this._resize(size, force);\n      this.trigger(\"resize\", size);\n    }\n  };\n  Surface.prototype.size = function size(value) {\n    if (!value) {\n      return this.getSize();\n    }\n    this.setSize(value);\n  };\n  Surface.prototype.suspendTracking = function suspendTracking() {\n    this._suspendedTracking = true;\n  };\n  Surface.prototype.resumeTracking = function resumeTracking() {\n    this._suspendedTracking = false;\n  };\n  Surface.prototype._enableTracking = function _enableTracking() {};\n  Surface.prototype._resize = function _resize() {};\n  Surface.prototype._handler = function _handler(eventName) {\n    var this$1 = this;\n    return function (e) {\n      var node = this$1.eventTarget(e);\n      if (node && !this$1._suspendedTracking) {\n        this$1.trigger(eventName, {\n          element: node,\n          originalEvent: e,\n          type: eventName\n        });\n      }\n    };\n  };\n  Surface.prototype._elementOffset = function _elementOffset() {\n    var element = this.element;\n    var padding = elementPadding(element);\n    var ref = elementOffset(element);\n    var left = ref.left;\n    var top = ref.top;\n    return {\n      left: left + padding.left,\n      top: top + padding.top\n    };\n  };\n  Surface.prototype._surfacePoint = function _surfacePoint(e) {\n    var offset = this._elementOffset();\n    var coord = eventCoordinates(e);\n    var x = coord.x - offset.left;\n    var y = coord.y - offset.top;\n    var inverseTransform = elementScale(this.element).invert();\n    var point = new Point(x, y).transform(inverseTransform);\n    return point;\n  };\n  return Surface;\n}(Observable);\nexport default Surface;", "map": {"version": 3, "names": ["Group", "Point", "Observable", "eventElement", "eventCoordinates", "elementPadding", "elementScale", "elementSize", "elementOffset", "events", "Surface", "element", "options", "call", "Object", "assign", "_kendoExportVisual", "exportVisual", "bind", "_click", "_handler", "_mouseenter", "_mouseleave", "_mousemove", "_visual", "_enableTracking", "__proto__", "prototype", "create", "constructor", "draw", "children", "push", "clear", "destroy", "unbind", "eventTarget", "e", "this$1", "domNode", "node", "_kendoNode", "parentElement", "srcElement", "getSize", "currentSize", "size", "_size", "setSize", "_resize", "resize", "force", "width", "height", "trigger", "value", "suspendTracking", "_suspendedTracking", "resumeTracking", "eventName", "originalEvent", "type", "_elementOffset", "padding", "ref", "left", "top", "_surfacePoint", "offset", "coord", "x", "y", "inverseTransform", "invert", "point", "transform"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/core/surface.js"], "sourcesContent": ["import Group from '../shapes/group';\nimport Point from '../geometry/point';\nimport { Observable } from '../common';\nimport { eventElement, eventCoordinates, elementPadding, elementScale, elementSize, elementOffset } from '../util';\n\nvar events = [\n    \"click\",\n    \"mouseenter\",\n    \"mouseleave\",\n    \"mousemove\",\n    \"resize\"\n];\n\nvar Surface = (function (Observable) {\n    function Surface(element, options) {\n        Observable.call(this);\n\n        this.options = Object.assign({}, options);\n        this.element = element;\n        this.element._kendoExportVisual = this.exportVisual.bind(this);\n\n        this._click = this._handler(\"click\");\n        this._mouseenter = this._handler(\"mouseenter\");\n        this._mouseleave = this._handler(\"mouseleave\");\n        this._mousemove = this._handler(\"mousemove\");\n\n        this._visual = new Group();\n\n        elementSize(element, this.options);\n\n        this.bind(events, this.options);\n\n        this._enableTracking();\n    }\n\n    if ( Observable ) Surface.__proto__ = Observable;\n    Surface.prototype = Object.create( Observable && Observable.prototype );\n    Surface.prototype.constructor = Surface;\n\n    Surface.prototype.draw = function draw (element) {\n        this._visual.children.push(element);\n    };\n\n    Surface.prototype.clear = function clear () {\n        this._visual.children = [];\n    };\n\n    Surface.prototype.destroy = function destroy () {\n        this._visual = null;\n        this.element._kendoExportVisual = null;\n        this.unbind();\n    };\n\n    Surface.prototype.eventTarget = function eventTarget (e) {\n        var this$1 = this;\n\n        var domNode = eventElement(e);\n        var node;\n\n        while (!node && domNode) {\n            node = domNode._kendoNode;\n            if (domNode === this$1.element) {\n                break;\n            }\n\n            domNode = domNode.parentElement;\n        }\n\n        if (node) {\n            return node.srcElement;\n        }\n    };\n\n    Surface.prototype.exportVisual = function exportVisual () {\n        return this._visual;\n    };\n\n    Surface.prototype.getSize = function getSize () {\n        return elementSize(this.element);\n    };\n\n    Surface.prototype.currentSize = function currentSize (size) {\n        if (size) {\n            this._size = size;\n        } else {\n            return this._size;\n        }\n    };\n\n    Surface.prototype.setSize = function setSize (size) {\n        elementSize(this.element, size);\n\n        this.currentSize(size);\n        this._resize();\n    };\n\n    Surface.prototype.resize = function resize (force) {\n        var size = this.getSize();\n        var currentSize = this.currentSize();\n\n        if (force || (size.width > 0 || size.height > 0) && (!currentSize || size.width !== currentSize.width || size.height !== currentSize.height)) {\n            this.currentSize(size);\n            this._resize(size, force);\n            this.trigger(\"resize\", size);\n        }\n    };\n\n    Surface.prototype.size = function size (value) {\n        if (!value) {\n            return this.getSize();\n        }\n\n        this.setSize(value);\n    };\n\n    Surface.prototype.suspendTracking = function suspendTracking () {\n        this._suspendedTracking = true;\n    };\n\n    Surface.prototype.resumeTracking = function resumeTracking () {\n        this._suspendedTracking = false;\n    };\n\n    Surface.prototype._enableTracking = function _enableTracking () {};\n\n    Surface.prototype._resize = function _resize () {};\n\n    Surface.prototype._handler = function _handler (eventName) {\n        var this$1 = this;\n\n        return function (e) {\n            var node = this$1.eventTarget(e);\n            if (node && !this$1._suspendedTracking) {\n                this$1.trigger(eventName, {\n                    element: node,\n                    originalEvent: e,\n                    type: eventName\n                });\n            }\n        };\n    };\n\n    Surface.prototype._elementOffset = function _elementOffset () {\n        var element = this.element;\n        var padding = elementPadding(element);\n        var ref = elementOffset(element);\n        var left = ref.left;\n        var top = ref.top;\n\n        return {\n            left: left + padding.left,\n            top: top + padding.top\n        };\n    };\n\n    Surface.prototype._surfacePoint = function _surfacePoint (e) {\n        var offset = this._elementOffset();\n        var coord = eventCoordinates(e);\n        var x = coord.x - offset.left;\n        var y = coord.y - offset.top;\n\n        var inverseTransform = elementScale(this.element).invert();\n        var point = new Point(\n            x,\n            y\n        ).transform(inverseTransform);\n\n        return point;\n    };\n\n    return Surface;\n}(Observable));\n\nexport default Surface;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,iBAAiB;AACnC,OAAOC,KAAK,MAAM,mBAAmB;AACrC,SAASC,UAAU,QAAQ,WAAW;AACtC,SAASC,YAAY,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,YAAY,EAAEC,WAAW,EAAEC,aAAa,QAAQ,SAAS;AAElH,IAAIC,MAAM,GAAG,CACT,OAAO,EACP,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,QAAQ,CACX;AAED,IAAIC,OAAO,GAAI,UAAUR,UAAU,EAAE;EACjC,SAASQ,OAAOA,CAACC,OAAO,EAAEC,OAAO,EAAE;IAC/BV,UAAU,CAACW,IAAI,CAAC,IAAI,CAAC;IAErB,IAAI,CAACD,OAAO,GAAGE,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,OAAO,CAAC;IACzC,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACA,OAAO,CAACK,kBAAkB,GAAG,IAAI,CAACC,YAAY,CAACC,IAAI,CAAC,IAAI,CAAC;IAE9D,IAAI,CAACC,MAAM,GAAG,IAAI,CAACC,QAAQ,CAAC,OAAO,CAAC;IACpC,IAAI,CAACC,WAAW,GAAG,IAAI,CAACD,QAAQ,CAAC,YAAY,CAAC;IAC9C,IAAI,CAACE,WAAW,GAAG,IAAI,CAACF,QAAQ,CAAC,YAAY,CAAC;IAC9C,IAAI,CAACG,UAAU,GAAG,IAAI,CAACH,QAAQ,CAAC,WAAW,CAAC;IAE5C,IAAI,CAACI,OAAO,GAAG,IAAIxB,KAAK,CAAC,CAAC;IAE1BO,WAAW,CAACI,OAAO,EAAE,IAAI,CAACC,OAAO,CAAC;IAElC,IAAI,CAACM,IAAI,CAACT,MAAM,EAAE,IAAI,CAACG,OAAO,CAAC;IAE/B,IAAI,CAACa,eAAe,CAAC,CAAC;EAC1B;EAEA,IAAKvB,UAAU,EAAGQ,OAAO,CAACgB,SAAS,GAAGxB,UAAU;EAChDQ,OAAO,CAACiB,SAAS,GAAGb,MAAM,CAACc,MAAM,CAAE1B,UAAU,IAAIA,UAAU,CAACyB,SAAU,CAAC;EACvEjB,OAAO,CAACiB,SAAS,CAACE,WAAW,GAAGnB,OAAO;EAEvCA,OAAO,CAACiB,SAAS,CAACG,IAAI,GAAG,SAASA,IAAIA,CAAEnB,OAAO,EAAE;IAC7C,IAAI,CAACa,OAAO,CAACO,QAAQ,CAACC,IAAI,CAACrB,OAAO,CAAC;EACvC,CAAC;EAEDD,OAAO,CAACiB,SAAS,CAACM,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAI;IACxC,IAAI,CAACT,OAAO,CAACO,QAAQ,GAAG,EAAE;EAC9B,CAAC;EAEDrB,OAAO,CAACiB,SAAS,CAACO,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;IAC5C,IAAI,CAACV,OAAO,GAAG,IAAI;IACnB,IAAI,CAACb,OAAO,CAACK,kBAAkB,GAAG,IAAI;IACtC,IAAI,CAACmB,MAAM,CAAC,CAAC;EACjB,CAAC;EAEDzB,OAAO,CAACiB,SAAS,CAACS,WAAW,GAAG,SAASA,WAAWA,CAAEC,CAAC,EAAE;IACrD,IAAIC,MAAM,GAAG,IAAI;IAEjB,IAAIC,OAAO,GAAGpC,YAAY,CAACkC,CAAC,CAAC;IAC7B,IAAIG,IAAI;IAER,OAAO,CAACA,IAAI,IAAID,OAAO,EAAE;MACrBC,IAAI,GAAGD,OAAO,CAACE,UAAU;MACzB,IAAIF,OAAO,KAAKD,MAAM,CAAC3B,OAAO,EAAE;QAC5B;MACJ;MAEA4B,OAAO,GAAGA,OAAO,CAACG,aAAa;IACnC;IAEA,IAAIF,IAAI,EAAE;MACN,OAAOA,IAAI,CAACG,UAAU;IAC1B;EACJ,CAAC;EAEDjC,OAAO,CAACiB,SAAS,CAACV,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAI;IACtD,OAAO,IAAI,CAACO,OAAO;EACvB,CAAC;EAEDd,OAAO,CAACiB,SAAS,CAACiB,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;IAC5C,OAAOrC,WAAW,CAAC,IAAI,CAACI,OAAO,CAAC;EACpC,CAAC;EAEDD,OAAO,CAACiB,SAAS,CAACkB,WAAW,GAAG,SAASA,WAAWA,CAAEC,IAAI,EAAE;IACxD,IAAIA,IAAI,EAAE;MACN,IAAI,CAACC,KAAK,GAAGD,IAAI;IACrB,CAAC,MAAM;MACH,OAAO,IAAI,CAACC,KAAK;IACrB;EACJ,CAAC;EAEDrC,OAAO,CAACiB,SAAS,CAACqB,OAAO,GAAG,SAASA,OAAOA,CAAEF,IAAI,EAAE;IAChDvC,WAAW,CAAC,IAAI,CAACI,OAAO,EAAEmC,IAAI,CAAC;IAE/B,IAAI,CAACD,WAAW,CAACC,IAAI,CAAC;IACtB,IAAI,CAACG,OAAO,CAAC,CAAC;EAClB,CAAC;EAEDvC,OAAO,CAACiB,SAAS,CAACuB,MAAM,GAAG,SAASA,MAAMA,CAAEC,KAAK,EAAE;IAC/C,IAAIL,IAAI,GAAG,IAAI,CAACF,OAAO,CAAC,CAAC;IACzB,IAAIC,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC,CAAC;IAEpC,IAAIM,KAAK,IAAI,CAACL,IAAI,CAACM,KAAK,GAAG,CAAC,IAAIN,IAAI,CAACO,MAAM,GAAG,CAAC,MAAM,CAACR,WAAW,IAAIC,IAAI,CAACM,KAAK,KAAKP,WAAW,CAACO,KAAK,IAAIN,IAAI,CAACO,MAAM,KAAKR,WAAW,CAACQ,MAAM,CAAC,EAAE;MAC1I,IAAI,CAACR,WAAW,CAACC,IAAI,CAAC;MACtB,IAAI,CAACG,OAAO,CAACH,IAAI,EAAEK,KAAK,CAAC;MACzB,IAAI,CAACG,OAAO,CAAC,QAAQ,EAAER,IAAI,CAAC;IAChC;EACJ,CAAC;EAEDpC,OAAO,CAACiB,SAAS,CAACmB,IAAI,GAAG,SAASA,IAAIA,CAAES,KAAK,EAAE;IAC3C,IAAI,CAACA,KAAK,EAAE;MACR,OAAO,IAAI,CAACX,OAAO,CAAC,CAAC;IACzB;IAEA,IAAI,CAACI,OAAO,CAACO,KAAK,CAAC;EACvB,CAAC;EAED7C,OAAO,CAACiB,SAAS,CAAC6B,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAI;IAC5D,IAAI,CAACC,kBAAkB,GAAG,IAAI;EAClC,CAAC;EAED/C,OAAO,CAACiB,SAAS,CAAC+B,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAI;IAC1D,IAAI,CAACD,kBAAkB,GAAG,KAAK;EACnC,CAAC;EAED/C,OAAO,CAACiB,SAAS,CAACF,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAI,CAAC,CAAC;EAElEf,OAAO,CAACiB,SAAS,CAACsB,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI,CAAC,CAAC;EAElDvC,OAAO,CAACiB,SAAS,CAACP,QAAQ,GAAG,SAASA,QAAQA,CAAEuC,SAAS,EAAE;IACvD,IAAIrB,MAAM,GAAG,IAAI;IAEjB,OAAO,UAAUD,CAAC,EAAE;MAChB,IAAIG,IAAI,GAAGF,MAAM,CAACF,WAAW,CAACC,CAAC,CAAC;MAChC,IAAIG,IAAI,IAAI,CAACF,MAAM,CAACmB,kBAAkB,EAAE;QACpCnB,MAAM,CAACgB,OAAO,CAACK,SAAS,EAAE;UACtBhD,OAAO,EAAE6B,IAAI;UACboB,aAAa,EAAEvB,CAAC;UAChBwB,IAAI,EAAEF;QACV,CAAC,CAAC;MACN;IACJ,CAAC;EACL,CAAC;EAEDjD,OAAO,CAACiB,SAAS,CAACmC,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAI;IAC1D,IAAInD,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,IAAIoD,OAAO,GAAG1D,cAAc,CAACM,OAAO,CAAC;IACrC,IAAIqD,GAAG,GAAGxD,aAAa,CAACG,OAAO,CAAC;IAChC,IAAIsD,IAAI,GAAGD,GAAG,CAACC,IAAI;IACnB,IAAIC,GAAG,GAAGF,GAAG,CAACE,GAAG;IAEjB,OAAO;MACHD,IAAI,EAAEA,IAAI,GAAGF,OAAO,CAACE,IAAI;MACzBC,GAAG,EAAEA,GAAG,GAAGH,OAAO,CAACG;IACvB,CAAC;EACL,CAAC;EAEDxD,OAAO,CAACiB,SAAS,CAACwC,aAAa,GAAG,SAASA,aAAaA,CAAE9B,CAAC,EAAE;IACzD,IAAI+B,MAAM,GAAG,IAAI,CAACN,cAAc,CAAC,CAAC;IAClC,IAAIO,KAAK,GAAGjE,gBAAgB,CAACiC,CAAC,CAAC;IAC/B,IAAIiC,CAAC,GAAGD,KAAK,CAACC,CAAC,GAAGF,MAAM,CAACH,IAAI;IAC7B,IAAIM,CAAC,GAAGF,KAAK,CAACE,CAAC,GAAGH,MAAM,CAACF,GAAG;IAE5B,IAAIM,gBAAgB,GAAGlE,YAAY,CAAC,IAAI,CAACK,OAAO,CAAC,CAAC8D,MAAM,CAAC,CAAC;IAC1D,IAAIC,KAAK,GAAG,IAAIzE,KAAK,CACjBqE,CAAC,EACDC,CACJ,CAAC,CAACI,SAAS,CAACH,gBAAgB,CAAC;IAE7B,OAAOE,KAAK;EAChB,CAAC;EAED,OAAOhE,OAAO;AAClB,CAAC,CAACR,UAAU,CAAE;AAEd,eAAeQ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}