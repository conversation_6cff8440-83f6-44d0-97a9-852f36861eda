{"ast": null, "code": "import { exportImage, geometry, Group, MultiPath, Path, Image, Surface } from '@progress/kendo-drawing';\nimport { elementOffset, limitValue } from '../common';\nvar Point = geometry.Point,\n  Rect = geometry.Rect,\n  transform = geometry.transform;\nvar noop = function () {};\nvar DECIMAL_DIGITS = 3;\nvar DEFAULT_COLOR = '#000';\nvar DEFAULT_BACKGROUND_COLOR = '#fff';\nvar DEFAULT_PRECISION = 1;\nvar DEFAULT_SAMPLING_RATE = 200; // Updates per second\nvar DEFAULT_STROKE_WIDTH = 1;\nvar DEFAULT_WIDTH = 750;\nvar DEFAULT_HEIGHT = 250;\nvar DEFAULT_SCALE = 1;\n// Export images at maximized scale (3x) and 2x pixel density to cover HiDPI screens.\nvar DEFAULT_EXPORT_SCALE = 6;\nvar SignaturePad = /** @class */function () {\n  function SignaturePad(element, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    this.element = element;\n    this.lastMoveTime = 0;\n    this.resolveColors(options);\n    this.options = Object.assign({\n      scale: DEFAULT_SCALE,\n      precision: DEFAULT_PRECISION,\n      samplingRate: DEFAULT_SAMPLING_RATE,\n      smooth: options.smooth !== false,\n      color: DEFAULT_COLOR,\n      backgroundColor: DEFAULT_BACKGROUND_COLOR,\n      strokeWidth: DEFAULT_STROKE_WIDTH,\n      onChange: noop,\n      onDraw: noop,\n      onDrawEnd: noop\n    }, options, {\n      color: this.color,\n      backgroundColor: this.backgroundColor\n    });\n    this.pathOptions = {\n      stroke: {\n        color: this.options.color,\n        width: this.options.strokeWidth,\n        lineCap: 'round',\n        lineJoin: 'round'\n      }\n    };\n    this.initSurface();\n    this.attachEvents();\n  }\n  SignaturePad.prototype.destroy = function () {\n    this.detachEvents();\n  };\n  SignaturePad.prototype.clear = function () {\n    this.rootGroup.clear();\n    this.path = null;\n  };\n  SignaturePad.prototype.readThemeColors = function () {\n    var themeColor;\n    var themeBackgroundColor;\n    if (typeof document !== 'undefined') {\n      var themeElement = this.element.closest(\".k-signature\") || this.element;\n      var computedStyle = themeElement.ownerDocument.defaultView.getComputedStyle(themeElement);\n      themeColor = computedStyle.color;\n      themeBackgroundColor = computedStyle.backgroundColor;\n    }\n    this.themeColor = themeColor || this.themeColor || DEFAULT_COLOR;\n    this.themeBackgroundColor = themeBackgroundColor || this.themeBackgroundColor || DEFAULT_BACKGROUND_COLOR;\n  };\n  SignaturePad.prototype.resolveColors = function (options) {\n    this.readThemeColors();\n    this.color = options.color || (this.options || {}).color || this.themeColor;\n    this.backgroundColor = options.backgroundColor || (this.options || {}).backgroundColor || this.themeBackgroundColor;\n  };\n  Object.defineProperty(SignaturePad.prototype, \"isDrawing\", {\n    get: function () {\n      return Boolean(this.points);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(SignaturePad.prototype, \"pathData\", {\n    get: function () {\n      var _a;\n      return (_a = this.path) === null || _a === void 0 ? void 0 : _a.toString(DECIMAL_DIGITS);\n    },\n    set: function (value) {\n      this.clear();\n      this.path = MultiPath.parse(value, this.pathOptions);\n      this.rootGroup.append(this.path);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  SignaturePad.prototype.loadImage = function (data, size) {\n    if (size === void 0) {\n      size = [];\n    }\n    if (!data) {\n      this.clear();\n      return;\n    }\n    var _a = this.size,\n      width = _a[0],\n      height = _a[1];\n    var contentWidth = width / this.options.scale;\n    var contentHeight = height / this.options.scale;\n    var importWidth = size[0] || contentWidth * DEFAULT_EXPORT_SCALE;\n    var importHeight = size[1] || contentHeight * DEFAULT_EXPORT_SCALE;\n    var scaleX = contentWidth / importWidth;\n    var scaleY = contentHeight / importHeight;\n    var scale = Math.min(scaleX, scaleY);\n    var img = new Image(data, new geometry.Rect([0, 0], [importWidth, importHeight]));\n    img.transform(transform().scale(scale, scale));\n    this.clear();\n    this.rootGroup.append(img);\n  };\n  SignaturePad.prototype.exportImage = function (options) {\n    var _a;\n    var _b = this.size,\n      width = _b[0],\n      height = _b[1];\n    var contentWidth = width / this.options.scale;\n    var contentHeight = height / this.options.scale;\n    var exportWidth = (options === null || options === void 0 ? void 0 : options.width) || contentWidth * DEFAULT_EXPORT_SCALE;\n    var exportHeight = (options === null || options === void 0 ? void 0 : options.height) || contentHeight * DEFAULT_EXPORT_SCALE;\n    var scaleX = exportWidth / contentWidth;\n    var scaleY = exportHeight / contentHeight;\n    var scale = Math.min(scaleX, scaleY);\n    var exportRect = new Rect([0, 0], [exportWidth, exportHeight]);\n    var exportGroup = new Group({\n      clip: Path.fromRect(exportRect)\n    });\n    var contentGroup = new Group({\n      transform: transform().scale(scale, scale)\n    });\n    var frame = Path.fromRect(exportRect, {\n      fill: {\n        color: this.options.backgroundColor\n      }\n    });\n    exportGroup.append(frame);\n    exportGroup.append(contentGroup);\n    (_a = contentGroup.children).push.apply(_a, this.rootGroup.children);\n    return exportImage(exportGroup, Object.assign({\n      width: exportWidth,\n      height: exportHeight\n    }, options));\n  };\n  SignaturePad.prototype.resize = function () {\n    this.surface.resize(true);\n  };\n  SignaturePad.prototype.setOptions = function (options) {\n    this.resolveColors(options);\n    Object.assign(this.options, options, {\n      color: this.color,\n      backgroundColor: this.backgroundColor\n    });\n    this.pathOptions.stroke.color = this.options.color;\n    this.pathOptions.stroke.width = this.options.strokeWidth;\n    if (this.path) {\n      this.path.options.set('stroke.color', this.options.color);\n      this.path.options.set('stroke.width', this.options.strokeWidth);\n    }\n    this.background.options.set('fill.color', this.options.backgroundColor);\n  };\n  SignaturePad.prototype.initSurface = function () {\n    this.surface = Surface.create(this.element, {\n      type: 'canvas'\n    });\n    this.element.style.touchAction = 'none';\n    var scale = this.options.scale;\n    this.rootGroup = new Group({\n      transform: transform().scale(scale, scale)\n    });\n    // The signature is not resizable, store initial dimensions.\n    var width = this.element.offsetWidth || DEFAULT_WIDTH;\n    var height = this.element.offsetHeight || DEFAULT_HEIGHT;\n    this.size = [width, height];\n    this.background = Path.fromRect(new Rect([0, 0], this.size), {\n      fill: {\n        color: this.options.backgroundColor\n      }\n    });\n    this.surface.draw(this.background);\n    this.surface.draw(this.rootGroup);\n  };\n  SignaturePad.prototype.attachEvents = function () {\n    this.onPointerDown = this.onPointerDown.bind(this);\n    this.onPointerMove = this.onPointerMove.bind(this);\n    this.onPointerUp = this.onPointerUp.bind(this);\n    this.onDragStart = this.onDragStart.bind(this);\n    this.element.addEventListener('pointerdown', this.onPointerDown);\n    this.element.addEventListener('pointerup', this.onPointerUp);\n    this.element.addEventListener('dragstart', this.onDragStart);\n  };\n  SignaturePad.prototype.detachEvents = function () {\n    this.element.removeEventListener('pointerdown', this.onPointerDown);\n    this.detachPointerMove();\n    this.element.removeEventListener('pointerup', this.onPointerUp);\n    this.element.removeEventListener('dragstart', this.onDragStart);\n  };\n  SignaturePad.prototype.attachPointerMove = function () {\n    this.element.addEventListener('pointermove', this.onPointerMove);\n  };\n  SignaturePad.prototype.detachPointerMove = function () {\n    this.element.removeEventListener('pointermove', this.onPointerMove);\n  };\n  SignaturePad.prototype.touchPoint = function (e) {\n    var offset = elementOffset(this.element);\n    var pageX = e.pageX;\n    var pageY = e.pageY;\n    var scale = 1 / this.options.scale;\n    return new Point(pageX - offset.left, pageY - offset.top).scale(scale, scale);\n  };\n  SignaturePad.prototype.onDragStart = function (e) {\n    // fixes a problem that is currently reproducible in Chrome on Windows only\n    // 1. Start selecting a text on the page and while holding, move inside the signature\n    // 2. Release.\n    // 3. Start drawing without releasing the mouse - the action is blocked (browser assumes dragstart event).\n    // 4. If you release, then you can continue drawing although you haven't pressed on the drawing surface.\n    e.preventDefault();\n  };\n  SignaturePad.prototype.onPointerDown = function (e) {\n    if (this.options.readonly || !e.isPrimary || !isMainButton(e)) {\n      return;\n    }\n    this.detachPointerMove();\n    this.attachPointerMove();\n    if (!this.path) {\n      this.path = new MultiPath(this.pathOptions);\n      this.rootGroup.append(this.path);\n    }\n    this.options.onDraw();\n    this.element.setPointerCapture(e.pointerId);\n    var point = this.touchPoint(e);\n    this.points = [point];\n    this.path.moveTo(point);\n  };\n  SignaturePad.prototype.onPointerMove = function (e) {\n    if (!this.points || !this.path || !e.isPrimary) {\n      return;\n    }\n    var now = new Date().getTime();\n    var elapsed = now - this.lastMoveTime;\n    var minTimeDelta = 1000 / limitValue(this.options.samplingRate, 1, 10000);\n    if (elapsed < minTimeDelta) {\n      return;\n    } else {\n      this.lastMoveTime = now;\n    }\n    var point = this.touchPoint(e);\n    var lastPoint = this.points[this.points.length - 1];\n    var minDelta = 1 / limitValue(this.options.precision, 0.01, 100);\n    if (point.distanceTo(lastPoint) < minDelta) {\n      return;\n    }\n    this.points.push(point);\n    this.path.lineTo(point);\n  };\n  SignaturePad.prototype.onPointerUp = function (e) {\n    if (!e.isPrimary || !this.path || !this.points || this.options.readonly) {\n      return;\n    }\n    this.detachPointerMove();\n    if (this.options.smooth) {\n      var segments = Path.curveFromPoints(this.points);\n      this.path.paths.splice(this.path.paths.length - 1, 1, segments);\n    }\n    this.points = null;\n    this.options.onDrawEnd();\n    this.options.onChange(this.pathData);\n  };\n  return SignaturePad;\n}();\nexport { SignaturePad };\nfunction isMainButton(e) {\n  return typeof e.button !== 'number' || e.button === 0;\n}", "map": {"version": 3, "names": ["exportImage", "geometry", "Group", "MultiPath", "Path", "Image", "Surface", "elementOffset", "limitValue", "Point", "Rect", "transform", "noop", "DECIMAL_DIGITS", "DEFAULT_COLOR", "DEFAULT_BACKGROUND_COLOR", "DEFAULT_PRECISION", "DEFAULT_SAMPLING_RATE", "DEFAULT_STROKE_WIDTH", "DEFAULT_WIDTH", "DEFAULT_HEIGHT", "DEFAULT_SCALE", "DEFAULT_EXPORT_SCALE", "SignaturePad", "element", "options", "lastMoveTime", "resolveColors", "Object", "assign", "scale", "precision", "samplingRate", "smooth", "color", "backgroundColor", "strokeWidth", "onChange", "onDraw", "onDrawEnd", "pathOptions", "stroke", "width", "lineCap", "lineJoin", "initSurface", "attachEvents", "prototype", "destroy", "detachEvents", "clear", "rootGroup", "path", "readThemeColors", "themeColor", "themeBackgroundColor", "document", "themeElement", "closest", "computedStyle", "ownerDocument", "defaultView", "getComputedStyle", "defineProperty", "get", "Boolean", "points", "enumerable", "configurable", "_a", "toString", "set", "value", "parse", "append", "loadImage", "data", "size", "height", "contentWidth", "contentHeight", "importWidth", "importHeight", "scaleX", "scaleY", "Math", "min", "img", "_b", "exportWidth", "exportHeight", "exportRect", "exportGroup", "clip", "fromRect", "contentGroup", "frame", "fill", "children", "push", "apply", "resize", "surface", "setOptions", "background", "create", "type", "style", "touchAction", "offsetWidth", "offsetHeight", "draw", "onPointerDown", "bind", "onPointerMove", "onPointerUp", "onDragStart", "addEventListener", "removeEventListener", "detachPointerMove", "attachPointerMove", "touchPoint", "e", "offset", "pageX", "pageY", "left", "top", "preventDefault", "readonly", "isPrimary", "is<PERSON>ain<PERSON><PERSON><PERSON>", "setPointerCapture", "pointerId", "point", "moveTo", "now", "Date", "getTime", "elapsed", "minTimeDelta", "lastPoint", "length", "min<PERSON><PERSON><PERSON>", "distanceTo", "lineTo", "segments", "curveFromPoints", "paths", "splice", "pathData", "button"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-inputs-common/dist/es/signature/signature-pad.js"], "sourcesContent": ["import { exportImage, geometry, Group, MultiPath, Path, Image, Surface, } from '@progress/kendo-drawing';\nimport { elementOffset, limitValue } from '../common';\nvar Point = geometry.Point, Rect = geometry.Rect, transform = geometry.transform;\nvar noop = function () { };\nvar DECIMAL_DIGITS = 3;\nvar DEFAULT_COLOR = '#000';\nvar DEFAULT_BACKGROUND_COLOR = '#fff';\nvar DEFAULT_PRECISION = 1;\nvar DEFAULT_SAMPLING_RATE = 200; // Updates per second\nvar DEFAULT_STROKE_WIDTH = 1;\nvar DEFAULT_WIDTH = 750;\nvar DEFAULT_HEIGHT = 250;\nvar DEFAULT_SCALE = 1;\n// Export images at maximized scale (3x) and 2x pixel density to cover HiDPI screens.\nvar DEFAULT_EXPORT_SCALE = 6;\nvar SignaturePad = /** @class */ (function () {\n    function SignaturePad(element, options) {\n        if (options === void 0) { options = {}; }\n        this.element = element;\n        this.lastMoveTime = 0;\n        this.resolveColors(options);\n        this.options = Object.assign({\n            scale: DEFAULT_SCALE,\n            precision: DEFAULT_PRECISION,\n            samplingRate: DEFAULT_SAMPLING_RATE,\n            smooth: options.smooth !== false,\n            color: DEFAULT_COLOR,\n            backgroundColor: DEFAULT_BACKGROUND_COLOR,\n            strokeWidth: DEFAULT_STROKE_WIDTH,\n            onChange: noop,\n            onDraw: noop,\n            onDrawEnd: noop\n        }, options, {\n            color: this.color,\n            backgroundColor: this.backgroundColor\n        });\n        this.pathOptions = {\n            stroke: {\n                color: this.options.color,\n                width: this.options.strokeWidth,\n                lineCap: 'round',\n                lineJoin: 'round'\n            }\n        };\n        this.initSurface();\n        this.attachEvents();\n    }\n    SignaturePad.prototype.destroy = function () {\n        this.detachEvents();\n    };\n    SignaturePad.prototype.clear = function () {\n        this.rootGroup.clear();\n        this.path = null;\n    };\n    SignaturePad.prototype.readThemeColors = function () {\n        var themeColor;\n        var themeBackgroundColor;\n        if (typeof document !== 'undefined') {\n            var themeElement = this.element.closest(\".k-signature\") || this.element;\n            var computedStyle = themeElement.ownerDocument.defaultView.getComputedStyle(themeElement);\n            themeColor = computedStyle.color;\n            themeBackgroundColor = computedStyle.backgroundColor;\n        }\n        this.themeColor = themeColor || this.themeColor || DEFAULT_COLOR;\n        this.themeBackgroundColor = themeBackgroundColor || this.themeBackgroundColor || DEFAULT_BACKGROUND_COLOR;\n    };\n    SignaturePad.prototype.resolveColors = function (options) {\n        this.readThemeColors();\n        this.color = options.color || (this.options || {}).color || this.themeColor;\n        this.backgroundColor = options.backgroundColor || (this.options || {}).backgroundColor || this.themeBackgroundColor;\n    };\n    Object.defineProperty(SignaturePad.prototype, \"isDrawing\", {\n        get: function () {\n            return Boolean(this.points);\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(SignaturePad.prototype, \"pathData\", {\n        get: function () {\n            var _a;\n            return (_a = this.path) === null || _a === void 0 ? void 0 : _a.toString(DECIMAL_DIGITS);\n        },\n        set: function (value) {\n            this.clear();\n            this.path = MultiPath.parse(value, this.pathOptions);\n            this.rootGroup.append(this.path);\n        },\n        enumerable: false,\n        configurable: true\n    });\n    SignaturePad.prototype.loadImage = function (data, size) {\n        if (size === void 0) { size = []; }\n        if (!data) {\n            this.clear();\n            return;\n        }\n        var _a = this.size, width = _a[0], height = _a[1];\n        var contentWidth = width / this.options.scale;\n        var contentHeight = height / this.options.scale;\n        var importWidth = size[0] || contentWidth * DEFAULT_EXPORT_SCALE;\n        var importHeight = size[1] || contentHeight * DEFAULT_EXPORT_SCALE;\n        var scaleX = contentWidth / importWidth;\n        var scaleY = contentHeight / importHeight;\n        var scale = Math.min(scaleX, scaleY);\n        var img = new Image(data, new geometry.Rect([0, 0], [importWidth, importHeight]));\n        img.transform(transform().scale(scale, scale));\n        this.clear();\n        this.rootGroup.append(img);\n    };\n    SignaturePad.prototype.exportImage = function (options) {\n        var _a;\n        var _b = this.size, width = _b[0], height = _b[1];\n        var contentWidth = width / this.options.scale;\n        var contentHeight = height / this.options.scale;\n        var exportWidth = (options === null || options === void 0 ? void 0 : options.width) || contentWidth * DEFAULT_EXPORT_SCALE;\n        var exportHeight = (options === null || options === void 0 ? void 0 : options.height) || contentHeight * DEFAULT_EXPORT_SCALE;\n        var scaleX = exportWidth / contentWidth;\n        var scaleY = exportHeight / contentHeight;\n        var scale = Math.min(scaleX, scaleY);\n        var exportRect = new Rect([0, 0], [exportWidth, exportHeight]);\n        var exportGroup = new Group({\n            clip: Path.fromRect(exportRect)\n        });\n        var contentGroup = new Group({\n            transform: transform().scale(scale, scale)\n        });\n        var frame = Path.fromRect(exportRect, {\n            fill: {\n                color: this.options.backgroundColor\n            }\n        });\n        exportGroup.append(frame);\n        exportGroup.append(contentGroup);\n        (_a = contentGroup.children).push.apply(_a, this.rootGroup.children);\n        return exportImage(exportGroup, Object.assign({\n            width: exportWidth,\n            height: exportHeight\n        }, options));\n    };\n    SignaturePad.prototype.resize = function () {\n        this.surface.resize(true);\n    };\n    SignaturePad.prototype.setOptions = function (options) {\n        this.resolveColors(options);\n        Object.assign(this.options, options, {\n            color: this.color,\n            backgroundColor: this.backgroundColor\n        });\n        this.pathOptions.stroke.color = this.options.color;\n        this.pathOptions.stroke.width = this.options.strokeWidth;\n        if (this.path) {\n            this.path.options.set('stroke.color', this.options.color);\n            this.path.options.set('stroke.width', this.options.strokeWidth);\n        }\n        this.background.options.set('fill.color', this.options.backgroundColor);\n    };\n    SignaturePad.prototype.initSurface = function () {\n        this.surface = Surface.create(this.element, { type: 'canvas' });\n        this.element.style.touchAction = 'none';\n        var scale = this.options.scale;\n        this.rootGroup = new Group({\n            transform: transform().scale(scale, scale)\n        });\n        // The signature is not resizable, store initial dimensions.\n        var width = this.element.offsetWidth || DEFAULT_WIDTH;\n        var height = this.element.offsetHeight || DEFAULT_HEIGHT;\n        this.size = [width, height];\n        this.background = Path.fromRect(new Rect([0, 0], this.size), {\n            fill: {\n                color: this.options.backgroundColor\n            }\n        });\n        this.surface.draw(this.background);\n        this.surface.draw(this.rootGroup);\n    };\n    SignaturePad.prototype.attachEvents = function () {\n        this.onPointerDown = this.onPointerDown.bind(this);\n        this.onPointerMove = this.onPointerMove.bind(this);\n        this.onPointerUp = this.onPointerUp.bind(this);\n        this.onDragStart = this.onDragStart.bind(this);\n        this.element.addEventListener('pointerdown', this.onPointerDown);\n        this.element.addEventListener('pointerup', this.onPointerUp);\n        this.element.addEventListener('dragstart', this.onDragStart);\n    };\n    SignaturePad.prototype.detachEvents = function () {\n        this.element.removeEventListener('pointerdown', this.onPointerDown);\n        this.detachPointerMove();\n        this.element.removeEventListener('pointerup', this.onPointerUp);\n        this.element.removeEventListener('dragstart', this.onDragStart);\n    };\n    SignaturePad.prototype.attachPointerMove = function () {\n        this.element.addEventListener('pointermove', this.onPointerMove);\n    };\n    SignaturePad.prototype.detachPointerMove = function () {\n        this.element.removeEventListener('pointermove', this.onPointerMove);\n    };\n    SignaturePad.prototype.touchPoint = function (e) {\n        var offset = elementOffset(this.element);\n        var pageX = e.pageX;\n        var pageY = e.pageY;\n        var scale = 1 / this.options.scale;\n        return new Point(pageX - offset.left, pageY - offset.top).scale(scale, scale);\n    };\n    SignaturePad.prototype.onDragStart = function (e) {\n        // fixes a problem that is currently reproducible in Chrome on Windows only\n        // 1. Start selecting a text on the page and while holding, move inside the signature\n        // 2. Release.\n        // 3. Start drawing without releasing the mouse - the action is blocked (browser assumes dragstart event).\n        // 4. If you release, then you can continue drawing although you haven't pressed on the drawing surface.\n        e.preventDefault();\n    };\n    SignaturePad.prototype.onPointerDown = function (e) {\n        if (this.options.readonly || !e.isPrimary || !isMainButton(e)) {\n            return;\n        }\n        this.detachPointerMove();\n        this.attachPointerMove();\n        if (!this.path) {\n            this.path = new MultiPath(this.pathOptions);\n            this.rootGroup.append(this.path);\n        }\n        this.options.onDraw();\n        this.element.setPointerCapture(e.pointerId);\n        var point = this.touchPoint(e);\n        this.points = [point];\n        this.path.moveTo(point);\n    };\n    SignaturePad.prototype.onPointerMove = function (e) {\n        if (!this.points || !this.path || !e.isPrimary) {\n            return;\n        }\n        var now = (new Date()).getTime();\n        var elapsed = now - this.lastMoveTime;\n        var minTimeDelta = 1000 / limitValue(this.options.samplingRate, 1, 10000);\n        if (elapsed < minTimeDelta) {\n            return;\n        }\n        else {\n            this.lastMoveTime = now;\n        }\n        var point = this.touchPoint(e);\n        var lastPoint = this.points[this.points.length - 1];\n        var minDelta = 1 / limitValue(this.options.precision, 0.01, 100);\n        if (point.distanceTo(lastPoint) < minDelta) {\n            return;\n        }\n        this.points.push(point);\n        this.path.lineTo(point);\n    };\n    SignaturePad.prototype.onPointerUp = function (e) {\n        if (!e.isPrimary || !this.path || !this.points || this.options.readonly) {\n            return;\n        }\n        this.detachPointerMove();\n        if (this.options.smooth) {\n            var segments = Path.curveFromPoints(this.points);\n            this.path.paths.splice(this.path.paths.length - 1, 1, segments);\n        }\n        this.points = null;\n        this.options.onDrawEnd();\n        this.options.onChange(this.pathData);\n    };\n    return SignaturePad;\n}());\nexport { SignaturePad };\nfunction isMainButton(e) {\n    return typeof (e.button) !== 'number' || e.button === 0;\n}\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,QAAS,yBAAyB;AACxG,SAASC,aAAa,EAAEC,UAAU,QAAQ,WAAW;AACrD,IAAIC,KAAK,GAAGR,QAAQ,CAACQ,KAAK;EAAEC,IAAI,GAAGT,QAAQ,CAACS,IAAI;EAAEC,SAAS,GAAGV,QAAQ,CAACU,SAAS;AAChF,IAAIC,IAAI,GAAG,SAAAA,CAAA,EAAY,CAAE,CAAC;AAC1B,IAAIC,cAAc,GAAG,CAAC;AACtB,IAAIC,aAAa,GAAG,MAAM;AAC1B,IAAIC,wBAAwB,GAAG,MAAM;AACrC,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,qBAAqB,GAAG,GAAG,CAAC,CAAC;AACjC,IAAIC,oBAAoB,GAAG,CAAC;AAC5B,IAAIC,aAAa,GAAG,GAAG;AACvB,IAAIC,cAAc,GAAG,GAAG;AACxB,IAAIC,aAAa,GAAG,CAAC;AACrB;AACA,IAAIC,oBAAoB,GAAG,CAAC;AAC5B,IAAIC,YAAY,GAAG,aAAe,YAAY;EAC1C,SAASA,YAAYA,CAACC,OAAO,EAAEC,OAAO,EAAE;IACpC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;MAAEA,OAAO,GAAG,CAAC,CAAC;IAAE;IACxC,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,aAAa,CAACF,OAAO,CAAC;IAC3B,IAAI,CAACA,OAAO,GAAGG,MAAM,CAACC,MAAM,CAAC;MACzBC,KAAK,EAAET,aAAa;MACpBU,SAAS,EAAEf,iBAAiB;MAC5BgB,YAAY,EAAEf,qBAAqB;MACnCgB,MAAM,EAAER,OAAO,CAACQ,MAAM,KAAK,KAAK;MAChCC,KAAK,EAAEpB,aAAa;MACpBqB,eAAe,EAAEpB,wBAAwB;MACzCqB,WAAW,EAAElB,oBAAoB;MACjCmB,QAAQ,EAAEzB,IAAI;MACd0B,MAAM,EAAE1B,IAAI;MACZ2B,SAAS,EAAE3B;IACf,CAAC,EAAEa,OAAO,EAAE;MACRS,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBC,eAAe,EAAE,IAAI,CAACA;IAC1B,CAAC,CAAC;IACF,IAAI,CAACK,WAAW,GAAG;MACfC,MAAM,EAAE;QACJP,KAAK,EAAE,IAAI,CAACT,OAAO,CAACS,KAAK;QACzBQ,KAAK,EAAE,IAAI,CAACjB,OAAO,CAACW,WAAW;QAC/BO,OAAO,EAAE,OAAO;QAChBC,QAAQ,EAAE;MACd;IACJ,CAAC;IACD,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,CAACC,YAAY,CAAC,CAAC;EACvB;EACAvB,YAAY,CAACwB,SAAS,CAACC,OAAO,GAAG,YAAY;IACzC,IAAI,CAACC,YAAY,CAAC,CAAC;EACvB,CAAC;EACD1B,YAAY,CAACwB,SAAS,CAACG,KAAK,GAAG,YAAY;IACvC,IAAI,CAACC,SAAS,CAACD,KAAK,CAAC,CAAC;IACtB,IAAI,CAACE,IAAI,GAAG,IAAI;EACpB,CAAC;EACD7B,YAAY,CAACwB,SAAS,CAACM,eAAe,GAAG,YAAY;IACjD,IAAIC,UAAU;IACd,IAAIC,oBAAoB;IACxB,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;MACjC,IAAIC,YAAY,GAAG,IAAI,CAACjC,OAAO,CAACkC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAClC,OAAO;MACvE,IAAImC,aAAa,GAAGF,YAAY,CAACG,aAAa,CAACC,WAAW,CAACC,gBAAgB,CAACL,YAAY,CAAC;MACzFH,UAAU,GAAGK,aAAa,CAACzB,KAAK;MAChCqB,oBAAoB,GAAGI,aAAa,CAACxB,eAAe;IACxD;IACA,IAAI,CAACmB,UAAU,GAAGA,UAAU,IAAI,IAAI,CAACA,UAAU,IAAIxC,aAAa;IAChE,IAAI,CAACyC,oBAAoB,GAAGA,oBAAoB,IAAI,IAAI,CAACA,oBAAoB,IAAIxC,wBAAwB;EAC7G,CAAC;EACDQ,YAAY,CAACwB,SAAS,CAACpB,aAAa,GAAG,UAAUF,OAAO,EAAE;IACtD,IAAI,CAAC4B,eAAe,CAAC,CAAC;IACtB,IAAI,CAACnB,KAAK,GAAGT,OAAO,CAACS,KAAK,IAAI,CAAC,IAAI,CAACT,OAAO,IAAI,CAAC,CAAC,EAAES,KAAK,IAAI,IAAI,CAACoB,UAAU;IAC3E,IAAI,CAACnB,eAAe,GAAGV,OAAO,CAACU,eAAe,IAAI,CAAC,IAAI,CAACV,OAAO,IAAI,CAAC,CAAC,EAAEU,eAAe,IAAI,IAAI,CAACoB,oBAAoB;EACvH,CAAC;EACD3B,MAAM,CAACmC,cAAc,CAACxC,YAAY,CAACwB,SAAS,EAAE,WAAW,EAAE;IACvDiB,GAAG,EAAE,SAAAA,CAAA,EAAY;MACb,OAAOC,OAAO,CAAC,IAAI,CAACC,MAAM,CAAC;IAC/B,CAAC;IACDC,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE;EAClB,CAAC,CAAC;EACFxC,MAAM,CAACmC,cAAc,CAACxC,YAAY,CAACwB,SAAS,EAAE,UAAU,EAAE;IACtDiB,GAAG,EAAE,SAAAA,CAAA,EAAY;MACb,IAAIK,EAAE;MACN,OAAO,CAACA,EAAE,GAAG,IAAI,CAACjB,IAAI,MAAM,IAAI,IAAIiB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,QAAQ,CAACzD,cAAc,CAAC;IAC5F,CAAC;IACD0D,GAAG,EAAE,SAAAA,CAAUC,KAAK,EAAE;MAClB,IAAI,CAACtB,KAAK,CAAC,CAAC;MACZ,IAAI,CAACE,IAAI,GAAGjD,SAAS,CAACsE,KAAK,CAACD,KAAK,EAAE,IAAI,CAAChC,WAAW,CAAC;MACpD,IAAI,CAACW,SAAS,CAACuB,MAAM,CAAC,IAAI,CAACtB,IAAI,CAAC;IACpC,CAAC;IACDe,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE;EAClB,CAAC,CAAC;EACF7C,YAAY,CAACwB,SAAS,CAAC4B,SAAS,GAAG,UAAUC,IAAI,EAAEC,IAAI,EAAE;IACrD,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;MAAEA,IAAI,GAAG,EAAE;IAAE;IAClC,IAAI,CAACD,IAAI,EAAE;MACP,IAAI,CAAC1B,KAAK,CAAC,CAAC;MACZ;IACJ;IACA,IAAImB,EAAE,GAAG,IAAI,CAACQ,IAAI;MAAEnC,KAAK,GAAG2B,EAAE,CAAC,CAAC,CAAC;MAAES,MAAM,GAAGT,EAAE,CAAC,CAAC,CAAC;IACjD,IAAIU,YAAY,GAAGrC,KAAK,GAAG,IAAI,CAACjB,OAAO,CAACK,KAAK;IAC7C,IAAIkD,aAAa,GAAGF,MAAM,GAAG,IAAI,CAACrD,OAAO,CAACK,KAAK;IAC/C,IAAImD,WAAW,GAAGJ,IAAI,CAAC,CAAC,CAAC,IAAIE,YAAY,GAAGzD,oBAAoB;IAChE,IAAI4D,YAAY,GAAGL,IAAI,CAAC,CAAC,CAAC,IAAIG,aAAa,GAAG1D,oBAAoB;IAClE,IAAI6D,MAAM,GAAGJ,YAAY,GAAGE,WAAW;IACvC,IAAIG,MAAM,GAAGJ,aAAa,GAAGE,YAAY;IACzC,IAAIpD,KAAK,GAAGuD,IAAI,CAACC,GAAG,CAACH,MAAM,EAAEC,MAAM,CAAC;IACpC,IAAIG,GAAG,GAAG,IAAIlF,KAAK,CAACuE,IAAI,EAAE,IAAI3E,QAAQ,CAACS,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAACuE,WAAW,EAAEC,YAAY,CAAC,CAAC,CAAC;IACjFK,GAAG,CAAC5E,SAAS,CAACA,SAAS,CAAC,CAAC,CAACmB,KAAK,CAACA,KAAK,EAAEA,KAAK,CAAC,CAAC;IAC9C,IAAI,CAACoB,KAAK,CAAC,CAAC;IACZ,IAAI,CAACC,SAAS,CAACuB,MAAM,CAACa,GAAG,CAAC;EAC9B,CAAC;EACDhE,YAAY,CAACwB,SAAS,CAAC/C,WAAW,GAAG,UAAUyB,OAAO,EAAE;IACpD,IAAI4C,EAAE;IACN,IAAImB,EAAE,GAAG,IAAI,CAACX,IAAI;MAAEnC,KAAK,GAAG8C,EAAE,CAAC,CAAC,CAAC;MAAEV,MAAM,GAAGU,EAAE,CAAC,CAAC,CAAC;IACjD,IAAIT,YAAY,GAAGrC,KAAK,GAAG,IAAI,CAACjB,OAAO,CAACK,KAAK;IAC7C,IAAIkD,aAAa,GAAGF,MAAM,GAAG,IAAI,CAACrD,OAAO,CAACK,KAAK;IAC/C,IAAI2D,WAAW,GAAG,CAAChE,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACiB,KAAK,KAAKqC,YAAY,GAAGzD,oBAAoB;IAC1H,IAAIoE,YAAY,GAAG,CAACjE,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACqD,MAAM,KAAKE,aAAa,GAAG1D,oBAAoB;IAC7H,IAAI6D,MAAM,GAAGM,WAAW,GAAGV,YAAY;IACvC,IAAIK,MAAM,GAAGM,YAAY,GAAGV,aAAa;IACzC,IAAIlD,KAAK,GAAGuD,IAAI,CAACC,GAAG,CAACH,MAAM,EAAEC,MAAM,CAAC;IACpC,IAAIO,UAAU,GAAG,IAAIjF,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC+E,WAAW,EAAEC,YAAY,CAAC,CAAC;IAC9D,IAAIE,WAAW,GAAG,IAAI1F,KAAK,CAAC;MACxB2F,IAAI,EAAEzF,IAAI,CAAC0F,QAAQ,CAACH,UAAU;IAClC,CAAC,CAAC;IACF,IAAII,YAAY,GAAG,IAAI7F,KAAK,CAAC;MACzBS,SAAS,EAAEA,SAAS,CAAC,CAAC,CAACmB,KAAK,CAACA,KAAK,EAAEA,KAAK;IAC7C,CAAC,CAAC;IACF,IAAIkE,KAAK,GAAG5F,IAAI,CAAC0F,QAAQ,CAACH,UAAU,EAAE;MAClCM,IAAI,EAAE;QACF/D,KAAK,EAAE,IAAI,CAACT,OAAO,CAACU;MACxB;IACJ,CAAC,CAAC;IACFyD,WAAW,CAAClB,MAAM,CAACsB,KAAK,CAAC;IACzBJ,WAAW,CAAClB,MAAM,CAACqB,YAAY,CAAC;IAChC,CAAC1B,EAAE,GAAG0B,YAAY,CAACG,QAAQ,EAAEC,IAAI,CAACC,KAAK,CAAC/B,EAAE,EAAE,IAAI,CAAClB,SAAS,CAAC+C,QAAQ,CAAC;IACpE,OAAOlG,WAAW,CAAC4F,WAAW,EAAEhE,MAAM,CAACC,MAAM,CAAC;MAC1Ca,KAAK,EAAE+C,WAAW;MAClBX,MAAM,EAAEY;IACZ,CAAC,EAAEjE,OAAO,CAAC,CAAC;EAChB,CAAC;EACDF,YAAY,CAACwB,SAAS,CAACsD,MAAM,GAAG,YAAY;IACxC,IAAI,CAACC,OAAO,CAACD,MAAM,CAAC,IAAI,CAAC;EAC7B,CAAC;EACD9E,YAAY,CAACwB,SAAS,CAACwD,UAAU,GAAG,UAAU9E,OAAO,EAAE;IACnD,IAAI,CAACE,aAAa,CAACF,OAAO,CAAC;IAC3BG,MAAM,CAACC,MAAM,CAAC,IAAI,CAACJ,OAAO,EAAEA,OAAO,EAAE;MACjCS,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBC,eAAe,EAAE,IAAI,CAACA;IAC1B,CAAC,CAAC;IACF,IAAI,CAACK,WAAW,CAACC,MAAM,CAACP,KAAK,GAAG,IAAI,CAACT,OAAO,CAACS,KAAK;IAClD,IAAI,CAACM,WAAW,CAACC,MAAM,CAACC,KAAK,GAAG,IAAI,CAACjB,OAAO,CAACW,WAAW;IACxD,IAAI,IAAI,CAACgB,IAAI,EAAE;MACX,IAAI,CAACA,IAAI,CAAC3B,OAAO,CAAC8C,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC9C,OAAO,CAACS,KAAK,CAAC;MACzD,IAAI,CAACkB,IAAI,CAAC3B,OAAO,CAAC8C,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC9C,OAAO,CAACW,WAAW,CAAC;IACnE;IACA,IAAI,CAACoE,UAAU,CAAC/E,OAAO,CAAC8C,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC9C,OAAO,CAACU,eAAe,CAAC;EAC3E,CAAC;EACDZ,YAAY,CAACwB,SAAS,CAACF,WAAW,GAAG,YAAY;IAC7C,IAAI,CAACyD,OAAO,GAAGhG,OAAO,CAACmG,MAAM,CAAC,IAAI,CAACjF,OAAO,EAAE;MAAEkF,IAAI,EAAE;IAAS,CAAC,CAAC;IAC/D,IAAI,CAAClF,OAAO,CAACmF,KAAK,CAACC,WAAW,GAAG,MAAM;IACvC,IAAI9E,KAAK,GAAG,IAAI,CAACL,OAAO,CAACK,KAAK;IAC9B,IAAI,CAACqB,SAAS,GAAG,IAAIjD,KAAK,CAAC;MACvBS,SAAS,EAAEA,SAAS,CAAC,CAAC,CAACmB,KAAK,CAACA,KAAK,EAAEA,KAAK;IAC7C,CAAC,CAAC;IACF;IACA,IAAIY,KAAK,GAAG,IAAI,CAAClB,OAAO,CAACqF,WAAW,IAAI1F,aAAa;IACrD,IAAI2D,MAAM,GAAG,IAAI,CAACtD,OAAO,CAACsF,YAAY,IAAI1F,cAAc;IACxD,IAAI,CAACyD,IAAI,GAAG,CAACnC,KAAK,EAAEoC,MAAM,CAAC;IAC3B,IAAI,CAAC0B,UAAU,GAAGpG,IAAI,CAAC0F,QAAQ,CAAC,IAAIpF,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAACmE,IAAI,CAAC,EAAE;MACzDoB,IAAI,EAAE;QACF/D,KAAK,EAAE,IAAI,CAACT,OAAO,CAACU;MACxB;IACJ,CAAC,CAAC;IACF,IAAI,CAACmE,OAAO,CAACS,IAAI,CAAC,IAAI,CAACP,UAAU,CAAC;IAClC,IAAI,CAACF,OAAO,CAACS,IAAI,CAAC,IAAI,CAAC5D,SAAS,CAAC;EACrC,CAAC;EACD5B,YAAY,CAACwB,SAAS,CAACD,YAAY,GAAG,YAAY;IAC9C,IAAI,CAACkE,aAAa,GAAG,IAAI,CAACA,aAAa,CAACC,IAAI,CAAC,IAAI,CAAC;IAClD,IAAI,CAACC,aAAa,GAAG,IAAI,CAACA,aAAa,CAACD,IAAI,CAAC,IAAI,CAAC;IAClD,IAAI,CAACE,WAAW,GAAG,IAAI,CAACA,WAAW,CAACF,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACG,WAAW,GAAG,IAAI,CAACA,WAAW,CAACH,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACzF,OAAO,CAAC6F,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACL,aAAa,CAAC;IAChE,IAAI,CAACxF,OAAO,CAAC6F,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACF,WAAW,CAAC;IAC5D,IAAI,CAAC3F,OAAO,CAAC6F,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACD,WAAW,CAAC;EAChE,CAAC;EACD7F,YAAY,CAACwB,SAAS,CAACE,YAAY,GAAG,YAAY;IAC9C,IAAI,CAACzB,OAAO,CAAC8F,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAACN,aAAa,CAAC;IACnE,IAAI,CAACO,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAAC/F,OAAO,CAAC8F,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACH,WAAW,CAAC;IAC/D,IAAI,CAAC3F,OAAO,CAAC8F,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACF,WAAW,CAAC;EACnE,CAAC;EACD7F,YAAY,CAACwB,SAAS,CAACyE,iBAAiB,GAAG,YAAY;IACnD,IAAI,CAAChG,OAAO,CAAC6F,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACH,aAAa,CAAC;EACpE,CAAC;EACD3F,YAAY,CAACwB,SAAS,CAACwE,iBAAiB,GAAG,YAAY;IACnD,IAAI,CAAC/F,OAAO,CAAC8F,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAACJ,aAAa,CAAC;EACvE,CAAC;EACD3F,YAAY,CAACwB,SAAS,CAAC0E,UAAU,GAAG,UAAUC,CAAC,EAAE;IAC7C,IAAIC,MAAM,GAAGpH,aAAa,CAAC,IAAI,CAACiB,OAAO,CAAC;IACxC,IAAIoG,KAAK,GAAGF,CAAC,CAACE,KAAK;IACnB,IAAIC,KAAK,GAAGH,CAAC,CAACG,KAAK;IACnB,IAAI/F,KAAK,GAAG,CAAC,GAAG,IAAI,CAACL,OAAO,CAACK,KAAK;IAClC,OAAO,IAAIrB,KAAK,CAACmH,KAAK,GAAGD,MAAM,CAACG,IAAI,EAAED,KAAK,GAAGF,MAAM,CAACI,GAAG,CAAC,CAACjG,KAAK,CAACA,KAAK,EAAEA,KAAK,CAAC;EACjF,CAAC;EACDP,YAAY,CAACwB,SAAS,CAACqE,WAAW,GAAG,UAAUM,CAAC,EAAE;IAC9C;IACA;IACA;IACA;IACA;IACAA,CAAC,CAACM,cAAc,CAAC,CAAC;EACtB,CAAC;EACDzG,YAAY,CAACwB,SAAS,CAACiE,aAAa,GAAG,UAAUU,CAAC,EAAE;IAChD,IAAI,IAAI,CAACjG,OAAO,CAACwG,QAAQ,IAAI,CAACP,CAAC,CAACQ,SAAS,IAAI,CAACC,YAAY,CAACT,CAAC,CAAC,EAAE;MAC3D;IACJ;IACA,IAAI,CAACH,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAAC,IAAI,CAACpE,IAAI,EAAE;MACZ,IAAI,CAACA,IAAI,GAAG,IAAIjD,SAAS,CAAC,IAAI,CAACqC,WAAW,CAAC;MAC3C,IAAI,CAACW,SAAS,CAACuB,MAAM,CAAC,IAAI,CAACtB,IAAI,CAAC;IACpC;IACA,IAAI,CAAC3B,OAAO,CAACa,MAAM,CAAC,CAAC;IACrB,IAAI,CAACd,OAAO,CAAC4G,iBAAiB,CAACV,CAAC,CAACW,SAAS,CAAC;IAC3C,IAAIC,KAAK,GAAG,IAAI,CAACb,UAAU,CAACC,CAAC,CAAC;IAC9B,IAAI,CAACxD,MAAM,GAAG,CAACoE,KAAK,CAAC;IACrB,IAAI,CAAClF,IAAI,CAACmF,MAAM,CAACD,KAAK,CAAC;EAC3B,CAAC;EACD/G,YAAY,CAACwB,SAAS,CAACmE,aAAa,GAAG,UAAUQ,CAAC,EAAE;IAChD,IAAI,CAAC,IAAI,CAACxD,MAAM,IAAI,CAAC,IAAI,CAACd,IAAI,IAAI,CAACsE,CAAC,CAACQ,SAAS,EAAE;MAC5C;IACJ;IACA,IAAIM,GAAG,GAAI,IAAIC,IAAI,CAAC,CAAC,CAAEC,OAAO,CAAC,CAAC;IAChC,IAAIC,OAAO,GAAGH,GAAG,GAAG,IAAI,CAAC9G,YAAY;IACrC,IAAIkH,YAAY,GAAG,IAAI,GAAGpI,UAAU,CAAC,IAAI,CAACiB,OAAO,CAACO,YAAY,EAAE,CAAC,EAAE,KAAK,CAAC;IACzE,IAAI2G,OAAO,GAAGC,YAAY,EAAE;MACxB;IACJ,CAAC,MACI;MACD,IAAI,CAAClH,YAAY,GAAG8G,GAAG;IAC3B;IACA,IAAIF,KAAK,GAAG,IAAI,CAACb,UAAU,CAACC,CAAC,CAAC;IAC9B,IAAImB,SAAS,GAAG,IAAI,CAAC3E,MAAM,CAAC,IAAI,CAACA,MAAM,CAAC4E,MAAM,GAAG,CAAC,CAAC;IACnD,IAAIC,QAAQ,GAAG,CAAC,GAAGvI,UAAU,CAAC,IAAI,CAACiB,OAAO,CAACM,SAAS,EAAE,IAAI,EAAE,GAAG,CAAC;IAChE,IAAIuG,KAAK,CAACU,UAAU,CAACH,SAAS,CAAC,GAAGE,QAAQ,EAAE;MACxC;IACJ;IACA,IAAI,CAAC7E,MAAM,CAACiC,IAAI,CAACmC,KAAK,CAAC;IACvB,IAAI,CAAClF,IAAI,CAAC6F,MAAM,CAACX,KAAK,CAAC;EAC3B,CAAC;EACD/G,YAAY,CAACwB,SAAS,CAACoE,WAAW,GAAG,UAAUO,CAAC,EAAE;IAC9C,IAAI,CAACA,CAAC,CAACQ,SAAS,IAAI,CAAC,IAAI,CAAC9E,IAAI,IAAI,CAAC,IAAI,CAACc,MAAM,IAAI,IAAI,CAACzC,OAAO,CAACwG,QAAQ,EAAE;MACrE;IACJ;IACA,IAAI,CAACV,iBAAiB,CAAC,CAAC;IACxB,IAAI,IAAI,CAAC9F,OAAO,CAACQ,MAAM,EAAE;MACrB,IAAIiH,QAAQ,GAAG9I,IAAI,CAAC+I,eAAe,CAAC,IAAI,CAACjF,MAAM,CAAC;MAChD,IAAI,CAACd,IAAI,CAACgG,KAAK,CAACC,MAAM,CAAC,IAAI,CAACjG,IAAI,CAACgG,KAAK,CAACN,MAAM,GAAG,CAAC,EAAE,CAAC,EAAEI,QAAQ,CAAC;IACnE;IACA,IAAI,CAAChF,MAAM,GAAG,IAAI;IAClB,IAAI,CAACzC,OAAO,CAACc,SAAS,CAAC,CAAC;IACxB,IAAI,CAACd,OAAO,CAACY,QAAQ,CAAC,IAAI,CAACiH,QAAQ,CAAC;EACxC,CAAC;EACD,OAAO/H,YAAY;AACvB,CAAC,CAAC,CAAE;AACJ,SAASA,YAAY;AACrB,SAAS4G,YAAYA,CAACT,CAAC,EAAE;EACrB,OAAO,OAAQA,CAAC,CAAC6B,MAAO,KAAK,QAAQ,IAAI7B,CAAC,CAAC6B,MAAM,KAAK,CAAC;AAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}