{"ast": null, "code": "import { addMonths } from './add-months';\nimport { createDate } from './create-date';\nimport { lastDayOfMonth } from './last-day-of-month';\n/**\n * @hidden\n */\nexport var setYear = function (value, year) {\n  var month = value.getMonth();\n  var candidate = createDate(year, month, value.getDate(), value.getHours(), value.getMinutes(), value.getSeconds(), value.getMilliseconds());\n  return candidate.getMonth() === month ? candidate : lastDayOfMonth(addMonths(candidate, -1));\n};", "map": {"version": 3, "names": ["addMonths", "createDate", "lastDayOfMonth", "setYear", "value", "year", "month", "getMonth", "candidate", "getDate", "getHours", "getMinutes", "getSeconds", "getMilliseconds"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/set-year.js"], "sourcesContent": ["import { addMonths } from './add-months';\nimport { createDate } from './create-date';\nimport { lastDayOfMonth } from './last-day-of-month';\n/**\n * @hidden\n */\nexport var setYear = function (value, year) {\n    var month = value.getMonth();\n    var candidate = createDate(year, month, value.getDate(), value.getHours(), value.getMinutes(), value.getSeconds(), value.getMilliseconds());\n    return candidate.getMonth() === month ? candidate : lastDayOfMonth(addMonths(candidate, -1));\n};\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,cAAc;AACxC,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,cAAc,QAAQ,qBAAqB;AACpD;AACA;AACA;AACA,OAAO,IAAIC,OAAO,GAAG,SAAAA,CAAUC,KAAK,EAAEC,IAAI,EAAE;EACxC,IAAIC,KAAK,GAAGF,KAAK,CAACG,QAAQ,CAAC,CAAC;EAC5B,IAAIC,SAAS,GAAGP,UAAU,CAACI,IAAI,EAAEC,KAAK,EAAEF,KAAK,CAACK,OAAO,CAAC,CAAC,EAAEL,KAAK,CAACM,QAAQ,CAAC,CAAC,EAAEN,KAAK,CAACO,UAAU,CAAC,CAAC,EAAEP,KAAK,CAACQ,UAAU,CAAC,CAAC,EAAER,KAAK,CAACS,eAAe,CAAC,CAAC,CAAC;EAC3I,OAAOL,SAAS,CAACD,QAAQ,CAAC,CAAC,KAAKD,KAAK,GAAGE,SAAS,GAAGN,cAAc,CAACF,SAAS,CAACQ,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;AAChG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}