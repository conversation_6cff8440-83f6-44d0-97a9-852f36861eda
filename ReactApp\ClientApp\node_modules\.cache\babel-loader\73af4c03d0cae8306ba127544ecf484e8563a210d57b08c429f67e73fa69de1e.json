{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as m from \"react\";\nimport i from \"prop-types\";\nimport { Keys as h, classNames as f, dispatchEvent as v } from \"@progress/kendo-react-common\";\nimport { EMPTY_ID as r, getItemById as a, getDirectParentId as b, getRootParentId as g, isIdEmptyOrZeroLevel as l, ZERO_LEVEL_ZERO_ITEM_ID as c } from \"../utils/itemsIdsUtils.mjs\";\nimport { prepareInputItemsForInternalWork as R } from \"../utils/prepareInputItemsForInternalWork.mjs\";\nimport { getNewItemIdUponKeyboardNavigation as I } from \"../utils/getNewItemIdUponKeyboardNavigation.mjs\";\nimport { getHoverOpenDelay as D, getHoverCloseDelay as H } from \"../utils/hoverDelay.mjs\";\nimport { MenuItemInternalsList as O } from \"./MenuItemInternal.mjs\";\nimport { DirectionHolder as C } from \"../utils/DirectionHolder.mjs\";\nimport { MouseOverHandler as y } from \"../utils/MouseOverHandler.mjs\";\nconst p = {\n    focusedItemId: r,\n    hoveredItemId: r,\n    tabbableItemId: c\n  },\n  n = class n extends m.Component {\n    constructor(t) {\n      super(t), this.menuWrapperEl = null, this.directionHolder = new C(), this.inputItems = [], this.items = [], this.reset = () => {\n        this.clearItemHoverAndLeaveRequestsIfApplicable(), this.setState(p);\n      }, this.onKeyDown = e => {\n        if (this.state.focusedItemId !== r) {\n          const s = a(this.state.focusedItemId, this.items);\n          let o = I(this.items, s.id, e.keyCode, e.key, this.props.vertical, this.directionHolder.getIsDirectionRightToLeft());\n          const d = a(o, this.items);\n          d && d.separator && (o = I(this.items, o, e.keyCode, e.key, this.props.vertical, this.directionHolder.getIsDirectionRightToLeft())), s.id !== o && (e.preventDefault(), this.setFocusedItemId(o)), (e.keyCode === h.enter || e.keyCode === h.space) && !s.disabled && (this.mouseOverHandler.handleItemSelectedViaKeyboard(), this.dispatchSelectEventIfWired(e, s.id), !e.isDefaultPrevented() && s.items.length === 0 && s.url && window.location.assign(s.url));\n        }\n        e.keyCode === h.esc && this.props.onClose && this.props.onClose.call(void 0, e);\n      }, this.onItemMouseOver = e => {\n        this.mouseOverHandler.IsMouseOverEnabled && (this.clearItemHoverAndLeaveRequestsIfApplicable(), this.itemHoverRequest = window.setTimeout(() => {\n          this.setHoveredItemId(e), this.itemHoverRequest = null;\n        }, D(this.props)));\n      }, this.onItemMouseLeave = e => {\n        this.mouseOverHandler.IsMouseOverEnabled && this.isItemWithDefaultClose(e) && (this.clearItemHoverAndLeaveRequestsIfApplicable(), this.itemLeaveRequest = window.setTimeout(() => {\n          this.setHoveredItemId(r), this.itemLeaveRequest = null;\n        }, H(this.props)));\n      }, this.onItemMouseDown = () => {\n        this.mouseOverHandler.handleItemMouseDown();\n      }, this.onItemFocus = e => {\n        this.setFocusedItemId(e), this.mouseOverHandler.handleItemFocus();\n      }, this.onItemClick = (e, s) => {\n        const o = a(s, this.items);\n        o.disabled || (this.setFocusedItemId(s), this.mouseOverHandler.handleItemClick(s, this.isItemWithDefaultClose(s)), this.dispatchSelectEventIfWired(e, s), !e.isDefaultPrevented() && o.url && window.location.assign(o.url));\n      }, this.onItemBlur = (e, s) => {\n        if (this.isItemWithDefaultClose(e) && this.setFocusedItemId(r), s.relatedTarget && s.relatedTarget.nodeName === \"LI\") {\n          const o = s.relatedTarget.getAttribute(\"id\");\n          if (o && o.includes(this.menuItemId)) return;\n        }\n        this.props.onClose && this.props.onClose.call(void 0, s);\n      }, this.getInputItem = e => a(e, this.inputItems), this.mouseOverHandler = new y(this.props.openOnClick, this.reset, this.onItemMouseOver), this.state = Object.assign({}, p, {\n        isFirstRender: !0\n      });\n    }\n    get menuItemId() {\n      return this.props.id;\n    }\n    get element() {\n      return this.menuWrapperEl;\n    }\n    get animate() {\n      return this.props.animate !== void 0 ? this.props.animate : n.defaultProps.animate;\n    }\n    /**\n     * @hidden\n     */\n    render() {\n      this.prepareItems(), this.state.isFirstRender || this.directionHolder.setIsDirectionRightToLeft(this.checkIsDirectionRightToLeft());\n      const t = this.state.hoveredItemId ? this.state.hoveredItemId : this.state.focusedItemId ? b(this.state.focusedItemId) : r;\n      return /* @__PURE__ */m.createElement(\"div\", {\n        id: this.props.id,\n        onKeyDown: this.onKeyDown,\n        style: this.props.style,\n        className: this.directionHolder.getIsDirectionRightToLeft() ? \"k-rtl\" : void 0,\n        ref: e => {\n          this.menuWrapperEl = e;\n        }\n      }, /* @__PURE__ */m.createElement(O, {\n        className: this.getMenuClassName(),\n        \"aria-orientation\": this.props.vertical ? \"vertical\" : void 0,\n        items: this.items,\n        animate: this.animate,\n        isMenuVertical: this.props.vertical,\n        isDirectionRightToLeft: this.directionHolder.getIsDirectionRightToLeft(),\n        focusedItemId: this.state.focusedItemId,\n        lastItemIdToBeOpened: t,\n        tabbableItemId: this.state.tabbableItemId,\n        itemRender: this.props.itemRender,\n        linkRender: this.props.linkRender,\n        menuGuid: this.menuItemId,\n        onMouseLeave: this.onItemMouseLeave,\n        onMouseOver: this.onItemMouseOver,\n        onMouseDown: this.onItemMouseDown,\n        onFocus: this.onItemFocus,\n        onClick: this.onItemClick,\n        onBlur: this.onItemBlur,\n        onOriginalItemNeeded: this.getInputItem,\n        role: this.props.role\n      }));\n    }\n    /**\n     * @hidden\n     */\n    componentDidMount() {\n      this.setState({\n        isFirstRender: !1\n      });\n    }\n    /**\n     * @hidden\n     */\n    componentDidUpdate(t) {\n      (!!t.vertical != !!this.props.vertical || this.directionHolder.hasDirectionChanged()) && this.reset(), this.mouseOverHandler.OpenOnClick = this.props.openOnClick;\n    }\n    /**\n     * @hidden\n     */\n    componentWillUnmount() {\n      this.clearItemHoverAndLeaveRequestsIfApplicable();\n    }\n    setFocusedItemId(t) {\n      this.setState(e => {\n        const s = t === r ? e.tabbableItemId : g(t);\n        return {\n          hoveredItemId: t === r || l(e.hoveredItemId) && l(t) ? e.hoveredItemId : r,\n          focusedItemId: t,\n          tabbableItemId: s\n        };\n      });\n    }\n    setHoveredItemId(t) {\n      this.setState(e => l(t) && l(e.focusedItemId) ? {\n        hoveredItemId: t,\n        focusedItemId: e.focusedItemId,\n        tabbableItemId: e.tabbableItemId\n      } : {\n        hoveredItemId: t,\n        focusedItemId: r,\n        tabbableItemId: c\n      });\n    }\n    getMenuClassName() {\n      return f(\"k-reset\", \"k-header\", \"k-menu\", {\n        \"k-menu-horizontal\": !this.props.vertical\n      }, {\n        \"k-menu-vertical\": this.props.vertical\n      }, this.props.className);\n    }\n    clearItemHoverAndLeaveRequestsIfApplicable() {\n      this.itemHoverRequest && (clearTimeout(this.itemHoverRequest), this.itemHoverRequest = null), this.itemLeaveRequest && (clearTimeout(this.itemLeaveRequest), this.itemLeaveRequest = null);\n    }\n    isItemWithDefaultClose(t) {\n      return !this.props.customCloseItemIds || this.props.customCloseItemIds.indexOf(t) === -1;\n    }\n    checkIsDirectionRightToLeft() {\n      return !!(this.props.dir !== void 0 ? this.props.dir === \"rtl\" : this.menuWrapperEl && getComputedStyle(this.menuWrapperEl).direction === \"rtl\");\n    }\n    prepareItems() {\n      const {\n        items: t,\n        inputItems: e\n      } = R(this.props.items, this.props.children);\n      this.items = t, this.inputItems = e;\n    }\n    dispatchSelectEventIfWired(t, e) {\n      v(this.props.onSelect, t, this, {\n        item: this.getInputItem(e),\n        itemId: e\n      });\n    }\n  };\nn.propTypes = {\n  vertical: i.bool,\n  items: i.arrayOf(i.object),\n  style: i.object,\n  animate: i.oneOfType([i.bool, i.shape({\n    openDuration: i.number,\n    closeDuration: i.number\n  })]),\n  dir: i.string,\n  hoverOpenDelay: i.number,\n  hoverCloseDelay: i.number,\n  openOnClick: i.bool,\n  itemRender: i.any,\n  linkRender: i.any,\n  customCloseItemIds: i.arrayOf(i.string),\n  onSelect: i.func,\n  role: i.string\n}, n.defaultProps = {\n  vertical: !1,\n  animate: !0\n};\nlet u = n;\nexport { u as Menu };", "map": {"version": 3, "names": ["m", "i", "Keys", "h", "classNames", "f", "dispatchEvent", "v", "EMPTY_ID", "r", "getItemById", "a", "getDirectParentId", "b", "getRootParentId", "g", "isIdEmptyOrZeroLevel", "l", "ZERO_LEVEL_ZERO_ITEM_ID", "c", "prepareInputItemsForInternalWork", "R", "getNewItemIdUponKeyboardNavigation", "I", "getHoverOpenDelay", "D", "getHoverCloseDelay", "H", "MenuItemInternalsList", "O", "DirectionHolder", "C", "MouseOverHandler", "y", "p", "focusedItemId", "hoveredItemId", "tabbableItemId", "n", "Component", "constructor", "t", "menuWrapperEl", "directionHolder", "inputItems", "items", "reset", "clearItemHoverAndLeaveRequestsIfApplicable", "setState", "onKeyDown", "e", "state", "s", "o", "id", "keyCode", "key", "props", "vertical", "getIsDirectionRightToLeft", "d", "separator", "preventDefault", "setFocusedItemId", "enter", "space", "disabled", "mouseOverHandler", "handleItemSelectedViaKeyboard", "dispatchSelectEventIfWired", "isDefaultPrevented", "length", "url", "window", "location", "assign", "esc", "onClose", "call", "onItemMouseOver", "IsMouseOverEnabled", "itemHoverRequest", "setTimeout", "setHoveredItemId", "onItemMouseLeave", "isItemWithDefaultClose", "itemLeaveRequest", "onItemMouseDown", "handleItemMouseDown", "onItemFocus", "handleItemFocus", "onItemClick", "handleItemClick", "onItemBlur", "relatedTarget", "nodeName", "getAttribute", "includes", "menuItemId", "getInputItem", "openOnClick", "Object", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "element", "animate", "defaultProps", "render", "prepareItems", "setIsDirectionRightToLeft", "checkIsDirectionRightToLeft", "createElement", "style", "className", "ref", "getMenuClassName", "isMenuVertical", "isDirectionRightToLeft", "lastItemIdToBeOpened", "itemRender", "linkRender", "menuGuid", "onMouseLeave", "onMouseOver", "onMouseDown", "onFocus", "onClick", "onBlur", "onOriginalItemNeeded", "role", "componentDidMount", "componentDidUpdate", "hasDirectionChanged", "OpenOnClick", "componentWillUnmount", "clearTimeout", "customCloseItemIds", "indexOf", "dir", "getComputedStyle", "direction", "children", "onSelect", "item", "itemId", "propTypes", "bool", "arrayOf", "object", "oneOfType", "shape", "openDuration", "number", "closeDuration", "string", "hoverOpenDelay", "hoverCloseDelay", "any", "func", "u", "<PERSON><PERSON>"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/menu/components/Menu.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as m from \"react\";\nimport i from \"prop-types\";\nimport { Keys as h, classNames as f, dispatchEvent as v } from \"@progress/kendo-react-common\";\nimport { EMPTY_ID as r, getItemById as a, getDirectParentId as b, getRootParentId as g, isIdEmptyOrZeroLevel as l, ZERO_LEVEL_ZERO_ITEM_ID as c } from \"../utils/itemsIdsUtils.mjs\";\nimport { prepareInputItemsForInternalWork as R } from \"../utils/prepareInputItemsForInternalWork.mjs\";\nimport { getNewItemIdUponKeyboardNavigation as I } from \"../utils/getNewItemIdUponKeyboardNavigation.mjs\";\nimport { getHoverOpenDelay as D, getHoverCloseDelay as H } from \"../utils/hoverDelay.mjs\";\nimport { MenuItemInternalsList as O } from \"./MenuItemInternal.mjs\";\nimport { DirectionHolder as C } from \"../utils/DirectionHolder.mjs\";\nimport { MouseOverHandler as y } from \"../utils/MouseOverHandler.mjs\";\nconst p = {\n  focusedItemId: r,\n  hoveredItemId: r,\n  tabbableItemId: c\n}, n = class n extends m.Component {\n  constructor(t) {\n    super(t), this.menuWrapperEl = null, this.directionHolder = new C(), this.inputItems = [], this.items = [], this.reset = () => {\n      this.clearItemHoverAndLeaveRequestsIfApplicable(), this.setState(p);\n    }, this.onKeyDown = (e) => {\n      if (this.state.focusedItemId !== r) {\n        const s = a(this.state.focusedItemId, this.items);\n        let o = I(\n          this.items,\n          s.id,\n          e.keyCode,\n          e.key,\n          this.props.vertical,\n          this.directionHolder.getIsDirectionRightToLeft()\n        );\n        const d = a(o, this.items);\n        d && d.separator && (o = I(\n          this.items,\n          o,\n          e.keyCode,\n          e.key,\n          this.props.vertical,\n          this.directionHolder.getIsDirectionRightToLeft()\n        )), s.id !== o && (e.preventDefault(), this.setFocusedItemId(o)), (e.keyCode === h.enter || e.keyCode === h.space) && !s.disabled && (this.mouseOverHandler.handleItemSelectedViaKeyboard(), this.dispatchSelectEventIfWired(e, s.id), !e.isDefaultPrevented() && s.items.length === 0 && s.url && window.location.assign(s.url));\n      }\n      e.keyCode === h.esc && this.props.onClose && this.props.onClose.call(void 0, e);\n    }, this.onItemMouseOver = (e) => {\n      this.mouseOverHandler.IsMouseOverEnabled && (this.clearItemHoverAndLeaveRequestsIfApplicable(), this.itemHoverRequest = window.setTimeout(() => {\n        this.setHoveredItemId(e), this.itemHoverRequest = null;\n      }, D(this.props)));\n    }, this.onItemMouseLeave = (e) => {\n      this.mouseOverHandler.IsMouseOverEnabled && this.isItemWithDefaultClose(e) && (this.clearItemHoverAndLeaveRequestsIfApplicable(), this.itemLeaveRequest = window.setTimeout(() => {\n        this.setHoveredItemId(r), this.itemLeaveRequest = null;\n      }, H(this.props)));\n    }, this.onItemMouseDown = () => {\n      this.mouseOverHandler.handleItemMouseDown();\n    }, this.onItemFocus = (e) => {\n      this.setFocusedItemId(e), this.mouseOverHandler.handleItemFocus();\n    }, this.onItemClick = (e, s) => {\n      const o = a(s, this.items);\n      o.disabled || (this.setFocusedItemId(s), this.mouseOverHandler.handleItemClick(s, this.isItemWithDefaultClose(s)), this.dispatchSelectEventIfWired(e, s), !e.isDefaultPrevented() && o.url && window.location.assign(o.url));\n    }, this.onItemBlur = (e, s) => {\n      if (this.isItemWithDefaultClose(e) && this.setFocusedItemId(r), s.relatedTarget && s.relatedTarget.nodeName === \"LI\") {\n        const o = s.relatedTarget.getAttribute(\"id\");\n        if (o && o.includes(this.menuItemId))\n          return;\n      }\n      this.props.onClose && this.props.onClose.call(void 0, s);\n    }, this.getInputItem = (e) => a(e, this.inputItems), this.mouseOverHandler = new y(this.props.openOnClick, this.reset, this.onItemMouseOver), this.state = Object.assign({}, p, { isFirstRender: !0 });\n  }\n  get menuItemId() {\n    return this.props.id;\n  }\n  get element() {\n    return this.menuWrapperEl;\n  }\n  get animate() {\n    return this.props.animate !== void 0 ? this.props.animate : n.defaultProps.animate;\n  }\n  /**\n   * @hidden\n   */\n  render() {\n    this.prepareItems(), this.state.isFirstRender || this.directionHolder.setIsDirectionRightToLeft(this.checkIsDirectionRightToLeft());\n    const t = this.state.hoveredItemId ? this.state.hoveredItemId : this.state.focusedItemId ? b(this.state.focusedItemId) : r;\n    return /* @__PURE__ */ m.createElement(\n      \"div\",\n      {\n        id: this.props.id,\n        onKeyDown: this.onKeyDown,\n        style: this.props.style,\n        className: this.directionHolder.getIsDirectionRightToLeft() ? \"k-rtl\" : void 0,\n        ref: (e) => {\n          this.menuWrapperEl = e;\n        }\n      },\n      /* @__PURE__ */ m.createElement(\n        O,\n        {\n          className: this.getMenuClassName(),\n          \"aria-orientation\": this.props.vertical ? \"vertical\" : void 0,\n          items: this.items,\n          animate: this.animate,\n          isMenuVertical: this.props.vertical,\n          isDirectionRightToLeft: this.directionHolder.getIsDirectionRightToLeft(),\n          focusedItemId: this.state.focusedItemId,\n          lastItemIdToBeOpened: t,\n          tabbableItemId: this.state.tabbableItemId,\n          itemRender: this.props.itemRender,\n          linkRender: this.props.linkRender,\n          menuGuid: this.menuItemId,\n          onMouseLeave: this.onItemMouseLeave,\n          onMouseOver: this.onItemMouseOver,\n          onMouseDown: this.onItemMouseDown,\n          onFocus: this.onItemFocus,\n          onClick: this.onItemClick,\n          onBlur: this.onItemBlur,\n          onOriginalItemNeeded: this.getInputItem,\n          role: this.props.role\n        }\n      )\n    );\n  }\n  /**\n   * @hidden\n   */\n  componentDidMount() {\n    this.setState({ isFirstRender: !1 });\n  }\n  /**\n   * @hidden\n   */\n  componentDidUpdate(t) {\n    (!!t.vertical != !!this.props.vertical || this.directionHolder.hasDirectionChanged()) && this.reset(), this.mouseOverHandler.OpenOnClick = this.props.openOnClick;\n  }\n  /**\n   * @hidden\n   */\n  componentWillUnmount() {\n    this.clearItemHoverAndLeaveRequestsIfApplicable();\n  }\n  setFocusedItemId(t) {\n    this.setState((e) => {\n      const s = t === r ? e.tabbableItemId : g(t);\n      return { hoveredItemId: t === r || l(e.hoveredItemId) && l(t) ? e.hoveredItemId : r, focusedItemId: t, tabbableItemId: s };\n    });\n  }\n  setHoveredItemId(t) {\n    this.setState((e) => l(t) && l(e.focusedItemId) ? {\n      hoveredItemId: t,\n      focusedItemId: e.focusedItemId,\n      tabbableItemId: e.tabbableItemId\n    } : { hoveredItemId: t, focusedItemId: r, tabbableItemId: c });\n  }\n  getMenuClassName() {\n    return f(\n      \"k-reset\",\n      \"k-header\",\n      \"k-menu\",\n      { \"k-menu-horizontal\": !this.props.vertical },\n      { \"k-menu-vertical\": this.props.vertical },\n      this.props.className\n    );\n  }\n  clearItemHoverAndLeaveRequestsIfApplicable() {\n    this.itemHoverRequest && (clearTimeout(this.itemHoverRequest), this.itemHoverRequest = null), this.itemLeaveRequest && (clearTimeout(this.itemLeaveRequest), this.itemLeaveRequest = null);\n  }\n  isItemWithDefaultClose(t) {\n    return !this.props.customCloseItemIds || this.props.customCloseItemIds.indexOf(t) === -1;\n  }\n  checkIsDirectionRightToLeft() {\n    return !!(this.props.dir !== void 0 ? this.props.dir === \"rtl\" : this.menuWrapperEl && getComputedStyle(this.menuWrapperEl).direction === \"rtl\");\n  }\n  prepareItems() {\n    const { items: t, inputItems: e } = R(this.props.items, this.props.children);\n    this.items = t, this.inputItems = e;\n  }\n  dispatchSelectEventIfWired(t, e) {\n    v(this.props.onSelect, t, this, { item: this.getInputItem(e), itemId: e });\n  }\n};\nn.propTypes = {\n  vertical: i.bool,\n  items: i.arrayOf(i.object),\n  style: i.object,\n  animate: i.oneOfType([\n    i.bool,\n    i.shape({\n      openDuration: i.number,\n      closeDuration: i.number\n    })\n  ]),\n  dir: i.string,\n  hoverOpenDelay: i.number,\n  hoverCloseDelay: i.number,\n  openOnClick: i.bool,\n  itemRender: i.any,\n  linkRender: i.any,\n  customCloseItemIds: i.arrayOf(i.string),\n  onSelect: i.func,\n  role: i.string\n}, n.defaultProps = { vertical: !1, animate: !0 };\nlet u = n;\nexport {\n  u as Menu\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,IAAI,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,EAAEC,aAAa,IAAIC,CAAC,QAAQ,8BAA8B;AAC7F,SAASC,QAAQ,IAAIC,CAAC,EAAEC,WAAW,IAAIC,CAAC,EAAEC,iBAAiB,IAAIC,CAAC,EAAEC,eAAe,IAAIC,CAAC,EAAEC,oBAAoB,IAAIC,CAAC,EAAEC,uBAAuB,IAAIC,CAAC,QAAQ,4BAA4B;AACnL,SAASC,gCAAgC,IAAIC,CAAC,QAAQ,+CAA+C;AACrG,SAASC,kCAAkC,IAAIC,CAAC,QAAQ,iDAAiD;AACzG,SAASC,iBAAiB,IAAIC,CAAC,EAAEC,kBAAkB,IAAIC,CAAC,QAAQ,yBAAyB;AACzF,SAASC,qBAAqB,IAAIC,CAAC,QAAQ,wBAAwB;AACnE,SAASC,eAAe,IAAIC,CAAC,QAAQ,8BAA8B;AACnE,SAASC,gBAAgB,IAAIC,CAAC,QAAQ,+BAA+B;AACrE,MAAMC,CAAC,GAAG;IACRC,aAAa,EAAE1B,CAAC;IAChB2B,aAAa,EAAE3B,CAAC;IAChB4B,cAAc,EAAElB;EAClB,CAAC;EAAEmB,CAAC,GAAG,MAAMA,CAAC,SAAStC,CAAC,CAACuC,SAAS,CAAC;IACjCC,WAAWA,CAACC,CAAC,EAAE;MACb,KAAK,CAACA,CAAC,CAAC,EAAE,IAAI,CAACC,aAAa,GAAG,IAAI,EAAE,IAAI,CAACC,eAAe,GAAG,IAAIZ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACa,UAAU,GAAG,EAAE,EAAE,IAAI,CAACC,KAAK,GAAG,EAAE,EAAE,IAAI,CAACC,KAAK,GAAG,MAAM;QAC7H,IAAI,CAACC,0CAA0C,CAAC,CAAC,EAAE,IAAI,CAACC,QAAQ,CAACd,CAAC,CAAC;MACrE,CAAC,EAAE,IAAI,CAACe,SAAS,GAAIC,CAAC,IAAK;QACzB,IAAI,IAAI,CAACC,KAAK,CAAChB,aAAa,KAAK1B,CAAC,EAAE;UAClC,MAAM2C,CAAC,GAAGzC,CAAC,CAAC,IAAI,CAACwC,KAAK,CAAChB,aAAa,EAAE,IAAI,CAACU,KAAK,CAAC;UACjD,IAAIQ,CAAC,GAAG9B,CAAC,CACP,IAAI,CAACsB,KAAK,EACVO,CAAC,CAACE,EAAE,EACJJ,CAAC,CAACK,OAAO,EACTL,CAAC,CAACM,GAAG,EACL,IAAI,CAACC,KAAK,CAACC,QAAQ,EACnB,IAAI,CAACf,eAAe,CAACgB,yBAAyB,CAAC,CACjD,CAAC;UACD,MAAMC,CAAC,GAAGjD,CAAC,CAAC0C,CAAC,EAAE,IAAI,CAACR,KAAK,CAAC;UAC1Be,CAAC,IAAIA,CAAC,CAACC,SAAS,KAAKR,CAAC,GAAG9B,CAAC,CACxB,IAAI,CAACsB,KAAK,EACVQ,CAAC,EACDH,CAAC,CAACK,OAAO,EACTL,CAAC,CAACM,GAAG,EACL,IAAI,CAACC,KAAK,CAACC,QAAQ,EACnB,IAAI,CAACf,eAAe,CAACgB,yBAAyB,CAAC,CACjD,CAAC,CAAC,EAAEP,CAAC,CAACE,EAAE,KAAKD,CAAC,KAAKH,CAAC,CAACY,cAAc,CAAC,CAAC,EAAE,IAAI,CAACC,gBAAgB,CAACV,CAAC,CAAC,CAAC,EAAE,CAACH,CAAC,CAACK,OAAO,KAAKpD,CAAC,CAAC6D,KAAK,IAAId,CAAC,CAACK,OAAO,KAAKpD,CAAC,CAAC8D,KAAK,KAAK,CAACb,CAAC,CAACc,QAAQ,KAAK,IAAI,CAACC,gBAAgB,CAACC,6BAA6B,CAAC,CAAC,EAAE,IAAI,CAACC,0BAA0B,CAACnB,CAAC,EAAEE,CAAC,CAACE,EAAE,CAAC,EAAE,CAACJ,CAAC,CAACoB,kBAAkB,CAAC,CAAC,IAAIlB,CAAC,CAACP,KAAK,CAAC0B,MAAM,KAAK,CAAC,IAAInB,CAAC,CAACoB,GAAG,IAAIC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAACvB,CAAC,CAACoB,GAAG,CAAC,CAAC;QACnU;QACAtB,CAAC,CAACK,OAAO,KAAKpD,CAAC,CAACyE,GAAG,IAAI,IAAI,CAACnB,KAAK,CAACoB,OAAO,IAAI,IAAI,CAACpB,KAAK,CAACoB,OAAO,CAACC,IAAI,CAAC,KAAK,CAAC,EAAE5B,CAAC,CAAC;MACjF,CAAC,EAAE,IAAI,CAAC6B,eAAe,GAAI7B,CAAC,IAAK;QAC/B,IAAI,CAACiB,gBAAgB,CAACa,kBAAkB,KAAK,IAAI,CAACjC,0CAA0C,CAAC,CAAC,EAAE,IAAI,CAACkC,gBAAgB,GAAGR,MAAM,CAACS,UAAU,CAAC,MAAM;UAC9I,IAAI,CAACC,gBAAgB,CAACjC,CAAC,CAAC,EAAE,IAAI,CAAC+B,gBAAgB,GAAG,IAAI;QACxD,CAAC,EAAExD,CAAC,CAAC,IAAI,CAACgC,KAAK,CAAC,CAAC,CAAC;MACpB,CAAC,EAAE,IAAI,CAAC2B,gBAAgB,GAAIlC,CAAC,IAAK;QAChC,IAAI,CAACiB,gBAAgB,CAACa,kBAAkB,IAAI,IAAI,CAACK,sBAAsB,CAACnC,CAAC,CAAC,KAAK,IAAI,CAACH,0CAA0C,CAAC,CAAC,EAAE,IAAI,CAACuC,gBAAgB,GAAGb,MAAM,CAACS,UAAU,CAAC,MAAM;UAChL,IAAI,CAACC,gBAAgB,CAAC1E,CAAC,CAAC,EAAE,IAAI,CAAC6E,gBAAgB,GAAG,IAAI;QACxD,CAAC,EAAE3D,CAAC,CAAC,IAAI,CAAC8B,KAAK,CAAC,CAAC,CAAC;MACpB,CAAC,EAAE,IAAI,CAAC8B,eAAe,GAAG,MAAM;QAC9B,IAAI,CAACpB,gBAAgB,CAACqB,mBAAmB,CAAC,CAAC;MAC7C,CAAC,EAAE,IAAI,CAACC,WAAW,GAAIvC,CAAC,IAAK;QAC3B,IAAI,CAACa,gBAAgB,CAACb,CAAC,CAAC,EAAE,IAAI,CAACiB,gBAAgB,CAACuB,eAAe,CAAC,CAAC;MACnE,CAAC,EAAE,IAAI,CAACC,WAAW,GAAG,CAACzC,CAAC,EAAEE,CAAC,KAAK;QAC9B,MAAMC,CAAC,GAAG1C,CAAC,CAACyC,CAAC,EAAE,IAAI,CAACP,KAAK,CAAC;QAC1BQ,CAAC,CAACa,QAAQ,KAAK,IAAI,CAACH,gBAAgB,CAACX,CAAC,CAAC,EAAE,IAAI,CAACe,gBAAgB,CAACyB,eAAe,CAACxC,CAAC,EAAE,IAAI,CAACiC,sBAAsB,CAACjC,CAAC,CAAC,CAAC,EAAE,IAAI,CAACiB,0BAA0B,CAACnB,CAAC,EAAEE,CAAC,CAAC,EAAE,CAACF,CAAC,CAACoB,kBAAkB,CAAC,CAAC,IAAIjB,CAAC,CAACmB,GAAG,IAAIC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAACtB,CAAC,CAACmB,GAAG,CAAC,CAAC;MAC9N,CAAC,EAAE,IAAI,CAACqB,UAAU,GAAG,CAAC3C,CAAC,EAAEE,CAAC,KAAK;QAC7B,IAAI,IAAI,CAACiC,sBAAsB,CAACnC,CAAC,CAAC,IAAI,IAAI,CAACa,gBAAgB,CAACtD,CAAC,CAAC,EAAE2C,CAAC,CAAC0C,aAAa,IAAI1C,CAAC,CAAC0C,aAAa,CAACC,QAAQ,KAAK,IAAI,EAAE;UACpH,MAAM1C,CAAC,GAAGD,CAAC,CAAC0C,aAAa,CAACE,YAAY,CAAC,IAAI,CAAC;UAC5C,IAAI3C,CAAC,IAAIA,CAAC,CAAC4C,QAAQ,CAAC,IAAI,CAACC,UAAU,CAAC,EAClC;QACJ;QACA,IAAI,CAACzC,KAAK,CAACoB,OAAO,IAAI,IAAI,CAACpB,KAAK,CAACoB,OAAO,CAACC,IAAI,CAAC,KAAK,CAAC,EAAE1B,CAAC,CAAC;MAC1D,CAAC,EAAE,IAAI,CAAC+C,YAAY,GAAIjD,CAAC,IAAKvC,CAAC,CAACuC,CAAC,EAAE,IAAI,CAACN,UAAU,CAAC,EAAE,IAAI,CAACuB,gBAAgB,GAAG,IAAIlC,CAAC,CAAC,IAAI,CAACwB,KAAK,CAAC2C,WAAW,EAAE,IAAI,CAACtD,KAAK,EAAE,IAAI,CAACiC,eAAe,CAAC,EAAE,IAAI,CAAC5B,KAAK,GAAGkD,MAAM,CAAC1B,MAAM,CAAC,CAAC,CAAC,EAAEzC,CAAC,EAAE;QAAEoE,aAAa,EAAE,CAAC;MAAE,CAAC,CAAC;IACxM;IACA,IAAIJ,UAAUA,CAAA,EAAG;MACf,OAAO,IAAI,CAACzC,KAAK,CAACH,EAAE;IACtB;IACA,IAAIiD,OAAOA,CAAA,EAAG;MACZ,OAAO,IAAI,CAAC7D,aAAa;IAC3B;IACA,IAAI8D,OAAOA,CAAA,EAAG;MACZ,OAAO,IAAI,CAAC/C,KAAK,CAAC+C,OAAO,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC/C,KAAK,CAAC+C,OAAO,GAAGlE,CAAC,CAACmE,YAAY,CAACD,OAAO;IACpF;IACA;AACF;AACA;IACEE,MAAMA,CAAA,EAAG;MACP,IAAI,CAACC,YAAY,CAAC,CAAC,EAAE,IAAI,CAACxD,KAAK,CAACmD,aAAa,IAAI,IAAI,CAAC3D,eAAe,CAACiE,yBAAyB,CAAC,IAAI,CAACC,2BAA2B,CAAC,CAAC,CAAC;MACnI,MAAMpE,CAAC,GAAG,IAAI,CAACU,KAAK,CAACf,aAAa,GAAG,IAAI,CAACe,KAAK,CAACf,aAAa,GAAG,IAAI,CAACe,KAAK,CAAChB,aAAa,GAAGtB,CAAC,CAAC,IAAI,CAACsC,KAAK,CAAChB,aAAa,CAAC,GAAG1B,CAAC;MAC1H,OAAO,eAAgBT,CAAC,CAAC8G,aAAa,CACpC,KAAK,EACL;QACExD,EAAE,EAAE,IAAI,CAACG,KAAK,CAACH,EAAE;QACjBL,SAAS,EAAE,IAAI,CAACA,SAAS;QACzB8D,KAAK,EAAE,IAAI,CAACtD,KAAK,CAACsD,KAAK;QACvBC,SAAS,EAAE,IAAI,CAACrE,eAAe,CAACgB,yBAAyB,CAAC,CAAC,GAAG,OAAO,GAAG,KAAK,CAAC;QAC9EsD,GAAG,EAAG/D,CAAC,IAAK;UACV,IAAI,CAACR,aAAa,GAAGQ,CAAC;QACxB;MACF,CAAC,EACD,eAAgBlD,CAAC,CAAC8G,aAAa,CAC7BjF,CAAC,EACD;QACEmF,SAAS,EAAE,IAAI,CAACE,gBAAgB,CAAC,CAAC;QAClC,kBAAkB,EAAE,IAAI,CAACzD,KAAK,CAACC,QAAQ,GAAG,UAAU,GAAG,KAAK,CAAC;QAC7Db,KAAK,EAAE,IAAI,CAACA,KAAK;QACjB2D,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBW,cAAc,EAAE,IAAI,CAAC1D,KAAK,CAACC,QAAQ;QACnC0D,sBAAsB,EAAE,IAAI,CAACzE,eAAe,CAACgB,yBAAyB,CAAC,CAAC;QACxExB,aAAa,EAAE,IAAI,CAACgB,KAAK,CAAChB,aAAa;QACvCkF,oBAAoB,EAAE5E,CAAC;QACvBJ,cAAc,EAAE,IAAI,CAACc,KAAK,CAACd,cAAc;QACzCiF,UAAU,EAAE,IAAI,CAAC7D,KAAK,CAAC6D,UAAU;QACjCC,UAAU,EAAE,IAAI,CAAC9D,KAAK,CAAC8D,UAAU;QACjCC,QAAQ,EAAE,IAAI,CAACtB,UAAU;QACzBuB,YAAY,EAAE,IAAI,CAACrC,gBAAgB;QACnCsC,WAAW,EAAE,IAAI,CAAC3C,eAAe;QACjC4C,WAAW,EAAE,IAAI,CAACpC,eAAe;QACjCqC,OAAO,EAAE,IAAI,CAACnC,WAAW;QACzBoC,OAAO,EAAE,IAAI,CAAClC,WAAW;QACzBmC,MAAM,EAAE,IAAI,CAACjC,UAAU;QACvBkC,oBAAoB,EAAE,IAAI,CAAC5B,YAAY;QACvC6B,IAAI,EAAE,IAAI,CAACvE,KAAK,CAACuE;MACnB,CACF,CACF,CAAC;IACH;IACA;AACF;AACA;IACEC,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAACjF,QAAQ,CAAC;QAAEsD,aAAa,EAAE,CAAC;MAAE,CAAC,CAAC;IACtC;IACA;AACF;AACA;IACE4B,kBAAkBA,CAACzF,CAAC,EAAE;MACpB,CAAC,CAAC,CAACA,CAAC,CAACiB,QAAQ,IAAI,CAAC,CAAC,IAAI,CAACD,KAAK,CAACC,QAAQ,IAAI,IAAI,CAACf,eAAe,CAACwF,mBAAmB,CAAC,CAAC,KAAK,IAAI,CAACrF,KAAK,CAAC,CAAC,EAAE,IAAI,CAACqB,gBAAgB,CAACiE,WAAW,GAAG,IAAI,CAAC3E,KAAK,CAAC2C,WAAW;IACnK;IACA;AACF;AACA;IACEiC,oBAAoBA,CAAA,EAAG;MACrB,IAAI,CAACtF,0CAA0C,CAAC,CAAC;IACnD;IACAgB,gBAAgBA,CAACtB,CAAC,EAAE;MAClB,IAAI,CAACO,QAAQ,CAAEE,CAAC,IAAK;QACnB,MAAME,CAAC,GAAGX,CAAC,KAAKhC,CAAC,GAAGyC,CAAC,CAACb,cAAc,GAAGtB,CAAC,CAAC0B,CAAC,CAAC;QAC3C,OAAO;UAAEL,aAAa,EAAEK,CAAC,KAAKhC,CAAC,IAAIQ,CAAC,CAACiC,CAAC,CAACd,aAAa,CAAC,IAAInB,CAAC,CAACwB,CAAC,CAAC,GAAGS,CAAC,CAACd,aAAa,GAAG3B,CAAC;UAAE0B,aAAa,EAAEM,CAAC;UAAEJ,cAAc,EAAEe;QAAE,CAAC;MAC5H,CAAC,CAAC;IACJ;IACA+B,gBAAgBA,CAAC1C,CAAC,EAAE;MAClB,IAAI,CAACO,QAAQ,CAAEE,CAAC,IAAKjC,CAAC,CAACwB,CAAC,CAAC,IAAIxB,CAAC,CAACiC,CAAC,CAACf,aAAa,CAAC,GAAG;QAChDC,aAAa,EAAEK,CAAC;QAChBN,aAAa,EAAEe,CAAC,CAACf,aAAa;QAC9BE,cAAc,EAAEa,CAAC,CAACb;MACpB,CAAC,GAAG;QAAED,aAAa,EAAEK,CAAC;QAAEN,aAAa,EAAE1B,CAAC;QAAE4B,cAAc,EAAElB;MAAE,CAAC,CAAC;IAChE;IACA+F,gBAAgBA,CAAA,EAAG;MACjB,OAAO7G,CAAC,CACN,SAAS,EACT,UAAU,EACV,QAAQ,EACR;QAAE,mBAAmB,EAAE,CAAC,IAAI,CAACoD,KAAK,CAACC;MAAS,CAAC,EAC7C;QAAE,iBAAiB,EAAE,IAAI,CAACD,KAAK,CAACC;MAAS,CAAC,EAC1C,IAAI,CAACD,KAAK,CAACuD,SACb,CAAC;IACH;IACAjE,0CAA0CA,CAAA,EAAG;MAC3C,IAAI,CAACkC,gBAAgB,KAAKqD,YAAY,CAAC,IAAI,CAACrD,gBAAgB,CAAC,EAAE,IAAI,CAACA,gBAAgB,GAAG,IAAI,CAAC,EAAE,IAAI,CAACK,gBAAgB,KAAKgD,YAAY,CAAC,IAAI,CAAChD,gBAAgB,CAAC,EAAE,IAAI,CAACA,gBAAgB,GAAG,IAAI,CAAC;IAC5L;IACAD,sBAAsBA,CAAC5C,CAAC,EAAE;MACxB,OAAO,CAAC,IAAI,CAACgB,KAAK,CAAC8E,kBAAkB,IAAI,IAAI,CAAC9E,KAAK,CAAC8E,kBAAkB,CAACC,OAAO,CAAC/F,CAAC,CAAC,KAAK,CAAC,CAAC;IAC1F;IACAoE,2BAA2BA,CAAA,EAAG;MAC5B,OAAO,CAAC,EAAE,IAAI,CAACpD,KAAK,CAACgF,GAAG,KAAK,KAAK,CAAC,GAAG,IAAI,CAAChF,KAAK,CAACgF,GAAG,KAAK,KAAK,GAAG,IAAI,CAAC/F,aAAa,IAAIgG,gBAAgB,CAAC,IAAI,CAAChG,aAAa,CAAC,CAACiG,SAAS,KAAK,KAAK,CAAC;IAClJ;IACAhC,YAAYA,CAAA,EAAG;MACb,MAAM;QAAE9D,KAAK,EAAEJ,CAAC;QAAEG,UAAU,EAAEM;MAAE,CAAC,GAAG7B,CAAC,CAAC,IAAI,CAACoC,KAAK,CAACZ,KAAK,EAAE,IAAI,CAACY,KAAK,CAACmF,QAAQ,CAAC;MAC5E,IAAI,CAAC/F,KAAK,GAAGJ,CAAC,EAAE,IAAI,CAACG,UAAU,GAAGM,CAAC;IACrC;IACAmB,0BAA0BA,CAAC5B,CAAC,EAAES,CAAC,EAAE;MAC/B3C,CAAC,CAAC,IAAI,CAACkD,KAAK,CAACoF,QAAQ,EAAEpG,CAAC,EAAE,IAAI,EAAE;QAAEqG,IAAI,EAAE,IAAI,CAAC3C,YAAY,CAACjD,CAAC,CAAC;QAAE6F,MAAM,EAAE7F;MAAE,CAAC,CAAC;IAC5E;EACF,CAAC;AACDZ,CAAC,CAAC0G,SAAS,GAAG;EACZtF,QAAQ,EAAEzD,CAAC,CAACgJ,IAAI;EAChBpG,KAAK,EAAE5C,CAAC,CAACiJ,OAAO,CAACjJ,CAAC,CAACkJ,MAAM,CAAC;EAC1BpC,KAAK,EAAE9G,CAAC,CAACkJ,MAAM;EACf3C,OAAO,EAAEvG,CAAC,CAACmJ,SAAS,CAAC,CACnBnJ,CAAC,CAACgJ,IAAI,EACNhJ,CAAC,CAACoJ,KAAK,CAAC;IACNC,YAAY,EAAErJ,CAAC,CAACsJ,MAAM;IACtBC,aAAa,EAAEvJ,CAAC,CAACsJ;EACnB,CAAC,CAAC,CACH,CAAC;EACFd,GAAG,EAAExI,CAAC,CAACwJ,MAAM;EACbC,cAAc,EAAEzJ,CAAC,CAACsJ,MAAM;EACxBI,eAAe,EAAE1J,CAAC,CAACsJ,MAAM;EACzBnD,WAAW,EAAEnG,CAAC,CAACgJ,IAAI;EACnB3B,UAAU,EAAErH,CAAC,CAAC2J,GAAG;EACjBrC,UAAU,EAAEtH,CAAC,CAAC2J,GAAG;EACjBrB,kBAAkB,EAAEtI,CAAC,CAACiJ,OAAO,CAACjJ,CAAC,CAACwJ,MAAM,CAAC;EACvCZ,QAAQ,EAAE5I,CAAC,CAAC4J,IAAI;EAChB7B,IAAI,EAAE/H,CAAC,CAACwJ;AACV,CAAC,EAAEnH,CAAC,CAACmE,YAAY,GAAG;EAAE/C,QAAQ,EAAE,CAAC,CAAC;EAAE8C,OAAO,EAAE,CAAC;AAAE,CAAC;AACjD,IAAIsD,CAAC,GAAGxH,CAAC;AACT,SACEwH,CAAC,IAAIC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}