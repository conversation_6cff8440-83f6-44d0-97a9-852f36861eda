{"ast": null, "code": "var baseGetTag = require('./_baseGetTag'),\n  isObject = require('./isObject');\n\n/** `Object#toString` result references. */\nvar asyncTag = '[object AsyncFunction]',\n  funcTag = '[object Function]',\n  genTag = '[object GeneratorFunction]',\n  proxyTag = '[object Proxy]';\n\n/**\n * Checks if `value` is classified as a `Function` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a function, else `false`.\n * @example\n *\n * _.isFunction(_);\n * // => true\n *\n * _.isFunction(/abc/);\n * // => false\n */\nfunction isFunction(value) {\n  if (!isObject(value)) {\n    return false;\n  }\n  // The use of `Object#toString` avoids issues with the `typeof` operator\n  // in Safari 9 which returns 'object' for typed arrays and other constructors.\n  var tag = baseGetTag(value);\n  return tag == funcTag || tag == genTag || tag == asyncTag || tag == proxyTag;\n}\nmodule.exports = isFunction;", "map": {"version": 3, "names": ["baseGetTag", "require", "isObject", "asyncTag", "funcTag", "genTag", "proxyTag", "isFunction", "value", "tag", "module", "exports"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/lodash/isFunction.js"], "sourcesContent": ["var baseGetTag = require('./_baseGetTag'),\n    isObject = require('./isObject');\n\n/** `Object#toString` result references. */\nvar asyncTag = '[object AsyncFunction]',\n    funcTag = '[object Function]',\n    genTag = '[object GeneratorFunction]',\n    proxyTag = '[object Proxy]';\n\n/**\n * Checks if `value` is classified as a `Function` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a function, else `false`.\n * @example\n *\n * _.isFunction(_);\n * // => true\n *\n * _.isFunction(/abc/);\n * // => false\n */\nfunction isFunction(value) {\n  if (!isObject(value)) {\n    return false;\n  }\n  // The use of `Object#toString` avoids issues with the `typeof` operator\n  // in Safari 9 which returns 'object' for typed arrays and other constructors.\n  var tag = baseGetTag(value);\n  return tag == funcTag || tag == genTag || tag == asyncTag || tag == proxyTag;\n}\n\nmodule.exports = isFunction;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAe,CAAC;EACrCC,QAAQ,GAAGD,OAAO,CAAC,YAAY,CAAC;;AAEpC;AACA,IAAIE,QAAQ,GAAG,wBAAwB;EACnCC,OAAO,GAAG,mBAAmB;EAC7BC,MAAM,GAAG,4BAA4B;EACrCC,QAAQ,GAAG,gBAAgB;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzB,IAAI,CAACN,QAAQ,CAACM,KAAK,CAAC,EAAE;IACpB,OAAO,KAAK;EACd;EACA;EACA;EACA,IAAIC,GAAG,GAAGT,UAAU,CAACQ,KAAK,CAAC;EAC3B,OAAOC,GAAG,IAAIL,OAAO,IAAIK,GAAG,IAAIJ,MAAM,IAAII,GAAG,IAAIN,QAAQ,IAAIM,GAAG,IAAIH,QAAQ;AAC9E;AAEAI,MAAM,CAACC,OAAO,GAAGJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}