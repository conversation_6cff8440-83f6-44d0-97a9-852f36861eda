{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport { provideLocalizationService as N, registerForLocalization as S } from \"@progress/kendo-react-intl\";\nimport { colorGradientContrastRatio as i, messages as o, colorGradientAALevel as l, colorGradientAAALevel as m, colorGradientPass as g, colorGradientFail as d } from \"../messages/index.mjs\";\nimport { getContrastFromTwoRGBAs as F } from \"./utils/color-parser.mjs\";\nimport { IconWrap as n } from \"@progress/kendo-react-common\";\nimport { checkIcon as r, xIcon as p } from \"@progress/kendo-svg-icons\";\nclass G extends e.Component {\n  render() {\n    const t = N(this),\n      x = t.toLanguageString(i, o[i]),\n      k = t.toLanguageString(l, o[l]),\n      v = t.toLanguageString(m, o[m]),\n      A = t.toLanguageString(g, o[g]),\n      E = t.toLanguageString(d, o[d]),\n      a = F(this.props.rgba, this.props.bgColor),\n      u = 4.5.toFixed(1),\n      L = 7 .toFixed(1),\n      C = `${x}: ${a.toFixed(2)}`,\n      f = `${k}: ${u}`,\n      h = `${v}: ${L}`,\n      s = /* @__PURE__ */e.createElement(\"span\", {\n        className: \"k-contrast-validation k-text-success\"\n      }, A, \" \", /* @__PURE__ */e.createElement(n, {\n        name: \"check\",\n        icon: r\n      })),\n      c = /* @__PURE__ */e.createElement(\"span\", {\n        className: \"k-contrast-validation k-text-error\"\n      }, E, \" \", /* @__PURE__ */e.createElement(n, {\n        name: \"x\",\n        icon: p\n      }));\n    return /* @__PURE__ */e.createElement(\"div\", {\n      className: \"k-vbox k-colorgradient-color-contrast\"\n    }, /* @__PURE__ */e.createElement(\"div\", {\n      className: \"k-contrast-ratio\"\n    }, /* @__PURE__ */e.createElement(\"span\", {\n      className: \"k-contrast-ratio-text\"\n    }, C), a >= 4.5 ? /* @__PURE__ */e.createElement(\"span\", {\n      className: \"k-contrast-validation k-text-success\"\n    }, /* @__PURE__ */e.createElement(n, {\n      name: \"check\",\n      icon: r\n    }), a >= 7 && /* @__PURE__ */e.createElement(n, {\n      name: \"check\",\n      icon: r\n    })) : /* @__PURE__ */e.createElement(\"span\", {\n      className: \"k-contrast-validation k-text-error\"\n    }, /* @__PURE__ */e.createElement(n, {\n      name: \"x\",\n      icon: p\n    }))), /* @__PURE__ */e.createElement(\"div\", null, /* @__PURE__ */e.createElement(\"span\", null, f), a >= 4.5 ? s : c), /* @__PURE__ */e.createElement(\"div\", null, /* @__PURE__ */e.createElement(\"span\", null, h), a >= 7 ? s : c));\n  }\n}\nS(G);\nexport { G as ColorContrastLabels };", "map": {"version": 3, "names": ["e", "provideLocalizationService", "N", "registerForLocalization", "S", "colorGradientContrastRatio", "i", "messages", "o", "colorGradientAALevel", "l", "colorGradientAAALevel", "m", "colorGradientPass", "g", "colorGradientFail", "d", "getContrastFromTwoRGBAs", "F", "IconWrap", "n", "checkIcon", "r", "xIcon", "p", "G", "Component", "render", "t", "x", "toLanguageString", "k", "v", "A", "E", "a", "props", "rgba", "bgColor", "u", "toFixed", "L", "C", "f", "h", "s", "createElement", "className", "name", "icon", "c", "ColorContrastLabels"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-inputs/colors/ColorContrastLabels.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport { provideLocalizationService as N, registerForLocalization as S } from \"@progress/kendo-react-intl\";\nimport { colorGradientContrastRatio as i, messages as o, colorGradientAALevel as l, colorGradientAAALevel as m, colorGradientPass as g, colorGradientFail as d } from \"../messages/index.mjs\";\nimport { getContrastFromTwoRGBAs as F } from \"./utils/color-parser.mjs\";\nimport { IconWrap as n } from \"@progress/kendo-react-common\";\nimport { checkIcon as r, xIcon as p } from \"@progress/kendo-svg-icons\";\nclass G extends e.Component {\n  render() {\n    const t = N(this), x = t.toLanguageString(\n      i,\n      o[i]\n    ), k = t.toLanguageString(\n      l,\n      o[l]\n    ), v = t.toLanguageString(\n      m,\n      o[m]\n    ), A = t.toLanguageString(g, o[g]), E = t.toLanguageString(d, o[d]), a = F(this.props.rgba, this.props.bgColor), u = 4.5.toFixed(1), L = 7 .toFixed(1), C = `${x}: ${a.toFixed(2)}`, f = `${k}: ${u}`, h = `${v}: ${L}`, s = /* @__PURE__ */ e.createElement(\"span\", { className: \"k-contrast-validation k-text-success\" }, A, \" \", /* @__PURE__ */ e.createElement(n, { name: \"check\", icon: r })), c = /* @__PURE__ */ e.createElement(\"span\", { className: \"k-contrast-validation k-text-error\" }, E, \" \", /* @__PURE__ */ e.createElement(n, { name: \"x\", icon: p }));\n    return /* @__PURE__ */ e.createElement(\"div\", { className: \"k-vbox k-colorgradient-color-contrast\" }, /* @__PURE__ */ e.createElement(\"div\", { className: \"k-contrast-ratio\" }, /* @__PURE__ */ e.createElement(\"span\", { className: \"k-contrast-ratio-text\" }, C), a >= 4.5 ? /* @__PURE__ */ e.createElement(\"span\", { className: \"k-contrast-validation k-text-success\" }, /* @__PURE__ */ e.createElement(n, { name: \"check\", icon: r }), a >= 7 && /* @__PURE__ */ e.createElement(n, { name: \"check\", icon: r })) : /* @__PURE__ */ e.createElement(\"span\", { className: \"k-contrast-validation k-text-error\" }, /* @__PURE__ */ e.createElement(n, { name: \"x\", icon: p }))), /* @__PURE__ */ e.createElement(\"div\", null, /* @__PURE__ */ e.createElement(\"span\", null, f), a >= 4.5 ? s : c), /* @__PURE__ */ e.createElement(\"div\", null, /* @__PURE__ */ e.createElement(\"span\", null, h), a >= 7 ? s : c));\n  }\n}\nS(G);\nexport {\n  G as ColorContrastLabels\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,SAASC,0BAA0B,IAAIC,CAAC,EAAEC,uBAAuB,IAAIC,CAAC,QAAQ,4BAA4B;AAC1G,SAASC,0BAA0B,IAAIC,CAAC,EAAEC,QAAQ,IAAIC,CAAC,EAAEC,oBAAoB,IAAIC,CAAC,EAAEC,qBAAqB,IAAIC,CAAC,EAAEC,iBAAiB,IAAIC,CAAC,EAAEC,iBAAiB,IAAIC,CAAC,QAAQ,uBAAuB;AAC7L,SAASC,uBAAuB,IAAIC,CAAC,QAAQ,0BAA0B;AACvE,SAASC,QAAQ,IAAIC,CAAC,QAAQ,8BAA8B;AAC5D,SAASC,SAAS,IAAIC,CAAC,EAAEC,KAAK,IAAIC,CAAC,QAAQ,2BAA2B;AACtE,MAAMC,CAAC,SAASzB,CAAC,CAAC0B,SAAS,CAAC;EAC1BC,MAAMA,CAAA,EAAG;IACP,MAAMC,CAAC,GAAG1B,CAAC,CAAC,IAAI,CAAC;MAAE2B,CAAC,GAAGD,CAAC,CAACE,gBAAgB,CACvCxB,CAAC,EACDE,CAAC,CAACF,CAAC,CACL,CAAC;MAAEyB,CAAC,GAAGH,CAAC,CAACE,gBAAgB,CACvBpB,CAAC,EACDF,CAAC,CAACE,CAAC,CACL,CAAC;MAAEsB,CAAC,GAAGJ,CAAC,CAACE,gBAAgB,CACvBlB,CAAC,EACDJ,CAAC,CAACI,CAAC,CACL,CAAC;MAAEqB,CAAC,GAAGL,CAAC,CAACE,gBAAgB,CAAChB,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,CAAC;MAAEoB,CAAC,GAAGN,CAAC,CAACE,gBAAgB,CAACd,CAAC,EAAER,CAAC,CAACQ,CAAC,CAAC,CAAC;MAAEmB,CAAC,GAAGjB,CAAC,CAAC,IAAI,CAACkB,KAAK,CAACC,IAAI,EAAE,IAAI,CAACD,KAAK,CAACE,OAAO,CAAC;MAAEC,CAAC,GAAG,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC;MAAEC,CAAC,GAAG,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC;MAAEE,CAAC,GAAG,GAAGb,CAAC,KAAKM,CAAC,CAACK,OAAO,CAAC,CAAC,CAAC,EAAE;MAAEG,CAAC,GAAG,GAAGZ,CAAC,KAAKQ,CAAC,EAAE;MAAEK,CAAC,GAAG,GAAGZ,CAAC,KAAKS,CAAC,EAAE;MAAEI,CAAC,GAAG,eAAgB7C,CAAC,CAAC8C,aAAa,CAAC,MAAM,EAAE;QAAEC,SAAS,EAAE;MAAuC,CAAC,EAAEd,CAAC,EAAE,GAAG,EAAE,eAAgBjC,CAAC,CAAC8C,aAAa,CAAC1B,CAAC,EAAE;QAAE4B,IAAI,EAAE,OAAO;QAAEC,IAAI,EAAE3B;MAAE,CAAC,CAAC,CAAC;MAAE4B,CAAC,GAAG,eAAgBlD,CAAC,CAAC8C,aAAa,CAAC,MAAM,EAAE;QAAEC,SAAS,EAAE;MAAqC,CAAC,EAAEb,CAAC,EAAE,GAAG,EAAE,eAAgBlC,CAAC,CAAC8C,aAAa,CAAC1B,CAAC,EAAE;QAAE4B,IAAI,EAAE,GAAG;QAAEC,IAAI,EAAEzB;MAAE,CAAC,CAAC,CAAC;IACziB,OAAO,eAAgBxB,CAAC,CAAC8C,aAAa,CAAC,KAAK,EAAE;MAAEC,SAAS,EAAE;IAAwC,CAAC,EAAE,eAAgB/C,CAAC,CAAC8C,aAAa,CAAC,KAAK,EAAE;MAAEC,SAAS,EAAE;IAAmB,CAAC,EAAE,eAAgB/C,CAAC,CAAC8C,aAAa,CAAC,MAAM,EAAE;MAAEC,SAAS,EAAE;IAAwB,CAAC,EAAEL,CAAC,CAAC,EAAEP,CAAC,IAAI,GAAG,GAAG,eAAgBnC,CAAC,CAAC8C,aAAa,CAAC,MAAM,EAAE;MAAEC,SAAS,EAAE;IAAuC,CAAC,EAAE,eAAgB/C,CAAC,CAAC8C,aAAa,CAAC1B,CAAC,EAAE;MAAE4B,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE3B;IAAE,CAAC,CAAC,EAAEa,CAAC,IAAI,CAAC,IAAI,eAAgBnC,CAAC,CAAC8C,aAAa,CAAC1B,CAAC,EAAE;MAAE4B,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE3B;IAAE,CAAC,CAAC,CAAC,GAAG,eAAgBtB,CAAC,CAAC8C,aAAa,CAAC,MAAM,EAAE;MAAEC,SAAS,EAAE;IAAqC,CAAC,EAAE,eAAgB/C,CAAC,CAAC8C,aAAa,CAAC1B,CAAC,EAAE;MAAE4B,IAAI,EAAE,GAAG;MAAEC,IAAI,EAAEzB;IAAE,CAAC,CAAC,CAAC,CAAC,EAAE,eAAgBxB,CAAC,CAAC8C,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,eAAgB9C,CAAC,CAAC8C,aAAa,CAAC,MAAM,EAAE,IAAI,EAAEH,CAAC,CAAC,EAAER,CAAC,IAAI,GAAG,GAAGU,CAAC,GAAGK,CAAC,CAAC,EAAE,eAAgBlD,CAAC,CAAC8C,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,eAAgB9C,CAAC,CAAC8C,aAAa,CAAC,MAAM,EAAE,IAAI,EAAEF,CAAC,CAAC,EAAET,CAAC,IAAI,CAAC,GAAGU,CAAC,GAAGK,CAAC,CAAC,CAAC;EACx3B;AACF;AACA9C,CAAC,CAACqB,CAAC,CAAC;AACJ,SACEA,CAAC,IAAI0B,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}