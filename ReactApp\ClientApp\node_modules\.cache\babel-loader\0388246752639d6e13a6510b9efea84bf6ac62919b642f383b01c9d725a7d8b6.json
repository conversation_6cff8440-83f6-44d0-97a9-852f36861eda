{"ast": null, "code": "import { formatDate } from './dates';\nimport { formatNumber } from './numbers';\nimport { EMPTY } from './common/constants';\nimport isDate from './common/is-date';\nimport isNumber from './common/is-number';\nvar formatRegExp = /\\{(\\d+)(:[^}]+)?\\}/g;\nexport function toString(value, format, locale) {\n  if (format) {\n    if (isDate(value)) {\n      return formatDate(value, format, locale);\n    } else if (isNumber(value)) {\n      return formatNumber(value, format, locale);\n    }\n  }\n  return value !== undefined && value !== null ? value : EMPTY;\n}\nexport function format(format, values, locale) {\n  return format.replace(formatRegExp, function (match, index, placeholderFormat) {\n    var value = values[parseInt(index, 10)];\n    return toString(value, placeholderFormat ? placeholderFormat.substring(1) : EMPTY, locale);\n  });\n}", "map": {"version": 3, "names": ["formatDate", "formatNumber", "EMPTY", "isDate", "isNumber", "formatRegExp", "toString", "value", "format", "locale", "undefined", "values", "replace", "match", "index", "placeholderFormat", "parseInt", "substring"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-intl/dist/es/format.js"], "sourcesContent": ["import { formatDate } from './dates';\nimport { formatNumber } from './numbers';\nimport { EMPTY } from './common/constants';\nimport isDate from './common/is-date';\nimport isNumber from './common/is-number';\n\nvar formatRegExp = /\\{(\\d+)(:[^}]+)?\\}/g;\n\nexport function toString(value, format, locale) {\n    if (format) {\n        if (isDate(value)) {\n            return formatDate(value, format, locale);\n        } else if (isNumber(value)) {\n            return formatNumber(value, format, locale);\n        }\n    }\n\n    return value !== undefined && value !== null ? value : EMPTY;\n}\n\nexport function format(format, values, locale) {\n    return format.replace(formatRegExp, function(match, index, placeholderFormat) {\n        var value = values[parseInt(index, 10)];\n\n        return toString(value, placeholderFormat ? placeholderFormat.substring(1) : EMPTY, locale);\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,SAAS;AACpC,SAASC,YAAY,QAAQ,WAAW;AACxC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,QAAQ,MAAM,oBAAoB;AAEzC,IAAIC,YAAY,GAAG,qBAAqB;AAExC,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAE;EAC5C,IAAID,MAAM,EAAE;IACR,IAAIL,MAAM,CAACI,KAAK,CAAC,EAAE;MACf,OAAOP,UAAU,CAACO,KAAK,EAAEC,MAAM,EAAEC,MAAM,CAAC;IAC5C,CAAC,MAAM,IAAIL,QAAQ,CAACG,KAAK,CAAC,EAAE;MACxB,OAAON,YAAY,CAACM,KAAK,EAAEC,MAAM,EAAEC,MAAM,CAAC;IAC9C;EACJ;EAEA,OAAOF,KAAK,KAAKG,SAAS,IAAIH,KAAK,KAAK,IAAI,GAAGA,KAAK,GAAGL,KAAK;AAChE;AAEA,OAAO,SAASM,MAAMA,CAACA,MAAM,EAAEG,MAAM,EAAEF,MAAM,EAAE;EAC3C,OAAOD,MAAM,CAACI,OAAO,CAACP,YAAY,EAAE,UAASQ,KAAK,EAAEC,KAAK,EAAEC,iBAAiB,EAAE;IAC1E,IAAIR,KAAK,GAAGI,MAAM,CAACK,QAAQ,CAACF,KAAK,EAAE,EAAE,CAAC,CAAC;IAEvC,OAAOR,QAAQ,CAACC,KAAK,EAAEQ,iBAAiB,GAAGA,iBAAiB,CAACE,SAAS,CAAC,CAAC,CAAC,GAAGf,KAAK,EAAEO,MAAM,CAAC;EAC9F,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}