{"ast": null, "code": "import BaseNode from '../core/base-node';\nimport renderAllAttr from './utils/render-all-attributes';\nimport renderAttr from './utils/render-attribute';\nimport renderStyle from './utils/render-style';\nimport NODE_MAP from './node-map';\nimport renderSVG from './utils/render-svg';\nimport { SVG_NS, NONE } from './constants';\nimport { support, htmlEncode } from '../common';\nimport { defined } from '../util';\nimport { PATTERN } from '../core/constants';\nvar TRANSFORM = \"transform\";\nvar DefinitionMap = {\n  clip: \"clip-path\",\n  fill: \"fill\"\n};\nfunction isDefinition(type, value) {\n  return type === \"clip\" || type === \"fill\" && (!value || value.nodeType === \"Gradient\" || value.nodeType === PATTERN);\n}\nfunction baseUrl() {\n  var base = document.getElementsByTagName(\"base\")[0];\n  var href = document.location.href;\n  var url = \"\";\n  if (base && !(support.browser || {}).msie) {\n    var hashIndex = href.indexOf(\"#\");\n    if (hashIndex !== -1) {\n      href = href.substring(0, hashIndex);\n    }\n    url = href;\n  }\n  return url;\n}\nvar Node = function (BaseNode) {\n  function Node(srcElement, options) {\n    BaseNode.call(this, srcElement);\n    this.definitions = {};\n    this.options = options;\n  }\n  if (BaseNode) Node.__proto__ = BaseNode;\n  Node.prototype = Object.create(BaseNode && BaseNode.prototype);\n  Node.prototype.constructor = Node;\n  Node.prototype.destroy = function destroy() {\n    if (this.element) {\n      this.element._kendoNode = null;\n      this.element = null;\n    }\n    this.clearDefinitions();\n    BaseNode.prototype.destroy.call(this);\n  };\n  Node.prototype.load = function load(elements, pos) {\n    var this$1 = this;\n    for (var i = 0; i < elements.length; i++) {\n      var srcElement = elements[i];\n      var children = srcElement.children;\n      var childNode = new NODE_MAP[srcElement.nodeType](srcElement, this$1.options);\n      if (defined(pos)) {\n        this$1.insertAt(childNode, pos);\n      } else {\n        this$1.append(childNode);\n      }\n      childNode.createDefinitions();\n      if (children && children.length > 0) {\n        childNode.load(children);\n      }\n      var element = this$1.element;\n      if (element) {\n        childNode.attachTo(element, pos);\n      }\n    }\n  };\n  Node.prototype.root = function root() {\n    var root = this;\n    while (root.parent) {\n      root = root.parent;\n    }\n    return root;\n  };\n  Node.prototype.attachTo = function attachTo(domElement, pos) {\n    var container = document.createElement(\"div\");\n    renderSVG(container, \"<svg xmlns='\" + SVG_NS + \"' version='1.1'>\" + this.render() + \"</svg>\");\n    var element = container.firstChild.firstChild;\n    if (element) {\n      if (defined(pos)) {\n        domElement.insertBefore(element, domElement.childNodes[pos] || null);\n      } else {\n        domElement.appendChild(element);\n      }\n      this.setElement(element);\n    }\n  };\n  Node.prototype.setElement = function setElement(element) {\n    if (this.element) {\n      this.element._kendoNode = null;\n    }\n    this.element = element;\n    this.element._kendoNode = this;\n    var nodes = this.childNodes;\n    for (var i = 0; i < nodes.length; i++) {\n      var childElement = element.childNodes[i];\n      nodes[i].setElement(childElement);\n    }\n  };\n  Node.prototype.clear = function clear() {\n    this.clearDefinitions();\n    if (this.element) {\n      this.element.innerHTML = \"\";\n    }\n    var children = this.childNodes;\n    for (var i = 0; i < children.length; i++) {\n      children[i].destroy();\n    }\n    this.childNodes = [];\n  };\n  Node.prototype.removeSelf = function removeSelf() {\n    if (this.element) {\n      var parentNode = this.element.parentNode;\n      if (parentNode) {\n        parentNode.removeChild(this.element);\n      }\n      this.element = null;\n    }\n    BaseNode.prototype.removeSelf.call(this);\n  };\n  Node.prototype.template = function template() {\n    return this.renderChildren();\n  };\n  Node.prototype.render = function render() {\n    return this.template();\n  };\n  Node.prototype.renderChildren = function renderChildren() {\n    var nodes = this.childNodes;\n    var output = \"\";\n    for (var i = 0; i < nodes.length; i++) {\n      output += nodes[i].render();\n    }\n    return output;\n  };\n  Node.prototype.optionsChange = function optionsChange(e) {\n    var field = e.field;\n    var value = e.value;\n    if (field === \"visible\") {\n      this.css(\"display\", value ? \"\" : NONE);\n    } else if (DefinitionMap[field] && isDefinition(field, value)) {\n      this.updateDefinition(field, value);\n    } else if (field === \"opacity\") {\n      this.attr(\"opacity\", value);\n    } else if (field === \"cursor\") {\n      this.css(\"cursor\", value);\n    } else if (field === \"id\") {\n      if (value) {\n        this.attr(\"id\", value);\n      } else {\n        this.removeAttr(\"id\");\n      }\n    }\n    BaseNode.prototype.optionsChange.call(this, e);\n  };\n  Node.prototype.accessibilityOptionsChange = function accessibilityOptionsChange(e) {\n    var field = e.field;\n    var value = e.value;\n    if (field === \"role\") {\n      if (value) {\n        this.attr(\"role\", value);\n      } else {\n        this.removeAttr(\"role\");\n      }\n    } else if (field === \"ariaLabel\") {\n      if (value) {\n        this.attr(\"aria-label\", htmlEncode(value));\n      } else {\n        this.removeAttr(\"aria-label\");\n      }\n    } else if (field === \"ariaRoleDescription\") {\n      if (value) {\n        this.attr(\"aria-roledescription\", htmlEncode(value));\n      } else {\n        this.removeAttr(\"aria-roledescription\");\n      }\n    } else if (field === \"ariaChecked\") {\n      if (defined(value)) {\n        this.attr(\"aria-checked\", value);\n      } else {\n        this.removeAttr(\"aria-checked\");\n      }\n    } else if (field === \"className\") {\n      this.className(value);\n    }\n  };\n  Node.prototype.attr = function attr(name, value) {\n    if (this.element) {\n      this.element.setAttribute(name, value);\n    }\n  };\n  Node.prototype.allAttr = function allAttr(attrs) {\n    var this$1 = this;\n    for (var i = 0; i < attrs.length; i++) {\n      this$1.attr(attrs[i][0], attrs[i][1]);\n    }\n  };\n  Node.prototype.toggleAttr = function toggleAttr(name, value) {\n    if (value) {\n      this.attr(name, value);\n    } else {\n      this.removeAttr(name);\n    }\n  };\n  Node.prototype.css = function css(name, value) {\n    if (this.element) {\n      this.element.style[name] = value;\n    }\n  };\n  Node.prototype.allCss = function allCss(styles) {\n    var this$1 = this;\n    for (var i = 0; i < styles.length; i++) {\n      this$1.css(styles[i][0], styles[i][1]);\n    }\n  };\n  Node.prototype.className = function className(value) {\n    var this$1 = this;\n    if (this.element) {\n      (ref = this.element.classList).remove.apply(ref, this.element.classList);\n      value.split(\" \").forEach(function (item) {\n        this$1.element.classList.add(item);\n      });\n    }\n    var ref;\n  };\n  Node.prototype.removeAttr = function removeAttr(name) {\n    if (this.element) {\n      this.element.removeAttribute(name);\n    }\n  };\n  Node.prototype.mapTransform = function mapTransform(transform) {\n    var attrs = [];\n    if (transform) {\n      attrs.push([TRANSFORM, \"matrix(\" + transform.matrix().toString(6) + \")\"]);\n    }\n    return attrs;\n  };\n  Node.prototype.renderTransform = function renderTransform() {\n    return renderAllAttr(this.mapTransform(this.srcElement.transform()));\n  };\n  Node.prototype.transformChange = function transformChange(value) {\n    if (value) {\n      this.allAttr(this.mapTransform(value));\n    } else {\n      this.removeAttr(TRANSFORM);\n    }\n  };\n  Node.prototype.mapStyle = function mapStyle() {\n    var options = this.srcElement.options;\n    var style = [[\"cursor\", options.cursor]];\n    if (options.visible === false) {\n      style.push([\"display\", NONE]);\n    }\n    return style;\n  };\n  Node.prototype.renderStyle = function renderStyle$1() {\n    return renderAttr(\"style\", renderStyle(this.mapStyle(true)));\n  };\n  Node.prototype.renderOpacity = function renderOpacity() {\n    return renderAttr(\"opacity\", this.srcElement.options.opacity);\n  };\n  Node.prototype.renderId = function renderId() {\n    return renderAttr(\"id\", this.srcElement.options.id);\n  };\n  Node.prototype.renderClassName = function renderClassName() {\n    return renderAttr(\"class\", this.srcElement.options.className);\n  };\n  Node.prototype.renderRole = function renderRole() {\n    return renderAttr(\"role\", this.srcElement.options.role);\n  };\n  Node.prototype.renderAriaLabel = function renderAriaLabel() {\n    var value = this.srcElement.options.ariaLabel;\n    if (value) {\n      value = htmlEncode(value);\n    }\n    return renderAttr(\"aria-label\", value);\n  };\n  Node.prototype.renderAriaRoleDescription = function renderAriaRoleDescription() {\n    var value = this.srcElement.options.ariaRoleDescription;\n    if (value) {\n      value = htmlEncode(value);\n    }\n    return renderAttr(\"aria-roledescription\", value);\n  };\n  Node.prototype.renderAriaChecked = function renderAriaChecked() {\n    return renderAttr(\"aria-checked\", this.srcElement.options.ariaChecked);\n  };\n  Node.prototype.createDefinitions = function createDefinitions() {\n    var srcElement = this.srcElement;\n    var definitions = this.definitions;\n    if (srcElement) {\n      var options = srcElement.options;\n      var hasDefinitions;\n      for (var field in DefinitionMap) {\n        var definition = options.get(field);\n        if (definition && isDefinition(field, definition)) {\n          definitions[field] = definition;\n          hasDefinitions = true;\n        }\n      }\n      if (hasDefinitions) {\n        this.definitionChange({\n          action: \"add\",\n          definitions: definitions\n        });\n      }\n    }\n  };\n  Node.prototype.definitionChange = function definitionChange(e) {\n    if (this.parent) {\n      this.parent.definitionChange(e);\n    }\n  };\n  Node.prototype.updateDefinition = function updateDefinition(type, value) {\n    var definitions = this.definitions;\n    var current = definitions[type];\n    var attr = DefinitionMap[type];\n    var definition = {};\n    if (current) {\n      definition[type] = current;\n      this.definitionChange({\n        action: \"remove\",\n        definitions: definition\n      });\n      delete definitions[type];\n    }\n    if (!value) {\n      if (current) {\n        this.removeAttr(attr);\n      }\n    } else {\n      definition[type] = value;\n      this.definitionChange({\n        action: \"add\",\n        definitions: definition\n      });\n      definitions[type] = value;\n      this.attr(attr, this.refUrl(value.id));\n    }\n  };\n  Node.prototype.clearDefinitions = function clearDefinitions() {\n    var definitions = this.definitions;\n    this.definitionChange({\n      action: \"remove\",\n      definitions: definitions\n    });\n    this.definitions = {};\n  };\n  Node.prototype.renderDefinitions = function renderDefinitions() {\n    return renderAllAttr(this.mapDefinitions());\n  };\n  Node.prototype.mapDefinitions = function mapDefinitions() {\n    var this$1 = this;\n    var definitions = this.definitions;\n    var attrs = [];\n    for (var field in definitions) {\n      attrs.push([DefinitionMap[field], this$1.refUrl(definitions[field].id)]);\n    }\n    return attrs;\n  };\n  Node.prototype.refUrl = function refUrl(id) {\n    var skipBaseHref = (this.options || {}).skipBaseHref;\n    var baseHref = this.baseUrl().replace(/'/g, \"\\\\'\");\n    var base = skipBaseHref ? '' : baseHref;\n    return \"url(\" + base + \"#\" + id + \")\";\n  };\n  Node.prototype.baseUrl = function baseUrl$1() {\n    return baseUrl();\n  };\n  return Node;\n}(BaseNode);\nexport default Node;", "map": {"version": 3, "names": ["BaseNode", "renderAllAttr", "renderAttr", "renderStyle", "NODE_MAP", "renderSVG", "SVG_NS", "NONE", "support", "htmlEncode", "defined", "PATTERN", "TRANSFORM", "DefinitionMap", "clip", "fill", "isDefinition", "type", "value", "nodeType", "baseUrl", "base", "document", "getElementsByTagName", "href", "location", "url", "browser", "msie", "hashIndex", "indexOf", "substring", "Node", "srcElement", "options", "call", "definitions", "__proto__", "prototype", "Object", "create", "constructor", "destroy", "element", "_kendoNode", "clearDefinitions", "load", "elements", "pos", "this$1", "i", "length", "children", "childNode", "insertAt", "append", "createDefinitions", "attachTo", "root", "parent", "dom<PERSON>lement", "container", "createElement", "render", "<PERSON><PERSON><PERSON><PERSON>", "insertBefore", "childNodes", "append<PERSON><PERSON><PERSON>", "setElement", "nodes", "childElement", "clear", "innerHTML", "removeSelf", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "template", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "output", "optionsChange", "e", "field", "css", "updateDefinition", "attr", "removeAttr", "accessibilityOptionsChange", "className", "name", "setAttribute", "allAttr", "attrs", "toggleAttr", "style", "allCss", "styles", "ref", "classList", "remove", "apply", "split", "for<PERSON>ach", "item", "add", "removeAttribute", "mapTransform", "transform", "push", "matrix", "toString", "renderTransform", "transformChange", "mapStyle", "cursor", "visible", "renderStyle$1", "renderOpacity", "opacity", "renderId", "id", "renderClassName", "renderRole", "role", "renderAriaLabel", "aria<PERSON><PERSON><PERSON>", "renderAriaRoleDescription", "ariaRoleDescription", "renderAriaChecked", "ariaChe<PERSON>", "hasDefinitions", "definition", "get", "definitionChange", "action", "current", "refUrl", "renderDefinitions", "mapDefinitions", "skipBaseHref", "baseHref", "replace", "baseUrl$1"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/svg/node.js"], "sourcesContent": ["import BaseNode from '../core/base-node';\nimport renderAllAttr from './utils/render-all-attributes';\nimport renderAttr from './utils/render-attribute';\nimport renderStyle from './utils/render-style';\nimport NODE_MAP from './node-map';\nimport renderSVG from './utils/render-svg';\nimport { SVG_NS, NONE } from './constants';\nimport { support, htmlEncode } from '../common';\nimport { defined } from '../util';\nimport { PATTERN } from '../core/constants';\n\nvar TRANSFORM = \"transform\";\nvar DefinitionMap = {\n    clip: \"clip-path\",\n    fill: \"fill\"\n};\n\nfunction isDefinition(type, value) {\n    return type === \"clip\" || (type === \"fill\" && (!value || value.nodeType === \"Gradient\" || value.nodeType === PATTERN));\n}\n\nfunction baseUrl() {\n    var base = document.getElementsByTagName(\"base\")[0];\n    var href = document.location.href;\n    var url = \"\";\n\n    if (base && !(support.browser || {}).msie) {\n        var hashIndex = href.indexOf(\"#\");\n        if (hashIndex !== -1) {\n            href = href.substring(0, hashIndex);\n        }\n\n        url = href;\n    }\n\n    return url;\n}\n\nvar Node = (function (BaseNode) {\n    function Node(srcElement, options) {\n        BaseNode.call(this, srcElement);\n        this.definitions = {};\n\n        this.options = options;\n    }\n\n    if ( BaseNode ) Node.__proto__ = BaseNode;\n    Node.prototype = Object.create( BaseNode && BaseNode.prototype );\n    Node.prototype.constructor = Node;\n\n    Node.prototype.destroy = function destroy () {\n        if (this.element) {\n            this.element._kendoNode = null;\n            this.element = null;\n        }\n\n        this.clearDefinitions();\n        BaseNode.prototype.destroy.call(this);\n    };\n\n    Node.prototype.load = function load (elements, pos) {\n        var this$1 = this;\n\n        for (var i = 0; i < elements.length; i++) {\n            var srcElement = elements[i];\n            var children = srcElement.children;\n\n            var childNode = new NODE_MAP[srcElement.nodeType](srcElement, this$1.options);\n\n            if (defined(pos)) {\n                this$1.insertAt(childNode, pos);\n            } else {\n                this$1.append(childNode);\n            }\n\n            childNode.createDefinitions();\n\n            if (children && children.length > 0) {\n                childNode.load(children);\n            }\n\n            var element = this$1.element;\n            if (element) {\n                childNode.attachTo(element, pos);\n            }\n        }\n    };\n\n    Node.prototype.root = function root () {\n        var root = this;\n\n        while (root.parent) {\n            root = root.parent;\n        }\n\n        return root;\n    };\n\n    Node.prototype.attachTo = function attachTo (domElement, pos) {\n        var container = document.createElement(\"div\");\n        renderSVG(container,\n            \"<svg xmlns='\" + SVG_NS + \"' version='1.1'>\" +\n                this.render() +\n            \"</svg>\"\n        );\n\n        var element = container.firstChild.firstChild;\n        if (element) {\n            if (defined(pos)) {\n                domElement.insertBefore(element, domElement.childNodes[pos] || null);\n            } else {\n                domElement.appendChild(element);\n            }\n            this.setElement(element);\n        }\n    };\n\n    Node.prototype.setElement = function setElement (element) {\n        if (this.element) {\n            this.element._kendoNode = null;\n        }\n\n        this.element = element;\n        this.element._kendoNode = this;\n\n        var nodes = this.childNodes;\n        for (var i = 0; i < nodes.length; i++) {\n            var childElement = element.childNodes[i];\n            nodes[i].setElement(childElement);\n        }\n    };\n\n    Node.prototype.clear = function clear () {\n        this.clearDefinitions();\n\n        if (this.element) {\n            this.element.innerHTML = \"\";\n        }\n\n        var children = this.childNodes;\n        for (var i = 0; i < children.length; i++) {\n            children[i].destroy();\n        }\n\n        this.childNodes = [];\n    };\n\n    Node.prototype.removeSelf = function removeSelf () {\n        if (this.element) {\n            var parentNode = this.element.parentNode;\n            if (parentNode) {\n                parentNode.removeChild(this.element);\n            }\n            this.element = null;\n        }\n\n        BaseNode.prototype.removeSelf.call(this);\n    };\n\n    Node.prototype.template = function template () {\n        return this.renderChildren();\n    };\n\n    Node.prototype.render = function render () {\n        return this.template();\n    };\n\n    Node.prototype.renderChildren = function renderChildren () {\n        var nodes = this.childNodes;\n        var output = \"\";\n\n        for (var i = 0; i < nodes.length; i++) {\n            output += nodes[i].render();\n        }\n\n        return output;\n    };\n\n    Node.prototype.optionsChange = function optionsChange (e) {\n        var field = e.field;\n        var value = e.value;\n\n        if (field === \"visible\") {\n            this.css(\"display\", value ? \"\" : NONE);\n        } else if (DefinitionMap[field] && isDefinition(field, value)) {\n            this.updateDefinition(field, value);\n        } else if (field === \"opacity\") {\n            this.attr(\"opacity\", value);\n        } else if (field === \"cursor\") {\n            this.css(\"cursor\", value);\n        } else if (field === \"id\") {\n            if (value) {\n                this.attr(\"id\", value);\n            } else {\n                this.removeAttr(\"id\");\n            }\n        }\n\n        BaseNode.prototype.optionsChange.call(this, e);\n    };\n\n    Node.prototype.accessibilityOptionsChange = function accessibilityOptionsChange (e) {\n        var field = e.field;\n        var value = e.value;\n\n\n        if (field === \"role\") {\n            if (value) {\n                this.attr(\"role\", value);\n            } else {\n                this.removeAttr(\"role\");\n            }\n        } else if (field === \"ariaLabel\") {\n            if (value) {\n                this.attr(\"aria-label\", htmlEncode(value));\n            } else {\n                this.removeAttr(\"aria-label\");\n            }\n        } else if (field === \"ariaRoleDescription\") {\n            if (value) {\n                this.attr(\"aria-roledescription\", htmlEncode(value));\n            } else {\n                this.removeAttr(\"aria-roledescription\");\n            }\n        } else if (field === \"ariaChecked\") {\n            if (defined(value)) {\n                this.attr(\"aria-checked\", value);\n            } else {\n                this.removeAttr(\"aria-checked\");\n            }\n        } else if (field === \"className\") {\n            this.className(value);\n        }\n    };\n\n    Node.prototype.attr = function attr (name, value) {\n        if (this.element) {\n            this.element.setAttribute(name, value);\n        }\n    };\n\n    Node.prototype.allAttr = function allAttr (attrs) {\n        var this$1 = this;\n\n        for (var i = 0; i < attrs.length; i++) {\n            this$1.attr(attrs[i][0], attrs[i][1]);\n        }\n    };\n\n    Node.prototype.toggleAttr = function toggleAttr (name, value) {\n        if (value) {\n            this.attr(name, value);\n        } else {\n            this.removeAttr(name);\n        }\n    };\n\n    Node.prototype.css = function css (name, value) {\n        if (this.element) {\n            this.element.style[name] = value;\n        }\n    };\n\n    Node.prototype.allCss = function allCss (styles) {\n        var this$1 = this;\n\n        for (var i = 0; i < styles.length; i++) {\n            this$1.css(styles[i][0], styles[i][1]);\n        }\n    };\n\n    Node.prototype.className = function className (value) {\n        var this$1 = this;\n\n        if (this.element) {\n            (ref = this.element.classList).remove.apply(ref, this.element.classList);\n            value.split(\" \").forEach(function (item) {\n                this$1.element.classList.add(item);\n            });\n        }\n        var ref;\n    };\n\n    Node.prototype.removeAttr = function removeAttr (name) {\n        if (this.element) {\n            this.element.removeAttribute(name);\n        }\n    };\n\n    Node.prototype.mapTransform = function mapTransform (transform) {\n        var attrs = [];\n        if (transform) {\n            attrs.push([\n                TRANSFORM,\n                \"matrix(\" + transform.matrix().toString(6) + \")\"\n            ]);\n        }\n\n        return attrs;\n    };\n\n    Node.prototype.renderTransform = function renderTransform () {\n        return renderAllAttr(\n            this.mapTransform(this.srcElement.transform())\n        );\n    };\n\n    Node.prototype.transformChange = function transformChange (value) {\n        if (value) {\n            this.allAttr(this.mapTransform(value));\n        } else {\n            this.removeAttr(TRANSFORM);\n        }\n    };\n\n    Node.prototype.mapStyle = function mapStyle () {\n        var options = this.srcElement.options;\n        var style = [ [ \"cursor\", options.cursor ] ];\n\n        if (options.visible === false) {\n            style.push([ \"display\", NONE ]);\n        }\n\n        return style;\n    };\n\n    Node.prototype.renderStyle = function renderStyle$1 () {\n        return renderAttr(\"style\", renderStyle(this.mapStyle(true)));\n    };\n\n    Node.prototype.renderOpacity = function renderOpacity () {\n        return renderAttr(\"opacity\", this.srcElement.options.opacity);\n    };\n\n    Node.prototype.renderId = function renderId () {\n        return renderAttr(\"id\", this.srcElement.options.id);\n    };\n\n    Node.prototype.renderClassName = function renderClassName () {\n        return renderAttr(\"class\", this.srcElement.options.className);\n    };\n\n    Node.prototype.renderRole = function renderRole () {\n        return renderAttr(\"role\", this.srcElement.options.role);\n    };\n\n    Node.prototype.renderAriaLabel = function renderAriaLabel () {\n        var value = this.srcElement.options.ariaLabel;\n        if (value) {\n            value = htmlEncode(value);\n        }\n        return renderAttr(\"aria-label\", value);\n    };\n\n    Node.prototype.renderAriaRoleDescription = function renderAriaRoleDescription () {\n        var value = this.srcElement.options.ariaRoleDescription;\n        if (value) {\n            value = htmlEncode(value);\n        }\n        return renderAttr(\"aria-roledescription\", value);\n    };\n\n    Node.prototype.renderAriaChecked = function renderAriaChecked () {\n        return renderAttr(\"aria-checked\", this.srcElement.options.ariaChecked);\n    };\n\n    Node.prototype.createDefinitions = function createDefinitions () {\n        var srcElement = this.srcElement;\n        var definitions = this.definitions;\n        if (srcElement) {\n            var options = srcElement.options;\n            var hasDefinitions;\n\n            for (var field in DefinitionMap) {\n                var definition = options.get(field);\n                if (definition && isDefinition(field, definition)) {\n                    definitions[field] = definition;\n                    hasDefinitions = true;\n                }\n            }\n            if (hasDefinitions) {\n                this.definitionChange({\n                    action: \"add\",\n                    definitions: definitions\n                });\n            }\n        }\n    };\n\n    Node.prototype.definitionChange = function definitionChange (e) {\n        if (this.parent) {\n            this.parent.definitionChange(e);\n        }\n    };\n\n    Node.prototype.updateDefinition = function updateDefinition (type, value) {\n        var definitions = this.definitions;\n        var current = definitions[type];\n        var attr = DefinitionMap[type];\n        var definition = {};\n        if (current) {\n            definition[type] = current;\n            this.definitionChange({\n                action: \"remove\",\n                definitions: definition\n            });\n            delete definitions[type];\n        }\n\n        if (!value) {\n            if (current) {\n                this.removeAttr(attr);\n            }\n        } else {\n            definition[type] = value;\n            this.definitionChange({\n                action: \"add\",\n                definitions: definition\n            });\n            definitions[type] = value;\n            this.attr(attr, this.refUrl(value.id));\n        }\n    };\n\n    Node.prototype.clearDefinitions = function clearDefinitions () {\n        var definitions = this.definitions;\n\n        this.definitionChange({\n            action: \"remove\",\n            definitions: definitions\n        });\n        this.definitions = {};\n    };\n\n    Node.prototype.renderDefinitions = function renderDefinitions () {\n        return renderAllAttr(this.mapDefinitions());\n    };\n\n    Node.prototype.mapDefinitions = function mapDefinitions () {\n        var this$1 = this;\n\n        var definitions = this.definitions;\n        var attrs = [];\n\n        for (var field in definitions) {\n            attrs.push([ DefinitionMap[field], this$1.refUrl(definitions[field].id) ]);\n        }\n\n        return attrs;\n    };\n\n    Node.prototype.refUrl = function refUrl (id) {\n        var skipBaseHref = (this.options || {}).skipBaseHref;\n        var baseHref = this.baseUrl().replace(/'/g, \"\\\\'\");\n        var base = skipBaseHref ? '' : baseHref;\n        return (\"url(\" + base + \"#\" + id + \")\");\n    };\n\n    Node.prototype.baseUrl = function baseUrl$1 () {\n        return baseUrl();\n    };\n\n    return Node;\n}(BaseNode));\n\nexport default Node;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,mBAAmB;AACxC,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,SAASC,MAAM,EAAEC,IAAI,QAAQ,aAAa;AAC1C,SAASC,OAAO,EAAEC,UAAU,QAAQ,WAAW;AAC/C,SAASC,OAAO,QAAQ,SAAS;AACjC,SAASC,OAAO,QAAQ,mBAAmB;AAE3C,IAAIC,SAAS,GAAG,WAAW;AAC3B,IAAIC,aAAa,GAAG;EAChBC,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE;AACV,CAAC;AAED,SAASC,YAAYA,CAACC,IAAI,EAAEC,KAAK,EAAE;EAC/B,OAAOD,IAAI,KAAK,MAAM,IAAKA,IAAI,KAAK,MAAM,KAAK,CAACC,KAAK,IAAIA,KAAK,CAACC,QAAQ,KAAK,UAAU,IAAID,KAAK,CAACC,QAAQ,KAAKR,OAAO,CAAE;AAC1H;AAEA,SAASS,OAAOA,CAAA,EAAG;EACf,IAAIC,IAAI,GAAGC,QAAQ,CAACC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;EACnD,IAAIC,IAAI,GAAGF,QAAQ,CAACG,QAAQ,CAACD,IAAI;EACjC,IAAIE,GAAG,GAAG,EAAE;EAEZ,IAAIL,IAAI,IAAI,CAAC,CAACb,OAAO,CAACmB,OAAO,IAAI,CAAC,CAAC,EAAEC,IAAI,EAAE;IACvC,IAAIC,SAAS,GAAGL,IAAI,CAACM,OAAO,CAAC,GAAG,CAAC;IACjC,IAAID,SAAS,KAAK,CAAC,CAAC,EAAE;MAClBL,IAAI,GAAGA,IAAI,CAACO,SAAS,CAAC,CAAC,EAAEF,SAAS,CAAC;IACvC;IAEAH,GAAG,GAAGF,IAAI;EACd;EAEA,OAAOE,GAAG;AACd;AAEA,IAAIM,IAAI,GAAI,UAAUhC,QAAQ,EAAE;EAC5B,SAASgC,IAAIA,CAACC,UAAU,EAAEC,OAAO,EAAE;IAC/BlC,QAAQ,CAACmC,IAAI,CAAC,IAAI,EAAEF,UAAU,CAAC;IAC/B,IAAI,CAACG,WAAW,GAAG,CAAC,CAAC;IAErB,IAAI,CAACF,OAAO,GAAGA,OAAO;EAC1B;EAEA,IAAKlC,QAAQ,EAAGgC,IAAI,CAACK,SAAS,GAAGrC,QAAQ;EACzCgC,IAAI,CAACM,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAExC,QAAQ,IAAIA,QAAQ,CAACsC,SAAU,CAAC;EAChEN,IAAI,CAACM,SAAS,CAACG,WAAW,GAAGT,IAAI;EAEjCA,IAAI,CAACM,SAAS,CAACI,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;IACzC,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,UAAU,GAAG,IAAI;MAC9B,IAAI,CAACD,OAAO,GAAG,IAAI;IACvB;IAEA,IAAI,CAACE,gBAAgB,CAAC,CAAC;IACvB7C,QAAQ,CAACsC,SAAS,CAACI,OAAO,CAACP,IAAI,CAAC,IAAI,CAAC;EACzC,CAAC;EAEDH,IAAI,CAACM,SAAS,CAACQ,IAAI,GAAG,SAASA,IAAIA,CAAEC,QAAQ,EAAEC,GAAG,EAAE;IAChD,IAAIC,MAAM,GAAG,IAAI;IAEjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,QAAQ,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MACtC,IAAIjB,UAAU,GAAGc,QAAQ,CAACG,CAAC,CAAC;MAC5B,IAAIE,QAAQ,GAAGnB,UAAU,CAACmB,QAAQ;MAElC,IAAIC,SAAS,GAAG,IAAIjD,QAAQ,CAAC6B,UAAU,CAACd,QAAQ,CAAC,CAACc,UAAU,EAAEgB,MAAM,CAACf,OAAO,CAAC;MAE7E,IAAIxB,OAAO,CAACsC,GAAG,CAAC,EAAE;QACdC,MAAM,CAACK,QAAQ,CAACD,SAAS,EAAEL,GAAG,CAAC;MACnC,CAAC,MAAM;QACHC,MAAM,CAACM,MAAM,CAACF,SAAS,CAAC;MAC5B;MAEAA,SAAS,CAACG,iBAAiB,CAAC,CAAC;MAE7B,IAAIJ,QAAQ,IAAIA,QAAQ,CAACD,MAAM,GAAG,CAAC,EAAE;QACjCE,SAAS,CAACP,IAAI,CAACM,QAAQ,CAAC;MAC5B;MAEA,IAAIT,OAAO,GAAGM,MAAM,CAACN,OAAO;MAC5B,IAAIA,OAAO,EAAE;QACTU,SAAS,CAACI,QAAQ,CAACd,OAAO,EAAEK,GAAG,CAAC;MACpC;IACJ;EACJ,CAAC;EAEDhB,IAAI,CAACM,SAAS,CAACoB,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAI;IACnC,IAAIA,IAAI,GAAG,IAAI;IAEf,OAAOA,IAAI,CAACC,MAAM,EAAE;MAChBD,IAAI,GAAGA,IAAI,CAACC,MAAM;IACtB;IAEA,OAAOD,IAAI;EACf,CAAC;EAED1B,IAAI,CAACM,SAAS,CAACmB,QAAQ,GAAG,SAASA,QAAQA,CAAEG,UAAU,EAAEZ,GAAG,EAAE;IAC1D,IAAIa,SAAS,GAAGvC,QAAQ,CAACwC,aAAa,CAAC,KAAK,CAAC;IAC7CzD,SAAS,CAACwD,SAAS,EACf,cAAc,GAAGvD,MAAM,GAAG,kBAAkB,GACxC,IAAI,CAACyD,MAAM,CAAC,CAAC,GACjB,QACJ,CAAC;IAED,IAAIpB,OAAO,GAAGkB,SAAS,CAACG,UAAU,CAACA,UAAU;IAC7C,IAAIrB,OAAO,EAAE;MACT,IAAIjC,OAAO,CAACsC,GAAG,CAAC,EAAE;QACdY,UAAU,CAACK,YAAY,CAACtB,OAAO,EAAEiB,UAAU,CAACM,UAAU,CAAClB,GAAG,CAAC,IAAI,IAAI,CAAC;MACxE,CAAC,MAAM;QACHY,UAAU,CAACO,WAAW,CAACxB,OAAO,CAAC;MACnC;MACA,IAAI,CAACyB,UAAU,CAACzB,OAAO,CAAC;IAC5B;EACJ,CAAC;EAEDX,IAAI,CAACM,SAAS,CAAC8B,UAAU,GAAG,SAASA,UAAUA,CAAEzB,OAAO,EAAE;IACtD,IAAI,IAAI,CAACA,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,UAAU,GAAG,IAAI;IAClC;IAEA,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACA,OAAO,CAACC,UAAU,GAAG,IAAI;IAE9B,IAAIyB,KAAK,GAAG,IAAI,CAACH,UAAU;IAC3B,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,KAAK,CAAClB,MAAM,EAAED,CAAC,EAAE,EAAE;MACnC,IAAIoB,YAAY,GAAG3B,OAAO,CAACuB,UAAU,CAAChB,CAAC,CAAC;MACxCmB,KAAK,CAACnB,CAAC,CAAC,CAACkB,UAAU,CAACE,YAAY,CAAC;IACrC;EACJ,CAAC;EAEDtC,IAAI,CAACM,SAAS,CAACiC,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAI;IACrC,IAAI,CAAC1B,gBAAgB,CAAC,CAAC;IAEvB,IAAI,IAAI,CAACF,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAAC6B,SAAS,GAAG,EAAE;IAC/B;IAEA,IAAIpB,QAAQ,GAAG,IAAI,CAACc,UAAU;IAC9B,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,QAAQ,CAACD,MAAM,EAAED,CAAC,EAAE,EAAE;MACtCE,QAAQ,CAACF,CAAC,CAAC,CAACR,OAAO,CAAC,CAAC;IACzB;IAEA,IAAI,CAACwB,UAAU,GAAG,EAAE;EACxB,CAAC;EAEDlC,IAAI,CAACM,SAAS,CAACmC,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAI;IAC/C,IAAI,IAAI,CAAC9B,OAAO,EAAE;MACd,IAAI+B,UAAU,GAAG,IAAI,CAAC/B,OAAO,CAAC+B,UAAU;MACxC,IAAIA,UAAU,EAAE;QACZA,UAAU,CAACC,WAAW,CAAC,IAAI,CAAChC,OAAO,CAAC;MACxC;MACA,IAAI,CAACA,OAAO,GAAG,IAAI;IACvB;IAEA3C,QAAQ,CAACsC,SAAS,CAACmC,UAAU,CAACtC,IAAI,CAAC,IAAI,CAAC;EAC5C,CAAC;EAEDH,IAAI,CAACM,SAAS,CAACsC,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;IAC3C,OAAO,IAAI,CAACC,cAAc,CAAC,CAAC;EAChC,CAAC;EAED7C,IAAI,CAACM,SAAS,CAACyB,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAI;IACvC,OAAO,IAAI,CAACa,QAAQ,CAAC,CAAC;EAC1B,CAAC;EAED5C,IAAI,CAACM,SAAS,CAACuC,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAI;IACvD,IAAIR,KAAK,GAAG,IAAI,CAACH,UAAU;IAC3B,IAAIY,MAAM,GAAG,EAAE;IAEf,KAAK,IAAI5B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,KAAK,CAAClB,MAAM,EAAED,CAAC,EAAE,EAAE;MACnC4B,MAAM,IAAIT,KAAK,CAACnB,CAAC,CAAC,CAACa,MAAM,CAAC,CAAC;IAC/B;IAEA,OAAOe,MAAM;EACjB,CAAC;EAED9C,IAAI,CAACM,SAAS,CAACyC,aAAa,GAAG,SAASA,aAAaA,CAAEC,CAAC,EAAE;IACtD,IAAIC,KAAK,GAAGD,CAAC,CAACC,KAAK;IACnB,IAAI/D,KAAK,GAAG8D,CAAC,CAAC9D,KAAK;IAEnB,IAAI+D,KAAK,KAAK,SAAS,EAAE;MACrB,IAAI,CAACC,GAAG,CAAC,SAAS,EAAEhE,KAAK,GAAG,EAAE,GAAGX,IAAI,CAAC;IAC1C,CAAC,MAAM,IAAIM,aAAa,CAACoE,KAAK,CAAC,IAAIjE,YAAY,CAACiE,KAAK,EAAE/D,KAAK,CAAC,EAAE;MAC3D,IAAI,CAACiE,gBAAgB,CAACF,KAAK,EAAE/D,KAAK,CAAC;IACvC,CAAC,MAAM,IAAI+D,KAAK,KAAK,SAAS,EAAE;MAC5B,IAAI,CAACG,IAAI,CAAC,SAAS,EAAElE,KAAK,CAAC;IAC/B,CAAC,MAAM,IAAI+D,KAAK,KAAK,QAAQ,EAAE;MAC3B,IAAI,CAACC,GAAG,CAAC,QAAQ,EAAEhE,KAAK,CAAC;IAC7B,CAAC,MAAM,IAAI+D,KAAK,KAAK,IAAI,EAAE;MACvB,IAAI/D,KAAK,EAAE;QACP,IAAI,CAACkE,IAAI,CAAC,IAAI,EAAElE,KAAK,CAAC;MAC1B,CAAC,MAAM;QACH,IAAI,CAACmE,UAAU,CAAC,IAAI,CAAC;MACzB;IACJ;IAEArF,QAAQ,CAACsC,SAAS,CAACyC,aAAa,CAAC5C,IAAI,CAAC,IAAI,EAAE6C,CAAC,CAAC;EAClD,CAAC;EAEDhD,IAAI,CAACM,SAAS,CAACgD,0BAA0B,GAAG,SAASA,0BAA0BA,CAAEN,CAAC,EAAE;IAChF,IAAIC,KAAK,GAAGD,CAAC,CAACC,KAAK;IACnB,IAAI/D,KAAK,GAAG8D,CAAC,CAAC9D,KAAK;IAGnB,IAAI+D,KAAK,KAAK,MAAM,EAAE;MAClB,IAAI/D,KAAK,EAAE;QACP,IAAI,CAACkE,IAAI,CAAC,MAAM,EAAElE,KAAK,CAAC;MAC5B,CAAC,MAAM;QACH,IAAI,CAACmE,UAAU,CAAC,MAAM,CAAC;MAC3B;IACJ,CAAC,MAAM,IAAIJ,KAAK,KAAK,WAAW,EAAE;MAC9B,IAAI/D,KAAK,EAAE;QACP,IAAI,CAACkE,IAAI,CAAC,YAAY,EAAE3E,UAAU,CAACS,KAAK,CAAC,CAAC;MAC9C,CAAC,MAAM;QACH,IAAI,CAACmE,UAAU,CAAC,YAAY,CAAC;MACjC;IACJ,CAAC,MAAM,IAAIJ,KAAK,KAAK,qBAAqB,EAAE;MACxC,IAAI/D,KAAK,EAAE;QACP,IAAI,CAACkE,IAAI,CAAC,sBAAsB,EAAE3E,UAAU,CAACS,KAAK,CAAC,CAAC;MACxD,CAAC,MAAM;QACH,IAAI,CAACmE,UAAU,CAAC,sBAAsB,CAAC;MAC3C;IACJ,CAAC,MAAM,IAAIJ,KAAK,KAAK,aAAa,EAAE;MAChC,IAAIvE,OAAO,CAACQ,KAAK,CAAC,EAAE;QAChB,IAAI,CAACkE,IAAI,CAAC,cAAc,EAAElE,KAAK,CAAC;MACpC,CAAC,MAAM;QACH,IAAI,CAACmE,UAAU,CAAC,cAAc,CAAC;MACnC;IACJ,CAAC,MAAM,IAAIJ,KAAK,KAAK,WAAW,EAAE;MAC9B,IAAI,CAACM,SAAS,CAACrE,KAAK,CAAC;IACzB;EACJ,CAAC;EAEDc,IAAI,CAACM,SAAS,CAAC8C,IAAI,GAAG,SAASA,IAAIA,CAAEI,IAAI,EAAEtE,KAAK,EAAE;IAC9C,IAAI,IAAI,CAACyB,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAAC8C,YAAY,CAACD,IAAI,EAAEtE,KAAK,CAAC;IAC1C;EACJ,CAAC;EAEDc,IAAI,CAACM,SAAS,CAACoD,OAAO,GAAG,SAASA,OAAOA,CAAEC,KAAK,EAAE;IAC9C,IAAI1C,MAAM,GAAG,IAAI;IAEjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyC,KAAK,CAACxC,MAAM,EAAED,CAAC,EAAE,EAAE;MACnCD,MAAM,CAACmC,IAAI,CAACO,KAAK,CAACzC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEyC,KAAK,CAACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC;EACJ,CAAC;EAEDlB,IAAI,CAACM,SAAS,CAACsD,UAAU,GAAG,SAASA,UAAUA,CAAEJ,IAAI,EAAEtE,KAAK,EAAE;IAC1D,IAAIA,KAAK,EAAE;MACP,IAAI,CAACkE,IAAI,CAACI,IAAI,EAAEtE,KAAK,CAAC;IAC1B,CAAC,MAAM;MACH,IAAI,CAACmE,UAAU,CAACG,IAAI,CAAC;IACzB;EACJ,CAAC;EAEDxD,IAAI,CAACM,SAAS,CAAC4C,GAAG,GAAG,SAASA,GAAGA,CAAEM,IAAI,EAAEtE,KAAK,EAAE;IAC5C,IAAI,IAAI,CAACyB,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACkD,KAAK,CAACL,IAAI,CAAC,GAAGtE,KAAK;IACpC;EACJ,CAAC;EAEDc,IAAI,CAACM,SAAS,CAACwD,MAAM,GAAG,SAASA,MAAMA,CAAEC,MAAM,EAAE;IAC7C,IAAI9C,MAAM,GAAG,IAAI;IAEjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6C,MAAM,CAAC5C,MAAM,EAAED,CAAC,EAAE,EAAE;MACpCD,MAAM,CAACiC,GAAG,CAACa,MAAM,CAAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE6C,MAAM,CAAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C;EACJ,CAAC;EAEDlB,IAAI,CAACM,SAAS,CAACiD,SAAS,GAAG,SAASA,SAASA,CAAErE,KAAK,EAAE;IAClD,IAAI+B,MAAM,GAAG,IAAI;IAEjB,IAAI,IAAI,CAACN,OAAO,EAAE;MACd,CAACqD,GAAG,GAAG,IAAI,CAACrD,OAAO,CAACsD,SAAS,EAAEC,MAAM,CAACC,KAAK,CAACH,GAAG,EAAE,IAAI,CAACrD,OAAO,CAACsD,SAAS,CAAC;MACxE/E,KAAK,CAACkF,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAC,UAAUC,IAAI,EAAE;QACrCrD,MAAM,CAACN,OAAO,CAACsD,SAAS,CAACM,GAAG,CAACD,IAAI,CAAC;MACtC,CAAC,CAAC;IACN;IACA,IAAIN,GAAG;EACX,CAAC;EAEDhE,IAAI,CAACM,SAAS,CAAC+C,UAAU,GAAG,SAASA,UAAUA,CAAEG,IAAI,EAAE;IACnD,IAAI,IAAI,CAAC7C,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAAC6D,eAAe,CAAChB,IAAI,CAAC;IACtC;EACJ,CAAC;EAEDxD,IAAI,CAACM,SAAS,CAACmE,YAAY,GAAG,SAASA,YAAYA,CAAEC,SAAS,EAAE;IAC5D,IAAIf,KAAK,GAAG,EAAE;IACd,IAAIe,SAAS,EAAE;MACXf,KAAK,CAACgB,IAAI,CAAC,CACP/F,SAAS,EACT,SAAS,GAAG8F,SAAS,CAACE,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,CACnD,CAAC;IACN;IAEA,OAAOlB,KAAK;EAChB,CAAC;EAED3D,IAAI,CAACM,SAAS,CAACwE,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAI;IACzD,OAAO7G,aAAa,CAChB,IAAI,CAACwG,YAAY,CAAC,IAAI,CAACxE,UAAU,CAACyE,SAAS,CAAC,CAAC,CACjD,CAAC;EACL,CAAC;EAED1E,IAAI,CAACM,SAAS,CAACyE,eAAe,GAAG,SAASA,eAAeA,CAAE7F,KAAK,EAAE;IAC9D,IAAIA,KAAK,EAAE;MACP,IAAI,CAACwE,OAAO,CAAC,IAAI,CAACe,YAAY,CAACvF,KAAK,CAAC,CAAC;IAC1C,CAAC,MAAM;MACH,IAAI,CAACmE,UAAU,CAACzE,SAAS,CAAC;IAC9B;EACJ,CAAC;EAEDoB,IAAI,CAACM,SAAS,CAAC0E,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;IAC3C,IAAI9E,OAAO,GAAG,IAAI,CAACD,UAAU,CAACC,OAAO;IACrC,IAAI2D,KAAK,GAAG,CAAE,CAAE,QAAQ,EAAE3D,OAAO,CAAC+E,MAAM,CAAE,CAAE;IAE5C,IAAI/E,OAAO,CAACgF,OAAO,KAAK,KAAK,EAAE;MAC3BrB,KAAK,CAACc,IAAI,CAAC,CAAE,SAAS,EAAEpG,IAAI,CAAE,CAAC;IACnC;IAEA,OAAOsF,KAAK;EAChB,CAAC;EAED7D,IAAI,CAACM,SAAS,CAACnC,WAAW,GAAG,SAASgH,aAAaA,CAAA,EAAI;IACnD,OAAOjH,UAAU,CAAC,OAAO,EAAEC,WAAW,CAAC,IAAI,CAAC6G,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAChE,CAAC;EAEDhF,IAAI,CAACM,SAAS,CAAC8E,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAI;IACrD,OAAOlH,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC+B,UAAU,CAACC,OAAO,CAACmF,OAAO,CAAC;EACjE,CAAC;EAEDrF,IAAI,CAACM,SAAS,CAACgF,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;IAC3C,OAAOpH,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC+B,UAAU,CAACC,OAAO,CAACqF,EAAE,CAAC;EACvD,CAAC;EAEDvF,IAAI,CAACM,SAAS,CAACkF,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAI;IACzD,OAAOtH,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC+B,UAAU,CAACC,OAAO,CAACqD,SAAS,CAAC;EACjE,CAAC;EAEDvD,IAAI,CAACM,SAAS,CAACmF,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAI;IAC/C,OAAOvH,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC+B,UAAU,CAACC,OAAO,CAACwF,IAAI,CAAC;EAC3D,CAAC;EAED1F,IAAI,CAACM,SAAS,CAACqF,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAI;IACzD,IAAIzG,KAAK,GAAG,IAAI,CAACe,UAAU,CAACC,OAAO,CAAC0F,SAAS;IAC7C,IAAI1G,KAAK,EAAE;MACPA,KAAK,GAAGT,UAAU,CAACS,KAAK,CAAC;IAC7B;IACA,OAAOhB,UAAU,CAAC,YAAY,EAAEgB,KAAK,CAAC;EAC1C,CAAC;EAEDc,IAAI,CAACM,SAAS,CAACuF,yBAAyB,GAAG,SAASA,yBAAyBA,CAAA,EAAI;IAC7E,IAAI3G,KAAK,GAAG,IAAI,CAACe,UAAU,CAACC,OAAO,CAAC4F,mBAAmB;IACvD,IAAI5G,KAAK,EAAE;MACPA,KAAK,GAAGT,UAAU,CAACS,KAAK,CAAC;IAC7B;IACA,OAAOhB,UAAU,CAAC,sBAAsB,EAAEgB,KAAK,CAAC;EACpD,CAAC;EAEDc,IAAI,CAACM,SAAS,CAACyF,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAI;IAC7D,OAAO7H,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC+B,UAAU,CAACC,OAAO,CAAC8F,WAAW,CAAC;EAC1E,CAAC;EAEDhG,IAAI,CAACM,SAAS,CAACkB,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAI;IAC7D,IAAIvB,UAAU,GAAG,IAAI,CAACA,UAAU;IAChC,IAAIG,WAAW,GAAG,IAAI,CAACA,WAAW;IAClC,IAAIH,UAAU,EAAE;MACZ,IAAIC,OAAO,GAAGD,UAAU,CAACC,OAAO;MAChC,IAAI+F,cAAc;MAElB,KAAK,IAAIhD,KAAK,IAAIpE,aAAa,EAAE;QAC7B,IAAIqH,UAAU,GAAGhG,OAAO,CAACiG,GAAG,CAAClD,KAAK,CAAC;QACnC,IAAIiD,UAAU,IAAIlH,YAAY,CAACiE,KAAK,EAAEiD,UAAU,CAAC,EAAE;UAC/C9F,WAAW,CAAC6C,KAAK,CAAC,GAAGiD,UAAU;UAC/BD,cAAc,GAAG,IAAI;QACzB;MACJ;MACA,IAAIA,cAAc,EAAE;QAChB,IAAI,CAACG,gBAAgB,CAAC;UAClBC,MAAM,EAAE,KAAK;UACbjG,WAAW,EAAEA;QACjB,CAAC,CAAC;MACN;IACJ;EACJ,CAAC;EAEDJ,IAAI,CAACM,SAAS,CAAC8F,gBAAgB,GAAG,SAASA,gBAAgBA,CAAEpD,CAAC,EAAE;IAC5D,IAAI,IAAI,CAACrB,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAACyE,gBAAgB,CAACpD,CAAC,CAAC;IACnC;EACJ,CAAC;EAEDhD,IAAI,CAACM,SAAS,CAAC6C,gBAAgB,GAAG,SAASA,gBAAgBA,CAAElE,IAAI,EAAEC,KAAK,EAAE;IACtE,IAAIkB,WAAW,GAAG,IAAI,CAACA,WAAW;IAClC,IAAIkG,OAAO,GAAGlG,WAAW,CAACnB,IAAI,CAAC;IAC/B,IAAImE,IAAI,GAAGvE,aAAa,CAACI,IAAI,CAAC;IAC9B,IAAIiH,UAAU,GAAG,CAAC,CAAC;IACnB,IAAII,OAAO,EAAE;MACTJ,UAAU,CAACjH,IAAI,CAAC,GAAGqH,OAAO;MAC1B,IAAI,CAACF,gBAAgB,CAAC;QAClBC,MAAM,EAAE,QAAQ;QAChBjG,WAAW,EAAE8F;MACjB,CAAC,CAAC;MACF,OAAO9F,WAAW,CAACnB,IAAI,CAAC;IAC5B;IAEA,IAAI,CAACC,KAAK,EAAE;MACR,IAAIoH,OAAO,EAAE;QACT,IAAI,CAACjD,UAAU,CAACD,IAAI,CAAC;MACzB;IACJ,CAAC,MAAM;MACH8C,UAAU,CAACjH,IAAI,CAAC,GAAGC,KAAK;MACxB,IAAI,CAACkH,gBAAgB,CAAC;QAClBC,MAAM,EAAE,KAAK;QACbjG,WAAW,EAAE8F;MACjB,CAAC,CAAC;MACF9F,WAAW,CAACnB,IAAI,CAAC,GAAGC,KAAK;MACzB,IAAI,CAACkE,IAAI,CAACA,IAAI,EAAE,IAAI,CAACmD,MAAM,CAACrH,KAAK,CAACqG,EAAE,CAAC,CAAC;IAC1C;EACJ,CAAC;EAEDvF,IAAI,CAACM,SAAS,CAACO,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAI;IAC3D,IAAIT,WAAW,GAAG,IAAI,CAACA,WAAW;IAElC,IAAI,CAACgG,gBAAgB,CAAC;MAClBC,MAAM,EAAE,QAAQ;MAChBjG,WAAW,EAAEA;IACjB,CAAC,CAAC;IACF,IAAI,CAACA,WAAW,GAAG,CAAC,CAAC;EACzB,CAAC;EAEDJ,IAAI,CAACM,SAAS,CAACkG,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAI;IAC7D,OAAOvI,aAAa,CAAC,IAAI,CAACwI,cAAc,CAAC,CAAC,CAAC;EAC/C,CAAC;EAEDzG,IAAI,CAACM,SAAS,CAACmG,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAI;IACvD,IAAIxF,MAAM,GAAG,IAAI;IAEjB,IAAIb,WAAW,GAAG,IAAI,CAACA,WAAW;IAClC,IAAIuD,KAAK,GAAG,EAAE;IAEd,KAAK,IAAIV,KAAK,IAAI7C,WAAW,EAAE;MAC3BuD,KAAK,CAACgB,IAAI,CAAC,CAAE9F,aAAa,CAACoE,KAAK,CAAC,EAAEhC,MAAM,CAACsF,MAAM,CAACnG,WAAW,CAAC6C,KAAK,CAAC,CAACsC,EAAE,CAAC,CAAE,CAAC;IAC9E;IAEA,OAAO5B,KAAK;EAChB,CAAC;EAED3D,IAAI,CAACM,SAAS,CAACiG,MAAM,GAAG,SAASA,MAAMA,CAAEhB,EAAE,EAAE;IACzC,IAAImB,YAAY,GAAG,CAAC,IAAI,CAACxG,OAAO,IAAI,CAAC,CAAC,EAAEwG,YAAY;IACpD,IAAIC,QAAQ,GAAG,IAAI,CAACvH,OAAO,CAAC,CAAC,CAACwH,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;IAClD,IAAIvH,IAAI,GAAGqH,YAAY,GAAG,EAAE,GAAGC,QAAQ;IACvC,OAAQ,MAAM,GAAGtH,IAAI,GAAG,GAAG,GAAGkG,EAAE,GAAG,GAAG;EAC1C,CAAC;EAEDvF,IAAI,CAACM,SAAS,CAAClB,OAAO,GAAG,SAASyH,SAASA,CAAA,EAAI;IAC3C,OAAOzH,OAAO,CAAC,CAAC;EACpB,CAAC;EAED,OAAOY,IAAI;AACf,CAAC,CAAChC,QAAQ,CAAE;AAEZ,eAAegC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}