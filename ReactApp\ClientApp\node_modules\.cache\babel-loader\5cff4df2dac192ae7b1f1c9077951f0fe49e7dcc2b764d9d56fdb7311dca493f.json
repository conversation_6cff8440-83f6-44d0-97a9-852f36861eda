{"ast": null, "code": "export default function last(array) {\n  if (array) {\n    return array[array.length - 1];\n  }\n}", "map": {"version": 3, "names": ["last", "array", "length"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/util/last.js"], "sourcesContent": ["export default function last(array) {\n    if (array) {\n        return array[array.length - 1];\n    }\n}"], "mappings": "AAAA,eAAe,SAASA,IAAIA,CAACC,KAAK,EAAE;EAChC,IAAIA,KAAK,EAAE;IACP,OAAOA,KAAK,CAACA,KAAK,CAACC,MAAM,GAAG,CAAC,CAAC;EAClC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}