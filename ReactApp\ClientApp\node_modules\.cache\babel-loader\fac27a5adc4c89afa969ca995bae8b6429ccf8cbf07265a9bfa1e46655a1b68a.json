{"ast": null, "code": "import PathNode from './path-node';\nimport renderAllAttr from './utils/render-all-attributes';\nimport { htmlEncode } from '../common';\nvar ImageNode = function (PathNode) {\n  function ImageNode() {\n    PathNode.apply(this, arguments);\n  }\n  if (PathNode) ImageNode.__proto__ = PathNode;\n  ImageNode.prototype = Object.create(PathNode && PathNode.prototype);\n  ImageNode.prototype.constructor = ImageNode;\n  ImageNode.prototype.geometryChange = function geometryChange() {\n    this.allAttr(this.mapPosition());\n    this.invalidate();\n  };\n  ImageNode.prototype.optionsChange = function optionsChange(e) {\n    if (e.field === \"src\") {\n      this.allAttr(this.mapSource());\n    }\n    PathNode.prototype.optionsChange.call(this, e);\n  };\n  ImageNode.prototype.mapPosition = function mapPosition() {\n    var rect = this.srcElement.rect();\n    var tl = rect.topLeft();\n    return [[\"x\", tl.x], [\"y\", tl.y], [\"width\", rect.width() + \"px\"], [\"height\", rect.height() + \"px\"]];\n  };\n  ImageNode.prototype.renderPosition = function renderPosition() {\n    return renderAllAttr(this.mapPosition());\n  };\n  ImageNode.prototype.mapSource = function mapSource(encode) {\n    var src = this.srcElement.src();\n    if (encode) {\n      src = htmlEncode(src);\n    }\n    return [[\"xlink:href\", src]];\n  };\n  ImageNode.prototype.renderSource = function renderSource() {\n    return renderAllAttr(this.mapSource(true));\n  };\n  ImageNode.prototype.template = function template() {\n    return \"<image preserveAspectRatio='none' \" + this.renderId() + \" \" + this.renderStyle() + \" \" + this.renderTransform() + \" \" + this.renderOpacity() + this.renderPosition() + \" \" + this.renderSource() + \" \" + this.renderDefinitions() + this.renderClassName() + \" \" + this.renderRole() + this.renderAriaLabel() + \" \" + this.renderAriaRoleDescription() + this.renderAriaChecked() + \" >\" + \"</image>\";\n  };\n  return ImageNode;\n}(PathNode);\nexport default ImageNode;", "map": {"version": 3, "names": ["PathNode", "renderAllAttr", "htmlEncode", "ImageNode", "apply", "arguments", "__proto__", "prototype", "Object", "create", "constructor", "geometryChange", "allAttr", "mapPosition", "invalidate", "optionsChange", "e", "field", "mapSource", "call", "rect", "srcElement", "tl", "topLeft", "x", "y", "width", "height", "renderPosition", "encode", "src", "renderSource", "template", "renderId", "renderStyle", "renderTransform", "renderOpacity", "renderDefinitions", "renderClassName", "renderRole", "renderAriaLabel", "renderAriaRoleDescription", "renderAriaChecked"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/svg/image-node.js"], "sourcesContent": ["import PathNode from './path-node';\nimport renderAllAttr from './utils/render-all-attributes';\nimport { htmlEncode } from '../common';\n\nvar ImageNode = (function (PathNode) {\n    function ImageNode () {\n        PathNode.apply(this, arguments);\n    }\n\n    if ( PathNode ) ImageNode.__proto__ = PathNode;\n    ImageNode.prototype = Object.create( PathNode && PathNode.prototype );\n    ImageNode.prototype.constructor = ImageNode;\n\n    ImageNode.prototype.geometryChange = function geometryChange () {\n        this.allAttr(this.mapPosition());\n        this.invalidate();\n    };\n\n    ImageNode.prototype.optionsChange = function optionsChange (e) {\n        if (e.field === \"src\") {\n            this.allAttr(this.mapSource());\n        }\n\n        PathNode.prototype.optionsChange.call(this, e);\n    };\n\n    ImageNode.prototype.mapPosition = function mapPosition () {\n        var rect = this.srcElement.rect();\n        var tl = rect.topLeft();\n\n        return [\n            [ \"x\", tl.x ],\n            [ \"y\", tl.y ],\n            [ \"width\", rect.width() + \"px\" ],\n            [ \"height\", rect.height() + \"px\" ]\n        ];\n    };\n\n    ImageNode.prototype.renderPosition = function renderPosition () {\n        return renderAllAttr(this.mapPosition());\n    };\n\n    ImageNode.prototype.mapSource = function mapSource (encode) {\n        var src = this.srcElement.src();\n\n        if (encode) {\n            src = htmlEncode(src);\n        }\n\n        return [ [ \"xlink:href\", src ] ];\n    };\n\n    ImageNode.prototype.renderSource = function renderSource () {\n        return renderAllAttr(this.mapSource(true));\n    };\n\n    ImageNode.prototype.template = function template () {\n        return \"<image preserveAspectRatio='none' \" + (this.renderId()) + \" \" + (this.renderStyle()) + \" \" + (this.renderTransform()) + \" \" + (this.renderOpacity()) +\n               (this.renderPosition()) + \" \" + (this.renderSource()) + \" \" + (this.renderDefinitions()) +\n               (this.renderClassName()) + \" \" + (this.renderRole()) +\n                (this.renderAriaLabel()) + \" \" + (this.renderAriaRoleDescription()) +\n                (this.renderAriaChecked()) + \" >\" +\n               \"</image>\";\n    };\n\n    return ImageNode;\n}(PathNode));\n\nexport default ImageNode;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,aAAa;AAClC,OAAOC,aAAa,MAAM,+BAA+B;AACzD,SAASC,UAAU,QAAQ,WAAW;AAEtC,IAAIC,SAAS,GAAI,UAAUH,QAAQ,EAAE;EACjC,SAASG,SAASA,CAAA,EAAI;IAClBH,QAAQ,CAACI,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACnC;EAEA,IAAKL,QAAQ,EAAGG,SAAS,CAACG,SAAS,GAAGN,QAAQ;EAC9CG,SAAS,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAET,QAAQ,IAAIA,QAAQ,CAACO,SAAU,CAAC;EACrEJ,SAAS,CAACI,SAAS,CAACG,WAAW,GAAGP,SAAS;EAE3CA,SAAS,CAACI,SAAS,CAACI,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAI;IAC5D,IAAI,CAACC,OAAO,CAAC,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC;IAChC,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB,CAAC;EAEDX,SAAS,CAACI,SAAS,CAACQ,aAAa,GAAG,SAASA,aAAaA,CAAEC,CAAC,EAAE;IAC3D,IAAIA,CAAC,CAACC,KAAK,KAAK,KAAK,EAAE;MACnB,IAAI,CAACL,OAAO,CAAC,IAAI,CAACM,SAAS,CAAC,CAAC,CAAC;IAClC;IAEAlB,QAAQ,CAACO,SAAS,CAACQ,aAAa,CAACI,IAAI,CAAC,IAAI,EAAEH,CAAC,CAAC;EAClD,CAAC;EAEDb,SAAS,CAACI,SAAS,CAACM,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAI;IACtD,IAAIO,IAAI,GAAG,IAAI,CAACC,UAAU,CAACD,IAAI,CAAC,CAAC;IACjC,IAAIE,EAAE,GAAGF,IAAI,CAACG,OAAO,CAAC,CAAC;IAEvB,OAAO,CACH,CAAE,GAAG,EAAED,EAAE,CAACE,CAAC,CAAE,EACb,CAAE,GAAG,EAAEF,EAAE,CAACG,CAAC,CAAE,EACb,CAAE,OAAO,EAAEL,IAAI,CAACM,KAAK,CAAC,CAAC,GAAG,IAAI,CAAE,EAChC,CAAE,QAAQ,EAAEN,IAAI,CAACO,MAAM,CAAC,CAAC,GAAG,IAAI,CAAE,CACrC;EACL,CAAC;EAEDxB,SAAS,CAACI,SAAS,CAACqB,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAI;IAC5D,OAAO3B,aAAa,CAAC,IAAI,CAACY,WAAW,CAAC,CAAC,CAAC;EAC5C,CAAC;EAEDV,SAAS,CAACI,SAAS,CAACW,SAAS,GAAG,SAASA,SAASA,CAAEW,MAAM,EAAE;IACxD,IAAIC,GAAG,GAAG,IAAI,CAACT,UAAU,CAACS,GAAG,CAAC,CAAC;IAE/B,IAAID,MAAM,EAAE;MACRC,GAAG,GAAG5B,UAAU,CAAC4B,GAAG,CAAC;IACzB;IAEA,OAAO,CAAE,CAAE,YAAY,EAAEA,GAAG,CAAE,CAAE;EACpC,CAAC;EAED3B,SAAS,CAACI,SAAS,CAACwB,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAI;IACxD,OAAO9B,aAAa,CAAC,IAAI,CAACiB,SAAS,CAAC,IAAI,CAAC,CAAC;EAC9C,CAAC;EAEDf,SAAS,CAACI,SAAS,CAACyB,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;IAChD,OAAO,oCAAoC,GAAI,IAAI,CAACC,QAAQ,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAACC,WAAW,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAACC,eAAe,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAACC,aAAa,CAAC,CAAE,GACpJ,IAAI,CAACR,cAAc,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAACG,YAAY,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAACM,iBAAiB,CAAC,CAAE,GACvF,IAAI,CAACC,eAAe,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAACC,UAAU,CAAC,CAAE,GAClD,IAAI,CAACC,eAAe,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAACC,yBAAyB,CAAC,CAAE,GAClE,IAAI,CAACC,iBAAiB,CAAC,CAAE,GAAG,IAAI,GAClC,UAAU;EACrB,CAAC;EAED,OAAOvC,SAAS;AACpB,CAAC,CAACH,QAAQ,CAAE;AAEZ,eAAeG,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}