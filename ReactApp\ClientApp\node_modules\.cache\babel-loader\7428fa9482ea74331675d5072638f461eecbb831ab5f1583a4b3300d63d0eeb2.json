{"ast": null, "code": "import { adjustDST } from './adjust-dst';\nimport { cloneDate } from './clone-date';\nimport { lastDayOfMonth } from './last-day-of-month';\nvar MONTHS_LENGTH = 12;\nvar normalize = function (date, expectedMonth) {\n  return date.getMonth() !== expectedMonth ? lastDayOfMonth(addMonths(date, -1)) : date //tslint:disable-line:no-use-before-declare\n  ;\n};\n/**\n * A function that adds and subtracts months from a `Date` object.\n *\n * @param date - The initial date value.\n * @param offset - The number of months to add or subtract from the date.\n * @returns - A new `Date` instance.\n *\n * @example\n * ```ts-no-run\n * addMonths(new Date(2016, 5, 1), 5); // 2016-11-1\n * addMonths(new Date(2016, 5, 1), -5); // 2015-1-1\n * ```\n */\nexport var addMonths = function (date, offset) {\n  var newDate = cloneDate(date);\n  var diff = (newDate.getMonth() + offset) % MONTHS_LENGTH;\n  var expectedMonth = (MONTHS_LENGTH + diff) % MONTHS_LENGTH;\n  newDate.setMonth(newDate.getMonth() + offset);\n  return normalize(adjustDST(newDate, date.getHours()), expectedMonth);\n};", "map": {"version": 3, "names": ["adjustDST", "cloneDate", "lastDayOfMonth", "MONTHS_LENGTH", "normalize", "date", "expectedM<PERSON><PERSON>", "getMonth", "addMonths", "offset", "newDate", "diff", "setMonth", "getHours"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/add-months.js"], "sourcesContent": ["import { adjustDST } from './adjust-dst';\nimport { cloneDate } from './clone-date';\nimport { lastDayOfMonth } from './last-day-of-month';\nvar MONTHS_LENGTH = 12;\nvar normalize = function (date, expectedMonth) { return (date.getMonth() !== expectedMonth ? lastDayOfMonth(addMonths(date, -1)) : date //tslint:disable-line:no-use-before-declare\n); };\n/**\n * A function that adds and subtracts months from a `Date` object.\n *\n * @param date - The initial date value.\n * @param offset - The number of months to add or subtract from the date.\n * @returns - A new `Date` instance.\n *\n * @example\n * ```ts-no-run\n * addMonths(new Date(2016, 5, 1), 5); // 2016-11-1\n * addMonths(new Date(2016, 5, 1), -5); // 2015-1-1\n * ```\n */\nexport var addMonths = function (date, offset) {\n    var newDate = cloneDate(date);\n    var diff = (newDate.getMonth() + offset) % MONTHS_LENGTH;\n    var expectedMonth = (MONTHS_LENGTH + diff) % MONTHS_LENGTH;\n    newDate.setMonth(newDate.getMonth() + offset);\n    return normalize(adjustDST(newDate, date.getHours()), expectedMonth);\n};\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,cAAc;AACxC,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,cAAc,QAAQ,qBAAqB;AACpD,IAAIC,aAAa,GAAG,EAAE;AACtB,IAAIC,SAAS,GAAG,SAAAA,CAAUC,IAAI,EAAEC,aAAa,EAAE;EAAE,OAAQD,IAAI,CAACE,QAAQ,CAAC,CAAC,KAAKD,aAAa,GAAGJ,cAAc,CAACM,SAAS,CAACH,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC;EAAA;AACrI,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIG,SAAS,GAAG,SAAAA,CAAUH,IAAI,EAAEI,MAAM,EAAE;EAC3C,IAAIC,OAAO,GAAGT,SAAS,CAACI,IAAI,CAAC;EAC7B,IAAIM,IAAI,GAAG,CAACD,OAAO,CAACH,QAAQ,CAAC,CAAC,GAAGE,MAAM,IAAIN,aAAa;EACxD,IAAIG,aAAa,GAAG,CAACH,aAAa,GAAGQ,IAAI,IAAIR,aAAa;EAC1DO,OAAO,CAACE,QAAQ,CAACF,OAAO,CAACH,QAAQ,CAAC,CAAC,GAAGE,MAAM,CAAC;EAC7C,OAAOL,SAAS,CAACJ,SAAS,CAACU,OAAO,EAAEL,IAAI,CAACQ,QAAQ,CAAC,CAAC,CAAC,EAAEP,aAAa,CAAC;AACxE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}