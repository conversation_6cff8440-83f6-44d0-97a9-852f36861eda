{"ast": null, "code": "var getRatio = function (element, target) {\n  var elementRect = element.getBoundingClientRect();\n  var targetRect = target.getBoundingClientRect();\n  var top = Math.max(targetRect.top, elementRect.top);\n  var left = Math.max(targetRect.left, elementRect.left);\n  var right = Math.min(targetRect.left + targetRect.width, elementRect.left + elementRect.width);\n  var bottom = Math.min(targetRect.top + targetRect.height, elementRect.top + elementRect.height);\n  var width = right - left;\n  var height = bottom - top;\n  if (left < right && top < bottom) {\n    var targetArea = targetRect.width * targetRect.height;\n    var entryArea = elementRect.width * elementRect.height;\n    var intersectionArea = width * height;\n    var intersectionRatio = intersectionArea / (targetArea + entryArea - intersectionArea);\n    return Number(intersectionRatio.toFixed(4));\n  }\n  return 0;\n};\n/** @hidden */\nexport var intersect = function (element, candidates) {\n  var max = 0;\n  var result = null;\n  candidates.forEach(function (candidate) {\n    if (candidate && element) {\n      var ration = getRatio(element, candidate);\n      if (ration > max) {\n        max = ration;\n        result = candidate;\n      }\n    }\n  });\n  return result;\n};", "map": {"version": 3, "names": ["getRatio", "element", "target", "elementRect", "getBoundingClientRect", "targetRect", "top", "Math", "max", "left", "right", "min", "width", "bottom", "height", "targetArea", "entryArea", "intersectionArea", "intersectionRatio", "Number", "toFixed", "intersect", "candidates", "result", "for<PERSON>ach", "candidate", "ration"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-draggable-common/dist/es/algorithms/intersect.js"], "sourcesContent": ["var getRatio = function (element, target) {\n    var elementRect = element.getBoundingClientRect();\n    var targetRect = target.getBoundingClientRect();\n    var top = Math.max(targetRect.top, elementRect.top);\n    var left = Math.max(targetRect.left, elementRect.left);\n    var right = Math.min(targetRect.left + targetRect.width, elementRect.left + elementRect.width);\n    var bottom = Math.min(targetRect.top + targetRect.height, elementRect.top + elementRect.height);\n    var width = right - left;\n    var height = bottom - top;\n    if (left < right && top < bottom) {\n        var targetArea = targetRect.width * targetRect.height;\n        var entryArea = elementRect.width * elementRect.height;\n        var intersectionArea = width * height;\n        var intersectionRatio = intersectionArea / (targetArea + entryArea - intersectionArea);\n        return Number(intersectionRatio.toFixed(4));\n    }\n    return 0;\n};\n/** @hidden */\nexport var intersect = function (element, candidates) {\n    var max = 0;\n    var result = null;\n    candidates.forEach(function (candidate) {\n        if (candidate && element) {\n            var ration = getRatio(element, candidate);\n            if (ration > max) {\n                max = ration;\n                result = candidate;\n            }\n        }\n    });\n    return result;\n};\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAG,SAAAA,CAAUC,OAAO,EAAEC,MAAM,EAAE;EACtC,IAAIC,WAAW,GAAGF,OAAO,CAACG,qBAAqB,CAAC,CAAC;EACjD,IAAIC,UAAU,GAAGH,MAAM,CAACE,qBAAqB,CAAC,CAAC;EAC/C,IAAIE,GAAG,GAAGC,IAAI,CAACC,GAAG,CAACH,UAAU,CAACC,GAAG,EAAEH,WAAW,CAACG,GAAG,CAAC;EACnD,IAAIG,IAAI,GAAGF,IAAI,CAACC,GAAG,CAACH,UAAU,CAACI,IAAI,EAAEN,WAAW,CAACM,IAAI,CAAC;EACtD,IAAIC,KAAK,GAAGH,IAAI,CAACI,GAAG,CAACN,UAAU,CAACI,IAAI,GAAGJ,UAAU,CAACO,KAAK,EAAET,WAAW,CAACM,IAAI,GAAGN,WAAW,CAACS,KAAK,CAAC;EAC9F,IAAIC,MAAM,GAAGN,IAAI,CAACI,GAAG,CAACN,UAAU,CAACC,GAAG,GAAGD,UAAU,CAACS,MAAM,EAAEX,WAAW,CAACG,GAAG,GAAGH,WAAW,CAACW,MAAM,CAAC;EAC/F,IAAIF,KAAK,GAAGF,KAAK,GAAGD,IAAI;EACxB,IAAIK,MAAM,GAAGD,MAAM,GAAGP,GAAG;EACzB,IAAIG,IAAI,GAAGC,KAAK,IAAIJ,GAAG,GAAGO,MAAM,EAAE;IAC9B,IAAIE,UAAU,GAAGV,UAAU,CAACO,KAAK,GAAGP,UAAU,CAACS,MAAM;IACrD,IAAIE,SAAS,GAAGb,WAAW,CAACS,KAAK,GAAGT,WAAW,CAACW,MAAM;IACtD,IAAIG,gBAAgB,GAAGL,KAAK,GAAGE,MAAM;IACrC,IAAII,iBAAiB,GAAGD,gBAAgB,IAAIF,UAAU,GAAGC,SAAS,GAAGC,gBAAgB,CAAC;IACtF,OAAOE,MAAM,CAACD,iBAAiB,CAACE,OAAO,CAAC,CAAC,CAAC,CAAC;EAC/C;EACA,OAAO,CAAC;AACZ,CAAC;AACD;AACA,OAAO,IAAIC,SAAS,GAAG,SAAAA,CAAUpB,OAAO,EAAEqB,UAAU,EAAE;EAClD,IAAId,GAAG,GAAG,CAAC;EACX,IAAIe,MAAM,GAAG,IAAI;EACjBD,UAAU,CAACE,OAAO,CAAC,UAAUC,SAAS,EAAE;IACpC,IAAIA,SAAS,IAAIxB,OAAO,EAAE;MACtB,IAAIyB,MAAM,GAAG1B,QAAQ,CAACC,OAAO,EAAEwB,SAAS,CAAC;MACzC,IAAIC,MAAM,GAAGlB,GAAG,EAAE;QACdA,GAAG,GAAGkB,MAAM;QACZH,MAAM,GAAGE,SAAS;MACtB;IACJ;EACJ,CAAC,CAAC;EACF,OAAOF,MAAM;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}