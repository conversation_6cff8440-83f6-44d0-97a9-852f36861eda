{"ast": null, "code": "export default function addScroll(rect, scroll) {\n  return {\n    top: rect.top + scroll.y,\n    left: rect.left + scroll.x,\n    height: rect.height,\n    width: rect.width\n  };\n}", "map": {"version": 3, "names": ["addScroll", "rect", "scroll", "top", "y", "left", "x", "height", "width"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-popup-common/dist/es/add-scroll.js"], "sourcesContent": ["export default function addScroll(rect, scroll) {\n    return {\n        top: rect.top + scroll.y,\n        left: rect.left + scroll.x,\n        height: rect.height,\n        width: rect.width\n    };\n}\n"], "mappings": "AAAA,eAAe,SAASA,SAASA,CAACC,IAAI,EAAEC,MAAM,EAAE;EAC5C,OAAO;IACHC,GAAG,EAAEF,IAAI,CAACE,GAAG,GAAGD,MAAM,CAACE,CAAC;IACxBC,IAAI,EAAEJ,IAAI,CAACI,IAAI,GAAGH,MAAM,CAACI,CAAC;IAC1BC,MAAM,EAAEN,IAAI,CAACM,MAAM;IACnBC,KAAK,EAAEP,IAAI,CAACO;EAChB,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}