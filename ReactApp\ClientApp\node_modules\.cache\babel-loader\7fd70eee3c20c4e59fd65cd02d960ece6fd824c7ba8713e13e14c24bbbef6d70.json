{"ast": null, "code": "var MONTHS = {\n  Jan: 0,\n  Feb: 1,\n  Mar: 2,\n  Apr: 3,\n  May: 4,\n  Jun: 5,\n  Jul: 6,\n  Aug: 7,\n  Sep: 8,\n  Oct: 9,\n  Nov: 10,\n  Dec: 11\n};\nvar DAYS = {\n  Sun: 0,\n  Mon: 1,\n  Tue: 2,\n  Wed: 3,\n  Thu: 4,\n  Fri: 5,\n  Sat: 6\n};\nvar MS_PER_MINUTE = 60000;\n/**\n * @hidden\n *\n * A function that finds zone rules which become applicable after a specific time.\n *\n * @param year - The value of the year.\n * @param rule - A specific zone rule.\n * @param zone - The definition of the zone.\n *\n * @return - Returns an extended rule.\n *\n * @example\n * ```ts-no-run\n * ruleToDate(2018, rule); // A rule that contains {'2018': |2018 DST date| }\n * ```\n */\nexport var ruleToDate = function (year, rule, zoneOffset) {\n  var month = rule[3];\n  var on = rule[4];\n  var time = rule[5];\n  var date;\n  var ruleOffset = time[3] === 'u' ? -zoneOffset * MS_PER_MINUTE : 0;\n  if (!isNaN(on)) {\n    date = new Date(Date.UTC(year, MONTHS[month], on, time[0], time[1], time[2]) + ruleOffset);\n  } else if (on.indexOf('last') === 0) {\n    date = new Date(Date.UTC(year, MONTHS[month] + 1, 1, time[0] - 24, time[1], time[2]) + ruleOffset);\n    var targetDay = DAYS[on.substr(4, 3)];\n    var ourDay = date.getUTCDay();\n    date.setUTCDate(date.getUTCDate() + targetDay - ourDay - (targetDay > ourDay ? 7 : 0));\n  } else if (on.indexOf('>=') >= 0) {\n    date = new Date(Date.UTC(year, MONTHS[month], on.substr(5), time[0], time[1], time[2], 0) + ruleOffset);\n    var targetDay = DAYS[on.substr(0, 3)];\n    var ourDay = date.getUTCDay();\n    date.setUTCDate(date.getUTCDate() + targetDay - ourDay + (targetDay < ourDay ? 7 : 0));\n  }\n  return date;\n};", "map": {"version": 3, "names": ["MONTHS", "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec", "DAYS", "Sun", "Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>", "Sat", "MS_PER_MINUTE", "ruleToDate", "year", "rule", "zoneOffset", "month", "on", "time", "date", "ruleOffset", "isNaN", "Date", "UTC", "indexOf", "targetDay", "substr", "ourDay", "getUTCDay", "setUTCDate", "getUTCDate"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/tz/rule-to-date.js"], "sourcesContent": ["var MONTHS = { Jan: 0, Feb: 1, Mar: 2, Apr: 3, May: 4, Jun: 5, Jul: 6, Aug: 7, Sep: 8, Oct: 9, Nov: 10, Dec: 11 };\nvar DAYS = { Sun: 0, Mon: 1, Tue: 2, Wed: 3, Thu: 4, Fri: 5, Sat: 6 };\nvar MS_PER_MINUTE = 60000;\n/**\n * @hidden\n *\n * A function that finds zone rules which become applicable after a specific time.\n *\n * @param year - The value of the year.\n * @param rule - A specific zone rule.\n * @param zone - The definition of the zone.\n *\n * @return - Returns an extended rule.\n *\n * @example\n * ```ts-no-run\n * ruleToDate(2018, rule); // A rule that contains {'2018': |2018 DST date| }\n * ```\n */\nexport var ruleToDate = function (year, rule, zoneOffset) {\n    var month = rule[3];\n    var on = rule[4];\n    var time = rule[5];\n    var date;\n    var ruleOffset = time[3] === 'u' ? -zoneOffset * MS_PER_MINUTE : 0;\n    if (!isNaN(on)) {\n        date = new Date(Date.UTC(year, MONTHS[month], on, time[0], time[1], time[2]) + ruleOffset);\n    }\n    else if (on.indexOf('last') === 0) {\n        date = new Date(Date.UTC(year, MONTHS[month] + 1, 1, time[0] - 24, time[1], time[2]) + ruleOffset);\n        var targetDay = DAYS[on.substr(4, 3)];\n        var ourDay = date.getUTCDay();\n        date.setUTCDate(date.getUTCDate() + targetDay - ourDay - (targetDay > ourDay ? 7 : 0));\n    }\n    else if (on.indexOf('>=') >= 0) {\n        date = new Date(Date.UTC(year, MONTHS[month], on.substr(5), time[0], time[1], time[2], 0) + ruleOffset);\n        var targetDay = DAYS[on.substr(0, 3)];\n        var ourDay = date.getUTCDay();\n        date.setUTCDate(date.getUTCDate() + targetDay - ourDay + (targetDay < ourDay ? 7 : 0));\n    }\n    return date;\n};\n"], "mappings": "AAAA,IAAIA,MAAM,GAAG;EAAEC,GAAG,EAAE,CAAC;EAAEC,GAAG,EAAE,CAAC;EAAEC,GAAG,EAAE,CAAC;EAAEC,GAAG,EAAE,CAAC;EAAEC,GAAG,EAAE,CAAC;EAAEC,GAAG,EAAE,CAAC;EAAEC,GAAG,EAAE,CAAC;EAAEC,GAAG,EAAE,CAAC;EAAEC,GAAG,EAAE,CAAC;EAAEC,GAAG,EAAE,CAAC;EAAEC,GAAG,EAAE,EAAE;EAAEC,GAAG,EAAE;AAAG,CAAC;AACjH,IAAIC,IAAI,GAAG;EAAEC,GAAG,EAAE,CAAC;EAAEC,GAAG,EAAE,CAAC;EAAEC,GAAG,EAAE,CAAC;EAAEC,GAAG,EAAE,CAAC;EAAEC,GAAG,EAAE,CAAC;EAAEC,GAAG,EAAE,CAAC;EAAEC,GAAG,EAAE;AAAE,CAAC;AACrE,IAAIC,aAAa,GAAG,KAAK;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,UAAU,GAAG,SAAAA,CAAUC,IAAI,EAAEC,IAAI,EAAEC,UAAU,EAAE;EACtD,IAAIC,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC;EACnB,IAAIG,EAAE,GAAGH,IAAI,CAAC,CAAC,CAAC;EAChB,IAAII,IAAI,GAAGJ,IAAI,CAAC,CAAC,CAAC;EAClB,IAAIK,IAAI;EACR,IAAIC,UAAU,GAAGF,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,CAACH,UAAU,GAAGJ,aAAa,GAAG,CAAC;EAClE,IAAI,CAACU,KAAK,CAACJ,EAAE,CAAC,EAAE;IACZE,IAAI,GAAG,IAAIG,IAAI,CAACA,IAAI,CAACC,GAAG,CAACV,IAAI,EAAEvB,MAAM,CAAC0B,KAAK,CAAC,EAAEC,EAAE,EAAEC,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,GAAGE,UAAU,CAAC;EAC9F,CAAC,MACI,IAAIH,EAAE,CAACO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;IAC/BL,IAAI,GAAG,IAAIG,IAAI,CAACA,IAAI,CAACC,GAAG,CAACV,IAAI,EAAEvB,MAAM,CAAC0B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAEE,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,GAAGE,UAAU,CAAC;IAClG,IAAIK,SAAS,GAAGtB,IAAI,CAACc,EAAE,CAACS,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACrC,IAAIC,MAAM,GAAGR,IAAI,CAACS,SAAS,CAAC,CAAC;IAC7BT,IAAI,CAACU,UAAU,CAACV,IAAI,CAACW,UAAU,CAAC,CAAC,GAAGL,SAAS,GAAGE,MAAM,IAAIF,SAAS,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EAC1F,CAAC,MACI,IAAIV,EAAE,CAACO,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;IAC5BL,IAAI,GAAG,IAAIG,IAAI,CAACA,IAAI,CAACC,GAAG,CAACV,IAAI,EAAEvB,MAAM,CAAC0B,KAAK,CAAC,EAAEC,EAAE,CAACS,MAAM,CAAC,CAAC,CAAC,EAAER,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGE,UAAU,CAAC;IACvG,IAAIK,SAAS,GAAGtB,IAAI,CAACc,EAAE,CAACS,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACrC,IAAIC,MAAM,GAAGR,IAAI,CAACS,SAAS,CAAC,CAAC;IAC7BT,IAAI,CAACU,UAAU,CAACV,IAAI,CAACW,UAAU,CAAC,CAAC,GAAGL,SAAS,GAAGE,MAAM,IAAIF,SAAS,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EAC1F;EACA,OAAOR,IAAI;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}