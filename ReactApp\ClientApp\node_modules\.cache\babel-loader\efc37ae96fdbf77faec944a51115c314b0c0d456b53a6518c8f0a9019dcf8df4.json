{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst u = \"\",\n  c = \"0\",\n  t = \"_\";\nfunction f(n, r) {\n  if (i(n)) return r[Number(n)];\n  const e = r[Number(s(n))];\n  return e.items ? f(g(n), e.items) : void 0;\n}\nfunction s(n) {\n  return o(n) ? n : n.split(t)[0];\n}\nfunction g(n) {\n  if (o(n)) return n;\n  const r = n.indexOf(t);\n  return n.substring(r + 1);\n}\nfunction b(n) {\n  return a(\"0\", n);\n}\nfunction x(n, r) {\n  return r.indexOf(n) === 0 ? r.length === n.length || r.charAt(n.length) === t : !1;\n}\nfunction a(n, r) {\n  return r ? r + t + n : n;\n}\nfunction O(n) {\n  const r = n.lastIndexOf(t);\n  return r < 0 ? u : n.substring(0, r);\n}\nfunction o(n) {\n  return n === u || n.indexOf(t) < 0;\n}\nfunction i(n) {\n  return n !== u && n.indexOf(t) < 0;\n}\nfunction E(n) {\n  return S(n) === 1;\n}\nfunction p(n) {\n  return l(n) === c;\n}\nfunction l(n) {\n  const r = n.lastIndexOf(t);\n  return r < 0 ? n : n.substring(r + 1);\n}\nfunction L(n, r, e) {\n  return i(r) ? n ? Number(r) < e - 1 ? (Number(r) + 1).toString() : \"0\" : Number(r) > 0 ? (Number(r) - 1).toString() : (e - 1).toString() : r;\n}\nfunction S(n) {\n  return n.split(t).length - 1;\n}\nexport { u as EMPTY_ID, t as SEPARATOR, c as ZERO_LEVEL_ZERO_ITEM_ID, a as createId, O as getDirectParentId, L as getDirectSiblingIdForLevelZero, b as getFirstChildId, g as getIdWithoutRootParentId, f as getItemById, s as getRootParentId, l as getShortId, p as isFirstItemFromSiblings, o as isIdEmptyOrZeroLevel, E as isIdFirstLevel, i as isIdZeroLevel, x as shouldOpenItem };", "map": {"version": 3, "names": ["u", "c", "t", "f", "n", "r", "i", "Number", "e", "s", "items", "g", "o", "split", "indexOf", "substring", "b", "a", "x", "length", "char<PERSON>t", "O", "lastIndexOf", "E", "S", "p", "l", "L", "toString", "EMPTY_ID", "SEPARATOR", "ZERO_LEVEL_ZERO_ITEM_ID", "createId", "getDirectParentId", "getDirectSiblingIdForLevelZero", "getFirstChildId", "getIdWithoutRootParentId", "getItemById", "getRootParentId", "getShortId", "isFirstItemFromSiblings", "isIdEmptyOrZeroLevel", "isIdFirstLevel", "isIdZeroLevel", "shouldOpenItem"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/menu/utils/itemsIdsUtils.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst u = \"\", c = \"0\", t = \"_\";\nfunction f(n, r) {\n  if (i(n))\n    return r[Number(n)];\n  const e = r[Number(s(n))];\n  return e.items ? f(g(n), e.items) : void 0;\n}\nfunction s(n) {\n  return o(n) ? n : n.split(t)[0];\n}\nfunction g(n) {\n  if (o(n))\n    return n;\n  const r = n.indexOf(t);\n  return n.substring(r + 1);\n}\nfunction b(n) {\n  return a(\"0\", n);\n}\nfunction x(n, r) {\n  return r.indexOf(n) === 0 ? r.length === n.length || r.charAt(n.length) === t : !1;\n}\nfunction a(n, r) {\n  return r ? r + t + n : n;\n}\nfunction O(n) {\n  const r = n.lastIndexOf(t);\n  return r < 0 ? u : n.substring(0, r);\n}\nfunction o(n) {\n  return n === u || n.indexOf(t) < 0;\n}\nfunction i(n) {\n  return n !== u && n.indexOf(t) < 0;\n}\nfunction E(n) {\n  return S(n) === 1;\n}\nfunction p(n) {\n  return l(n) === c;\n}\nfunction l(n) {\n  const r = n.lastIndexOf(t);\n  return r < 0 ? n : n.substring(r + 1);\n}\nfunction L(n, r, e) {\n  return i(r) ? n ? Number(r) < e - 1 ? (Number(r) + 1).toString() : \"0\" : Number(r) > 0 ? (Number(r) - 1).toString() : (e - 1).toString() : r;\n}\nfunction S(n) {\n  return n.split(t).length - 1;\n}\nexport {\n  u as EMPTY_ID,\n  t as SEPARATOR,\n  c as ZERO_LEVEL_ZERO_ITEM_ID,\n  a as createId,\n  O as getDirectParentId,\n  L as getDirectSiblingIdForLevelZero,\n  b as getFirstChildId,\n  g as getIdWithoutRootParentId,\n  f as getItemById,\n  s as getRootParentId,\n  l as getShortId,\n  p as isFirstItemFromSiblings,\n  o as isIdEmptyOrZeroLevel,\n  E as isIdFirstLevel,\n  i as isIdZeroLevel,\n  x as shouldOpenItem\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAG,EAAE;EAAEC,CAAC,GAAG,GAAG;EAAEC,CAAC,GAAG,GAAG;AAC9B,SAASC,CAACA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACf,IAAIC,CAAC,CAACF,CAAC,CAAC,EACN,OAAOC,CAAC,CAACE,MAAM,CAACH,CAAC,CAAC,CAAC;EACrB,MAAMI,CAAC,GAAGH,CAAC,CAACE,MAAM,CAACE,CAAC,CAACL,CAAC,CAAC,CAAC,CAAC;EACzB,OAAOI,CAAC,CAACE,KAAK,GAAGP,CAAC,CAACQ,CAAC,CAACP,CAAC,CAAC,EAAEI,CAAC,CAACE,KAAK,CAAC,GAAG,KAAK,CAAC;AAC5C;AACA,SAASD,CAACA,CAACL,CAAC,EAAE;EACZ,OAAOQ,CAAC,CAACR,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,CAACS,KAAK,CAACX,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC;AACA,SAASS,CAACA,CAACP,CAAC,EAAE;EACZ,IAAIQ,CAAC,CAACR,CAAC,CAAC,EACN,OAAOA,CAAC;EACV,MAAMC,CAAC,GAAGD,CAAC,CAACU,OAAO,CAACZ,CAAC,CAAC;EACtB,OAAOE,CAAC,CAACW,SAAS,CAACV,CAAC,GAAG,CAAC,CAAC;AAC3B;AACA,SAASW,CAACA,CAACZ,CAAC,EAAE;EACZ,OAAOa,CAAC,CAAC,GAAG,EAAEb,CAAC,CAAC;AAClB;AACA,SAASc,CAACA,CAACd,CAAC,EAAEC,CAAC,EAAE;EACf,OAAOA,CAAC,CAACS,OAAO,CAACV,CAAC,CAAC,KAAK,CAAC,GAAGC,CAAC,CAACc,MAAM,KAAKf,CAAC,CAACe,MAAM,IAAId,CAAC,CAACe,MAAM,CAAChB,CAAC,CAACe,MAAM,CAAC,KAAKjB,CAAC,GAAG,CAAC,CAAC;AACpF;AACA,SAASe,CAACA,CAACb,CAAC,EAAEC,CAAC,EAAE;EACf,OAAOA,CAAC,GAAGA,CAAC,GAAGH,CAAC,GAAGE,CAAC,GAAGA,CAAC;AAC1B;AACA,SAASiB,CAACA,CAACjB,CAAC,EAAE;EACZ,MAAMC,CAAC,GAAGD,CAAC,CAACkB,WAAW,CAACpB,CAAC,CAAC;EAC1B,OAAOG,CAAC,GAAG,CAAC,GAAGL,CAAC,GAAGI,CAAC,CAACW,SAAS,CAAC,CAAC,EAAEV,CAAC,CAAC;AACtC;AACA,SAASO,CAACA,CAACR,CAAC,EAAE;EACZ,OAAOA,CAAC,KAAKJ,CAAC,IAAII,CAAC,CAACU,OAAO,CAACZ,CAAC,CAAC,GAAG,CAAC;AACpC;AACA,SAASI,CAACA,CAACF,CAAC,EAAE;EACZ,OAAOA,CAAC,KAAKJ,CAAC,IAAII,CAAC,CAACU,OAAO,CAACZ,CAAC,CAAC,GAAG,CAAC;AACpC;AACA,SAASqB,CAACA,CAACnB,CAAC,EAAE;EACZ,OAAOoB,CAAC,CAACpB,CAAC,CAAC,KAAK,CAAC;AACnB;AACA,SAASqB,CAACA,CAACrB,CAAC,EAAE;EACZ,OAAOsB,CAAC,CAACtB,CAAC,CAAC,KAAKH,CAAC;AACnB;AACA,SAASyB,CAACA,CAACtB,CAAC,EAAE;EACZ,MAAMC,CAAC,GAAGD,CAAC,CAACkB,WAAW,CAACpB,CAAC,CAAC;EAC1B,OAAOG,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGA,CAAC,CAACW,SAAS,CAACV,CAAC,GAAG,CAAC,CAAC;AACvC;AACA,SAASsB,CAACA,CAACvB,CAAC,EAAEC,CAAC,EAAEG,CAAC,EAAE;EAClB,OAAOF,CAAC,CAACD,CAAC,CAAC,GAAGD,CAAC,GAAGG,MAAM,CAACF,CAAC,CAAC,GAAGG,CAAC,GAAG,CAAC,GAAG,CAACD,MAAM,CAACF,CAAC,CAAC,GAAG,CAAC,EAAEuB,QAAQ,CAAC,CAAC,GAAG,GAAG,GAAGrB,MAAM,CAACF,CAAC,CAAC,GAAG,CAAC,GAAG,CAACE,MAAM,CAACF,CAAC,CAAC,GAAG,CAAC,EAAEuB,QAAQ,CAAC,CAAC,GAAG,CAACpB,CAAC,GAAG,CAAC,EAAEoB,QAAQ,CAAC,CAAC,GAAGvB,CAAC;AAC9I;AACA,SAASmB,CAACA,CAACpB,CAAC,EAAE;EACZ,OAAOA,CAAC,CAACS,KAAK,CAACX,CAAC,CAAC,CAACiB,MAAM,GAAG,CAAC;AAC9B;AACA,SACEnB,CAAC,IAAI6B,QAAQ,EACb3B,CAAC,IAAI4B,SAAS,EACd7B,CAAC,IAAI8B,uBAAuB,EAC5Bd,CAAC,IAAIe,QAAQ,EACbX,CAAC,IAAIY,iBAAiB,EACtBN,CAAC,IAAIO,8BAA8B,EACnClB,CAAC,IAAImB,eAAe,EACpBxB,CAAC,IAAIyB,wBAAwB,EAC7BjC,CAAC,IAAIkC,WAAW,EAChB5B,CAAC,IAAI6B,eAAe,EACpBZ,CAAC,IAAIa,UAAU,EACfd,CAAC,IAAIe,uBAAuB,EAC5B5B,CAAC,IAAI6B,oBAAoB,EACzBlB,CAAC,IAAImB,cAAc,EACnBpC,CAAC,IAAIqC,aAAa,EAClBzB,CAAC,IAAI0B,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}