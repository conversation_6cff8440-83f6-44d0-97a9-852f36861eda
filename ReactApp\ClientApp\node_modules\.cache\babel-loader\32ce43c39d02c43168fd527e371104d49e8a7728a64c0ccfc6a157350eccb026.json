{"ast": null, "code": "export const addClass = (className, element) => {\n  element.classList.add(className);\n};\nexport const removeClass = (className, element) => {\n  element.classList.remove(className);\n};\nexport const hasClass = (className, element) => {\n  if (element instanceof HTMLElement) {\n    return element.classList.contains(className);\n  }\n};", "map": {"version": 3, "names": ["addClass", "className", "element", "classList", "add", "removeClass", "remove", "hasClass", "HTMLElement", "contains"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-pdfviewer-common/dist/es/common/dom.js"], "sourcesContent": ["export const addClass = (className, element) => {\n    element.classList.add(className);\n};\nexport const removeClass = (className, element) => {\n    element.classList.remove(className);\n};\nexport const hasClass = (className, element) => {\n    if (element instanceof HTMLElement) {\n        return element.classList.contains(className);\n    }\n};\n"], "mappings": "AAAA,OAAO,MAAMA,QAAQ,GAAGA,CAACC,SAAS,EAAEC,OAAO,KAAK;EAC5CA,OAAO,CAACC,SAAS,CAACC,GAAG,CAACH,SAAS,CAAC;AACpC,CAAC;AACD,OAAO,MAAMI,WAAW,GAAGA,CAACJ,SAAS,EAAEC,OAAO,KAAK;EAC/CA,OAAO,CAACC,SAAS,CAACG,MAAM,CAACL,SAAS,CAAC;AACvC,CAAC;AACD,OAAO,MAAMM,QAAQ,GAAGA,CAACN,SAAS,EAAEC,OAAO,KAAK;EAC5C,IAAIA,OAAO,YAAYM,WAAW,EAAE;IAChC,OAAON,OAAO,CAACC,SAAS,CAACM,QAAQ,CAACR,SAAS,CAAC;EAChD;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}