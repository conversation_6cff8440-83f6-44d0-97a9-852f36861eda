{"ast": null, "code": "/* Copyright 2023 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar _DrawLayer_instances, _a, _DrawLayer_parent, _DrawLayer_id, _DrawLayer_mapping, _DrawLayer_toUpdate, _DrawLayer_setBox, _DrawLayer_createSVG, _DrawLayer_createClipPath;\nimport { __classPrivateFieldGet, __classPrivateFieldSet } from \"tslib\";\n// import { DOMSVGFactory } from \"./display_utils.js\";\n// import { shadow } from \"../shared/util.js\";\nimport { DOMSVGFactory, shadow } from \"pdfjs-dist/legacy/build/pdf.mjs\";\n/**\n * Manage the SVGs drawn on top of the page canvas.\n * It's important to have them directly on top of the canvas because we want to\n * be able to use mix-blend-mode for some of them.\n */\nclass DrawLayer {\n  constructor({\n    pageIndex\n  }) {\n    _DrawLayer_instances.add(this);\n    // todo: props\n    this.pageIndex = 0;\n    // todo: props\n    _DrawLayer_parent.set(this, null);\n    _DrawLayer_id.set(this, 0);\n    _DrawLayer_mapping.set(this, new Map());\n    _DrawLayer_toUpdate.set(this, new Map());\n    this.pageIndex = pageIndex;\n  }\n  setParent(parent) {\n    if (!__classPrivateFieldGet(this, _DrawLayer_parent, \"f\")) {\n      __classPrivateFieldSet(this, _DrawLayer_parent, parent, \"f\");\n      return;\n    }\n    if (__classPrivateFieldGet(this, _DrawLayer_parent, \"f\") !== parent) {\n      if (__classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").size > 0) {\n        for (const root of __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").values()) {\n          root.remove();\n          parent.append(root);\n        }\n      }\n      __classPrivateFieldSet(this, _DrawLayer_parent, parent, \"f\");\n    }\n  }\n  static get _svgFactory() {\n    return shadow(this, \"_svgFactory\", new DOMSVGFactory());\n  }\n  highlight(outlines, color, opacity, isPathUpdatable = false) {\n    var _b, _c;\n    const id = (__classPrivateFieldSet(this, _DrawLayer_id, (_c = __classPrivateFieldGet(this, _DrawLayer_id, \"f\"), _b = _c++, _c), \"f\"), _b);\n    const root = __classPrivateFieldGet(this, _DrawLayer_instances, \"m\", _DrawLayer_createSVG).call(this, outlines.box);\n    // root.classList.add(\"highlight\");\n    root.classList.add(\"k-highlight\");\n    if (outlines.free) {\n      // root.classList.add(\"free\");\n    }\n    const defs = _a._svgFactory.createElement(\"defs\");\n    root.append(defs);\n    const path = _a._svgFactory.createElement(\"path\");\n    defs.append(path);\n    const pathId = `path_p${this.pageIndex}_${id}`;\n    path.setAttribute(\"id\", pathId);\n    path.setAttribute(\"d\", outlines.toSVGPath());\n    if (isPathUpdatable) {\n      __classPrivateFieldGet(this, _DrawLayer_toUpdate, \"f\").set(id, path);\n    }\n    // Create the clipping path for the editor div.\n    const clipPathId = __classPrivateFieldGet(this, _DrawLayer_instances, \"m\", _DrawLayer_createClipPath).call(this, defs, pathId);\n    const use = _a._svgFactory.createElement(\"use\");\n    root.append(use);\n    root.setAttribute(\"fill\", color);\n    root.setAttribute(\"fill-opacity\", opacity);\n    use.setAttribute(\"href\", `#${pathId}`);\n    __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").set(id, root);\n    return {\n      id,\n      clipPathId: `url(#${clipPathId})`\n    };\n  }\n  highlightOutline(outlines) {\n    var _b, _c;\n    // We cannot draw the outline directly in the SVG for highlights because\n    // it composes with its parent with mix-blend-mode: multiply.\n    // But the outline has a different mix-blend-mode, so we need to draw it in\n    // its own SVG.\n    const id = (__classPrivateFieldSet(this, _DrawLayer_id, (_c = __classPrivateFieldGet(this, _DrawLayer_id, \"f\"), _b = _c++, _c), \"f\"), _b);\n    const root = __classPrivateFieldGet(this, _DrawLayer_instances, \"m\", _DrawLayer_createSVG).call(this, outlines.box);\n    // root.classList.add(\"highlightOutline\");\n    root.classList.add(\"k-highlight-outline\");\n    const defs = _a._svgFactory.createElement(\"defs\");\n    root.append(defs);\n    const path = _a._svgFactory.createElement(\"path\");\n    defs.append(path);\n    const pathId = `path_p${this.pageIndex}_${id}`;\n    path.setAttribute(\"id\", pathId);\n    path.setAttribute(\"d\", outlines.toSVGPath());\n    path.setAttribute(\"vector-effect\", \"non-scaling-stroke\");\n    let maskId;\n    if (outlines.free) {\n      // root.classList.add(\"free\");\n      const mask = _a._svgFactory.createElement(\"mask\");\n      defs.append(mask);\n      maskId = `mask_p${this.pageIndex}_${id}`;\n      mask.setAttribute(\"id\", maskId);\n      mask.setAttribute(\"maskUnits\", \"objectBoundingBox\");\n      const rect = _a._svgFactory.createElement(\"rect\");\n      mask.append(rect);\n      rect.setAttribute(\"width\", \"1\");\n      rect.setAttribute(\"height\", \"1\");\n      rect.setAttribute(\"fill\", \"white\");\n      const use = _a._svgFactory.createElement(\"use\");\n      mask.append(use);\n      use.setAttribute(\"href\", `#${pathId}`);\n      use.setAttribute(\"stroke\", \"none\");\n      use.setAttribute(\"fill\", \"black\");\n      use.setAttribute(\"fill-rule\", \"nonzero\");\n      // use.classList.add(\"mask\");\n    }\n    const use1 = _a._svgFactory.createElement(\"use\");\n    root.append(use1);\n    use1.setAttribute(\"href\", `#${pathId}`);\n    if (maskId) {\n      use1.setAttribute(\"mask\", `url(#${maskId})`);\n    }\n    const use2 = use1.cloneNode();\n    root.append(use2);\n    // use1.classList.add(\"mainOutline\");\n    // use2.classList.add(\"secondaryOutline\");\n    __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").set(id, root);\n    return id;\n  }\n  finalizeLine(id, line) {\n    const path = __classPrivateFieldGet(this, _DrawLayer_toUpdate, \"f\").get(id);\n    __classPrivateFieldGet(this, _DrawLayer_toUpdate, \"f\").delete(id);\n    this.updateBox(id, line.box);\n    path.setAttribute(\"d\", line.toSVGPath());\n  }\n  updateLine(id, line) {\n    const root = __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").get(id);\n    const defs = root.firstChild;\n    const path = defs.firstChild;\n    path.setAttribute(\"d\", line.toSVGPath());\n  }\n  removeFreeHighlight(id) {\n    this.remove(id);\n    __classPrivateFieldGet(this, _DrawLayer_toUpdate, \"f\").delete(id);\n  }\n  updatePath(id, line) {\n    __classPrivateFieldGet(this, _DrawLayer_toUpdate, \"f\").get(id).setAttribute(\"d\", line.toSVGPath());\n  }\n  updateBox(id, box) {\n    __classPrivateFieldGet(_a, _a, \"m\", _DrawLayer_setBox).call(_a, __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").get(id), box);\n  }\n  show(id, visible) {\n    // this.#mapping.get(id).classList.toggle(\"hidden\", !visible);\n    __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").get(id).classList.toggle(\"k-hidden\", !visible);\n  }\n  rotate(id, angle) {\n    __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").get(id).setAttribute(\"data-main-rotation\", angle);\n  }\n  changeColor(id, color) {\n    __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").get(id).setAttribute(\"fill\", color);\n  }\n  changeOpacity(id, opacity) {\n    __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").get(id).setAttribute(\"fill-opacity\", opacity);\n  }\n  addClass(id, className) {\n    __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").get(id).classList.add(className);\n  }\n  removeClass(id, className) {\n    __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").get(id).classList.remove(className);\n  }\n  remove(id) {\n    if (__classPrivateFieldGet(this, _DrawLayer_parent, \"f\") === null) {\n      return;\n    }\n    __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").get(id).remove();\n    __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").delete(id);\n  }\n  destroy() {\n    __classPrivateFieldSet(this, _DrawLayer_parent, null, \"f\");\n    for (const root of __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").values()) {\n      root.remove();\n    }\n    __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").clear();\n  }\n}\n_a = DrawLayer, _DrawLayer_parent = new WeakMap(), _DrawLayer_id = new WeakMap(), _DrawLayer_mapping = new WeakMap(), _DrawLayer_toUpdate = new WeakMap(), _DrawLayer_instances = new WeakSet(), _DrawLayer_setBox = function _DrawLayer_setBox(element, {\n  x = 0,\n  y = 0,\n  width = 1,\n  height = 1\n} = {}) {\n  const {\n    style\n  } = element;\n  style.top = `${100 * y}%`;\n  style.left = `${100 * x}%`;\n  // style.width = `${100 * width}%`;\n  // style.height = `${100 * height}%`;\n  // todo: reduce the dimensions, so that the annotation editor toolbar\n  // does not overlap the outline\n  style.width = `${100 * width - 0.2}%`;\n  style.height = `${100 * height - 0.2}%`;\n}, _DrawLayer_createSVG = function _DrawLayer_createSVG(box) {\n  const svg = _a._svgFactory.create(1, 1, /* skipDimensions = */true);\n  __classPrivateFieldGet(this, _DrawLayer_parent, \"f\").append(svg);\n  svg.setAttribute(\"aria-hidden\", true);\n  __classPrivateFieldGet(_a, _a, \"m\", _DrawLayer_setBox).call(_a, svg, box);\n  return svg;\n}, _DrawLayer_createClipPath = function _DrawLayer_createClipPath(defs, pathId) {\n  const clipPath = _a._svgFactory.createElement(\"clipPath\");\n  defs.append(clipPath);\n  const clipPathId = `clip_${pathId}`;\n  clipPath.setAttribute(\"id\", clipPathId);\n  clipPath.setAttribute(\"clipPathUnits\", \"objectBoundingBox\");\n  const clipPathUse = _a._svgFactory.createElement(\"use\");\n  clipPath.append(clipPathUse);\n  clipPathUse.setAttribute(\"href\", `#${pathId}`);\n  // clipPathUse.classList.add(\"clip\");\n  return clipPathId;\n};\nexport { DrawLayer };", "map": {"version": 3, "names": ["_DrawLayer_instances", "_a", "_DrawLayer_parent", "_DrawLayer_id", "_DrawLayer_mapping", "_DrawLayer_toUpdate", "_DrawLayer_setBox", "_DrawLayer_createSVG", "_DrawLayer_createClipPath", "__classPrivateFieldGet", "__classPrivateFieldSet", "DOMSVGFactory", "shadow", "Draw<PERSON>ayer", "constructor", "pageIndex", "add", "set", "Map", "setParent", "parent", "size", "root", "values", "remove", "append", "_svgFactory", "highlight", "outlines", "color", "opacity", "isPathUpdatable", "_b", "_c", "id", "call", "box", "classList", "free", "defs", "createElement", "path", "pathId", "setAttribute", "to<PERSON><PERSON><PERSON>", "clipPathId", "use", "highlightOutline", "maskId", "mask", "rect", "use1", "use2", "cloneNode", "finalizeLine", "line", "get", "delete", "updateBox", "updateLine", "<PERSON><PERSON><PERSON><PERSON>", "removeFreeHighlight", "updatePath", "show", "visible", "toggle", "rotate", "angle", "changeColor", "changeOpacity", "addClass", "className", "removeClass", "destroy", "clear", "WeakMap", "WeakSet", "element", "x", "y", "width", "height", "style", "top", "left", "svg", "create", "clipPath", "clipPathUse"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-pdfviewer-common/dist/es/annotations/draw-layer.js"], "sourcesContent": ["/* Copyright 2023 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar _DrawLayer_instances, _a, _DrawLayer_parent, _DrawLayer_id, _DrawLayer_mapping, _DrawLayer_toUpdate, _DrawLayer_setBox, _DrawLayer_createSVG, _DrawLayer_createClipPath;\nimport { __classPrivateFieldGet, __classPrivateFieldSet } from \"tslib\";\n// import { DOMSVGFactory } from \"./display_utils.js\";\n// import { shadow } from \"../shared/util.js\";\nimport { DOMSVGFactory, shadow } from \"pdfjs-dist/legacy/build/pdf.mjs\";\n/**\n * Manage the SVGs drawn on top of the page canvas.\n * It's important to have them directly on top of the canvas because we want to\n * be able to use mix-blend-mode for some of them.\n */\nclass DrawLayer {\n    constructor({ pageIndex }) {\n        _DrawLayer_instances.add(this);\n        // todo: props\n        this.pageIndex = 0;\n        // todo: props\n        _DrawLayer_parent.set(this, null);\n        _DrawLayer_id.set(this, 0);\n        _DrawLayer_mapping.set(this, new Map());\n        _DrawLayer_toUpdate.set(this, new Map());\n        this.pageIndex = pageIndex;\n    }\n    setParent(parent) {\n        if (!__classPrivateFieldGet(this, _DrawLayer_parent, \"f\")) {\n            __classPrivateFieldSet(this, _DrawLayer_parent, parent, \"f\");\n            return;\n        }\n        if (__classPrivateFieldGet(this, _DrawLayer_parent, \"f\") !== parent) {\n            if (__classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").size > 0) {\n                for (const root of __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").values()) {\n                    root.remove();\n                    parent.append(root);\n                }\n            }\n            __classPrivateFieldSet(this, _DrawLayer_parent, parent, \"f\");\n        }\n    }\n    static get _svgFactory() {\n        return shadow(this, \"_svgFactory\", new DOMSVGFactory());\n    }\n    highlight(outlines, color, opacity, isPathUpdatable = false) {\n        var _b, _c;\n        const id = (__classPrivateFieldSet(this, _DrawLayer_id, (_c = __classPrivateFieldGet(this, _DrawLayer_id, \"f\"), _b = _c++, _c), \"f\"), _b);\n        const root = __classPrivateFieldGet(this, _DrawLayer_instances, \"m\", _DrawLayer_createSVG).call(this, outlines.box);\n        // root.classList.add(\"highlight\");\n        root.classList.add(\"k-highlight\");\n        if (outlines.free) {\n            // root.classList.add(\"free\");\n        }\n        const defs = _a._svgFactory.createElement(\"defs\");\n        root.append(defs);\n        const path = _a._svgFactory.createElement(\"path\");\n        defs.append(path);\n        const pathId = `path_p${this.pageIndex}_${id}`;\n        path.setAttribute(\"id\", pathId);\n        path.setAttribute(\"d\", outlines.toSVGPath());\n        if (isPathUpdatable) {\n            __classPrivateFieldGet(this, _DrawLayer_toUpdate, \"f\").set(id, path);\n        }\n        // Create the clipping path for the editor div.\n        const clipPathId = __classPrivateFieldGet(this, _DrawLayer_instances, \"m\", _DrawLayer_createClipPath).call(this, defs, pathId);\n        const use = _a._svgFactory.createElement(\"use\");\n        root.append(use);\n        root.setAttribute(\"fill\", color);\n        root.setAttribute(\"fill-opacity\", opacity);\n        use.setAttribute(\"href\", `#${pathId}`);\n        __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").set(id, root);\n        return { id, clipPathId: `url(#${clipPathId})` };\n    }\n    highlightOutline(outlines) {\n        var _b, _c;\n        // We cannot draw the outline directly in the SVG for highlights because\n        // it composes with its parent with mix-blend-mode: multiply.\n        // But the outline has a different mix-blend-mode, so we need to draw it in\n        // its own SVG.\n        const id = (__classPrivateFieldSet(this, _DrawLayer_id, (_c = __classPrivateFieldGet(this, _DrawLayer_id, \"f\"), _b = _c++, _c), \"f\"), _b);\n        const root = __classPrivateFieldGet(this, _DrawLayer_instances, \"m\", _DrawLayer_createSVG).call(this, outlines.box);\n        // root.classList.add(\"highlightOutline\");\n        root.classList.add(\"k-highlight-outline\");\n        const defs = _a._svgFactory.createElement(\"defs\");\n        root.append(defs);\n        const path = _a._svgFactory.createElement(\"path\");\n        defs.append(path);\n        const pathId = `path_p${this.pageIndex}_${id}`;\n        path.setAttribute(\"id\", pathId);\n        path.setAttribute(\"d\", outlines.toSVGPath());\n        path.setAttribute(\"vector-effect\", \"non-scaling-stroke\");\n        let maskId;\n        if (outlines.free) {\n            // root.classList.add(\"free\");\n            const mask = _a._svgFactory.createElement(\"mask\");\n            defs.append(mask);\n            maskId = `mask_p${this.pageIndex}_${id}`;\n            mask.setAttribute(\"id\", maskId);\n            mask.setAttribute(\"maskUnits\", \"objectBoundingBox\");\n            const rect = _a._svgFactory.createElement(\"rect\");\n            mask.append(rect);\n            rect.setAttribute(\"width\", \"1\");\n            rect.setAttribute(\"height\", \"1\");\n            rect.setAttribute(\"fill\", \"white\");\n            const use = _a._svgFactory.createElement(\"use\");\n            mask.append(use);\n            use.setAttribute(\"href\", `#${pathId}`);\n            use.setAttribute(\"stroke\", \"none\");\n            use.setAttribute(\"fill\", \"black\");\n            use.setAttribute(\"fill-rule\", \"nonzero\");\n            // use.classList.add(\"mask\");\n        }\n        const use1 = _a._svgFactory.createElement(\"use\");\n        root.append(use1);\n        use1.setAttribute(\"href\", `#${pathId}`);\n        if (maskId) {\n            use1.setAttribute(\"mask\", `url(#${maskId})`);\n        }\n        const use2 = use1.cloneNode();\n        root.append(use2);\n        // use1.classList.add(\"mainOutline\");\n        // use2.classList.add(\"secondaryOutline\");\n        __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").set(id, root);\n        return id;\n    }\n    finalizeLine(id, line) {\n        const path = __classPrivateFieldGet(this, _DrawLayer_toUpdate, \"f\").get(id);\n        __classPrivateFieldGet(this, _DrawLayer_toUpdate, \"f\").delete(id);\n        this.updateBox(id, line.box);\n        path.setAttribute(\"d\", line.toSVGPath());\n    }\n    updateLine(id, line) {\n        const root = __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").get(id);\n        const defs = root.firstChild;\n        const path = defs.firstChild;\n        path.setAttribute(\"d\", line.toSVGPath());\n    }\n    removeFreeHighlight(id) {\n        this.remove(id);\n        __classPrivateFieldGet(this, _DrawLayer_toUpdate, \"f\").delete(id);\n    }\n    updatePath(id, line) {\n        __classPrivateFieldGet(this, _DrawLayer_toUpdate, \"f\").get(id).setAttribute(\"d\", line.toSVGPath());\n    }\n    updateBox(id, box) {\n        __classPrivateFieldGet(_a, _a, \"m\", _DrawLayer_setBox).call(_a, __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").get(id), box);\n    }\n    show(id, visible) {\n        // this.#mapping.get(id).classList.toggle(\"hidden\", !visible);\n        __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").get(id).classList.toggle(\"k-hidden\", !visible);\n    }\n    rotate(id, angle) {\n        __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").get(id).setAttribute(\"data-main-rotation\", angle);\n    }\n    changeColor(id, color) {\n        __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").get(id).setAttribute(\"fill\", color);\n    }\n    changeOpacity(id, opacity) {\n        __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").get(id).setAttribute(\"fill-opacity\", opacity);\n    }\n    addClass(id, className) {\n        __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").get(id).classList.add(className);\n    }\n    removeClass(id, className) {\n        __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").get(id).classList.remove(className);\n    }\n    remove(id) {\n        if (__classPrivateFieldGet(this, _DrawLayer_parent, \"f\") === null) {\n            return;\n        }\n        __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").get(id).remove();\n        __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").delete(id);\n    }\n    destroy() {\n        __classPrivateFieldSet(this, _DrawLayer_parent, null, \"f\");\n        for (const root of __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").values()) {\n            root.remove();\n        }\n        __classPrivateFieldGet(this, _DrawLayer_mapping, \"f\").clear();\n    }\n}\n_a = DrawLayer, _DrawLayer_parent = new WeakMap(), _DrawLayer_id = new WeakMap(), _DrawLayer_mapping = new WeakMap(), _DrawLayer_toUpdate = new WeakMap(), _DrawLayer_instances = new WeakSet(), _DrawLayer_setBox = function _DrawLayer_setBox(element, { x = 0, y = 0, width = 1, height = 1 } = {}) {\n    const { style } = element;\n    style.top = `${100 * y}%`;\n    style.left = `${100 * x}%`;\n    // style.width = `${100 * width}%`;\n    // style.height = `${100 * height}%`;\n    // todo: reduce the dimensions, so that the annotation editor toolbar\n    // does not overlap the outline\n    style.width = `${100 * width - 0.2}%`;\n    style.height = `${100 * height - 0.2}%`;\n}, _DrawLayer_createSVG = function _DrawLayer_createSVG(box) {\n    const svg = _a._svgFactory.create(1, 1, /* skipDimensions = */ true);\n    __classPrivateFieldGet(this, _DrawLayer_parent, \"f\").append(svg);\n    svg.setAttribute(\"aria-hidden\", true);\n    __classPrivateFieldGet(_a, _a, \"m\", _DrawLayer_setBox).call(_a, svg, box);\n    return svg;\n}, _DrawLayer_createClipPath = function _DrawLayer_createClipPath(defs, pathId) {\n    const clipPath = _a._svgFactory.createElement(\"clipPath\");\n    defs.append(clipPath);\n    const clipPathId = `clip_${pathId}`;\n    clipPath.setAttribute(\"id\", clipPathId);\n    clipPath.setAttribute(\"clipPathUnits\", \"objectBoundingBox\");\n    const clipPathUse = _a._svgFactory.createElement(\"use\");\n    clipPath.append(clipPathUse);\n    clipPathUse.setAttribute(\"href\", `#${pathId}`);\n    // clipPathUse.classList.add(\"clip\");\n    return clipPathId;\n};\nexport { DrawLayer };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,oBAAoB,EAAEC,EAAE,EAAEC,iBAAiB,EAAEC,aAAa,EAAEC,kBAAkB,EAAEC,mBAAmB,EAAEC,iBAAiB,EAAEC,oBAAoB,EAAEC,yBAAyB;AAC3K,SAASC,sBAAsB,EAAEC,sBAAsB,QAAQ,OAAO;AACtE;AACA;AACA,SAASC,aAAa,EAAEC,MAAM,QAAQ,iCAAiC;AACvE;AACA;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZC,WAAWA,CAAC;IAAEC;EAAU,CAAC,EAAE;IACvBf,oBAAoB,CAACgB,GAAG,CAAC,IAAI,CAAC;IAC9B;IACA,IAAI,CAACD,SAAS,GAAG,CAAC;IAClB;IACAb,iBAAiB,CAACe,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IACjCd,aAAa,CAACc,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;IAC1Bb,kBAAkB,CAACa,GAAG,CAAC,IAAI,EAAE,IAAIC,GAAG,CAAC,CAAC,CAAC;IACvCb,mBAAmB,CAACY,GAAG,CAAC,IAAI,EAAE,IAAIC,GAAG,CAAC,CAAC,CAAC;IACxC,IAAI,CAACH,SAAS,GAAGA,SAAS;EAC9B;EACAI,SAASA,CAACC,MAAM,EAAE;IACd,IAAI,CAACX,sBAAsB,CAAC,IAAI,EAAEP,iBAAiB,EAAE,GAAG,CAAC,EAAE;MACvDQ,sBAAsB,CAAC,IAAI,EAAER,iBAAiB,EAAEkB,MAAM,EAAE,GAAG,CAAC;MAC5D;IACJ;IACA,IAAIX,sBAAsB,CAAC,IAAI,EAAEP,iBAAiB,EAAE,GAAG,CAAC,KAAKkB,MAAM,EAAE;MACjE,IAAIX,sBAAsB,CAAC,IAAI,EAAEL,kBAAkB,EAAE,GAAG,CAAC,CAACiB,IAAI,GAAG,CAAC,EAAE;QAChE,KAAK,MAAMC,IAAI,IAAIb,sBAAsB,CAAC,IAAI,EAAEL,kBAAkB,EAAE,GAAG,CAAC,CAACmB,MAAM,CAAC,CAAC,EAAE;UAC/ED,IAAI,CAACE,MAAM,CAAC,CAAC;UACbJ,MAAM,CAACK,MAAM,CAACH,IAAI,CAAC;QACvB;MACJ;MACAZ,sBAAsB,CAAC,IAAI,EAAER,iBAAiB,EAAEkB,MAAM,EAAE,GAAG,CAAC;IAChE;EACJ;EACA,WAAWM,WAAWA,CAAA,EAAG;IACrB,OAAOd,MAAM,CAAC,IAAI,EAAE,aAAa,EAAE,IAAID,aAAa,CAAC,CAAC,CAAC;EAC3D;EACAgB,SAASA,CAACC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,EAAEC,eAAe,GAAG,KAAK,EAAE;IACzD,IAAIC,EAAE,EAAEC,EAAE;IACV,MAAMC,EAAE,IAAIxB,sBAAsB,CAAC,IAAI,EAAEP,aAAa,GAAG8B,EAAE,GAAGxB,sBAAsB,CAAC,IAAI,EAAEN,aAAa,EAAE,GAAG,CAAC,EAAE6B,EAAE,GAAGC,EAAE,EAAE,EAAEA,EAAE,GAAG,GAAG,CAAC,EAAED,EAAE,CAAC;IACzI,MAAMV,IAAI,GAAGb,sBAAsB,CAAC,IAAI,EAAET,oBAAoB,EAAE,GAAG,EAAEO,oBAAoB,CAAC,CAAC4B,IAAI,CAAC,IAAI,EAAEP,QAAQ,CAACQ,GAAG,CAAC;IACnH;IACAd,IAAI,CAACe,SAAS,CAACrB,GAAG,CAAC,aAAa,CAAC;IACjC,IAAIY,QAAQ,CAACU,IAAI,EAAE;MACf;IAAA;IAEJ,MAAMC,IAAI,GAAGtC,EAAE,CAACyB,WAAW,CAACc,aAAa,CAAC,MAAM,CAAC;IACjDlB,IAAI,CAACG,MAAM,CAACc,IAAI,CAAC;IACjB,MAAME,IAAI,GAAGxC,EAAE,CAACyB,WAAW,CAACc,aAAa,CAAC,MAAM,CAAC;IACjDD,IAAI,CAACd,MAAM,CAACgB,IAAI,CAAC;IACjB,MAAMC,MAAM,GAAG,SAAS,IAAI,CAAC3B,SAAS,IAAImB,EAAE,EAAE;IAC9CO,IAAI,CAACE,YAAY,CAAC,IAAI,EAAED,MAAM,CAAC;IAC/BD,IAAI,CAACE,YAAY,CAAC,GAAG,EAAEf,QAAQ,CAACgB,SAAS,CAAC,CAAC,CAAC;IAC5C,IAAIb,eAAe,EAAE;MACjBtB,sBAAsB,CAAC,IAAI,EAAEJ,mBAAmB,EAAE,GAAG,CAAC,CAACY,GAAG,CAACiB,EAAE,EAAEO,IAAI,CAAC;IACxE;IACA;IACA,MAAMI,UAAU,GAAGpC,sBAAsB,CAAC,IAAI,EAAET,oBAAoB,EAAE,GAAG,EAAEQ,yBAAyB,CAAC,CAAC2B,IAAI,CAAC,IAAI,EAAEI,IAAI,EAAEG,MAAM,CAAC;IAC9H,MAAMI,GAAG,GAAG7C,EAAE,CAACyB,WAAW,CAACc,aAAa,CAAC,KAAK,CAAC;IAC/ClB,IAAI,CAACG,MAAM,CAACqB,GAAG,CAAC;IAChBxB,IAAI,CAACqB,YAAY,CAAC,MAAM,EAAEd,KAAK,CAAC;IAChCP,IAAI,CAACqB,YAAY,CAAC,cAAc,EAAEb,OAAO,CAAC;IAC1CgB,GAAG,CAACH,YAAY,CAAC,MAAM,EAAE,IAAID,MAAM,EAAE,CAAC;IACtCjC,sBAAsB,CAAC,IAAI,EAAEL,kBAAkB,EAAE,GAAG,CAAC,CAACa,GAAG,CAACiB,EAAE,EAAEZ,IAAI,CAAC;IACnE,OAAO;MAAEY,EAAE;MAAEW,UAAU,EAAE,QAAQA,UAAU;IAAI,CAAC;EACpD;EACAE,gBAAgBA,CAACnB,QAAQ,EAAE;IACvB,IAAII,EAAE,EAAEC,EAAE;IACV;IACA;IACA;IACA;IACA,MAAMC,EAAE,IAAIxB,sBAAsB,CAAC,IAAI,EAAEP,aAAa,GAAG8B,EAAE,GAAGxB,sBAAsB,CAAC,IAAI,EAAEN,aAAa,EAAE,GAAG,CAAC,EAAE6B,EAAE,GAAGC,EAAE,EAAE,EAAEA,EAAE,GAAG,GAAG,CAAC,EAAED,EAAE,CAAC;IACzI,MAAMV,IAAI,GAAGb,sBAAsB,CAAC,IAAI,EAAET,oBAAoB,EAAE,GAAG,EAAEO,oBAAoB,CAAC,CAAC4B,IAAI,CAAC,IAAI,EAAEP,QAAQ,CAACQ,GAAG,CAAC;IACnH;IACAd,IAAI,CAACe,SAAS,CAACrB,GAAG,CAAC,qBAAqB,CAAC;IACzC,MAAMuB,IAAI,GAAGtC,EAAE,CAACyB,WAAW,CAACc,aAAa,CAAC,MAAM,CAAC;IACjDlB,IAAI,CAACG,MAAM,CAACc,IAAI,CAAC;IACjB,MAAME,IAAI,GAAGxC,EAAE,CAACyB,WAAW,CAACc,aAAa,CAAC,MAAM,CAAC;IACjDD,IAAI,CAACd,MAAM,CAACgB,IAAI,CAAC;IACjB,MAAMC,MAAM,GAAG,SAAS,IAAI,CAAC3B,SAAS,IAAImB,EAAE,EAAE;IAC9CO,IAAI,CAACE,YAAY,CAAC,IAAI,EAAED,MAAM,CAAC;IAC/BD,IAAI,CAACE,YAAY,CAAC,GAAG,EAAEf,QAAQ,CAACgB,SAAS,CAAC,CAAC,CAAC;IAC5CH,IAAI,CAACE,YAAY,CAAC,eAAe,EAAE,oBAAoB,CAAC;IACxD,IAAIK,MAAM;IACV,IAAIpB,QAAQ,CAACU,IAAI,EAAE;MACf;MACA,MAAMW,IAAI,GAAGhD,EAAE,CAACyB,WAAW,CAACc,aAAa,CAAC,MAAM,CAAC;MACjDD,IAAI,CAACd,MAAM,CAACwB,IAAI,CAAC;MACjBD,MAAM,GAAG,SAAS,IAAI,CAACjC,SAAS,IAAImB,EAAE,EAAE;MACxCe,IAAI,CAACN,YAAY,CAAC,IAAI,EAAEK,MAAM,CAAC;MAC/BC,IAAI,CAACN,YAAY,CAAC,WAAW,EAAE,mBAAmB,CAAC;MACnD,MAAMO,IAAI,GAAGjD,EAAE,CAACyB,WAAW,CAACc,aAAa,CAAC,MAAM,CAAC;MACjDS,IAAI,CAACxB,MAAM,CAACyB,IAAI,CAAC;MACjBA,IAAI,CAACP,YAAY,CAAC,OAAO,EAAE,GAAG,CAAC;MAC/BO,IAAI,CAACP,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC;MAChCO,IAAI,CAACP,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC;MAClC,MAAMG,GAAG,GAAG7C,EAAE,CAACyB,WAAW,CAACc,aAAa,CAAC,KAAK,CAAC;MAC/CS,IAAI,CAACxB,MAAM,CAACqB,GAAG,CAAC;MAChBA,GAAG,CAACH,YAAY,CAAC,MAAM,EAAE,IAAID,MAAM,EAAE,CAAC;MACtCI,GAAG,CAACH,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC;MAClCG,GAAG,CAACH,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC;MACjCG,GAAG,CAACH,YAAY,CAAC,WAAW,EAAE,SAAS,CAAC;MACxC;IACJ;IACA,MAAMQ,IAAI,GAAGlD,EAAE,CAACyB,WAAW,CAACc,aAAa,CAAC,KAAK,CAAC;IAChDlB,IAAI,CAACG,MAAM,CAAC0B,IAAI,CAAC;IACjBA,IAAI,CAACR,YAAY,CAAC,MAAM,EAAE,IAAID,MAAM,EAAE,CAAC;IACvC,IAAIM,MAAM,EAAE;MACRG,IAAI,CAACR,YAAY,CAAC,MAAM,EAAE,QAAQK,MAAM,GAAG,CAAC;IAChD;IACA,MAAMI,IAAI,GAAGD,IAAI,CAACE,SAAS,CAAC,CAAC;IAC7B/B,IAAI,CAACG,MAAM,CAAC2B,IAAI,CAAC;IACjB;IACA;IACA3C,sBAAsB,CAAC,IAAI,EAAEL,kBAAkB,EAAE,GAAG,CAAC,CAACa,GAAG,CAACiB,EAAE,EAAEZ,IAAI,CAAC;IACnE,OAAOY,EAAE;EACb;EACAoB,YAAYA,CAACpB,EAAE,EAAEqB,IAAI,EAAE;IACnB,MAAMd,IAAI,GAAGhC,sBAAsB,CAAC,IAAI,EAAEJ,mBAAmB,EAAE,GAAG,CAAC,CAACmD,GAAG,CAACtB,EAAE,CAAC;IAC3EzB,sBAAsB,CAAC,IAAI,EAAEJ,mBAAmB,EAAE,GAAG,CAAC,CAACoD,MAAM,CAACvB,EAAE,CAAC;IACjE,IAAI,CAACwB,SAAS,CAACxB,EAAE,EAAEqB,IAAI,CAACnB,GAAG,CAAC;IAC5BK,IAAI,CAACE,YAAY,CAAC,GAAG,EAAEY,IAAI,CAACX,SAAS,CAAC,CAAC,CAAC;EAC5C;EACAe,UAAUA,CAACzB,EAAE,EAAEqB,IAAI,EAAE;IACjB,MAAMjC,IAAI,GAAGb,sBAAsB,CAAC,IAAI,EAAEL,kBAAkB,EAAE,GAAG,CAAC,CAACoD,GAAG,CAACtB,EAAE,CAAC;IAC1E,MAAMK,IAAI,GAAGjB,IAAI,CAACsC,UAAU;IAC5B,MAAMnB,IAAI,GAAGF,IAAI,CAACqB,UAAU;IAC5BnB,IAAI,CAACE,YAAY,CAAC,GAAG,EAAEY,IAAI,CAACX,SAAS,CAAC,CAAC,CAAC;EAC5C;EACAiB,mBAAmBA,CAAC3B,EAAE,EAAE;IACpB,IAAI,CAACV,MAAM,CAACU,EAAE,CAAC;IACfzB,sBAAsB,CAAC,IAAI,EAAEJ,mBAAmB,EAAE,GAAG,CAAC,CAACoD,MAAM,CAACvB,EAAE,CAAC;EACrE;EACA4B,UAAUA,CAAC5B,EAAE,EAAEqB,IAAI,EAAE;IACjB9C,sBAAsB,CAAC,IAAI,EAAEJ,mBAAmB,EAAE,GAAG,CAAC,CAACmD,GAAG,CAACtB,EAAE,CAAC,CAACS,YAAY,CAAC,GAAG,EAAEY,IAAI,CAACX,SAAS,CAAC,CAAC,CAAC;EACtG;EACAc,SAASA,CAACxB,EAAE,EAAEE,GAAG,EAAE;IACf3B,sBAAsB,CAACR,EAAE,EAAEA,EAAE,EAAE,GAAG,EAAEK,iBAAiB,CAAC,CAAC6B,IAAI,CAAClC,EAAE,EAAEQ,sBAAsB,CAAC,IAAI,EAAEL,kBAAkB,EAAE,GAAG,CAAC,CAACoD,GAAG,CAACtB,EAAE,CAAC,EAAEE,GAAG,CAAC;EACvI;EACA2B,IAAIA,CAAC7B,EAAE,EAAE8B,OAAO,EAAE;IACd;IACAvD,sBAAsB,CAAC,IAAI,EAAEL,kBAAkB,EAAE,GAAG,CAAC,CAACoD,GAAG,CAACtB,EAAE,CAAC,CAACG,SAAS,CAAC4B,MAAM,CAAC,UAAU,EAAE,CAACD,OAAO,CAAC;EACxG;EACAE,MAAMA,CAAChC,EAAE,EAAEiC,KAAK,EAAE;IACd1D,sBAAsB,CAAC,IAAI,EAAEL,kBAAkB,EAAE,GAAG,CAAC,CAACoD,GAAG,CAACtB,EAAE,CAAC,CAACS,YAAY,CAAC,oBAAoB,EAAEwB,KAAK,CAAC;EAC3G;EACAC,WAAWA,CAAClC,EAAE,EAAEL,KAAK,EAAE;IACnBpB,sBAAsB,CAAC,IAAI,EAAEL,kBAAkB,EAAE,GAAG,CAAC,CAACoD,GAAG,CAACtB,EAAE,CAAC,CAACS,YAAY,CAAC,MAAM,EAAEd,KAAK,CAAC;EAC7F;EACAwC,aAAaA,CAACnC,EAAE,EAAEJ,OAAO,EAAE;IACvBrB,sBAAsB,CAAC,IAAI,EAAEL,kBAAkB,EAAE,GAAG,CAAC,CAACoD,GAAG,CAACtB,EAAE,CAAC,CAACS,YAAY,CAAC,cAAc,EAAEb,OAAO,CAAC;EACvG;EACAwC,QAAQA,CAACpC,EAAE,EAAEqC,SAAS,EAAE;IACpB9D,sBAAsB,CAAC,IAAI,EAAEL,kBAAkB,EAAE,GAAG,CAAC,CAACoD,GAAG,CAACtB,EAAE,CAAC,CAACG,SAAS,CAACrB,GAAG,CAACuD,SAAS,CAAC;EAC1F;EACAC,WAAWA,CAACtC,EAAE,EAAEqC,SAAS,EAAE;IACvB9D,sBAAsB,CAAC,IAAI,EAAEL,kBAAkB,EAAE,GAAG,CAAC,CAACoD,GAAG,CAACtB,EAAE,CAAC,CAACG,SAAS,CAACb,MAAM,CAAC+C,SAAS,CAAC;EAC7F;EACA/C,MAAMA,CAACU,EAAE,EAAE;IACP,IAAIzB,sBAAsB,CAAC,IAAI,EAAEP,iBAAiB,EAAE,GAAG,CAAC,KAAK,IAAI,EAAE;MAC/D;IACJ;IACAO,sBAAsB,CAAC,IAAI,EAAEL,kBAAkB,EAAE,GAAG,CAAC,CAACoD,GAAG,CAACtB,EAAE,CAAC,CAACV,MAAM,CAAC,CAAC;IACtEf,sBAAsB,CAAC,IAAI,EAAEL,kBAAkB,EAAE,GAAG,CAAC,CAACqD,MAAM,CAACvB,EAAE,CAAC;EACpE;EACAuC,OAAOA,CAAA,EAAG;IACN/D,sBAAsB,CAAC,IAAI,EAAER,iBAAiB,EAAE,IAAI,EAAE,GAAG,CAAC;IAC1D,KAAK,MAAMoB,IAAI,IAAIb,sBAAsB,CAAC,IAAI,EAAEL,kBAAkB,EAAE,GAAG,CAAC,CAACmB,MAAM,CAAC,CAAC,EAAE;MAC/ED,IAAI,CAACE,MAAM,CAAC,CAAC;IACjB;IACAf,sBAAsB,CAAC,IAAI,EAAEL,kBAAkB,EAAE,GAAG,CAAC,CAACsE,KAAK,CAAC,CAAC;EACjE;AACJ;AACAzE,EAAE,GAAGY,SAAS,EAAEX,iBAAiB,GAAG,IAAIyE,OAAO,CAAC,CAAC,EAAExE,aAAa,GAAG,IAAIwE,OAAO,CAAC,CAAC,EAAEvE,kBAAkB,GAAG,IAAIuE,OAAO,CAAC,CAAC,EAAEtE,mBAAmB,GAAG,IAAIsE,OAAO,CAAC,CAAC,EAAE3E,oBAAoB,GAAG,IAAI4E,OAAO,CAAC,CAAC,EAAEtE,iBAAiB,GAAG,SAASA,iBAAiBA,CAACuE,OAAO,EAAE;EAAEC,CAAC,GAAG,CAAC;EAAEC,CAAC,GAAG,CAAC;EAAEC,KAAK,GAAG,CAAC;EAAEC,MAAM,GAAG;AAAE,CAAC,GAAG,CAAC,CAAC,EAAE;EACnS,MAAM;IAAEC;EAAM,CAAC,GAAGL,OAAO;EACzBK,KAAK,CAACC,GAAG,GAAG,GAAG,GAAG,GAAGJ,CAAC,GAAG;EACzBG,KAAK,CAACE,IAAI,GAAG,GAAG,GAAG,GAAGN,CAAC,GAAG;EAC1B;EACA;EACA;EACA;EACAI,KAAK,CAACF,KAAK,GAAG,GAAG,GAAG,GAAGA,KAAK,GAAG,GAAG,GAAG;EACrCE,KAAK,CAACD,MAAM,GAAG,GAAG,GAAG,GAAGA,MAAM,GAAG,GAAG,GAAG;AAC3C,CAAC,EAAE1E,oBAAoB,GAAG,SAASA,oBAAoBA,CAAC6B,GAAG,EAAE;EACzD,MAAMiD,GAAG,GAAGpF,EAAE,CAACyB,WAAW,CAAC4D,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,sBAAuB,IAAI,CAAC;EACpE7E,sBAAsB,CAAC,IAAI,EAAEP,iBAAiB,EAAE,GAAG,CAAC,CAACuB,MAAM,CAAC4D,GAAG,CAAC;EAChEA,GAAG,CAAC1C,YAAY,CAAC,aAAa,EAAE,IAAI,CAAC;EACrClC,sBAAsB,CAACR,EAAE,EAAEA,EAAE,EAAE,GAAG,EAAEK,iBAAiB,CAAC,CAAC6B,IAAI,CAAClC,EAAE,EAAEoF,GAAG,EAAEjD,GAAG,CAAC;EACzE,OAAOiD,GAAG;AACd,CAAC,EAAE7E,yBAAyB,GAAG,SAASA,yBAAyBA,CAAC+B,IAAI,EAAEG,MAAM,EAAE;EAC5E,MAAM6C,QAAQ,GAAGtF,EAAE,CAACyB,WAAW,CAACc,aAAa,CAAC,UAAU,CAAC;EACzDD,IAAI,CAACd,MAAM,CAAC8D,QAAQ,CAAC;EACrB,MAAM1C,UAAU,GAAG,QAAQH,MAAM,EAAE;EACnC6C,QAAQ,CAAC5C,YAAY,CAAC,IAAI,EAAEE,UAAU,CAAC;EACvC0C,QAAQ,CAAC5C,YAAY,CAAC,eAAe,EAAE,mBAAmB,CAAC;EAC3D,MAAM6C,WAAW,GAAGvF,EAAE,CAACyB,WAAW,CAACc,aAAa,CAAC,KAAK,CAAC;EACvD+C,QAAQ,CAAC9D,MAAM,CAAC+D,WAAW,CAAC;EAC5BA,WAAW,CAAC7C,YAAY,CAAC,MAAM,EAAE,IAAID,MAAM,EAAE,CAAC;EAC9C;EACA,OAAOG,UAAU;AACrB,CAAC;AACD,SAAShC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}