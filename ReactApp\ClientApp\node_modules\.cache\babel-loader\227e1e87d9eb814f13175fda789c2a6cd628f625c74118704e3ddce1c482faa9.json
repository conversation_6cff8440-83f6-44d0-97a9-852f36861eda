{"ast": null, "code": "/**\n * A function that compares two dates. The comparison also includes the time portion.\n *\n * @param candidate - The candidate date.\n * @param expected - The expected date.\n * @returns - A Boolean value indicating whether the values are equal.\n *\n * @example\n * ```ts-no-run\n * isEqual(new Date(2016, 0, 1), new Date(2016, 0, 1)); // true\n * isEqual(new Date(2016, 0, 1), new Date(2016, 0, 2)); // false\n * isEqual(new Date(2016, 0, 1, 10), new Date(2016, 0, 1, 20)); // false\n * ```\n */\nexport var isEqual = function (candidate, expected) {\n  if (!candidate && !expected) {\n    return true;\n  }\n  return candidate && expected && candidate.getTime() === expected.getTime();\n};", "map": {"version": 3, "names": ["isEqual", "candidate", "expected", "getTime"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/is-equal.js"], "sourcesContent": ["/**\n * A function that compares two dates. The comparison also includes the time portion.\n *\n * @param candidate - The candidate date.\n * @param expected - The expected date.\n * @returns - A Boolean value indicating whether the values are equal.\n *\n * @example\n * ```ts-no-run\n * isEqual(new Date(2016, 0, 1), new Date(2016, 0, 1)); // true\n * isEqual(new Date(2016, 0, 1), new Date(2016, 0, 2)); // false\n * isEqual(new Date(2016, 0, 1, 10), new Date(2016, 0, 1, 20)); // false\n * ```\n */\nexport var isEqual = function (candidate, expected) {\n    if (!candidate && !expected) {\n        return true;\n    }\n    return candidate && expected && candidate.getTime() === expected.getTime();\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIA,OAAO,GAAG,SAAAA,CAAUC,SAAS,EAAEC,QAAQ,EAAE;EAChD,IAAI,CAACD,SAAS,IAAI,CAACC,QAAQ,EAAE;IACzB,OAAO,IAAI;EACf;EACA,OAAOD,SAAS,IAAIC,QAAQ,IAAID,SAAS,CAACE,OAAO,CAAC,CAAC,KAAKD,QAAQ,CAACC,OAAO,CAAC,CAAC;AAC9E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}