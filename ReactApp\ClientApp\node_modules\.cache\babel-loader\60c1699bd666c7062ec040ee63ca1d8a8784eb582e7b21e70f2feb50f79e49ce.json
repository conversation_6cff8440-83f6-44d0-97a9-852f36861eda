{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { isCompositeFilterDescriptor } from './filtering/filter-descriptor.interface';\nimport { compose, ifElse } from './funcs';\nimport { normalizeField, quote, toLower, isDateValue, isStringValue, serializeFilters, encodeValue, toUTC } from './filter-serialization.common';\nimport { normalizeFilters } from './filtering/filter.operators';\nvar formatDate = function (_a) {\n  var utcDates = _a.utcDates;\n  return function (_a) {\n    var field = _a.field,\n      value = _a.value,\n      ignoreCase = _a.ignoreCase,\n      operator = _a.operator;\n    return {\n      value: (!utcDates ? toUTC(value) : value).toISOString(),\n      field: field,\n      ignoreCase: ignoreCase,\n      operator: operator\n    };\n  };\n};\nvar fnFormatter = function (_a) {\n  var operator = _a.operator;\n  return function (_a) {\n    var field = _a.field,\n      value = _a.value;\n    return \"\".concat(operator, \"(\").concat(field, \",\").concat(value, \")\");\n  };\n};\nvar singleOperatorFormatter = function (_a) {\n  var operator = _a.operator;\n  return function (_a) {\n    var field = _a.field,\n      value = _a.value;\n    return \"\".concat(field, \" \").concat(operator, \" \").concat(value);\n  };\n};\nvar stringFormat = function (formatter) {\n  return compose(formatter, encodeValue, quote, toLower, normalizeField);\n};\nvar stringFnOperator = function (settings) {\n  return stringFormat(fnFormatter(settings));\n};\nvar stringOperator = function (settings) {\n  return stringFormat(singleOperatorFormatter(settings));\n};\nvar numericOperator = function (settings) {\n  return compose(singleOperatorFormatter(settings), normalizeField);\n};\nvar dateOperator = function (settings) {\n  return compose(singleOperatorFormatter(settings), normalizeField, formatDate(settings));\n};\nvar ifDate = function (settings) {\n  return ifElse(isDateValue, dateOperator(settings), numericOperator(settings));\n};\nvar typedOperator = function (settings) {\n  return ifElse(isStringValue, stringOperator(settings), ifDate(settings));\n};\nvar appendEqual = function (str) {\n  return \"\".concat(str, \" eq -1\");\n};\nvar nonValueExpression = function (formatter) {\n  return compose(formatter, normalizeField);\n};\nvar filterOperators = function (operator, settings) {\n  return {\n    contains: stringFnOperator(__assign(__assign({}, settings), {\n      operator: \"contains\"\n    })),\n    doesnotcontain: compose(appendEqual, stringFnOperator(__assign(__assign({}, settings), {\n      operator: \"indexof\"\n    }))),\n    endswith: stringFnOperator(__assign(__assign({}, settings), {\n      operator: \"endswith\"\n    })),\n    eq: typedOperator(__assign(__assign({}, settings), {\n      operator: \"eq\"\n    })),\n    gt: typedOperator(__assign(__assign({}, settings), {\n      operator: \"gt\"\n    })),\n    gte: typedOperator(__assign(__assign({}, settings), {\n      operator: \"ge\"\n    })),\n    isempty: nonValueExpression(function (_a) {\n      var field = _a.field;\n      return \"\".concat(field, \" eq ''\");\n    }),\n    isnotempty: nonValueExpression(function (_a) {\n      var field = _a.field;\n      return \"\".concat(field, \" ne ''\");\n    }),\n    isnotnull: nonValueExpression(function (_a) {\n      var field = _a.field;\n      return \"\".concat(field, \" ne null\");\n    }),\n    isnull: nonValueExpression(function (_a) {\n      var field = _a.field;\n      return \"\".concat(field, \" eq null\");\n    }),\n    lt: typedOperator(__assign(__assign({}, settings), {\n      operator: \"lt\"\n    })),\n    lte: typedOperator(__assign(__assign({}, settings), {\n      operator: \"le\"\n    })),\n    neq: typedOperator(__assign(__assign({}, settings), {\n      operator: \"ne\"\n    })),\n    startswith: stringFnOperator(__assign(__assign({}, settings), {\n      operator: \"startswith\"\n    }))\n  }[operator];\n};\nvar join = function (x) {\n  return \" \".concat(x.logic, \" \");\n};\nvar serialize = function (settings) {\n  return function (x) {\n    return filterOperators(x.operator, settings)(x);\n  };\n};\nvar serializeAll = function (settings) {\n  return serializeFilters(function (filter) {\n    return ifElse(isCompositeFilterDescriptor, serializeAll(settings), serialize(settings))(filter);\n  }, join);\n};\n/**\n * @hidden\n */\nexport var serializeFilter = function (filter, settings) {\n  if (settings === void 0) {\n    settings = {};\n  }\n  if (filter.filters && filter.filters.length) {\n    return \"$filter=\" + serializeAll(settings)(normalizeFilters(filter));\n  }\n  return \"\";\n};", "map": {"version": 3, "names": ["__assign", "isCompositeFilterDescriptor", "compose", "ifElse", "normalizeField", "quote", "<PERSON><PERSON><PERSON><PERSON>", "isDateValue", "isStringValue", "serializeFilters", "encodeValue", "toUTC", "normalizeFilters", "formatDate", "_a", "utcDates", "field", "value", "ignoreCase", "operator", "toISOString", "fnFormatter", "concat", "singleOperatorFormatter", "stringFormat", "formatter", "stringFnOperator", "settings", "stringOperator", "numericOperator", "dateOperator", "ifDate", "typedOperator", "appendEqual", "str", "nonValueExpression", "filterOperators", "contains", "doesnotcontain", "endswith", "eq", "gt", "gte", "isempty", "isnotempty", "isnotnull", "isnull", "lt", "lte", "neq", "startswith", "join", "x", "logic", "serialize", "serializeAll", "filter", "serializeFilter", "filters", "length"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-data-query/dist/es/odata-filtering.operators.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nimport { isCompositeFilterDescriptor } from './filtering/filter-descriptor.interface';\nimport { compose, ifElse } from './funcs';\nimport { normalizeField, quote, toLower, isDateValue, isStringValue, serializeFilters, encodeValue, toUTC } from './filter-serialization.common';\nimport { normalizeFilters } from './filtering/filter.operators';\nvar formatDate = function (_a) {\n    var utcDates = _a.utcDates;\n    return function (_a) {\n        var field = _a.field, value = _a.value, ignoreCase = _a.ignoreCase, operator = _a.operator;\n        return ({\n            value: (!utcDates ? toUTC(value) : value).toISOString(),\n            field: field,\n            ignoreCase: ignoreCase,\n            operator: operator\n        });\n    };\n};\nvar fnFormatter = function (_a) {\n    var operator = _a.operator;\n    return function (_a) {\n        var field = _a.field, value = _a.value;\n        return \"\".concat(operator, \"(\").concat(field, \",\").concat(value, \")\");\n    };\n};\nvar singleOperatorFormatter = function (_a) {\n    var operator = _a.operator;\n    return function (_a) {\n        var field = _a.field, value = _a.value;\n        return \"\".concat(field, \" \").concat(operator, \" \").concat(value);\n    };\n};\nvar stringFormat = function (formatter) { return compose(formatter, encodeValue, quote, toLower, normalizeField); };\nvar stringFnOperator = function (settings) { return stringFormat(fnFormatter(settings)); };\nvar stringOperator = function (settings) { return stringFormat(singleOperatorFormatter(settings)); };\nvar numericOperator = function (settings) { return compose(singleOperatorFormatter(settings), normalizeField); };\nvar dateOperator = function (settings) { return compose(singleOperatorFormatter(settings), normalizeField, formatDate(settings)); };\nvar ifDate = function (settings) { return ifElse(isDateValue, dateOperator(settings), numericOperator(settings)); };\nvar typedOperator = function (settings) { return ifElse(isStringValue, stringOperator(settings), ifDate(settings)); };\nvar appendEqual = function (str) { return \"\".concat(str, \" eq -1\"); };\nvar nonValueExpression = function (formatter) { return compose(formatter, normalizeField); };\nvar filterOperators = function (operator, settings) { return ({\n    contains: stringFnOperator(__assign(__assign({}, settings), { operator: \"contains\" })),\n    doesnotcontain: compose(appendEqual, stringFnOperator(__assign(__assign({}, settings), { operator: \"indexof\" }))),\n    endswith: stringFnOperator(__assign(__assign({}, settings), { operator: \"endswith\" })),\n    eq: typedOperator(__assign(__assign({}, settings), { operator: \"eq\" })),\n    gt: typedOperator(__assign(__assign({}, settings), { operator: \"gt\" })),\n    gte: typedOperator(__assign(__assign({}, settings), { operator: \"ge\" })),\n    isempty: nonValueExpression(function (_a) {\n        var field = _a.field;\n        return \"\".concat(field, \" eq ''\");\n    }),\n    isnotempty: nonValueExpression(function (_a) {\n        var field = _a.field;\n        return \"\".concat(field, \" ne ''\");\n    }),\n    isnotnull: nonValueExpression(function (_a) {\n        var field = _a.field;\n        return \"\".concat(field, \" ne null\");\n    }),\n    isnull: nonValueExpression(function (_a) {\n        var field = _a.field;\n        return \"\".concat(field, \" eq null\");\n    }),\n    lt: typedOperator(__assign(__assign({}, settings), { operator: \"lt\" })),\n    lte: typedOperator(__assign(__assign({}, settings), { operator: \"le\" })),\n    neq: typedOperator(__assign(__assign({}, settings), { operator: \"ne\" })),\n    startswith: stringFnOperator(__assign(__assign({}, settings), { operator: \"startswith\" }))\n}[operator]); };\nvar join = function (x) { return \" \".concat(x.logic, \" \"); };\nvar serialize = function (settings) { return function (x) { return filterOperators(x.operator, settings)(x); }; };\nvar serializeAll = function (settings) { return serializeFilters(function (filter) { return ifElse(isCompositeFilterDescriptor, serializeAll(settings), serialize(settings))(filter); }, join); };\n/**\n * @hidden\n */\nexport var serializeFilter = function (filter, settings) {\n    if (settings === void 0) { settings = {}; }\n    if (filter.filters && filter.filters.length) {\n        return \"$filter=\" + serializeAll(settings)(normalizeFilters(filter));\n    }\n    return \"\";\n};\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,2BAA2B,QAAQ,yCAAyC;AACrF,SAASC,OAAO,EAAEC,MAAM,QAAQ,SAAS;AACzC,SAASC,cAAc,EAAEC,KAAK,EAAEC,OAAO,EAAEC,WAAW,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,KAAK,QAAQ,+BAA+B;AAChJ,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,IAAIC,UAAU,GAAG,SAAAA,CAAUC,EAAE,EAAE;EAC3B,IAAIC,QAAQ,GAAGD,EAAE,CAACC,QAAQ;EAC1B,OAAO,UAAUD,EAAE,EAAE;IACjB,IAAIE,KAAK,GAAGF,EAAE,CAACE,KAAK;MAAEC,KAAK,GAAGH,EAAE,CAACG,KAAK;MAAEC,UAAU,GAAGJ,EAAE,CAACI,UAAU;MAAEC,QAAQ,GAAGL,EAAE,CAACK,QAAQ;IAC1F,OAAQ;MACJF,KAAK,EAAE,CAAC,CAACF,QAAQ,GAAGJ,KAAK,CAACM,KAAK,CAAC,GAAGA,KAAK,EAAEG,WAAW,CAAC,CAAC;MACvDJ,KAAK,EAAEA,KAAK;MACZE,UAAU,EAAEA,UAAU;MACtBC,QAAQ,EAAEA;IACd,CAAC;EACL,CAAC;AACL,CAAC;AACD,IAAIE,WAAW,GAAG,SAAAA,CAAUP,EAAE,EAAE;EAC5B,IAAIK,QAAQ,GAAGL,EAAE,CAACK,QAAQ;EAC1B,OAAO,UAAUL,EAAE,EAAE;IACjB,IAAIE,KAAK,GAAGF,EAAE,CAACE,KAAK;MAAEC,KAAK,GAAGH,EAAE,CAACG,KAAK;IACtC,OAAO,EAAE,CAACK,MAAM,CAACH,QAAQ,EAAE,GAAG,CAAC,CAACG,MAAM,CAACN,KAAK,EAAE,GAAG,CAAC,CAACM,MAAM,CAACL,KAAK,EAAE,GAAG,CAAC;EACzE,CAAC;AACL,CAAC;AACD,IAAIM,uBAAuB,GAAG,SAAAA,CAAUT,EAAE,EAAE;EACxC,IAAIK,QAAQ,GAAGL,EAAE,CAACK,QAAQ;EAC1B,OAAO,UAAUL,EAAE,EAAE;IACjB,IAAIE,KAAK,GAAGF,EAAE,CAACE,KAAK;MAAEC,KAAK,GAAGH,EAAE,CAACG,KAAK;IACtC,OAAO,EAAE,CAACK,MAAM,CAACN,KAAK,EAAE,GAAG,CAAC,CAACM,MAAM,CAACH,QAAQ,EAAE,GAAG,CAAC,CAACG,MAAM,CAACL,KAAK,CAAC;EACpE,CAAC;AACL,CAAC;AACD,IAAIO,YAAY,GAAG,SAAAA,CAAUC,SAAS,EAAE;EAAE,OAAOvB,OAAO,CAACuB,SAAS,EAAEf,WAAW,EAAEL,KAAK,EAAEC,OAAO,EAAEF,cAAc,CAAC;AAAE,CAAC;AACnH,IAAIsB,gBAAgB,GAAG,SAAAA,CAAUC,QAAQ,EAAE;EAAE,OAAOH,YAAY,CAACH,WAAW,CAACM,QAAQ,CAAC,CAAC;AAAE,CAAC;AAC1F,IAAIC,cAAc,GAAG,SAAAA,CAAUD,QAAQ,EAAE;EAAE,OAAOH,YAAY,CAACD,uBAAuB,CAACI,QAAQ,CAAC,CAAC;AAAE,CAAC;AACpG,IAAIE,eAAe,GAAG,SAAAA,CAAUF,QAAQ,EAAE;EAAE,OAAOzB,OAAO,CAACqB,uBAAuB,CAACI,QAAQ,CAAC,EAAEvB,cAAc,CAAC;AAAE,CAAC;AAChH,IAAI0B,YAAY,GAAG,SAAAA,CAAUH,QAAQ,EAAE;EAAE,OAAOzB,OAAO,CAACqB,uBAAuB,CAACI,QAAQ,CAAC,EAAEvB,cAAc,EAAES,UAAU,CAACc,QAAQ,CAAC,CAAC;AAAE,CAAC;AACnI,IAAII,MAAM,GAAG,SAAAA,CAAUJ,QAAQ,EAAE;EAAE,OAAOxB,MAAM,CAACI,WAAW,EAAEuB,YAAY,CAACH,QAAQ,CAAC,EAAEE,eAAe,CAACF,QAAQ,CAAC,CAAC;AAAE,CAAC;AACnH,IAAIK,aAAa,GAAG,SAAAA,CAAUL,QAAQ,EAAE;EAAE,OAAOxB,MAAM,CAACK,aAAa,EAAEoB,cAAc,CAACD,QAAQ,CAAC,EAAEI,MAAM,CAACJ,QAAQ,CAAC,CAAC;AAAE,CAAC;AACrH,IAAIM,WAAW,GAAG,SAAAA,CAAUC,GAAG,EAAE;EAAE,OAAO,EAAE,CAACZ,MAAM,CAACY,GAAG,EAAE,QAAQ,CAAC;AAAE,CAAC;AACrE,IAAIC,kBAAkB,GAAG,SAAAA,CAAUV,SAAS,EAAE;EAAE,OAAOvB,OAAO,CAACuB,SAAS,EAAErB,cAAc,CAAC;AAAE,CAAC;AAC5F,IAAIgC,eAAe,GAAG,SAAAA,CAAUjB,QAAQ,EAAEQ,QAAQ,EAAE;EAAE,OAAQ;IAC1DU,QAAQ,EAAEX,gBAAgB,CAAC1B,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE2B,QAAQ,CAAC,EAAE;MAAER,QAAQ,EAAE;IAAW,CAAC,CAAC,CAAC;IACtFmB,cAAc,EAAEpC,OAAO,CAAC+B,WAAW,EAAEP,gBAAgB,CAAC1B,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE2B,QAAQ,CAAC,EAAE;MAAER,QAAQ,EAAE;IAAU,CAAC,CAAC,CAAC,CAAC;IACjHoB,QAAQ,EAAEb,gBAAgB,CAAC1B,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE2B,QAAQ,CAAC,EAAE;MAAER,QAAQ,EAAE;IAAW,CAAC,CAAC,CAAC;IACtFqB,EAAE,EAAER,aAAa,CAAChC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE2B,QAAQ,CAAC,EAAE;MAAER,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC;IACvEsB,EAAE,EAAET,aAAa,CAAChC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE2B,QAAQ,CAAC,EAAE;MAAER,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC;IACvEuB,GAAG,EAAEV,aAAa,CAAChC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE2B,QAAQ,CAAC,EAAE;MAAER,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC;IACxEwB,OAAO,EAAER,kBAAkB,CAAC,UAAUrB,EAAE,EAAE;MACtC,IAAIE,KAAK,GAAGF,EAAE,CAACE,KAAK;MACpB,OAAO,EAAE,CAACM,MAAM,CAACN,KAAK,EAAE,QAAQ,CAAC;IACrC,CAAC,CAAC;IACF4B,UAAU,EAAET,kBAAkB,CAAC,UAAUrB,EAAE,EAAE;MACzC,IAAIE,KAAK,GAAGF,EAAE,CAACE,KAAK;MACpB,OAAO,EAAE,CAACM,MAAM,CAACN,KAAK,EAAE,QAAQ,CAAC;IACrC,CAAC,CAAC;IACF6B,SAAS,EAAEV,kBAAkB,CAAC,UAAUrB,EAAE,EAAE;MACxC,IAAIE,KAAK,GAAGF,EAAE,CAACE,KAAK;MACpB,OAAO,EAAE,CAACM,MAAM,CAACN,KAAK,EAAE,UAAU,CAAC;IACvC,CAAC,CAAC;IACF8B,MAAM,EAAEX,kBAAkB,CAAC,UAAUrB,EAAE,EAAE;MACrC,IAAIE,KAAK,GAAGF,EAAE,CAACE,KAAK;MACpB,OAAO,EAAE,CAACM,MAAM,CAACN,KAAK,EAAE,UAAU,CAAC;IACvC,CAAC,CAAC;IACF+B,EAAE,EAAEf,aAAa,CAAChC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE2B,QAAQ,CAAC,EAAE;MAAER,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC;IACvE6B,GAAG,EAAEhB,aAAa,CAAChC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE2B,QAAQ,CAAC,EAAE;MAAER,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC;IACxE8B,GAAG,EAAEjB,aAAa,CAAChC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE2B,QAAQ,CAAC,EAAE;MAAER,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC;IACxE+B,UAAU,EAAExB,gBAAgB,CAAC1B,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE2B,QAAQ,CAAC,EAAE;MAAER,QAAQ,EAAE;IAAa,CAAC,CAAC;EAC7F,CAAC,CAACA,QAAQ,CAAC;AAAG,CAAC;AACf,IAAIgC,IAAI,GAAG,SAAAA,CAAUC,CAAC,EAAE;EAAE,OAAO,GAAG,CAAC9B,MAAM,CAAC8B,CAAC,CAACC,KAAK,EAAE,GAAG,CAAC;AAAE,CAAC;AAC5D,IAAIC,SAAS,GAAG,SAAAA,CAAU3B,QAAQ,EAAE;EAAE,OAAO,UAAUyB,CAAC,EAAE;IAAE,OAAOhB,eAAe,CAACgB,CAAC,CAACjC,QAAQ,EAAEQ,QAAQ,CAAC,CAACyB,CAAC,CAAC;EAAE,CAAC;AAAE,CAAC;AACjH,IAAIG,YAAY,GAAG,SAAAA,CAAU5B,QAAQ,EAAE;EAAE,OAAOlB,gBAAgB,CAAC,UAAU+C,MAAM,EAAE;IAAE,OAAOrD,MAAM,CAACF,2BAA2B,EAAEsD,YAAY,CAAC5B,QAAQ,CAAC,EAAE2B,SAAS,CAAC3B,QAAQ,CAAC,CAAC,CAAC6B,MAAM,CAAC;EAAE,CAAC,EAAEL,IAAI,CAAC;AAAE,CAAC;AACjM;AACA;AACA;AACA,OAAO,IAAIM,eAAe,GAAG,SAAAA,CAAUD,MAAM,EAAE7B,QAAQ,EAAE;EACrD,IAAIA,QAAQ,KAAK,KAAK,CAAC,EAAE;IAAEA,QAAQ,GAAG,CAAC,CAAC;EAAE;EAC1C,IAAI6B,MAAM,CAACE,OAAO,IAAIF,MAAM,CAACE,OAAO,CAACC,MAAM,EAAE;IACzC,OAAO,UAAU,GAAGJ,YAAY,CAAC5B,QAAQ,CAAC,CAACf,gBAAgB,CAAC4C,MAAM,CAAC,CAAC;EACxE;EACA,OAAO,EAAE;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}