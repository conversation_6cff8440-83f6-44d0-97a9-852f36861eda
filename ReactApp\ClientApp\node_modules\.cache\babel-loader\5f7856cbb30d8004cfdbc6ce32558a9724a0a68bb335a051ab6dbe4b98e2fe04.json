{"ast": null, "code": "export default function promiseAll(promises) {\n  return Promise.all(promises);\n}", "map": {"version": 3, "names": ["promiseAll", "promises", "Promise", "all"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/util/promise-all.js"], "sourcesContent": ["export default function promiseAll(promises) {\n    return Promise.all(promises);\n}"], "mappings": "AAAA,eAAe,SAASA,UAAUA,CAACC,QAAQ,EAAE;EACzC,OAAOC,OAAO,CAACC,GAAG,CAACF,QAAQ,CAAC;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}