{"ast": null, "code": "var eitherRect = function (rect, offset) {\n  if (!rect) {\n    return {\n      height: 0,\n      left: offset.left,\n      top: offset.top,\n      width: 0\n    };\n  }\n  return rect;\n};\nvar scaleRect = function (rect, scale) {\n  if (!rect || scale === 1) {\n    return rect;\n  }\n  return {\n    height: rect.height / scale,\n    left: rect.left / scale,\n    top: rect.top / scale,\n    width: rect.width / scale\n  };\n};\nvar removeStackingOffset = function (rect, stackingOffset) {\n  if (!stackingOffset) {\n    return rect;\n  }\n  var result = {\n    height: rect.height,\n    left: rect.left - stackingOffset.left,\n    top: rect.top - stackingOffset.top,\n    width: rect.width\n  };\n  return result;\n};\nfunction memoize(fun) {\n  var result;\n  var called = false;\n  return function () {\n    var args = [],\n      len = arguments.length;\n    while (len--) args[len] = arguments[len];\n    if (called) {\n      return result;\n    }\n    result = fun.apply(void 0, args);\n    called = true;\n    return result;\n  };\n}\nvar hasRelativeStackingContext = memoize(function (elementSource) {\n  if (!canUseDOM()) {\n    return false;\n  }\n\n  // Component need to pass element to make sure document owner is correct.\n  // This however might be performance hit if checked for example on each drag event.\n  var currentDocument = elementSource ? elementSource.ownerDocument : document;\n  if (!currentDocument || !currentDocument.body) {\n    return false;\n  }\n  var top = 10;\n  var parent = currentDocument.createElement(\"div\");\n  parent.style.transform = \"matrix(10, 0, 0, 10, 0, 0)\";\n  parent.innerHTML = \"<div style=\\\"position: fixed; top: \" + top + \"px;\\\">child</div>\";\n  currentDocument.body.appendChild(parent);\n  var isDifferent = parent.children[0].getBoundingClientRect().top !== top;\n  currentDocument.body.removeChild(parent);\n  return isDifferent;\n});\nvar canUseDOM = function () {\n  return Boolean(\n  // from fbjs\n  typeof window !== 'undefined' && window.document && window.document.createElement);\n};\nvar utils = {\n  eitherRect: eitherRect,\n  scaleRect: scaleRect,\n  removeStackingOffset: removeStackingOffset,\n  hasRelativeStackingContext: hasRelativeStackingContext,\n  canUseDOM: canUseDOM\n};\nexport default utils;", "map": {"version": 3, "names": ["eitherRect", "rect", "offset", "height", "left", "top", "width", "scaleRect", "scale", "removeStackingOffset", "stackingOffset", "result", "memoize", "fun", "called", "args", "len", "arguments", "length", "apply", "hasRelativeStackingContext", "elementSource", "canUseDOM", "currentDocument", "ownerDocument", "document", "body", "parent", "createElement", "style", "transform", "innerHTML", "append<PERSON><PERSON><PERSON>", "isDifferent", "children", "getBoundingClientRect", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "window", "utils"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-popup-common/dist/es/utils.js"], "sourcesContent": ["\nvar eitherRect = function (rect, offset) {\n    if (!rect) {\n        return { height: 0, left: offset.left, top: offset.top, width: 0 };\n    }\n\n    return rect;\n};\n\nvar scaleRect = function (rect, scale) {\n    if (!rect || scale === 1) {\n        return rect;\n    }\n\n    return {\n        height: rect.height / scale,\n        left: rect.left / scale,\n        top: rect.top / scale,\n        width: rect.width / scale\n    };\n};\n\nvar removeStackingOffset = function (rect, stackingOffset) {\n    if (!stackingOffset) { return rect; }\n\n    var result = {\n        height: rect.height,\n        left: rect.left - stackingOffset.left,\n        top: rect.top - stackingOffset.top,\n        width: rect.width\n    };\n\n    return result;\n};\n\nfunction memoize(fun) {\n    var result;\n    var called = false;\n\n    return function () {\n        var args = [], len = arguments.length;\n        while ( len-- ) args[ len ] = arguments[ len ];\n\n        if (called) {\n            return result;\n        }\n\n        result = fun.apply(void 0, args);\n        called = true;\n        return result;\n    };\n}\n\nvar hasRelativeStackingContext = memoize(function (elementSource) {\n    if (!canUseDOM()) { return false; }\n\n    // Component need to pass element to make sure document owner is correct.\n    // This however might be performance hit if checked for example on each drag event.\n    var currentDocument = elementSource ? elementSource.ownerDocument : document;\n\n    if (!currentDocument || !currentDocument.body) { return false; }\n\n    var top = 10;\n    var parent = currentDocument.createElement(\"div\");\n    parent.style.transform = \"matrix(10, 0, 0, 10, 0, 0)\";\n    parent.innerHTML = \"<div style=\\\"position: fixed; top: \" + top + \"px;\\\">child</div>\";\n\n    currentDocument.body.appendChild(parent);\n\n    var isDifferent = parent.children[0].getBoundingClientRect().top !== top;\n\n    currentDocument.body.removeChild(parent);\n\n    return isDifferent;\n});\n\nvar canUseDOM = function () { return Boolean(\n    // from fbjs\n    typeof window !== 'undefined' &&\n    window.document &&\n    window.document.createElement\n); };\n\nvar utils = {\n    eitherRect: eitherRect,\n    scaleRect: scaleRect,\n    removeStackingOffset: removeStackingOffset,\n    hasRelativeStackingContext: hasRelativeStackingContext,\n    canUseDOM: canUseDOM\n};\n\nexport default utils;"], "mappings": "AACA,IAAIA,UAAU,GAAG,SAAAA,CAAUC,IAAI,EAAEC,MAAM,EAAE;EACrC,IAAI,CAACD,IAAI,EAAE;IACP,OAAO;MAAEE,MAAM,EAAE,CAAC;MAAEC,IAAI,EAAEF,MAAM,CAACE,IAAI;MAAEC,GAAG,EAAEH,MAAM,CAACG,GAAG;MAAEC,KAAK,EAAE;IAAE,CAAC;EACtE;EAEA,OAAOL,IAAI;AACf,CAAC;AAED,IAAIM,SAAS,GAAG,SAAAA,CAAUN,IAAI,EAAEO,KAAK,EAAE;EACnC,IAAI,CAACP,IAAI,IAAIO,KAAK,KAAK,CAAC,EAAE;IACtB,OAAOP,IAAI;EACf;EAEA,OAAO;IACHE,MAAM,EAAEF,IAAI,CAACE,MAAM,GAAGK,KAAK;IAC3BJ,IAAI,EAAEH,IAAI,CAACG,IAAI,GAAGI,KAAK;IACvBH,GAAG,EAAEJ,IAAI,CAACI,GAAG,GAAGG,KAAK;IACrBF,KAAK,EAAEL,IAAI,CAACK,KAAK,GAAGE;EACxB,CAAC;AACL,CAAC;AAED,IAAIC,oBAAoB,GAAG,SAAAA,CAAUR,IAAI,EAAES,cAAc,EAAE;EACvD,IAAI,CAACA,cAAc,EAAE;IAAE,OAAOT,IAAI;EAAE;EAEpC,IAAIU,MAAM,GAAG;IACTR,MAAM,EAAEF,IAAI,CAACE,MAAM;IACnBC,IAAI,EAAEH,IAAI,CAACG,IAAI,GAAGM,cAAc,CAACN,IAAI;IACrCC,GAAG,EAAEJ,IAAI,CAACI,GAAG,GAAGK,cAAc,CAACL,GAAG;IAClCC,KAAK,EAAEL,IAAI,CAACK;EAChB,CAAC;EAED,OAAOK,MAAM;AACjB,CAAC;AAED,SAASC,OAAOA,CAACC,GAAG,EAAE;EAClB,IAAIF,MAAM;EACV,IAAIG,MAAM,GAAG,KAAK;EAElB,OAAO,YAAY;IACf,IAAIC,IAAI,GAAG,EAAE;MAAEC,GAAG,GAAGC,SAAS,CAACC,MAAM;IACrC,OAAQF,GAAG,EAAE,EAAGD,IAAI,CAAEC,GAAG,CAAE,GAAGC,SAAS,CAAED,GAAG,CAAE;IAE9C,IAAIF,MAAM,EAAE;MACR,OAAOH,MAAM;IACjB;IAEAA,MAAM,GAAGE,GAAG,CAACM,KAAK,CAAC,KAAK,CAAC,EAAEJ,IAAI,CAAC;IAChCD,MAAM,GAAG,IAAI;IACb,OAAOH,MAAM;EACjB,CAAC;AACL;AAEA,IAAIS,0BAA0B,GAAGR,OAAO,CAAC,UAAUS,aAAa,EAAE;EAC9D,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;;EAElC;EACA;EACA,IAAIC,eAAe,GAAGF,aAAa,GAAGA,aAAa,CAACG,aAAa,GAAGC,QAAQ;EAE5E,IAAI,CAACF,eAAe,IAAI,CAACA,eAAe,CAACG,IAAI,EAAE;IAAE,OAAO,KAAK;EAAE;EAE/D,IAAIrB,GAAG,GAAG,EAAE;EACZ,IAAIsB,MAAM,GAAGJ,eAAe,CAACK,aAAa,CAAC,KAAK,CAAC;EACjDD,MAAM,CAACE,KAAK,CAACC,SAAS,GAAG,4BAA4B;EACrDH,MAAM,CAACI,SAAS,GAAG,qCAAqC,GAAG1B,GAAG,GAAG,mBAAmB;EAEpFkB,eAAe,CAACG,IAAI,CAACM,WAAW,CAACL,MAAM,CAAC;EAExC,IAAIM,WAAW,GAAGN,MAAM,CAACO,QAAQ,CAAC,CAAC,CAAC,CAACC,qBAAqB,CAAC,CAAC,CAAC9B,GAAG,KAAKA,GAAG;EAExEkB,eAAe,CAACG,IAAI,CAACU,WAAW,CAACT,MAAM,CAAC;EAExC,OAAOM,WAAW;AACtB,CAAC,CAAC;AAEF,IAAIX,SAAS,GAAG,SAAAA,CAAA,EAAY;EAAE,OAAOe,OAAO;EACxC;EACA,OAAOC,MAAM,KAAK,WAAW,IAC7BA,MAAM,CAACb,QAAQ,IACfa,MAAM,CAACb,QAAQ,CAACG,aACpB,CAAC;AAAE,CAAC;AAEJ,IAAIW,KAAK,GAAG;EACRvC,UAAU,EAAEA,UAAU;EACtBO,SAAS,EAAEA,SAAS;EACpBE,oBAAoB,EAAEA,oBAAoB;EAC1CW,0BAA0B,EAAEA,0BAA0B;EACtDE,SAAS,EAAEA;AACf,CAAC;AAED,eAAeiB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}