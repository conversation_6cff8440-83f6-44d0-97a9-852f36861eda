{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useEffect, useRef, useState } from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport { useAcceptTncMutation, useEvaluateTncQuery } from '@app/api/tncApiSlice';\nimport { useAppSelector } from '@app/hooks/useAppSelector';\nimport { errorNotification } from '@app/utils/antNotifications';\nexport const useTermsAndConditions = () => {\n  _s();\n  var _location$state;\n  const navigate = useNavigate();\n  const location = useLocation();\n  const pdfViewerRef = useRef(null);\n  const [pdfDocument, setPdfDocument] = useState(null);\n  const tncDocument = useAppSelector(state => state.app.tnc);\n  const redirectPath = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.from) || '/';\n  const {\n    refetch: tncRefetch,\n    isFetching: tncLoading\n  } = useEvaluateTncQuery();\n  const [acceptTnc, {\n    isLoading: isAccepting\n  }] = useAcceptTncMutation();\n  useEffect(() => {\n    if (tncDocument.isValidated || !tncDocument.document) {\n      navigate(redirectPath);\n    }\n  }, [tncDocument, navigate]);\n  const onDocumentLoad = event => {\n    setPdfDocument(event.target);\n  };\n  const handleAgree = async () => {\n    let messageText;\n    try {\n      await acceptTnc({\n        termsAndConditionsId: tncDocument.document.termsAndConditionsId\n      }).unwrap();\n      navigate(redirectPath);\n    } catch (err) {\n      var _err$data;\n      messageText = 'Terms & Conditions acceptance failed';\n      if ((err === null || err === void 0 ? void 0 : err.status) >= 400 && (err === null || err === void 0 ? void 0 : err.status) < 500 && err !== null && err !== void 0 && (_err$data = err.data) !== null && _err$data !== void 0 && _err$data.message) {\n        var _err$data2;\n        messageText = err === null || err === void 0 ? void 0 : (_err$data2 = err.data) === null || _err$data2 === void 0 ? void 0 : _err$data2.message;\n      }\n      errorNotification([''], messageText);\n    }\n  };\n  const clickToolbarButtonByTitle = title => {\n    const button = document.querySelector(`button[title=\"${title}\"]`);\n    button === null || button === void 0 ? void 0 : button.click();\n  };\n  return {\n    tncDocument,\n    pdfViewerRef,\n    pdfDocument,\n    setPdfDocument,\n    tncRefetch,\n    tncLoading,\n    isAccepting,\n    onDocumentLoad,\n    handleAgree,\n    clickToolbarButtonByTitle\n  };\n};\n_s(useTermsAndConditions, \"s5ogJPJ64R313vsJC9KSA27UR18=\", false, function () {\n  return [useNavigate, useLocation, useAppSelector, useEvaluateTncQuery, useAcceptTncMutation];\n});", "map": {"version": 3, "names": ["useEffect", "useRef", "useState", "useLocation", "useNavigate", "useAcceptTncMutation", "useEvaluateTncQuery", "useAppSelector", "errorNotification", "useTermsAndConditions", "_s", "_location$state", "navigate", "location", "pdfViewerRef", "pdfDocument", "setPdfDocument", "tncDocument", "state", "app", "tnc", "redirectPath", "from", "refetch", "tncRefetch", "isFetching", "tncLoading", "acceptTnc", "isLoading", "isAccepting", "isValidated", "document", "onDocumentLoad", "event", "target", "handleAgree", "messageText", "termsAndConditionsId", "unwrap", "err", "_err$data", "status", "data", "message", "_err$data2", "clickToolbarButtonByTitle", "title", "button", "querySelector", "click"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/src/app/hooks/useTermsAndConditions.ts"], "sourcesContent": ["import { useEffect, useRef, useState } from 'react';\r\nimport { useLocation, useNavigate } from 'react-router-dom';\r\nimport { useAcceptTncMutation, useEvaluateTncQuery } from '@app/api/tncApiSlice';\r\nimport { useAppSelector } from '@app/hooks/useAppSelector';\r\nimport { errorNotification } from '@app/utils/antNotifications';\r\n\r\nexport const useTermsAndConditions = () => {\r\n    const navigate = useNavigate();\r\n    const location = useLocation();\r\n    const pdfViewerRef = useRef<any>(null);\r\n    const [pdfDocument, setPdfDocument] = useState<any>(null);\r\n    const tncDocument = useAppSelector((state) => state.app.tnc);\r\n\r\n    const redirectPath = location.state?.from || '/';\r\n\r\n    const { refetch: tncRefetch, isFetching: tncLoading } = useEvaluateTncQuery();\r\n    const [acceptTnc, { isLoading: isAccepting }] = useAcceptTncMutation();\r\n\r\n    useEffect(() => {\r\n        if (tncDocument.isValidated || !tncDocument.document) {\r\n            navigate(redirectPath);\r\n        }\r\n    }, [tncDocument, navigate]);\r\n\r\n    const onDocumentLoad = (event: any) => {\r\n        setPdfDocument(event.target);\r\n    };\r\n\r\n    const handleAgree = async () => {\r\n        let messageText;\r\n        try {\r\n            await acceptTnc({ termsAndConditionsId: tncDocument.document!.termsAndConditionsId }).unwrap();\r\n            navigate(redirectPath);\r\n        } catch (err: any) {\r\n            messageText = 'Terms & Conditions acceptance failed';\r\n            if (err?.status >= 400 && err?.status < 500 && err?.data?.message) {\r\n               messageText = err?.data?.message;\r\n            } \r\n            errorNotification([''], messageText);\r\n        }\r\n    };\r\n\r\n    const clickToolbarButtonByTitle = (title: string) => {\r\n        const button = document.querySelector(`button[title=\"${title}\"]`) as HTMLElement;\r\n        button?.click();\r\n    };\r\n\r\n\r\n    return {\r\n        tncDocument,\r\n        pdfViewerRef,\r\n        pdfDocument,\r\n        setPdfDocument,\r\n        tncRefetch,\r\n        tncLoading,\r\n        isAccepting,\r\n        onDocumentLoad,\r\n        handleAgree,\r\n        clickToolbarButtonByTitle\r\n    };\r\n};\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,oBAAoB,EAAEC,mBAAmB,QAAQ,sBAAsB;AAChF,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,iBAAiB,QAAQ,6BAA6B;AAE/D,OAAO,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA;EACvC,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAMS,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,YAAY,GAAGb,MAAM,CAAM,IAAI,CAAC;EACtC,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAM,IAAI,CAAC;EACzD,MAAMe,WAAW,GAAGV,cAAc,CAAEW,KAAK,IAAKA,KAAK,CAACC,GAAG,CAACC,GAAG,CAAC;EAE5D,MAAMC,YAAY,GAAG,EAAAV,eAAA,GAAAE,QAAQ,CAACK,KAAK,cAAAP,eAAA,uBAAdA,eAAA,CAAgBW,IAAI,KAAI,GAAG;EAEhD,MAAM;IAAEC,OAAO,EAAEC,UAAU;IAAEC,UAAU,EAAEC;EAAW,CAAC,GAAGpB,mBAAmB,CAAC,CAAC;EAC7E,MAAM,CAACqB,SAAS,EAAE;IAAEC,SAAS,EAAEC;EAAY,CAAC,CAAC,GAAGxB,oBAAoB,CAAC,CAAC;EAEtEL,SAAS,CAAC,MAAM;IACZ,IAAIiB,WAAW,CAACa,WAAW,IAAI,CAACb,WAAW,CAACc,QAAQ,EAAE;MAClDnB,QAAQ,CAACS,YAAY,CAAC;IAC1B;EACJ,CAAC,EAAE,CAACJ,WAAW,EAAEL,QAAQ,CAAC,CAAC;EAE3B,MAAMoB,cAAc,GAAIC,KAAU,IAAK;IACnCjB,cAAc,CAACiB,KAAK,CAACC,MAAM,CAAC;EAChC,CAAC;EAED,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAIC,WAAW;IACf,IAAI;MACA,MAAMT,SAAS,CAAC;QAAEU,oBAAoB,EAAEpB,WAAW,CAACc,QAAQ,CAAEM;MAAqB,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;MAC9F1B,QAAQ,CAACS,YAAY,CAAC;IAC1B,CAAC,CAAC,OAAOkB,GAAQ,EAAE;MAAA,IAAAC,SAAA;MACfJ,WAAW,GAAG,sCAAsC;MACpD,IAAI,CAAAG,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEE,MAAM,KAAI,GAAG,IAAI,CAAAF,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEE,MAAM,IAAG,GAAG,IAAIF,GAAG,aAAHA,GAAG,gBAAAC,SAAA,GAAHD,GAAG,CAAEG,IAAI,cAAAF,SAAA,eAATA,SAAA,CAAWG,OAAO,EAAE;QAAA,IAAAC,UAAA;QAChER,WAAW,GAAGG,GAAG,aAAHA,GAAG,wBAAAK,UAAA,GAAHL,GAAG,CAAEG,IAAI,cAAAE,UAAA,uBAATA,UAAA,CAAWD,OAAO;MACnC;MACAnC,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE4B,WAAW,CAAC;IACxC;EACJ,CAAC;EAED,MAAMS,yBAAyB,GAAIC,KAAa,IAAK;IACjD,MAAMC,MAAM,GAAGhB,QAAQ,CAACiB,aAAa,CAAC,iBAAiBF,KAAK,IAAI,CAAgB;IAChFC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEE,KAAK,CAAC,CAAC;EACnB,CAAC;EAGD,OAAO;IACHhC,WAAW;IACXH,YAAY;IACZC,WAAW;IACXC,cAAc;IACdQ,UAAU;IACVE,UAAU;IACVG,WAAW;IACXG,cAAc;IACdG,WAAW;IACXU;EACJ,CAAC;AACL,CAAC;AAACnC,EAAA,CAtDWD,qBAAqB;EAAA,QACbL,WAAW,EACXD,WAAW,EAGRI,cAAc,EAIsBD,mBAAmB,EAC3BD,oBAAoB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}