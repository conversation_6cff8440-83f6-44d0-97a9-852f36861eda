{"ast": null, "code": "import { exec, map, groupCombinator } from '../transducers';\nimport { isArray, isPresent } from '../utils';\nimport { aggregateBy } from './aggregate.operators';\nimport { filterBy } from '../filtering/filter-expression.factory';\n/**\n * @hidden\n */\nexport var normalizeGroups = function (descriptors) {\n  descriptors = isArray(descriptors) ? descriptors : [descriptors];\n  return descriptors.map(function (x) {\n    return Object.assign({\n      dir: \"asc\"\n    }, x);\n  });\n};\nvar identity = map(function (x) {\n  return x;\n});\n/**\n * Groups the provided data according to the specified descriptors.\n *\n * @param {Array} data - The data that will be grouped.\n * @param {GroupDescriptor[]} descriptors - The descriptors.\n * @param {any} transformers - For internal use.\n * @param {Array} originalData - For internal use.\n * @returns {(Array<GroupResult<T>> | T[])} - The grouped data.\n *\n * @example\n * ```ts\n *\n * import { groupBy } from '@progress/kendo-data-query';\n *\n * const data = [\n *     { name: \"Pork\", category: \"Food\", subcategory: \"Meat\" },\n *     { name: \"Pepper\", category: \"Food\", subcategory: \"Vegetables\" },\n *     { name: \"Beef\", category: \"Food\", subcategory: \"Meat\" }\n * ];\n *\n * const result = groupBy(data, [{ field: \"subcategory\" }]);\n * ```\n */\nexport var groupBy = function (data, descriptors, transformers, originalData) {\n  if (descriptors === void 0) {\n    descriptors = [];\n  }\n  if (transformers === void 0) {\n    transformers = identity;\n  }\n  if (originalData === void 0) {\n    originalData = data;\n  }\n  descriptors = normalizeGroups(descriptors);\n  if (!descriptors.length) {\n    return data;\n  }\n  var descriptor = descriptors[0];\n  var initialValue = {};\n  var view = exec(transformers(groupCombinator(descriptor.field)), initialValue, data);\n  var result = [];\n  Object.keys(view).forEach(function (field) {\n    Object.keys(view[field]).forEach(function (value) {\n      var group = view[field][value];\n      var aggregateResult = {};\n      var filteredData = originalData;\n      if (isPresent(descriptor.aggregates)) {\n        filteredData = filterBy(originalData, {\n          field: descriptor.field,\n          ignoreCase: false,\n          operator: 'eq',\n          value: group.value\n        });\n        aggregateResult = aggregateBy(filteredData, descriptor.aggregates);\n      }\n      result[group.__position] = {\n        aggregates: aggregateResult,\n        field: field,\n        items: descriptors.length > 1 ? groupBy(group.items, descriptors.slice(1), identity, filteredData) : group.items,\n        value: group.value\n      };\n    });\n  });\n  return result;\n};", "map": {"version": 3, "names": ["exec", "map", "groupCombinator", "isArray", "isPresent", "aggregateBy", "filterBy", "normalizeGroups", "descriptors", "x", "Object", "assign", "dir", "identity", "groupBy", "data", "transformers", "originalData", "length", "descriptor", "initialValue", "view", "field", "result", "keys", "for<PERSON>ach", "value", "group", "aggregateResult", "filteredData", "aggregates", "ignoreCase", "operator", "__position", "items", "slice"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-data-query/dist/es/grouping/group.operators.js"], "sourcesContent": ["import { exec, map, groupCombinator } from '../transducers';\nimport { isArray, isPresent } from '../utils';\nimport { aggregateBy } from './aggregate.operators';\nimport { filterBy } from '../filtering/filter-expression.factory';\n/**\n * @hidden\n */\nexport var normalizeGroups = function (descriptors) {\n    descriptors = isArray(descriptors) ? descriptors : [descriptors];\n    return descriptors.map(function (x) { return Object.assign({ dir: \"asc\" }, x); });\n};\nvar identity = map(function (x) { return x; });\n/**\n * Groups the provided data according to the specified descriptors.\n *\n * @param {Array} data - The data that will be grouped.\n * @param {GroupDescriptor[]} descriptors - The descriptors.\n * @param {any} transformers - For internal use.\n * @param {Array} originalData - For internal use.\n * @returns {(Array<GroupResult<T>> | T[])} - The grouped data.\n *\n * @example\n * ```ts\n *\n * import { groupBy } from '@progress/kendo-data-query';\n *\n * const data = [\n *     { name: \"Pork\", category: \"Food\", subcategory: \"Meat\" },\n *     { name: \"Pepper\", category: \"Food\", subcategory: \"Vegetables\" },\n *     { name: \"Beef\", category: \"Food\", subcategory: \"Meat\" }\n * ];\n *\n * const result = groupBy(data, [{ field: \"subcategory\" }]);\n * ```\n */\nexport var groupBy = function (data, descriptors, transformers, originalData) {\n    if (descriptors === void 0) { descriptors = []; }\n    if (transformers === void 0) { transformers = identity; }\n    if (originalData === void 0) { originalData = data; }\n    descriptors = normalizeGroups(descriptors);\n    if (!descriptors.length) {\n        return data;\n    }\n    var descriptor = descriptors[0];\n    var initialValue = {};\n    var view = exec(transformers(groupCombinator(descriptor.field)), initialValue, data);\n    var result = [];\n    Object.keys(view).forEach(function (field) {\n        Object.keys(view[field]).forEach(function (value) {\n            var group = view[field][value];\n            var aggregateResult = {};\n            var filteredData = originalData;\n            if (isPresent(descriptor.aggregates)) {\n                filteredData = filterBy(originalData, {\n                    field: descriptor.field,\n                    ignoreCase: false,\n                    operator: 'eq',\n                    value: group.value\n                });\n                aggregateResult = aggregateBy(filteredData, descriptor.aggregates);\n            }\n            result[group.__position] = {\n                aggregates: aggregateResult,\n                field: field,\n                items: descriptors.length > 1 ?\n                    groupBy(group.items, descriptors.slice(1), identity, filteredData)\n                    : group.items,\n                value: group.value\n            };\n        });\n    });\n    return result;\n};\n"], "mappings": "AAAA,SAASA,IAAI,EAAEC,GAAG,EAAEC,eAAe,QAAQ,gBAAgB;AAC3D,SAASC,OAAO,EAAEC,SAAS,QAAQ,UAAU;AAC7C,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,QAAQ,QAAQ,wCAAwC;AACjE;AACA;AACA;AACA,OAAO,IAAIC,eAAe,GAAG,SAAAA,CAAUC,WAAW,EAAE;EAChDA,WAAW,GAAGL,OAAO,CAACK,WAAW,CAAC,GAAGA,WAAW,GAAG,CAACA,WAAW,CAAC;EAChE,OAAOA,WAAW,CAACP,GAAG,CAAC,UAAUQ,CAAC,EAAE;IAAE,OAAOC,MAAM,CAACC,MAAM,CAAC;MAAEC,GAAG,EAAE;IAAM,CAAC,EAAEH,CAAC,CAAC;EAAE,CAAC,CAAC;AACrF,CAAC;AACD,IAAII,QAAQ,GAAGZ,GAAG,CAAC,UAAUQ,CAAC,EAAE;EAAE,OAAOA,CAAC;AAAE,CAAC,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIK,OAAO,GAAG,SAAAA,CAAUC,IAAI,EAAEP,WAAW,EAAEQ,YAAY,EAAEC,YAAY,EAAE;EAC1E,IAAIT,WAAW,KAAK,KAAK,CAAC,EAAE;IAAEA,WAAW,GAAG,EAAE;EAAE;EAChD,IAAIQ,YAAY,KAAK,KAAK,CAAC,EAAE;IAAEA,YAAY,GAAGH,QAAQ;EAAE;EACxD,IAAII,YAAY,KAAK,KAAK,CAAC,EAAE;IAAEA,YAAY,GAAGF,IAAI;EAAE;EACpDP,WAAW,GAAGD,eAAe,CAACC,WAAW,CAAC;EAC1C,IAAI,CAACA,WAAW,CAACU,MAAM,EAAE;IACrB,OAAOH,IAAI;EACf;EACA,IAAII,UAAU,GAAGX,WAAW,CAAC,CAAC,CAAC;EAC/B,IAAIY,YAAY,GAAG,CAAC,CAAC;EACrB,IAAIC,IAAI,GAAGrB,IAAI,CAACgB,YAAY,CAACd,eAAe,CAACiB,UAAU,CAACG,KAAK,CAAC,CAAC,EAAEF,YAAY,EAAEL,IAAI,CAAC;EACpF,IAAIQ,MAAM,GAAG,EAAE;EACfb,MAAM,CAACc,IAAI,CAACH,IAAI,CAAC,CAACI,OAAO,CAAC,UAAUH,KAAK,EAAE;IACvCZ,MAAM,CAACc,IAAI,CAACH,IAAI,CAACC,KAAK,CAAC,CAAC,CAACG,OAAO,CAAC,UAAUC,KAAK,EAAE;MAC9C,IAAIC,KAAK,GAAGN,IAAI,CAACC,KAAK,CAAC,CAACI,KAAK,CAAC;MAC9B,IAAIE,eAAe,GAAG,CAAC,CAAC;MACxB,IAAIC,YAAY,GAAGZ,YAAY;MAC/B,IAAIb,SAAS,CAACe,UAAU,CAACW,UAAU,CAAC,EAAE;QAClCD,YAAY,GAAGvB,QAAQ,CAACW,YAAY,EAAE;UAClCK,KAAK,EAAEH,UAAU,CAACG,KAAK;UACvBS,UAAU,EAAE,KAAK;UACjBC,QAAQ,EAAE,IAAI;UACdN,KAAK,EAAEC,KAAK,CAACD;QACjB,CAAC,CAAC;QACFE,eAAe,GAAGvB,WAAW,CAACwB,YAAY,EAAEV,UAAU,CAACW,UAAU,CAAC;MACtE;MACAP,MAAM,CAACI,KAAK,CAACM,UAAU,CAAC,GAAG;QACvBH,UAAU,EAAEF,eAAe;QAC3BN,KAAK,EAAEA,KAAK;QACZY,KAAK,EAAE1B,WAAW,CAACU,MAAM,GAAG,CAAC,GACzBJ,OAAO,CAACa,KAAK,CAACO,KAAK,EAAE1B,WAAW,CAAC2B,KAAK,CAAC,CAAC,CAAC,EAAEtB,QAAQ,EAAEgB,YAAY,CAAC,GAChEF,KAAK,CAACO,KAAK;QACjBR,KAAK,EAAEC,KAAK,CAACD;MACjB,CAAC;IACL,CAAC,CAAC;EACN,CAAC,CAAC;EACF,OAAOH,MAAM;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}