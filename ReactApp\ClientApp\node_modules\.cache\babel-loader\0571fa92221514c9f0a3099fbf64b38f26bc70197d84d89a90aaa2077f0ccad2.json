{"ast": null, "code": "/**\n * A function that clones the passed date. The parameter could be `null`.\n *\n * @param date - The initial date value.\n * @returns - A new `Date` instance.\n *\n * @example\n * ```ts-no-run\n * cloneDate(new Date(2016, 0, 1)); // returns new Date(2016, 0, 1);\n * cloneDate(null); // returns null\n * ```\n */\nexport var cloneDate = function (date) {\n  return date ? new Date(date.getTime()) : null;\n};", "map": {"version": 3, "names": ["cloneDate", "date", "Date", "getTime"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/clone-date.js"], "sourcesContent": ["/**\n * A function that clones the passed date. The parameter could be `null`.\n *\n * @param date - The initial date value.\n * @returns - A new `Date` instance.\n *\n * @example\n * ```ts-no-run\n * cloneDate(new Date(2016, 0, 1)); // returns new Date(2016, 0, 1);\n * cloneDate(null); // returns null\n * ```\n */\nexport var cloneDate = function (date) { return date ? new Date(date.getTime()) : null; };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIA,SAAS,GAAG,SAAAA,CAAUC,IAAI,EAAE;EAAE,OAAOA,IAAI,GAAG,IAAIC,IAAI,CAACD,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;AAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}