{"ast": null, "code": "import Point from '../point';\nexport default function lineIntersection(p0, p1, p2, p3) {\n  var s1x = p1.x - p0.x;\n  var s2x = p3.x - p2.x;\n  var s1y = p1.y - p0.y;\n  var s2y = p3.y - p2.y;\n  var nx = p0.x - p2.x;\n  var ny = p0.y - p2.y;\n  var d = s1x * s2y - s2x * s1y;\n  var s = (s1x * ny - s1y * nx) / d;\n  var t = (s2x * ny - s2y * nx) / d;\n  if (s >= 0 && s <= 1 && t >= 0 && t <= 1) {\n    return new Point(p0.x + t * s1x, p0.y + t * s1y);\n  }\n}", "map": {"version": 3, "names": ["Point", "lineIntersection", "p0", "p1", "p2", "p3", "s1x", "x", "s2x", "s1y", "y", "s2y", "nx", "ny", "d", "s", "t"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/geometry/math/line-intersection.js"], "sourcesContent": ["import Point from '../point';\n\nexport default function lineIntersection(p0, p1, p2, p3) {\n    var s1x = p1.x - p0.x;\n    var s2x = p3.x - p2.x;\n    var s1y = p1.y - p0.y;\n    var s2y = p3.y - p2.y;\n    var nx = p0.x - p2.x;\n    var ny = p0.y - p2.y;\n    var d = s1x * s2y - s2x * s1y;\n    var s = (s1x * ny - s1y * nx) / d;\n    var t = (s2x * ny - s2y * nx) / d;\n\n    if (s >= 0 && s <= 1 && t >= 0 && t <= 1) {\n        return new Point(p0.x + t * s1x, p0.y + t * s1y);\n    }\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,UAAU;AAE5B,eAAe,SAASC,gBAAgBA,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EACrD,IAAIC,GAAG,GAAGH,EAAE,CAACI,CAAC,GAAGL,EAAE,CAACK,CAAC;EACrB,IAAIC,GAAG,GAAGH,EAAE,CAACE,CAAC,GAAGH,EAAE,CAACG,CAAC;EACrB,IAAIE,GAAG,GAAGN,EAAE,CAACO,CAAC,GAAGR,EAAE,CAACQ,CAAC;EACrB,IAAIC,GAAG,GAAGN,EAAE,CAACK,CAAC,GAAGN,EAAE,CAACM,CAAC;EACrB,IAAIE,EAAE,GAAGV,EAAE,CAACK,CAAC,GAAGH,EAAE,CAACG,CAAC;EACpB,IAAIM,EAAE,GAAGX,EAAE,CAACQ,CAAC,GAAGN,EAAE,CAACM,CAAC;EACpB,IAAII,CAAC,GAAGR,GAAG,GAAGK,GAAG,GAAGH,GAAG,GAAGC,GAAG;EAC7B,IAAIM,CAAC,GAAG,CAACT,GAAG,GAAGO,EAAE,GAAGJ,GAAG,GAAGG,EAAE,IAAIE,CAAC;EACjC,IAAIE,CAAC,GAAG,CAACR,GAAG,GAAGK,EAAE,GAAGF,GAAG,GAAGC,EAAE,IAAIE,CAAC;EAEjC,IAAIC,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAC,IAAIC,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAC,EAAE;IACtC,OAAO,IAAIhB,KAAK,CAACE,EAAE,CAACK,CAAC,GAAGS,CAAC,GAAGV,GAAG,EAAEJ,EAAE,CAACQ,CAAC,GAAGM,CAAC,GAAGP,GAAG,CAAC;EACpD;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}