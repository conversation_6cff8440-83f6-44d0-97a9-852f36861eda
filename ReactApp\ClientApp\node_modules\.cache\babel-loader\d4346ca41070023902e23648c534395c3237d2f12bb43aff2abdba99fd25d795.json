{"ast": null, "code": "import Node from './node';\nimport traversable from '../mixins/traversable';\nvar GroupNode = function (superclass) {\n  function GroupNode() {\n    superclass.apply(this, arguments);\n  }\n  if (superclass) GroupNode.__proto__ = superclass;\n  GroupNode.prototype = Object.create(superclass && superclass.prototype);\n  GroupNode.prototype.constructor = GroupNode;\n  GroupNode.prototype.renderTo = function renderTo(ctx) {\n    if (!this.visible()) {\n      return;\n    }\n    ctx.save();\n    this.setTransform(ctx);\n    this.setClip(ctx);\n    this.setOpacity(ctx);\n    var childNodes = this.childNodes;\n    for (var i = 0; i < childNodes.length; i++) {\n      var child = childNodes[i];\n      if (child.visible()) {\n        child.renderTo(ctx);\n      }\n    }\n    ctx.restore();\n  };\n  return GroupNode;\n}(traversable(Node, \"childNodes\"));\nexport default GroupNode;", "map": {"version": 3, "names": ["Node", "traversable", "GroupNode", "superclass", "apply", "arguments", "__proto__", "prototype", "Object", "create", "constructor", "renderTo", "ctx", "visible", "save", "setTransform", "setClip", "setOpacity", "childNodes", "i", "length", "child", "restore"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/canvas/group-node.js"], "sourcesContent": ["import Node from './node';\nimport traversable from '../mixins/traversable';\n\n\nvar GroupNode = (function (superclass) {\n    function GroupNode () {\n        superclass.apply(this, arguments);\n    }\n\n    if ( superclass ) GroupNode.__proto__ = superclass;\n    GroupNode.prototype = Object.create( superclass && superclass.prototype );\n    GroupNode.prototype.constructor = GroupNode;\n\n    GroupNode.prototype.renderTo = function renderTo (ctx) {\n        if (!this.visible()) {\n            return;\n        }\n\n        ctx.save();\n\n        this.setTransform(ctx);\n        this.setClip(ctx);\n        this.setOpacity(ctx);\n\n        var childNodes = this.childNodes;\n        for (var i = 0; i < childNodes.length; i++) {\n            var child = childNodes[i];\n            if (child.visible()) {\n                child.renderTo(ctx);\n            }\n        }\n\n        ctx.restore();\n    };\n\n    return GroupNode;\n}(traversable(Node, \"childNodes\")));\n\nexport default GroupNode;\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,QAAQ;AACzB,OAAOC,WAAW,MAAM,uBAAuB;AAG/C,IAAIC,SAAS,GAAI,UAAUC,UAAU,EAAE;EACnC,SAASD,SAASA,CAAA,EAAI;IAClBC,UAAU,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACrC;EAEA,IAAKF,UAAU,EAAGD,SAAS,CAACI,SAAS,GAAGH,UAAU;EAClDD,SAAS,CAACK,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAEN,UAAU,IAAIA,UAAU,CAACI,SAAU,CAAC;EACzEL,SAAS,CAACK,SAAS,CAACG,WAAW,GAAGR,SAAS;EAE3CA,SAAS,CAACK,SAAS,CAACI,QAAQ,GAAG,SAASA,QAAQA,CAAEC,GAAG,EAAE;IACnD,IAAI,CAAC,IAAI,CAACC,OAAO,CAAC,CAAC,EAAE;MACjB;IACJ;IAEAD,GAAG,CAACE,IAAI,CAAC,CAAC;IAEV,IAAI,CAACC,YAAY,CAACH,GAAG,CAAC;IACtB,IAAI,CAACI,OAAO,CAACJ,GAAG,CAAC;IACjB,IAAI,CAACK,UAAU,CAACL,GAAG,CAAC;IAEpB,IAAIM,UAAU,GAAG,IAAI,CAACA,UAAU;IAChC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,UAAU,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MACxC,IAAIE,KAAK,GAAGH,UAAU,CAACC,CAAC,CAAC;MACzB,IAAIE,KAAK,CAACR,OAAO,CAAC,CAAC,EAAE;QACjBQ,KAAK,CAACV,QAAQ,CAACC,GAAG,CAAC;MACvB;IACJ;IAEAA,GAAG,CAACU,OAAO,CAAC,CAAC;EACjB,CAAC;EAED,OAAOpB,SAAS;AACpB,CAAC,CAACD,WAAW,CAACD,IAAI,EAAE,YAAY,CAAC,CAAE;AAEnC,eAAeE,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}