{"ast": null, "code": "export default function groupInteger(number, start, end, options, info) {\n  var symbols = info.numbers.symbols;\n  var decimalIndex = number.indexOf(symbols.decimal);\n  var groupSizes = options.groupSize.slice();\n  var groupSize = groupSizes.shift();\n  var integerEnd = decimalIndex !== -1 ? decimalIndex : end + 1;\n  var integer = number.substring(start, integerEnd);\n  var result = number;\n  var integerLength = integer.length;\n  if (integerLength >= groupSize) {\n    var idx = integerLength;\n    var parts = [];\n    while (idx > -1) {\n      var value = integer.substring(idx - groupSize, idx);\n      if (value) {\n        parts.push(value);\n      }\n      idx -= groupSize;\n      var newGroupSize = groupSizes.shift();\n      groupSize = newGroupSize !== undefined ? newGroupSize : groupSize;\n      if (groupSize === 0) {\n        value = integer.substring(0, idx);\n        if (value) {\n          parts.push(value);\n        }\n        break;\n      }\n    }\n    integer = parts.reverse().join(symbols.group);\n    result = number.substring(0, start) + integer + number.substring(integerEnd);\n  }\n  return result;\n}", "map": {"version": 3, "names": ["groupInteger", "number", "start", "end", "options", "info", "symbols", "numbers", "decimalIndex", "indexOf", "decimal", "groupSizes", "groupSize", "slice", "shift", "integerEnd", "integer", "substring", "result", "integerLength", "length", "idx", "parts", "value", "push", "newGroupSize", "undefined", "reverse", "join", "group"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-intl/dist/es/numbers/group-integer.js"], "sourcesContent": ["export default function groupInteger(number, start, end, options, info) {\n    var symbols = info.numbers.symbols;\n    var decimalIndex = number.indexOf(symbols.decimal);\n    var groupSizes = options.groupSize.slice();\n    var groupSize = groupSizes.shift();\n\n    var integerEnd = decimalIndex !== -1 ? decimalIndex : end + 1;\n\n    var integer = number.substring(start, integerEnd);\n    var result = number;\n    var integerLength = integer.length;\n\n    if (integerLength >= groupSize) {\n        var idx = integerLength;\n        var parts = [];\n\n        while (idx > -1) {\n            var value = integer.substring(idx - groupSize, idx);\n            if (value) {\n                parts.push(value);\n            }\n            idx -= groupSize;\n            var newGroupSize = groupSizes.shift();\n            groupSize = newGroupSize !== undefined ? newGroupSize : groupSize;\n\n            if (groupSize === 0) {\n                value = integer.substring(0, idx);\n                if (value) {\n                    parts.push(value);\n                }\n                break;\n            }\n        }\n\n        integer = parts.reverse().join(symbols.group);\n        result = number.substring(0, start) + integer + number.substring(integerEnd);\n    }\n\n    return result;\n}"], "mappings": "AAAA,eAAe,SAASA,YAAYA,CAACC,MAAM,EAAEC,KAAK,EAAEC,GAAG,EAAEC,OAAO,EAAEC,IAAI,EAAE;EACpE,IAAIC,OAAO,GAAGD,IAAI,CAACE,OAAO,CAACD,OAAO;EAClC,IAAIE,YAAY,GAAGP,MAAM,CAACQ,OAAO,CAACH,OAAO,CAACI,OAAO,CAAC;EAClD,IAAIC,UAAU,GAAGP,OAAO,CAACQ,SAAS,CAACC,KAAK,CAAC,CAAC;EAC1C,IAAID,SAAS,GAAGD,UAAU,CAACG,KAAK,CAAC,CAAC;EAElC,IAAIC,UAAU,GAAGP,YAAY,KAAK,CAAC,CAAC,GAAGA,YAAY,GAAGL,GAAG,GAAG,CAAC;EAE7D,IAAIa,OAAO,GAAGf,MAAM,CAACgB,SAAS,CAACf,KAAK,EAAEa,UAAU,CAAC;EACjD,IAAIG,MAAM,GAAGjB,MAAM;EACnB,IAAIkB,aAAa,GAAGH,OAAO,CAACI,MAAM;EAElC,IAAID,aAAa,IAAIP,SAAS,EAAE;IAC5B,IAAIS,GAAG,GAAGF,aAAa;IACvB,IAAIG,KAAK,GAAG,EAAE;IAEd,OAAOD,GAAG,GAAG,CAAC,CAAC,EAAE;MACb,IAAIE,KAAK,GAAGP,OAAO,CAACC,SAAS,CAACI,GAAG,GAAGT,SAAS,EAAES,GAAG,CAAC;MACnD,IAAIE,KAAK,EAAE;QACPD,KAAK,CAACE,IAAI,CAACD,KAAK,CAAC;MACrB;MACAF,GAAG,IAAIT,SAAS;MAChB,IAAIa,YAAY,GAAGd,UAAU,CAACG,KAAK,CAAC,CAAC;MACrCF,SAAS,GAAGa,YAAY,KAAKC,SAAS,GAAGD,YAAY,GAAGb,SAAS;MAEjE,IAAIA,SAAS,KAAK,CAAC,EAAE;QACjBW,KAAK,GAAGP,OAAO,CAACC,SAAS,CAAC,CAAC,EAAEI,GAAG,CAAC;QACjC,IAAIE,KAAK,EAAE;UACPD,KAAK,CAACE,IAAI,CAACD,KAAK,CAAC;QACrB;QACA;MACJ;IACJ;IAEAP,OAAO,GAAGM,KAAK,CAACK,OAAO,CAAC,CAAC,CAACC,IAAI,CAACtB,OAAO,CAACuB,KAAK,CAAC;IAC7CX,MAAM,GAAGjB,MAAM,CAACgB,SAAS,CAAC,CAAC,EAAEf,KAAK,CAAC,GAAGc,OAAO,GAAGf,MAAM,CAACgB,SAAS,CAACF,UAAU,CAAC;EAChF;EAEA,OAAOG,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}