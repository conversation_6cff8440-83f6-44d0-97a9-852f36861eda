{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as d from \"react\";\nimport { svgPath as p, bezierCommand as f, controlPoint as A, line as b } from \"./utils/svg-calc.mjs\";\nimport { getContrastFromTwoRGBAs as S, getRGBA as h, getColorFromHSV as T } from \"./utils/color-parser.mjs\";\nconst C = 4.5,\n  g = 7,\n  m = 16;\nclass w extends d.Component {\n  renderSvgCurveLine() {\n    const t = this.props.metrics,\n      v = (o, i, r, e, s) => {\n        const a = (r + e) / 2,\n          l = Object.assign({}, this.props.hsva, {\n            s: i / t.width,\n            v: 1 - a / t.height\n          }),\n          n = S(h(T(l)), h(this.props.backgroundColor || \"\"));\n        return r + 0.5 > e ? n < o + 1 && n > o - 1 ? a : null : s(n, o) ? v(o, i, r, e - (e - r) / 2, s) : v(o, i, r + (e - r) / 2, e, s);\n      },\n      c = (o, i, r = !1) => {\n        const e = [];\n        for (let s = 0; s <= t.width; s += t.width / i) {\n          const a = v(o, s, 0, t.height, r ? (l, n) => l < n : (l, n) => l > n);\n          a !== null && e.push([s, a]);\n        }\n        return e;\n      },\n      u = f(A(b));\n    return p(c(C, m), u) + p(c(C, m, !0), u) + p(c(g, m), u) + p(c(g, m, !0), u);\n  }\n  shouldComponentUpdate(t) {\n    return !(t.hsva.h === this.props.hsva.h && t.hsva.a === this.props.hsva.a && this.props.backgroundColor === t.backgroundColor && t.metrics === this.props.metrics);\n  }\n  render() {\n    return /* @__PURE__ */d.createElement(\"svg\", {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      className: \"k-color-contrast-svg\",\n      dangerouslySetInnerHTML: {\n        __html: this.renderSvgCurveLine()\n      },\n      style: {\n        position: \"absolute\",\n        overflow: \"visible\",\n        pointerEvents: \"none\",\n        left: 0,\n        top: 0,\n        zIndex: 3\n      }\n    });\n  }\n}\nexport { w as ColorContrastSvg };", "map": {"version": 3, "names": ["d", "svgPath", "p", "bezierCommand", "f", "controlPoint", "A", "line", "b", "getContrastFromTwoRGBAs", "S", "getRGBA", "h", "getColorFromHSV", "T", "C", "g", "m", "w", "Component", "renderSvgCurveLine", "t", "props", "metrics", "v", "o", "i", "r", "e", "s", "a", "l", "Object", "assign", "hsva", "width", "height", "n", "backgroundColor", "c", "push", "u", "shouldComponentUpdate", "render", "createElement", "xmlns", "className", "dangerouslySetInnerHTML", "__html", "style", "position", "overflow", "pointerEvents", "left", "top", "zIndex", "ColorContrastSvg"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-inputs/colors/ColorContrastSvg.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as d from \"react\";\nimport { svgPath as p, bezierCommand as f, controlPoint as A, line as b } from \"./utils/svg-calc.mjs\";\nimport { getContrastFromTwoRGBAs as S, getRGBA as h, getColorFromHSV as T } from \"./utils/color-parser.mjs\";\nconst C = 4.5, g = 7, m = 16;\nclass w extends d.Component {\n  renderSvgCurveLine() {\n    const t = this.props.metrics, v = (o, i, r, e, s) => {\n      const a = (r + e) / 2, l = Object.assign({}, this.props.hsva, {\n        s: i / t.width,\n        v: 1 - a / t.height\n      }), n = S(\n        h(T(l)),\n        h(this.props.backgroundColor || \"\")\n      );\n      return r + 0.5 > e ? n < o + 1 && n > o - 1 ? a : null : s(n, o) ? v(o, i, r, e - (e - r) / 2, s) : v(o, i, r + (e - r) / 2, e, s);\n    }, c = (o, i, r = !1) => {\n      const e = [];\n      for (let s = 0; s <= t.width; s += t.width / i) {\n        const a = v(\n          o,\n          s,\n          0,\n          t.height,\n          r ? (l, n) => l < n : (l, n) => l > n\n        );\n        a !== null && e.push([s, a]);\n      }\n      return e;\n    }, u = f(A(b));\n    return p(c(C, m), u) + p(c(C, m, !0), u) + p(c(g, m), u) + p(c(g, m, !0), u);\n  }\n  shouldComponentUpdate(t) {\n    return !(t.hsva.h === this.props.hsva.h && t.hsva.a === this.props.hsva.a && this.props.backgroundColor === t.backgroundColor && t.metrics === this.props.metrics);\n  }\n  render() {\n    return /* @__PURE__ */ d.createElement(\n      \"svg\",\n      {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: \"k-color-contrast-svg\",\n        dangerouslySetInnerHTML: { __html: this.renderSvgCurveLine() },\n        style: {\n          position: \"absolute\",\n          overflow: \"visible\",\n          pointerEvents: \"none\",\n          left: 0,\n          top: 0,\n          zIndex: 3\n        }\n      }\n    );\n  }\n}\nexport {\n  w as ColorContrastSvg\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,SAASC,OAAO,IAAIC,CAAC,EAAEC,aAAa,IAAIC,CAAC,EAAEC,YAAY,IAAIC,CAAC,EAAEC,IAAI,IAAIC,CAAC,QAAQ,sBAAsB;AACrG,SAASC,uBAAuB,IAAIC,CAAC,EAAEC,OAAO,IAAIC,CAAC,EAAEC,eAAe,IAAIC,CAAC,QAAQ,0BAA0B;AAC3G,MAAMC,CAAC,GAAG,GAAG;EAAEC,CAAC,GAAG,CAAC;EAAEC,CAAC,GAAG,EAAE;AAC5B,MAAMC,CAAC,SAASlB,CAAC,CAACmB,SAAS,CAAC;EAC1BC,kBAAkBA,CAAA,EAAG;IACnB,MAAMC,CAAC,GAAG,IAAI,CAACC,KAAK,CAACC,OAAO;MAAEC,CAAC,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;QACnD,MAAMC,CAAC,GAAG,CAACH,CAAC,GAAGC,CAAC,IAAI,CAAC;UAAEG,CAAC,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACX,KAAK,CAACY,IAAI,EAAE;YAC5DL,CAAC,EAAEH,CAAC,GAAGL,CAAC,CAACc,KAAK;YACdX,CAAC,EAAE,CAAC,GAAGM,CAAC,GAAGT,CAAC,CAACe;UACf,CAAC,CAAC;UAAEC,CAAC,GAAG3B,CAAC,CACPE,CAAC,CAACE,CAAC,CAACiB,CAAC,CAAC,CAAC,EACPnB,CAAC,CAAC,IAAI,CAACU,KAAK,CAACgB,eAAe,IAAI,EAAE,CACpC,CAAC;QACD,OAAOX,CAAC,GAAG,GAAG,GAAGC,CAAC,GAAGS,CAAC,GAAGZ,CAAC,GAAG,CAAC,IAAIY,CAAC,GAAGZ,CAAC,GAAG,CAAC,GAAGK,CAAC,GAAG,IAAI,GAAGD,CAAC,CAACQ,CAAC,EAAEZ,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,GAAG,CAACA,CAAC,GAAGD,CAAC,IAAI,CAAC,EAAEE,CAAC,CAAC,GAAGL,CAAC,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,GAAG,CAACC,CAAC,GAAGD,CAAC,IAAI,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;MACpI,CAAC;MAAEU,CAAC,GAAGA,CAACd,CAAC,EAAEC,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC,KAAK;QACvB,MAAMC,CAAC,GAAG,EAAE;QACZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIR,CAAC,CAACc,KAAK,EAAEN,CAAC,IAAIR,CAAC,CAACc,KAAK,GAAGT,CAAC,EAAE;UAC9C,MAAMI,CAAC,GAAGN,CAAC,CACTC,CAAC,EACDI,CAAC,EACD,CAAC,EACDR,CAAC,CAACe,MAAM,EACRT,CAAC,GAAG,CAACI,CAAC,EAAEM,CAAC,KAAKN,CAAC,GAAGM,CAAC,GAAG,CAACN,CAAC,EAAEM,CAAC,KAAKN,CAAC,GAAGM,CACtC,CAAC;UACDP,CAAC,KAAK,IAAI,IAAIF,CAAC,CAACY,IAAI,CAAC,CAACX,CAAC,EAAEC,CAAC,CAAC,CAAC;QAC9B;QACA,OAAOF,CAAC;MACV,CAAC;MAAEa,CAAC,GAAGrC,CAAC,CAACE,CAAC,CAACE,CAAC,CAAC,CAAC;IACd,OAAON,CAAC,CAACqC,CAAC,CAACxB,CAAC,EAAEE,CAAC,CAAC,EAAEwB,CAAC,CAAC,GAAGvC,CAAC,CAACqC,CAAC,CAACxB,CAAC,EAAEE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEwB,CAAC,CAAC,GAAGvC,CAAC,CAACqC,CAAC,CAACvB,CAAC,EAAEC,CAAC,CAAC,EAAEwB,CAAC,CAAC,GAAGvC,CAAC,CAACqC,CAAC,CAACvB,CAAC,EAAEC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEwB,CAAC,CAAC;EAC9E;EACAC,qBAAqBA,CAACrB,CAAC,EAAE;IACvB,OAAO,EAAEA,CAAC,CAACa,IAAI,CAACtB,CAAC,KAAK,IAAI,CAACU,KAAK,CAACY,IAAI,CAACtB,CAAC,IAAIS,CAAC,CAACa,IAAI,CAACJ,CAAC,KAAK,IAAI,CAACR,KAAK,CAACY,IAAI,CAACJ,CAAC,IAAI,IAAI,CAACR,KAAK,CAACgB,eAAe,KAAKjB,CAAC,CAACiB,eAAe,IAAIjB,CAAC,CAACE,OAAO,KAAK,IAAI,CAACD,KAAK,CAACC,OAAO,CAAC;EACpK;EACAoB,MAAMA,CAAA,EAAG;IACP,OAAO,eAAgB3C,CAAC,CAAC4C,aAAa,CACpC,KAAK,EACL;MACEC,KAAK,EAAE,4BAA4B;MACnCC,SAAS,EAAE,sBAAsB;MACjCC,uBAAuB,EAAE;QAAEC,MAAM,EAAE,IAAI,CAAC5B,kBAAkB,CAAC;MAAE,CAAC;MAC9D6B,KAAK,EAAE;QACLC,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,SAAS;QACnBC,aAAa,EAAE,MAAM;QACrBC,IAAI,EAAE,CAAC;QACPC,GAAG,EAAE,CAAC;QACNC,MAAM,EAAE;MACV;IACF,CACF,CAAC;EACH;AACF;AACA,SACErC,CAAC,IAAIsC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}