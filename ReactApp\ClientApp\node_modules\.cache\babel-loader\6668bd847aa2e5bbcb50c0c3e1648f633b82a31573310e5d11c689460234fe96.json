{"ast": null, "code": "import { setMonth } from './set-month';\n/**\n * A function that returns a `Date` object of the last month in a year.\n *\n * @param date - The start date value.\n * @returns - The last month in a year.\n *\n * @example\n * ```ts-no-run\n * lastMonthOfYear(new Date(2017, 5, 3)); // 2017-12-3\n * lastMonthOfYear(new Date(2017, 11, 3)); // 2017-12-3\n * ```\n */\nexport var lastMonthOfYear = function (value) {\n  return setMonth(value, 11);\n};", "map": {"version": 3, "names": ["setMonth", "lastMonthOfYear", "value"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/last-month-of-year.js"], "sourcesContent": ["import { setMonth } from './set-month';\n/**\n * A function that returns a `Date` object of the last month in a year.\n *\n * @param date - The start date value.\n * @returns - The last month in a year.\n *\n * @example\n * ```ts-no-run\n * lastMonthOfYear(new Date(2017, 5, 3)); // 2017-12-3\n * lastMonthOfYear(new Date(2017, 11, 3)); // 2017-12-3\n * ```\n */\nexport var lastMonthOfYear = function (value) { return setMonth(value, 11); };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,aAAa;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,eAAe,GAAG,SAAAA,CAAUC,KAAK,EAAE;EAAE,OAAOF,QAAQ,CAACE,KAAK,EAAE,EAAE,CAAC;AAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}