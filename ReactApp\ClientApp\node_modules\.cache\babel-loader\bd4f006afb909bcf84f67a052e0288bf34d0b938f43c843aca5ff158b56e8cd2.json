{"ast": null, "code": "import { isPresent, isString, isArray } from '../utils';\nimport { isCompositeFilterDescriptor } from './filter-descriptor.interface';\nvar operatorMap = function (key) {\n  return {\n    \"!=\": \"neq\",\n    \"<\": \"lt\",\n    \"<=\": \"lte\",\n    \"==\": \"eq\",\n    \">\": \"gt\",\n    \">=\": \"gte\",\n    equal: \"eq\",\n    equals: \"eq\",\n    equalto: \"eq\",\n    ge: \"gte\",\n    greater: \"gt\",\n    greaterthan: \"gt\",\n    greaterthanequal: \"gte\",\n    isempty: \"isempty\",\n    isequalto: \"eq\",\n    isgreaterthan: \"gt\",\n    isgreaterthanorequalto: \"gte\",\n    islessthan: \"lt\",\n    islessthanorequalto: \"lte\",\n    isnotempty: \"isnotempty\",\n    isnotequalto: \"neq\",\n    isnull: \"isnull\",\n    le: \"lte\",\n    less: \"lt\",\n    lessthan: \"lt\",\n    lessthanequal: \"lte\",\n    ne: \"neq\",\n    notequal: \"neq\",\n    notequals: \"neq\",\n    notequalto: \"neq\",\n    notsubstringof: \"doesnotcontain\"\n  }[key.toLowerCase()] || key;\n};\nvar normalizeOperator = function (descriptor) {\n  if (descriptor.filters) {\n    descriptor.filters = descriptor.filters.map(function (filter) {\n      var result = Object.assign({}, filter);\n      if (!isCompositeFilterDescriptor(filter) && isString(filter.operator)) {\n        result.operator = operatorMap(filter.operator);\n      }\n      if (isCompositeFilterDescriptor(filter)) {\n        normalizeOperator(result);\n      }\n      return result;\n    });\n  }\n};\nvar normalizeDescriptor = function (descriptor) {\n  if (!isCompositeFilterDescriptor(descriptor)) {\n    return {\n      filters: isArray(descriptor) ? descriptor : [descriptor],\n      logic: \"and\"\n    };\n  }\n  return Object.assign({}, descriptor);\n};\n// tslint:disable:max-line-length\n/**\n * Converts a [`FilterDescriptor`]({% slug api_kendo-data-query_filterdescriptor %}) into a [`CompositeFilterDescriptor`]({% slug api_kendo-data-query_compositefilterdescriptor %}). If a `CompositeFilterDescriptor` is passed, no modifications will be made.\n *\n * @param {CompositeFilterDescriptor | FilterDescriptor} descriptor - The descriptor that will be normalized.\n * @returns {CompositeFilterDescriptor} - The normalized descriptor.\n */\n// tslint:enable:max-line-length\nexport var normalizeFilters = function (descriptor) {\n  if (isPresent(descriptor)) {\n    descriptor = normalizeDescriptor(descriptor);\n    normalizeOperator(descriptor);\n  }\n  return descriptor;\n};", "map": {"version": 3, "names": ["isPresent", "isString", "isArray", "isCompositeFilterDescriptor", "operatorMap", "key", "equal", "equals", "equalto", "ge", "greater", "greaterthan", "greaterthanequal", "isempty", "isequalto", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isgreaterthanorequalto", "islessthan", "is<PERSON>thanore<PERSON>lt<PERSON>", "isnotempty", "isnotequalto", "isnull", "le", "less", "lessthan", "lessthanequal", "ne", "notequal", "notequals", "notequalto", "notsubstringof", "toLowerCase", "normalizeOperator", "descriptor", "filters", "map", "filter", "result", "Object", "assign", "operator", "normalizeDescriptor", "logic", "normalizeFilters"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-data-query/dist/es/filtering/filter.operators.js"], "sourcesContent": ["import { isPresent, isString, isArray } from '../utils';\nimport { isCompositeFilterDescriptor } from './filter-descriptor.interface';\nvar operatorMap = function (key) { return ({\n    \"!=\": \"neq\",\n    \"<\": \"lt\",\n    \"<=\": \"lte\",\n    \"==\": \"eq\",\n    \">\": \"gt\",\n    \">=\": \"gte\",\n    equal: \"eq\",\n    equals: \"eq\",\n    equalto: \"eq\",\n    ge: \"gte\",\n    greater: \"gt\",\n    greaterthan: \"gt\",\n    greaterthanequal: \"gte\",\n    isempty: \"isempty\",\n    isequalto: \"eq\",\n    isgreaterthan: \"gt\",\n    isgreaterthanorequalto: \"gte\",\n    islessthan: \"lt\",\n    islessthanorequalto: \"lte\",\n    isnotempty: \"isnotempty\",\n    isnotequalto: \"neq\",\n    isnull: \"isnull\",\n    le: \"lte\",\n    less: \"lt\",\n    lessthan: \"lt\",\n    lessthanequal: \"lte\",\n    ne: \"neq\",\n    notequal: \"neq\",\n    notequals: \"neq\",\n    notequalto: \"neq\",\n    notsubstringof: \"doesnotcontain\"\n}[key.toLowerCase()] || key); };\nvar normalizeOperator = function (descriptor) {\n    if (descriptor.filters) {\n        descriptor.filters = descriptor.filters.map(function (filter) {\n            var result = Object.assign({}, filter);\n            if (!isCompositeFilterDescriptor(filter) && isString(filter.operator)) {\n                result.operator = operatorMap(filter.operator);\n            }\n            if (isCompositeFilterDescriptor(filter)) {\n                normalizeOperator(result);\n            }\n            return result;\n        });\n    }\n};\nvar normalizeDescriptor = function (descriptor) {\n    if (!isCompositeFilterDescriptor(descriptor)) {\n        return {\n            filters: isArray(descriptor) ? descriptor : [descriptor],\n            logic: \"and\"\n        };\n    }\n    return Object.assign({}, descriptor);\n};\n// tslint:disable:max-line-length\n/**\n * Converts a [`FilterDescriptor`]({% slug api_kendo-data-query_filterdescriptor %}) into a [`CompositeFilterDescriptor`]({% slug api_kendo-data-query_compositefilterdescriptor %}). If a `CompositeFilterDescriptor` is passed, no modifications will be made.\n *\n * @param {CompositeFilterDescriptor | FilterDescriptor} descriptor - The descriptor that will be normalized.\n * @returns {CompositeFilterDescriptor} - The normalized descriptor.\n */\n// tslint:enable:max-line-length\nexport var normalizeFilters = function (descriptor) {\n    if (isPresent(descriptor)) {\n        descriptor = normalizeDescriptor(descriptor);\n        normalizeOperator(descriptor);\n    }\n    return descriptor;\n};\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,QAAQ,EAAEC,OAAO,QAAQ,UAAU;AACvD,SAASC,2BAA2B,QAAQ,+BAA+B;AAC3E,IAAIC,WAAW,GAAG,SAAAA,CAAUC,GAAG,EAAE;EAAE,OAAQ;IACvC,IAAI,EAAE,KAAK;IACX,GAAG,EAAE,IAAI;IACT,IAAI,EAAE,KAAK;IACX,IAAI,EAAE,IAAI;IACV,GAAG,EAAE,IAAI;IACT,IAAI,EAAE,KAAK;IACXC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,IAAI;IACZC,OAAO,EAAE,IAAI;IACbC,EAAE,EAAE,KAAK;IACTC,OAAO,EAAE,IAAI;IACbC,WAAW,EAAE,IAAI;IACjBC,gBAAgB,EAAE,KAAK;IACvBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,IAAI;IACfC,aAAa,EAAE,IAAI;IACnBC,sBAAsB,EAAE,KAAK;IAC7BC,UAAU,EAAE,IAAI;IAChBC,mBAAmB,EAAE,KAAK;IAC1BC,UAAU,EAAE,YAAY;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,QAAQ;IAChBC,EAAE,EAAE,KAAK;IACTC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,IAAI;IACdC,aAAa,EAAE,KAAK;IACpBC,EAAE,EAAE,KAAK;IACTC,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE,KAAK;IACjBC,cAAc,EAAE;EACpB,CAAC,CAACzB,GAAG,CAAC0B,WAAW,CAAC,CAAC,CAAC,IAAI1B,GAAG;AAAG,CAAC;AAC/B,IAAI2B,iBAAiB,GAAG,SAAAA,CAAUC,UAAU,EAAE;EAC1C,IAAIA,UAAU,CAACC,OAAO,EAAE;IACpBD,UAAU,CAACC,OAAO,GAAGD,UAAU,CAACC,OAAO,CAACC,GAAG,CAAC,UAAUC,MAAM,EAAE;MAC1D,IAAIC,MAAM,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,MAAM,CAAC;MACtC,IAAI,CAACjC,2BAA2B,CAACiC,MAAM,CAAC,IAAInC,QAAQ,CAACmC,MAAM,CAACI,QAAQ,CAAC,EAAE;QACnEH,MAAM,CAACG,QAAQ,GAAGpC,WAAW,CAACgC,MAAM,CAACI,QAAQ,CAAC;MAClD;MACA,IAAIrC,2BAA2B,CAACiC,MAAM,CAAC,EAAE;QACrCJ,iBAAiB,CAACK,MAAM,CAAC;MAC7B;MACA,OAAOA,MAAM;IACjB,CAAC,CAAC;EACN;AACJ,CAAC;AACD,IAAII,mBAAmB,GAAG,SAAAA,CAAUR,UAAU,EAAE;EAC5C,IAAI,CAAC9B,2BAA2B,CAAC8B,UAAU,CAAC,EAAE;IAC1C,OAAO;MACHC,OAAO,EAAEhC,OAAO,CAAC+B,UAAU,CAAC,GAAGA,UAAU,GAAG,CAACA,UAAU,CAAC;MACxDS,KAAK,EAAE;IACX,CAAC;EACL;EACA,OAAOJ,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEN,UAAU,CAAC;AACxC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIU,gBAAgB,GAAG,SAAAA,CAAUV,UAAU,EAAE;EAChD,IAAIjC,SAAS,CAACiC,UAAU,CAAC,EAAE;IACvBA,UAAU,GAAGQ,mBAAmB,CAACR,UAAU,CAAC;IAC5CD,iBAAiB,CAACC,UAAU,CAAC;EACjC;EACA,OAAOA,UAAU;AACrB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}