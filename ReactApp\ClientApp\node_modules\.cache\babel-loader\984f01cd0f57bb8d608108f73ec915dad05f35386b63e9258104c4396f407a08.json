{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nvar r = /* @__PURE__ */(p => (p[p.PopupList = 0] = \"PopupList\", p[p.TagsList = 1] = \"TagsList\", p))(r || {});\nexport { r as ActiveDescendant };", "map": {"version": 3, "names": ["r", "p", "PopupList", "TagsList", "ActiveDescendant"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dropdowns/common/settings.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nvar r = /* @__PURE__ */ ((p) => (p[p.PopupList = 0] = \"PopupList\", p[p.TagsList = 1] = \"TagsList\", p))(r || {});\nexport {\n  r as ActiveDescendant\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,CAAC,GAAG,eAAgB,CAAEC,CAAC,KAAMA,CAAC,CAACA,CAAC,CAACC,SAAS,GAAG,CAAC,CAAC,GAAG,WAAW,EAAED,CAAC,CAACA,CAAC,CAACE,QAAQ,GAAG,CAAC,CAAC,GAAG,UAAU,EAAEF,CAAC,CAAC,EAAED,CAAC,IAAI,CAAC,CAAC,CAAC;AAC/G,SACEA,CAAC,IAAII,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}