{"ast": null, "code": "import * as React from 'react';\nvar RadioGroupContext = /*#__PURE__*/React.createContext(null);\nexport var RadioGroupContextProvider = RadioGroupContext.Provider;\nexport default RadioGroupContext;\nexport var RadioOptionTypeContext = /*#__PURE__*/React.createContext(null);\nexport var RadioOptionTypeContextProvider = RadioOptionTypeContext.Provider;", "map": {"version": 3, "names": ["React", "RadioGroupContext", "createContext", "RadioGroupContextProvider", "Provider", "RadioOptionTypeContext", "RadioOptionTypeContextProvider"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/antd/es/radio/context.js"], "sourcesContent": ["import * as React from 'react';\nvar RadioGroupContext = /*#__PURE__*/React.createContext(null);\nexport var RadioGroupContextProvider = RadioGroupContext.Provider;\nexport default RadioGroupContext;\nexport var RadioOptionTypeContext = /*#__PURE__*/React.createContext(null);\nexport var RadioOptionTypeContextProvider = RadioOptionTypeContext.Provider;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,iBAAiB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC9D,OAAO,IAAIC,yBAAyB,GAAGF,iBAAiB,CAACG,QAAQ;AACjE,eAAeH,iBAAiB;AAChC,OAAO,IAAII,sBAAsB,GAAG,aAAaL,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC1E,OAAO,IAAII,8BAA8B,GAAGD,sBAAsB,CAACD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}