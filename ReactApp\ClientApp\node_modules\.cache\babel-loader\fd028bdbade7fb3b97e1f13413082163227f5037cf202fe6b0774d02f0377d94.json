{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as l from \"react\";\nimport { classNames as h } from \"@progress/kendo-react-common\";\nclass x extends l.Component {\n  render() {\n    const {\n        id: a,\n        size: e,\n        collapsed: s,\n        overlay: t,\n        containsSplitter: o,\n        collapsible: r,\n        resizable: n,\n        scrollable: i,\n        keepMounted: p,\n        style: c,\n        className: d\n      } = this.props,\n      m = e && e.length > 0,\n      k = {\n        flexBasis: e,\n        ...c\n      },\n      v = h(\"k-pane\", {\n        \"k-hidden\": s,\n        hidden: s,\n        \"k-pane-flex\": o,\n        \"k-pane-static\": !n && !r || m,\n        \"k-scrollable\": i\n      }, d);\n    return /* @__PURE__ */l.createElement(\"div\", {\n      id: a,\n      role: \"group\",\n      style: k,\n      className: v\n    }, !s || p ? this.props.children : void 0, t ? /* @__PURE__ */l.createElement(\"div\", {\n      className: \"k-splitter-overlay k-overlay\"\n    }) : void 0);\n  }\n}\nexport { x as SplitterPane };", "map": {"version": 3, "names": ["l", "classNames", "h", "x", "Component", "render", "id", "a", "size", "e", "collapsed", "s", "overlay", "t", "contains<PERSON><PERSON><PERSON>ter", "o", "collapsible", "r", "resizable", "n", "scrollable", "i", "keepMounted", "p", "style", "c", "className", "d", "props", "m", "length", "k", "flexBasis", "v", "hidden", "createElement", "role", "children", "SplitterPane"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/splitter/SplitterPane.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as l from \"react\";\nimport { classNames as h } from \"@progress/kendo-react-common\";\nclass x extends l.Component {\n  render() {\n    const {\n      id: a,\n      size: e,\n      collapsed: s,\n      overlay: t,\n      containsSplitter: o,\n      collapsible: r,\n      resizable: n,\n      scrollable: i,\n      keepMounted: p,\n      style: c,\n      className: d\n    } = this.props, m = e && e.length > 0, k = {\n      flexBasis: e,\n      ...c\n    }, v = h(\n      \"k-pane\",\n      {\n        \"k-hidden\": s,\n        hidden: s,\n        \"k-pane-flex\": o,\n        \"k-pane-static\": !n && !r || m,\n        \"k-scrollable\": i\n      },\n      d\n    );\n    return /* @__PURE__ */ l.createElement(\"div\", { id: a, role: \"group\", style: k, className: v }, !s || p ? this.props.children : void 0, t ? /* @__PURE__ */ l.createElement(\"div\", { className: \"k-splitter-overlay k-overlay\" }) : void 0);\n  }\n}\nexport {\n  x as SplitterPane\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,SAASC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AAC9D,MAAMC,CAAC,SAASH,CAAC,CAACI,SAAS,CAAC;EAC1BC,MAAMA,CAAA,EAAG;IACP,MAAM;QACJC,EAAE,EAAEC,CAAC;QACLC,IAAI,EAAEC,CAAC;QACPC,SAAS,EAAEC,CAAC;QACZC,OAAO,EAAEC,CAAC;QACVC,gBAAgB,EAAEC,CAAC;QACnBC,WAAW,EAAEC,CAAC;QACdC,SAAS,EAAEC,CAAC;QACZC,UAAU,EAAEC,CAAC;QACbC,WAAW,EAAEC,CAAC;QACdC,KAAK,EAAEC,CAAC;QACRC,SAAS,EAAEC;MACb,CAAC,GAAG,IAAI,CAACC,KAAK;MAAEC,CAAC,GAAGpB,CAAC,IAAIA,CAAC,CAACqB,MAAM,GAAG,CAAC;MAAEC,CAAC,GAAG;QACzCC,SAAS,EAAEvB,CAAC;QACZ,GAAGgB;MACL,CAAC;MAAEQ,CAAC,GAAG/B,CAAC,CACN,QAAQ,EACR;QACE,UAAU,EAAES,CAAC;QACbuB,MAAM,EAAEvB,CAAC;QACT,aAAa,EAAEI,CAAC;QAChB,eAAe,EAAE,CAACI,CAAC,IAAI,CAACF,CAAC,IAAIY,CAAC;QAC9B,cAAc,EAAER;MAClB,CAAC,EACDM,CACF,CAAC;IACD,OAAO,eAAgB3B,CAAC,CAACmC,aAAa,CAAC,KAAK,EAAE;MAAE7B,EAAE,EAAEC,CAAC;MAAE6B,IAAI,EAAE,OAAO;MAAEZ,KAAK,EAAEO,CAAC;MAAEL,SAAS,EAAEO;IAAE,CAAC,EAAE,CAACtB,CAAC,IAAIY,CAAC,GAAG,IAAI,CAACK,KAAK,CAACS,QAAQ,GAAG,KAAK,CAAC,EAAExB,CAAC,GAAG,eAAgBb,CAAC,CAACmC,aAAa,CAAC,KAAK,EAAE;MAAET,SAAS,EAAE;IAA+B,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;EAC7O;AACF;AACA,SACEvB,CAAC,IAAImC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}