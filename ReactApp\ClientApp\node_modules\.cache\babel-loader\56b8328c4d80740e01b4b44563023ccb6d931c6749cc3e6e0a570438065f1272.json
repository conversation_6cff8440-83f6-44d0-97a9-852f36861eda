{"ast": null, "code": "import alignElements from './align-elements';\nexport default function vAlign(elements, rect, alignment) {\n  alignElements(elements, rect, alignment, \"y\", \"height\");\n}", "map": {"version": 3, "names": ["alignElements", "vAlign", "elements", "rect", "alignment"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/alignment/v-align.js"], "sourcesContent": ["import alignElements from './align-elements';\n\nexport default function vAlign(elements, rect, alignment) {\n    alignElements(elements, rect, alignment, \"y\", \"height\");\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,kBAAkB;AAE5C,eAAe,SAASC,MAAMA,CAACC,QAAQ,EAAEC,IAAI,EAAEC,SAAS,EAAE;EACtDJ,aAAa,CAACE,QAAQ,EAAEC,IAAI,EAAEC,SAAS,EAAE,GAAG,EAAE,QAAQ,CAAC;AAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}