{"ast": null, "code": "import { createApi } from '@reduxjs/toolkit/query/react';\nimport { baseQueryWithReAuth } from './interceptorsSlice';\nimport { OperationalServiceTypes } from '@iris/discovery.fe.client';\nimport config from '@app/utils/config';\nimport httpVerbs from '@app/utils/http/httpVerbs';\nexport const waiting = ms => {\n  return new Promise(resolve => setTimeout(resolve, ms));\n};\nexport const apiSlice = createApi({\n  reducerPath: '/tnc',\n  baseQuery: baseQueryWithReAuth,\n  endpoints: builder => ({\n    evaluateTnc: builder.query({\n      query: () => ({\n        url: `${config.api[OperationalServiceTypes.UserService].evaluateTnc}`\n      })\n    }),\n    acceptTnc: builder.mutation({\n      query: ({\n        termsAndConditionsId,\n        triggerType\n      }) => ({\n        url: `${config.api[OperationalServiceTypes.UserService].acceptTnc}`,\n        method: httpVerbs.POST,\n        body: {\n          termsAndConditionsId,\n          triggerType\n        }\n      })\n    })\n  })\n});\nexport const {\n  useEvaluateTncQuery,\n  useAcceptTncMutation\n} = apiSlice;", "map": {"version": 3, "names": ["createApi", "baseQueryWithReAuth", "OperationalServiceTypes", "config", "httpVerbs", "waiting", "ms", "Promise", "resolve", "setTimeout", "apiSlice", "reducerPath", "base<PERSON><PERSON>y", "endpoints", "builder", "evaluateTnc", "query", "url", "api", "UserService", "acceptTnc", "mutation", "termsAndConditionsId", "triggerType", "method", "POST", "body", "useEvaluateTncQuery", "useAcceptTncMutation"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/src/app/api/tncApiSlice.ts"], "sourcesContent": ["import { createApi } from '@reduxjs/toolkit/query/react';\r\nimport { baseQueryWithReAuth } from './interceptorsSlice';\r\nimport { OperationalServiceTypes } from '@iris/discovery.fe.client';\r\nimport config from '@app/utils/config';\r\nimport { AcceptTncResponse, EvaluateTncResponse, TncTags } from '@app/types/tncTypes';\r\nimport httpVerbs from '@app/utils/http/httpVerbs';\r\n\r\nexport const waiting = (ms: number): Promise<void> => {\r\n    return new Promise((resolve) => setTimeout(resolve, ms));\r\n};\r\n\r\n\r\nexport const apiSlice = createApi({\r\n    reducerPath: '/tnc',\r\n    baseQuery: baseQueryWithReAuth,\r\n    endpoints: (builder) => ({\r\n        evaluateTnc: builder.query<EvaluateTncResponse, void>({\r\n            query: () => ({\r\n                url: `${config.api[OperationalServiceTypes.UserService].evaluateTnc}`,\r\n            }),\r\n        }),\r\n        acceptTnc: builder.mutation<AcceptTncResponse, { termsAndConditionsId: number, triggerType: string }>({\r\n            query: ({ termsAndConditionsId, triggerType }) => ({\r\n                url: `${config.api[OperationalServiceTypes.UserService].acceptTnc}`,\r\n                method: httpVerbs.POST,\r\n                body: { termsAndConditionsId, triggerType },\r\n            }),\r\n\r\n        }),\r\n    })\r\n});\r\n\r\nexport const { useEvaluateTncQuery, useAcceptTncMutation } = apiSlice;\r\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,8BAA8B;AACxD,SAASC,mBAAmB,QAAQ,qBAAqB;AACzD,SAASC,uBAAuB,QAAQ,2BAA2B;AACnE,OAAOC,MAAM,MAAM,mBAAmB;AAEtC,OAAOC,SAAS,MAAM,2BAA2B;AAEjD,OAAO,MAAMC,OAAO,GAAIC,EAAU,IAAoB;EAClD,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAKC,UAAU,CAACD,OAAO,EAAEF,EAAE,CAAC,CAAC;AAC5D,CAAC;AAGD,OAAO,MAAMI,QAAQ,GAAGV,SAAS,CAAC;EAC9BW,WAAW,EAAE,MAAM;EACnBC,SAAS,EAAEX,mBAAmB;EAC9BY,SAAS,EAAGC,OAAO,KAAM;IACrBC,WAAW,EAAED,OAAO,CAACE,KAAK,CAA4B;MAClDA,KAAK,EAAEA,CAAA,MAAO;QACVC,GAAG,EAAE,GAAGd,MAAM,CAACe,GAAG,CAAChB,uBAAuB,CAACiB,WAAW,CAAC,CAACJ,WAAW;MACvE,CAAC;IACL,CAAC,CAAC;IACFK,SAAS,EAAEN,OAAO,CAACO,QAAQ,CAA2E;MAClGL,KAAK,EAAEA,CAAC;QAAEM,oBAAoB;QAAEC;MAAY,CAAC,MAAM;QAC/CN,GAAG,EAAE,GAAGd,MAAM,CAACe,GAAG,CAAChB,uBAAuB,CAACiB,WAAW,CAAC,CAACC,SAAS,EAAE;QACnEI,MAAM,EAAEpB,SAAS,CAACqB,IAAI;QACtBC,IAAI,EAAE;UAAEJ,oBAAoB;UAAEC;QAAY;MAC9C,CAAC;IAEL,CAAC;EACL,CAAC;AACL,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEI,mBAAmB;EAAEC;AAAqB,CAAC,GAAGlB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}