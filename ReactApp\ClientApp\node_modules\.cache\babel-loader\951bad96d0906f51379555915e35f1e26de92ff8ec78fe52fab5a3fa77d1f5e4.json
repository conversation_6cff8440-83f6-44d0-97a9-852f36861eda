{"ast": null, "code": "import { createApi } from '@reduxjs/toolkit/query/react';\nimport { baseQueryWithReAuth } from './interceptorsSlice';\nimport { mockAcceptTncError, mockAcceptTncSuccess, mockEvaluateTnc, mockEvaluateTncEmpty } from '@app/mocks/tnc.mock';\nexport const waiting = ms => {\n  return new Promise(resolve => setTimeout(resolve, ms));\n};\nlet evaluateCallCount = 0;\nexport const apiSlice = createApi({\n  reducerPath: '/tnc',\n  baseQuery: baseQueryWithReAuth,\n  // tagTypes: [TncTags.EvaluateTnc] as const,\n  endpoints: builder => ({\n    evaluateTnc: builder.query({\n      // query: () => ({\n      //   url: `${config.api[47].evaluateTnc}`,\n      // }),\n      queryFn: async () => {\n        console.log(\"EVALUATE\");\n\n        // evaluateCallCount += 1;\n\n        await waiting(4000);\n        if (evaluateCallCount === 2) {\n          return {\n            data: mockEvaluateTncEmpty\n          };\n        }\n        console.log(\"EVALUATE END\");\n        return {\n          data: mockEvaluateTnc\n        };\n      }\n      // providesTags: [TncTags.EvaluateTnc],\n    }),\n    acceptTnc: builder.mutation({\n      // query: ({ documentId }) => ({\n      //     url: `${config.api[47].acceptTnc}`,\n      //     method: httpVerbs.POST,\n      //     body: { documentId },\n      // }),\n      queryFn: async ({\n        termsAndConditionsId\n      }) => {\n        console.log(\"ACCEPT\", termsAndConditionsId);\n        await waiting(4000);\n        if (termsAndConditionsId === 42) {\n          return {\n            data: mockAcceptTncSuccess\n          };\n        }\n        return {\n          error: {\n            status: 400,\n            data: mockAcceptTncError\n          }\n        };\n      }\n      // invalidatesTags: [TncTags.EvaluateTnc],\n    })\n  })\n});\nexport const {\n  useEvaluateTncQuery,\n  useAcceptTncMutation\n} = apiSlice;", "map": {"version": 3, "names": ["createApi", "baseQueryWithReAuth", "mockAcceptTncError", "mockAcceptTncSuccess", "mockEvaluateTnc", "mockEvaluateTncEmpty", "waiting", "ms", "Promise", "resolve", "setTimeout", "evaluateCallCount", "apiSlice", "reducerPath", "base<PERSON><PERSON>y", "endpoints", "builder", "evaluateTnc", "query", "queryFn", "console", "log", "data", "acceptTnc", "mutation", "termsAndConditionsId", "error", "status", "useEvaluateTncQuery", "useAcceptTncMutation"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/src/app/api/tncApiSlice.ts"], "sourcesContent": ["import { createApi } from '@reduxjs/toolkit/query/react';\r\nimport { baseQueryWithReAuth } from './interceptorsSlice';\r\nimport { OperationalServiceTypes } from '@iris/discovery.fe.client';\r\nimport config from '@app/utils/config';\r\nimport { mockAcceptTncError, mockAcceptTncSuccess, mockEvaluateTnc, mockEvaluateTncEmpty } from '@app/mocks/tnc.mock';\r\nimport { AcceptTncResponse, EvaluateTncResponse, TncTags } from '@app/types/tncTypes';\r\nimport { httpVerbs } from '@app/utils/http';\r\n\r\nexport const waiting = (ms: number): Promise<void> => {\r\n    return new Promise((resolve) => setTimeout(resolve, ms));\r\n};\r\n\r\nlet evaluateCallCount = 0;\r\n\r\nexport const apiSlice = createApi({\r\n    reducerPath: '/tnc',\r\n    baseQuery: baseQueryWithReAuth,\r\n    // tagTypes: [TncTags.EvaluateTnc] as const,\r\n    endpoints: (builder) => ({\r\n        evaluateTnc: builder.query<EvaluateTncResponse, void>({\r\n            // query: () => ({\r\n            //   url: `${config.api[47].evaluateTnc}`,\r\n            // }),\r\n            queryFn: async () => {\r\n                console.log(\"EVALUATE\");\r\n\r\n                // evaluateCallCount += 1;\r\n\r\n                await waiting(4000);\r\n\r\n                if (evaluateCallCount === 2) {\r\n                    return { data: mockEvaluateTncEmpty };\r\n                }\r\n                console.log(\"EVALUATE END\");\r\n                return { data: mockEvaluateTnc };\r\n            },\r\n            // providesTags: [TncTags.EvaluateTnc],\r\n        }),\r\n        acceptTnc: builder.mutation<AcceptTncResponse, { termsAndConditionsId: number }>({\r\n            // query: ({ documentId }) => ({\r\n            //     url: `${config.api[47].acceptTnc}`,\r\n            //     method: httpVerbs.POST,\r\n            //     body: { documentId },\r\n            // }),\r\n            queryFn: async ({ termsAndConditionsId }) => {\r\n                console.log(\"ACCEPT\", termsAndConditionsId);\r\n                await waiting(4000);\r\n\r\n                if (termsAndConditionsId === 42) {\r\n                    return { data: mockAcceptTncSuccess };\r\n                }\r\n\r\n                return {\r\n                    error: {\r\n                        status: 400,\r\n                        data: mockAcceptTncError,\r\n                    },\r\n                };\r\n            },\r\n            // invalidatesTags: [TncTags.EvaluateTnc],\r\n        }),\r\n    }),\r\n});\r\n\r\nexport const { useEvaluateTncQuery, useAcceptTncMutation } = apiSlice;\r\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,8BAA8B;AACxD,SAASC,mBAAmB,QAAQ,qBAAqB;AAGzD,SAASC,kBAAkB,EAAEC,oBAAoB,EAAEC,eAAe,EAAEC,oBAAoB,QAAQ,qBAAqB;AAIrH,OAAO,MAAMC,OAAO,GAAIC,EAAU,IAAoB;EAClD,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAKC,UAAU,CAACD,OAAO,EAAEF,EAAE,CAAC,CAAC;AAC5D,CAAC;AAED,IAAII,iBAAiB,GAAG,CAAC;AAEzB,OAAO,MAAMC,QAAQ,GAAGZ,SAAS,CAAC;EAC9Ba,WAAW,EAAE,MAAM;EACnBC,SAAS,EAAEb,mBAAmB;EAC9B;EACAc,SAAS,EAAGC,OAAO,KAAM;IACrBC,WAAW,EAAED,OAAO,CAACE,KAAK,CAA4B;MAClD;MACA;MACA;MACAC,OAAO,EAAE,MAAAA,CAAA,KAAY;QACjBC,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;;QAEvB;;QAEA,MAAMf,OAAO,CAAC,IAAI,CAAC;QAEnB,IAAIK,iBAAiB,KAAK,CAAC,EAAE;UACzB,OAAO;YAAEW,IAAI,EAAEjB;UAAqB,CAAC;QACzC;QACAe,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;QAC3B,OAAO;UAAEC,IAAI,EAAElB;QAAgB,CAAC;MACpC;MACA;IACJ,CAAC,CAAC;IACFmB,SAAS,EAAEP,OAAO,CAACQ,QAAQ,CAAsD;MAC7E;MACA;MACA;MACA;MACA;MACAL,OAAO,EAAE,MAAAA,CAAO;QAAEM;MAAqB,CAAC,KAAK;QACzCL,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEI,oBAAoB,CAAC;QAC3C,MAAMnB,OAAO,CAAC,IAAI,CAAC;QAEnB,IAAImB,oBAAoB,KAAK,EAAE,EAAE;UAC7B,OAAO;YAAEH,IAAI,EAAEnB;UAAqB,CAAC;QACzC;QAEA,OAAO;UACHuB,KAAK,EAAE;YACHC,MAAM,EAAE,GAAG;YACXL,IAAI,EAAEpB;UACV;QACJ,CAAC;MACL;MACA;IACJ,CAAC;EACL,CAAC;AACL,CAAC,CAAC;AAEF,OAAO,MAAM;EAAE0B,mBAAmB;EAAEC;AAAqB,CAAC,GAAGjB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}