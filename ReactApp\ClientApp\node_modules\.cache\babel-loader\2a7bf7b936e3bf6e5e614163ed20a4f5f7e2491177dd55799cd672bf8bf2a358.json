{"ast": null, "code": "import translateToPoint from './translate-to-point';\nimport alignStart from './align-start';\nexport default function alignElements(elements, rect, alignment, axis, sizeField) {\n  for (var idx = 0; idx < elements.length; idx++) {\n    var bbox = elements[idx].clippedBBox();\n    if (bbox) {\n      var point = bbox.origin.clone();\n      point[axis] = alignStart(bbox.size[sizeField], rect, alignment || \"start\", axis, sizeField);\n      translateToPoint(point, bbox, elements[idx]);\n    }\n  }\n}", "map": {"version": 3, "names": ["translateToPoint", "alignStart", "alignElements", "elements", "rect", "alignment", "axis", "sizeField", "idx", "length", "bbox", "clippedBBox", "point", "origin", "clone", "size"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/alignment/align-elements.js"], "sourcesContent": ["import translateToPoint from './translate-to-point';\nimport alignStart from './align-start';\n\nexport default function alignElements(elements, rect, alignment, axis, sizeField) {\n    for (var idx = 0; idx < elements.length; idx++) {\n        var bbox = elements[idx].clippedBBox();\n        if (bbox) {\n            var point = bbox.origin.clone();\n            point[axis] = alignStart(bbox.size[sizeField], rect, alignment || \"start\", axis, sizeField);\n            translateToPoint(point, bbox, elements[idx]);\n        }\n    }\n}"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,sBAAsB;AACnD,OAAOC,UAAU,MAAM,eAAe;AAEtC,eAAe,SAASC,aAAaA,CAACC,QAAQ,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,EAAEC,SAAS,EAAE;EAC9E,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGL,QAAQ,CAACM,MAAM,EAAED,GAAG,EAAE,EAAE;IAC5C,IAAIE,IAAI,GAAGP,QAAQ,CAACK,GAAG,CAAC,CAACG,WAAW,CAAC,CAAC;IACtC,IAAID,IAAI,EAAE;MACN,IAAIE,KAAK,GAAGF,IAAI,CAACG,MAAM,CAACC,KAAK,CAAC,CAAC;MAC/BF,KAAK,CAACN,IAAI,CAAC,GAAGL,UAAU,CAACS,IAAI,CAACK,IAAI,CAACR,SAAS,CAAC,EAAEH,IAAI,EAAEC,SAAS,IAAI,OAAO,EAAEC,IAAI,EAAEC,SAAS,CAAC;MAC3FP,gBAAgB,CAACY,KAAK,EAAEF,IAAI,EAAEP,QAAQ,CAACK,GAAG,CAAC,CAAC;IAChD;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}