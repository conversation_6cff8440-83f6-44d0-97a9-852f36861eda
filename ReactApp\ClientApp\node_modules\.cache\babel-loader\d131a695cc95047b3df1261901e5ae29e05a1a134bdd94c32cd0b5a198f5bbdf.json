{"ast": null, "code": "var traversable = function (TBase, childrenField) {\n  return function (TBase) {\n    function anonymous() {\n      TBase.apply(this, arguments);\n    }\n    if (TBase) anonymous.__proto__ = TBase;\n    anonymous.prototype = Object.create(TBase && TBase.prototype);\n    anonymous.prototype.constructor = anonymous;\n    anonymous.prototype.traverse = function traverse(callback) {\n      var children = this[childrenField];\n      for (var i = 0; i < children.length; i++) {\n        var child = children[i];\n        if (child.traverse) {\n          child.traverse(callback);\n        } else {\n          callback(child);\n        }\n      }\n      return this;\n    };\n    return anonymous;\n  }(TBase);\n};\nexport default traversable;", "map": {"version": 3, "names": ["traversable", "TBase", "childrenField", "anonymous", "apply", "arguments", "__proto__", "prototype", "Object", "create", "constructor", "traverse", "callback", "children", "i", "length", "child"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/mixins/traversable.js"], "sourcesContent": ["var traversable = function (TBase, childrenField) { return (\n    (function (TBase) {\n        function anonymous () {\n            TBase.apply(this, arguments);\n        }\n\n        if ( TBase ) anonymous.__proto__ = TBase;\n        anonymous.prototype = Object.create( TBase && TBase.prototype );\n        anonymous.prototype.constructor = anonymous;\n\n        anonymous.prototype.traverse = function traverse (callback) {\n            var children = this[childrenField];\n\n            for (var i = 0; i < children.length; i++) {\n                var child = children[i];\n\n                if (child.traverse) {\n                    child.traverse(callback);\n                } else {\n                    callback(child);\n                }\n            }\n\n            return this;\n        };\n\n        return anonymous;\n    }(TBase))\n); };\n\nexport default traversable;\n"], "mappings": "AAAA,IAAIA,WAAW,GAAG,SAAAA,CAAUC,KAAK,EAAEC,aAAa,EAAE;EAAE,OAC/C,UAAUD,KAAK,EAAE;IACd,SAASE,SAASA,CAAA,EAAI;MAClBF,KAAK,CAACG,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAChC;IAEA,IAAKJ,KAAK,EAAGE,SAAS,CAACG,SAAS,GAAGL,KAAK;IACxCE,SAAS,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAER,KAAK,IAAIA,KAAK,CAACM,SAAU,CAAC;IAC/DJ,SAAS,CAACI,SAAS,CAACG,WAAW,GAAGP,SAAS;IAE3CA,SAAS,CAACI,SAAS,CAACI,QAAQ,GAAG,SAASA,QAAQA,CAAEC,QAAQ,EAAE;MACxD,IAAIC,QAAQ,GAAG,IAAI,CAACX,aAAa,CAAC;MAElC,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,QAAQ,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;QACtC,IAAIE,KAAK,GAAGH,QAAQ,CAACC,CAAC,CAAC;QAEvB,IAAIE,KAAK,CAACL,QAAQ,EAAE;UAChBK,KAAK,CAACL,QAAQ,CAACC,QAAQ,CAAC;QAC5B,CAAC,MAAM;UACHA,QAAQ,CAACI,KAAK,CAAC;QACnB;MACJ;MAEA,OAAO,IAAI;IACf,CAAC;IAED,OAAOb,SAAS;EACpB,CAAC,CAACF,KAAK,CAAC;AACT,CAAC;AAEJ,eAAeD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}