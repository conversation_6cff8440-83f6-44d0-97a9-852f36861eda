{"ast": null, "code": "import { replaceStyleAttr, restoreStyleAttr, setInnerHTML } from '../../util/element-set-styles-safe';\nimport { SVG_NS } from '../constants';\nvar renderSVG = function (container, svg) {\n  setInnerHTML(container, svg);\n};\nif (typeof document !== \"undefined\") {\n  var testFragment = \"<svg xmlns='\" + SVG_NS + \"'></svg>\";\n  var testContainer = document.createElement(\"div\");\n  var hasParser = typeof DOMParser !== \"undefined\";\n  testContainer.innerHTML = testFragment;\n  if (hasParser && testContainer.firstChild.namespaceURI !== SVG_NS) {\n    renderSVG = function (container, svg) {\n      var parser = new DOMParser();\n      var chartDoc = parser.parseFromString(replaceStyleAttr(svg), \"text/xml\");\n      restoreStyleAttr(chartDoc);\n      var importedDoc = document.adoptNode(chartDoc.documentElement);\n      container.innerHTML = \"\";\n      container.appendChild(importedDoc);\n    };\n  }\n}\nexport default renderSVG;", "map": {"version": 3, "names": ["replaceStyleAttr", "restoreStyleAttr", "setInnerHTML", "SVG_NS", "renderSVG", "container", "svg", "document", "testFragment", "testC<PERSON>r", "createElement", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "innerHTML", "<PERSON><PERSON><PERSON><PERSON>", "namespaceURI", "parser", "chartDoc", "parseFromString", "importedDoc", "adoptNode", "documentElement", "append<PERSON><PERSON><PERSON>"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/svg/utils/render-svg.js"], "sourcesContent": ["import { replaceStyleAttr, restoreStyleAttr, setInnerHTML } from '../../util/element-set-styles-safe';\nimport { SVG_NS } from '../constants';\n\nvar renderSVG = function(container, svg) {\n    setInnerHTML(container, svg);\n};\n\nif (typeof document !== \"undefined\") {\n    var testFragment = \"<svg xmlns='\" + SVG_NS + \"'></svg>\";\n    var testContainer = document.createElement(\"div\");\n    var hasParser = typeof DOMParser !== \"undefined\";\n\n    testContainer.innerHTML = testFragment;\n\n    if (hasParser && testContainer.firstChild.namespaceURI !== SVG_NS) {\n        renderSVG = function(container, svg) {\n            var parser = new DOMParser();\n            var chartDoc = parser.parseFromString(replaceStyleAttr(svg), \"text/xml\");\n            restoreStyleAttr(chartDoc);\n            var importedDoc = document.adoptNode(chartDoc.documentElement);\n\n            container.innerHTML = \"\";\n            container.appendChild(importedDoc);\n        };\n    }\n}\n\nexport default renderSVG;\n"], "mappings": "AAAA,SAASA,gBAAgB,EAAEC,gBAAgB,EAAEC,YAAY,QAAQ,oCAAoC;AACrG,SAASC,MAAM,QAAQ,cAAc;AAErC,IAAIC,SAAS,GAAG,SAAAA,CAASC,SAAS,EAAEC,GAAG,EAAE;EACrCJ,YAAY,CAACG,SAAS,EAAEC,GAAG,CAAC;AAChC,CAAC;AAED,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;EACjC,IAAIC,YAAY,GAAG,cAAc,GAAGL,MAAM,GAAG,UAAU;EACvD,IAAIM,aAAa,GAAGF,QAAQ,CAACG,aAAa,CAAC,KAAK,CAAC;EACjD,IAAIC,SAAS,GAAG,OAAOC,SAAS,KAAK,WAAW;EAEhDH,aAAa,CAACI,SAAS,GAAGL,YAAY;EAEtC,IAAIG,SAAS,IAAIF,aAAa,CAACK,UAAU,CAACC,YAAY,KAAKZ,MAAM,EAAE;IAC/DC,SAAS,GAAG,SAAAA,CAASC,SAAS,EAAEC,GAAG,EAAE;MACjC,IAAIU,MAAM,GAAG,IAAIJ,SAAS,CAAC,CAAC;MAC5B,IAAIK,QAAQ,GAAGD,MAAM,CAACE,eAAe,CAAClB,gBAAgB,CAACM,GAAG,CAAC,EAAE,UAAU,CAAC;MACxEL,gBAAgB,CAACgB,QAAQ,CAAC;MAC1B,IAAIE,WAAW,GAAGZ,QAAQ,CAACa,SAAS,CAACH,QAAQ,CAACI,eAAe,CAAC;MAE9DhB,SAAS,CAACQ,SAAS,GAAG,EAAE;MACxBR,SAAS,CAACiB,WAAW,CAACH,WAAW,CAAC;IACtC,CAAC;EACL;AACJ;AAEA,eAAef,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}