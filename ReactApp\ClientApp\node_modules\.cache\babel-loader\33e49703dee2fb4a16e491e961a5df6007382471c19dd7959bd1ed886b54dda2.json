{"ast": null, "code": "import withGeometry from '../mixins/with-geometry';\nimport Element from './element';\nimport Rect from '../geometry/rect';\nimport toMatrix from '../geometry/to-matrix';\nimport { defined } from '../util';\nvar Image = function (superclass) {\n  function Image(src, rect, options) {\n    if (rect === void 0) rect = new Rect();\n    if (options === void 0) options = {};\n    superclass.call(this, options);\n    this.src(src);\n    this.rect(rect);\n  }\n  if (superclass) Image.__proto__ = superclass;\n  Image.prototype = Object.create(superclass && superclass.prototype);\n  Image.prototype.constructor = Image;\n  var prototypeAccessors = {\n    nodeType: {\n      configurable: true\n    }\n  };\n  prototypeAccessors.nodeType.get = function () {\n    return \"Image\";\n  };\n  Image.prototype.src = function src(value) {\n    if (defined(value)) {\n      this.options.set(\"src\", value);\n      return this;\n    }\n    return this.options.get(\"src\");\n  };\n  Image.prototype.bbox = function bbox(transformation) {\n    var combinedMatrix = toMatrix(this.currentTransform(transformation));\n    return this._rect.bbox(combinedMatrix);\n  };\n  Image.prototype.rawBBox = function rawBBox() {\n    return this._rect.bbox();\n  };\n  Image.prototype._containsPoint = function _containsPoint(point) {\n    return this._rect.containsPoint(point);\n  };\n  Image.prototype._hasFill = function _hasFill() {\n    return this.src();\n  };\n  Object.defineProperties(Image.prototype, prototypeAccessors);\n  return Image;\n}(withGeometry(Element, [\"rect\"]));\nexport default Image;", "map": {"version": 3, "names": ["withGeometry", "Element", "Rect", "toMatrix", "defined", "Image", "superclass", "src", "rect", "options", "call", "__proto__", "prototype", "Object", "create", "constructor", "prototypeAccessors", "nodeType", "configurable", "get", "value", "set", "bbox", "transformation", "combinedMatrix", "currentTransform", "_rect", "rawBBox", "_containsPoint", "point", "containsPoint", "_hasFill", "defineProperties"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/shapes/image.js"], "sourcesContent": ["import withGeometry from '../mixins/with-geometry';\nimport Element from './element';\nimport Rect from '../geometry/rect';\nimport toMatrix from '../geometry/to-matrix';\nimport { defined } from '../util';\n\n\nvar Image = (function (superclass) {\n    function Image(src, rect, options) {\n        if ( rect === void 0 ) rect = new Rect();\n        if ( options === void 0 ) options = {};\n\n        superclass.call(this, options);\n\n        this.src(src);\n        this.rect(rect);\n    }\n\n    if ( superclass ) Image.__proto__ = superclass;\n    Image.prototype = Object.create( superclass && superclass.prototype );\n    Image.prototype.constructor = Image;\n\n    var prototypeAccessors = { nodeType: { configurable: true } };\n\n    prototypeAccessors.nodeType.get = function () {\n        return \"Image\";\n    };\n\n    Image.prototype.src = function src (value) {\n        if (defined(value)) {\n            this.options.set(\"src\", value);\n            return this;\n        }\n\n        return this.options.get(\"src\");\n    };\n\n    Image.prototype.bbox = function bbox (transformation) {\n        var combinedMatrix = toMatrix(this.currentTransform(transformation));\n        return this._rect.bbox(combinedMatrix);\n    };\n\n    Image.prototype.rawBBox = function rawBBox () {\n        return this._rect.bbox();\n    };\n\n    Image.prototype._containsPoint = function _containsPoint (point) {\n        return this._rect.containsPoint(point);\n    };\n\n    Image.prototype._hasFill = function _hasFill () {\n        return this.src();\n    };\n\n    Object.defineProperties( Image.prototype, prototypeAccessors );\n\n    return Image;\n}(withGeometry(Element, [ \"rect\" ])));\n\nexport default Image;"], "mappings": "AAAA,OAAOA,YAAY,MAAM,yBAAyB;AAClD,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,IAAI,MAAM,kBAAkB;AACnC,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,SAASC,OAAO,QAAQ,SAAS;AAGjC,IAAIC,KAAK,GAAI,UAAUC,UAAU,EAAE;EAC/B,SAASD,KAAKA,CAACE,GAAG,EAAEC,IAAI,EAAEC,OAAO,EAAE;IAC/B,IAAKD,IAAI,KAAK,KAAK,CAAC,EAAGA,IAAI,GAAG,IAAIN,IAAI,CAAC,CAAC;IACxC,IAAKO,OAAO,KAAK,KAAK,CAAC,EAAGA,OAAO,GAAG,CAAC,CAAC;IAEtCH,UAAU,CAACI,IAAI,CAAC,IAAI,EAAED,OAAO,CAAC;IAE9B,IAAI,CAACF,GAAG,CAACA,GAAG,CAAC;IACb,IAAI,CAACC,IAAI,CAACA,IAAI,CAAC;EACnB;EAEA,IAAKF,UAAU,EAAGD,KAAK,CAACM,SAAS,GAAGL,UAAU;EAC9CD,KAAK,CAACO,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAER,UAAU,IAAIA,UAAU,CAACM,SAAU,CAAC;EACrEP,KAAK,CAACO,SAAS,CAACG,WAAW,GAAGV,KAAK;EAEnC,IAAIW,kBAAkB,GAAG;IAAEC,QAAQ,EAAE;MAAEC,YAAY,EAAE;IAAK;EAAE,CAAC;EAE7DF,kBAAkB,CAACC,QAAQ,CAACE,GAAG,GAAG,YAAY;IAC1C,OAAO,OAAO;EAClB,CAAC;EAEDd,KAAK,CAACO,SAAS,CAACL,GAAG,GAAG,SAASA,GAAGA,CAAEa,KAAK,EAAE;IACvC,IAAIhB,OAAO,CAACgB,KAAK,CAAC,EAAE;MAChB,IAAI,CAACX,OAAO,CAACY,GAAG,CAAC,KAAK,EAAED,KAAK,CAAC;MAC9B,OAAO,IAAI;IACf;IAEA,OAAO,IAAI,CAACX,OAAO,CAACU,GAAG,CAAC,KAAK,CAAC;EAClC,CAAC;EAEDd,KAAK,CAACO,SAAS,CAACU,IAAI,GAAG,SAASA,IAAIA,CAAEC,cAAc,EAAE;IAClD,IAAIC,cAAc,GAAGrB,QAAQ,CAAC,IAAI,CAACsB,gBAAgB,CAACF,cAAc,CAAC,CAAC;IACpE,OAAO,IAAI,CAACG,KAAK,CAACJ,IAAI,CAACE,cAAc,CAAC;EAC1C,CAAC;EAEDnB,KAAK,CAACO,SAAS,CAACe,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;IAC1C,OAAO,IAAI,CAACD,KAAK,CAACJ,IAAI,CAAC,CAAC;EAC5B,CAAC;EAEDjB,KAAK,CAACO,SAAS,CAACgB,cAAc,GAAG,SAASA,cAAcA,CAAEC,KAAK,EAAE;IAC7D,OAAO,IAAI,CAACH,KAAK,CAACI,aAAa,CAACD,KAAK,CAAC;EAC1C,CAAC;EAEDxB,KAAK,CAACO,SAAS,CAACmB,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;IAC5C,OAAO,IAAI,CAACxB,GAAG,CAAC,CAAC;EACrB,CAAC;EAEDM,MAAM,CAACmB,gBAAgB,CAAE3B,KAAK,CAACO,SAAS,EAAEI,kBAAmB,CAAC;EAE9D,OAAOX,KAAK;AAChB,CAAC,CAACL,YAAY,CAACC,OAAO,EAAE,CAAE,MAAM,CAAE,CAAC,CAAE;AAErC,eAAeI,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}