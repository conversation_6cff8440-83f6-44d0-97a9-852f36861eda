{"ast": null, "code": "import defined from './defined';\nexport default function eventCoordinates(e) {\n  if (defined((e.x || {}).location)) {\n    return {\n      x: e.x.location,\n      y: e.y.location\n    };\n  }\n  return {\n    x: e.pageX || e.clientX || 0,\n    y: e.pageY || e.clientY || 0\n  };\n}", "map": {"version": 3, "names": ["defined", "eventCoordinates", "e", "x", "location", "y", "pageX", "clientX", "pageY", "clientY"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/util/event-coordinates.js"], "sourcesContent": ["import defined from './defined';\n\nexport default function eventCoordinates(e) {\n    if (defined((e.x || {}).location)) {\n        return {\n            x: e.x.location,\n            y: e.y.location\n        };\n    }\n\n    return {\n        x: e.pageX || e.clientX || 0,\n        y: e.pageY || e.clientY || 0\n    };\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,WAAW;AAE/B,eAAe,SAASC,gBAAgBA,CAACC,CAAC,EAAE;EACxC,IAAIF,OAAO,CAAC,CAACE,CAAC,CAACC,CAAC,IAAI,CAAC,CAAC,EAAEC,QAAQ,CAAC,EAAE;IAC/B,OAAO;MACHD,CAAC,EAAED,CAAC,CAACC,CAAC,CAACC,QAAQ;MACfC,CAAC,EAAEH,CAAC,CAACG,CAAC,CAACD;IACX,CAAC;EACL;EAEA,OAAO;IACHD,CAAC,EAAED,CAAC,CAACI,KAAK,IAAIJ,CAAC,CAACK,OAAO,IAAI,CAAC;IAC5BF,CAAC,EAAEH,CAAC,CAACM,KAAK,IAAIN,CAAC,CAACO,OAAO,IAAI;EAC/B,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}