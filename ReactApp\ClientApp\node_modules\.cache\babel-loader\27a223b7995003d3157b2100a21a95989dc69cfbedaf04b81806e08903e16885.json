{"ast": null, "code": "function setAccessor(field) {\n  return function (value) {\n    if (this[field] !== value) {\n      this[field] = value;\n      this.geometryChange();\n    }\n    return this;\n  };\n}\nfunction getAccessor(field) {\n  return function () {\n    return this[field];\n  };\n}\nfunction defineAccessors(fn, fields) {\n  for (var i = 0; i < fields.length; i++) {\n    var name = fields[i];\n    var capitalized = name.charAt(0).toUpperCase() + name.substring(1, name.length);\n    fn[\"set\" + capitalized] = setAccessor(name);\n    fn[\"get\" + capitalized] = getAccessor(name);\n  }\n}\nvar withAccessors = function (TBase, names) {\n  var result = function (TBase) {\n    function result() {\n      TBase.apply(this, arguments);\n    }\n    if (TBase) result.__proto__ = TBase;\n    result.prototype = Object.create(TBase && TBase.prototype);\n    result.prototype.constructor = result;\n    return result;\n  }(TBase);\n  defineAccessors(result.prototype, names);\n  return result;\n};\nexport default withAccessors;", "map": {"version": 3, "names": ["setAccessor", "field", "value", "geometryChange", "getAccessor", "defineAccessors", "fn", "fields", "i", "length", "name", "capitalized", "char<PERSON>t", "toUpperCase", "substring", "withAccessors", "TBase", "names", "result", "apply", "arguments", "__proto__", "prototype", "Object", "create", "constructor"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/mixins/with-accessors.js"], "sourcesContent": ["function setAccessor(field) {\n    return function(value) {\n        if (this[field] !== value) {\n            this[field] = value;\n            this.geometryChange();\n        }\n\n        return this;\n    };\n}\n\nfunction getAccessor(field) {\n    return function() {\n        return this[field];\n    };\n}\n\nfunction defineAccessors(fn, fields) {\n    for (var i = 0; i < fields.length; i++) {\n        var name = fields[i];\n        var capitalized = name.charAt(0).toUpperCase() +\n                          name.substring(1, name.length);\n\n        fn[\"set\" + capitalized] = setAccessor(name);\n        fn[\"get\" + capitalized] = getAccessor(name);\n    }\n}\n\nvar withAccessors = function (TBase, names) {\n    var result = (function (TBase) {\n        function result () {\n            TBase.apply(this, arguments);\n        }if ( TBase ) result.__proto__ = TBase;\n        result.prototype = Object.create( TBase && TBase.prototype );\n        result.prototype.constructor = result;\n\n        \n\n        return result;\n    }(TBase));\n    defineAccessors(result.prototype, names);\n\n    return result;\n};\n\nexport default withAccessors;\n"], "mappings": "AAAA,SAASA,WAAWA,CAACC,KAAK,EAAE;EACxB,OAAO,UAASC,KAAK,EAAE;IACnB,IAAI,IAAI,CAACD,KAAK,CAAC,KAAKC,KAAK,EAAE;MACvB,IAAI,CAACD,KAAK,CAAC,GAAGC,KAAK;MACnB,IAAI,CAACC,cAAc,CAAC,CAAC;IACzB;IAEA,OAAO,IAAI;EACf,CAAC;AACL;AAEA,SAASC,WAAWA,CAACH,KAAK,EAAE;EACxB,OAAO,YAAW;IACd,OAAO,IAAI,CAACA,KAAK,CAAC;EACtB,CAAC;AACL;AAEA,SAASI,eAAeA,CAACC,EAAE,EAAEC,MAAM,EAAE;EACjC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,MAAM,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IACpC,IAAIE,IAAI,GAAGH,MAAM,CAACC,CAAC,CAAC;IACpB,IAAIG,WAAW,GAAGD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAC5BH,IAAI,CAACI,SAAS,CAAC,CAAC,EAAEJ,IAAI,CAACD,MAAM,CAAC;IAEhDH,EAAE,CAAC,KAAK,GAAGK,WAAW,CAAC,GAAGX,WAAW,CAACU,IAAI,CAAC;IAC3CJ,EAAE,CAAC,KAAK,GAAGK,WAAW,CAAC,GAAGP,WAAW,CAACM,IAAI,CAAC;EAC/C;AACJ;AAEA,IAAIK,aAAa,GAAG,SAAAA,CAAUC,KAAK,EAAEC,KAAK,EAAE;EACxC,IAAIC,MAAM,GAAI,UAAUF,KAAK,EAAE;IAC3B,SAASE,MAAMA,CAAA,EAAI;MACfF,KAAK,CAACG,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAChC;IAAC,IAAKJ,KAAK,EAAGE,MAAM,CAACG,SAAS,GAAGL,KAAK;IACtCE,MAAM,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAER,KAAK,IAAIA,KAAK,CAACM,SAAU,CAAC;IAC5DJ,MAAM,CAACI,SAAS,CAACG,WAAW,GAAGP,MAAM;IAIrC,OAAOA,MAAM;EACjB,CAAC,CAACF,KAAK,CAAE;EACTX,eAAe,CAACa,MAAM,CAACI,SAAS,EAAEL,KAAK,CAAC;EAExC,OAAOC,MAAM;AACjB,CAAC;AAED,eAAeH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}