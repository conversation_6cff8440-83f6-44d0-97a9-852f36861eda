{"ast": null, "code": "function isFunction(fun) {\n  return typeof fun === 'function';\n}\nexport default function isDate(value) {\n  return Boolean(value) && isFunction(value.getTime) && isFunction(value.getMonth);\n}", "map": {"version": 3, "names": ["isFunction", "fun", "isDate", "value", "Boolean", "getTime", "getMonth"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-intl/dist/es/common/is-date.js"], "sourcesContent": ["function isFunction(fun) {\n    return typeof(fun) === 'function';\n}\n\nexport default function isDate(value) {\n    return Boolean(value) && isFunction(value.getTime) && isFunction(value.getMonth);\n}\n"], "mappings": "AAAA,SAASA,UAAUA,CAACC,GAAG,EAAE;EACrB,OAAO,OAAOA,GAAI,KAAK,UAAU;AACrC;AAEA,eAAe,SAASC,MAAMA,CAACC,KAAK,EAAE;EAClC,OAAOC,OAAO,CAACD,KAAK,CAAC,IAAIH,UAAU,CAACG,KAAK,CAACE,OAAO,CAAC,IAAIL,UAAU,CAACG,KAAK,CAACG,QAAQ,CAAC;AACpF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}