{"ast": null, "code": "import alignPoint from './align-point';\nimport collision from './collision';\nvar fit = function (position, size, viewPortSize) {\n  var output = 0;\n  if (position + size > viewPortSize) {\n    output = viewPortSize - (position + size);\n  }\n  if (position < 0) {\n    output = -position;\n  }\n  return output;\n};\nvar flip = function (ref) {\n  var offset = ref.offset;\n  var size = ref.size;\n  var anchorSize = ref.anchorSize;\n  var viewPortSize = ref.viewPortSize;\n  var anchorAlignPoint = ref.anchorAlignPoint;\n  var elementAlignPoint = ref.elementAlignPoint;\n  var margin = ref.margin;\n  var output = 0;\n  var isPositionCentered = elementAlignPoint === alignPoint.center || elementAlignPoint === alignPoint.middle;\n  var isOriginCentered = anchorAlignPoint === alignPoint.center || anchorAlignPoint === alignPoint.middle;\n  var marginToAdd = 2 * margin; //2x to keep margin after flip\n\n  if (elementAlignPoint !== anchorAlignPoint && !isPositionCentered && !isOriginCentered) {\n    var isBeforeAnchor = anchorAlignPoint === alignPoint.top || anchorAlignPoint === alignPoint.left;\n    if (offset < 0 && isBeforeAnchor) {\n      output = size + anchorSize + marginToAdd;\n      if (offset + output + size > viewPortSize) {\n        output = 0; //skip flip\n      }\n    } else if (offset >= 0 && !isBeforeAnchor) {\n      if (offset + size > viewPortSize) {\n        output += -(anchorSize + size + marginToAdd);\n      }\n      if (offset + output < 0) {\n        output = 0; //skip flip\n      }\n    }\n  }\n  return output;\n};\nvar restrictToView = function (options) {\n  var anchorRect = options.anchorRect;\n  var anchorAlign = options.anchorAlign;\n  var elementRect = options.elementRect;\n  var elementAlign = options.elementAlign;\n  var collisions = options.collisions;\n  var viewPort = options.viewPort;\n  var margin = options.margin;\n  if (margin === void 0) margin = {};\n  var elementTop = elementRect.top;\n  var elementLeft = elementRect.left;\n  var elementHeight = elementRect.height;\n  var elementWidth = elementRect.width;\n  var viewPortHeight = viewPort.height;\n  var viewPortWidth = viewPort.width;\n  var horizontalMargin = margin.horizontal || 0;\n  var verticalMargin = margin.vertical || 0;\n  var left = 0;\n  var top = 0;\n  var isVerticalFit = collisions.vertical === collision.fit;\n  var isHorizontalFit = collisions.horizontal === collision.fit;\n  var isVerticalFlip = collisions.vertical === collision.flip;\n  var isHorizontalFlip = collisions.horizontal === collision.flip;\n  if (isVerticalFit) {\n    top += fit(elementTop, elementHeight, viewPortHeight);\n  }\n  if (isHorizontalFit) {\n    left += fit(elementLeft, elementWidth, viewPortWidth);\n  }\n  if (isVerticalFlip) {\n    top += flip({\n      margin: verticalMargin,\n      offset: elementTop,\n      size: elementHeight,\n      anchorSize: anchorRect.height,\n      viewPortSize: viewPortHeight,\n      anchorAlignPoint: anchorAlign.vertical,\n      elementAlignPoint: elementAlign.vertical\n    });\n  }\n  if (isHorizontalFlip) {\n    left += flip({\n      margin: horizontalMargin,\n      offset: elementLeft,\n      size: elementWidth,\n      anchorSize: anchorRect.width,\n      viewPortSize: viewPortWidth,\n      anchorAlignPoint: anchorAlign.horizontal,\n      elementAlignPoint: elementAlign.horizontal\n    });\n  }\n  var flippedVertical = isVerticalFlip && top !== 0;\n  var flippedHorizontal = isHorizontalFlip && left !== 0;\n  var fittedVertical = isVerticalFit && top !== 0;\n  var fittedHorizontal = isHorizontalFit && left !== 0;\n  return {\n    flipped: flippedHorizontal || flippedVertical,\n    fitted: fittedVertical || fittedHorizontal,\n    flip: {\n      horizontal: flippedHorizontal,\n      vertical: flippedVertical\n    },\n    fit: {\n      horizontal: fittedHorizontal,\n      vertical: fittedVertical\n    },\n    offset: {\n      left: left,\n      top: top\n    }\n  };\n};\nexport default restrictToView;", "map": {"version": 3, "names": ["alignPoint", "collision", "fit", "position", "size", "viewPortSize", "output", "flip", "ref", "offset", "anchorSize", "anchorAlignPoint", "elementAlignPoint", "margin", "isPositionCentered", "center", "middle", "isOriginCentered", "marginToAdd", "isBeforeAnchor", "top", "left", "restrict<PERSON><PERSON><PERSON>iew", "options", "anchorRect", "anchorAlign", "elementRect", "elementAlign", "collisions", "viewPort", "elementTop", "elementLeft", "elementHeight", "height", "elementWidth", "width", "viewPortHeight", "viewPortWidth", "<PERSON><PERSON><PERSON><PERSON>", "horizontal", "verticalMargin", "vertical", "isVerticalFit", "isHorizontalFit", "isVerticalFlip", "isHorizontalFlip", "flippedVertical", "flippedHorizontal", "fittedVertical", "fittedHorizontal", "flipped", "fitted"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-popup-common/dist/es/restrict-to-view.js"], "sourcesContent": ["import alignPoint from './align-point';\nimport collision from './collision';\n\nvar fit = function(position, size, viewPortSize) {\n    var output = 0;\n\n    if (position + size > viewPortSize) {\n        output = viewPortSize - (position + size);\n    }\n\n    if (position < 0) {\n        output = -position;\n    }\n\n    return output;\n};\n\nvar flip = function(ref) {\n    var offset = ref.offset;\n    var size = ref.size;\n    var anchorSize = ref.anchorSize;\n    var viewPortSize = ref.viewPortSize;\n    var anchorAlignPoint = ref.anchorAlignPoint;\n    var elementAlignPoint = ref.elementAlignPoint;\n    var margin = ref.margin;\n\n    var output = 0;\n\n    var isPositionCentered = elementAlignPoint === alignPoint.center || elementAlignPoint === alignPoint.middle;\n    var isOriginCentered = anchorAlignPoint === alignPoint.center || anchorAlignPoint === alignPoint.middle;\n    var marginToAdd = 2 * margin; //2x to keep margin after flip\n\n    if (elementAlignPoint !== anchorAlignPoint && !isPositionCentered && !isOriginCentered) {\n        var isBeforeAnchor = anchorAlignPoint === alignPoint.top || anchorAlignPoint === alignPoint.left;\n        if (offset < 0 && isBeforeAnchor) {\n            output = size + anchorSize + marginToAdd;\n            if (offset + output + size > viewPortSize) {\n                output = 0; //skip flip\n            }\n        } else if (offset >= 0 && !isBeforeAnchor) {\n            if (offset + size > viewPortSize) {\n                output += -(anchorSize + size + marginToAdd);\n            }\n\n            if (offset + output < 0) {\n                output = 0; //skip flip\n            }\n        }\n    }\n\n    return output;\n};\n\nvar restrictToView = function (options) {\n    var anchorRect = options.anchorRect;\n    var anchorAlign = options.anchorAlign;\n    var elementRect = options.elementRect;\n    var elementAlign = options.elementAlign;\n    var collisions = options.collisions;\n    var viewPort = options.viewPort;\n    var margin = options.margin; if ( margin === void 0 ) margin = {};\n    var elementTop = elementRect.top;\n    var elementLeft = elementRect.left;\n    var elementHeight = elementRect.height;\n    var elementWidth = elementRect.width;\n    var viewPortHeight = viewPort.height;\n    var viewPortWidth = viewPort.width;\n    var horizontalMargin = margin.horizontal || 0;\n    var verticalMargin = margin.vertical || 0;\n\n    var left = 0;\n    var top = 0;\n\n    var isVerticalFit = collisions.vertical === collision.fit;\n    var isHorizontalFit = collisions.horizontal === collision.fit;\n    var isVerticalFlip = collisions.vertical === collision.flip;\n    var isHorizontalFlip = collisions.horizontal === collision.flip;\n\n    if (isVerticalFit) {\n        top += fit(elementTop, elementHeight, viewPortHeight);\n    }\n\n    if (isHorizontalFit) {\n        left += fit(elementLeft, elementWidth, viewPortWidth);\n    }\n\n    if (isVerticalFlip) {\n        top += flip({\n            margin: verticalMargin,\n            offset: elementTop,\n            size: elementHeight,\n            anchorSize: anchorRect.height,\n            viewPortSize: viewPortHeight,\n            anchorAlignPoint: anchorAlign.vertical,\n            elementAlignPoint: elementAlign.vertical\n        });\n    }\n\n    if (isHorizontalFlip) {\n        left += flip({\n            margin: horizontalMargin,\n            offset: elementLeft,\n            size: elementWidth,\n            anchorSize: anchorRect.width,\n            viewPortSize: viewPortWidth,\n            anchorAlignPoint: anchorAlign.horizontal,\n            elementAlignPoint: elementAlign.horizontal\n        });\n    }\n\n    var flippedVertical = isVerticalFlip && top !== 0;\n    var flippedHorizontal = isHorizontalFlip && left !== 0;\n    var fittedVertical = isVerticalFit && top !== 0;\n    var fittedHorizontal = isHorizontalFit && left !== 0;\n\n    return {\n        flipped: flippedHorizontal || flippedVertical,\n        fitted: fittedVertical || fittedHorizontal,\n        flip: {\n            horizontal: flippedHorizontal,\n            vertical: flippedVertical\n        },\n        fit: {\n            horizontal: fittedHorizontal,\n            vertical: fittedVertical\n        },\n        offset: {\n            left: left,\n            top: top\n        }\n    };\n};\n\nexport default restrictToView;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,eAAe;AACtC,OAAOC,SAAS,MAAM,aAAa;AAEnC,IAAIC,GAAG,GAAG,SAAAA,CAASC,QAAQ,EAAEC,IAAI,EAAEC,YAAY,EAAE;EAC7C,IAAIC,MAAM,GAAG,CAAC;EAEd,IAAIH,QAAQ,GAAGC,IAAI,GAAGC,YAAY,EAAE;IAChCC,MAAM,GAAGD,YAAY,IAAIF,QAAQ,GAAGC,IAAI,CAAC;EAC7C;EAEA,IAAID,QAAQ,GAAG,CAAC,EAAE;IACdG,MAAM,GAAG,CAACH,QAAQ;EACtB;EAEA,OAAOG,MAAM;AACjB,CAAC;AAED,IAAIC,IAAI,GAAG,SAAAA,CAASC,GAAG,EAAE;EACrB,IAAIC,MAAM,GAAGD,GAAG,CAACC,MAAM;EACvB,IAAIL,IAAI,GAAGI,GAAG,CAACJ,IAAI;EACnB,IAAIM,UAAU,GAAGF,GAAG,CAACE,UAAU;EAC/B,IAAIL,YAAY,GAAGG,GAAG,CAACH,YAAY;EACnC,IAAIM,gBAAgB,GAAGH,GAAG,CAACG,gBAAgB;EAC3C,IAAIC,iBAAiB,GAAGJ,GAAG,CAACI,iBAAiB;EAC7C,IAAIC,MAAM,GAAGL,GAAG,CAACK,MAAM;EAEvB,IAAIP,MAAM,GAAG,CAAC;EAEd,IAAIQ,kBAAkB,GAAGF,iBAAiB,KAAKZ,UAAU,CAACe,MAAM,IAAIH,iBAAiB,KAAKZ,UAAU,CAACgB,MAAM;EAC3G,IAAIC,gBAAgB,GAAGN,gBAAgB,KAAKX,UAAU,CAACe,MAAM,IAAIJ,gBAAgB,KAAKX,UAAU,CAACgB,MAAM;EACvG,IAAIE,WAAW,GAAG,CAAC,GAAGL,MAAM,CAAC,CAAC;;EAE9B,IAAID,iBAAiB,KAAKD,gBAAgB,IAAI,CAACG,kBAAkB,IAAI,CAACG,gBAAgB,EAAE;IACpF,IAAIE,cAAc,GAAGR,gBAAgB,KAAKX,UAAU,CAACoB,GAAG,IAAIT,gBAAgB,KAAKX,UAAU,CAACqB,IAAI;IAChG,IAAIZ,MAAM,GAAG,CAAC,IAAIU,cAAc,EAAE;MAC9Bb,MAAM,GAAGF,IAAI,GAAGM,UAAU,GAAGQ,WAAW;MACxC,IAAIT,MAAM,GAAGH,MAAM,GAAGF,IAAI,GAAGC,YAAY,EAAE;QACvCC,MAAM,GAAG,CAAC,CAAC,CAAC;MAChB;IACJ,CAAC,MAAM,IAAIG,MAAM,IAAI,CAAC,IAAI,CAACU,cAAc,EAAE;MACvC,IAAIV,MAAM,GAAGL,IAAI,GAAGC,YAAY,EAAE;QAC9BC,MAAM,IAAI,EAAEI,UAAU,GAAGN,IAAI,GAAGc,WAAW,CAAC;MAChD;MAEA,IAAIT,MAAM,GAAGH,MAAM,GAAG,CAAC,EAAE;QACrBA,MAAM,GAAG,CAAC,CAAC,CAAC;MAChB;IACJ;EACJ;EAEA,OAAOA,MAAM;AACjB,CAAC;AAED,IAAIgB,cAAc,GAAG,SAAAA,CAAUC,OAAO,EAAE;EACpC,IAAIC,UAAU,GAAGD,OAAO,CAACC,UAAU;EACnC,IAAIC,WAAW,GAAGF,OAAO,CAACE,WAAW;EACrC,IAAIC,WAAW,GAAGH,OAAO,CAACG,WAAW;EACrC,IAAIC,YAAY,GAAGJ,OAAO,CAACI,YAAY;EACvC,IAAIC,UAAU,GAAGL,OAAO,CAACK,UAAU;EACnC,IAAIC,QAAQ,GAAGN,OAAO,CAACM,QAAQ;EAC/B,IAAIhB,MAAM,GAAGU,OAAO,CAACV,MAAM;EAAE,IAAKA,MAAM,KAAK,KAAK,CAAC,EAAGA,MAAM,GAAG,CAAC,CAAC;EACjE,IAAIiB,UAAU,GAAGJ,WAAW,CAACN,GAAG;EAChC,IAAIW,WAAW,GAAGL,WAAW,CAACL,IAAI;EAClC,IAAIW,aAAa,GAAGN,WAAW,CAACO,MAAM;EACtC,IAAIC,YAAY,GAAGR,WAAW,CAACS,KAAK;EACpC,IAAIC,cAAc,GAAGP,QAAQ,CAACI,MAAM;EACpC,IAAII,aAAa,GAAGR,QAAQ,CAACM,KAAK;EAClC,IAAIG,gBAAgB,GAAGzB,MAAM,CAAC0B,UAAU,IAAI,CAAC;EAC7C,IAAIC,cAAc,GAAG3B,MAAM,CAAC4B,QAAQ,IAAI,CAAC;EAEzC,IAAIpB,IAAI,GAAG,CAAC;EACZ,IAAID,GAAG,GAAG,CAAC;EAEX,IAAIsB,aAAa,GAAGd,UAAU,CAACa,QAAQ,KAAKxC,SAAS,CAACC,GAAG;EACzD,IAAIyC,eAAe,GAAGf,UAAU,CAACW,UAAU,KAAKtC,SAAS,CAACC,GAAG;EAC7D,IAAI0C,cAAc,GAAGhB,UAAU,CAACa,QAAQ,KAAKxC,SAAS,CAACM,IAAI;EAC3D,IAAIsC,gBAAgB,GAAGjB,UAAU,CAACW,UAAU,KAAKtC,SAAS,CAACM,IAAI;EAE/D,IAAImC,aAAa,EAAE;IACftB,GAAG,IAAIlB,GAAG,CAAC4B,UAAU,EAAEE,aAAa,EAAEI,cAAc,CAAC;EACzD;EAEA,IAAIO,eAAe,EAAE;IACjBtB,IAAI,IAAInB,GAAG,CAAC6B,WAAW,EAAEG,YAAY,EAAEG,aAAa,CAAC;EACzD;EAEA,IAAIO,cAAc,EAAE;IAChBxB,GAAG,IAAIb,IAAI,CAAC;MACRM,MAAM,EAAE2B,cAAc;MACtB/B,MAAM,EAAEqB,UAAU;MAClB1B,IAAI,EAAE4B,aAAa;MACnBtB,UAAU,EAAEc,UAAU,CAACS,MAAM;MAC7B5B,YAAY,EAAE+B,cAAc;MAC5BzB,gBAAgB,EAAEc,WAAW,CAACgB,QAAQ;MACtC7B,iBAAiB,EAAEe,YAAY,CAACc;IACpC,CAAC,CAAC;EACN;EAEA,IAAII,gBAAgB,EAAE;IAClBxB,IAAI,IAAId,IAAI,CAAC;MACTM,MAAM,EAAEyB,gBAAgB;MACxB7B,MAAM,EAAEsB,WAAW;MACnB3B,IAAI,EAAE8B,YAAY;MAClBxB,UAAU,EAAEc,UAAU,CAACW,KAAK;MAC5B9B,YAAY,EAAEgC,aAAa;MAC3B1B,gBAAgB,EAAEc,WAAW,CAACc,UAAU;MACxC3B,iBAAiB,EAAEe,YAAY,CAACY;IACpC,CAAC,CAAC;EACN;EAEA,IAAIO,eAAe,GAAGF,cAAc,IAAIxB,GAAG,KAAK,CAAC;EACjD,IAAI2B,iBAAiB,GAAGF,gBAAgB,IAAIxB,IAAI,KAAK,CAAC;EACtD,IAAI2B,cAAc,GAAGN,aAAa,IAAItB,GAAG,KAAK,CAAC;EAC/C,IAAI6B,gBAAgB,GAAGN,eAAe,IAAItB,IAAI,KAAK,CAAC;EAEpD,OAAO;IACH6B,OAAO,EAAEH,iBAAiB,IAAID,eAAe;IAC7CK,MAAM,EAAEH,cAAc,IAAIC,gBAAgB;IAC1C1C,IAAI,EAAE;MACFgC,UAAU,EAAEQ,iBAAiB;MAC7BN,QAAQ,EAAEK;IACd,CAAC;IACD5C,GAAG,EAAE;MACDqC,UAAU,EAAEU,gBAAgB;MAC5BR,QAAQ,EAAEO;IACd,CAAC;IACDvC,MAAM,EAAE;MACJY,IAAI,EAAEA,IAAI;MACVD,GAAG,EAAEA;IACT;EACJ,CAAC;AACL,CAAC;AAED,eAAeE,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}