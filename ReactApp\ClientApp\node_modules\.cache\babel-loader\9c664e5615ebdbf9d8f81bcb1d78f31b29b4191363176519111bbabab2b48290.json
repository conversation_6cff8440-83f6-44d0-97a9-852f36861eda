{"ast": null, "code": "var _a;\nimport { __assign, __extends } from \"tslib\";\nimport { DateObject } from '../common/dateobject';\nimport { approximateStringMatching } from './utils';\nimport { KeyCode } from '../common/keycode';\nimport { Key } from '../common/key';\nimport { extend, isPresent, isDocumentAvailable, millisecondDigitsInFormat, millisecondStepFor, isValidDate, isIOS } from '../common/utils';\nimport { Observable } from '../common/observable';\nimport { DateInputInteractionMode } from './interaction-mode';\nimport { isEqual, cloneDate } from '@progress/kendo-date-math';\nimport { Constants } from '../common/constants';\nvar DEFAULT_SEGMENT_STEP = 1;\nvar DRAG_START = \"dragStart\";\nvar DROP = \"drop\";\nvar TOUCH_START = \"touchstart\";\nvar MOUSE_DOWN = \"mousedown\";\nvar MOUSE_UP = \"mouseup\";\nvar CLICK = \"click\";\nvar INPUT = \"input\";\nvar KEY_DOWN = \"keydown\";\nvar FOCUS = \"focus\";\nvar BLUR = \"blur\";\nvar PASTE = \"paste\";\nvar MOUSE_SCROLL = \"DOMMouseScroll\";\nvar MOUSE_WHEEL = \"mousewheel\";\nvar VALUE_CHANGE = \"valueChange\";\nvar INPUT_END = \"inputEnd\";\nvar BLUR_END = \"blurEnd\";\nvar FOCUS_END = \"focusEnd\";\nvar CHANGE = \"change\";\nvar defaultDateInputOptions = {\n  format: \"d\",\n  hasPlaceholder: false,\n  placeholder: null,\n  cycleTime: true,\n  locale: null,\n  steps: {\n    millisecond: DEFAULT_SEGMENT_STEP,\n    second: DEFAULT_SEGMENT_STEP,\n    minute: DEFAULT_SEGMENT_STEP,\n    hour: DEFAULT_SEGMENT_STEP,\n    day: DEFAULT_SEGMENT_STEP,\n    month: DEFAULT_SEGMENT_STEP,\n    year: DEFAULT_SEGMENT_STEP\n  },\n  formatPlaceholder: null,\n  events: (_a = {}, _a[VALUE_CHANGE] = null, _a[INPUT] = null, _a[INPUT_END] = null, _a[FOCUS] = null, _a[FOCUS_END] = null, _a[BLUR] = null, _a[BLUR_END] = null, _a[KEY_DOWN] = null, _a[MOUSE_WHEEL] = null, _a[CHANGE] = null, _a),\n  selectNearestSegmentOnFocus: false,\n  selectPreviousSegmentOnBackspace: false,\n  enableMouseWheel: false,\n  allowCaretMode: false,\n  autoSwitchParts: true,\n  autoSwitchKeys: [],\n  twoDigitYearMax: Constants.twoDigitYearMax,\n  autoCorrectParts: true,\n  autoFill: false,\n  toggleDayPeriod: false\n};\nvar DateInput = /** @class */function (_super) {\n  __extends(DateInput, _super);\n  function DateInput(element, options) {\n    var _this = _super.call(this, options) || this;\n    _this.dateObject = null;\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore\n    _this.currentText = '';\n    _this.currentFormat = '';\n    _this.interactionMode = DateInputInteractionMode.None;\n    _this.previousElementSelection = {\n      start: 0,\n      end: 0\n    };\n    _this.init(element, options);\n    return _this;\n  }\n  Object.defineProperty(DateInput.prototype, \"value\", {\n    get: function () {\n      return this.dateObject && this.dateObject.getValue();\n    },\n    enumerable: false,\n    configurable: true\n  });\n  DateInput.prototype.init = function (element, options) {\n    var dateValue = isValidDate(this.options.value) ? cloneDate(this.options.value) : new Date(options.formattedValue);\n    if (!isValidDate(dateValue)) {\n      dateValue = null;\n    }\n    this.element = element;\n    // this.element._kendoWidget = this;\n    this.options = extend({}, defaultDateInputOptions, options, {\n      steps: __assign(__assign({}, defaultDateInputOptions.steps), options.steps)\n    });\n    this.intl = this.options.intlService;\n    this.dateObject = this.createDateObject();\n    this.dateObject.setValue(dateValue);\n    this.setTextAndFormat();\n    this.bindEvents();\n    this.resetSegmentValue = true;\n    this.interactionMode = DateInputInteractionMode.None;\n    this.forceUpdate();\n  };\n  DateInput.prototype.destroy = function () {\n    this.unbindEvents();\n    this.dateObject = null;\n    _super.prototype.destroy.call(this);\n  };\n  DateInput.prototype.bindEvents = function () {\n    this.onElementDragStart = this.onElementDragStart.bind(this);\n    this.element.addEventListener(DRAG_START, this.onElementDragStart);\n    this.onElementDrop = this.onElementDrop.bind(this);\n    this.element.addEventListener(DROP, this.onElementDrop);\n    this.onElementClick = this.onElementClick.bind(this);\n    this.element.addEventListener(CLICK, this.onElementClick);\n    this.onElementMouseDown = this.onElementMouseDown.bind(this);\n    this.element.addEventListener(MOUSE_DOWN, this.onElementMouseDown);\n    this.element.addEventListener(TOUCH_START, this.onElementMouseDown);\n    this.onElementMouseUp = this.onElementMouseUp.bind(this);\n    this.element.addEventListener(MOUSE_UP, this.onElementMouseUp);\n    this.onElementInput = this.onElementInput.bind(this);\n    this.element.addEventListener(INPUT, this.onElementInput);\n    this.onElementKeyDown = this.onElementKeyDown.bind(this);\n    this.element.addEventListener(KEY_DOWN, this.onElementKeyDown);\n    this.onElementFocus = this.onElementFocus.bind(this);\n    this.element.addEventListener(FOCUS, this.onElementFocus);\n    this.onElementBlur = this.onElementBlur.bind(this);\n    this.element.addEventListener(BLUR, this.onElementBlur);\n    this.onElementChange = this.onElementChange.bind(this);\n    this.element.addEventListener(CHANGE, this.onElementChange);\n    this.onElementPaste = this.onElementPaste.bind(this);\n    this.element.addEventListener(PASTE, this.onElementPaste);\n    this.onElementMouseWheel = this.onElementMouseWheel.bind(this);\n    this.element.addEventListener(MOUSE_SCROLL, this.onElementMouseWheel);\n    this.element.addEventListener(MOUSE_WHEEL, this.onElementMouseWheel);\n  };\n  DateInput.prototype.unbindEvents = function () {\n    this.element.removeEventListener(DRAG_START, this.onElementDragStart);\n    this.element.removeEventListener(DROP, this.onElementDrop);\n    this.element.removeEventListener(TOUCH_START, this.onElementMouseDown);\n    this.element.removeEventListener(MOUSE_DOWN, this.onElementMouseDown);\n    this.element.removeEventListener(MOUSE_UP, this.onElementMouseUp);\n    this.element.removeEventListener(CLICK, this.onElementClick);\n    this.element.removeEventListener(INPUT, this.onElementInput);\n    this.element.removeEventListener(KEY_DOWN, this.onElementKeyDown);\n    this.element.removeEventListener(FOCUS, this.onElementFocus);\n    this.element.removeEventListener(BLUR, this.onElementBlur);\n    this.element.removeEventListener(CHANGE, this.onElementChange);\n    this.element.removeEventListener(PASTE, this.onElementPaste);\n    this.element.removeEventListener(MOUSE_SCROLL, this.onElementMouseWheel);\n    this.element.removeEventListener(MOUSE_WHEEL, this.onElementMouseWheel);\n  };\n  DateInput.prototype.setOptions = function (options, refresh) {\n    if (refresh === void 0) {\n      refresh = false;\n    }\n    this.options = extend({}, this.options, options, {\n      steps: __assign(__assign({}, defaultDateInputOptions.steps), options.steps)\n    });\n    this.setDateObjectOptions();\n    if (refresh) {\n      this.unbindEvents();\n      this.init(this.element, this.options);\n    }\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.setDateObjectOptions = function () {\n    if (this.dateObject) {\n      var newOptions = this.getDateObjectOptions();\n      this.dateObject.setOptions(newOptions);\n    }\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.resetLocale = function () {\n    this.unbindEvents();\n    this.init(this.element, this.options);\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.isInCaretMode = function () {\n    return this.interactionMode === DateInputInteractionMode.Caret;\n  };\n  DateInput.prototype.focus = function () {\n    this.element.focus();\n    if (this.options.selectNearestSegmentOnFocus) {\n      this.selectNearestSegment(0);\n    }\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.onElementDragStart = function (e) {\n    e.preventDefault();\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.onElementDrop = function (e) {\n    e.preventDefault();\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.onElementMouseDown = function () {\n    this.mouseDownStarted = true;\n    this.focusedPriorToMouseDown = this.isActive;\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.onElementMouseUp = function (e) {\n    this.mouseDownStarted = false;\n    e.preventDefault();\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.onElementClick = function (e) {\n    this.mouseDownStarted = false;\n    this.switchedPartOnPreviousKeyAction = false;\n    var selection = this.selection;\n    if (this.isInCaretMode()) {\n      // explicitly refresh the input element value\n      // caret mode can change the number of symbols in the element\n      // thus clicking on a segment can result in incorrect selection\n      this.forceUpdate();\n    }\n    if (e.detail === 3) {\n      // when 3 clicks occur, leave the native event to handle the change\n      // this results in selecting the whole element value\n    } else {\n      if (this.isActive && this.options.selectNearestSegmentOnFocus) {\n        var selectionPresent = this.element.selectionStart !== this.element.selectionEnd;\n        var placeholderToggled = isPresent(this.options.placeholder) && !this.dateObject.hasValue() && !this.focusedPriorToMouseDown;\n        // focus first segment if the user hasn't selected something during mousedown and if the placeholder was just toggled\n        var selectFirstSegment = !selectionPresent && placeholderToggled;\n        var index = selectFirstSegment ? 0 : this.caret()[0];\n        this.selectNearestSegment(index);\n      } else {\n        this.setSelection(this.selectionByIndex(selection.start));\n      }\n    }\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.onElementInput = function (e) {\n    this.triggerInput({\n      event: e\n    });\n    var oldElementValue = this.elementValue;\n    if (!this.element || !this.dateObject) {\n      return;\n    }\n    var switchedPartOnPreviousKeyAction = this.switchedPartOnPreviousKeyAction;\n    if (this.isPasteInProgress) {\n      if (this.options.allowCaretMode) {\n        // pasting should leave the input with caret\n        // thus allow direct input instead of selection mode\n        this.resetSegmentValue = false;\n      }\n      this.updateOnPaste(e);\n      this.isPasteInProgress = false;\n      return;\n    }\n    var keyDownEvent = this.keyDownEvent || {};\n    var isBackspaceKey = keyDownEvent.keyCode === KeyCode.BACKSPACE || keyDownEvent.key === Key.BACKSPACE;\n    var isDeleteKey = keyDownEvent.keyCode === KeyCode.DELETE || keyDownEvent.key === Key.DELETE;\n    var originalInteractionMode = this.interactionMode;\n    if (this.options.allowCaretMode && originalInteractionMode !== DateInputInteractionMode.Caret && !isDeleteKey && !isBackspaceKey) {\n      this.resetSegmentValue = true;\n    }\n    if (this.options.allowCaretMode) {\n      this.interactionMode = DateInputInteractionMode.Caret;\n    } else {\n      this.interactionMode = DateInputInteractionMode.Selection;\n    }\n    var hasCaret = this.isInCaretMode();\n    if (hasCaret && this.keyDownEvent.key === Key.SPACE) {\n      // do not allow custom \"holes\" in the date segments\n      this.restorePreviousInputEventState();\n      return;\n    }\n    var oldExistingDateValue = this.dateObject && this.dateObject.getValue();\n    var oldDateValue = this.dateObject ? this.dateObject.value : null;\n    var _a = this.dateObject.getTextAndFormat(),\n      currentText = _a.text,\n      currentFormat = _a.format;\n    this.currentFormat = currentFormat;\n    var oldText = \"\";\n    if (hasCaret) {\n      if (isBackspaceKey || isDeleteKey) {\n        oldText = this.previousElementValue;\n      } else if (originalInteractionMode === DateInputInteractionMode.Caret) {\n        oldText = this.previousElementValue;\n      } else {\n        oldText = currentText;\n      }\n    } else {\n      oldText = currentText;\n    }\n    var newText = this.elementValue;\n    var diff = approximateStringMatching({\n      oldText: oldText,\n      newText: newText,\n      formatPattern: this.currentFormat,\n      selectionStart: this.selection.start,\n      isInCaretMode: hasCaret,\n      keyEvent: this.keyDownEvent\n    });\n    if (diff && diff.length && diff[0] && diff[0][1] !== Constants.formatSeparator) {\n      this.switchedPartOnPreviousKeyAction = false;\n    }\n    if (hasCaret && (!diff || diff.length === 0)) {\n      this.restorePreviousInputEventState();\n      return;\n    } else if (hasCaret && diff.length === 1) {\n      if (!diff[0] || !diff[0][0]) {\n        this.restorePreviousInputEventState();\n        return;\n      } else if (hasCaret && diff[0] && (diff[0][0] === Constants.formatSeparator || diff[0][1] === Constants.formatSeparator)) {\n        this.restorePreviousInputEventState();\n        return;\n      }\n    }\n    var navigationOnly = diff.length === 1 && diff[0][1] === Constants.formatSeparator;\n    var parsePartsResults = [];\n    var switchPart = false;\n    var error = null;\n    if (!navigationOnly) {\n      for (var i = 0; i < diff.length; i++) {\n        var parsePartResult = this.dateObject.parsePart({\n          symbol: diff[i][0],\n          currentChar: diff[i][1],\n          resetSegmentValue: this.resetSegmentValue,\n          cycleSegmentValue: !this.isInCaretMode(),\n          rawTextValue: this.element.value,\n          isDeleting: isBackspaceKey || isDeleteKey,\n          originalFormat: this.currentFormat\n        });\n        parsePartsResults.push(parsePartResult);\n        if (!parsePartResult.value) {\n          error = {\n            type: \"parse\"\n          };\n        }\n        switchPart = parsePartResult.switchToNext;\n      }\n    }\n    if (!this.options.autoSwitchParts) {\n      switchPart = false;\n    }\n    this.resetSegmentValue = false;\n    var hasFixedFormat = this.options.format === this.currentFormat ||\n    // all not fixed formats are 1 symbol, e.g. \"d\"\n    isPresent(this.options.format) && this.options.format.length > 1;\n    var lastParseResult = parsePartsResults[parsePartsResults.length - 1];\n    var lastParseResultHasNoValue = lastParseResult && !isPresent(lastParseResult.value);\n    var parsingFailedOnDelete = hasCaret && (isBackspaceKey || isDeleteKey) && lastParseResultHasNoValue;\n    var resetPart = lastParseResult ? lastParseResult.resetPart : false;\n    var newExistingDateValue = this.dateObject.getValue();\n    var hasExistingDateValueChanged = !isEqual(oldExistingDateValue, newExistingDateValue);\n    var newDateValue = this.dateObject.value;\n    var symbolForSelection;\n    var currentSelection = this.selection;\n    if (hasCaret) {\n      var diffChar = diff && diff.length > 0 ? diff[0][0] : null;\n      var hasLeadingZero = this.dateObject.getLeadingZero()[diffChar];\n      if (diff.length && diff[0][0] !== Constants.formatSeparator) {\n        if (switchPart) {\n          this.forceUpdateWithSelection();\n          this.switchDateSegment(1);\n        } else if (resetPart) {\n          symbolForSelection = this.currentFormat[currentSelection.start];\n          if (symbolForSelection) {\n            this.forceUpdate();\n            this.setSelection(this.selectionBySymbol(symbolForSelection));\n          } else {\n            this.restorePreviousInputEventState();\n          }\n        } else if (parsingFailedOnDelete) {\n          this.forceUpdate();\n          if (diff.length && diff[0][0] !== Constants.formatSeparator) {\n            this.setSelection(this.selectionBySymbol(diff[0][0]));\n          }\n        } else if (lastParseResultHasNoValue) {\n          if (e.data === \"0\" && hasLeadingZero) {\n            // do not reset element value on a leading zero\n            // wait for consecutive input to determine the value\n          } else if (isPresent(oldExistingDateValue) && !isPresent(newExistingDateValue)) {\n            this.restorePreviousInputEventState();\n          } else if (!isPresent(oldExistingDateValue) && isPresent(newExistingDateValue)) {\n            this.forceUpdateWithSelection();\n          } else if (isPresent(oldExistingDateValue) && isPresent(newExistingDateValue)) {\n            if (hasExistingDateValueChanged) {\n              this.forceUpdateWithSelection();\n            } else {\n              this.restorePreviousInputEventState();\n            }\n          } else if (!isPresent(oldExistingDateValue) && !isPresent(newExistingDateValue)) {\n            this.forceUpdateWithSelection();\n          } else if (oldDateValue !== newDateValue) {\n            // this can happen on auto correct when no valid value is parsed\n          } else {\n            this.restorePreviousInputEventState();\n          }\n        } else if (!lastParseResultHasNoValue) {\n          // the user types a valid but incomplete date (e.g. year \"123\" with format \"yyyy\")\n          // let them continue typing, but refresh for not fixed formats\n          if (!hasFixedFormat) {\n            this.forceUpdateWithSelection();\n          }\n        }\n      } else {\n        if (!this.options.autoSwitchParts && diff[0][1] === Constants.formatSeparator) {\n          // do not change the selection when a separator is pressed\n          // this should happen only if autoSwitchKeys contains the separator explicitly\n        } else {\n          this.setSelection(this.selectionBySymbol(diff[0][0]));\n        }\n      }\n    } else if (!hasCaret) {\n      this.forceUpdate();\n      if (diff.length && diff[0][0] !== Constants.formatSeparator) {\n        this.setSelection(this.selectionBySymbol(diff[0][0]));\n      }\n      if (this.options.autoSwitchParts) {\n        if (navigationOnly) {\n          this.resetSegmentValue = true;\n          if (!switchedPartOnPreviousKeyAction) {\n            this.switchDateSegment(1);\n          }\n          this.switchedPartOnPreviousKeyAction = true;\n        } else if (switchPart) {\n          this.switchDateSegment(1);\n          this.switchedPartOnPreviousKeyAction = true;\n        }\n      } else {\n        if (lastParseResult && lastParseResult.switchToNext) {\n          // the value is complete and should be switched, but the \"autoSwitchParts\" option prevents this\n          // ensure that the segment value can be reset on next input\n          this.resetSegmentValue = true;\n        } else if (navigationOnly) {\n          this.resetSegmentValue = true;\n          if (!switchedPartOnPreviousKeyAction) {\n            this.switchDateSegment(1);\n          }\n          this.switchedPartOnPreviousKeyAction = true;\n        }\n      }\n      if (isBackspaceKey && this.options.selectPreviousSegmentOnBackspace) {\n        // kendo angular have this UX\n        this.switchDateSegment(-1);\n      }\n    }\n    this.tryTriggerValueChange({\n      oldValue: oldExistingDateValue,\n      event: e\n    });\n    this.triggerInputEnd({\n      event: e,\n      error: error,\n      oldElementValue: oldElementValue,\n      newElementValue: this.elementValue\n    });\n    if (hasCaret) {\n      // a format like \"F\" can dynamically change the resolved format pattern based on the value, e.g.\n      // \"Tuesday, February 1, 2022 3:04:05 AM\" becomes\n      // \"Wednesday, February 2, 2022 3:04:05 AM\" giving a diff of 2 (\"Tuesday\".length - \"Wednesday\".length)\n      this.setTextAndFormat();\n    }\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.onElementFocus = function (e) {\n    if (this.triggerFocus({\n      event: e\n    })) {\n      return;\n    }\n    this.isActive = true;\n    this.interactionMode = DateInputInteractionMode.None;\n    this.switchedPartOnPreviousKeyAction = false;\n    this.refreshElementValue();\n    if (!this.mouseDownStarted) {\n      this.caret(0, this.elementValue.length);\n    }\n    this.mouseDownStarted = false;\n    this.triggerFocusEnd({\n      event: e\n    });\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.onElementBlur = function (e) {\n    this.resetSegmentValue = true;\n    this.isActive = false;\n    if (this.triggerBlur({\n      event: e\n    })) {\n      return;\n    }\n    if (this.options.autoFill) {\n      this.autoFill();\n    }\n    this.interactionMode = DateInputInteractionMode.None;\n    this.switchedPartOnPreviousKeyAction = false;\n    this.refreshElementValue();\n    this.triggerBlurEnd({\n      event: e\n    });\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.onElementChange = function (e) {\n    this.triggerChange({\n      event: e\n    });\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.onElementKeyDown = function (e) {\n    if (this.triggerKeyDown({\n      event: e\n    })) {\n      return;\n    }\n    var _a = this.selection,\n      start = _a.start,\n      end = _a.end;\n    var event = e;\n    this.keyDownEvent = e;\n    this.previousElementValue = this.element.value;\n    this.previousElementSelection = {\n      start: start,\n      end: end\n    };\n    if (this.keyEventMatchesAutoSwitchKeys(e)) {\n      var isTabKey = e.keyCode === KeyCode.TAB;\n      if (isTabKey) {\n        var _b = this.selection,\n          selectionStart = _b.start,\n          selectionEnd = _b.end;\n        if (e.shiftKey && isTabKey) {\n          this.switchDateSegment(-1);\n        } else {\n          this.switchDateSegment(1);\n        }\n        if (selectionStart !== this.selection.start || selectionEnd !== this.selection.end) {\n          // when the selection changes, prevent the default Tab behavior\n          e.preventDefault();\n          return;\n        }\n      } else {\n        // do not allow the \"input\" event to be triggered\n        e.preventDefault();\n        this.switchDateSegment(1);\n        return;\n      }\n    }\n    var symbol = this.currentFormat[this.selection.start];\n    var step = this.getStepFromSymbol(symbol);\n    var shouldPreventDefault = false;\n    var oldElementValue = this.elementValue;\n    if (e.altKey || e.ctrlKey || e.metaKey || e.keyCode === KeyCode.TAB) {\n      return;\n    }\n    switch (e.keyCode) {\n      case KeyCode.ARROW_LEFT:\n        this.switchDateSegment(-1);\n        shouldPreventDefault = true;\n        this.switchedPartOnPreviousKeyAction = false;\n        break;\n      case KeyCode.ARROW_UP:\n        this.modifyDateSegmentValue(step, symbol, event);\n        if (oldElementValue !== this.elementValue) {\n          this.triggerInputEnd({\n            event: e,\n            error: null,\n            newElementValue: this.elementValue,\n            oldElementValue: oldElementValue\n          });\n        }\n        shouldPreventDefault = true;\n        this.switchedPartOnPreviousKeyAction = false;\n        break;\n      case KeyCode.ARROW_RIGHT:\n        this.switchDateSegment(1);\n        shouldPreventDefault = true;\n        this.switchedPartOnPreviousKeyAction = false;\n        break;\n      case KeyCode.ARROW_DOWN:\n        this.modifyDateSegmentValue(-step, symbol, event);\n        if (oldElementValue !== this.elementValue) {\n          this.triggerInputEnd({\n            event: e,\n            error: null,\n            newElementValue: this.elementValue,\n            oldElementValue: oldElementValue\n          });\n        }\n        shouldPreventDefault = true;\n        this.switchedPartOnPreviousKeyAction = false;\n        break;\n      case KeyCode.ENTER:\n        // todo: handle \"change\" event\n        break;\n      case KeyCode.HOME:\n        this.selectNearestSegment(0);\n        shouldPreventDefault = true;\n        this.switchedPartOnPreviousKeyAction = false;\n        this.resetSegmentValue = true;\n        break;\n      case KeyCode.END:\n        this.selectNearestSegment(this.elementValue.length);\n        shouldPreventDefault = true;\n        this.switchedPartOnPreviousKeyAction = false;\n        this.resetSegmentValue = true;\n        break;\n      default:\n        // allow the \"input\" event to handle the change\n        return;\n    }\n    if (shouldPreventDefault) {\n      e.preventDefault();\n    }\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.onElementPaste = function () {\n    this.isPasteInProgress = true;\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.onElementMouseWheel = function (e) {\n    var oldElementValue = this.elementValue;\n    if (!this.options.enableMouseWheel || this.triggerMouseWheel({\n      event: e\n    })) {\n      return;\n    }\n    if (!this.isActive) {\n      return;\n    }\n    var event = e;\n    if (event.shiftKey) {\n      this.switchDateSegment((event.wheelDelta || -event.detail) > 0 ? -1 : 1);\n    } else {\n      this.modifyDateSegmentValue((event.wheelDelta || -event.detail) > 0 ? 1 : -1);\n    }\n    event.returnValue = false;\n    if (event.preventDefault) {\n      event.preventDefault();\n    }\n    if (oldElementValue !== this.elementValue) {\n      this.triggerInputEnd({\n        event: e,\n        error: null,\n        newElementValue: this.elementValue,\n        oldElementValue: oldElementValue\n      });\n    }\n  };\n  DateInput.prototype.updateOnPaste = function (e) {\n    var value = this.intl.parseDate(this.elementValue, this.inputFormat) || this.value;\n    if (isPresent(value) && this.dateObject.shouldNormalizeCentury()) {\n      value = this.dateObject.normalizeCentury(value);\n    }\n    var oldDateObjectValue = this.dateObject && this.dateObject.getValue();\n    this.writeValue(value);\n    this.tryTriggerValueChange({\n      oldValue: oldDateObjectValue,\n      event: e\n    });\n  };\n  Object.defineProperty(DateInput.prototype, \"elementValue\", {\n    get: function () {\n      return (this.element || {}).value || '';\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(DateInput.prototype, \"inputFormat\", {\n    get: function () {\n      if (!this.options.format) {\n        return Constants.defaultDateFormat;\n      }\n      if (typeof this.options.format === 'string') {\n        return this.options.format;\n      } else {\n        return this.options.format.inputFormat;\n      }\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(DateInput.prototype, \"displayFormat\", {\n    get: function () {\n      if (!this.options.format) {\n        return Constants.defaultDateFormat;\n      }\n      if (typeof this.options.format === 'string') {\n        return this.options.format;\n      } else {\n        return this.options.format.displayFormat;\n      }\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(DateInput.prototype, \"selection\", {\n    get: function () {\n      var returnValue = {\n        start: 0,\n        end: 0\n      };\n      if (this.element !== null && this.element.selectionStart !== undefined) {\n        returnValue = {\n          start: this.element.selectionStart,\n          end: this.element.selectionEnd\n        };\n      }\n      return returnValue;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  DateInput.prototype.setSelection = function (selection) {\n    if (this.element && document.activeElement === this.element) {\n      this.element.setSelectionRange(selection.start, selection.end);\n      if (isDocumentAvailable() && isIOS()) {\n        this.element.scrollIntoView({\n          block: 'nearest',\n          inline: 'nearest'\n        });\n      }\n      if (selection.start !== selection.end) {\n        this.interactionMode = DateInputInteractionMode.Selection;\n      }\n    }\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.selectionBySymbol = function (symbol) {\n    var start = -1;\n    var end = 0;\n    for (var i = 0; i < this.currentFormat.length; i++) {\n      if (this.currentFormat[i] === symbol) {\n        end = i + 1;\n        if (start === -1) {\n          start = i;\n        }\n      }\n    }\n    if (start < 0) {\n      start = 0;\n    }\n    if (!this.options.autoCorrectParts && this.currentFormat.length !== this.currentText.length) {\n      if (this.currentFormat.length < this.currentText.length) {\n        end += this.currentText.length - this.currentFormat.length;\n      } else {\n        end = Math.max(0, end - (this.currentFormat.length - this.currentText.length));\n      }\n    }\n    return {\n      start: start,\n      end: end\n    };\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.selectionByIndex = function (index) {\n    var selection = {\n      start: index,\n      end: index\n    };\n    for (var i = index, j = index - 1; i < this.currentFormat.length || j >= 0; i++, j--) {\n      if (i < this.currentFormat.length && this.currentFormat[i] !== Constants.formatSeparator) {\n        selection = this.selectionBySymbol(this.currentFormat[i]);\n        break;\n      }\n      if (j >= 0 && this.currentFormat[j] !== Constants.formatSeparator) {\n        selection = this.selectionBySymbol(this.currentFormat[j]);\n        break;\n      }\n    }\n    return selection;\n  };\n  DateInput.prototype.switchDateSegment = function (offset) {\n    var selection = this.selection;\n    if (this.isInCaretMode()) {\n      var start = selection.start;\n      var currentSymbol = this.currentFormat[start - 1];\n      var symbol = \"\";\n      var symbolCandidate = \"\";\n      if (offset < 0) {\n        for (var i = start + offset; i >= 0; i--) {\n          symbolCandidate = this.currentFormat[i];\n          if (symbolCandidate !== Constants.formatSeparator && symbolCandidate !== currentSymbol) {\n            start = i;\n            symbol = symbolCandidate;\n            break;\n          }\n        }\n      } else {\n        for (var i = start + offset; i < this.currentFormat.length; i++) {\n          symbolCandidate = this.currentFormat[i];\n          if (symbolCandidate !== Constants.formatSeparator && symbolCandidate !== currentSymbol) {\n            start = i;\n            symbol = symbolCandidate;\n            break;\n          }\n        }\n      }\n      if (symbol) {\n        this.forceUpdate();\n        this.setSelection(this.selectionBySymbol(symbol));\n        this.interactionMode = DateInputInteractionMode.Selection;\n        return;\n      }\n    }\n    this.interactionMode = DateInputInteractionMode.None;\n    var _a = this.selection,\n      selectionStart = _a.start,\n      selectionEnd = _a.end;\n    if (selectionStart < selectionEnd && this.currentFormat[selectionStart] !== this.currentFormat[selectionEnd - 1]) {\n      this.setSelection(this.selectionByIndex(offset > 0 ? selectionStart : selectionEnd - 1));\n      this.resetSegmentValue = true;\n      this.interactionMode = DateInputInteractionMode.None;\n      return;\n    }\n    var previousFormatSymbol = this.currentFormat[selectionStart];\n    var a = selectionStart + offset;\n    while (a > 0 && a < this.currentFormat.length) {\n      if (this.currentFormat[a] !== previousFormatSymbol && this.currentFormat[a] !== Constants.formatSeparator) {\n        break;\n      }\n      a += offset;\n    }\n    if (this.currentFormat[a] === Constants.formatSeparator) {\n      // no known symbol is found\n      return;\n    }\n    var b = a;\n    while (b >= 0 && b < this.currentFormat.length) {\n      if (this.currentFormat[b] !== this.currentFormat[a]) {\n        break;\n      }\n      b += offset;\n    }\n    if (a > b && (b + 1 !== selectionStart || a + 1 !== selectionEnd)) {\n      this.setSelection({\n        start: b + 1,\n        end: a + 1\n      });\n      this.resetSegmentValue = true;\n    } else if (a < b && (a !== selectionStart || b !== selectionEnd)) {\n      this.setSelection({\n        start: a,\n        end: b\n      });\n      this.resetSegmentValue = true;\n    }\n    this.interactionMode = DateInputInteractionMode.None;\n  };\n  DateInput.prototype.modifyDateSegmentValue = function (offset, symbol, event) {\n    if (symbol === void 0) {\n      symbol = \"\";\n    }\n    if (event === void 0) {\n      event = {};\n    }\n    if (!this.dateObject || this.options.readonly) {\n      return;\n    }\n    var oldValue = this.value;\n    var step = DEFAULT_SEGMENT_STEP;\n    var caret = this.caret();\n    symbol = symbol || this.currentFormat[caret[0]];\n    if (symbol === \"S\" && (!this.options.steps.millisecond || this.options.steps.millisecond === DEFAULT_SEGMENT_STEP)) {\n      var msDigits = millisecondDigitsInFormat(this.inputFormat);\n      step = millisecondStepFor(msDigits);\n    }\n    this.dateObject.modifyPart(symbol, step * offset);\n    this.tryTriggerValueChange({\n      oldValue: oldValue,\n      event: event\n    });\n    this.forceUpdate();\n    this.setSelection(this.selectionBySymbol(symbol));\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.tryTriggerValueChange = function (args) {\n    if (args === void 0) {\n      args = {\n        oldValue: null,\n        event: {}\n      };\n    }\n    if (!isEqual(this.value, args.oldValue)) {\n      return this.triggerValueChange(args);\n    }\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.triggerValueChange = function (args) {\n    if (args === void 0) {\n      args = {\n        oldValue: null,\n        event: {}\n      };\n    }\n    return this.trigger(VALUE_CHANGE, extend(args, {\n      value: this.value\n    }));\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.triggerInput = function (args) {\n    if (args === void 0) {\n      args = {\n        event: {}\n      };\n    }\n    return this.trigger(INPUT, extend(args, {\n      value: this.value\n    }));\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.triggerInputEnd = function (args) {\n    if (args === void 0) {\n      args = {\n        event: {},\n        error: null,\n        oldElementValue: '',\n        newElementValue: ''\n      };\n    }\n    return this.trigger(INPUT_END, extend(args, {\n      value: this.value\n    }));\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.triggerFocus = function (args) {\n    if (args === void 0) {\n      args = {\n        event: {}\n      };\n    }\n    return this.trigger(FOCUS, extend({}, args));\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.triggerFocusEnd = function (args) {\n    if (args === void 0) {\n      args = {\n        event: {}\n      };\n    }\n    return this.trigger(FOCUS_END, extend({}, args));\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.triggerBlur = function (args) {\n    if (args === void 0) {\n      args = {\n        event: {}\n      };\n    }\n    return this.trigger(BLUR, extend({}, args));\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.triggerBlurEnd = function (args) {\n    if (args === void 0) {\n      args = {\n        event: {}\n      };\n    }\n    return this.trigger(BLUR_END, extend({}, args));\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.triggerChange = function (args) {\n    if (args === void 0) {\n      args = {\n        event: {}\n      };\n    }\n    return this.trigger(CHANGE, extend(args, {\n      value: this.value\n    }));\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.triggerKeyDown = function (args) {\n    if (args === void 0) {\n      args = {\n        event: {}\n      };\n    }\n    return this.trigger(KEY_DOWN, extend({}, args));\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.triggerMouseWheel = function (args) {\n    if (args === void 0) {\n      args = {\n        event: {}\n      };\n    }\n    return this.trigger(MOUSE_WHEEL, extend({}, args));\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.forceUpdate = function () {\n    this.setTextAndFormat();\n    this.refreshElementValue();\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.forceUpdateWithSelection = function () {\n    var _a = this.selection,\n      start = _a.start,\n      end = _a.end;\n    var elementValueLength = this.elementValue.length;\n    this.forceUpdate();\n    var selectionOffset = this.elementValue.length - elementValueLength;\n    this.setSelection({\n      start: start + selectionOffset,\n      end: end + selectionOffset\n    });\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.setTextAndFormat = function () {\n    var _a = this.dateObject.getTextAndFormat(),\n      currentText = _a.text,\n      currentFormat = _a.format;\n    this.currentFormat = currentFormat;\n    this.currentText = currentText;\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.setElementValue = function (value) {\n    this.element.value = value;\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.getStepFromSymbol = function (symbol) {\n    /* eslint-disable no-fallthrough */\n    switch (symbol) {\n      case \"S\":\n        return Number(this.options.steps.millisecond);\n      case \"s\":\n        return Number(this.options.steps.second);\n      case \"m\":\n        return Number(this.options.steps.minute);\n      // represents hour as value from 01 through 12\n      case \"h\":\n      // represents hour as value from 01 through 23\n      case \"H\":\n        return Number(this.options.steps.hour);\n      case \"M\":\n        return Number(this.options.steps.month);\n      // there is no 'D' format specifier for day\n      case \"d\":\n      // used for formats such as \"EEEE, MMMM d, yyyy\",\n      // where \"EEEE\" stands for full name of the day e.g. Monday\n      case \"E\":\n        return Number(this.options.steps.day);\n      // there is no 'Y' format specifier for year\n      case \"y\":\n        return Number(this.options.steps.year);\n      default:\n        return DEFAULT_SEGMENT_STEP;\n    }\n    /* eslint-enable no-fallthrough */\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.restorePreviousInputEventState = function () {\n    this.restorePreviousElementValue();\n    this.restorePreviousElementSelection();\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.restorePreviousElementValue = function () {\n    this.setElementValue(this.previousElementValue || '');\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.restorePreviousElementSelection = function () {\n    var _a = this.previousElementSelection,\n      start = _a.start,\n      end = _a.end;\n    this.setSelection({\n      start: start || 0,\n      end: end || 0\n    });\n  };\n  DateInput.prototype.writeValue = function (value) {\n    this.verifyValue(value);\n    this.dateObject = this.getDateObject(value);\n    this.refreshElementValue();\n  };\n  DateInput.prototype.verifyValue = function (value) {\n    if (value && !isValidDate(value)) {\n      throw new Error(\"The 'value' should be a valid JavaScript Date instance.\");\n    }\n  };\n  DateInput.prototype.refreshElementValue = function () {\n    var element = this.element;\n    var format = this.isActive ? this.inputFormat : this.displayFormat;\n    var _a = this.dateObject.getTextAndFormat(format),\n      currentText = _a.text,\n      currentFormat = _a.format;\n    this.currentFormat = currentFormat;\n    this.currentText = currentText;\n    var hasPlaceholder = this.options.hasPlaceholder || isPresent(this.options.placeholder);\n    var showPlaceholder = !this.isActive && hasPlaceholder && !this.dateObject.hasValue();\n    if (hasPlaceholder && isPresent(this.options.placeholder)) {\n      element.placeholder = this.options.placeholder;\n    }\n    var newElementValue = showPlaceholder ? \"\" : currentText;\n    this.previousElementValue = this.elementValue;\n    this.setElementValue(newElementValue);\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.caret = function (start, end) {\n    if (end === void 0) {\n      end = start;\n    }\n    var isPosition = start !== undefined;\n    var returnValue = [start, start];\n    var element = this.element;\n    if (isPosition && (this.options.disabled || this.options.readonly)) {\n      return undefined;\n    }\n    try {\n      if (element.selectionStart !== undefined) {\n        if (isPosition) {\n          if (isDocumentAvailable() && document.activeElement !== element) {\n            element.focus();\n          }\n          element.setSelectionRange(start, end);\n        }\n        returnValue = [element.selectionStart, element.selectionEnd];\n      }\n    } catch (e) {\n      returnValue = [];\n    }\n    return returnValue;\n  };\n  DateInput.prototype.selectNearestSegment = function (index) {\n    // Finds the nearest (in both directions) known part.\n    for (var i = index, j = index - 1; i < this.currentFormat.length || j >= 0; i++, j--) {\n      if (i < this.currentFormat.length && this.currentFormat[i] !== \"_\") {\n        this.selectDateSegment(this.currentFormat[i]);\n        return;\n      }\n      if (j >= 0 && this.currentFormat[j] !== \"_\") {\n        this.selectDateSegment(this.currentFormat[j]);\n        return;\n      }\n    }\n  };\n  DateInput.prototype.selectDateSegment = function (symbol) {\n    var begin = -1;\n    var end = 0;\n    for (var i = 0; i < this.currentFormat.length; i++) {\n      if (this.currentFormat[i] === symbol) {\n        end = i + 1;\n        if (begin === -1) {\n          begin = i;\n        }\n      }\n    }\n    if (begin < 0) {\n      begin = 0;\n    }\n    this.caret(0, 0);\n    this.caret(begin, end);\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.getDateObject = function (value) {\n    var leadingZero = (this.dateObject || {} || null).leadingZero;\n    this.options.value = value;\n    var dateObject = this.createDateObject();\n    dateObject.setLeadingZero(this.isActive ? leadingZero : null);\n    return dateObject;\n  };\n  /* tslint:disable:align */\n  /**\n   * @hidden\n   */\n  DateInput.prototype.createDateObject = function () {\n    var defaultOptions = this.getDateObjectOptions();\n    var dateObject = new DateObject(extend({}, defaultOptions));\n    return dateObject;\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.getDateObjectOptions = function () {\n    var newOptions = {\n      intlService: this.options.intlService,\n      formatPlaceholder: this.options.formatPlaceholder ? this.options.formatPlaceholder : 'formatPattern',\n      format: this.inputFormat,\n      cycleTime: this.options.cycleTime,\n      twoDigitYearMax: this.options.twoDigitYearMax,\n      autoCorrectParts: this.options.autoCorrectParts,\n      value: this.options.value,\n      toggleDayPeriod: this.options.toggleDayPeriod,\n      autoSwitchParts: this.options.autoSwitchParts\n    };\n    return newOptions;\n  };\n  /* tslint:enable:align */\n  /**\n   * @hidden\n   */\n  DateInput.prototype.keyEventMatchesAutoSwitchKeys = function (keyObject) {\n    var autoSwitchKeys = (this.options.autoSwitchKeys || []).map(function (x) {\n      return x.toString().toLowerCase().trim();\n    });\n    if (autoSwitchKeys.indexOf(keyObject.keyCode.toString()) >= 0 || autoSwitchKeys.indexOf(keyObject.keyCode) >= 0 || autoSwitchKeys.indexOf(keyObject.key.toLowerCase().trim()) >= 0) {\n      return true;\n    }\n    return false;\n  };\n  /**\n   * @hidden\n   */\n  DateInput.prototype.autoFill = function () {\n    var dateObject = this.dateObject;\n    var currentDate = new Date();\n    var day, month, year, hours, minutes, seconds;\n    if (dateObject.date || dateObject.month || dateObject.year || dateObject.hours || dateObject.minutes || dateObject.seconds) {\n      year = dateObject.year ? dateObject.value.getFullYear() : currentDate.getFullYear(), month = dateObject.month ? dateObject.value.getMonth() : currentDate.getMonth(), day = dateObject.date ? dateObject.value.getDate() : currentDate.getDate(), hours = dateObject.hours ? dateObject.value.getHours() : currentDate.getHours(), minutes = dateObject.minutes ? dateObject.value.getMinutes() : currentDate.getMinutes(), seconds = dateObject.seconds ? dateObject.value.getSeconds() : currentDate.getSeconds();\n      dateObject.setValue(new Date(year, month, day, hours, minutes, seconds));\n      this.refreshElementValue();\n      this.triggerValueChange();\n    }\n  };\n  return DateInput;\n}(Observable);\nexport { DateInput };", "map": {"version": 3, "names": ["_a", "__assign", "__extends", "DateObject", "approximateStringMatching", "KeyCode", "Key", "extend", "isPresent", "isDocumentAvailable", "millisecondDigitsInFormat", "millisecondStepFor", "isValidDate", "isIOS", "Observable", "DateInputInteractionMode", "isEqual", "cloneDate", "Constants", "DEFAULT_SEGMENT_STEP", "DRAG_START", "DROP", "TOUCH_START", "MOUSE_DOWN", "MOUSE_UP", "CLICK", "INPUT", "KEY_DOWN", "FOCUS", "BLUR", "PASTE", "MOUSE_SCROLL", "MOUSE_WHEEL", "VALUE_CHANGE", "INPUT_END", "BLUR_END", "FOCUS_END", "CHANGE", "defaultDateInputOptions", "format", "hasPlaceholder", "placeholder", "cycleTime", "locale", "steps", "millisecond", "second", "minute", "hour", "day", "month", "year", "formatPlaceholder", "events", "selectNearestSegmentOnFocus", "selectPreviousSegmentOnBackspace", "enableMouseWheel", "allowCaretMode", "autoSwitchParts", "autoSwitchKeys", "twoDigitYearMax", "autoCorrectParts", "autoFill", "toggleDayPeriod", "DateInput", "_super", "element", "options", "_this", "call", "dateObject", "currentText", "currentFormat", "interactionMode", "None", "previousElementSelection", "start", "end", "init", "Object", "defineProperty", "prototype", "get", "getValue", "enumerable", "configurable", "dateValue", "value", "Date", "formattedValue", "intl", "intlService", "createDateObject", "setValue", "setTextAndFormat", "bindEvents", "resetSegmentValue", "forceUpdate", "destroy", "unbindEvents", "onElementDragStart", "bind", "addEventListener", "onElementDrop", "onElementClick", "onElementMouseDown", "onElementMouseUp", "onElementInput", "onElementKeyDown", "onElementFocus", "onElementBlur", "onElementChange", "onElementPaste", "onElementMouseWheel", "removeEventListener", "setOptions", "refresh", "setDateObjectOptions", "newOptions", "getDateObjectOptions", "resetLocale", "isInCaretMode", "<PERSON><PERSON>", "focus", "selectNearestSegment", "e", "preventDefault", "mouseDownStarted", "focusedPriorToMouseDown", "isActive", "switchedPartOnPreviousKeyAction", "selection", "detail", "selectionPresent", "selectionStart", "selectionEnd", "placeholderToggled", "hasValue", "selectFirstSegment", "index", "caret", "setSelection", "selectionByIndex", "triggerInput", "event", "oldElementValue", "elementValue", "isPasteInProgress", "updateOnPaste", "keyDownEvent", "isBackspaceKey", "keyCode", "BACKSPACE", "key", "isDeleteKey", "DELETE", "originalInteractionMode", "Selection", "hasCaret", "SPACE", "restorePreviousInputEventState", "oldExistingDateValue", "oldDateValue", "getTextAndFormat", "text", "oldText", "previousElementValue", "newText", "diff", "formatPattern", "keyEvent", "length", "formatSeparator", "navigationOnly", "parsePartsResults", "switchPart", "error", "i", "parsePartResult", "parsePart", "symbol", "currentChar", "cycleSegmentValue", "rawTextValue", "isDeleting", "originalFormat", "push", "type", "switchToNext", "hasFixedFormat", "lastParseResult", "lastParseResultHasNoValue", "parsingFailedOnDelete", "resetPart", "newExistingDateValue", "hasExistingDateValueChanged", "newDateValue", "symbolForSelection", "currentSelection", "diffChar", "hasLeadingZero", "getLeadingZero", "forceUpdateWithSelection", "switchDateSegment", "selectionBySymbol", "data", "tryTriggerValueChange", "oldValue", "triggerInputEnd", "newElementValue", "triggerFocus", "refreshElementValue", "triggerFocusEnd", "triggerBlur", "triggerBlurEnd", "trigger<PERSON>hange", "triggerKeyDown", "keyEventMatchesAutoSwitchKeys", "isTabKey", "TAB", "_b", "shift<PERSON>ey", "step", "getStepFromSymbol", "shouldPreventDefault", "altKey", "ctrl<PERSON>ey", "metaKey", "ARROW_LEFT", "ARROW_UP", "modifyDateSegmentValue", "ARROW_RIGHT", "ARROW_DOWN", "ENTER", "HOME", "END", "triggerMouseWheel", "wheelDelta", "returnValue", "parseDate", "inputFormat", "shouldNormalizeCentury", "normalizeCentury", "oldDateObjectValue", "writeValue", "defaultDateFormat", "displayFormat", "undefined", "document", "activeElement", "setSelectionRange", "scrollIntoView", "block", "inline", "Math", "max", "j", "offset", "currentSymbol", "symbolCandidate", "previousFormatSymbol", "a", "b", "readonly", "msDigits", "modifyPart", "args", "triggerValueChange", "trigger", "elementValueLength", "selectionOffset", "setElementValue", "Number", "restorePreviousElementValue", "restorePreviousElementSelection", "verifyValue", "getDateObject", "Error", "showPlaceholder", "isPosition", "disabled", "selectDateSegment", "begin", "leadingZero", "setLeadingZero", "defaultOptions", "keyObject", "map", "x", "toString", "toLowerCase", "trim", "indexOf", "currentDate", "hours", "minutes", "seconds", "date", "getFullYear", "getMonth", "getDate", "getHours", "getMinutes", "getSeconds"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-dateinputs-common/dist/es/dateinput/dateinput.js"], "sourcesContent": ["var _a;\nimport { __assign, __extends } from \"tslib\";\nimport { DateObject } from '../common/dateobject';\nimport { approximateStringMatching } from './utils';\nimport { KeyCode } from '../common/keycode';\nimport { Key } from '../common/key';\nimport { extend, isPresent, isDocumentAvailable, millisecondDigitsInFormat, millisecondStepFor, isValidDate, isIOS } from '../common/utils';\nimport { Observable } from '../common/observable';\nimport { DateInputInteractionMode } from './interaction-mode';\nimport { isEqual, cloneDate } from '@progress/kendo-date-math';\nimport { Constants } from '../common/constants';\nvar DEFAULT_SEGMENT_STEP = 1;\nvar DRAG_START = \"dragStart\";\nvar DROP = \"drop\";\nvar TOUCH_START = \"touchstart\";\nvar MOUSE_DOWN = \"mousedown\";\nvar MOUSE_UP = \"mouseup\";\nvar CLICK = \"click\";\nvar INPUT = \"input\";\nvar KEY_DOWN = \"keydown\";\nvar FOCUS = \"focus\";\nvar BLUR = \"blur\";\nvar PASTE = \"paste\";\nvar MOUSE_SCROLL = \"DOMMouseScroll\";\nvar MOUSE_WHEEL = \"mousewheel\";\nvar VALUE_CHANGE = \"valueChange\";\nvar INPUT_END = \"inputEnd\";\nvar BLUR_END = \"blurEnd\";\nvar FOCUS_END = \"focusEnd\";\nvar CHANGE = \"change\";\nvar defaultDateInputOptions = {\n    format: \"d\",\n    hasPlaceholder: false,\n    placeholder: null,\n    cycleTime: true,\n    locale: null,\n    steps: {\n        millisecond: DEFAULT_SEGMENT_STEP,\n        second: DEFAULT_SEGMENT_STEP,\n        minute: DEFAULT_SEGMENT_STEP,\n        hour: DEFAULT_SEGMENT_STEP,\n        day: DEFAULT_SEGMENT_STEP,\n        month: DEFAULT_SEGMENT_STEP,\n        year: DEFAULT_SEGMENT_STEP\n    },\n    formatPlaceholder: null,\n    events: (_a = {},\n        _a[VALUE_CHANGE] = null,\n        _a[INPUT] = null,\n        _a[INPUT_END] = null,\n        _a[FOCUS] = null,\n        _a[FOCUS_END] = null,\n        _a[BLUR] = null,\n        _a[BLUR_END] = null,\n        _a[KEY_DOWN] = null,\n        _a[MOUSE_WHEEL] = null,\n        _a[CHANGE] = null,\n        _a),\n    selectNearestSegmentOnFocus: false,\n    selectPreviousSegmentOnBackspace: false,\n    enableMouseWheel: false,\n    allowCaretMode: false,\n    autoSwitchParts: true,\n    autoSwitchKeys: [],\n    twoDigitYearMax: Constants.twoDigitYearMax,\n    autoCorrectParts: true,\n    autoFill: false,\n    toggleDayPeriod: false\n};\nvar DateInput = /** @class */ (function (_super) {\n    __extends(DateInput, _super);\n    function DateInput(element, options) {\n        var _this = _super.call(this, options) || this;\n        _this.dateObject = null;\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        _this.currentText = '';\n        _this.currentFormat = '';\n        _this.interactionMode = DateInputInteractionMode.None;\n        _this.previousElementSelection = { start: 0, end: 0 };\n        _this.init(element, options);\n        return _this;\n    }\n    Object.defineProperty(DateInput.prototype, \"value\", {\n        get: function () {\n            return this.dateObject && this.dateObject.getValue();\n        },\n        enumerable: false,\n        configurable: true\n    });\n    DateInput.prototype.init = function (element, options) {\n        var dateValue = isValidDate(this.options.value) ? cloneDate(this.options.value) : new Date(options.formattedValue);\n        if (!isValidDate(dateValue)) {\n            dateValue = null;\n        }\n        this.element = element;\n        // this.element._kendoWidget = this;\n        this.options = extend({}, defaultDateInputOptions, options, { steps: __assign(__assign({}, defaultDateInputOptions.steps), options.steps) });\n        this.intl = this.options.intlService;\n        this.dateObject = this.createDateObject();\n        this.dateObject.setValue(dateValue);\n        this.setTextAndFormat();\n        this.bindEvents();\n        this.resetSegmentValue = true;\n        this.interactionMode = DateInputInteractionMode.None;\n        this.forceUpdate();\n    };\n    DateInput.prototype.destroy = function () {\n        this.unbindEvents();\n        this.dateObject = null;\n        _super.prototype.destroy.call(this);\n    };\n    DateInput.prototype.bindEvents = function () {\n        this.onElementDragStart = this.onElementDragStart.bind(this);\n        this.element.addEventListener(DRAG_START, this.onElementDragStart);\n        this.onElementDrop = this.onElementDrop.bind(this);\n        this.element.addEventListener(DROP, this.onElementDrop);\n        this.onElementClick = this.onElementClick.bind(this);\n        this.element.addEventListener(CLICK, this.onElementClick);\n        this.onElementMouseDown = this.onElementMouseDown.bind(this);\n        this.element.addEventListener(MOUSE_DOWN, this.onElementMouseDown);\n        this.element.addEventListener(TOUCH_START, this.onElementMouseDown);\n        this.onElementMouseUp = this.onElementMouseUp.bind(this);\n        this.element.addEventListener(MOUSE_UP, this.onElementMouseUp);\n        this.onElementInput = this.onElementInput.bind(this);\n        this.element.addEventListener(INPUT, this.onElementInput);\n        this.onElementKeyDown = this.onElementKeyDown.bind(this);\n        this.element.addEventListener(KEY_DOWN, this.onElementKeyDown);\n        this.onElementFocus = this.onElementFocus.bind(this);\n        this.element.addEventListener(FOCUS, this.onElementFocus);\n        this.onElementBlur = this.onElementBlur.bind(this);\n        this.element.addEventListener(BLUR, this.onElementBlur);\n        this.onElementChange = this.onElementChange.bind(this);\n        this.element.addEventListener(CHANGE, this.onElementChange);\n        this.onElementPaste = this.onElementPaste.bind(this);\n        this.element.addEventListener(PASTE, this.onElementPaste);\n        this.onElementMouseWheel = this.onElementMouseWheel.bind(this);\n        this.element.addEventListener(MOUSE_SCROLL, this.onElementMouseWheel);\n        this.element.addEventListener(MOUSE_WHEEL, this.onElementMouseWheel);\n    };\n    DateInput.prototype.unbindEvents = function () {\n        this.element.removeEventListener(DRAG_START, this.onElementDragStart);\n        this.element.removeEventListener(DROP, this.onElementDrop);\n        this.element.removeEventListener(TOUCH_START, this.onElementMouseDown);\n        this.element.removeEventListener(MOUSE_DOWN, this.onElementMouseDown);\n        this.element.removeEventListener(MOUSE_UP, this.onElementMouseUp);\n        this.element.removeEventListener(CLICK, this.onElementClick);\n        this.element.removeEventListener(INPUT, this.onElementInput);\n        this.element.removeEventListener(KEY_DOWN, this.onElementKeyDown);\n        this.element.removeEventListener(FOCUS, this.onElementFocus);\n        this.element.removeEventListener(BLUR, this.onElementBlur);\n        this.element.removeEventListener(CHANGE, this.onElementChange);\n        this.element.removeEventListener(PASTE, this.onElementPaste);\n        this.element.removeEventListener(MOUSE_SCROLL, this.onElementMouseWheel);\n        this.element.removeEventListener(MOUSE_WHEEL, this.onElementMouseWheel);\n    };\n    DateInput.prototype.setOptions = function (options, refresh) {\n        if (refresh === void 0) { refresh = false; }\n        this.options = extend({}, this.options, options, { steps: __assign(__assign({}, defaultDateInputOptions.steps), options.steps) });\n        this.setDateObjectOptions();\n        if (refresh) {\n            this.unbindEvents();\n            this.init(this.element, this.options);\n        }\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.setDateObjectOptions = function () {\n        if (this.dateObject) {\n            var newOptions = this.getDateObjectOptions();\n            this.dateObject.setOptions(newOptions);\n        }\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.resetLocale = function () {\n        this.unbindEvents();\n        this.init(this.element, this.options);\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.isInCaretMode = function () {\n        return this.interactionMode === DateInputInteractionMode.Caret;\n    };\n    DateInput.prototype.focus = function () {\n        this.element.focus();\n        if (this.options.selectNearestSegmentOnFocus) {\n            this.selectNearestSegment(0);\n        }\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.onElementDragStart = function (e) {\n        e.preventDefault();\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.onElementDrop = function (e) {\n        e.preventDefault();\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.onElementMouseDown = function () {\n        this.mouseDownStarted = true;\n        this.focusedPriorToMouseDown = this.isActive;\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.onElementMouseUp = function (e) {\n        this.mouseDownStarted = false;\n        e.preventDefault();\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.onElementClick = function (e) {\n        this.mouseDownStarted = false;\n        this.switchedPartOnPreviousKeyAction = false;\n        var selection = this.selection;\n        if (this.isInCaretMode()) {\n            // explicitly refresh the input element value\n            // caret mode can change the number of symbols in the element\n            // thus clicking on a segment can result in incorrect selection\n            this.forceUpdate();\n        }\n        if (e.detail === 3) {\n            // when 3 clicks occur, leave the native event to handle the change\n            // this results in selecting the whole element value\n        }\n        else {\n            if (this.isActive && this.options.selectNearestSegmentOnFocus) {\n                var selectionPresent = this.element.selectionStart !== this.element.selectionEnd;\n                var placeholderToggled = isPresent(this.options.placeholder) &&\n                    !this.dateObject.hasValue() &&\n                    !this.focusedPriorToMouseDown;\n                // focus first segment if the user hasn't selected something during mousedown and if the placeholder was just toggled\n                var selectFirstSegment = !selectionPresent && placeholderToggled;\n                var index = selectFirstSegment ? 0 : this.caret()[0];\n                this.selectNearestSegment(index);\n            }\n            else {\n                this.setSelection(this.selectionByIndex(selection.start));\n            }\n        }\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.onElementInput = function (e) {\n        this.triggerInput({ event: e });\n        var oldElementValue = this.elementValue;\n        if (!this.element || !this.dateObject) {\n            return;\n        }\n        var switchedPartOnPreviousKeyAction = this.switchedPartOnPreviousKeyAction;\n        if (this.isPasteInProgress) {\n            if (this.options.allowCaretMode) {\n                // pasting should leave the input with caret\n                // thus allow direct input instead of selection mode\n                this.resetSegmentValue = false;\n            }\n            this.updateOnPaste(e);\n            this.isPasteInProgress = false;\n            return;\n        }\n        var keyDownEvent = this.keyDownEvent || {};\n        var isBackspaceKey = keyDownEvent.keyCode === KeyCode.BACKSPACE || keyDownEvent.key === Key.BACKSPACE;\n        var isDeleteKey = keyDownEvent.keyCode === KeyCode.DELETE || keyDownEvent.key === Key.DELETE;\n        var originalInteractionMode = this.interactionMode;\n        if (this.options.allowCaretMode &&\n            originalInteractionMode !== DateInputInteractionMode.Caret &&\n            !isDeleteKey && !isBackspaceKey) {\n            this.resetSegmentValue = true;\n        }\n        if (this.options.allowCaretMode) {\n            this.interactionMode = DateInputInteractionMode.Caret;\n        }\n        else {\n            this.interactionMode = DateInputInteractionMode.Selection;\n        }\n        var hasCaret = this.isInCaretMode();\n        if (hasCaret && this.keyDownEvent.key === Key.SPACE) {\n            // do not allow custom \"holes\" in the date segments\n            this.restorePreviousInputEventState();\n            return;\n        }\n        var oldExistingDateValue = this.dateObject && this.dateObject.getValue();\n        var oldDateValue = this.dateObject ? this.dateObject.value : null;\n        var _a = this.dateObject.getTextAndFormat(), currentText = _a.text, currentFormat = _a.format;\n        this.currentFormat = currentFormat;\n        var oldText = \"\";\n        if (hasCaret) {\n            if (isBackspaceKey || isDeleteKey) {\n                oldText = this.previousElementValue;\n            }\n            else if (originalInteractionMode === DateInputInteractionMode.Caret) {\n                oldText = this.previousElementValue;\n            }\n            else {\n                oldText = currentText;\n            }\n        }\n        else {\n            oldText = currentText;\n        }\n        var newText = this.elementValue;\n        var diff = approximateStringMatching({\n            oldText: oldText,\n            newText: newText,\n            formatPattern: this.currentFormat,\n            selectionStart: this.selection.start,\n            isInCaretMode: hasCaret,\n            keyEvent: this.keyDownEvent\n        });\n        if (diff && diff.length && diff[0] && diff[0][1] !== Constants.formatSeparator) {\n            this.switchedPartOnPreviousKeyAction = false;\n        }\n        if (hasCaret && (!diff || diff.length === 0)) {\n            this.restorePreviousInputEventState();\n            return;\n        }\n        else if (hasCaret && diff.length === 1) {\n            if (!diff[0] || !diff[0][0]) {\n                this.restorePreviousInputEventState();\n                return;\n            }\n            else if (hasCaret && diff[0] &&\n                (diff[0][0] === Constants.formatSeparator || diff[0][1] === Constants.formatSeparator)) {\n                this.restorePreviousInputEventState();\n                return;\n            }\n        }\n        var navigationOnly = (diff.length === 1 && diff[0][1] === Constants.formatSeparator);\n        var parsePartsResults = [];\n        var switchPart = false;\n        var error = null;\n        if (!navigationOnly) {\n            for (var i = 0; i < diff.length; i++) {\n                var parsePartResult = this.dateObject.parsePart({\n                    symbol: diff[i][0],\n                    currentChar: diff[i][1],\n                    resetSegmentValue: this.resetSegmentValue,\n                    cycleSegmentValue: !this.isInCaretMode(),\n                    rawTextValue: this.element.value,\n                    isDeleting: isBackspaceKey || isDeleteKey,\n                    originalFormat: this.currentFormat\n                });\n                parsePartsResults.push(parsePartResult);\n                if (!parsePartResult.value) {\n                    error = { type: \"parse\" };\n                }\n                switchPart = parsePartResult.switchToNext;\n            }\n        }\n        if (!this.options.autoSwitchParts) {\n            switchPart = false;\n        }\n        this.resetSegmentValue = false;\n        var hasFixedFormat = this.options.format === this.currentFormat ||\n            // all not fixed formats are 1 symbol, e.g. \"d\"\n            (isPresent(this.options.format) && this.options.format.length > 1);\n        var lastParseResult = parsePartsResults[parsePartsResults.length - 1];\n        var lastParseResultHasNoValue = lastParseResult && !isPresent(lastParseResult.value);\n        var parsingFailedOnDelete = (hasCaret && (isBackspaceKey || isDeleteKey) && lastParseResultHasNoValue);\n        var resetPart = lastParseResult ? lastParseResult.resetPart : false;\n        var newExistingDateValue = this.dateObject.getValue();\n        var hasExistingDateValueChanged = !isEqual(oldExistingDateValue, newExistingDateValue);\n        var newDateValue = this.dateObject.value;\n        var symbolForSelection;\n        var currentSelection = this.selection;\n        if (hasCaret) {\n            var diffChar = diff && diff.length > 0 ? diff[0][0] : null;\n            var hasLeadingZero = this.dateObject.getLeadingZero()[diffChar];\n            if (diff.length && diff[0][0] !== Constants.formatSeparator) {\n                if (switchPart) {\n                    this.forceUpdateWithSelection();\n                    this.switchDateSegment(1);\n                }\n                else if (resetPart) {\n                    symbolForSelection = this.currentFormat[currentSelection.start];\n                    if (symbolForSelection) {\n                        this.forceUpdate();\n                        this.setSelection(this.selectionBySymbol(symbolForSelection));\n                    }\n                    else {\n                        this.restorePreviousInputEventState();\n                    }\n                }\n                else if (parsingFailedOnDelete) {\n                    this.forceUpdate();\n                    if (diff.length && diff[0][0] !== Constants.formatSeparator) {\n                        this.setSelection(this.selectionBySymbol(diff[0][0]));\n                    }\n                }\n                else if (lastParseResultHasNoValue) {\n                    if (e.data === \"0\" && hasLeadingZero) {\n                        // do not reset element value on a leading zero\n                        // wait for consecutive input to determine the value\n                    }\n                    else if (isPresent(oldExistingDateValue) && !isPresent(newExistingDateValue)) {\n                        this.restorePreviousInputEventState();\n                    }\n                    else if (!isPresent(oldExistingDateValue) && isPresent(newExistingDateValue)) {\n                        this.forceUpdateWithSelection();\n                    }\n                    else if (isPresent(oldExistingDateValue) && isPresent(newExistingDateValue)) {\n                        if (hasExistingDateValueChanged) {\n                            this.forceUpdateWithSelection();\n                        }\n                        else {\n                            this.restorePreviousInputEventState();\n                        }\n                    }\n                    else if (!isPresent(oldExistingDateValue) && !isPresent(newExistingDateValue)) {\n                        this.forceUpdateWithSelection();\n                    }\n                    else if (oldDateValue !== newDateValue) {\n                        // this can happen on auto correct when no valid value is parsed\n                    }\n                    else {\n                        this.restorePreviousInputEventState();\n                    }\n                }\n                else if (!lastParseResultHasNoValue) {\n                    // the user types a valid but incomplete date (e.g. year \"123\" with format \"yyyy\")\n                    // let them continue typing, but refresh for not fixed formats\n                    if (!hasFixedFormat) {\n                        this.forceUpdateWithSelection();\n                    }\n                }\n            }\n            else {\n                if (!this.options.autoSwitchParts && diff[0][1] === Constants.formatSeparator) {\n                    // do not change the selection when a separator is pressed\n                    // this should happen only if autoSwitchKeys contains the separator explicitly\n                }\n                else {\n                    this.setSelection(this.selectionBySymbol(diff[0][0]));\n                }\n            }\n        }\n        else if (!hasCaret) {\n            this.forceUpdate();\n            if (diff.length && diff[0][0] !== Constants.formatSeparator) {\n                this.setSelection(this.selectionBySymbol(diff[0][0]));\n            }\n            if (this.options.autoSwitchParts) {\n                if (navigationOnly) {\n                    this.resetSegmentValue = true;\n                    if (!switchedPartOnPreviousKeyAction) {\n                        this.switchDateSegment(1);\n                    }\n                    this.switchedPartOnPreviousKeyAction = true;\n                }\n                else if (switchPart) {\n                    this.switchDateSegment(1);\n                    this.switchedPartOnPreviousKeyAction = true;\n                }\n            }\n            else {\n                if (lastParseResult && lastParseResult.switchToNext) {\n                    // the value is complete and should be switched, but the \"autoSwitchParts\" option prevents this\n                    // ensure that the segment value can be reset on next input\n                    this.resetSegmentValue = true;\n                }\n                else if (navigationOnly) {\n                    this.resetSegmentValue = true;\n                    if (!switchedPartOnPreviousKeyAction) {\n                        this.switchDateSegment(1);\n                    }\n                    this.switchedPartOnPreviousKeyAction = true;\n                }\n            }\n            if (isBackspaceKey && this.options.selectPreviousSegmentOnBackspace) {\n                // kendo angular have this UX\n                this.switchDateSegment(-1);\n            }\n        }\n        this.tryTriggerValueChange({\n            oldValue: oldExistingDateValue,\n            event: e\n        });\n        this.triggerInputEnd({ event: e, error: error, oldElementValue: oldElementValue, newElementValue: this.elementValue });\n        if (hasCaret) {\n            // a format like \"F\" can dynamically change the resolved format pattern based on the value, e.g.\n            // \"Tuesday, February 1, 2022 3:04:05 AM\" becomes\n            // \"Wednesday, February 2, 2022 3:04:05 AM\" giving a diff of 2 (\"Tuesday\".length - \"Wednesday\".length)\n            this.setTextAndFormat();\n        }\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.onElementFocus = function (e) {\n        if (this.triggerFocus({ event: e })) {\n            return;\n        }\n        this.isActive = true;\n        this.interactionMode = DateInputInteractionMode.None;\n        this.switchedPartOnPreviousKeyAction = false;\n        this.refreshElementValue();\n        if (!this.mouseDownStarted) {\n            this.caret(0, this.elementValue.length);\n        }\n        this.mouseDownStarted = false;\n        this.triggerFocusEnd({ event: e });\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.onElementBlur = function (e) {\n        this.resetSegmentValue = true;\n        this.isActive = false;\n        if (this.triggerBlur({ event: e })) {\n            return;\n        }\n        if (this.options.autoFill) {\n            this.autoFill();\n        }\n        this.interactionMode = DateInputInteractionMode.None;\n        this.switchedPartOnPreviousKeyAction = false;\n        this.refreshElementValue();\n        this.triggerBlurEnd({ event: e });\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.onElementChange = function (e) {\n        this.triggerChange({ event: e });\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.onElementKeyDown = function (e) {\n        if (this.triggerKeyDown({ event: e })) {\n            return;\n        }\n        var _a = this.selection, start = _a.start, end = _a.end;\n        var event = e;\n        this.keyDownEvent = e;\n        this.previousElementValue = this.element.value;\n        this.previousElementSelection = { start: start, end: end };\n        if (this.keyEventMatchesAutoSwitchKeys(e)) {\n            var isTabKey = e.keyCode === KeyCode.TAB;\n            if (isTabKey) {\n                var _b = this.selection, selectionStart = _b.start, selectionEnd = _b.end;\n                if (e.shiftKey && isTabKey) {\n                    this.switchDateSegment(-1);\n                }\n                else {\n                    this.switchDateSegment(1);\n                }\n                if (selectionStart !== this.selection.start || selectionEnd !== this.selection.end) {\n                    // when the selection changes, prevent the default Tab behavior\n                    e.preventDefault();\n                    return;\n                }\n            }\n            else {\n                // do not allow the \"input\" event to be triggered\n                e.preventDefault();\n                this.switchDateSegment(1);\n                return;\n            }\n        }\n        var symbol = this.currentFormat[this.selection.start];\n        var step = this.getStepFromSymbol(symbol);\n        var shouldPreventDefault = false;\n        var oldElementValue = this.elementValue;\n        if (e.altKey || e.ctrlKey || e.metaKey || e.keyCode === KeyCode.TAB) {\n            return;\n        }\n        switch (e.keyCode) {\n            case KeyCode.ARROW_LEFT:\n                this.switchDateSegment(-1);\n                shouldPreventDefault = true;\n                this.switchedPartOnPreviousKeyAction = false;\n                break;\n            case KeyCode.ARROW_UP:\n                this.modifyDateSegmentValue(step, symbol, event);\n                if (oldElementValue !== this.elementValue) {\n                    this.triggerInputEnd({ event: e, error: null, newElementValue: this.elementValue, oldElementValue: oldElementValue });\n                }\n                shouldPreventDefault = true;\n                this.switchedPartOnPreviousKeyAction = false;\n                break;\n            case KeyCode.ARROW_RIGHT:\n                this.switchDateSegment(1);\n                shouldPreventDefault = true;\n                this.switchedPartOnPreviousKeyAction = false;\n                break;\n            case KeyCode.ARROW_DOWN:\n                this.modifyDateSegmentValue(-step, symbol, event);\n                if (oldElementValue !== this.elementValue) {\n                    this.triggerInputEnd({ event: e, error: null, newElementValue: this.elementValue, oldElementValue: oldElementValue });\n                }\n                shouldPreventDefault = true;\n                this.switchedPartOnPreviousKeyAction = false;\n                break;\n            case KeyCode.ENTER:\n                // todo: handle \"change\" event\n                break;\n            case KeyCode.HOME:\n                this.selectNearestSegment(0);\n                shouldPreventDefault = true;\n                this.switchedPartOnPreviousKeyAction = false;\n                this.resetSegmentValue = true;\n                break;\n            case KeyCode.END:\n                this.selectNearestSegment(this.elementValue.length);\n                shouldPreventDefault = true;\n                this.switchedPartOnPreviousKeyAction = false;\n                this.resetSegmentValue = true;\n                break;\n            default:\n                // allow the \"input\" event to handle the change\n                return;\n        }\n        if (shouldPreventDefault) {\n            e.preventDefault();\n        }\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.onElementPaste = function () {\n        this.isPasteInProgress = true;\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.onElementMouseWheel = function (e) {\n        var oldElementValue = this.elementValue;\n        if (!this.options.enableMouseWheel || this.triggerMouseWheel({ event: e })) {\n            return;\n        }\n        if (!this.isActive) {\n            return;\n        }\n        var event = e;\n        if (event.shiftKey) {\n            this.switchDateSegment((event.wheelDelta || -event.detail) > 0 ? -1 : 1);\n        }\n        else {\n            this.modifyDateSegmentValue((event.wheelDelta || -event.detail) > 0 ? 1 : -1);\n        }\n        event.returnValue = false;\n        if (event.preventDefault) {\n            event.preventDefault();\n        }\n        if (oldElementValue !== this.elementValue) {\n            this.triggerInputEnd({ event: e, error: null, newElementValue: this.elementValue, oldElementValue: oldElementValue });\n        }\n    };\n    DateInput.prototype.updateOnPaste = function (e) {\n        var value = this.intl.parseDate(this.elementValue, this.inputFormat) || this.value;\n        if (isPresent(value) && this.dateObject.shouldNormalizeCentury()) {\n            value = this.dateObject.normalizeCentury(value);\n        }\n        var oldDateObjectValue = this.dateObject && this.dateObject.getValue();\n        this.writeValue(value);\n        this.tryTriggerValueChange({\n            oldValue: oldDateObjectValue,\n            event: e\n        });\n    };\n    Object.defineProperty(DateInput.prototype, \"elementValue\", {\n        get: function () {\n            return (this.element || {}).value || '';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(DateInput.prototype, \"inputFormat\", {\n        get: function () {\n            if (!this.options.format) {\n                return Constants.defaultDateFormat;\n            }\n            if (typeof this.options.format === 'string') {\n                return this.options.format;\n            }\n            else {\n                return this.options.format.inputFormat;\n            }\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(DateInput.prototype, \"displayFormat\", {\n        get: function () {\n            if (!this.options.format) {\n                return Constants.defaultDateFormat;\n            }\n            if (typeof this.options.format === 'string') {\n                return this.options.format;\n            }\n            else {\n                return this.options.format.displayFormat;\n            }\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(DateInput.prototype, \"selection\", {\n        get: function () {\n            var returnValue = { start: 0, end: 0 };\n            if (this.element !== null && this.element.selectionStart !== undefined) {\n                returnValue = {\n                    start: this.element.selectionStart,\n                    end: this.element.selectionEnd\n                };\n            }\n            return returnValue;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    DateInput.prototype.setSelection = function (selection) {\n        if (this.element && document.activeElement === this.element) {\n            this.element.setSelectionRange(selection.start, selection.end);\n            if (isDocumentAvailable() && isIOS()) {\n                this.element.scrollIntoView({ block: 'nearest', inline: 'nearest' });\n            }\n            if (selection.start !== selection.end) {\n                this.interactionMode = DateInputInteractionMode.Selection;\n            }\n        }\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.selectionBySymbol = function (symbol) {\n        var start = -1;\n        var end = 0;\n        for (var i = 0; i < this.currentFormat.length; i++) {\n            if (this.currentFormat[i] === symbol) {\n                end = i + 1;\n                if (start === -1) {\n                    start = i;\n                }\n            }\n        }\n        if (start < 0) {\n            start = 0;\n        }\n        if (!this.options.autoCorrectParts && this.currentFormat.length !== this.currentText.length) {\n            if (this.currentFormat.length < this.currentText.length) {\n                end += this.currentText.length - this.currentFormat.length;\n            }\n            else {\n                end = Math.max(0, end - (this.currentFormat.length - this.currentText.length));\n            }\n        }\n        return { start: start, end: end };\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.selectionByIndex = function (index) {\n        var selection = { start: index, end: index };\n        for (var i = index, j = index - 1; i < this.currentFormat.length || j >= 0; i++, j--) {\n            if (i < this.currentFormat.length && this.currentFormat[i] !== Constants.formatSeparator) {\n                selection = this.selectionBySymbol(this.currentFormat[i]);\n                break;\n            }\n            if (j >= 0 && this.currentFormat[j] !== Constants.formatSeparator) {\n                selection = this.selectionBySymbol(this.currentFormat[j]);\n                break;\n            }\n        }\n        return selection;\n    };\n    DateInput.prototype.switchDateSegment = function (offset) {\n        var selection = this.selection;\n        if (this.isInCaretMode()) {\n            var start = selection.start;\n            var currentSymbol = this.currentFormat[start - 1];\n            var symbol = \"\";\n            var symbolCandidate = \"\";\n            if (offset < 0) {\n                for (var i = start + offset; i >= 0; i--) {\n                    symbolCandidate = this.currentFormat[i];\n                    if (symbolCandidate !== Constants.formatSeparator &&\n                        symbolCandidate !== currentSymbol) {\n                        start = i;\n                        symbol = symbolCandidate;\n                        break;\n                    }\n                }\n            }\n            else {\n                for (var i = start + offset; i < this.currentFormat.length; i++) {\n                    symbolCandidate = this.currentFormat[i];\n                    if (symbolCandidate !== Constants.formatSeparator &&\n                        symbolCandidate !== currentSymbol) {\n                        start = i;\n                        symbol = symbolCandidate;\n                        break;\n                    }\n                }\n            }\n            if (symbol) {\n                this.forceUpdate();\n                this.setSelection(this.selectionBySymbol(symbol));\n                this.interactionMode = DateInputInteractionMode.Selection;\n                return;\n            }\n        }\n        this.interactionMode = DateInputInteractionMode.None;\n        var _a = this.selection, selectionStart = _a.start, selectionEnd = _a.end;\n        if (selectionStart < selectionEnd &&\n            this.currentFormat[selectionStart] !== this.currentFormat[selectionEnd - 1]) {\n            this.setSelection(this.selectionByIndex(offset > 0 ? selectionStart : selectionEnd - 1));\n            this.resetSegmentValue = true;\n            this.interactionMode = DateInputInteractionMode.None;\n            return;\n        }\n        var previousFormatSymbol = this.currentFormat[selectionStart];\n        var a = selectionStart + offset;\n        while (a > 0 && a < this.currentFormat.length) {\n            if (this.currentFormat[a] !== previousFormatSymbol &&\n                this.currentFormat[a] !== Constants.formatSeparator) {\n                break;\n            }\n            a += offset;\n        }\n        if (this.currentFormat[a] === Constants.formatSeparator) {\n            // no known symbol is found\n            return;\n        }\n        var b = a;\n        while (b >= 0 && b < this.currentFormat.length) {\n            if (this.currentFormat[b] !== this.currentFormat[a]) {\n                break;\n            }\n            b += offset;\n        }\n        if (a > b && (b + 1 !== selectionStart || a + 1 !== selectionEnd)) {\n            this.setSelection({ start: b + 1, end: a + 1 });\n            this.resetSegmentValue = true;\n        }\n        else if (a < b && (a !== selectionStart || b !== selectionEnd)) {\n            this.setSelection({ start: a, end: b });\n            this.resetSegmentValue = true;\n        }\n        this.interactionMode = DateInputInteractionMode.None;\n    };\n    DateInput.prototype.modifyDateSegmentValue = function (offset, symbol, event) {\n        if (symbol === void 0) { symbol = \"\"; }\n        if (event === void 0) { event = {}; }\n        if (!this.dateObject || this.options.readonly) {\n            return;\n        }\n        var oldValue = this.value;\n        var step = DEFAULT_SEGMENT_STEP;\n        var caret = this.caret();\n        symbol = symbol || this.currentFormat[caret[0]];\n        if (symbol === \"S\" && (!this.options.steps.millisecond || this.options.steps.millisecond === DEFAULT_SEGMENT_STEP)) {\n            var msDigits = millisecondDigitsInFormat(this.inputFormat);\n            step = millisecondStepFor(msDigits);\n        }\n        this.dateObject.modifyPart(symbol, step * offset);\n        this.tryTriggerValueChange({\n            oldValue: oldValue,\n            event: event\n        });\n        this.forceUpdate();\n        this.setSelection(this.selectionBySymbol(symbol));\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.tryTriggerValueChange = function (args) {\n        if (args === void 0) { args = { oldValue: null, event: {} }; }\n        if (!isEqual(this.value, args.oldValue)) {\n            return this.triggerValueChange(args);\n        }\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.triggerValueChange = function (args) {\n        if (args === void 0) { args = { oldValue: null, event: {} }; }\n        return this.trigger(VALUE_CHANGE, extend(args, {\n            value: this.value\n        }));\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.triggerInput = function (args) {\n        if (args === void 0) { args = { event: {} }; }\n        return this.trigger(INPUT, extend(args, {\n            value: this.value\n        }));\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.triggerInputEnd = function (args) {\n        if (args === void 0) { args = { event: {}, error: null, oldElementValue: '', newElementValue: '' }; }\n        return this.trigger(INPUT_END, extend(args, {\n            value: this.value\n        }));\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.triggerFocus = function (args) {\n        if (args === void 0) { args = { event: {} }; }\n        return this.trigger(FOCUS, extend({}, args));\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.triggerFocusEnd = function (args) {\n        if (args === void 0) { args = { event: {} }; }\n        return this.trigger(FOCUS_END, extend({}, args));\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.triggerBlur = function (args) {\n        if (args === void 0) { args = { event: {} }; }\n        return this.trigger(BLUR, extend({}, args));\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.triggerBlurEnd = function (args) {\n        if (args === void 0) { args = { event: {} }; }\n        return this.trigger(BLUR_END, extend({}, args));\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.triggerChange = function (args) {\n        if (args === void 0) { args = { event: {} }; }\n        return this.trigger(CHANGE, extend(args, {\n            value: this.value\n        }));\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.triggerKeyDown = function (args) {\n        if (args === void 0) { args = { event: {} }; }\n        return this.trigger(KEY_DOWN, extend({}, args));\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.triggerMouseWheel = function (args) {\n        if (args === void 0) { args = { event: {} }; }\n        return this.trigger(MOUSE_WHEEL, extend({}, args));\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.forceUpdate = function () {\n        this.setTextAndFormat();\n        this.refreshElementValue();\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.forceUpdateWithSelection = function () {\n        var _a = this.selection, start = _a.start, end = _a.end;\n        var elementValueLength = this.elementValue.length;\n        this.forceUpdate();\n        var selectionOffset = this.elementValue.length - elementValueLength;\n        this.setSelection({\n            start: start + selectionOffset,\n            end: end + selectionOffset\n        });\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.setTextAndFormat = function () {\n        var _a = this.dateObject.getTextAndFormat(), currentText = _a.text, currentFormat = _a.format;\n        this.currentFormat = currentFormat;\n        this.currentText = currentText;\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.setElementValue = function (value) {\n        this.element.value = value;\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.getStepFromSymbol = function (symbol) {\n        /* eslint-disable no-fallthrough */\n        switch (symbol) {\n            case \"S\":\n                return Number(this.options.steps.millisecond);\n            case \"s\":\n                return Number(this.options.steps.second);\n            case \"m\":\n                return Number(this.options.steps.minute);\n            // represents hour as value from 01 through 12\n            case \"h\":\n            // represents hour as value from 01 through 23\n            case \"H\":\n                return Number(this.options.steps.hour);\n            case \"M\":\n                return Number(this.options.steps.month);\n            // there is no 'D' format specifier for day\n            case \"d\":\n            // used for formats such as \"EEEE, MMMM d, yyyy\",\n            // where \"EEEE\" stands for full name of the day e.g. Monday\n            case \"E\":\n                return Number(this.options.steps.day);\n            // there is no 'Y' format specifier for year\n            case \"y\":\n                return Number(this.options.steps.year);\n            default:\n                return DEFAULT_SEGMENT_STEP;\n        }\n        /* eslint-enable no-fallthrough */\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.restorePreviousInputEventState = function () {\n        this.restorePreviousElementValue();\n        this.restorePreviousElementSelection();\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.restorePreviousElementValue = function () {\n        this.setElementValue(this.previousElementValue || '');\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.restorePreviousElementSelection = function () {\n        var _a = this.previousElementSelection, start = _a.start, end = _a.end;\n        this.setSelection({ start: start || 0, end: end || 0 });\n    };\n    DateInput.prototype.writeValue = function (value) {\n        this.verifyValue(value);\n        this.dateObject = this.getDateObject(value);\n        this.refreshElementValue();\n    };\n    DateInput.prototype.verifyValue = function (value) {\n        if (value && !isValidDate(value)) {\n            throw new Error(\"The 'value' should be a valid JavaScript Date instance.\");\n        }\n    };\n    DateInput.prototype.refreshElementValue = function () {\n        var element = this.element;\n        var format = this.isActive ? this.inputFormat : this.displayFormat;\n        var _a = this.dateObject.getTextAndFormat(format), currentText = _a.text, currentFormat = _a.format;\n        this.currentFormat = currentFormat;\n        this.currentText = currentText;\n        var hasPlaceholder = this.options.hasPlaceholder || isPresent(this.options.placeholder);\n        var showPlaceholder = !this.isActive &&\n            hasPlaceholder &&\n            !this.dateObject.hasValue();\n        if (hasPlaceholder && isPresent(this.options.placeholder)) {\n            element.placeholder = this.options.placeholder;\n        }\n        var newElementValue = showPlaceholder ? \"\" : currentText;\n        this.previousElementValue = this.elementValue;\n        this.setElementValue(newElementValue);\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.caret = function (start, end) {\n        if (end === void 0) { end = start; }\n        var isPosition = start !== undefined;\n        var returnValue = [start, start];\n        var element = this.element;\n        if (isPosition && (this.options.disabled || this.options.readonly)) {\n            return undefined;\n        }\n        try {\n            if (element.selectionStart !== undefined) {\n                if (isPosition) {\n                    if (isDocumentAvailable() && document.activeElement !== element) {\n                        element.focus();\n                    }\n                    element.setSelectionRange(start, end);\n                }\n                returnValue = [element.selectionStart, element.selectionEnd];\n            }\n        }\n        catch (e) {\n            returnValue = [];\n        }\n        return returnValue;\n    };\n    DateInput.prototype.selectNearestSegment = function (index) {\n        // Finds the nearest (in both directions) known part.\n        for (var i = index, j = index - 1; i < this.currentFormat.length || j >= 0; i++, j--) {\n            if (i < this.currentFormat.length && this.currentFormat[i] !== \"_\") {\n                this.selectDateSegment(this.currentFormat[i]);\n                return;\n            }\n            if (j >= 0 && this.currentFormat[j] !== \"_\") {\n                this.selectDateSegment(this.currentFormat[j]);\n                return;\n            }\n        }\n    };\n    DateInput.prototype.selectDateSegment = function (symbol) {\n        var begin = -1;\n        var end = 0;\n        for (var i = 0; i < this.currentFormat.length; i++) {\n            if (this.currentFormat[i] === symbol) {\n                end = i + 1;\n                if (begin === -1) {\n                    begin = i;\n                }\n            }\n        }\n        if (begin < 0) {\n            begin = 0;\n        }\n        this.caret(0, 0);\n        this.caret(begin, end);\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.getDateObject = function (value) {\n        var leadingZero = ((this.dateObject || {}) || null).leadingZero;\n        this.options.value = value;\n        var dateObject = this.createDateObject();\n        dateObject.setLeadingZero(this.isActive ? leadingZero : null);\n        return dateObject;\n    };\n    /* tslint:disable:align */\n    /**\n     * @hidden\n     */\n    DateInput.prototype.createDateObject = function () {\n        var defaultOptions = this.getDateObjectOptions();\n        var dateObject = new DateObject(extend({}, defaultOptions));\n        return dateObject;\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.getDateObjectOptions = function () {\n        var newOptions = {\n            intlService: this.options.intlService,\n            formatPlaceholder: this.options.formatPlaceholder ? this.options.formatPlaceholder : 'formatPattern',\n            format: this.inputFormat,\n            cycleTime: this.options.cycleTime,\n            twoDigitYearMax: this.options.twoDigitYearMax,\n            autoCorrectParts: this.options.autoCorrectParts,\n            value: this.options.value,\n            toggleDayPeriod: this.options.toggleDayPeriod,\n            autoSwitchParts: this.options.autoSwitchParts\n        };\n        return newOptions;\n    };\n    /* tslint:enable:align */\n    /**\n     * @hidden\n     */\n    DateInput.prototype.keyEventMatchesAutoSwitchKeys = function (keyObject) {\n        var autoSwitchKeys = (this.options.autoSwitchKeys || [])\n            .map(function (x) { return x.toString().toLowerCase().trim(); });\n        if (autoSwitchKeys.indexOf(keyObject.keyCode.toString()) >= 0 ||\n            autoSwitchKeys.indexOf(keyObject.keyCode) >= 0 ||\n            autoSwitchKeys.indexOf(keyObject.key.toLowerCase().trim()) >= 0) {\n            return true;\n        }\n        return false;\n    };\n    /**\n     * @hidden\n     */\n    DateInput.prototype.autoFill = function () {\n        var dateObject = this.dateObject;\n        var currentDate = new Date();\n        var day, month, year, hours, minutes, seconds;\n        if (dateObject.date || dateObject.month || dateObject.year || dateObject.hours || dateObject.minutes || dateObject.seconds) {\n            year = dateObject.year ? dateObject.value.getFullYear() : currentDate.getFullYear(),\n                month = dateObject.month ? dateObject.value.getMonth() : currentDate.getMonth(),\n                day = dateObject.date ? dateObject.value.getDate() : currentDate.getDate(),\n                hours = dateObject.hours ? dateObject.value.getHours() : currentDate.getHours(),\n                minutes = dateObject.minutes ? dateObject.value.getMinutes() : currentDate.getMinutes(),\n                seconds = dateObject.seconds ? dateObject.value.getSeconds() : currentDate.getSeconds();\n            dateObject.setValue(new Date(year, month, day, hours, minutes, seconds));\n            this.refreshElementValue();\n            this.triggerValueChange();\n        }\n    };\n    return DateInput;\n}(Observable));\nexport { DateInput };\n"], "mappings": "AAAA,IAAIA,EAAE;AACN,SAASC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,yBAAyB,QAAQ,SAAS;AACnD,SAASC,OAAO,QAAQ,mBAAmB;AAC3C,SAASC,GAAG,QAAQ,eAAe;AACnC,SAASC,MAAM,EAAEC,SAAS,EAAEC,mBAAmB,EAAEC,yBAAyB,EAAEC,kBAAkB,EAAEC,WAAW,EAAEC,KAAK,QAAQ,iBAAiB;AAC3I,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,wBAAwB,QAAQ,oBAAoB;AAC7D,SAASC,OAAO,EAAEC,SAAS,QAAQ,2BAA2B;AAC9D,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,IAAIC,oBAAoB,GAAG,CAAC;AAC5B,IAAIC,UAAU,GAAG,WAAW;AAC5B,IAAIC,IAAI,GAAG,MAAM;AACjB,IAAIC,WAAW,GAAG,YAAY;AAC9B,IAAIC,UAAU,GAAG,WAAW;AAC5B,IAAIC,QAAQ,GAAG,SAAS;AACxB,IAAIC,KAAK,GAAG,OAAO;AACnB,IAAIC,KAAK,GAAG,OAAO;AACnB,IAAIC,QAAQ,GAAG,SAAS;AACxB,IAAIC,KAAK,GAAG,OAAO;AACnB,IAAIC,IAAI,GAAG,MAAM;AACjB,IAAIC,KAAK,GAAG,OAAO;AACnB,IAAIC,YAAY,GAAG,gBAAgB;AACnC,IAAIC,WAAW,GAAG,YAAY;AAC9B,IAAIC,YAAY,GAAG,aAAa;AAChC,IAAIC,SAAS,GAAG,UAAU;AAC1B,IAAIC,QAAQ,GAAG,SAAS;AACxB,IAAIC,SAAS,GAAG,UAAU;AAC1B,IAAIC,MAAM,GAAG,QAAQ;AACrB,IAAIC,uBAAuB,GAAG;EAC1BC,MAAM,EAAE,GAAG;EACXC,cAAc,EAAE,KAAK;EACrBC,WAAW,EAAE,IAAI;EACjBC,SAAS,EAAE,IAAI;EACfC,MAAM,EAAE,IAAI;EACZC,KAAK,EAAE;IACHC,WAAW,EAAE1B,oBAAoB;IACjC2B,MAAM,EAAE3B,oBAAoB;IAC5B4B,MAAM,EAAE5B,oBAAoB;IAC5B6B,IAAI,EAAE7B,oBAAoB;IAC1B8B,GAAG,EAAE9B,oBAAoB;IACzB+B,KAAK,EAAE/B,oBAAoB;IAC3BgC,IAAI,EAAEhC;EACV,CAAC;EACDiC,iBAAiB,EAAE,IAAI;EACvBC,MAAM,GAAGrD,EAAE,GAAG,CAAC,CAAC,EACZA,EAAE,CAACiC,YAAY,CAAC,GAAG,IAAI,EACvBjC,EAAE,CAAC0B,KAAK,CAAC,GAAG,IAAI,EAChB1B,EAAE,CAACkC,SAAS,CAAC,GAAG,IAAI,EACpBlC,EAAE,CAAC4B,KAAK,CAAC,GAAG,IAAI,EAChB5B,EAAE,CAACoC,SAAS,CAAC,GAAG,IAAI,EACpBpC,EAAE,CAAC6B,IAAI,CAAC,GAAG,IAAI,EACf7B,EAAE,CAACmC,QAAQ,CAAC,GAAG,IAAI,EACnBnC,EAAE,CAAC2B,QAAQ,CAAC,GAAG,IAAI,EACnB3B,EAAE,CAACgC,WAAW,CAAC,GAAG,IAAI,EACtBhC,EAAE,CAACqC,MAAM,CAAC,GAAG,IAAI,EACjBrC,EAAE,CAAC;EACPsD,2BAA2B,EAAE,KAAK;EAClCC,gCAAgC,EAAE,KAAK;EACvCC,gBAAgB,EAAE,KAAK;EACvBC,cAAc,EAAE,KAAK;EACrBC,eAAe,EAAE,IAAI;EACrBC,cAAc,EAAE,EAAE;EAClBC,eAAe,EAAE1C,SAAS,CAAC0C,eAAe;EAC1CC,gBAAgB,EAAE,IAAI;EACtBC,QAAQ,EAAE,KAAK;EACfC,eAAe,EAAE;AACrB,CAAC;AACD,IAAIC,SAAS,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC7C/D,SAAS,CAAC8D,SAAS,EAAEC,MAAM,CAAC;EAC5B,SAASD,SAASA,CAACE,OAAO,EAAEC,OAAO,EAAE;IACjC,IAAIC,KAAK,GAAGH,MAAM,CAACI,IAAI,CAAC,IAAI,EAAEF,OAAO,CAAC,IAAI,IAAI;IAC9CC,KAAK,CAACE,UAAU,GAAG,IAAI;IACvB;IACA;IACAF,KAAK,CAACG,WAAW,GAAG,EAAE;IACtBH,KAAK,CAACI,aAAa,GAAG,EAAE;IACxBJ,KAAK,CAACK,eAAe,GAAG1D,wBAAwB,CAAC2D,IAAI;IACrDN,KAAK,CAACO,wBAAwB,GAAG;MAAEC,KAAK,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAE,CAAC;IACrDT,KAAK,CAACU,IAAI,CAACZ,OAAO,EAAEC,OAAO,CAAC;IAC5B,OAAOC,KAAK;EAChB;EACAW,MAAM,CAACC,cAAc,CAAChB,SAAS,CAACiB,SAAS,EAAE,OAAO,EAAE;IAChDC,GAAG,EAAE,SAAAA,CAAA,EAAY;MACb,OAAO,IAAI,CAACZ,UAAU,IAAI,IAAI,CAACA,UAAU,CAACa,QAAQ,CAAC,CAAC;IACxD,CAAC;IACDC,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE;EAClB,CAAC,CAAC;EACFrB,SAAS,CAACiB,SAAS,CAACH,IAAI,GAAG,UAAUZ,OAAO,EAAEC,OAAO,EAAE;IACnD,IAAImB,SAAS,GAAG1E,WAAW,CAAC,IAAI,CAACuD,OAAO,CAACoB,KAAK,CAAC,GAAGtE,SAAS,CAAC,IAAI,CAACkD,OAAO,CAACoB,KAAK,CAAC,GAAG,IAAIC,IAAI,CAACrB,OAAO,CAACsB,cAAc,CAAC;IAClH,IAAI,CAAC7E,WAAW,CAAC0E,SAAS,CAAC,EAAE;MACzBA,SAAS,GAAG,IAAI;IACpB;IACA,IAAI,CAACpB,OAAO,GAAGA,OAAO;IACtB;IACA,IAAI,CAACC,OAAO,GAAG5D,MAAM,CAAC,CAAC,CAAC,EAAE+B,uBAAuB,EAAE6B,OAAO,EAAE;MAAEvB,KAAK,EAAE3C,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEqC,uBAAuB,CAACM,KAAK,CAAC,EAAEuB,OAAO,CAACvB,KAAK;IAAE,CAAC,CAAC;IAC5I,IAAI,CAAC8C,IAAI,GAAG,IAAI,CAACvB,OAAO,CAACwB,WAAW;IACpC,IAAI,CAACrB,UAAU,GAAG,IAAI,CAACsB,gBAAgB,CAAC,CAAC;IACzC,IAAI,CAACtB,UAAU,CAACuB,QAAQ,CAACP,SAAS,CAAC;IACnC,IAAI,CAACQ,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACC,UAAU,CAAC,CAAC;IACjB,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACvB,eAAe,GAAG1D,wBAAwB,CAAC2D,IAAI;IACpD,IAAI,CAACuB,WAAW,CAAC,CAAC;EACtB,CAAC;EACDjC,SAAS,CAACiB,SAAS,CAACiB,OAAO,GAAG,YAAY;IACtC,IAAI,CAACC,YAAY,CAAC,CAAC;IACnB,IAAI,CAAC7B,UAAU,GAAG,IAAI;IACtBL,MAAM,CAACgB,SAAS,CAACiB,OAAO,CAAC7B,IAAI,CAAC,IAAI,CAAC;EACvC,CAAC;EACDL,SAAS,CAACiB,SAAS,CAACc,UAAU,GAAG,YAAY;IACzC,IAAI,CAACK,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACC,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACnC,OAAO,CAACoC,gBAAgB,CAAClF,UAAU,EAAE,IAAI,CAACgF,kBAAkB,CAAC;IAClE,IAAI,CAACG,aAAa,GAAG,IAAI,CAACA,aAAa,CAACF,IAAI,CAAC,IAAI,CAAC;IAClD,IAAI,CAACnC,OAAO,CAACoC,gBAAgB,CAACjF,IAAI,EAAE,IAAI,CAACkF,aAAa,CAAC;IACvD,IAAI,CAACC,cAAc,GAAG,IAAI,CAACA,cAAc,CAACH,IAAI,CAAC,IAAI,CAAC;IACpD,IAAI,CAACnC,OAAO,CAACoC,gBAAgB,CAAC7E,KAAK,EAAE,IAAI,CAAC+E,cAAc,CAAC;IACzD,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACJ,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACnC,OAAO,CAACoC,gBAAgB,CAAC/E,UAAU,EAAE,IAAI,CAACkF,kBAAkB,CAAC;IAClE,IAAI,CAACvC,OAAO,CAACoC,gBAAgB,CAAChF,WAAW,EAAE,IAAI,CAACmF,kBAAkB,CAAC;IACnE,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACL,IAAI,CAAC,IAAI,CAAC;IACxD,IAAI,CAACnC,OAAO,CAACoC,gBAAgB,CAAC9E,QAAQ,EAAE,IAAI,CAACkF,gBAAgB,CAAC;IAC9D,IAAI,CAACC,cAAc,GAAG,IAAI,CAACA,cAAc,CAACN,IAAI,CAAC,IAAI,CAAC;IACpD,IAAI,CAACnC,OAAO,CAACoC,gBAAgB,CAAC5E,KAAK,EAAE,IAAI,CAACiF,cAAc,CAAC;IACzD,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACP,IAAI,CAAC,IAAI,CAAC;IACxD,IAAI,CAACnC,OAAO,CAACoC,gBAAgB,CAAC3E,QAAQ,EAAE,IAAI,CAACiF,gBAAgB,CAAC;IAC9D,IAAI,CAACC,cAAc,GAAG,IAAI,CAACA,cAAc,CAACR,IAAI,CAAC,IAAI,CAAC;IACpD,IAAI,CAACnC,OAAO,CAACoC,gBAAgB,CAAC1E,KAAK,EAAE,IAAI,CAACiF,cAAc,CAAC;IACzD,IAAI,CAACC,aAAa,GAAG,IAAI,CAACA,aAAa,CAACT,IAAI,CAAC,IAAI,CAAC;IAClD,IAAI,CAACnC,OAAO,CAACoC,gBAAgB,CAACzE,IAAI,EAAE,IAAI,CAACiF,aAAa,CAAC;IACvD,IAAI,CAACC,eAAe,GAAG,IAAI,CAACA,eAAe,CAACV,IAAI,CAAC,IAAI,CAAC;IACtD,IAAI,CAACnC,OAAO,CAACoC,gBAAgB,CAACjE,MAAM,EAAE,IAAI,CAAC0E,eAAe,CAAC;IAC3D,IAAI,CAACC,cAAc,GAAG,IAAI,CAACA,cAAc,CAACX,IAAI,CAAC,IAAI,CAAC;IACpD,IAAI,CAACnC,OAAO,CAACoC,gBAAgB,CAACxE,KAAK,EAAE,IAAI,CAACkF,cAAc,CAAC;IACzD,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACZ,IAAI,CAAC,IAAI,CAAC;IAC9D,IAAI,CAACnC,OAAO,CAACoC,gBAAgB,CAACvE,YAAY,EAAE,IAAI,CAACkF,mBAAmB,CAAC;IACrE,IAAI,CAAC/C,OAAO,CAACoC,gBAAgB,CAACtE,WAAW,EAAE,IAAI,CAACiF,mBAAmB,CAAC;EACxE,CAAC;EACDjD,SAAS,CAACiB,SAAS,CAACkB,YAAY,GAAG,YAAY;IAC3C,IAAI,CAACjC,OAAO,CAACgD,mBAAmB,CAAC9F,UAAU,EAAE,IAAI,CAACgF,kBAAkB,CAAC;IACrE,IAAI,CAAClC,OAAO,CAACgD,mBAAmB,CAAC7F,IAAI,EAAE,IAAI,CAACkF,aAAa,CAAC;IAC1D,IAAI,CAACrC,OAAO,CAACgD,mBAAmB,CAAC5F,WAAW,EAAE,IAAI,CAACmF,kBAAkB,CAAC;IACtE,IAAI,CAACvC,OAAO,CAACgD,mBAAmB,CAAC3F,UAAU,EAAE,IAAI,CAACkF,kBAAkB,CAAC;IACrE,IAAI,CAACvC,OAAO,CAACgD,mBAAmB,CAAC1F,QAAQ,EAAE,IAAI,CAACkF,gBAAgB,CAAC;IACjE,IAAI,CAACxC,OAAO,CAACgD,mBAAmB,CAACzF,KAAK,EAAE,IAAI,CAAC+E,cAAc,CAAC;IAC5D,IAAI,CAACtC,OAAO,CAACgD,mBAAmB,CAACxF,KAAK,EAAE,IAAI,CAACiF,cAAc,CAAC;IAC5D,IAAI,CAACzC,OAAO,CAACgD,mBAAmB,CAACvF,QAAQ,EAAE,IAAI,CAACiF,gBAAgB,CAAC;IACjE,IAAI,CAAC1C,OAAO,CAACgD,mBAAmB,CAACtF,KAAK,EAAE,IAAI,CAACiF,cAAc,CAAC;IAC5D,IAAI,CAAC3C,OAAO,CAACgD,mBAAmB,CAACrF,IAAI,EAAE,IAAI,CAACiF,aAAa,CAAC;IAC1D,IAAI,CAAC5C,OAAO,CAACgD,mBAAmB,CAAC7E,MAAM,EAAE,IAAI,CAAC0E,eAAe,CAAC;IAC9D,IAAI,CAAC7C,OAAO,CAACgD,mBAAmB,CAACpF,KAAK,EAAE,IAAI,CAACkF,cAAc,CAAC;IAC5D,IAAI,CAAC9C,OAAO,CAACgD,mBAAmB,CAACnF,YAAY,EAAE,IAAI,CAACkF,mBAAmB,CAAC;IACxE,IAAI,CAAC/C,OAAO,CAACgD,mBAAmB,CAAClF,WAAW,EAAE,IAAI,CAACiF,mBAAmB,CAAC;EAC3E,CAAC;EACDjD,SAAS,CAACiB,SAAS,CAACkC,UAAU,GAAG,UAAUhD,OAAO,EAAEiD,OAAO,EAAE;IACzD,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;MAAEA,OAAO,GAAG,KAAK;IAAE;IAC3C,IAAI,CAACjD,OAAO,GAAG5D,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC4D,OAAO,EAAEA,OAAO,EAAE;MAAEvB,KAAK,EAAE3C,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEqC,uBAAuB,CAACM,KAAK,CAAC,EAAEuB,OAAO,CAACvB,KAAK;IAAE,CAAC,CAAC;IACjI,IAAI,CAACyE,oBAAoB,CAAC,CAAC;IAC3B,IAAID,OAAO,EAAE;MACT,IAAI,CAACjB,YAAY,CAAC,CAAC;MACnB,IAAI,CAACrB,IAAI,CAAC,IAAI,CAACZ,OAAO,EAAE,IAAI,CAACC,OAAO,CAAC;IACzC;EACJ,CAAC;EACD;AACJ;AACA;EACIH,SAAS,CAACiB,SAAS,CAACoC,oBAAoB,GAAG,YAAY;IACnD,IAAI,IAAI,CAAC/C,UAAU,EAAE;MACjB,IAAIgD,UAAU,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC;MAC5C,IAAI,CAACjD,UAAU,CAAC6C,UAAU,CAACG,UAAU,CAAC;IAC1C;EACJ,CAAC;EACD;AACJ;AACA;EACItD,SAAS,CAACiB,SAAS,CAACuC,WAAW,GAAG,YAAY;IAC1C,IAAI,CAACrB,YAAY,CAAC,CAAC;IACnB,IAAI,CAACrB,IAAI,CAAC,IAAI,CAACZ,OAAO,EAAE,IAAI,CAACC,OAAO,CAAC;EACzC,CAAC;EACD;AACJ;AACA;EACIH,SAAS,CAACiB,SAAS,CAACwC,aAAa,GAAG,YAAY;IAC5C,OAAO,IAAI,CAAChD,eAAe,KAAK1D,wBAAwB,CAAC2G,KAAK;EAClE,CAAC;EACD1D,SAAS,CAACiB,SAAS,CAAC0C,KAAK,GAAG,YAAY;IACpC,IAAI,CAACzD,OAAO,CAACyD,KAAK,CAAC,CAAC;IACpB,IAAI,IAAI,CAACxD,OAAO,CAACb,2BAA2B,EAAE;MAC1C,IAAI,CAACsE,oBAAoB,CAAC,CAAC,CAAC;IAChC;EACJ,CAAC;EACD;AACJ;AACA;EACI5D,SAAS,CAACiB,SAAS,CAACmB,kBAAkB,GAAG,UAAUyB,CAAC,EAAE;IAClDA,CAAC,CAACC,cAAc,CAAC,CAAC;EACtB,CAAC;EACD;AACJ;AACA;EACI9D,SAAS,CAACiB,SAAS,CAACsB,aAAa,GAAG,UAAUsB,CAAC,EAAE;IAC7CA,CAAC,CAACC,cAAc,CAAC,CAAC;EACtB,CAAC;EACD;AACJ;AACA;EACI9D,SAAS,CAACiB,SAAS,CAACwB,kBAAkB,GAAG,YAAY;IACjD,IAAI,CAACsB,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,uBAAuB,GAAG,IAAI,CAACC,QAAQ;EAChD,CAAC;EACD;AACJ;AACA;EACIjE,SAAS,CAACiB,SAAS,CAACyB,gBAAgB,GAAG,UAAUmB,CAAC,EAAE;IAChD,IAAI,CAACE,gBAAgB,GAAG,KAAK;IAC7BF,CAAC,CAACC,cAAc,CAAC,CAAC;EACtB,CAAC;EACD;AACJ;AACA;EACI9D,SAAS,CAACiB,SAAS,CAACuB,cAAc,GAAG,UAAUqB,CAAC,EAAE;IAC9C,IAAI,CAACE,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACG,+BAA+B,GAAG,KAAK;IAC5C,IAAIC,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9B,IAAI,IAAI,CAACV,aAAa,CAAC,CAAC,EAAE;MACtB;MACA;MACA;MACA,IAAI,CAACxB,WAAW,CAAC,CAAC;IACtB;IACA,IAAI4B,CAAC,CAACO,MAAM,KAAK,CAAC,EAAE;MAChB;MACA;IAAA,CACH,MACI;MACD,IAAI,IAAI,CAACH,QAAQ,IAAI,IAAI,CAAC9D,OAAO,CAACb,2BAA2B,EAAE;QAC3D,IAAI+E,gBAAgB,GAAG,IAAI,CAACnE,OAAO,CAACoE,cAAc,KAAK,IAAI,CAACpE,OAAO,CAACqE,YAAY;QAChF,IAAIC,kBAAkB,GAAGhI,SAAS,CAAC,IAAI,CAAC2D,OAAO,CAAC1B,WAAW,CAAC,IACxD,CAAC,IAAI,CAAC6B,UAAU,CAACmE,QAAQ,CAAC,CAAC,IAC3B,CAAC,IAAI,CAACT,uBAAuB;QACjC;QACA,IAAIU,kBAAkB,GAAG,CAACL,gBAAgB,IAAIG,kBAAkB;QAChE,IAAIG,KAAK,GAAGD,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,IAAI,CAAChB,oBAAoB,CAACe,KAAK,CAAC;MACpC,CAAC,MACI;QACD,IAAI,CAACE,YAAY,CAAC,IAAI,CAACC,gBAAgB,CAACX,SAAS,CAACvD,KAAK,CAAC,CAAC;MAC7D;IACJ;EACJ,CAAC;EACD;AACJ;AACA;EACIZ,SAAS,CAACiB,SAAS,CAAC0B,cAAc,GAAG,UAAUkB,CAAC,EAAE;IAC9C,IAAI,CAACkB,YAAY,CAAC;MAAEC,KAAK,EAAEnB;IAAE,CAAC,CAAC;IAC/B,IAAIoB,eAAe,GAAG,IAAI,CAACC,YAAY;IACvC,IAAI,CAAC,IAAI,CAAChF,OAAO,IAAI,CAAC,IAAI,CAACI,UAAU,EAAE;MACnC;IACJ;IACA,IAAI4D,+BAA+B,GAAG,IAAI,CAACA,+BAA+B;IAC1E,IAAI,IAAI,CAACiB,iBAAiB,EAAE;MACxB,IAAI,IAAI,CAAChF,OAAO,CAACV,cAAc,EAAE;QAC7B;QACA;QACA,IAAI,CAACuC,iBAAiB,GAAG,KAAK;MAClC;MACA,IAAI,CAACoD,aAAa,CAACvB,CAAC,CAAC;MACrB,IAAI,CAACsB,iBAAiB,GAAG,KAAK;MAC9B;IACJ;IACA,IAAIE,YAAY,GAAG,IAAI,CAACA,YAAY,IAAI,CAAC,CAAC;IAC1C,IAAIC,cAAc,GAAGD,YAAY,CAACE,OAAO,KAAKlJ,OAAO,CAACmJ,SAAS,IAAIH,YAAY,CAACI,GAAG,KAAKnJ,GAAG,CAACkJ,SAAS;IACrG,IAAIE,WAAW,GAAGL,YAAY,CAACE,OAAO,KAAKlJ,OAAO,CAACsJ,MAAM,IAAIN,YAAY,CAACI,GAAG,KAAKnJ,GAAG,CAACqJ,MAAM;IAC5F,IAAIC,uBAAuB,GAAG,IAAI,CAACnF,eAAe;IAClD,IAAI,IAAI,CAACN,OAAO,CAACV,cAAc,IAC3BmG,uBAAuB,KAAK7I,wBAAwB,CAAC2G,KAAK,IAC1D,CAACgC,WAAW,IAAI,CAACJ,cAAc,EAAE;MACjC,IAAI,CAACtD,iBAAiB,GAAG,IAAI;IACjC;IACA,IAAI,IAAI,CAAC7B,OAAO,CAACV,cAAc,EAAE;MAC7B,IAAI,CAACgB,eAAe,GAAG1D,wBAAwB,CAAC2G,KAAK;IACzD,CAAC,MACI;MACD,IAAI,CAACjD,eAAe,GAAG1D,wBAAwB,CAAC8I,SAAS;IAC7D;IACA,IAAIC,QAAQ,GAAG,IAAI,CAACrC,aAAa,CAAC,CAAC;IACnC,IAAIqC,QAAQ,IAAI,IAAI,CAACT,YAAY,CAACI,GAAG,KAAKnJ,GAAG,CAACyJ,KAAK,EAAE;MACjD;MACA,IAAI,CAACC,8BAA8B,CAAC,CAAC;MACrC;IACJ;IACA,IAAIC,oBAAoB,GAAG,IAAI,CAAC3F,UAAU,IAAI,IAAI,CAACA,UAAU,CAACa,QAAQ,CAAC,CAAC;IACxE,IAAI+E,YAAY,GAAG,IAAI,CAAC5F,UAAU,GAAG,IAAI,CAACA,UAAU,CAACiB,KAAK,GAAG,IAAI;IACjE,IAAIvF,EAAE,GAAG,IAAI,CAACsE,UAAU,CAAC6F,gBAAgB,CAAC,CAAC;MAAE5F,WAAW,GAAGvE,EAAE,CAACoK,IAAI;MAAE5F,aAAa,GAAGxE,EAAE,CAACuC,MAAM;IAC7F,IAAI,CAACiC,aAAa,GAAGA,aAAa;IAClC,IAAI6F,OAAO,GAAG,EAAE;IAChB,IAAIP,QAAQ,EAAE;MACV,IAAIR,cAAc,IAAII,WAAW,EAAE;QAC/BW,OAAO,GAAG,IAAI,CAACC,oBAAoB;MACvC,CAAC,MACI,IAAIV,uBAAuB,KAAK7I,wBAAwB,CAAC2G,KAAK,EAAE;QACjE2C,OAAO,GAAG,IAAI,CAACC,oBAAoB;MACvC,CAAC,MACI;QACDD,OAAO,GAAG9F,WAAW;MACzB;IACJ,CAAC,MACI;MACD8F,OAAO,GAAG9F,WAAW;IACzB;IACA,IAAIgG,OAAO,GAAG,IAAI,CAACrB,YAAY;IAC/B,IAAIsB,IAAI,GAAGpK,yBAAyB,CAAC;MACjCiK,OAAO,EAAEA,OAAO;MAChBE,OAAO,EAAEA,OAAO;MAChBE,aAAa,EAAE,IAAI,CAACjG,aAAa;MACjC8D,cAAc,EAAE,IAAI,CAACH,SAAS,CAACvD,KAAK;MACpC6C,aAAa,EAAEqC,QAAQ;MACvBY,QAAQ,EAAE,IAAI,CAACrB;IACnB,CAAC,CAAC;IACF,IAAImB,IAAI,IAAIA,IAAI,CAACG,MAAM,IAAIH,IAAI,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKtJ,SAAS,CAAC0J,eAAe,EAAE;MAC5E,IAAI,CAAC1C,+BAA+B,GAAG,KAAK;IAChD;IACA,IAAI4B,QAAQ,KAAK,CAACU,IAAI,IAAIA,IAAI,CAACG,MAAM,KAAK,CAAC,CAAC,EAAE;MAC1C,IAAI,CAACX,8BAA8B,CAAC,CAAC;MACrC;IACJ,CAAC,MACI,IAAIF,QAAQ,IAAIU,IAAI,CAACG,MAAM,KAAK,CAAC,EAAE;MACpC,IAAI,CAACH,IAAI,CAAC,CAAC,CAAC,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACzB,IAAI,CAACR,8BAA8B,CAAC,CAAC;QACrC;MACJ,CAAC,MACI,IAAIF,QAAQ,IAAIU,IAAI,CAAC,CAAC,CAAC,KACvBA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKtJ,SAAS,CAAC0J,eAAe,IAAIJ,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKtJ,SAAS,CAAC0J,eAAe,CAAC,EAAE;QACxF,IAAI,CAACZ,8BAA8B,CAAC,CAAC;QACrC;MACJ;IACJ;IACA,IAAIa,cAAc,GAAIL,IAAI,CAACG,MAAM,KAAK,CAAC,IAAIH,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKtJ,SAAS,CAAC0J,eAAgB;IACpF,IAAIE,iBAAiB,GAAG,EAAE;IAC1B,IAAIC,UAAU,GAAG,KAAK;IACtB,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAI,CAACH,cAAc,EAAE;MACjB,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,IAAI,CAACG,MAAM,EAAEM,CAAC,EAAE,EAAE;QAClC,IAAIC,eAAe,GAAG,IAAI,CAAC5G,UAAU,CAAC6G,SAAS,CAAC;UAC5CC,MAAM,EAAEZ,IAAI,CAACS,CAAC,CAAC,CAAC,CAAC,CAAC;UAClBI,WAAW,EAAEb,IAAI,CAACS,CAAC,CAAC,CAAC,CAAC,CAAC;UACvBjF,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;UACzCsF,iBAAiB,EAAE,CAAC,IAAI,CAAC7D,aAAa,CAAC,CAAC;UACxC8D,YAAY,EAAE,IAAI,CAACrH,OAAO,CAACqB,KAAK;UAChCiG,UAAU,EAAElC,cAAc,IAAII,WAAW;UACzC+B,cAAc,EAAE,IAAI,CAACjH;QACzB,CAAC,CAAC;QACFsG,iBAAiB,CAACY,IAAI,CAACR,eAAe,CAAC;QACvC,IAAI,CAACA,eAAe,CAAC3F,KAAK,EAAE;UACxByF,KAAK,GAAG;YAAEW,IAAI,EAAE;UAAQ,CAAC;QAC7B;QACAZ,UAAU,GAAGG,eAAe,CAACU,YAAY;MAC7C;IACJ;IACA,IAAI,CAAC,IAAI,CAACzH,OAAO,CAACT,eAAe,EAAE;MAC/BqH,UAAU,GAAG,KAAK;IACtB;IACA,IAAI,CAAC/E,iBAAiB,GAAG,KAAK;IAC9B,IAAI6F,cAAc,GAAG,IAAI,CAAC1H,OAAO,CAAC5B,MAAM,KAAK,IAAI,CAACiC,aAAa;IAC3D;IACChE,SAAS,CAAC,IAAI,CAAC2D,OAAO,CAAC5B,MAAM,CAAC,IAAI,IAAI,CAAC4B,OAAO,CAAC5B,MAAM,CAACoI,MAAM,GAAG,CAAE;IACtE,IAAImB,eAAe,GAAGhB,iBAAiB,CAACA,iBAAiB,CAACH,MAAM,GAAG,CAAC,CAAC;IACrE,IAAIoB,yBAAyB,GAAGD,eAAe,IAAI,CAACtL,SAAS,CAACsL,eAAe,CAACvG,KAAK,CAAC;IACpF,IAAIyG,qBAAqB,GAAIlC,QAAQ,KAAKR,cAAc,IAAII,WAAW,CAAC,IAAIqC,yBAA0B;IACtG,IAAIE,SAAS,GAAGH,eAAe,GAAGA,eAAe,CAACG,SAAS,GAAG,KAAK;IACnE,IAAIC,oBAAoB,GAAG,IAAI,CAAC5H,UAAU,CAACa,QAAQ,CAAC,CAAC;IACrD,IAAIgH,2BAA2B,GAAG,CAACnL,OAAO,CAACiJ,oBAAoB,EAAEiC,oBAAoB,CAAC;IACtF,IAAIE,YAAY,GAAG,IAAI,CAAC9H,UAAU,CAACiB,KAAK;IACxC,IAAI8G,kBAAkB;IACtB,IAAIC,gBAAgB,GAAG,IAAI,CAACnE,SAAS;IACrC,IAAI2B,QAAQ,EAAE;MACV,IAAIyC,QAAQ,GAAG/B,IAAI,IAAIA,IAAI,CAACG,MAAM,GAAG,CAAC,GAAGH,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;MAC1D,IAAIgC,cAAc,GAAG,IAAI,CAAClI,UAAU,CAACmI,cAAc,CAAC,CAAC,CAACF,QAAQ,CAAC;MAC/D,IAAI/B,IAAI,CAACG,MAAM,IAAIH,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKtJ,SAAS,CAAC0J,eAAe,EAAE;QACzD,IAAIG,UAAU,EAAE;UACZ,IAAI,CAAC2B,wBAAwB,CAAC,CAAC;UAC/B,IAAI,CAACC,iBAAiB,CAAC,CAAC,CAAC;QAC7B,CAAC,MACI,IAAIV,SAAS,EAAE;UAChBI,kBAAkB,GAAG,IAAI,CAAC7H,aAAa,CAAC8H,gBAAgB,CAAC1H,KAAK,CAAC;UAC/D,IAAIyH,kBAAkB,EAAE;YACpB,IAAI,CAACpG,WAAW,CAAC,CAAC;YAClB,IAAI,CAAC4C,YAAY,CAAC,IAAI,CAAC+D,iBAAiB,CAACP,kBAAkB,CAAC,CAAC;UACjE,CAAC,MACI;YACD,IAAI,CAACrC,8BAA8B,CAAC,CAAC;UACzC;QACJ,CAAC,MACI,IAAIgC,qBAAqB,EAAE;UAC5B,IAAI,CAAC/F,WAAW,CAAC,CAAC;UAClB,IAAIuE,IAAI,CAACG,MAAM,IAAIH,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKtJ,SAAS,CAAC0J,eAAe,EAAE;YACzD,IAAI,CAAC/B,YAAY,CAAC,IAAI,CAAC+D,iBAAiB,CAACpC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD;QACJ,CAAC,MACI,IAAIuB,yBAAyB,EAAE;UAChC,IAAIlE,CAAC,CAACgF,IAAI,KAAK,GAAG,IAAIL,cAAc,EAAE;YAClC;YACA;UAAA,CACH,MACI,IAAIhM,SAAS,CAACyJ,oBAAoB,CAAC,IAAI,CAACzJ,SAAS,CAAC0L,oBAAoB,CAAC,EAAE;YAC1E,IAAI,CAAClC,8BAA8B,CAAC,CAAC;UACzC,CAAC,MACI,IAAI,CAACxJ,SAAS,CAACyJ,oBAAoB,CAAC,IAAIzJ,SAAS,CAAC0L,oBAAoB,CAAC,EAAE;YAC1E,IAAI,CAACQ,wBAAwB,CAAC,CAAC;UACnC,CAAC,MACI,IAAIlM,SAAS,CAACyJ,oBAAoB,CAAC,IAAIzJ,SAAS,CAAC0L,oBAAoB,CAAC,EAAE;YACzE,IAAIC,2BAA2B,EAAE;cAC7B,IAAI,CAACO,wBAAwB,CAAC,CAAC;YACnC,CAAC,MACI;cACD,IAAI,CAAC1C,8BAA8B,CAAC,CAAC;YACzC;UACJ,CAAC,MACI,IAAI,CAACxJ,SAAS,CAACyJ,oBAAoB,CAAC,IAAI,CAACzJ,SAAS,CAAC0L,oBAAoB,CAAC,EAAE;YAC3E,IAAI,CAACQ,wBAAwB,CAAC,CAAC;UACnC,CAAC,MACI,IAAIxC,YAAY,KAAKkC,YAAY,EAAE;YACpC;UAAA,CACH,MACI;YACD,IAAI,CAACpC,8BAA8B,CAAC,CAAC;UACzC;QACJ,CAAC,MACI,IAAI,CAAC+B,yBAAyB,EAAE;UACjC;UACA;UACA,IAAI,CAACF,cAAc,EAAE;YACjB,IAAI,CAACa,wBAAwB,CAAC,CAAC;UACnC;QACJ;MACJ,CAAC,MACI;QACD,IAAI,CAAC,IAAI,CAACvI,OAAO,CAACT,eAAe,IAAI8G,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKtJ,SAAS,CAAC0J,eAAe,EAAE;UAC3E;UACA;QAAA,CACH,MACI;UACD,IAAI,CAAC/B,YAAY,CAAC,IAAI,CAAC+D,iBAAiB,CAACpC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD;MACJ;IACJ,CAAC,MACI,IAAI,CAACV,QAAQ,EAAE;MAChB,IAAI,CAAC7D,WAAW,CAAC,CAAC;MAClB,IAAIuE,IAAI,CAACG,MAAM,IAAIH,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKtJ,SAAS,CAAC0J,eAAe,EAAE;QACzD,IAAI,CAAC/B,YAAY,CAAC,IAAI,CAAC+D,iBAAiB,CAACpC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzD;MACA,IAAI,IAAI,CAACrG,OAAO,CAACT,eAAe,EAAE;QAC9B,IAAImH,cAAc,EAAE;UAChB,IAAI,CAAC7E,iBAAiB,GAAG,IAAI;UAC7B,IAAI,CAACkC,+BAA+B,EAAE;YAClC,IAAI,CAACyE,iBAAiB,CAAC,CAAC,CAAC;UAC7B;UACA,IAAI,CAACzE,+BAA+B,GAAG,IAAI;QAC/C,CAAC,MACI,IAAI6C,UAAU,EAAE;UACjB,IAAI,CAAC4B,iBAAiB,CAAC,CAAC,CAAC;UACzB,IAAI,CAACzE,+BAA+B,GAAG,IAAI;QAC/C;MACJ,CAAC,MACI;QACD,IAAI4D,eAAe,IAAIA,eAAe,CAACF,YAAY,EAAE;UACjD;UACA;UACA,IAAI,CAAC5F,iBAAiB,GAAG,IAAI;QACjC,CAAC,MACI,IAAI6E,cAAc,EAAE;UACrB,IAAI,CAAC7E,iBAAiB,GAAG,IAAI;UAC7B,IAAI,CAACkC,+BAA+B,EAAE;YAClC,IAAI,CAACyE,iBAAiB,CAAC,CAAC,CAAC;UAC7B;UACA,IAAI,CAACzE,+BAA+B,GAAG,IAAI;QAC/C;MACJ;MACA,IAAIoB,cAAc,IAAI,IAAI,CAACnF,OAAO,CAACZ,gCAAgC,EAAE;QACjE;QACA,IAAI,CAACoJ,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAC9B;IACJ;IACA,IAAI,CAACG,qBAAqB,CAAC;MACvBC,QAAQ,EAAE9C,oBAAoB;MAC9BjB,KAAK,EAAEnB;IACX,CAAC,CAAC;IACF,IAAI,CAACmF,eAAe,CAAC;MAAEhE,KAAK,EAAEnB,CAAC;MAAEmD,KAAK,EAAEA,KAAK;MAAE/B,eAAe,EAAEA,eAAe;MAAEgE,eAAe,EAAE,IAAI,CAAC/D;IAAa,CAAC,CAAC;IACtH,IAAIY,QAAQ,EAAE;MACV;MACA;MACA;MACA,IAAI,CAAChE,gBAAgB,CAAC,CAAC;IAC3B;EACJ,CAAC;EACD;AACJ;AACA;EACI9B,SAAS,CAACiB,SAAS,CAAC4B,cAAc,GAAG,UAAUgB,CAAC,EAAE;IAC9C,IAAI,IAAI,CAACqF,YAAY,CAAC;MAAElE,KAAK,EAAEnB;IAAE,CAAC,CAAC,EAAE;MACjC;IACJ;IACA,IAAI,CAACI,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACxD,eAAe,GAAG1D,wBAAwB,CAAC2D,IAAI;IACpD,IAAI,CAACwD,+BAA+B,GAAG,KAAK;IAC5C,IAAI,CAACiF,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAAC,IAAI,CAACpF,gBAAgB,EAAE;MACxB,IAAI,CAACa,KAAK,CAAC,CAAC,EAAE,IAAI,CAACM,YAAY,CAACyB,MAAM,CAAC;IAC3C;IACA,IAAI,CAAC5C,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACqF,eAAe,CAAC;MAAEpE,KAAK,EAAEnB;IAAE,CAAC,CAAC;EACtC,CAAC;EACD;AACJ;AACA;EACI7D,SAAS,CAACiB,SAAS,CAAC6B,aAAa,GAAG,UAAUe,CAAC,EAAE;IAC7C,IAAI,CAAC7B,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACiC,QAAQ,GAAG,KAAK;IACrB,IAAI,IAAI,CAACoF,WAAW,CAAC;MAAErE,KAAK,EAAEnB;IAAE,CAAC,CAAC,EAAE;MAChC;IACJ;IACA,IAAI,IAAI,CAAC1D,OAAO,CAACL,QAAQ,EAAE;MACvB,IAAI,CAACA,QAAQ,CAAC,CAAC;IACnB;IACA,IAAI,CAACW,eAAe,GAAG1D,wBAAwB,CAAC2D,IAAI;IACpD,IAAI,CAACwD,+BAA+B,GAAG,KAAK;IAC5C,IAAI,CAACiF,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAACG,cAAc,CAAC;MAAEtE,KAAK,EAAEnB;IAAE,CAAC,CAAC;EACrC,CAAC;EACD;AACJ;AACA;EACI7D,SAAS,CAACiB,SAAS,CAAC8B,eAAe,GAAG,UAAUc,CAAC,EAAE;IAC/C,IAAI,CAAC0F,aAAa,CAAC;MAAEvE,KAAK,EAAEnB;IAAE,CAAC,CAAC;EACpC,CAAC;EACD;AACJ;AACA;EACI7D,SAAS,CAACiB,SAAS,CAAC2B,gBAAgB,GAAG,UAAUiB,CAAC,EAAE;IAChD,IAAI,IAAI,CAAC2F,cAAc,CAAC;MAAExE,KAAK,EAAEnB;IAAE,CAAC,CAAC,EAAE;MACnC;IACJ;IACA,IAAI7H,EAAE,GAAG,IAAI,CAACmI,SAAS;MAAEvD,KAAK,GAAG5E,EAAE,CAAC4E,KAAK;MAAEC,GAAG,GAAG7E,EAAE,CAAC6E,GAAG;IACvD,IAAImE,KAAK,GAAGnB,CAAC;IACb,IAAI,CAACwB,YAAY,GAAGxB,CAAC;IACrB,IAAI,CAACyC,oBAAoB,GAAG,IAAI,CAACpG,OAAO,CAACqB,KAAK;IAC9C,IAAI,CAACZ,wBAAwB,GAAG;MAAEC,KAAK,EAAEA,KAAK;MAAEC,GAAG,EAAEA;IAAI,CAAC;IAC1D,IAAI,IAAI,CAAC4I,6BAA6B,CAAC5F,CAAC,CAAC,EAAE;MACvC,IAAI6F,QAAQ,GAAG7F,CAAC,CAAC0B,OAAO,KAAKlJ,OAAO,CAACsN,GAAG;MACxC,IAAID,QAAQ,EAAE;QACV,IAAIE,EAAE,GAAG,IAAI,CAACzF,SAAS;UAAEG,cAAc,GAAGsF,EAAE,CAAChJ,KAAK;UAAE2D,YAAY,GAAGqF,EAAE,CAAC/I,GAAG;QACzE,IAAIgD,CAAC,CAACgG,QAAQ,IAAIH,QAAQ,EAAE;UACxB,IAAI,CAACf,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,MACI;UACD,IAAI,CAACA,iBAAiB,CAAC,CAAC,CAAC;QAC7B;QACA,IAAIrE,cAAc,KAAK,IAAI,CAACH,SAAS,CAACvD,KAAK,IAAI2D,YAAY,KAAK,IAAI,CAACJ,SAAS,CAACtD,GAAG,EAAE;UAChF;UACAgD,CAAC,CAACC,cAAc,CAAC,CAAC;UAClB;QACJ;MACJ,CAAC,MACI;QACD;QACAD,CAAC,CAACC,cAAc,CAAC,CAAC;QAClB,IAAI,CAAC6E,iBAAiB,CAAC,CAAC,CAAC;QACzB;MACJ;IACJ;IACA,IAAIvB,MAAM,GAAG,IAAI,CAAC5G,aAAa,CAAC,IAAI,CAAC2D,SAAS,CAACvD,KAAK,CAAC;IACrD,IAAIkJ,IAAI,GAAG,IAAI,CAACC,iBAAiB,CAAC3C,MAAM,CAAC;IACzC,IAAI4C,oBAAoB,GAAG,KAAK;IAChC,IAAI/E,eAAe,GAAG,IAAI,CAACC,YAAY;IACvC,IAAIrB,CAAC,CAACoG,MAAM,IAAIpG,CAAC,CAACqG,OAAO,IAAIrG,CAAC,CAACsG,OAAO,IAAItG,CAAC,CAAC0B,OAAO,KAAKlJ,OAAO,CAACsN,GAAG,EAAE;MACjE;IACJ;IACA,QAAQ9F,CAAC,CAAC0B,OAAO;MACb,KAAKlJ,OAAO,CAAC+N,UAAU;QACnB,IAAI,CAACzB,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAC1BqB,oBAAoB,GAAG,IAAI;QAC3B,IAAI,CAAC9F,+BAA+B,GAAG,KAAK;QAC5C;MACJ,KAAK7H,OAAO,CAACgO,QAAQ;QACjB,IAAI,CAACC,sBAAsB,CAACR,IAAI,EAAE1C,MAAM,EAAEpC,KAAK,CAAC;QAChD,IAAIC,eAAe,KAAK,IAAI,CAACC,YAAY,EAAE;UACvC,IAAI,CAAC8D,eAAe,CAAC;YAAEhE,KAAK,EAAEnB,CAAC;YAAEmD,KAAK,EAAE,IAAI;YAAEiC,eAAe,EAAE,IAAI,CAAC/D,YAAY;YAAED,eAAe,EAAEA;UAAgB,CAAC,CAAC;QACzH;QACA+E,oBAAoB,GAAG,IAAI;QAC3B,IAAI,CAAC9F,+BAA+B,GAAG,KAAK;QAC5C;MACJ,KAAK7H,OAAO,CAACkO,WAAW;QACpB,IAAI,CAAC5B,iBAAiB,CAAC,CAAC,CAAC;QACzBqB,oBAAoB,GAAG,IAAI;QAC3B,IAAI,CAAC9F,+BAA+B,GAAG,KAAK;QAC5C;MACJ,KAAK7H,OAAO,CAACmO,UAAU;QACnB,IAAI,CAACF,sBAAsB,CAAC,CAACR,IAAI,EAAE1C,MAAM,EAAEpC,KAAK,CAAC;QACjD,IAAIC,eAAe,KAAK,IAAI,CAACC,YAAY,EAAE;UACvC,IAAI,CAAC8D,eAAe,CAAC;YAAEhE,KAAK,EAAEnB,CAAC;YAAEmD,KAAK,EAAE,IAAI;YAAEiC,eAAe,EAAE,IAAI,CAAC/D,YAAY;YAAED,eAAe,EAAEA;UAAgB,CAAC,CAAC;QACzH;QACA+E,oBAAoB,GAAG,IAAI;QAC3B,IAAI,CAAC9F,+BAA+B,GAAG,KAAK;QAC5C;MACJ,KAAK7H,OAAO,CAACoO,KAAK;QACd;QACA;MACJ,KAAKpO,OAAO,CAACqO,IAAI;QACb,IAAI,CAAC9G,oBAAoB,CAAC,CAAC,CAAC;QAC5BoG,oBAAoB,GAAG,IAAI;QAC3B,IAAI,CAAC9F,+BAA+B,GAAG,KAAK;QAC5C,IAAI,CAAClC,iBAAiB,GAAG,IAAI;QAC7B;MACJ,KAAK3F,OAAO,CAACsO,GAAG;QACZ,IAAI,CAAC/G,oBAAoB,CAAC,IAAI,CAACsB,YAAY,CAACyB,MAAM,CAAC;QACnDqD,oBAAoB,GAAG,IAAI;QAC3B,IAAI,CAAC9F,+BAA+B,GAAG,KAAK;QAC5C,IAAI,CAAClC,iBAAiB,GAAG,IAAI;QAC7B;MACJ;QACI;QACA;IACR;IACA,IAAIgI,oBAAoB,EAAE;MACtBnG,CAAC,CAACC,cAAc,CAAC,CAAC;IACtB;EACJ,CAAC;EACD;AACJ;AACA;EACI9D,SAAS,CAACiB,SAAS,CAAC+B,cAAc,GAAG,YAAY;IAC7C,IAAI,CAACmC,iBAAiB,GAAG,IAAI;EACjC,CAAC;EACD;AACJ;AACA;EACInF,SAAS,CAACiB,SAAS,CAACgC,mBAAmB,GAAG,UAAUY,CAAC,EAAE;IACnD,IAAIoB,eAAe,GAAG,IAAI,CAACC,YAAY;IACvC,IAAI,CAAC,IAAI,CAAC/E,OAAO,CAACX,gBAAgB,IAAI,IAAI,CAACoL,iBAAiB,CAAC;MAAE5F,KAAK,EAAEnB;IAAE,CAAC,CAAC,EAAE;MACxE;IACJ;IACA,IAAI,CAAC,IAAI,CAACI,QAAQ,EAAE;MAChB;IACJ;IACA,IAAIe,KAAK,GAAGnB,CAAC;IACb,IAAImB,KAAK,CAAC6E,QAAQ,EAAE;MAChB,IAAI,CAAClB,iBAAiB,CAAC,CAAC3D,KAAK,CAAC6F,UAAU,IAAI,CAAC7F,KAAK,CAACZ,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IAC5E,CAAC,MACI;MACD,IAAI,CAACkG,sBAAsB,CAAC,CAACtF,KAAK,CAAC6F,UAAU,IAAI,CAAC7F,KAAK,CAACZ,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACjF;IACAY,KAAK,CAAC8F,WAAW,GAAG,KAAK;IACzB,IAAI9F,KAAK,CAAClB,cAAc,EAAE;MACtBkB,KAAK,CAAClB,cAAc,CAAC,CAAC;IAC1B;IACA,IAAImB,eAAe,KAAK,IAAI,CAACC,YAAY,EAAE;MACvC,IAAI,CAAC8D,eAAe,CAAC;QAAEhE,KAAK,EAAEnB,CAAC;QAAEmD,KAAK,EAAE,IAAI;QAAEiC,eAAe,EAAE,IAAI,CAAC/D,YAAY;QAAED,eAAe,EAAEA;MAAgB,CAAC,CAAC;IACzH;EACJ,CAAC;EACDjF,SAAS,CAACiB,SAAS,CAACmE,aAAa,GAAG,UAAUvB,CAAC,EAAE;IAC7C,IAAItC,KAAK,GAAG,IAAI,CAACG,IAAI,CAACqJ,SAAS,CAAC,IAAI,CAAC7F,YAAY,EAAE,IAAI,CAAC8F,WAAW,CAAC,IAAI,IAAI,CAACzJ,KAAK;IAClF,IAAI/E,SAAS,CAAC+E,KAAK,CAAC,IAAI,IAAI,CAACjB,UAAU,CAAC2K,sBAAsB,CAAC,CAAC,EAAE;MAC9D1J,KAAK,GAAG,IAAI,CAACjB,UAAU,CAAC4K,gBAAgB,CAAC3J,KAAK,CAAC;IACnD;IACA,IAAI4J,kBAAkB,GAAG,IAAI,CAAC7K,UAAU,IAAI,IAAI,CAACA,UAAU,CAACa,QAAQ,CAAC,CAAC;IACtE,IAAI,CAACiK,UAAU,CAAC7J,KAAK,CAAC;IACtB,IAAI,CAACuH,qBAAqB,CAAC;MACvBC,QAAQ,EAAEoC,kBAAkB;MAC5BnG,KAAK,EAAEnB;IACX,CAAC,CAAC;EACN,CAAC;EACD9C,MAAM,CAACC,cAAc,CAAChB,SAAS,CAACiB,SAAS,EAAE,cAAc,EAAE;IACvDC,GAAG,EAAE,SAAAA,CAAA,EAAY;MACb,OAAO,CAAC,IAAI,CAAChB,OAAO,IAAI,CAAC,CAAC,EAAEqB,KAAK,IAAI,EAAE;IAC3C,CAAC;IACDH,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE;EAClB,CAAC,CAAC;EACFN,MAAM,CAACC,cAAc,CAAChB,SAAS,CAACiB,SAAS,EAAE,aAAa,EAAE;IACtDC,GAAG,EAAE,SAAAA,CAAA,EAAY;MACb,IAAI,CAAC,IAAI,CAACf,OAAO,CAAC5B,MAAM,EAAE;QACtB,OAAOrB,SAAS,CAACmO,iBAAiB;MACtC;MACA,IAAI,OAAO,IAAI,CAAClL,OAAO,CAAC5B,MAAM,KAAK,QAAQ,EAAE;QACzC,OAAO,IAAI,CAAC4B,OAAO,CAAC5B,MAAM;MAC9B,CAAC,MACI;QACD,OAAO,IAAI,CAAC4B,OAAO,CAAC5B,MAAM,CAACyM,WAAW;MAC1C;IACJ,CAAC;IACD5J,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE;EAClB,CAAC,CAAC;EACFN,MAAM,CAACC,cAAc,CAAChB,SAAS,CAACiB,SAAS,EAAE,eAAe,EAAE;IACxDC,GAAG,EAAE,SAAAA,CAAA,EAAY;MACb,IAAI,CAAC,IAAI,CAACf,OAAO,CAAC5B,MAAM,EAAE;QACtB,OAAOrB,SAAS,CAACmO,iBAAiB;MACtC;MACA,IAAI,OAAO,IAAI,CAAClL,OAAO,CAAC5B,MAAM,KAAK,QAAQ,EAAE;QACzC,OAAO,IAAI,CAAC4B,OAAO,CAAC5B,MAAM;MAC9B,CAAC,MACI;QACD,OAAO,IAAI,CAAC4B,OAAO,CAAC5B,MAAM,CAAC+M,aAAa;MAC5C;IACJ,CAAC;IACDlK,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE;EAClB,CAAC,CAAC;EACFN,MAAM,CAACC,cAAc,CAAChB,SAAS,CAACiB,SAAS,EAAE,WAAW,EAAE;IACpDC,GAAG,EAAE,SAAAA,CAAA,EAAY;MACb,IAAI4J,WAAW,GAAG;QAAElK,KAAK,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAE,CAAC;MACtC,IAAI,IAAI,CAACX,OAAO,KAAK,IAAI,IAAI,IAAI,CAACA,OAAO,CAACoE,cAAc,KAAKiH,SAAS,EAAE;QACpET,WAAW,GAAG;UACVlK,KAAK,EAAE,IAAI,CAACV,OAAO,CAACoE,cAAc;UAClCzD,GAAG,EAAE,IAAI,CAACX,OAAO,CAACqE;QACtB,CAAC;MACL;MACA,OAAOuG,WAAW;IACtB,CAAC;IACD1J,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE;EAClB,CAAC,CAAC;EACFrB,SAAS,CAACiB,SAAS,CAAC4D,YAAY,GAAG,UAAUV,SAAS,EAAE;IACpD,IAAI,IAAI,CAACjE,OAAO,IAAIsL,QAAQ,CAACC,aAAa,KAAK,IAAI,CAACvL,OAAO,EAAE;MACzD,IAAI,CAACA,OAAO,CAACwL,iBAAiB,CAACvH,SAAS,CAACvD,KAAK,EAAEuD,SAAS,CAACtD,GAAG,CAAC;MAC9D,IAAIpE,mBAAmB,CAAC,CAAC,IAAII,KAAK,CAAC,CAAC,EAAE;QAClC,IAAI,CAACqD,OAAO,CAACyL,cAAc,CAAC;UAAEC,KAAK,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAU,CAAC,CAAC;MACxE;MACA,IAAI1H,SAAS,CAACvD,KAAK,KAAKuD,SAAS,CAACtD,GAAG,EAAE;QACnC,IAAI,CAACJ,eAAe,GAAG1D,wBAAwB,CAAC8I,SAAS;MAC7D;IACJ;EACJ,CAAC;EACD;AACJ;AACA;EACI7F,SAAS,CAACiB,SAAS,CAAC2H,iBAAiB,GAAG,UAAUxB,MAAM,EAAE;IACtD,IAAIxG,KAAK,GAAG,CAAC,CAAC;IACd,IAAIC,GAAG,GAAG,CAAC;IACX,KAAK,IAAIoG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACzG,aAAa,CAACmG,MAAM,EAAEM,CAAC,EAAE,EAAE;MAChD,IAAI,IAAI,CAACzG,aAAa,CAACyG,CAAC,CAAC,KAAKG,MAAM,EAAE;QAClCvG,GAAG,GAAGoG,CAAC,GAAG,CAAC;QACX,IAAIrG,KAAK,KAAK,CAAC,CAAC,EAAE;UACdA,KAAK,GAAGqG,CAAC;QACb;MACJ;IACJ;IACA,IAAIrG,KAAK,GAAG,CAAC,EAAE;MACXA,KAAK,GAAG,CAAC;IACb;IACA,IAAI,CAAC,IAAI,CAACT,OAAO,CAACN,gBAAgB,IAAI,IAAI,CAACW,aAAa,CAACmG,MAAM,KAAK,IAAI,CAACpG,WAAW,CAACoG,MAAM,EAAE;MACzF,IAAI,IAAI,CAACnG,aAAa,CAACmG,MAAM,GAAG,IAAI,CAACpG,WAAW,CAACoG,MAAM,EAAE;QACrD9F,GAAG,IAAI,IAAI,CAACN,WAAW,CAACoG,MAAM,GAAG,IAAI,CAACnG,aAAa,CAACmG,MAAM;MAC9D,CAAC,MACI;QACD9F,GAAG,GAAGiL,IAAI,CAACC,GAAG,CAAC,CAAC,EAAElL,GAAG,IAAI,IAAI,CAACL,aAAa,CAACmG,MAAM,GAAG,IAAI,CAACpG,WAAW,CAACoG,MAAM,CAAC,CAAC;MAClF;IACJ;IACA,OAAO;MAAE/F,KAAK,EAAEA,KAAK;MAAEC,GAAG,EAAEA;IAAI,CAAC;EACrC,CAAC;EACD;AACJ;AACA;EACIb,SAAS,CAACiB,SAAS,CAAC6D,gBAAgB,GAAG,UAAUH,KAAK,EAAE;IACpD,IAAIR,SAAS,GAAG;MAAEvD,KAAK,EAAE+D,KAAK;MAAE9D,GAAG,EAAE8D;IAAM,CAAC;IAC5C,KAAK,IAAIsC,CAAC,GAAGtC,KAAK,EAAEqH,CAAC,GAAGrH,KAAK,GAAG,CAAC,EAAEsC,CAAC,GAAG,IAAI,CAACzG,aAAa,CAACmG,MAAM,IAAIqF,CAAC,IAAI,CAAC,EAAE/E,CAAC,EAAE,EAAE+E,CAAC,EAAE,EAAE;MAClF,IAAI/E,CAAC,GAAG,IAAI,CAACzG,aAAa,CAACmG,MAAM,IAAI,IAAI,CAACnG,aAAa,CAACyG,CAAC,CAAC,KAAK/J,SAAS,CAAC0J,eAAe,EAAE;QACtFzC,SAAS,GAAG,IAAI,CAACyE,iBAAiB,CAAC,IAAI,CAACpI,aAAa,CAACyG,CAAC,CAAC,CAAC;QACzD;MACJ;MACA,IAAI+E,CAAC,IAAI,CAAC,IAAI,IAAI,CAACxL,aAAa,CAACwL,CAAC,CAAC,KAAK9O,SAAS,CAAC0J,eAAe,EAAE;QAC/DzC,SAAS,GAAG,IAAI,CAACyE,iBAAiB,CAAC,IAAI,CAACpI,aAAa,CAACwL,CAAC,CAAC,CAAC;QACzD;MACJ;IACJ;IACA,OAAO7H,SAAS;EACpB,CAAC;EACDnE,SAAS,CAACiB,SAAS,CAAC0H,iBAAiB,GAAG,UAAUsD,MAAM,EAAE;IACtD,IAAI9H,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9B,IAAI,IAAI,CAACV,aAAa,CAAC,CAAC,EAAE;MACtB,IAAI7C,KAAK,GAAGuD,SAAS,CAACvD,KAAK;MAC3B,IAAIsL,aAAa,GAAG,IAAI,CAAC1L,aAAa,CAACI,KAAK,GAAG,CAAC,CAAC;MACjD,IAAIwG,MAAM,GAAG,EAAE;MACf,IAAI+E,eAAe,GAAG,EAAE;MACxB,IAAIF,MAAM,GAAG,CAAC,EAAE;QACZ,KAAK,IAAIhF,CAAC,GAAGrG,KAAK,GAAGqL,MAAM,EAAEhF,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;UACtCkF,eAAe,GAAG,IAAI,CAAC3L,aAAa,CAACyG,CAAC,CAAC;UACvC,IAAIkF,eAAe,KAAKjP,SAAS,CAAC0J,eAAe,IAC7CuF,eAAe,KAAKD,aAAa,EAAE;YACnCtL,KAAK,GAAGqG,CAAC;YACTG,MAAM,GAAG+E,eAAe;YACxB;UACJ;QACJ;MACJ,CAAC,MACI;QACD,KAAK,IAAIlF,CAAC,GAAGrG,KAAK,GAAGqL,MAAM,EAAEhF,CAAC,GAAG,IAAI,CAACzG,aAAa,CAACmG,MAAM,EAAEM,CAAC,EAAE,EAAE;UAC7DkF,eAAe,GAAG,IAAI,CAAC3L,aAAa,CAACyG,CAAC,CAAC;UACvC,IAAIkF,eAAe,KAAKjP,SAAS,CAAC0J,eAAe,IAC7CuF,eAAe,KAAKD,aAAa,EAAE;YACnCtL,KAAK,GAAGqG,CAAC;YACTG,MAAM,GAAG+E,eAAe;YACxB;UACJ;QACJ;MACJ;MACA,IAAI/E,MAAM,EAAE;QACR,IAAI,CAACnF,WAAW,CAAC,CAAC;QAClB,IAAI,CAAC4C,YAAY,CAAC,IAAI,CAAC+D,iBAAiB,CAACxB,MAAM,CAAC,CAAC;QACjD,IAAI,CAAC3G,eAAe,GAAG1D,wBAAwB,CAAC8I,SAAS;QACzD;MACJ;IACJ;IACA,IAAI,CAACpF,eAAe,GAAG1D,wBAAwB,CAAC2D,IAAI;IACpD,IAAI1E,EAAE,GAAG,IAAI,CAACmI,SAAS;MAAEG,cAAc,GAAGtI,EAAE,CAAC4E,KAAK;MAAE2D,YAAY,GAAGvI,EAAE,CAAC6E,GAAG;IACzE,IAAIyD,cAAc,GAAGC,YAAY,IAC7B,IAAI,CAAC/D,aAAa,CAAC8D,cAAc,CAAC,KAAK,IAAI,CAAC9D,aAAa,CAAC+D,YAAY,GAAG,CAAC,CAAC,EAAE;MAC7E,IAAI,CAACM,YAAY,CAAC,IAAI,CAACC,gBAAgB,CAACmH,MAAM,GAAG,CAAC,GAAG3H,cAAc,GAAGC,YAAY,GAAG,CAAC,CAAC,CAAC;MACxF,IAAI,CAACvC,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACvB,eAAe,GAAG1D,wBAAwB,CAAC2D,IAAI;MACpD;IACJ;IACA,IAAI0L,oBAAoB,GAAG,IAAI,CAAC5L,aAAa,CAAC8D,cAAc,CAAC;IAC7D,IAAI+H,CAAC,GAAG/H,cAAc,GAAG2H,MAAM;IAC/B,OAAOI,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,IAAI,CAAC7L,aAAa,CAACmG,MAAM,EAAE;MAC3C,IAAI,IAAI,CAACnG,aAAa,CAAC6L,CAAC,CAAC,KAAKD,oBAAoB,IAC9C,IAAI,CAAC5L,aAAa,CAAC6L,CAAC,CAAC,KAAKnP,SAAS,CAAC0J,eAAe,EAAE;QACrD;MACJ;MACAyF,CAAC,IAAIJ,MAAM;IACf;IACA,IAAI,IAAI,CAACzL,aAAa,CAAC6L,CAAC,CAAC,KAAKnP,SAAS,CAAC0J,eAAe,EAAE;MACrD;MACA;IACJ;IACA,IAAI0F,CAAC,GAAGD,CAAC;IACT,OAAOC,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAG,IAAI,CAAC9L,aAAa,CAACmG,MAAM,EAAE;MAC5C,IAAI,IAAI,CAACnG,aAAa,CAAC8L,CAAC,CAAC,KAAK,IAAI,CAAC9L,aAAa,CAAC6L,CAAC,CAAC,EAAE;QACjD;MACJ;MACAC,CAAC,IAAIL,MAAM;IACf;IACA,IAAII,CAAC,GAAGC,CAAC,KAAKA,CAAC,GAAG,CAAC,KAAKhI,cAAc,IAAI+H,CAAC,GAAG,CAAC,KAAK9H,YAAY,CAAC,EAAE;MAC/D,IAAI,CAACM,YAAY,CAAC;QAAEjE,KAAK,EAAE0L,CAAC,GAAG,CAAC;QAAEzL,GAAG,EAAEwL,CAAC,GAAG;MAAE,CAAC,CAAC;MAC/C,IAAI,CAACrK,iBAAiB,GAAG,IAAI;IACjC,CAAC,MACI,IAAIqK,CAAC,GAAGC,CAAC,KAAKD,CAAC,KAAK/H,cAAc,IAAIgI,CAAC,KAAK/H,YAAY,CAAC,EAAE;MAC5D,IAAI,CAACM,YAAY,CAAC;QAAEjE,KAAK,EAAEyL,CAAC;QAAExL,GAAG,EAAEyL;MAAE,CAAC,CAAC;MACvC,IAAI,CAACtK,iBAAiB,GAAG,IAAI;IACjC;IACA,IAAI,CAACvB,eAAe,GAAG1D,wBAAwB,CAAC2D,IAAI;EACxD,CAAC;EACDV,SAAS,CAACiB,SAAS,CAACqJ,sBAAsB,GAAG,UAAU2B,MAAM,EAAE7E,MAAM,EAAEpC,KAAK,EAAE;IAC1E,IAAIoC,MAAM,KAAK,KAAK,CAAC,EAAE;MAAEA,MAAM,GAAG,EAAE;IAAE;IACtC,IAAIpC,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,CAAC,CAAC;IAAE;IACpC,IAAI,CAAC,IAAI,CAAC1E,UAAU,IAAI,IAAI,CAACH,OAAO,CAACoM,QAAQ,EAAE;MAC3C;IACJ;IACA,IAAIxD,QAAQ,GAAG,IAAI,CAACxH,KAAK;IACzB,IAAIuI,IAAI,GAAG3M,oBAAoB;IAC/B,IAAIyH,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC;IACxBwC,MAAM,GAAGA,MAAM,IAAI,IAAI,CAAC5G,aAAa,CAACoE,KAAK,CAAC,CAAC,CAAC,CAAC;IAC/C,IAAIwC,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAACjH,OAAO,CAACvB,KAAK,CAACC,WAAW,IAAI,IAAI,CAACsB,OAAO,CAACvB,KAAK,CAACC,WAAW,KAAK1B,oBAAoB,CAAC,EAAE;MAChH,IAAIqP,QAAQ,GAAG9P,yBAAyB,CAAC,IAAI,CAACsO,WAAW,CAAC;MAC1DlB,IAAI,GAAGnN,kBAAkB,CAAC6P,QAAQ,CAAC;IACvC;IACA,IAAI,CAAClM,UAAU,CAACmM,UAAU,CAACrF,MAAM,EAAE0C,IAAI,GAAGmC,MAAM,CAAC;IACjD,IAAI,CAACnD,qBAAqB,CAAC;MACvBC,QAAQ,EAAEA,QAAQ;MAClB/D,KAAK,EAAEA;IACX,CAAC,CAAC;IACF,IAAI,CAAC/C,WAAW,CAAC,CAAC;IAClB,IAAI,CAAC4C,YAAY,CAAC,IAAI,CAAC+D,iBAAiB,CAACxB,MAAM,CAAC,CAAC;EACrD,CAAC;EACD;AACJ;AACA;EACIpH,SAAS,CAACiB,SAAS,CAAC6H,qBAAqB,GAAG,UAAU4D,IAAI,EAAE;IACxD,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;MAAEA,IAAI,GAAG;QAAE3D,QAAQ,EAAE,IAAI;QAAE/D,KAAK,EAAE,CAAC;MAAE,CAAC;IAAE;IAC7D,IAAI,CAAChI,OAAO,CAAC,IAAI,CAACuE,KAAK,EAAEmL,IAAI,CAAC3D,QAAQ,CAAC,EAAE;MACrC,OAAO,IAAI,CAAC4D,kBAAkB,CAACD,IAAI,CAAC;IACxC;EACJ,CAAC;EACD;AACJ;AACA;EACI1M,SAAS,CAACiB,SAAS,CAAC0L,kBAAkB,GAAG,UAAUD,IAAI,EAAE;IACrD,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;MAAEA,IAAI,GAAG;QAAE3D,QAAQ,EAAE,IAAI;QAAE/D,KAAK,EAAE,CAAC;MAAE,CAAC;IAAE;IAC7D,OAAO,IAAI,CAAC4H,OAAO,CAAC3O,YAAY,EAAE1B,MAAM,CAACmQ,IAAI,EAAE;MAC3CnL,KAAK,EAAE,IAAI,CAACA;IAChB,CAAC,CAAC,CAAC;EACP,CAAC;EACD;AACJ;AACA;EACIvB,SAAS,CAACiB,SAAS,CAAC8D,YAAY,GAAG,UAAU2H,IAAI,EAAE;IAC/C,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;MAAEA,IAAI,GAAG;QAAE1H,KAAK,EAAE,CAAC;MAAE,CAAC;IAAE;IAC7C,OAAO,IAAI,CAAC4H,OAAO,CAAClP,KAAK,EAAEnB,MAAM,CAACmQ,IAAI,EAAE;MACpCnL,KAAK,EAAE,IAAI,CAACA;IAChB,CAAC,CAAC,CAAC;EACP,CAAC;EACD;AACJ;AACA;EACIvB,SAAS,CAACiB,SAAS,CAAC+H,eAAe,GAAG,UAAU0D,IAAI,EAAE;IAClD,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;MAAEA,IAAI,GAAG;QAAE1H,KAAK,EAAE,CAAC,CAAC;QAAEgC,KAAK,EAAE,IAAI;QAAE/B,eAAe,EAAE,EAAE;QAAEgE,eAAe,EAAE;MAAG,CAAC;IAAE;IACpG,OAAO,IAAI,CAAC2D,OAAO,CAAC1O,SAAS,EAAE3B,MAAM,CAACmQ,IAAI,EAAE;MACxCnL,KAAK,EAAE,IAAI,CAACA;IAChB,CAAC,CAAC,CAAC;EACP,CAAC;EACD;AACJ;AACA;EACIvB,SAAS,CAACiB,SAAS,CAACiI,YAAY,GAAG,UAAUwD,IAAI,EAAE;IAC/C,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;MAAEA,IAAI,GAAG;QAAE1H,KAAK,EAAE,CAAC;MAAE,CAAC;IAAE;IAC7C,OAAO,IAAI,CAAC4H,OAAO,CAAChP,KAAK,EAAErB,MAAM,CAAC,CAAC,CAAC,EAAEmQ,IAAI,CAAC,CAAC;EAChD,CAAC;EACD;AACJ;AACA;EACI1M,SAAS,CAACiB,SAAS,CAACmI,eAAe,GAAG,UAAUsD,IAAI,EAAE;IAClD,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;MAAEA,IAAI,GAAG;QAAE1H,KAAK,EAAE,CAAC;MAAE,CAAC;IAAE;IAC7C,OAAO,IAAI,CAAC4H,OAAO,CAACxO,SAAS,EAAE7B,MAAM,CAAC,CAAC,CAAC,EAAEmQ,IAAI,CAAC,CAAC;EACpD,CAAC;EACD;AACJ;AACA;EACI1M,SAAS,CAACiB,SAAS,CAACoI,WAAW,GAAG,UAAUqD,IAAI,EAAE;IAC9C,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;MAAEA,IAAI,GAAG;QAAE1H,KAAK,EAAE,CAAC;MAAE,CAAC;IAAE;IAC7C,OAAO,IAAI,CAAC4H,OAAO,CAAC/O,IAAI,EAAEtB,MAAM,CAAC,CAAC,CAAC,EAAEmQ,IAAI,CAAC,CAAC;EAC/C,CAAC;EACD;AACJ;AACA;EACI1M,SAAS,CAACiB,SAAS,CAACqI,cAAc,GAAG,UAAUoD,IAAI,EAAE;IACjD,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;MAAEA,IAAI,GAAG;QAAE1H,KAAK,EAAE,CAAC;MAAE,CAAC;IAAE;IAC7C,OAAO,IAAI,CAAC4H,OAAO,CAACzO,QAAQ,EAAE5B,MAAM,CAAC,CAAC,CAAC,EAAEmQ,IAAI,CAAC,CAAC;EACnD,CAAC;EACD;AACJ;AACA;EACI1M,SAAS,CAACiB,SAAS,CAACsI,aAAa,GAAG,UAAUmD,IAAI,EAAE;IAChD,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;MAAEA,IAAI,GAAG;QAAE1H,KAAK,EAAE,CAAC;MAAE,CAAC;IAAE;IAC7C,OAAO,IAAI,CAAC4H,OAAO,CAACvO,MAAM,EAAE9B,MAAM,CAACmQ,IAAI,EAAE;MACrCnL,KAAK,EAAE,IAAI,CAACA;IAChB,CAAC,CAAC,CAAC;EACP,CAAC;EACD;AACJ;AACA;EACIvB,SAAS,CAACiB,SAAS,CAACuI,cAAc,GAAG,UAAUkD,IAAI,EAAE;IACjD,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;MAAEA,IAAI,GAAG;QAAE1H,KAAK,EAAE,CAAC;MAAE,CAAC;IAAE;IAC7C,OAAO,IAAI,CAAC4H,OAAO,CAACjP,QAAQ,EAAEpB,MAAM,CAAC,CAAC,CAAC,EAAEmQ,IAAI,CAAC,CAAC;EACnD,CAAC;EACD;AACJ;AACA;EACI1M,SAAS,CAACiB,SAAS,CAAC2J,iBAAiB,GAAG,UAAU8B,IAAI,EAAE;IACpD,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;MAAEA,IAAI,GAAG;QAAE1H,KAAK,EAAE,CAAC;MAAE,CAAC;IAAE;IAC7C,OAAO,IAAI,CAAC4H,OAAO,CAAC5O,WAAW,EAAEzB,MAAM,CAAC,CAAC,CAAC,EAAEmQ,IAAI,CAAC,CAAC;EACtD,CAAC;EACD;AACJ;AACA;EACI1M,SAAS,CAACiB,SAAS,CAACgB,WAAW,GAAG,YAAY;IAC1C,IAAI,CAACH,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACqH,mBAAmB,CAAC,CAAC;EAC9B,CAAC;EACD;AACJ;AACA;EACInJ,SAAS,CAACiB,SAAS,CAACyH,wBAAwB,GAAG,YAAY;IACvD,IAAI1M,EAAE,GAAG,IAAI,CAACmI,SAAS;MAAEvD,KAAK,GAAG5E,EAAE,CAAC4E,KAAK;MAAEC,GAAG,GAAG7E,EAAE,CAAC6E,GAAG;IACvD,IAAIgM,kBAAkB,GAAG,IAAI,CAAC3H,YAAY,CAACyB,MAAM;IACjD,IAAI,CAAC1E,WAAW,CAAC,CAAC;IAClB,IAAI6K,eAAe,GAAG,IAAI,CAAC5H,YAAY,CAACyB,MAAM,GAAGkG,kBAAkB;IACnE,IAAI,CAAChI,YAAY,CAAC;MACdjE,KAAK,EAAEA,KAAK,GAAGkM,eAAe;MAC9BjM,GAAG,EAAEA,GAAG,GAAGiM;IACf,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;EACI9M,SAAS,CAACiB,SAAS,CAACa,gBAAgB,GAAG,YAAY;IAC/C,IAAI9F,EAAE,GAAG,IAAI,CAACsE,UAAU,CAAC6F,gBAAgB,CAAC,CAAC;MAAE5F,WAAW,GAAGvE,EAAE,CAACoK,IAAI;MAAE5F,aAAa,GAAGxE,EAAE,CAACuC,MAAM;IAC7F,IAAI,CAACiC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACD,WAAW,GAAGA,WAAW;EAClC,CAAC;EACD;AACJ;AACA;EACIP,SAAS,CAACiB,SAAS,CAAC8L,eAAe,GAAG,UAAUxL,KAAK,EAAE;IACnD,IAAI,CAACrB,OAAO,CAACqB,KAAK,GAAGA,KAAK;EAC9B,CAAC;EACD;AACJ;AACA;EACIvB,SAAS,CAACiB,SAAS,CAAC8I,iBAAiB,GAAG,UAAU3C,MAAM,EAAE;IACtD;IACA,QAAQA,MAAM;MACV,KAAK,GAAG;QACJ,OAAO4F,MAAM,CAAC,IAAI,CAAC7M,OAAO,CAACvB,KAAK,CAACC,WAAW,CAAC;MACjD,KAAK,GAAG;QACJ,OAAOmO,MAAM,CAAC,IAAI,CAAC7M,OAAO,CAACvB,KAAK,CAACE,MAAM,CAAC;MAC5C,KAAK,GAAG;QACJ,OAAOkO,MAAM,CAAC,IAAI,CAAC7M,OAAO,CAACvB,KAAK,CAACG,MAAM,CAAC;MAC5C;MACA,KAAK,GAAG;MACR;MACA,KAAK,GAAG;QACJ,OAAOiO,MAAM,CAAC,IAAI,CAAC7M,OAAO,CAACvB,KAAK,CAACI,IAAI,CAAC;MAC1C,KAAK,GAAG;QACJ,OAAOgO,MAAM,CAAC,IAAI,CAAC7M,OAAO,CAACvB,KAAK,CAACM,KAAK,CAAC;MAC3C;MACA,KAAK,GAAG;MACR;MACA;MACA,KAAK,GAAG;QACJ,OAAO8N,MAAM,CAAC,IAAI,CAAC7M,OAAO,CAACvB,KAAK,CAACK,GAAG,CAAC;MACzC;MACA,KAAK,GAAG;QACJ,OAAO+N,MAAM,CAAC,IAAI,CAAC7M,OAAO,CAACvB,KAAK,CAACO,IAAI,CAAC;MAC1C;QACI,OAAOhC,oBAAoB;IACnC;IACA;EACJ,CAAC;EACD;AACJ;AACA;EACI6C,SAAS,CAACiB,SAAS,CAAC+E,8BAA8B,GAAG,YAAY;IAC7D,IAAI,CAACiH,2BAA2B,CAAC,CAAC;IAClC,IAAI,CAACC,+BAA+B,CAAC,CAAC;EAC1C,CAAC;EACD;AACJ;AACA;EACIlN,SAAS,CAACiB,SAAS,CAACgM,2BAA2B,GAAG,YAAY;IAC1D,IAAI,CAACF,eAAe,CAAC,IAAI,CAACzG,oBAAoB,IAAI,EAAE,CAAC;EACzD,CAAC;EACD;AACJ;AACA;EACItG,SAAS,CAACiB,SAAS,CAACiM,+BAA+B,GAAG,YAAY;IAC9D,IAAIlR,EAAE,GAAG,IAAI,CAAC2E,wBAAwB;MAAEC,KAAK,GAAG5E,EAAE,CAAC4E,KAAK;MAAEC,GAAG,GAAG7E,EAAE,CAAC6E,GAAG;IACtE,IAAI,CAACgE,YAAY,CAAC;MAAEjE,KAAK,EAAEA,KAAK,IAAI,CAAC;MAAEC,GAAG,EAAEA,GAAG,IAAI;IAAE,CAAC,CAAC;EAC3D,CAAC;EACDb,SAAS,CAACiB,SAAS,CAACmK,UAAU,GAAG,UAAU7J,KAAK,EAAE;IAC9C,IAAI,CAAC4L,WAAW,CAAC5L,KAAK,CAAC;IACvB,IAAI,CAACjB,UAAU,GAAG,IAAI,CAAC8M,aAAa,CAAC7L,KAAK,CAAC;IAC3C,IAAI,CAAC4H,mBAAmB,CAAC,CAAC;EAC9B,CAAC;EACDnJ,SAAS,CAACiB,SAAS,CAACkM,WAAW,GAAG,UAAU5L,KAAK,EAAE;IAC/C,IAAIA,KAAK,IAAI,CAAC3E,WAAW,CAAC2E,KAAK,CAAC,EAAE;MAC9B,MAAM,IAAI8L,KAAK,CAAC,yDAAyD,CAAC;IAC9E;EACJ,CAAC;EACDrN,SAAS,CAACiB,SAAS,CAACkI,mBAAmB,GAAG,YAAY;IAClD,IAAIjJ,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,IAAI3B,MAAM,GAAG,IAAI,CAAC0F,QAAQ,GAAG,IAAI,CAAC+G,WAAW,GAAG,IAAI,CAACM,aAAa;IAClE,IAAItP,EAAE,GAAG,IAAI,CAACsE,UAAU,CAAC6F,gBAAgB,CAAC5H,MAAM,CAAC;MAAEgC,WAAW,GAAGvE,EAAE,CAACoK,IAAI;MAAE5F,aAAa,GAAGxE,EAAE,CAACuC,MAAM;IACnG,IAAI,CAACiC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACD,WAAW,GAAGA,WAAW;IAC9B,IAAI/B,cAAc,GAAG,IAAI,CAAC2B,OAAO,CAAC3B,cAAc,IAAIhC,SAAS,CAAC,IAAI,CAAC2D,OAAO,CAAC1B,WAAW,CAAC;IACvF,IAAI6O,eAAe,GAAG,CAAC,IAAI,CAACrJ,QAAQ,IAChCzF,cAAc,IACd,CAAC,IAAI,CAAC8B,UAAU,CAACmE,QAAQ,CAAC,CAAC;IAC/B,IAAIjG,cAAc,IAAIhC,SAAS,CAAC,IAAI,CAAC2D,OAAO,CAAC1B,WAAW,CAAC,EAAE;MACvDyB,OAAO,CAACzB,WAAW,GAAG,IAAI,CAAC0B,OAAO,CAAC1B,WAAW;IAClD;IACA,IAAIwK,eAAe,GAAGqE,eAAe,GAAG,EAAE,GAAG/M,WAAW;IACxD,IAAI,CAAC+F,oBAAoB,GAAG,IAAI,CAACpB,YAAY;IAC7C,IAAI,CAAC6H,eAAe,CAAC9D,eAAe,CAAC;EACzC,CAAC;EACD;AACJ;AACA;EACIjJ,SAAS,CAACiB,SAAS,CAAC2D,KAAK,GAAG,UAAUhE,KAAK,EAAEC,GAAG,EAAE;IAC9C,IAAIA,GAAG,KAAK,KAAK,CAAC,EAAE;MAAEA,GAAG,GAAGD,KAAK;IAAE;IACnC,IAAI2M,UAAU,GAAG3M,KAAK,KAAK2K,SAAS;IACpC,IAAIT,WAAW,GAAG,CAAClK,KAAK,EAAEA,KAAK,CAAC;IAChC,IAAIV,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,IAAIqN,UAAU,KAAK,IAAI,CAACpN,OAAO,CAACqN,QAAQ,IAAI,IAAI,CAACrN,OAAO,CAACoM,QAAQ,CAAC,EAAE;MAChE,OAAOhB,SAAS;IACpB;IACA,IAAI;MACA,IAAIrL,OAAO,CAACoE,cAAc,KAAKiH,SAAS,EAAE;QACtC,IAAIgC,UAAU,EAAE;UACZ,IAAI9Q,mBAAmB,CAAC,CAAC,IAAI+O,QAAQ,CAACC,aAAa,KAAKvL,OAAO,EAAE;YAC7DA,OAAO,CAACyD,KAAK,CAAC,CAAC;UACnB;UACAzD,OAAO,CAACwL,iBAAiB,CAAC9K,KAAK,EAAEC,GAAG,CAAC;QACzC;QACAiK,WAAW,GAAG,CAAC5K,OAAO,CAACoE,cAAc,EAAEpE,OAAO,CAACqE,YAAY,CAAC;MAChE;IACJ,CAAC,CACD,OAAOV,CAAC,EAAE;MACNiH,WAAW,GAAG,EAAE;IACpB;IACA,OAAOA,WAAW;EACtB,CAAC;EACD9K,SAAS,CAACiB,SAAS,CAAC2C,oBAAoB,GAAG,UAAUe,KAAK,EAAE;IACxD;IACA,KAAK,IAAIsC,CAAC,GAAGtC,KAAK,EAAEqH,CAAC,GAAGrH,KAAK,GAAG,CAAC,EAAEsC,CAAC,GAAG,IAAI,CAACzG,aAAa,CAACmG,MAAM,IAAIqF,CAAC,IAAI,CAAC,EAAE/E,CAAC,EAAE,EAAE+E,CAAC,EAAE,EAAE;MAClF,IAAI/E,CAAC,GAAG,IAAI,CAACzG,aAAa,CAACmG,MAAM,IAAI,IAAI,CAACnG,aAAa,CAACyG,CAAC,CAAC,KAAK,GAAG,EAAE;QAChE,IAAI,CAACwG,iBAAiB,CAAC,IAAI,CAACjN,aAAa,CAACyG,CAAC,CAAC,CAAC;QAC7C;MACJ;MACA,IAAI+E,CAAC,IAAI,CAAC,IAAI,IAAI,CAACxL,aAAa,CAACwL,CAAC,CAAC,KAAK,GAAG,EAAE;QACzC,IAAI,CAACyB,iBAAiB,CAAC,IAAI,CAACjN,aAAa,CAACwL,CAAC,CAAC,CAAC;QAC7C;MACJ;IACJ;EACJ,CAAC;EACDhM,SAAS,CAACiB,SAAS,CAACwM,iBAAiB,GAAG,UAAUrG,MAAM,EAAE;IACtD,IAAIsG,KAAK,GAAG,CAAC,CAAC;IACd,IAAI7M,GAAG,GAAG,CAAC;IACX,KAAK,IAAIoG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACzG,aAAa,CAACmG,MAAM,EAAEM,CAAC,EAAE,EAAE;MAChD,IAAI,IAAI,CAACzG,aAAa,CAACyG,CAAC,CAAC,KAAKG,MAAM,EAAE;QAClCvG,GAAG,GAAGoG,CAAC,GAAG,CAAC;QACX,IAAIyG,KAAK,KAAK,CAAC,CAAC,EAAE;UACdA,KAAK,GAAGzG,CAAC;QACb;MACJ;IACJ;IACA,IAAIyG,KAAK,GAAG,CAAC,EAAE;MACXA,KAAK,GAAG,CAAC;IACb;IACA,IAAI,CAAC9I,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IAChB,IAAI,CAACA,KAAK,CAAC8I,KAAK,EAAE7M,GAAG,CAAC;EAC1B,CAAC;EACD;AACJ;AACA;EACIb,SAAS,CAACiB,SAAS,CAACmM,aAAa,GAAG,UAAU7L,KAAK,EAAE;IACjD,IAAIoM,WAAW,GAAG,CAAE,IAAI,CAACrN,UAAU,IAAI,CAAC,CAAC,IAAK,IAAI,EAAEqN,WAAW;IAC/D,IAAI,CAACxN,OAAO,CAACoB,KAAK,GAAGA,KAAK;IAC1B,IAAIjB,UAAU,GAAG,IAAI,CAACsB,gBAAgB,CAAC,CAAC;IACxCtB,UAAU,CAACsN,cAAc,CAAC,IAAI,CAAC3J,QAAQ,GAAG0J,WAAW,GAAG,IAAI,CAAC;IAC7D,OAAOrN,UAAU;EACrB,CAAC;EACD;EACA;AACJ;AACA;EACIN,SAAS,CAACiB,SAAS,CAACW,gBAAgB,GAAG,YAAY;IAC/C,IAAIiM,cAAc,GAAG,IAAI,CAACtK,oBAAoB,CAAC,CAAC;IAChD,IAAIjD,UAAU,GAAG,IAAInE,UAAU,CAACI,MAAM,CAAC,CAAC,CAAC,EAAEsR,cAAc,CAAC,CAAC;IAC3D,OAAOvN,UAAU;EACrB,CAAC;EACD;AACJ;AACA;EACIN,SAAS,CAACiB,SAAS,CAACsC,oBAAoB,GAAG,YAAY;IACnD,IAAID,UAAU,GAAG;MACb3B,WAAW,EAAE,IAAI,CAACxB,OAAO,CAACwB,WAAW;MACrCvC,iBAAiB,EAAE,IAAI,CAACe,OAAO,CAACf,iBAAiB,GAAG,IAAI,CAACe,OAAO,CAACf,iBAAiB,GAAG,eAAe;MACpGb,MAAM,EAAE,IAAI,CAACyM,WAAW;MACxBtM,SAAS,EAAE,IAAI,CAACyB,OAAO,CAACzB,SAAS;MACjCkB,eAAe,EAAE,IAAI,CAACO,OAAO,CAACP,eAAe;MAC7CC,gBAAgB,EAAE,IAAI,CAACM,OAAO,CAACN,gBAAgB;MAC/C0B,KAAK,EAAE,IAAI,CAACpB,OAAO,CAACoB,KAAK;MACzBxB,eAAe,EAAE,IAAI,CAACI,OAAO,CAACJ,eAAe;MAC7CL,eAAe,EAAE,IAAI,CAACS,OAAO,CAACT;IAClC,CAAC;IACD,OAAO4D,UAAU;EACrB,CAAC;EACD;EACA;AACJ;AACA;EACItD,SAAS,CAACiB,SAAS,CAACwI,6BAA6B,GAAG,UAAUqE,SAAS,EAAE;IACrE,IAAInO,cAAc,GAAG,CAAC,IAAI,CAACQ,OAAO,CAACR,cAAc,IAAI,EAAE,EAClDoO,GAAG,CAAC,UAAUC,CAAC,EAAE;MAAE,OAAOA,CAAC,CAACC,QAAQ,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;IAAE,CAAC,CAAC;IACpE,IAAIxO,cAAc,CAACyO,OAAO,CAACN,SAAS,CAACvI,OAAO,CAAC0I,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IACzDtO,cAAc,CAACyO,OAAO,CAACN,SAAS,CAACvI,OAAO,CAAC,IAAI,CAAC,IAC9C5F,cAAc,CAACyO,OAAO,CAACN,SAAS,CAACrI,GAAG,CAACyI,WAAW,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;MACjE,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB,CAAC;EACD;AACJ;AACA;EACInO,SAAS,CAACiB,SAAS,CAACnB,QAAQ,GAAG,YAAY;IACvC,IAAIQ,UAAU,GAAG,IAAI,CAACA,UAAU;IAChC,IAAI+N,WAAW,GAAG,IAAI7M,IAAI,CAAC,CAAC;IAC5B,IAAIvC,GAAG,EAAEC,KAAK,EAAEC,IAAI,EAAEmP,KAAK,EAAEC,OAAO,EAAEC,OAAO;IAC7C,IAAIlO,UAAU,CAACmO,IAAI,IAAInO,UAAU,CAACpB,KAAK,IAAIoB,UAAU,CAACnB,IAAI,IAAImB,UAAU,CAACgO,KAAK,IAAIhO,UAAU,CAACiO,OAAO,IAAIjO,UAAU,CAACkO,OAAO,EAAE;MACxHrP,IAAI,GAAGmB,UAAU,CAACnB,IAAI,GAAGmB,UAAU,CAACiB,KAAK,CAACmN,WAAW,CAAC,CAAC,GAAGL,WAAW,CAACK,WAAW,CAAC,CAAC,EAC/ExP,KAAK,GAAGoB,UAAU,CAACpB,KAAK,GAAGoB,UAAU,CAACiB,KAAK,CAACoN,QAAQ,CAAC,CAAC,GAAGN,WAAW,CAACM,QAAQ,CAAC,CAAC,EAC/E1P,GAAG,GAAGqB,UAAU,CAACmO,IAAI,GAAGnO,UAAU,CAACiB,KAAK,CAACqN,OAAO,CAAC,CAAC,GAAGP,WAAW,CAACO,OAAO,CAAC,CAAC,EAC1EN,KAAK,GAAGhO,UAAU,CAACgO,KAAK,GAAGhO,UAAU,CAACiB,KAAK,CAACsN,QAAQ,CAAC,CAAC,GAAGR,WAAW,CAACQ,QAAQ,CAAC,CAAC,EAC/EN,OAAO,GAAGjO,UAAU,CAACiO,OAAO,GAAGjO,UAAU,CAACiB,KAAK,CAACuN,UAAU,CAAC,CAAC,GAAGT,WAAW,CAACS,UAAU,CAAC,CAAC,EACvFN,OAAO,GAAGlO,UAAU,CAACkO,OAAO,GAAGlO,UAAU,CAACiB,KAAK,CAACwN,UAAU,CAAC,CAAC,GAAGV,WAAW,CAACU,UAAU,CAAC,CAAC;MAC3FzO,UAAU,CAACuB,QAAQ,CAAC,IAAIL,IAAI,CAACrC,IAAI,EAAED,KAAK,EAAED,GAAG,EAAEqP,KAAK,EAAEC,OAAO,EAAEC,OAAO,CAAC,CAAC;MACxE,IAAI,CAACrF,mBAAmB,CAAC,CAAC;MAC1B,IAAI,CAACwD,kBAAkB,CAAC,CAAC;IAC7B;EACJ,CAAC;EACD,OAAO3M,SAAS;AACpB,CAAC,CAAClD,UAAU,CAAE;AACd,SAASkD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}