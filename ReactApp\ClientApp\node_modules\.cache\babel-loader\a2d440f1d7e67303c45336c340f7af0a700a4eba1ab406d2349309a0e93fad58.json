{"ast": null, "code": "import { __makeTemplateObject } from \"tslib\";\nimport { isString, isDate } from './utils';\n/**\n * @hidden\n * Creates a single arity function which wraps the value based on the provided predicate.\n * @example\n * ```\n * wrapIf(() => ignoreCase) `tolower(${field})`\n * //ignoreCase=true -> tolower(${field})`\n * //ignoreCase=false -> ${field}`\n * ```\n */\nexport var wrapIf = function (predicate) {\n  return function (str) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n      args[_i - 1] = arguments[_i];\n    }\n    return predicate() ? \"\".concat(str[0]).concat(args[0]).concat(str[1]) : args[0];\n  };\n};\n/**\n * @hidden\n */\nexport var toUTC = function (date) {\n  return new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds()));\n};\n/**\n * @hidden\n */\nexport var quote = function (_a) {\n  var field = _a.field,\n    value = _a.value,\n    ignoreCase = _a.ignoreCase,\n    operator = _a.operator;\n  return {\n    value: \"'\".concat(value.replace(/'/g, \"''\"), \"'\"),\n    field: field,\n    ignoreCase: ignoreCase,\n    operator: operator\n  };\n};\n/**\n * @hidden\n */\nexport var encodeValue = function (_a) {\n  var field = _a.field,\n    value = _a.value,\n    ignoreCase = _a.ignoreCase,\n    operator = _a.operator;\n  return {\n    value: \"\".concat(encodeURIComponent(value)),\n    field: field,\n    ignoreCase: ignoreCase,\n    operator: operator\n  };\n};\n/**\n * @hidden\n */\nexport var toLower = function (_a) {\n  var field = _a.field,\n    value = _a.value,\n    ignoreCase = _a.ignoreCase,\n    operator = _a.operator;\n  return {\n    field: wrapIf(function () {\n      return ignoreCase;\n    })(templateObject_1 || (templateObject_1 = __makeTemplateObject([\"tolower(\", \")\"], [\"tolower(\", \")\"])), field),\n    value: value,\n    ignoreCase: ignoreCase,\n    operator: operator\n  };\n};\n/**\n * @hidden\n */\nexport var normalizeField = function (_a) {\n  var field = _a.field,\n    value = _a.value,\n    ignoreCase = _a.ignoreCase,\n    operator = _a.operator;\n  return {\n    value: value,\n    field: field.replace(/\\./g, \"/\"),\n    ignoreCase: ignoreCase,\n    operator: operator\n  };\n};\n/**\n * @hidden\n */\nexport var isStringValue = function (x) {\n  return isString(x.value);\n};\n/**\n * @hidden\n */\nexport var isDateValue = function (x) {\n  return isDate(x.value);\n};\n/**\n * @hidden\n */\nexport var serializeFilters = function (map, join) {\n  return function (filter) {\n    var brackets = wrapIf(function () {\n      return filter.filters.length > 1;\n    });\n    return brackets(templateObject_2 || (templateObject_2 = __makeTemplateObject([\"(\", \")\"], [\"(\", \")\"])), filter.filters.map(map).join(join(filter)));\n  };\n};\nvar templateObject_1, templateObject_2;", "map": {"version": 3, "names": ["__makeTemplateObject", "isString", "isDate", "wrapIf", "predicate", "str", "args", "_i", "arguments", "length", "concat", "toUTC", "date", "Date", "UTC", "getFullYear", "getMonth", "getDate", "getHours", "getMinutes", "getSeconds", "getMilliseconds", "quote", "_a", "field", "value", "ignoreCase", "operator", "replace", "encodeValue", "encodeURIComponent", "<PERSON><PERSON><PERSON><PERSON>", "templateObject_1", "normalizeField", "isStringValue", "x", "isDateValue", "serializeFilters", "map", "join", "filter", "brackets", "filters", "templateObject_2"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-data-query/dist/es/filter-serialization.common.js"], "sourcesContent": ["import { __makeTemplateObject } from \"tslib\";\nimport { isString, isDate } from './utils';\n/**\n * @hidden\n * Creates a single arity function which wraps the value based on the provided predicate.\n * @example\n * ```\n * wrapIf(() => ignoreCase) `tolower(${field})`\n * //ignoreCase=true -> tolower(${field})`\n * //ignoreCase=false -> ${field}`\n * ```\n */\nexport var wrapIf = function (predicate) { return function (str) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        args[_i - 1] = arguments[_i];\n    }\n    return predicate() ? \"\".concat(str[0]).concat(args[0]).concat(str[1]) : args[0];\n}; };\n/**\n * @hidden\n */\nexport var toUTC = function (date) {\n    return new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds()));\n};\n/**\n * @hidden\n */\nexport var quote = function (_a) {\n    var field = _a.field, value = _a.value, ignoreCase = _a.ignoreCase, operator = _a.operator;\n    return ({\n        value: \"'\".concat(value.replace(/'/g, \"''\"), \"'\"),\n        field: field,\n        ignoreCase: ignoreCase,\n        operator: operator\n    });\n};\n/**\n * @hidden\n */\nexport var encodeValue = function (_a) {\n    var field = _a.field, value = _a.value, ignoreCase = _a.ignoreCase, operator = _a.operator;\n    return ({\n        value: \"\".concat(encodeURIComponent(value)),\n        field: field,\n        ignoreCase: ignoreCase,\n        operator: operator\n    });\n};\n/**\n * @hidden\n */\nexport var toLower = function (_a) {\n    var field = _a.field, value = _a.value, ignoreCase = _a.ignoreCase, operator = _a.operator;\n    return ({\n        field: wrapIf(function () { return ignoreCase; })(templateObject_1 || (templateObject_1 = __makeTemplateObject([\"tolower(\", \")\"], [\"tolower(\", \")\"])), field),\n        value: value,\n        ignoreCase: ignoreCase,\n        operator: operator\n    });\n};\n/**\n * @hidden\n */\nexport var normalizeField = function (_a) {\n    var field = _a.field, value = _a.value, ignoreCase = _a.ignoreCase, operator = _a.operator;\n    return ({\n        value: value,\n        field: field.replace(/\\./g, \"/\"),\n        ignoreCase: ignoreCase,\n        operator: operator\n    });\n};\n/**\n * @hidden\n */\nexport var isStringValue = function (x) { return isString(x.value); };\n/**\n * @hidden\n */\nexport var isDateValue = function (x) { return isDate(x.value); };\n/**\n * @hidden\n */\nexport var serializeFilters = function (map, join) { return function (filter) {\n    var brackets = wrapIf(function () { return filter.filters.length > 1; });\n    return brackets(templateObject_2 || (templateObject_2 = __makeTemplateObject([\"(\", \")\"], [\"(\", \")\"])), filter.filters\n        .map(map)\n        .join(join(filter)));\n}; };\nvar templateObject_1, templateObject_2;\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,OAAO;AAC5C,SAASC,QAAQ,EAAEC,MAAM,QAAQ,SAAS;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,MAAM,GAAG,SAAAA,CAAUC,SAAS,EAAE;EAAE,OAAO,UAAUC,GAAG,EAAE;IAC7D,IAAIC,IAAI,GAAG,EAAE;IACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;MAC1CD,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAChC;IACA,OAAOH,SAAS,CAAC,CAAC,GAAG,EAAE,CAACM,MAAM,CAACL,GAAG,CAAC,CAAC,CAAC,CAAC,CAACK,MAAM,CAACJ,IAAI,CAAC,CAAC,CAAC,CAAC,CAACI,MAAM,CAACL,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGC,IAAI,CAAC,CAAC,CAAC;EACnF,CAAC;AAAE,CAAC;AACJ;AACA;AACA;AACA,OAAO,IAAIK,KAAK,GAAG,SAAAA,CAAUC,IAAI,EAAE;EAC/B,OAAO,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAACF,IAAI,CAACG,WAAW,CAAC,CAAC,EAAEH,IAAI,CAACI,QAAQ,CAAC,CAAC,EAAEJ,IAAI,CAACK,OAAO,CAAC,CAAC,EAAEL,IAAI,CAACM,QAAQ,CAAC,CAAC,EAAEN,IAAI,CAACO,UAAU,CAAC,CAAC,EAAEP,IAAI,CAACQ,UAAU,CAAC,CAAC,EAAER,IAAI,CAACS,eAAe,CAAC,CAAC,CAAC,CAAC;AACjK,CAAC;AACD;AACA;AACA;AACA,OAAO,IAAIC,KAAK,GAAG,SAAAA,CAAUC,EAAE,EAAE;EAC7B,IAAIC,KAAK,GAAGD,EAAE,CAACC,KAAK;IAAEC,KAAK,GAAGF,EAAE,CAACE,KAAK;IAAEC,UAAU,GAAGH,EAAE,CAACG,UAAU;IAAEC,QAAQ,GAAGJ,EAAE,CAACI,QAAQ;EAC1F,OAAQ;IACJF,KAAK,EAAE,GAAG,CAACf,MAAM,CAACe,KAAK,CAACG,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC;IACjDJ,KAAK,EAAEA,KAAK;IACZE,UAAU,EAAEA,UAAU;IACtBC,QAAQ,EAAEA;EACd,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA,OAAO,IAAIE,WAAW,GAAG,SAAAA,CAAUN,EAAE,EAAE;EACnC,IAAIC,KAAK,GAAGD,EAAE,CAACC,KAAK;IAAEC,KAAK,GAAGF,EAAE,CAACE,KAAK;IAAEC,UAAU,GAAGH,EAAE,CAACG,UAAU;IAAEC,QAAQ,GAAGJ,EAAE,CAACI,QAAQ;EAC1F,OAAQ;IACJF,KAAK,EAAE,EAAE,CAACf,MAAM,CAACoB,kBAAkB,CAACL,KAAK,CAAC,CAAC;IAC3CD,KAAK,EAAEA,KAAK;IACZE,UAAU,EAAEA,UAAU;IACtBC,QAAQ,EAAEA;EACd,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA,OAAO,IAAII,OAAO,GAAG,SAAAA,CAAUR,EAAE,EAAE;EAC/B,IAAIC,KAAK,GAAGD,EAAE,CAACC,KAAK;IAAEC,KAAK,GAAGF,EAAE,CAACE,KAAK;IAAEC,UAAU,GAAGH,EAAE,CAACG,UAAU;IAAEC,QAAQ,GAAGJ,EAAE,CAACI,QAAQ;EAC1F,OAAQ;IACJH,KAAK,EAAErB,MAAM,CAAC,YAAY;MAAE,OAAOuB,UAAU;IAAE,CAAC,CAAC,CAACM,gBAAgB,KAAKA,gBAAgB,GAAGhC,oBAAoB,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,EAAE,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC,EAAEwB,KAAK,CAAC;IAC7JC,KAAK,EAAEA,KAAK;IACZC,UAAU,EAAEA,UAAU;IACtBC,QAAQ,EAAEA;EACd,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA,OAAO,IAAIM,cAAc,GAAG,SAAAA,CAAUV,EAAE,EAAE;EACtC,IAAIC,KAAK,GAAGD,EAAE,CAACC,KAAK;IAAEC,KAAK,GAAGF,EAAE,CAACE,KAAK;IAAEC,UAAU,GAAGH,EAAE,CAACG,UAAU;IAAEC,QAAQ,GAAGJ,EAAE,CAACI,QAAQ;EAC1F,OAAQ;IACJF,KAAK,EAAEA,KAAK;IACZD,KAAK,EAAEA,KAAK,CAACI,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;IAChCF,UAAU,EAAEA,UAAU;IACtBC,QAAQ,EAAEA;EACd,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA,OAAO,IAAIO,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAE;EAAE,OAAOlC,QAAQ,CAACkC,CAAC,CAACV,KAAK,CAAC;AAAE,CAAC;AACrE;AACA;AACA;AACA,OAAO,IAAIW,WAAW,GAAG,SAAAA,CAAUD,CAAC,EAAE;EAAE,OAAOjC,MAAM,CAACiC,CAAC,CAACV,KAAK,CAAC;AAAE,CAAC;AACjE;AACA;AACA;AACA,OAAO,IAAIY,gBAAgB,GAAG,SAAAA,CAAUC,GAAG,EAAEC,IAAI,EAAE;EAAE,OAAO,UAAUC,MAAM,EAAE;IAC1E,IAAIC,QAAQ,GAAGtC,MAAM,CAAC,YAAY;MAAE,OAAOqC,MAAM,CAACE,OAAO,CAACjC,MAAM,GAAG,CAAC;IAAE,CAAC,CAAC;IACxE,OAAOgC,QAAQ,CAACE,gBAAgB,KAAKA,gBAAgB,GAAG3C,oBAAoB,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,EAAEwC,MAAM,CAACE,OAAO,CAChHJ,GAAG,CAACA,GAAG,CAAC,CACRC,IAAI,CAACA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC;EAC5B,CAAC;AAAE,CAAC;AACJ,IAAIR,gBAAgB,EAAEW,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}