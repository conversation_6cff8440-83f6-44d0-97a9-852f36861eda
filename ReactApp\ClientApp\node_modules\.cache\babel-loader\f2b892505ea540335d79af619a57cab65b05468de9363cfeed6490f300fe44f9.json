{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nvar _excluded = [\"eventKey\", \"className\", \"style\", \"dragOver\", \"dragOverGapTop\", \"dragOverGapBottom\", \"isLeaf\", \"isStart\", \"isEnd\", \"expanded\", \"selected\", \"checked\", \"halfChecked\", \"loading\", \"domRef\", \"active\", \"data\", \"onMouseMove\", \"selectable\"];\nimport classNames from 'classnames';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport * as React from 'react';\n// @ts-ignore\nimport { TreeContext } from './contextTypes';\nimport Indent from './Indent';\nimport getEntity from './utils/keyUtil';\nimport { convertNodePropsToEventData } from './utils/treeUtil';\nvar ICON_OPEN = 'open';\nvar ICON_CLOSE = 'close';\nvar defaultTitle = '---';\nvar InternalTreeNode = /*#__PURE__*/function (_React$Component) {\n  _inherits(InternalTreeNode, _React$Component);\n  var _super = _createSuper(InternalTreeNode);\n  function InternalTreeNode() {\n    var _this;\n    _classCallCheck(this, InternalTreeNode);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _this.state = {\n      dragNodeHighlight: false\n    };\n    _this.selectHandle = void 0;\n    _this.cacheIndent = void 0;\n    _this.onSelectorClick = function (e) {\n      // Click trigger before select/check operation\n      var onNodeClick = _this.props.context.onNodeClick;\n      onNodeClick(e, convertNodePropsToEventData(_this.props));\n      if (_this.isSelectable()) {\n        _this.onSelect(e);\n      } else {\n        _this.onCheck(e);\n      }\n    };\n    _this.onSelectorDoubleClick = function (e) {\n      var onNodeDoubleClick = _this.props.context.onNodeDoubleClick;\n      onNodeDoubleClick(e, convertNodePropsToEventData(_this.props));\n    };\n    _this.onSelect = function (e) {\n      if (_this.isDisabled()) return;\n      var onNodeSelect = _this.props.context.onNodeSelect;\n      onNodeSelect(e, convertNodePropsToEventData(_this.props));\n    };\n    _this.onCheck = function (e) {\n      if (_this.isDisabled()) return;\n      var _this$props = _this.props,\n        disableCheckbox = _this$props.disableCheckbox,\n        checked = _this$props.checked;\n      var onNodeCheck = _this.props.context.onNodeCheck;\n      if (!_this.isCheckable() || disableCheckbox) return;\n      var targetChecked = !checked;\n      onNodeCheck(e, convertNodePropsToEventData(_this.props), targetChecked);\n    };\n    _this.onMouseEnter = function (e) {\n      var onNodeMouseEnter = _this.props.context.onNodeMouseEnter;\n      onNodeMouseEnter(e, convertNodePropsToEventData(_this.props));\n    };\n    _this.onMouseLeave = function (e) {\n      var onNodeMouseLeave = _this.props.context.onNodeMouseLeave;\n      onNodeMouseLeave(e, convertNodePropsToEventData(_this.props));\n    };\n    _this.onContextMenu = function (e) {\n      var onNodeContextMenu = _this.props.context.onNodeContextMenu;\n      onNodeContextMenu(e, convertNodePropsToEventData(_this.props));\n    };\n    _this.onDragStart = function (e) {\n      var onNodeDragStart = _this.props.context.onNodeDragStart;\n      e.stopPropagation();\n      _this.setState({\n        dragNodeHighlight: true\n      });\n      onNodeDragStart(e, _assertThisInitialized(_this));\n      try {\n        // ie throw error\n        // firefox-need-it\n        e.dataTransfer.setData('text/plain', '');\n      } catch (error) {\n        // empty\n      }\n    };\n    _this.onDragEnter = function (e) {\n      var onNodeDragEnter = _this.props.context.onNodeDragEnter;\n      e.preventDefault();\n      e.stopPropagation();\n      onNodeDragEnter(e, _assertThisInitialized(_this));\n    };\n    _this.onDragOver = function (e) {\n      var onNodeDragOver = _this.props.context.onNodeDragOver;\n      e.preventDefault();\n      e.stopPropagation();\n      onNodeDragOver(e, _assertThisInitialized(_this));\n    };\n    _this.onDragLeave = function (e) {\n      var onNodeDragLeave = _this.props.context.onNodeDragLeave;\n      e.stopPropagation();\n      onNodeDragLeave(e, _assertThisInitialized(_this));\n    };\n    _this.onDragEnd = function (e) {\n      var onNodeDragEnd = _this.props.context.onNodeDragEnd;\n      e.stopPropagation();\n      _this.setState({\n        dragNodeHighlight: false\n      });\n      onNodeDragEnd(e, _assertThisInitialized(_this));\n    };\n    _this.onDrop = function (e) {\n      var onNodeDrop = _this.props.context.onNodeDrop;\n      e.preventDefault();\n      e.stopPropagation();\n      _this.setState({\n        dragNodeHighlight: false\n      });\n      onNodeDrop(e, _assertThisInitialized(_this));\n    };\n    // Disabled item still can be switch\n    _this.onExpand = function (e) {\n      var _this$props2 = _this.props,\n        loading = _this$props2.loading,\n        onNodeExpand = _this$props2.context.onNodeExpand;\n      if (loading) return;\n      onNodeExpand(e, convertNodePropsToEventData(_this.props));\n    };\n    // Drag usage\n    _this.setSelectHandle = function (node) {\n      _this.selectHandle = node;\n    };\n    _this.getNodeState = function () {\n      var expanded = _this.props.expanded;\n      if (_this.isLeaf()) {\n        return null;\n      }\n      return expanded ? ICON_OPEN : ICON_CLOSE;\n    };\n    _this.hasChildren = function () {\n      var eventKey = _this.props.eventKey;\n      var keyEntities = _this.props.context.keyEntities;\n      var _ref = getEntity(keyEntities, eventKey) || {},\n        children = _ref.children;\n      return !!(children || []).length;\n    };\n    _this.isLeaf = function () {\n      var _this$props3 = _this.props,\n        isLeaf = _this$props3.isLeaf,\n        loaded = _this$props3.loaded;\n      var loadData = _this.props.context.loadData;\n      var hasChildren = _this.hasChildren();\n      if (isLeaf === false) {\n        return false;\n      }\n      return isLeaf || !loadData && !hasChildren || loadData && loaded && !hasChildren;\n    };\n    _this.isDisabled = function () {\n      var disabled = _this.props.disabled;\n      var treeDisabled = _this.props.context.disabled;\n      return !!(treeDisabled || disabled);\n    };\n    _this.isCheckable = function () {\n      var checkable = _this.props.checkable;\n      var treeCheckable = _this.props.context.checkable;\n      // Return false if tree or treeNode is not checkable\n      if (!treeCheckable || checkable === false) return false;\n      return treeCheckable;\n    };\n    // Load data to avoid default expanded tree without data\n    _this.syncLoadData = function (props) {\n      var expanded = props.expanded,\n        loading = props.loading,\n        loaded = props.loaded;\n      var _this$props$context = _this.props.context,\n        loadData = _this$props$context.loadData,\n        onNodeLoad = _this$props$context.onNodeLoad;\n      if (loading) {\n        return;\n      }\n      // read from state to avoid loadData at same time\n      if (loadData && expanded && !_this.isLeaf()) {\n        // We needn't reload data when has children in sync logic\n        // It's only needed in node expanded\n        if (!_this.hasChildren() && !loaded) {\n          onNodeLoad(convertNodePropsToEventData(_this.props));\n        }\n      }\n    };\n    _this.isDraggable = function () {\n      var _this$props4 = _this.props,\n        data = _this$props4.data,\n        draggable = _this$props4.context.draggable;\n      return !!(draggable && (!draggable.nodeDraggable || draggable.nodeDraggable(data)));\n    };\n    // ==================== Render: Drag Handler ====================\n    _this.renderDragHandler = function () {\n      var _this$props$context2 = _this.props.context,\n        draggable = _this$props$context2.draggable,\n        prefixCls = _this$props$context2.prefixCls;\n      return (draggable === null || draggable === void 0 ? void 0 : draggable.icon) ? /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-draggable-icon\")\n      }, draggable.icon) : null;\n    };\n    // ====================== Render: Switcher ======================\n    _this.renderSwitcherIconDom = function (isLeaf) {\n      var switcherIconFromProps = _this.props.switcherIcon;\n      var switcherIconFromCtx = _this.props.context.switcherIcon;\n      var switcherIcon = switcherIconFromProps || switcherIconFromCtx;\n      // if switcherIconDom is null, no render switcher span\n      if (typeof switcherIcon === 'function') {\n        return switcherIcon(_objectSpread(_objectSpread({}, _this.props), {}, {\n          isLeaf: isLeaf\n        }));\n      }\n      return switcherIcon;\n    };\n    // Switcher\n    _this.renderSwitcher = function () {\n      var expanded = _this.props.expanded;\n      var prefixCls = _this.props.context.prefixCls;\n      if (_this.isLeaf()) {\n        // if switcherIconDom is null, no render switcher span\n        var _switcherIconDom = _this.renderSwitcherIconDom(true);\n        return _switcherIconDom !== false ? /*#__PURE__*/React.createElement(\"span\", {\n          className: classNames(\"\".concat(prefixCls, \"-switcher\"), \"\".concat(prefixCls, \"-switcher-noop\"))\n        }, _switcherIconDom) : null;\n      }\n      var switcherCls = classNames(\"\".concat(prefixCls, \"-switcher\"), \"\".concat(prefixCls, \"-switcher_\").concat(expanded ? ICON_OPEN : ICON_CLOSE));\n      var switcherIconDom = _this.renderSwitcherIconDom(false);\n      return switcherIconDom !== false ? /*#__PURE__*/React.createElement(\"span\", {\n        onClick: _this.onExpand,\n        className: switcherCls\n      }, switcherIconDom) : null;\n    };\n    // ====================== Render: Checkbox ======================\n    // Checkbox\n    _this.renderCheckbox = function () {\n      var _this$props5 = _this.props,\n        checked = _this$props5.checked,\n        halfChecked = _this$props5.halfChecked,\n        disableCheckbox = _this$props5.disableCheckbox;\n      var prefixCls = _this.props.context.prefixCls;\n      var disabled = _this.isDisabled();\n      var checkable = _this.isCheckable();\n      if (!checkable) return null;\n      // [Legacy] Custom element should be separate with `checkable` in future\n      var $custom = typeof checkable !== 'boolean' ? checkable : null;\n      return /*#__PURE__*/React.createElement(\"span\", {\n        className: classNames(\"\".concat(prefixCls, \"-checkbox\"), checked && \"\".concat(prefixCls, \"-checkbox-checked\"), !checked && halfChecked && \"\".concat(prefixCls, \"-checkbox-indeterminate\"), (disabled || disableCheckbox) && \"\".concat(prefixCls, \"-checkbox-disabled\")),\n        onClick: _this.onCheck\n      }, $custom);\n    };\n    // ==================== Render: Title + Icon ====================\n    _this.renderIcon = function () {\n      var loading = _this.props.loading;\n      var prefixCls = _this.props.context.prefixCls;\n      return /*#__PURE__*/React.createElement(\"span\", {\n        className: classNames(\"\".concat(prefixCls, \"-iconEle\"), \"\".concat(prefixCls, \"-icon__\").concat(_this.getNodeState() || 'docu'), loading && \"\".concat(prefixCls, \"-icon_loading\"))\n      });\n    };\n    // Icon + Title\n    _this.renderSelector = function () {\n      var dragNodeHighlight = _this.state.dragNodeHighlight;\n      var _this$props6 = _this.props,\n        _this$props6$title = _this$props6.title,\n        title = _this$props6$title === void 0 ? defaultTitle : _this$props6$title,\n        selected = _this$props6.selected,\n        icon = _this$props6.icon,\n        loading = _this$props6.loading,\n        data = _this$props6.data;\n      var _this$props$context3 = _this.props.context,\n        prefixCls = _this$props$context3.prefixCls,\n        showIcon = _this$props$context3.showIcon,\n        treeIcon = _this$props$context3.icon,\n        loadData = _this$props$context3.loadData,\n        titleRender = _this$props$context3.titleRender;\n      var disabled = _this.isDisabled();\n      var wrapClass = \"\".concat(prefixCls, \"-node-content-wrapper\");\n      // Icon - Still show loading icon when loading without showIcon\n      var $icon;\n      if (showIcon) {\n        var currentIcon = icon || treeIcon;\n        $icon = currentIcon ? /*#__PURE__*/React.createElement(\"span\", {\n          className: classNames(\"\".concat(prefixCls, \"-iconEle\"), \"\".concat(prefixCls, \"-icon__customize\"))\n        }, typeof currentIcon === 'function' ? currentIcon(_this.props) : currentIcon) : _this.renderIcon();\n      } else if (loadData && loading) {\n        $icon = _this.renderIcon();\n      }\n      // Title\n      var titleNode;\n      if (typeof title === 'function') {\n        titleNode = title(data);\n      } else if (titleRender) {\n        titleNode = titleRender(data);\n      } else {\n        titleNode = title;\n      }\n      var $title = /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-title\")\n      }, titleNode);\n      return /*#__PURE__*/React.createElement(\"span\", {\n        ref: _this.setSelectHandle,\n        title: typeof title === 'string' ? title : '',\n        className: classNames(\"\".concat(wrapClass), \"\".concat(wrapClass, \"-\").concat(_this.getNodeState() || 'normal'), !disabled && (selected || dragNodeHighlight) && \"\".concat(prefixCls, \"-node-selected\")),\n        onMouseEnter: _this.onMouseEnter,\n        onMouseLeave: _this.onMouseLeave,\n        onContextMenu: _this.onContextMenu,\n        onClick: _this.onSelectorClick,\n        onDoubleClick: _this.onSelectorDoubleClick\n      }, $icon, $title, _this.renderDropIndicator());\n    };\n    // =================== Render: Drop Indicator ===================\n    _this.renderDropIndicator = function () {\n      var _this$props7 = _this.props,\n        disabled = _this$props7.disabled,\n        eventKey = _this$props7.eventKey;\n      var _this$props$context4 = _this.props.context,\n        draggable = _this$props$context4.draggable,\n        dropLevelOffset = _this$props$context4.dropLevelOffset,\n        dropPosition = _this$props$context4.dropPosition,\n        prefixCls = _this$props$context4.prefixCls,\n        indent = _this$props$context4.indent,\n        dropIndicatorRender = _this$props$context4.dropIndicatorRender,\n        dragOverNodeKey = _this$props$context4.dragOverNodeKey,\n        direction = _this$props$context4.direction;\n      var rootDraggable = !!draggable;\n      // allowDrop is calculated in Tree.tsx, there is no need for calc it here\n      var showIndicator = !disabled && rootDraggable && dragOverNodeKey === eventKey;\n      // This is a hot fix which is already fixed in\n      // https://github.com/react-component/tree/pull/743/files\n      // But some case need break point so we hack on this\n      // ref https://github.com/ant-design/ant-design/issues/43493\n      var mergedIndent = indent !== null && indent !== void 0 ? indent : _this.cacheIndent;\n      _this.cacheIndent = indent;\n      return showIndicator ? dropIndicatorRender({\n        dropPosition: dropPosition,\n        dropLevelOffset: dropLevelOffset,\n        indent: mergedIndent,\n        prefixCls: prefixCls,\n        direction: direction\n      }) : null;\n    };\n    return _this;\n  }\n  _createClass(InternalTreeNode, [{\n    key: \"componentDidMount\",\n    value:\n    // Isomorphic needn't load data in server side\n    function componentDidMount() {\n      this.syncLoadData(this.props);\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      this.syncLoadData(this.props);\n    }\n  }, {\n    key: \"isSelectable\",\n    value: function isSelectable() {\n      var selectable = this.props.selectable;\n      var treeSelectable = this.props.context.selectable;\n      // Ignore when selectable is undefined or null\n      if (typeof selectable === 'boolean') {\n        return selectable;\n      }\n      return treeSelectable;\n    }\n  }, {\n    key: \"render\",\n    value:\n    // =========================== Render ===========================\n    function render() {\n      var _classNames;\n      var _this$props8 = this.props,\n        eventKey = _this$props8.eventKey,\n        className = _this$props8.className,\n        style = _this$props8.style,\n        dragOver = _this$props8.dragOver,\n        dragOverGapTop = _this$props8.dragOverGapTop,\n        dragOverGapBottom = _this$props8.dragOverGapBottom,\n        isLeaf = _this$props8.isLeaf,\n        isStart = _this$props8.isStart,\n        isEnd = _this$props8.isEnd,\n        expanded = _this$props8.expanded,\n        selected = _this$props8.selected,\n        checked = _this$props8.checked,\n        halfChecked = _this$props8.halfChecked,\n        loading = _this$props8.loading,\n        domRef = _this$props8.domRef,\n        active = _this$props8.active,\n        data = _this$props8.data,\n        onMouseMove = _this$props8.onMouseMove,\n        selectable = _this$props8.selectable,\n        otherProps = _objectWithoutProperties(_this$props8, _excluded);\n      var _this$props$context5 = this.props.context,\n        prefixCls = _this$props$context5.prefixCls,\n        filterTreeNode = _this$props$context5.filterTreeNode,\n        keyEntities = _this$props$context5.keyEntities,\n        dropContainerKey = _this$props$context5.dropContainerKey,\n        dropTargetKey = _this$props$context5.dropTargetKey,\n        draggingNodeKey = _this$props$context5.draggingNodeKey;\n      var disabled = this.isDisabled();\n      var dataOrAriaAttributeProps = pickAttrs(otherProps, {\n        aria: true,\n        data: true\n      });\n      var _ref2 = getEntity(keyEntities, eventKey) || {},\n        level = _ref2.level;\n      var isEndNode = isEnd[isEnd.length - 1];\n      var mergedDraggable = this.isDraggable();\n      var draggableWithoutDisabled = !disabled && mergedDraggable;\n      var dragging = draggingNodeKey === eventKey;\n      var ariaSelected = selectable !== undefined ? {\n        'aria-selected': !!selectable\n      } : undefined;\n      return /*#__PURE__*/React.createElement(\"div\", _extends({\n        ref: domRef,\n        className: classNames(className, \"\".concat(prefixCls, \"-treenode\"), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-disabled\"), disabled), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-switcher-\").concat(expanded ? 'open' : 'close'), !isLeaf), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-checkbox-checked\"), checked), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-checkbox-indeterminate\"), halfChecked), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-selected\"), selected), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-loading\"), loading), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-active\"), active), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-leaf-last\"), isEndNode), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-draggable\"), mergedDraggable), _defineProperty(_classNames, \"dragging\", dragging), _defineProperty(_classNames, 'drop-target', dropTargetKey === eventKey), _defineProperty(_classNames, 'drop-container', dropContainerKey === eventKey), _defineProperty(_classNames, 'drag-over', !disabled && dragOver), _defineProperty(_classNames, 'drag-over-gap-top', !disabled && dragOverGapTop), _defineProperty(_classNames, 'drag-over-gap-bottom', !disabled && dragOverGapBottom), _defineProperty(_classNames, 'filter-node', filterTreeNode && filterTreeNode(convertNodePropsToEventData(this.props))), _classNames)),\n        style: style\n        // Draggable config\n        ,\n\n        draggable: draggableWithoutDisabled,\n        \"aria-grabbed\": dragging,\n        onDragStart: draggableWithoutDisabled ? this.onDragStart : undefined\n        // Drop config\n        ,\n\n        onDragEnter: mergedDraggable ? this.onDragEnter : undefined,\n        onDragOver: mergedDraggable ? this.onDragOver : undefined,\n        onDragLeave: mergedDraggable ? this.onDragLeave : undefined,\n        onDrop: mergedDraggable ? this.onDrop : undefined,\n        onDragEnd: mergedDraggable ? this.onDragEnd : undefined,\n        onMouseMove: onMouseMove\n      }, ariaSelected, dataOrAriaAttributeProps), /*#__PURE__*/React.createElement(Indent, {\n        prefixCls: prefixCls,\n        level: level,\n        isStart: isStart,\n        isEnd: isEnd\n      }), this.renderDragHandler(), this.renderSwitcher(), this.renderCheckbox(), this.renderSelector());\n    }\n  }]);\n  return InternalTreeNode;\n}(React.Component);\nvar ContextTreeNode = function ContextTreeNode(props) {\n  return /*#__PURE__*/React.createElement(TreeContext.Consumer, null, function (context) {\n    return /*#__PURE__*/React.createElement(InternalTreeNode, _extends({}, props, {\n      context: context\n    }));\n  });\n};\nContextTreeNode.displayName = 'TreeNode';\nContextTreeNode.isTreeNode = 1;\nexport default ContextTreeNode;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_objectWithoutProperties", "_objectSpread", "_classCallCheck", "_createClass", "_assertThisInitialized", "_inherits", "_createSuper", "_excluded", "classNames", "pickAttrs", "React", "TreeContext", "Indent", "getEntity", "convertNodePropsToEventData", "ICON_OPEN", "ICON_CLOSE", "defaultTitle", "InternalTreeNode", "_React$Component", "_super", "_this", "_len", "arguments", "length", "args", "Array", "_key", "call", "apply", "concat", "state", "dragNodeHighlight", "selectHandle", "cacheIndent", "onSelectorClick", "e", "onNodeClick", "props", "context", "isSelectable", "onSelect", "onCheck", "onSelectorDoubleClick", "onNodeDoubleClick", "isDisabled", "onNodeSelect", "_this$props", "disableCheckbox", "checked", "onNodeCheck", "isCheckable", "targetChecked", "onMouseEnter", "onNodeMouseEnter", "onMouseLeave", "onNodeMouseLeave", "onContextMenu", "onNodeContextMenu", "onDragStart", "onNodeDragStart", "stopPropagation", "setState", "dataTransfer", "setData", "error", "onDragEnter", "onNodeDragEnter", "preventDefault", "onDragOver", "onNodeDragOver", "onDragLeave", "onNodeDragLeave", "onDragEnd", "onNodeDragEnd", "onDrop", "onNodeDrop", "onExpand", "_this$props2", "loading", "onNodeExpand", "setSelectHandle", "node", "getNodeState", "expanded", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eventKey", "keyEntities", "_ref", "children", "_this$props3", "loaded", "loadData", "disabled", "treeDisabled", "checkable", "treeCheckable", "syncLoadData", "_this$props$context", "onNodeLoad", "isDraggable", "_this$props4", "data", "draggable", "nodeDraggable", "renderDragHandler", "_this$props$context2", "prefixCls", "icon", "createElement", "className", "renderSwitcherIconDom", "switcherIconFromProps", "switcherIcon", "switcherIconFromCtx", "renderSwitcher", "_switcherIconDom", "switcherCls", "switcherIconDom", "onClick", "renderCheckbox", "_this$props5", "halfChecked", "$custom", "renderIcon", "renderSelector", "_this$props6", "_this$props6$title", "title", "selected", "_this$props$context3", "showIcon", "treeIcon", "titleRender", "wrapClass", "$icon", "currentIcon", "titleNode", "$title", "ref", "onDoubleClick", "renderDropIndicator", "_this$props7", "_this$props$context4", "dropLevelOffset", "dropPosition", "indent", "dropIndicatorRender", "dragOverNodeKey", "direction", "rootDraggable", "showIndicator", "mergedIndent", "key", "value", "componentDidMount", "componentDidUpdate", "selectable", "treeSelectable", "render", "_classNames", "_this$props8", "style", "dragOver", "dragOverGapTop", "dragOverGapBottom", "isStart", "isEnd", "domRef", "active", "onMouseMove", "otherProps", "_this$props$context5", "filterTreeNode", "dropContainerKey", "dropTargetKey", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataOrAriaAttributeProps", "aria", "_ref2", "level", "isEndNode", "mergedDraggable", "draggableWithoutDisabled", "dragging", "ariaSelected", "undefined", "Component", "ContextTreeNode", "Consumer", "displayName", "isTreeNode"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/rc-tree/es/TreeNode.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nvar _excluded = [\"eventKey\", \"className\", \"style\", \"dragOver\", \"dragOverGapTop\", \"dragOverGapBottom\", \"isLeaf\", \"isStart\", \"isEnd\", \"expanded\", \"selected\", \"checked\", \"halfChecked\", \"loading\", \"domRef\", \"active\", \"data\", \"onMouseMove\", \"selectable\"];\nimport classNames from 'classnames';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport * as React from 'react';\n// @ts-ignore\nimport { TreeContext } from './contextTypes';\nimport Indent from './Indent';\nimport getEntity from './utils/keyUtil';\nimport { convertNodePropsToEventData } from './utils/treeUtil';\nvar ICON_OPEN = 'open';\nvar ICON_CLOSE = 'close';\nvar defaultTitle = '---';\nvar InternalTreeNode = /*#__PURE__*/function (_React$Component) {\n  _inherits(InternalTreeNode, _React$Component);\n  var _super = _createSuper(InternalTreeNode);\n  function InternalTreeNode() {\n    var _this;\n    _classCallCheck(this, InternalTreeNode);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _this.state = {\n      dragNodeHighlight: false\n    };\n    _this.selectHandle = void 0;\n    _this.cacheIndent = void 0;\n    _this.onSelectorClick = function (e) {\n      // Click trigger before select/check operation\n      var onNodeClick = _this.props.context.onNodeClick;\n      onNodeClick(e, convertNodePropsToEventData(_this.props));\n      if (_this.isSelectable()) {\n        _this.onSelect(e);\n      } else {\n        _this.onCheck(e);\n      }\n    };\n    _this.onSelectorDoubleClick = function (e) {\n      var onNodeDoubleClick = _this.props.context.onNodeDoubleClick;\n      onNodeDoubleClick(e, convertNodePropsToEventData(_this.props));\n    };\n    _this.onSelect = function (e) {\n      if (_this.isDisabled()) return;\n      var onNodeSelect = _this.props.context.onNodeSelect;\n      onNodeSelect(e, convertNodePropsToEventData(_this.props));\n    };\n    _this.onCheck = function (e) {\n      if (_this.isDisabled()) return;\n      var _this$props = _this.props,\n        disableCheckbox = _this$props.disableCheckbox,\n        checked = _this$props.checked;\n      var onNodeCheck = _this.props.context.onNodeCheck;\n      if (!_this.isCheckable() || disableCheckbox) return;\n      var targetChecked = !checked;\n      onNodeCheck(e, convertNodePropsToEventData(_this.props), targetChecked);\n    };\n    _this.onMouseEnter = function (e) {\n      var onNodeMouseEnter = _this.props.context.onNodeMouseEnter;\n      onNodeMouseEnter(e, convertNodePropsToEventData(_this.props));\n    };\n    _this.onMouseLeave = function (e) {\n      var onNodeMouseLeave = _this.props.context.onNodeMouseLeave;\n      onNodeMouseLeave(e, convertNodePropsToEventData(_this.props));\n    };\n    _this.onContextMenu = function (e) {\n      var onNodeContextMenu = _this.props.context.onNodeContextMenu;\n      onNodeContextMenu(e, convertNodePropsToEventData(_this.props));\n    };\n    _this.onDragStart = function (e) {\n      var onNodeDragStart = _this.props.context.onNodeDragStart;\n      e.stopPropagation();\n      _this.setState({\n        dragNodeHighlight: true\n      });\n      onNodeDragStart(e, _assertThisInitialized(_this));\n      try {\n        // ie throw error\n        // firefox-need-it\n        e.dataTransfer.setData('text/plain', '');\n      } catch (error) {\n        // empty\n      }\n    };\n    _this.onDragEnter = function (e) {\n      var onNodeDragEnter = _this.props.context.onNodeDragEnter;\n      e.preventDefault();\n      e.stopPropagation();\n      onNodeDragEnter(e, _assertThisInitialized(_this));\n    };\n    _this.onDragOver = function (e) {\n      var onNodeDragOver = _this.props.context.onNodeDragOver;\n      e.preventDefault();\n      e.stopPropagation();\n      onNodeDragOver(e, _assertThisInitialized(_this));\n    };\n    _this.onDragLeave = function (e) {\n      var onNodeDragLeave = _this.props.context.onNodeDragLeave;\n      e.stopPropagation();\n      onNodeDragLeave(e, _assertThisInitialized(_this));\n    };\n    _this.onDragEnd = function (e) {\n      var onNodeDragEnd = _this.props.context.onNodeDragEnd;\n      e.stopPropagation();\n      _this.setState({\n        dragNodeHighlight: false\n      });\n      onNodeDragEnd(e, _assertThisInitialized(_this));\n    };\n    _this.onDrop = function (e) {\n      var onNodeDrop = _this.props.context.onNodeDrop;\n      e.preventDefault();\n      e.stopPropagation();\n      _this.setState({\n        dragNodeHighlight: false\n      });\n      onNodeDrop(e, _assertThisInitialized(_this));\n    };\n    // Disabled item still can be switch\n    _this.onExpand = function (e) {\n      var _this$props2 = _this.props,\n        loading = _this$props2.loading,\n        onNodeExpand = _this$props2.context.onNodeExpand;\n      if (loading) return;\n      onNodeExpand(e, convertNodePropsToEventData(_this.props));\n    };\n    // Drag usage\n    _this.setSelectHandle = function (node) {\n      _this.selectHandle = node;\n    };\n    _this.getNodeState = function () {\n      var expanded = _this.props.expanded;\n      if (_this.isLeaf()) {\n        return null;\n      }\n      return expanded ? ICON_OPEN : ICON_CLOSE;\n    };\n    _this.hasChildren = function () {\n      var eventKey = _this.props.eventKey;\n      var keyEntities = _this.props.context.keyEntities;\n      var _ref = getEntity(keyEntities, eventKey) || {},\n        children = _ref.children;\n      return !!(children || []).length;\n    };\n    _this.isLeaf = function () {\n      var _this$props3 = _this.props,\n        isLeaf = _this$props3.isLeaf,\n        loaded = _this$props3.loaded;\n      var loadData = _this.props.context.loadData;\n      var hasChildren = _this.hasChildren();\n      if (isLeaf === false) {\n        return false;\n      }\n      return isLeaf || !loadData && !hasChildren || loadData && loaded && !hasChildren;\n    };\n    _this.isDisabled = function () {\n      var disabled = _this.props.disabled;\n      var treeDisabled = _this.props.context.disabled;\n      return !!(treeDisabled || disabled);\n    };\n    _this.isCheckable = function () {\n      var checkable = _this.props.checkable;\n      var treeCheckable = _this.props.context.checkable;\n      // Return false if tree or treeNode is not checkable\n      if (!treeCheckable || checkable === false) return false;\n      return treeCheckable;\n    };\n    // Load data to avoid default expanded tree without data\n    _this.syncLoadData = function (props) {\n      var expanded = props.expanded,\n        loading = props.loading,\n        loaded = props.loaded;\n      var _this$props$context = _this.props.context,\n        loadData = _this$props$context.loadData,\n        onNodeLoad = _this$props$context.onNodeLoad;\n      if (loading) {\n        return;\n      }\n      // read from state to avoid loadData at same time\n      if (loadData && expanded && !_this.isLeaf()) {\n        // We needn't reload data when has children in sync logic\n        // It's only needed in node expanded\n        if (!_this.hasChildren() && !loaded) {\n          onNodeLoad(convertNodePropsToEventData(_this.props));\n        }\n      }\n    };\n    _this.isDraggable = function () {\n      var _this$props4 = _this.props,\n        data = _this$props4.data,\n        draggable = _this$props4.context.draggable;\n      return !!(draggable && (!draggable.nodeDraggable || draggable.nodeDraggable(data)));\n    };\n    // ==================== Render: Drag Handler ====================\n    _this.renderDragHandler = function () {\n      var _this$props$context2 = _this.props.context,\n        draggable = _this$props$context2.draggable,\n        prefixCls = _this$props$context2.prefixCls;\n      return (draggable === null || draggable === void 0 ? void 0 : draggable.icon) ? /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-draggable-icon\")\n      }, draggable.icon) : null;\n    };\n    // ====================== Render: Switcher ======================\n    _this.renderSwitcherIconDom = function (isLeaf) {\n      var switcherIconFromProps = _this.props.switcherIcon;\n      var switcherIconFromCtx = _this.props.context.switcherIcon;\n      var switcherIcon = switcherIconFromProps || switcherIconFromCtx;\n      // if switcherIconDom is null, no render switcher span\n      if (typeof switcherIcon === 'function') {\n        return switcherIcon(_objectSpread(_objectSpread({}, _this.props), {}, {\n          isLeaf: isLeaf\n        }));\n      }\n      return switcherIcon;\n    };\n    // Switcher\n    _this.renderSwitcher = function () {\n      var expanded = _this.props.expanded;\n      var prefixCls = _this.props.context.prefixCls;\n      if (_this.isLeaf()) {\n        // if switcherIconDom is null, no render switcher span\n        var _switcherIconDom = _this.renderSwitcherIconDom(true);\n        return _switcherIconDom !== false ? /*#__PURE__*/React.createElement(\"span\", {\n          className: classNames(\"\".concat(prefixCls, \"-switcher\"), \"\".concat(prefixCls, \"-switcher-noop\"))\n        }, _switcherIconDom) : null;\n      }\n      var switcherCls = classNames(\"\".concat(prefixCls, \"-switcher\"), \"\".concat(prefixCls, \"-switcher_\").concat(expanded ? ICON_OPEN : ICON_CLOSE));\n      var switcherIconDom = _this.renderSwitcherIconDom(false);\n      return switcherIconDom !== false ? /*#__PURE__*/React.createElement(\"span\", {\n        onClick: _this.onExpand,\n        className: switcherCls\n      }, switcherIconDom) : null;\n    };\n    // ====================== Render: Checkbox ======================\n    // Checkbox\n    _this.renderCheckbox = function () {\n      var _this$props5 = _this.props,\n        checked = _this$props5.checked,\n        halfChecked = _this$props5.halfChecked,\n        disableCheckbox = _this$props5.disableCheckbox;\n      var prefixCls = _this.props.context.prefixCls;\n      var disabled = _this.isDisabled();\n      var checkable = _this.isCheckable();\n      if (!checkable) return null;\n      // [Legacy] Custom element should be separate with `checkable` in future\n      var $custom = typeof checkable !== 'boolean' ? checkable : null;\n      return /*#__PURE__*/React.createElement(\"span\", {\n        className: classNames(\"\".concat(prefixCls, \"-checkbox\"), checked && \"\".concat(prefixCls, \"-checkbox-checked\"), !checked && halfChecked && \"\".concat(prefixCls, \"-checkbox-indeterminate\"), (disabled || disableCheckbox) && \"\".concat(prefixCls, \"-checkbox-disabled\")),\n        onClick: _this.onCheck\n      }, $custom);\n    };\n    // ==================== Render: Title + Icon ====================\n    _this.renderIcon = function () {\n      var loading = _this.props.loading;\n      var prefixCls = _this.props.context.prefixCls;\n      return /*#__PURE__*/React.createElement(\"span\", {\n        className: classNames(\"\".concat(prefixCls, \"-iconEle\"), \"\".concat(prefixCls, \"-icon__\").concat(_this.getNodeState() || 'docu'), loading && \"\".concat(prefixCls, \"-icon_loading\"))\n      });\n    };\n    // Icon + Title\n    _this.renderSelector = function () {\n      var dragNodeHighlight = _this.state.dragNodeHighlight;\n      var _this$props6 = _this.props,\n        _this$props6$title = _this$props6.title,\n        title = _this$props6$title === void 0 ? defaultTitle : _this$props6$title,\n        selected = _this$props6.selected,\n        icon = _this$props6.icon,\n        loading = _this$props6.loading,\n        data = _this$props6.data;\n      var _this$props$context3 = _this.props.context,\n        prefixCls = _this$props$context3.prefixCls,\n        showIcon = _this$props$context3.showIcon,\n        treeIcon = _this$props$context3.icon,\n        loadData = _this$props$context3.loadData,\n        titleRender = _this$props$context3.titleRender;\n      var disabled = _this.isDisabled();\n      var wrapClass = \"\".concat(prefixCls, \"-node-content-wrapper\");\n      // Icon - Still show loading icon when loading without showIcon\n      var $icon;\n      if (showIcon) {\n        var currentIcon = icon || treeIcon;\n        $icon = currentIcon ? /*#__PURE__*/React.createElement(\"span\", {\n          className: classNames(\"\".concat(prefixCls, \"-iconEle\"), \"\".concat(prefixCls, \"-icon__customize\"))\n        }, typeof currentIcon === 'function' ? currentIcon(_this.props) : currentIcon) : _this.renderIcon();\n      } else if (loadData && loading) {\n        $icon = _this.renderIcon();\n      }\n      // Title\n      var titleNode;\n      if (typeof title === 'function') {\n        titleNode = title(data);\n      } else if (titleRender) {\n        titleNode = titleRender(data);\n      } else {\n        titleNode = title;\n      }\n      var $title = /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-title\")\n      }, titleNode);\n      return /*#__PURE__*/React.createElement(\"span\", {\n        ref: _this.setSelectHandle,\n        title: typeof title === 'string' ? title : '',\n        className: classNames(\"\".concat(wrapClass), \"\".concat(wrapClass, \"-\").concat(_this.getNodeState() || 'normal'), !disabled && (selected || dragNodeHighlight) && \"\".concat(prefixCls, \"-node-selected\")),\n        onMouseEnter: _this.onMouseEnter,\n        onMouseLeave: _this.onMouseLeave,\n        onContextMenu: _this.onContextMenu,\n        onClick: _this.onSelectorClick,\n        onDoubleClick: _this.onSelectorDoubleClick\n      }, $icon, $title, _this.renderDropIndicator());\n    };\n    // =================== Render: Drop Indicator ===================\n    _this.renderDropIndicator = function () {\n      var _this$props7 = _this.props,\n        disabled = _this$props7.disabled,\n        eventKey = _this$props7.eventKey;\n      var _this$props$context4 = _this.props.context,\n        draggable = _this$props$context4.draggable,\n        dropLevelOffset = _this$props$context4.dropLevelOffset,\n        dropPosition = _this$props$context4.dropPosition,\n        prefixCls = _this$props$context4.prefixCls,\n        indent = _this$props$context4.indent,\n        dropIndicatorRender = _this$props$context4.dropIndicatorRender,\n        dragOverNodeKey = _this$props$context4.dragOverNodeKey,\n        direction = _this$props$context4.direction;\n      var rootDraggable = !!draggable;\n      // allowDrop is calculated in Tree.tsx, there is no need for calc it here\n      var showIndicator = !disabled && rootDraggable && dragOverNodeKey === eventKey;\n      // This is a hot fix which is already fixed in\n      // https://github.com/react-component/tree/pull/743/files\n      // But some case need break point so we hack on this\n      // ref https://github.com/ant-design/ant-design/issues/43493\n      var mergedIndent = indent !== null && indent !== void 0 ? indent : _this.cacheIndent;\n      _this.cacheIndent = indent;\n      return showIndicator ? dropIndicatorRender({\n        dropPosition: dropPosition,\n        dropLevelOffset: dropLevelOffset,\n        indent: mergedIndent,\n        prefixCls: prefixCls,\n        direction: direction\n      }) : null;\n    };\n    return _this;\n  }\n  _createClass(InternalTreeNode, [{\n    key: \"componentDidMount\",\n    value:\n    // Isomorphic needn't load data in server side\n    function componentDidMount() {\n      this.syncLoadData(this.props);\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      this.syncLoadData(this.props);\n    }\n  }, {\n    key: \"isSelectable\",\n    value: function isSelectable() {\n      var selectable = this.props.selectable;\n      var treeSelectable = this.props.context.selectable;\n      // Ignore when selectable is undefined or null\n      if (typeof selectable === 'boolean') {\n        return selectable;\n      }\n      return treeSelectable;\n    }\n  }, {\n    key: \"render\",\n    value:\n    // =========================== Render ===========================\n    function render() {\n      var _classNames;\n      var _this$props8 = this.props,\n        eventKey = _this$props8.eventKey,\n        className = _this$props8.className,\n        style = _this$props8.style,\n        dragOver = _this$props8.dragOver,\n        dragOverGapTop = _this$props8.dragOverGapTop,\n        dragOverGapBottom = _this$props8.dragOverGapBottom,\n        isLeaf = _this$props8.isLeaf,\n        isStart = _this$props8.isStart,\n        isEnd = _this$props8.isEnd,\n        expanded = _this$props8.expanded,\n        selected = _this$props8.selected,\n        checked = _this$props8.checked,\n        halfChecked = _this$props8.halfChecked,\n        loading = _this$props8.loading,\n        domRef = _this$props8.domRef,\n        active = _this$props8.active,\n        data = _this$props8.data,\n        onMouseMove = _this$props8.onMouseMove,\n        selectable = _this$props8.selectable,\n        otherProps = _objectWithoutProperties(_this$props8, _excluded);\n      var _this$props$context5 = this.props.context,\n        prefixCls = _this$props$context5.prefixCls,\n        filterTreeNode = _this$props$context5.filterTreeNode,\n        keyEntities = _this$props$context5.keyEntities,\n        dropContainerKey = _this$props$context5.dropContainerKey,\n        dropTargetKey = _this$props$context5.dropTargetKey,\n        draggingNodeKey = _this$props$context5.draggingNodeKey;\n      var disabled = this.isDisabled();\n      var dataOrAriaAttributeProps = pickAttrs(otherProps, {\n        aria: true,\n        data: true\n      });\n      var _ref2 = getEntity(keyEntities, eventKey) || {},\n        level = _ref2.level;\n      var isEndNode = isEnd[isEnd.length - 1];\n      var mergedDraggable = this.isDraggable();\n      var draggableWithoutDisabled = !disabled && mergedDraggable;\n      var dragging = draggingNodeKey === eventKey;\n      var ariaSelected = selectable !== undefined ? {\n        'aria-selected': !!selectable\n      } : undefined;\n      return /*#__PURE__*/React.createElement(\"div\", _extends({\n        ref: domRef,\n        className: classNames(className, \"\".concat(prefixCls, \"-treenode\"), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-disabled\"), disabled), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-switcher-\").concat(expanded ? 'open' : 'close'), !isLeaf), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-checkbox-checked\"), checked), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-checkbox-indeterminate\"), halfChecked), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-selected\"), selected), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-loading\"), loading), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-active\"), active), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-leaf-last\"), isEndNode), _defineProperty(_classNames, \"\".concat(prefixCls, \"-treenode-draggable\"), mergedDraggable), _defineProperty(_classNames, \"dragging\", dragging), _defineProperty(_classNames, 'drop-target', dropTargetKey === eventKey), _defineProperty(_classNames, 'drop-container', dropContainerKey === eventKey), _defineProperty(_classNames, 'drag-over', !disabled && dragOver), _defineProperty(_classNames, 'drag-over-gap-top', !disabled && dragOverGapTop), _defineProperty(_classNames, 'drag-over-gap-bottom', !disabled && dragOverGapBottom), _defineProperty(_classNames, 'filter-node', filterTreeNode && filterTreeNode(convertNodePropsToEventData(this.props))), _classNames)),\n        style: style\n        // Draggable config\n        ,\n        draggable: draggableWithoutDisabled,\n        \"aria-grabbed\": dragging,\n        onDragStart: draggableWithoutDisabled ? this.onDragStart : undefined\n        // Drop config\n        ,\n        onDragEnter: mergedDraggable ? this.onDragEnter : undefined,\n        onDragOver: mergedDraggable ? this.onDragOver : undefined,\n        onDragLeave: mergedDraggable ? this.onDragLeave : undefined,\n        onDrop: mergedDraggable ? this.onDrop : undefined,\n        onDragEnd: mergedDraggable ? this.onDragEnd : undefined,\n        onMouseMove: onMouseMove\n      }, ariaSelected, dataOrAriaAttributeProps), /*#__PURE__*/React.createElement(Indent, {\n        prefixCls: prefixCls,\n        level: level,\n        isStart: isStart,\n        isEnd: isEnd\n      }), this.renderDragHandler(), this.renderSwitcher(), this.renderCheckbox(), this.renderSelector());\n    }\n  }]);\n  return InternalTreeNode;\n}(React.Component);\nvar ContextTreeNode = function ContextTreeNode(props) {\n  return /*#__PURE__*/React.createElement(TreeContext.Consumer, null, function (context) {\n    return /*#__PURE__*/React.createElement(InternalTreeNode, _extends({}, props, {\n      context: context\n    }));\n  });\n};\nContextTreeNode.displayName = 'TreeNode';\nContextTreeNode.isTreeNode = 1;\nexport default ContextTreeNode;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,IAAIC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,YAAY,CAAC;AACzP,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B;AACA,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,SAAS,MAAM,iBAAiB;AACvC,SAASC,2BAA2B,QAAQ,kBAAkB;AAC9D,IAAIC,SAAS,GAAG,MAAM;AACtB,IAAIC,UAAU,GAAG,OAAO;AACxB,IAAIC,YAAY,GAAG,KAAK;AACxB,IAAIC,gBAAgB,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EAC9Dd,SAAS,CAACa,gBAAgB,EAAEC,gBAAgB,CAAC;EAC7C,IAAIC,MAAM,GAAGd,YAAY,CAACY,gBAAgB,CAAC;EAC3C,SAASA,gBAAgBA,CAAA,EAAG;IAC1B,IAAIG,KAAK;IACTnB,eAAe,CAAC,IAAI,EAAEgB,gBAAgB,CAAC;IACvC,KAAK,IAAII,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IACAN,KAAK,GAAGD,MAAM,CAACQ,IAAI,CAACC,KAAK,CAACT,MAAM,EAAE,CAAC,IAAI,CAAC,CAACU,MAAM,CAACL,IAAI,CAAC,CAAC;IACtDJ,KAAK,CAACU,KAAK,GAAG;MACZC,iBAAiB,EAAE;IACrB,CAAC;IACDX,KAAK,CAACY,YAAY,GAAG,KAAK,CAAC;IAC3BZ,KAAK,CAACa,WAAW,GAAG,KAAK,CAAC;IAC1Bb,KAAK,CAACc,eAAe,GAAG,UAAUC,CAAC,EAAE;MACnC;MACA,IAAIC,WAAW,GAAGhB,KAAK,CAACiB,KAAK,CAACC,OAAO,CAACF,WAAW;MACjDA,WAAW,CAACD,CAAC,EAAEtB,2BAA2B,CAACO,KAAK,CAACiB,KAAK,CAAC,CAAC;MACxD,IAAIjB,KAAK,CAACmB,YAAY,CAAC,CAAC,EAAE;QACxBnB,KAAK,CAACoB,QAAQ,CAACL,CAAC,CAAC;MACnB,CAAC,MAAM;QACLf,KAAK,CAACqB,OAAO,CAACN,CAAC,CAAC;MAClB;IACF,CAAC;IACDf,KAAK,CAACsB,qBAAqB,GAAG,UAAUP,CAAC,EAAE;MACzC,IAAIQ,iBAAiB,GAAGvB,KAAK,CAACiB,KAAK,CAACC,OAAO,CAACK,iBAAiB;MAC7DA,iBAAiB,CAACR,CAAC,EAAEtB,2BAA2B,CAACO,KAAK,CAACiB,KAAK,CAAC,CAAC;IAChE,CAAC;IACDjB,KAAK,CAACoB,QAAQ,GAAG,UAAUL,CAAC,EAAE;MAC5B,IAAIf,KAAK,CAACwB,UAAU,CAAC,CAAC,EAAE;MACxB,IAAIC,YAAY,GAAGzB,KAAK,CAACiB,KAAK,CAACC,OAAO,CAACO,YAAY;MACnDA,YAAY,CAACV,CAAC,EAAEtB,2BAA2B,CAACO,KAAK,CAACiB,KAAK,CAAC,CAAC;IAC3D,CAAC;IACDjB,KAAK,CAACqB,OAAO,GAAG,UAAUN,CAAC,EAAE;MAC3B,IAAIf,KAAK,CAACwB,UAAU,CAAC,CAAC,EAAE;MACxB,IAAIE,WAAW,GAAG1B,KAAK,CAACiB,KAAK;QAC3BU,eAAe,GAAGD,WAAW,CAACC,eAAe;QAC7CC,OAAO,GAAGF,WAAW,CAACE,OAAO;MAC/B,IAAIC,WAAW,GAAG7B,KAAK,CAACiB,KAAK,CAACC,OAAO,CAACW,WAAW;MACjD,IAAI,CAAC7B,KAAK,CAAC8B,WAAW,CAAC,CAAC,IAAIH,eAAe,EAAE;MAC7C,IAAII,aAAa,GAAG,CAACH,OAAO;MAC5BC,WAAW,CAACd,CAAC,EAAEtB,2BAA2B,CAACO,KAAK,CAACiB,KAAK,CAAC,EAAEc,aAAa,CAAC;IACzE,CAAC;IACD/B,KAAK,CAACgC,YAAY,GAAG,UAAUjB,CAAC,EAAE;MAChC,IAAIkB,gBAAgB,GAAGjC,KAAK,CAACiB,KAAK,CAACC,OAAO,CAACe,gBAAgB;MAC3DA,gBAAgB,CAAClB,CAAC,EAAEtB,2BAA2B,CAACO,KAAK,CAACiB,KAAK,CAAC,CAAC;IAC/D,CAAC;IACDjB,KAAK,CAACkC,YAAY,GAAG,UAAUnB,CAAC,EAAE;MAChC,IAAIoB,gBAAgB,GAAGnC,KAAK,CAACiB,KAAK,CAACC,OAAO,CAACiB,gBAAgB;MAC3DA,gBAAgB,CAACpB,CAAC,EAAEtB,2BAA2B,CAACO,KAAK,CAACiB,KAAK,CAAC,CAAC;IAC/D,CAAC;IACDjB,KAAK,CAACoC,aAAa,GAAG,UAAUrB,CAAC,EAAE;MACjC,IAAIsB,iBAAiB,GAAGrC,KAAK,CAACiB,KAAK,CAACC,OAAO,CAACmB,iBAAiB;MAC7DA,iBAAiB,CAACtB,CAAC,EAAEtB,2BAA2B,CAACO,KAAK,CAACiB,KAAK,CAAC,CAAC;IAChE,CAAC;IACDjB,KAAK,CAACsC,WAAW,GAAG,UAAUvB,CAAC,EAAE;MAC/B,IAAIwB,eAAe,GAAGvC,KAAK,CAACiB,KAAK,CAACC,OAAO,CAACqB,eAAe;MACzDxB,CAAC,CAACyB,eAAe,CAAC,CAAC;MACnBxC,KAAK,CAACyC,QAAQ,CAAC;QACb9B,iBAAiB,EAAE;MACrB,CAAC,CAAC;MACF4B,eAAe,CAACxB,CAAC,EAAEhC,sBAAsB,CAACiB,KAAK,CAAC,CAAC;MACjD,IAAI;QACF;QACA;QACAe,CAAC,CAAC2B,YAAY,CAACC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;MAC1C,CAAC,CAAC,OAAOC,KAAK,EAAE;QACd;MAAA;IAEJ,CAAC;IACD5C,KAAK,CAAC6C,WAAW,GAAG,UAAU9B,CAAC,EAAE;MAC/B,IAAI+B,eAAe,GAAG9C,KAAK,CAACiB,KAAK,CAACC,OAAO,CAAC4B,eAAe;MACzD/B,CAAC,CAACgC,cAAc,CAAC,CAAC;MAClBhC,CAAC,CAACyB,eAAe,CAAC,CAAC;MACnBM,eAAe,CAAC/B,CAAC,EAAEhC,sBAAsB,CAACiB,KAAK,CAAC,CAAC;IACnD,CAAC;IACDA,KAAK,CAACgD,UAAU,GAAG,UAAUjC,CAAC,EAAE;MAC9B,IAAIkC,cAAc,GAAGjD,KAAK,CAACiB,KAAK,CAACC,OAAO,CAAC+B,cAAc;MACvDlC,CAAC,CAACgC,cAAc,CAAC,CAAC;MAClBhC,CAAC,CAACyB,eAAe,CAAC,CAAC;MACnBS,cAAc,CAAClC,CAAC,EAAEhC,sBAAsB,CAACiB,KAAK,CAAC,CAAC;IAClD,CAAC;IACDA,KAAK,CAACkD,WAAW,GAAG,UAAUnC,CAAC,EAAE;MAC/B,IAAIoC,eAAe,GAAGnD,KAAK,CAACiB,KAAK,CAACC,OAAO,CAACiC,eAAe;MACzDpC,CAAC,CAACyB,eAAe,CAAC,CAAC;MACnBW,eAAe,CAACpC,CAAC,EAAEhC,sBAAsB,CAACiB,KAAK,CAAC,CAAC;IACnD,CAAC;IACDA,KAAK,CAACoD,SAAS,GAAG,UAAUrC,CAAC,EAAE;MAC7B,IAAIsC,aAAa,GAAGrD,KAAK,CAACiB,KAAK,CAACC,OAAO,CAACmC,aAAa;MACrDtC,CAAC,CAACyB,eAAe,CAAC,CAAC;MACnBxC,KAAK,CAACyC,QAAQ,CAAC;QACb9B,iBAAiB,EAAE;MACrB,CAAC,CAAC;MACF0C,aAAa,CAACtC,CAAC,EAAEhC,sBAAsB,CAACiB,KAAK,CAAC,CAAC;IACjD,CAAC;IACDA,KAAK,CAACsD,MAAM,GAAG,UAAUvC,CAAC,EAAE;MAC1B,IAAIwC,UAAU,GAAGvD,KAAK,CAACiB,KAAK,CAACC,OAAO,CAACqC,UAAU;MAC/CxC,CAAC,CAACgC,cAAc,CAAC,CAAC;MAClBhC,CAAC,CAACyB,eAAe,CAAC,CAAC;MACnBxC,KAAK,CAACyC,QAAQ,CAAC;QACb9B,iBAAiB,EAAE;MACrB,CAAC,CAAC;MACF4C,UAAU,CAACxC,CAAC,EAAEhC,sBAAsB,CAACiB,KAAK,CAAC,CAAC;IAC9C,CAAC;IACD;IACAA,KAAK,CAACwD,QAAQ,GAAG,UAAUzC,CAAC,EAAE;MAC5B,IAAI0C,YAAY,GAAGzD,KAAK,CAACiB,KAAK;QAC5ByC,OAAO,GAAGD,YAAY,CAACC,OAAO;QAC9BC,YAAY,GAAGF,YAAY,CAACvC,OAAO,CAACyC,YAAY;MAClD,IAAID,OAAO,EAAE;MACbC,YAAY,CAAC5C,CAAC,EAAEtB,2BAA2B,CAACO,KAAK,CAACiB,KAAK,CAAC,CAAC;IAC3D,CAAC;IACD;IACAjB,KAAK,CAAC4D,eAAe,GAAG,UAAUC,IAAI,EAAE;MACtC7D,KAAK,CAACY,YAAY,GAAGiD,IAAI;IAC3B,CAAC;IACD7D,KAAK,CAAC8D,YAAY,GAAG,YAAY;MAC/B,IAAIC,QAAQ,GAAG/D,KAAK,CAACiB,KAAK,CAAC8C,QAAQ;MACnC,IAAI/D,KAAK,CAACgE,MAAM,CAAC,CAAC,EAAE;QAClB,OAAO,IAAI;MACb;MACA,OAAOD,QAAQ,GAAGrE,SAAS,GAAGC,UAAU;IAC1C,CAAC;IACDK,KAAK,CAACiE,WAAW,GAAG,YAAY;MAC9B,IAAIC,QAAQ,GAAGlE,KAAK,CAACiB,KAAK,CAACiD,QAAQ;MACnC,IAAIC,WAAW,GAAGnE,KAAK,CAACiB,KAAK,CAACC,OAAO,CAACiD,WAAW;MACjD,IAAIC,IAAI,GAAG5E,SAAS,CAAC2E,WAAW,EAAED,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC/CG,QAAQ,GAAGD,IAAI,CAACC,QAAQ;MAC1B,OAAO,CAAC,CAAC,CAACA,QAAQ,IAAI,EAAE,EAAElE,MAAM;IAClC,CAAC;IACDH,KAAK,CAACgE,MAAM,GAAG,YAAY;MACzB,IAAIM,YAAY,GAAGtE,KAAK,CAACiB,KAAK;QAC5B+C,MAAM,GAAGM,YAAY,CAACN,MAAM;QAC5BO,MAAM,GAAGD,YAAY,CAACC,MAAM;MAC9B,IAAIC,QAAQ,GAAGxE,KAAK,CAACiB,KAAK,CAACC,OAAO,CAACsD,QAAQ;MAC3C,IAAIP,WAAW,GAAGjE,KAAK,CAACiE,WAAW,CAAC,CAAC;MACrC,IAAID,MAAM,KAAK,KAAK,EAAE;QACpB,OAAO,KAAK;MACd;MACA,OAAOA,MAAM,IAAI,CAACQ,QAAQ,IAAI,CAACP,WAAW,IAAIO,QAAQ,IAAID,MAAM,IAAI,CAACN,WAAW;IAClF,CAAC;IACDjE,KAAK,CAACwB,UAAU,GAAG,YAAY;MAC7B,IAAIiD,QAAQ,GAAGzE,KAAK,CAACiB,KAAK,CAACwD,QAAQ;MACnC,IAAIC,YAAY,GAAG1E,KAAK,CAACiB,KAAK,CAACC,OAAO,CAACuD,QAAQ;MAC/C,OAAO,CAAC,EAAEC,YAAY,IAAID,QAAQ,CAAC;IACrC,CAAC;IACDzE,KAAK,CAAC8B,WAAW,GAAG,YAAY;MAC9B,IAAI6C,SAAS,GAAG3E,KAAK,CAACiB,KAAK,CAAC0D,SAAS;MACrC,IAAIC,aAAa,GAAG5E,KAAK,CAACiB,KAAK,CAACC,OAAO,CAACyD,SAAS;MACjD;MACA,IAAI,CAACC,aAAa,IAAID,SAAS,KAAK,KAAK,EAAE,OAAO,KAAK;MACvD,OAAOC,aAAa;IACtB,CAAC;IACD;IACA5E,KAAK,CAAC6E,YAAY,GAAG,UAAU5D,KAAK,EAAE;MACpC,IAAI8C,QAAQ,GAAG9C,KAAK,CAAC8C,QAAQ;QAC3BL,OAAO,GAAGzC,KAAK,CAACyC,OAAO;QACvBa,MAAM,GAAGtD,KAAK,CAACsD,MAAM;MACvB,IAAIO,mBAAmB,GAAG9E,KAAK,CAACiB,KAAK,CAACC,OAAO;QAC3CsD,QAAQ,GAAGM,mBAAmB,CAACN,QAAQ;QACvCO,UAAU,GAAGD,mBAAmB,CAACC,UAAU;MAC7C,IAAIrB,OAAO,EAAE;QACX;MACF;MACA;MACA,IAAIc,QAAQ,IAAIT,QAAQ,IAAI,CAAC/D,KAAK,CAACgE,MAAM,CAAC,CAAC,EAAE;QAC3C;QACA;QACA,IAAI,CAAChE,KAAK,CAACiE,WAAW,CAAC,CAAC,IAAI,CAACM,MAAM,EAAE;UACnCQ,UAAU,CAACtF,2BAA2B,CAACO,KAAK,CAACiB,KAAK,CAAC,CAAC;QACtD;MACF;IACF,CAAC;IACDjB,KAAK,CAACgF,WAAW,GAAG,YAAY;MAC9B,IAAIC,YAAY,GAAGjF,KAAK,CAACiB,KAAK;QAC5BiE,IAAI,GAAGD,YAAY,CAACC,IAAI;QACxBC,SAAS,GAAGF,YAAY,CAAC/D,OAAO,CAACiE,SAAS;MAC5C,OAAO,CAAC,EAAEA,SAAS,KAAK,CAACA,SAAS,CAACC,aAAa,IAAID,SAAS,CAACC,aAAa,CAACF,IAAI,CAAC,CAAC,CAAC;IACrF,CAAC;IACD;IACAlF,KAAK,CAACqF,iBAAiB,GAAG,YAAY;MACpC,IAAIC,oBAAoB,GAAGtF,KAAK,CAACiB,KAAK,CAACC,OAAO;QAC5CiE,SAAS,GAAGG,oBAAoB,CAACH,SAAS;QAC1CI,SAAS,GAAGD,oBAAoB,CAACC,SAAS;MAC5C,OAAO,CAACJ,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACK,IAAI,IAAI,aAAanG,KAAK,CAACoG,aAAa,CAAC,MAAM,EAAE;QACvHC,SAAS,EAAE,EAAE,CAACjF,MAAM,CAAC8E,SAAS,EAAE,iBAAiB;MACnD,CAAC,EAAEJ,SAAS,CAACK,IAAI,CAAC,GAAG,IAAI;IAC3B,CAAC;IACD;IACAxF,KAAK,CAAC2F,qBAAqB,GAAG,UAAU3B,MAAM,EAAE;MAC9C,IAAI4B,qBAAqB,GAAG5F,KAAK,CAACiB,KAAK,CAAC4E,YAAY;MACpD,IAAIC,mBAAmB,GAAG9F,KAAK,CAACiB,KAAK,CAACC,OAAO,CAAC2E,YAAY;MAC1D,IAAIA,YAAY,GAAGD,qBAAqB,IAAIE,mBAAmB;MAC/D;MACA,IAAI,OAAOD,YAAY,KAAK,UAAU,EAAE;QACtC,OAAOA,YAAY,CAACjH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoB,KAAK,CAACiB,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACpE+C,MAAM,EAAEA;QACV,CAAC,CAAC,CAAC;MACL;MACA,OAAO6B,YAAY;IACrB,CAAC;IACD;IACA7F,KAAK,CAAC+F,cAAc,GAAG,YAAY;MACjC,IAAIhC,QAAQ,GAAG/D,KAAK,CAACiB,KAAK,CAAC8C,QAAQ;MACnC,IAAIwB,SAAS,GAAGvF,KAAK,CAACiB,KAAK,CAACC,OAAO,CAACqE,SAAS;MAC7C,IAAIvF,KAAK,CAACgE,MAAM,CAAC,CAAC,EAAE;QAClB;QACA,IAAIgC,gBAAgB,GAAGhG,KAAK,CAAC2F,qBAAqB,CAAC,IAAI,CAAC;QACxD,OAAOK,gBAAgB,KAAK,KAAK,GAAG,aAAa3G,KAAK,CAACoG,aAAa,CAAC,MAAM,EAAE;UAC3EC,SAAS,EAAEvG,UAAU,CAAC,EAAE,CAACsB,MAAM,CAAC8E,SAAS,EAAE,WAAW,CAAC,EAAE,EAAE,CAAC9E,MAAM,CAAC8E,SAAS,EAAE,gBAAgB,CAAC;QACjG,CAAC,EAAES,gBAAgB,CAAC,GAAG,IAAI;MAC7B;MACA,IAAIC,WAAW,GAAG9G,UAAU,CAAC,EAAE,CAACsB,MAAM,CAAC8E,SAAS,EAAE,WAAW,CAAC,EAAE,EAAE,CAAC9E,MAAM,CAAC8E,SAAS,EAAE,YAAY,CAAC,CAAC9E,MAAM,CAACsD,QAAQ,GAAGrE,SAAS,GAAGC,UAAU,CAAC,CAAC;MAC7I,IAAIuG,eAAe,GAAGlG,KAAK,CAAC2F,qBAAqB,CAAC,KAAK,CAAC;MACxD,OAAOO,eAAe,KAAK,KAAK,GAAG,aAAa7G,KAAK,CAACoG,aAAa,CAAC,MAAM,EAAE;QAC1EU,OAAO,EAAEnG,KAAK,CAACwD,QAAQ;QACvBkC,SAAS,EAAEO;MACb,CAAC,EAAEC,eAAe,CAAC,GAAG,IAAI;IAC5B,CAAC;IACD;IACA;IACAlG,KAAK,CAACoG,cAAc,GAAG,YAAY;MACjC,IAAIC,YAAY,GAAGrG,KAAK,CAACiB,KAAK;QAC5BW,OAAO,GAAGyE,YAAY,CAACzE,OAAO;QAC9B0E,WAAW,GAAGD,YAAY,CAACC,WAAW;QACtC3E,eAAe,GAAG0E,YAAY,CAAC1E,eAAe;MAChD,IAAI4D,SAAS,GAAGvF,KAAK,CAACiB,KAAK,CAACC,OAAO,CAACqE,SAAS;MAC7C,IAAId,QAAQ,GAAGzE,KAAK,CAACwB,UAAU,CAAC,CAAC;MACjC,IAAImD,SAAS,GAAG3E,KAAK,CAAC8B,WAAW,CAAC,CAAC;MACnC,IAAI,CAAC6C,SAAS,EAAE,OAAO,IAAI;MAC3B;MACA,IAAI4B,OAAO,GAAG,OAAO5B,SAAS,KAAK,SAAS,GAAGA,SAAS,GAAG,IAAI;MAC/D,OAAO,aAAatF,KAAK,CAACoG,aAAa,CAAC,MAAM,EAAE;QAC9CC,SAAS,EAAEvG,UAAU,CAAC,EAAE,CAACsB,MAAM,CAAC8E,SAAS,EAAE,WAAW,CAAC,EAAE3D,OAAO,IAAI,EAAE,CAACnB,MAAM,CAAC8E,SAAS,EAAE,mBAAmB,CAAC,EAAE,CAAC3D,OAAO,IAAI0E,WAAW,IAAI,EAAE,CAAC7F,MAAM,CAAC8E,SAAS,EAAE,yBAAyB,CAAC,EAAE,CAACd,QAAQ,IAAI9C,eAAe,KAAK,EAAE,CAAClB,MAAM,CAAC8E,SAAS,EAAE,oBAAoB,CAAC,CAAC;QACvQY,OAAO,EAAEnG,KAAK,CAACqB;MACjB,CAAC,EAAEkF,OAAO,CAAC;IACb,CAAC;IACD;IACAvG,KAAK,CAACwG,UAAU,GAAG,YAAY;MAC7B,IAAI9C,OAAO,GAAG1D,KAAK,CAACiB,KAAK,CAACyC,OAAO;MACjC,IAAI6B,SAAS,GAAGvF,KAAK,CAACiB,KAAK,CAACC,OAAO,CAACqE,SAAS;MAC7C,OAAO,aAAalG,KAAK,CAACoG,aAAa,CAAC,MAAM,EAAE;QAC9CC,SAAS,EAAEvG,UAAU,CAAC,EAAE,CAACsB,MAAM,CAAC8E,SAAS,EAAE,UAAU,CAAC,EAAE,EAAE,CAAC9E,MAAM,CAAC8E,SAAS,EAAE,SAAS,CAAC,CAAC9E,MAAM,CAACT,KAAK,CAAC8D,YAAY,CAAC,CAAC,IAAI,MAAM,CAAC,EAAEJ,OAAO,IAAI,EAAE,CAACjD,MAAM,CAAC8E,SAAS,EAAE,eAAe,CAAC;MAClL,CAAC,CAAC;IACJ,CAAC;IACD;IACAvF,KAAK,CAACyG,cAAc,GAAG,YAAY;MACjC,IAAI9F,iBAAiB,GAAGX,KAAK,CAACU,KAAK,CAACC,iBAAiB;MACrD,IAAI+F,YAAY,GAAG1G,KAAK,CAACiB,KAAK;QAC5B0F,kBAAkB,GAAGD,YAAY,CAACE,KAAK;QACvCA,KAAK,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG/G,YAAY,GAAG+G,kBAAkB;QACzEE,QAAQ,GAAGH,YAAY,CAACG,QAAQ;QAChCrB,IAAI,GAAGkB,YAAY,CAAClB,IAAI;QACxB9B,OAAO,GAAGgD,YAAY,CAAChD,OAAO;QAC9BwB,IAAI,GAAGwB,YAAY,CAACxB,IAAI;MAC1B,IAAI4B,oBAAoB,GAAG9G,KAAK,CAACiB,KAAK,CAACC,OAAO;QAC5CqE,SAAS,GAAGuB,oBAAoB,CAACvB,SAAS;QAC1CwB,QAAQ,GAAGD,oBAAoB,CAACC,QAAQ;QACxCC,QAAQ,GAAGF,oBAAoB,CAACtB,IAAI;QACpChB,QAAQ,GAAGsC,oBAAoB,CAACtC,QAAQ;QACxCyC,WAAW,GAAGH,oBAAoB,CAACG,WAAW;MAChD,IAAIxC,QAAQ,GAAGzE,KAAK,CAACwB,UAAU,CAAC,CAAC;MACjC,IAAI0F,SAAS,GAAG,EAAE,CAACzG,MAAM,CAAC8E,SAAS,EAAE,uBAAuB,CAAC;MAC7D;MACA,IAAI4B,KAAK;MACT,IAAIJ,QAAQ,EAAE;QACZ,IAAIK,WAAW,GAAG5B,IAAI,IAAIwB,QAAQ;QAClCG,KAAK,GAAGC,WAAW,GAAG,aAAa/H,KAAK,CAACoG,aAAa,CAAC,MAAM,EAAE;UAC7DC,SAAS,EAAEvG,UAAU,CAAC,EAAE,CAACsB,MAAM,CAAC8E,SAAS,EAAE,UAAU,CAAC,EAAE,EAAE,CAAC9E,MAAM,CAAC8E,SAAS,EAAE,kBAAkB,CAAC;QAClG,CAAC,EAAE,OAAO6B,WAAW,KAAK,UAAU,GAAGA,WAAW,CAACpH,KAAK,CAACiB,KAAK,CAAC,GAAGmG,WAAW,CAAC,GAAGpH,KAAK,CAACwG,UAAU,CAAC,CAAC;MACrG,CAAC,MAAM,IAAIhC,QAAQ,IAAId,OAAO,EAAE;QAC9ByD,KAAK,GAAGnH,KAAK,CAACwG,UAAU,CAAC,CAAC;MAC5B;MACA;MACA,IAAIa,SAAS;MACb,IAAI,OAAOT,KAAK,KAAK,UAAU,EAAE;QAC/BS,SAAS,GAAGT,KAAK,CAAC1B,IAAI,CAAC;MACzB,CAAC,MAAM,IAAI+B,WAAW,EAAE;QACtBI,SAAS,GAAGJ,WAAW,CAAC/B,IAAI,CAAC;MAC/B,CAAC,MAAM;QACLmC,SAAS,GAAGT,KAAK;MACnB;MACA,IAAIU,MAAM,GAAG,aAAajI,KAAK,CAACoG,aAAa,CAAC,MAAM,EAAE;QACpDC,SAAS,EAAE,EAAE,CAACjF,MAAM,CAAC8E,SAAS,EAAE,QAAQ;MAC1C,CAAC,EAAE8B,SAAS,CAAC;MACb,OAAO,aAAahI,KAAK,CAACoG,aAAa,CAAC,MAAM,EAAE;QAC9C8B,GAAG,EAAEvH,KAAK,CAAC4D,eAAe;QAC1BgD,KAAK,EAAE,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG,EAAE;QAC7ClB,SAAS,EAAEvG,UAAU,CAAC,EAAE,CAACsB,MAAM,CAACyG,SAAS,CAAC,EAAE,EAAE,CAACzG,MAAM,CAACyG,SAAS,EAAE,GAAG,CAAC,CAACzG,MAAM,CAACT,KAAK,CAAC8D,YAAY,CAAC,CAAC,IAAI,QAAQ,CAAC,EAAE,CAACW,QAAQ,KAAKoC,QAAQ,IAAIlG,iBAAiB,CAAC,IAAI,EAAE,CAACF,MAAM,CAAC8E,SAAS,EAAE,gBAAgB,CAAC,CAAC;QACvMvD,YAAY,EAAEhC,KAAK,CAACgC,YAAY;QAChCE,YAAY,EAAElC,KAAK,CAACkC,YAAY;QAChCE,aAAa,EAAEpC,KAAK,CAACoC,aAAa;QAClC+D,OAAO,EAAEnG,KAAK,CAACc,eAAe;QAC9B0G,aAAa,EAAExH,KAAK,CAACsB;MACvB,CAAC,EAAE6F,KAAK,EAAEG,MAAM,EAAEtH,KAAK,CAACyH,mBAAmB,CAAC,CAAC,CAAC;IAChD,CAAC;IACD;IACAzH,KAAK,CAACyH,mBAAmB,GAAG,YAAY;MACtC,IAAIC,YAAY,GAAG1H,KAAK,CAACiB,KAAK;QAC5BwD,QAAQ,GAAGiD,YAAY,CAACjD,QAAQ;QAChCP,QAAQ,GAAGwD,YAAY,CAACxD,QAAQ;MAClC,IAAIyD,oBAAoB,GAAG3H,KAAK,CAACiB,KAAK,CAACC,OAAO;QAC5CiE,SAAS,GAAGwC,oBAAoB,CAACxC,SAAS;QAC1CyC,eAAe,GAAGD,oBAAoB,CAACC,eAAe;QACtDC,YAAY,GAAGF,oBAAoB,CAACE,YAAY;QAChDtC,SAAS,GAAGoC,oBAAoB,CAACpC,SAAS;QAC1CuC,MAAM,GAAGH,oBAAoB,CAACG,MAAM;QACpCC,mBAAmB,GAAGJ,oBAAoB,CAACI,mBAAmB;QAC9DC,eAAe,GAAGL,oBAAoB,CAACK,eAAe;QACtDC,SAAS,GAAGN,oBAAoB,CAACM,SAAS;MAC5C,IAAIC,aAAa,GAAG,CAAC,CAAC/C,SAAS;MAC/B;MACA,IAAIgD,aAAa,GAAG,CAAC1D,QAAQ,IAAIyD,aAAa,IAAIF,eAAe,KAAK9D,QAAQ;MAC9E;MACA;MACA;MACA;MACA,IAAIkE,YAAY,GAAGN,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAG9H,KAAK,CAACa,WAAW;MACpFb,KAAK,CAACa,WAAW,GAAGiH,MAAM;MAC1B,OAAOK,aAAa,GAAGJ,mBAAmB,CAAC;QACzCF,YAAY,EAAEA,YAAY;QAC1BD,eAAe,EAAEA,eAAe;QAChCE,MAAM,EAAEM,YAAY;QACpB7C,SAAS,EAAEA,SAAS;QACpB0C,SAAS,EAAEA;MACb,CAAC,CAAC,GAAG,IAAI;IACX,CAAC;IACD,OAAOjI,KAAK;EACd;EACAlB,YAAY,CAACe,gBAAgB,EAAE,CAAC;IAC9BwI,GAAG,EAAE,mBAAmB;IACxBC,KAAK;IACL;IACA,SAASC,iBAAiBA,CAAA,EAAG;MAC3B,IAAI,CAAC1D,YAAY,CAAC,IAAI,CAAC5D,KAAK,CAAC;IAC/B;EACF,CAAC,EAAE;IACDoH,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE,SAASE,kBAAkBA,CAAA,EAAG;MACnC,IAAI,CAAC3D,YAAY,CAAC,IAAI,CAAC5D,KAAK,CAAC;IAC/B;EACF,CAAC,EAAE;IACDoH,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,SAASnH,YAAYA,CAAA,EAAG;MAC7B,IAAIsH,UAAU,GAAG,IAAI,CAACxH,KAAK,CAACwH,UAAU;MACtC,IAAIC,cAAc,GAAG,IAAI,CAACzH,KAAK,CAACC,OAAO,CAACuH,UAAU;MAClD;MACA,IAAI,OAAOA,UAAU,KAAK,SAAS,EAAE;QACnC,OAAOA,UAAU;MACnB;MACA,OAAOC,cAAc;IACvB;EACF,CAAC,EAAE;IACDL,GAAG,EAAE,QAAQ;IACbC,KAAK;IACL;IACA,SAASK,MAAMA,CAAA,EAAG;MAChB,IAAIC,WAAW;MACf,IAAIC,YAAY,GAAG,IAAI,CAAC5H,KAAK;QAC3BiD,QAAQ,GAAG2E,YAAY,CAAC3E,QAAQ;QAChCwB,SAAS,GAAGmD,YAAY,CAACnD,SAAS;QAClCoD,KAAK,GAAGD,YAAY,CAACC,KAAK;QAC1BC,QAAQ,GAAGF,YAAY,CAACE,QAAQ;QAChCC,cAAc,GAAGH,YAAY,CAACG,cAAc;QAC5CC,iBAAiB,GAAGJ,YAAY,CAACI,iBAAiB;QAClDjF,MAAM,GAAG6E,YAAY,CAAC7E,MAAM;QAC5BkF,OAAO,GAAGL,YAAY,CAACK,OAAO;QAC9BC,KAAK,GAAGN,YAAY,CAACM,KAAK;QAC1BpF,QAAQ,GAAG8E,YAAY,CAAC9E,QAAQ;QAChC8C,QAAQ,GAAGgC,YAAY,CAAChC,QAAQ;QAChCjF,OAAO,GAAGiH,YAAY,CAACjH,OAAO;QAC9B0E,WAAW,GAAGuC,YAAY,CAACvC,WAAW;QACtC5C,OAAO,GAAGmF,YAAY,CAACnF,OAAO;QAC9B0F,MAAM,GAAGP,YAAY,CAACO,MAAM;QAC5BC,MAAM,GAAGR,YAAY,CAACQ,MAAM;QAC5BnE,IAAI,GAAG2D,YAAY,CAAC3D,IAAI;QACxBoE,WAAW,GAAGT,YAAY,CAACS,WAAW;QACtCb,UAAU,GAAGI,YAAY,CAACJ,UAAU;QACpCc,UAAU,GAAG5K,wBAAwB,CAACkK,YAAY,EAAE3J,SAAS,CAAC;MAChE,IAAIsK,oBAAoB,GAAG,IAAI,CAACvI,KAAK,CAACC,OAAO;QAC3CqE,SAAS,GAAGiE,oBAAoB,CAACjE,SAAS;QAC1CkE,cAAc,GAAGD,oBAAoB,CAACC,cAAc;QACpDtF,WAAW,GAAGqF,oBAAoB,CAACrF,WAAW;QAC9CuF,gBAAgB,GAAGF,oBAAoB,CAACE,gBAAgB;QACxDC,aAAa,GAAGH,oBAAoB,CAACG,aAAa;QAClDC,eAAe,GAAGJ,oBAAoB,CAACI,eAAe;MACxD,IAAInF,QAAQ,GAAG,IAAI,CAACjD,UAAU,CAAC,CAAC;MAChC,IAAIqI,wBAAwB,GAAGzK,SAAS,CAACmK,UAAU,EAAE;QACnDO,IAAI,EAAE,IAAI;QACV5E,IAAI,EAAE;MACR,CAAC,CAAC;MACF,IAAI6E,KAAK,GAAGvK,SAAS,CAAC2E,WAAW,EAAED,QAAQ,CAAC,IAAI,CAAC,CAAC;QAChD8F,KAAK,GAAGD,KAAK,CAACC,KAAK;MACrB,IAAIC,SAAS,GAAGd,KAAK,CAACA,KAAK,CAAChJ,MAAM,GAAG,CAAC,CAAC;MACvC,IAAI+J,eAAe,GAAG,IAAI,CAAClF,WAAW,CAAC,CAAC;MACxC,IAAImF,wBAAwB,GAAG,CAAC1F,QAAQ,IAAIyF,eAAe;MAC3D,IAAIE,QAAQ,GAAGR,eAAe,KAAK1F,QAAQ;MAC3C,IAAImG,YAAY,GAAG5B,UAAU,KAAK6B,SAAS,GAAG;QAC5C,eAAe,EAAE,CAAC,CAAC7B;MACrB,CAAC,GAAG6B,SAAS;MACb,OAAO,aAAajL,KAAK,CAACoG,aAAa,CAAC,KAAK,EAAEhH,QAAQ,CAAC;QACtD8I,GAAG,EAAE6B,MAAM;QACX1D,SAAS,EAAEvG,UAAU,CAACuG,SAAS,EAAE,EAAE,CAACjF,MAAM,CAAC8E,SAAS,EAAE,WAAW,CAAC,GAAGqD,WAAW,GAAG,CAAC,CAAC,EAAElK,eAAe,CAACkK,WAAW,EAAE,EAAE,CAACnI,MAAM,CAAC8E,SAAS,EAAE,oBAAoB,CAAC,EAAEd,QAAQ,CAAC,EAAE/F,eAAe,CAACkK,WAAW,EAAE,EAAE,CAACnI,MAAM,CAAC8E,SAAS,EAAE,qBAAqB,CAAC,CAAC9E,MAAM,CAACsD,QAAQ,GAAG,MAAM,GAAG,OAAO,CAAC,EAAE,CAACC,MAAM,CAAC,EAAEtF,eAAe,CAACkK,WAAW,EAAE,EAAE,CAACnI,MAAM,CAAC8E,SAAS,EAAE,4BAA4B,CAAC,EAAE3D,OAAO,CAAC,EAAElD,eAAe,CAACkK,WAAW,EAAE,EAAE,CAACnI,MAAM,CAAC8E,SAAS,EAAE,kCAAkC,CAAC,EAAEe,WAAW,CAAC,EAAE5H,eAAe,CAACkK,WAAW,EAAE,EAAE,CAACnI,MAAM,CAAC8E,SAAS,EAAE,oBAAoB,CAAC,EAAEsB,QAAQ,CAAC,EAAEnI,eAAe,CAACkK,WAAW,EAAE,EAAE,CAACnI,MAAM,CAAC8E,SAAS,EAAE,mBAAmB,CAAC,EAAE7B,OAAO,CAAC,EAAEhF,eAAe,CAACkK,WAAW,EAAE,EAAE,CAACnI,MAAM,CAAC8E,SAAS,EAAE,kBAAkB,CAAC,EAAE8D,MAAM,CAAC,EAAE3K,eAAe,CAACkK,WAAW,EAAE,EAAE,CAACnI,MAAM,CAAC8E,SAAS,EAAE,qBAAqB,CAAC,EAAE0E,SAAS,CAAC,EAAEvL,eAAe,CAACkK,WAAW,EAAE,EAAE,CAACnI,MAAM,CAAC8E,SAAS,EAAE,qBAAqB,CAAC,EAAE2E,eAAe,CAAC,EAAExL,eAAe,CAACkK,WAAW,EAAE,UAAU,EAAEwB,QAAQ,CAAC,EAAE1L,eAAe,CAACkK,WAAW,EAAE,aAAa,EAAEe,aAAa,KAAKzF,QAAQ,CAAC,EAAExF,eAAe,CAACkK,WAAW,EAAE,gBAAgB,EAAEc,gBAAgB,KAAKxF,QAAQ,CAAC,EAAExF,eAAe,CAACkK,WAAW,EAAE,WAAW,EAAE,CAACnE,QAAQ,IAAIsE,QAAQ,CAAC,EAAErK,eAAe,CAACkK,WAAW,EAAE,mBAAmB,EAAE,CAACnE,QAAQ,IAAIuE,cAAc,CAAC,EAAEtK,eAAe,CAACkK,WAAW,EAAE,sBAAsB,EAAE,CAACnE,QAAQ,IAAIwE,iBAAiB,CAAC,EAAEvK,eAAe,CAACkK,WAAW,EAAE,aAAa,EAAEa,cAAc,IAAIA,cAAc,CAAChK,2BAA2B,CAAC,IAAI,CAACwB,KAAK,CAAC,CAAC,CAAC,EAAE2H,WAAW,CAAC,CAAC;QACp8CE,KAAK,EAAEA;QACP;QAAA;;QAEA3D,SAAS,EAAEgF,wBAAwB;QACnC,cAAc,EAAEC,QAAQ;QACxB9H,WAAW,EAAE6H,wBAAwB,GAAG,IAAI,CAAC7H,WAAW,GAAGgI;QAC3D;QAAA;;QAEAzH,WAAW,EAAEqH,eAAe,GAAG,IAAI,CAACrH,WAAW,GAAGyH,SAAS;QAC3DtH,UAAU,EAAEkH,eAAe,GAAG,IAAI,CAAClH,UAAU,GAAGsH,SAAS;QACzDpH,WAAW,EAAEgH,eAAe,GAAG,IAAI,CAAChH,WAAW,GAAGoH,SAAS;QAC3DhH,MAAM,EAAE4G,eAAe,GAAG,IAAI,CAAC5G,MAAM,GAAGgH,SAAS;QACjDlH,SAAS,EAAE8G,eAAe,GAAG,IAAI,CAAC9G,SAAS,GAAGkH,SAAS;QACvDhB,WAAW,EAAEA;MACf,CAAC,EAAEe,YAAY,EAAER,wBAAwB,CAAC,EAAE,aAAaxK,KAAK,CAACoG,aAAa,CAAClG,MAAM,EAAE;QACnFgG,SAAS,EAAEA,SAAS;QACpByE,KAAK,EAAEA,KAAK;QACZd,OAAO,EAAEA,OAAO;QAChBC,KAAK,EAAEA;MACT,CAAC,CAAC,EAAE,IAAI,CAAC9D,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAACU,cAAc,CAAC,CAAC,EAAE,IAAI,CAACK,cAAc,CAAC,CAAC,EAAE,IAAI,CAACK,cAAc,CAAC,CAAC,CAAC;IACpG;EACF,CAAC,CAAC,CAAC;EACH,OAAO5G,gBAAgB;AACzB,CAAC,CAACR,KAAK,CAACkL,SAAS,CAAC;AAClB,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACvJ,KAAK,EAAE;EACpD,OAAO,aAAa5B,KAAK,CAACoG,aAAa,CAACnG,WAAW,CAACmL,QAAQ,EAAE,IAAI,EAAE,UAAUvJ,OAAO,EAAE;IACrF,OAAO,aAAa7B,KAAK,CAACoG,aAAa,CAAC5F,gBAAgB,EAAEpB,QAAQ,CAAC,CAAC,CAAC,EAAEwC,KAAK,EAAE;MAC5EC,OAAO,EAAEA;IACX,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ,CAAC;AACDsJ,eAAe,CAACE,WAAW,GAAG,UAAU;AACxCF,eAAe,CAACG,UAAU,GAAG,CAAC;AAC9B,eAAeH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}