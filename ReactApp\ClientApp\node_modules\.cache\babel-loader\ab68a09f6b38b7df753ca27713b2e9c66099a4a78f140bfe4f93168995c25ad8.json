{"ast": null, "code": "import { addYears } from './add-years';\n/**\n * A function that adds and subtracts decades from a `Date` object.\n *\n * @param date - The initial date value.\n * @param offset - The number of decades to add or subtract from the date.\n * @returns - A new `Date` instance.\n *\n * @example\n * ```ts-no-run\n * addDecades(new Date(2016, 5, 1), 5); // 2066-6-1\n * addDecades(new Date(2016, 5, 1), -5); // 1966-6-1\n * ```\n */\nexport var addDecades = function (value, offset) {\n  return addYears(value, 10 * offset);\n};", "map": {"version": 3, "names": ["addYears", "addDecades", "value", "offset"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/add-decades.js"], "sourcesContent": ["import { addYears } from './add-years';\n/**\n * A function that adds and subtracts decades from a `Date` object.\n *\n * @param date - The initial date value.\n * @param offset - The number of decades to add or subtract from the date.\n * @returns - A new `Date` instance.\n *\n * @example\n * ```ts-no-run\n * addDecades(new Date(2016, 5, 1), 5); // 2066-6-1\n * addDecades(new Date(2016, 5, 1), -5); // 1966-6-1\n * ```\n */\nexport var addDecades = function (value, offset) {\n    return addYears(value, 10 * offset);\n};\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,aAAa;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,UAAU,GAAG,SAAAA,CAAUC,KAAK,EAAEC,MAAM,EAAE;EAC7C,OAAOH,QAAQ,CAACE,KAAK,EAAE,EAAE,GAAGC,MAAM,CAAC;AACvC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}