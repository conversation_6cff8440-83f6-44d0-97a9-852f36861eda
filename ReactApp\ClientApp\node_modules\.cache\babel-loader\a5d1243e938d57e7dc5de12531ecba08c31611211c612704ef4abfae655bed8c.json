{"ast": null, "code": "import stackElements from './stack-elements';\nimport createStackElements from './create-stack-elements';\nexport default function stack(elements) {\n  stackElements(createStackElements(elements), \"x\", \"y\", \"width\");\n}", "map": {"version": 3, "names": ["stackElements", "createStackElements", "stack", "elements"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/alignment/stack.js"], "sourcesContent": ["import stackElements from './stack-elements';\nimport createStackElements from './create-stack-elements';\n\nexport default function stack(elements) {\n    stackElements(createStackElements(elements), \"x\", \"y\", \"width\");\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,mBAAmB,MAAM,yBAAyB;AAEzD,eAAe,SAASC,KAAKA,CAACC,QAAQ,EAAE;EACpCH,aAAa,CAACC,mBAAmB,CAACE,QAAQ,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC;AACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}