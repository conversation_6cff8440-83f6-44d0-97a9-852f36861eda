{"ast": null, "code": "import ElementsArray from '../shapes/elements-array';\nvar StopsArray = function (ElementsArray) {\n  function StopsArray() {\n    ElementsArray.apply(this, arguments);\n  }\n  if (ElementsArray) StopsArray.__proto__ = ElementsArray;\n  StopsArray.prototype = Object.create(ElementsArray && ElementsArray.prototype);\n  StopsArray.prototype.constructor = StopsArray;\n  StopsArray.prototype._change = function _change() {\n    this.optionsChange({\n      field: \"stops\"\n    });\n  };\n  return StopsArray;\n}(ElementsArray);\nexport default StopsArray;", "map": {"version": 3, "names": ["ElementsArray", "StopsArray", "apply", "arguments", "__proto__", "prototype", "Object", "create", "constructor", "_change", "optionsChange", "field"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/gradients/stops-array.js"], "sourcesContent": ["import ElementsArray from '../shapes/elements-array';\n\nvar StopsArray = (function (ElementsArray) {\n    function StopsArray () {\n        ElementsArray.apply(this, arguments);\n    }\n\n    if ( ElementsArray ) StopsArray.__proto__ = ElementsArray;\n    StopsArray.prototype = Object.create( ElementsArray && ElementsArray.prototype );\n    StopsArray.prototype.constructor = StopsArray;\n\n    StopsArray.prototype._change = function _change () {\n        this.optionsChange({\n            field: \"stops\"\n        });\n    };\n\n    return StopsArray;\n}(ElementsArray));\n\nexport default StopsArray;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0BAA0B;AAEpD,IAAIC,UAAU,GAAI,UAAUD,aAAa,EAAE;EACvC,SAASC,UAAUA,CAAA,EAAI;IACnBD,aAAa,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACxC;EAEA,IAAKH,aAAa,EAAGC,UAAU,CAACG,SAAS,GAAGJ,aAAa;EACzDC,UAAU,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAEP,aAAa,IAAIA,aAAa,CAACK,SAAU,CAAC;EAChFJ,UAAU,CAACI,SAAS,CAACG,WAAW,GAAGP,UAAU;EAE7CA,UAAU,CAACI,SAAS,CAACI,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;IAC/C,IAAI,CAACC,aAAa,CAAC;MACfC,KAAK,EAAE;IACX,CAAC,CAAC;EACN,CAAC;EAED,OAAOV,UAAU;AACrB,CAAC,CAACD,aAAa,CAAE;AAEjB,eAAeC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}