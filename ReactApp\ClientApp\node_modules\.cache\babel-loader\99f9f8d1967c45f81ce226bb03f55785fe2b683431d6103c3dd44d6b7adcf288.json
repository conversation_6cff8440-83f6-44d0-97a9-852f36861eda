{"ast": null, "code": "export default function unbindEvents(element, events) {\n  if (events === void 0) events = {};\n  for (var name in events) {\n    var eventNames = name.trim().split(\" \");\n    for (var idx = 0; idx < eventNames.length; idx++) {\n      element.removeEventListener(eventNames[idx], events[name], false);\n    }\n  }\n}", "map": {"version": 3, "names": ["unbindEvents", "element", "events", "name", "eventNames", "trim", "split", "idx", "length", "removeEventListener"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/util/unbind-events.js"], "sourcesContent": ["export default function unbindEvents(element, events) {\n    if ( events === void 0 ) events = {};\n\n    for (var name in events) {\n        var eventNames = name.trim().split(\" \");\n        for (var idx = 0; idx < eventNames.length; idx++) {\n            element.removeEventListener(eventNames[idx], events[name], false);\n        }\n    }\n}"], "mappings": "AAAA,eAAe,SAASA,YAAYA,CAACC,OAAO,EAAEC,MAAM,EAAE;EAClD,IAAKA,MAAM,KAAK,KAAK,CAAC,EAAGA,MAAM,GAAG,CAAC,CAAC;EAEpC,KAAK,IAAIC,IAAI,IAAID,MAAM,EAAE;IACrB,IAAIE,UAAU,GAAGD,IAAI,CAACE,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;IACvC,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGH,UAAU,CAACI,MAAM,EAAED,GAAG,EAAE,EAAE;MAC9CN,OAAO,CAACQ,mBAAmB,CAACL,UAAU,CAACG,GAAG,CAAC,EAAEL,MAAM,CAACC,IAAI,CAAC,EAAE,KAAK,CAAC;IACrE;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}