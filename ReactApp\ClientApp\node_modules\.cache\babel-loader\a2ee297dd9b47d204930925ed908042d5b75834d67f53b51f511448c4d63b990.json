{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\"];\nimport toArray from \"rc-util/es/Children/toArray\";\nimport omit from \"rc-util/es/omit\";\nimport warning from \"rc-util/es/warning\";\nimport getEntity from './keyUtil';\nexport function getPosition(level, index) {\n  return \"\".concat(level, \"-\").concat(index);\n}\nexport function isTreeNode(node) {\n  return node && node.type && node.type.isTreeNode;\n}\nexport function getKey(key, pos) {\n  if (key !== null && key !== undefined) {\n    return key;\n  }\n  return pos;\n}\nexport function fillFieldNames(fieldNames) {\n  var _ref = fieldNames || {},\n    title = _ref.title,\n    _title = _ref._title,\n    key = _ref.key,\n    children = _ref.children;\n  var mergedTitle = title || 'title';\n  return {\n    title: mergedTitle,\n    _title: _title || [mergedTitle],\n    key: key || 'key',\n    children: children || 'children'\n  };\n}\n/**\n * Warning if TreeNode do not provides key\n */\nexport function warningWithoutKey(treeData, fieldNames) {\n  var keys = new Map();\n  function dig(list) {\n    var path = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n    (list || []).forEach(function (treeNode) {\n      var key = treeNode[fieldNames.key];\n      var children = treeNode[fieldNames.children];\n      warning(key !== null && key !== undefined, \"Tree node must have a certain key: [\".concat(path).concat(key, \"]\"));\n      var recordKey = String(key);\n      warning(!keys.has(recordKey) || key === null || key === undefined, \"Same 'key' exist in the Tree: \".concat(recordKey));\n      keys.set(recordKey, true);\n      dig(children, \"\".concat(path).concat(recordKey, \" > \"));\n    });\n  }\n  dig(treeData);\n}\n/**\n * Convert `children` of Tree into `treeData` structure.\n */\nexport function convertTreeToData(rootNodes) {\n  function dig(node) {\n    var treeNodes = toArray(node);\n    return treeNodes.map(function (treeNode) {\n      // Filter invalidate node\n      if (!isTreeNode(treeNode)) {\n        warning(!treeNode, 'Tree/TreeNode can only accept TreeNode as children.');\n        return null;\n      }\n      var key = treeNode.key;\n      var _treeNode$props = treeNode.props,\n        children = _treeNode$props.children,\n        rest = _objectWithoutProperties(_treeNode$props, _excluded);\n      var dataNode = _objectSpread({\n        key: key\n      }, rest);\n      var parsedChildren = dig(children);\n      if (parsedChildren.length) {\n        dataNode.children = parsedChildren;\n      }\n      return dataNode;\n    }).filter(function (dataNode) {\n      return dataNode;\n    });\n  }\n  return dig(rootNodes);\n}\n/**\n * Flat nest tree data into flatten list. This is used for virtual list render.\n * @param treeNodeList Origin data node list\n * @param expandedKeys\n * need expanded keys, provides `true` means all expanded (used in `rc-tree-select`).\n */\nexport function flattenTreeData(treeNodeList, expandedKeys, fieldNames) {\n  var _fillFieldNames = fillFieldNames(fieldNames),\n    fieldTitles = _fillFieldNames._title,\n    fieldKey = _fillFieldNames.key,\n    fieldChildren = _fillFieldNames.children;\n  var expandedKeySet = new Set(expandedKeys === true ? [] : expandedKeys);\n  var flattenList = [];\n  function dig(list) {\n    var parent = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n    return list.map(function (treeNode, index) {\n      var pos = getPosition(parent ? parent.pos : '0', index);\n      var mergedKey = getKey(treeNode[fieldKey], pos);\n      // Pick matched title in field title list\n      var mergedTitle;\n      for (var i = 0; i < fieldTitles.length; i += 1) {\n        var fieldTitle = fieldTitles[i];\n        if (treeNode[fieldTitle] !== undefined) {\n          mergedTitle = treeNode[fieldTitle];\n          break;\n        }\n      }\n      // Add FlattenDataNode into list\n      var flattenNode = _objectSpread(_objectSpread({}, omit(treeNode, [].concat(_toConsumableArray(fieldTitles), [fieldKey, fieldChildren]))), {}, {\n        title: mergedTitle,\n        key: mergedKey,\n        parent: parent,\n        pos: pos,\n        children: null,\n        data: treeNode,\n        isStart: [].concat(_toConsumableArray(parent ? parent.isStart : []), [index === 0]),\n        isEnd: [].concat(_toConsumableArray(parent ? parent.isEnd : []), [index === list.length - 1])\n      });\n      flattenList.push(flattenNode);\n      // Loop treeNode children\n      if (expandedKeys === true || expandedKeySet.has(mergedKey)) {\n        flattenNode.children = dig(treeNode[fieldChildren] || [], flattenNode);\n      } else {\n        flattenNode.children = [];\n      }\n      return flattenNode;\n    });\n  }\n  dig(treeNodeList);\n  return flattenList;\n}\n/**\n * Traverse all the data by `treeData`.\n * Please not use it out of the `rc-tree` since we may refactor this code.\n */\nexport function traverseDataNodes(dataNodes, callback,\n// To avoid too many params, let use config instead of origin param\nconfig) {\n  var mergedConfig = {};\n  if (_typeof(config) === 'object') {\n    mergedConfig = config;\n  } else {\n    mergedConfig = {\n      externalGetKey: config\n    };\n  }\n  mergedConfig = mergedConfig || {};\n  // Init config\n  var _mergedConfig = mergedConfig,\n    childrenPropName = _mergedConfig.childrenPropName,\n    externalGetKey = _mergedConfig.externalGetKey,\n    fieldNames = _mergedConfig.fieldNames;\n  var _fillFieldNames2 = fillFieldNames(fieldNames),\n    fieldKey = _fillFieldNames2.key,\n    fieldChildren = _fillFieldNames2.children;\n  var mergeChildrenPropName = childrenPropName || fieldChildren;\n  // Get keys\n  var syntheticGetKey;\n  if (externalGetKey) {\n    if (typeof externalGetKey === 'string') {\n      syntheticGetKey = function syntheticGetKey(node) {\n        return node[externalGetKey];\n      };\n    } else if (typeof externalGetKey === 'function') {\n      syntheticGetKey = function syntheticGetKey(node) {\n        return externalGetKey(node);\n      };\n    }\n  } else {\n    syntheticGetKey = function syntheticGetKey(node, pos) {\n      return getKey(node[fieldKey], pos);\n    };\n  }\n  // Process\n  function processNode(node, index, parent, pathNodes) {\n    var children = node ? node[mergeChildrenPropName] : dataNodes;\n    var pos = node ? getPosition(parent.pos, index) : '0';\n    var connectNodes = node ? [].concat(_toConsumableArray(pathNodes), [node]) : [];\n    // Process node if is not root\n    if (node) {\n      var key = syntheticGetKey(node, pos);\n      var data = {\n        node: node,\n        index: index,\n        pos: pos,\n        key: key,\n        parentPos: parent.node ? parent.pos : null,\n        level: parent.level + 1,\n        nodes: connectNodes\n      };\n      callback(data);\n    }\n    // Process children node\n    if (children) {\n      children.forEach(function (subNode, subIndex) {\n        processNode(subNode, subIndex, {\n          node: node,\n          pos: pos,\n          level: parent ? parent.level + 1 : -1\n        }, connectNodes);\n      });\n    }\n  }\n  processNode(null);\n}\n/**\n * Convert `treeData` into entity records.\n */\nexport function convertDataToEntities(dataNodes) {\n  var _ref2 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n    initWrapper = _ref2.initWrapper,\n    processEntity = _ref2.processEntity,\n    onProcessFinished = _ref2.onProcessFinished,\n    externalGetKey = _ref2.externalGetKey,\n    childrenPropName = _ref2.childrenPropName,\n    fieldNames = _ref2.fieldNames;\n  var /** @deprecated Use `config.externalGetKey` instead */\n  legacyExternalGetKey = arguments.length > 2 ? arguments[2] : undefined;\n  // Init config\n  var mergedExternalGetKey = externalGetKey || legacyExternalGetKey;\n  var posEntities = {};\n  var keyEntities = {};\n  var wrapper = {\n    posEntities: posEntities,\n    keyEntities: keyEntities\n  };\n  if (initWrapper) {\n    wrapper = initWrapper(wrapper) || wrapper;\n  }\n  traverseDataNodes(dataNodes, function (item) {\n    var node = item.node,\n      index = item.index,\n      pos = item.pos,\n      key = item.key,\n      parentPos = item.parentPos,\n      level = item.level,\n      nodes = item.nodes;\n    var entity = {\n      node: node,\n      nodes: nodes,\n      index: index,\n      key: key,\n      pos: pos,\n      level: level\n    };\n    var mergedKey = getKey(key, pos);\n    posEntities[pos] = entity;\n    keyEntities[mergedKey] = entity;\n    // Fill children\n    entity.parent = posEntities[parentPos];\n    if (entity.parent) {\n      entity.parent.children = entity.parent.children || [];\n      entity.parent.children.push(entity);\n    }\n    if (processEntity) {\n      processEntity(entity, wrapper);\n    }\n  }, {\n    externalGetKey: mergedExternalGetKey,\n    childrenPropName: childrenPropName,\n    fieldNames: fieldNames\n  });\n  if (onProcessFinished) {\n    onProcessFinished(wrapper);\n  }\n  return wrapper;\n}\n/**\n * Get TreeNode props with Tree props.\n */\nexport function getTreeNodeProps(key, _ref3) {\n  var expandedKeys = _ref3.expandedKeys,\n    selectedKeys = _ref3.selectedKeys,\n    loadedKeys = _ref3.loadedKeys,\n    loadingKeys = _ref3.loadingKeys,\n    checkedKeys = _ref3.checkedKeys,\n    halfCheckedKeys = _ref3.halfCheckedKeys,\n    dragOverNodeKey = _ref3.dragOverNodeKey,\n    dropPosition = _ref3.dropPosition,\n    keyEntities = _ref3.keyEntities;\n  var entity = getEntity(keyEntities, key);\n  var treeNodeProps = {\n    eventKey: key,\n    expanded: expandedKeys.indexOf(key) !== -1,\n    selected: selectedKeys.indexOf(key) !== -1,\n    loaded: loadedKeys.indexOf(key) !== -1,\n    loading: loadingKeys.indexOf(key) !== -1,\n    checked: checkedKeys.indexOf(key) !== -1,\n    halfChecked: halfCheckedKeys.indexOf(key) !== -1,\n    pos: String(entity ? entity.pos : ''),\n    // [Legacy] Drag props\n    // Since the interaction of drag is changed, the semantic of the props are\n    // not accuracy, I think it should be finally removed\n    dragOver: dragOverNodeKey === key && dropPosition === 0,\n    dragOverGapTop: dragOverNodeKey === key && dropPosition === -1,\n    dragOverGapBottom: dragOverNodeKey === key && dropPosition === 1\n  };\n  return treeNodeProps;\n}\nexport function convertNodePropsToEventData(props) {\n  var data = props.data,\n    expanded = props.expanded,\n    selected = props.selected,\n    checked = props.checked,\n    loaded = props.loaded,\n    loading = props.loading,\n    halfChecked = props.halfChecked,\n    dragOver = props.dragOver,\n    dragOverGapTop = props.dragOverGapTop,\n    dragOverGapBottom = props.dragOverGapBottom,\n    pos = props.pos,\n    active = props.active,\n    eventKey = props.eventKey;\n  var eventData = _objectSpread(_objectSpread({}, data), {}, {\n    expanded: expanded,\n    selected: selected,\n    checked: checked,\n    loaded: loaded,\n    loading: loading,\n    halfChecked: halfChecked,\n    dragOver: dragOver,\n    dragOverGapTop: dragOverGapTop,\n    dragOverGapBottom: dragOverGapBottom,\n    pos: pos,\n    active: active,\n    key: eventKey\n  });\n  if (!('props' in eventData)) {\n    Object.defineProperty(eventData, 'props', {\n      get: function get() {\n        warning(false, 'Second param return from event is node data instead of TreeNode instance. Please read value directly instead of reading from `props`.');\n        return props;\n      }\n    });\n  }\n  return eventData;\n}", "map": {"version": 3, "names": ["_typeof", "_toConsumableArray", "_objectSpread", "_objectWithoutProperties", "_excluded", "toArray", "omit", "warning", "getEntity", "getPosition", "level", "index", "concat", "isTreeNode", "node", "type", "<PERSON><PERSON><PERSON>", "key", "pos", "undefined", "fillFieldNames", "fieldNames", "_ref", "title", "_title", "children", "mergedTitle", "warningWithoutKey", "treeData", "keys", "Map", "dig", "list", "path", "arguments", "length", "for<PERSON>ach", "treeNode", "<PERSON><PERSON>ey", "String", "has", "set", "convertTreeToData", "rootNodes", "treeNodes", "map", "_treeNode$props", "props", "rest", "dataNode", "parsed<PERSON><PERSON><PERSON><PERSON>", "filter", "flattenTreeData", "treeNodeList", "expandedKeys", "_fillField<PERSON><PERSON>s", "fieldTitles", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "expandedKeySet", "Set", "flattenList", "parent", "mergedKey", "i", "fieldTitle", "flattenNode", "data", "isStart", "isEnd", "push", "traverseDataNodes", "dataNodes", "callback", "config", "mergedConfig", "externalGetKey", "_mergedConfig", "childrenPropName", "_fillFieldNames2", "mergeChildrenPropName", "syntheticGetKey", "processNode", "pathNodes", "connectNodes", "parentPos", "nodes", "subNode", "subIndex", "convertDataToEntities", "_ref2", "initWrapper", "processEntity", "onProcessFinished", "legacyExternalGetKey", "mergedExternalGetKey", "posEntities", "keyEntities", "wrapper", "item", "entity", "getTreeNodeProps", "_ref3", "<PERSON><PERSON><PERSON><PERSON>", "loadedKeys", "loadingKeys", "checked<PERSON>eys", "halfC<PERSON>cked<PERSON>eys", "dragOverNodeKey", "dropPosition", "treeNodeProps", "eventKey", "expanded", "indexOf", "selected", "loaded", "loading", "checked", "halfChecked", "dragOver", "dragOverGapTop", "dragOverGapBottom", "convertNodePropsToEventData", "active", "eventData", "Object", "defineProperty", "get"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/rc-tree/es/utils/treeUtil.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\"];\nimport toArray from \"rc-util/es/Children/toArray\";\nimport omit from \"rc-util/es/omit\";\nimport warning from \"rc-util/es/warning\";\nimport getEntity from './keyUtil';\nexport function getPosition(level, index) {\n  return \"\".concat(level, \"-\").concat(index);\n}\nexport function isTreeNode(node) {\n  return node && node.type && node.type.isTreeNode;\n}\nexport function getKey(key, pos) {\n  if (key !== null && key !== undefined) {\n    return key;\n  }\n  return pos;\n}\nexport function fillFieldNames(fieldNames) {\n  var _ref = fieldNames || {},\n    title = _ref.title,\n    _title = _ref._title,\n    key = _ref.key,\n    children = _ref.children;\n  var mergedTitle = title || 'title';\n  return {\n    title: mergedTitle,\n    _title: _title || [mergedTitle],\n    key: key || 'key',\n    children: children || 'children'\n  };\n}\n/**\n * Warning if TreeNode do not provides key\n */\nexport function warningWithoutKey(treeData, fieldNames) {\n  var keys = new Map();\n  function dig(list) {\n    var path = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n    (list || []).forEach(function (treeNode) {\n      var key = treeNode[fieldNames.key];\n      var children = treeNode[fieldNames.children];\n      warning(key !== null && key !== undefined, \"Tree node must have a certain key: [\".concat(path).concat(key, \"]\"));\n      var recordKey = String(key);\n      warning(!keys.has(recordKey) || key === null || key === undefined, \"Same 'key' exist in the Tree: \".concat(recordKey));\n      keys.set(recordKey, true);\n      dig(children, \"\".concat(path).concat(recordKey, \" > \"));\n    });\n  }\n  dig(treeData);\n}\n/**\n * Convert `children` of Tree into `treeData` structure.\n */\nexport function convertTreeToData(rootNodes) {\n  function dig(node) {\n    var treeNodes = toArray(node);\n    return treeNodes.map(function (treeNode) {\n      // Filter invalidate node\n      if (!isTreeNode(treeNode)) {\n        warning(!treeNode, 'Tree/TreeNode can only accept TreeNode as children.');\n        return null;\n      }\n      var key = treeNode.key;\n      var _treeNode$props = treeNode.props,\n        children = _treeNode$props.children,\n        rest = _objectWithoutProperties(_treeNode$props, _excluded);\n      var dataNode = _objectSpread({\n        key: key\n      }, rest);\n      var parsedChildren = dig(children);\n      if (parsedChildren.length) {\n        dataNode.children = parsedChildren;\n      }\n      return dataNode;\n    }).filter(function (dataNode) {\n      return dataNode;\n    });\n  }\n  return dig(rootNodes);\n}\n/**\n * Flat nest tree data into flatten list. This is used for virtual list render.\n * @param treeNodeList Origin data node list\n * @param expandedKeys\n * need expanded keys, provides `true` means all expanded (used in `rc-tree-select`).\n */\nexport function flattenTreeData(treeNodeList, expandedKeys, fieldNames) {\n  var _fillFieldNames = fillFieldNames(fieldNames),\n    fieldTitles = _fillFieldNames._title,\n    fieldKey = _fillFieldNames.key,\n    fieldChildren = _fillFieldNames.children;\n  var expandedKeySet = new Set(expandedKeys === true ? [] : expandedKeys);\n  var flattenList = [];\n  function dig(list) {\n    var parent = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n    return list.map(function (treeNode, index) {\n      var pos = getPosition(parent ? parent.pos : '0', index);\n      var mergedKey = getKey(treeNode[fieldKey], pos);\n      // Pick matched title in field title list\n      var mergedTitle;\n      for (var i = 0; i < fieldTitles.length; i += 1) {\n        var fieldTitle = fieldTitles[i];\n        if (treeNode[fieldTitle] !== undefined) {\n          mergedTitle = treeNode[fieldTitle];\n          break;\n        }\n      }\n      // Add FlattenDataNode into list\n      var flattenNode = _objectSpread(_objectSpread({}, omit(treeNode, [].concat(_toConsumableArray(fieldTitles), [fieldKey, fieldChildren]))), {}, {\n        title: mergedTitle,\n        key: mergedKey,\n        parent: parent,\n        pos: pos,\n        children: null,\n        data: treeNode,\n        isStart: [].concat(_toConsumableArray(parent ? parent.isStart : []), [index === 0]),\n        isEnd: [].concat(_toConsumableArray(parent ? parent.isEnd : []), [index === list.length - 1])\n      });\n      flattenList.push(flattenNode);\n      // Loop treeNode children\n      if (expandedKeys === true || expandedKeySet.has(mergedKey)) {\n        flattenNode.children = dig(treeNode[fieldChildren] || [], flattenNode);\n      } else {\n        flattenNode.children = [];\n      }\n      return flattenNode;\n    });\n  }\n  dig(treeNodeList);\n  return flattenList;\n}\n/**\n * Traverse all the data by `treeData`.\n * Please not use it out of the `rc-tree` since we may refactor this code.\n */\nexport function traverseDataNodes(dataNodes, callback,\n// To avoid too many params, let use config instead of origin param\nconfig) {\n  var mergedConfig = {};\n  if (_typeof(config) === 'object') {\n    mergedConfig = config;\n  } else {\n    mergedConfig = {\n      externalGetKey: config\n    };\n  }\n  mergedConfig = mergedConfig || {};\n  // Init config\n  var _mergedConfig = mergedConfig,\n    childrenPropName = _mergedConfig.childrenPropName,\n    externalGetKey = _mergedConfig.externalGetKey,\n    fieldNames = _mergedConfig.fieldNames;\n  var _fillFieldNames2 = fillFieldNames(fieldNames),\n    fieldKey = _fillFieldNames2.key,\n    fieldChildren = _fillFieldNames2.children;\n  var mergeChildrenPropName = childrenPropName || fieldChildren;\n  // Get keys\n  var syntheticGetKey;\n  if (externalGetKey) {\n    if (typeof externalGetKey === 'string') {\n      syntheticGetKey = function syntheticGetKey(node) {\n        return node[externalGetKey];\n      };\n    } else if (typeof externalGetKey === 'function') {\n      syntheticGetKey = function syntheticGetKey(node) {\n        return externalGetKey(node);\n      };\n    }\n  } else {\n    syntheticGetKey = function syntheticGetKey(node, pos) {\n      return getKey(node[fieldKey], pos);\n    };\n  }\n  // Process\n  function processNode(node, index, parent, pathNodes) {\n    var children = node ? node[mergeChildrenPropName] : dataNodes;\n    var pos = node ? getPosition(parent.pos, index) : '0';\n    var connectNodes = node ? [].concat(_toConsumableArray(pathNodes), [node]) : [];\n    // Process node if is not root\n    if (node) {\n      var key = syntheticGetKey(node, pos);\n      var data = {\n        node: node,\n        index: index,\n        pos: pos,\n        key: key,\n        parentPos: parent.node ? parent.pos : null,\n        level: parent.level + 1,\n        nodes: connectNodes\n      };\n      callback(data);\n    }\n    // Process children node\n    if (children) {\n      children.forEach(function (subNode, subIndex) {\n        processNode(subNode, subIndex, {\n          node: node,\n          pos: pos,\n          level: parent ? parent.level + 1 : -1\n        }, connectNodes);\n      });\n    }\n  }\n  processNode(null);\n}\n/**\n * Convert `treeData` into entity records.\n */\nexport function convertDataToEntities(dataNodes) {\n  var _ref2 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n    initWrapper = _ref2.initWrapper,\n    processEntity = _ref2.processEntity,\n    onProcessFinished = _ref2.onProcessFinished,\n    externalGetKey = _ref2.externalGetKey,\n    childrenPropName = _ref2.childrenPropName,\n    fieldNames = _ref2.fieldNames;\n  var /** @deprecated Use `config.externalGetKey` instead */\n  legacyExternalGetKey = arguments.length > 2 ? arguments[2] : undefined;\n  // Init config\n  var mergedExternalGetKey = externalGetKey || legacyExternalGetKey;\n  var posEntities = {};\n  var keyEntities = {};\n  var wrapper = {\n    posEntities: posEntities,\n    keyEntities: keyEntities\n  };\n  if (initWrapper) {\n    wrapper = initWrapper(wrapper) || wrapper;\n  }\n  traverseDataNodes(dataNodes, function (item) {\n    var node = item.node,\n      index = item.index,\n      pos = item.pos,\n      key = item.key,\n      parentPos = item.parentPos,\n      level = item.level,\n      nodes = item.nodes;\n    var entity = {\n      node: node,\n      nodes: nodes,\n      index: index,\n      key: key,\n      pos: pos,\n      level: level\n    };\n    var mergedKey = getKey(key, pos);\n    posEntities[pos] = entity;\n    keyEntities[mergedKey] = entity;\n    // Fill children\n    entity.parent = posEntities[parentPos];\n    if (entity.parent) {\n      entity.parent.children = entity.parent.children || [];\n      entity.parent.children.push(entity);\n    }\n    if (processEntity) {\n      processEntity(entity, wrapper);\n    }\n  }, {\n    externalGetKey: mergedExternalGetKey,\n    childrenPropName: childrenPropName,\n    fieldNames: fieldNames\n  });\n  if (onProcessFinished) {\n    onProcessFinished(wrapper);\n  }\n  return wrapper;\n}\n/**\n * Get TreeNode props with Tree props.\n */\nexport function getTreeNodeProps(key, _ref3) {\n  var expandedKeys = _ref3.expandedKeys,\n    selectedKeys = _ref3.selectedKeys,\n    loadedKeys = _ref3.loadedKeys,\n    loadingKeys = _ref3.loadingKeys,\n    checkedKeys = _ref3.checkedKeys,\n    halfCheckedKeys = _ref3.halfCheckedKeys,\n    dragOverNodeKey = _ref3.dragOverNodeKey,\n    dropPosition = _ref3.dropPosition,\n    keyEntities = _ref3.keyEntities;\n  var entity = getEntity(keyEntities, key);\n  var treeNodeProps = {\n    eventKey: key,\n    expanded: expandedKeys.indexOf(key) !== -1,\n    selected: selectedKeys.indexOf(key) !== -1,\n    loaded: loadedKeys.indexOf(key) !== -1,\n    loading: loadingKeys.indexOf(key) !== -1,\n    checked: checkedKeys.indexOf(key) !== -1,\n    halfChecked: halfCheckedKeys.indexOf(key) !== -1,\n    pos: String(entity ? entity.pos : ''),\n    // [Legacy] Drag props\n    // Since the interaction of drag is changed, the semantic of the props are\n    // not accuracy, I think it should be finally removed\n    dragOver: dragOverNodeKey === key && dropPosition === 0,\n    dragOverGapTop: dragOverNodeKey === key && dropPosition === -1,\n    dragOverGapBottom: dragOverNodeKey === key && dropPosition === 1\n  };\n  return treeNodeProps;\n}\nexport function convertNodePropsToEventData(props) {\n  var data = props.data,\n    expanded = props.expanded,\n    selected = props.selected,\n    checked = props.checked,\n    loaded = props.loaded,\n    loading = props.loading,\n    halfChecked = props.halfChecked,\n    dragOver = props.dragOver,\n    dragOverGapTop = props.dragOverGapTop,\n    dragOverGapBottom = props.dragOverGapBottom,\n    pos = props.pos,\n    active = props.active,\n    eventKey = props.eventKey;\n  var eventData = _objectSpread(_objectSpread({}, data), {}, {\n    expanded: expanded,\n    selected: selected,\n    checked: checked,\n    loaded: loaded,\n    loading: loading,\n    halfChecked: halfChecked,\n    dragOver: dragOver,\n    dragOverGapTop: dragOverGapTop,\n    dragOverGapBottom: dragOverGapBottom,\n    pos: pos,\n    active: active,\n    key: eventKey\n  });\n  if (!('props' in eventData)) {\n    Object.defineProperty(eventData, 'props', {\n      get: function get() {\n        warning(false, 'Second param return from event is node data instead of TreeNode instance. Please read value directly instead of reading from `props`.');\n        return props;\n      }\n    });\n  }\n  return eventData;\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,UAAU,CAAC;AAC5B,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAO,SAASC,WAAWA,CAACC,KAAK,EAAEC,KAAK,EAAE;EACxC,OAAO,EAAE,CAACC,MAAM,CAACF,KAAK,EAAE,GAAG,CAAC,CAACE,MAAM,CAACD,KAAK,CAAC;AAC5C;AACA,OAAO,SAASE,UAAUA,CAACC,IAAI,EAAE;EAC/B,OAAOA,IAAI,IAAIA,IAAI,CAACC,IAAI,IAAID,IAAI,CAACC,IAAI,CAACF,UAAU;AAClD;AACA,OAAO,SAASG,MAAMA,CAACC,GAAG,EAAEC,GAAG,EAAE;EAC/B,IAAID,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,EAAE;IACrC,OAAOF,GAAG;EACZ;EACA,OAAOC,GAAG;AACZ;AACA,OAAO,SAASE,cAAcA,CAACC,UAAU,EAAE;EACzC,IAAIC,IAAI,GAAGD,UAAU,IAAI,CAAC,CAAC;IACzBE,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBC,MAAM,GAAGF,IAAI,CAACE,MAAM;IACpBP,GAAG,GAAGK,IAAI,CAACL,GAAG;IACdQ,QAAQ,GAAGH,IAAI,CAACG,QAAQ;EAC1B,IAAIC,WAAW,GAAGH,KAAK,IAAI,OAAO;EAClC,OAAO;IACLA,KAAK,EAAEG,WAAW;IAClBF,MAAM,EAAEA,MAAM,IAAI,CAACE,WAAW,CAAC;IAC/BT,GAAG,EAAEA,GAAG,IAAI,KAAK;IACjBQ,QAAQ,EAAEA,QAAQ,IAAI;EACxB,CAAC;AACH;AACA;AACA;AACA;AACA,OAAO,SAASE,iBAAiBA,CAACC,QAAQ,EAAEP,UAAU,EAAE;EACtD,IAAIQ,IAAI,GAAG,IAAIC,GAAG,CAAC,CAAC;EACpB,SAASC,GAAGA,CAACC,IAAI,EAAE;IACjB,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKf,SAAS,GAAGe,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;IACjF,CAACF,IAAI,IAAI,EAAE,EAAEI,OAAO,CAAC,UAAUC,QAAQ,EAAE;MACvC,IAAIpB,GAAG,GAAGoB,QAAQ,CAAChB,UAAU,CAACJ,GAAG,CAAC;MAClC,IAAIQ,QAAQ,GAAGY,QAAQ,CAAChB,UAAU,CAACI,QAAQ,CAAC;MAC5ClB,OAAO,CAACU,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,EAAE,sCAAsC,CAACP,MAAM,CAACqB,IAAI,CAAC,CAACrB,MAAM,CAACK,GAAG,EAAE,GAAG,CAAC,CAAC;MAChH,IAAIqB,SAAS,GAAGC,MAAM,CAACtB,GAAG,CAAC;MAC3BV,OAAO,CAAC,CAACsB,IAAI,CAACW,GAAG,CAACF,SAAS,CAAC,IAAIrB,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,EAAE,gCAAgC,CAACP,MAAM,CAAC0B,SAAS,CAAC,CAAC;MACtHT,IAAI,CAACY,GAAG,CAACH,SAAS,EAAE,IAAI,CAAC;MACzBP,GAAG,CAACN,QAAQ,EAAE,EAAE,CAACb,MAAM,CAACqB,IAAI,CAAC,CAACrB,MAAM,CAAC0B,SAAS,EAAE,KAAK,CAAC,CAAC;IACzD,CAAC,CAAC;EACJ;EACAP,GAAG,CAACH,QAAQ,CAAC;AACf;AACA;AACA;AACA;AACA,OAAO,SAASc,iBAAiBA,CAACC,SAAS,EAAE;EAC3C,SAASZ,GAAGA,CAACjB,IAAI,EAAE;IACjB,IAAI8B,SAAS,GAAGvC,OAAO,CAACS,IAAI,CAAC;IAC7B,OAAO8B,SAAS,CAACC,GAAG,CAAC,UAAUR,QAAQ,EAAE;MACvC;MACA,IAAI,CAACxB,UAAU,CAACwB,QAAQ,CAAC,EAAE;QACzB9B,OAAO,CAAC,CAAC8B,QAAQ,EAAE,qDAAqD,CAAC;QACzE,OAAO,IAAI;MACb;MACA,IAAIpB,GAAG,GAAGoB,QAAQ,CAACpB,GAAG;MACtB,IAAI6B,eAAe,GAAGT,QAAQ,CAACU,KAAK;QAClCtB,QAAQ,GAAGqB,eAAe,CAACrB,QAAQ;QACnCuB,IAAI,GAAG7C,wBAAwB,CAAC2C,eAAe,EAAE1C,SAAS,CAAC;MAC7D,IAAI6C,QAAQ,GAAG/C,aAAa,CAAC;QAC3Be,GAAG,EAAEA;MACP,CAAC,EAAE+B,IAAI,CAAC;MACR,IAAIE,cAAc,GAAGnB,GAAG,CAACN,QAAQ,CAAC;MAClC,IAAIyB,cAAc,CAACf,MAAM,EAAE;QACzBc,QAAQ,CAACxB,QAAQ,GAAGyB,cAAc;MACpC;MACA,OAAOD,QAAQ;IACjB,CAAC,CAAC,CAACE,MAAM,CAAC,UAAUF,QAAQ,EAAE;MAC5B,OAAOA,QAAQ;IACjB,CAAC,CAAC;EACJ;EACA,OAAOlB,GAAG,CAACY,SAAS,CAAC;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASS,eAAeA,CAACC,YAAY,EAAEC,YAAY,EAAEjC,UAAU,EAAE;EACtE,IAAIkC,eAAe,GAAGnC,cAAc,CAACC,UAAU,CAAC;IAC9CmC,WAAW,GAAGD,eAAe,CAAC/B,MAAM;IACpCiC,QAAQ,GAAGF,eAAe,CAACtC,GAAG;IAC9ByC,aAAa,GAAGH,eAAe,CAAC9B,QAAQ;EAC1C,IAAIkC,cAAc,GAAG,IAAIC,GAAG,CAACN,YAAY,KAAK,IAAI,GAAG,EAAE,GAAGA,YAAY,CAAC;EACvE,IAAIO,WAAW,GAAG,EAAE;EACpB,SAAS9B,GAAGA,CAACC,IAAI,EAAE;IACjB,IAAI8B,MAAM,GAAG5B,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKf,SAAS,GAAGe,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;IACrF,OAAOF,IAAI,CAACa,GAAG,CAAC,UAAUR,QAAQ,EAAE1B,KAAK,EAAE;MACzC,IAAIO,GAAG,GAAGT,WAAW,CAACqD,MAAM,GAAGA,MAAM,CAAC5C,GAAG,GAAG,GAAG,EAAEP,KAAK,CAAC;MACvD,IAAIoD,SAAS,GAAG/C,MAAM,CAACqB,QAAQ,CAACoB,QAAQ,CAAC,EAAEvC,GAAG,CAAC;MAC/C;MACA,IAAIQ,WAAW;MACf,KAAK,IAAIsC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,WAAW,CAACrB,MAAM,EAAE6B,CAAC,IAAI,CAAC,EAAE;QAC9C,IAAIC,UAAU,GAAGT,WAAW,CAACQ,CAAC,CAAC;QAC/B,IAAI3B,QAAQ,CAAC4B,UAAU,CAAC,KAAK9C,SAAS,EAAE;UACtCO,WAAW,GAAGW,QAAQ,CAAC4B,UAAU,CAAC;UAClC;QACF;MACF;MACA;MACA,IAAIC,WAAW,GAAGhE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEI,IAAI,CAAC+B,QAAQ,EAAE,EAAE,CAACzB,MAAM,CAACX,kBAAkB,CAACuD,WAAW,CAAC,EAAE,CAACC,QAAQ,EAAEC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAC5InC,KAAK,EAAEG,WAAW;QAClBT,GAAG,EAAE8C,SAAS;QACdD,MAAM,EAAEA,MAAM;QACd5C,GAAG,EAAEA,GAAG;QACRO,QAAQ,EAAE,IAAI;QACd0C,IAAI,EAAE9B,QAAQ;QACd+B,OAAO,EAAE,EAAE,CAACxD,MAAM,CAACX,kBAAkB,CAAC6D,MAAM,GAAGA,MAAM,CAACM,OAAO,GAAG,EAAE,CAAC,EAAE,CAACzD,KAAK,KAAK,CAAC,CAAC,CAAC;QACnF0D,KAAK,EAAE,EAAE,CAACzD,MAAM,CAACX,kBAAkB,CAAC6D,MAAM,GAAGA,MAAM,CAACO,KAAK,GAAG,EAAE,CAAC,EAAE,CAAC1D,KAAK,KAAKqB,IAAI,CAACG,MAAM,GAAG,CAAC,CAAC;MAC9F,CAAC,CAAC;MACF0B,WAAW,CAACS,IAAI,CAACJ,WAAW,CAAC;MAC7B;MACA,IAAIZ,YAAY,KAAK,IAAI,IAAIK,cAAc,CAACnB,GAAG,CAACuB,SAAS,CAAC,EAAE;QAC1DG,WAAW,CAACzC,QAAQ,GAAGM,GAAG,CAACM,QAAQ,CAACqB,aAAa,CAAC,IAAI,EAAE,EAAEQ,WAAW,CAAC;MACxE,CAAC,MAAM;QACLA,WAAW,CAACzC,QAAQ,GAAG,EAAE;MAC3B;MACA,OAAOyC,WAAW;IACpB,CAAC,CAAC;EACJ;EACAnC,GAAG,CAACsB,YAAY,CAAC;EACjB,OAAOQ,WAAW;AACpB;AACA;AACA;AACA;AACA;AACA,OAAO,SAASU,iBAAiBA,CAACC,SAAS,EAAEC,QAAQ;AACrD;AACAC,MAAM,EAAE;EACN,IAAIC,YAAY,GAAG,CAAC,CAAC;EACrB,IAAI3E,OAAO,CAAC0E,MAAM,CAAC,KAAK,QAAQ,EAAE;IAChCC,YAAY,GAAGD,MAAM;EACvB,CAAC,MAAM;IACLC,YAAY,GAAG;MACbC,cAAc,EAAEF;IAClB,CAAC;EACH;EACAC,YAAY,GAAGA,YAAY,IAAI,CAAC,CAAC;EACjC;EACA,IAAIE,aAAa,GAAGF,YAAY;IAC9BG,gBAAgB,GAAGD,aAAa,CAACC,gBAAgB;IACjDF,cAAc,GAAGC,aAAa,CAACD,cAAc;IAC7CvD,UAAU,GAAGwD,aAAa,CAACxD,UAAU;EACvC,IAAI0D,gBAAgB,GAAG3D,cAAc,CAACC,UAAU,CAAC;IAC/CoC,QAAQ,GAAGsB,gBAAgB,CAAC9D,GAAG;IAC/ByC,aAAa,GAAGqB,gBAAgB,CAACtD,QAAQ;EAC3C,IAAIuD,qBAAqB,GAAGF,gBAAgB,IAAIpB,aAAa;EAC7D;EACA,IAAIuB,eAAe;EACnB,IAAIL,cAAc,EAAE;IAClB,IAAI,OAAOA,cAAc,KAAK,QAAQ,EAAE;MACtCK,eAAe,GAAG,SAASA,eAAeA,CAACnE,IAAI,EAAE;QAC/C,OAAOA,IAAI,CAAC8D,cAAc,CAAC;MAC7B,CAAC;IACH,CAAC,MAAM,IAAI,OAAOA,cAAc,KAAK,UAAU,EAAE;MAC/CK,eAAe,GAAG,SAASA,eAAeA,CAACnE,IAAI,EAAE;QAC/C,OAAO8D,cAAc,CAAC9D,IAAI,CAAC;MAC7B,CAAC;IACH;EACF,CAAC,MAAM;IACLmE,eAAe,GAAG,SAASA,eAAeA,CAACnE,IAAI,EAAEI,GAAG,EAAE;MACpD,OAAOF,MAAM,CAACF,IAAI,CAAC2C,QAAQ,CAAC,EAAEvC,GAAG,CAAC;IACpC,CAAC;EACH;EACA;EACA,SAASgE,WAAWA,CAACpE,IAAI,EAAEH,KAAK,EAAEmD,MAAM,EAAEqB,SAAS,EAAE;IACnD,IAAI1D,QAAQ,GAAGX,IAAI,GAAGA,IAAI,CAACkE,qBAAqB,CAAC,GAAGR,SAAS;IAC7D,IAAItD,GAAG,GAAGJ,IAAI,GAAGL,WAAW,CAACqD,MAAM,CAAC5C,GAAG,EAAEP,KAAK,CAAC,GAAG,GAAG;IACrD,IAAIyE,YAAY,GAAGtE,IAAI,GAAG,EAAE,CAACF,MAAM,CAACX,kBAAkB,CAACkF,SAAS,CAAC,EAAE,CAACrE,IAAI,CAAC,CAAC,GAAG,EAAE;IAC/E;IACA,IAAIA,IAAI,EAAE;MACR,IAAIG,GAAG,GAAGgE,eAAe,CAACnE,IAAI,EAAEI,GAAG,CAAC;MACpC,IAAIiD,IAAI,GAAG;QACTrD,IAAI,EAAEA,IAAI;QACVH,KAAK,EAAEA,KAAK;QACZO,GAAG,EAAEA,GAAG;QACRD,GAAG,EAAEA,GAAG;QACRoE,SAAS,EAAEvB,MAAM,CAAChD,IAAI,GAAGgD,MAAM,CAAC5C,GAAG,GAAG,IAAI;QAC1CR,KAAK,EAAEoD,MAAM,CAACpD,KAAK,GAAG,CAAC;QACvB4E,KAAK,EAAEF;MACT,CAAC;MACDX,QAAQ,CAACN,IAAI,CAAC;IAChB;IACA;IACA,IAAI1C,QAAQ,EAAE;MACZA,QAAQ,CAACW,OAAO,CAAC,UAAUmD,OAAO,EAAEC,QAAQ,EAAE;QAC5CN,WAAW,CAACK,OAAO,EAAEC,QAAQ,EAAE;UAC7B1E,IAAI,EAAEA,IAAI;UACVI,GAAG,EAAEA,GAAG;UACRR,KAAK,EAAEoD,MAAM,GAAGA,MAAM,CAACpD,KAAK,GAAG,CAAC,GAAG,CAAC;QACtC,CAAC,EAAE0E,YAAY,CAAC;MAClB,CAAC,CAAC;IACJ;EACF;EACAF,WAAW,CAAC,IAAI,CAAC;AACnB;AACA;AACA;AACA;AACA,OAAO,SAASO,qBAAqBA,CAACjB,SAAS,EAAE;EAC/C,IAAIkB,KAAK,GAAGxD,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKf,SAAS,GAAGe,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAChFyD,WAAW,GAAGD,KAAK,CAACC,WAAW;IAC/BC,aAAa,GAAGF,KAAK,CAACE,aAAa;IACnCC,iBAAiB,GAAGH,KAAK,CAACG,iBAAiB;IAC3CjB,cAAc,GAAGc,KAAK,CAACd,cAAc;IACrCE,gBAAgB,GAAGY,KAAK,CAACZ,gBAAgB;IACzCzD,UAAU,GAAGqE,KAAK,CAACrE,UAAU;EAC/B,IAAI;EACJyE,oBAAoB,GAAG5D,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGf,SAAS;EACtE;EACA,IAAI4E,oBAAoB,GAAGnB,cAAc,IAAIkB,oBAAoB;EACjE,IAAIE,WAAW,GAAG,CAAC,CAAC;EACpB,IAAIC,WAAW,GAAG,CAAC,CAAC;EACpB,IAAIC,OAAO,GAAG;IACZF,WAAW,EAAEA,WAAW;IACxBC,WAAW,EAAEA;EACf,CAAC;EACD,IAAIN,WAAW,EAAE;IACfO,OAAO,GAAGP,WAAW,CAACO,OAAO,CAAC,IAAIA,OAAO;EAC3C;EACA3B,iBAAiB,CAACC,SAAS,EAAE,UAAU2B,IAAI,EAAE;IAC3C,IAAIrF,IAAI,GAAGqF,IAAI,CAACrF,IAAI;MAClBH,KAAK,GAAGwF,IAAI,CAACxF,KAAK;MAClBO,GAAG,GAAGiF,IAAI,CAACjF,GAAG;MACdD,GAAG,GAAGkF,IAAI,CAAClF,GAAG;MACdoE,SAAS,GAAGc,IAAI,CAACd,SAAS;MAC1B3E,KAAK,GAAGyF,IAAI,CAACzF,KAAK;MAClB4E,KAAK,GAAGa,IAAI,CAACb,KAAK;IACpB,IAAIc,MAAM,GAAG;MACXtF,IAAI,EAAEA,IAAI;MACVwE,KAAK,EAAEA,KAAK;MACZ3E,KAAK,EAAEA,KAAK;MACZM,GAAG,EAAEA,GAAG;MACRC,GAAG,EAAEA,GAAG;MACRR,KAAK,EAAEA;IACT,CAAC;IACD,IAAIqD,SAAS,GAAG/C,MAAM,CAACC,GAAG,EAAEC,GAAG,CAAC;IAChC8E,WAAW,CAAC9E,GAAG,CAAC,GAAGkF,MAAM;IACzBH,WAAW,CAAClC,SAAS,CAAC,GAAGqC,MAAM;IAC/B;IACAA,MAAM,CAACtC,MAAM,GAAGkC,WAAW,CAACX,SAAS,CAAC;IACtC,IAAIe,MAAM,CAACtC,MAAM,EAAE;MACjBsC,MAAM,CAACtC,MAAM,CAACrC,QAAQ,GAAG2E,MAAM,CAACtC,MAAM,CAACrC,QAAQ,IAAI,EAAE;MACrD2E,MAAM,CAACtC,MAAM,CAACrC,QAAQ,CAAC6C,IAAI,CAAC8B,MAAM,CAAC;IACrC;IACA,IAAIR,aAAa,EAAE;MACjBA,aAAa,CAACQ,MAAM,EAAEF,OAAO,CAAC;IAChC;EACF,CAAC,EAAE;IACDtB,cAAc,EAAEmB,oBAAoB;IACpCjB,gBAAgB,EAAEA,gBAAgB;IAClCzD,UAAU,EAAEA;EACd,CAAC,CAAC;EACF,IAAIwE,iBAAiB,EAAE;IACrBA,iBAAiB,CAACK,OAAO,CAAC;EAC5B;EACA,OAAOA,OAAO;AAChB;AACA;AACA;AACA;AACA,OAAO,SAASG,gBAAgBA,CAACpF,GAAG,EAAEqF,KAAK,EAAE;EAC3C,IAAIhD,YAAY,GAAGgD,KAAK,CAAChD,YAAY;IACnCiD,YAAY,GAAGD,KAAK,CAACC,YAAY;IACjCC,UAAU,GAAGF,KAAK,CAACE,UAAU;IAC7BC,WAAW,GAAGH,KAAK,CAACG,WAAW;IAC/BC,WAAW,GAAGJ,KAAK,CAACI,WAAW;IAC/BC,eAAe,GAAGL,KAAK,CAACK,eAAe;IACvCC,eAAe,GAAGN,KAAK,CAACM,eAAe;IACvCC,YAAY,GAAGP,KAAK,CAACO,YAAY;IACjCZ,WAAW,GAAGK,KAAK,CAACL,WAAW;EACjC,IAAIG,MAAM,GAAG5F,SAAS,CAACyF,WAAW,EAAEhF,GAAG,CAAC;EACxC,IAAI6F,aAAa,GAAG;IAClBC,QAAQ,EAAE9F,GAAG;IACb+F,QAAQ,EAAE1D,YAAY,CAAC2D,OAAO,CAAChG,GAAG,CAAC,KAAK,CAAC,CAAC;IAC1CiG,QAAQ,EAAEX,YAAY,CAACU,OAAO,CAAChG,GAAG,CAAC,KAAK,CAAC,CAAC;IAC1CkG,MAAM,EAAEX,UAAU,CAACS,OAAO,CAAChG,GAAG,CAAC,KAAK,CAAC,CAAC;IACtCmG,OAAO,EAAEX,WAAW,CAACQ,OAAO,CAAChG,GAAG,CAAC,KAAK,CAAC,CAAC;IACxCoG,OAAO,EAAEX,WAAW,CAACO,OAAO,CAAChG,GAAG,CAAC,KAAK,CAAC,CAAC;IACxCqG,WAAW,EAAEX,eAAe,CAACM,OAAO,CAAChG,GAAG,CAAC,KAAK,CAAC,CAAC;IAChDC,GAAG,EAAEqB,MAAM,CAAC6D,MAAM,GAAGA,MAAM,CAAClF,GAAG,GAAG,EAAE,CAAC;IACrC;IACA;IACA;IACAqG,QAAQ,EAAEX,eAAe,KAAK3F,GAAG,IAAI4F,YAAY,KAAK,CAAC;IACvDW,cAAc,EAAEZ,eAAe,KAAK3F,GAAG,IAAI4F,YAAY,KAAK,CAAC,CAAC;IAC9DY,iBAAiB,EAAEb,eAAe,KAAK3F,GAAG,IAAI4F,YAAY,KAAK;EACjE,CAAC;EACD,OAAOC,aAAa;AACtB;AACA,OAAO,SAASY,2BAA2BA,CAAC3E,KAAK,EAAE;EACjD,IAAIoB,IAAI,GAAGpB,KAAK,CAACoB,IAAI;IACnB6C,QAAQ,GAAGjE,KAAK,CAACiE,QAAQ;IACzBE,QAAQ,GAAGnE,KAAK,CAACmE,QAAQ;IACzBG,OAAO,GAAGtE,KAAK,CAACsE,OAAO;IACvBF,MAAM,GAAGpE,KAAK,CAACoE,MAAM;IACrBC,OAAO,GAAGrE,KAAK,CAACqE,OAAO;IACvBE,WAAW,GAAGvE,KAAK,CAACuE,WAAW;IAC/BC,QAAQ,GAAGxE,KAAK,CAACwE,QAAQ;IACzBC,cAAc,GAAGzE,KAAK,CAACyE,cAAc;IACrCC,iBAAiB,GAAG1E,KAAK,CAAC0E,iBAAiB;IAC3CvG,GAAG,GAAG6B,KAAK,CAAC7B,GAAG;IACfyG,MAAM,GAAG5E,KAAK,CAAC4E,MAAM;IACrBZ,QAAQ,GAAGhE,KAAK,CAACgE,QAAQ;EAC3B,IAAIa,SAAS,GAAG1H,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiE,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;IACzD6C,QAAQ,EAAEA,QAAQ;IAClBE,QAAQ,EAAEA,QAAQ;IAClBG,OAAO,EAAEA,OAAO;IAChBF,MAAM,EAAEA,MAAM;IACdC,OAAO,EAAEA,OAAO;IAChBE,WAAW,EAAEA,WAAW;IACxBC,QAAQ,EAAEA,QAAQ;IAClBC,cAAc,EAAEA,cAAc;IAC9BC,iBAAiB,EAAEA,iBAAiB;IACpCvG,GAAG,EAAEA,GAAG;IACRyG,MAAM,EAAEA,MAAM;IACd1G,GAAG,EAAE8F;EACP,CAAC,CAAC;EACF,IAAI,EAAE,OAAO,IAAIa,SAAS,CAAC,EAAE;IAC3BC,MAAM,CAACC,cAAc,CAACF,SAAS,EAAE,OAAO,EAAE;MACxCG,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;QAClBxH,OAAO,CAAC,KAAK,EAAE,uIAAuI,CAAC;QACvJ,OAAOwC,KAAK;MACd;IACF,CAAC,CAAC;EACJ;EACA,OAAO6E,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}