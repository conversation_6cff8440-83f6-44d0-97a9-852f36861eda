{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { DIALOGS_SELECTOR as f, DATA_DIALOGS_ID as i, ZINDEX_DIALOGS_STEP as s } from \"./constants.mjs\";\nconst A = (n, t, o) => {\n  let e = n;\n  if (t && t.defaultView) {\n    const x = t.querySelectorAll(f);\n    let l = !1;\n    return x.forEach(d => {\n      const a = t.defaultView.getComputedStyle(d, null);\n      if (d.getAttribute(i) !== o && a.zIndex !== null) {\n        const I = parseInt(a.zIndex, 10);\n        I >= e && (e = I, l = !0);\n      }\n    }), l ? e + s : e;\n  }\n  return e;\n};\nexport { A as getMaxZIndex };", "map": {"version": 3, "names": ["DIALOGS_SELECTOR", "f", "DATA_DIALOGS_ID", "i", "ZINDEX_DIALOGS_STEP", "s", "A", "n", "t", "o", "e", "defaultView", "x", "querySelectorAll", "l", "for<PERSON>ach", "d", "a", "getComputedStyle", "getAttribute", "zIndex", "I", "parseInt", "getMaxZIndex"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-dialogs/utils.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { DIALOGS_SELECTOR as f, DATA_DIALOGS_ID as i, ZINDEX_DIALOGS_STEP as s } from \"./constants.mjs\";\nconst A = (n, t, o) => {\n  let e = n;\n  if (t && t.defaultView) {\n    const x = t.querySelectorAll(f);\n    let l = !1;\n    return x.forEach((d) => {\n      const a = t.defaultView.getComputedStyle(d, null);\n      if (d.getAttribute(i) !== o && a.zIndex !== null) {\n        const I = parseInt(a.zIndex, 10);\n        I >= e && (e = I, l = !0);\n      }\n    }), l ? e + s : e;\n  }\n  return e;\n};\nexport {\n  A as getMaxZIndex\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,gBAAgB,IAAIC,CAAC,EAAEC,eAAe,IAAIC,CAAC,EAAEC,mBAAmB,IAAIC,CAAC,QAAQ,iBAAiB;AACvG,MAAMC,CAAC,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;EACrB,IAAIC,CAAC,GAAGH,CAAC;EACT,IAAIC,CAAC,IAAIA,CAAC,CAACG,WAAW,EAAE;IACtB,MAAMC,CAAC,GAAGJ,CAAC,CAACK,gBAAgB,CAACZ,CAAC,CAAC;IAC/B,IAAIa,CAAC,GAAG,CAAC,CAAC;IACV,OAAOF,CAAC,CAACG,OAAO,CAAEC,CAAC,IAAK;MACtB,MAAMC,CAAC,GAAGT,CAAC,CAACG,WAAW,CAACO,gBAAgB,CAACF,CAAC,EAAE,IAAI,CAAC;MACjD,IAAIA,CAAC,CAACG,YAAY,CAAChB,CAAC,CAAC,KAAKM,CAAC,IAAIQ,CAAC,CAACG,MAAM,KAAK,IAAI,EAAE;QAChD,MAAMC,CAAC,GAAGC,QAAQ,CAACL,CAAC,CAACG,MAAM,EAAE,EAAE,CAAC;QAChCC,CAAC,IAAIX,CAAC,KAAKA,CAAC,GAAGW,CAAC,EAAEP,CAAC,GAAG,CAAC,CAAC,CAAC;MAC3B;IACF,CAAC,CAAC,EAAEA,CAAC,GAAGJ,CAAC,GAAGL,CAAC,GAAGK,CAAC;EACnB;EACA,OAAOA,CAAC;AACV,CAAC;AACD,SACEJ,CAAC,IAAIiB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}