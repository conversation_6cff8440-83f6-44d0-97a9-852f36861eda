{"ast": null, "code": "import { PRECISION } from '../constants';\nimport { Class } from '../../common';\nimport { round } from '../../util';\nvar ComplexNumber = function (Class) {\n  function ComplexNumber(real, img) {\n    if (real === void 0) real = 0;\n    if (img === void 0) img = 0;\n    Class.call(this);\n    this.real = real;\n    this.img = img;\n  }\n  if (Class) ComplexNumber.__proto__ = Class;\n  ComplexNumber.prototype = Object.create(Class && Class.prototype);\n  ComplexNumber.prototype.constructor = ComplexNumber;\n  ComplexNumber.prototype.add = function add(cNumber) {\n    return new ComplexNumber(round(this.real + cNumber.real, PRECISION), round(this.img + cNumber.img, PRECISION));\n  };\n  ComplexNumber.prototype.addConstant = function addConstant(value) {\n    return new ComplexNumber(this.real + value, this.img);\n  };\n  ComplexNumber.prototype.negate = function negate() {\n    return new ComplexNumber(-this.real, -this.img);\n  };\n  ComplexNumber.prototype.multiply = function multiply(cNumber) {\n    return new ComplexNumber(this.real * cNumber.real - this.img * cNumber.img, this.real * cNumber.img + this.img * cNumber.real);\n  };\n  ComplexNumber.prototype.multiplyConstant = function multiplyConstant(value) {\n    return new ComplexNumber(this.real * value, this.img * value);\n  };\n  ComplexNumber.prototype.nthRoot = function nthRoot(n) {\n    var rad = Math.atan2(this.img, this.real);\n    var r = Math.sqrt(Math.pow(this.img, 2) + Math.pow(this.real, 2));\n    var nthR = Math.pow(r, 1 / n);\n    return new ComplexNumber(nthR * Math.cos(rad / n), nthR * Math.sin(rad / n)); //Moivre's formula\n  };\n  ComplexNumber.prototype.equals = function equals(cNumber) {\n    return this.real === cNumber.real && this.img === cNumber.img;\n  };\n  ComplexNumber.prototype.isReal = function isReal() {\n    return this.img === 0;\n  };\n  return ComplexNumber;\n}(Class);\nexport default ComplexNumber;", "map": {"version": 3, "names": ["PRECISION", "Class", "round", "ComplexNumber", "real", "img", "call", "__proto__", "prototype", "Object", "create", "constructor", "add", "cNumber", "addConstant", "value", "negate", "multiply", "multiplyConstant", "nthRoot", "n", "rad", "Math", "atan2", "r", "sqrt", "pow", "nthR", "cos", "sin", "equals", "isReal"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/geometry/math/complex-number.js"], "sourcesContent": ["import { PRECISION } from '../constants';\nimport { Class } from '../../common';\nimport { round } from '../../util';\n\nvar ComplexNumber = (function (Class) {\n    function ComplexNumber(real, img) {\n        if ( real === void 0 ) real = 0;\n        if ( img === void 0 ) img = 0;\n\n        Class.call(this);\n\n        this.real = real;\n        this.img = img;\n    }\n\n    if ( Class ) ComplexNumber.__proto__ = Class;\n    ComplexNumber.prototype = Object.create( Class && Class.prototype );\n    ComplexNumber.prototype.constructor = ComplexNumber;\n\n    ComplexNumber.prototype.add = function add (cNumber) {\n        return new ComplexNumber(round(this.real + cNumber.real, PRECISION), round(this.img + cNumber.img, PRECISION));\n    };\n\n    ComplexNumber.prototype.addConstant = function addConstant (value) {\n        return new ComplexNumber(this.real + value, this.img);\n    };\n\n    ComplexNumber.prototype.negate = function negate () {\n        return new ComplexNumber(-this.real, -this.img);\n    };\n\n    ComplexNumber.prototype.multiply = function multiply (cNumber) {\n        return new ComplexNumber(this.real * cNumber.real - this.img * cNumber.img,\n            this.real * cNumber.img + this.img * cNumber.real);\n    };\n\n    ComplexNumber.prototype.multiplyConstant = function multiplyConstant (value) {\n        return new ComplexNumber(this.real * value, this.img * value);\n    };\n\n    ComplexNumber.prototype.nthRoot = function nthRoot (n) {\n        var rad = Math.atan2(this.img, this.real);\n        var r = Math.sqrt(Math.pow(this.img, 2) + Math.pow(this.real, 2));\n        var nthR = Math.pow(r, 1 / n);\n\n        return new ComplexNumber(nthR * Math.cos(rad / n), nthR * Math.sin(rad / n)); //Moivre's formula\n    };\n\n    ComplexNumber.prototype.equals = function equals (cNumber) {\n        return this.real === cNumber.real && this.img === cNumber.img;\n    };\n\n    ComplexNumber.prototype.isReal = function isReal () {\n        return this.img === 0;\n    };\n\n    return ComplexNumber;\n}(Class));\n\nexport default ComplexNumber;"], "mappings": "AAAA,SAASA,SAAS,QAAQ,cAAc;AACxC,SAASC,KAAK,QAAQ,cAAc;AACpC,SAASC,KAAK,QAAQ,YAAY;AAElC,IAAIC,aAAa,GAAI,UAAUF,KAAK,EAAE;EAClC,SAASE,aAAaA,CAACC,IAAI,EAAEC,GAAG,EAAE;IAC9B,IAAKD,IAAI,KAAK,KAAK,CAAC,EAAGA,IAAI,GAAG,CAAC;IAC/B,IAAKC,GAAG,KAAK,KAAK,CAAC,EAAGA,GAAG,GAAG,CAAC;IAE7BJ,KAAK,CAACK,IAAI,CAAC,IAAI,CAAC;IAEhB,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,GAAG,GAAGA,GAAG;EAClB;EAEA,IAAKJ,KAAK,EAAGE,aAAa,CAACI,SAAS,GAAGN,KAAK;EAC5CE,aAAa,CAACK,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAET,KAAK,IAAIA,KAAK,CAACO,SAAU,CAAC;EACnEL,aAAa,CAACK,SAAS,CAACG,WAAW,GAAGR,aAAa;EAEnDA,aAAa,CAACK,SAAS,CAACI,GAAG,GAAG,SAASA,GAAGA,CAAEC,OAAO,EAAE;IACjD,OAAO,IAAIV,aAAa,CAACD,KAAK,CAAC,IAAI,CAACE,IAAI,GAAGS,OAAO,CAACT,IAAI,EAAEJ,SAAS,CAAC,EAAEE,KAAK,CAAC,IAAI,CAACG,GAAG,GAAGQ,OAAO,CAACR,GAAG,EAAEL,SAAS,CAAC,CAAC;EAClH,CAAC;EAEDG,aAAa,CAACK,SAAS,CAACM,WAAW,GAAG,SAASA,WAAWA,CAAEC,KAAK,EAAE;IAC/D,OAAO,IAAIZ,aAAa,CAAC,IAAI,CAACC,IAAI,GAAGW,KAAK,EAAE,IAAI,CAACV,GAAG,CAAC;EACzD,CAAC;EAEDF,aAAa,CAACK,SAAS,CAACQ,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAI;IAChD,OAAO,IAAIb,aAAa,CAAC,CAAC,IAAI,CAACC,IAAI,EAAE,CAAC,IAAI,CAACC,GAAG,CAAC;EACnD,CAAC;EAEDF,aAAa,CAACK,SAAS,CAACS,QAAQ,GAAG,SAASA,QAAQA,CAAEJ,OAAO,EAAE;IAC3D,OAAO,IAAIV,aAAa,CAAC,IAAI,CAACC,IAAI,GAAGS,OAAO,CAACT,IAAI,GAAG,IAAI,CAACC,GAAG,GAAGQ,OAAO,CAACR,GAAG,EACtE,IAAI,CAACD,IAAI,GAAGS,OAAO,CAACR,GAAG,GAAG,IAAI,CAACA,GAAG,GAAGQ,OAAO,CAACT,IAAI,CAAC;EAC1D,CAAC;EAEDD,aAAa,CAACK,SAAS,CAACU,gBAAgB,GAAG,SAASA,gBAAgBA,CAAEH,KAAK,EAAE;IACzE,OAAO,IAAIZ,aAAa,CAAC,IAAI,CAACC,IAAI,GAAGW,KAAK,EAAE,IAAI,CAACV,GAAG,GAAGU,KAAK,CAAC;EACjE,CAAC;EAEDZ,aAAa,CAACK,SAAS,CAACW,OAAO,GAAG,SAASA,OAAOA,CAAEC,CAAC,EAAE;IACnD,IAAIC,GAAG,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAI,CAAClB,GAAG,EAAE,IAAI,CAACD,IAAI,CAAC;IACzC,IAAIoB,CAAC,GAAGF,IAAI,CAACG,IAAI,CAACH,IAAI,CAACI,GAAG,CAAC,IAAI,CAACrB,GAAG,EAAE,CAAC,CAAC,GAAGiB,IAAI,CAACI,GAAG,CAAC,IAAI,CAACtB,IAAI,EAAE,CAAC,CAAC,CAAC;IACjE,IAAIuB,IAAI,GAAGL,IAAI,CAACI,GAAG,CAACF,CAAC,EAAE,CAAC,GAAGJ,CAAC,CAAC;IAE7B,OAAO,IAAIjB,aAAa,CAACwB,IAAI,GAAGL,IAAI,CAACM,GAAG,CAACP,GAAG,GAAGD,CAAC,CAAC,EAAEO,IAAI,GAAGL,IAAI,CAACO,GAAG,CAACR,GAAG,GAAGD,CAAC,CAAC,CAAC,CAAC,CAAC;EAClF,CAAC;EAEDjB,aAAa,CAACK,SAAS,CAACsB,MAAM,GAAG,SAASA,MAAMA,CAAEjB,OAAO,EAAE;IACvD,OAAO,IAAI,CAACT,IAAI,KAAKS,OAAO,CAACT,IAAI,IAAI,IAAI,CAACC,GAAG,KAAKQ,OAAO,CAACR,GAAG;EACjE,CAAC;EAEDF,aAAa,CAACK,SAAS,CAACuB,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAI;IAChD,OAAO,IAAI,CAAC1B,GAAG,KAAK,CAAC;EACzB,CAAC;EAED,OAAOF,aAAa;AACxB,CAAC,CAACF,KAAK,CAAE;AAET,eAAeE,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}