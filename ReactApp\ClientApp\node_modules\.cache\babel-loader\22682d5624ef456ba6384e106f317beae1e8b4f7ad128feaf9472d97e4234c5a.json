{"ast": null, "code": "import { timezones } from './timezones';\nimport { formatMessage, NO_TZ_INFO } from '../errors';\n/**\n * @hidden\n *\n * A function that gets all zone rules for a specific zone.\n *\n * @param timezone - The timezone name. For example, `America/Chicago`, `Europe/Sofia`.\n *\n * @return - Returns all zone rules for the specific zone name.\n *\n * @example\n * ```ts-no-run\n * findZone('Europe/Sofia'); //[[-120,\"E-Eur\",\"EE%sT\",883526400000], [-120,\"EU\",\"EE%sT\",null]]\n * ```\n */\nexport var getZoneRules = function (timezone) {\n  var zones = timezones.zones;\n  if (!zones) {\n    throw new Error(formatMessage(NO_TZ_INFO, timezone));\n  }\n  var zoneRules = zones[timezone];\n  var result = typeof zoneRules === \"string\" ? zones[zoneRules] : zoneRules;\n  if (!result) {\n    throw new Error(formatMessage(NO_TZ_INFO, timezone));\n  }\n  return result;\n};", "map": {"version": 3, "names": ["timezones", "formatMessage", "NO_TZ_INFO", "getZoneRules", "timezone", "zones", "Error", "zoneRules", "result"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/tz/get-zone.js"], "sourcesContent": ["import { timezones } from './timezones';\nimport { formatMessage, NO_TZ_INFO } from '../errors';\n/**\n * @hidden\n *\n * A function that gets all zone rules for a specific zone.\n *\n * @param timezone - The timezone name. For example, `America/Chicago`, `Europe/Sofia`.\n *\n * @return - Returns all zone rules for the specific zone name.\n *\n * @example\n * ```ts-no-run\n * findZone('Europe/Sofia'); //[[-120,\"E-Eur\",\"EE%sT\",883526400000], [-120,\"EU\",\"EE%sT\",null]]\n * ```\n */\nexport var getZoneRules = function (timezone) {\n    var zones = timezones.zones;\n    if (!zones) {\n        throw new Error(formatMessage(NO_TZ_INFO, timezone));\n    }\n    var zoneRules = zones[timezone];\n    var result = typeof zoneRules === \"string\" ? zones[zoneRules] : zoneRules;\n    if (!result) {\n        throw new Error(formatMessage(NO_TZ_INFO, timezone));\n    }\n    return result;\n};\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AACvC,SAASC,aAAa,EAAEC,UAAU,QAAQ,WAAW;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,YAAY,GAAG,SAAAA,CAAUC,QAAQ,EAAE;EAC1C,IAAIC,KAAK,GAAGL,SAAS,CAACK,KAAK;EAC3B,IAAI,CAACA,KAAK,EAAE;IACR,MAAM,IAAIC,KAAK,CAACL,aAAa,CAACC,UAAU,EAAEE,QAAQ,CAAC,CAAC;EACxD;EACA,IAAIG,SAAS,GAAGF,KAAK,CAACD,QAAQ,CAAC;EAC/B,IAAII,MAAM,GAAG,OAAOD,SAAS,KAAK,QAAQ,GAAGF,KAAK,CAACE,SAAS,CAAC,GAAGA,SAAS;EACzE,IAAI,CAACC,MAAM,EAAE;IACT,MAAM,IAAIF,KAAK,CAACL,aAAa,CAACC,UAAU,EAAEE,QAAQ,CAAC,CAAC;EACxD;EACA,OAAOI,MAAM;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}