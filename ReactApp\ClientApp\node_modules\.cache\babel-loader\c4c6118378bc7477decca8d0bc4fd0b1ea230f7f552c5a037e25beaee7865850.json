{"ast": null, "code": "import { getLocaleInfo } from './info';\nexport default function numberSymbols(locale) {\n  var info = getLocaleInfo(locale);\n  return info.numbers.symbols;\n}", "map": {"version": 3, "names": ["getLocaleInfo", "numberSymbols", "locale", "info", "numbers", "symbols"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-intl/dist/es/cldr/number-symbols.js"], "sourcesContent": ["import { getLocaleInfo } from './info';\n\nexport default function numberSymbols(locale) {\n    var info = getLocaleInfo(locale);\n\n    return info.numbers.symbols;\n}"], "mappings": "AAAA,SAASA,aAAa,QAAQ,QAAQ;AAEtC,eAAe,SAASC,aAAaA,CAACC,MAAM,EAAE;EAC1C,IAAIC,IAAI,GAAGH,aAAa,CAACE,MAAM,CAAC;EAEhC,OAAOC,IAAI,CAACC,OAAO,CAACC,OAAO;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}