{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { classNames as w, uPopup as v } from \"@progress/kendo-react-common\";\nconst p = (i, s, t) => {\n    i.style.transitionDuration = \"\", s && i.classList.remove(...s.split(\" \")), t && i.classList.remove(...t.split(\" \"));\n  },\n  y = typeof window != \"undefined\" && /Firefox/.test(window.navigator.userAgent),\n  x = (i, s, t, r, d, f) => {\n    if (t === 0) return d();\n    const o = w(v.slide({\n        direction: s,\n        type: r,\n        c: f\n      })),\n      a = w(v.slideActive({\n        direction: s,\n        type: r,\n        c: f\n      })),\n      c = n => {\n        n.target === i && (i.removeEventListener(\"transitionend\", c), p(i, o, a), r === \"exit\" ? i.style.display = \"none\" : i.style.display = \"\", d());\n      };\n    i.addEventListener(\"transitionend\", c);\n    const u = i.ownerDocument;\n    if (!u) return;\n    const e = u.defaultView;\n    if (!e) return;\n    const l = () => {\n      p(i, o, a), r === \"enter\" && (i.style.display = \"\"), o && i.classList.add(...o.split(\" \").filter(n => n)), e.requestAnimationFrame(() => {\n        i.style.transitionDuration = t + \"ms\", i.classList.add(...a.split(\" \").filter(n => n));\n      });\n    };\n    y ? e.requestAnimationFrame(l) : l();\n  };\nexport { x as slide };", "map": {"version": 3, "names": ["classNames", "w", "uPopup", "v", "p", "i", "s", "t", "style", "transitionDuration", "classList", "remove", "split", "y", "window", "test", "navigator", "userAgent", "x", "r", "d", "f", "o", "slide", "direction", "type", "c", "a", "slideActive", "n", "target", "removeEventListener", "display", "addEventListener", "u", "ownerDocument", "e", "defaultView", "l", "add", "filter", "requestAnimationFrame"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-popup/animation.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { classNames as w, uPopup as v } from \"@progress/kendo-react-common\";\nconst p = (i, s, t) => {\n  i.style.transitionDuration = \"\", s && i.classList.remove(...s.split(\" \")), t && i.classList.remove(...t.split(\" \"));\n}, y = typeof window != \"undefined\" && /Firefox/.test(window.navigator.userAgent), x = (i, s, t, r, d, f) => {\n  if (t === 0)\n    return d();\n  const o = w(v.slide({ direction: s, type: r, c: f })), a = w(v.slideActive({ direction: s, type: r, c: f })), c = (n) => {\n    n.target === i && (i.removeEventListener(\"transitionend\", c), p(i, o, a), r === \"exit\" ? i.style.display = \"none\" : i.style.display = \"\", d());\n  };\n  i.addEventListener(\"transitionend\", c);\n  const u = i.ownerDocument;\n  if (!u)\n    return;\n  const e = u.defaultView;\n  if (!e)\n    return;\n  const l = () => {\n    p(i, o, a), r === \"enter\" && (i.style.display = \"\"), o && i.classList.add(...o.split(\" \").filter((n) => n)), e.requestAnimationFrame(() => {\n      i.style.transitionDuration = t + \"ms\", i.classList.add(...a.split(\" \").filter((n) => n));\n    });\n  };\n  y ? e.requestAnimationFrame(l) : l();\n};\nexport {\n  x as slide\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAU,IAAIC,CAAC,EAAEC,MAAM,IAAIC,CAAC,QAAQ,8BAA8B;AAC3E,MAAMC,CAAC,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;IACrBF,CAAC,CAACG,KAAK,CAACC,kBAAkB,GAAG,EAAE,EAAEH,CAAC,IAAID,CAAC,CAACK,SAAS,CAACC,MAAM,CAAC,GAAGL,CAAC,CAACM,KAAK,CAAC,GAAG,CAAC,CAAC,EAAEL,CAAC,IAAIF,CAAC,CAACK,SAAS,CAACC,MAAM,CAAC,GAAGJ,CAAC,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC;EACrH,CAAC;EAAEC,CAAC,GAAG,OAAOC,MAAM,IAAI,WAAW,IAAI,SAAS,CAACC,IAAI,CAACD,MAAM,CAACE,SAAS,CAACC,SAAS,CAAC;EAAEC,CAAC,GAAGA,CAACb,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEY,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;IAC3G,IAAId,CAAC,KAAK,CAAC,EACT,OAAOa,CAAC,CAAC,CAAC;IACZ,MAAME,CAAC,GAAGrB,CAAC,CAACE,CAAC,CAACoB,KAAK,CAAC;QAAEC,SAAS,EAAElB,CAAC;QAAEmB,IAAI,EAAEN,CAAC;QAAEO,CAAC,EAAEL;MAAE,CAAC,CAAC,CAAC;MAAEM,CAAC,GAAG1B,CAAC,CAACE,CAAC,CAACyB,WAAW,CAAC;QAAEJ,SAAS,EAAElB,CAAC;QAAEmB,IAAI,EAAEN,CAAC;QAAEO,CAAC,EAAEL;MAAE,CAAC,CAAC,CAAC;MAAEK,CAAC,GAAIG,CAAC,IAAK;QACvHA,CAAC,CAACC,MAAM,KAAKzB,CAAC,KAAKA,CAAC,CAAC0B,mBAAmB,CAAC,eAAe,EAAEL,CAAC,CAAC,EAAEtB,CAAC,CAACC,CAAC,EAAEiB,CAAC,EAAEK,CAAC,CAAC,EAAER,CAAC,KAAK,MAAM,GAAGd,CAAC,CAACG,KAAK,CAACwB,OAAO,GAAG,MAAM,GAAG3B,CAAC,CAACG,KAAK,CAACwB,OAAO,GAAG,EAAE,EAAEZ,CAAC,CAAC,CAAC,CAAC;MAChJ,CAAC;IACDf,CAAC,CAAC4B,gBAAgB,CAAC,eAAe,EAAEP,CAAC,CAAC;IACtC,MAAMQ,CAAC,GAAG7B,CAAC,CAAC8B,aAAa;IACzB,IAAI,CAACD,CAAC,EACJ;IACF,MAAME,CAAC,GAAGF,CAAC,CAACG,WAAW;IACvB,IAAI,CAACD,CAAC,EACJ;IACF,MAAME,CAAC,GAAGA,CAAA,KAAM;MACdlC,CAAC,CAACC,CAAC,EAAEiB,CAAC,EAAEK,CAAC,CAAC,EAAER,CAAC,KAAK,OAAO,KAAKd,CAAC,CAACG,KAAK,CAACwB,OAAO,GAAG,EAAE,CAAC,EAAEV,CAAC,IAAIjB,CAAC,CAACK,SAAS,CAAC6B,GAAG,CAAC,GAAGjB,CAAC,CAACV,KAAK,CAAC,GAAG,CAAC,CAAC4B,MAAM,CAAEX,CAAC,IAAKA,CAAC,CAAC,CAAC,EAAEO,CAAC,CAACK,qBAAqB,CAAC,MAAM;QACzIpC,CAAC,CAACG,KAAK,CAACC,kBAAkB,GAAGF,CAAC,GAAG,IAAI,EAAEF,CAAC,CAACK,SAAS,CAAC6B,GAAG,CAAC,GAAGZ,CAAC,CAACf,KAAK,CAAC,GAAG,CAAC,CAAC4B,MAAM,CAAEX,CAAC,IAAKA,CAAC,CAAC,CAAC;MAC1F,CAAC,CAAC;IACJ,CAAC;IACDhB,CAAC,GAAGuB,CAAC,CAACK,qBAAqB,CAACH,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC;EACtC,CAAC;AACD,SACEpB,CAAC,IAAIK,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}