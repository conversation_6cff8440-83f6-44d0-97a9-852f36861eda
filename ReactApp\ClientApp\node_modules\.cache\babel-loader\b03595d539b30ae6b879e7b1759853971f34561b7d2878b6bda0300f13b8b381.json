{"ast": null, "code": "export default function alignStartReverse(size, rect, align, axis, sizeField) {\n  var start;\n  if (align === \"start\") {\n    start = rect.origin[axis] + rect.size[sizeField] - size;\n  } else if (align === \"end\") {\n    start = rect.origin[axis];\n  } else {\n    start = rect.origin[axis] + (rect.size[sizeField] - size) / 2;\n  }\n  return start;\n}", "map": {"version": 3, "names": ["alignStartReverse", "size", "rect", "align", "axis", "sizeField", "start", "origin"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/alignment/align-start-reverse.js"], "sourcesContent": ["\nexport default function alignStartReverse(size, rect, align, axis, sizeField) {\n    var start;\n    if (align === \"start\") {\n        start = rect.origin[axis] + rect.size[sizeField] - size;\n    } else if (align === \"end\") {\n        start = rect.origin[axis];\n    } else {\n        start = rect.origin[axis] + (rect.size[sizeField] - size) / 2;\n    }\n\n    return start;\n}"], "mappings": "AACA,eAAe,SAASA,iBAAiBA,CAACC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,SAAS,EAAE;EAC1E,IAAIC,KAAK;EACT,IAAIH,KAAK,KAAK,OAAO,EAAE;IACnBG,KAAK,GAAGJ,IAAI,CAACK,MAAM,CAACH,IAAI,CAAC,GAAGF,IAAI,CAACD,IAAI,CAACI,SAAS,CAAC,GAAGJ,IAAI;EAC3D,CAAC,MAAM,IAAIE,KAAK,KAAK,KAAK,EAAE;IACxBG,KAAK,GAAGJ,IAAI,CAACK,MAAM,CAACH,IAAI,CAAC;EAC7B,CAAC,MAAM;IACHE,KAAK,GAAGJ,IAAI,CAACK,MAAM,CAACH,IAAI,CAAC,GAAG,CAACF,IAAI,CAACD,IAAI,CAACI,SAAS,CAAC,GAAGJ,IAAI,IAAI,CAAC;EACjE;EAEA,OAAOK,KAAK;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}