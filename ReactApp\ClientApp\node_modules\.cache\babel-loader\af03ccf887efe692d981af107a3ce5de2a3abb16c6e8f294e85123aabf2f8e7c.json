{"ast": null, "code": "export function saveAs(data, fileName, options) {\n  if (options === void 0) options = {};\n  var save = postToProxy;\n  if (options.forceProxy && !options.proxyURL) {\n    throw new Error('No proxyURL is set, but forceProxy is true');\n  }\n  if (!options.forceProxy) {\n    if (canDownload()) {\n      save = saveAsDataURI;\n    }\n    if (navigator.msSaveBlob) {\n      save = saveAsBlob;\n    }\n  }\n  save(data, fileName, options);\n}\nvar anchor = function () {\n  return document.createElement('a');\n};\nvar canDownload = function () {\n  return 'download' in anchor();\n};\nfunction saveAsBlob(data, fileName) {\n  var blob = data; // could be a Blob object\n\n  if (typeof data === 'string') {\n    var parts = data.split(';base64,');\n    var contentType = parts[0];\n    var base64 = atob(parts[1]);\n    var array = new Uint8Array(base64.length);\n    for (var idx = 0; idx < base64.length; idx++) {\n      array[idx] = base64.charCodeAt(idx);\n    }\n    blob = new Blob([array.buffer], {\n      type: contentType\n    });\n  }\n  navigator.msSaveBlob(blob, fileName);\n}\nfunction saveAsDataURI(data, fileName) {\n  var dataURI = data;\n  if (window.Blob && data instanceof Blob) {\n    dataURI = URL.createObjectURL(data);\n  }\n  var fileSaver = anchor();\n  fileSaver.download = fileName;\n  fileSaver.href = dataURI;\n  var e = document.createEvent('MouseEvents');\n  e.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);\n  fileSaver.dispatchEvent(e);\n  setTimeout(function () {\n    return URL.revokeObjectURL(dataURI);\n  });\n}\nfunction postToProxy(dataURI, fileName, options) {\n  if (!options.proxyURL) {\n    return;\n  }\n  var form = document.createElement('form');\n  form.setAttribute('action', options.proxyURL);\n  form.setAttribute('method', 'POST');\n  form.setAttribute('target', options.proxyTarget || '_self');\n  var formData = options.proxyData || {};\n  formData.fileName = fileName;\n  var parts = dataURI.split(\";base64,\");\n  formData.contentType = parts[0].replace(\"data:\", \"\");\n  formData.base64 = parts[1];\n  for (var name in formData) {\n    if (formData.hasOwnProperty(name)) {\n      var input = document.createElement('input');\n      input.setAttribute('type', 'hidden');\n      input.setAttribute('name', name);\n      input.setAttribute('value', formData[name]);\n      form.appendChild(input);\n    }\n  }\n  document.body.appendChild(form);\n  form.submit();\n  document.body.removeChild(form);\n}", "map": {"version": 3, "names": ["saveAs", "data", "fileName", "options", "save", "postToProxy", "forceProxy", "proxyURL", "Error", "canDownload", "saveAsDataURI", "navigator", "msSaveBlob", "saveAsBlob", "anchor", "document", "createElement", "blob", "parts", "split", "contentType", "base64", "atob", "array", "Uint8Array", "length", "idx", "charCodeAt", "Blob", "buffer", "type", "dataURI", "window", "URL", "createObjectURL", "fileSaver", "download", "href", "e", "createEvent", "initMouseEvent", "dispatchEvent", "setTimeout", "revokeObjectURL", "form", "setAttribute", "proxyTarget", "formData", "proxyData", "replace", "name", "hasOwnProperty", "input", "append<PERSON><PERSON><PERSON>", "body", "submit", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-file-saver/dist/es/save-as.js"], "sourcesContent": ["export function saveAs(data, fileName, options) {\n  if ( options === void 0 ) options = {};\n\n  var save = postToProxy;\n\n  if (options.forceProxy && !options.proxyURL) {\n    throw new Error('No proxyURL is set, but forceProxy is true');\n  }\n\n  if (!options.forceProxy) {\n    if (canDownload()) {\n      save = saveAsDataURI;\n    }\n\n    if (navigator.msSaveBlob) {\n      save = saveAsBlob;\n    }\n  }\n\n  save(data, fileName, options);\n}\n\nvar anchor = function () { return document.createElement('a'); };\nvar canDownload = function () { return 'download' in anchor(); };\n\nfunction saveAsBlob(data, fileName) {\n  var blob = data; // could be a Blob object\n\n  if (typeof data === 'string') {\n    var parts = data.split(';base64,');\n    var contentType = parts[0];\n    var base64 = atob(parts[1]);\n    var array = new Uint8Array(base64.length);\n\n    for (var idx = 0; idx < base64.length; idx++) {\n      array[idx] = base64.charCodeAt(idx);\n    }\n\n    blob = new Blob([ array.buffer ], { type: contentType });\n  }\n\n  navigator.msSaveBlob(blob, fileName);\n}\n\nfunction saveAsDataURI(data, fileName) {\n  var dataURI = data;\n  if (window.Blob && data instanceof Blob) {\n    dataURI = URL.createObjectURL(data);\n  }\n\n  var fileSaver = anchor();\n  fileSaver.download = fileName;\n  fileSaver.href = dataURI;\n\n  var e = document.createEvent('MouseEvents');\n  e.initMouseEvent('click', true, false, window,\n    0, 0, 0, 0, 0, false, false, false, false, 0, null);\n\n  fileSaver.dispatchEvent(e);\n  setTimeout(function () { return URL.revokeObjectURL(dataURI); });\n}\n\nfunction postToProxy(dataURI, fileName, options) {\n  if (!options.proxyURL) {\n    return;\n  }\n\n  var form = document.createElement('form');\n  form.setAttribute('action', options.proxyURL);\n  form.setAttribute('method', 'POST');\n  form.setAttribute('target', options.proxyTarget || '_self');\n\n  var formData = options.proxyData || {};\n  formData.fileName = fileName;\n\n  var parts = dataURI.split(\";base64,\");\n  formData.contentType = parts[0].replace(\"data:\", \"\");\n  formData.base64 = parts[1];\n\n  for (var name in formData) {\n    if (formData.hasOwnProperty(name)) {\n      var input = document.createElement('input');\n      input.setAttribute('type', 'hidden');\n      input.setAttribute('name', name);\n      input.setAttribute('value', formData[name]);\n\n      form.appendChild(input);\n    }\n  }\n\n  document.body.appendChild(form);\n  form.submit();\n  document.body.removeChild(form);\n}\n\n"], "mappings": "AAAA,OAAO,SAASA,MAAMA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAC9C,IAAKA,OAAO,KAAK,KAAK,CAAC,EAAGA,OAAO,GAAG,CAAC,CAAC;EAEtC,IAAIC,IAAI,GAAGC,WAAW;EAEtB,IAAIF,OAAO,CAACG,UAAU,IAAI,CAACH,OAAO,CAACI,QAAQ,EAAE;IAC3C,MAAM,IAAIC,KAAK,CAAC,4CAA4C,CAAC;EAC/D;EAEA,IAAI,CAACL,OAAO,CAACG,UAAU,EAAE;IACvB,IAAIG,WAAW,CAAC,CAAC,EAAE;MACjBL,IAAI,GAAGM,aAAa;IACtB;IAEA,IAAIC,SAAS,CAACC,UAAU,EAAE;MACxBR,IAAI,GAAGS,UAAU;IACnB;EACF;EAEAT,IAAI,CAACH,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC;AAC/B;AAEA,IAAIW,MAAM,GAAG,SAAAA,CAAA,EAAY;EAAE,OAAOC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;AAAE,CAAC;AAChE,IAAIP,WAAW,GAAG,SAAAA,CAAA,EAAY;EAAE,OAAO,UAAU,IAAIK,MAAM,CAAC,CAAC;AAAE,CAAC;AAEhE,SAASD,UAAUA,CAACZ,IAAI,EAAEC,QAAQ,EAAE;EAClC,IAAIe,IAAI,GAAGhB,IAAI,CAAC,CAAC;;EAEjB,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5B,IAAIiB,KAAK,GAAGjB,IAAI,CAACkB,KAAK,CAAC,UAAU,CAAC;IAClC,IAAIC,WAAW,GAAGF,KAAK,CAAC,CAAC,CAAC;IAC1B,IAAIG,MAAM,GAAGC,IAAI,CAACJ,KAAK,CAAC,CAAC,CAAC,CAAC;IAC3B,IAAIK,KAAK,GAAG,IAAIC,UAAU,CAACH,MAAM,CAACI,MAAM,CAAC;IAEzC,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGL,MAAM,CAACI,MAAM,EAAEC,GAAG,EAAE,EAAE;MAC5CH,KAAK,CAACG,GAAG,CAAC,GAAGL,MAAM,CAACM,UAAU,CAACD,GAAG,CAAC;IACrC;IAEAT,IAAI,GAAG,IAAIW,IAAI,CAAC,CAAEL,KAAK,CAACM,MAAM,CAAE,EAAE;MAAEC,IAAI,EAAEV;IAAY,CAAC,CAAC;EAC1D;EAEAT,SAAS,CAACC,UAAU,CAACK,IAAI,EAAEf,QAAQ,CAAC;AACtC;AAEA,SAASQ,aAAaA,CAACT,IAAI,EAAEC,QAAQ,EAAE;EACrC,IAAI6B,OAAO,GAAG9B,IAAI;EAClB,IAAI+B,MAAM,CAACJ,IAAI,IAAI3B,IAAI,YAAY2B,IAAI,EAAE;IACvCG,OAAO,GAAGE,GAAG,CAACC,eAAe,CAACjC,IAAI,CAAC;EACrC;EAEA,IAAIkC,SAAS,GAAGrB,MAAM,CAAC,CAAC;EACxBqB,SAAS,CAACC,QAAQ,GAAGlC,QAAQ;EAC7BiC,SAAS,CAACE,IAAI,GAAGN,OAAO;EAExB,IAAIO,CAAC,GAAGvB,QAAQ,CAACwB,WAAW,CAAC,aAAa,CAAC;EAC3CD,CAAC,CAACE,cAAc,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAER,MAAM,EAC3C,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC;EAErDG,SAAS,CAACM,aAAa,CAACH,CAAC,CAAC;EAC1BI,UAAU,CAAC,YAAY;IAAE,OAAOT,GAAG,CAACU,eAAe,CAACZ,OAAO,CAAC;EAAE,CAAC,CAAC;AAClE;AAEA,SAAS1B,WAAWA,CAAC0B,OAAO,EAAE7B,QAAQ,EAAEC,OAAO,EAAE;EAC/C,IAAI,CAACA,OAAO,CAACI,QAAQ,EAAE;IACrB;EACF;EAEA,IAAIqC,IAAI,GAAG7B,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;EACzC4B,IAAI,CAACC,YAAY,CAAC,QAAQ,EAAE1C,OAAO,CAACI,QAAQ,CAAC;EAC7CqC,IAAI,CAACC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC;EACnCD,IAAI,CAACC,YAAY,CAAC,QAAQ,EAAE1C,OAAO,CAAC2C,WAAW,IAAI,OAAO,CAAC;EAE3D,IAAIC,QAAQ,GAAG5C,OAAO,CAAC6C,SAAS,IAAI,CAAC,CAAC;EACtCD,QAAQ,CAAC7C,QAAQ,GAAGA,QAAQ;EAE5B,IAAIgB,KAAK,GAAGa,OAAO,CAACZ,KAAK,CAAC,UAAU,CAAC;EACrC4B,QAAQ,CAAC3B,WAAW,GAAGF,KAAK,CAAC,CAAC,CAAC,CAAC+B,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;EACpDF,QAAQ,CAAC1B,MAAM,GAAGH,KAAK,CAAC,CAAC,CAAC;EAE1B,KAAK,IAAIgC,IAAI,IAAIH,QAAQ,EAAE;IACzB,IAAIA,QAAQ,CAACI,cAAc,CAACD,IAAI,CAAC,EAAE;MACjC,IAAIE,KAAK,GAAGrC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;MAC3CoC,KAAK,CAACP,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;MACpCO,KAAK,CAACP,YAAY,CAAC,MAAM,EAAEK,IAAI,CAAC;MAChCE,KAAK,CAACP,YAAY,CAAC,OAAO,EAAEE,QAAQ,CAACG,IAAI,CAAC,CAAC;MAE3CN,IAAI,CAACS,WAAW,CAACD,KAAK,CAAC;IACzB;EACF;EAEArC,QAAQ,CAACuC,IAAI,CAACD,WAAW,CAACT,IAAI,CAAC;EAC/BA,IAAI,CAACW,MAAM,CAAC,CAAC;EACbxC,QAAQ,CAACuC,IAAI,CAACE,WAAW,CAACZ,IAAI,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}