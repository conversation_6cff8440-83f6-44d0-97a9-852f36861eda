{"ast": null, "code": "/* eslint-disable no-multi-spaces, key-spacing, indent, camelcase, space-before-blocks, eqeqeq, brace-style */\n/* eslint-disable space-infix-ops, space-before-function-paren, array-bracket-spacing, object-curly-spacing */\n/* eslint-disable no-nested-ternary, max-params, default-case, no-else-return, no-empty */\n/* eslint-disable no-param-reassign, no-var, block-scoped-var */\n\nimport { BinaryStream, ucs2encode, base64ToUint8Array, HAS_TYPED_ARRAYS } from \"./utils\";\nimport { support } from '../common';\nimport { TTFFont } from \"./ttf\";\nimport { deflate, supportsDeflate } from './deflate';\nimport { encodeUTF16BE, BOM } from \"../util/encode-utf\";\nimport { encodeBase64 } from \"../util\";\nvar browser = support.browser;\nvar NL = \"\\n\";\nvar RESOURCE_COUNTER = 0;\nvar PATTERN_COUNTER = 0;\nvar PAPER_SIZE = {\n  a0: [2383.94, 3370.39],\n  a1: [1683.78, 2383.94],\n  a2: [1190.55, 1683.78],\n  a3: [841.89, 1190.55],\n  a4: [595.28, 841.89],\n  a5: [419.53, 595.28],\n  a6: [297.64, 419.53],\n  a7: [209.76, 297.64],\n  a8: [147.40, 209.76],\n  a9: [104.88, 147.40],\n  a10: [73.70, 104.88],\n  b0: [2834.65, 4008.19],\n  b1: [2004.09, 2834.65],\n  b2: [1417.32, 2004.09],\n  b3: [1000.63, 1417.32],\n  b4: [708.66, 1000.63],\n  b5: [498.90, 708.66],\n  b6: [354.33, 498.90],\n  b7: [249.45, 354.33],\n  b8: [175.75, 249.45],\n  b9: [124.72, 175.75],\n  b10: [87.87, 124.72],\n  c0: [2599.37, 3676.54],\n  c1: [1836.85, 2599.37],\n  c2: [1298.27, 1836.85],\n  c3: [918.43, 1298.27],\n  c4: [649.13, 918.43],\n  c5: [459.21, 649.13],\n  c6: [323.15, 459.21],\n  c7: [229.61, 323.15],\n  c8: [161.57, 229.61],\n  c9: [113.39, 161.57],\n  c10: [79.37, 113.39],\n  executive: [521.86, 756.00],\n  folio: [612.00, 936.00],\n  legal: [612.00, 1008.00],\n  letter: [612.00, 792.00],\n  tabloid: [792.00, 1224.00]\n};\nfunction makeOutput() {\n  var indentLevel = 0,\n    output = BinaryStream();\n  function out() {\n    var arguments$1 = arguments;\n    for (var i = 0; i < arguments.length; ++i) {\n      var x = arguments$1[i];\n      if (x === undefined) {\n        throw new Error(\"Cannot output undefined to PDF\");\n      } else if (x instanceof PDFValue) {\n        x.beforeRender(out);\n        x.render(out);\n      } else if (isArray(x)) {\n        renderArray(x, out);\n      } else if (isDate(x)) {\n        renderDate(x, out);\n      } else if (typeof x == \"number\") {\n        if (isNaN(x)) {\n          throw new Error(\"Cannot output NaN to PDF\");\n        }\n        // make sure it doesn't end up in exponent notation\n        var num = x.toFixed(7);\n        if (num.indexOf(\".\") >= 0) {\n          num = num.replace(/\\.?0+$/, \"\");\n        }\n        if (num == \"-0\") {\n          num = \"0\";\n        }\n        output.writeString(num);\n      } else if (/string|boolean/.test(typeof x)) {\n        output.writeString(String(x));\n      } else if (typeof x.get == \"function\") {\n        output.write(x.get());\n      } else if (typeof x == \"object\") {\n        if (!x) {\n          output.writeString(\"null\");\n        } else {\n          out(new PDFDictionary(x));\n        }\n      }\n    }\n  }\n  out.writeData = function (data) {\n    output.write(data);\n  };\n  out.withIndent = function (f) {\n    ++indentLevel;\n    f(out);\n    --indentLevel;\n  };\n  out.indent = function () {\n    out(NL, pad(\"\", indentLevel * 2, \"  \"));\n    out.apply(null, arguments);\n  };\n  out.offset = function () {\n    return output.offset();\n  };\n  out.toString = function () {\n    throw new Error(\"FIX CALLER\");\n  };\n  out.get = function () {\n    return output.get();\n  };\n  out.stream = function () {\n    return output;\n  };\n  return out;\n}\nfunction wrapObject(value, id) {\n  var beforeRender = value.beforeRender;\n  var renderValue = value.render;\n  value.beforeRender = function () {};\n  value.render = function (out) {\n    out(id, \" 0 R\");\n  };\n  value.renderFull = function (out) {\n    value._offset = out.offset();\n    out(id, \" 0 obj \");\n    beforeRender.call(value, out);\n    renderValue.call(value, out);\n    out(\" endobj\");\n  };\n}\nfunction getPaperOptions(getOption) {\n  if (typeof getOption != \"function\") {\n    var options = getOption;\n    getOption = function (key, def) {\n      return key in options ? options[key] : def;\n    };\n  }\n  var paperSize = getOption(\"paperSize\", PAPER_SIZE.a4);\n  if (!paperSize) {\n    return {};\n  }\n  if (typeof paperSize == \"string\") {\n    paperSize = PAPER_SIZE[paperSize.toLowerCase()];\n    if (paperSize == null) {\n      throw new Error(\"Unknown paper size\");\n    }\n  }\n  paperSize[0] = unitsToPoints(paperSize[0]);\n  paperSize[1] = unitsToPoints(paperSize[1]);\n  if (getOption(\"landscape\", false)) {\n    paperSize = [Math.max(paperSize[0], paperSize[1]), Math.min(paperSize[0], paperSize[1])];\n  }\n  var margin = getOption(\"margin\");\n  if (margin) {\n    if (typeof margin == \"string\" || typeof margin == \"number\") {\n      margin = unitsToPoints(margin, 0);\n      margin = {\n        left: margin,\n        top: margin,\n        right: margin,\n        bottom: margin\n      };\n    } else {\n      margin = {\n        left: unitsToPoints(margin.left, 0),\n        top: unitsToPoints(margin.top, 0),\n        right: unitsToPoints(margin.right, 0),\n        bottom: unitsToPoints(margin.bottom, 0)\n      };\n    }\n    if (getOption(\"addMargin\")) {\n      paperSize[0] += margin.left + margin.right;\n      paperSize[1] += margin.top + margin.bottom;\n    }\n  }\n  return {\n    paperSize: paperSize,\n    margin: margin\n  };\n}\nvar FONT_CACHE = {\n  \"Times-Roman\": true,\n  \"Times-Bold\": true,\n  \"Times-Italic\": true,\n  \"Times-BoldItalic\": true,\n  \"Helvetica\": true,\n  \"Helvetica-Bold\": true,\n  \"Helvetica-Oblique\": true,\n  \"Helvetica-BoldOblique\": true,\n  \"Courier\": true,\n  \"Courier-Bold\": true,\n  \"Courier-Oblique\": true,\n  \"Courier-BoldOblique\": true,\n  \"Symbol\": true,\n  \"ZapfDingbats\": true\n};\nfunction loadBinary(url, cont) {\n  // IE throws Accesss denied error for Data URIs\n  var m;\n  if (browser.msie && (m = /^data:.*?;base64,/i.exec(url))) {\n    cont(base64ToUint8Array(url.substr(m[0].length)));\n    return;\n  }\n  function error() {\n    if (window.console) {\n      if (window.console.error) {\n        window.console.error(\"Cannot load URL: %s\", url);\n      } else {\n        window.console.log(\"Cannot load URL: %s\", url);\n      }\n    }\n    cont(null);\n  }\n  var req = new XMLHttpRequest();\n  req.open('GET', url, true);\n  if (HAS_TYPED_ARRAYS) {\n    req.responseType = \"arraybuffer\";\n  }\n  req.onload = function () {\n    if (req.status == 200 || req.status == 304) {\n      if (HAS_TYPED_ARRAYS) {\n        cont(new Uint8Array(req.response));\n      } else {\n        cont(new window.VBArray(req.responseBody).toArray()); // IE9 only\n      }\n    } else {\n      error();\n    }\n  };\n  req.onerror = error;\n  req.send(null);\n}\nfunction loadFont(url, cont) {\n  var font = FONT_CACHE[url];\n  if (font) {\n    cont(font);\n  } else {\n    loadBinary(url, function (data) {\n      if (data == null) {\n        throw new Error(\"Cannot load font from \" + url);\n      } else {\n        var font = new TTFFont(data);\n        FONT_CACHE[url] = font;\n        cont(font);\n      }\n    });\n  }\n}\nvar IMAGE_CACHE = {};\nfunction clearImageCache() {\n  IMAGE_CACHE = {};\n}\nfunction loadImage(url, size, cont, options) {\n  var img = IMAGE_CACHE[url],\n    bloburl,\n    blob;\n  if (img) {\n    cont(img);\n  } else {\n    img = new Image();\n    if (!/^data:/i.test(url)) {\n      img.crossOrigin = \"Anonymous\";\n    }\n    if (HAS_TYPED_ARRAYS && !/^data:/i.test(url)) {\n      // IE10 fails to load images from another domain even when the server sends the\n      // proper CORS headers.  a XHR, however, will be able to load the data.\n      // http://stackoverflow.com/a/19734516/154985\n      //\n      // On the other hand, it's worth doing it this way for all browsers which support\n      // responseType = \"blob\" (HAS_TYPED_ARRAYS will be true), because we can inspect the\n      // mime type and if it's a JPEG (very common case) we can save a lot of time in\n      // _load below.\n      var xhr = new XMLHttpRequest();\n      xhr.onload = function () {\n        blob = xhr.response;\n        if (browser.mozilla && blob.type == \"image/svg+xml\") {\n          // Firefox won't render SVGs that don't contain width and height attributes.\n          var reader = new FileReader();\n          reader.onload = function () {\n            var doc = new window.DOMParser().parseFromString(this.result, \"image/svg+xml\");\n            var svg = doc.documentElement;\n            if (svg.getAttribute(\"width\") && svg.getAttribute(\"height\")) {\n              // we're good, continue with the existing blob.\n              bloburl = URL.createObjectURL(blob);\n              _load(bloburl);\n            } else {\n              svg.setAttribute(\"width\", size.width);\n              svg.setAttribute(\"height\", size.height);\n              var xml = new window.XMLSerializer().serializeToString(svg);\n              var dataURL = \"data:image/svg+xml;base64,\" + encodeBase64(xml);\n              _load(dataURL);\n            }\n          };\n          reader.readAsText(blob);\n        } else {\n          bloburl = URL.createObjectURL(blob);\n          _load(bloburl);\n        }\n      };\n      xhr.onerror = _onerror;\n      xhr.open(\"GET\", url, true);\n      xhr.responseType = \"blob\";\n      xhr.send();\n    } else {\n      _load(url);\n    }\n  }\n  function _load(url) {\n    img.src = url;\n    if (img.complete && !browser.msie) {\n      // IE, bless its little heart, says img.complete == true even though the image is\n      // not loaded (width=0), therefore we must go the onload route (ticket 929635).\n      _onload.call(img);\n    } else {\n      img.onload = _onload;\n      img.onerror = _onerror;\n    }\n  }\n  function _trycanvas() {\n    if (!size) {\n      size = {\n        width: img.width,\n        height: img.height\n      };\n    }\n    var canvas = document.createElement(\"canvas\");\n    canvas.width = size.width;\n    canvas.height = size.height;\n    var ctx = canvas.getContext(\"2d\");\n    ctx.drawImage(img, 0, 0, size.width, size.height);\n    var imgdata;\n    try {\n      imgdata = ctx.getImageData(0, 0, size.width, size.height);\n    } catch (ex) {\n      // it tainted the canvas -- can't draw it.\n      _onerror();\n      return;\n    } finally {\n      if (bloburl) {\n        URL.revokeObjectURL(bloburl);\n      }\n    }\n\n    // in case it contains transparency, we must separate rgb data from the alpha\n    // channel and create a PDFRawImage image with opacity.  otherwise we can use a\n    // PDFJpegImage.\n    //\n    // to do this in one step, we create the rgb and alpha streams anyway, even if\n    // we might end up not using them if hasAlpha remains false.\n\n    var hasAlpha = false,\n      rgb = BinaryStream(),\n      alpha = BinaryStream();\n    var rawbytes = imgdata.data;\n    var i = 0;\n    while (i < rawbytes.length) {\n      rgb.writeByte(rawbytes[i++]);\n      rgb.writeByte(rawbytes[i++]);\n      rgb.writeByte(rawbytes[i++]);\n      var a = rawbytes[i++];\n      if (a < 255) {\n        hasAlpha = true;\n      }\n      alpha.writeByte(a);\n    }\n    if (hasAlpha || options.keepPNG) {\n      img = new PDFRawImage(size.width, size.height, rgb, alpha);\n    } else {\n      // no transparency, encode as JPEG.\n      var data = canvas.toDataURL(\"image/jpeg\", options.jpegQuality);\n      data = data.substr(data.indexOf(\";base64,\") + 8);\n      var stream = BinaryStream();\n      stream.writeBase64(data);\n      img = new PDFJpegImage(stream);\n    }\n    cont(IMAGE_CACHE[url] = img);\n  }\n  function _onerror() {\n    cont(IMAGE_CACHE[url] = \"ERROR\");\n  }\n  function _onload() {\n    if (size) {\n      var svg = blob && blob.type === 'image/svg+xml' || /^data:image\\/svg\\+xml;/i.test(this.src.substring(0, 19));\n      var upscale = size.width >= img.width || size.height >= img.height;\n\n      // Use the original image if requested size is bigger than the source,\n      // unless it's an SVG that can be upscaled.\n      if (!svg && upscale) {\n        size = null;\n      }\n    }\n    if (!size && blob && /^image\\/jpe?g$/i.test(blob.type)) {\n      // If we know we got a JPEG, we can skip the process of rendering it to a\n      // canvas, getting the pixel data, searching for transparency we know we won't\n      // find, getting back a data URI and then decoding the BASE64 to finally get the\n      // binary we already have.  Also, we avoid downgrading the image quality, with\n      // the possible drawback of making a bigger PDF; still, seems legit.\n      //\n      // Besides saving a lot of work, this also reuses the buffer memory\n      // (BinaryStream does not create a copy), potentially saving some GC cycles.\n      var reader = new FileReader();\n      reader.onload = function () {\n        try {\n          var img = new PDFJpegImage(BinaryStream(new Uint8Array(this.result)));\n          URL.revokeObjectURL(bloburl);\n          cont(IMAGE_CACHE[url] = img);\n        } catch (ex) {\n          // if there's an error parsing the JPEG stream, it could be due to a\n          // misconfigured server (improper content-type:\n          // https://github.com/telerik/kendo-ui-core/issues/4184).  If that's the case,\n          // the canvas will still be able to draw it.\n          _trycanvas();\n        }\n      };\n      reader.readAsArrayBuffer(blob);\n    } else {\n      _trycanvas();\n    }\n  }\n}\nfunction manyLoader(loadOne) {\n  return function (urls, callback) {\n    var n = urls.length,\n      i = n;\n    if (n === 0) {\n      return callback();\n    }\n    function next() {\n      if (--n === 0) {\n        callback();\n      }\n    }\n    while (i-- > 0) {\n      loadOne(urls[i], next);\n    }\n  };\n}\nvar loadFonts = manyLoader(loadFont);\nvar loadImages = function (images, callback, options) {\n  options = Object.assign({\n    jpegQuality: 0.92,\n    keepPNG: false\n  }, options);\n  var urls = Object.keys(images),\n    n = urls.length;\n  if (n === 0) {\n    return callback();\n  }\n  function next() {\n    if (--n === 0) {\n      callback();\n    }\n  }\n  urls.forEach(function (url) {\n    loadImage(url, images[url], next, options);\n  });\n};\nvar PDFDocument = function PDFDocument(options) {\n  var self = this;\n  var out = makeOutput();\n  var objcount = 0;\n  var objects = [];\n  function getOption(name, defval) {\n    return options && options[name] != null ? options[name] : defval;\n  }\n  self.getOption = getOption;\n  self.attach = function (value) {\n    if (objects.indexOf(value) < 0) {\n      wrapObject(value, ++objcount);\n      objects.push(value);\n    }\n    return value;\n  };\n  self.pages = [];\n  self.FONTS = {};\n  self.PATTERNS = {};\n  self.IMAGES = {};\n  self.GRAD_COL_FUNCTIONS = {}; // cache for color gradient functions\n  self.GRAD_OPC_FUNCTIONS = {}; // cache for opacity gradient functions\n  self.GRAD_COL = {}; // cache for whole color gradient objects\n  self.GRAD_OPC = {}; // cache for whole opacity gradient objects\n\n  var catalog = self.attach(new PDFCatalog());\n  var pageTree = self.attach(new PDFPageTree());\n  if (getOption(\"autoPrint\")) {\n    var nameTree = {};\n    nameTree.JavaScript = new PDFDictionary({\n      Names: [new PDFString(\"JS\"), self.attach(new PDFDictionary({\n        S: _(\"JavaScript\"),\n        JS: new PDFString(\"print(true);\")\n      }))]\n    });\n    catalog.props.Names = new PDFDictionary(nameTree);\n  }\n  catalog.setPages(pageTree);\n  var info = self.attach(new PDFDictionary({\n    Producer: new PDFString(getOption(\"producer\", \"Kendo UI PDF Generator\"), true),\n    // XXX: kendo.version?\n    Title: new PDFString(getOption(\"title\", \"\"), true),\n    Author: new PDFString(getOption(\"author\", \"\"), true),\n    Subject: new PDFString(getOption(\"subject\", \"\"), true),\n    Keywords: new PDFString(getOption(\"keywords\", \"\"), true),\n    Creator: new PDFString(getOption(\"creator\", \"Kendo UI PDF Generator\"), true),\n    CreationDate: getOption(\"date\", new Date())\n  }));\n  self.addPage = function (options) {\n    var paperOptions = getPaperOptions(function (name, defval) {\n      return options && options[name] != null ? options[name] : defval;\n    });\n    var paperSize = paperOptions.paperSize;\n    var margin = paperOptions.margin;\n    var contentWidth = paperSize[0];\n    var contentHeight = paperSize[1];\n    if (margin) {\n      contentWidth -= margin.left + margin.right;\n      contentHeight -= margin.top + margin.bottom;\n    }\n    var content = new PDFStream(makeOutput(), null, true);\n    var props = {\n      Contents: self.attach(content),\n      Parent: pageTree,\n      MediaBox: [0, 0, paperSize[0], paperSize[1]]\n    };\n    var page = new PDFPage(self, props);\n    page._content = content;\n    pageTree.addPage(self.attach(page));\n\n    // canvas-like coord. system.  (0,0) is upper-left.\n    // text must be vertically mirorred before drawing.\n    page.transform(1, 0, 0, -1, 0, paperSize[1]);\n    if (margin) {\n      page.translate(margin.left, margin.top);\n      // XXX: clip to right/bottom margin.  Make this optional?\n      page.rect(0, 0, contentWidth, contentHeight);\n      page.clip();\n    }\n    self.pages.push(page);\n    return page;\n  };\n  self.render = function () {\n    var i;\n    /// file header\n    out(\"%PDF-1.4\", NL, \"%\\xc2\\xc1\\xda\\xcf\\xce\", NL, NL);\n\n    /// file body\n    for (i = 0; i < objects.length; ++i) {\n      objects[i].renderFull(out);\n      out(NL, NL);\n    }\n\n    /// cross-reference table\n    var xrefOffset = out.offset();\n    out(\"xref\", NL, 0, \" \", objects.length + 1, NL);\n    out(\"0000000000 65535 f \", NL);\n    for (i = 0; i < objects.length; ++i) {\n      out(zeropad(objects[i]._offset, 10), \" 00000 n \", NL);\n    }\n    out(NL);\n\n    /// trailer\n    out(\"trailer\", NL);\n    out(new PDFDictionary({\n      Size: objects.length + 1,\n      Root: catalog,\n      Info: info\n    }), NL, NL);\n\n    /// end\n    out(\"startxref\", NL, xrefOffset, NL);\n    out(\"%%EOF\", NL);\n    return out.stream().offset(0);\n  };\n  self.loadFonts = loadFonts;\n  self.loadImages = loadImages;\n};\nPDFDocument.prototype.getFont = function getFont(url) {\n  var font = this.FONTS[url];\n  if (!font) {\n    font = FONT_CACHE[url];\n    if (!font) {\n      throw new Error(\"Font \" + url + \" has not been loaded\");\n    }\n    if (font === true) {\n      font = this.attach(new PDFStandardFont(url));\n    } else {\n      font = this.attach(new PDFFont(this, font));\n    }\n    this.FONTS[url] = font;\n  }\n  return font;\n};\nPDFDocument.prototype.getPattern = function getPattern(fill, page, drawPattern) {\n  var pattern = this.PATTERNS[fill.id];\n  if (!pattern) {\n    pattern = this.attach(new PDFPattern(fill, page, drawPattern));\n    this.PATTERNS[fill.id] = pattern;\n  }\n  return pattern;\n};\nPDFDocument.prototype.getImage = function getImage(url) {\n  var img = this.IMAGES[url];\n  if (!img) {\n    img = IMAGE_CACHE[url];\n    if (!img) {\n      throw new Error(\"Image \" + url + \" has not been loaded\");\n    }\n    if (img === \"ERROR\") {\n      return null;\n    }\n    img = this.IMAGES[url] = this.attach(img.asStream(this));\n  }\n  return img;\n};\nPDFDocument.prototype.getOpacityGS = function getOpacityGS(opacity, forStroke) {\n  var id = parseFloat(opacity).toFixed(3);\n  opacity = parseFloat(id);\n  id += forStroke ? \"S\" : \"F\";\n  var cache = this._opacityGSCache || (this._opacityGSCache = {});\n  var gs = cache[id];\n  if (!gs) {\n    var props = {\n      Type: _(\"ExtGState\")\n    };\n    if (forStroke) {\n      props.CA = opacity;\n    } else {\n      props.ca = opacity;\n    }\n    gs = this.attach(new PDFDictionary(props));\n    gs._resourceName = _(\"GS\" + ++RESOURCE_COUNTER);\n    cache[id] = gs;\n  }\n  return gs;\n};\nPDFDocument.prototype.dict = function dict(props) {\n  return new PDFDictionary(props);\n};\nPDFDocument.prototype.name = function name(str) {\n  return _(str);\n};\nPDFDocument.prototype.stream = function stream(props, content) {\n  return new PDFStream(content, props);\n};\n\n/* -----[ utils ]----- */\n\nfunction pad(str, len, ch) {\n  while (str.length < len) {\n    str = ch + str;\n  }\n  return str;\n}\nfunction zeropad(n, len) {\n  return pad(String(n), len, \"0\");\n}\nfunction hasOwnProperty(obj, key) {\n  return Object.prototype.hasOwnProperty.call(obj, key);\n}\nvar isArray = Array.isArray || function (obj) {\n  return obj instanceof Array;\n};\nfunction isDate(obj) {\n  return obj instanceof Date;\n}\nfunction renderArray(a, out) {\n  out(\"[\");\n  if (a.length > 0) {\n    out.withIndent(function () {\n      for (var i = 0; i < a.length; ++i) {\n        if (i > 0 && i % 8 === 0) {\n          out.indent(a[i]);\n        } else {\n          out(\" \", a[i]);\n        }\n      }\n    });\n    //out.indent();\n  }\n  out(\" ]\");\n}\nfunction renderDate(date, out) {\n  out(\"(D:\", zeropad(date.getUTCFullYear(), 4), zeropad(date.getUTCMonth() + 1, 2), zeropad(date.getUTCDate(), 2), zeropad(date.getUTCHours(), 2), zeropad(date.getUTCMinutes(), 2), zeropad(date.getUTCSeconds(), 2), \"Z)\");\n}\nfunction mm2pt(mm) {\n  return mm * (72 / 25.4);\n}\nfunction cm2pt(cm) {\n  return mm2pt(cm * 10);\n}\nfunction in2pt(inch) {\n  return inch * 72;\n}\nfunction unitsToPoints(x, def) {\n  if (typeof x == \"number\") {\n    return x;\n  }\n  if (typeof x == \"string\") {\n    var m;\n    m = /^\\s*([0-9.]+)\\s*(mm|cm|in|pt)\\s*$/.exec(x);\n    if (m) {\n      var num = parseFloat(m[1]);\n      if (!isNaN(num)) {\n        if (m[2] == \"pt\") {\n          return num;\n        }\n        return {\n          \"mm\": mm2pt,\n          \"cm\": cm2pt,\n          \"in\": in2pt\n        }[m[2]](num);\n      }\n    }\n  }\n  if (def != null) {\n    return def;\n  }\n  throw new Error(\"Can't parse unit: \" + x);\n}\n\n/* -----[ PDF basic objects ]----- */\n\nvar PDFValue = function PDFValue() {};\nPDFValue.prototype.beforeRender = function beforeRender() {};\nvar PDFString = function (PDFValue) {\n  function PDFString(value, utf16be) {\n    PDFValue.call(this);\n    this.value = value;\n    this.utf16be = Boolean(utf16be);\n  }\n  if (PDFValue) PDFString.__proto__ = PDFValue;\n  PDFString.prototype = Object.create(PDFValue && PDFValue.prototype);\n  PDFString.prototype.constructor = PDFString;\n  PDFString.prototype.render = function render(out) {\n    var txt = this.value;\n    if (this.utf16be) {\n      txt = BOM + encodeUTF16BE(txt);\n      txt = txt.replace(/([\\(\\)\\\\])/g, \"\\\\$1\");\n      out(\"(\", txt, \")\");\n    } else {\n      // out.writeString truncates charcodes to 8 bits and\n      // 0x128 & 0xFF is 40, the code for open paren.\n      // therefore we need to do the chopping here to make\n      // sure we backslash all cases.\n      var data = [40]; // open PDF string '('\n      for (var i = 0; i < txt.length; ++i) {\n        var code = txt.charCodeAt(i) & 0xFF;\n        if (code == 40 || code == 41 || code == 92) {\n          // backslash before (, ) and \\\n          data.push(92);\n        }\n        data.push(code);\n      }\n      data.push(41); // ')' close PDF string\n      out.writeData(data);\n    }\n  };\n  PDFString.prototype.toString = function toString() {\n    return this.value;\n  };\n  return PDFString;\n}(PDFValue);\nvar PDFHexString = function (PDFString) {\n  function PDFHexString(value) {\n    PDFString.call(this, value);\n    this.value = value;\n  }\n  if (PDFString) PDFHexString.__proto__ = PDFString;\n  PDFHexString.prototype = Object.create(PDFString && PDFString.prototype);\n  PDFHexString.prototype.constructor = PDFHexString;\n  PDFHexString.prototype.render = function render(out) {\n    var this$1 = this;\n    out(\"<\");\n    for (var i = 0; i < this.value.length; ++i) {\n      out(zeropad(this$1.value.charCodeAt(i).toString(16), 4));\n    }\n    out(\">\");\n  };\n  return PDFHexString;\n}(PDFString);\n\n/// names\nvar PDFName = function (PDFValue) {\n  function PDFName(name) {\n    PDFValue.call(this);\n    this.name = name;\n  }\n  if (PDFValue) PDFName.__proto__ = PDFValue;\n  PDFName.prototype = Object.create(PDFValue && PDFValue.prototype);\n  PDFName.prototype.constructor = PDFName;\n  PDFName.get = function get(name) {\n    return _(name);\n  };\n  PDFName.prototype.render = function render(out) {\n    out(\"/\" + this.escape());\n  };\n  PDFName.prototype.escape = function escape() {\n    return this.name.replace(/[^\\x21-\\x7E]/g, function (c) {\n      return \"#\" + zeropad(c.charCodeAt(0).toString(16), 2);\n    });\n  };\n  PDFName.prototype.toString = function toString() {\n    return this.name;\n  };\n  return PDFName;\n}(PDFValue);\nfunction _(name) {\n  return new PDFName(name);\n}\n\n/// dictionary\n\nvar PDFDictionary = function (PDFValue) {\n  function PDFDictionary(props) {\n    PDFValue.call(this);\n    this.props = props;\n  }\n  if (PDFValue) PDFDictionary.__proto__ = PDFValue;\n  PDFDictionary.prototype = Object.create(PDFValue && PDFValue.prototype);\n  PDFDictionary.prototype.constructor = PDFDictionary;\n  PDFDictionary.prototype.render = function render(out) {\n    var props = this.props,\n      empty = true;\n    out(\"<<\");\n    out.withIndent(function () {\n      for (var i in props) {\n        if (hasOwnProperty(props, i) && !/^_/.test(i)) {\n          empty = false;\n          out.indent(_(i), \" \", props[i]);\n        }\n      }\n    });\n    if (!empty) {\n      out.indent();\n    }\n    out(\">>\");\n  };\n  return PDFDictionary;\n}(PDFValue);\n\n/// streams\n\nvar PDFStream = function (PDFValue) {\n  function PDFStream(data, props, compress) {\n    PDFValue.call(this);\n    if (typeof data == \"string\") {\n      var tmp = BinaryStream();\n      tmp.write(data);\n      data = tmp;\n    }\n    this.data = data;\n    this.props = props || {};\n    this.compress = compress;\n  }\n  if (PDFValue) PDFStream.__proto__ = PDFValue;\n  PDFStream.prototype = Object.create(PDFValue && PDFValue.prototype);\n  PDFStream.prototype.constructor = PDFStream;\n  PDFStream.prototype.render = function render(out) {\n    var data = this.data.get(),\n      props = this.props;\n    if (this.compress && supportsDeflate()) {\n      if (!props.Filter) {\n        props.Filter = [];\n      } else if (!(props.Filter instanceof Array)) {\n        props.Filter = [props.Filter];\n      }\n      props.Filter.unshift(_(\"FlateDecode\"));\n      data = deflate(data);\n    }\n    props.Length = data.length;\n    out(new PDFDictionary(props), \" stream\", NL);\n    out.writeData(data);\n    out(NL, \"endstream\");\n  };\n  return PDFStream;\n}(PDFValue);\n\n/// catalog\n\nvar PDFCatalog = function (PDFDictionary) {\n  function PDFCatalog() {\n    PDFDictionary.call(this, {\n      Type: _(\"Catalog\")\n    });\n  }\n  if (PDFDictionary) PDFCatalog.__proto__ = PDFDictionary;\n  PDFCatalog.prototype = Object.create(PDFDictionary && PDFDictionary.prototype);\n  PDFCatalog.prototype.constructor = PDFCatalog;\n  PDFCatalog.prototype.setPages = function setPages(pagesObj) {\n    this.props.Pages = pagesObj;\n  };\n  return PDFCatalog;\n}(PDFDictionary);\n\n/// page tree\n\nvar PDFPageTree = function (PDFDictionary) {\n  function PDFPageTree() {\n    PDFDictionary.call(this, {\n      Type: _(\"Pages\"),\n      Kids: [],\n      Count: 0\n    });\n  }\n  if (PDFDictionary) PDFPageTree.__proto__ = PDFDictionary;\n  PDFPageTree.prototype = Object.create(PDFDictionary && PDFDictionary.prototype);\n  PDFPageTree.prototype.constructor = PDFPageTree;\n  PDFPageTree.prototype.addPage = function addPage(pageObj) {\n    this.props.Kids.push(pageObj);\n    this.props.Count++;\n  };\n  return PDFPageTree;\n}(PDFDictionary);\n\n/// images\n\n// JPEG\n\nvar SOF_CODES = [0xc0, 0xc1, 0xc2, 0xc3, 0xc5, 0xc6, 0xc7, 0xc9, 0xca, 0xcb, 0xcd, 0xce, 0xcf];\nvar PDFJpegImage = function PDFJpegImage(data) {\n  // we must determine the correct color space.  we'll parse a bit\n  // of the JPEG stream for this, it's still better than going\n  // through the canvas.\n  // https://github.com/telerik/kendo-ui-core/issues/2845\n  data.offset(0);\n  var width, height, colorSpace, bitsPerComponent;\n  var soi = data.readShort();\n  if (soi != 0xFFD8) {\n    // XXX: do we have some better options here?\n    throw new Error(\"Invalid JPEG image\");\n  }\n  while (!data.eof()) {\n    var ff = data.readByte();\n    if (ff != 0xFF) {\n      throw new Error(\"Invalid JPEG image\");\n    }\n    var marker = data.readByte();\n    var length = data.readShort();\n    if (SOF_CODES.indexOf(marker) >= 0) {\n      // \"start of frame\" marker\n      bitsPerComponent = data.readByte();\n      height = data.readShort();\n      width = data.readShort();\n      colorSpace = data.readByte();\n      break;\n    }\n    data.skip(length - 2);\n  }\n  if (colorSpace == null) {\n    throw new Error(\"Invalid JPEG image\");\n  }\n  var props = {\n    Type: _(\"XObject\"),\n    Subtype: _(\"Image\"),\n    Width: width,\n    Height: height,\n    BitsPerComponent: bitsPerComponent,\n    Filter: _(\"DCTDecode\")\n  };\n  switch (colorSpace) {\n    case 1:\n      props.ColorSpace = _(\"DeviceGray\");\n      break;\n    case 3:\n      props.ColorSpace = _(\"DeviceRGB\");\n      break;\n    case 4:\n      props.ColorSpace = _(\"DeviceCMYK\");\n      props.Decode = [1, 0, 1, 0, 1, 0, 1, 0]; // invert colors\n      break;\n  }\n  this.asStream = function () {\n    data.offset(0);\n    var stream = new PDFStream(data, props);\n    stream._resourceName = _(\"I\" + ++RESOURCE_COUNTER);\n    return stream;\n  };\n};\n\n// PDFRawImage will be used for images with transparency (PNG)\n\nvar PDFRawImage = function PDFRawImage(width, height, rgb, alpha) {\n  this.asStream = function (pdf) {\n    var mask = new PDFStream(alpha, {\n      Type: _(\"XObject\"),\n      Subtype: _(\"Image\"),\n      Width: width,\n      Height: height,\n      BitsPerComponent: 8,\n      ColorSpace: _(\"DeviceGray\")\n    }, true);\n    var stream = new PDFStream(rgb, {\n      Type: _(\"XObject\"),\n      Subtype: _(\"Image\"),\n      Width: width,\n      Height: height,\n      BitsPerComponent: 8,\n      ColorSpace: _(\"DeviceRGB\"),\n      SMask: pdf.attach(mask)\n    }, true);\n    stream._resourceName = _(\"I\" + ++RESOURCE_COUNTER);\n    return stream;\n  };\n};\nvar PDFPattern = function (PDFDictionary) {\n  function PDFPattern(fill, curPage, drawPattern) {\n    var ref = fill.size();\n    var width = ref.width;\n    var height = ref.height;\n    var page = new PDFPage(curPage._pdf, {});\n    page._content = new PDFStream(makeOutput(), null, true);\n    drawPattern(fill, page, {});\n    curPage._xResources = Object.assign(curPage._xResources, page._xResources);\n    curPage._fontResources = Object.assign(curPage._fontResources, page._fontResources);\n    curPage._gsResources = Object.assign(curPage._gsResources, page._gsResources);\n    PDFDictionary.call(this, {\n      Type: _(\"Pattern\"),\n      PatternType: 1,\n      PaintType: 1,\n      TilingType: 1,\n      BBox: [0, 0, width, height],\n      XStep: width,\n      YStep: height,\n      Matrix: [1, 0, 0, -1, 0, height],\n      Resources: {\n        ExtGState: new PDFDictionary(page._gsResources),\n        XObject: new PDFDictionary(page._xResources),\n        Font: new PDFDictionary(page._fontResources)\n      }\n    });\n    this._resourceName = _(\"P\" + ++PATTERN_COUNTER);\n    this.data = page._content.data;\n    this.compress = true;\n  }\n  if (PDFDictionary) PDFPattern.__proto__ = PDFDictionary;\n  PDFPattern.prototype = Object.create(PDFDictionary && PDFDictionary.prototype);\n  PDFPattern.prototype.constructor = PDFPattern;\n  PDFPattern.prototype.render = function render(out) {\n    PDFStream.prototype.render.call(this, out);\n  };\n  return PDFPattern;\n}(PDFDictionary);\n\n/// standard fonts\n\nvar PDFStandardFont = function (PDFDictionary) {\n  function PDFStandardFont(name) {\n    PDFDictionary.call(this, {\n      Type: _(\"Font\"),\n      Subtype: _(\"Type1\"),\n      BaseFont: _(name)\n    });\n    this._resourceName = _(\"F\" + ++RESOURCE_COUNTER);\n  }\n  if (PDFDictionary) PDFStandardFont.__proto__ = PDFDictionary;\n  PDFStandardFont.prototype = Object.create(PDFDictionary && PDFDictionary.prototype);\n  PDFStandardFont.prototype.constructor = PDFStandardFont;\n  PDFStandardFont.prototype.encodeText = function encodeText(str) {\n    return new PDFString(String(str));\n  };\n  return PDFStandardFont;\n}(PDFDictionary);\n\n/// TTF fonts\n\nvar PDFFont = function (PDFDictionary) {\n  function PDFFont(pdf, font, props) {\n    PDFDictionary.call(this, {});\n    props = this.props;\n    props.Type = _(\"Font\");\n    props.Subtype = _(\"Type0\");\n    props.Encoding = _(\"Identity-H\");\n    this._pdf = pdf;\n    this._font = font;\n    this._sub = font.makeSubset();\n    this._resourceName = _(\"F\" + ++RESOURCE_COUNTER);\n    var head = font.head;\n    this.name = font.psName;\n    var scale = this.scale = font.scale;\n    this.bbox = [head.xMin * scale, head.yMin * scale, head.xMax * scale, head.yMax * scale];\n    this.italicAngle = font.post.italicAngle;\n    this.ascent = font.ascent * scale;\n    this.descent = font.descent * scale;\n    this.lineGap = font.lineGap * scale;\n    this.capHeight = font.os2.capHeight || this.ascent;\n    this.xHeight = font.os2.xHeight || 0;\n    this.stemV = 0;\n    this.familyClass = (font.os2.familyClass || 0) >> 8;\n    this.isSerif = this.familyClass >= 1 && this.familyClass <= 7;\n    this.isScript = this.familyClass == 10;\n    this.flags = (font.post.isFixedPitch ? 1 : 0) | (this.isSerif ? 1 << 1 : 0) | (this.isScript ? 1 << 3 : 0) | (this.italicAngle !== 0 ? 1 << 6 : 0) | 1 << 5;\n  }\n  if (PDFDictionary) PDFFont.__proto__ = PDFDictionary;\n  PDFFont.prototype = Object.create(PDFDictionary && PDFDictionary.prototype);\n  PDFFont.prototype.constructor = PDFFont;\n  PDFFont.prototype.encodeText = function encodeText(text) {\n    return new PDFHexString(this._sub.encodeText(String(text)));\n  };\n  PDFFont.prototype.getTextWidth = function getTextWidth(fontSize, text) {\n    var this$1 = this;\n    var width = 0,\n      codeMap = this._font.cmap.codeMap;\n    for (var i = 0; i < text.length; ++i) {\n      var glyphId = codeMap[text.charCodeAt(i)];\n      width += this$1._font.widthOfGlyph(glyphId || 0);\n    }\n    return width * fontSize / 1000;\n  };\n  PDFFont.prototype.beforeRender = function beforeRender() {\n    var self = this;\n    var sub = self._sub;\n\n    // write the TTF data\n    var data = sub.render();\n    var fontStream = new PDFStream(BinaryStream(data), {\n      Length1: data.length\n    }, true);\n    var descriptor = self._pdf.attach(new PDFDictionary({\n      Type: _(\"FontDescriptor\"),\n      FontName: _(self._sub.psName),\n      FontBBox: self.bbox,\n      Flags: self.flags,\n      StemV: self.stemV,\n      ItalicAngle: self.italicAngle,\n      Ascent: self.ascent,\n      Descent: self.descent,\n      CapHeight: self.capHeight,\n      XHeight: self.xHeight,\n      FontFile2: self._pdf.attach(fontStream)\n    }));\n    var cmap = sub.ncid2ogid;\n    var firstChar = sub.firstChar;\n    var lastChar = sub.lastChar;\n    var charWidths = [];\n    (function loop(i, chunk) {\n      if (i <= lastChar) {\n        var gid = cmap[i];\n        if (gid == null) {\n          loop(i + 1);\n        } else {\n          if (!chunk) {\n            charWidths.push(i, chunk = []);\n          }\n          chunk.push(self._font.widthOfGlyph(gid));\n          loop(i + 1, chunk);\n        }\n      }\n    })(firstChar);\n\n    // As if two dictionaries weren't enough, we need another\n    // one, the \"descendant font\".  Only that one can be of\n    // Subtype CIDFontType2.  PDF is the X11 of document\n    // formats: portable but full of legacy that nobody cares\n    // about anymore.\n\n    var descendant = new PDFDictionary({\n      Type: _(\"Font\"),\n      Subtype: _(\"CIDFontType2\"),\n      BaseFont: _(self._sub.psName),\n      CIDSystemInfo: new PDFDictionary({\n        Registry: new PDFString(\"Adobe\"),\n        Ordering: new PDFString(\"Identity\"),\n        Supplement: 0\n      }),\n      FontDescriptor: descriptor,\n      FirstChar: firstChar,\n      LastChar: lastChar,\n      DW: Math.round(self._font.widthOfGlyph(0)),\n      W: charWidths,\n      CIDToGIDMap: self._pdf.attach(self._makeCidToGidMap())\n    });\n    var dict = self.props;\n    dict.BaseFont = _(self._sub.psName);\n    dict.DescendantFonts = [self._pdf.attach(descendant)];\n\n    // Compute the ToUnicode map so that apps can extract\n    // meaningful text from the PDF.\n    var unimap = new PDFToUnicodeCmap(firstChar, lastChar, sub.subset);\n    var unimapStream = new PDFStream(makeOutput(), null, true);\n    unimapStream.data(unimap);\n    dict.ToUnicode = self._pdf.attach(unimapStream);\n  };\n  PDFFont.prototype._makeCidToGidMap = function _makeCidToGidMap() {\n    return new PDFStream(BinaryStream(this._sub.cidToGidMap()), null, true);\n  };\n  return PDFFont;\n}(PDFDictionary);\nvar PDFToUnicodeCmap = function (PDFValue) {\n  function PDFToUnicodeCmap(firstChar, lastChar, map) {\n    PDFValue.call(this);\n    this.firstChar = firstChar;\n    this.lastChar = lastChar;\n    this.map = map;\n  }\n  if (PDFValue) PDFToUnicodeCmap.__proto__ = PDFValue;\n  PDFToUnicodeCmap.prototype = Object.create(PDFValue && PDFValue.prototype);\n  PDFToUnicodeCmap.prototype.constructor = PDFToUnicodeCmap;\n  PDFToUnicodeCmap.prototype.render = function render(out) {\n    out.indent(\"/CIDInit /ProcSet findresource begin\");\n    out.indent(\"12 dict begin\");\n    out.indent(\"begincmap\");\n    out.indent(\"/CIDSystemInfo <<\");\n    out.indent(\"  /Registry (Adobe)\");\n    out.indent(\"  /Ordering (UCS)\");\n    out.indent(\"  /Supplement 0\");\n    out.indent(\">> def\");\n    out.indent(\"/CMapName /Adobe-Identity-UCS def\");\n    out.indent(\"/CMapType 2 def\");\n    out.indent(\"1 begincodespacerange\");\n    out.indent(\"  <0000><ffff>\");\n    out.indent(\"endcodespacerange\");\n    var self = this;\n    out.indent(self.lastChar - self.firstChar + 1, \" beginbfchar\");\n    out.withIndent(function () {\n      for (var code = self.firstChar; code <= self.lastChar; ++code) {\n        var unicode = self.map[code];\n        var str = ucs2encode([unicode]);\n        out.indent(\"<\", zeropad(code.toString(16), 4), \">\", \"<\");\n        for (var i = 0; i < str.length; ++i) {\n          out(zeropad(str.charCodeAt(i).toString(16), 4));\n        }\n        out(\">\");\n      }\n    });\n    out.indent(\"endbfchar\");\n    out.indent(\"endcmap\");\n    out.indent(\"CMapName currentdict /CMap defineresource pop\");\n    out.indent(\"end\");\n    out.indent(\"end\");\n  };\n  return PDFToUnicodeCmap;\n}(PDFValue);\n\n/// gradients\n\nfunction makeHash(a) {\n  return a.map(function (x) {\n    return isArray(x) ? makeHash(x) : typeof x == \"number\" ? (Math.round(x * 1000) / 1000).toFixed(3) : x;\n  }).join(\" \");\n}\nfunction cacheColorGradientFunction(pdf, r1, g1, b1, r2, g2, b2) {\n  var hash = makeHash([r1, g1, b1, r2, g2, b2]);\n  var func = pdf.GRAD_COL_FUNCTIONS[hash];\n  if (!func) {\n    func = pdf.GRAD_COL_FUNCTIONS[hash] = pdf.attach(new PDFDictionary({\n      FunctionType: 2,\n      Domain: [0, 1],\n      Range: [0, 1, 0, 1, 0, 1],\n      N: 1,\n      C0: [r1, g1, b1],\n      C1: [r2, g2, b2]\n    }));\n  }\n  return func;\n}\nfunction cacheOpacityGradientFunction(pdf, a1, a2) {\n  var hash = makeHash([a1, a2]);\n  var func = pdf.GRAD_OPC_FUNCTIONS[hash];\n  if (!func) {\n    func = pdf.GRAD_OPC_FUNCTIONS[hash] = pdf.attach(new PDFDictionary({\n      FunctionType: 2,\n      Domain: [0, 1],\n      Range: [0, 1],\n      N: 1,\n      C0: [a1],\n      C1: [a2]\n    }));\n  }\n  return func;\n}\nfunction makeGradientFunctions(pdf, stops) {\n  var hasAlpha = false;\n  var opacities = [];\n  var colors = [];\n  var offsets = [];\n  var encode = [];\n  var i, prev, cur, prevColor, curColor;\n  for (i = 1; i < stops.length; ++i) {\n    prev = stops[i - 1];\n    cur = stops[i];\n    prevColor = prev.color;\n    curColor = cur.color;\n    colors.push(cacheColorGradientFunction(pdf, prevColor.r, prevColor.g, prevColor.b, curColor.r, curColor.g, curColor.b));\n    if (prevColor.a < 1 || curColor.a < 1) {\n      hasAlpha = true;\n    }\n    offsets.push(cur.offset);\n    encode.push(0, 1);\n  }\n  if (hasAlpha) {\n    for (i = 1; i < stops.length; ++i) {\n      prev = stops[i - 1];\n      cur = stops[i];\n      prevColor = prev.color;\n      curColor = cur.color;\n      opacities.push(cacheOpacityGradientFunction(pdf, prevColor.a, curColor.a));\n    }\n  }\n  offsets.pop();\n  return {\n    hasAlpha: hasAlpha,\n    colors: assemble(colors),\n    opacities: hasAlpha ? assemble(opacities) : null\n  };\n  function assemble(funcs) {\n    if (funcs.length == 1) {\n      return funcs[0];\n    }\n    return {\n      FunctionType: 3,\n      Functions: funcs,\n      Domain: [0, 1],\n      Bounds: offsets,\n      Encode: encode\n    };\n  }\n}\nfunction cacheColorGradient(pdf, isRadial, stops, coords, funcs, box) {\n  var shading, hash;\n  // if box is given then we have user-space coordinates, which\n  // means the gradient is designed for a certain position/size\n  // on page.  caching won't do any good.\n  if (!box) {\n    var a = [isRadial].concat(coords);\n    stops.forEach(function (x) {\n      a.push(x.offset, x.color.r, x.color.g, x.color.b);\n    });\n    hash = makeHash(a);\n    shading = pdf.GRAD_COL[hash];\n  }\n  if (!shading) {\n    shading = new PDFDictionary({\n      Type: _(\"Shading\"),\n      ShadingType: isRadial ? 3 : 2,\n      ColorSpace: _(\"DeviceRGB\"),\n      Coords: coords,\n      Domain: [0, 1],\n      Function: funcs,\n      Extend: [true, true]\n    });\n    pdf.attach(shading);\n    shading._resourceName = \"S\" + ++RESOURCE_COUNTER;\n    if (hash) {\n      pdf.GRAD_COL[hash] = shading;\n    }\n  }\n  return shading;\n}\nfunction cacheOpacityGradient(pdf, isRadial, stops, coords, funcs, box) {\n  var opacity, hash;\n  // if box is given then we have user-space coordinates, which\n  // means the gradient is designed for a certain position/size\n  // on page.  caching won't do any good.\n  if (!box) {\n    var a = [isRadial].concat(coords);\n    stops.forEach(function (x) {\n      a.push(x.offset, x.color.a);\n    });\n    hash = makeHash(a);\n    opacity = pdf.GRAD_OPC[hash];\n  }\n  if (!opacity) {\n    opacity = new PDFDictionary({\n      Type: _(\"ExtGState\"),\n      AIS: false,\n      CA: 1,\n      ca: 1,\n      SMask: {\n        Type: _(\"Mask\"),\n        S: _(\"Luminosity\"),\n        G: pdf.attach(new PDFStream(\"/a0 gs /s0 sh\", {\n          Type: _(\"XObject\"),\n          Subtype: _(\"Form\"),\n          FormType: 1,\n          BBox: box ? [box.left, box.top + box.height, box.left + box.width, box.top] : [0, 1, 1, 0],\n          Group: {\n            Type: _(\"Group\"),\n            S: _(\"Transparency\"),\n            CS: _(\"DeviceGray\"),\n            I: true\n          },\n          Resources: {\n            ExtGState: {\n              a0: {\n                CA: 1,\n                ca: 1\n              }\n            },\n            Shading: {\n              s0: {\n                ColorSpace: _(\"DeviceGray\"),\n                Coords: coords,\n                Domain: [0, 1],\n                ShadingType: isRadial ? 3 : 2,\n                Function: funcs,\n                Extend: [true, true]\n              }\n            }\n          }\n        }))\n      }\n    });\n    pdf.attach(opacity);\n    opacity._resourceName = \"O\" + ++RESOURCE_COUNTER;\n    if (hash) {\n      pdf.GRAD_OPC[hash] = opacity;\n    }\n  }\n  return opacity;\n}\nfunction cacheGradient(pdf, gradient, box) {\n  var isRadial = gradient.type == \"radial\";\n  var funcs = makeGradientFunctions(pdf, gradient.stops);\n  var coords = isRadial ? [gradient.start.x, gradient.start.y, gradient.start.r, gradient.end.x, gradient.end.y, gradient.end.r] : [gradient.start.x, gradient.start.y, gradient.end.x, gradient.end.y];\n  var shading = cacheColorGradient(pdf, isRadial, gradient.stops, coords, funcs.colors, gradient.userSpace && box);\n  var opacity = funcs.hasAlpha ? cacheOpacityGradient(pdf, isRadial, gradient.stops, coords, funcs.opacities, gradient.userSpace && box) : null;\n  return {\n    hasAlpha: funcs.hasAlpha,\n    shading: shading,\n    opacity: opacity\n  };\n}\n\n/// page object\n\nvar PDFPage = function (PDFDictionary) {\n  function PDFPage(pdf, props) {\n    PDFDictionary.call(this, props);\n    this._pdf = pdf;\n    this._rcount = 0;\n    this._textMode = false;\n    this._fontResources = {};\n    this._gsResources = {};\n    this._xResources = {};\n    this._patResources = {};\n    this._shResources = {};\n    this._opacity = 1;\n    this._matrix = [1, 0, 0, 1, 0, 0];\n    this._annotations = [];\n    this._font = null;\n    this._fontSize = null;\n    this._contextStack = [];\n    props = this.props;\n    props.Type = _(\"Page\");\n    props.ProcSet = [_(\"PDF\"), _(\"Text\"), _(\"ImageB\"), _(\"ImageC\"), _(\"ImageI\")];\n    props.Resources = new PDFDictionary({\n      Font: new PDFDictionary(this._fontResources),\n      ExtGState: new PDFDictionary(this._gsResources),\n      XObject: new PDFDictionary(this._xResources),\n      Pattern: new PDFDictionary(this._patResources),\n      Shading: new PDFDictionary(this._shResources)\n    });\n    props.Annots = this._annotations;\n  }\n  if (PDFDictionary) PDFPage.__proto__ = PDFDictionary;\n  PDFPage.prototype = Object.create(PDFDictionary && PDFDictionary.prototype);\n  PDFPage.prototype.constructor = PDFPage;\n  PDFPage.prototype._out = function _out() {\n    this._content.data.apply(null, arguments);\n  };\n  PDFPage.prototype.transform = function transform(a, b, c, d, e, f) {\n    if (!isIdentityMatrix(arguments)) {\n      this._matrix = mmul(arguments, this._matrix);\n      this._out(a, \" \", b, \" \", c, \" \", d, \" \", e, \" \", f, \" cm\");\n      // XXX: debug\n      // this._out(\" % current matrix: \", this._matrix);\n      this._out(NL);\n    }\n  };\n  PDFPage.prototype.translate = function translate(dx, dy) {\n    this.transform(1, 0, 0, 1, dx, dy);\n  };\n  PDFPage.prototype.scale = function scale(sx, sy) {\n    this.transform(sx, 0, 0, sy, 0, 0);\n  };\n  PDFPage.prototype.rotate = function rotate(angle) {\n    var cos = Math.cos(angle),\n      sin = Math.sin(angle);\n    this.transform(cos, sin, -sin, cos, 0, 0);\n  };\n  PDFPage.prototype.beginText = function beginText() {\n    this._textMode = true;\n    this._out(\"BT\", NL);\n  };\n  PDFPage.prototype.endText = function endText() {\n    this._textMode = false;\n    this._out(\"ET\", NL);\n  };\n  PDFPage.prototype._requireTextMode = function _requireTextMode() {\n    if (!this._textMode) {\n      throw new Error(\"Text mode required; call page.beginText() first\");\n    }\n  };\n  PDFPage.prototype._requireFont = function _requireFont() {\n    if (!this._font) {\n      throw new Error(\"No font selected; call page.setFont() first\");\n    }\n  };\n  PDFPage.prototype.setFont = function setFont(font, size) {\n    this._requireTextMode();\n    if (font == null) {\n      font = this._font;\n    } else if (!(font instanceof PDFFont)) {\n      font = this._pdf.getFont(font);\n    }\n    if (size == null) {\n      size = this._fontSize;\n    }\n    this._fontResources[font._resourceName] = font;\n    this._font = font;\n    this._fontSize = size;\n    this._out(font._resourceName, \" \", size, \" Tf\", NL);\n  };\n  PDFPage.prototype.setTextLeading = function setTextLeading(size) {\n    this._requireTextMode();\n    this._out(size, \" TL\", NL);\n  };\n  PDFPage.prototype.setTextRenderingMode = function setTextRenderingMode(mode) {\n    this._requireTextMode();\n    this._out(mode, \" Tr\", NL);\n  };\n  PDFPage.prototype.showText = function showText(text, requestedWidth) {\n    this._requireFont();\n    if (text.length > 1 && requestedWidth && this._font instanceof PDFFont) {\n      var outputWidth = this._font.getTextWidth(this._fontSize, text);\n      var scale = requestedWidth / outputWidth * 100;\n      this._out(scale, \" Tz \");\n    }\n    this._out(this._font.encodeText(text), \" Tj\", NL);\n  };\n  PDFPage.prototype.showTextNL = function showTextNL(text) {\n    this._requireFont();\n    this._out(this._font.encodeText(text), \" '\", NL);\n  };\n  PDFPage.prototype.addLink = function addLink(uri, box) {\n    var ll = this._toPage({\n      x: box.left,\n      y: box.bottom\n    });\n    var ur = this._toPage({\n      x: box.right,\n      y: box.top\n    });\n    this._annotations.push(new PDFDictionary({\n      Type: _(\"Annot\"),\n      Subtype: _(\"Link\"),\n      Rect: [ll.x, ll.y, ur.x, ur.y],\n      Border: [0, 0, 0],\n      A: new PDFDictionary({\n        Type: _(\"Action\"),\n        S: _(\"URI\"),\n        URI: new PDFString(uri)\n      })\n    }));\n  };\n  PDFPage.prototype.setStrokeColor = function setStrokeColor(r, g, b) {\n    this._out(r, \" \", g, \" \", b, \" RG\", NL);\n  };\n  PDFPage.prototype.setOpacity = function setOpacity(opacity) {\n    this.setFillOpacity(opacity);\n    this.setStrokeOpacity(opacity);\n    this._opacity *= opacity;\n  };\n  PDFPage.prototype.setStrokeOpacity = function setStrokeOpacity(opacity) {\n    if (opacity < 1) {\n      var gs = this._pdf.getOpacityGS(this._opacity * opacity, true);\n      this._gsResources[gs._resourceName] = gs;\n      this._out(gs._resourceName, \" gs\", NL);\n    }\n  };\n  PDFPage.prototype.setFillColor = function setFillColor(r, g, b) {\n    this._out(r, \" \", g, \" \", b, \" rg\", NL);\n  };\n  PDFPage.prototype.pattern = function pattern(fill, box, drawPattern) {\n    var pattern = this._pdf.getPattern(fill, this, drawPattern);\n    this._patResources[pattern._resourceName] = pattern;\n    this._out(\"/Pattern cs\", NL);\n    this._out(pattern._resourceName, \" scn\", NL);\n    this.rect(box.left, box.top, box.width, box.height);\n    this.fill();\n  };\n  PDFPage.prototype.setFillOpacity = function setFillOpacity(opacity) {\n    if (opacity < 1) {\n      var gs = this._pdf.getOpacityGS(this._opacity * opacity, false);\n      this._gsResources[gs._resourceName] = gs;\n      this._out(gs._resourceName, \" gs\", NL);\n    }\n  };\n  PDFPage.prototype.gradient = function gradient(gradient$1, box) {\n    this.save();\n    this.rect(box.left, box.top, box.width, box.height);\n    this.clip();\n    if (!gradient$1.userSpace) {\n      this.transform(box.width, 0, 0, box.height, box.left, box.top);\n    }\n    var g = cacheGradient(this._pdf, gradient$1, box);\n    var sname = g.shading._resourceName,\n      oname;\n    this._shResources[sname] = g.shading;\n    if (g.hasAlpha) {\n      oname = g.opacity._resourceName;\n      this._gsResources[oname] = g.opacity;\n      this._out(\"/\" + oname + \" gs \");\n    }\n    this._out(\"/\" + sname + \" sh\", NL);\n    this.restore();\n  };\n  PDFPage.prototype.setDashPattern = function setDashPattern(dashArray, dashPhase) {\n    this._out(dashArray, \" \", dashPhase, \" d\", NL);\n  };\n  PDFPage.prototype.setLineWidth = function setLineWidth(width) {\n    this._out(width, \" w\", NL);\n  };\n  PDFPage.prototype.setLineCap = function setLineCap(lineCap) {\n    this._out(lineCap, \" J\", NL);\n  };\n  PDFPage.prototype.setLineJoin = function setLineJoin(lineJoin) {\n    this._out(lineJoin, \" j\", NL);\n  };\n  PDFPage.prototype.setMitterLimit = function setMitterLimit(mitterLimit) {\n    this._out(mitterLimit, \" M\", NL);\n  };\n  PDFPage.prototype.save = function save() {\n    this._contextStack.push(this._context());\n    this._out(\"q\", NL);\n  };\n  PDFPage.prototype.restore = function restore() {\n    this._out(\"Q\", NL);\n    this._context(this._contextStack.pop());\n  };\n\n  // paths\n  PDFPage.prototype.moveTo = function moveTo(x, y) {\n    this._out(x, \" \", y, \" m\", NL);\n  };\n  PDFPage.prototype.lineTo = function lineTo(x, y) {\n    this._out(x, \" \", y, \" l\", NL);\n  };\n  PDFPage.prototype.bezier = function bezier(x1, y1, x2, y2, x3, y3) {\n    this._out(x1, \" \", y1, \" \", x2, \" \", y2, \" \", x3, \" \", y3, \" c\", NL);\n  };\n  PDFPage.prototype.bezier1 = function bezier1(x1, y1, x3, y3) {\n    this._out(x1, \" \", y1, \" \", x3, \" \", y3, \" y\", NL);\n  };\n  PDFPage.prototype.bezier2 = function bezier2(x2, y2, x3, y3) {\n    this._out(x2, \" \", y2, \" \", x3, \" \", y3, \" v\", NL);\n  };\n  PDFPage.prototype.close = function close() {\n    this._out(\"h\", NL);\n  };\n  PDFPage.prototype.rect = function rect(x, y, w, h) {\n    this._out(x, \" \", y, \" \", w, \" \", h, \" re\", NL);\n  };\n  PDFPage.prototype.ellipse = function ellipse(x, y, rx, ry) {\n    function _X(v) {\n      return x + v;\n    }\n    function _Y(v) {\n      return y + v;\n    }\n\n    // how to get to the \"magic number\" is explained here:\n    // http://www.whizkidtech.redprince.net/bezier/circle/kappa/\n    var k = 0.5522847498307936;\n    this.moveTo(_X(0), _Y(ry));\n    this.bezier(_X(rx * k), _Y(ry), _X(rx), _Y(ry * k), _X(rx), _Y(0));\n    this.bezier(_X(rx), _Y(-ry * k), _X(rx * k), _Y(-ry), _X(0), _Y(-ry));\n    this.bezier(_X(-rx * k), _Y(-ry), _X(-rx), _Y(-ry * k), _X(-rx), _Y(0));\n    this.bezier(_X(-rx), _Y(ry * k), _X(-rx * k), _Y(ry), _X(0), _Y(ry));\n  };\n  PDFPage.prototype.circle = function circle(x, y, r) {\n    this.ellipse(x, y, r, r);\n  };\n  PDFPage.prototype.stroke = function stroke() {\n    this._out(\"S\", NL);\n  };\n  PDFPage.prototype.nop = function nop() {\n    this._out(\"n\", NL);\n  };\n  PDFPage.prototype.clip = function clip() {\n    this._out(\"W n\", NL);\n  };\n  PDFPage.prototype.clipStroke = function clipStroke() {\n    this._out(\"W S\", NL);\n  };\n  PDFPage.prototype.closeStroke = function closeStroke() {\n    this._out(\"s\", NL);\n  };\n  PDFPage.prototype.fill = function fill() {\n    this._out(\"f\", NL);\n  };\n  PDFPage.prototype.fillStroke = function fillStroke() {\n    this._out(\"B\", NL);\n  };\n  PDFPage.prototype.drawImage = function drawImage(url) {\n    var img = this._pdf.getImage(url);\n    if (img) {\n      // the result can be null for a cross-domain image\n      this._xResources[img._resourceName] = img;\n      this._out(img._resourceName, \" Do\", NL);\n    }\n  };\n  PDFPage.prototype.comment = function comment(txt) {\n    var self = this;\n    txt.split(/\\r?\\n/g).forEach(function (line) {\n      self._out(\"% \", line, NL);\n    });\n  };\n\n  // internal\n  PDFPage.prototype._context = function _context(val) {\n    if (val != null) {\n      this._opacity = val.opacity;\n      this._matrix = val.matrix;\n    } else {\n      return {\n        opacity: this._opacity,\n        matrix: this._matrix\n      };\n    }\n  };\n  PDFPage.prototype._toPage = function _toPage(p) {\n    var m = this._matrix;\n    var a = m[0],\n      b = m[1],\n      c = m[2],\n      d = m[3],\n      e = m[4],\n      f = m[5];\n    return {\n      x: a * p.x + c * p.y + e,\n      y: b * p.x + d * p.y + f\n    };\n  };\n  return PDFPage;\n}(PDFDictionary);\nfunction unquote(str) {\n  return str.replace(/^\\s*(['\"])(.*)\\1\\s*$/, \"$2\");\n}\nfunction parseFontDef(fontdef) {\n  // XXX: this is very crude for now and buggy.  Proper parsing is quite involved.\n  var rx = /^\\s*((normal|italic)\\s+)?((normal|small-caps)\\s+)?((normal|bold|\\d+)\\s+)?(([0-9.]+)(px|pt))(\\/(([0-9.]+)(px|pt)|normal))?\\s+(.*?)\\s*$/i;\n  var m = rx.exec(fontdef);\n  if (!m) {\n    return {\n      fontSize: 12,\n      fontFamily: \"sans-serif\"\n    };\n  }\n  var fontSize = m[8] ? parseInt(m[8], 10) : 12;\n  return {\n    italic: m[2] && m[2].toLowerCase() == \"italic\",\n    variant: m[4],\n    bold: m[6] && /bold|700/i.test(m[6]),\n    fontSize: fontSize,\n    lineHeight: m[12] ? m[12] == \"normal\" ? fontSize : parseInt(m[12], 10) : null,\n    fontFamily: m[14].split(/\\s*,\\s*/g).map(unquote)\n  };\n}\nfunction getFontURL(style) {\n  function mkFamily(name) {\n    if (style.bold) {\n      name += \"|bold\";\n    }\n    if (style.italic) {\n      name += \"|italic\";\n    }\n    return name.toLowerCase();\n  }\n  var fontFamily = style.fontFamily;\n  var name, url;\n  if (fontFamily instanceof Array) {\n    for (var i = 0; i < fontFamily.length; ++i) {\n      name = mkFamily(fontFamily[i]);\n      url = FONT_MAPPINGS[name];\n      if (url) {\n        break;\n      }\n    }\n  } else {\n    url = FONT_MAPPINGS[fontFamily.toLowerCase()];\n  }\n  while (typeof url == \"function\") {\n    url = url();\n  }\n  if (!url) {\n    url = \"Times-Roman\";\n  }\n  return url;\n}\nvar FONT_MAPPINGS = {\n  \"serif\": \"Times-Roman\",\n  \"serif|bold\": \"Times-Bold\",\n  \"serif|italic\": \"Times-Italic\",\n  \"serif|bold|italic\": \"Times-BoldItalic\",\n  \"sans-serif\": \"Helvetica\",\n  \"sans-serif|bold\": \"Helvetica-Bold\",\n  \"sans-serif|italic\": \"Helvetica-Oblique\",\n  \"sans-serif|bold|italic\": \"Helvetica-BoldOblique\",\n  \"monospace\": \"Courier\",\n  \"monospace|bold\": \"Courier-Bold\",\n  \"monospace|italic\": \"Courier-Oblique\",\n  \"monospace|bold|italic\": \"Courier-BoldOblique\",\n  \"zapfdingbats\": \"ZapfDingbats\",\n  \"zapfdingbats|bold\": \"ZapfDingbats\",\n  \"zapfdingbats|italic\": \"ZapfDingbats\",\n  \"zapfdingbats|bold|italic\": \"ZapfDingbats\"\n};\nfunction fontAlias(alias, name) {\n  alias = alias.toLowerCase();\n  FONT_MAPPINGS[alias] = function () {\n    return FONT_MAPPINGS[name];\n  };\n  FONT_MAPPINGS[alias + \"|bold\"] = function () {\n    return FONT_MAPPINGS[name + \"|bold\"];\n  };\n  FONT_MAPPINGS[alias + \"|italic\"] = function () {\n    return FONT_MAPPINGS[name + \"|italic\"];\n  };\n  FONT_MAPPINGS[alias + \"|bold|italic\"] = function () {\n    return FONT_MAPPINGS[name + \"|bold|italic\"];\n  };\n}\n\n// Let's define some common names to an appropriate replacement.\n// These are overridable via pdf.defineFont, should the user want to\n// include the proper versions.\n\nfontAlias(\"Times New Roman\", \"serif\");\nfontAlias(\"Courier New\", \"monospace\");\nfontAlias(\"Arial\", \"sans-serif\");\nfontAlias(\"Helvetica\", \"sans-serif\");\nfontAlias(\"Verdana\", \"sans-serif\");\nfontAlias(\"Tahoma\", \"sans-serif\");\nfontAlias(\"Georgia\", \"sans-serif\");\nfontAlias(\"Monaco\", \"monospace\");\nfontAlias(\"Andale Mono\", \"monospace\");\nfunction defineFont(name, url) {\n  if (arguments.length == 1) {\n    for (var i in name) {\n      if (hasOwnProperty(name, i)) {\n        defineFont(i, name[i]);\n      }\n    }\n  } else {\n    name = name.toLowerCase();\n    FONT_MAPPINGS[name] = url;\n\n    // special handling for DejaVu fonts: if they get defined,\n    // let them also replace the default families, for good\n    // Unicode support out of the box.\n    switch (name) {\n      case \"dejavu sans\":\n        FONT_MAPPINGS[\"sans-serif\"] = url;\n        break;\n      case \"dejavu sans|bold\":\n        FONT_MAPPINGS[\"sans-serif|bold\"] = url;\n        break;\n      case \"dejavu sans|italic\":\n        FONT_MAPPINGS[\"sans-serif|italic\"] = url;\n        break;\n      case \"dejavu sans|bold|italic\":\n        FONT_MAPPINGS[\"sans-serif|bold|italic\"] = url;\n        break;\n      case \"dejavu serif\":\n        FONT_MAPPINGS[\"serif\"] = url;\n        break;\n      case \"dejavu serif|bold\":\n        FONT_MAPPINGS[\"serif|bold\"] = url;\n        break;\n      case \"dejavu serif|italic\":\n        FONT_MAPPINGS[\"serif|italic\"] = url;\n        break;\n      case \"dejavu serif|bold|italic\":\n        FONT_MAPPINGS[\"serif|bold|italic\"] = url;\n        break;\n      case \"dejavu mono\":\n        FONT_MAPPINGS[\"monospace\"] = url;\n        break;\n      case \"dejavu mono|bold\":\n        FONT_MAPPINGS[\"monospace|bold\"] = url;\n        break;\n      case \"dejavu mono|italic\":\n        FONT_MAPPINGS[\"monospace|italic\"] = url;\n        break;\n      case \"dejavu mono|bold|italic\":\n        FONT_MAPPINGS[\"monospace|bold|italic\"] = url;\n        break;\n    }\n  }\n}\nfunction mmul(a, b) {\n  var a1 = a[0],\n    b1 = a[1],\n    c1 = a[2],\n    d1 = a[3],\n    e1 = a[4],\n    f1 = a[5];\n  var a2 = b[0],\n    b2 = b[1],\n    c2 = b[2],\n    d2 = b[3],\n    e2 = b[4],\n    f2 = b[5];\n  return [a1 * a2 + b1 * c2, a1 * b2 + b1 * d2, c1 * a2 + d1 * c2, c1 * b2 + d1 * d2, e1 * a2 + f1 * c2 + e2, e1 * b2 + f1 * d2 + f2];\n}\nfunction isIdentityMatrix(m) {\n  return m[0] === 1 && m[1] === 0 && m[2] === 0 && m[3] === 1 && m[4] === 0 && m[5] === 0;\n}\nvar TEXT_RENDERING_MODE = {\n  fill: 0,\n  stroke: 1,\n  fillAndStroke: 2,\n  invisible: 3,\n  fillAndClip: 4,\n  strokeAndClip: 5,\n  fillStrokeClip: 6,\n  clip: 7\n};\nexport { PDFDocument as Document, BinaryStream, defineFont, parseFontDef, getFontURL, loadFonts, loadImages, getPaperOptions, clearImageCache, TEXT_RENDERING_MODE };", "map": {"version": 3, "names": ["BinaryStream", "ucs2encode", "base64ToUint8Array", "HAS_TYPED_ARRAYS", "support", "TTFFont", "deflate", "supportsDeflate", "encodeUTF16BE", "BOM", "encodeBase64", "browser", "NL", "RESOURCE_COUNTER", "PATTERN_COUNTER", "PAPER_SIZE", "a0", "a1", "a2", "a3", "a4", "a5", "a6", "a7", "a8", "a9", "a10", "b0", "b1", "b2", "b3", "b4", "b5", "b6", "b7", "b8", "b9", "b10", "c0", "c1", "c2", "c3", "c4", "c5", "c6", "c7", "c8", "c9", "c10", "executive", "folio", "legal", "letter", "tabloid", "makeOutput", "indentLevel", "output", "out", "arguments$1", "arguments", "i", "length", "x", "undefined", "Error", "PDFValue", "beforeRender", "render", "isArray", "renderArray", "isDate", "renderDate", "isNaN", "num", "toFixed", "indexOf", "replace", "writeString", "test", "String", "get", "write", "PDFDictionary", "writeData", "data", "with<PERSON>ndent", "f", "indent", "pad", "apply", "offset", "toString", "stream", "wrapObject", "value", "id", "renderValue", "renderFull", "_offset", "call", "getPaperOptions", "getOption", "options", "key", "def", "paperSize", "toLowerCase", "unitsToPoints", "Math", "max", "min", "margin", "left", "top", "right", "bottom", "FONT_CACHE", "loadBinary", "url", "cont", "m", "msie", "exec", "substr", "error", "window", "console", "log", "req", "XMLHttpRequest", "open", "responseType", "onload", "status", "Uint8Array", "response", "VBArray", "responseBody", "toArray", "onerror", "send", "loadFont", "font", "IMAGE_CACHE", "clearImageCache", "loadImage", "size", "img", "bloburl", "blob", "Image", "crossOrigin", "xhr", "mozilla", "type", "reader", "FileReader", "doc", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "result", "svg", "documentElement", "getAttribute", "URL", "createObjectURL", "_load", "setAttribute", "width", "height", "xml", "XMLSerializer", "serializeToString", "dataURL", "readAsText", "_onerror", "src", "complete", "_onload", "_trycanvas", "canvas", "document", "createElement", "ctx", "getContext", "drawImage", "imgdata", "getImageData", "ex", "revokeObjectURL", "has<PERSON><PERSON><PERSON>", "rgb", "alpha", "rawbytes", "writeByte", "a", "keepPNG", "PDFRawImage", "toDataURL", "jpegQuality", "writeBase64", "PDFJpegImage", "substring", "upscale", "readAsA<PERSON>y<PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "loadOne", "urls", "callback", "n", "next", "loadFonts", "loadImages", "images", "Object", "assign", "keys", "for<PERSON>ach", "PDFDocument", "self", "objcount", "objects", "name", "defval", "attach", "push", "pages", "FONTS", "PATTERNS", "IMAGES", "GRAD_COL_FUNCTIONS", "GRAD_OPC_FUNCTIONS", "GRAD_COL", "GRAD_OPC", "catalog", "PDFCatalog", "pageTree", "PDFPageTree", "nameTree", "JavaScript", "Names", "PDFString", "S", "_", "JS", "props", "setPages", "info", "Producer", "Title", "Author", "Subject", "Keywords", "Creator", "CreationDate", "Date", "addPage", "paperOptions", "contentWidth", "contentHeight", "content", "PDFStream", "Contents", "Parent", "MediaBox", "page", "PDFPage", "_content", "transform", "translate", "rect", "clip", "xrefOffset", "zeropad", "Size", "Root", "Info", "prototype", "getFont", "PDFStandardFont", "PDFFont", "getPattern", "fill", "drawPattern", "pattern", "PDFPattern", "getImage", "asStream", "getOpacityGS", "opacity", "forStroke", "parseFloat", "cache", "_opacityGSCache", "gs", "Type", "CA", "ca", "_resourceName", "dict", "str", "len", "ch", "hasOwnProperty", "obj", "Array", "date", "getUTCFullYear", "getUTCMonth", "getUTCDate", "getUTCHours", "getUTCMinutes", "getUTCSeconds", "mm2pt", "mm", "cm2pt", "cm", "in2pt", "inch", "utf16be", "Boolean", "__proto__", "create", "constructor", "txt", "code", "charCodeAt", "PDFHexString", "this$1", "PDFName", "escape", "c", "empty", "compress", "tmp", "Filter", "unshift", "Length", "pagesObj", "Pages", "Kids", "Count", "pageObj", "SOF_CODES", "colorSpace", "bitsPerComponent", "soi", "readShort", "eof", "ff", "readByte", "marker", "skip", "Subtype", "<PERSON><PERSON><PERSON>", "Height", "BitsPerComponent", "ColorSpace", "Decode", "pdf", "mask", "SMask", "curPage", "ref", "_pdf", "_xResources", "_fontResources", "_gsResources", "PatternType", "PaintType", "TilingType", "BBox", "XStep", "YStep", "Matrix", "Resources", "ExtGState", "XObject", "Font", "BaseFont", "encodeText", "Encoding", "_font", "_sub", "makeSubset", "head", "psName", "scale", "bbox", "xMin", "yMin", "xMax", "yMax", "italicAngle", "post", "ascent", "descent", "lineGap", "capHeight", "os2", "xHeight", "stemV", "familyClass", "<PERSON><PERSON><PERSON><PERSON>", "isScript", "flags", "isFixedPitch", "text", "getTextWidth", "fontSize", "codeMap", "cmap", "glyphId", "widthOfGlyph", "sub", "fontStream", "Length1", "descriptor", "FontName", "FontBBox", "Flags", "StemV", "ItalicAngle", "Ascent", "Descent", "CapHeight", "XHeight", "FontFile2", "ncid2ogid", "firstChar", "lastChar", "char<PERSON><PERSON><PERSON>", "loop", "chunk", "gid", "descendant", "CIDSystemInfo", "Registry", "Ordering", "Supplement", "FontDescriptor", "FirstChar", "LastChar", "DW", "round", "W", "CIDToGIDMap", "_makeCidToGidMap", "DescendantFonts", "unimap", "PDFToUnicodeCmap", "subset", "unimapStream", "ToUnicode", "cidToGidMap", "map", "unicode", "makeHash", "join", "cacheColorGradientFunction", "r1", "g1", "r2", "g2", "hash", "func", "FunctionType", "Domain", "Range", "N", "C0", "C1", "cacheOpacityGradientFunction", "makeGradientFunctions", "stops", "opacities", "colors", "offsets", "encode", "prev", "cur", "prevColor", "curColor", "color", "r", "g", "b", "pop", "assemble", "funcs", "Functions", "Bounds", "Encode", "cacheColorGradient", "isRadial", "coords", "box", "shading", "concat", "ShadingType", "<PERSON><PERSON><PERSON>", "Function", "Extend", "cacheOpacityGradient", "AIS", "G", "FormType", "Group", "CS", "I", "Shading", "s0", "cacheGradient", "gradient", "start", "y", "end", "userSpace", "_rcount", "_textMode", "_patResources", "_shResources", "_opacity", "_matrix", "_annotations", "_fontSize", "_contextStack", "ProcSet", "Pattern", "Ann<PERSON>", "_out", "d", "e", "isIdentityMatrix", "mmul", "dx", "dy", "sx", "sy", "rotate", "angle", "cos", "sin", "beginText", "endText", "_requireTextMode", "_requireFont", "setFont", "setTextLeading", "setTextRenderingMode", "mode", "showText", "<PERSON><PERSON><PERSON><PERSON>", "outputWidth", "showTextNL", "addLink", "uri", "ll", "_toPage", "ur", "Rect", "Border", "A", "URI", "setStrokeColor", "setOpacity", "setFillOpacity", "setStrokeOpacity", "setFillColor", "gradient$1", "save", "sname", "oname", "restore", "setDashPattern", "dashArray", "dashPhase", "setLineWidth", "setLineCap", "lineCap", "setLineJoin", "lineJoin", "setMitterLimit", "mitterLimit", "_context", "moveTo", "lineTo", "bezier", "x1", "y1", "x2", "y2", "x3", "y3", "bezier1", "bezier2", "close", "w", "h", "ellipse", "rx", "ry", "_X", "v", "_Y", "k", "circle", "stroke", "nop", "clipStroke", "closeStroke", "fillStroke", "comment", "split", "line", "val", "matrix", "p", "unquote", "parseFontDef", "fontdef", "fontFamily", "parseInt", "italic", "variant", "bold", "lineHeight", "getFontURL", "style", "mkFamily", "FONT_MAPPINGS", "fontAlias", "alias", "defineFont", "d1", "e1", "f1", "d2", "e2", "f2", "TEXT_RENDERING_MODE", "fillAndStroke", "invisible", "fillAndClip", "strokeAndClip", "fillStrokeClip", "Document"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/pdf/core.js"], "sourcesContent": ["/* eslint-disable no-multi-spaces, key-spacing, indent, camelcase, space-before-blocks, eqeqeq, brace-style */\n/* eslint-disable space-infix-ops, space-before-function-paren, array-bracket-spacing, object-curly-spacing */\n/* eslint-disable no-nested-ternary, max-params, default-case, no-else-return, no-empty */\n/* eslint-disable no-param-reassign, no-var, block-scoped-var */\n\nimport { BinaryStream, ucs2encode, base64ToUint8Array, HAS_TYPED_ARRAYS } from \"./utils\";\nimport { support } from '../common';\nimport { TTFFont } from \"./ttf\";\nimport { deflate, supportsDeflate } from './deflate';\nimport { encodeUTF16BE, BOM } from \"../util/encode-utf\";\nimport { encodeBase64 } from \"../util\";\n\nvar browser = support.browser;\nvar NL = \"\\n\";\n\nvar RESOURCE_COUNTER = 0;\nvar PATTERN_COUNTER = 0;\n\nvar PAPER_SIZE = {\n    a0        : [ 2383.94 , 3370.39 ],\n    a1        : [ 1683.78 , 2383.94 ],\n    a2        : [ 1190.55 , 1683.78 ],\n    a3        : [ 841.89  , 1190.55 ],\n    a4        : [ 595.28  , 841.89  ],\n    a5        : [ 419.53  , 595.28  ],\n    a6        : [ 297.64  , 419.53  ],\n    a7        : [ 209.76  , 297.64  ],\n    a8        : [ 147.40  , 209.76  ],\n    a9        : [ 104.88  , 147.40  ],\n    a10       : [ 73.70   , 104.88  ],\n    b0        : [ 2834.65 , 4008.19 ],\n    b1        : [ 2004.09 , 2834.65 ],\n    b2        : [ 1417.32 , 2004.09 ],\n    b3        : [ 1000.63 , 1417.32 ],\n    b4        : [ 708.66  , 1000.63 ],\n    b5        : [ 498.90  , 708.66  ],\n    b6        : [ 354.33  , 498.90  ],\n    b7        : [ 249.45  , 354.33  ],\n    b8        : [ 175.75  , 249.45  ],\n    b9        : [ 124.72  , 175.75  ],\n    b10       : [ 87.87   , 124.72  ],\n    c0        : [ 2599.37 , 3676.54 ],\n    c1        : [ 1836.85 , 2599.37 ],\n    c2        : [ 1298.27 , 1836.85 ],\n    c3        : [ 918.43  , 1298.27 ],\n    c4        : [ 649.13  , 918.43  ],\n    c5        : [ 459.21  , 649.13  ],\n    c6        : [ 323.15  , 459.21  ],\n    c7        : [ 229.61  , 323.15  ],\n    c8        : [ 161.57  , 229.61  ],\n    c9        : [ 113.39  , 161.57  ],\n    c10       : [ 79.37   , 113.39  ],\n    executive : [ 521.86  , 756.00  ],\n    folio     : [ 612.00  , 936.00  ],\n    legal     : [ 612.00  , 1008.00 ],\n    letter    : [ 612.00  , 792.00  ],\n    tabloid   : [ 792.00  , 1224.00 ]\n};\n\nfunction makeOutput() {\n    var indentLevel = 0, output = BinaryStream();\n    function out() {\n        var arguments$1 = arguments;\n\n        for (var i = 0; i < arguments.length; ++i) {\n            var x = arguments$1[i];\n            if (x === undefined) {\n                throw new Error(\"Cannot output undefined to PDF\");\n            }\n            else if (x instanceof PDFValue) {\n                x.beforeRender(out);\n                x.render(out);\n            }\n            else if (isArray(x)) {\n                renderArray(x, out);\n            }\n            else if (isDate(x)) {\n                renderDate(x, out);\n            }\n            else if (typeof x == \"number\") {\n                if (isNaN(x)) {\n                    throw new Error(\"Cannot output NaN to PDF\");\n                }\n                // make sure it doesn't end up in exponent notation\n                var num = x.toFixed(7);\n                if (num.indexOf(\".\") >= 0) {\n                    num = num.replace(/\\.?0+$/, \"\");\n                }\n                if (num == \"-0\") {\n                    num = \"0\";\n                }\n                output.writeString(num);\n            }\n            else if (/string|boolean/.test(typeof x)) {\n                output.writeString(String(x));\n            }\n            else if (typeof x.get == \"function\") {\n                output.write(x.get());\n            }\n            else if (typeof x == \"object\") {\n                if (!x) {\n                    output.writeString(\"null\");\n                } else {\n                    out(new PDFDictionary(x));\n                }\n            }\n        }\n    }\n    out.writeData = function(data) {\n        output.write(data);\n    };\n    out.withIndent = function(f) {\n        ++indentLevel;\n        f(out);\n        --indentLevel;\n    };\n    out.indent = function() {\n        out(NL, pad(\"\", indentLevel * 2, \"  \"));\n        out.apply(null, arguments);\n    };\n    out.offset = function() {\n        return output.offset();\n    };\n    out.toString = function() {\n        throw new Error(\"FIX CALLER\");\n    };\n    out.get = function() {\n        return output.get();\n    };\n    out.stream = function() {\n        return output;\n    };\n    return out;\n}\n\nfunction wrapObject(value, id) {\n    var beforeRender = value.beforeRender;\n    var renderValue = value.render;\n\n    value.beforeRender = function(){};\n\n    value.render = function(out) {\n        out(id, \" 0 R\");\n    };\n\n    value.renderFull = function(out) {\n        value._offset = out.offset();\n        out(id, \" 0 obj \");\n        beforeRender.call(value, out);\n        renderValue.call(value, out);\n        out(\" endobj\");\n    };\n}\n\nfunction getPaperOptions(getOption) {\n    if (typeof getOption != \"function\") {\n        var options = getOption;\n        getOption = function(key, def) {\n            return key in options ? options[key] : def;\n        };\n    }\n    var paperSize = getOption(\"paperSize\", PAPER_SIZE.a4);\n    if (!paperSize) {\n        return {};\n    }\n    if (typeof paperSize == \"string\") {\n        paperSize = PAPER_SIZE[paperSize.toLowerCase()];\n        if (paperSize == null) {\n            throw new Error(\"Unknown paper size\");\n        }\n    }\n\n    paperSize[0] = unitsToPoints(paperSize[0]);\n    paperSize[1] = unitsToPoints(paperSize[1]);\n\n    if (getOption(\"landscape\", false)) {\n        paperSize = [\n            Math.max(paperSize[0], paperSize[1]),\n            Math.min(paperSize[0], paperSize[1])\n        ];\n    }\n\n    var margin = getOption(\"margin\");\n    if (margin) {\n        if (typeof margin == \"string\" || typeof margin == \"number\") {\n            margin = unitsToPoints(margin, 0);\n            margin = { left: margin, top: margin, right: margin, bottom: margin };\n        } else {\n            margin = {\n                left   : unitsToPoints(margin.left, 0),\n                top    : unitsToPoints(margin.top, 0),\n                right  : unitsToPoints(margin.right, 0),\n                bottom : unitsToPoints(margin.bottom, 0)\n            };\n        }\n        if (getOption(\"addMargin\")) {\n            paperSize[0] += margin.left + margin.right;\n            paperSize[1] += margin.top + margin.bottom;\n        }\n    }\n    return { paperSize: paperSize, margin: margin };\n}\n\nvar FONT_CACHE = {\n    \"Times-Roman\"           : true,\n    \"Times-Bold\"            : true,\n    \"Times-Italic\"          : true,\n    \"Times-BoldItalic\"      : true,\n    \"Helvetica\"             : true,\n    \"Helvetica-Bold\"        : true,\n    \"Helvetica-Oblique\"     : true,\n    \"Helvetica-BoldOblique\" : true,\n    \"Courier\"               : true,\n    \"Courier-Bold\"          : true,\n    \"Courier-Oblique\"       : true,\n    \"Courier-BoldOblique\"   : true,\n    \"Symbol\"                : true,\n    \"ZapfDingbats\"          : true\n};\n\nfunction loadBinary(url, cont) {\n    // IE throws Accesss denied error for Data URIs\n    var m;\n    if (browser.msie && (m = /^data:.*?;base64,/i.exec(url))) {\n        cont(base64ToUint8Array(url.substr(m[0].length)));\n        return;\n    }\n\n    function error() {\n        if (window.console) {\n            if (window.console.error) {\n                window.console.error(\"Cannot load URL: %s\", url);\n            } else {\n                window.console.log(\"Cannot load URL: %s\", url);\n            }\n        }\n        cont(null);\n    }\n    var req = new XMLHttpRequest();\n    req.open('GET', url, true);\n    if (HAS_TYPED_ARRAYS) {\n        req.responseType = \"arraybuffer\";\n    }\n    req.onload = function() {\n        if (req.status == 200 || req.status == 304) {\n            if (HAS_TYPED_ARRAYS) {\n                cont(new Uint8Array(req.response));\n            } else {\n                cont(new window.VBArray(req.responseBody).toArray()); // IE9 only\n            }\n        } else {\n            error();\n        }\n    };\n    req.onerror = error;\n    req.send(null);\n}\n\nfunction loadFont(url, cont) {\n    var font = FONT_CACHE[url];\n    if (font) {\n        cont(font);\n    } else {\n        loadBinary(url, function(data){\n            if (data == null) {\n                throw new Error(\"Cannot load font from \" + url);\n            } else {\n                var font = new TTFFont(data);\n                FONT_CACHE[url] = font;\n                cont(font);\n            }\n        });\n    }\n}\n\nvar IMAGE_CACHE = {};\n\nfunction clearImageCache() {\n    IMAGE_CACHE = {};\n}\n\nfunction loadImage(url, size, cont, options) {\n    var img = IMAGE_CACHE[url], bloburl, blob;\n    if (img) {\n        cont(img);\n    } else {\n        img = new Image();\n        if (!(/^data:/i.test(url))) {\n            img.crossOrigin = \"Anonymous\";\n        }\n        if (HAS_TYPED_ARRAYS && !(/^data:/i.test(url))) {\n            // IE10 fails to load images from another domain even when the server sends the\n            // proper CORS headers.  a XHR, however, will be able to load the data.\n            // http://stackoverflow.com/a/19734516/154985\n            //\n            // On the other hand, it's worth doing it this way for all browsers which support\n            // responseType = \"blob\" (HAS_TYPED_ARRAYS will be true), because we can inspect the\n            // mime type and if it's a JPEG (very common case) we can save a lot of time in\n            // _load below.\n            var xhr = new XMLHttpRequest();\n            xhr.onload = function() {\n                blob = xhr.response;\n                if (browser.mozilla && blob.type == \"image/svg+xml\") {\n                    // Firefox won't render SVGs that don't contain width and height attributes.\n                    var reader = new FileReader();\n                    reader.onload = function() {\n                        var doc = new window.DOMParser().parseFromString(this.result, \"image/svg+xml\");\n                        var svg = doc.documentElement;\n                        if (svg.getAttribute(\"width\") && svg.getAttribute(\"height\")) {\n                            // we're good, continue with the existing blob.\n                            bloburl = URL.createObjectURL(blob);\n                            _load(bloburl);\n                        } else {\n                            svg.setAttribute(\"width\", size.width);\n                            svg.setAttribute(\"height\", size.height);\n                            var xml = new window.XMLSerializer().serializeToString(svg);\n                            var dataURL = \"data:image/svg+xml;base64,\" + (encodeBase64(xml));\n                            _load(dataURL);\n                        }\n                    };\n                    reader.readAsText(blob);\n                } else {\n                    bloburl = URL.createObjectURL(blob);\n                    _load(bloburl);\n                }\n            };\n            xhr.onerror = _onerror;\n            xhr.open(\"GET\", url, true);\n            xhr.responseType = \"blob\";\n            xhr.send();\n        } else {\n            _load(url);\n        }\n    }\n\n    function _load(url) {\n        img.src = url;\n        if (img.complete && !browser.msie) {\n            // IE, bless its little heart, says img.complete == true even though the image is\n            // not loaded (width=0), therefore we must go the onload route (ticket 929635).\n            _onload.call(img);\n        } else {\n            img.onload = _onload;\n            img.onerror = _onerror;\n        }\n    }\n\n    function _trycanvas() {\n        if (!size) {\n            size = { width: img.width, height: img.height };\n        }\n\n        var canvas = document.createElement(\"canvas\");\n        canvas.width = size.width;\n        canvas.height = size.height;\n\n        var ctx = canvas.getContext(\"2d\");\n        ctx.drawImage(img, 0, 0, size.width, size.height);\n\n        var imgdata;\n        try {\n            imgdata = ctx.getImageData(0, 0, size.width, size.height);\n        } catch (ex) {\n            // it tainted the canvas -- can't draw it.\n            _onerror();\n            return;\n        } finally {\n            if (bloburl) {\n                URL.revokeObjectURL(bloburl);\n            }\n        }\n\n        // in case it contains transparency, we must separate rgb data from the alpha\n        // channel and create a PDFRawImage image with opacity.  otherwise we can use a\n        // PDFJpegImage.\n        //\n        // to do this in one step, we create the rgb and alpha streams anyway, even if\n        // we might end up not using them if hasAlpha remains false.\n\n        var hasAlpha = false, rgb = BinaryStream(), alpha = BinaryStream();\n        var rawbytes = imgdata.data;\n        var i = 0;\n        while (i < rawbytes.length) {\n            rgb.writeByte(rawbytes[i++]);\n            rgb.writeByte(rawbytes[i++]);\n            rgb.writeByte(rawbytes[i++]);\n            var a = rawbytes[i++];\n            if (a < 255) {\n                hasAlpha = true;\n            }\n            alpha.writeByte(a);\n        }\n\n        if (hasAlpha || options.keepPNG) {\n            img = new PDFRawImage(size.width, size.height, rgb, alpha);\n        } else {\n            // no transparency, encode as JPEG.\n            var data = canvas.toDataURL(\"image/jpeg\", options.jpegQuality);\n            data = data.substr(data.indexOf(\";base64,\") + 8);\n\n            var stream = BinaryStream();\n            stream.writeBase64(data);\n            img = new PDFJpegImage(stream);\n        }\n\n        cont(IMAGE_CACHE[url] = img);\n    }\n\n    function _onerror() {\n        cont(IMAGE_CACHE[url] = \"ERROR\");\n    }\n\n    function _onload() {\n        if (size) {\n            var svg = (blob && blob.type === 'image/svg+xml') || (\n              /^data:image\\/svg\\+xml;/i.test(this.src.substring(0, 19))\n            );\n\n            var upscale = size.width >= img.width || size.height >= img.height;\n\n            // Use the original image if requested size is bigger than the source,\n            // unless it's an SVG that can be upscaled.\n            if (!svg && upscale) {\n                size = null;\n            }\n        }\n        if (!size && blob && /^image\\/jpe?g$/i.test(blob.type)) {\n            // If we know we got a JPEG, we can skip the process of rendering it to a\n            // canvas, getting the pixel data, searching for transparency we know we won't\n            // find, getting back a data URI and then decoding the BASE64 to finally get the\n            // binary we already have.  Also, we avoid downgrading the image quality, with\n            // the possible drawback of making a bigger PDF; still, seems legit.\n            //\n            // Besides saving a lot of work, this also reuses the buffer memory\n            // (BinaryStream does not create a copy), potentially saving some GC cycles.\n            var reader = new FileReader();\n            reader.onload = function() {\n                try {\n                    var img = new PDFJpegImage(BinaryStream(new Uint8Array(this.result)));\n                    URL.revokeObjectURL(bloburl);\n                    cont(IMAGE_CACHE[url] = img);\n                } catch (ex) {\n                    // if there's an error parsing the JPEG stream, it could be due to a\n                    // misconfigured server (improper content-type:\n                    // https://github.com/telerik/kendo-ui-core/issues/4184).  If that's the case,\n                    // the canvas will still be able to draw it.\n                    _trycanvas();\n                }\n            };\n            reader.readAsArrayBuffer(blob);\n        } else {\n            _trycanvas();\n        }\n    }\n}\n\nfunction manyLoader(loadOne) {\n    return function(urls, callback) {\n        var n = urls.length, i = n;\n        if (n === 0) {\n            return callback();\n        }\n        function next() {\n            if (--n === 0) {\n                callback();\n            }\n        }\n        while (i-- > 0) {\n            loadOne(urls[i], next);\n        }\n    };\n}\n\nvar loadFonts = manyLoader(loadFont);\nvar loadImages = function(images, callback, options) {\n    options = Object.assign({\n        jpegQuality : 0.92,\n        keepPNG     : false\n    }, options);\n    var urls = Object.keys(images), n = urls.length;\n    if (n === 0) {\n        return callback();\n    }\n    function next() {\n        if (--n === 0) {\n            callback();\n        }\n    }\n    urls.forEach(function(url){\n        loadImage(url, images[url], next, options);\n    });\n};\n\nvar PDFDocument = function PDFDocument (options) {\n    var self = this;\n    var out = makeOutput();\n    var objcount = 0;\n    var objects = [];\n\n    function getOption(name, defval) {\n        return (options && options[name] != null) ? options[name] : defval;\n    }\n\n    self.getOption = getOption;\n\n    self.attach = function(value) {\n        if (objects.indexOf(value) < 0) {\n            wrapObject(value, ++objcount);\n            objects.push(value);\n        }\n        return value;\n    };\n\n    self.pages = [];\n\n    self.FONTS = {};\n    self.PATTERNS = {};\n    self.IMAGES = {};\n    self.GRAD_COL_FUNCTIONS = {}; // cache for color gradient functions\n    self.GRAD_OPC_FUNCTIONS = {}; // cache for opacity gradient functions\n    self.GRAD_COL = {}; // cache for whole color gradient objects\n    self.GRAD_OPC = {}; // cache for whole opacity gradient objects\n\n    var catalog = self.attach(new PDFCatalog());\n    var pageTree = self.attach(new PDFPageTree());\n\n    if (getOption(\"autoPrint\")) {\n        var nameTree = {};\n        nameTree.JavaScript = new PDFDictionary({ Names: [\n            new PDFString(\"JS\"), self.attach(new PDFDictionary({\n                S: _(\"JavaScript\"),\n                JS: new PDFString(\"print(true);\")\n            }))\n        ] });\n        catalog.props.Names = new PDFDictionary(nameTree);\n    }\n\n    catalog.setPages(pageTree);\n\n    var info = self.attach(new PDFDictionary({\n        Producer : new PDFString(getOption(\"producer\", \"Kendo UI PDF Generator\"), true), // XXX: kendo.version?\n        Title    : new PDFString(getOption(\"title\", \"\"), true),\n        Author   : new PDFString(getOption(\"author\", \"\"), true),\n        Subject  : new PDFString(getOption(\"subject\", \"\"), true),\n        Keywords : new PDFString(getOption(\"keywords\", \"\"), true),\n        Creator  : new PDFString(getOption(\"creator\", \"Kendo UI PDF Generator\"), true),\n        CreationDate : getOption(\"date\", new Date())\n    }));\n\n    self.addPage = function(options) {\n        var paperOptions  = getPaperOptions(function(name, defval){\n            return (options && options[name] != null) ? options[name] : defval;\n        });\n        var paperSize = paperOptions.paperSize;\n        var margin    = paperOptions.margin;\n        var contentWidth  = paperSize[0];\n        var contentHeight = paperSize[1];\n        if (margin) {\n            contentWidth -= margin.left + margin.right;\n            contentHeight -= margin.top + margin.bottom;\n        }\n        var content = new PDFStream(makeOutput(), null, true);\n        var props = {\n            Contents : self.attach(content),\n            Parent   : pageTree,\n            MediaBox : [ 0, 0, paperSize[0], paperSize[1] ]\n        };\n        var page = new PDFPage(self, props);\n        page._content = content;\n        pageTree.addPage(self.attach(page));\n\n        // canvas-like coord. system.  (0,0) is upper-left.\n        // text must be vertically mirorred before drawing.\n        page.transform(1, 0, 0, -1, 0, paperSize[1]);\n\n        if (margin) {\n            page.translate(margin.left, margin.top);\n            // XXX: clip to right/bottom margin.  Make this optional?\n            page.rect(0, 0, contentWidth, contentHeight);\n            page.clip();\n        }\n\n        self.pages.push(page);\n        return page;\n    };\n\n    self.render = function() {\n        var i;\n        /// file header\n        out(\"%PDF-1.4\", NL, \"%\\xc2\\xc1\\xda\\xcf\\xce\", NL, NL);\n\n        /// file body\n        for (i = 0; i < objects.length; ++i) {\n            objects[i].renderFull(out);\n            out(NL, NL);\n        }\n\n        /// cross-reference table\n        var xrefOffset = out.offset();\n        out(\"xref\", NL, 0, \" \", objects.length + 1, NL);\n        out(\"0000000000 65535 f \", NL);\n        for (i = 0; i < objects.length; ++i) {\n            out(zeropad(objects[i]._offset, 10), \" 00000 n \", NL);\n        }\n        out(NL);\n\n        /// trailer\n        out(\"trailer\", NL);\n        out(new PDFDictionary({\n            Size: objects.length + 1,\n            Root: catalog,\n            Info: info\n        }), NL, NL);\n\n        /// end\n        out(\"startxref\", NL, xrefOffset, NL);\n        out(\"%%EOF\", NL);\n\n        return out.stream().offset(0);\n    };\n\n    self.loadFonts = loadFonts;\n    self.loadImages = loadImages;\n};\n\nPDFDocument.prototype.getFont = function getFont (url) {\n    var font = this.FONTS[url];\n    if (!font) {\n        font = FONT_CACHE[url];\n        if (!font) {\n            throw new Error(\"Font \" + url + \" has not been loaded\");\n        }\n        if (font === true) {\n            font = this.attach(new PDFStandardFont(url));\n        } else {\n            font = this.attach(new PDFFont(this, font));\n        }\n        this.FONTS[url] = font;\n    }\n    return font;\n};\n\nPDFDocument.prototype.getPattern = function getPattern (fill, page, drawPattern) {\n    var pattern = this.PATTERNS[fill.id];\n    if (!pattern) {\n        pattern = this.attach(new PDFPattern(fill, page, drawPattern));\n        this.PATTERNS[fill.id] = pattern;\n    }\n    return pattern;\n};\n\nPDFDocument.prototype.getImage = function getImage (url) {\n    var img = this.IMAGES[url];\n    if (!img) {\n        img = IMAGE_CACHE[url];\n        if (!img) {\n            throw new Error(\"Image \" + url + \" has not been loaded\");\n        }\n        if (img === \"ERROR\") {\n            return null;\n        }\n        img = this.IMAGES[url] = this.attach(img.asStream(this));\n    }\n    return img;\n};\n\nPDFDocument.prototype.getOpacityGS = function getOpacityGS (opacity, forStroke) {\n    var id = parseFloat(opacity).toFixed(3);\n    opacity = parseFloat(id);\n    id += forStroke ? \"S\" : \"F\";\n    var cache = this._opacityGSCache || (this._opacityGSCache = {});\n    var gs = cache[id];\n    if (!gs) {\n        var props = {\n            Type: _(\"ExtGState\")\n        };\n        if (forStroke) {\n            props.CA = opacity;\n        } else {\n            props.ca = opacity;\n        }\n        gs = this.attach(new PDFDictionary(props));\n        gs._resourceName = _(\"GS\" + (++RESOURCE_COUNTER));\n        cache[id] = gs;\n    }\n    return gs;\n};\n\nPDFDocument.prototype.dict = function dict (props) {\n    return new PDFDictionary(props);\n};\n\nPDFDocument.prototype.name = function name (str) {\n    return _(str);\n};\n\nPDFDocument.prototype.stream = function stream (props, content) {\n    return new PDFStream(content, props);\n};\n\n/* -----[ utils ]----- */\n\nfunction pad(str, len, ch) {\n    while (str.length < len) {\n        str = ch + str;\n    }\n    return str;\n}\n\nfunction zeropad(n, len) {\n    return pad(String(n), len, \"0\");\n}\n\nfunction hasOwnProperty(obj, key) {\n    return Object.prototype.hasOwnProperty.call(obj, key);\n}\n\nvar isArray = Array.isArray || function(obj) {\n    return obj instanceof Array;\n};\n\nfunction isDate(obj) {\n    return obj instanceof Date;\n}\n\nfunction renderArray(a, out) {\n    out(\"[\");\n    if (a.length > 0) {\n        out.withIndent(function(){\n            for (var i = 0; i < a.length; ++i) {\n                if (i > 0 && i % 8 === 0) {\n                    out.indent(a[i]);\n                } else {\n                    out(\" \", a[i]);\n                }\n            }\n        });\n        //out.indent();\n    }\n    out(\" ]\");\n}\n\nfunction renderDate(date, out) {\n    out(\"(D:\",\n        zeropad(date.getUTCFullYear(), 4),\n        zeropad(date.getUTCMonth() + 1, 2),\n        zeropad(date.getUTCDate(), 2),\n        zeropad(date.getUTCHours(), 2),\n        zeropad(date.getUTCMinutes(), 2),\n        zeropad(date.getUTCSeconds(), 2),\n        \"Z)\");\n}\n\nfunction mm2pt(mm) {\n    return mm * (72/25.4);\n}\n\nfunction cm2pt(cm) {\n    return mm2pt(cm * 10);\n}\n\nfunction in2pt(inch)  {\n    return inch * 72;\n}\n\n\nfunction unitsToPoints(x, def) {\n    if (typeof x == \"number\") {\n        return x;\n    }\n    if (typeof x == \"string\") {\n        var m;\n        m = /^\\s*([0-9.]+)\\s*(mm|cm|in|pt)\\s*$/.exec(x);\n        if (m) {\n            var num = parseFloat(m[1]);\n            if (!isNaN(num)) {\n                if (m[2] == \"pt\") {\n                    return num;\n                }\n                return {\n                    \"mm\": mm2pt,\n                    \"cm\": cm2pt,\n                    \"in\": in2pt\n                }[m[2]](num);\n            }\n        }\n    }\n    if (def != null) {\n        return def;\n    }\n    throw new Error(\"Can't parse unit: \" + x);\n}\n\n/* -----[ PDF basic objects ]----- */\n\nvar PDFValue = function PDFValue () {};\n\nPDFValue.prototype.beforeRender = function beforeRender () {};\n\nvar PDFString = (function (PDFValue) {\n    function PDFString(value, utf16be) {\n        PDFValue.call(this);\n        this.value = value;\n        this.utf16be = Boolean(utf16be);\n    }\n\n    if ( PDFValue ) PDFString.__proto__ = PDFValue;\n    PDFString.prototype = Object.create( PDFValue && PDFValue.prototype );\n    PDFString.prototype.constructor = PDFString;\n\n    PDFString.prototype.render = function render (out) {\n        var txt = this.value;\n        if (this.utf16be) {\n            txt = BOM + encodeUTF16BE(txt);\n            txt = txt.replace(/([\\(\\)\\\\])/g, \"\\\\$1\");\n            out(\"(\", txt, \")\");\n        } else {\n            // out.writeString truncates charcodes to 8 bits and\n            // 0x128 & 0xFF is 40, the code for open paren.\n            // therefore we need to do the chopping here to make\n            // sure we backslash all cases.\n            var data = [ 40 ]; // open PDF string '('\n            for (var i = 0; i < txt.length; ++i) {\n                var code = txt.charCodeAt(i) & 0xFF;\n                if (code == 40 || code == 41 || code == 92) {\n                    // backslash before (, ) and \\\n                    data.push(92);\n                }\n                data.push(code);\n            }\n            data.push(41);  // ')' close PDF string\n            out.writeData(data);\n        }\n    };\n\n    PDFString.prototype.toString = function toString () {\n        return this.value;\n    };\n\n    return PDFString;\n}(PDFValue));\n\nvar PDFHexString = (function (PDFString) {\n    function PDFHexString(value) {\n        PDFString.call(this, value);\n        this.value = value;\n    }\n\n    if ( PDFString ) PDFHexString.__proto__ = PDFString;\n    PDFHexString.prototype = Object.create( PDFString && PDFString.prototype );\n    PDFHexString.prototype.constructor = PDFHexString;\n\n    PDFHexString.prototype.render = function render (out) {\n        var this$1 = this;\n\n        out(\"<\");\n        for (var i = 0; i < this.value.length; ++i) {\n            out(zeropad(this$1.value.charCodeAt(i).toString(16), 4));\n        }\n        out(\">\");\n    };\n\n    return PDFHexString;\n}(PDFString));\n\n/// names\nvar PDFName = (function (PDFValue) {\n    function PDFName(name) {\n        PDFValue.call(this);\n        this.name = name;\n    }\n\n    if ( PDFValue ) PDFName.__proto__ = PDFValue;\n    PDFName.prototype = Object.create( PDFValue && PDFValue.prototype );\n    PDFName.prototype.constructor = PDFName;\n\n    PDFName.get = function get (name) {\n        return _(name);\n    };\n\n    PDFName.prototype.render = function render (out) {\n        out(\"/\" + this.escape());\n    };\n\n    PDFName.prototype.escape = function escape () {\n        return this.name.replace(/[^\\x21-\\x7E]/g, function(c){\n            return \"#\" + zeropad(c.charCodeAt(0).toString(16), 2);\n        });\n    };\n\n    PDFName.prototype.toString = function toString () {\n        return this.name;\n    };\n\n    return PDFName;\n}(PDFValue));\n\nfunction _(name) {\n    return new PDFName(name);\n}\n\n/// dictionary\n\nvar PDFDictionary = (function (PDFValue) {\n    function PDFDictionary(props) {\n        PDFValue.call(this);\n        this.props = props;\n    }\n\n    if ( PDFValue ) PDFDictionary.__proto__ = PDFValue;\n    PDFDictionary.prototype = Object.create( PDFValue && PDFValue.prototype );\n    PDFDictionary.prototype.constructor = PDFDictionary;\n\n    PDFDictionary.prototype.render = function render (out) {\n        var props = this.props, empty = true;\n        out(\"<<\");\n        out.withIndent(function(){\n            for (var i in props) {\n                if (hasOwnProperty(props, i) && !/^_/.test(i)) {\n                    empty = false;\n                    out.indent(_(i), \" \", props[i]);\n                }\n            }\n        });\n        if (!empty) {\n            out.indent();\n        }\n        out(\">>\");\n    };\n\n    return PDFDictionary;\n}(PDFValue));\n\n/// streams\n\nvar PDFStream = (function (PDFValue) {\n    function PDFStream(data, props, compress) {\n        PDFValue.call(this);\n        if (typeof data == \"string\") {\n            var tmp = BinaryStream();\n            tmp.write(data);\n            data = tmp;\n        }\n        this.data = data;\n        this.props = props || {};\n        this.compress = compress;\n    }\n\n    if ( PDFValue ) PDFStream.__proto__ = PDFValue;\n    PDFStream.prototype = Object.create( PDFValue && PDFValue.prototype );\n    PDFStream.prototype.constructor = PDFStream;\n\n    PDFStream.prototype.render = function render (out) {\n        var data = this.data.get(), props = this.props;\n        if (this.compress && supportsDeflate()) {\n            if (!props.Filter) {\n                props.Filter = [];\n            } else if (!(props.Filter instanceof Array)) {\n                props.Filter = [ props.Filter ];\n            }\n            props.Filter.unshift(_(\"FlateDecode\"));\n            data = deflate(data);\n        }\n        props.Length = data.length;\n        out(new PDFDictionary(props), \" stream\", NL);\n        out.writeData(data);\n        out(NL, \"endstream\");\n    };\n\n    return PDFStream;\n}(PDFValue));\n\n/// catalog\n\nvar PDFCatalog = (function (PDFDictionary) {\n    function PDFCatalog() {\n        PDFDictionary.call(this, {\n            Type: _(\"Catalog\")\n        });\n    }\n\n    if ( PDFDictionary ) PDFCatalog.__proto__ = PDFDictionary;\n    PDFCatalog.prototype = Object.create( PDFDictionary && PDFDictionary.prototype );\n    PDFCatalog.prototype.constructor = PDFCatalog;\n\n    PDFCatalog.prototype.setPages = function setPages (pagesObj) {\n        this.props.Pages = pagesObj;\n    };\n\n    return PDFCatalog;\n}(PDFDictionary));\n\n/// page tree\n\nvar PDFPageTree = (function (PDFDictionary) {\n    function PDFPageTree() {\n        PDFDictionary.call(this, {\n            Type  : _(\"Pages\"),\n            Kids  : [],\n            Count : 0\n        });\n    }\n\n    if ( PDFDictionary ) PDFPageTree.__proto__ = PDFDictionary;\n    PDFPageTree.prototype = Object.create( PDFDictionary && PDFDictionary.prototype );\n    PDFPageTree.prototype.constructor = PDFPageTree;\n\n    PDFPageTree.prototype.addPage = function addPage (pageObj) {\n        this.props.Kids.push(pageObj);\n        this.props.Count++;\n    };\n\n    return PDFPageTree;\n}(PDFDictionary));\n\n/// images\n\n// JPEG\n\nvar SOF_CODES = [0xc0, 0xc1, 0xc2, 0xc3, 0xc5, 0xc6, 0xc7, 0xc9, 0xca, 0xcb, 0xcd, 0xce, 0xcf];\n\nvar PDFJpegImage = function PDFJpegImage(data) {\n    // we must determine the correct color space.  we'll parse a bit\n    // of the JPEG stream for this, it's still better than going\n    // through the canvas.\n    // https://github.com/telerik/kendo-ui-core/issues/2845\n    data.offset(0);\n    var width, height, colorSpace, bitsPerComponent;\n    var soi = data.readShort();\n    if (soi != 0xFFD8) {\n        // XXX: do we have some better options here?\n        throw new Error(\"Invalid JPEG image\");\n    }\n    while (!data.eof()) {\n        var ff = data.readByte();\n        if (ff != 0xFF) {\n            throw new Error(\"Invalid JPEG image\");\n        }\n        var marker = data.readByte();\n        var length = data.readShort();\n        if (SOF_CODES.indexOf(marker) >= 0) {\n            // \"start of frame\" marker\n            bitsPerComponent = data.readByte();\n            height = data.readShort();\n            width = data.readShort();\n            colorSpace = data.readByte();\n            break;\n        }\n        data.skip(length - 2);\n    }\n\n    if (colorSpace == null) {\n        throw new Error(\"Invalid JPEG image\");\n    }\n\n    var props = {\n        Type         : _(\"XObject\"),\n        Subtype      : _(\"Image\"),\n        Width        : width,\n        Height       : height,\n        BitsPerComponent : bitsPerComponent,\n        Filter       : _(\"DCTDecode\")\n    };\n\n    switch (colorSpace) {\n    case 1:\n        props.ColorSpace = _(\"DeviceGray\");\n        break;\n    case 3:\n        props.ColorSpace = _(\"DeviceRGB\");\n        break;\n    case 4:\n        props.ColorSpace = _(\"DeviceCMYK\");\n        props.Decode = [ 1, 0, 1, 0, 1, 0, 1, 0 ]; // invert colors\n        break;\n    }\n\n    this.asStream = function() {\n        data.offset(0);\n        var stream = new PDFStream(data, props);\n        stream._resourceName = _(\"I\" + (++RESOURCE_COUNTER));\n        return stream;\n    };\n};\n\n// PDFRawImage will be used for images with transparency (PNG)\n\nvar PDFRawImage = function PDFRawImage(width, height, rgb, alpha) {\n    this.asStream = function(pdf) {\n        var mask = new PDFStream(alpha, {\n            Type         : _(\"XObject\"),\n            Subtype      : _(\"Image\"),\n            Width        : width,\n            Height       : height,\n            BitsPerComponent : 8,\n            ColorSpace   : _(\"DeviceGray\")\n        }, true);\n        var stream = new PDFStream(rgb, {\n            Type         : _(\"XObject\"),\n            Subtype      : _(\"Image\"),\n            Width        : width,\n            Height       : height,\n            BitsPerComponent : 8,\n            ColorSpace   : _(\"DeviceRGB\"),\n            SMask        : pdf.attach(mask)\n        }, true);\n        stream._resourceName = _(\"I\" + (++RESOURCE_COUNTER));\n        return stream;\n    };\n};\n\nvar PDFPattern = (function (PDFDictionary) {\n    function PDFPattern(fill, curPage, drawPattern) {\n        var ref = fill.size();\n        var width = ref.width;\n        var height = ref.height;\n        var page = new PDFPage(curPage._pdf, {});\n        page._content = new PDFStream(makeOutput(), null, true);\n\n        drawPattern(fill, page, {});\n\n        curPage._xResources = Object.assign(curPage._xResources, page._xResources);\n        curPage._fontResources = Object.assign(curPage._fontResources, page._fontResources);\n        curPage._gsResources = Object.assign(curPage._gsResources, page._gsResources);\n\n        PDFDictionary.call(this, {\n            Type: _(\"Pattern\"),\n            PatternType: 1,\n            PaintType: 1,\n            TilingType: 1,\n            BBox: [0 , 0, width, height],\n            XStep: width,\n            YStep: height,\n            Matrix: [1, 0, 0, -1, 0, height],\n            Resources: {\n                ExtGState: new PDFDictionary(page._gsResources),\n                XObject: new PDFDictionary(page._xResources),\n                Font: new PDFDictionary(page._fontResources)\n            }\n        });\n        this._resourceName = _(\"P\" + (++PATTERN_COUNTER));\n        this.data = page._content.data;\n        this.compress = true;\n    }\n\n    if ( PDFDictionary ) PDFPattern.__proto__ = PDFDictionary;\n    PDFPattern.prototype = Object.create( PDFDictionary && PDFDictionary.prototype );\n    PDFPattern.prototype.constructor = PDFPattern;\n\n    PDFPattern.prototype.render = function render (out) {\n        PDFStream.prototype.render.call(this, out);\n    };\n\n    return PDFPattern;\n}(PDFDictionary));\n\n/// standard fonts\n\nvar PDFStandardFont = (function (PDFDictionary) {\n    function PDFStandardFont(name){\n        PDFDictionary.call(this, {\n            Type     : _(\"Font\"),\n            Subtype  : _(\"Type1\"),\n            BaseFont : _(name)\n        });\n\n        this._resourceName = _(\"F\" + (++RESOURCE_COUNTER));\n    }\n\n    if ( PDFDictionary ) PDFStandardFont.__proto__ = PDFDictionary;\n    PDFStandardFont.prototype = Object.create( PDFDictionary && PDFDictionary.prototype );\n    PDFStandardFont.prototype.constructor = PDFStandardFont;\n\n    PDFStandardFont.prototype.encodeText = function encodeText (str) {\n        return new PDFString(String(str));\n    };\n\n    return PDFStandardFont;\n}(PDFDictionary));\n\n/// TTF fonts\n\nvar PDFFont = (function (PDFDictionary) {\n    function PDFFont(pdf, font, props){\n        PDFDictionary.call(this, {});\n\n        props = this.props;\n        props.Type = _(\"Font\");\n        props.Subtype = _(\"Type0\");\n        props.Encoding = _(\"Identity-H\");\n\n        this._pdf = pdf;\n        this._font = font;\n        this._sub = font.makeSubset();\n        this._resourceName = _(\"F\" + (++RESOURCE_COUNTER));\n\n        var head = font.head;\n\n        this.name = font.psName;\n        var scale = this.scale = font.scale;\n        this.bbox = [\n            head.xMin * scale,\n            head.yMin * scale,\n            head.xMax * scale,\n            head.yMax * scale\n        ];\n\n        this.italicAngle = font.post.italicAngle;\n        this.ascent = font.ascent * scale;\n        this.descent = font.descent * scale;\n        this.lineGap = font.lineGap * scale;\n        this.capHeight = font.os2.capHeight || this.ascent;\n        this.xHeight = font.os2.xHeight || 0;\n        this.stemV = 0;\n\n        this.familyClass = (font.os2.familyClass || 0) >> 8;\n        this.isSerif = this.familyClass >= 1 && this.familyClass <= 7;\n        this.isScript = this.familyClass == 10;\n\n        this.flags = ((font.post.isFixedPitch ? 1 : 0) |\n                    (this.isSerif ? 1 << 1 : 0) |\n                    (this.isScript ? 1 << 3 : 0) |\n                    (this.italicAngle !== 0 ? 1 << 6 : 0) |\n                    (1 << 5));\n        }\n\n    if ( PDFDictionary ) PDFFont.__proto__ = PDFDictionary;\n    PDFFont.prototype = Object.create( PDFDictionary && PDFDictionary.prototype );\n    PDFFont.prototype.constructor = PDFFont;\n\n        PDFFont.prototype.encodeText = function encodeText (text) {\n            return new PDFHexString(this._sub.encodeText(String(text)));\n        };\n\n        PDFFont.prototype.getTextWidth = function getTextWidth (fontSize, text) {\n            var this$1 = this;\n\n            var width = 0, codeMap = this._font.cmap.codeMap;\n            for (var i = 0; i < text.length; ++i) {\n                var glyphId = codeMap[text.charCodeAt(i)];\n                width += this$1._font.widthOfGlyph(glyphId || 0);\n            }\n            return width * fontSize / 1000;\n        };\n\n        PDFFont.prototype.beforeRender = function beforeRender () {\n            var self = this;\n            var sub = self._sub;\n\n            // write the TTF data\n            var data = sub.render();\n            var fontStream = new PDFStream(BinaryStream(data), {\n                Length1: data.length\n            }, true);\n\n            var descriptor = self._pdf.attach(new PDFDictionary({\n                Type         : _(\"FontDescriptor\"),\n                FontName     : _(self._sub.psName),\n                FontBBox     : self.bbox,\n                Flags        : self.flags,\n                StemV        : self.stemV,\n                ItalicAngle  : self.italicAngle,\n                Ascent       : self.ascent,\n                Descent      : self.descent,\n                CapHeight    : self.capHeight,\n                XHeight      : self.xHeight,\n                FontFile2    : self._pdf.attach(fontStream)\n            }));\n\n            var cmap = sub.ncid2ogid;\n            var firstChar = sub.firstChar;\n            var lastChar = sub.lastChar;\n            var charWidths = [];\n            (function loop(i, chunk){\n                if (i <= lastChar) {\n                    var gid = cmap[i];\n                    if (gid == null) {\n                        loop(i + 1);\n                    } else {\n                        if (!chunk) {\n                            charWidths.push(i, chunk = []);\n                        }\n                        chunk.push(self._font.widthOfGlyph(gid));\n                        loop(i + 1, chunk);\n                    }\n                }\n            })(firstChar);\n\n            // As if two dictionaries weren't enough, we need another\n            // one, the \"descendant font\".  Only that one can be of\n            // Subtype CIDFontType2.  PDF is the X11 of document\n            // formats: portable but full of legacy that nobody cares\n            // about anymore.\n\n            var descendant = new PDFDictionary({\n                Type: _(\"Font\"),\n                Subtype: _(\"CIDFontType2\"),\n                BaseFont: _(self._sub.psName),\n                CIDSystemInfo: new PDFDictionary({\n                    Registry   : new PDFString(\"Adobe\"),\n                    Ordering   : new PDFString(\"Identity\"),\n                    Supplement : 0\n                }),\n                FontDescriptor: descriptor,\n                FirstChar: firstChar,\n                LastChar: lastChar,\n                DW: Math.round(self._font.widthOfGlyph(0)),\n                W: charWidths,\n                CIDToGIDMap: self._pdf.attach(self._makeCidToGidMap())\n            });\n\n            var dict = self.props;\n            dict.BaseFont = _(self._sub.psName);\n            dict.DescendantFonts = [ self._pdf.attach(descendant) ];\n\n            // Compute the ToUnicode map so that apps can extract\n            // meaningful text from the PDF.\n            var unimap = new PDFToUnicodeCmap(firstChar, lastChar, sub.subset);\n            var unimapStream = new PDFStream(makeOutput(), null, true);\n            unimapStream.data(unimap);\n            dict.ToUnicode = self._pdf.attach(unimapStream);\n        };\n\n        PDFFont.prototype._makeCidToGidMap = function _makeCidToGidMap () {\n            return new PDFStream(BinaryStream(this._sub.cidToGidMap()), null, true);\n        };\n\n    return PDFFont;\n}(PDFDictionary));\n\nvar PDFToUnicodeCmap = (function (PDFValue) {\n    function PDFToUnicodeCmap(firstChar, lastChar, map){\n        PDFValue.call(this);\n        this.firstChar = firstChar;\n        this.lastChar = lastChar;\n        this.map = map;\n    }\n\n    if ( PDFValue ) PDFToUnicodeCmap.__proto__ = PDFValue;\n    PDFToUnicodeCmap.prototype = Object.create( PDFValue && PDFValue.prototype );\n    PDFToUnicodeCmap.prototype.constructor = PDFToUnicodeCmap;\n\n    PDFToUnicodeCmap.prototype.render = function render (out) {\n        out.indent(\"/CIDInit /ProcSet findresource begin\");\n        out.indent(\"12 dict begin\");\n        out.indent(\"begincmap\");\n        out.indent(\"/CIDSystemInfo <<\");\n        out.indent(\"  /Registry (Adobe)\");\n        out.indent(\"  /Ordering (UCS)\");\n        out.indent(\"  /Supplement 0\");\n        out.indent(\">> def\");\n        out.indent(\"/CMapName /Adobe-Identity-UCS def\");\n        out.indent(\"/CMapType 2 def\");\n        out.indent(\"1 begincodespacerange\");\n        out.indent(\"  <0000><ffff>\");\n        out.indent(\"endcodespacerange\");\n\n        var self = this;\n        out.indent(self.lastChar - self.firstChar + 1, \" beginbfchar\");\n        out.withIndent(function(){\n            for (var code = self.firstChar; code <= self.lastChar; ++code) {\n                var unicode = self.map[code];\n                var str = ucs2encode([ unicode ]);\n                out.indent(\"<\", zeropad(code.toString(16), 4), \">\", \"<\");\n                for (var i = 0; i < str.length; ++i) {\n                    out(zeropad(str.charCodeAt(i).toString(16), 4));\n                }\n                out(\">\");\n            }\n        });\n        out.indent(\"endbfchar\");\n\n        out.indent(\"endcmap\");\n        out.indent(\"CMapName currentdict /CMap defineresource pop\");\n        out.indent(\"end\");\n        out.indent(\"end\");\n    };\n\n    return PDFToUnicodeCmap;\n}(PDFValue));\n\n/// gradients\n\nfunction makeHash(a) {\n    return a.map(function(x){\n        return isArray(x) ? makeHash(x)\n            : typeof x == \"number\" ? (Math.round(x * 1000) / 1000).toFixed(3)\n            : x;\n    }).join(\" \");\n}\n\nfunction cacheColorGradientFunction(pdf, r1, g1, b1, r2, g2, b2) {\n    var hash = makeHash([ r1, g1, b1, r2, g2, b2 ]);\n    var func = pdf.GRAD_COL_FUNCTIONS[hash];\n    if (!func) {\n        func = pdf.GRAD_COL_FUNCTIONS[hash] = pdf.attach(new PDFDictionary({\n            FunctionType: 2,\n            Domain: [ 0, 1 ],\n            Range: [ 0, 1, 0, 1, 0, 1 ],\n            N: 1,\n            C0: [ r1 , g1 , b1 ],\n            C1: [ r2 , g2 , b2 ]\n        }));\n    }\n    return func;\n}\n\nfunction cacheOpacityGradientFunction(pdf, a1, a2) {\n    var hash = makeHash([ a1, a2 ]);\n    var func = pdf.GRAD_OPC_FUNCTIONS[hash];\n    if (!func) {\n        func = pdf.GRAD_OPC_FUNCTIONS[hash] = pdf.attach(new PDFDictionary({\n            FunctionType: 2,\n            Domain: [ 0, 1 ],\n            Range: [ 0, 1 ],\n            N: 1,\n            C0: [ a1 ],\n            C1: [ a2 ]\n        }));\n    }\n    return func;\n}\n\nfunction makeGradientFunctions(pdf, stops) {\n    var hasAlpha = false;\n    var opacities = [];\n    var colors = [];\n    var offsets = [];\n    var encode = [];\n    var i, prev, cur, prevColor, curColor;\n    for (i = 1; i < stops.length; ++i) {\n        prev = stops[i - 1];\n        cur = stops[i];\n        prevColor = prev.color;\n        curColor = cur.color;\n        colors.push(cacheColorGradientFunction(\n            pdf,\n            prevColor.r, prevColor.g, prevColor.b,\n            curColor.r,  curColor.g,  curColor.b\n        ));\n        if (prevColor.a < 1 || curColor.a < 1) {\n            hasAlpha = true;\n        }\n        offsets.push(cur.offset);\n        encode.push(0, 1);\n    }\n    if (hasAlpha) {\n        for (i = 1; i < stops.length; ++i) {\n            prev = stops[i - 1];\n            cur = stops[i];\n            prevColor = prev.color;\n            curColor = cur.color;\n            opacities.push(cacheOpacityGradientFunction(\n                pdf, prevColor.a, curColor.a\n            ));\n        }\n    }\n    offsets.pop();\n    return {\n        hasAlpha  : hasAlpha,\n        colors    : assemble(colors),\n        opacities : hasAlpha ? assemble(opacities) : null\n    };\n    function assemble(funcs) {\n        if (funcs.length == 1) {\n            return funcs[0];\n        }\n        return {\n            FunctionType: 3,\n            Functions: funcs,\n            Domain: [ 0, 1 ],\n            Bounds: offsets,\n            Encode: encode\n        };\n    }\n}\n\nfunction cacheColorGradient(pdf, isRadial, stops, coords, funcs, box) {\n    var shading, hash;\n    // if box is given then we have user-space coordinates, which\n    // means the gradient is designed for a certain position/size\n    // on page.  caching won't do any good.\n    if (!box) {\n        var a = [ isRadial ].concat(coords);\n        stops.forEach(function(x){\n            a.push(x.offset, x.color.r, x.color.g, x.color.b);\n        });\n        hash = makeHash(a);\n        shading = pdf.GRAD_COL[hash];\n    }\n    if (!shading) {\n        shading = new PDFDictionary({\n            Type: _(\"Shading\"),\n            ShadingType: isRadial ? 3 : 2,\n            ColorSpace: _(\"DeviceRGB\"),\n            Coords: coords,\n            Domain: [ 0, 1 ],\n            Function: funcs,\n            Extend: [ true, true ]\n        });\n        pdf.attach(shading);\n        shading._resourceName = \"S\" + (++RESOURCE_COUNTER);\n        if (hash) {\n            pdf.GRAD_COL[hash] = shading;\n        }\n    }\n    return shading;\n}\n\nfunction cacheOpacityGradient(pdf, isRadial, stops, coords, funcs, box) {\n    var opacity, hash;\n    // if box is given then we have user-space coordinates, which\n    // means the gradient is designed for a certain position/size\n    // on page.  caching won't do any good.\n    if (!box) {\n        var a = [ isRadial ].concat(coords);\n        stops.forEach(function(x){\n            a.push(x.offset, x.color.a);\n        });\n        hash = makeHash(a);\n        opacity = pdf.GRAD_OPC[hash];\n    }\n    if (!opacity) {\n        opacity = new PDFDictionary({\n            Type: _(\"ExtGState\"),\n            AIS: false,\n            CA: 1,\n            ca: 1,\n            SMask: {\n                Type: _(\"Mask\"),\n                S: _(\"Luminosity\"),\n                G: pdf.attach(new PDFStream(\"/a0 gs /s0 sh\", {\n                    Type: _(\"XObject\"),\n                    Subtype: _(\"Form\"),\n                    FormType: 1,\n                    BBox: (box ? [\n                        box.left, box.top + box.height, box.left + box.width, box.top\n                    ] : [ 0, 1, 1, 0 ]),\n                    Group: {\n                        Type: _(\"Group\"),\n                        S: _(\"Transparency\"),\n                        CS: _(\"DeviceGray\"),\n                        I: true\n                    },\n                    Resources: {\n                        ExtGState: {\n                            a0: { CA: 1, ca: 1 }\n                        },\n                        Shading: {\n                            s0: {\n                                ColorSpace: _(\"DeviceGray\"),\n                                Coords: coords,\n                                Domain: [ 0, 1 ],\n                                ShadingType: isRadial ? 3 : 2,\n                                Function: funcs,\n                                Extend: [ true, true ]\n                            }\n                        }\n                    }\n                }))\n            }\n        });\n        pdf.attach(opacity);\n        opacity._resourceName = \"O\" + (++RESOURCE_COUNTER);\n        if (hash) {\n            pdf.GRAD_OPC[hash] = opacity;\n        }\n    }\n    return opacity;\n}\n\nfunction cacheGradient(pdf, gradient, box) {\n    var isRadial = gradient.type == \"radial\";\n    var funcs = makeGradientFunctions(pdf, gradient.stops);\n    var coords = isRadial ? [\n        gradient.start.x , gradient.start.y , gradient.start.r,\n        gradient.end.x   , gradient.end.y   , gradient.end.r\n    ] : [\n        gradient.start.x , gradient.start.y,\n        gradient.end.x   , gradient.end.y\n    ];\n    var shading = cacheColorGradient(\n        pdf, isRadial, gradient.stops, coords, funcs.colors, gradient.userSpace && box\n    );\n    var opacity = funcs.hasAlpha ? cacheOpacityGradient(\n        pdf, isRadial, gradient.stops, coords, funcs.opacities, gradient.userSpace && box\n    ) : null;\n    return {\n        hasAlpha: funcs.hasAlpha,\n        shading: shading,\n        opacity: opacity\n    };\n}\n\n/// page object\n\nvar PDFPage = (function (PDFDictionary) {\n    function PDFPage(pdf, props){\n        PDFDictionary.call(this, props);\n\n        this._pdf = pdf;\n        this._rcount = 0;\n        this._textMode = false;\n        this._fontResources = {};\n        this._gsResources = {};\n        this._xResources = {};\n        this._patResources = {};\n        this._shResources = {};\n        this._opacity = 1;\n        this._matrix = [ 1, 0, 0, 1, 0, 0 ];\n        this._annotations = [];\n\n        this._font = null;\n        this._fontSize = null;\n\n        this._contextStack = [];\n\n        props = this.props;\n        props.Type = _(\"Page\");\n        props.ProcSet = [\n            _(\"PDF\"),\n            _(\"Text\"),\n            _(\"ImageB\"),\n            _(\"ImageC\"),\n            _(\"ImageI\")\n        ];\n        props.Resources = new PDFDictionary({\n            Font      : new PDFDictionary(this._fontResources),\n            ExtGState : new PDFDictionary(this._gsResources),\n            XObject   : new PDFDictionary(this._xResources),\n            Pattern   : new PDFDictionary(this._patResources),\n            Shading   : new PDFDictionary(this._shResources)\n        });\n        props.Annots = this._annotations;\n    }\n\n    if ( PDFDictionary ) PDFPage.__proto__ = PDFDictionary;\n    PDFPage.prototype = Object.create( PDFDictionary && PDFDictionary.prototype );\n    PDFPage.prototype.constructor = PDFPage;\n\n    PDFPage.prototype._out = function _out () {\n        this._content.data.apply(null, arguments);\n    };\n\n    PDFPage.prototype.transform = function transform (a, b, c, d, e, f) {\n        if (!isIdentityMatrix(arguments)) {\n            this._matrix = mmul(arguments, this._matrix);\n            this._out(a, \" \", b, \" \", c, \" \", d, \" \", e, \" \", f, \" cm\");\n            // XXX: debug\n            // this._out(\" % current matrix: \", this._matrix);\n            this._out(NL);\n        }\n    };\n\n    PDFPage.prototype.translate = function translate (dx, dy) {\n        this.transform(1, 0, 0, 1, dx, dy);\n    };\n\n    PDFPage.prototype.scale = function scale (sx, sy) {\n        this.transform(sx, 0, 0, sy, 0, 0);\n    };\n\n    PDFPage.prototype.rotate = function rotate (angle) {\n        var cos = Math.cos(angle), sin = Math.sin(angle);\n        this.transform(cos, sin, -sin, cos, 0, 0);\n    };\n\n    PDFPage.prototype.beginText = function beginText () {\n        this._textMode = true;\n        this._out(\"BT\", NL);\n    };\n\n    PDFPage.prototype.endText = function endText () {\n        this._textMode = false;\n        this._out(\"ET\", NL);\n    };\n\n    PDFPage.prototype._requireTextMode = function _requireTextMode () {\n        if (!this._textMode) {\n            throw new Error(\"Text mode required; call page.beginText() first\");\n        }\n    };\n\n    PDFPage.prototype._requireFont = function _requireFont () {\n        if (!this._font) {\n            throw new Error(\"No font selected; call page.setFont() first\");\n        }\n    };\n\n    PDFPage.prototype.setFont = function setFont (font, size) {\n        this._requireTextMode();\n        if (font == null) {\n            font = this._font;\n        } else if (!(font instanceof PDFFont)) {\n            font = this._pdf.getFont(font);\n        }\n        if (size == null) {\n            size = this._fontSize;\n        }\n        this._fontResources[font._resourceName] = font;\n        this._font = font;\n        this._fontSize = size;\n        this._out(font._resourceName, \" \", size, \" Tf\", NL);\n    };\n\n    PDFPage.prototype.setTextLeading = function setTextLeading (size) {\n        this._requireTextMode();\n        this._out(size, \" TL\", NL);\n    };\n\n    PDFPage.prototype.setTextRenderingMode = function setTextRenderingMode (mode) {\n        this._requireTextMode();\n        this._out(mode, \" Tr\", NL);\n    };\n\n    PDFPage.prototype.showText = function showText (text, requestedWidth) {\n        this._requireFont();\n        if (text.length > 1 && requestedWidth && this._font instanceof PDFFont) {\n            var outputWidth = this._font.getTextWidth(this._fontSize, text);\n            var scale = requestedWidth / outputWidth * 100;\n            this._out(scale, \" Tz \");\n        }\n        this._out(this._font.encodeText(text), \" Tj\", NL);\n    };\n\n    PDFPage.prototype.showTextNL = function showTextNL (text) {\n        this._requireFont();\n        this._out(this._font.encodeText(text), \" '\", NL);\n    };\n\n    PDFPage.prototype.addLink = function addLink (uri, box) {\n        var ll = this._toPage({ x: box.left, y: box.bottom });\n        var ur = this._toPage({ x: box.right, y: box.top });\n        this._annotations.push(new PDFDictionary({\n            Type    : _(\"Annot\"),\n            Subtype : _(\"Link\"),\n            Rect    : [ ll.x, ll.y, ur.x, ur.y ],\n            Border  : [ 0, 0, 0 ],\n            A       : new PDFDictionary({\n                Type : _(\"Action\"),\n                S    : _(\"URI\"),\n                URI  : new PDFString(uri)\n            })\n        }));\n    };\n\n    PDFPage.prototype.setStrokeColor = function setStrokeColor (r, g, b) {\n        this._out(r, \" \", g, \" \", b, \" RG\", NL);\n    };\n\n    PDFPage.prototype.setOpacity = function setOpacity (opacity) {\n        this.setFillOpacity(opacity);\n        this.setStrokeOpacity(opacity);\n        this._opacity *= opacity;\n    };\n\n    PDFPage.prototype.setStrokeOpacity = function setStrokeOpacity (opacity) {\n        if (opacity < 1) {\n            var gs = this._pdf.getOpacityGS(this._opacity * opacity, true);\n            this._gsResources[gs._resourceName] = gs;\n            this._out(gs._resourceName, \" gs\", NL);\n        }\n    };\n\n    PDFPage.prototype.setFillColor = function setFillColor (r, g, b) {\n        this._out(r, \" \", g, \" \", b, \" rg\", NL);\n    };\n\n    PDFPage.prototype.pattern = function pattern (fill, box, drawPattern) {\n        var pattern = this._pdf.getPattern(fill, this, drawPattern);\n        this._patResources[pattern._resourceName] = pattern;\n\n        this._out(\"/Pattern cs\", NL);\n        this._out(pattern._resourceName, \" scn\", NL);\n\n        this.rect(box.left, box.top, box.width, box.height);\n        this.fill();\n    };\n\n    PDFPage.prototype.setFillOpacity = function setFillOpacity (opacity) {\n        if (opacity < 1) {\n            var gs = this._pdf.getOpacityGS(this._opacity * opacity, false);\n            this._gsResources[gs._resourceName] = gs;\n            this._out(gs._resourceName, \" gs\", NL);\n        }\n    };\n\n    PDFPage.prototype.gradient = function gradient (gradient$1, box) {\n        this.save();\n        this.rect(box.left, box.top, box.width, box.height);\n        this.clip();\n        if (!gradient$1.userSpace) {\n            this.transform(box.width, 0, 0, box.height, box.left, box.top);\n        }\n        var g = cacheGradient(this._pdf, gradient$1, box);\n        var sname = g.shading._resourceName, oname;\n        this._shResources[sname] = g.shading;\n        if (g.hasAlpha) {\n            oname = g.opacity._resourceName;\n            this._gsResources[oname] = g.opacity;\n            this._out(\"/\" + oname + \" gs \");\n        }\n        this._out(\"/\" + sname + \" sh\", NL);\n        this.restore();\n    };\n\n    PDFPage.prototype.setDashPattern = function setDashPattern (dashArray, dashPhase) {\n        this._out(dashArray, \" \", dashPhase, \" d\", NL);\n    };\n\n    PDFPage.prototype.setLineWidth = function setLineWidth (width) {\n        this._out(width, \" w\", NL);\n    };\n\n    PDFPage.prototype.setLineCap = function setLineCap (lineCap) {\n        this._out(lineCap, \" J\", NL);\n    };\n\n    PDFPage.prototype.setLineJoin = function setLineJoin (lineJoin) {\n        this._out(lineJoin, \" j\", NL);\n    };\n\n    PDFPage.prototype.setMitterLimit = function setMitterLimit (mitterLimit) {\n        this._out(mitterLimit, \" M\", NL);\n    };\n\n    PDFPage.prototype.save = function save () {\n        this._contextStack.push(this._context());\n        this._out(\"q\", NL);\n    };\n\n    PDFPage.prototype.restore = function restore () {\n        this._out(\"Q\", NL);\n        this._context(this._contextStack.pop());\n    };\n\n\n    // paths\n    PDFPage.prototype.moveTo = function moveTo (x, y) {\n        this._out(x, \" \", y, \" m\", NL);\n    };\n\n    PDFPage.prototype.lineTo = function lineTo (x, y) {\n        this._out(x, \" \", y, \" l\", NL);\n    };\n\n    PDFPage.prototype.bezier = function bezier (x1, y1, x2, y2, x3, y3) {\n        this._out(x1, \" \", y1, \" \", x2, \" \", y2, \" \", x3, \" \", y3, \" c\", NL);\n    };\n\n    PDFPage.prototype.bezier1 = function bezier1 (x1, y1, x3, y3) {\n        this._out(x1, \" \", y1, \" \", x3, \" \", y3, \" y\", NL);\n    };\n\n    PDFPage.prototype.bezier2 = function bezier2 (x2, y2, x3, y3) {\n        this._out(x2, \" \", y2, \" \", x3, \" \", y3, \" v\", NL);\n    };\n\n    PDFPage.prototype.close = function close () {\n        this._out(\"h\", NL);\n    };\n\n    PDFPage.prototype.rect = function rect (x, y, w, h) {\n        this._out(x, \" \", y, \" \", w, \" \", h, \" re\", NL);\n    };\n\n    PDFPage.prototype.ellipse = function ellipse (x, y, rx, ry) {\n        function _X(v) { return x + v; }\n        function _Y(v) { return y + v; }\n\n        // how to get to the \"magic number\" is explained here:\n        // http://www.whizkidtech.redprince.net/bezier/circle/kappa/\n        var k = 0.5522847498307936;\n\n        this.moveTo(_X(0), _Y(ry));\n        this.bezier(\n            _X(rx * k) , _Y(ry),\n            _X(rx)     , _Y(ry * k),\n            _X(rx)     , _Y(0)\n        );\n        this.bezier(\n            _X(rx)     , _Y(-ry * k),\n            _X(rx * k) , _Y(-ry),\n            _X(0)      , _Y(-ry)\n        );\n        this.bezier(\n            _X(-rx * k) , _Y(-ry),\n            _X(-rx)     , _Y(-ry * k),\n            _X(-rx)     , _Y(0)\n        );\n        this.bezier(\n            _X(-rx)     , _Y(ry * k),\n            _X(-rx * k) , _Y(ry),\n            _X(0)       , _Y(ry)\n        );\n    };\n\n    PDFPage.prototype.circle = function circle (x, y, r) {\n        this.ellipse(x, y, r, r);\n    };\n\n    PDFPage.prototype.stroke = function stroke () {\n        this._out(\"S\", NL);\n    };\n\n    PDFPage.prototype.nop = function nop () {\n        this._out(\"n\", NL);\n    };\n\n    PDFPage.prototype.clip = function clip () {\n        this._out(\"W n\", NL);\n    };\n\n    PDFPage.prototype.clipStroke = function clipStroke () {\n        this._out(\"W S\", NL);\n    };\n\n    PDFPage.prototype.closeStroke = function closeStroke () {\n        this._out(\"s\", NL);\n    };\n\n    PDFPage.prototype.fill = function fill () {\n        this._out(\"f\", NL);\n    };\n\n    PDFPage.prototype.fillStroke = function fillStroke () {\n        this._out(\"B\", NL);\n    };\n\n    PDFPage.prototype.drawImage = function drawImage (url) {\n        var img = this._pdf.getImage(url);\n        if (img) { // the result can be null for a cross-domain image\n            this._xResources[img._resourceName] = img;\n            this._out(img._resourceName, \" Do\", NL);\n        }\n    };\n\n    PDFPage.prototype.comment = function comment (txt) {\n        var self = this;\n        txt.split(/\\r?\\n/g).forEach(function(line){\n            self._out(\"% \", line, NL);\n        });\n    };\n\n    // internal\n    PDFPage.prototype._context = function _context (val) {\n        if (val != null) {\n            this._opacity = val.opacity;\n            this._matrix = val.matrix;\n        } else {\n            return {\n                opacity: this._opacity,\n                matrix: this._matrix\n            };\n        }\n    };\n\n    PDFPage.prototype._toPage = function _toPage (p) {\n        var m = this._matrix;\n        var a = m[0], b = m[1], c = m[2], d = m[3], e = m[4], f = m[5];\n        return {\n            x: a*p.x + c*p.y + e,\n            y: b*p.x + d*p.y + f\n        };\n    };\n\n    return PDFPage;\n}(PDFDictionary));\n\nfunction unquote(str) {\n    return str.replace(/^\\s*(['\"])(.*)\\1\\s*$/, \"$2\");\n}\n\nfunction parseFontDef(fontdef) {\n    // XXX: this is very crude for now and buggy.  Proper parsing is quite involved.\n    var rx = /^\\s*((normal|italic)\\s+)?((normal|small-caps)\\s+)?((normal|bold|\\d+)\\s+)?(([0-9.]+)(px|pt))(\\/(([0-9.]+)(px|pt)|normal))?\\s+(.*?)\\s*$/i;\n    var m = rx.exec(fontdef);\n    if (!m) {\n        return { fontSize: 12, fontFamily: \"sans-serif\" };\n    }\n    var fontSize = m[8] ? parseInt(m[8], 10) : 12;\n    return {\n        italic     : m[2] && m[2].toLowerCase() == \"italic\",\n        variant    : m[4],\n        bold       : m[6] && /bold|700/i.test(m[6]),\n        fontSize   : fontSize,\n        lineHeight : m[12] ? m[12] == \"normal\" ? fontSize : parseInt(m[12], 10) : null,\n        fontFamily : m[14].split(/\\s*,\\s*/g).map(unquote)\n    };\n}\n\nfunction getFontURL(style) {\n    function mkFamily(name) {\n        if (style.bold) {\n            name += \"|bold\";\n        }\n        if (style.italic) {\n            name += \"|italic\";\n        }\n        return name.toLowerCase();\n    }\n    var fontFamily = style.fontFamily;\n    var name, url;\n    if (fontFamily instanceof Array) {\n        for (var i = 0; i < fontFamily.length; ++i) {\n            name = mkFamily(fontFamily[i]);\n            url = FONT_MAPPINGS[name];\n            if (url) {\n                break;\n            }\n        }\n    } else {\n        url = FONT_MAPPINGS[fontFamily.toLowerCase()];\n    }\n    while (typeof url == \"function\") {\n        url = url();\n    }\n    if (!url) {\n        url = \"Times-Roman\";\n    }\n    return url;\n}\n\nvar FONT_MAPPINGS = {\n    \"serif\"                    : \"Times-Roman\",\n    \"serif|bold\"               : \"Times-Bold\",\n    \"serif|italic\"             : \"Times-Italic\",\n    \"serif|bold|italic\"        : \"Times-BoldItalic\",\n    \"sans-serif\"               : \"Helvetica\",\n    \"sans-serif|bold\"          : \"Helvetica-Bold\",\n    \"sans-serif|italic\"        : \"Helvetica-Oblique\",\n    \"sans-serif|bold|italic\"   : \"Helvetica-BoldOblique\",\n    \"monospace\"                : \"Courier\",\n    \"monospace|bold\"           : \"Courier-Bold\",\n    \"monospace|italic\"         : \"Courier-Oblique\",\n    \"monospace|bold|italic\"    : \"Courier-BoldOblique\",\n    \"zapfdingbats\"             : \"ZapfDingbats\",\n    \"zapfdingbats|bold\"        : \"ZapfDingbats\",\n    \"zapfdingbats|italic\"      : \"ZapfDingbats\",\n    \"zapfdingbats|bold|italic\" : \"ZapfDingbats\"\n};\n\nfunction fontAlias(alias, name) {\n    alias = alias.toLowerCase();\n    FONT_MAPPINGS[alias] = function() {\n        return FONT_MAPPINGS[name];\n    };\n    FONT_MAPPINGS[alias + \"|bold\"] = function() {\n        return FONT_MAPPINGS[name + \"|bold\"];\n    };\n    FONT_MAPPINGS[alias + \"|italic\"] = function() {\n        return FONT_MAPPINGS[name + \"|italic\"];\n    };\n    FONT_MAPPINGS[alias + \"|bold|italic\"] = function() {\n        return FONT_MAPPINGS[name + \"|bold|italic\"];\n    };\n}\n\n// Let's define some common names to an appropriate replacement.\n// These are overridable via pdf.defineFont, should the user want to\n// include the proper versions.\n\nfontAlias(\"Times New Roman\" , \"serif\");\nfontAlias(\"Courier New\"     , \"monospace\");\nfontAlias(\"Arial\"           , \"sans-serif\");\nfontAlias(\"Helvetica\"       , \"sans-serif\");\nfontAlias(\"Verdana\"         , \"sans-serif\");\nfontAlias(\"Tahoma\"          , \"sans-serif\");\nfontAlias(\"Georgia\"         , \"sans-serif\");\nfontAlias(\"Monaco\"          , \"monospace\");\nfontAlias(\"Andale Mono\"     , \"monospace\");\n\nfunction defineFont(name, url) {\n    if (arguments.length == 1) {\n        for (var i in name) {\n            if (hasOwnProperty(name, i)) {\n                defineFont(i, name[i]);\n            }\n        }\n    } else {\n        name = name.toLowerCase();\n        FONT_MAPPINGS[name] = url;\n\n        // special handling for DejaVu fonts: if they get defined,\n        // let them also replace the default families, for good\n        // Unicode support out of the box.\n        switch (name) {\n          case \"dejavu sans\"               : FONT_MAPPINGS[\"sans-serif\"]              = url; break;\n          case \"dejavu sans|bold\"          : FONT_MAPPINGS[\"sans-serif|bold\"]         = url; break;\n          case \"dejavu sans|italic\"        : FONT_MAPPINGS[\"sans-serif|italic\"]       = url; break;\n          case \"dejavu sans|bold|italic\"   : FONT_MAPPINGS[\"sans-serif|bold|italic\"]  = url; break;\n          case \"dejavu serif\"              : FONT_MAPPINGS[\"serif\"]                   = url; break;\n          case \"dejavu serif|bold\"         : FONT_MAPPINGS[\"serif|bold\"]              = url; break;\n          case \"dejavu serif|italic\"       : FONT_MAPPINGS[\"serif|italic\"]            = url; break;\n          case \"dejavu serif|bold|italic\"  : FONT_MAPPINGS[\"serif|bold|italic\"]       = url; break;\n          case \"dejavu mono\"               : FONT_MAPPINGS[\"monospace\"]               = url; break;\n          case \"dejavu mono|bold\"          : FONT_MAPPINGS[\"monospace|bold\"]          = url; break;\n          case \"dejavu mono|italic\"        : FONT_MAPPINGS[\"monospace|italic\"]        = url; break;\n          case \"dejavu mono|bold|italic\"   : FONT_MAPPINGS[\"monospace|bold|italic\"]   = url; break;\n        }\n    }\n}\n\nfunction mmul(a, b) {\n    var a1 = a[0], b1 = a[1], c1 = a[2], d1 = a[3], e1 = a[4], f1 = a[5];\n    var a2 = b[0], b2 = b[1], c2 = b[2], d2 = b[3], e2 = b[4], f2 = b[5];\n    return [\n        a1*a2 + b1*c2,          a1*b2 + b1*d2,\n        c1*a2 + d1*c2,          c1*b2 + d1*d2,\n        e1*a2 + f1*c2 + e2,     e1*b2 + f1*d2 + f2\n    ];\n}\n\nfunction isIdentityMatrix(m) {\n    return m[0] === 1 && m[1] === 0 && m[2] === 0 && m[3] === 1 && m[4] === 0 && m[5] === 0;\n}\n\nvar TEXT_RENDERING_MODE = {\n    fill           : 0,\n    stroke         : 1,\n    fillAndStroke  : 2,\n    invisible      : 3,\n    fillAndClip    : 4,\n    strokeAndClip  : 5,\n    fillStrokeClip : 6,\n    clip           : 7\n};\n\nexport {\n    PDFDocument as Document,\n    BinaryStream,\n    defineFont,\n    parseFontDef,\n    getFontURL,\n    loadFonts,\n    loadImages,\n    getPaperOptions,\n    clearImageCache,\n    TEXT_RENDERING_MODE\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,SAASA,YAAY,EAAEC,UAAU,EAAEC,kBAAkB,EAAEC,gBAAgB,QAAQ,SAAS;AACxF,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,OAAO,QAAQ,OAAO;AAC/B,SAASC,OAAO,EAAEC,eAAe,QAAQ,WAAW;AACpD,SAASC,aAAa,EAAEC,GAAG,QAAQ,oBAAoB;AACvD,SAASC,YAAY,QAAQ,SAAS;AAEtC,IAAIC,OAAO,GAAGP,OAAO,CAACO,OAAO;AAC7B,IAAIC,EAAE,GAAG,IAAI;AAEb,IAAIC,gBAAgB,GAAG,CAAC;AACxB,IAAIC,eAAe,GAAG,CAAC;AAEvB,IAAIC,UAAU,GAAG;EACbC,EAAE,EAAU,CAAE,OAAO,EAAG,OAAO,CAAE;EACjCC,EAAE,EAAU,CAAE,OAAO,EAAG,OAAO,CAAE;EACjCC,EAAE,EAAU,CAAE,OAAO,EAAG,OAAO,CAAE;EACjCC,EAAE,EAAU,CAAE,MAAM,EAAI,OAAO,CAAE;EACjCC,EAAE,EAAU,CAAE,MAAM,EAAI,MAAM,CAAG;EACjCC,EAAE,EAAU,CAAE,MAAM,EAAI,MAAM,CAAG;EACjCC,EAAE,EAAU,CAAE,MAAM,EAAI,MAAM,CAAG;EACjCC,EAAE,EAAU,CAAE,MAAM,EAAI,MAAM,CAAG;EACjCC,EAAE,EAAU,CAAE,MAAM,EAAI,MAAM,CAAG;EACjCC,EAAE,EAAU,CAAE,MAAM,EAAI,MAAM,CAAG;EACjCC,GAAG,EAAS,CAAE,KAAK,EAAK,MAAM,CAAG;EACjCC,EAAE,EAAU,CAAE,OAAO,EAAG,OAAO,CAAE;EACjCC,EAAE,EAAU,CAAE,OAAO,EAAG,OAAO,CAAE;EACjCC,EAAE,EAAU,CAAE,OAAO,EAAG,OAAO,CAAE;EACjCC,EAAE,EAAU,CAAE,OAAO,EAAG,OAAO,CAAE;EACjCC,EAAE,EAAU,CAAE,MAAM,EAAI,OAAO,CAAE;EACjCC,EAAE,EAAU,CAAE,MAAM,EAAI,MAAM,CAAG;EACjCC,EAAE,EAAU,CAAE,MAAM,EAAI,MAAM,CAAG;EACjCC,EAAE,EAAU,CAAE,MAAM,EAAI,MAAM,CAAG;EACjCC,EAAE,EAAU,CAAE,MAAM,EAAI,MAAM,CAAG;EACjCC,EAAE,EAAU,CAAE,MAAM,EAAI,MAAM,CAAG;EACjCC,GAAG,EAAS,CAAE,KAAK,EAAK,MAAM,CAAG;EACjCC,EAAE,EAAU,CAAE,OAAO,EAAG,OAAO,CAAE;EACjCC,EAAE,EAAU,CAAE,OAAO,EAAG,OAAO,CAAE;EACjCC,EAAE,EAAU,CAAE,OAAO,EAAG,OAAO,CAAE;EACjCC,EAAE,EAAU,CAAE,MAAM,EAAI,OAAO,CAAE;EACjCC,EAAE,EAAU,CAAE,MAAM,EAAI,MAAM,CAAG;EACjCC,EAAE,EAAU,CAAE,MAAM,EAAI,MAAM,CAAG;EACjCC,EAAE,EAAU,CAAE,MAAM,EAAI,MAAM,CAAG;EACjCC,EAAE,EAAU,CAAE,MAAM,EAAI,MAAM,CAAG;EACjCC,EAAE,EAAU,CAAE,MAAM,EAAI,MAAM,CAAG;EACjCC,EAAE,EAAU,CAAE,MAAM,EAAI,MAAM,CAAG;EACjCC,GAAG,EAAS,CAAE,KAAK,EAAK,MAAM,CAAG;EACjCC,SAAS,EAAG,CAAE,MAAM,EAAI,MAAM,CAAG;EACjCC,KAAK,EAAO,CAAE,MAAM,EAAI,MAAM,CAAG;EACjCC,KAAK,EAAO,CAAE,MAAM,EAAI,OAAO,CAAE;EACjCC,MAAM,EAAM,CAAE,MAAM,EAAI,MAAM,CAAG;EACjCC,OAAO,EAAK,CAAE,MAAM,EAAI,OAAO;AACnC,CAAC;AAED,SAASC,UAAUA,CAAA,EAAG;EAClB,IAAIC,WAAW,GAAG,CAAC;IAAEC,MAAM,GAAGxD,YAAY,CAAC,CAAC;EAC5C,SAASyD,GAAGA,CAAA,EAAG;IACX,IAAIC,WAAW,GAAGC,SAAS;IAE3B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAACE,MAAM,EAAE,EAAED,CAAC,EAAE;MACvC,IAAIE,CAAC,GAAGJ,WAAW,CAACE,CAAC,CAAC;MACtB,IAAIE,CAAC,KAAKC,SAAS,EAAE;QACjB,MAAM,IAAIC,KAAK,CAAC,gCAAgC,CAAC;MACrD,CAAC,MACI,IAAIF,CAAC,YAAYG,QAAQ,EAAE;QAC5BH,CAAC,CAACI,YAAY,CAACT,GAAG,CAAC;QACnBK,CAAC,CAACK,MAAM,CAACV,GAAG,CAAC;MACjB,CAAC,MACI,IAAIW,OAAO,CAACN,CAAC,CAAC,EAAE;QACjBO,WAAW,CAACP,CAAC,EAAEL,GAAG,CAAC;MACvB,CAAC,MACI,IAAIa,MAAM,CAACR,CAAC,CAAC,EAAE;QAChBS,UAAU,CAACT,CAAC,EAAEL,GAAG,CAAC;MACtB,CAAC,MACI,IAAI,OAAOK,CAAC,IAAI,QAAQ,EAAE;QAC3B,IAAIU,KAAK,CAACV,CAAC,CAAC,EAAE;UACV,MAAM,IAAIE,KAAK,CAAC,0BAA0B,CAAC;QAC/C;QACA;QACA,IAAIS,GAAG,GAAGX,CAAC,CAACY,OAAO,CAAC,CAAC,CAAC;QACtB,IAAID,GAAG,CAACE,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;UACvBF,GAAG,GAAGA,GAAG,CAACG,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;QACnC;QACA,IAAIH,GAAG,IAAI,IAAI,EAAE;UACbA,GAAG,GAAG,GAAG;QACb;QACAjB,MAAM,CAACqB,WAAW,CAACJ,GAAG,CAAC;MAC3B,CAAC,MACI,IAAI,gBAAgB,CAACK,IAAI,CAAC,OAAOhB,CAAC,CAAC,EAAE;QACtCN,MAAM,CAACqB,WAAW,CAACE,MAAM,CAACjB,CAAC,CAAC,CAAC;MACjC,CAAC,MACI,IAAI,OAAOA,CAAC,CAACkB,GAAG,IAAI,UAAU,EAAE;QACjCxB,MAAM,CAACyB,KAAK,CAACnB,CAAC,CAACkB,GAAG,CAAC,CAAC,CAAC;MACzB,CAAC,MACI,IAAI,OAAOlB,CAAC,IAAI,QAAQ,EAAE;QAC3B,IAAI,CAACA,CAAC,EAAE;UACJN,MAAM,CAACqB,WAAW,CAAC,MAAM,CAAC;QAC9B,CAAC,MAAM;UACHpB,GAAG,CAAC,IAAIyB,aAAa,CAACpB,CAAC,CAAC,CAAC;QAC7B;MACJ;IACJ;EACJ;EACAL,GAAG,CAAC0B,SAAS,GAAG,UAASC,IAAI,EAAE;IAC3B5B,MAAM,CAACyB,KAAK,CAACG,IAAI,CAAC;EACtB,CAAC;EACD3B,GAAG,CAAC4B,UAAU,GAAG,UAASC,CAAC,EAAE;IACzB,EAAE/B,WAAW;IACb+B,CAAC,CAAC7B,GAAG,CAAC;IACN,EAAEF,WAAW;EACjB,CAAC;EACDE,GAAG,CAAC8B,MAAM,GAAG,YAAW;IACpB9B,GAAG,CAAC7C,EAAE,EAAE4E,GAAG,CAAC,EAAE,EAAEjC,WAAW,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;IACvCE,GAAG,CAACgC,KAAK,CAAC,IAAI,EAAE9B,SAAS,CAAC;EAC9B,CAAC;EACDF,GAAG,CAACiC,MAAM,GAAG,YAAW;IACpB,OAAOlC,MAAM,CAACkC,MAAM,CAAC,CAAC;EAC1B,CAAC;EACDjC,GAAG,CAACkC,QAAQ,GAAG,YAAW;IACtB,MAAM,IAAI3B,KAAK,CAAC,YAAY,CAAC;EACjC,CAAC;EACDP,GAAG,CAACuB,GAAG,GAAG,YAAW;IACjB,OAAOxB,MAAM,CAACwB,GAAG,CAAC,CAAC;EACvB,CAAC;EACDvB,GAAG,CAACmC,MAAM,GAAG,YAAW;IACpB,OAAOpC,MAAM;EACjB,CAAC;EACD,OAAOC,GAAG;AACd;AAEA,SAASoC,UAAUA,CAACC,KAAK,EAAEC,EAAE,EAAE;EAC3B,IAAI7B,YAAY,GAAG4B,KAAK,CAAC5B,YAAY;EACrC,IAAI8B,WAAW,GAAGF,KAAK,CAAC3B,MAAM;EAE9B2B,KAAK,CAAC5B,YAAY,GAAG,YAAU,CAAC,CAAC;EAEjC4B,KAAK,CAAC3B,MAAM,GAAG,UAASV,GAAG,EAAE;IACzBA,GAAG,CAACsC,EAAE,EAAE,MAAM,CAAC;EACnB,CAAC;EAEDD,KAAK,CAACG,UAAU,GAAG,UAASxC,GAAG,EAAE;IAC7BqC,KAAK,CAACI,OAAO,GAAGzC,GAAG,CAACiC,MAAM,CAAC,CAAC;IAC5BjC,GAAG,CAACsC,EAAE,EAAE,SAAS,CAAC;IAClB7B,YAAY,CAACiC,IAAI,CAACL,KAAK,EAAErC,GAAG,CAAC;IAC7BuC,WAAW,CAACG,IAAI,CAACL,KAAK,EAAErC,GAAG,CAAC;IAC5BA,GAAG,CAAC,SAAS,CAAC;EAClB,CAAC;AACL;AAEA,SAAS2C,eAAeA,CAACC,SAAS,EAAE;EAChC,IAAI,OAAOA,SAAS,IAAI,UAAU,EAAE;IAChC,IAAIC,OAAO,GAAGD,SAAS;IACvBA,SAAS,GAAG,SAAAA,CAASE,GAAG,EAAEC,GAAG,EAAE;MAC3B,OAAOD,GAAG,IAAID,OAAO,GAAGA,OAAO,CAACC,GAAG,CAAC,GAAGC,GAAG;IAC9C,CAAC;EACL;EACA,IAAIC,SAAS,GAAGJ,SAAS,CAAC,WAAW,EAAEtF,UAAU,CAACK,EAAE,CAAC;EACrD,IAAI,CAACqF,SAAS,EAAE;IACZ,OAAO,CAAC,CAAC;EACb;EACA,IAAI,OAAOA,SAAS,IAAI,QAAQ,EAAE;IAC9BA,SAAS,GAAG1F,UAAU,CAAC0F,SAAS,CAACC,WAAW,CAAC,CAAC,CAAC;IAC/C,IAAID,SAAS,IAAI,IAAI,EAAE;MACnB,MAAM,IAAIzC,KAAK,CAAC,oBAAoB,CAAC;IACzC;EACJ;EAEAyC,SAAS,CAAC,CAAC,CAAC,GAAGE,aAAa,CAACF,SAAS,CAAC,CAAC,CAAC,CAAC;EAC1CA,SAAS,CAAC,CAAC,CAAC,GAAGE,aAAa,CAACF,SAAS,CAAC,CAAC,CAAC,CAAC;EAE1C,IAAIJ,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC,EAAE;IAC/BI,SAAS,GAAG,CACRG,IAAI,CAACC,GAAG,CAACJ,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CAAC,EACpCG,IAAI,CAACE,GAAG,CAACL,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CAAC,CACvC;EACL;EAEA,IAAIM,MAAM,GAAGV,SAAS,CAAC,QAAQ,CAAC;EAChC,IAAIU,MAAM,EAAE;IACR,IAAI,OAAOA,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,IAAI,QAAQ,EAAE;MACxDA,MAAM,GAAGJ,aAAa,CAACI,MAAM,EAAE,CAAC,CAAC;MACjCA,MAAM,GAAG;QAAEC,IAAI,EAAED,MAAM;QAAEE,GAAG,EAAEF,MAAM;QAAEG,KAAK,EAAEH,MAAM;QAAEI,MAAM,EAAEJ;MAAO,CAAC;IACzE,CAAC,MAAM;MACHA,MAAM,GAAG;QACLC,IAAI,EAAKL,aAAa,CAACI,MAAM,CAACC,IAAI,EAAE,CAAC,CAAC;QACtCC,GAAG,EAAMN,aAAa,CAACI,MAAM,CAACE,GAAG,EAAE,CAAC,CAAC;QACrCC,KAAK,EAAIP,aAAa,CAACI,MAAM,CAACG,KAAK,EAAE,CAAC,CAAC;QACvCC,MAAM,EAAGR,aAAa,CAACI,MAAM,CAACI,MAAM,EAAE,CAAC;MAC3C,CAAC;IACL;IACA,IAAId,SAAS,CAAC,WAAW,CAAC,EAAE;MACxBI,SAAS,CAAC,CAAC,CAAC,IAAIM,MAAM,CAACC,IAAI,GAAGD,MAAM,CAACG,KAAK;MAC1CT,SAAS,CAAC,CAAC,CAAC,IAAIM,MAAM,CAACE,GAAG,GAAGF,MAAM,CAACI,MAAM;IAC9C;EACJ;EACA,OAAO;IAAEV,SAAS,EAAEA,SAAS;IAAEM,MAAM,EAAEA;EAAO,CAAC;AACnD;AAEA,IAAIK,UAAU,GAAG;EACb,aAAa,EAAa,IAAI;EAC9B,YAAY,EAAc,IAAI;EAC9B,cAAc,EAAY,IAAI;EAC9B,kBAAkB,EAAQ,IAAI;EAC9B,WAAW,EAAe,IAAI;EAC9B,gBAAgB,EAAU,IAAI;EAC9B,mBAAmB,EAAO,IAAI;EAC9B,uBAAuB,EAAG,IAAI;EAC9B,SAAS,EAAiB,IAAI;EAC9B,cAAc,EAAY,IAAI;EAC9B,iBAAiB,EAAS,IAAI;EAC9B,qBAAqB,EAAK,IAAI;EAC9B,QAAQ,EAAkB,IAAI;EAC9B,cAAc,EAAY;AAC9B,CAAC;AAED,SAASC,UAAUA,CAACC,GAAG,EAAEC,IAAI,EAAE;EAC3B;EACA,IAAIC,CAAC;EACL,IAAI7G,OAAO,CAAC8G,IAAI,KAAKD,CAAC,GAAG,oBAAoB,CAACE,IAAI,CAACJ,GAAG,CAAC,CAAC,EAAE;IACtDC,IAAI,CAACrH,kBAAkB,CAACoH,GAAG,CAACK,MAAM,CAACH,CAAC,CAAC,CAAC,CAAC,CAAC3D,MAAM,CAAC,CAAC,CAAC;IACjD;EACJ;EAEA,SAAS+D,KAAKA,CAAA,EAAG;IACb,IAAIC,MAAM,CAACC,OAAO,EAAE;MAChB,IAAID,MAAM,CAACC,OAAO,CAACF,KAAK,EAAE;QACtBC,MAAM,CAACC,OAAO,CAACF,KAAK,CAAC,qBAAqB,EAAEN,GAAG,CAAC;MACpD,CAAC,MAAM;QACHO,MAAM,CAACC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAET,GAAG,CAAC;MAClD;IACJ;IACAC,IAAI,CAAC,IAAI,CAAC;EACd;EACA,IAAIS,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;EAC9BD,GAAG,CAACE,IAAI,CAAC,KAAK,EAAEZ,GAAG,EAAE,IAAI,CAAC;EAC1B,IAAInH,gBAAgB,EAAE;IAClB6H,GAAG,CAACG,YAAY,GAAG,aAAa;EACpC;EACAH,GAAG,CAACI,MAAM,GAAG,YAAW;IACpB,IAAIJ,GAAG,CAACK,MAAM,IAAI,GAAG,IAAIL,GAAG,CAACK,MAAM,IAAI,GAAG,EAAE;MACxC,IAAIlI,gBAAgB,EAAE;QAClBoH,IAAI,CAAC,IAAIe,UAAU,CAACN,GAAG,CAACO,QAAQ,CAAC,CAAC;MACtC,CAAC,MAAM;QACHhB,IAAI,CAAC,IAAIM,MAAM,CAACW,OAAO,CAACR,GAAG,CAACS,YAAY,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1D;IACJ,CAAC,MAAM;MACHd,KAAK,CAAC,CAAC;IACX;EACJ,CAAC;EACDI,GAAG,CAACW,OAAO,GAAGf,KAAK;EACnBI,GAAG,CAACY,IAAI,CAAC,IAAI,CAAC;AAClB;AAEA,SAASC,QAAQA,CAACvB,GAAG,EAAEC,IAAI,EAAE;EACzB,IAAIuB,IAAI,GAAG1B,UAAU,CAACE,GAAG,CAAC;EAC1B,IAAIwB,IAAI,EAAE;IACNvB,IAAI,CAACuB,IAAI,CAAC;EACd,CAAC,MAAM;IACHzB,UAAU,CAACC,GAAG,EAAE,UAASlC,IAAI,EAAC;MAC1B,IAAIA,IAAI,IAAI,IAAI,EAAE;QACd,MAAM,IAAIpB,KAAK,CAAC,wBAAwB,GAAGsD,GAAG,CAAC;MACnD,CAAC,MAAM;QACH,IAAIwB,IAAI,GAAG,IAAIzI,OAAO,CAAC+E,IAAI,CAAC;QAC5BgC,UAAU,CAACE,GAAG,CAAC,GAAGwB,IAAI;QACtBvB,IAAI,CAACuB,IAAI,CAAC;MACd;IACJ,CAAC,CAAC;EACN;AACJ;AAEA,IAAIC,WAAW,GAAG,CAAC,CAAC;AAEpB,SAASC,eAAeA,CAAA,EAAG;EACvBD,WAAW,GAAG,CAAC,CAAC;AACpB;AAEA,SAASE,SAASA,CAAC3B,GAAG,EAAE4B,IAAI,EAAE3B,IAAI,EAAEjB,OAAO,EAAE;EACzC,IAAI6C,GAAG,GAAGJ,WAAW,CAACzB,GAAG,CAAC;IAAE8B,OAAO;IAAEC,IAAI;EACzC,IAAIF,GAAG,EAAE;IACL5B,IAAI,CAAC4B,GAAG,CAAC;EACb,CAAC,MAAM;IACHA,GAAG,GAAG,IAAIG,KAAK,CAAC,CAAC;IACjB,IAAI,CAAE,SAAS,CAACxE,IAAI,CAACwC,GAAG,CAAE,EAAE;MACxB6B,GAAG,CAACI,WAAW,GAAG,WAAW;IACjC;IACA,IAAIpJ,gBAAgB,IAAI,CAAE,SAAS,CAAC2E,IAAI,CAACwC,GAAG,CAAE,EAAE;MAC5C;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIkC,GAAG,GAAG,IAAIvB,cAAc,CAAC,CAAC;MAC9BuB,GAAG,CAACpB,MAAM,GAAG,YAAW;QACpBiB,IAAI,GAAGG,GAAG,CAACjB,QAAQ;QACnB,IAAI5H,OAAO,CAAC8I,OAAO,IAAIJ,IAAI,CAACK,IAAI,IAAI,eAAe,EAAE;UACjD;UACA,IAAIC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;UAC7BD,MAAM,CAACvB,MAAM,GAAG,YAAW;YACvB,IAAIyB,GAAG,GAAG,IAAIhC,MAAM,CAACiC,SAAS,CAAC,CAAC,CAACC,eAAe,CAAC,IAAI,CAACC,MAAM,EAAE,eAAe,CAAC;YAC9E,IAAIC,GAAG,GAAGJ,GAAG,CAACK,eAAe;YAC7B,IAAID,GAAG,CAACE,YAAY,CAAC,OAAO,CAAC,IAAIF,GAAG,CAACE,YAAY,CAAC,QAAQ,CAAC,EAAE;cACzD;cACAf,OAAO,GAAGgB,GAAG,CAACC,eAAe,CAAChB,IAAI,CAAC;cACnCiB,KAAK,CAAClB,OAAO,CAAC;YAClB,CAAC,MAAM;cACHa,GAAG,CAACM,YAAY,CAAC,OAAO,EAAErB,IAAI,CAACsB,KAAK,CAAC;cACrCP,GAAG,CAACM,YAAY,CAAC,QAAQ,EAAErB,IAAI,CAACuB,MAAM,CAAC;cACvC,IAAIC,GAAG,GAAG,IAAI7C,MAAM,CAAC8C,aAAa,CAAC,CAAC,CAACC,iBAAiB,CAACX,GAAG,CAAC;cAC3D,IAAIY,OAAO,GAAG,4BAA4B,GAAInK,YAAY,CAACgK,GAAG,CAAE;cAChEJ,KAAK,CAACO,OAAO,CAAC;YAClB;UACJ,CAAC;UACDlB,MAAM,CAACmB,UAAU,CAACzB,IAAI,CAAC;QAC3B,CAAC,MAAM;UACHD,OAAO,GAAGgB,GAAG,CAACC,eAAe,CAAChB,IAAI,CAAC;UACnCiB,KAAK,CAAClB,OAAO,CAAC;QAClB;MACJ,CAAC;MACDI,GAAG,CAACb,OAAO,GAAGoC,QAAQ;MACtBvB,GAAG,CAACtB,IAAI,CAAC,KAAK,EAAEZ,GAAG,EAAE,IAAI,CAAC;MAC1BkC,GAAG,CAACrB,YAAY,GAAG,MAAM;MACzBqB,GAAG,CAACZ,IAAI,CAAC,CAAC;IACd,CAAC,MAAM;MACH0B,KAAK,CAAChD,GAAG,CAAC;IACd;EACJ;EAEA,SAASgD,KAAKA,CAAChD,GAAG,EAAE;IAChB6B,GAAG,CAAC6B,GAAG,GAAG1D,GAAG;IACb,IAAI6B,GAAG,CAAC8B,QAAQ,IAAI,CAACtK,OAAO,CAAC8G,IAAI,EAAE;MAC/B;MACA;MACAyD,OAAO,CAAC/E,IAAI,CAACgD,GAAG,CAAC;IACrB,CAAC,MAAM;MACHA,GAAG,CAACf,MAAM,GAAG8C,OAAO;MACpB/B,GAAG,CAACR,OAAO,GAAGoC,QAAQ;IAC1B;EACJ;EAEA,SAASI,UAAUA,CAAA,EAAG;IAClB,IAAI,CAACjC,IAAI,EAAE;MACPA,IAAI,GAAG;QAAEsB,KAAK,EAAErB,GAAG,CAACqB,KAAK;QAAEC,MAAM,EAAEtB,GAAG,CAACsB;MAAO,CAAC;IACnD;IAEA,IAAIW,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC7CF,MAAM,CAACZ,KAAK,GAAGtB,IAAI,CAACsB,KAAK;IACzBY,MAAM,CAACX,MAAM,GAAGvB,IAAI,CAACuB,MAAM;IAE3B,IAAIc,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;IACjCD,GAAG,CAACE,SAAS,CAACtC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAED,IAAI,CAACsB,KAAK,EAAEtB,IAAI,CAACuB,MAAM,CAAC;IAEjD,IAAIiB,OAAO;IACX,IAAI;MACAA,OAAO,GAAGH,GAAG,CAACI,YAAY,CAAC,CAAC,EAAE,CAAC,EAAEzC,IAAI,CAACsB,KAAK,EAAEtB,IAAI,CAACuB,MAAM,CAAC;IAC7D,CAAC,CAAC,OAAOmB,EAAE,EAAE;MACT;MACAb,QAAQ,CAAC,CAAC;MACV;IACJ,CAAC,SAAS;MACN,IAAI3B,OAAO,EAAE;QACTgB,GAAG,CAACyB,eAAe,CAACzC,OAAO,CAAC;MAChC;IACJ;;IAEA;IACA;IACA;IACA;IACA;IACA;;IAEA,IAAI0C,QAAQ,GAAG,KAAK;MAAEC,GAAG,GAAG/L,YAAY,CAAC,CAAC;MAAEgM,KAAK,GAAGhM,YAAY,CAAC,CAAC;IAClE,IAAIiM,QAAQ,GAAGP,OAAO,CAACtG,IAAI;IAC3B,IAAIxB,CAAC,GAAG,CAAC;IACT,OAAOA,CAAC,GAAGqI,QAAQ,CAACpI,MAAM,EAAE;MACxBkI,GAAG,CAACG,SAAS,CAACD,QAAQ,CAACrI,CAAC,EAAE,CAAC,CAAC;MAC5BmI,GAAG,CAACG,SAAS,CAACD,QAAQ,CAACrI,CAAC,EAAE,CAAC,CAAC;MAC5BmI,GAAG,CAACG,SAAS,CAACD,QAAQ,CAACrI,CAAC,EAAE,CAAC,CAAC;MAC5B,IAAIuI,CAAC,GAAGF,QAAQ,CAACrI,CAAC,EAAE,CAAC;MACrB,IAAIuI,CAAC,GAAG,GAAG,EAAE;QACTL,QAAQ,GAAG,IAAI;MACnB;MACAE,KAAK,CAACE,SAAS,CAACC,CAAC,CAAC;IACtB;IAEA,IAAIL,QAAQ,IAAIxF,OAAO,CAAC8F,OAAO,EAAE;MAC7BjD,GAAG,GAAG,IAAIkD,WAAW,CAACnD,IAAI,CAACsB,KAAK,EAAEtB,IAAI,CAACuB,MAAM,EAAEsB,GAAG,EAAEC,KAAK,CAAC;IAC9D,CAAC,MAAM;MACH;MACA,IAAI5G,IAAI,GAAGgG,MAAM,CAACkB,SAAS,CAAC,YAAY,EAAEhG,OAAO,CAACiG,WAAW,CAAC;MAC9DnH,IAAI,GAAGA,IAAI,CAACuC,MAAM,CAACvC,IAAI,CAACT,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;MAEhD,IAAIiB,MAAM,GAAG5F,YAAY,CAAC,CAAC;MAC3B4F,MAAM,CAAC4G,WAAW,CAACpH,IAAI,CAAC;MACxB+D,GAAG,GAAG,IAAIsD,YAAY,CAAC7G,MAAM,CAAC;IAClC;IAEA2B,IAAI,CAACwB,WAAW,CAACzB,GAAG,CAAC,GAAG6B,GAAG,CAAC;EAChC;EAEA,SAAS4B,QAAQA,CAAA,EAAG;IAChBxD,IAAI,CAACwB,WAAW,CAACzB,GAAG,CAAC,GAAG,OAAO,CAAC;EACpC;EAEA,SAAS4D,OAAOA,CAAA,EAAG;IACf,IAAIhC,IAAI,EAAE;MACN,IAAIe,GAAG,GAAIZ,IAAI,IAAIA,IAAI,CAACK,IAAI,KAAK,eAAe,IAC9C,yBAAyB,CAAC5E,IAAI,CAAC,IAAI,CAACkG,GAAG,CAAC0B,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CACzD;MAED,IAAIC,OAAO,GAAGzD,IAAI,CAACsB,KAAK,IAAIrB,GAAG,CAACqB,KAAK,IAAItB,IAAI,CAACuB,MAAM,IAAItB,GAAG,CAACsB,MAAM;;MAElE;MACA;MACA,IAAI,CAACR,GAAG,IAAI0C,OAAO,EAAE;QACjBzD,IAAI,GAAG,IAAI;MACf;IACJ;IACA,IAAI,CAACA,IAAI,IAAIG,IAAI,IAAI,iBAAiB,CAACvE,IAAI,CAACuE,IAAI,CAACK,IAAI,CAAC,EAAE;MACpD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC7BD,MAAM,CAACvB,MAAM,GAAG,YAAW;QACvB,IAAI;UACA,IAAIe,GAAG,GAAG,IAAIsD,YAAY,CAACzM,YAAY,CAAC,IAAIsI,UAAU,CAAC,IAAI,CAAC0B,MAAM,CAAC,CAAC,CAAC;UACrEI,GAAG,CAACyB,eAAe,CAACzC,OAAO,CAAC;UAC5B7B,IAAI,CAACwB,WAAW,CAACzB,GAAG,CAAC,GAAG6B,GAAG,CAAC;QAChC,CAAC,CAAC,OAAOyC,EAAE,EAAE;UACT;UACA;UACA;UACA;UACAT,UAAU,CAAC,CAAC;QAChB;MACJ,CAAC;MACDxB,MAAM,CAACiD,iBAAiB,CAACvD,IAAI,CAAC;IAClC,CAAC,MAAM;MACH8B,UAAU,CAAC,CAAC;IAChB;EACJ;AACJ;AAEA,SAAS0B,UAAUA,CAACC,OAAO,EAAE;EACzB,OAAO,UAASC,IAAI,EAAEC,QAAQ,EAAE;IAC5B,IAAIC,CAAC,GAAGF,IAAI,CAAClJ,MAAM;MAAED,CAAC,GAAGqJ,CAAC;IAC1B,IAAIA,CAAC,KAAK,CAAC,EAAE;MACT,OAAOD,QAAQ,CAAC,CAAC;IACrB;IACA,SAASE,IAAIA,CAAA,EAAG;MACZ,IAAI,EAAED,CAAC,KAAK,CAAC,EAAE;QACXD,QAAQ,CAAC,CAAC;MACd;IACJ;IACA,OAAOpJ,CAAC,EAAE,GAAG,CAAC,EAAE;MACZkJ,OAAO,CAACC,IAAI,CAACnJ,CAAC,CAAC,EAAEsJ,IAAI,CAAC;IAC1B;EACJ,CAAC;AACL;AAEA,IAAIC,SAAS,GAAGN,UAAU,CAAChE,QAAQ,CAAC;AACpC,IAAIuE,UAAU,GAAG,SAAAA,CAASC,MAAM,EAAEL,QAAQ,EAAE1G,OAAO,EAAE;EACjDA,OAAO,GAAGgH,MAAM,CAACC,MAAM,CAAC;IACpBhB,WAAW,EAAG,IAAI;IAClBH,OAAO,EAAO;EAClB,CAAC,EAAE9F,OAAO,CAAC;EACX,IAAIyG,IAAI,GAAGO,MAAM,CAACE,IAAI,CAACH,MAAM,CAAC;IAAEJ,CAAC,GAAGF,IAAI,CAAClJ,MAAM;EAC/C,IAAIoJ,CAAC,KAAK,CAAC,EAAE;IACT,OAAOD,QAAQ,CAAC,CAAC;EACrB;EACA,SAASE,IAAIA,CAAA,EAAG;IACZ,IAAI,EAAED,CAAC,KAAK,CAAC,EAAE;MACXD,QAAQ,CAAC,CAAC;IACd;EACJ;EACAD,IAAI,CAACU,OAAO,CAAC,UAASnG,GAAG,EAAC;IACtB2B,SAAS,CAAC3B,GAAG,EAAE+F,MAAM,CAAC/F,GAAG,CAAC,EAAE4F,IAAI,EAAE5G,OAAO,CAAC;EAC9C,CAAC,CAAC;AACN,CAAC;AAED,IAAIoH,WAAW,GAAG,SAASA,WAAWA,CAAEpH,OAAO,EAAE;EAC7C,IAAIqH,IAAI,GAAG,IAAI;EACf,IAAIlK,GAAG,GAAGH,UAAU,CAAC,CAAC;EACtB,IAAIsK,QAAQ,GAAG,CAAC;EAChB,IAAIC,OAAO,GAAG,EAAE;EAEhB,SAASxH,SAASA,CAACyH,IAAI,EAAEC,MAAM,EAAE;IAC7B,OAAQzH,OAAO,IAAIA,OAAO,CAACwH,IAAI,CAAC,IAAI,IAAI,GAAIxH,OAAO,CAACwH,IAAI,CAAC,GAAGC,MAAM;EACtE;EAEAJ,IAAI,CAACtH,SAAS,GAAGA,SAAS;EAE1BsH,IAAI,CAACK,MAAM,GAAG,UAASlI,KAAK,EAAE;IAC1B,IAAI+H,OAAO,CAAClJ,OAAO,CAACmB,KAAK,CAAC,GAAG,CAAC,EAAE;MAC5BD,UAAU,CAACC,KAAK,EAAE,EAAE8H,QAAQ,CAAC;MAC7BC,OAAO,CAACI,IAAI,CAACnI,KAAK,CAAC;IACvB;IACA,OAAOA,KAAK;EAChB,CAAC;EAED6H,IAAI,CAACO,KAAK,GAAG,EAAE;EAEfP,IAAI,CAACQ,KAAK,GAAG,CAAC,CAAC;EACfR,IAAI,CAACS,QAAQ,GAAG,CAAC,CAAC;EAClBT,IAAI,CAACU,MAAM,GAAG,CAAC,CAAC;EAChBV,IAAI,CAACW,kBAAkB,GAAG,CAAC,CAAC,CAAC,CAAC;EAC9BX,IAAI,CAACY,kBAAkB,GAAG,CAAC,CAAC,CAAC,CAAC;EAC9BZ,IAAI,CAACa,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;EACpBb,IAAI,CAACc,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;;EAEpB,IAAIC,OAAO,GAAGf,IAAI,CAACK,MAAM,CAAC,IAAIW,UAAU,CAAC,CAAC,CAAC;EAC3C,IAAIC,QAAQ,GAAGjB,IAAI,CAACK,MAAM,CAAC,IAAIa,WAAW,CAAC,CAAC,CAAC;EAE7C,IAAIxI,SAAS,CAAC,WAAW,CAAC,EAAE;IACxB,IAAIyI,QAAQ,GAAG,CAAC,CAAC;IACjBA,QAAQ,CAACC,UAAU,GAAG,IAAI7J,aAAa,CAAC;MAAE8J,KAAK,EAAE,CAC7C,IAAIC,SAAS,CAAC,IAAI,CAAC,EAAEtB,IAAI,CAACK,MAAM,CAAC,IAAI9I,aAAa,CAAC;QAC/CgK,CAAC,EAAEC,CAAC,CAAC,YAAY,CAAC;QAClBC,EAAE,EAAE,IAAIH,SAAS,CAAC,cAAc;MACpC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IACJP,OAAO,CAACW,KAAK,CAACL,KAAK,GAAG,IAAI9J,aAAa,CAAC4J,QAAQ,CAAC;EACrD;EAEAJ,OAAO,CAACY,QAAQ,CAACV,QAAQ,CAAC;EAE1B,IAAIW,IAAI,GAAG5B,IAAI,CAACK,MAAM,CAAC,IAAI9I,aAAa,CAAC;IACrCsK,QAAQ,EAAG,IAAIP,SAAS,CAAC5I,SAAS,CAAC,UAAU,EAAE,wBAAwB,CAAC,EAAE,IAAI,CAAC;IAAE;IACjFoJ,KAAK,EAAM,IAAIR,SAAS,CAAC5I,SAAS,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC;IACtDqJ,MAAM,EAAK,IAAIT,SAAS,CAAC5I,SAAS,CAAC,QAAQ,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC;IACvDsJ,OAAO,EAAI,IAAIV,SAAS,CAAC5I,SAAS,CAAC,SAAS,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC;IACxDuJ,QAAQ,EAAG,IAAIX,SAAS,CAAC5I,SAAS,CAAC,UAAU,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC;IACzDwJ,OAAO,EAAI,IAAIZ,SAAS,CAAC5I,SAAS,CAAC,SAAS,EAAE,wBAAwB,CAAC,EAAE,IAAI,CAAC;IAC9EyJ,YAAY,EAAGzJ,SAAS,CAAC,MAAM,EAAE,IAAI0J,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,CAAC;EAEHpC,IAAI,CAACqC,OAAO,GAAG,UAAS1J,OAAO,EAAE;IAC7B,IAAI2J,YAAY,GAAI7J,eAAe,CAAC,UAAS0H,IAAI,EAAEC,MAAM,EAAC;MACtD,OAAQzH,OAAO,IAAIA,OAAO,CAACwH,IAAI,CAAC,IAAI,IAAI,GAAIxH,OAAO,CAACwH,IAAI,CAAC,GAAGC,MAAM;IACtE,CAAC,CAAC;IACF,IAAItH,SAAS,GAAGwJ,YAAY,CAACxJ,SAAS;IACtC,IAAIM,MAAM,GAAMkJ,YAAY,CAAClJ,MAAM;IACnC,IAAImJ,YAAY,GAAIzJ,SAAS,CAAC,CAAC,CAAC;IAChC,IAAI0J,aAAa,GAAG1J,SAAS,CAAC,CAAC,CAAC;IAChC,IAAIM,MAAM,EAAE;MACRmJ,YAAY,IAAInJ,MAAM,CAACC,IAAI,GAAGD,MAAM,CAACG,KAAK;MAC1CiJ,aAAa,IAAIpJ,MAAM,CAACE,GAAG,GAAGF,MAAM,CAACI,MAAM;IAC/C;IACA,IAAIiJ,OAAO,GAAG,IAAIC,SAAS,CAAC/M,UAAU,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;IACrD,IAAI+L,KAAK,GAAG;MACRiB,QAAQ,EAAG3C,IAAI,CAACK,MAAM,CAACoC,OAAO,CAAC;MAC/BG,MAAM,EAAK3B,QAAQ;MACnB4B,QAAQ,EAAG,CAAE,CAAC,EAAE,CAAC,EAAE/J,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC;IACjD,CAAC;IACD,IAAIgK,IAAI,GAAG,IAAIC,OAAO,CAAC/C,IAAI,EAAE0B,KAAK,CAAC;IACnCoB,IAAI,CAACE,QAAQ,GAAGP,OAAO;IACvBxB,QAAQ,CAACoB,OAAO,CAACrC,IAAI,CAACK,MAAM,CAACyC,IAAI,CAAC,CAAC;;IAEnC;IACA;IACAA,IAAI,CAACG,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAEnK,SAAS,CAAC,CAAC,CAAC,CAAC;IAE5C,IAAIM,MAAM,EAAE;MACR0J,IAAI,CAACI,SAAS,CAAC9J,MAAM,CAACC,IAAI,EAAED,MAAM,CAACE,GAAG,CAAC;MACvC;MACAwJ,IAAI,CAACK,IAAI,CAAC,CAAC,EAAE,CAAC,EAAEZ,YAAY,EAAEC,aAAa,CAAC;MAC5CM,IAAI,CAACM,IAAI,CAAC,CAAC;IACf;IAEApD,IAAI,CAACO,KAAK,CAACD,IAAI,CAACwC,IAAI,CAAC;IACrB,OAAOA,IAAI;EACf,CAAC;EAED9C,IAAI,CAACxJ,MAAM,GAAG,YAAW;IACrB,IAAIP,CAAC;IACL;IACAH,GAAG,CAAC,UAAU,EAAE7C,EAAE,EAAE,uBAAuB,EAAEA,EAAE,EAAEA,EAAE,CAAC;;IAEpD;IACA,KAAKgD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiK,OAAO,CAAChK,MAAM,EAAE,EAAED,CAAC,EAAE;MACjCiK,OAAO,CAACjK,CAAC,CAAC,CAACqC,UAAU,CAACxC,GAAG,CAAC;MAC1BA,GAAG,CAAC7C,EAAE,EAAEA,EAAE,CAAC;IACf;;IAEA;IACA,IAAIoQ,UAAU,GAAGvN,GAAG,CAACiC,MAAM,CAAC,CAAC;IAC7BjC,GAAG,CAAC,MAAM,EAAE7C,EAAE,EAAE,CAAC,EAAE,GAAG,EAAEiN,OAAO,CAAChK,MAAM,GAAG,CAAC,EAAEjD,EAAE,CAAC;IAC/C6C,GAAG,CAAC,qBAAqB,EAAE7C,EAAE,CAAC;IAC9B,KAAKgD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiK,OAAO,CAAChK,MAAM,EAAE,EAAED,CAAC,EAAE;MACjCH,GAAG,CAACwN,OAAO,CAACpD,OAAO,CAACjK,CAAC,CAAC,CAACsC,OAAO,EAAE,EAAE,CAAC,EAAE,WAAW,EAAEtF,EAAE,CAAC;IACzD;IACA6C,GAAG,CAAC7C,EAAE,CAAC;;IAEP;IACA6C,GAAG,CAAC,SAAS,EAAE7C,EAAE,CAAC;IAClB6C,GAAG,CAAC,IAAIyB,aAAa,CAAC;MAClBgM,IAAI,EAAErD,OAAO,CAAChK,MAAM,GAAG,CAAC;MACxBsN,IAAI,EAAEzC,OAAO;MACb0C,IAAI,EAAE7B;IACV,CAAC,CAAC,EAAE3O,EAAE,EAAEA,EAAE,CAAC;;IAEX;IACA6C,GAAG,CAAC,WAAW,EAAE7C,EAAE,EAAEoQ,UAAU,EAAEpQ,EAAE,CAAC;IACpC6C,GAAG,CAAC,OAAO,EAAE7C,EAAE,CAAC;IAEhB,OAAO6C,GAAG,CAACmC,MAAM,CAAC,CAAC,CAACF,MAAM,CAAC,CAAC,CAAC;EACjC,CAAC;EAEDiI,IAAI,CAACR,SAAS,GAAGA,SAAS;EAC1BQ,IAAI,CAACP,UAAU,GAAGA,UAAU;AAChC,CAAC;AAEDM,WAAW,CAAC2D,SAAS,CAACC,OAAO,GAAG,SAASA,OAAOA,CAAEhK,GAAG,EAAE;EACnD,IAAIwB,IAAI,GAAG,IAAI,CAACqF,KAAK,CAAC7G,GAAG,CAAC;EAC1B,IAAI,CAACwB,IAAI,EAAE;IACPA,IAAI,GAAG1B,UAAU,CAACE,GAAG,CAAC;IACtB,IAAI,CAACwB,IAAI,EAAE;MACP,MAAM,IAAI9E,KAAK,CAAC,OAAO,GAAGsD,GAAG,GAAG,sBAAsB,CAAC;IAC3D;IACA,IAAIwB,IAAI,KAAK,IAAI,EAAE;MACfA,IAAI,GAAG,IAAI,CAACkF,MAAM,CAAC,IAAIuD,eAAe,CAACjK,GAAG,CAAC,CAAC;IAChD,CAAC,MAAM;MACHwB,IAAI,GAAG,IAAI,CAACkF,MAAM,CAAC,IAAIwD,OAAO,CAAC,IAAI,EAAE1I,IAAI,CAAC,CAAC;IAC/C;IACA,IAAI,CAACqF,KAAK,CAAC7G,GAAG,CAAC,GAAGwB,IAAI;EAC1B;EACA,OAAOA,IAAI;AACf,CAAC;AAED4E,WAAW,CAAC2D,SAAS,CAACI,UAAU,GAAG,SAASA,UAAUA,CAAEC,IAAI,EAAEjB,IAAI,EAAEkB,WAAW,EAAE;EAC7E,IAAIC,OAAO,GAAG,IAAI,CAACxD,QAAQ,CAACsD,IAAI,CAAC3L,EAAE,CAAC;EACpC,IAAI,CAAC6L,OAAO,EAAE;IACVA,OAAO,GAAG,IAAI,CAAC5D,MAAM,CAAC,IAAI6D,UAAU,CAACH,IAAI,EAAEjB,IAAI,EAAEkB,WAAW,CAAC,CAAC;IAC9D,IAAI,CAACvD,QAAQ,CAACsD,IAAI,CAAC3L,EAAE,CAAC,GAAG6L,OAAO;EACpC;EACA,OAAOA,OAAO;AAClB,CAAC;AAEDlE,WAAW,CAAC2D,SAAS,CAACS,QAAQ,GAAG,SAASA,QAAQA,CAAExK,GAAG,EAAE;EACrD,IAAI6B,GAAG,GAAG,IAAI,CAACkF,MAAM,CAAC/G,GAAG,CAAC;EAC1B,IAAI,CAAC6B,GAAG,EAAE;IACNA,GAAG,GAAGJ,WAAW,CAACzB,GAAG,CAAC;IACtB,IAAI,CAAC6B,GAAG,EAAE;MACN,MAAM,IAAInF,KAAK,CAAC,QAAQ,GAAGsD,GAAG,GAAG,sBAAsB,CAAC;IAC5D;IACA,IAAI6B,GAAG,KAAK,OAAO,EAAE;MACjB,OAAO,IAAI;IACf;IACAA,GAAG,GAAG,IAAI,CAACkF,MAAM,CAAC/G,GAAG,CAAC,GAAG,IAAI,CAAC0G,MAAM,CAAC7E,GAAG,CAAC4I,QAAQ,CAAC,IAAI,CAAC,CAAC;EAC5D;EACA,OAAO5I,GAAG;AACd,CAAC;AAEDuE,WAAW,CAAC2D,SAAS,CAACW,YAAY,GAAG,SAASA,YAAYA,CAAEC,OAAO,EAAEC,SAAS,EAAE;EAC5E,IAAInM,EAAE,GAAGoM,UAAU,CAACF,OAAO,CAAC,CAACvN,OAAO,CAAC,CAAC,CAAC;EACvCuN,OAAO,GAAGE,UAAU,CAACpM,EAAE,CAAC;EACxBA,EAAE,IAAImM,SAAS,GAAG,GAAG,GAAG,GAAG;EAC3B,IAAIE,KAAK,GAAG,IAAI,CAACC,eAAe,KAAK,IAAI,CAACA,eAAe,GAAG,CAAC,CAAC,CAAC;EAC/D,IAAIC,EAAE,GAAGF,KAAK,CAACrM,EAAE,CAAC;EAClB,IAAI,CAACuM,EAAE,EAAE;IACL,IAAIjD,KAAK,GAAG;MACRkD,IAAI,EAAEpD,CAAC,CAAC,WAAW;IACvB,CAAC;IACD,IAAI+C,SAAS,EAAE;MACX7C,KAAK,CAACmD,EAAE,GAAGP,OAAO;IACtB,CAAC,MAAM;MACH5C,KAAK,CAACoD,EAAE,GAAGR,OAAO;IACtB;IACAK,EAAE,GAAG,IAAI,CAACtE,MAAM,CAAC,IAAI9I,aAAa,CAACmK,KAAK,CAAC,CAAC;IAC1CiD,EAAE,CAACI,aAAa,GAAGvD,CAAC,CAAC,IAAI,GAAI,EAAEtO,gBAAiB,CAAC;IACjDuR,KAAK,CAACrM,EAAE,CAAC,GAAGuM,EAAE;EAClB;EACA,OAAOA,EAAE;AACb,CAAC;AAED5E,WAAW,CAAC2D,SAAS,CAACsB,IAAI,GAAG,SAASA,IAAIA,CAAEtD,KAAK,EAAE;EAC/C,OAAO,IAAInK,aAAa,CAACmK,KAAK,CAAC;AACnC,CAAC;AAED3B,WAAW,CAAC2D,SAAS,CAACvD,IAAI,GAAG,SAASA,IAAIA,CAAE8E,GAAG,EAAE;EAC7C,OAAOzD,CAAC,CAACyD,GAAG,CAAC;AACjB,CAAC;AAEDlF,WAAW,CAAC2D,SAAS,CAACzL,MAAM,GAAG,SAASA,MAAMA,CAAEyJ,KAAK,EAAEe,OAAO,EAAE;EAC5D,OAAO,IAAIC,SAAS,CAACD,OAAO,EAAEf,KAAK,CAAC;AACxC,CAAC;;AAED;;AAEA,SAAS7J,GAAGA,CAACoN,GAAG,EAAEC,GAAG,EAAEC,EAAE,EAAE;EACvB,OAAOF,GAAG,CAAC/O,MAAM,GAAGgP,GAAG,EAAE;IACrBD,GAAG,GAAGE,EAAE,GAAGF,GAAG;EAClB;EACA,OAAOA,GAAG;AACd;AAEA,SAAS3B,OAAOA,CAAChE,CAAC,EAAE4F,GAAG,EAAE;EACrB,OAAOrN,GAAG,CAACT,MAAM,CAACkI,CAAC,CAAC,EAAE4F,GAAG,EAAE,GAAG,CAAC;AACnC;AAEA,SAASE,cAAcA,CAACC,GAAG,EAAEzM,GAAG,EAAE;EAC9B,OAAO+G,MAAM,CAAC+D,SAAS,CAAC0B,cAAc,CAAC5M,IAAI,CAAC6M,GAAG,EAAEzM,GAAG,CAAC;AACzD;AAEA,IAAInC,OAAO,GAAG6O,KAAK,CAAC7O,OAAO,IAAI,UAAS4O,GAAG,EAAE;EACzC,OAAOA,GAAG,YAAYC,KAAK;AAC/B,CAAC;AAED,SAAS3O,MAAMA,CAAC0O,GAAG,EAAE;EACjB,OAAOA,GAAG,YAAYjD,IAAI;AAC9B;AAEA,SAAS1L,WAAWA,CAAC8H,CAAC,EAAE1I,GAAG,EAAE;EACzBA,GAAG,CAAC,GAAG,CAAC;EACR,IAAI0I,CAAC,CAACtI,MAAM,GAAG,CAAC,EAAE;IACdJ,GAAG,CAAC4B,UAAU,CAAC,YAAU;MACrB,KAAK,IAAIzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuI,CAAC,CAACtI,MAAM,EAAE,EAAED,CAAC,EAAE;QAC/B,IAAIA,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;UACtBH,GAAG,CAAC8B,MAAM,CAAC4G,CAAC,CAACvI,CAAC,CAAC,CAAC;QACpB,CAAC,MAAM;UACHH,GAAG,CAAC,GAAG,EAAE0I,CAAC,CAACvI,CAAC,CAAC,CAAC;QAClB;MACJ;IACJ,CAAC,CAAC;IACF;EACJ;EACAH,GAAG,CAAC,IAAI,CAAC;AACb;AAEA,SAASc,UAAUA,CAAC2O,IAAI,EAAEzP,GAAG,EAAE;EAC3BA,GAAG,CAAC,KAAK,EACLwN,OAAO,CAACiC,IAAI,CAACC,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,EACjClC,OAAO,CAACiC,IAAI,CAACE,WAAW,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAClCnC,OAAO,CAACiC,IAAI,CAACG,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,EAC7BpC,OAAO,CAACiC,IAAI,CAACI,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,EAC9BrC,OAAO,CAACiC,IAAI,CAACK,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,EAChCtC,OAAO,CAACiC,IAAI,CAACM,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,EAChC,IAAI,CAAC;AACb;AAEA,SAASC,KAAKA,CAACC,EAAE,EAAE;EACf,OAAOA,EAAE,IAAI,EAAE,GAAC,IAAI,CAAC;AACzB;AAEA,SAASC,KAAKA,CAACC,EAAE,EAAE;EACf,OAAOH,KAAK,CAACG,EAAE,GAAG,EAAE,CAAC;AACzB;AAEA,SAASC,KAAKA,CAACC,IAAI,EAAG;EAClB,OAAOA,IAAI,GAAG,EAAE;AACpB;AAGA,SAASnN,aAAaA,CAAC7C,CAAC,EAAE0C,GAAG,EAAE;EAC3B,IAAI,OAAO1C,CAAC,IAAI,QAAQ,EAAE;IACtB,OAAOA,CAAC;EACZ;EACA,IAAI,OAAOA,CAAC,IAAI,QAAQ,EAAE;IACtB,IAAI0D,CAAC;IACLA,CAAC,GAAG,mCAAmC,CAACE,IAAI,CAAC5D,CAAC,CAAC;IAC/C,IAAI0D,CAAC,EAAE;MACH,IAAI/C,GAAG,GAAG0N,UAAU,CAAC3K,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,IAAI,CAAChD,KAAK,CAACC,GAAG,CAAC,EAAE;QACb,IAAI+C,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;UACd,OAAO/C,GAAG;QACd;QACA,OAAO;UACH,IAAI,EAAEgP,KAAK;UACX,IAAI,EAAEE,KAAK;UACX,IAAI,EAAEE;QACV,CAAC,CAACrM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC/C,GAAG,CAAC;MAChB;IACJ;EACJ;EACA,IAAI+B,GAAG,IAAI,IAAI,EAAE;IACb,OAAOA,GAAG;EACd;EACA,MAAM,IAAIxC,KAAK,CAAC,oBAAoB,GAAGF,CAAC,CAAC;AAC7C;;AAEA;;AAEA,IAAIG,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI,CAAC,CAAC;AAEtCA,QAAQ,CAACoN,SAAS,CAACnN,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAI,CAAC,CAAC;AAE7D,IAAI+K,SAAS,GAAI,UAAUhL,QAAQ,EAAE;EACjC,SAASgL,SAASA,CAACnJ,KAAK,EAAEiO,OAAO,EAAE;IAC/B9P,QAAQ,CAACkC,IAAI,CAAC,IAAI,CAAC;IACnB,IAAI,CAACL,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACiO,OAAO,GAAGC,OAAO,CAACD,OAAO,CAAC;EACnC;EAEA,IAAK9P,QAAQ,EAAGgL,SAAS,CAACgF,SAAS,GAAGhQ,QAAQ;EAC9CgL,SAAS,CAACoC,SAAS,GAAG/D,MAAM,CAAC4G,MAAM,CAAEjQ,QAAQ,IAAIA,QAAQ,CAACoN,SAAU,CAAC;EACrEpC,SAAS,CAACoC,SAAS,CAAC8C,WAAW,GAAGlF,SAAS;EAE3CA,SAAS,CAACoC,SAAS,CAAClN,MAAM,GAAG,SAASA,MAAMA,CAAEV,GAAG,EAAE;IAC/C,IAAI2Q,GAAG,GAAG,IAAI,CAACtO,KAAK;IACpB,IAAI,IAAI,CAACiO,OAAO,EAAE;MACdK,GAAG,GAAG3T,GAAG,GAAGD,aAAa,CAAC4T,GAAG,CAAC;MAC9BA,GAAG,GAAGA,GAAG,CAACxP,OAAO,CAAC,aAAa,EAAE,MAAM,CAAC;MACxCnB,GAAG,CAAC,GAAG,EAAE2Q,GAAG,EAAE,GAAG,CAAC;IACtB,CAAC,MAAM;MACH;MACA;MACA;MACA;MACA,IAAIhP,IAAI,GAAG,CAAE,EAAE,CAAE,CAAC,CAAC;MACnB,KAAK,IAAIxB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwQ,GAAG,CAACvQ,MAAM,EAAE,EAAED,CAAC,EAAE;QACjC,IAAIyQ,IAAI,GAAGD,GAAG,CAACE,UAAU,CAAC1Q,CAAC,CAAC,GAAG,IAAI;QACnC,IAAIyQ,IAAI,IAAI,EAAE,IAAIA,IAAI,IAAI,EAAE,IAAIA,IAAI,IAAI,EAAE,EAAE;UACxC;UACAjP,IAAI,CAAC6I,IAAI,CAAC,EAAE,CAAC;QACjB;QACA7I,IAAI,CAAC6I,IAAI,CAACoG,IAAI,CAAC;MACnB;MACAjP,IAAI,CAAC6I,IAAI,CAAC,EAAE,CAAC,CAAC,CAAE;MAChBxK,GAAG,CAAC0B,SAAS,CAACC,IAAI,CAAC;IACvB;EACJ,CAAC;EAED6J,SAAS,CAACoC,SAAS,CAAC1L,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;IAChD,OAAO,IAAI,CAACG,KAAK;EACrB,CAAC;EAED,OAAOmJ,SAAS;AACpB,CAAC,CAAChL,QAAQ,CAAE;AAEZ,IAAIsQ,YAAY,GAAI,UAAUtF,SAAS,EAAE;EACrC,SAASsF,YAAYA,CAACzO,KAAK,EAAE;IACzBmJ,SAAS,CAAC9I,IAAI,CAAC,IAAI,EAAEL,KAAK,CAAC;IAC3B,IAAI,CAACA,KAAK,GAAGA,KAAK;EACtB;EAEA,IAAKmJ,SAAS,EAAGsF,YAAY,CAACN,SAAS,GAAGhF,SAAS;EACnDsF,YAAY,CAAClD,SAAS,GAAG/D,MAAM,CAAC4G,MAAM,CAAEjF,SAAS,IAAIA,SAAS,CAACoC,SAAU,CAAC;EAC1EkD,YAAY,CAAClD,SAAS,CAAC8C,WAAW,GAAGI,YAAY;EAEjDA,YAAY,CAAClD,SAAS,CAAClN,MAAM,GAAG,SAASA,MAAMA,CAAEV,GAAG,EAAE;IAClD,IAAI+Q,MAAM,GAAG,IAAI;IAEjB/Q,GAAG,CAAC,GAAG,CAAC;IACR,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACkC,KAAK,CAACjC,MAAM,EAAE,EAAED,CAAC,EAAE;MACxCH,GAAG,CAACwN,OAAO,CAACuD,MAAM,CAAC1O,KAAK,CAACwO,UAAU,CAAC1Q,CAAC,CAAC,CAAC+B,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5D;IACAlC,GAAG,CAAC,GAAG,CAAC;EACZ,CAAC;EAED,OAAO8Q,YAAY;AACvB,CAAC,CAACtF,SAAS,CAAE;;AAEb;AACA,IAAIwF,OAAO,GAAI,UAAUxQ,QAAQ,EAAE;EAC/B,SAASwQ,OAAOA,CAAC3G,IAAI,EAAE;IACnB7J,QAAQ,CAACkC,IAAI,CAAC,IAAI,CAAC;IACnB,IAAI,CAAC2H,IAAI,GAAGA,IAAI;EACpB;EAEA,IAAK7J,QAAQ,EAAGwQ,OAAO,CAACR,SAAS,GAAGhQ,QAAQ;EAC5CwQ,OAAO,CAACpD,SAAS,GAAG/D,MAAM,CAAC4G,MAAM,CAAEjQ,QAAQ,IAAIA,QAAQ,CAACoN,SAAU,CAAC;EACnEoD,OAAO,CAACpD,SAAS,CAAC8C,WAAW,GAAGM,OAAO;EAEvCA,OAAO,CAACzP,GAAG,GAAG,SAASA,GAAGA,CAAE8I,IAAI,EAAE;IAC9B,OAAOqB,CAAC,CAACrB,IAAI,CAAC;EAClB,CAAC;EAED2G,OAAO,CAACpD,SAAS,CAAClN,MAAM,GAAG,SAASA,MAAMA,CAAEV,GAAG,EAAE;IAC7CA,GAAG,CAAC,GAAG,GAAG,IAAI,CAACiR,MAAM,CAAC,CAAC,CAAC;EAC5B,CAAC;EAEDD,OAAO,CAACpD,SAAS,CAACqD,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAI;IAC1C,OAAO,IAAI,CAAC5G,IAAI,CAAClJ,OAAO,CAAC,eAAe,EAAE,UAAS+P,CAAC,EAAC;MACjD,OAAO,GAAG,GAAG1D,OAAO,CAAC0D,CAAC,CAACL,UAAU,CAAC,CAAC,CAAC,CAAC3O,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACzD,CAAC,CAAC;EACN,CAAC;EAED8O,OAAO,CAACpD,SAAS,CAAC1L,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;IAC9C,OAAO,IAAI,CAACmI,IAAI;EACpB,CAAC;EAED,OAAO2G,OAAO;AAClB,CAAC,CAACxQ,QAAQ,CAAE;AAEZ,SAASkL,CAACA,CAACrB,IAAI,EAAE;EACb,OAAO,IAAI2G,OAAO,CAAC3G,IAAI,CAAC;AAC5B;;AAEA;;AAEA,IAAI5I,aAAa,GAAI,UAAUjB,QAAQ,EAAE;EACrC,SAASiB,aAAaA,CAACmK,KAAK,EAAE;IAC1BpL,QAAQ,CAACkC,IAAI,CAAC,IAAI,CAAC;IACnB,IAAI,CAACkJ,KAAK,GAAGA,KAAK;EACtB;EAEA,IAAKpL,QAAQ,EAAGiB,aAAa,CAAC+O,SAAS,GAAGhQ,QAAQ;EAClDiB,aAAa,CAACmM,SAAS,GAAG/D,MAAM,CAAC4G,MAAM,CAAEjQ,QAAQ,IAAIA,QAAQ,CAACoN,SAAU,CAAC;EACzEnM,aAAa,CAACmM,SAAS,CAAC8C,WAAW,GAAGjP,aAAa;EAEnDA,aAAa,CAACmM,SAAS,CAAClN,MAAM,GAAG,SAASA,MAAMA,CAAEV,GAAG,EAAE;IACnD,IAAI4L,KAAK,GAAG,IAAI,CAACA,KAAK;MAAEuF,KAAK,GAAG,IAAI;IACpCnR,GAAG,CAAC,IAAI,CAAC;IACTA,GAAG,CAAC4B,UAAU,CAAC,YAAU;MACrB,KAAK,IAAIzB,CAAC,IAAIyL,KAAK,EAAE;QACjB,IAAI0D,cAAc,CAAC1D,KAAK,EAAEzL,CAAC,CAAC,IAAI,CAAC,IAAI,CAACkB,IAAI,CAAClB,CAAC,CAAC,EAAE;UAC3CgR,KAAK,GAAG,KAAK;UACbnR,GAAG,CAAC8B,MAAM,CAAC4J,CAAC,CAACvL,CAAC,CAAC,EAAE,GAAG,EAAEyL,KAAK,CAACzL,CAAC,CAAC,CAAC;QACnC;MACJ;IACJ,CAAC,CAAC;IACF,IAAI,CAACgR,KAAK,EAAE;MACRnR,GAAG,CAAC8B,MAAM,CAAC,CAAC;IAChB;IACA9B,GAAG,CAAC,IAAI,CAAC;EACb,CAAC;EAED,OAAOyB,aAAa;AACxB,CAAC,CAACjB,QAAQ,CAAE;;AAEZ;;AAEA,IAAIoM,SAAS,GAAI,UAAUpM,QAAQ,EAAE;EACjC,SAASoM,SAASA,CAACjL,IAAI,EAAEiK,KAAK,EAAEwF,QAAQ,EAAE;IACtC5Q,QAAQ,CAACkC,IAAI,CAAC,IAAI,CAAC;IACnB,IAAI,OAAOf,IAAI,IAAI,QAAQ,EAAE;MACzB,IAAI0P,GAAG,GAAG9U,YAAY,CAAC,CAAC;MACxB8U,GAAG,CAAC7P,KAAK,CAACG,IAAI,CAAC;MACfA,IAAI,GAAG0P,GAAG;IACd;IACA,IAAI,CAAC1P,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACiK,KAAK,GAAGA,KAAK,IAAI,CAAC,CAAC;IACxB,IAAI,CAACwF,QAAQ,GAAGA,QAAQ;EAC5B;EAEA,IAAK5Q,QAAQ,EAAGoM,SAAS,CAAC4D,SAAS,GAAGhQ,QAAQ;EAC9CoM,SAAS,CAACgB,SAAS,GAAG/D,MAAM,CAAC4G,MAAM,CAAEjQ,QAAQ,IAAIA,QAAQ,CAACoN,SAAU,CAAC;EACrEhB,SAAS,CAACgB,SAAS,CAAC8C,WAAW,GAAG9D,SAAS;EAE3CA,SAAS,CAACgB,SAAS,CAAClN,MAAM,GAAG,SAASA,MAAMA,CAAEV,GAAG,EAAE;IAC/C,IAAI2B,IAAI,GAAG,IAAI,CAACA,IAAI,CAACJ,GAAG,CAAC,CAAC;MAAEqK,KAAK,GAAG,IAAI,CAACA,KAAK;IAC9C,IAAI,IAAI,CAACwF,QAAQ,IAAItU,eAAe,CAAC,CAAC,EAAE;MACpC,IAAI,CAAC8O,KAAK,CAAC0F,MAAM,EAAE;QACf1F,KAAK,CAAC0F,MAAM,GAAG,EAAE;MACrB,CAAC,MAAM,IAAI,EAAE1F,KAAK,CAAC0F,MAAM,YAAY9B,KAAK,CAAC,EAAE;QACzC5D,KAAK,CAAC0F,MAAM,GAAG,CAAE1F,KAAK,CAAC0F,MAAM,CAAE;MACnC;MACA1F,KAAK,CAAC0F,MAAM,CAACC,OAAO,CAAC7F,CAAC,CAAC,aAAa,CAAC,CAAC;MACtC/J,IAAI,GAAG9E,OAAO,CAAC8E,IAAI,CAAC;IACxB;IACAiK,KAAK,CAAC4F,MAAM,GAAG7P,IAAI,CAACvB,MAAM;IAC1BJ,GAAG,CAAC,IAAIyB,aAAa,CAACmK,KAAK,CAAC,EAAE,SAAS,EAAEzO,EAAE,CAAC;IAC5C6C,GAAG,CAAC0B,SAAS,CAACC,IAAI,CAAC;IACnB3B,GAAG,CAAC7C,EAAE,EAAE,WAAW,CAAC;EACxB,CAAC;EAED,OAAOyP,SAAS;AACpB,CAAC,CAACpM,QAAQ,CAAE;;AAEZ;;AAEA,IAAI0K,UAAU,GAAI,UAAUzJ,aAAa,EAAE;EACvC,SAASyJ,UAAUA,CAAA,EAAG;IAClBzJ,aAAa,CAACiB,IAAI,CAAC,IAAI,EAAE;MACrBoM,IAAI,EAAEpD,CAAC,CAAC,SAAS;IACrB,CAAC,CAAC;EACN;EAEA,IAAKjK,aAAa,EAAGyJ,UAAU,CAACsF,SAAS,GAAG/O,aAAa;EACzDyJ,UAAU,CAAC0C,SAAS,GAAG/D,MAAM,CAAC4G,MAAM,CAAEhP,aAAa,IAAIA,aAAa,CAACmM,SAAU,CAAC;EAChF1C,UAAU,CAAC0C,SAAS,CAAC8C,WAAW,GAAGxF,UAAU;EAE7CA,UAAU,CAAC0C,SAAS,CAAC/B,QAAQ,GAAG,SAASA,QAAQA,CAAE4F,QAAQ,EAAE;IACzD,IAAI,CAAC7F,KAAK,CAAC8F,KAAK,GAAGD,QAAQ;EAC/B,CAAC;EAED,OAAOvG,UAAU;AACrB,CAAC,CAACzJ,aAAa,CAAE;;AAEjB;;AAEA,IAAI2J,WAAW,GAAI,UAAU3J,aAAa,EAAE;EACxC,SAAS2J,WAAWA,CAAA,EAAG;IACnB3J,aAAa,CAACiB,IAAI,CAAC,IAAI,EAAE;MACrBoM,IAAI,EAAIpD,CAAC,CAAC,OAAO,CAAC;MAClBiG,IAAI,EAAI,EAAE;MACVC,KAAK,EAAG;IACZ,CAAC,CAAC;EACN;EAEA,IAAKnQ,aAAa,EAAG2J,WAAW,CAACoF,SAAS,GAAG/O,aAAa;EAC1D2J,WAAW,CAACwC,SAAS,GAAG/D,MAAM,CAAC4G,MAAM,CAAEhP,aAAa,IAAIA,aAAa,CAACmM,SAAU,CAAC;EACjFxC,WAAW,CAACwC,SAAS,CAAC8C,WAAW,GAAGtF,WAAW;EAE/CA,WAAW,CAACwC,SAAS,CAACrB,OAAO,GAAG,SAASA,OAAOA,CAAEsF,OAAO,EAAE;IACvD,IAAI,CAACjG,KAAK,CAAC+F,IAAI,CAACnH,IAAI,CAACqH,OAAO,CAAC;IAC7B,IAAI,CAACjG,KAAK,CAACgG,KAAK,EAAE;EACtB,CAAC;EAED,OAAOxG,WAAW;AACtB,CAAC,CAAC3J,aAAa,CAAE;;AAEjB;;AAEA;;AAEA,IAAIqQ,SAAS,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AAE9F,IAAI9I,YAAY,GAAG,SAASA,YAAYA,CAACrH,IAAI,EAAE;EAC3C;EACA;EACA;EACA;EACAA,IAAI,CAACM,MAAM,CAAC,CAAC,CAAC;EACd,IAAI8E,KAAK,EAAEC,MAAM,EAAE+K,UAAU,EAAEC,gBAAgB;EAC/C,IAAIC,GAAG,GAAGtQ,IAAI,CAACuQ,SAAS,CAAC,CAAC;EAC1B,IAAID,GAAG,IAAI,MAAM,EAAE;IACf;IACA,MAAM,IAAI1R,KAAK,CAAC,oBAAoB,CAAC;EACzC;EACA,OAAO,CAACoB,IAAI,CAACwQ,GAAG,CAAC,CAAC,EAAE;IAChB,IAAIC,EAAE,GAAGzQ,IAAI,CAAC0Q,QAAQ,CAAC,CAAC;IACxB,IAAID,EAAE,IAAI,IAAI,EAAE;MACZ,MAAM,IAAI7R,KAAK,CAAC,oBAAoB,CAAC;IACzC;IACA,IAAI+R,MAAM,GAAG3Q,IAAI,CAAC0Q,QAAQ,CAAC,CAAC;IAC5B,IAAIjS,MAAM,GAAGuB,IAAI,CAACuQ,SAAS,CAAC,CAAC;IAC7B,IAAIJ,SAAS,CAAC5Q,OAAO,CAACoR,MAAM,CAAC,IAAI,CAAC,EAAE;MAChC;MACAN,gBAAgB,GAAGrQ,IAAI,CAAC0Q,QAAQ,CAAC,CAAC;MAClCrL,MAAM,GAAGrF,IAAI,CAACuQ,SAAS,CAAC,CAAC;MACzBnL,KAAK,GAAGpF,IAAI,CAACuQ,SAAS,CAAC,CAAC;MACxBH,UAAU,GAAGpQ,IAAI,CAAC0Q,QAAQ,CAAC,CAAC;MAC5B;IACJ;IACA1Q,IAAI,CAAC4Q,IAAI,CAACnS,MAAM,GAAG,CAAC,CAAC;EACzB;EAEA,IAAI2R,UAAU,IAAI,IAAI,EAAE;IACpB,MAAM,IAAIxR,KAAK,CAAC,oBAAoB,CAAC;EACzC;EAEA,IAAIqL,KAAK,GAAG;IACRkD,IAAI,EAAWpD,CAAC,CAAC,SAAS,CAAC;IAC3B8G,OAAO,EAAQ9G,CAAC,CAAC,OAAO,CAAC;IACzB+G,KAAK,EAAU1L,KAAK;IACpB2L,MAAM,EAAS1L,MAAM;IACrB2L,gBAAgB,EAAGX,gBAAgB;IACnCV,MAAM,EAAS5F,CAAC,CAAC,WAAW;EAChC,CAAC;EAED,QAAQqG,UAAU;IAClB,KAAK,CAAC;MACFnG,KAAK,CAACgH,UAAU,GAAGlH,CAAC,CAAC,YAAY,CAAC;MAClC;IACJ,KAAK,CAAC;MACFE,KAAK,CAACgH,UAAU,GAAGlH,CAAC,CAAC,WAAW,CAAC;MACjC;IACJ,KAAK,CAAC;MACFE,KAAK,CAACgH,UAAU,GAAGlH,CAAC,CAAC,YAAY,CAAC;MAClCE,KAAK,CAACiH,MAAM,GAAG,CAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAE,CAAC,CAAC;MAC3C;EACJ;EAEA,IAAI,CAACvE,QAAQ,GAAG,YAAW;IACvB3M,IAAI,CAACM,MAAM,CAAC,CAAC,CAAC;IACd,IAAIE,MAAM,GAAG,IAAIyK,SAAS,CAACjL,IAAI,EAAEiK,KAAK,CAAC;IACvCzJ,MAAM,CAAC8M,aAAa,GAAGvD,CAAC,CAAC,GAAG,GAAI,EAAEtO,gBAAiB,CAAC;IACpD,OAAO+E,MAAM;EACjB,CAAC;AACL,CAAC;;AAED;;AAEA,IAAIyG,WAAW,GAAG,SAASA,WAAWA,CAAC7B,KAAK,EAAEC,MAAM,EAAEsB,GAAG,EAAEC,KAAK,EAAE;EAC9D,IAAI,CAAC+F,QAAQ,GAAG,UAASwE,GAAG,EAAE;IAC1B,IAAIC,IAAI,GAAG,IAAInG,SAAS,CAACrE,KAAK,EAAE;MAC5BuG,IAAI,EAAWpD,CAAC,CAAC,SAAS,CAAC;MAC3B8G,OAAO,EAAQ9G,CAAC,CAAC,OAAO,CAAC;MACzB+G,KAAK,EAAU1L,KAAK;MACpB2L,MAAM,EAAS1L,MAAM;MACrB2L,gBAAgB,EAAG,CAAC;MACpBC,UAAU,EAAKlH,CAAC,CAAC,YAAY;IACjC,CAAC,EAAE,IAAI,CAAC;IACR,IAAIvJ,MAAM,GAAG,IAAIyK,SAAS,CAACtE,GAAG,EAAE;MAC5BwG,IAAI,EAAWpD,CAAC,CAAC,SAAS,CAAC;MAC3B8G,OAAO,EAAQ9G,CAAC,CAAC,OAAO,CAAC;MACzB+G,KAAK,EAAU1L,KAAK;MACpB2L,MAAM,EAAS1L,MAAM;MACrB2L,gBAAgB,EAAG,CAAC;MACpBC,UAAU,EAAKlH,CAAC,CAAC,WAAW,CAAC;MAC7BsH,KAAK,EAAUF,GAAG,CAACvI,MAAM,CAACwI,IAAI;IAClC,CAAC,EAAE,IAAI,CAAC;IACR5Q,MAAM,CAAC8M,aAAa,GAAGvD,CAAC,CAAC,GAAG,GAAI,EAAEtO,gBAAiB,CAAC;IACpD,OAAO+E,MAAM;EACjB,CAAC;AACL,CAAC;AAED,IAAIiM,UAAU,GAAI,UAAU3M,aAAa,EAAE;EACvC,SAAS2M,UAAUA,CAACH,IAAI,EAAEgF,OAAO,EAAE/E,WAAW,EAAE;IAC5C,IAAIgF,GAAG,GAAGjF,IAAI,CAACxI,IAAI,CAAC,CAAC;IACrB,IAAIsB,KAAK,GAAGmM,GAAG,CAACnM,KAAK;IACrB,IAAIC,MAAM,GAAGkM,GAAG,CAAClM,MAAM;IACvB,IAAIgG,IAAI,GAAG,IAAIC,OAAO,CAACgG,OAAO,CAACE,IAAI,EAAE,CAAC,CAAC,CAAC;IACxCnG,IAAI,CAACE,QAAQ,GAAG,IAAIN,SAAS,CAAC/M,UAAU,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;IAEvDqO,WAAW,CAACD,IAAI,EAAEjB,IAAI,EAAE,CAAC,CAAC,CAAC;IAE3BiG,OAAO,CAACG,WAAW,GAAGvJ,MAAM,CAACC,MAAM,CAACmJ,OAAO,CAACG,WAAW,EAAEpG,IAAI,CAACoG,WAAW,CAAC;IAC1EH,OAAO,CAACI,cAAc,GAAGxJ,MAAM,CAACC,MAAM,CAACmJ,OAAO,CAACI,cAAc,EAAErG,IAAI,CAACqG,cAAc,CAAC;IACnFJ,OAAO,CAACK,YAAY,GAAGzJ,MAAM,CAACC,MAAM,CAACmJ,OAAO,CAACK,YAAY,EAAEtG,IAAI,CAACsG,YAAY,CAAC;IAE7E7R,aAAa,CAACiB,IAAI,CAAC,IAAI,EAAE;MACrBoM,IAAI,EAAEpD,CAAC,CAAC,SAAS,CAAC;MAClB6H,WAAW,EAAE,CAAC;MACdC,SAAS,EAAE,CAAC;MACZC,UAAU,EAAE,CAAC;MACbC,IAAI,EAAE,CAAC,CAAC,EAAG,CAAC,EAAE3M,KAAK,EAAEC,MAAM,CAAC;MAC5B2M,KAAK,EAAE5M,KAAK;MACZ6M,KAAK,EAAE5M,MAAM;MACb6M,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE7M,MAAM,CAAC;MAChC8M,SAAS,EAAE;QACPC,SAAS,EAAE,IAAItS,aAAa,CAACuL,IAAI,CAACsG,YAAY,CAAC;QAC/CU,OAAO,EAAE,IAAIvS,aAAa,CAACuL,IAAI,CAACoG,WAAW,CAAC;QAC5Ca,IAAI,EAAE,IAAIxS,aAAa,CAACuL,IAAI,CAACqG,cAAc;MAC/C;IACJ,CAAC,CAAC;IACF,IAAI,CAACpE,aAAa,GAAGvD,CAAC,CAAC,GAAG,GAAI,EAAErO,eAAgB,CAAC;IACjD,IAAI,CAACsE,IAAI,GAAGqL,IAAI,CAACE,QAAQ,CAACvL,IAAI;IAC9B,IAAI,CAACyP,QAAQ,GAAG,IAAI;EACxB;EAEA,IAAK3P,aAAa,EAAG2M,UAAU,CAACoC,SAAS,GAAG/O,aAAa;EACzD2M,UAAU,CAACR,SAAS,GAAG/D,MAAM,CAAC4G,MAAM,CAAEhP,aAAa,IAAIA,aAAa,CAACmM,SAAU,CAAC;EAChFQ,UAAU,CAACR,SAAS,CAAC8C,WAAW,GAAGtC,UAAU;EAE7CA,UAAU,CAACR,SAAS,CAAClN,MAAM,GAAG,SAASA,MAAMA,CAAEV,GAAG,EAAE;IAChD4M,SAAS,CAACgB,SAAS,CAAClN,MAAM,CAACgC,IAAI,CAAC,IAAI,EAAE1C,GAAG,CAAC;EAC9C,CAAC;EAED,OAAOoO,UAAU;AACrB,CAAC,CAAC3M,aAAa,CAAE;;AAEjB;;AAEA,IAAIqM,eAAe,GAAI,UAAUrM,aAAa,EAAE;EAC5C,SAASqM,eAAeA,CAACzD,IAAI,EAAC;IAC1B5I,aAAa,CAACiB,IAAI,CAAC,IAAI,EAAE;MACrBoM,IAAI,EAAOpD,CAAC,CAAC,MAAM,CAAC;MACpB8G,OAAO,EAAI9G,CAAC,CAAC,OAAO,CAAC;MACrBwI,QAAQ,EAAGxI,CAAC,CAACrB,IAAI;IACrB,CAAC,CAAC;IAEF,IAAI,CAAC4E,aAAa,GAAGvD,CAAC,CAAC,GAAG,GAAI,EAAEtO,gBAAiB,CAAC;EACtD;EAEA,IAAKqE,aAAa,EAAGqM,eAAe,CAAC0C,SAAS,GAAG/O,aAAa;EAC9DqM,eAAe,CAACF,SAAS,GAAG/D,MAAM,CAAC4G,MAAM,CAAEhP,aAAa,IAAIA,aAAa,CAACmM,SAAU,CAAC;EACrFE,eAAe,CAACF,SAAS,CAAC8C,WAAW,GAAG5C,eAAe;EAEvDA,eAAe,CAACF,SAAS,CAACuG,UAAU,GAAG,SAASA,UAAUA,CAAEhF,GAAG,EAAE;IAC7D,OAAO,IAAI3D,SAAS,CAAClK,MAAM,CAAC6N,GAAG,CAAC,CAAC;EACrC,CAAC;EAED,OAAOrB,eAAe;AAC1B,CAAC,CAACrM,aAAa,CAAE;;AAEjB;;AAEA,IAAIsM,OAAO,GAAI,UAAUtM,aAAa,EAAE;EACpC,SAASsM,OAAOA,CAAC+E,GAAG,EAAEzN,IAAI,EAAEuG,KAAK,EAAC;IAC9BnK,aAAa,CAACiB,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IAE5BkJ,KAAK,GAAG,IAAI,CAACA,KAAK;IAClBA,KAAK,CAACkD,IAAI,GAAGpD,CAAC,CAAC,MAAM,CAAC;IACtBE,KAAK,CAAC4G,OAAO,GAAG9G,CAAC,CAAC,OAAO,CAAC;IAC1BE,KAAK,CAACwI,QAAQ,GAAG1I,CAAC,CAAC,YAAY,CAAC;IAEhC,IAAI,CAACyH,IAAI,GAAGL,GAAG;IACf,IAAI,CAACuB,KAAK,GAAGhP,IAAI;IACjB,IAAI,CAACiP,IAAI,GAAGjP,IAAI,CAACkP,UAAU,CAAC,CAAC;IAC7B,IAAI,CAACtF,aAAa,GAAGvD,CAAC,CAAC,GAAG,GAAI,EAAEtO,gBAAiB,CAAC;IAElD,IAAIoX,IAAI,GAAGnP,IAAI,CAACmP,IAAI;IAEpB,IAAI,CAACnK,IAAI,GAAGhF,IAAI,CAACoP,MAAM;IACvB,IAAIC,KAAK,GAAG,IAAI,CAACA,KAAK,GAAGrP,IAAI,CAACqP,KAAK;IACnC,IAAI,CAACC,IAAI,GAAG,CACRH,IAAI,CAACI,IAAI,GAAGF,KAAK,EACjBF,IAAI,CAACK,IAAI,GAAGH,KAAK,EACjBF,IAAI,CAACM,IAAI,GAAGJ,KAAK,EACjBF,IAAI,CAACO,IAAI,GAAGL,KAAK,CACpB;IAED,IAAI,CAACM,WAAW,GAAG3P,IAAI,CAAC4P,IAAI,CAACD,WAAW;IACxC,IAAI,CAACE,MAAM,GAAG7P,IAAI,CAAC6P,MAAM,GAAGR,KAAK;IACjC,IAAI,CAACS,OAAO,GAAG9P,IAAI,CAAC8P,OAAO,GAAGT,KAAK;IACnC,IAAI,CAACU,OAAO,GAAG/P,IAAI,CAAC+P,OAAO,GAAGV,KAAK;IACnC,IAAI,CAACW,SAAS,GAAGhQ,IAAI,CAACiQ,GAAG,CAACD,SAAS,IAAI,IAAI,CAACH,MAAM;IAClD,IAAI,CAACK,OAAO,GAAGlQ,IAAI,CAACiQ,GAAG,CAACC,OAAO,IAAI,CAAC;IACpC,IAAI,CAACC,KAAK,GAAG,CAAC;IAEd,IAAI,CAACC,WAAW,GAAG,CAACpQ,IAAI,CAACiQ,GAAG,CAACG,WAAW,IAAI,CAAC,KAAK,CAAC;IACnD,IAAI,CAACC,OAAO,GAAG,IAAI,CAACD,WAAW,IAAI,CAAC,IAAI,IAAI,CAACA,WAAW,IAAI,CAAC;IAC7D,IAAI,CAACE,QAAQ,GAAG,IAAI,CAACF,WAAW,IAAI,EAAE;IAEtC,IAAI,CAACG,KAAK,GAAI,CAACvQ,IAAI,CAAC4P,IAAI,CAACY,YAAY,GAAG,CAAC,GAAG,CAAC,KAChC,IAAI,CAACH,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAC1B,IAAI,CAACC,QAAQ,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAC3B,IAAI,CAACX,WAAW,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GACpC,CAAC,IAAI,CAAG;EACrB;EAEJ,IAAKvT,aAAa,EAAGsM,OAAO,CAACyC,SAAS,GAAG/O,aAAa;EACtDsM,OAAO,CAACH,SAAS,GAAG/D,MAAM,CAAC4G,MAAM,CAAEhP,aAAa,IAAIA,aAAa,CAACmM,SAAU,CAAC;EAC7EG,OAAO,CAACH,SAAS,CAAC8C,WAAW,GAAG3C,OAAO;EAEnCA,OAAO,CAACH,SAAS,CAACuG,UAAU,GAAG,SAASA,UAAUA,CAAE2B,IAAI,EAAE;IACtD,OAAO,IAAIhF,YAAY,CAAC,IAAI,CAACwD,IAAI,CAACH,UAAU,CAAC7S,MAAM,CAACwU,IAAI,CAAC,CAAC,CAAC;EAC/D,CAAC;EAED/H,OAAO,CAACH,SAAS,CAACmI,YAAY,GAAG,SAASA,YAAYA,CAAEC,QAAQ,EAAEF,IAAI,EAAE;IACpE,IAAI/E,MAAM,GAAG,IAAI;IAEjB,IAAIhK,KAAK,GAAG,CAAC;MAAEkP,OAAO,GAAG,IAAI,CAAC5B,KAAK,CAAC6B,IAAI,CAACD,OAAO;IAChD,KAAK,IAAI9V,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2V,IAAI,CAAC1V,MAAM,EAAE,EAAED,CAAC,EAAE;MAClC,IAAIgW,OAAO,GAAGF,OAAO,CAACH,IAAI,CAACjF,UAAU,CAAC1Q,CAAC,CAAC,CAAC;MACzC4G,KAAK,IAAIgK,MAAM,CAACsD,KAAK,CAAC+B,YAAY,CAACD,OAAO,IAAI,CAAC,CAAC;IACpD;IACA,OAAOpP,KAAK,GAAGiP,QAAQ,GAAG,IAAI;EAClC,CAAC;EAEDjI,OAAO,CAACH,SAAS,CAACnN,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAI;IACtD,IAAIyJ,IAAI,GAAG,IAAI;IACf,IAAImM,GAAG,GAAGnM,IAAI,CAACoK,IAAI;;IAEnB;IACA,IAAI3S,IAAI,GAAG0U,GAAG,CAAC3V,MAAM,CAAC,CAAC;IACvB,IAAI4V,UAAU,GAAG,IAAI1J,SAAS,CAACrQ,YAAY,CAACoF,IAAI,CAAC,EAAE;MAC/C4U,OAAO,EAAE5U,IAAI,CAACvB;IAClB,CAAC,EAAE,IAAI,CAAC;IAER,IAAIoW,UAAU,GAAGtM,IAAI,CAACiJ,IAAI,CAAC5I,MAAM,CAAC,IAAI9I,aAAa,CAAC;MAChDqN,IAAI,EAAWpD,CAAC,CAAC,gBAAgB,CAAC;MAClC+K,QAAQ,EAAO/K,CAAC,CAACxB,IAAI,CAACoK,IAAI,CAACG,MAAM,CAAC;MAClCiC,QAAQ,EAAOxM,IAAI,CAACyK,IAAI;MACxBgC,KAAK,EAAUzM,IAAI,CAAC0L,KAAK;MACzBgB,KAAK,EAAU1M,IAAI,CAACsL,KAAK;MACzBqB,WAAW,EAAI3M,IAAI,CAAC8K,WAAW;MAC/B8B,MAAM,EAAS5M,IAAI,CAACgL,MAAM;MAC1B6B,OAAO,EAAQ7M,IAAI,CAACiL,OAAO;MAC3B6B,SAAS,EAAM9M,IAAI,CAACmL,SAAS;MAC7B4B,OAAO,EAAQ/M,IAAI,CAACqL,OAAO;MAC3B2B,SAAS,EAAMhN,IAAI,CAACiJ,IAAI,CAAC5I,MAAM,CAAC+L,UAAU;IAC9C,CAAC,CAAC,CAAC;IAEH,IAAIJ,IAAI,GAAGG,GAAG,CAACc,SAAS;IACxB,IAAIC,SAAS,GAAGf,GAAG,CAACe,SAAS;IAC7B,IAAIC,QAAQ,GAAGhB,GAAG,CAACgB,QAAQ;IAC3B,IAAIC,UAAU,GAAG,EAAE;IACnB,CAAC,SAASC,IAAIA,CAACpX,CAAC,EAAEqX,KAAK,EAAC;MACpB,IAAIrX,CAAC,IAAIkX,QAAQ,EAAE;QACf,IAAII,GAAG,GAAGvB,IAAI,CAAC/V,CAAC,CAAC;QACjB,IAAIsX,GAAG,IAAI,IAAI,EAAE;UACbF,IAAI,CAACpX,CAAC,GAAG,CAAC,CAAC;QACf,CAAC,MAAM;UACH,IAAI,CAACqX,KAAK,EAAE;YACRF,UAAU,CAAC9M,IAAI,CAACrK,CAAC,EAAEqX,KAAK,GAAG,EAAE,CAAC;UAClC;UACAA,KAAK,CAAChN,IAAI,CAACN,IAAI,CAACmK,KAAK,CAAC+B,YAAY,CAACqB,GAAG,CAAC,CAAC;UACxCF,IAAI,CAACpX,CAAC,GAAG,CAAC,EAAEqX,KAAK,CAAC;QACtB;MACJ;IACJ,CAAC,EAAEJ,SAAS,CAAC;;IAEb;IACA;IACA;IACA;IACA;;IAEA,IAAIM,UAAU,GAAG,IAAIjW,aAAa,CAAC;MAC/BqN,IAAI,EAAEpD,CAAC,CAAC,MAAM,CAAC;MACf8G,OAAO,EAAE9G,CAAC,CAAC,cAAc,CAAC;MAC1BwI,QAAQ,EAAExI,CAAC,CAACxB,IAAI,CAACoK,IAAI,CAACG,MAAM,CAAC;MAC7BkD,aAAa,EAAE,IAAIlW,aAAa,CAAC;QAC7BmW,QAAQ,EAAK,IAAIpM,SAAS,CAAC,OAAO,CAAC;QACnCqM,QAAQ,EAAK,IAAIrM,SAAS,CAAC,UAAU,CAAC;QACtCsM,UAAU,EAAG;MACjB,CAAC,CAAC;MACFC,cAAc,EAAEvB,UAAU;MAC1BwB,SAAS,EAAEZ,SAAS;MACpBa,QAAQ,EAAEZ,QAAQ;MAClBa,EAAE,EAAE/U,IAAI,CAACgV,KAAK,CAACjO,IAAI,CAACmK,KAAK,CAAC+B,YAAY,CAAC,CAAC,CAAC,CAAC;MAC1CgC,CAAC,EAAEd,UAAU;MACbe,WAAW,EAAEnO,IAAI,CAACiJ,IAAI,CAAC5I,MAAM,CAACL,IAAI,CAACoO,gBAAgB,CAAC,CAAC;IACzD,CAAC,CAAC;IAEF,IAAIpJ,IAAI,GAAGhF,IAAI,CAAC0B,KAAK;IACrBsD,IAAI,CAACgF,QAAQ,GAAGxI,CAAC,CAACxB,IAAI,CAACoK,IAAI,CAACG,MAAM,CAAC;IACnCvF,IAAI,CAACqJ,eAAe,GAAG,CAAErO,IAAI,CAACiJ,IAAI,CAAC5I,MAAM,CAACmN,UAAU,CAAC,CAAE;;IAEvD;IACA;IACA,IAAIc,MAAM,GAAG,IAAIC,gBAAgB,CAACrB,SAAS,EAAEC,QAAQ,EAAEhB,GAAG,CAACqC,MAAM,CAAC;IAClE,IAAIC,YAAY,GAAG,IAAI/L,SAAS,CAAC/M,UAAU,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;IAC1D8Y,YAAY,CAAChX,IAAI,CAAC6W,MAAM,CAAC;IACzBtJ,IAAI,CAAC0J,SAAS,GAAG1O,IAAI,CAACiJ,IAAI,CAAC5I,MAAM,CAACoO,YAAY,CAAC;EACnD,CAAC;EAED5K,OAAO,CAACH,SAAS,CAAC0K,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAI;IAC9D,OAAO,IAAI1L,SAAS,CAACrQ,YAAY,CAAC,IAAI,CAAC+X,IAAI,CAACuE,WAAW,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;EAC3E,CAAC;EAEL,OAAO9K,OAAO;AAClB,CAAC,CAACtM,aAAa,CAAE;AAEjB,IAAIgX,gBAAgB,GAAI,UAAUjY,QAAQ,EAAE;EACxC,SAASiY,gBAAgBA,CAACrB,SAAS,EAAEC,QAAQ,EAAEyB,GAAG,EAAC;IAC/CtY,QAAQ,CAACkC,IAAI,CAAC,IAAI,CAAC;IACnB,IAAI,CAAC0U,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACyB,GAAG,GAAGA,GAAG;EAClB;EAEA,IAAKtY,QAAQ,EAAGiY,gBAAgB,CAACjI,SAAS,GAAGhQ,QAAQ;EACrDiY,gBAAgB,CAAC7K,SAAS,GAAG/D,MAAM,CAAC4G,MAAM,CAAEjQ,QAAQ,IAAIA,QAAQ,CAACoN,SAAU,CAAC;EAC5E6K,gBAAgB,CAAC7K,SAAS,CAAC8C,WAAW,GAAG+H,gBAAgB;EAEzDA,gBAAgB,CAAC7K,SAAS,CAAClN,MAAM,GAAG,SAASA,MAAMA,CAAEV,GAAG,EAAE;IACtDA,GAAG,CAAC8B,MAAM,CAAC,sCAAsC,CAAC;IAClD9B,GAAG,CAAC8B,MAAM,CAAC,eAAe,CAAC;IAC3B9B,GAAG,CAAC8B,MAAM,CAAC,WAAW,CAAC;IACvB9B,GAAG,CAAC8B,MAAM,CAAC,mBAAmB,CAAC;IAC/B9B,GAAG,CAAC8B,MAAM,CAAC,qBAAqB,CAAC;IACjC9B,GAAG,CAAC8B,MAAM,CAAC,mBAAmB,CAAC;IAC/B9B,GAAG,CAAC8B,MAAM,CAAC,iBAAiB,CAAC;IAC7B9B,GAAG,CAAC8B,MAAM,CAAC,QAAQ,CAAC;IACpB9B,GAAG,CAAC8B,MAAM,CAAC,mCAAmC,CAAC;IAC/C9B,GAAG,CAAC8B,MAAM,CAAC,iBAAiB,CAAC;IAC7B9B,GAAG,CAAC8B,MAAM,CAAC,uBAAuB,CAAC;IACnC9B,GAAG,CAAC8B,MAAM,CAAC,gBAAgB,CAAC;IAC5B9B,GAAG,CAAC8B,MAAM,CAAC,mBAAmB,CAAC;IAE/B,IAAIoI,IAAI,GAAG,IAAI;IACflK,GAAG,CAAC8B,MAAM,CAACoI,IAAI,CAACmN,QAAQ,GAAGnN,IAAI,CAACkN,SAAS,GAAG,CAAC,EAAE,cAAc,CAAC;IAC9DpX,GAAG,CAAC4B,UAAU,CAAC,YAAU;MACrB,KAAK,IAAIgP,IAAI,GAAG1G,IAAI,CAACkN,SAAS,EAAExG,IAAI,IAAI1G,IAAI,CAACmN,QAAQ,EAAE,EAAEzG,IAAI,EAAE;QAC3D,IAAImI,OAAO,GAAG7O,IAAI,CAAC4O,GAAG,CAAClI,IAAI,CAAC;QAC5B,IAAIzB,GAAG,GAAG3S,UAAU,CAAC,CAAEuc,OAAO,CAAE,CAAC;QACjC/Y,GAAG,CAAC8B,MAAM,CAAC,GAAG,EAAE0L,OAAO,CAACoD,IAAI,CAAC1O,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;QACxD,KAAK,IAAI/B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgP,GAAG,CAAC/O,MAAM,EAAE,EAAED,CAAC,EAAE;UACjCH,GAAG,CAACwN,OAAO,CAAC2B,GAAG,CAAC0B,UAAU,CAAC1Q,CAAC,CAAC,CAAC+B,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACnD;QACAlC,GAAG,CAAC,GAAG,CAAC;MACZ;IACJ,CAAC,CAAC;IACFA,GAAG,CAAC8B,MAAM,CAAC,WAAW,CAAC;IAEvB9B,GAAG,CAAC8B,MAAM,CAAC,SAAS,CAAC;IACrB9B,GAAG,CAAC8B,MAAM,CAAC,+CAA+C,CAAC;IAC3D9B,GAAG,CAAC8B,MAAM,CAAC,KAAK,CAAC;IACjB9B,GAAG,CAAC8B,MAAM,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,OAAO2W,gBAAgB;AAC3B,CAAC,CAACjY,QAAQ,CAAE;;AAEZ;;AAEA,SAASwY,QAAQA,CAACtQ,CAAC,EAAE;EACjB,OAAOA,CAAC,CAACoQ,GAAG,CAAC,UAASzY,CAAC,EAAC;IACpB,OAAOM,OAAO,CAACN,CAAC,CAAC,GAAG2Y,QAAQ,CAAC3Y,CAAC,CAAC,GACzB,OAAOA,CAAC,IAAI,QAAQ,GAAG,CAAC8C,IAAI,CAACgV,KAAK,CAAC9X,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,EAAEY,OAAO,CAAC,CAAC,CAAC,GAC/DZ,CAAC;EACX,CAAC,CAAC,CAAC4Y,IAAI,CAAC,GAAG,CAAC;AAChB;AAEA,SAASC,0BAA0BA,CAACpG,GAAG,EAAEqG,EAAE,EAAEC,EAAE,EAAEjb,EAAE,EAAEkb,EAAE,EAAEC,EAAE,EAAElb,EAAE,EAAE;EAC7D,IAAImb,IAAI,GAAGP,QAAQ,CAAC,CAAEG,EAAE,EAAEC,EAAE,EAAEjb,EAAE,EAAEkb,EAAE,EAAEC,EAAE,EAAElb,EAAE,CAAE,CAAC;EAC/C,IAAIob,IAAI,GAAG1G,GAAG,CAACjI,kBAAkB,CAAC0O,IAAI,CAAC;EACvC,IAAI,CAACC,IAAI,EAAE;IACPA,IAAI,GAAG1G,GAAG,CAACjI,kBAAkB,CAAC0O,IAAI,CAAC,GAAGzG,GAAG,CAACvI,MAAM,CAAC,IAAI9I,aAAa,CAAC;MAC/DgY,YAAY,EAAE,CAAC;MACfC,MAAM,EAAE,CAAE,CAAC,EAAE,CAAC,CAAE;MAChBC,KAAK,EAAE,CAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAE;MAC3BC,CAAC,EAAE,CAAC;MACJC,EAAE,EAAE,CAAEV,EAAE,EAAGC,EAAE,EAAGjb,EAAE,CAAE;MACpB2b,EAAE,EAAE,CAAET,EAAE,EAAGC,EAAE,EAAGlb,EAAE;IACtB,CAAC,CAAC,CAAC;EACP;EACA,OAAOob,IAAI;AACf;AAEA,SAASO,4BAA4BA,CAACjH,GAAG,EAAEtV,EAAE,EAAEC,EAAE,EAAE;EAC/C,IAAI8b,IAAI,GAAGP,QAAQ,CAAC,CAAExb,EAAE,EAAEC,EAAE,CAAE,CAAC;EAC/B,IAAI+b,IAAI,GAAG1G,GAAG,CAAChI,kBAAkB,CAACyO,IAAI,CAAC;EACvC,IAAI,CAACC,IAAI,EAAE;IACPA,IAAI,GAAG1G,GAAG,CAAChI,kBAAkB,CAACyO,IAAI,CAAC,GAAGzG,GAAG,CAACvI,MAAM,CAAC,IAAI9I,aAAa,CAAC;MAC/DgY,YAAY,EAAE,CAAC;MACfC,MAAM,EAAE,CAAE,CAAC,EAAE,CAAC,CAAE;MAChBC,KAAK,EAAE,CAAE,CAAC,EAAE,CAAC,CAAE;MACfC,CAAC,EAAE,CAAC;MACJC,EAAE,EAAE,CAAErc,EAAE,CAAE;MACVsc,EAAE,EAAE,CAAErc,EAAE;IACZ,CAAC,CAAC,CAAC;EACP;EACA,OAAO+b,IAAI;AACf;AAEA,SAASQ,qBAAqBA,CAAClH,GAAG,EAAEmH,KAAK,EAAE;EACvC,IAAI5R,QAAQ,GAAG,KAAK;EACpB,IAAI6R,SAAS,GAAG,EAAE;EAClB,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,OAAO,GAAG,EAAE;EAChB,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIla,CAAC,EAAEma,IAAI,EAAEC,GAAG,EAAEC,SAAS,EAAEC,QAAQ;EACrC,KAAKta,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8Z,KAAK,CAAC7Z,MAAM,EAAE,EAAED,CAAC,EAAE;IAC/Bma,IAAI,GAAGL,KAAK,CAAC9Z,CAAC,GAAG,CAAC,CAAC;IACnBoa,GAAG,GAAGN,KAAK,CAAC9Z,CAAC,CAAC;IACdqa,SAAS,GAAGF,IAAI,CAACI,KAAK;IACtBD,QAAQ,GAAGF,GAAG,CAACG,KAAK;IACpBP,MAAM,CAAC3P,IAAI,CAAC0O,0BAA0B,CAClCpG,GAAG,EACH0H,SAAS,CAACG,CAAC,EAAEH,SAAS,CAACI,CAAC,EAAEJ,SAAS,CAACK,CAAC,EACrCJ,QAAQ,CAACE,CAAC,EAAGF,QAAQ,CAACG,CAAC,EAAGH,QAAQ,CAACI,CACvC,CAAC,CAAC;IACF,IAAIL,SAAS,CAAC9R,CAAC,GAAG,CAAC,IAAI+R,QAAQ,CAAC/R,CAAC,GAAG,CAAC,EAAE;MACnCL,QAAQ,GAAG,IAAI;IACnB;IACA+R,OAAO,CAAC5P,IAAI,CAAC+P,GAAG,CAACtY,MAAM,CAAC;IACxBoY,MAAM,CAAC7P,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACrB;EACA,IAAInC,QAAQ,EAAE;IACV,KAAKlI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8Z,KAAK,CAAC7Z,MAAM,EAAE,EAAED,CAAC,EAAE;MAC/Bma,IAAI,GAAGL,KAAK,CAAC9Z,CAAC,GAAG,CAAC,CAAC;MACnBoa,GAAG,GAAGN,KAAK,CAAC9Z,CAAC,CAAC;MACdqa,SAAS,GAAGF,IAAI,CAACI,KAAK;MACtBD,QAAQ,GAAGF,GAAG,CAACG,KAAK;MACpBR,SAAS,CAAC1P,IAAI,CAACuP,4BAA4B,CACvCjH,GAAG,EAAE0H,SAAS,CAAC9R,CAAC,EAAE+R,QAAQ,CAAC/R,CAC/B,CAAC,CAAC;IACN;EACJ;EACA0R,OAAO,CAACU,GAAG,CAAC,CAAC;EACb,OAAO;IACHzS,QAAQ,EAAIA,QAAQ;IACpB8R,MAAM,EAAMY,QAAQ,CAACZ,MAAM,CAAC;IAC5BD,SAAS,EAAG7R,QAAQ,GAAG0S,QAAQ,CAACb,SAAS,CAAC,GAAG;EACjD,CAAC;EACD,SAASa,QAAQA,CAACC,KAAK,EAAE;IACrB,IAAIA,KAAK,CAAC5a,MAAM,IAAI,CAAC,EAAE;MACnB,OAAO4a,KAAK,CAAC,CAAC,CAAC;IACnB;IACA,OAAO;MACHvB,YAAY,EAAE,CAAC;MACfwB,SAAS,EAAED,KAAK;MAChBtB,MAAM,EAAE,CAAE,CAAC,EAAE,CAAC,CAAE;MAChBwB,MAAM,EAAEd,OAAO;MACfe,MAAM,EAAEd;IACZ,CAAC;EACL;AACJ;AAEA,SAASe,kBAAkBA,CAACtI,GAAG,EAAEuI,QAAQ,EAAEpB,KAAK,EAAEqB,MAAM,EAAEN,KAAK,EAAEO,GAAG,EAAE;EAClE,IAAIC,OAAO,EAAEjC,IAAI;EACjB;EACA;EACA;EACA,IAAI,CAACgC,GAAG,EAAE;IACN,IAAI7S,CAAC,GAAG,CAAE2S,QAAQ,CAAE,CAACI,MAAM,CAACH,MAAM,CAAC;IACnCrB,KAAK,CAACjQ,OAAO,CAAC,UAAS3J,CAAC,EAAC;MACrBqI,CAAC,CAAC8B,IAAI,CAACnK,CAAC,CAAC4B,MAAM,EAAE5B,CAAC,CAACqa,KAAK,CAACC,CAAC,EAAEta,CAAC,CAACqa,KAAK,CAACE,CAAC,EAAEva,CAAC,CAACqa,KAAK,CAACG,CAAC,CAAC;IACrD,CAAC,CAAC;IACFtB,IAAI,GAAGP,QAAQ,CAACtQ,CAAC,CAAC;IAClB8S,OAAO,GAAG1I,GAAG,CAAC/H,QAAQ,CAACwO,IAAI,CAAC;EAChC;EACA,IAAI,CAACiC,OAAO,EAAE;IACVA,OAAO,GAAG,IAAI/Z,aAAa,CAAC;MACxBqN,IAAI,EAAEpD,CAAC,CAAC,SAAS,CAAC;MAClBgQ,WAAW,EAAEL,QAAQ,GAAG,CAAC,GAAG,CAAC;MAC7BzI,UAAU,EAAElH,CAAC,CAAC,WAAW,CAAC;MAC1BiQ,MAAM,EAAEL,MAAM;MACd5B,MAAM,EAAE,CAAE,CAAC,EAAE,CAAC,CAAE;MAChBkC,QAAQ,EAAEZ,KAAK;MACfa,MAAM,EAAE,CAAE,IAAI,EAAE,IAAI;IACxB,CAAC,CAAC;IACF/I,GAAG,CAACvI,MAAM,CAACiR,OAAO,CAAC;IACnBA,OAAO,CAACvM,aAAa,GAAG,GAAG,GAAI,EAAE7R,gBAAiB;IAClD,IAAImc,IAAI,EAAE;MACNzG,GAAG,CAAC/H,QAAQ,CAACwO,IAAI,CAAC,GAAGiC,OAAO;IAChC;EACJ;EACA,OAAOA,OAAO;AAClB;AAEA,SAASM,oBAAoBA,CAAChJ,GAAG,EAAEuI,QAAQ,EAAEpB,KAAK,EAAEqB,MAAM,EAAEN,KAAK,EAAEO,GAAG,EAAE;EACpE,IAAI/M,OAAO,EAAE+K,IAAI;EACjB;EACA;EACA;EACA,IAAI,CAACgC,GAAG,EAAE;IACN,IAAI7S,CAAC,GAAG,CAAE2S,QAAQ,CAAE,CAACI,MAAM,CAACH,MAAM,CAAC;IACnCrB,KAAK,CAACjQ,OAAO,CAAC,UAAS3J,CAAC,EAAC;MACrBqI,CAAC,CAAC8B,IAAI,CAACnK,CAAC,CAAC4B,MAAM,EAAE5B,CAAC,CAACqa,KAAK,CAAChS,CAAC,CAAC;IAC/B,CAAC,CAAC;IACF6Q,IAAI,GAAGP,QAAQ,CAACtQ,CAAC,CAAC;IAClB8F,OAAO,GAAGsE,GAAG,CAAC9H,QAAQ,CAACuO,IAAI,CAAC;EAChC;EACA,IAAI,CAAC/K,OAAO,EAAE;IACVA,OAAO,GAAG,IAAI/M,aAAa,CAAC;MACxBqN,IAAI,EAAEpD,CAAC,CAAC,WAAW,CAAC;MACpBqQ,GAAG,EAAE,KAAK;MACVhN,EAAE,EAAE,CAAC;MACLC,EAAE,EAAE,CAAC;MACLgE,KAAK,EAAE;QACHlE,IAAI,EAAEpD,CAAC,CAAC,MAAM,CAAC;QACfD,CAAC,EAAEC,CAAC,CAAC,YAAY,CAAC;QAClBsQ,CAAC,EAAElJ,GAAG,CAACvI,MAAM,CAAC,IAAIqC,SAAS,CAAC,eAAe,EAAE;UACzCkC,IAAI,EAAEpD,CAAC,CAAC,SAAS,CAAC;UAClB8G,OAAO,EAAE9G,CAAC,CAAC,MAAM,CAAC;UAClBuQ,QAAQ,EAAE,CAAC;UACXvI,IAAI,EAAG6H,GAAG,GAAG,CACTA,GAAG,CAAChY,IAAI,EAAEgY,GAAG,CAAC/X,GAAG,GAAG+X,GAAG,CAACvU,MAAM,EAAEuU,GAAG,CAAChY,IAAI,GAAGgY,GAAG,CAACxU,KAAK,EAAEwU,GAAG,CAAC/X,GAAG,CAChE,GAAG,CAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAG;UACnB0Y,KAAK,EAAE;YACHpN,IAAI,EAAEpD,CAAC,CAAC,OAAO,CAAC;YAChBD,CAAC,EAAEC,CAAC,CAAC,cAAc,CAAC;YACpByQ,EAAE,EAAEzQ,CAAC,CAAC,YAAY,CAAC;YACnB0Q,CAAC,EAAE;UACP,CAAC;UACDtI,SAAS,EAAE;YACPC,SAAS,EAAE;cACPxW,EAAE,EAAE;gBAAEwR,EAAE,EAAE,CAAC;gBAAEC,EAAE,EAAE;cAAE;YACvB,CAAC;YACDqN,OAAO,EAAE;cACLC,EAAE,EAAE;gBACA1J,UAAU,EAAElH,CAAC,CAAC,YAAY,CAAC;gBAC3BiQ,MAAM,EAAEL,MAAM;gBACd5B,MAAM,EAAE,CAAE,CAAC,EAAE,CAAC,CAAE;gBAChBgC,WAAW,EAAEL,QAAQ,GAAG,CAAC,GAAG,CAAC;gBAC7BO,QAAQ,EAAEZ,KAAK;gBACfa,MAAM,EAAE,CAAE,IAAI,EAAE,IAAI;cACxB;YACJ;UACJ;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;IACF/I,GAAG,CAACvI,MAAM,CAACiE,OAAO,CAAC;IACnBA,OAAO,CAACS,aAAa,GAAG,GAAG,GAAI,EAAE7R,gBAAiB;IAClD,IAAImc,IAAI,EAAE;MACNzG,GAAG,CAAC9H,QAAQ,CAACuO,IAAI,CAAC,GAAG/K,OAAO;IAChC;EACJ;EACA,OAAOA,OAAO;AAClB;AAEA,SAAS+N,aAAaA,CAACzJ,GAAG,EAAE0J,QAAQ,EAAEjB,GAAG,EAAE;EACvC,IAAIF,QAAQ,GAAGmB,QAAQ,CAACvW,IAAI,IAAI,QAAQ;EACxC,IAAI+U,KAAK,GAAGhB,qBAAqB,CAAClH,GAAG,EAAE0J,QAAQ,CAACvC,KAAK,CAAC;EACtD,IAAIqB,MAAM,GAAGD,QAAQ,GAAG,CACpBmB,QAAQ,CAACC,KAAK,CAACpc,CAAC,EAAGmc,QAAQ,CAACC,KAAK,CAACC,CAAC,EAAGF,QAAQ,CAACC,KAAK,CAAC9B,CAAC,EACtD6B,QAAQ,CAACG,GAAG,CAACtc,CAAC,EAAKmc,QAAQ,CAACG,GAAG,CAACD,CAAC,EAAKF,QAAQ,CAACG,GAAG,CAAChC,CAAC,CACvD,GAAG,CACA6B,QAAQ,CAACC,KAAK,CAACpc,CAAC,EAAGmc,QAAQ,CAACC,KAAK,CAACC,CAAC,EACnCF,QAAQ,CAACG,GAAG,CAACtc,CAAC,EAAKmc,QAAQ,CAACG,GAAG,CAACD,CAAC,CACpC;EACD,IAAIlB,OAAO,GAAGJ,kBAAkB,CAC5BtI,GAAG,EAAEuI,QAAQ,EAAEmB,QAAQ,CAACvC,KAAK,EAAEqB,MAAM,EAAEN,KAAK,CAACb,MAAM,EAAEqC,QAAQ,CAACI,SAAS,IAAIrB,GAC/E,CAAC;EACD,IAAI/M,OAAO,GAAGwM,KAAK,CAAC3S,QAAQ,GAAGyT,oBAAoB,CAC/ChJ,GAAG,EAAEuI,QAAQ,EAAEmB,QAAQ,CAACvC,KAAK,EAAEqB,MAAM,EAAEN,KAAK,CAACd,SAAS,EAAEsC,QAAQ,CAACI,SAAS,IAAIrB,GAClF,CAAC,GAAG,IAAI;EACR,OAAO;IACHlT,QAAQ,EAAE2S,KAAK,CAAC3S,QAAQ;IACxBmT,OAAO,EAAEA,OAAO;IAChBhN,OAAO,EAAEA;EACb,CAAC;AACL;;AAEA;;AAEA,IAAIvB,OAAO,GAAI,UAAUxL,aAAa,EAAE;EACpC,SAASwL,OAAOA,CAAC6F,GAAG,EAAElH,KAAK,EAAC;IACxBnK,aAAa,CAACiB,IAAI,CAAC,IAAI,EAAEkJ,KAAK,CAAC;IAE/B,IAAI,CAACuH,IAAI,GAAGL,GAAG;IACf,IAAI,CAAC+J,OAAO,GAAG,CAAC;IAChB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACzJ,cAAc,GAAG,CAAC,CAAC;IACxB,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC;IACtB,IAAI,CAACF,WAAW,GAAG,CAAC,CAAC;IACrB,IAAI,CAAC2J,aAAa,GAAG,CAAC,CAAC;IACvB,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC;IACtB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,OAAO,GAAG,CAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAE;IACnC,IAAI,CAACC,YAAY,GAAG,EAAE;IAEtB,IAAI,CAAC9I,KAAK,GAAG,IAAI;IACjB,IAAI,CAAC+I,SAAS,GAAG,IAAI;IAErB,IAAI,CAACC,aAAa,GAAG,EAAE;IAEvBzR,KAAK,GAAG,IAAI,CAACA,KAAK;IAClBA,KAAK,CAACkD,IAAI,GAAGpD,CAAC,CAAC,MAAM,CAAC;IACtBE,KAAK,CAAC0R,OAAO,GAAG,CACZ5R,CAAC,CAAC,KAAK,CAAC,EACRA,CAAC,CAAC,MAAM,CAAC,EACTA,CAAC,CAAC,QAAQ,CAAC,EACXA,CAAC,CAAC,QAAQ,CAAC,EACXA,CAAC,CAAC,QAAQ,CAAC,CACd;IACDE,KAAK,CAACkI,SAAS,GAAG,IAAIrS,aAAa,CAAC;MAChCwS,IAAI,EAAQ,IAAIxS,aAAa,CAAC,IAAI,CAAC4R,cAAc,CAAC;MAClDU,SAAS,EAAG,IAAItS,aAAa,CAAC,IAAI,CAAC6R,YAAY,CAAC;MAChDU,OAAO,EAAK,IAAIvS,aAAa,CAAC,IAAI,CAAC2R,WAAW,CAAC;MAC/CmK,OAAO,EAAK,IAAI9b,aAAa,CAAC,IAAI,CAACsb,aAAa,CAAC;MACjDV,OAAO,EAAK,IAAI5a,aAAa,CAAC,IAAI,CAACub,YAAY;IACnD,CAAC,CAAC;IACFpR,KAAK,CAAC4R,MAAM,GAAG,IAAI,CAACL,YAAY;EACpC;EAEA,IAAK1b,aAAa,EAAGwL,OAAO,CAACuD,SAAS,GAAG/O,aAAa;EACtDwL,OAAO,CAACW,SAAS,GAAG/D,MAAM,CAAC4G,MAAM,CAAEhP,aAAa,IAAIA,aAAa,CAACmM,SAAU,CAAC;EAC7EX,OAAO,CAACW,SAAS,CAAC8C,WAAW,GAAGzD,OAAO;EAEvCA,OAAO,CAACW,SAAS,CAAC6P,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAI;IACtC,IAAI,CAACvQ,QAAQ,CAACvL,IAAI,CAACK,KAAK,CAAC,IAAI,EAAE9B,SAAS,CAAC;EAC7C,CAAC;EAED+M,OAAO,CAACW,SAAS,CAACT,SAAS,GAAG,SAASA,SAASA,CAAEzE,CAAC,EAAEmS,CAAC,EAAE3J,CAAC,EAAEwM,CAAC,EAAEC,CAAC,EAAE9b,CAAC,EAAE;IAChE,IAAI,CAAC+b,gBAAgB,CAAC1d,SAAS,CAAC,EAAE;MAC9B,IAAI,CAACgd,OAAO,GAAGW,IAAI,CAAC3d,SAAS,EAAE,IAAI,CAACgd,OAAO,CAAC;MAC5C,IAAI,CAACO,IAAI,CAAC/U,CAAC,EAAE,GAAG,EAAEmS,CAAC,EAAE,GAAG,EAAE3J,CAAC,EAAE,GAAG,EAAEwM,CAAC,EAAE,GAAG,EAAEC,CAAC,EAAE,GAAG,EAAE9b,CAAC,EAAE,KAAK,CAAC;MAC3D;MACA;MACA,IAAI,CAAC4b,IAAI,CAACtgB,EAAE,CAAC;IACjB;EACJ,CAAC;EAED8P,OAAO,CAACW,SAAS,CAACR,SAAS,GAAG,SAASA,SAASA,CAAE0Q,EAAE,EAAEC,EAAE,EAAE;IACtD,IAAI,CAAC5Q,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE2Q,EAAE,EAAEC,EAAE,CAAC;EACtC,CAAC;EAED9Q,OAAO,CAACW,SAAS,CAAC8G,KAAK,GAAG,SAASA,KAAKA,CAAEsJ,EAAE,EAAEC,EAAE,EAAE;IAC9C,IAAI,CAAC9Q,SAAS,CAAC6Q,EAAE,EAAE,CAAC,EAAE,CAAC,EAAEC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;EACtC,CAAC;EAEDhR,OAAO,CAACW,SAAS,CAACsQ,MAAM,GAAG,SAASA,MAAMA,CAAEC,KAAK,EAAE;IAC/C,IAAIC,GAAG,GAAGjb,IAAI,CAACib,GAAG,CAACD,KAAK,CAAC;MAAEE,GAAG,GAAGlb,IAAI,CAACkb,GAAG,CAACF,KAAK,CAAC;IAChD,IAAI,CAAChR,SAAS,CAACiR,GAAG,EAAEC,GAAG,EAAE,CAACA,GAAG,EAAED,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAC7C,CAAC;EAEDnR,OAAO,CAACW,SAAS,CAAC0Q,SAAS,GAAG,SAASA,SAASA,CAAA,EAAI;IAChD,IAAI,CAACxB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACW,IAAI,CAAC,IAAI,EAAEtgB,EAAE,CAAC;EACvB,CAAC;EAED8P,OAAO,CAACW,SAAS,CAAC2Q,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;IAC5C,IAAI,CAACzB,SAAS,GAAG,KAAK;IACtB,IAAI,CAACW,IAAI,CAAC,IAAI,EAAEtgB,EAAE,CAAC;EACvB,CAAC;EAED8P,OAAO,CAACW,SAAS,CAAC4Q,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAI;IAC9D,IAAI,CAAC,IAAI,CAAC1B,SAAS,EAAE;MACjB,MAAM,IAAIvc,KAAK,CAAC,iDAAiD,CAAC;IACtE;EACJ,CAAC;EAED0M,OAAO,CAACW,SAAS,CAAC6Q,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAI;IACtD,IAAI,CAAC,IAAI,CAACpK,KAAK,EAAE;MACb,MAAM,IAAI9T,KAAK,CAAC,6CAA6C,CAAC;IAClE;EACJ,CAAC;EAED0M,OAAO,CAACW,SAAS,CAAC8Q,OAAO,GAAG,SAASA,OAAOA,CAAErZ,IAAI,EAAEI,IAAI,EAAE;IACtD,IAAI,CAAC+Y,gBAAgB,CAAC,CAAC;IACvB,IAAInZ,IAAI,IAAI,IAAI,EAAE;MACdA,IAAI,GAAG,IAAI,CAACgP,KAAK;IACrB,CAAC,MAAM,IAAI,EAAEhP,IAAI,YAAY0I,OAAO,CAAC,EAAE;MACnC1I,IAAI,GAAG,IAAI,CAAC8N,IAAI,CAACtF,OAAO,CAACxI,IAAI,CAAC;IAClC;IACA,IAAII,IAAI,IAAI,IAAI,EAAE;MACdA,IAAI,GAAG,IAAI,CAAC2X,SAAS;IACzB;IACA,IAAI,CAAC/J,cAAc,CAAChO,IAAI,CAAC4J,aAAa,CAAC,GAAG5J,IAAI;IAC9C,IAAI,CAACgP,KAAK,GAAGhP,IAAI;IACjB,IAAI,CAAC+X,SAAS,GAAG3X,IAAI;IACrB,IAAI,CAACgY,IAAI,CAACpY,IAAI,CAAC4J,aAAa,EAAE,GAAG,EAAExJ,IAAI,EAAE,KAAK,EAAEtI,EAAE,CAAC;EACvD,CAAC;EAED8P,OAAO,CAACW,SAAS,CAAC+Q,cAAc,GAAG,SAASA,cAAcA,CAAElZ,IAAI,EAAE;IAC9D,IAAI,CAAC+Y,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACf,IAAI,CAAChY,IAAI,EAAE,KAAK,EAAEtI,EAAE,CAAC;EAC9B,CAAC;EAED8P,OAAO,CAACW,SAAS,CAACgR,oBAAoB,GAAG,SAASA,oBAAoBA,CAAEC,IAAI,EAAE;IAC1E,IAAI,CAACL,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACf,IAAI,CAACoB,IAAI,EAAE,KAAK,EAAE1hB,EAAE,CAAC;EAC9B,CAAC;EAED8P,OAAO,CAACW,SAAS,CAACkR,QAAQ,GAAG,SAASA,QAAQA,CAAEhJ,IAAI,EAAEiJ,cAAc,EAAE;IAClE,IAAI,CAACN,YAAY,CAAC,CAAC;IACnB,IAAI3I,IAAI,CAAC1V,MAAM,GAAG,CAAC,IAAI2e,cAAc,IAAI,IAAI,CAAC1K,KAAK,YAAYtG,OAAO,EAAE;MACpE,IAAIiR,WAAW,GAAG,IAAI,CAAC3K,KAAK,CAAC0B,YAAY,CAAC,IAAI,CAACqH,SAAS,EAAEtH,IAAI,CAAC;MAC/D,IAAIpB,KAAK,GAAGqK,cAAc,GAAGC,WAAW,GAAG,GAAG;MAC9C,IAAI,CAACvB,IAAI,CAAC/I,KAAK,EAAE,MAAM,CAAC;IAC5B;IACA,IAAI,CAAC+I,IAAI,CAAC,IAAI,CAACpJ,KAAK,CAACF,UAAU,CAAC2B,IAAI,CAAC,EAAE,KAAK,EAAE3Y,EAAE,CAAC;EACrD,CAAC;EAED8P,OAAO,CAACW,SAAS,CAACqR,UAAU,GAAG,SAASA,UAAUA,CAAEnJ,IAAI,EAAE;IACtD,IAAI,CAAC2I,YAAY,CAAC,CAAC;IACnB,IAAI,CAAChB,IAAI,CAAC,IAAI,CAACpJ,KAAK,CAACF,UAAU,CAAC2B,IAAI,CAAC,EAAE,IAAI,EAAE3Y,EAAE,CAAC;EACpD,CAAC;EAED8P,OAAO,CAACW,SAAS,CAACsR,OAAO,GAAG,SAASA,OAAOA,CAAEC,GAAG,EAAE5D,GAAG,EAAE;IACpD,IAAI6D,EAAE,GAAG,IAAI,CAACC,OAAO,CAAC;MAAEhf,CAAC,EAAEkb,GAAG,CAAChY,IAAI;MAAEmZ,CAAC,EAAEnB,GAAG,CAAC7X;IAAO,CAAC,CAAC;IACrD,IAAI4b,EAAE,GAAG,IAAI,CAACD,OAAO,CAAC;MAAEhf,CAAC,EAAEkb,GAAG,CAAC9X,KAAK;MAAEiZ,CAAC,EAAEnB,GAAG,CAAC/X;IAAI,CAAC,CAAC;IACnD,IAAI,CAAC2Z,YAAY,CAAC3S,IAAI,CAAC,IAAI/I,aAAa,CAAC;MACrCqN,IAAI,EAAMpD,CAAC,CAAC,OAAO,CAAC;MACpB8G,OAAO,EAAG9G,CAAC,CAAC,MAAM,CAAC;MACnB6T,IAAI,EAAM,CAAEH,EAAE,CAAC/e,CAAC,EAAE+e,EAAE,CAAC1C,CAAC,EAAE4C,EAAE,CAACjf,CAAC,EAAEif,EAAE,CAAC5C,CAAC,CAAE;MACpC8C,MAAM,EAAI,CAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAE;MACrBC,CAAC,EAAS,IAAIhe,aAAa,CAAC;QACxBqN,IAAI,EAAGpD,CAAC,CAAC,QAAQ,CAAC;QAClBD,CAAC,EAAMC,CAAC,CAAC,KAAK,CAAC;QACfgU,GAAG,EAAI,IAAIlU,SAAS,CAAC2T,GAAG;MAC5B,CAAC;IACL,CAAC,CAAC,CAAC;EACP,CAAC;EAEDlS,OAAO,CAACW,SAAS,CAAC+R,cAAc,GAAG,SAASA,cAAcA,CAAEhF,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;IACjE,IAAI,CAAC4C,IAAI,CAAC9C,CAAC,EAAE,GAAG,EAAEC,CAAC,EAAE,GAAG,EAAEC,CAAC,EAAE,KAAK,EAAE1d,EAAE,CAAC;EAC3C,CAAC;EAED8P,OAAO,CAACW,SAAS,CAACgS,UAAU,GAAG,SAASA,UAAUA,CAAEpR,OAAO,EAAE;IACzD,IAAI,CAACqR,cAAc,CAACrR,OAAO,CAAC;IAC5B,IAAI,CAACsR,gBAAgB,CAACtR,OAAO,CAAC;IAC9B,IAAI,CAACyO,QAAQ,IAAIzO,OAAO;EAC5B,CAAC;EAEDvB,OAAO,CAACW,SAAS,CAACkS,gBAAgB,GAAG,SAASA,gBAAgBA,CAAEtR,OAAO,EAAE;IACrE,IAAIA,OAAO,GAAG,CAAC,EAAE;MACb,IAAIK,EAAE,GAAG,IAAI,CAACsE,IAAI,CAAC5E,YAAY,CAAC,IAAI,CAAC0O,QAAQ,GAAGzO,OAAO,EAAE,IAAI,CAAC;MAC9D,IAAI,CAAC8E,YAAY,CAACzE,EAAE,CAACI,aAAa,CAAC,GAAGJ,EAAE;MACxC,IAAI,CAAC4O,IAAI,CAAC5O,EAAE,CAACI,aAAa,EAAE,KAAK,EAAE9R,EAAE,CAAC;IAC1C;EACJ,CAAC;EAED8P,OAAO,CAACW,SAAS,CAACmS,YAAY,GAAG,SAASA,YAAYA,CAAEpF,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;IAC7D,IAAI,CAAC4C,IAAI,CAAC9C,CAAC,EAAE,GAAG,EAAEC,CAAC,EAAE,GAAG,EAAEC,CAAC,EAAE,KAAK,EAAE1d,EAAE,CAAC;EAC3C,CAAC;EAED8P,OAAO,CAACW,SAAS,CAACO,OAAO,GAAG,SAASA,OAAOA,CAAEF,IAAI,EAAEsN,GAAG,EAAErN,WAAW,EAAE;IAClE,IAAIC,OAAO,GAAG,IAAI,CAACgF,IAAI,CAACnF,UAAU,CAACC,IAAI,EAAE,IAAI,EAAEC,WAAW,CAAC;IAC3D,IAAI,CAAC6O,aAAa,CAAC5O,OAAO,CAACc,aAAa,CAAC,GAAGd,OAAO;IAEnD,IAAI,CAACsP,IAAI,CAAC,aAAa,EAAEtgB,EAAE,CAAC;IAC5B,IAAI,CAACsgB,IAAI,CAACtP,OAAO,CAACc,aAAa,EAAE,MAAM,EAAE9R,EAAE,CAAC;IAE5C,IAAI,CAACkQ,IAAI,CAACkO,GAAG,CAAChY,IAAI,EAAEgY,GAAG,CAAC/X,GAAG,EAAE+X,GAAG,CAACxU,KAAK,EAAEwU,GAAG,CAACvU,MAAM,CAAC;IACnD,IAAI,CAACiH,IAAI,CAAC,CAAC;EACf,CAAC;EAEDhB,OAAO,CAACW,SAAS,CAACiS,cAAc,GAAG,SAASA,cAAcA,CAAErR,OAAO,EAAE;IACjE,IAAIA,OAAO,GAAG,CAAC,EAAE;MACb,IAAIK,EAAE,GAAG,IAAI,CAACsE,IAAI,CAAC5E,YAAY,CAAC,IAAI,CAAC0O,QAAQ,GAAGzO,OAAO,EAAE,KAAK,CAAC;MAC/D,IAAI,CAAC8E,YAAY,CAACzE,EAAE,CAACI,aAAa,CAAC,GAAGJ,EAAE;MACxC,IAAI,CAAC4O,IAAI,CAAC5O,EAAE,CAACI,aAAa,EAAE,KAAK,EAAE9R,EAAE,CAAC;IAC1C;EACJ,CAAC;EAED8P,OAAO,CAACW,SAAS,CAAC4O,QAAQ,GAAG,SAASA,QAAQA,CAAEwD,UAAU,EAAEzE,GAAG,EAAE;IAC7D,IAAI,CAAC0E,IAAI,CAAC,CAAC;IACX,IAAI,CAAC5S,IAAI,CAACkO,GAAG,CAAChY,IAAI,EAAEgY,GAAG,CAAC/X,GAAG,EAAE+X,GAAG,CAACxU,KAAK,EAAEwU,GAAG,CAACvU,MAAM,CAAC;IACnD,IAAI,CAACsG,IAAI,CAAC,CAAC;IACX,IAAI,CAAC0S,UAAU,CAACpD,SAAS,EAAE;MACvB,IAAI,CAACzP,SAAS,CAACoO,GAAG,CAACxU,KAAK,EAAE,CAAC,EAAE,CAAC,EAAEwU,GAAG,CAACvU,MAAM,EAAEuU,GAAG,CAAChY,IAAI,EAAEgY,GAAG,CAAC/X,GAAG,CAAC;IAClE;IACA,IAAIoX,CAAC,GAAG2B,aAAa,CAAC,IAAI,CAACpJ,IAAI,EAAE6M,UAAU,EAAEzE,GAAG,CAAC;IACjD,IAAI2E,KAAK,GAAGtF,CAAC,CAACY,OAAO,CAACvM,aAAa;MAAEkR,KAAK;IAC1C,IAAI,CAACnD,YAAY,CAACkD,KAAK,CAAC,GAAGtF,CAAC,CAACY,OAAO;IACpC,IAAIZ,CAAC,CAACvS,QAAQ,EAAE;MACZ8X,KAAK,GAAGvF,CAAC,CAACpM,OAAO,CAACS,aAAa;MAC/B,IAAI,CAACqE,YAAY,CAAC6M,KAAK,CAAC,GAAGvF,CAAC,CAACpM,OAAO;MACpC,IAAI,CAACiP,IAAI,CAAC,GAAG,GAAG0C,KAAK,GAAG,MAAM,CAAC;IACnC;IACA,IAAI,CAAC1C,IAAI,CAAC,GAAG,GAAGyC,KAAK,GAAG,KAAK,EAAE/iB,EAAE,CAAC;IAClC,IAAI,CAACijB,OAAO,CAAC,CAAC;EAClB,CAAC;EAEDnT,OAAO,CAACW,SAAS,CAACyS,cAAc,GAAG,SAASA,cAAcA,CAAEC,SAAS,EAAEC,SAAS,EAAE;IAC9E,IAAI,CAAC9C,IAAI,CAAC6C,SAAS,EAAE,GAAG,EAAEC,SAAS,EAAE,IAAI,EAAEpjB,EAAE,CAAC;EAClD,CAAC;EAED8P,OAAO,CAACW,SAAS,CAAC4S,YAAY,GAAG,SAASA,YAAYA,CAAEzZ,KAAK,EAAE;IAC3D,IAAI,CAAC0W,IAAI,CAAC1W,KAAK,EAAE,IAAI,EAAE5J,EAAE,CAAC;EAC9B,CAAC;EAED8P,OAAO,CAACW,SAAS,CAAC6S,UAAU,GAAG,SAASA,UAAUA,CAAEC,OAAO,EAAE;IACzD,IAAI,CAACjD,IAAI,CAACiD,OAAO,EAAE,IAAI,EAAEvjB,EAAE,CAAC;EAChC,CAAC;EAED8P,OAAO,CAACW,SAAS,CAAC+S,WAAW,GAAG,SAASA,WAAWA,CAAEC,QAAQ,EAAE;IAC5D,IAAI,CAACnD,IAAI,CAACmD,QAAQ,EAAE,IAAI,EAAEzjB,EAAE,CAAC;EACjC,CAAC;EAED8P,OAAO,CAACW,SAAS,CAACiT,cAAc,GAAG,SAASA,cAAcA,CAAEC,WAAW,EAAE;IACrE,IAAI,CAACrD,IAAI,CAACqD,WAAW,EAAE,IAAI,EAAE3jB,EAAE,CAAC;EACpC,CAAC;EAED8P,OAAO,CAACW,SAAS,CAACqS,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAI;IACtC,IAAI,CAAC5C,aAAa,CAAC7S,IAAI,CAAC,IAAI,CAACuW,QAAQ,CAAC,CAAC,CAAC;IACxC,IAAI,CAACtD,IAAI,CAAC,GAAG,EAAEtgB,EAAE,CAAC;EACtB,CAAC;EAED8P,OAAO,CAACW,SAAS,CAACwS,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;IAC5C,IAAI,CAAC3C,IAAI,CAAC,GAAG,EAAEtgB,EAAE,CAAC;IAClB,IAAI,CAAC4jB,QAAQ,CAAC,IAAI,CAAC1D,aAAa,CAACvC,GAAG,CAAC,CAAC,CAAC;EAC3C,CAAC;;EAGD;EACA7N,OAAO,CAACW,SAAS,CAACoT,MAAM,GAAG,SAASA,MAAMA,CAAE3gB,CAAC,EAAEqc,CAAC,EAAE;IAC9C,IAAI,CAACe,IAAI,CAACpd,CAAC,EAAE,GAAG,EAAEqc,CAAC,EAAE,IAAI,EAAEvf,EAAE,CAAC;EAClC,CAAC;EAED8P,OAAO,CAACW,SAAS,CAACqT,MAAM,GAAG,SAASA,MAAMA,CAAE5gB,CAAC,EAAEqc,CAAC,EAAE;IAC9C,IAAI,CAACe,IAAI,CAACpd,CAAC,EAAE,GAAG,EAAEqc,CAAC,EAAE,IAAI,EAAEvf,EAAE,CAAC;EAClC,CAAC;EAED8P,OAAO,CAACW,SAAS,CAACsT,MAAM,GAAG,SAASA,MAAMA,CAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;IAChE,IAAI,CAAC/D,IAAI,CAAC0D,EAAE,EAAE,GAAG,EAAEC,EAAE,EAAE,GAAG,EAAEC,EAAE,EAAE,GAAG,EAAEC,EAAE,EAAE,GAAG,EAAEC,EAAE,EAAE,GAAG,EAAEC,EAAE,EAAE,IAAI,EAAErkB,EAAE,CAAC;EACxE,CAAC;EAED8P,OAAO,CAACW,SAAS,CAAC6T,OAAO,GAAG,SAASA,OAAOA,CAAEN,EAAE,EAAEC,EAAE,EAAEG,EAAE,EAAEC,EAAE,EAAE;IAC1D,IAAI,CAAC/D,IAAI,CAAC0D,EAAE,EAAE,GAAG,EAAEC,EAAE,EAAE,GAAG,EAAEG,EAAE,EAAE,GAAG,EAAEC,EAAE,EAAE,IAAI,EAAErkB,EAAE,CAAC;EACtD,CAAC;EAED8P,OAAO,CAACW,SAAS,CAAC8T,OAAO,GAAG,SAASA,OAAOA,CAAEL,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;IAC1D,IAAI,CAAC/D,IAAI,CAAC4D,EAAE,EAAE,GAAG,EAAEC,EAAE,EAAE,GAAG,EAAEC,EAAE,EAAE,GAAG,EAAEC,EAAE,EAAE,IAAI,EAAErkB,EAAE,CAAC;EACtD,CAAC;EAED8P,OAAO,CAACW,SAAS,CAAC+T,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAI;IACxC,IAAI,CAAClE,IAAI,CAAC,GAAG,EAAEtgB,EAAE,CAAC;EACtB,CAAC;EAED8P,OAAO,CAACW,SAAS,CAACP,IAAI,GAAG,SAASA,IAAIA,CAAEhN,CAAC,EAAEqc,CAAC,EAAEkF,CAAC,EAAEC,CAAC,EAAE;IAChD,IAAI,CAACpE,IAAI,CAACpd,CAAC,EAAE,GAAG,EAAEqc,CAAC,EAAE,GAAG,EAAEkF,CAAC,EAAE,GAAG,EAAEC,CAAC,EAAE,KAAK,EAAE1kB,EAAE,CAAC;EACnD,CAAC;EAED8P,OAAO,CAACW,SAAS,CAACkU,OAAO,GAAG,SAASA,OAAOA,CAAEzhB,CAAC,EAAEqc,CAAC,EAAEqF,EAAE,EAAEC,EAAE,EAAE;IACxD,SAASC,EAAEA,CAACC,CAAC,EAAE;MAAE,OAAO7hB,CAAC,GAAG6hB,CAAC;IAAE;IAC/B,SAASC,EAAEA,CAACD,CAAC,EAAE;MAAE,OAAOxF,CAAC,GAAGwF,CAAC;IAAE;;IAE/B;IACA;IACA,IAAIE,CAAC,GAAG,kBAAkB;IAE1B,IAAI,CAACpB,MAAM,CAACiB,EAAE,CAAC,CAAC,CAAC,EAAEE,EAAE,CAACH,EAAE,CAAC,CAAC;IAC1B,IAAI,CAACd,MAAM,CACPe,EAAE,CAACF,EAAE,GAAGK,CAAC,CAAC,EAAGD,EAAE,CAACH,EAAE,CAAC,EACnBC,EAAE,CAACF,EAAE,CAAC,EAAOI,EAAE,CAACH,EAAE,GAAGI,CAAC,CAAC,EACvBH,EAAE,CAACF,EAAE,CAAC,EAAOI,EAAE,CAAC,CAAC,CACrB,CAAC;IACD,IAAI,CAACjB,MAAM,CACPe,EAAE,CAACF,EAAE,CAAC,EAAOI,EAAE,CAAC,CAACH,EAAE,GAAGI,CAAC,CAAC,EACxBH,EAAE,CAACF,EAAE,GAAGK,CAAC,CAAC,EAAGD,EAAE,CAAC,CAACH,EAAE,CAAC,EACpBC,EAAE,CAAC,CAAC,CAAC,EAAQE,EAAE,CAAC,CAACH,EAAE,CACvB,CAAC;IACD,IAAI,CAACd,MAAM,CACPe,EAAE,CAAC,CAACF,EAAE,GAAGK,CAAC,CAAC,EAAGD,EAAE,CAAC,CAACH,EAAE,CAAC,EACrBC,EAAE,CAAC,CAACF,EAAE,CAAC,EAAOI,EAAE,CAAC,CAACH,EAAE,GAAGI,CAAC,CAAC,EACzBH,EAAE,CAAC,CAACF,EAAE,CAAC,EAAOI,EAAE,CAAC,CAAC,CACtB,CAAC;IACD,IAAI,CAACjB,MAAM,CACPe,EAAE,CAAC,CAACF,EAAE,CAAC,EAAOI,EAAE,CAACH,EAAE,GAAGI,CAAC,CAAC,EACxBH,EAAE,CAAC,CAACF,EAAE,GAAGK,CAAC,CAAC,EAAGD,EAAE,CAACH,EAAE,CAAC,EACpBC,EAAE,CAAC,CAAC,CAAC,EAASE,EAAE,CAACH,EAAE,CACvB,CAAC;EACL,CAAC;EAED/U,OAAO,CAACW,SAAS,CAACyU,MAAM,GAAG,SAASA,MAAMA,CAAEhiB,CAAC,EAAEqc,CAAC,EAAE/B,CAAC,EAAE;IACjD,IAAI,CAACmH,OAAO,CAACzhB,CAAC,EAAEqc,CAAC,EAAE/B,CAAC,EAAEA,CAAC,CAAC;EAC5B,CAAC;EAED1N,OAAO,CAACW,SAAS,CAAC0U,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAI;IAC1C,IAAI,CAAC7E,IAAI,CAAC,GAAG,EAAEtgB,EAAE,CAAC;EACtB,CAAC;EAED8P,OAAO,CAACW,SAAS,CAAC2U,GAAG,GAAG,SAASA,GAAGA,CAAA,EAAI;IACpC,IAAI,CAAC9E,IAAI,CAAC,GAAG,EAAEtgB,EAAE,CAAC;EACtB,CAAC;EAED8P,OAAO,CAACW,SAAS,CAACN,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAI;IACtC,IAAI,CAACmQ,IAAI,CAAC,KAAK,EAAEtgB,EAAE,CAAC;EACxB,CAAC;EAED8P,OAAO,CAACW,SAAS,CAAC4U,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAI;IAClD,IAAI,CAAC/E,IAAI,CAAC,KAAK,EAAEtgB,EAAE,CAAC;EACxB,CAAC;EAED8P,OAAO,CAACW,SAAS,CAAC6U,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAI;IACpD,IAAI,CAAChF,IAAI,CAAC,GAAG,EAAEtgB,EAAE,CAAC;EACtB,CAAC;EAED8P,OAAO,CAACW,SAAS,CAACK,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAI;IACtC,IAAI,CAACwP,IAAI,CAAC,GAAG,EAAEtgB,EAAE,CAAC;EACtB,CAAC;EAED8P,OAAO,CAACW,SAAS,CAAC8U,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAI;IAClD,IAAI,CAACjF,IAAI,CAAC,GAAG,EAAEtgB,EAAE,CAAC;EACtB,CAAC;EAED8P,OAAO,CAACW,SAAS,CAAC5F,SAAS,GAAG,SAASA,SAASA,CAAEnE,GAAG,EAAE;IACnD,IAAI6B,GAAG,GAAG,IAAI,CAACyN,IAAI,CAAC9E,QAAQ,CAACxK,GAAG,CAAC;IACjC,IAAI6B,GAAG,EAAE;MAAE;MACP,IAAI,CAAC0N,WAAW,CAAC1N,GAAG,CAACuJ,aAAa,CAAC,GAAGvJ,GAAG;MACzC,IAAI,CAAC+X,IAAI,CAAC/X,GAAG,CAACuJ,aAAa,EAAE,KAAK,EAAE9R,EAAE,CAAC;IAC3C;EACJ,CAAC;EAED8P,OAAO,CAACW,SAAS,CAAC+U,OAAO,GAAG,SAASA,OAAOA,CAAEhS,GAAG,EAAE;IAC/C,IAAIzG,IAAI,GAAG,IAAI;IACfyG,GAAG,CAACiS,KAAK,CAAC,QAAQ,CAAC,CAAC5Y,OAAO,CAAC,UAAS6Y,IAAI,EAAC;MACtC3Y,IAAI,CAACuT,IAAI,CAAC,IAAI,EAAEoF,IAAI,EAAE1lB,EAAE,CAAC;IAC7B,CAAC,CAAC;EACN,CAAC;;EAED;EACA8P,OAAO,CAACW,SAAS,CAACmT,QAAQ,GAAG,SAASA,QAAQA,CAAE+B,GAAG,EAAE;IACjD,IAAIA,GAAG,IAAI,IAAI,EAAE;MACb,IAAI,CAAC7F,QAAQ,GAAG6F,GAAG,CAACtU,OAAO;MAC3B,IAAI,CAAC0O,OAAO,GAAG4F,GAAG,CAACC,MAAM;IAC7B,CAAC,MAAM;MACH,OAAO;QACHvU,OAAO,EAAE,IAAI,CAACyO,QAAQ;QACtB8F,MAAM,EAAE,IAAI,CAAC7F;MACjB,CAAC;IACL;EACJ,CAAC;EAEDjQ,OAAO,CAACW,SAAS,CAACyR,OAAO,GAAG,SAASA,OAAOA,CAAE2D,CAAC,EAAE;IAC7C,IAAIjf,CAAC,GAAG,IAAI,CAACmZ,OAAO;IACpB,IAAIxU,CAAC,GAAG3E,CAAC,CAAC,CAAC,CAAC;MAAE8W,CAAC,GAAG9W,CAAC,CAAC,CAAC,CAAC;MAAEmN,CAAC,GAAGnN,CAAC,CAAC,CAAC,CAAC;MAAE2Z,CAAC,GAAG3Z,CAAC,CAAC,CAAC,CAAC;MAAE4Z,CAAC,GAAG5Z,CAAC,CAAC,CAAC,CAAC;MAAElC,CAAC,GAAGkC,CAAC,CAAC,CAAC,CAAC;IAC9D,OAAO;MACH1D,CAAC,EAAEqI,CAAC,GAACsa,CAAC,CAAC3iB,CAAC,GAAG6Q,CAAC,GAAC8R,CAAC,CAACtG,CAAC,GAAGiB,CAAC;MACpBjB,CAAC,EAAE7B,CAAC,GAACmI,CAAC,CAAC3iB,CAAC,GAAGqd,CAAC,GAACsF,CAAC,CAACtG,CAAC,GAAG7a;IACvB,CAAC;EACL,CAAC;EAED,OAAOoL,OAAO;AAClB,CAAC,CAACxL,aAAa,CAAE;AAEjB,SAASwhB,OAAOA,CAAC9T,GAAG,EAAE;EAClB,OAAOA,GAAG,CAAChO,OAAO,CAAC,sBAAsB,EAAE,IAAI,CAAC;AACpD;AAEA,SAAS+hB,YAAYA,CAACC,OAAO,EAAE;EAC3B;EACA,IAAIpB,EAAE,GAAG,wIAAwI;EACjJ,IAAIhe,CAAC,GAAGge,EAAE,CAAC9d,IAAI,CAACkf,OAAO,CAAC;EACxB,IAAI,CAACpf,CAAC,EAAE;IACJ,OAAO;MAAEiS,QAAQ,EAAE,EAAE;MAAEoN,UAAU,EAAE;IAAa,CAAC;EACrD;EACA,IAAIpN,QAAQ,GAAGjS,CAAC,CAAC,CAAC,CAAC,GAAGsf,QAAQ,CAACtf,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE;EAC7C,OAAO;IACHuf,MAAM,EAAOvf,CAAC,CAAC,CAAC,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAACd,WAAW,CAAC,CAAC,IAAI,QAAQ;IACnDsgB,OAAO,EAAMxf,CAAC,CAAC,CAAC,CAAC;IACjByf,IAAI,EAASzf,CAAC,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC1C,IAAI,CAAC0C,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3CiS,QAAQ,EAAKA,QAAQ;IACrByN,UAAU,EAAG1f,CAAC,CAAC,EAAE,CAAC,GAAGA,CAAC,CAAC,EAAE,CAAC,IAAI,QAAQ,GAAGiS,QAAQ,GAAGqN,QAAQ,CAACtf,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI;IAC9Eqf,UAAU,EAAGrf,CAAC,CAAC,EAAE,CAAC,CAAC6e,KAAK,CAAC,UAAU,CAAC,CAAC9J,GAAG,CAACmK,OAAO;EACpD,CAAC;AACL;AAEA,SAASS,UAAUA,CAACC,KAAK,EAAE;EACvB,SAASC,QAAQA,CAACvZ,IAAI,EAAE;IACpB,IAAIsZ,KAAK,CAACH,IAAI,EAAE;MACZnZ,IAAI,IAAI,OAAO;IACnB;IACA,IAAIsZ,KAAK,CAACL,MAAM,EAAE;MACdjZ,IAAI,IAAI,SAAS;IACrB;IACA,OAAOA,IAAI,CAACpH,WAAW,CAAC,CAAC;EAC7B;EACA,IAAImgB,UAAU,GAAGO,KAAK,CAACP,UAAU;EACjC,IAAI/Y,IAAI,EAAExG,GAAG;EACb,IAAIuf,UAAU,YAAY5T,KAAK,EAAE;IAC7B,KAAK,IAAIrP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGijB,UAAU,CAAChjB,MAAM,EAAE,EAAED,CAAC,EAAE;MACxCkK,IAAI,GAAGuZ,QAAQ,CAACR,UAAU,CAACjjB,CAAC,CAAC,CAAC;MAC9B0D,GAAG,GAAGggB,aAAa,CAACxZ,IAAI,CAAC;MACzB,IAAIxG,GAAG,EAAE;QACL;MACJ;IACJ;EACJ,CAAC,MAAM;IACHA,GAAG,GAAGggB,aAAa,CAACT,UAAU,CAACngB,WAAW,CAAC,CAAC,CAAC;EACjD;EACA,OAAO,OAAOY,GAAG,IAAI,UAAU,EAAE;IAC7BA,GAAG,GAAGA,GAAG,CAAC,CAAC;EACf;EACA,IAAI,CAACA,GAAG,EAAE;IACNA,GAAG,GAAG,aAAa;EACvB;EACA,OAAOA,GAAG;AACd;AAEA,IAAIggB,aAAa,GAAG;EAChB,OAAO,EAAsB,aAAa;EAC1C,YAAY,EAAiB,YAAY;EACzC,cAAc,EAAe,cAAc;EAC3C,mBAAmB,EAAU,kBAAkB;EAC/C,YAAY,EAAiB,WAAW;EACxC,iBAAiB,EAAY,gBAAgB;EAC7C,mBAAmB,EAAU,mBAAmB;EAChD,wBAAwB,EAAK,uBAAuB;EACpD,WAAW,EAAkB,SAAS;EACtC,gBAAgB,EAAa,cAAc;EAC3C,kBAAkB,EAAW,iBAAiB;EAC9C,uBAAuB,EAAM,qBAAqB;EAClD,cAAc,EAAe,cAAc;EAC3C,mBAAmB,EAAU,cAAc;EAC3C,qBAAqB,EAAQ,cAAc;EAC3C,0BAA0B,EAAG;AACjC,CAAC;AAED,SAASC,SAASA,CAACC,KAAK,EAAE1Z,IAAI,EAAE;EAC5B0Z,KAAK,GAAGA,KAAK,CAAC9gB,WAAW,CAAC,CAAC;EAC3B4gB,aAAa,CAACE,KAAK,CAAC,GAAG,YAAW;IAC9B,OAAOF,aAAa,CAACxZ,IAAI,CAAC;EAC9B,CAAC;EACDwZ,aAAa,CAACE,KAAK,GAAG,OAAO,CAAC,GAAG,YAAW;IACxC,OAAOF,aAAa,CAACxZ,IAAI,GAAG,OAAO,CAAC;EACxC,CAAC;EACDwZ,aAAa,CAACE,KAAK,GAAG,SAAS,CAAC,GAAG,YAAW;IAC1C,OAAOF,aAAa,CAACxZ,IAAI,GAAG,SAAS,CAAC;EAC1C,CAAC;EACDwZ,aAAa,CAACE,KAAK,GAAG,cAAc,CAAC,GAAG,YAAW;IAC/C,OAAOF,aAAa,CAACxZ,IAAI,GAAG,cAAc,CAAC;EAC/C,CAAC;AACL;;AAEA;AACA;AACA;;AAEAyZ,SAAS,CAAC,iBAAiB,EAAG,OAAO,CAAC;AACtCA,SAAS,CAAC,aAAa,EAAO,WAAW,CAAC;AAC1CA,SAAS,CAAC,OAAO,EAAa,YAAY,CAAC;AAC3CA,SAAS,CAAC,WAAW,EAAS,YAAY,CAAC;AAC3CA,SAAS,CAAC,SAAS,EAAW,YAAY,CAAC;AAC3CA,SAAS,CAAC,QAAQ,EAAY,YAAY,CAAC;AAC3CA,SAAS,CAAC,SAAS,EAAW,YAAY,CAAC;AAC3CA,SAAS,CAAC,QAAQ,EAAY,WAAW,CAAC;AAC1CA,SAAS,CAAC,aAAa,EAAO,WAAW,CAAC;AAE1C,SAASE,UAAUA,CAAC3Z,IAAI,EAAExG,GAAG,EAAE;EAC3B,IAAI3D,SAAS,CAACE,MAAM,IAAI,CAAC,EAAE;IACvB,KAAK,IAAID,CAAC,IAAIkK,IAAI,EAAE;MAChB,IAAIiF,cAAc,CAACjF,IAAI,EAAElK,CAAC,CAAC,EAAE;QACzB6jB,UAAU,CAAC7jB,CAAC,EAAEkK,IAAI,CAAClK,CAAC,CAAC,CAAC;MAC1B;IACJ;EACJ,CAAC,MAAM;IACHkK,IAAI,GAAGA,IAAI,CAACpH,WAAW,CAAC,CAAC;IACzB4gB,aAAa,CAACxZ,IAAI,CAAC,GAAGxG,GAAG;;IAEzB;IACA;IACA;IACA,QAAQwG,IAAI;MACV,KAAK,aAAa;QAAiBwZ,aAAa,CAAC,YAAY,CAAC,GAAgBhgB,GAAG;QAAE;MACnF,KAAK,kBAAkB;QAAYggB,aAAa,CAAC,iBAAiB,CAAC,GAAWhgB,GAAG;QAAE;MACnF,KAAK,oBAAoB;QAAUggB,aAAa,CAAC,mBAAmB,CAAC,GAAShgB,GAAG;QAAE;MACnF,KAAK,yBAAyB;QAAKggB,aAAa,CAAC,wBAAwB,CAAC,GAAIhgB,GAAG;QAAE;MACnF,KAAK,cAAc;QAAgBggB,aAAa,CAAC,OAAO,CAAC,GAAqBhgB,GAAG;QAAE;MACnF,KAAK,mBAAmB;QAAWggB,aAAa,CAAC,YAAY,CAAC,GAAgBhgB,GAAG;QAAE;MACnF,KAAK,qBAAqB;QAASggB,aAAa,CAAC,cAAc,CAAC,GAAchgB,GAAG;QAAE;MACnF,KAAK,0BAA0B;QAAIggB,aAAa,CAAC,mBAAmB,CAAC,GAAShgB,GAAG;QAAE;MACnF,KAAK,aAAa;QAAiBggB,aAAa,CAAC,WAAW,CAAC,GAAiBhgB,GAAG;QAAE;MACnF,KAAK,kBAAkB;QAAYggB,aAAa,CAAC,gBAAgB,CAAC,GAAYhgB,GAAG;QAAE;MACnF,KAAK,oBAAoB;QAAUggB,aAAa,CAAC,kBAAkB,CAAC,GAAUhgB,GAAG;QAAE;MACnF,KAAK,yBAAyB;QAAKggB,aAAa,CAAC,uBAAuB,CAAC,GAAKhgB,GAAG;QAAE;IACrF;EACJ;AACJ;AAEA,SAASga,IAAIA,CAACnV,CAAC,EAAEmS,CAAC,EAAE;EAChB,IAAIrd,EAAE,GAAGkL,CAAC,CAAC,CAAC,CAAC;IAAEvK,EAAE,GAAGuK,CAAC,CAAC,CAAC,CAAC;IAAE5J,EAAE,GAAG4J,CAAC,CAAC,CAAC,CAAC;IAAEub,EAAE,GAAGvb,CAAC,CAAC,CAAC,CAAC;IAAEwb,EAAE,GAAGxb,CAAC,CAAC,CAAC,CAAC;IAAEyb,EAAE,GAAGzb,CAAC,CAAC,CAAC,CAAC;EACpE,IAAIjL,EAAE,GAAGod,CAAC,CAAC,CAAC,CAAC;IAAEzc,EAAE,GAAGyc,CAAC,CAAC,CAAC,CAAC;IAAE9b,EAAE,GAAG8b,CAAC,CAAC,CAAC,CAAC;IAAEuJ,EAAE,GAAGvJ,CAAC,CAAC,CAAC,CAAC;IAAEwJ,EAAE,GAAGxJ,CAAC,CAAC,CAAC,CAAC;IAAEyJ,EAAE,GAAGzJ,CAAC,CAAC,CAAC,CAAC;EACpE,OAAO,CACHrd,EAAE,GAACC,EAAE,GAAGU,EAAE,GAACY,EAAE,EAAWvB,EAAE,GAACY,EAAE,GAAGD,EAAE,GAACimB,EAAE,EACrCtlB,EAAE,GAACrB,EAAE,GAAGwmB,EAAE,GAACllB,EAAE,EAAWD,EAAE,GAACV,EAAE,GAAG6lB,EAAE,GAACG,EAAE,EACrCF,EAAE,GAACzmB,EAAE,GAAG0mB,EAAE,GAACplB,EAAE,GAAGslB,EAAE,EAAMH,EAAE,GAAC9lB,EAAE,GAAG+lB,EAAE,GAACC,EAAE,GAAGE,EAAE,CAC7C;AACL;AAEA,SAAS1G,gBAAgBA,CAAC7Z,CAAC,EAAE;EACzB,OAAOA,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AAC3F;AAEA,IAAIwgB,mBAAmB,GAAG;EACtBtW,IAAI,EAAa,CAAC;EAClBqU,MAAM,EAAW,CAAC;EAClBkC,aAAa,EAAI,CAAC;EAClBC,SAAS,EAAQ,CAAC;EAClBC,WAAW,EAAM,CAAC;EAClBC,aAAa,EAAI,CAAC;EAClBC,cAAc,EAAG,CAAC;EAClBtX,IAAI,EAAa;AACrB,CAAC;AAED,SACIrD,WAAW,IAAI4a,QAAQ,EACvBtoB,YAAY,EACZynB,UAAU,EACVd,YAAY,EACZQ,UAAU,EACVha,SAAS,EACTC,UAAU,EACVhH,eAAe,EACf4C,eAAe,EACfgf,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}