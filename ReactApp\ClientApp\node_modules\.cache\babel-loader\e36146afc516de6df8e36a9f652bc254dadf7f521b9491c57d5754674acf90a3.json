{"ast": null, "code": "import { deflate as pakoDeflate } from '@progress/pako-esm';\nexport var deflate = pakoDeflate;\nexport function supportsDeflate() {\n  return true;\n}", "map": {"version": 3, "names": ["deflate", "pakoDeflate", "supportsDeflate"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/pdf/deflate.js"], "sourcesContent": ["import { deflate as pakoDeflate } from '@progress/pako-esm';\n\nexport var deflate = pakoDeflate;\n\nexport function supportsDeflate() {\n    return true;\n}\n\n"], "mappings": "AAAA,SAASA,OAAO,IAAIC,WAAW,QAAQ,oBAAoB;AAE3D,OAAO,IAAID,OAAO,GAAGC,WAAW;AAEhC,OAAO,SAASC,eAAeA,CAAA,EAAG;EAC9B,OAAO,IAAI;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}