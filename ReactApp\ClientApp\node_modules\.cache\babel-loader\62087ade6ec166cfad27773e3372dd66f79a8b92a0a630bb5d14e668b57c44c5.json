{"ast": null, "code": "import { Direction } from \"./direction.enum\";\nimport { adjustDST } from \"./adjust-dst\";\nimport { cloneDate } from './clone-date';\n/**\n * @hidden\n *\n * A function which returns the next or previous date for a specific week day. For example, `Day.Monday`.\n *\n * @param date - The date to calculate from.\n * @param weekDay - The `Day` enum specifying the desired week day.\n * @param direction - The `Direction` enum specifying the calculation direction.\n * @returns - A `Date` instance.\n *\n * @example\n * ```ts-no-run\n * dayOfWeek(new Date(2016, 0, 1), Day.Wednesday, Direction.Forward); // 2016-01-06, Wednesday\n * dayOfWeek(new Date(2016, 0, 1), Day.Wednesday, Direction.Backward); // 2015-12-30, Wednesday\n * ```\n */\nexport var dayOfWeek = function (date, weekDay, direction) {\n  if (direction === void 0) {\n    direction = Direction.Forward;\n  }\n  var newDate = cloneDate(date);\n  var newDay = (weekDay - newDate.getDay() + 7 * direction) % 7;\n  newDate.setDate(newDate.getDate() + newDay);\n  return adjustDST(newDate, date.getHours());\n};", "map": {"version": 3, "names": ["Direction", "adjustDST", "cloneDate", "dayOfWeek", "date", "weekDay", "direction", "Forward", "newDate", "newDay", "getDay", "setDate", "getDate", "getHours"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-date-math/dist/es/day-of-week.js"], "sourcesContent": ["import { Direction } from \"./direction.enum\";\nimport { adjustDST } from \"./adjust-dst\";\nimport { cloneDate } from './clone-date';\n/**\n * @hidden\n *\n * A function which returns the next or previous date for a specific week day. For example, `Day.Monday`.\n *\n * @param date - The date to calculate from.\n * @param weekDay - The `Day` enum specifying the desired week day.\n * @param direction - The `Direction` enum specifying the calculation direction.\n * @returns - A `Date` instance.\n *\n * @example\n * ```ts-no-run\n * dayOfWeek(new Date(2016, 0, 1), Day.Wednesday, Direction.Forward); // 2016-01-06, Wednesday\n * dayOfWeek(new Date(2016, 0, 1), Day.Wednesday, Direction.Backward); // 2015-12-30, Wednesday\n * ```\n */\nexport var dayOfWeek = function (date, weekDay, direction) {\n    if (direction === void 0) { direction = Direction.Forward; }\n    var newDate = cloneDate(date);\n    var newDay = ((weekDay - newDate.getDay()) + (7 * direction)) % 7;\n    newDate.setDate(newDate.getDate() + newDay);\n    return adjustDST(newDate, date.getHours());\n};\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,SAAS,QAAQ,cAAc;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,SAAS,GAAG,SAAAA,CAAUC,IAAI,EAAEC,OAAO,EAAEC,SAAS,EAAE;EACvD,IAAIA,SAAS,KAAK,KAAK,CAAC,EAAE;IAAEA,SAAS,GAAGN,SAAS,CAACO,OAAO;EAAE;EAC3D,IAAIC,OAAO,GAAGN,SAAS,CAACE,IAAI,CAAC;EAC7B,IAAIK,MAAM,GAAG,CAAEJ,OAAO,GAAGG,OAAO,CAACE,MAAM,CAAC,CAAC,GAAK,CAAC,GAAGJ,SAAU,IAAI,CAAC;EACjEE,OAAO,CAACG,OAAO,CAACH,OAAO,CAACI,OAAO,CAAC,CAAC,GAAGH,MAAM,CAAC;EAC3C,OAAOR,SAAS,CAACO,OAAO,EAAEJ,IAAI,CAACS,QAAQ,CAAC,CAAC,CAAC;AAC9C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}