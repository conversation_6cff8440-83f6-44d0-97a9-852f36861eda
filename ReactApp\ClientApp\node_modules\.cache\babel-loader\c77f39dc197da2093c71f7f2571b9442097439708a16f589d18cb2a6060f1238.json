{"ast": null, "code": "import Class from '../class';\nimport support from '../support';\nimport namedColors from './named-colors';\nvar browser = support.browser;\nvar matchNamedColor = function (color) {\n  var colorNames = Object.keys(namedColors);\n  colorNames.push(\"transparent\");\n  var regexp = new RegExp(\"^(\" + colorNames.join(\"|\") + \")(\\\\W|$)\", \"i\");\n  matchNamedColor = function (color) {\n    return regexp.exec(color);\n  };\n  return regexp.exec(color);\n};\nvar BaseColor = function (Class) {\n  function BaseColor() {\n    Class.call(this);\n  }\n  if (Class) BaseColor.__proto__ = Class;\n  BaseColor.prototype = Object.create(Class && Class.prototype);\n  BaseColor.prototype.constructor = BaseColor;\n  BaseColor.prototype.toHSV = function toHSV() {\n    return this;\n  };\n  BaseColor.prototype.toRGB = function toRGB() {\n    return this;\n  };\n  BaseColor.prototype.toHex = function toHex(options) {\n    return this.toBytes().toHex(options);\n  };\n  BaseColor.prototype.toBytes = function toBytes() {\n    return this;\n  };\n  BaseColor.prototype.toCss = function toCss(options) {\n    return \"#\" + this.toHex(options);\n  };\n  BaseColor.prototype.toCssRgba = function toCssRgba() {\n    var rgb = this.toBytes();\n    return \"rgba(\" + rgb.r + \", \" + rgb.g + \", \" + rgb.b + \", \" + parseFloat(Number(this.a).toFixed(3)) + \")\";\n  };\n  BaseColor.prototype.toDisplay = function toDisplay() {\n    if (browser.msie && browser.version < 9) {\n      return this.toCss(); // no RGBA support; does it support any opacity in colors?\n    }\n    return this.toCssRgba();\n  };\n  BaseColor.prototype.equals = function equals(c) {\n    return c === this || c !== null && c !== undefined && this.toCssRgba() === parseColor(c).toCssRgba();\n  };\n  BaseColor.prototype.diff = function diff(other) {\n    if (other === null) {\n      return NaN;\n    }\n    var c1 = this.toBytes();\n    var c2 = other.toBytes();\n    return Math.sqrt(Math.pow((c1.r - c2.r) * 0.30, 2) + Math.pow((c1.g - c2.g) * 0.59, 2) + Math.pow((c1.b - c2.b) * 0.11, 2));\n  };\n  BaseColor.prototype.clone = function clone() {\n    var c = this.toBytes();\n    if (c === this) {\n      c = new Bytes(c.r, c.g, c.b, c.a);\n    }\n    return c;\n  };\n  return BaseColor;\n}(Class);\nvar RGB = function (BaseColor) {\n  function RGB(r, g, b, a) {\n    BaseColor.call(this);\n    this.r = r;\n    this.g = g;\n    this.b = b;\n    this.a = a;\n  }\n  if (BaseColor) RGB.__proto__ = BaseColor;\n  RGB.prototype = Object.create(BaseColor && BaseColor.prototype);\n  RGB.prototype.constructor = RGB;\n  RGB.prototype.toHSV = function toHSV() {\n    var ref = this;\n    var r = ref.r;\n    var g = ref.g;\n    var b = ref.b;\n    var min = Math.min(r, g, b);\n    var max = Math.max(r, g, b);\n    var delta = max - min;\n    var v = max;\n    var h, s;\n    if (delta === 0) {\n      return new HSV(0, 0, v, this.a);\n    }\n    if (max !== 0) {\n      s = delta / max;\n      if (r === max) {\n        h = (g - b) / delta;\n      } else if (g === max) {\n        h = 2 + (b - r) / delta;\n      } else {\n        h = 4 + (r - g) / delta;\n      }\n      h *= 60;\n      if (h < 0) {\n        h += 360;\n      }\n    } else {\n      s = 0;\n      h = -1;\n    }\n    return new HSV(h, s, v, this.a);\n  };\n  RGB.prototype.toHSL = function toHSL() {\n    var ref = this;\n    var r = ref.r;\n    var g = ref.g;\n    var b = ref.b;\n    var max = Math.max(r, g, b);\n    var min = Math.min(r, g, b);\n    var h,\n      s,\n      l = (max + min) / 2;\n    if (max === min) {\n      h = s = 0;\n    } else {\n      var d = max - min;\n      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n      switch (max) {\n        case r:\n          h = (g - b) / d + (g < b ? 6 : 0);\n          break;\n        case g:\n          h = (b - r) / d + 2;\n          break;\n        case b:\n          h = (r - g) / d + 4;\n          break;\n        default:\n          break;\n      }\n    }\n    return new HSL(h * 60, s * 100, l * 100, this.a);\n  };\n  RGB.prototype.toBytes = function toBytes() {\n    return new Bytes(this.r * 255, this.g * 255, this.b * 255, this.a);\n  };\n  return RGB;\n}(BaseColor);\nvar Bytes = function (RGB) {\n  function Bytes(r, g, b, a) {\n    RGB.call(this, Math.round(r), Math.round(g), Math.round(b), a);\n  }\n  if (RGB) Bytes.__proto__ = RGB;\n  Bytes.prototype = Object.create(RGB && RGB.prototype);\n  Bytes.prototype.constructor = Bytes;\n  Bytes.prototype.toRGB = function toRGB() {\n    return new RGB(this.r / 255, this.g / 255, this.b / 255, this.a);\n  };\n  Bytes.prototype.toHSV = function toHSV() {\n    return this.toRGB().toHSV();\n  };\n  Bytes.prototype.toHSL = function toHSL() {\n    return this.toRGB().toHSL();\n  };\n  Bytes.prototype.toHex = function toHex(options) {\n    var value = hex(this.r, 2) + hex(this.g, 2) + hex(this.b, 2);\n    if (options && options.alpha) {\n      value += hex(Math.round(this.a * 255), 2);\n    }\n    return value;\n  };\n  Bytes.prototype.toBytes = function toBytes() {\n    return this;\n  };\n  return Bytes;\n}(RGB);\nfunction hex(n, width, pad) {\n  if (pad === void 0) pad = \"0\";\n  var result = n.toString(16);\n  while (width > result.length) {\n    result = pad + result;\n  }\n  return result;\n}\nvar HSV = function (BaseColor) {\n  function HSV(h, s, v, a) {\n    BaseColor.call(this);\n    this.h = h;\n    this.s = s;\n    this.v = v;\n    this.a = a;\n  }\n  if (BaseColor) HSV.__proto__ = BaseColor;\n  HSV.prototype = Object.create(BaseColor && BaseColor.prototype);\n  HSV.prototype.constructor = HSV;\n  HSV.prototype.toRGB = function toRGB() {\n    var ref = this;\n    var h = ref.h;\n    var s = ref.s;\n    var v = ref.v;\n    var r, g, b;\n    if (s === 0) {\n      r = g = b = v;\n    } else {\n      h /= 60;\n      var i = Math.floor(h);\n      var f = h - i;\n      var p = v * (1 - s);\n      var q = v * (1 - s * f);\n      var t = v * (1 - s * (1 - f));\n      switch (i) {\n        case 0:\n          r = v;\n          g = t;\n          b = p;\n          break;\n        case 1:\n          r = q;\n          g = v;\n          b = p;\n          break;\n        case 2:\n          r = p;\n          g = v;\n          b = t;\n          break;\n        case 3:\n          r = p;\n          g = q;\n          b = v;\n          break;\n        case 4:\n          r = t;\n          g = p;\n          b = v;\n          break;\n        default:\n          r = v;\n          g = p;\n          b = q;\n          break;\n      }\n    }\n    return new RGB(r, g, b, this.a);\n  };\n  HSV.prototype.toHSL = function toHSL() {\n    return this.toRGB().toHSL();\n  };\n  HSV.prototype.toBytes = function toBytes() {\n    return this.toRGB().toBytes();\n  };\n  return HSV;\n}(BaseColor);\nvar HSL = function (BaseColor) {\n  function HSL(h, s, l, a) {\n    BaseColor.call(this);\n    this.h = h;\n    this.s = s;\n    this.l = l;\n    this.a = a;\n  }\n  if (BaseColor) HSL.__proto__ = BaseColor;\n  HSL.prototype = Object.create(BaseColor && BaseColor.prototype);\n  HSL.prototype.constructor = HSL;\n  HSL.prototype.toRGB = function toRGB() {\n    var h = this.h / 360;\n    var s = this.s / 100;\n    var l = this.l / 100;\n    var r, g, b;\n    if (s === 0) {\n      r = g = b = l; // achromatic\n    } else {\n      var q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n      var p = 2 * l - q;\n      r = hue2rgb(p, q, h + 1 / 3);\n      g = hue2rgb(p, q, h);\n      b = hue2rgb(p, q, h - 1 / 3);\n    }\n    return new RGB(r, g, b, this.a);\n  };\n  HSL.prototype.toHSV = function toHSV() {\n    return this.toRGB().toHSV();\n  };\n  HSL.prototype.toBytes = function toBytes() {\n    return this.toRGB().toBytes();\n  };\n  return HSL;\n}(BaseColor);\nfunction hue2rgb(p, q, s) {\n  var t = s;\n  if (t < 0) {\n    t += 1;\n  }\n  if (t > 1) {\n    t -= 1;\n  }\n  if (t < 1 / 6) {\n    return p + (q - p) * 6 * t;\n  }\n  if (t < 1 / 2) {\n    return q;\n  }\n  if (t < 2 / 3) {\n    return p + (q - p) * (2 / 3 - t) * 6;\n  }\n  return p;\n}\nfunction alphaFromHex(a) {\n  return parseFloat(parseFloat(parseInt(a, 16) / 255).toFixed(3));\n}\nexport { RGB, Bytes, HSV, HSL };\nexport default function parseColor(value, safe) {\n  var m, ret;\n  if (!value || value === \"none\") {\n    return null;\n  }\n  if (value instanceof BaseColor) {\n    return value;\n  }\n  var color = value.toLowerCase();\n  if (m = matchNamedColor(color)) {\n    if (m[1] === \"transparent\") {\n      color = new RGB(1, 1, 1, 0);\n    } else {\n      color = parseColor(namedColors[m[1]], safe);\n    }\n    color.match = [m[1]];\n    return color;\n  }\n  if (m = /^#?([0-9a-f]{2})([0-9a-f]{2})([0-9a-f]{2})\\b/i.exec(color)) {\n    ret = new Bytes(parseInt(m[1], 16), parseInt(m[2], 16), parseInt(m[3], 16), 1);\n  } else if (m = /^#?([0-9a-f])([0-9a-f])([0-9a-f])\\b/i.exec(color)) {\n    ret = new Bytes(parseInt(m[1] + m[1], 16), parseInt(m[2] + m[2], 16), parseInt(m[3] + m[3], 16), 1);\n  } else if (m = /^#?([0-9a-f])([0-9a-f])([0-9a-f])([0-9a-f])\\b/i.exec(color)) {\n    // Parse 4 digit hex color\n    ret = new Bytes(parseInt(m[1] + m[1], 16), parseInt(m[2] + m[2], 16), parseInt(m[3] + m[3], 16), alphaFromHex(m[4] + m[4]));\n  } else if (m = /^#?([0-9a-f]{2})([0-9a-f]{2})([0-9a-f]{2})([0-9a-f]{2})\\b/i.exec(color)) {\n    // Parse 8 digit hex color\n    ret = new Bytes(parseInt(m[1], 16), parseInt(m[2], 16), parseInt(m[3], 16), alphaFromHex(m[4]));\n  } else if (m = /^rgb\\(\\s*([0-9]+)\\s*,\\s*([0-9]+)\\s*,\\s*([0-9]+)\\s*\\)/.exec(color)) {\n    ret = new Bytes(parseInt(m[1], 10), parseInt(m[2], 10), parseInt(m[3], 10), 1);\n  } else if (m = /^rgba\\(\\s*([0-9]+)\\s*,\\s*([0-9]+)\\s*,\\s*([0-9]+)\\s*,\\s*([0-9.]+)\\s*\\)/.exec(color)) {\n    ret = new Bytes(parseInt(m[1], 10), parseInt(m[2], 10), parseInt(m[3], 10), parseFloat(m[4]));\n  } else if (m = /^rgb\\(\\s*([0-9]*\\.?[0-9]+)%\\s*,\\s*([0-9]*\\.?[0-9]+)%\\s*,\\s*([0-9]*\\.?[0-9]+)%\\s*\\)/.exec(color)) {\n    ret = new RGB(parseFloat(m[1]) / 100, parseFloat(m[2]) / 100, parseFloat(m[3]) / 100, 1);\n  } else if (m = /^rgba\\(\\s*([0-9]*\\.?[0-9]+)%\\s*,\\s*([0-9]*\\.?[0-9]+)%\\s*,\\s*([0-9]*\\.?[0-9]+)%\\s*,\\s*([0-9.]+)\\s*\\)/.exec(color)) {\n    ret = new RGB(parseFloat(m[1]) / 100, parseFloat(m[2]) / 100, parseFloat(m[3]) / 100, parseFloat(m[4]));\n  } else if (m = /^color\\(\\s*srgb\\s*([0-9]*\\.?[0-9]+)\\s+([0-9]*\\.?[0-9]+)\\s+([0-9]*\\.?[0-9]+)\\s*(\\/\\s+([0-9]*\\.?[0-9]+))?\\)/.exec(color)) {\n    ret = new RGB(parseFloat(m[1]), parseFloat(m[2]), parseFloat(m[3]), parseFloat(m[5] || '1'));\n  }\n  if (ret) {\n    ret.match = m;\n  } else if (!safe) {\n    throw new Error(\"Cannot parse color: \" + color);\n  }\n  return ret;\n}", "map": {"version": 3, "names": ["Class", "support", "namedColors", "browser", "matchNamedColor", "color", "colorNames", "Object", "keys", "push", "regexp", "RegExp", "join", "exec", "BaseColor", "call", "__proto__", "prototype", "create", "constructor", "toHSV", "toRGB", "toHex", "options", "toBytes", "to<PERSON>s", "toCssRgba", "rgb", "r", "g", "b", "parseFloat", "Number", "a", "toFixed", "toDisplay", "msie", "version", "equals", "c", "undefined", "parseColor", "diff", "other", "NaN", "c1", "c2", "Math", "sqrt", "pow", "clone", "Bytes", "RGB", "ref", "min", "max", "delta", "v", "h", "s", "HSV", "toHSL", "l", "d", "HSL", "round", "value", "hex", "alpha", "n", "width", "pad", "result", "toString", "length", "i", "floor", "f", "p", "q", "t", "hue2rgb", "alphaFromHex", "parseInt", "safe", "m", "ret", "toLowerCase", "match", "Error"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/common/color/parse-color.js"], "sourcesContent": ["import Class from '../class';\nimport support from '../support';\nimport namedColors from './named-colors';\n\nvar browser = support.browser;\n\nvar matchNamedColor = function (color) {\n    var colorNames = Object.keys(namedColors);\n    colorNames.push(\"transparent\");\n\n    var regexp = new RegExp(\"^(\" + colorNames.join(\"|\") + \")(\\\\W|$)\", \"i\");\n    matchNamedColor = function (color) { return regexp.exec(color); };\n\n    return regexp.exec(color);\n};\n\nvar BaseColor = (function (Class) {\n    function BaseColor() { Class.call(this); }\n\n    if ( Class ) BaseColor.__proto__ = Class;\n    BaseColor.prototype = Object.create( Class && Class.prototype );\n    BaseColor.prototype.constructor = BaseColor;\n    BaseColor.prototype.toHSV = function toHSV () { return this; };\n\n    BaseColor.prototype.toRGB = function toRGB () { return this; };\n\n    BaseColor.prototype.toHex = function toHex (options) { return this.toBytes().toHex(options); };\n\n    BaseColor.prototype.toBytes = function toBytes () { return this; };\n\n    BaseColor.prototype.toCss = function toCss (options) { return \"#\" + this.toHex(options); };\n\n    BaseColor.prototype.toCssRgba = function toCssRgba () {\n        var rgb = this.toBytes();\n        return (\"rgba(\" + (rgb.r) + \", \" + (rgb.g) + \", \" + (rgb.b) + \", \" + (parseFloat((Number(this.a)).toFixed(3))) + \")\");\n    };\n\n    BaseColor.prototype.toDisplay = function toDisplay () {\n        if (browser.msie && browser.version < 9) {\n            return this.toCss(); // no RGBA support; does it support any opacity in colors?\n        }\n        return this.toCssRgba();\n    };\n\n    BaseColor.prototype.equals = function equals (c) {\n        return c === this || ((c !== null && c !== undefined) && this.toCssRgba() === parseColor(c).toCssRgba());\n    };\n\n    BaseColor.prototype.diff = function diff (other) {\n        if (other === null) {\n            return NaN;\n        }\n\n        var c1 = this.toBytes();\n        var c2 = other.toBytes();\n\n        return Math.sqrt(Math.pow((c1.r - c2.r) * 0.30, 2) +\n                         Math.pow((c1.g - c2.g) * 0.59, 2) +\n                         Math.pow((c1.b - c2.b) * 0.11, 2));\n    };\n\n    BaseColor.prototype.clone = function clone () {\n        var c = this.toBytes();\n        if (c === this) {\n            c = new Bytes(c.r, c.g, c.b, c.a);\n        }\n\n        return c;\n    };\n\n    return BaseColor;\n}(Class));\n\nvar RGB = (function (BaseColor) {\n    function RGB(r, g, b, a) {\n        BaseColor.call(this);\n\n        this.r = r;\n        this.g = g;\n        this.b = b;\n        this.a = a;\n    }\n\n    if ( BaseColor ) RGB.__proto__ = BaseColor;\n    RGB.prototype = Object.create( BaseColor && BaseColor.prototype );\n    RGB.prototype.constructor = RGB;\n\n    RGB.prototype.toHSV = function toHSV () {\n        var ref = this;\n        var r = ref.r;\n        var g = ref.g;\n        var b = ref.b;\n        var min = Math.min(r, g, b);\n        var max = Math.max(r, g, b);\n        var delta = max - min;\n        var v = max;\n        var h, s;\n\n        if (delta === 0) {\n            return new HSV(0, 0, v, this.a);\n        }\n\n        if (max !== 0) {\n            s = delta / max;\n            if (r === max) {\n                h = (g - b) / delta;\n            } else if (g === max) {\n                h = 2 + (b - r) / delta;\n            } else {\n                h = 4 + (r - g) / delta;\n            }\n\n            h *= 60;\n            if (h < 0) {\n                h += 360;\n            }\n        } else {\n            s = 0;\n            h = -1;\n        }\n\n        return new HSV(h, s, v, this.a);\n    };\n\n    RGB.prototype.toHSL = function toHSL () {\n        var ref = this;\n        var r = ref.r;\n        var g = ref.g;\n        var b = ref.b;\n        var max = Math.max(r, g, b);\n        var min = Math.min(r, g, b);\n        var h, s, l = (max + min) / 2;\n\n        if (max === min) {\n            h = s = 0;\n        } else {\n            var d = max - min;\n            s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n            switch (max) {\n            case r: h = (g - b) / d + (g < b ? 6 : 0); break;\n            case g: h = (b - r) / d + 2; break;\n            case b: h = (r - g) / d + 4; break;\n            default: break;\n            }\n        }\n\n        return new HSL(h * 60, s * 100, l * 100, this.a);\n    };\n\n    RGB.prototype.toBytes = function toBytes () {\n        return new Bytes(this.r * 255, this.g * 255, this.b * 255, this.a);\n    };\n\n    return RGB;\n}(BaseColor));\n\nvar Bytes = (function (RGB) {\n    function Bytes(r, g, b, a) {\n        RGB.call(this, Math.round(r), Math.round(g), Math.round(b), a);\n    }\n\n    if ( RGB ) Bytes.__proto__ = RGB;\n    Bytes.prototype = Object.create( RGB && RGB.prototype );\n    Bytes.prototype.constructor = Bytes;\n\n    Bytes.prototype.toRGB = function toRGB () {\n        return new RGB(this.r / 255, this.g / 255, this.b / 255, this.a);\n    };\n\n    Bytes.prototype.toHSV = function toHSV () {\n        return this.toRGB().toHSV();\n    };\n\n    Bytes.prototype.toHSL = function toHSL () {\n        return this.toRGB().toHSL();\n    };\n\n    Bytes.prototype.toHex = function toHex (options) {\n        var value = hex(this.r, 2) + hex(this.g, 2) + hex(this.b, 2);\n\n        if (options && options.alpha) {\n            value += hex(Math.round(this.a * 255), 2);\n        }\n\n        return value;\n    };\n\n    Bytes.prototype.toBytes = function toBytes () {\n        return this;\n    };\n\n    return Bytes;\n}(RGB));\n\nfunction hex(n, width, pad) {\n    if ( pad === void 0 ) pad = \"0\";\n\n    var result = n.toString(16);\n    while (width > result.length) {\n        result = pad + result;\n    }\n\n    return result;\n}\n\nvar HSV = (function (BaseColor) {\n    function HSV(h, s, v, a) {\n        BaseColor.call(this);\n\n        this.h = h;\n        this.s = s;\n        this.v = v;\n        this.a = a;\n    }\n\n    if ( BaseColor ) HSV.__proto__ = BaseColor;\n    HSV.prototype = Object.create( BaseColor && BaseColor.prototype );\n    HSV.prototype.constructor = HSV;\n\n    HSV.prototype.toRGB = function toRGB () {\n        var ref = this;\n        var h = ref.h;\n        var s = ref.s;\n        var v = ref.v;\n        var r, g, b;\n\n        if (s === 0) {\n            r = g = b = v;\n        } else {\n            h /= 60;\n\n            var i = Math.floor(h);\n            var f = h - i;\n            var p = v * (1 - s);\n            var q = v * (1 - s * f);\n            var t = v * (1 - s * (1 - f));\n\n            switch (i) {\n            case 0: r = v; g = t; b = p; break;\n            case 1: r = q; g = v; b = p; break;\n            case 2: r = p; g = v; b = t; break;\n            case 3: r = p; g = q; b = v; break;\n            case 4: r = t; g = p; b = v; break;\n            default: r = v; g = p; b = q; break;\n            }\n        }\n\n        return new RGB(r, g, b, this.a);\n    };\n\n    HSV.prototype.toHSL = function toHSL () {\n        return this.toRGB().toHSL();\n    };\n\n    HSV.prototype.toBytes = function toBytes () {\n        return this.toRGB().toBytes();\n    };\n\n    return HSV;\n}(BaseColor));\n\nvar HSL = (function (BaseColor) {\n    function HSL(h, s, l, a) {\n        BaseColor.call(this);\n\n        this.h = h;\n        this.s = s;\n        this.l = l;\n        this.a = a;\n    }\n\n    if ( BaseColor ) HSL.__proto__ = BaseColor;\n    HSL.prototype = Object.create( BaseColor && BaseColor.prototype );\n    HSL.prototype.constructor = HSL;\n\n    HSL.prototype.toRGB = function toRGB () {\n        var h = this.h / 360;\n        var s = this.s / 100;\n        var l = this.l / 100;\n        var r, g, b;\n\n        if (s === 0) {\n            r = g = b = l; // achromatic\n        } else {\n            var q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n            var p = 2 * l - q;\n            r = hue2rgb(p, q, h + 1 / 3);\n            g = hue2rgb(p, q, h);\n            b = hue2rgb(p, q, h - 1 / 3);\n        }\n\n        return new RGB(r, g, b, this.a);\n    };\n\n    HSL.prototype.toHSV = function toHSV () {\n        return this.toRGB().toHSV();\n    };\n\n    HSL.prototype.toBytes = function toBytes () {\n        return this.toRGB().toBytes();\n    };\n\n    return HSL;\n}(BaseColor));\n\nfunction hue2rgb(p, q, s) {\n    var t = s;\n\n    if (t < 0) {\n        t += 1;\n    }\n\n    if (t > 1) {\n        t -= 1;\n    }\n\n    if (t < 1 / 6) {\n        return p + (q - p) * 6 * t;\n    }\n\n    if (t < 1 / 2) {\n        return q;\n    }\n\n    if (t < 2 / 3) {\n        return p + (q - p) * (2 / 3 - t) * 6;\n    }\n\n    return p;\n}\n\nfunction alphaFromHex(a) {\n    return parseFloat(parseFloat(parseInt(a, 16) / 255 ).toFixed(3));\n}\n\nexport { RGB, Bytes, HSV, HSL };\n\nexport default function parseColor(value, safe) {\n    var m, ret;\n\n    if (!value || value === \"none\") {\n        return null;\n    }\n\n    if (value instanceof BaseColor) {\n        return value;\n    }\n\n    var color = value.toLowerCase();\n    if ((m = matchNamedColor(color))) {\n        if (m[1] === \"transparent\") {\n            color = new RGB(1, 1, 1, 0);\n        } else {\n            color = parseColor(namedColors[m[1]], safe);\n        }\n        color.match = [ m[1] ];\n        return color;\n    }\n    if ((m = /^#?([0-9a-f]{2})([0-9a-f]{2})([0-9a-f]{2})\\b/i.exec(color))) {\n        ret = new Bytes(parseInt(m[1], 16),\n            parseInt(m[2], 16),\n            parseInt(m[3], 16), 1);\n    } else if ((m = /^#?([0-9a-f])([0-9a-f])([0-9a-f])\\b/i.exec(color))) {\n        ret = new Bytes(parseInt(m[1] + m[1], 16),\n            parseInt(m[2] + m[2], 16),\n            parseInt(m[3] + m[3], 16), 1);\n    } else if ((m = /^#?([0-9a-f])([0-9a-f])([0-9a-f])([0-9a-f])\\b/i.exec(color))) { // Parse 4 digit hex color\n        ret = new Bytes(parseInt(m[1] + m[1], 16),\n            parseInt(m[2] + m[2], 16),\n            parseInt(m[3] + m[3], 16),\n            alphaFromHex(m[4] + m[4]));\n    } else if ((m = /^#?([0-9a-f]{2})([0-9a-f]{2})([0-9a-f]{2})([0-9a-f]{2})\\b/i.exec(color))) { // Parse 8 digit hex color\n        ret = new Bytes(parseInt(m[1], 16),\n            parseInt(m[2], 16),\n            parseInt(m[3], 16),\n            alphaFromHex(m[4]));\n    } else if ((m = /^rgb\\(\\s*([0-9]+)\\s*,\\s*([0-9]+)\\s*,\\s*([0-9]+)\\s*\\)/.exec(color))) {\n        ret = new Bytes(parseInt(m[1], 10),\n            parseInt(m[2], 10),\n            parseInt(m[3], 10), 1);\n    } else if ((m = /^rgba\\(\\s*([0-9]+)\\s*,\\s*([0-9]+)\\s*,\\s*([0-9]+)\\s*,\\s*([0-9.]+)\\s*\\)/.exec(color))) {\n        ret = new Bytes(parseInt(m[1], 10),\n            parseInt(m[2], 10),\n            parseInt(m[3], 10), parseFloat(m[4]));\n    } else if ((m = /^rgb\\(\\s*([0-9]*\\.?[0-9]+)%\\s*,\\s*([0-9]*\\.?[0-9]+)%\\s*,\\s*([0-9]*\\.?[0-9]+)%\\s*\\)/.exec(color))) {\n        ret = new RGB(parseFloat(m[1]) / 100,\n            parseFloat(m[2]) / 100,\n            parseFloat(m[3]) / 100, 1);\n    } else if ((m = /^rgba\\(\\s*([0-9]*\\.?[0-9]+)%\\s*,\\s*([0-9]*\\.?[0-9]+)%\\s*,\\s*([0-9]*\\.?[0-9]+)%\\s*,\\s*([0-9.]+)\\s*\\)/.exec(color))) {\n        ret = new RGB(parseFloat(m[1]) / 100,\n            parseFloat(m[2]) / 100,\n            parseFloat(m[3]) / 100, parseFloat(m[4]));\n    } else if ((m = /^color\\(\\s*srgb\\s*([0-9]*\\.?[0-9]+)\\s+([0-9]*\\.?[0-9]+)\\s+([0-9]*\\.?[0-9]+)\\s*(\\/\\s+([0-9]*\\.?[0-9]+))?\\)/.exec(color))) {\n        ret = new RGB(\n            parseFloat(m[1]),\n            parseFloat(m[2]),\n            parseFloat(m[3]),\n            parseFloat(m[5] || '1'));\n    }\n\n    if (ret) {\n        ret.match = m;\n    } else if (!safe) {\n        throw new Error(\"Cannot parse color: \" + color);\n    }\n\n    return ret;\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,UAAU;AAC5B,OAAOC,OAAO,MAAM,YAAY;AAChC,OAAOC,WAAW,MAAM,gBAAgB;AAExC,IAAIC,OAAO,GAAGF,OAAO,CAACE,OAAO;AAE7B,IAAIC,eAAe,GAAG,SAAAA,CAAUC,KAAK,EAAE;EACnC,IAAIC,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACN,WAAW,CAAC;EACzCI,UAAU,CAACG,IAAI,CAAC,aAAa,CAAC;EAE9B,IAAIC,MAAM,GAAG,IAAIC,MAAM,CAAC,IAAI,GAAGL,UAAU,CAACM,IAAI,CAAC,GAAG,CAAC,GAAG,UAAU,EAAE,GAAG,CAAC;EACtER,eAAe,GAAG,SAAAA,CAAUC,KAAK,EAAE;IAAE,OAAOK,MAAM,CAACG,IAAI,CAACR,KAAK,CAAC;EAAE,CAAC;EAEjE,OAAOK,MAAM,CAACG,IAAI,CAACR,KAAK,CAAC;AAC7B,CAAC;AAED,IAAIS,SAAS,GAAI,UAAUd,KAAK,EAAE;EAC9B,SAASc,SAASA,CAAA,EAAG;IAAEd,KAAK,CAACe,IAAI,CAAC,IAAI,CAAC;EAAE;EAEzC,IAAKf,KAAK,EAAGc,SAAS,CAACE,SAAS,GAAGhB,KAAK;EACxCc,SAAS,CAACG,SAAS,GAAGV,MAAM,CAACW,MAAM,CAAElB,KAAK,IAAIA,KAAK,CAACiB,SAAU,CAAC;EAC/DH,SAAS,CAACG,SAAS,CAACE,WAAW,GAAGL,SAAS;EAC3CA,SAAS,CAACG,SAAS,CAACG,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAI;IAAE,OAAO,IAAI;EAAE,CAAC;EAE9DN,SAAS,CAACG,SAAS,CAACI,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAI;IAAE,OAAO,IAAI;EAAE,CAAC;EAE9DP,SAAS,CAACG,SAAS,CAACK,KAAK,GAAG,SAASA,KAAKA,CAAEC,OAAO,EAAE;IAAE,OAAO,IAAI,CAACC,OAAO,CAAC,CAAC,CAACF,KAAK,CAACC,OAAO,CAAC;EAAE,CAAC;EAE9FT,SAAS,CAACG,SAAS,CAACO,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;IAAE,OAAO,IAAI;EAAE,CAAC;EAElEV,SAAS,CAACG,SAAS,CAACQ,KAAK,GAAG,SAASA,KAAKA,CAAEF,OAAO,EAAE;IAAE,OAAO,GAAG,GAAG,IAAI,CAACD,KAAK,CAACC,OAAO,CAAC;EAAE,CAAC;EAE1FT,SAAS,CAACG,SAAS,CAACS,SAAS,GAAG,SAASA,SAASA,CAAA,EAAI;IAClD,IAAIC,GAAG,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IACxB,OAAQ,OAAO,GAAIG,GAAG,CAACC,CAAE,GAAG,IAAI,GAAID,GAAG,CAACE,CAAE,GAAG,IAAI,GAAIF,GAAG,CAACG,CAAE,GAAG,IAAI,GAAIC,UAAU,CAAEC,MAAM,CAAC,IAAI,CAACC,CAAC,CAAC,CAAEC,OAAO,CAAC,CAAC,CAAC,CAAE,GAAG,GAAG;EACxH,CAAC;EAEDpB,SAAS,CAACG,SAAS,CAACkB,SAAS,GAAG,SAASA,SAASA,CAAA,EAAI;IAClD,IAAIhC,OAAO,CAACiC,IAAI,IAAIjC,OAAO,CAACkC,OAAO,GAAG,CAAC,EAAE;MACrC,OAAO,IAAI,CAACZ,KAAK,CAAC,CAAC,CAAC,CAAC;IACzB;IACA,OAAO,IAAI,CAACC,SAAS,CAAC,CAAC;EAC3B,CAAC;EAEDZ,SAAS,CAACG,SAAS,CAACqB,MAAM,GAAG,SAASA,MAAMA,CAAEC,CAAC,EAAE;IAC7C,OAAOA,CAAC,KAAK,IAAI,IAAMA,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAKC,SAAS,IAAK,IAAI,CAACd,SAAS,CAAC,CAAC,KAAKe,UAAU,CAACF,CAAC,CAAC,CAACb,SAAS,CAAC,CAAE;EAC5G,CAAC;EAEDZ,SAAS,CAACG,SAAS,CAACyB,IAAI,GAAG,SAASA,IAAIA,CAAEC,KAAK,EAAE;IAC7C,IAAIA,KAAK,KAAK,IAAI,EAAE;MAChB,OAAOC,GAAG;IACd;IAEA,IAAIC,EAAE,GAAG,IAAI,CAACrB,OAAO,CAAC,CAAC;IACvB,IAAIsB,EAAE,GAAGH,KAAK,CAACnB,OAAO,CAAC,CAAC;IAExB,OAAOuB,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,GAAG,CAAC,CAACJ,EAAE,CAACjB,CAAC,GAAGkB,EAAE,CAAClB,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,GACjCmB,IAAI,CAACE,GAAG,CAAC,CAACJ,EAAE,CAAChB,CAAC,GAAGiB,EAAE,CAACjB,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,GACjCkB,IAAI,CAACE,GAAG,CAAC,CAACJ,EAAE,CAACf,CAAC,GAAGgB,EAAE,CAAChB,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC;EACvD,CAAC;EAEDhB,SAAS,CAACG,SAAS,CAACiC,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAI;IAC1C,IAAIX,CAAC,GAAG,IAAI,CAACf,OAAO,CAAC,CAAC;IACtB,IAAIe,CAAC,KAAK,IAAI,EAAE;MACZA,CAAC,GAAG,IAAIY,KAAK,CAACZ,CAAC,CAACX,CAAC,EAAEW,CAAC,CAACV,CAAC,EAAEU,CAAC,CAACT,CAAC,EAAES,CAAC,CAACN,CAAC,CAAC;IACrC;IAEA,OAAOM,CAAC;EACZ,CAAC;EAED,OAAOzB,SAAS;AACpB,CAAC,CAACd,KAAK,CAAE;AAET,IAAIoD,GAAG,GAAI,UAAUtC,SAAS,EAAE;EAC5B,SAASsC,GAAGA,CAACxB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEG,CAAC,EAAE;IACrBnB,SAAS,CAACC,IAAI,CAAC,IAAI,CAAC;IAEpB,IAAI,CAACa,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACG,CAAC,GAAGA,CAAC;EACd;EAEA,IAAKnB,SAAS,EAAGsC,GAAG,CAACpC,SAAS,GAAGF,SAAS;EAC1CsC,GAAG,CAACnC,SAAS,GAAGV,MAAM,CAACW,MAAM,CAAEJ,SAAS,IAAIA,SAAS,CAACG,SAAU,CAAC;EACjEmC,GAAG,CAACnC,SAAS,CAACE,WAAW,GAAGiC,GAAG;EAE/BA,GAAG,CAACnC,SAAS,CAACG,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAI;IACpC,IAAIiC,GAAG,GAAG,IAAI;IACd,IAAIzB,CAAC,GAAGyB,GAAG,CAACzB,CAAC;IACb,IAAIC,CAAC,GAAGwB,GAAG,CAACxB,CAAC;IACb,IAAIC,CAAC,GAAGuB,GAAG,CAACvB,CAAC;IACb,IAAIwB,GAAG,GAAGP,IAAI,CAACO,GAAG,CAAC1B,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;IAC3B,IAAIyB,GAAG,GAAGR,IAAI,CAACQ,GAAG,CAAC3B,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;IAC3B,IAAI0B,KAAK,GAAGD,GAAG,GAAGD,GAAG;IACrB,IAAIG,CAAC,GAAGF,GAAG;IACX,IAAIG,CAAC,EAAEC,CAAC;IAER,IAAIH,KAAK,KAAK,CAAC,EAAE;MACb,OAAO,IAAII,GAAG,CAAC,CAAC,EAAE,CAAC,EAAEH,CAAC,EAAE,IAAI,CAACxB,CAAC,CAAC;IACnC;IAEA,IAAIsB,GAAG,KAAK,CAAC,EAAE;MACXI,CAAC,GAAGH,KAAK,GAAGD,GAAG;MACf,IAAI3B,CAAC,KAAK2B,GAAG,EAAE;QACXG,CAAC,GAAG,CAAC7B,CAAC,GAAGC,CAAC,IAAI0B,KAAK;MACvB,CAAC,MAAM,IAAI3B,CAAC,KAAK0B,GAAG,EAAE;QAClBG,CAAC,GAAG,CAAC,GAAG,CAAC5B,CAAC,GAAGF,CAAC,IAAI4B,KAAK;MAC3B,CAAC,MAAM;QACHE,CAAC,GAAG,CAAC,GAAG,CAAC9B,CAAC,GAAGC,CAAC,IAAI2B,KAAK;MAC3B;MAEAE,CAAC,IAAI,EAAE;MACP,IAAIA,CAAC,GAAG,CAAC,EAAE;QACPA,CAAC,IAAI,GAAG;MACZ;IACJ,CAAC,MAAM;MACHC,CAAC,GAAG,CAAC;MACLD,CAAC,GAAG,CAAC,CAAC;IACV;IAEA,OAAO,IAAIE,GAAG,CAACF,CAAC,EAAEC,CAAC,EAAEF,CAAC,EAAE,IAAI,CAACxB,CAAC,CAAC;EACnC,CAAC;EAEDmB,GAAG,CAACnC,SAAS,CAAC4C,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAI;IACpC,IAAIR,GAAG,GAAG,IAAI;IACd,IAAIzB,CAAC,GAAGyB,GAAG,CAACzB,CAAC;IACb,IAAIC,CAAC,GAAGwB,GAAG,CAACxB,CAAC;IACb,IAAIC,CAAC,GAAGuB,GAAG,CAACvB,CAAC;IACb,IAAIyB,GAAG,GAAGR,IAAI,CAACQ,GAAG,CAAC3B,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;IAC3B,IAAIwB,GAAG,GAAGP,IAAI,CAACO,GAAG,CAAC1B,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;IAC3B,IAAI4B,CAAC;MAAEC,CAAC;MAAEG,CAAC,GAAG,CAACP,GAAG,GAAGD,GAAG,IAAI,CAAC;IAE7B,IAAIC,GAAG,KAAKD,GAAG,EAAE;MACbI,CAAC,GAAGC,CAAC,GAAG,CAAC;IACb,CAAC,MAAM;MACH,IAAII,CAAC,GAAGR,GAAG,GAAGD,GAAG;MACjBK,CAAC,GAAGG,CAAC,GAAG,GAAG,GAAGC,CAAC,IAAI,CAAC,GAAGR,GAAG,GAAGD,GAAG,CAAC,GAAGS,CAAC,IAAIR,GAAG,GAAGD,GAAG,CAAC;MACnD,QAAQC,GAAG;QACX,KAAK3B,CAAC;UAAE8B,CAAC,GAAG,CAAC7B,CAAC,GAAGC,CAAC,IAAIiC,CAAC,IAAIlC,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;UAAE;QAC3C,KAAKD,CAAC;UAAE6B,CAAC,GAAG,CAAC5B,CAAC,GAAGF,CAAC,IAAImC,CAAC,GAAG,CAAC;UAAE;QAC7B,KAAKjC,CAAC;UAAE4B,CAAC,GAAG,CAAC9B,CAAC,GAAGC,CAAC,IAAIkC,CAAC,GAAG,CAAC;UAAE;QAC7B;UAAS;MACT;IACJ;IAEA,OAAO,IAAIC,GAAG,CAACN,CAAC,GAAG,EAAE,EAAEC,CAAC,GAAG,GAAG,EAAEG,CAAC,GAAG,GAAG,EAAE,IAAI,CAAC7B,CAAC,CAAC;EACpD,CAAC;EAEDmB,GAAG,CAACnC,SAAS,CAACO,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;IACxC,OAAO,IAAI2B,KAAK,CAAC,IAAI,CAACvB,CAAC,GAAG,GAAG,EAAE,IAAI,CAACC,CAAC,GAAG,GAAG,EAAE,IAAI,CAACC,CAAC,GAAG,GAAG,EAAE,IAAI,CAACG,CAAC,CAAC;EACtE,CAAC;EAED,OAAOmB,GAAG;AACd,CAAC,CAACtC,SAAS,CAAE;AAEb,IAAIqC,KAAK,GAAI,UAAUC,GAAG,EAAE;EACxB,SAASD,KAAKA,CAACvB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEG,CAAC,EAAE;IACvBmB,GAAG,CAACrC,IAAI,CAAC,IAAI,EAAEgC,IAAI,CAACkB,KAAK,CAACrC,CAAC,CAAC,EAAEmB,IAAI,CAACkB,KAAK,CAACpC,CAAC,CAAC,EAAEkB,IAAI,CAACkB,KAAK,CAACnC,CAAC,CAAC,EAAEG,CAAC,CAAC;EAClE;EAEA,IAAKmB,GAAG,EAAGD,KAAK,CAACnC,SAAS,GAAGoC,GAAG;EAChCD,KAAK,CAAClC,SAAS,GAAGV,MAAM,CAACW,MAAM,CAAEkC,GAAG,IAAIA,GAAG,CAACnC,SAAU,CAAC;EACvDkC,KAAK,CAAClC,SAAS,CAACE,WAAW,GAAGgC,KAAK;EAEnCA,KAAK,CAAClC,SAAS,CAACI,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAI;IACtC,OAAO,IAAI+B,GAAG,CAAC,IAAI,CAACxB,CAAC,GAAG,GAAG,EAAE,IAAI,CAACC,CAAC,GAAG,GAAG,EAAE,IAAI,CAACC,CAAC,GAAG,GAAG,EAAE,IAAI,CAACG,CAAC,CAAC;EACpE,CAAC;EAEDkB,KAAK,CAAClC,SAAS,CAACG,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAI;IACtC,OAAO,IAAI,CAACC,KAAK,CAAC,CAAC,CAACD,KAAK,CAAC,CAAC;EAC/B,CAAC;EAED+B,KAAK,CAAClC,SAAS,CAAC4C,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAI;IACtC,OAAO,IAAI,CAACxC,KAAK,CAAC,CAAC,CAACwC,KAAK,CAAC,CAAC;EAC/B,CAAC;EAEDV,KAAK,CAAClC,SAAS,CAACK,KAAK,GAAG,SAASA,KAAKA,CAAEC,OAAO,EAAE;IAC7C,IAAI2C,KAAK,GAAGC,GAAG,CAAC,IAAI,CAACvC,CAAC,EAAE,CAAC,CAAC,GAAGuC,GAAG,CAAC,IAAI,CAACtC,CAAC,EAAE,CAAC,CAAC,GAAGsC,GAAG,CAAC,IAAI,CAACrC,CAAC,EAAE,CAAC,CAAC;IAE5D,IAAIP,OAAO,IAAIA,OAAO,CAAC6C,KAAK,EAAE;MAC1BF,KAAK,IAAIC,GAAG,CAACpB,IAAI,CAACkB,KAAK,CAAC,IAAI,CAAChC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;IAC7C;IAEA,OAAOiC,KAAK;EAChB,CAAC;EAEDf,KAAK,CAAClC,SAAS,CAACO,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;IAC1C,OAAO,IAAI;EACf,CAAC;EAED,OAAO2B,KAAK;AAChB,CAAC,CAACC,GAAG,CAAE;AAEP,SAASe,GAAGA,CAACE,CAAC,EAAEC,KAAK,EAAEC,GAAG,EAAE;EACxB,IAAKA,GAAG,KAAK,KAAK,CAAC,EAAGA,GAAG,GAAG,GAAG;EAE/B,IAAIC,MAAM,GAAGH,CAAC,CAACI,QAAQ,CAAC,EAAE,CAAC;EAC3B,OAAOH,KAAK,GAAGE,MAAM,CAACE,MAAM,EAAE;IAC1BF,MAAM,GAAGD,GAAG,GAAGC,MAAM;EACzB;EAEA,OAAOA,MAAM;AACjB;AAEA,IAAIZ,GAAG,GAAI,UAAU9C,SAAS,EAAE;EAC5B,SAAS8C,GAAGA,CAACF,CAAC,EAAEC,CAAC,EAAEF,CAAC,EAAExB,CAAC,EAAE;IACrBnB,SAAS,CAACC,IAAI,CAAC,IAAI,CAAC;IAEpB,IAAI,CAAC2C,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACF,CAAC,GAAGA,CAAC;IACV,IAAI,CAACxB,CAAC,GAAGA,CAAC;EACd;EAEA,IAAKnB,SAAS,EAAG8C,GAAG,CAAC5C,SAAS,GAAGF,SAAS;EAC1C8C,GAAG,CAAC3C,SAAS,GAAGV,MAAM,CAACW,MAAM,CAAEJ,SAAS,IAAIA,SAAS,CAACG,SAAU,CAAC;EACjE2C,GAAG,CAAC3C,SAAS,CAACE,WAAW,GAAGyC,GAAG;EAE/BA,GAAG,CAAC3C,SAAS,CAACI,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAI;IACpC,IAAIgC,GAAG,GAAG,IAAI;IACd,IAAIK,CAAC,GAAGL,GAAG,CAACK,CAAC;IACb,IAAIC,CAAC,GAAGN,GAAG,CAACM,CAAC;IACb,IAAIF,CAAC,GAAGJ,GAAG,CAACI,CAAC;IACb,IAAI7B,CAAC,EAAEC,CAAC,EAAEC,CAAC;IAEX,IAAI6B,CAAC,KAAK,CAAC,EAAE;MACT/B,CAAC,GAAGC,CAAC,GAAGC,CAAC,GAAG2B,CAAC;IACjB,CAAC,MAAM;MACHC,CAAC,IAAI,EAAE;MAEP,IAAIiB,CAAC,GAAG5B,IAAI,CAAC6B,KAAK,CAAClB,CAAC,CAAC;MACrB,IAAImB,CAAC,GAAGnB,CAAC,GAAGiB,CAAC;MACb,IAAIG,CAAC,GAAGrB,CAAC,IAAI,CAAC,GAAGE,CAAC,CAAC;MACnB,IAAIoB,CAAC,GAAGtB,CAAC,IAAI,CAAC,GAAGE,CAAC,GAAGkB,CAAC,CAAC;MACvB,IAAIG,CAAC,GAAGvB,CAAC,IAAI,CAAC,GAAGE,CAAC,IAAI,CAAC,GAAGkB,CAAC,CAAC,CAAC;MAE7B,QAAQF,CAAC;QACT,KAAK,CAAC;UAAE/C,CAAC,GAAG6B,CAAC;UAAE5B,CAAC,GAAGmD,CAAC;UAAElD,CAAC,GAAGgD,CAAC;UAAE;QAC7B,KAAK,CAAC;UAAElD,CAAC,GAAGmD,CAAC;UAAElD,CAAC,GAAG4B,CAAC;UAAE3B,CAAC,GAAGgD,CAAC;UAAE;QAC7B,KAAK,CAAC;UAAElD,CAAC,GAAGkD,CAAC;UAAEjD,CAAC,GAAG4B,CAAC;UAAE3B,CAAC,GAAGkD,CAAC;UAAE;QAC7B,KAAK,CAAC;UAAEpD,CAAC,GAAGkD,CAAC;UAAEjD,CAAC,GAAGkD,CAAC;UAAEjD,CAAC,GAAG2B,CAAC;UAAE;QAC7B,KAAK,CAAC;UAAE7B,CAAC,GAAGoD,CAAC;UAAEnD,CAAC,GAAGiD,CAAC;UAAEhD,CAAC,GAAG2B,CAAC;UAAE;QAC7B;UAAS7B,CAAC,GAAG6B,CAAC;UAAE5B,CAAC,GAAGiD,CAAC;UAAEhD,CAAC,GAAGiD,CAAC;UAAE;MAC9B;IACJ;IAEA,OAAO,IAAI3B,GAAG,CAACxB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE,IAAI,CAACG,CAAC,CAAC;EACnC,CAAC;EAED2B,GAAG,CAAC3C,SAAS,CAAC4C,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAI;IACpC,OAAO,IAAI,CAACxC,KAAK,CAAC,CAAC,CAACwC,KAAK,CAAC,CAAC;EAC/B,CAAC;EAEDD,GAAG,CAAC3C,SAAS,CAACO,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;IACxC,OAAO,IAAI,CAACH,KAAK,CAAC,CAAC,CAACG,OAAO,CAAC,CAAC;EACjC,CAAC;EAED,OAAOoC,GAAG;AACd,CAAC,CAAC9C,SAAS,CAAE;AAEb,IAAIkD,GAAG,GAAI,UAAUlD,SAAS,EAAE;EAC5B,SAASkD,GAAGA,CAACN,CAAC,EAAEC,CAAC,EAAEG,CAAC,EAAE7B,CAAC,EAAE;IACrBnB,SAAS,CAACC,IAAI,CAAC,IAAI,CAAC;IAEpB,IAAI,CAAC2C,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACG,CAAC,GAAGA,CAAC;IACV,IAAI,CAAC7B,CAAC,GAAGA,CAAC;EACd;EAEA,IAAKnB,SAAS,EAAGkD,GAAG,CAAChD,SAAS,GAAGF,SAAS;EAC1CkD,GAAG,CAAC/C,SAAS,GAAGV,MAAM,CAACW,MAAM,CAAEJ,SAAS,IAAIA,SAAS,CAACG,SAAU,CAAC;EACjE+C,GAAG,CAAC/C,SAAS,CAACE,WAAW,GAAG6C,GAAG;EAE/BA,GAAG,CAAC/C,SAAS,CAACI,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAI;IACpC,IAAIqC,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG,GAAG;IACpB,IAAIC,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG,GAAG;IACpB,IAAIG,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG,GAAG;IACpB,IAAIlC,CAAC,EAAEC,CAAC,EAAEC,CAAC;IAEX,IAAI6B,CAAC,KAAK,CAAC,EAAE;MACT/B,CAAC,GAAGC,CAAC,GAAGC,CAAC,GAAGgC,CAAC,CAAC,CAAC;IACnB,CAAC,MAAM;MACH,IAAIiB,CAAC,GAAGjB,CAAC,GAAG,GAAG,GAAGA,CAAC,IAAI,CAAC,GAAGH,CAAC,CAAC,GAAGG,CAAC,GAAGH,CAAC,GAAGG,CAAC,GAAGH,CAAC;MAC7C,IAAImB,CAAC,GAAG,CAAC,GAAGhB,CAAC,GAAGiB,CAAC;MACjBnD,CAAC,GAAGqD,OAAO,CAACH,CAAC,EAAEC,CAAC,EAAErB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MAC5B7B,CAAC,GAAGoD,OAAO,CAACH,CAAC,EAAEC,CAAC,EAAErB,CAAC,CAAC;MACpB5B,CAAC,GAAGmD,OAAO,CAACH,CAAC,EAAEC,CAAC,EAAErB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAChC;IAEA,OAAO,IAAIN,GAAG,CAACxB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE,IAAI,CAACG,CAAC,CAAC;EACnC,CAAC;EAED+B,GAAG,CAAC/C,SAAS,CAACG,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAI;IACpC,OAAO,IAAI,CAACC,KAAK,CAAC,CAAC,CAACD,KAAK,CAAC,CAAC;EAC/B,CAAC;EAED4C,GAAG,CAAC/C,SAAS,CAACO,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;IACxC,OAAO,IAAI,CAACH,KAAK,CAAC,CAAC,CAACG,OAAO,CAAC,CAAC;EACjC,CAAC;EAED,OAAOwC,GAAG;AACd,CAAC,CAAClD,SAAS,CAAE;AAEb,SAASmE,OAAOA,CAACH,CAAC,EAAEC,CAAC,EAAEpB,CAAC,EAAE;EACtB,IAAIqB,CAAC,GAAGrB,CAAC;EAET,IAAIqB,CAAC,GAAG,CAAC,EAAE;IACPA,CAAC,IAAI,CAAC;EACV;EAEA,IAAIA,CAAC,GAAG,CAAC,EAAE;IACPA,CAAC,IAAI,CAAC;EACV;EAEA,IAAIA,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;IACX,OAAOF,CAAC,GAAG,CAACC,CAAC,GAAGD,CAAC,IAAI,CAAC,GAAGE,CAAC;EAC9B;EAEA,IAAIA,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;IACX,OAAOD,CAAC;EACZ;EAEA,IAAIC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;IACX,OAAOF,CAAC,GAAG,CAACC,CAAC,GAAGD,CAAC,KAAK,CAAC,GAAG,CAAC,GAAGE,CAAC,CAAC,GAAG,CAAC;EACxC;EAEA,OAAOF,CAAC;AACZ;AAEA,SAASI,YAAYA,CAACjD,CAAC,EAAE;EACrB,OAAOF,UAAU,CAACA,UAAU,CAACoD,QAAQ,CAAClD,CAAC,EAAE,EAAE,CAAC,GAAG,GAAI,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC;AACpE;AAEA,SAASkB,GAAG,EAAED,KAAK,EAAES,GAAG,EAAEI,GAAG;AAE7B,eAAe,SAASvB,UAAUA,CAACyB,KAAK,EAAEkB,IAAI,EAAE;EAC5C,IAAIC,CAAC,EAAEC,GAAG;EAEV,IAAI,CAACpB,KAAK,IAAIA,KAAK,KAAK,MAAM,EAAE;IAC5B,OAAO,IAAI;EACf;EAEA,IAAIA,KAAK,YAAYpD,SAAS,EAAE;IAC5B,OAAOoD,KAAK;EAChB;EAEA,IAAI7D,KAAK,GAAG6D,KAAK,CAACqB,WAAW,CAAC,CAAC;EAC/B,IAAKF,CAAC,GAAGjF,eAAe,CAACC,KAAK,CAAC,EAAG;IAC9B,IAAIgF,CAAC,CAAC,CAAC,CAAC,KAAK,aAAa,EAAE;MACxBhF,KAAK,GAAG,IAAI+C,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC/B,CAAC,MAAM;MACH/C,KAAK,GAAGoC,UAAU,CAACvC,WAAW,CAACmF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAED,IAAI,CAAC;IAC/C;IACA/E,KAAK,CAACmF,KAAK,GAAG,CAAEH,CAAC,CAAC,CAAC,CAAC,CAAE;IACtB,OAAOhF,KAAK;EAChB;EACA,IAAKgF,CAAC,GAAG,+CAA+C,CAACxE,IAAI,CAACR,KAAK,CAAC,EAAG;IACnEiF,GAAG,GAAG,IAAInC,KAAK,CAACgC,QAAQ,CAACE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAC9BF,QAAQ,CAACE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAClBF,QAAQ,CAACE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;EAC9B,CAAC,MAAM,IAAKA,CAAC,GAAG,sCAAsC,CAACxE,IAAI,CAACR,KAAK,CAAC,EAAG;IACjEiF,GAAG,GAAG,IAAInC,KAAK,CAACgC,QAAQ,CAACE,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EACrCF,QAAQ,CAACE,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EACzBF,QAAQ,CAACE,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;EACrC,CAAC,MAAM,IAAKA,CAAC,GAAG,gDAAgD,CAACxE,IAAI,CAACR,KAAK,CAAC,EAAG;IAAE;IAC7EiF,GAAG,GAAG,IAAInC,KAAK,CAACgC,QAAQ,CAACE,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EACrCF,QAAQ,CAACE,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EACzBF,QAAQ,CAACE,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EACzBH,YAAY,CAACG,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClC,CAAC,MAAM,IAAKA,CAAC,GAAG,4DAA4D,CAACxE,IAAI,CAACR,KAAK,CAAC,EAAG;IAAE;IACzFiF,GAAG,GAAG,IAAInC,KAAK,CAACgC,QAAQ,CAACE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAC9BF,QAAQ,CAACE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAClBF,QAAQ,CAACE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAClBH,YAAY,CAACG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B,CAAC,MAAM,IAAKA,CAAC,GAAG,sDAAsD,CAACxE,IAAI,CAACR,KAAK,CAAC,EAAG;IACjFiF,GAAG,GAAG,IAAInC,KAAK,CAACgC,QAAQ,CAACE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAC9BF,QAAQ,CAACE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAClBF,QAAQ,CAACE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;EAC9B,CAAC,MAAM,IAAKA,CAAC,GAAG,uEAAuE,CAACxE,IAAI,CAACR,KAAK,CAAC,EAAG;IAClGiF,GAAG,GAAG,IAAInC,KAAK,CAACgC,QAAQ,CAACE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAC9BF,QAAQ,CAACE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAClBF,QAAQ,CAACE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAEtD,UAAU,CAACsD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7C,CAAC,MAAM,IAAKA,CAAC,GAAG,oFAAoF,CAACxE,IAAI,CAACR,KAAK,CAAC,EAAG;IAC/GiF,GAAG,GAAG,IAAIlC,GAAG,CAACrB,UAAU,CAACsD,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EAChCtD,UAAU,CAACsD,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EACtBtD,UAAU,CAACsD,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;EAClC,CAAC,MAAM,IAAKA,CAAC,GAAG,qGAAqG,CAACxE,IAAI,CAACR,KAAK,CAAC,EAAG;IAChIiF,GAAG,GAAG,IAAIlC,GAAG,CAACrB,UAAU,CAACsD,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EAChCtD,UAAU,CAACsD,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EACtBtD,UAAU,CAACsD,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EAAEtD,UAAU,CAACsD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjD,CAAC,MAAM,IAAKA,CAAC,GAAG,2GAA2G,CAACxE,IAAI,CAACR,KAAK,CAAC,EAAG;IACtIiF,GAAG,GAAG,IAAIlC,GAAG,CACTrB,UAAU,CAACsD,CAAC,CAAC,CAAC,CAAC,CAAC,EAChBtD,UAAU,CAACsD,CAAC,CAAC,CAAC,CAAC,CAAC,EAChBtD,UAAU,CAACsD,CAAC,CAAC,CAAC,CAAC,CAAC,EAChBtD,UAAU,CAACsD,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;EAChC;EAEA,IAAIC,GAAG,EAAE;IACLA,GAAG,CAACE,KAAK,GAAGH,CAAC;EACjB,CAAC,MAAM,IAAI,CAACD,IAAI,EAAE;IACd,MAAM,IAAIK,KAAK,CAAC,sBAAsB,GAAGpF,KAAK,CAAC;EACnD;EAEA,OAAOiF,GAAG;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}