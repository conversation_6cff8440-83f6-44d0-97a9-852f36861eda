{"ast": null, "code": "export default function lineIntersectionsCount(a, b, point) {\n  var intersects;\n  if (a.x !== b.x) {\n    var minX = Math.min(a.x, b.x);\n    var maxX = Math.max(a.x, b.x);\n    var minY = Math.min(a.y, b.y);\n    var maxY = Math.max(a.y, b.y);\n    var inRange = minX <= point.x && point.x < maxX;\n    if (minY === maxY) {\n      intersects = point.y <= minY && inRange;\n    } else {\n      intersects = inRange && (maxY - minY) * ((a.x - b.x) * (a.y - b.y) > 0 ? point.x - minX : maxX - point.x) / (maxX - minX) + minY - point.y >= 0;\n    }\n  }\n  return intersects ? 1 : 0;\n}", "map": {"version": 3, "names": ["lineIntersectionsCount", "a", "b", "point", "intersects", "x", "minX", "Math", "min", "maxX", "max", "minY", "y", "maxY", "inRange"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/geometry/math/line-intersections-count.js"], "sourcesContent": ["export default function lineIntersectionsCount(a, b, point) {\n    var intersects;\n    if (a.x !== b.x) {\n        var minX = Math.min(a.x, b.x);\n        var maxX = Math.max(a.x, b.x);\n        var minY = Math.min(a.y, b.y);\n        var maxY = Math.max(a.y, b.y);\n        var inRange = minX <= point.x && point.x < maxX;\n\n        if (minY === maxY) {\n            intersects = point.y <= minY && inRange;\n        } else {\n            intersects = inRange && (((maxY - minY) * ((a.x - b.x) * (a.y - b.y) > 0 ? point.x - minX : maxX - point.x)) / (maxX - minX) + minY - point.y) >= 0;\n        }\n    }\n\n    return intersects ? 1 : 0;\n}"], "mappings": "AAAA,eAAe,SAASA,sBAAsBA,CAACC,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAE;EACxD,IAAIC,UAAU;EACd,IAAIH,CAAC,CAACI,CAAC,KAAKH,CAAC,CAACG,CAAC,EAAE;IACb,IAAIC,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACP,CAAC,CAACI,CAAC,EAAEH,CAAC,CAACG,CAAC,CAAC;IAC7B,IAAII,IAAI,GAAGF,IAAI,CAACG,GAAG,CAACT,CAAC,CAACI,CAAC,EAAEH,CAAC,CAACG,CAAC,CAAC;IAC7B,IAAIM,IAAI,GAAGJ,IAAI,CAACC,GAAG,CAACP,CAAC,CAACW,CAAC,EAAEV,CAAC,CAACU,CAAC,CAAC;IAC7B,IAAIC,IAAI,GAAGN,IAAI,CAACG,GAAG,CAACT,CAAC,CAACW,CAAC,EAAEV,CAAC,CAACU,CAAC,CAAC;IAC7B,IAAIE,OAAO,GAAGR,IAAI,IAAIH,KAAK,CAACE,CAAC,IAAIF,KAAK,CAACE,CAAC,GAAGI,IAAI;IAE/C,IAAIE,IAAI,KAAKE,IAAI,EAAE;MACfT,UAAU,GAAGD,KAAK,CAACS,CAAC,IAAID,IAAI,IAAIG,OAAO;IAC3C,CAAC,MAAM;MACHV,UAAU,GAAGU,OAAO,IAAM,CAACD,IAAI,GAAGF,IAAI,KAAK,CAACV,CAAC,CAACI,CAAC,GAAGH,CAAC,CAACG,CAAC,KAAKJ,CAAC,CAACW,CAAC,GAAGV,CAAC,CAACU,CAAC,CAAC,GAAG,CAAC,GAAGT,KAAK,CAACE,CAAC,GAAGC,IAAI,GAAGG,IAAI,GAAGN,KAAK,CAACE,CAAC,CAAC,IAAKI,IAAI,GAAGH,IAAI,CAAC,GAAGK,IAAI,GAAGR,KAAK,CAACS,CAAC,IAAK,CAAC;IACvJ;EACJ;EAEA,OAAOR,UAAU,GAAG,CAAC,GAAG,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}