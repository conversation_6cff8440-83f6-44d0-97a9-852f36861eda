{"ast": null, "code": "import PathNode from './path-node';\nimport renderStyle from './utils/render-style';\nimport renderAttr from './utils/render-attribute';\nimport { htmlEncode, support } from '../common';\nimport { normalizeText } from '../text-metrics';\nvar ENTITY_REGEX = /&(?:[a-zA-Z]+|#\\d+);/g;\nfunction decodeEntities(text) {\n  if (!text || typeof text !== \"string\" || !ENTITY_REGEX.test(text)) {\n    return text;\n  }\n  var element = decodeEntities._element;\n  ENTITY_REGEX.lastIndex = 0;\n  return text.replace(ENTITY_REGEX, function (match) {\n    element.innerHTML = match;\n    return element.textContent || element.innerText;\n  });\n}\nif (typeof document !== \"undefined\") {\n  decodeEntities._element = document.createElement(\"span\");\n}\nvar TextNode = function (PathNode) {\n  function TextNode() {\n    PathNode.apply(this, arguments);\n  }\n  if (PathNode) TextNode.__proto__ = PathNode;\n  TextNode.prototype = Object.create(PathNode && PathNode.prototype);\n  TextNode.prototype.constructor = TextNode;\n  TextNode.prototype.geometryChange = function geometryChange() {\n    var pos = this.pos();\n    this.attr(\"x\", pos.x);\n    this.attr(\"y\", pos.y);\n    this.invalidate();\n  };\n  TextNode.prototype.optionsChange = function optionsChange(e) {\n    if (e.field === \"font\") {\n      this.attr(\"style\", renderStyle(this.mapStyle()));\n      this.geometryChange();\n    } else if (e.field === \"content\") {\n      PathNode.prototype.content.call(this, this.srcElement.content());\n    }\n    PathNode.prototype.optionsChange.call(this, e);\n  };\n  TextNode.prototype.mapStyle = function mapStyle(encode) {\n    var style = PathNode.prototype.mapStyle.call(this, encode);\n    var font = this.srcElement.options.font;\n    if (encode) {\n      font = htmlEncode(font);\n    }\n    style.push([\"font\", font], [\"white-space\", \"pre\"]);\n    return style;\n  };\n  TextNode.prototype.pos = function pos() {\n    var pos = this.srcElement.position();\n    var size = this.srcElement.measure();\n    return pos.clone().setY(pos.y + size.baseline);\n  };\n  TextNode.prototype.renderContent = function renderContent() {\n    var content = this.srcElement.content();\n    content = decodeEntities(content);\n    content = htmlEncode(content);\n    return normalizeText(content);\n  };\n  TextNode.prototype.renderTextAnchor = function renderTextAnchor() {\n    var anchor;\n    if ((this.options || {}).rtl && !(support.browser.msie || support.browser.edge)) {\n      anchor = 'end';\n    }\n    return renderAttr(\"text-anchor\", anchor);\n  };\n  TextNode.prototype.renderPaintOrder = function renderPaintOrder() {\n    var paintOrder = this.srcElement.options.paintOrder;\n    return paintOrder ? renderAttr(\"paint-order\", paintOrder) : \"\";\n  };\n  TextNode.prototype.template = function template() {\n    return \"<text \" + this.renderId() + \" \" + this.renderTextAnchor() + \" \" + this.renderStyle() + \" \" + this.renderOpacity() + \"x='\" + this.pos().x + \"' y='\" + this.pos().y + \"' \" + this.renderStroke() + \" \" + this.renderTransform() + \" \" + this.renderDefinitions() + \"\" + this.renderPaintOrder() + \"\" + this.renderFill() + this.renderClassName() + \" \" + this.renderRole() + this.renderAriaLabel() + \" \" + this.renderAriaRoleDescription() + this.renderAriaChecked() + \">\" + this.renderContent() + \"</text>\";\n  };\n  return TextNode;\n}(PathNode);\nexport default TextNode;", "map": {"version": 3, "names": ["PathNode", "renderStyle", "renderAttr", "htmlEncode", "support", "normalizeText", "ENTITY_REGEX", "decodeEntities", "text", "test", "element", "_element", "lastIndex", "replace", "match", "innerHTML", "textContent", "innerText", "document", "createElement", "TextNode", "apply", "arguments", "__proto__", "prototype", "Object", "create", "constructor", "geometryChange", "pos", "attr", "x", "y", "invalidate", "optionsChange", "e", "field", "mapStyle", "content", "call", "srcElement", "encode", "style", "font", "options", "push", "position", "size", "measure", "clone", "setY", "baseline", "renderContent", "renderTextAnchor", "anchor", "rtl", "browser", "msie", "edge", "renderPaintOrder", "paintOrder", "template", "renderId", "renderOpacity", "renderStroke", "renderTransform", "renderDefinitions", "renderFill", "renderClassName", "renderRole", "renderAriaLabel", "renderAriaRoleDescription", "renderAriaChecked"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-drawing/dist/es/svg/text-node.js"], "sourcesContent": ["import PathNode from './path-node';\nimport renderStyle from './utils/render-style';\nimport renderAttr from './utils/render-attribute';\nimport { htmlEncode, support } from '../common';\nimport { normalizeText } from '../text-metrics';\n\nvar ENTITY_REGEX = /&(?:[a-zA-Z]+|#\\d+);/g;\n\nfunction decodeEntities(text) {\n    if (!text || typeof text !== \"string\" || !ENTITY_REGEX.test(text)) {\n        return text;\n    }\n\n    var element = decodeEntities._element;\n    ENTITY_REGEX.lastIndex = 0;\n\n    return text.replace(ENTITY_REGEX, function (match) {\n        element.innerHTML = match;\n\n        return element.textContent || element.innerText;\n    });\n}\n\nif (typeof document !== \"undefined\") {\n    decodeEntities._element = document.createElement(\"span\");\n}\n\nvar TextNode = (function (PathNode) {\n    function TextNode () {\n        PathNode.apply(this, arguments);\n    }\n\n    if ( PathNode ) TextNode.__proto__ = PathNode;\n    TextNode.prototype = Object.create( PathNode && PathNode.prototype );\n    TextNode.prototype.constructor = TextNode;\n\n    TextNode.prototype.geometryChange = function geometryChange () {\n        var pos = this.pos();\n        this.attr(\"x\", pos.x);\n        this.attr(\"y\", pos.y);\n        this.invalidate();\n    };\n\n    TextNode.prototype.optionsChange = function optionsChange (e) {\n        if (e.field === \"font\") {\n            this.attr(\"style\", renderStyle(this.mapStyle()));\n            this.geometryChange();\n        } else if (e.field === \"content\") {\n            PathNode.prototype.content.call(this, this.srcElement.content());\n        }\n\n        PathNode.prototype.optionsChange.call(this, e);\n    };\n\n    TextNode.prototype.mapStyle = function mapStyle (encode) {\n        var style = PathNode.prototype.mapStyle.call(this, encode);\n        var font = this.srcElement.options.font;\n\n        if (encode) {\n            font = htmlEncode(font);\n        }\n\n        style.push([ \"font\", font ], [ \"white-space\", \"pre\" ]);\n\n        return style;\n    };\n\n    TextNode.prototype.pos = function pos () {\n        var pos = this.srcElement.position();\n        var size = this.srcElement.measure();\n        return pos.clone().setY(pos.y + size.baseline);\n    };\n\n    TextNode.prototype.renderContent = function renderContent () {\n        var content = this.srcElement.content();\n        content = decodeEntities(content);\n        content = htmlEncode(content);\n\n        return normalizeText(content);\n    };\n\n    TextNode.prototype.renderTextAnchor = function renderTextAnchor () {\n        var anchor;\n\n        if ((this.options || {}).rtl && !(support.browser.msie || support.browser.edge)) {\n            anchor = 'end';\n        }\n\n        return renderAttr(\"text-anchor\", anchor);\n    };\n\n    TextNode.prototype.renderPaintOrder = function renderPaintOrder () {\n        var paintOrder = this.srcElement.options.paintOrder;\n        return paintOrder ? renderAttr(\"paint-order\", paintOrder) : \"\";\n    };\n\n    TextNode.prototype.template = function template () {\n        return \"<text \" + (this.renderId()) + \" \" + (this.renderTextAnchor()) + \" \" + (this.renderStyle()) + \" \" + (this.renderOpacity()) +\n                    \"x='\" + (this.pos().x) + \"' y='\" + (this.pos().y) + \"' \" + (this.renderStroke()) + \" \" + (this.renderTransform()) + \" \" + (this.renderDefinitions()) +\n                    \"\" + (this.renderPaintOrder()) +\n                    \"\" + (this.renderFill()) +\n                    (this.renderClassName()) + \" \" + (this.renderRole()) +\n                    (this.renderAriaLabel()) + \" \" + (this.renderAriaRoleDescription()) +\n                    (this.renderAriaChecked()) + \">\" + (this.renderContent()) + \"</text>\";\n    };\n\n    return TextNode;\n}(PathNode));\n\nexport default TextNode;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,aAAa;AAClC,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,UAAU,MAAM,0BAA0B;AACjD,SAASC,UAAU,EAAEC,OAAO,QAAQ,WAAW;AAC/C,SAASC,aAAa,QAAQ,iBAAiB;AAE/C,IAAIC,YAAY,GAAG,uBAAuB;AAE1C,SAASC,cAAcA,CAACC,IAAI,EAAE;EAC1B,IAAI,CAACA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,CAACF,YAAY,CAACG,IAAI,CAACD,IAAI,CAAC,EAAE;IAC/D,OAAOA,IAAI;EACf;EAEA,IAAIE,OAAO,GAAGH,cAAc,CAACI,QAAQ;EACrCL,YAAY,CAACM,SAAS,GAAG,CAAC;EAE1B,OAAOJ,IAAI,CAACK,OAAO,CAACP,YAAY,EAAE,UAAUQ,KAAK,EAAE;IAC/CJ,OAAO,CAACK,SAAS,GAAGD,KAAK;IAEzB,OAAOJ,OAAO,CAACM,WAAW,IAAIN,OAAO,CAACO,SAAS;EACnD,CAAC,CAAC;AACN;AAEA,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;EACjCX,cAAc,CAACI,QAAQ,GAAGO,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;AAC5D;AAEA,IAAIC,QAAQ,GAAI,UAAUpB,QAAQ,EAAE;EAChC,SAASoB,QAAQA,CAAA,EAAI;IACjBpB,QAAQ,CAACqB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACnC;EAEA,IAAKtB,QAAQ,EAAGoB,QAAQ,CAACG,SAAS,GAAGvB,QAAQ;EAC7CoB,QAAQ,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAE1B,QAAQ,IAAIA,QAAQ,CAACwB,SAAU,CAAC;EACpEJ,QAAQ,CAACI,SAAS,CAACG,WAAW,GAAGP,QAAQ;EAEzCA,QAAQ,CAACI,SAAS,CAACI,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAI;IAC3D,IAAIC,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC,CAAC;IACpB,IAAI,CAACC,IAAI,CAAC,GAAG,EAAED,GAAG,CAACE,CAAC,CAAC;IACrB,IAAI,CAACD,IAAI,CAAC,GAAG,EAAED,GAAG,CAACG,CAAC,CAAC;IACrB,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB,CAAC;EAEDb,QAAQ,CAACI,SAAS,CAACU,aAAa,GAAG,SAASA,aAAaA,CAAEC,CAAC,EAAE;IAC1D,IAAIA,CAAC,CAACC,KAAK,KAAK,MAAM,EAAE;MACpB,IAAI,CAACN,IAAI,CAAC,OAAO,EAAE7B,WAAW,CAAC,IAAI,CAACoC,QAAQ,CAAC,CAAC,CAAC,CAAC;MAChD,IAAI,CAACT,cAAc,CAAC,CAAC;IACzB,CAAC,MAAM,IAAIO,CAAC,CAACC,KAAK,KAAK,SAAS,EAAE;MAC9BpC,QAAQ,CAACwB,SAAS,CAACc,OAAO,CAACC,IAAI,CAAC,IAAI,EAAE,IAAI,CAACC,UAAU,CAACF,OAAO,CAAC,CAAC,CAAC;IACpE;IAEAtC,QAAQ,CAACwB,SAAS,CAACU,aAAa,CAACK,IAAI,CAAC,IAAI,EAAEJ,CAAC,CAAC;EAClD,CAAC;EAEDf,QAAQ,CAACI,SAAS,CAACa,QAAQ,GAAG,SAASA,QAAQA,CAAEI,MAAM,EAAE;IACrD,IAAIC,KAAK,GAAG1C,QAAQ,CAACwB,SAAS,CAACa,QAAQ,CAACE,IAAI,CAAC,IAAI,EAAEE,MAAM,CAAC;IAC1D,IAAIE,IAAI,GAAG,IAAI,CAACH,UAAU,CAACI,OAAO,CAACD,IAAI;IAEvC,IAAIF,MAAM,EAAE;MACRE,IAAI,GAAGxC,UAAU,CAACwC,IAAI,CAAC;IAC3B;IAEAD,KAAK,CAACG,IAAI,CAAC,CAAE,MAAM,EAAEF,IAAI,CAAE,EAAE,CAAE,aAAa,EAAE,KAAK,CAAE,CAAC;IAEtD,OAAOD,KAAK;EAChB,CAAC;EAEDtB,QAAQ,CAACI,SAAS,CAACK,GAAG,GAAG,SAASA,GAAGA,CAAA,EAAI;IACrC,IAAIA,GAAG,GAAG,IAAI,CAACW,UAAU,CAACM,QAAQ,CAAC,CAAC;IACpC,IAAIC,IAAI,GAAG,IAAI,CAACP,UAAU,CAACQ,OAAO,CAAC,CAAC;IACpC,OAAOnB,GAAG,CAACoB,KAAK,CAAC,CAAC,CAACC,IAAI,CAACrB,GAAG,CAACG,CAAC,GAAGe,IAAI,CAACI,QAAQ,CAAC;EAClD,CAAC;EAED/B,QAAQ,CAACI,SAAS,CAAC4B,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAI;IACzD,IAAId,OAAO,GAAG,IAAI,CAACE,UAAU,CAACF,OAAO,CAAC,CAAC;IACvCA,OAAO,GAAG/B,cAAc,CAAC+B,OAAO,CAAC;IACjCA,OAAO,GAAGnC,UAAU,CAACmC,OAAO,CAAC;IAE7B,OAAOjC,aAAa,CAACiC,OAAO,CAAC;EACjC,CAAC;EAEDlB,QAAQ,CAACI,SAAS,CAAC6B,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAI;IAC/D,IAAIC,MAAM;IAEV,IAAI,CAAC,IAAI,CAACV,OAAO,IAAI,CAAC,CAAC,EAAEW,GAAG,IAAI,EAAEnD,OAAO,CAACoD,OAAO,CAACC,IAAI,IAAIrD,OAAO,CAACoD,OAAO,CAACE,IAAI,CAAC,EAAE;MAC7EJ,MAAM,GAAG,KAAK;IAClB;IAEA,OAAOpD,UAAU,CAAC,aAAa,EAAEoD,MAAM,CAAC;EAC5C,CAAC;EAEDlC,QAAQ,CAACI,SAAS,CAACmC,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAI;IAC/D,IAAIC,UAAU,GAAG,IAAI,CAACpB,UAAU,CAACI,OAAO,CAACgB,UAAU;IACnD,OAAOA,UAAU,GAAG1D,UAAU,CAAC,aAAa,EAAE0D,UAAU,CAAC,GAAG,EAAE;EAClE,CAAC;EAEDxC,QAAQ,CAACI,SAAS,CAACqC,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;IAC/C,OAAO,QAAQ,GAAI,IAAI,CAACC,QAAQ,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAACT,gBAAgB,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAACpD,WAAW,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAAC8D,aAAa,CAAC,CAAE,GACrH,KAAK,GAAI,IAAI,CAAClC,GAAG,CAAC,CAAC,CAACE,CAAE,GAAG,OAAO,GAAI,IAAI,CAACF,GAAG,CAAC,CAAC,CAACG,CAAE,GAAG,IAAI,GAAI,IAAI,CAACgC,YAAY,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAACC,eAAe,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAACC,iBAAiB,CAAC,CAAE,GACpJ,EAAE,GAAI,IAAI,CAACP,gBAAgB,CAAC,CAAE,GAC9B,EAAE,GAAI,IAAI,CAACQ,UAAU,CAAC,CAAE,GACvB,IAAI,CAACC,eAAe,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAACC,UAAU,CAAC,CAAE,GACnD,IAAI,CAACC,eAAe,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAACC,yBAAyB,CAAC,CAAE,GAClE,IAAI,CAACC,iBAAiB,CAAC,CAAE,GAAG,GAAG,GAAI,IAAI,CAACpB,aAAa,CAAC,CAAE,GAAG,SAAS;EACrF,CAAC;EAED,OAAOhC,QAAQ;AACnB,CAAC,CAACpB,QAAQ,CAAE;AAEZ,eAAeoB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}